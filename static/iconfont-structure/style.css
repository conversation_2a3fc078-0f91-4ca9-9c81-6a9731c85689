@font-face {
  font-family: 'icomoon';
  src:  url('fonts/icomoon.eot?gzgbpk');
  src:  url('fonts/icomoon.eot?gzgbpk#iefix') format('embedded-opentype'),
    url('fonts/icomoon.ttf?gzgbpk') format('truetype'),
    url('fonts/icomoon.woff?gzgbpk') format('woff'),
    url('fonts/icomoon.svg?gzgbpk#icomoon') format('svg');
  font-weight: normal;
  font-style: normal;
  font-display: block;
}

[class^="icon-"], [class*=" icon-"] {
  /* use !important to prevent issues with browser extensions that change fonts */
  font-family: 'icomoon' !important;
  speak: never;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;

  /* Better Font Rendering =========== */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-newface-Archives:before {
  content: "\f1";
  color: white;
}
.icon-newface-structure:before {
  content: "\f2";
  color: white;
}
