﻿window.bim_config = {
  newModelHttpUrl: 'http://www.probim.cn:3231',
  newModelApi: '/scenemanager/index.html',
  hasRouterFile: '', // 该配置用于有路由地址需求，比如：http://101.201.155.10:5010/BIME/#/,则hasRouterFile配置BIME，
  Version_number: '6.2.0.3190', //版本号
  // webserverurl: 'http://172.17.1.96:6311',
  webserverurl: 'http://101.201.155.10:3011',
  cwi_code: 'NEWWEB_PROBIMCN_FLOW',
  cwi_pwd_enc: 'f882f5baca0007ee02f852749a959077dbc70d1f64c234b954df10c85b3002696101027d62fb5c8aa27394e4d739d60f', // 直接配密文
  detectInterval: 3000,
};
// 走一个同步请求，获取 config.js 中的内容
var xhr = new XMLHttpRequest();

xhr.onreadystatechange = function() {
    if (xhr.readyState == 4) {
        // 反序列化为对象
        var obj = undefined;
        try {
            obj = JSON.parse(xhr.responseText);
        } catch (e) {

        }
        // 读取 Data
        if (obj) {
            if (obj.Ret > 0) {
                // 将 obj.Data 拷贝给 window.bim_config
                for (var attr in obj.Data) {
                    window.bim_config[attr] = obj.Data[attr];
                }
            }
        }
    }
};
var data = JSON.stringify({
    CwiCode: window.bim_config.cwi_code,
    CwiPwdPubEnc: window.bim_config.cwi_pwd_enc
});
xhr.open('POST', `${window.bim_config.webserverurl}/api/Global/GCfg/GetWebCfgItems`, false);
xhr.setRequestHeader("Content-Type", "application/json");
xhr.send(data);
