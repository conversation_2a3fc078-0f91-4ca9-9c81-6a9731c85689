<!DOCTYPE html>
<html lang="en">

<head>
 <meta charset="UTF-8">
 <meta http-equiv="X-UA-Compatible" content="IE=edge">
 <meta name="viewport" content="width=device-width, initial-scale=1.0">
 <title>Document</title>
</head>
<style>
 p{
  letter-spacing: 0.8pt;
 }
 span{
  letter-spacing: 0.8pt;
 }
 ::-webkit-scrollbar {
        /*滚动条整体样式*/
        width: 4px;
        /*高宽分别对应横竖滚动条的尺寸*/
        height: 4px;
      }
      ::-webkit-scrollbar-thumb {
        /*滚动条里面小方块*/
        border-radius: 5px;
        -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
        background: rgba(0, 0, 0, 0.2);
      }

      ::-webkit-scrollbar-track {
        /*滚动条里面轨道*/
        -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
        border-radius: 0;
        background: rgba(0, 0, 0, 0.1);
      }
</style>
<body>
 <div>
  <p style="margin-top:0pt; margin-bottom:8pt; text-align:center; widows:0;
    orphans:0; font-size:16pt"><span style="font-family:'宋体'; font-weight:bold">帮助说明</span></p>
  <p style="margin-top:0pt; margin-bottom:8pt; widows:0; orphans:0;
    font-size:10.5pt"><span style="font-family:Calibri; font-weight:bold">1.</span><span
    style="font-family:'宋体'; font-weight:bold">支持格式</span></p>
  <p style="margin-top:0pt; margin-bottom:8pt; widows:0; orphans:0;
    font-size:10.5pt"><span style="font-family:'宋体'">直接</span><span style="font-family:'宋体'">可应用</span><span
    style="font-family:'宋体'">的</span><span style="font-family:'宋体'">轻量化格式：</span><span
    style="font-family:Calibri">.</span><span style="font-family:Calibri">m</span><span
    style="font-family:Calibri">v</span></p>
  <p style="margin-top:0pt; margin-bottom:8pt; widows:0; orphans:0;
    font-size:10.5pt"><span style="font-family:'宋体'">需等待后台转换完成后应用的</span><span style="font-family:'宋体'">格式：</span><span
    style="font-family:Calibri">dwg,rvt,dgn,nwd,nwc,iam,ipt,ifc,3dxml,rvm,zip,rfa</span></p>
  <p style="margin-top:0pt; margin-bottom:8pt; widows:0; orphans:0;
    font-size:10.5pt"><span style="font-family:Calibri; font-weight:bold">2.</span><span
    style="font-family:'宋体'; font-weight:bold">外部导入或者链接文件的处理方法</span></p>
  <p style="margin-top:0pt; margin-bottom:8pt; widows:0; orphans:0;
    font-size:10.5pt"><span style="font-family:'宋体'">针对有链接文件或者外部导入的模型，支持上传总模型压缩包（压缩文件不得超</span><span
    style="font-family:'宋体'">过</span><span style="font-family:Calibri">500MB)</span><span
    style="font-family:'宋体'">。</span></p>
  <p style="margin-top:0pt;
    margin-bottom:8pt; widows:0; orphans:0; font-size:10.5pt"><span
    style="font-family:'宋体'; font-weight:bold; color:#ff0000">注：</span></p>
  <p style="margin-top:0pt; margin-bottom:8pt; widows:0; orphans:0;
    font-size:10.5pt"><span style="font-family:Calibri; color:#ff0000">1.</span><span
    style="font-family:'宋体'; color:#ff0000">压缩包命名规则为：主文件名</span><span
    style="font-family:Calibri; color:#ff0000">.</span><span style="font-family:'宋体'; color:#ff0000">后缀</span><span
    style="font-family:Calibri; color:#ff0000">.zip</span><span
    style="font-family:'宋体'; color:#ff0000">。例：合并模型</span><span
    style="font-family:Calibri; color:#ff0000">.rvt.zip</span><span
    style="font-family:'宋体'; color:#ff0000">。如压缩包命名未按照上述规则，则无法转换。</span></p>
  <p style="margin-top:0pt; margin-bottom:8pt; widows:0; orphans:0;
    font-size:10.5pt"><img src="Help_files/img_01.png" width="176" height="177" alt="" style="-aw-left-pos:0pt; -aw-rel-hpos:column;
     -aw-rel-vpos:paragraph; -aw-top-pos:0pt; -aw-wrap-type:inline" /><img src="Help_files/img_02.png" width="390"
    height="220" alt="" style="-aw-left-pos:0pt; -aw-rel-hpos:column; -aw-rel-vpos:paragraph;
     -aw-top-pos:0pt; -aw-wrap-type:inline" /></p>
  <p style="margin-top:0pt;
    margin-bottom:8pt; widows:0; orphans:0; font-size:10.5pt"><span
    style="font-family:Calibri; color:#ff0000">2</span><span style="font-family:Calibri; color:#ff0000">.</span><span
    style="font-family:'宋体'; color:#ff0000">压缩方法：选中所有需要压缩的文件，右键添加到压缩文件。</span><span
    style="font-family:'宋体'; color:#ff0000">如果</span><span
    style="font-family:'宋体'; color:#ff0000">直接压缩最外层文件夹，则无法转换。</span></p>
  <p style="margin-top:0pt; margin-bottom:8pt; widows:0; orphans:0;
    font-size:10.5pt"><img src="Help_files/img_03.png" width="465" height="287" alt="" style="-aw-left-pos:0pt; -aw-rel-hpos:column;
     -aw-rel-vpos:paragraph; -aw-top-pos:0pt; -aw-wrap-type:inline" /></p>
  <p style="margin-top:0pt; margin-bottom:8pt; widows:0; orphans:0;
    font-size:10.5pt"><img src="Help_files/img_04.png" width="435" height="162" alt="" style="-aw-left-pos:0pt; -aw-rel-hpos:column;
     -aw-rel-vpos:paragraph; -aw-top-pos:0pt; -aw-wrap-type:inline" /></p>
  <p style="margin-top:0pt; margin-bottom:8pt; widows:0; orphans:0;
    font-size:10.5pt"><span style="font-family:Calibri">&#xa0;</span></p>
  <p style="margin-top:0pt; margin-bottom:8pt; widows:0; orphans:0;
    font-size:10.5pt"><span style="font-family:Calibri; color:#ff0000">3</span><span
    style="font-family:Calibri; color:#ff0000">.</span><span style="font-family:'宋体'; color:#ff0000">压缩前，所有</span><span
    style="font-family:'宋体'; color:#ff0000">主文件不可套在文件夹中，否则无法转换。</span></p>
  <p style="margin-top:0pt; margin-bottom:8pt; widows:0;
    orphans:0; font-size:10.5pt"><img src="Help_files/img_06.png" width="552" height="241" alt="" style="-aw-left-pos:0pt;
     -aw-rel-hpos:column; -aw-rel-vpos:paragraph; -aw-top-pos:0pt;
     -aw-wrap-type:inline" /></p>
  <p style="margin-top:0pt; margin-bottom:8pt;
    widows:0; orphans:0; font-size:10.5pt"><img src="Help_files/img_07.png" width="567"
    height="216" alt="" style="-aw-left-pos:0pt; -aw-rel-hpos:column; -aw-rel-vpos:paragraph;
     -aw-top-pos:0pt; -aw-wrap-type:inline" /></p>
  <p style="margin-top:0pt;
    margin-bottom:8pt; widows:0; orphans:0; font-size:10.5pt"><span
    style="font-family:Calibri; color:#ff0000">4</span><span style="font-family:Calibri; color:#ff0000">.</span><span
    style="font-family:'宋体'; color:#ff0000">压缩包检查：压缩完成后双击压缩包，打开，如图</span><span
    style="font-family:Calibri; color:#ff0000">1</span><span
    style="font-family:'宋体'; color:#ff0000">，符合转换要求；如图</span><span
    style="font-family:Calibri; color:#ff0000">2</span><span style="font-family:'宋体'; color:#ff0000">，不可转换。</span></p>
  <p style="margin-top:0pt; margin-bottom:8pt; widows:0; orphans:0;
    font-size:10.5pt"><img src="Help_files/img_08.png" width="520" height="280" alt="" style="-aw-left-pos:0pt; -aw-rel-hpos:column;
     -aw-rel-vpos:paragraph; -aw-top-pos:0pt; -aw-wrap-type:inline" /></p>
  <p style="margin-top:0pt; margin-bottom:8pt; text-align:center; widows:0;
    orphans:0; font-size:9pt"><span style="font-family:'宋体'">图</span><span style="font-family:Calibri">1</span></p>
  <p style="margin-top:0pt;
    margin-bottom:8pt; text-align:justify; widows:0; orphans:0;
    font-size:10.5pt"><img src="Help_files/img_10.png" width="514" height="137" alt="" style="-aw-left-pos:0pt; -aw-rel-hpos:column;
     -aw-rel-vpos:paragraph; -aw-top-pos:0pt; -aw-wrap-type:inline" /></p>
  <p style="margin-top:0pt; margin-bottom:8pt; text-align:center; widows:0;
    orphans:0; font-size:9pt"><span style="font-family:'宋体'">图</span><span style="font-family:Calibri">2</span></p>
  <p style="margin-top:0pt;
    margin-bottom:8pt; widows:0; orphans:0; font-size:10.5pt"><span style="font-family:Calibri">&#xa0;</span></p>
 </div>
</body>

</html>