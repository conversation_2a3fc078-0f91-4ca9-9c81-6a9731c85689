@font-face {
    font-family: 'icomoon';
    src: url('./fonts/icomoon.eot?wyshk7');
    src: url('./fonts/icomoon.eot?wyshk7#iefix') format('embedded-opentype'), url('./fonts/icomoon.ttf?wyshk7') format('truetype'), url('./fonts/icomoon.woff?wyshk7') format('woff'), url('./fonts/icomoon.svg?wyshk7#icomoon') format('svg');
    font-weight: normal;
    font-style: normal;
    font-display: block;
}

[class^="icon-"],
[class*=" icon-"] {
    /* use !important to prevent issues with browser extensions that change fonts */
    font-family: 'icomoon' !important;
    speak: never;
    font-style: normal;
    font-weight: normal;
    font-variant: normal;
    text-transform: none;
    line-height: 1;
    /* Better Font Rendering =========== */
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

.icon-duibi:before {
    content: "\f900";
}

.icon-new-dangan:before {
    content: "\f901";
}

.icon-new-down:before {
    content: "\f902";
    color: #777;
}

.icon-new-tree-last:before {
    content: "\f903";
    color: #747475;
}

.icon-new-tree:before {
    content: "\f904";
    color: #747475;
}

.icon-new-up:before {
    content: "\f905";
    color: #777;
}

.icon-newface-add:before {
    content: "\f906";
}

.icon-newface-anquan:before {
    content: "\f907";
    color: #ddd;
}

.icon-newface-app-update:before {
    content: "\f908";
    color: #575757;
}

.icon-newface-biangeng:before {
    content: "\f909";
    color: #ddd;
}

.icon-newface-chengben:before {
    content: "\f90a";
    color: #ddd;
}

.icon-newface-disanfang:before {
    content: "\f90b";
    color: #ddd;
}

.icon-newface-gongcheng:before {
    content: "\f90c";
    color: #ddd;
}

.icon-newface-gongchengyanshou:before {
    content: "\f90d";
    color: #ddd;
}

.icon-newface-hetong:before {
    content: "\f90e";
    color: #ddd;
}

.icon-newface-home:before {
    content: "\f90f";
    color: #ddd;
}

.icon-newface-huiyi:before {
    content: "\f910";
    color: #ddd;
}

.icon-newface-jiashicang:before {
    content: "\f911";
    color: #ddd;
}

.icon-newface-jindu:before {
    content: "\f912";
    color: #ddd;
}

.icon-newface-kancha:before {
    content: "\f913";
    color: #ddd;
}

.icon-newface-liucheng:before {
    content: "\f914";
    color: #ddd;
}

.icon-newface-model:before {
    content: "\f915";
    color: #606266;
}

.icon-newface-moxing:before {
    content: "\f916";
    color: #ddd;
}

.icon-newface-pro-close:before {
    content: "\f917";
    color: #ddd;
}

.icon-newface-pro-jd:before {
    content: "\f918";
}

.icon-newface-pro-open:before {
    content: "\f919";
    color: #ddd;
}

.icon-newface-qianqiguanli:before {
    content: "\f91a";
    color: #ddd;
}

.icon-newface-quanjing:before {
    content: "\f91b";
    color: #ddd;
}

.icon-newface-scene:before {
    content: "\f91c";
    color: #ddd;
}

.icon-newface-set:before {
    content: "\f91d";
    color: #ddd;
}

.icon-newface-sheji:before {
    content: "\f91e";
    color: #ddd;
}

.icon-newface-touzi:before {
    content: "\f91f";
    color: #ddd;
}

.icon-newface-wendang:before {
    content: "\f920";
    color: #ddd;
}

.icon-newface-xianchang:before {
    content: "\f921";
    color: #ddd;
}

.icon-newface-zhiliang:before {
    content: "\f922";
    color: #ddd;
}

.icon-yizhuanhuan:before {
    content: "\f923";
}

.icon-zhuanhuan:before {
    content: "\f924";
}
