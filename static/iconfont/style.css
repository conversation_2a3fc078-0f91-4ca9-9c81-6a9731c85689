@font-face {
    font-family: 'icomoon';
    src: url('./fonts/icomoon.eot?ut5zyd');
    src: url('./fonts/icomoon.eot?ut5zyd#iefix') format('embedded-opentype'), url('./fonts/icomoon.ttf?ut5zyd') format('truetype'), url('./fonts/icomoon.woff?ut5zyd') format('woff'), url('./fonts/icomoon.svg?ut5zyd#icomoon') format('svg');
    font-weight: normal;
    font-style: normal;
    font-display: block;
}

[class^="icon-"],
[class*=" icon-"] {
    /* use !important to prevent issues with browser extensions that change fonts */
    font-family: 'icomoon' !important;
    speak: never;
    font-style: normal;
    font-weight: normal;
    font-variant: normal;
    text-transform: none;
    line-height: 1;
    /* Better Font Rendering =========== */
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

.icon-arrow-arrowdown:before {
    content: "\e900";
}

.icon-arrow-arrowleft:before {
    content: "\e901";
}

.icon-arrow-arrowright:before {
    content: "\e902";
}

.icon-arrow-arrowup:before {
    content: "\e903";
}

.icon-arrow-down:before {
    content: "\e904";
}

.icon-arrow-down_outline:before {
    content: "\e905";
}

.icon-arrow-fullscreen:before {
    content: "\e906";
}

.icon-arrow-fullscreen_exit:before {
    content: "\e907";
}

.icon-arrow-indent:before {
    content: "\e908";
}

.icon-arrow-left_outline:before {
    content: "\e909";
}

.icon-arrow-login:before {
    content: "\e90a";
}

.icon-arrow-logout:before {
    content: "\e90b";
}

.icon-arrow-outdent:before {
    content: "\e90c";
}

.icon-arrow-rank:before {
    content: "\e90d";
}

.icon-arrow-right:before {
    content: "\e90e";
}

.icon-arrow-right_outline:before {
    content: "\e90f";
}

.icon-arrow-swap:before {
    content: "\e910";
}

.icon-arrow-up_outline:before {
    content: "\e911";
}

.icon-checkbox-Checked .path1:before {
    content: "\e912";
    color: rgb(24, 144, 255);
}

.icon-checkbox-Checked .path2:before {
    content: "\e913";
    margin-left: -1em;
    color: rgb(255, 255, 255);
}

.icon-checkbox-Default:before {
    content: "\e914";
}

.icon-checkbox-Disable .path1:before {
    content: "\e915";
    color: rgb(0, 0, 0);
    opacity: 0.0400;
}

.icon-checkbox-Disable .path2:before {
    content: "\e916";
    margin-left: -1em;
    color: rgb(0, 0, 0);
    opacity: 0.15;
}

.icon-checkbox-Indeterminate .path1:before {
    content: "\e917";
    color: rgb(0, 0, 0);
    opacity: 0.15;
}

.icon-checkbox-Indeterminate .path2:before {
    content: "\e918";
    margin-left: -1em;
    color: rgb(24, 144, 255);
}

.icon-checkbox-Radio:before {
    content: "\e919";
}

.icon-checkbox-Radio-Disable .path1:before {
    content: "\e91a";
    color: rgb(0, 0, 0);
    opacity: 0.0400;
}

.icon-checkbox-Radio-Disable .path2:before {
    content: "\e91b";
    margin-left: -1em;
    color: rgb(0, 0, 0);
    opacity: 0.15;
}

.icon-checkbox-Radio-Selected:before {
    content: "\e91c";
    color: #1890ff;
}

.icon-checkbox-Selected-Disabled .path1:before {
    content: "\e91d";
    color: rgb(0, 0, 0);
    opacity: 0.25;
}

.icon-checkbox-Selected-Disabled .path2:before {
    content: "\e91e";
    margin-left: -1em;
    color: rgb(0, 0, 0);
    opacity: 0.0400;
}

.icon-checkbox-Selected-Disabled .path3:before {
    content: "\e91f";
    margin-left: -1em;
    color: rgb(0, 0, 0);
    opacity: 0.15;
}

.icon-checkbox-Selected-Disabled-dis:before {
    content: "\e920";
}

.icon-checkbox-Selected-Disabled-dis-blue:before {
    content: "\e921";
    color: #1890ff;
}

.icon-dl-approve .path1:before {
    content: "\e922";
    color: rgb(255, 255, 255);
    opacity: 0.8;
}

.icon-dl-approve .path2:before {
    content: "\e923";
    margin-left: -1em;
    color: rgb(0, 122, 255);
    opacity: 0.8;
}

.icon-dl-disapprove:before {
    content: "\e924";
    color: #fff;
}

.icon-dl-doc:before {
    content: "\e925";
    color: #a6aeb6;
}

.icon-dl-model:before {
    content: "\e926";
    color: #a6aeb6;
}

.icon-dl-nodeicon:before {
    content: "\e927";
    color: #a6aeb6;
}

.icon-dl-submit:before {
    content: "\e928";
    color: #fff;
}

.icon-dl-viewflows:before {
    content: "\e929";
    color: #616f7d;
}

.icon-interface_bigscreen:before {
    content: "\e92a";
}

.icon-interface_formexcel:before {
    content: "\e92b";
}

.icon-interface_project_image_progress:before {
    content: "\e92c";
}

.icon-interface-2d:before {
    content: "\e92d";
}

.icon-interface-2d_se:before {
    content: "\e92e";
}

.icon-interface-720:before {
    content: "\e92f";
}

.icon-interface-addnew:before {
    content: "\e930";
}

.icon-interface-advanced:before {
    content: "\e931";
}

.icon-interface-angle-measurement:before {
    content: "\e932";
}

.icon-interface-area-measurement:before {
    content: "\e933";
}

.icon-interface-ascending:before {
    content: "\e934";
}

.icon-interface-associated-component:before {
    content: "\e935";
}

.icon-interface-attachment:before {
    content: "\e936";
}

.icon-interface-attributes:before {
    content: "\e937";
}

.icon-interface-attributes_se:before {
    content: "\e938";
}

.icon-interface-authorization:before {
    content: "\e939";
}

.icon-interface-back:before {
    content: "\e93a";
}

.icon-interface-bell:before {
    content: "\e93b";
}

.icon-interface-business_card:before {
    content: "\e93c";
}

.icon-interface-camera:before {
    content: "\e93d";
}

.icon-interface-camera_se:before {
    content: "\e93e";
}

.icon-interface-character-roaming:before {
    content: "\e93f";
}

.icon-interface-clan:before {
    content: "\e940";
}

.icon-interface-cloud-download_could:before {
    content: "\e941";
}

.icon-interface-cloud-upload:before {
    content: "\e942";
}

.icon-interface-comment:before {
    content: "\e943";
}

.icon-interface-compared:before {
    content: "\e944";
}

.icon-interface-component_classification:before {
    content: "\e945";
}

.icon-interface-component_cost:before {
    content: "\e946";
}

.icon-interface-component_stage:before {
    content: "\e947";
}

.icon-interface-component-outline:before {
    content: "\e948";
}

.icon-interface-component-search:before {
    content: "\e949";
}

.icon-interface-copy:before {
    content: "\e94a";
}

.icon-interface-cord:before {
    content: "\e94b";
}

.icon-interface-creditcard:before {
    content: "\e94c";
}

.icon-interface-crew:before {
    content: "\e94d";
}

.icon-interface-data:before {
    content: "\e94e";
}

.icon-interface-database:before {
    content: "\e94f";
}

.icon-interface-delete:before {
    content: "\e950";
}

.icon-interface-delete-fill:before {
    content: "\e951";
}

.icon-interface-descending:before {
    content: "\e952";
}

.icon-interface-diannaodenglu:before {
    content: "\e953";
}

.icon-interface-dig:before {
    content: "\e954";
}

.icon-interface-disable-role:before {
    content: "\e955";
}

.icon-interface-disanrencheng:before {
    content: "\e956";
}

.icon-interface-displayed_in_folder:before {
    content: "\e957";
}

.icon-interface-doc:before {
    content: "\e958";
}

.icon-interface-document:before {
    content: "\e959";
}

.icon-interface-document_fill:before {
    content: "\e95a";
}

.icon-interface-download:before {
    content: "\e95b";
}

.icon-interface-download-fill:before {
    content: "\e95c";
}

.icon-interface-drag-icon:before {
    content: "\e95d";
}

.icon-interface-draw:before {
    content: "\e95e";
}

.icon-interface-drawing:before {
    content: "\e95f";
}

.icon-interface-edit:before {
    content: "\e960";
}

.icon-interface-edit_se:before {
    content: "\e961";
}

.icon-interface-edit-model:before {
    content: "\e962";
}

.icon-interface-edit-user:before {
    content: "\e963";
}

.icon-interface-email:before {
    content: "\e964";
}

.icon-interface-erweima:before {
    content: "\e965";
}

.icon-interface-explosion-analysis:before {
    content: "\e966";
}

.icon-interface-eye:before {
    content: "\e967";
}

.icon-interface-face-selection:before {
    content: "\e968";
}

.icon-interface-filter:before {
    content: "\e969";
}

.icon-interface-folder-copy:before {
    content: "\e96a";
}

.icon-interface-folder:before {
    content: "\e96b";
}

.icon-interface-form:before {
    content: "\e96c";
}

.icon-interface-gis:before {
    content: "\e96d";
}

.icon-interface-gravity:before {
    content: "\e96e";
}

.icon-interface-guanbimoxing:before {
    content: "\e96f";
}

.icon-interface-guangbiao:before {
    content: "\e970";
}

.icon-interface-guanlianmoxing:before {
    content: "\e971";
}

.icon-interface-hebingmoxing .path1:before {
    content: "\e972";
    color: rgb(186, 202, 218);
    opacity: 0.0700;
}

.icon-interface-hebingmoxing .path2:before {
    content: "\e973";
    margin-left: -1em;
    color: rgb(219, 219, 219);
    opacity: 0.7;
}

.icon-interface-hebingmoxing .path3:before {
    content: "\e974";
    margin-left: -1em;
    color: rgb(0, 0, 0);
    opacity: 0.0700;
}

.icon-interface-hebingmoxing .path4:before {
    content: "\e975";
    margin-left: -1em;
    color: rgb(219, 219, 219);
    opacity: 0.7;
}

.icon-interface-hebingmoxing .path5:before {
    content: "\e976";
    margin-left: -1em;
    color: rgb(0, 0, 0);
    opacity: 0.0700;
}

.icon-interface-hebingmoxing .path6:before {
    content: "\e977";
    margin-left: -1em;
    color: rgb(0, 0, 0);
    opacity: 0.0700;
}

.icon-interface-hebingmoxing .path7:before {
    content: "\e978";
    margin-left: -1em;
    color: rgb(219, 219, 219);
    opacity: 0.7;
}

.icon-interface-hebingmoxing .path8:before {
    content: "\e979";
    margin-left: -1em;
    color: rgb(0, 0, 0);
    opacity: 0.0700;
}

.icon-interface-hebingmoxing .path9:before {
    content: "\e97a";
    margin-left: -1em;
    color: rgb(0, 0, 0);
    opacity: 0.0700;
}

.icon-interface-hebingmoxing .path10:before {
    content: "\e97b";
    margin-left: -1em;
    color: rgb(219, 219, 219);
    opacity: 0.7;
}

.icon-interface-hebingmoxing .path11:before {
    content: "\e97c";
    margin-left: -1em;
    color: rgb(0, 0, 0);
    opacity: 0.0700;
}

.icon-interface-hebingmoxing .path12:before {
    content: "\e97d";
    margin-left: -1em;
    color: rgb(219, 219, 219);
    opacity: 0.7;
}

.icon-interface-hebingmoxing .path13:before {
    content: "\e97e";
    margin-left: -1em;
    color: rgb(0, 0, 0);
    opacity: 0.0700;
}

.icon-interface-history:before {
    content: "\e97f";
}

.icon-interface-home:before {
    content: "\e980";
}

.icon-interface-home_fill:before {
    content: "\e981";
}

.icon-interface-image:before {
    content: "\e982";
}

.icon-interface-inbox:before {
    content: "\e983";
}

.icon-interface-institution:before {
    content: "\e984";
}

.icon-interface-link:before {
    content: "\e985";
}

.icon-interface-linkage:before {
    content: "\e986";
}

.icon-interface-linkfailed:before {
    content: "\e987";
    color: #0d2c6f;
}

.icon-interface-link-fill:before {
    content: "\e988";
}

.icon-interface-list:before {
    content: "\e989";
}

.icon-interface-list-fill:before {
    content: "\e98a";
}

.icon-interface-location:before {
    content: "\e98b";
}

.icon-interface-lock:before {
    content: "\e98c";
}

.icon-interface-log:before {
    content: "\e98d";
}

.icon-interface-louceng:before {
    content: "\e98e";
}

.icon-interface-magnifier_big:before {
    content: "\e98f";
}

.icon-interface-magnifier_small:before {
    content: "\e990";
}

.icon-interface-man:before {
    content: "\e991";
}

.icon-interface-mark:before {
    content: "\e992";
}

.icon-interface-material-management:before {
    content: "\e993";
}

.icon-interface-measuring:before {
    content: "\e994";
}

.icon-interface-Merge:before {
    content: "\e995";
}

.icon-interface-merge-model:before {
    content: "\e996";
}

.icon-interface-message:before {
    content: "\e997";
}

.icon-interface-model:before {
    content: "\e998";
}

.icon-interface-model_list:before {
    content: "\e999";
}

.icon-interface-model-collision:before {
    content: "\e99a";
}

.icon-interface-model-delete:before {
    content: "\e99b";
}

.icon-interface-model-measurement:before {
    content: "\e99c";
}

.icon-interface-model-picture:before {
    content: "\e99d";
}

.icon-interface-model-sectioning:before {
    content: "\e99e";
}

.icon-interface-model-statistics:before {
    content: "\e99f";
}

.icon-interface-more:before {
    content: "\e9a0";
}

.icon-interface-moren:before {
    content: "\e9a1";
}

.icon-interface-move:before {
    content: "\e9a2";
}

.icon-interface-new-institution:before {
    content: "\e9a3";
}

.icon-interface-noneeye:before {
    content: "\e9a4";
}

.icon-interface-notification:before {
    content: "\e9a5";
}

.icon-interface-paste:before {
    content: "\e9a6";
}

.icon-interface-path-roaming:before {
    content: "\e9a7";
}

.icon-interface-person-fill:before {
    content: "\e9a8";
}

.icon-interface-phone:before {
    content: "\e9a9";
}

.icon-interface-photo:before {
    content: "\e9aa";
}

.icon-interface-plan-list .path1:before {
    content: "\e9ab";
    color: rgb(0, 0, 0);
}

.icon-interface-plan-list .path2:before {
    content: "\e9ac";
    margin-left: -1em;
    color: rgb(245, 245, 245);
}

.icon-interface-plan-list .path3:before {
    content: "\e9ad";
    margin-left: -1em;
    color: rgb(0, 97, 255);
}

.icon-interface-plan-list .path4:before {
    content: "\e9ae";
    margin-left: -1em;
    color: rgb(0, 97, 255);
}

.icon-interface-platform:before {
    content: "\e9af";
}

.icon-interface-play:before {
    content: "\e9b0";
}

.icon-interface-portrait:before {
    content: "\e9b1";
}

.icon-interface-print:before {
    content: "\e9b2";
}

.icon-interface-probim-time:before {
    content: "\e9b3";
}

.icon-interface-problem:before {
    content: "\e9b4";
}

.icon-interface-problem-sorting:before {
    content: "\e9b5";
}

.icon-interface-problem-status:before {
    content: "\e9b6";
}

.icon-interface-process:before {
    content: "\e9b7";
}

.icon-interface-process_classification:before {
    content: "\e9b8";
}

.icon-interface-processicon:before {
    content: "\e9b9";
    color: #1da48c;
}

.icon-interface-project-process:before {
    content: "\e9ba";
}

.icon-interface-qrcode:before {
    content: "\e9bb";
}

.icon-interface-quality:before {
    content: "\e9bc";
}

.icon-interface-red:before {
    content: "\e9bd";
    color: #f5222d;
}

.icon-interface-restore:before {
    content: "\e9be";
}

.icon-interface-return:before {
    content: "\e9bf";
}

.icon-interface-safety:before {
    content: "\e9c0";
}

.icon-interface-sanweidiejia-copy:before {
    content: "\e9c1";
}

.icon-interface-sanweidiejia:before {
    content: "\e9c2";
}

.icon-interface-sapce:before {
    content: "\e9c3";
}

.icon-interface-save:before {
    content: "\e9c4";
}

.icon-interface-search:before {
    content: "\e9c5";
}

.icon-interface-search-fill:before {
    content: "\e9c6";
}

.icon-interface-section:before {
    content: "\e9c7";
}

.icon-interface-select:before {
    content: "\e9c8";
}

.icon-interface-set:before {
    content: "\e9c9";
}

.icon-interface-set_se:before {
    content: "\e9ca";
}

.icon-interface-setting:before {
    content: "\e9cb";
}

.icon-interface-shaft_net:before {
    content: "\e9cc";
}

.icon-interface-shaft_net_se:before {
    content: "\e9cd";
}

.icon-interface-share:before {
    content: "\e9ce";
}

.icon-interface-share-fill:before {
    content: "\e9cf";
}

.icon-interface-shortest-distance:before {
    content: "\e9d0";
}

.icon-interface-show:before {
    content: "\e9d1";
}

.icon-interface-sidebar_pdf:before {
    content: "\e9d2";
}

.icon-interface-spatial-distance:before {
    content: "\e9d3";
}

.icon-interface-star-nor:before {
    content: "\e9d4";
}

.icon-interface-star-se:before {
    content: "\e9d5";
}

.icon-interface-stop:before {
    content: "\e9d6";
}

.icon-interface-sun:before {
    content: "\e9d7";
}

.icon-interface-superimposed:before {
    content: "\e9d8";
}

.icon-interface-taping:before {
    content: "\e9d9";
}

.icon-interface-team:before {
    content: "\e9da";
}

.icon-interface-terrain:before {
    content: "\e9db";
}

.icon-interface-tilt_photography:before {
    content: "\e9dc";
}

.icon-interface-time:before {
    content: "\e9dd";
}

.icon-interface-time-out:before {
    content: "\e9de";
}

.icon-interface-toleft:before {
    content: "\e9df";
}

.icon-interface-toolbox:before {
    content: "\e9e0";
}

.icon-interface-toolbox_se:before {
    content: "\e9e1";
}

.icon-interface-toright:before {
    content: "\e9e2";
}

.icon-interface-transfer-agency:before {
    content: "\e9e3";
}

.icon-interface-transparency:before {
    content: "\e9e4";
}

.icon-interface-tree:before {
    content: "\e9e5";
}

.icon-interface-tree_se:before {
    content: "\e9e6";
}

.icon-interface-tuodong:before {
    content: "\e9e7";
}

.icon-interface-tuoputu:before {
    content: "\e9e8";
}

.icon-interface-unfolder:before {
    content: "\e9e9";
}

.icon-interface-unlock:before {
    content: "\e9ea";
}

.icon-interface-unsubscribe:before {
    content: "\e9eb";
}

.icon-interface-user:before {
    content: "\e9ec";
}

.icon-interface-user_fill:before {
    content: "\e9ed";
}

.icon-interface-video:before {
    content: "\e9ee";
}

.icon-interface-wa:before {
    content: "\e9ef";
    color: #1890ff;
}

.icon-interface-xianshimoxing:before {
    content: "\e9f0";
}

.icon-interface-xyz:before {
    content: "\e9f1";
}

.icon-interface-ya:before {
    content: "\e9f2";
    color: #faad14;
}

.icon-interface-yiguidang:before {
    content: "\e9f3";
    color: #f5222d;
}

.icon-interface-yincangmoxing:before {
    content: "\e9f4";
}

.icon-interface-zoom:before {
    content: "\e9f5";
}

.icon-interface-zoomin:before {
    content: "\e9f6";
}

.icon-model-GIS:before {
    content: "\e9f7";
}

.icon-suggested-check:before {
    content: "\e9f8";
}

.icon-suggested-check_circle:before {
    content: "\e9f9";
}

.icon-suggested-check_circle_outline:before {
    content: "\e9fa";
}

.icon-suggested-close:before {
    content: "\e9fb";
}

.icon-suggested-close_circle:before {
    content: "\e9fc";
}

.icon-suggested-close_circle-outline:before {
    content: "\e9fd";
}

.icon-suggested-close-fill-copy:before {
    content: "\e9fe";
}

.icon-suggested-close-fill:before {
    content: "\e9ff";
}

.icon-suggested-exclaimination:before {
    content: "\ea00";
}

.icon-suggested-info_circle:before {
    content: "\ea01";
}

.icon-suggested-infomation:before {
    content: "\ea02";
}

.icon-suggested-minus:before {
    content: "\ea03";
}

.icon-suggested-minus_circle:before {
    content: "\ea04";
}

.icon-suggested-minus_square:before {
    content: "\ea05";
}

.icon-suggested-platform:before {
    content: "\ea06";
}

.icon-suggested-plus:before {
    content: "\ea07";
}

.icon-suggested-plus_circle:before {
    content: "\ea08";
}

.icon-suggested-plus_square:before {
    content: "\ea09";
}

.icon-suggested-question:before {
    content: "\ea0a";
}

.icon-suggested-question_circle:before {
    content: "\ea0b";
}

.icon-suggested-time:before {
    content: "\ea0c";
}

.icon-suggested-warning_circle:before {
    content: "\ea0d";
}

.icon-suggested-warning_circle_fill:before {
    content: "\ea0e";
}