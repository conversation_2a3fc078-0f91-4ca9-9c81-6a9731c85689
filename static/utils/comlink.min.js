!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports):"function"==typeof define&&define.amd?define(["exports"],t):t((e=e||self).Comlink={})}(this,function(e){"use strict";const t=Symbol("Comlink.proxy"),n=Symbol("Comlink.endpoint"),r=new WeakSet,a=new Map([["proxy",{canHandle:e=>e&&e[t],serialize(e){const{port1:t,port2:n}=new MessageChannel;return s(e,t),[n,[n]]},deserialize:e=>(e.start(),o(e))}],["throw",{canHandle:e=>r.has(e),serialize(e){const t=e instanceof Error;let n=e;return t&&(n={isError:t,message:e.message,stack:e.stack}),[n,[]]},deserialize(e){if(e.isError)throw Object.assign(new Error,e);throw e}}]]);function s(e,t=self){t.addEventListener("message",async n=>{if(!n||!n.data)return;const{id:a,type:o,path:i}={path:[],...n.data},c=(n.data.argumentList||[]).map(l);let f;try{const t=i.slice(0,-1).reduce((e,t)=>e[t],e),a=i.reduce((e,t)=>e[t],e);switch(o){case 0:f=await a;break;case 1:t[i.slice(-1)[0]]=l(n.data.value),f=!0;break;case 2:f=await a.apply(t,c);break;case 3:f=u(await new a(...c));break;case 4:{const{port1:t,port2:n}=new MessageChannel;s(e,n),f=p(t,[t])}break;default:console.warn("Unrecognized message",n.data)}}catch(e){f=e,r.add(e)}const[m,g]=d(f);t.postMessage({...m,id:a},g)}),t.start&&t.start()}function o(e){return function e(t,r=[]){const a=new Proxy(new Function,{get(n,s){if("then"===s){if(0===r.length)return{then:()=>a};const e=f(t,{type:0,path:r.map(e=>e.toString())}).then(l);return e.then.bind(e)}return e(t,[...r,s])},set(e,n,a){const[s,o]=d(a);return f(t,{type:1,path:[...r,n].map(e=>e.toString()),value:s},o).then(l)},apply(a,s,o){const c=r[r.length-1];if(c===n)return f(t,{type:4}).then(l);if("bind"===c)return e(t,r.slice(0,-1));const[p,u]=i(o);return f(t,{type:2,path:r.map(e=>e.toString()),argumentList:p},u).then(l)},construct(e,n){const[a,s]=i(n);return f(t,{type:3,path:r.map(e=>e.toString()),argumentList:a},s).then(l)}});return a}(e)}function i(e){const t=e.map(d);return[t.map(e=>e[0]),(n=t.map(e=>e[1]),Array.prototype.concat.apply([],n))];var n}const c=new WeakMap;function p(e,t){return c.set(e,t),e}function u(e){return Object.assign(e,{[t]:!0})}function d(e){for(const[t,n]of a)if(n.canHandle(e)){const[r,a]=n.serialize(e);return[{type:3,name:t,value:r},a]}return[{type:0,value:e},c.get(e)||[]]}function l(e){switch(e.type){case 3:return a.get(e.name).deserialize(e.value);case 0:return e.value}}function f(e,t,n){return new Promise(r=>{const a=new Array(4).fill(0).map(()=>Math.floor(Math.random()*Number.MAX_SAFE_INTEGER).toString(16)).join("-");e.addEventListener("message",function t(n){n.data&&n.data.id&&n.data.id===a&&(e.removeEventListener("message",t),r(n.data))}),e.start&&e.start(),e.postMessage({id:a,...t},n)})}e.proxyMarker=t,e.createEndpoint=n,e.transferHandlers=a,e.expose=s,e.wrap=o,e.transfer=p,e.proxy=u,e.windowEndpoint=function(e,t=self){return{postMessage:(t,n)=>e.postMessage(t,"*",n),addEventListener:t.addEventListener.bind(t),removeEventListener:t.removeEventListener.bind(t)}},Object.defineProperty(e,"__esModule",{value:!0})});
//# sourceMappingURL=comlink.min.js.map
