window.showShare = function(projectid, modelid, token, event, ) {
        console.log(projectid, modelid, event);

        let viewpointId = event.viewpointId;
        let bimcomposerId = window.projectbootvue.$staticmethod._Get("bimcomposerId");
        let shareUrl = `${window.location.origin}/BIMComposer/index.html?projectId=${bimcomposerId}&model=${modelid}&viewpointId=${viewpointId}`;
        let encodedshareUrl = encodeURIComponent(shareUrl);
        let codeImgshareUrl = `${window.bim_config.webserverurl}/api/Tool/File/QRCode?encodedUrl=${encodedshareUrl}&token=${token}`

        window.projectbootvue.viewpointShareTitle = event.viewpointName; //  分享title
        window.projectbootvue.shareImg = codeImgshareUrl; // 分享图片
        window.projectbootvue.viewpointShareUrl = shareUrl; // 分享链接路径
        window.projectbootvue.showViewpointShare = true; // 是否显示
    };
    const regForEmptyStr = /^\s*$/i
    window.tool = {
        // 判断val是否是空的：undefined、null、NaN、全空白的字符串都判定为空，其余的则反回false
        GetValIsEmpty(val) {
            const valType = typeof val
            switch (valType) {
                case 'undefined': {
                    return true
                }
                case 'object': {
                    return val === null ? true : false
                }
                case 'number': {
                    return isNaN(val) ? true : false
                }
                case 'string': {
                    return regForEmptyStr.test(val)
                }
                default: {
                    return false
                }
            }
        },

        // 计算 Pano 的浏览地址，并传入相关参数
        // ----------------------------------
        getPanoUrl(pbStrPart, organizeId, isDis) {
            var url = `${window.bim_config.webserverurl}/Panorama${pbStrPart}/vtour/tour2.html?organizeId=${
            organizeId}&isDis=${isDis}&panoapiurl=${encodeURIComponent(window.bim_config.webserverurl)}&gisurl=${
                encodeURIComponent(window.bim_config.webserverurl_gis)
            }`;
            return url;
        },

        // withDevmode 为 false 时，只需要参数：wfs_guid 及 currentNodeId
        // -------------------------------------------------------------
        getFlowUrl(withDevmode, devmode, wfs_guid, currentNodeId, wft_guid, wftp_guid) {
            console.log('getFlowUrl has been called!');
            var url;
            if (withDevmode) {
                url = `${window.bim_config.integrated_innerview}/Addons/Module_flow/index.html?devmode=${
                devmode
            }&wfs_guid=${wfs_guid}&currentNodeId=${currentNodeId||''}&wft_guid=${wft_guid || ''
            }&wftp_guid=${wftp_guid || ''}`;
            } else {
                // `${window.bim_config.integrated_innerview}/Addons/Module_Flow/index.html?wfs_guid=${wfs_guid}`
                // `${window.bim_config.integrated_innerview}/Addons/Module_Flow/index.html?wfs_guid=${item.wfs_guid}&currentNodeId=${curnodeId}`
                url = `${window.bim_config.integrated_innerview}/Addons/Module_flow/index.html?wfs_guid=${wfs_guid
                }&currentNodeId=${currentNodeId||''}`;

            }
            url += `&webserverurl=${encodeURIComponent(window.bim_config.webserverurl)}`;
            return url;
        },

        getFormUrl(innerviewurl, wf_guid, wfi_guid, nodeId, isInstance) {
            console.log('getFormUrl has been called!');
            var urltoret = `${innerviewurl
        }/Addons/Module_Form/index.html?wf_guid=${wf_guid
        }&isInstance=${isInstance||''
        }&wfi_guid=${wfi_guid
        }&nodeId=${nodeId}&webserverurl=${encodeURIComponent(window.bim_config.webserverurl)}`;
            return urltoret;
        },

        // 原 bim 相关接口，直接返回 bimserverurl
        // ------------------------------------
        getBIMServer() {
            var url = window.bim_config.bimserverurl;
            return url;
        },

        // 原文档相关接口（web直接调用的，非newAPI中转的）
        // ---改为使用 getDocServer() 作为地址头
        // -----------------------------------
        getDocServer() {
            var url = window.bim_config.docserverurl || window.bim_config.bimserverurl;
            return url;
        },

        // 参数修正
        // 移除 #/zh-CN/ 部分
        formhelper_processpara(oldval) {
            if (oldval) {
                oldval = oldval.replace(/#\/zh-CN\//g, '');
                return oldval;
            } else {
                return oldval;
            }
        },

        // getElementPosition
        // ------------------
        bimhelper_getElementPosition(thewin, eleid) {
            if (thewin.BIMe) {
                return thewin.BIMe.control.BIMeUtility.getElementPosition(eleid);
            } else {
                return thewin.model.BIM365API.Selector.getElementByElementID([eleid])[0].getElementPosition();
            }
        },

        // mainViewpoint 方法
        // ------------------
        bimhelper_mainViewpoint(thewin) {
            if (thewin.BIMe) {
                thewin.BIMe.control.BIMeUtility.mainViewpoint();
            } else {
                console.error(`暂不支持 mainViewpoint`);
            }
        },

        // isShowMultipleSelection 方法
        // ----------------------
        bimhelper_isShowMultipleSelection(thewin, val) {
            if (thewin.BIMe) {
                thewin.BIMe.view.BIMeSelection.isShowMultipleSelection(val);
            } else {
                console.error('暂不支持 isShowMultipleSelection');
            }
        },

        // addRightMenu 方法
        // -----------------
        bimhelper_addRightMenu(thewin, para) {
            if (thewin.BIMe) {
                thewin.BIMe.control.BIMeUtility.addRightMenu(para);
            } else {
                console.error('暂不支持 addRightMenu');
            }
        },

        // 拿到可调用锚点方法的对象
        // clearAllAnchorpoint: this.$staticmethod.bimhelper_point(modelIframe).clearAllAnchorpoint();
        // addAnchorPointByPosition: _this.$staticmethod.bimhelper_point(modelIframe).addAnchorPointByPosition(...
        // ----------------------
        bimhelper_point(thewin) {
            if (thewin.BIMe) {
                return thewin.BIMe.control.BIMeUtility;
            } else {
                return thewin.model.BIM365API.Extension.Point;
            }
        },

        // 调用 removeAllSelectorElements 或 removeAllHighlightElements
        // ------------------------------------------------------------
        bimhelper_callcancelhighlight(cancelhighobj) {
            if (cancelhighobj.removeAllSelectorElements) {
                cancelhighobj.removeAllSelectorElements();
            } else {
                cancelhighobj.removeAllHighlightElements();
            }
        },

        // 拿到可调用清空选择的方法的对象
        // 旧: removeAllSelectorElements
        // 新：removeAllHighlightElements
        // ---------------------------
        bimhelper_cancelhighlight(thewin) {
            if (thewin.BIMe) {
                return thewin.BIMe.control.BIMeSelector
            } else {
                return thewin.model.BIM365API.Controller;
            }
        },

        // 调用高亮
        // var highlightobj = _this.$staticmethod.bimhelper_highlight(ifrbimviewer.contentWindow);
        // _this.$staticmethod.bimhelper_callhighlight(highlightobj, modelid_eles);
        // -------------------------------------
        bimhelper_callhighlight(obj, para) {
            if (obj.highlightElementByElementId) {
                obj.highlightElementByElementId(para);
            } else {
                obj.selectorElementByElementId(para);
            }
        },

        // 甩出用于调用高亮构件方法的对象
        // 旧方法 selectorElementByElementId
        // 新方法 highlightElementByElementId
        // ----------------------------------
        bimhelper_highlight(thewin) {
            if (thewin.BIMe) {
                return thewin.BIMe.control.BIMeSelector;
            } else {
                return thewin.model.BIM365API.Controller;
            }
        },

        // getIsolate Utility，或许可以也调用其它函数
        // 可调用：
        // isolateElementByElementId
        // removeAllIsolateElement: _this.$staticmethod.bimhelper_getIsolateUtility(playModelView).removeAllIsolateElement();
        // --------------------------------------
        bimhelper_getIsolateUtility(thewin) {
            if (thewin.BIMe) {
                return thewin.BIMe.control.BIMeIsolate;
            } else {
                return thewin.model.BIM365API.Controller;
            }
        },

        // getColor Utility，或许可以也调用其它函数
        // 可调用：
        // resetElementColor: _this.$staticmethod.bimhelper_getColorUtility(playModelView).resetElementColor(_this.setColorData.planElement);
        // setElementColor: _this.$staticmethod.bimhelper_getColorUtility(playModelView).setElementColor(_this.setColorData.planElement,0,255,0,0.5);
        // --------------------------------------
        bimhelper_getColorUtility(thewin) {
            if (thewin.BIMe) {
                return thewin.BIMe.control.BIMeUtility;
            } else {
                return thewin.model.BIM365API.Controller;
            }
        },

        // onSelect
        // --------
        bimhelper_onSelect(thewin, callback) {
            if (thewin.BIMe) {
                thewin.BIMe.control.BIMeSelector.selectorCallBack(callback);
            } else {
                thewin.model.BIM365API.Events.selectorEvent.on("default", callback);
            }
        },

        // cancleSelectEvent
        // -----------------
        bimhelper_cancelSelect(thewin, callback) {
            if (thewin.BIMe) {
                thewin.BIMe.control.BIMeSelector.BIMeEvent.cancleSelectEvent.subscribe(callback);
            } else {
                thewin.model.BIM365API.Events.cancleSelectEvent.on('default', callback);
            }
        },

        // 获取可调用 zoomElementByElementId 的对象
        // ---------------------------------------
        bimhelper_getzoomer(thewin) {
            if (thewin.BIMe) {
                return thewin.BIMe.control.BIMeZoom;
            } else {
                return thewin.model.BIM365API.Controller;
            }
        },

        // 返回 可调用 finishRender 的对象
        // finishRender: _this.$staticmethod.bimhelper_finishrender(iframeWindow,<lambda>);
        // ------------------------------
        bimhelper_finishrender(thewin, callback) {
            if (thewin.BIMe) {
                thewin.BIMe.event.BIMeEvent.finishRender.subscribe(callback);
            } else {
                thewin.model.BIM365API.Events.finishRender.on('default', callback);
            }
        },

        // 直接返回截屏的方法后的 Promise
        // --------------------
        bimhelper_clipmethod(thewin) {
            if (thewin.BIMe) {
                return thewin.BIMe.control.BIMeUtility.getPicture();
            } else {
                return thewin.model.BIM365API.Context.asynCapturePicture();
            }
        },

        // 通过不同版本的 bimviewer 的 window，获取可以调用 getViewPointBasicInfo 函数的对象
        // setViewPointBasicInfo: this.$staticmethod.bimhelper_getview(win).getViewPointBasicInfo(JSON.parse(xx));
        // ------------------------------------------------------------------------------
        bimhelper_getview(thewin) {
            if (thewin.BIMe) {
                return thewin.BIMe.view.BIMeViewpoint;
            } else if (thewin.model) {
                return thewin.model.BIM365API.Context;
            }
        },

        // 计算在线预览地址
        // ---------------
        computeViewUrl(filedownloadurl, filename) {

            // 如果是 modelapi 的下载地址
            if (window.bim_config.inner_docserverurl && filedownloadurl.indexOf(window.tool.getDocServer()) >= 0) {
                // 换成内网的
                filedownloadurl = filedownloadurl.replace(window.tool.getDocServer(), window.bim_config.inner_docserverurl);
            }

            if (window.bim_config.inner_bimserverurl && filedownloadurl.indexOf(window.tool.getBIMServer()) >= 0) {
                // 换成内网的
                filedownloadurl = filedownloadurl.replace(window.tool.getBIMServer(), window.bim_config.inner_bimserverurl);
            }

            var url_subview = `${window.bim_config.webserverurl}/api/DocViewer/Viewer/Show?downloadUrl=${encodeURIComponent(filedownloadurl)}&tempNameAfterDownload=${filename}`;
            var url_view = `${window.bim_config.webserverurl}/Content/PDFJS/web/viewer.html?file=${encodeURIComponent(url_subview)}`;
            return url_view;
        },

        getHuangNewcomputeViewUrl(filedownloadurl, filename, fileExtension) {
            if (fileExtension) {
                fileExtension = fileExtension.toLowerCase()
            }

            // 如果是 modelapi 的下载地址
            if (window.bim_config.inner_docserverurl && filedownloadurl.indexOf(window.tool.getDocServer()) >= 0) {
                // 换成内网的
                filedownloadurl = filedownloadurl.replace(window.tool.getDocServer(), window.bim_config.inner_docserverurl);
            }

            if (window.bim_config.inner_bimserverurl && filedownloadurl.indexOf(window.tool.getBIMServer()) >= 0) {
                // 换成内网的
                filedownloadurl = filedownloadurl.replace(window.tool.getBIMServer(), window.bim_config.inner_bimserverurl);
            }
            var url_subview = filedownloadurl
            var url_view = `${window.bim_config.webserverurl}/Content/PDFJS/web/viewer.html?file=${encodeURIComponent(url_subview)}|${fileExtension}`;
            return url_view;
        },

        // 正则验证邮箱
        // -----------
        validEmail(email) {
            return /^[_\-\w\d\.]+@[\w\d]+\.[\w\d]+/.test(email);
        },

        // 获取所有的消息类型
        // ----------------
        getMsgTypesObj() {
            var typesObj = {
                Issue_Create: 4,
                Issue_Modify: 5,
                Issue_Delete: 6,
                Issue_Comment_Create: 7,
                Issue_Comment_Delete: 8,
                Issue_Doc_Create: 9,
                Issue_Doc_Delete: 10,

                Doc_NewFile: 21,
                Doc_RemoveFile: 22,
                Doc_ModifyFile: 23,
                Doc_MoveFile: 24,
                Doc_NewDir: 27,

                Web_Notify: 50,

                Flow_Submit: 47,
                Flow_Reject: 48,
                Flow_Examine: 49,

                Issue_Comment_Create_At: 1007
            };
            return typesObj;
        },

        dtToString(dt) {
            if (!dt || dt == '') {
                return '';
            }
            var year = dt.getFullYear();
            var month = dt.getMonth() + 1;
            var date = dt.getDate();
            var h = dt.getHours();
            if (h < 10) {
                h = '0' + h;
            }
            var m = dt.getMinutes();
            if (m < 10) {
                m = '0' + m;
            }
            var s = dt.getSeconds();
            if (s < 10) {
                s = '0' + s;
            }
            return `${year}-${month}-${date} ${h}:${m}:${s}`;

        },
        dtToString3(dt) {
            if (!dt || dt == '') {
                return '';
            }
            var year = dt.getFullYear();
            var month = dt.getMonth() + 1;
            var date = dt.getDate();
            var h = dt.getHours();
            if (h < 10) {
                h = '0' + h;
            }
            var m = dt.getMinutes();
            if (m < 10) {
                m = '0' + m;
            }
            var s = dt.getSeconds();
            if (s < 10) {
                s = '0' + s;
            }
            return `${year}-${month}-${date}`;

        },

        dtToString2(dt) {
            if (!dt || dt == '') {
                return '';
            }
            var year = dt.getFullYear();
            var month = dt.getMonth() + 1;
            var date = dt.getDate();

            //
            if (month < 10) {
                month = '0' + month;
            }
            if (date < 10) {
                date = '0' + date;
            }

            var h = dt.getHours();
            if (h < 10) {
                h = '0' + h;
            }
            var m = dt.getMinutes();
            if (m < 10) {
                m = '0' + m;
            }
            var s = dt.getSeconds();
            if (s < 10) {
                s = '0' + s;
            }
            return `${year}-${month}-${date} ${h}:${m}:${s}`;

        },

        // 判断扩展名是否为图片
        testFileExtensionsIsImage(extname) {
            var arr = [];
            var extname_tolower = extname.toString().toLowerCase();
            return arr.indexOf(extname_tolower) >= 0;
        },

        GetWSUrl() {
            var _wsurl = window.bim_config.webserverurl.replace("http", "ws") +
                "/api/WS/Message/Get";
            return _wsurl;
        },

        // 对称加密算法
        // json.input   输入的明文
        // json.key     加密密钥
        // return value 正常时返回：加密后的密码，异常时返回-1
        SymmetricEncryption(json, CryptoJS) {

            // parameter tests
            if (!json || !json.input || !json.key) {
                console.warn("json, json.input and json.key are not allow null!");
                return -1;
            }

            // 由于 key 小于16位会有问题，需要额外处理 key
            if (json.key.length == 0) {
                console.warn("json.length should not be zero!");
                return -1;
            }
            while (json.key.length < 16) {
                json.key = json.key + json.key;
            }
            // //由于 key 小于16位会有问题，需要额外处理 key

            //var CryptoJS = Vue.prototype.$CryptoJS;

            var KEY = json.key; //32位
            var IV = "*BIM19FF4KMY0R8*"; //16位
            var key = CryptoJS.enc.Utf8.parse(KEY);
            var iv = CryptoJS.enc.Utf8.parse(IV);

            var encrypted = "";

            var srcs = CryptoJS.enc.Utf8.parse(json.input);
            encrypted = CryptoJS.AES.encrypt(srcs, key, {
                iv: iv,
                mode: CryptoJS.mode.CBC,
                padding: CryptoJS.pad.Pkcs7
            });

            return encrypted.ciphertext.toString(); //5c1b2248cb4ba581472afab0702518cc
        },
        // //对称加密算法

        computepwdstr(pwdorigin, _this_CryptoJS) {
            var that = this;
            var afterpwd = that.SymmetricEncryption({
                input: pwdorigin,
                key: window.bim_config.publickey
            }, _this_CryptoJS);
            return afterpwd;
        },

        debug(msg) {},

        // bim_config.showlog 为 true 时，打印调试信息
        // -----------------------------------------
        consoleLog() {
            if (window.bim_config.showlog == true) {
                console.log(arguments);
            }
        },

        // bim_config.showlog 为 true 时，打印调试信息
        // -----------------------------------------
        consoleWarn() {
            if (window.bim_config.showlog == true) {
                console.warn(arguments);
            }
        },

        // bim_config.showlog 为 true 时，打印调试信息
        // -----------------------------------------
        consoleError() {
            if (window.bim_config.showlog == true) {
                console.error(arguments);
            }
        },

        // 添加try catch 的处理
        // -------------------
        trythat(dofunc, exfunc) {
            try {
                if (dofunc) {
                    dofunc();
                }
            } catch (e) {
                if (exfunc) {
                    exfunc(e);
                }
            }
        },

        // bim_config.debug_showlog 为 true 时，打印调试信息
        debugshowlog(msg) {
            // if (window.bim_config.debug_showlog == true) {
            //     console.log(`DEBUGLOG: ${msg}`);
            // }
        },

        // 生成guid
        // --------
        S4() {     
            return (((1 + Math.random()) * 0x10000) | 0).toString(16).substring(1);   
        },
        NewGuid() {
            var _this = this;     
            return (_this.S4() + _this.S4() + "-" + _this.S4() + "-" + _this.S4() + "-" + _this.S4() + "-" + _this.S4() + _this.S4() + _this.S4());   
        },

        // 通过尝试的新文件名，旧扩展名得到推荐的带扩展名的文件名
        giveupnewext(trynewextfullname, oldextname) {
            var _lastindexofdot = -1;
            if (!trynewextfullname || trynewextfullname.length == 0) {
                return `.${oldextname}`; // .docx
            }
            _lastindexofdot = trynewextfullname.lastIndexOf(".");
            if (_lastindexofdot == -1) {
                return `${trynewextfullname}.${oldextname}`; // abc.docx
            } else {
                var _trynewnamewithoutextname = trynewextfullname.substr(0, _lastindexofdot);
                return `${_trynewnamewithoutextname}.${oldextname}`
            }

        },

        Unique(arr, equalcallback) {
            var _retarr = [];
            for (var i = 0; i < arr.length; i++) {
                var ifhas = _retarr.filter(x => (equalcallback(x)) == (equalcallback(arr[i])));
                if (ifhas.length == 0) {
                    _retarr.push(arr[i]);
                }
            }
            return _retarr;
        },

        DeepCopyDic(dic) {
            var that = this;
            var rdic = [];
            for (var key in dic) {
                rdic[key] = that.DeepCopy(dic[key]);
            }
            return rdic;
        },

        DeepCopy(arr) {
            var str = JSON.stringify(arr);
            return JSON.parse(str);
        },

        ReNameRepeatFile(repeatingfilename) {

            var that = this;

            // 判断有无扩展名，如果没有，直接调用 ReNameRepeat
            var _lastindexofdot = repeatingfilename.lastIndexOf(".");
            if (_lastindexofdot == -1) {
                return that.ReNameRepeat(repeatingfilename);
            }

            // 取出扩展名，缓存之
            var repeatingfilename_withoutext = repeatingfilename.substr(0, _lastindexofdot);
            var _ext = repeatingfilename.substr(_lastindexofdot + 1);
            var getname = that.ReNameRepeat(repeatingfilename_withoutext);
            return `${getname}.${_ext}`;
        },

        // 给一个即将重名的名字进行新命名：abc(1) -> abc(2), abc -> abc(1)
        // repeatingname 正在重名的名字
        ReNameRepeat(repeatingname) {

            // 分别得到左括号和右括号的位置，并进行计算它们之间的位置差
            var lastl = repeatingname.lastIndexOf('(');
            var lastr = repeatingname.lastIndexOf(')');
            // 没有小括号对，或者它们之间不足以存放一个数字
            if (lastl < 0 || lastr < 0 || lastr - lastl < 2) {
                return repeatingname + '(1)';
            }
            // 得到它们之间的数字
            var thenum = repeatingname.substr(lastl + 1, (lastr - lastl + 1 - 2));
            // 如果数字不正确，则直接+'(1)'
            var num = parseInt(thenum);
            if (isNaN(num)) {
                return repeatingname + '(1)';
            }
            // 得到名字体
            var body = repeatingname.substr(0, repeatingname.length - (lastr - lastl + 1));
            return body + '(' + (num + 1) + ')';

        },

        // 浏览器取消全屏
        ExitFullScreen() {
            if (document.exitFullscreen) {
                document.exitFullscreen();
            } else if (document.mozCancelFullScreen) {
                document.mozCancelFullScreen();
            } else if (document.webkitCancelFullScreen) {
                document.webkitCancelFullScreen();
            } else if (document.msExitFullscreen) {
                document.msExitFullscreen();
            }
            if (typeof cfs != "undefined" && cfs) {
                cfs.call(el);
            }
        },

        // 浏览器全屏
        FullScreen() {
            var el = document.documentElement;
            var rfs = el.requestFullScreen || el.webkitRequestFullScreen || el.mozRequestFullScreen || el.msRequestFullscreen;
            if (typeof rfs != "undefined" && rfs) {
                rfs.call(el);
            };
            return;
        },

        // 内部判断 usesso，并在没有正常的Token驻留在浏览器时，调用此方法。
        // ----------------------------------------------------------
        jumpssomvc() {

            // 未配置时，什么也不做
            // ------------------
            if (!window.bim_config.sso_isusing) {
                return;
            }

            // 跳转到 sso_usingurl 指定的MVC站点
            // ------------------------------
            //console.warn('triggered sso jump...');
            if (!window.bim_config.sso_usingurl || window.bim_config.sso_usingurl == '') {
                console.error('未配置 sso_usingurl');
                return;
            }

            // 跳转
            // ----
            //window.location.href = window.bim_config.sso_usingurl;

        },

        // 进程级数据存储
        Set(key, value) {
            /**/
            localStorage.setItem(key, value);
        },
        Get(key) {
            /**/
            return localStorage.getItem(key);
        },

        RemoveLocalStorage(key) {
            localStorage.removeItem(key);
        },

        // 会话级数据存储
        _Set(key, value) {
            /**/
            sessionStorage.setItem(key, value);
        },
        _Get(key) {
            /**/
            return sessionStorage.getItem(key);
        },

        GetDroppingFiles(items) {

            // 所有文件
            var arr = [];

            // 遍历这些 items，其中可能是文件，也可能是文件夹
            var i = 0;
            for (i = 0; i < items.length; i++) {

                // 判断当前项是文件还是文件夹
                let entry = items[i].webkitGetAsEntry();
                if (entry.isDirectory) {

                    // 读第一层文件夹
                    let reader = entry.createReader();
                    reader.readEntries(function(res) {

                        // 遍历每个res，并判断是文件还是文件夹
                        let j = 0;
                        for (j = 0; j < res.length; j++) {
                            if (res[j].isDirectory) {

                                // 读第二层文件夹
                                let readerj = res[j].createReader();
                                readerj.readEntries(function(resj) {
                                    let _j = 0;
                                    for (_j = 0; _j < resj.length; _j++) {
                                        if (resj[_j].isDirectory) {

                                            let readerk = resj[_j].createReader();
                                            readerk.readEntries(function(resk) {
                                                let _k = 0;
                                                for (_k = 0; _k < resk.length; _k++) {

                                                    if (resk[_k].isDirectory) {
                                                        //debugger;
                                                    } else {
                                                        //debugger;
                                                    }

                                                }
                                            }, null)


                                        } else {

                                        }
                                    }
                                }, null);

                            } else {

                                // 当前是文件
                                // 获取到这个文件
                                // ..
                                res[j].file(x => {
                                    //debugger;
                                }, undefined);

                            }
                        }

                    }, null);
                    console.warn('未处理：文件夹');
                } else {
                    // 是文件
                    let _file = items[i].getAsFile();
                    arr.push(_file);
                }
            }
            return arr;
        },

        // GetAxiosRes_GetBIMComposerId(to, _this_axios) {
        //     var that = this;
        //     if (to.params && to.params.organizeId) {
        //         //if (loc1alStorage.getItem('organizeId') != to.params.organizeId) {
        //         if (that._Get('organizeId') != to.params.organizeId) {
        //             var fullrequrl = `${window.bim_config.webserverurl}/api/User/Project/GetBIMComposerID?organizeId=${to.params.organizeId}`;
        //             var _requrlres = _this_axios.get(fullrequrl);
        //             return _requrlres;
        //         }
        //     }
        //     return;
        // },

        getReturnUrl_If_has_ToMatched(to) {

            if (!to || !to.matched || to.matched.length <= 0) {
                console.error('参数 to 不正确！');
                return;
            }

            // 获取带占位符的目标地址，如："/Home/ProjectBoot/Document/:organizeId?/:Toke1n?"
            var holder_target_url = to.matched[to.matched.length - 1];

            // 遍历 to.params 中的每一个参数，如遇 Tok1en 跳过
            var pathtoencode = holder_target_url.path;
            for (var key in to.params) {
                if (key == 'Token') {
                    continue;
                }
                var val = to.params[key];
                pathtoencode = pathtoencode.replace(`/:${key}?`, `/${to.params[key]}`);
                pathtoencode = pathtoencode.replace(`/:${key}`, `/${to.params[key]}`);
            }
            var retv = `/#${pathtoencode}`;
            return retv;

        },

        makesureend(url) {
            if (!url) {
                return url;
            }
            return url + (url.endsWith('/') ? '' : '/');
        },

        getIconClassByExtname(extname, sizestr, FileExtension) {
            var supportedformat = ['avi', 'bmp', 'doc', 'docx', 'dwg', 'gif', 'jpeg', 'jpg', 'mp3', 'mp4', 'pdf', 'png', 'ppt', 'pptx', 'rar', 'svg', 'txt', 'xls', 'xlsx', 'zip'];
            if (!FileExtension) {
                if (sizestr == '0') {
                    return 'icon-interface-unfolder css-folder';
                } else if (!extname) {
                    return '';
                } else if (extname.indexOf('.') < 0) {
                    return "mulcolor-interface-unknown";
                } else {
                    // 如果有“.”，取出最后面的东西。
                    var onlyextname = extname.replace(/(.)+\.([\w\d]*)/g, "$2");
                    onlyextname = onlyextname.toLowerCase()
                        // 确定已知范围，如果不在范围内，直接返回 "mulcolor-interface-unknown"
                    if (supportedformat.indexOf(onlyextname) < 0) {
                        return "mulcolor-interface-unknown";
                    } else {
                        extname = extname.toLowerCase()
                        var computedclassname = extname.replace(/(.)+\.([\w\d]+)/g, "mulcolor-interface-$2");
                        return computedclassname; // extname.replace(/(.)+\.([\w\d]+)/g, "mulcolor-interface-$2");
                    }
                }
            } else {
                let index = FileExtension.indexOf('.');
                let b = ''
                if (index !== -1) {
                    b = FileExtension.substring(index + 1);
                }
                if (supportedformat.indexOf(b) < 0 && sizestr == '0') {
                    return 'icon-interface-unfolder css-folder';
                } else if (supportedformat.indexOf(b) >= 0) {
                    let computedclassname = 'mulcolor-interface-' + b
                    return computedclassname;
                } else {
                    return "mulcolor-interface-unknown";
                }
            }
        },
        convertToSizeStr(sizeoriginstr) {
            if (!sizeoriginstr) {
                return sizeoriginstr;
            }
            var isize = parseInt(sizeoriginstr);
            if (isNaN(isize)) {
                return sizeoriginstr;
            }

            // isize
            if (isize < 1024) {
                return isize + 'B';
            } else if (isize < 1024 * 1024) {
                return (isize / 1024).toFixed(2) + 'KB';
            } else if (isize < 1024 * 1024 * 1024) {
                return (isize / (1024 * 1024)).toFixed(2) + 'MB';
            } else if (isize < 1024 * 1024 * 1024 * 1024) {
                return (isize / (1024 * 1024 * 1024)).toFixed(2) + 'GB';
            }

        },
        ImgToBase64(fileObj, callback) {
            if (typeof(FileReader) === 'undefined') {
                console.log("抱歉，你的浏览器不支持 FileReader，不能将图片转换为Base64，请使用现代浏览器操作！");
            } else {
                try {
                    var file = fileObj;
                    if (!/image\/\w+/.test(file.type)) {
                        console.log("请确保文件为图像类型");
                        return false;
                    }
                    var reader = new FileReader();
                    reader.onload = function() {
                        callback(this.result);
                    }
                    reader.readAsDataURL(file);
                } catch (e) {
                    console.log('图片转Base64出错啦！' + e.toString())
                }
            }
        },
        getClientHeight() {
            var clientHeight = 0;
            if (document.body.clientHeight && document.documentElement.clientHeight) {
                clientHeight = (document.body.clientHeight < document.documentElement.clientHeight) ? document.body.clientHeight : document.documentElement.clientHeight;
            } else {
                clientHeight = (document.body.clientHeight > document.documentElement.clientHeight) ? document.body.clientHeight : document.documentElement.clientHeight;
            }
            return clientHeight;
        },
        JSONArreyIndexOf(JSONArrey, item) //得到JSON数组中item的序号
        {
            for (let i = 0; i < JSONArrey.length; i++) {
                var equal = true;
                for (let key in JSONArrey[i]) {
                    equal = equal && JSONArrey[i][key] == item[key];
                }
                if (equal)
                    return i;
            }
            return -1;
        },
        // 获取权限 权限存在缓存直接取值
        hasSomeAuth(code) {
            let has_Auth = false;
            let allBtnAuth = JSON.parse(this._Get("menuListHasAuth"));
            let index = allBtnAuth.findIndex(x => x.ButtonCode == code)
            index != -1 ? has_Auth = true : has_Auth = false;
            return has_Auth;
        },

        isObject(target) {
            return Object.prototype.toString.call(target) === '[object Object]'
        },

        /**
         * 广度优先遍历树，并针对每个节点调用handler函数
         * @param {Array} trees 树数组
         * @param {Function} handler 处理函数:接收到的参数为树的节点
         * @param {String} childrenKey 子树的key，默认Children
         * @param {Boolean} returnWhenMatch 当匹配的时候就返回：handler函数的返回值与returnWhenMatch做与运算的结果为true则返回，否则不返回
         */
        walkThroughTreesByBreadthFirst(trees, handler, childrenKey = "Children", returnWhenMatch = true) {
            if (Array.isArray(trees) && trees.length && typeof handler === "function") {
                const queue = trees.slice(0)
                let currentNode, handlerResult
                while (queue.length) {
                    currentNode = queue.shift()
                    handlerResult = handler(currentNode)
                    if (returnWhenMatch && handlerResult) {
                        return currentNode
                    }
                    const children = currentNode[childrenKey]
                    if (children && (Array.isArray(children) || children.length)) {
                        queue.push(...children)
                    }
                }
            }
        },

        /**
         * 深度优先遍历树，并针对每个节点调用handler函数
         * @param {Array} trees 树数组
         * @param {Function} handler 处理函数:接收到的参数为树的节点
         * @param {String} childrenKey 子树的key，默认Children
         * @param {Boolean} returnWhenMatch 当匹配的时候就返回：handler函数的返回值与returnWhenMatch做与运算的结果为true则返回，否则不返回
         */
        walkThroughTreesByDepthFirst(trees, handler, childrenKey = "Children", returnWhenMatch = true) {
            if (Array.isArray(trees) && trees.length && typeof handler === "function") {
                const stack = trees.slice(0)
                let currentNode, handlerResult
                while (stack.length) {
                    currentNode = stack.shift()
                    handlerResult = handler(currentNode)
                    if (returnWhenMatch && handlerResult) {
                        return currentNode
                    }
                    const children = currentNode[childrenKey]
                    if (children && (Array.isArray(children) || children.length)) {
                        stack.unshift(...children)
                    }
                }
            }
        },
    };