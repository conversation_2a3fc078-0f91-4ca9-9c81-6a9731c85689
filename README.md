# probim_newweb

> A Vue.js project

## Build Setup

``` bash
# install dependencies
npm install

# serve with hot reload at localhost:8080
npm run dev

# build for production with minification
npm run build

# build for production and view the bundle analyzer report
npm run build --report
```

For a detailed explanation on how things work, check out the [guide](http://vuejs-templates.github.io/webpack/) and [docs for vue-loader](http://vuejs.github.io/vue-loader).

国产化系统改造
分支信息
online   5010
longyuncopy   6010
water   再生水7310   
analysis2  BIM推演分析项目

平台代码前端打包版本号命名规则：
时间：20××××××+git提交版本号+分支名+当天第几次发布：V×。
例：master分支
npm run build --setVersion=20220101.000000.masterV1