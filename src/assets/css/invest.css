.btn-add {
    margin: 15px 0;
}

.handel-btns {
    display: flex;
}

._css-text-align {
    width: 100%;
    text-align: center;
}

._css-line {
    padding: 0 24px;
    box-sizing: border-box;
    margin: 8px 0 0 0;
    display: flex;
    align-items: center;
}

._css-title-flowname {
    width: 25%;
    text-align: left;
}

._css-fieldvaluename {
    flex: 1;
    height: 36px;
    display: flex;
    align-items: center;
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: 4px;
    box-sizing: border-box;
}

._css-fieldvaluename /deep/ .el-input__inner {
    line-height: 34px;
    height: 34px;
}

._css-fieldvaluename /deep/ .el-date-editor.el-input,
._css-fieldvaluename /deep/ .el-date-editor.el-input__inner {
    width: 60%;
}

._css-fieldvaluename /deep/ .el-input-number .el-input__inner::-webkit-inner-spin-button,
._css-fieldvaluename /deep/ .el-input-number .el-input__inner::-webkit-outer-spin-button {
    -webkit-appearance: none;
    appearance: none;
    margin: 0;
}

._css-selectednode {
    display: flex;
    flex: 1;
}

.invest-table ._css-selectednode .left {
    width: 300px;
}

.invest-table /deep/ .el-table .cell {
    line-height: 40px;
    text-align: center;
}

.invest-table /deep/ .el-table__body-wrapper .cell {
    font-size: 14px;
    font-weight: 400;
}

.invest-table /deep/ .el-table td {
    border-color: #ebeef5;
}

.invest-table .el-table {
    border: 1px solid #ebeef5;
}

.invest-table /deep/ .el-table__body-wrapper .el-table__row {
    height: 44px !important;
    line-height: 44px !important;
}

.padding16 {
    padding-left: 16px;
}

.invest-table /deep/ .el-input-number .el-input__inner {
    text-align: left;
}

.invest-table /deep/ .el-input-number {
    width: 90%;
}

._css-customstyle /deep/ .el-table__body-wrapper,
._css-customstyle /deep/ th,
._css-table-ele,
._css-table-ele /deep/ .el-table th,
._css-treewhole /deep/ .el-tree-node__content,
.css-common-tablehead {
    background: #fff !important;
}

.report-table {
    position: absolute;
    top: 57px;
    left: 20px;
    width: calc(100% - 40px);
    z-index: 10;
}