/* 在线预览相关 */
._css-doc-preview {
    z-index: 70000;
    position: fixed;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    /* background-color: black; */
    background-color: rgba(0, 0, 0, 0.45);
    opacity: 1;
    justify-content: space-around;
  }
  ._css-doc-preview-iframe {
    height: 80%;
    width: 80%;
    border-width: 0;
    background-color: #fff;
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
  }
  ._css-doc-preview-closebtn-,
  ._css-doc-preview-closebtn-office {
    /* flex: none;
    width: 20px;
    height: 20px;
    float: right;
    background-image: url("/Content/images/ProjectManage/Project/close_normal.png");
    background-repeat: no-repeat;
    cursor: pointer;
    color: #fff; */
    font-size: 20px;
    flex: none;
    width: 30px;
    height: 30px;
    position: fixed;
    background-image: url(/Content/images/ProjectManage/Project/close_normal.png);
    background-repeat: no-repeat;
    cursor: pointer;
    color: rgba(0, 0, 0, 0.4);
    border: 1px solid transparent;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.8);
    right: calc(10% - 15px);
    top: calc(10% - 15px);
    line-height: 30px !important;
    text-align: center;
  }
  
  ._css-doc-preview-closebtn-dwg {
    /* flex: none;
    width: 20px;
    height: 20px;
    float: right;
    background-image: url("/Content/images/ProjectManage/Project/close_normal.png");
    background-repeat: no-repeat;
    cursor: pointer;
    color: red; */
    font-size: 20px;
    flex: none;
    width: 30px;
    height: 30px;
    position: fixed;
    background-image: url(/Content/images/ProjectManage/Project/close_normal.png);
    background-repeat: no-repeat;
    cursor: pointer;
    color: rgba(0, 0, 0, 0.4);
    border: 1px solid transparent;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.8);
    right: calc(10% - 15px);
    top: calc(10% - 15px);
    line-height: 30px;
  }
  
  ._css-doc-preview-beforeiframe {
    position: fixed;
    width: 30px;
    height: 40px;
    top: 0;
    right: 35px;
    background-color: transparent;
    display: flex;
    align-items: center;
    font-family: "微软雅黑";
    z-index: 99;
  }
  ._css-doc-preview-beforeiframe-01 {
    flex: none;
    width: 20px;
  }
  ._css-doc-preview-beforeiframe-02 {
    flex: none;
    font-size: 20px;
    font-weight: normal;
    font-stretch: normal;
    letter-spacing: 0px;
    color: #ffffff;
  }
  ._css-doc-preview-beforeiframe-03 {
    flex: 1;
    height: 100%;
  }
  ._css-doc-preview-beforeiframe-04 {
    flex: none;
    width: 25px;
    height: 100%;
  }
  /* //在线预览相关 */