._css-rightbtns-btntext {
    margin-left: 8px;
}

._css-rightbtns-btnicon {
    height: 16px;
    width: 16px;
    margin-left: 24px;
    font-size: 16px;
}

._css-hover-highlight {
    height: 100%;
    display: flex;
    align-items: center;
    line-height: 38px;
}

._css-hover-highlight:not(.css-dis):hover {
    background-color: rgba(0, 0, 0, 0.04);
}

._css-menuitemstyle {
    height: 40px;
    font-size: 14px;
    color: rgba(0, 0, 0, 0.85);
}

._css-menuitemstyle._css-hasbottom {
    padding-bottom: 4px;
    margin-bottom: 4px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.15);
}

._css-contextMenuContainer {
    background: rgba(255, 255, 255, 1);
    box-shadow: 0px 0px 8px 0px rgba(0, 0, 0, 0.15);
    border-radius: 4px;
    width: 160px;
}

._css-alwaysfollowfname {
    margin-left: 8px;
}

._css-modelcp {
    cursor: pointer;
}

.mv {
    position: absolute;
    top: 0px;
    left: 0px;
    z-index: 1001;
    background-color: #fff;
}

.mi {
    width: 869px;
    height: 420px;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 97;
}

._css-backpfolder,
._css-modelcp:hover {
    color: #1890ff;
}

._css-basicfolder,
._css-parentsfolder {
    color: rgba(0, 0, 0, 0.65);
}

._css-parentsfolder:hover,
._css-basicfolder:hover {
    color: #1890ff;
}

._css-currentfolder {
    cursor: default;
    color: rgb(0, 0, 0);
}

._css-btn-docviewtype {
    width: 56px;
    height: 40px;
}

._css-btnoperlist {
    margin-left: 24px;
}

._css-doc-patcher1 {
    flex: 1;
}

._css-doc-patcher2 {
    height: 40px;
}

._css-uploadqueueitembtn {
    height: 20px;
    border: 1px solid #1890ff;
    line-height: 20px;
    width: 35px;
    color: #1890ff;
    text-align: center;
    cursor: pointer;
    border-radius: 4px;
}

._css-uploadqueueitembtn:hover {
    background-color: #1890ff;
    color: #fff;
}

._css-uploadqueueinnerminwidth {
    min-width: 671px;
}

#id_datamain {
    width: calc(100% - 240px);
}

.css-hoverunder {
    font-size: 14px;
}

._css-docleftfuncitems {
    background-color: rgba(0, 0, 0, 0.02);
    width: 240px;
}
.padding-10{
  background: white;
  padding: 10px;
}
.summary-table{
  background: white;
}
._css-icon60 {
    background-size: 60px;
}

._css-doc-body-in-left {
    border-right: 1px solid transparent;
    padding-top: 10px;
    display: flex;
    flex-direction: column;
}

._css-newdirname-input {
    height: 24px;
    border-radius: 2px;
    border: 1px solid rgba(0, 0, 0, 0.15);
    outline: none;
    color: rgba(0, 0, 0, 0.45);
}

._css-newdir-ok {
    width: 53px;
    height: 24px;
    border-radius: 2px;
}

._css-newdir-cancel {
    width: 53px;
    height: 24px;
    border-radius: 2px;
}

._css-newdir-name {
    height: 24px;
    border-radius: 2px;
    border: 1px solid rgba(0, 0, 0, 0.15);
    width: calc(100% - 140px);
    font-size: 14px;
    font-weight: 400;
    color: rgba(0, 0, 0, 1);
    padding: 0 12px 0 12px;
    outline: none;
}

._css-pointhead {
    width: 6px;
    height: 6px;
    border-radius: 50%;
}

._css-filebtnlist {
    z-index: 1;
    top: calc(100% + 4px);
    left: 0;
    width: 120px;
    color: rgba(0, 0, 0, 0.65);
    background: rgba(255, 255, 255, 1);
    box-shadow: 0px 0px 8px 0px rgba(0, 0, 0, 0.15);
    border-radius: 4px;
}

._css-filebtnlist-none,
._css-fileitem-contextbtns {
    display: none;
}

._css-fileitem-contentbtn {
    color: #1890ff;
}

._css-fileitem-contentbtn2 {
    color: #faad14;
}

._css-fileitem-contentbtn:hover {
    opacity: 0.7;
}

.cell:hover>._css-fileitem-contextbtns,
._css-fileitem-contextstatus {
    display: flex;
}

.cell:hover>._css-fileitem-contextstatus {
    display: none;
}


/* //订阅状态相关 */

._css-doc-list-item {
    width: 100px;
    height: 130px;
    margin-left: 24px;
    margin-top: 24px;
    display: flex;
    flex-direction: column;
    align-items: center;
    font-size: 12px;
    cursor: pointer;
}

._css-doc-list-item-namectn {
    display: flex;
    justify-content: center;
    align-items: flex-end;
}

._css-doc-list-item-name {
    width: 73px;
    height: 20px;
    color: rgba(0, 0, 0, 0.45);
    border-radius: 4px;
    border: 1px solid transparent;
    bottom: 0;
    padding-left: 2px;
    padding-right: 2px;
}

._css-doc-list-item:not(.css-nohover):hover ._css-doc-list-item-name {
    background: rgba(16, 142, 233, 1);
    border-radius: 4px;
    border: 1px solid rgba(255, 255, 255, 1);
    color: #fff;
}

._css-doc-list-item-icon {
    width: 98px;
    height: 98px;
    background-color: rgba(0, 0, 0, 0.02);
    box-shadow: 0px 1px 1px 0px rgba(0, 21, 41, 0.12);
    border-radius: 2px;
}

th {
    border-bottom: 1px solid rgba(0, 0, 0, 0.08);
}

.el-tree {
    flex: 1;
}

.toparea {
    height: 114px;
}

._css-table-ele {
    height: 100% !important;
}

._css-keyword-col {
    max-width: 370px;
}

._css-keyword-input {
    flex: 1;
    border-top: 1px solid rgb(220, 223, 230);
    border-left: 1px solid rgb(220, 223, 230);
    border-bottom: 1px solid rgb(220, 223, 230);
    border: 1px solid rgb(220, 223, 230);
    padding-left: 20px;
    padding-right: 20px;
    border-radius: 4px;
}

._css-keyword-btn {
    font-size: 16px;
    width: 70px;
    height: 32px;
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
}

._css-searchmember-btn {
    font-size: 14px;
    width: 70px;
    height: 32px;
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
}

._css-bgcolor004 {
    background-color: rgba(0, 0, 0, 0.04) !important;
}

._css-doc-top {
    height: 54px;
}

._css-doc-body {
    height: calc(100% - 54px);
}

._css-child-menui:not(._css-doc-patcher1):hover,
._css-child-menui.clicked {
    cursor: pointer;
    background-color: rgba(0, 0, 0, 0.04);
    color: rgba(0, 0, 0, 0.65);
}

._css-pispublic {
    text-align: left;
}

._css-pispublictext {
    text-align: left;
}

._css-uploadqueue {
    transition: all 0.5s;
    opacity: 1;
    z-index: 1;
}

._css-hide {
    transition: all 0.5s;
    opacity: 0;
    z-index: 0;
}

._css-uploadqueue_inner {
    background: rgba(255, 255, 255, 1);
    box-shadow: 1px 0px 1px 0px rgba(0, 21, 41, 0.12);
    border-radius: 2px;
}

._css-table-top-right {
    text-align: right;
    min-width: 480px;
}

._css-table-top-btngrouptip {
    /* height: 32px; */
    box-sizing: border-box;
    width: 228px;
    background-color: #f5f7fa;
    font-size: 12px;
}

._css-table-top {
    height: 54px;
    box-sizing: border-box;
}

._css-table-body {
    height: calc(100% - 54px);
}

._css-progress-outer {
    border-radius: 16px;
}

h1,
h2 {
    font-weight: normal;
}

ul {
    list-style-type: none;
    padding: 0;
}

li {
    display: inline-block;
    margin: 0 10px;
}

.el-row:last-child {
    margin-bottom: 0;
}

.el-col {
    border-radius: 4px;
}

.grid-content {
    border-radius: 4px;
    min-height: 36px;
}

.row-bg {
    padding: 10px 0;
    background-color: #f9fafc;
}


/* 文档数 */

._css-doccount {
    height: 22px;
    margin-top: 3px;
    text-align: right;
    font-size: 14px;
    font-weight: 400;
    color: rgba(0, 0, 0, 1);
    line-height: 100%;
}


/* //文档数 */


/* 文档操作按钮及搜索框 */

._css-search-operators {
    height: 40px;
}

._css-btn-upload {
    width: 120px;
    height: 40px;
}


/* //文档操作按钮及搜索框 */

._css-keyword {
    width: 100% !important;
}

._css-docqueue-close {
    transition: height 0.5s;
    height: 0;
}

._css-h-48 {
    transition: height 0.5s;
    height: calc(350px - 48px);
}

._css-queuebtn-text {
    width: calc(100% - 90px);
    height: 100%;
    display: flex;
    justify-content: space-around;
    align-items: center;
}

._css-progress-pointhead-full {
    background-color: rgba(24, 144, 255, 1);
}

._css-progress-pointhead-running {
    background-color: rgba(24, 144, 255, 1);
}

._css-progress-running-hover {
    background-color: rgb(0, 0, 0);
    opacity: 0.04;
    top: 0;
}

._css-progress-font-full {
    color: #1890ff;
}

._css-progress-back-full {
    background-color: rgba(230, 247, 255, 1);
}

._css-progress-font-running {
    font-size: 12px;
    color: #1890ff;
}

._css-progress-back-running {
    background-color: rgba(230, 247, 255, 1);
}

._css-proappend-font-err {
    color: rgba(245, 34, 45, 1);
}

._css-proappend-back-err {
    background-color: rgba(245, 34, 45, 0.1);
}

._css-proappend-pointhead-err {
    background-color: rgba(245, 34, 45, 1);
}

._css-sharingmain {
    width: 750px;
    border-radius: 4px;
    border: 1px solid rgba(0, 0, 0, 0.15);
    background-color: #fff;
}

._css-sharingauth {
    width: 600px;
    border-radius: 4px;
    border: 1px solid rgba(0, 0, 0, 0.15);
    background-color: #fff;
    user-select: none;
}

._css-sharingmember {
    width: 600px;
    border-radius: 4px;
    border: 1px solid rgba(0, 0, 0, 0.15);
    background-color: #fff;
}

._css-rolemember-dataarea {
    height: 250px;
    border-radius: 4px;
    border: 1px solid rgba(0, 0, 0, 0.15);
}

._css-middlesplit-area {
    border-radius: 4px;
    border: 1px solid rgba(0, 0, 0, 0.15);
}

._css-rolelistarea {
    width: 218px;
    border-right: 4px solid rgba(0, 0, 0, 0.02);
    box-sizing: border-box;
}

._css-memberlistarea {
    width: calc(100% - 218px);
}

._css-rolelistarea-in {
    padding: 12px 16px 16px 16px;
}

._css-roleitem,
._css-useritem {
    height: 24px;
}

._css-member-selected {
    height: 144px;
    border-radius: 4px;
    border: 1px solid rgba(0, 0, 0, 0.15);
    background: rgba(255, 255, 255, 1);
    display: flex;
    flex-wrap: wrap;
    align-content: flex-start;
    justify-content: flex-start;
    overflow-y: auto;
}

._css-member-selected-i {
    min-width: 155px;
    max-width: 176px;
    border: 1px solid rgba(0, 0, 0, 0.2);
}

._css-member-selected-i:hover {
    background-color: rgba(230, 247, 255, 1);
}

._css-memberemail {
    color: rgba(0, 0, 0, 0.25);
}

._css-description {
    color: rgba(0, 0, 0, 0.25);
}

._css-sharing-mode-title {
    width: 72px;
}

._css-sharing-mode-option {
    width: 120px;
}

._css-sharing-main-content {
    padding-left: 45px;
    padding-right: 45px;
    display: flex;
}

._css-sharing-main-email {
    padding-left: 85px;
    padding-right: 85px;
    margin-top: 53px;
    border-top: 1px dashed rgba(217, 217, 217, 1);
}

._css-sharing-main-qrcode {
    width: 150px;
    height: 150px;
}

._css-sharing-main-link {
    padding: 1px 0 1px 30px;
    color: rgba(0, 0, 0, 0.65);
}

._css-sharing-main-link-copy {
    border-radius: 4px;
    height: 32px;
    border: 1px solid rgba(24, 144, 255, 1);
    box-sizing: border-box;
    padding: 5px 16px 5px 16px;
}

._css-sharing-main-link-input {
    outline: none;
    border: none;
    color: rgba(0, 0, 0, 0.65);
}

._css-sharing-email-content {
    width: 430px;
    height: 111px;
    border-radius: 4px;
    border: 1px solid rgba(0, 0, 0, 0.15);
    margin-top: 16px;
}

._css-sharing-email-btncontainer {
    padding-right: 85px;
}

._css-sharing-email-content-in {
    border: none;
    box-sizing: border-box;
    resize: none;
    outline: none;
    border-radius: 4px;
    padding: 8px 16px;
    font-size: 14px;
}

._css-sharingauth-edit {
    color: #1890ff;
    cursor: pointer;
}

._css-sharingauth-edit:hover {
    text-decoration: underline;
}

.model-detail-iframe {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: #fff;
    width: 100%;
    height: 100%;
    z-index: 9999;
}

.cell:hover .css-ellipsis,
.cell:hover>._css-trbtn-container {
    color: #1890ff;
}

._css-trbtn-container {
    flex: 1;
    height: 100%;
    display: none;
    flex-direction: row-reverse;
    cursor: pointer;
}

.cell:hover>._css-trbtn-container {
    display: flex;
}
.left-header{
  text-align: left;
  line-height: 50px;
  height: 50px;
  padding-left: 16px;
  font-size: 16px;
  font-weight: 500;
  color: rgba(0, 0, 0, .9);
}
