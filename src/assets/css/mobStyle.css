@charset "utf-8";

/*公共样式 start*/

body,
div,
h1,
p,
h2,
header,
ul,
li,
dl,
dt,
dd,
ol,
nav,
input,
footer {
    margin: 0;
    padding: 0;
}

body,
#app,
html {
    height: 100%;
    -webkit-touch-callout: none;
    -webkit-user-select: none;
}

html,
body {
    width: 100%;
    height: 100%;
    overflow: hidden;
}

@media screen and (max-width:479px) {
    body,
    #app {
        font-size: 10px;
        text-align: left !important;
        -webkit-text-size-adjust: none;
        color: #2c3e50 !important;
        margin-top: 0px !important;
        height: 100% !important;
        min-width: 0 !important;
        font-family: PingFangSC !important;
    }
    ._css-mobile-materials-all input {
        font-size: 10px;
    }
}

@media screen and (min-width:480px) and (max-width:639px) {
    body,
    #app {
        font-size: 15px;
        text-align: left !important;
        -webkit-text-size-adjust: none;
        color: #2c3e50 !important;
        margin-top: 0px !important;
        height: 100% !important;
        min-width: 0 !important;
        font-family: PingFangSC !important;
    }
    ._css-mobile-materials-all input {
        font-size: 15px;
    }
}

@media screen and (min-width:640px) and (max-width:719px) {
    body,
    #app {
        font-size: 20px;
        text-align: left !important;
        -webkit-text-size-adjust: none;
        color: #2c3e50 !important;
        margin-top: 0px !important;
        height: 100% !important;
        min-width: 0 !important;
        font-family: PingFangSC !important;
    }
    ._css-mobile-materials-all input {
        font-size: 20px;
    }
}

@media screen and (min-width:720px) and (max-width:1000px) {
    body,
    #app {
        font-size: 22.5px;
        text-align: left !important;
        -webkit-text-size-adjust: none;
        color: #2c3e50 !important;
        margin-top: 0px !important;
        height: 100% !important;
        min-width: 0 !important;
        font-family: PingFangSC !important;
    }
    ._css-mobile-materials-all input {
        font-size: 22.5px;
    }
}

h1,
h2,
h3,
h4,
h5,
h6 {
    margin: 0;
    padding: 0;
    font-size: 100%;
    font-weight: normal;
}

ul,
li {
    list-style: none;
}

html {
    -webkit-text-size-adjust: none;
}

article,
aside,
details,
figcaption,
figure,
footer,
header,
hgroup,
menu,
nav,
section {
    display: block;
}

.clearfix:after {
    content: "\200B";
    display: block;
    height: 0;
    clear: both;
}

.clearfix {
    *zoom: 1;
}

input {
    font-family: Arial, Helveetica, sans-serif;
}

a {
    text-decoration: none;
    color: #666666
}

a:hover {
    text-decoration: none;
}

.clearfix:after {
    content: "\200B";
    display: block;
    height: 0;
    clear: both;
}

.clearfix {
    *zoom: 1;
}


/*公共样式 end*/