/* * {
  margin: 0;
  padding: 0;
  list-style: none;
  -moz-user-select: none;
  -o-user-select:none;
  -khtml-user-select:none;
  -webkit-user-select:none;
  -ms-user-select:none;
  user-select:none;
}

html,body {
  height: 100%;
  font-size: 14px;
  font-family: 'SourceHanSansCN'
} */
::-webkit-scrollbar {
  width: 0px;
  height: 0px;
}
i{
  font-style:normal;
}
#app {
  height: 100%;
}

.project-wampper{
  flex: 1;
  display: flex;
  height: 100%;
}
.bg071129{
  /* background: #071129; */
}
.font-color-FFFFFF{
  opacity: 0.5;
}
.font-color,.f2899FF{
  color: #2899FF;
}
.font-FFFFFF-50{
  color: rgba(255,255,255,.5);
  font-family: SourceHanSansCN;
  font-weight: 500;
}
.font-FFB224{
  color:#FFB224;
  font-family: AlibabaPuHuiTi;
}
.font-size-16{
  font-size: 16px;
  font-weight: 500;
  font-family: SourceHanSansCN-Medium
}
.font-size-22{
  font-size:22px;
}
.font-size-18{
  font-size:18px;
}
.font-26ABFF{
  color:#26ABFF;
}
.font-family-AR{
  font-family: 'AlibabaPuHuiTi-Regular'
}
.font-5-family{
  font-family: SourceHanSansCN;
  font-weight: 500;
}
.fontweight{
  font-weight: 550;
}
.f0B79F0{
  color: #0B79F0;
}
.air-wampper{
  width: 100%;
  height: 100%;
  background-image: url("~@/assets/images/VisualPanel/bg.png");
  background-repeat: no-repeat;
  background-size: 100% 100%;
  display: flex; 
  color: #fff;
  flex-direction: column;
}
.air-header{
  width: 100%;
  height: 80px;
  display:flex;
  background-image: url("~@/assets/images/VisualPanel/headbg.png");
  background-repeat: no-repeat;
  background-size: 100% 100%;
  justify-content : space-between;

}
.air-content{
  flex:1;
  overflow: hidden;
}
.title-logo{
  width: 450px;
  margin: 20px 30px;
  height: 40px;
  font-size: 25px;
  color: #FFFFFF;
  line-height: 40px;
  text-align: left;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  cursor: pointer;
  /* background-repeat: no-repeat; */
  /* background-image: url("~@/assets/images/title.png"); */
  /* background-size: 100%; */
}