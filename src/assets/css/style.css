.left {
    float: left;
}

.right {
    float: right;
}

.clearfix,
.clear {
    clear: both;
}

i {
    font-style: normal;
}

.text-left {
    text-align: left;
}

.bg-white {
    background-color: #FFFFFF;
}

.color-red {
    color: #CC0000;
}

.color-black-45 {
    color: rgba(0, 0, 0, 0.45);
}

.color-black-85 {
    color: rgba(0, 0, 0, 0.85);
}

.inline-block {
    display: inline-block;
}

.c1684FC {
    color: rgba(22, 132, 252, 1);
}


/***margin***/

.margin-top-15 {
    margin-top: 15px;
}

.margin-bottom-10 {
    margin-bottom: 10px;
}

.margin-bottom-20 {
    margin-bottom: 20px;
}


/***padding***/

.padding-top-15 {
    padding-top: 15px;
}

.padding-bottom-15 {
    padding-bottom: 15px;
}

.padding-25 {
    padding: 25px;
}

.btn-initiating-header {
    text-align: left;
    line-height: 50px;
    height: 50px;
    background-color: #fff;
    padding-left: 16px;
    font-size: 16px;
    font-weight: 500;
    color: rgba(0, 0, 0, .9);
    border-bottom: 1px solid rgba(0, 0, 0, .15);
}

ul li {
    list-style: none;
}

ul,
ul li,
p {
    margin: 0;
    padding: 0;
}

.cursor-pointer {
    cursor: pointer;
}

.overflow-point {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}