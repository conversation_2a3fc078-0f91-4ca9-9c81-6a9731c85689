._css-importexport-btns {
    position: fixed;
    top: 0;
    left: 0;
    z-index: 1003;
    background-color: #fff;
}

._css-1890ff {
    color: #1890FF;
}

._css-flhead-btn {
    width: 32px;
    height: 32px;
    line-height: 32px;
    font-size: 20px;
    color: rgba(0, 0, 0, 0.85);
    cursor: pointer;
    position: absolute;
    right: 80px;
}

._css-flhead-btn:hover {
    color: rgba(24, 144, 255, 1);
    background-color: rgba(24, 144, 255, 0.1);
}

._css-flhead-btn._css-flhead-btn-copy {
    right: 46px;
}

._css-flhead-btn._css-flhead-btn-paste {
    right: 12px;
}

._css-treenode-menubtn {
    /* display: none; */
    visibility: hidden;
    font-size: 16px;
    height: 24px;
    width: 24px;
    line-height: 24px;
    text-align: center;
    margin-right: 8px;
}

._css-treenode-content:hover ._css-treenode-menubtn {
    /* display: block; */
    visibility: inherit;
}

._css-treenode-menubtn:hover {
    color: #1890FF;
    background-color: rgba(0, 0, 0, 0.04);
}

._css-treenode-content {
    width: calc(100% - 24px);
    text-align: left;
}

._css-treenodelabel {
    font-size: 13px;
    flex: 1;
    display: flex;
    height: 32px;
    align-items: center;
    width: calc(100% - 48px);
}

._css-treenodellabelname {
    flex: 1;
    width: calc(100% - 24px);
    overflow-x: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

._css-treenode-content ._css-treenodellabelnum {
    margin-right: 12px;
    max-width: 30px;
    overflow-x: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    color: rgba(0, 0, 0, 0.3);
    display: block;
}

._css-treenode-content:hover ._css-treenodellabelnum {
    display: none;
}

._css-dis {
    cursor: not-allowed !important;
    pointer-events: none;
    opacity: 0.3;
}

._css-buttom-down-word._css-btnmaterialrelmodel-btn {
    width: 76px;
    height: 24px;
    display: flex;
    align-items: center;
    position: relative;
}

._css-buttom-down-word._css-btnmaterialrelmodel-btnimport {
    width: 96px;
    height: 24px;
    display: flex;
    align-items: center;
    position: relative;
}

._css-btnmaterialrelmodel-btndropctn {
    position: absolute;
    left: 0;
    top: calc(100% + 4px);
    border-radius: 4px;
    border: 1px solid #1890FF;
    width: 100%;
    position: absolute;
    left: 0;
    top: calc(100% + 4px);
    border-radius: 4px;
    border: 1px solid #1890FF;
    z-index: 2;
    text-align: left;
    color: #1890FF;
    background-color: #fff;
}

._css-btnmaterialrelmodel-btndropi {
    height: 24px;
    text-align: left;
    padding-left: 12px;
}

._css-btnmaterialrelmodel-btndropi:hover {
    background-color: #1890FF;
    color: #fff;
}

._css-btnmaterialrelmodel-text {
    margin-left: 8px;
}

._css-seloper-modifystatus {
    position: relative;
}

._css-efbtn {
    width: 88px;
    height: 40px;
    line-height: 40px;
    border-radius: 4px;
    box-sizing: border-box;
    cursor: pointer;
    user-select: none;
}

._css-efbtn-del {
    color: rgba(245, 34, 45, 1);
    background-color: rgba(250, 84, 28, 0.1);
    border: 1px solid rgba(245, 34, 45, 1);
    margin-left: 24px;
}

._css-efbtn-ok {
    color: rgba(255, 255, 255, 1);
    background-color: rgba(73, 181, 255, 1);
    border: 1px solid rgba(73, 181, 255, 1);
    margin-right: 24px;
}

._css-efbtn-del:hover {
    color: rgba(255, 255, 255, 1);
    background-color: rgba(245, 34, 45, 1);
    border: 1px solid rgba(245, 34, 45, 1);
}

._css-efbtn-ok:hover {
    color: rgba(255, 255, 255, 1);
    background-color: rgba(24, 144, 255, 1);
    border: 1px solid rgba(24, 144, 255, 1);
}

._css-ef-btns {
    flex: 1;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

._css-ef-text-input {
    border: none;
    outline: none;
    color: rgba(0, 0, 0, 0.85);
    font-size: 14px;
    width: 200px;
    height: 40px;
    border: 1px solid rgba(0, 0, 0, 0.09);
    border-radius: 4px;
    margin-top: 16px;
    padding-left: 16px;
    padding-right: 16px;
    box-sizing: border-box;
}

._css-eftitle-back {
    width: 20px;
    height: 20px;
    margin-left: 24px;
    line-height: 20px;
    cursor: pointer;
    visibility: hidden;
}

._css-eftitle-btn:hover {
    color: rgba(24, 144, 255, 1);
}

._css-eftitle-label {
    height: 22px;
    line-height: 22px;
    text-align: center;
    color: rgba(0, 0, 0, 0.85);
    flex: 1;
}

._css-eftitle-close {
    width: 20px;
    height: 20px;
    margin-right: 24px;
    line-height: 20px;
    cursor: pointer;
}

._css-ef-title {
    height: 40px;
    box-sizing: border-box;
    border-bottom: 1px solid rgba(0, 0, 0, 0.09);
    display: flex;
    align-items: center;
}

._css-edit-field {
    width: 248px;
    height: 169px;
    background-color: rgba(255, 255, 255, 1);
    box-shadow: 0px 6px 14px 0px rgba(26, 26, 26, 0.06);
    border-radius: 4px;
    display: flex;
    flex-direction: column;
}

._css-edit-field-hover {
    position: fixed;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    background-color: rgba(0, 0, 0, 0.3);
    z-index: 2;
    display: flex;
    align-items: center;
    justify-content: space-around;
}

._css-flbody-li:hover ._css-fli-btn {
    display: block;
}

._css-fli-btn {
    width: 32px;
    display: none;
    height: 32px;
    line-height: 32px;
    font-size: 20px;
    color: rgba(0, 0, 0, 0.85);
    cursor: pointer;
    margin-right: 18px;
}

._css-fli-btn:hover {
    color: rgba(24, 144, 255, 1);
    background-color: rgba(24, 144, 255, 0.1);
}

._css-fli-label {
    flex: 1;
    text-align: left;
    margin-left: 24px;
}

._css-flex-jcsa {
    justify-content: space-around;
}

._css-flbody-li {
    height: 50px;
    width: 100%;
    flex: none;
    display: flex;
    align-items: center;
}

._css-flbody-li:hover {
    background-color: rgba(0, 0, 0, 0.04);
}

._css-cur-fields-emptylabel {
    height: 22px;
    width: 110px;
    color: rgba(0, 0, 0, 0.25);
    font-size: 14px;
}

._css-cur-fields-emptyimg {
    height: 60px;
    width: 60px;
    line-height: 60px;
}

._css-cur-fields-emptycontent {
    width: 110px;
    height: 99px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    align-items: center;
}

._css-fieldlist-body {
    display: flex;
    flex-direction: column;
    height: calc(100% - 40px);
    flex: 1;
    align-items: center;
    max-height: 300px;
    overflow-y: auto;
}

._css-flhead-text {
    border: none;
    outline: none;
    font-size: 14px;
    margin-left: 24px;
    color: rgba(0, 0, 0, 0.45);
}

._css-fieldlist-head {
    height: 40px;
    flex: none;
    width: 100%;
    display: flex;
    align-items: center;
    position: relative;
    border-bottom: 1px solid rgba(0, 0, 0, 0.09);
    box-sizing: border-box;
}

._css-field-list {
    position: fixed;
    width: 328px;
    min-height: 248px;
    background-color: rgba(255, 255, 255, 1);
    box-shadow: 0px 6px 14px 0px rgba(26, 26, 26, 0.06);
    border-radius: 4px;
    z-index: 2;
    display: flex;
    flex-direction: column;
}

._css-typei-expand {
    font-size: 12px;
    width: 12px;
    height: 12px;
    line-height: 12px;
    right: 16px;
    position: absolute;
}

._css-rel-body-ele {
    height: 50px;
    box-sizing: border-box;
    border: 1px dashed red;
}

._css-newtypename-text {
    outline: none;
    border: none;
    background-color: transparent;
    font-size: 14px;
    color: rgba(0, 0, 0, 0.45);
    width: 100%;
}

._css-newmaterialtypename {
    height: 40px;
    box-sizing: border-box;
    border-radius: 4px;
    border: 1px solid rgba(24, 144, 255, 1);
    display: flex;
    align-items: center;
    background-color: rgba(24, 144, 255, 0.04);
    padding-left: 28px;
    padding-right: 42px;
    width: 100%;
}

._css-seloperi-label {
    color: rgba(0, 0, 0, 0.65);
}

._css-seloperi-icon {
    margin-right: 8px;
    width: 20px;
    height: 20px;
    font-size: 20px;
}

._css-seloperbtn {
    cursor: pointer;
}

._css-seloperbtn:hover {
    color: rgba(24, 144, 255, 1);
}

._css-seloperi {
    height: 32px;
    min-width: 52px;
    margin-left: 32px;
    display: flex;
    align-items: center;
    font-size: 12px;
    color: rgba(0, 0, 0, 0.45);
    font-weight: 400;
}

._css-seloperi._css-first {
    margin-left: 14px;
}

._css-materialtab-selectedcontrol {
    position: absolute;
    top: 0;
    left: 40px;
    width: calc(100% - 40px);
    height: 44px;
    display: flex;
    z-index: 1;
    align-items: center;
    background-color: #fff;
}

._css-typei-text {
    height: 22px;
    line-height: 22px;
    margin-left: 8px;
}

._css-typei:hover {
    background-color: rgba(0, 0, 0, 0.04);
}

._css-typei-spliter {
    border-top: 0.5px solid rgba(0, 0, 0, 0.15);
    border-bottom: 0.5px solid rgba(0, 0, 0, 0.15);
    height: 1px;
    box-sizing: border-box;
    margin-top: 4px;
    margin-bottom: 4px;
}

._css-typei-icon {
    width: 16px;
    height: 16px;
    color: rgba(0, 0, 0, 0.45);
    margin-left: 16px;
}

._css-typei {
    height: 40px;
    display: flex;
    align-items: center;
    cursor: pointer;
    color: rgba(0, 0, 0, 0.85);
    font-weight: 400;
}

._css-typei._css-dis {
    color: rgba(0, 0, 0, 0.25);
}

._css-dis {
    cursor: not-allowed;
    pointer-events: none;
}

._css-typeitem-menu {
    width: 220px;
    z-index: 2000;
    background-color: rgba(255, 255, 255, 1);
    box-shadow: 0px 4px 20px 0px rgba(0, 0, 0, 0.15);
    border-radius: 2px;
    position: fixed;
    min-height: 137px;
    box-sizing: border-box;
    padding-top: 4px;
    padding-bottom: 4px;
}

._css-typeitem-title {
    text-align: center;
    position: relative;
    line-height: 44px;
    font-size: 16px;
    font-weight: 500;
    color: rgba(0, 0, 0, 1);
    border-bottom: 1px solid rgba(0, 0, 0, 0.09);
}

._css-typeitem-previous {
    position: absolute;
    top: 12px;
    left: 12px;
    color: rgba(0, 0, 0, 0.45);
    cursor: pointer;
}

._css-option-add-sure._css-option-add-sure p {
    width: 80%;
    margin-left: 10%;
    background-color: #1890FF;
}

._css-typeitem-title ._css-typeitem-close {
    position: absolute;
    top: 12px;
    right: 12px;
    color: rgba(0, 0, 0, 0.45);
    cursor: pointer;
}

._css-itemmorebtn {
    width: 32px;
    height: 32px;
    line-height: 32px;
    margin-right: 4px;
    font-size: 20px;
    color: rgba(0, 0, 0, 0.65);
    cursor: pointer;
}

._css-itemmorebtn:hover {
    color: rgba(24, 144, 255, 1);
    background-color: rgba(24, 144, 255, 0.1);
}

._css-sortoption-item {
    height: 40px;
    line-height: 40px;
    font-size: 14px;
    color: rgba(0, 0, 0, 0.85);
    padding-left: 24px;
    box-sizing: border-box;
    text-align: left;
    cursor: pointer;
}

._css-sortoption-item._css-sortitemclicked,
._css-sortoption-item:hover {
    background-color: rgba(0, 0, 0, 0.04);
}

._css-sortoptions-menu {
    width: 180px;
    padding-top: 4px;
    padding-bottom: 4px;
    overflow-y: auto;
    max-height: 216px;
    background-color: rgba(255, 255, 255, 1);
    box-shadow: 0px 4px 20px 0px rgba(0, 0, 0, 0.15);
    border-radius: 4px;
    position: fixed;
    z-index: 2;
}

._css-material-status-popup {
    width: 220px;
    height: 351px;
    background: rgba(255, 255, 255, 1);
    box-shadow: 0px 4px 20px 0px rgba(0, 0, 0, 0.15);
    border-radius: 2px;
    position: fixed;
    z-index: 2;
}

._css-menu-in-icon {
    width: 16px;
    height: 16px;
    color: rgba(0, 0, 0, 0.85);
    margin-left: 16px;
}

._css-menu-in-text {
    margin-left: 8px;
    color: rgba(0, 0, 0, 0.85);
    font-size: 14px;
    flex: 1;
    text-align: left;
}

._css-menu-in-expander {
    width: 12px;
    height: 12px;
    margin-right: 16px;
    font-size: 12px;
}

._css-material-menu-in {
    display: flex;
    align-items: center;
    width: 100%;
    height: 100%;
    cursor: pointer;
}

._css-material-menu-in:hover {
    background-color: rgba(0, 0, 0, 0.04);
}

._css-material-menu-i {
    height: 48px;
    box-sizing: border-box;
    padding-top: 4px;
    padding-bottom: 4px;
}

._css-material-menu-i._css-last {
    border-bottom-style: none;
}

._css-materialitem-menu {
    z-index: 2;
    position: fixed;
    width: 220px;
    background: rgba(255, 255, 255, 1);
    box-shadow: 0px 4px 20px 0px rgba(0, 0, 0, 0.15);
    border-radius: 4px;
}

._css-trbtn {
    /* height: 32px; */
    width: 32px;
    color: rgba(24, 144, 255, 1);
    cursor: pointer;
    text-align: center;
    /* line-height: 32px; */
}

._css-trbtn:hover {
    background-color: rgba(24, 144, 255, 0.1);
}

._css-trbtn-container {
    flex: 1;
    height: 100%;
    display: none;
    flex-direction: row-reverse;
}

.cell:hover>._css-trbtn-container {
    display: flex;
}

._css-search-clear {
    height: 20px;
    width: 20px;
    margin-right: 10px;
    font-size: 18px;
    cursor: pointer;
}

._css-mat-alter-state ._css-mat-status-optionicon-in {
    width: 100px;
    height: 28px;
    line-height: 29px;
}

._css-mat-status-optionicon-in {
    width: 90px;
    height: 38px;
    border-radius: 2px;
    text-align: center;
    line-height: 38px;
    font-size: 12px;
    margin-left: 16px;
}

._css-mat-status-optionicon-in2 {
    width: 70px;
    height: 28px;
    border-radius: 2px;
    text-align: center;
    line-height: 28px;
    font-size: 12px;
    margin-left: 16px;
    margin-right: 16px;
}

._css-mat-status-optionicon {
    flex: 1;
    height: 100%;
    display: flex;
    align-items: center;
}

._css-mat-status-optionstatus {
    height: 20px;
    width: 20px;
    margin-right: 10px;
}

._css-typeitem-options {
    margin-top: 20px;
    max-height: 222px;
    overflow-y: auto;
    border-bottom: 1px solid rgba(0, 0, 0, .09);
}

._css-mat-status-option {
    display: inline-block;
    cursor: pointer;
    margin-bottom: 16px;
}

._css-mat-status-options {
    width: 100%;
    box-sizing: border-box;
}

._css-mat-options-previous {
    text-align: left;
}

._css-mat-status-options2 {
    flex: 1;
    overflow-y: auto;
    padding-top: 4px;
    padding-bottom: 4px;
    box-sizing: border-box;
    background-color: #fff;
    top: 100%;
    left: 0;
    position: absolute;
}

._css-mat-status-btn {
    height: 32px;
    margin-top: 16px;
    margin-bottom: 16px;
    width: calc(100% - 32px);
    border-radius: 4px;
    background-color: rgba(24, 144, 255, 1);
    color: rgba(255, 255, 255, 1);
    font-size: 14px;
    text-align: center;
    line-height: 32px;
    cursor: pointer;
    user-select: none;
}

._css-filter-resetbtn {
    width: 312px;
    height: 40px;
    line-height: 40px;
    cursor: pointer;
    border-radius: 20px;
    background-color: rgba(86, 98, 112, 0.1);
    color: rgba(0, 0, 0, 0.65);
    user-select: none;
}

._css-filter-resetbtn:hover {
    background-color: rgba(24, 144, 255, 1);
    color: rgba(255, 255, 255, 1);
}

._css-reloptiontext {
    color: rgba(0, 0, 0, 0.45);
    margin-left: 8px;
}

._css-reloptiontext._css-selecttext {
    color: rgba(0, 0, 0, 0.85);
}

._css-reloptionicon {
    width: 16px;
    height: 16px;
    border-radius: 50%;
    border: 1px solid rgba(0, 0, 0, 0.45);
    display: flex;
    align-items: center;
    justify-content: space-around;
    cursor: pointer;
}

._css-reloptionicon._css-selectradio {
    border: 1px solid rgba(24, 144, 255, 1);
    background-color: rgba(24, 144, 255, 0.1);
}

._css-reloptionicon-inner {
    width: 14px;
    height: 14px;
    border-radius: 50%;
}

._css-reloptionicon-inner._css-selectradio {
    background-color: rgba(24, 144, 255, 1);
}

._css-releleoption {
    height: 22px;
    margin-bottom: 18px;
    display: flex;
    align-items: center;
    cursor: pointer;
}

._css-releleoptions {
    flex-direction: column;
}

._css-releletitle {
    width: 112px;
    text-align: left;
    margin-top: 21px;
    color: rgba(0, 0, 0, 0.45);
}

._css-filter-releleoptions ._css-filter-checkedList {
    text-align: left;
    line-height: 40px;
}

._css-filter-releleoptions ._css-filter-checkedList /deep/ .el-checkbox__inner {
    width: 16px;
    height: 16px;
}

._css-filter-releleoptions ._css-filter-checkedList /deep/ .el-checkbox__inner::after {
    left: 5px;
}

._css-filter-releleoptions ._css-filter-checkedList /deep/ .el-checkbox__label {
    padding-left: 24px;
}

._css-filter-releleoptions ._css-filter-checkedList /deep/ .el-date-editor.el-input,
.el-date-editor.el-input__inner {
    width: 168px;
}

._css-filter-releleoptions ._css-filter-checkedList /deep/ .el-checkbox__input.is-checked+.el-checkbox__label {
    color: rgba(0, 0, 0, 0.65);
}

._css-statusval {
    text-align: left;
    color: rgba(0, 0, 0, 0.45);
    cursor: pointer;
    position: relative;
}

._css-statustitle {
    width: 112px;
    text-align: left;
    color: rgba(0, 0, 0, 0.45);
}

._css-materialstatus {
    align-items: center;
}

._css-filtersearchinput {
    outline: none;
    border: none;
    width: calc(100% - 32px);
    margin-left: 16px;
    margin-right: 16px;
    font-size: 14px;
}

._css-filtersearch {
    width: 100%;
    height: 40px;
    border-radius: 4px;
    border: 1px solid rgba(0, 0, 0, 0.09);
    display: flex;
    align-items: center;
}

._css-filterdrop {
    width: 400px;
    height: 520px;
    background: rgba(255, 255, 255, 1);
    box-shadow: 0px 4px 20px 0px rgba(0, 0, 0, 0.15);
    border-radius: 4px;
    position: fixed;
    z-index: 2;
    box-sizing: border-box;
}

._css-filter-top {
    padding: 0 24px 10px 24px;
    height: 430px;
    overflow-y: scroll;
    overflow-x: hidden;
}

._css-relele {
    height: 20px;
    width: 20px;
    cursor: pointer;
}

._css-relele-center {
    margin: 0 auto;
}

._css-guanlianmoxing-hover:hover {
    color: rgba(24, 144, 255, 1);
    cursor: pointer;
}

._css-qrcode {
    height: 20px;
    width: 20px;
}

._css-hover-qrcode {
    display: block;
    position: absolute;
    z-index: 199;
    background-color: #fff;
    border: 1px solid #ccc;
    text-align: center;
    padding: 0 8px 8px 8px;
}

._css-customstyle {
    height: 100% !important;
}

._css-datastatus {
    width: 66px;
    height: 24px;
    border-radius: 4px;
    border: 1px solid rgba(0, 0, 0, 1);
    background-color: rgba(0, 0, 0, 0.1);
    color: rgba(0, 0, 0, 1);
    line-height: 24px;
    text-align: center;
}

._css-dataitemname {
    font-size: 14px;
    font-weight: 500;
    color: rgba(0, 0, 0, 0.85);
    cursor: pointer;
}

._css-dataitemname:hover {
    color: #1890ff;
    text-decoration: underline;
}

._css-dataitemcode {
    font-size: 14px;
    color: rgba(0, 0, 0, 0.85);
    font-weight: 800;
}

._css-table-ele /deep/ th>.cell {
    display: flex !important;
}

._css-table-ele /deep/ .el-table__body tr.current-row>td {
    background-color: rgba(0, 0, 0, 0.04);
}

._css-materialtypetab-body /deep/ .el-table td {
    border-bottom-color: rgba(0, 0, 0, 0.08) !important;
    border-right-color: rgba(0, 0, 0, 0.08) !important;
}

._css-materialtypetab-body /deep/ ._css-customstyle.el-table--enable-row-hover .el-table__body tr:hover>td {
    background-color: rgba(0, 0, 0, 0.04);
}

._css-materialtypetab-body /deep/ ._css-table-ele.el-table--border {
    border: none;
}

._css-materialtypetab-body /deep/ .el-table td:last-child {
    border-right-color: rgba(0, 0, 0, 0.00) !important;
}

._css-table-title {
    width: 100%;
    height: 44px;
    line-height: 44px;
    padding: 0 16px !important;
    text-align: center;
}

._css-table-title-hover:hover {
    background-color: rgba(0, 0, 0, 0.04);
}

._css-table-title-hover:hover i {
    color: #1890ff;
}

._css-materialtypetab-body thead /deep/ .el-table .cell {
    line-height: initial !important;
}

._css-materialtypetab-body /deep/ .el-table .cell {
    line-height: initial !important;
    padding-left: 16px;
}

._css-materialthead-filter {
    margin-right: 8px;
    /* padding: 0 8px; */
    height: 24px;
    line-height: 24px;
    font-size: 12px;
    cursor: pointer;
    color: #1890ff;
    border-radius: 4px;
    border: 1px solid rgba(24, 144, 255, 1);
}

._css-materialthead-filter i {
    vertical-align: middle;
    padding-right: 8px;
}

._css-materialthead-filter._css-filterclicked {
    color: rgba(24, 144, 255, 1);
    background-color: rgba(24, 144, 255, 0.1);
}

._css-materialthead-filter._css-commonbtn-clicked,
._css-materialthead-filter._css-commonbtn-clicked:hover {
    color: #fff;
    background-color: rgba(24, 144, 255, 1);
    border: 1px solid rgba(24, 144, 255, 1);
}

._css-materialthead-sort {
    margin-right: 24px;
    width: 32px;
    height: 32px;
    line-height: 32px;
    font-size: 20px;
    cursor: pointer;
}

._css-materialthead-sort._css-sortclicked {
    color: rgba(24, 144, 255, 1);
    background-color: rgba(24, 144, 255, 0.1);
}

._css-materialthead-splitter {
    height: 24px;
    width: 2px;
    background-color: rgba(0, 0, 0, 0.09);
    margin-right: 24px;
}

._css-materialthead-relele {
    margin-right: 24px;
    width: 32px;
    height: 32px;
    line-height: 32px;
    font-size: 20px;
    cursor: pointer;
}

._css-materialthead-addbtn {
    margin-right: 16px;
    width: 32px;
    height: 32px;
    line-height: 32px;
    font-size: 20px;
    cursor: pointer;
}

._css-materialthead-title {
    flex: 1;
    height: 100%;
}

._css-materialtypetab-head {
    height: 64px;
    display: flex;
    align-items: center;
}

._css-materialtypetab-body {
    height: calc(100% - 64px);
    position: relative;
    background-color: #fff;
    overflow: auto;
}

._css-materialitemicon {
    color: rgba(0, 0, 0, 0.45);
    padding-left: 16px;
}

._css-materialtypeitem._css-clicked ._css-materialitemicon {
    color: rgba(24, 144, 255, 1);
}

._css-materialtypeitem:hover ._css-materialitemicon {
    color: rgba(0, 0, 0, 0.65);
}

._css-materialitemname {
    flex: 1;
    margin-left: 12px;
    font-size: 14px;
    font-weight: 400;
    height: 22px;
    line-height: 22px;
    text-align: left;
    display: flex;
}

._css-materialtypeitem ._css-materialitemname {
    color: rgba(0, 0, 0, 0.65);
}

._css-materialtypeitem._css-clicked ._css-materialitemname {
    color: rgba(24, 144, 255, 1);
    display: flex;
    align-content: center;
}

._css-materialitemname ._css-material-number {
    color: rgba(0, 0, 0, 0.25);
}

._css-material-overflow {
    MAX-width: 150px;
    display: inline-block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap
}

._css-materialtypeitem._css-clicked ._css-materialitemname ._css-material-number {
    color: rgba(24, 144, 255, 0.5);
}

._css-materialitemnum {
    width: 18px;
    margin-right: 12px;
    color: rgba(0, 0, 0, 0.25);
    font-size: 14px;
    height: 22px;
    line-height: 22px;
    font-weight: 900;
}

._css-materialtypeitem._css-clicked ._css-materialitemnum {
    color: rgba(24, 144, 255, 1);
}

._css-materialtypeitem:hover ._css-materialitemnum {
    color: rgba(0, 0, 0, 0.65);
}

._css-materhead-icon {
    height: 20px;
    width: 20px;
}

._css-materhead-text {
    height: 22px;
    line-height: 22px;
    flex: 1;
    text-align: left;
    margin-left: 8px;
    font-weight: 500;
    font-size: 14px;
    color: rgba(0, 0, 0, 0.9);
}

._css-materhead-addbtn {
    height: 32px;
    width: 32px;
    line-height: 32px;
    margin-right: 6px;
    cursor: pointer;
}

._css-materialthead-addbtn:hover {
    color: rgba(24, 144, 255, 1);
    background-color: rgba(24, 144, 255, 0.1);
}

._css-materhead-addbtn:hover {
    color: rgba(24, 144, 255, 1);
    background-color: rgba(24, 144, 255, 0.1);
}

._css-commonbtn:hover {
    color: #fff;
    background-color: #1890ff;
    border-color: #1890ff;
}

._css-materialtypelist-head {
    height: 64px;
    display: flex;
    align-items: center;
    padding-left: 8px;
}

._css-materialtypeitem {
    height: 40px;
    display: flex;
    align-items: center;
    user-select: none;
    cursor: pointer;
}

._css-materialtypeitem:hover {
    background-color: rgba(0, 0, 0, 0.04);
}

._css-materialtypeitem._css-clicked {
    background-color: rgba(24, 144, 255, 0.1);
    color: rgba(24, 144, 255, 1);
}

._css-materialtypelist {
    height: calc(100% - 64px - 24px);
    overflow-y: auto;
    overflow-x: hidden;
    margin-bottom: 24px;
    background-color: #ffffff;
}

._css-materialtypelist._css-withadding {
    height: calc(100% - 64px - 40px - 24px);
}

._css-materials-all {
    height: 100%;
    display: flex;
    flex-direction: column;
}

._css-tabcontrolarea {
    height: 54px;
    display: flex;
    align-items: center;
    flex: none;
    background-color: #fff;
    border-bottom: 1px solid rgba(0, 0, 0, 0.15);
}

._css-bottomarea {
    flex: 1;
    display: flex;
    height: calc(100% - 54px);
}

._css-bottomlist {
    width: 270px;
    height: 100%;
    box-sizing: border-box;
    background-color: #fff;
}

._css-bottomtable {
    flex: 1;
    display: flex;
    flex-direction: column;
    width: calc(100% - 347px);
    height: 100%;
    padding-right: 24px;
    box-sizing: border-box;
}

._css-tabcontrolitem {
    margin-left: 40px;
    width: 64px;
    height: 24px;
    line-height: 24px;
    font-size: 16px;
    font-weight: 500;
    color: rgba(0, 0, 0, 0.45);
}

._css-tabcontrolitem._css-clicked {
    color: rgba(0, 0, 0, 0.9);
}

._css-tabcontrolalltype {
    margin-left: 16px;
}

.mv {
    position: absolute;
    top: 0;
    left: 0;
    z-index: 99;
    background-color: #fff;
}

.mi {
    width: 869px;
    height: 420px;
    position: absolute;
    top: 50%;
    left: 50%;
    -webkit-transform: translate(-50%, -50%);
    transform: translate(-50%, -50%);
    z-index: 97;
}

._css-hover-qrcode img {
    width: 150px;
    height: 150px;
}

._css-hover-qrcode p {
    margin: 10px 0;
    line-height: 30px;
    font-size: 14px;
}

._css-dialog-select {
    background-color: #fff;
    width: 360px;
    font-size: 14px;
    position: absolute;
    border-radius: 2px;
    font-family: "PingFangSC-Regular,PingFangSC";
}

._css-dialog-select-check {
    margin: 20px 24px 0;
}

._css-dialog-select-check ._css-dialog-select-asc {
    display: inline-block;
    padding-right: 22px;
    text-indent: 5px;
}

._css-dialog-select-check ._css-dialog-select-asc i {
    padding-right: 8px;
    color: rgba(0, 0, 0, 0.4);
    vertical-align: middle;
}

._css-dialog-select-icon {
    width: 20px;
    height: 20px;
}

._css-dialog-select-input {
    margin: 16px 24px;
}

._css-dialog-select-input .el-autocomplete {
    width: 296px;
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: 5px;
    padding-left: 16px;
    color: rgba(0, 0, 0, 0.25);
}

._css-dialog-select-input input {
    width: 90%;
    height: 40px;
    line-height: 40px;
    padding-left: 16px;
    color: rgba(0, 0, 0, 1);
    outline: none;
    font-size: 14px;
    border-radius: 4px;
    border: 1px solid rgba(0, 0, 0, 0.09);
}

._css-dialog-select-list {
    margin: 0 24px;
}

._css-select-list-overflow {
    margin: 0 24px 20px;
    max-height: 220px;
    overflow-y: scroll;
}

._css-dialog-select-list-child {
    line-height: 20px;
    padding: 10px 0;
    text-align: left;
    border-bottom: 1px solid rgba(0, 0, 0, 0.04);
    display: block;
    color: rgba(0, 0, 0, 0.6);
}

._css-dialog-select-list-child /deep/ .el-checkbox__input.is-checked+.el-checkbox__label,
._css-dialog-select-list-all /deep/ .el-checkbox__input.is-checked+.el-checkbox__label {
    color: rgba(0, 0, 0, 0.6);
}

._css-dialog-select-list-child /deep/ span:nth-child(2),
._css-dialog-select-list-all /deep/ span:nth-child(2) {
    display: inline-block;
    padding-left: 33px !important;
}

._css-dialog-select-list ul li {
    line-height: 20px;
    padding: 10px 0;
    text-align: left;
    border-bottom: 1px solid rgba(0, 0, 0, 0.04);
}

._css-dialog-select-list ul li i {
    display: inline-block;
    width: 20px;
    height: 20px;
    padding-right: 33px;
    line-height: 20px;
    vertical-align: middle;
}

._css-dialog-select-button {
    margin: 12px 0;
    border-top: 1px solid rgba(0, 0, 0, 0.09);
}

._css-dialog-select-button span {
    display: inline-block;
    width: 155px;
    height: 38px;
    line-height: 38px;
    margin-top: 12px;
    border-radius: 4px;
    background-color: rgba(24, 144, 255, 1);
    color: rgba(255, 255, 255, 1);
    border: 1px solid transparent;
    cursor: pointer;
}

._css-dialog-select-button span:first-child {
    border: 1px solid rgba(0, 0, 0, 0.09);
    margin-right: 7%;
    background-color: #fff;
    color: rgba(0, 0, 0, 0.45);
}

._css-table-header-icon {
    float: right;
    line-height: 44px;
    width: 12px;
    height: 12px;
    cursor: pointer;
    color: transparent;
}

._css-choose-switch-parent {
    height: 100%;
    display: flex;
    align-items: center;
    cursor: pointer;
}

._css-choose-switch {
    width: 16px;
    height: 120px;
}

._css-choose-switch ._css-interface-back {
    color: rgba(0, 0, 0, 0.4);
    text-align: center;
    line-height: 32px;
}

._css-click-close-or-open {
    color: rgba(0, 0, 0, 0.6);
    padding: 3px 8px 0 0;
    /* cursor: pointer; */
    vertical-align: middle;
}

.fade-enter-active,
.fade-leave-active {
    transition: opacity 0.5s;
}

.fade-enter,
.fade-leave-to
/* .fade-leave-active below version 2.1.8 */

{
    opacity: 0;
}

._css-materialtypetab-body ._css-customstyle .el-table__header-wrapper /deep/ .el-table th>.cell {
    color: rgba(0, 0, 0, 0.6);
    /* color: #f00; */
}

._css-filter-title {
    text-align: left;
    font-weight: 500;
    line-height: 48px;
}

._css-filter-line {
    height: 1px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.09);
    margin-top: 16px;
}

._css-filter-line-margin {
    margin: 0;
}

._css-fliter-time {
    height: 100px;
}

._css-date-picker {
    float: left;
    width: 47.5%;
    height: 38px;
    line-height: 38px;
    border: 1px solid rgba(0, 0, 0, 0.09);
    border-radius: 4px;
}

._css-start-time {
    margin-right: 3%;
}

._css-addMaterialtypelist {
    width: 220px;
    height: 242px;
    position: fixed;
    z-index: 2;
    background-color: #fff;
}

._css-typeitem-add-input {
    margin: 17px 5% 50px;
    text-align: left;
}

._css-typeitem-add-input ._css-newtypename-text {
    line-height: 40px;
    padding-left: 16px;
    border: 1px solid rgba(0, 0, 0, 0.09);
    border-radius: 4px;
    margin-top: 5px;
    width: 90%;
}

._css-typeitem-add-sure {
    border-top: 1px solid rgba(0, 0, 0, 0.09);
    display: flex;
    align-items: center;
    justify-content: space-around;
    height: 64px;
}

._css-typeitem-add-sure p {
    width: 90%;
    line-height: 40px;
    background-color: rgba(24, 144, 255, 1);
    color: #fff;
    text-align: center;
    border-radius: 4px;
    cursor: pointer;
}

._css-materialtypetab-body /deep/ .el-table__body-wrapper .el-table__row {
    height: 40px !important;
    border-bottom: 1px solid rgba(0, 0, 0, .09);
    line-height: 40px !important;
}

._css-materialtypetab-body /deep/ .el-table__header-wrapper th {
    height: 44px !important;
    border-bottom: 1px solid rgba(0, 0, 0, .09);
    border-right: 1px solid rgba(0, 0, 0, .09);
    line-height: 44px !important;
}

._css-materialtypetab-body /deep/ .el-table__header-wrapper .cell,
._css-materialtypetab-body /deep/ .el-table__header-wrapper th div,
._css-materialtypetab-body /deep/ .el-table--border td:first-child .cell,
._css-materialtypetab-body /deep/ .el-table--border th:first-child .cell {
    padding: 0;
}

._css-header-right-buttom {
    display: flex;
}

._css-righthide-checkbuttom {
    color: rgba(0, 0, 0, 0.45);
    font-weight: 500;
    cursor: pointer;
    display: flex;
}

._css-righthide-checkbuttom:hover {
    color: #1890ff;
    font-weight: 500;
    cursor: pointer;
    display: flex;
}

._css-righthide-more i {
    padding-left: 24px;
    vertical-align: middle;
}

._css-right-model-iframe {
    flex: 1
}

._css-modeliframe {
    border: none;
    width: 100%;
    height: 100%;
}

._css-buttom-two-word {
    width: 66px;
}

._css-buttom-two-word._css-buttom-two-word-import {
    width: 96px;
}

._css-buttom-four-word {
    width: 76px;
}

._css-buttom-down-word {
    width: 76px;
    padding: 0 3px 0 5px;
    white-space: nowrap;
    display: flex;
    align-items: center;
}

._css-down-disabled {
    cursor: not-allowed;
    background: rgba(0, 0, 0, 0.25);
    color: #fff;
    border: 1px solid transparent;
}

._css-down-disabled:hover {
    cursor: not-allowed;
    background: rgba(0, 0, 0, 0.25);
    color: #fff;
    border: 1px solid transparent;
}

._css-notree-data {
    font-size: 12px;
    color: #909399
}

._css-flow-contextmenu {
    position: fixed;
    min-height: 48px;
    z-index: 4;
    box-shadow: 0px 0px 8px 0px rgba(0, 0, 0, 0.15);
    border-radius: 4px;
    width: 160px;
    background-color: #fff;
}

._css-flow-contextmenu-i {
    height: 48px;
    padding-top: 4px;
    padding-bottom: 4px;
    box-sizing: border-box;
}

._css-flow-contextmenu-in {
    height: 100%;
    width: 100%;
    cursor: pointer;
    display: flex;
    align-items: center;
}

._css-flow-contextmenu-in._css-dis {
    opacity: 0.5;
    cursor: not-allowed;
    pointer-events: none;
}

._css-flow-contextmenu-in:not(._css-dis):hover {
    background-color: rgba(0, 0, 0, 0.04);
}

._css-flow-contextmenu-in:not(._css-dis):hover div,
._css-customstyle /deep/ .el-tree-node>.el-tree-node__content:hover {
    color: #007AFF;
}

._css-flow-contextmenu-inicon {
    width: 16px;
    line-height: 40px;
    height: 100%;
    margin-left: 16px;
    font-size: 16px;
    color: #999999;
}

._css-flow-contextmenu-intext {
    margin-left: 8px;
    font-size: 14px;
    height: 100%;
    line-height: 40px;
    color: #666666;
}

._css-bottomtable /deep/ .plTableBox .el-table td,
._css-bottomtable /deep/ .plTableBox .el-table th {
    padding: 0;
}

._css-bottomtable /deep/ .el-table__empty-block,
._css-materialtypetab-body /deep/ .el-icon-loading {
    display: block;
}

._css-bottomtable /deep/ .el-loading-spinner .el-loading-text {
    font-size: 15px;
}

._css-bottomtable /deep/ .el-loading-spinner i {
    font-size: 18px;
}

._css-auto-element-relation {
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, .2);
    position: absolute;
    top: 0;
    left: 0;
    z-index: 10;
    display: flex;
    align-items: center;
    justify-content: center;
}

._css-table-loading {
    width: 300px;
    height: 130px;
    background: #fff;
    box-shadow: 0px 13px 24px -17px rgba(11, 41, 62, 0.8);
    border-radius: 4px;
    position: relative;
    bottom: 40% !important;
}

._css-loading-num {
    font-size: 20px;
    font-family: 'PingFangSC-Medium, PingFang SC';
    font-weight: 500;
    color: #1890FF;
    line-height: 38px;
    margin: 14px 0;
}

._css-loading-line {
    position: absolute;
    left: 0;
    bottom: 0;
    width: 20%;
    height: 6px;
    background: #1890FF;
    border-radius: 0px 0px 0px 2px;
}

._css-loading-num-success {
    margin: 14px 0;
    line-height: 38px;
}

._css-loading-num-success span {
    color: #24B990;
    font-size: 14px;
}

._css-loading-num-success span:last-child {
    color: #FD5F38;
    padding-left: 10px;
}

._css-loading-close {
    width: 150px;
    height: 32px;
    background: #007AFF;
    border-radius: 4px;
    margin: 0 auto;
    line-height: 32px;
    color: #fff;
    cursor: pointer;
}

._css-mgr-tree {
    height: 100%;
}

.pagination-css {
    background: #fff;
    padding: 20px;
}

.table-top-bar {
    position: absolute;
    top: 0;
    left: 0;
    height: 43px;
    width: 100%;
    background-color: #FFFFFF;
    z-index: 10;
    text-align: left;
    padding-left: 67px;
    box-sizing: border-box;
}

.table-top-bar .menu-item {
    display: flex;
    align-items: center;
    font-weight: 500;
    color: rgba(0, 0, 0, 0.45);
}

.table-top-bar .menu-item li {
    cursor: pointer;
}

.table-top-bar .menu-item.menu-left li {
    margin-right: 25px;
}

.table-top-bar .menu-item.menu-right li {
    margin-left: 25px;
}

.table-top-bar .menu-item li .icon {
    vertical-align: text-bottom;
    margin-right: 5px;
}

.table-top-bar .menu-item li .desc {
    color: rgba(0, 0, 0, 0.25);
}

.table-top-bar .menu-item li:hover {
    opacity: 0.8;
}