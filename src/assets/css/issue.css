._css-cp {
    cursor: pointer;
}

._checkoutitems-title {
    height: 32px;
}

._checkoutitems-list {
    height: calc(100% - 32px);
    overflow-y: auto;
}

._css-leftfilter-part {
    height: 50%;
    box-sizing: border-box;
}

._css-dis {
    opacity: 0.3;
    pointer-events: none;
    cursor: not-allowed;
}

._css-taglist-opti-flag {
    position: absolute;
    right: 0;
    width: 20px;
    height: 20px;
    right: 15px;
    color: rgba(0, 0, 0, 0.45);
}

._css-taglist-opti-icon {
    width: 100px;
    height: 28px;
    border-radius: 4px;
    /* color:rgba(245,34,45,1);
    border:1px solid rgba(245,34,45,1);
    background-color: rgba(245,34,45,0.1); */
    border-width: 1px;
    border-style: solid;
    margin-left: 16px;
    line-height: 30px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

._css-taglist-options-i {
    height: 40px;
    display: flex;
    align-items: center;
    position: relative;
    cursor: pointer;
}

._css-taglist-options-i:hover {
    background-color: rgba(0, 0, 0, 0.04);
}

._css-taglist-options {
    overflow-y: auto;
    padding-top: 8px;
    padding-bottom: 8px;
    height: calc(100% - 64px);
    max-height: 180px;
}

._css-taglist-btn {
    display: flex;
    justify-content: space-around;
    align-items: center;
}

._css-taglist-btn-in {
    height: 40px;
    width: 152px;
    background-color: rgba(24, 144, 255, 1);
    color: #fff;
    border-radius: 4px;
    line-height: 40px;
    cursor: pointer;
}

._css-taglist-btn {
    height: 64px;
    box-sizing: border-box;
    border-top: 1px solid rgba(0, 0, 0, 0.09);
}

._css-taglistoptions {
    width: 200px;
    max-height: 300px;
    background-color: rgba(255, 255, 255, 1);
    box-shadow: 0px 2px 14px 0px rgba(26, 26, 26, 0.1);
    border-radius: 2px;
    position: fixed;
    z-index: 4;
    /* top:0;
    left:0; */
}

._css-detail-taglist {
    display: flex;
    flex-wrap: wrap;
    padding-bottom: 24px;
}

._css-detail-tagi {
    min-width: 76px;
    max-width: 112px;
    box-sizing: border-box;
    height: 28px;
    margin-right: 16px;
    margin-top: 16px;
    border-radius: 20px;
    border: 1px solid rgba(24, 144, 255, 1);
    position: relative;
    display: flex;
    align-items: center;
}

._css-detail-tagi-text {
    height: 20px;
    line-height: 20px;
    overflow-x: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    width: 100%;
    text-align: center;
    padding-left: 24px;
    padding-right: 24px;
}

._css-detail-tagi-delbtn {
    position: absolute;
    right: 4px;
    width: 16px;
    height: 16px;
    font-size: 16px;
    display: none;
    cursor: pointer;
}

._css-detail-tagi:hover ._css-detail-tagi-delbtn {
    display: block;
}

._css-tag-addbtn {
    border: 1px solid rgba(0, 0, 0, 0.45);
    background-color: rgba(0, 0, 0, 0.04);
    color: rgba(0, 0, 0, 0.45);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-left: 10px;
    padding-right: 10px;
}

._css-tagbtn-text {
    margin-left: 10px;
}

._css-detail-tagtitle {
    height: 22px;
    line-height: 22px;
    margin-top: 17px;
    font-size: 14px;
    text-align: left;
}

._css-detail-tags-in {
    width: calc(100% - 32px);
    border-bottom: 1px dashed rgba(217, 217, 217, 1);
}

._css-detail-tags {
    display: flex;
    justify-content: space-around;
}

._css-tooltip {
    position: fixed;
    width: 70px;
    height: 36px;
    background-color: rgba(0, 0, 0, 0.65);
    border-radius: 4px;
    /* top:200px;
    left:400px; */
    z-index: 10;
    display: flex;
    align-items: center;
    justify-content: space-around;
}

._css-tiptext {
    color: rgba(255, 255, 255, 1);
    font-size: 12px;
}

._css-detail-imageitem-patch {
    width: 20px;
    height: 100%;
    min-width: 20px;
}

._css-prevnext-detail-image {
    color: rgba(255, 255, 255, 0.85);
}

._css-prevnext-detail-image:hover {
    color: #fff;
}

._css-prev-detail-image {
    position: absolute;
    height: 50px;
    width: 16px;
    border: 1px solid transparent;
    left: 0;
    cursor: pointer;
    display: flex;
    align-items: center;
}

._css-next-detail-image {
    position: absolute;
    height: 50px;
    width: 16px;
    border: 1px solid transparent;
    right: 0;
    cursor: pointer;
    display: flex;
    align-items: center;
}

._css-issue-ccontent-text {
    margin-left: 8px;
    font-size: 12px;
    color: rgba(0, 0, 0, 0.45);
}

._css-issue-ccontent-icon {
    width: 16px;
    height: 16px;
    color: rgba(0, 0, 0, 0.45);
}

._css-issue-cbottom-content {
    margin-top: 8px;
    display: flex;
    align-items: center;
}

._css-issue-list-ctip-bottom {
    display: flex;
    align-items: center;
    height: calc(100% - 77px);
}

._css-issue-lrightpart-name {
    height: 22px;
    color: rgba(0, 0, 0, 0.85);
    font-size: 14px;
    line-height: 22px;
    text-align: left;
    font-weight: 500;
}

._css-issue-lrightpart-email {
    height: 22px;
    color: rgba(0, 0, 0, 0.45);
    font-size: 14px;
    line-height: 22px;
    text-align: left;
    font-weight: 500;
}

._css-issue-liconarea-rightpart {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    flex: 1;
    height: 100%;
    margin-left: 12px;
}

._css-issue-liconarea-icon {
    height: 40px;
    width: 40px;
    border-radius: 4px;
    background-color: rgba(40, 46, 61, 1);
    color: #fff;
    font-size: 12px;
    line-height: 40px;
    text-align: center;
}

._css-issue-listctop-iconarea {
    height: 46px;
    margin-bottom: 4px;
    width: 100%;
    display: flex;
    align-items: center;
}

._css-issue-list-ctip-top {
    height: 76px;
    border-bottom: 1px dashed rgba(217, 217, 217, 1);
    display: flex;
    align-items: center;
}

._css-issue-list-creator-tip {
    background-color: #fff;
    position: fixed;
    /* top:100px;
    left:100px; */
    z-index: 100;
    width: 240px;
    height: 124px;
    box-shadow: 0px 1px 3px 0px rgba(0, 21, 41, 0.12);
    border-radius: 2px;
    box-sizing: border-box;
    padding-left: 16px;
    padding-right: 16px;
}


/* KC */

.mv {
    position: absolute;
    top: 0px;
    left: 0px;
    z-index: 1001;
    background-color: #fff;
}

.mi {
    width: 869px;
    height: 420px;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 97;
}

._css-leftfilter {
    height: calc(100% - 76px);
}


/* 详情图标样式（旧版） */

._css-deletecurrentbtn {
    width: 24px;
    font-size: 24px;
    height: 24px;
}


/* 详情图标新换样式（新版） */

.slideshow-bottom-li {
    position: relative;
    overflow: visible !important;
}

.slideshow-bottom-li:hover ._css-deletecurrentbtn-small-pic {
    display: block;
}

._css-deletecurrentbtn-small-pic {
    width: 20px;
    font-size: 20px;
    height: 20px;
    border-radius: 10px;
    margin-top: 0;
    margin-left: 30px;
    display: none;
    position: absolute;
    top: -5px;
    right: -5px;
    color: rgba(0, 0, 0, 0.65)
}

._css-deletecurrentbtn-small-pic:hover {
    color: #1890FF;
}

._css-model-micon-container {
    position: relative;
}

._css-model-micon {
    position: absolute;
    right: 4px;
    top: 2px;
    color: #1890FF;
    width: 8px;
    height: 8px;
    font-size: 8px !important;
}

._css-model-micon._css-model-m18 {
    position: absolute;
    right: 4px;
    top: 4px;
    color: #1890FF;
    width: 18px;
    height: 18px;
    font-size: 18px;
}

._css-juserhead {
    height: 80px;
    width: 100%;
    border-bottom: 1px dashed #d9d9d9;
    display: flex;
    position: relative;
    align-items: center;
}

._css-juserhead-icon {
    width: 48px;
    height: 48px;
    border-radius: 4px;
    background-color: #282e3d;
    color: #ffffff;
    font-size: 12px;
    line-height: 48px;
}

._css-juserhead-right {
    margin-left: 12px;
    height: 46px;
    flex: 1;
    display: flex;
    flex-direction: column;
}

._css-juserhead-rtop {
    height: 22px;
    line-height: 22px;
    text-align: left;
    font-size: 14px;
    color: rgba(0, 0, 0, 0.85);
}

._css-juserhead-rbottom {
    margin-top: 2px;
    height: 22px;
    line-height: 22px;
    text-align: left;
    font-size: 14px;
    color: rgba(0, 0, 0, 0.45);
}

._css-juserbody {
    width: 100%;
    display: flex;
    flex-direction: column;
    height: calc(100% - 80px);
    justify-content: space-around;
    padding-top: 12px;
    padding-bottom: 12px;
    box-sizing: border-box;
}

._css-juserbody-top {
    width: 100%;
    height: 32px;
    display: flex;
    align-items: center;
    color: rgba(0, 0, 0, 0.45);
}

._css-juserbody-top:hover {
    background-color: rgba(0, 0, 0, 0.02);
}

._css-juserbody-icon {
    font-size: 16px;
    height: 16px;
    width: 16px;
    margin-left: 8px;
}

._css-juserbody-content {
    font-size: 12px;
    height: 20px;
    line-height: 20px;
    width: 156px;
    margin-left: 3px;
    text-align: left;
}

._css-juserbody-righticon {
    margin-left: 8px;
    width: 12px;
    height: 12px;
    font-size: 12px;
    line-height: 12px;
}

._css-juserbody-bottom {
    height: 32px;
    width: 100%;
    display: flex;
    align-items: center;
    color: rgba(0, 0, 0, 0.65);
}

._css-juserbody-bottom:hover {
    background-color: rgba(0, 0, 0, 0.02);
}

._css-detail-joinerdetail._css-showingitem {
    display: flex;
    flex-direction: column;
    position: fixed;
    /* top:0;
    left:0; */
    z-index: 1;
    width: 240px;
    height: 180px;
    background-color: rgba(255, 255, 255, 1);
    box-shadow: 0px 1px 3px 0px rgba(0, 21, 41, 0.12);
    border-radius: 2px;
}

._css-issuetype-select {
    font-size: 14px;
    width: 78px;
    cursor: pointer;
    margin-right: 10px;
    height: 24px;
    z-index: 1;
    background: rgba(24, 144, 255, 1);
}

._css-commentitem-rm {
    position: absolute;
    right: 12px;
    cursor: pointer;
}

._css-commentitem-rm:hover {
    color: rgba(24, 144, 255, 1);
}

._css-fileimg {
    height: 222px;
    margin-top: 10px;
    text-align: left;
    box-sizing: border-box;
    padding-bottom: 8px;
}

._css-fileimg-img {
    height: 100%;
    width: 255px;
    margin-left: 36px;
    box-sizing: border-box;
}

._css-fname {
    color: rgba(0, 0, 0, 0.25);
}

._css-commentdiv-container {
    border: 1px solid transparent;
    box-sizing: border-box;
    height: 100%;
    display: flex;
    flex-direction: column;
}

._css-comments-item {
    min-height: 72px;
    border-radius: 2px;
    background-color: rgba(0, 0, 0, 0.02);
    margin-top: 8px;
    border: 1px solid transparent;
    box-sizing: border-box;
}

._css-commentitem-headiconline {
    height: 20px;
    width: 100%;
    margin-top: 10px;
    display: flex;
    align-items: center;
    position: relative;
}

._css-commentitem-headicon {
    width: 20px;
    height: 20px;
    border-radius: 2px;
    background-color: #202020;
    margin-left: 8px;
    color: #fff;
    font-size: 12px;
    line-height: 20px;
    user-select: none;
}

._css-commentitem-headname {
    height: 20px;
    line-height: 20px;
    margin-left: 8px;
    color: rgba(0, 0, 0, 0.85);
}

._css-commentitem-time {
    height: 20px;
    line-height: 20px;
    margin-left: 12px;
    color: rgba(0, 0, 0, 0.45);
}

._css-filenameorcontent {
    margin-top: 12px;
    min-height: 20px;
    padding-left: 36px;
    padding-right: 12px;
    text-align: left;
    padding-bottom: 8px;
    box-sizing: border-box;
}

._css-comments {
    padding-left: 16px;
    padding-right: 16px;
    box-sizing: border-box;
    flex: 1;
    width: 100%;
    /* padding-top: 67px; */
    overflow-y: auto;
}

._css-comments-in {
    width: 100%;
}

._css-comment-subbtn {
    height: 24px;
    margin-top: 12px;
    padding-left: 16px;
}


/* 添加图片评论样式改变后得 父元素变动 */

._css-comment-subbtn {
    display: flex;
    align-items: center;
}

._css-comment-attachbtn {
    position: absolute;
    right: 28px;
    top: 12px;
    width: 20px;
    height: 20px;
    cursor: pointer;
    border-radius: 2px;
    font-size: 20px;
    z-index: 1;
}

._css-comment-textinput {
    margin-top: 16px;
    padding-left: 16px;
    padding-right: 16px;
    box-sizing: border-box;
    position: relative;
}

._css-comment-textinput-in {
    width: 100%;
    min-height: 64px;
    box-sizing: border-box;
    border-radius: 2px;
    padding: 0px 0px 0px 0px;
    text-align: left;
    position: relative;
}

._css-comment-textinput-in textarea {
    resize: none !important;
    outline: none;
    border: none;
    height: 100%;
    width: 100%;
    box-sizing: border-box;
    background-color: transparent;
}

._css-detail-attachmentitem-name {
    /* color:#1890FF; */
    color: #000;
    font-size: 12px;
    margin-left: 2px;
}

._css-detail-attachment-file:hover ._css-detail-attachmentitem-name {
    color: #1890ff;
    font-size: 12px;
    margin-left: 2px;
    cursor: pointer;
}

._css-detail-attachmentitem-icon {
    width: 16px;
    height: 16px;
    font-size: 16px;
    margin-left: 8px;
}

._css-detail-attachment-fleft {
    flex: 1;
    height: 100%;
    display: flex;
    align-items: center;
}

._css-detail-attachment-fright {
    width: 16px;
    height: 16px;
    line-height: 16px;
    cursor: pointer;
    margin-right: 16px;
}

._css-attach-download {
    margin-right: 16px;
}

._css-detail-attachment-file {
    height: 50px;
    width: 100%;
    margin-top: 12px;
    display: flex;
    align-items: center;
}

._css-detail-attachment-file:hover {
    background-color: rgba(0, 0, 0, 0.02);
}

._css-detail-localattachmenttext {
    font-size: 12px;
    color: rgba(0, 0, 0, 0.65);
    margin-left: 2px;
}

._css-detail-localattachmenticon {
    color: rgb(125, 137, 157);
}

._css-detail-attachment-btnlocal {
    width: 74px;
    height: 24px;
    display: flex;
    align-items: center;
    cursor: pointer;
    box-sizing: border-box;
}

._css-detail-attachment-btnlocal._css-notfirst {
    margin-left: 24px;
}

._css-detail-attachment-btndoclib {
    width: 92px;
    height: 20px;
    display: flex;
    align-items: center;
    margin-left: 32px;
    cursor: pointer;
}

._css-detail-attachment-btns {
    height: 24px;
    margin-top: 19px;
    display: flex;
    align-items: center;
    margin-left: 8px;
}

._css-detail-attachment-title {
    height: 22px;
    line-height: 22px;
    margin-top: 18px;
    font-size: 14px;
    color: rgba(0, 0, 0, 0.85);
    text-align: left;
    margin-left: 8px;
}

._css-detail-attachment-in {
    box-sizing: border-box;
    padding-bottom: 16px;
    margin-left: -8px;
}

._css-detail-attachment {
    box-sizing: border-box;
    padding-left: 16px;
    padding-right: 16px;
    width: 100%;
}

._css-relmodel-nametext {
    margin-left: 2px;
    height: 20px;
    line-height: 20px;
    font-size: 12px;
    color: rgba(0, 0, 0, 0.45);
}

._css-relmodel-btntext {
    margin-left: 2px;
    height: 20px;
    line-height: 20px;
    font-size: 12px;
    color: rgba(0, 0, 0, 0.65);
}

._css-relmodel-modelicon {
    height: 16px;
    width: 16px;
    font-size: 16px;
    color: rgb(219, 165, 55);
}

._css-relmodel-modeliconbtn {
    height: 16px;
    width: 16px;
    font-size: 16px;
    color: rgba(0, 0, 0, 0.65);
}

._css-relmodel-modelname {
    height: 20px;
    top: 2px;
    left: 0;
    position: absolute;
    display: flex;
    align-items: center;
}

._css-relmodel-modelviewbtn {
    height: 20px;
    bottom: 2px;
    left: 0;
    position: absolute;
    display: flex;
    align-items: center;
}

._css-relmodel-rpart {
    flex: 1;
    height: 100%;
    box-sizing: border-box;
    margin-left: 16px;
    position: relative;
}

._css-relmodel-img {
    width: 160px;
    height: 120px;
    box-sizing: border-box;
    background-image: url("/static/img/test.5981312.png");
    background-repeat: no-repeat;
}

._css-relmodel-imgarea {
    height: 120px;
    display: flex;
    align-items: center;
    margin-top: 13px;
}

._css-relmodel-intitle {
    margin-top: 17px;
    height: 22px;
    color: rgba(0, 0, 0, 0.85);
    font-size: 14px;
    line-height: 22px;
    text-align: left;
}

._css-relmodel-in {
    height: 188px;
    width: 100%;
    box-sizing: border-box;
    border-bottom: 1px dashed rgba(217, 217, 217, 1);
}

._css-relmodel {
    box-sizing: border-box;
    padding-left: 16px;
    padding-right: 16px;
    width: 100%;
}

._css-detail-joinericon {
    width: 20px;
    height: 20px;
    border-radius: 2px;
    background-color: #202020;
    font-size: 12px;
    color: #fff;
    line-height: 20px;
    text-align: center;
}

._css-detail-joinertext {
    font-size: 12px;
    color: #000;
    padding-left: 4px;
    height: 20px;
    line-height: 20px;
    box-sizing: border-box;
    overflow: hidden;
}

._css-detail-joiner._css-detail-joineradd {
    text-align: left;
    font-size: 12px;
    color: #1890ff;
    width: 46px;
    cursor: pointer;
    border: 1px solid transparent;
}

._css-detail-joinerdetail {
    position: fixed;
    z-index: 1;
    width: 240px;
    height: 180px;
    background-color: rgba(255, 255, 255, 1);
    box-shadow: 0px 1px 3px 0px rgba(0, 21, 41, 0.12);
    border-radius: 2px;
    display: none;
    padding-left: 16px;
    padding-right: 16px;
}

._css-detail-joiner {
    width: 84px;
    height: 22px;
    margin-left: 8px;
    /* border:1px solid red; */
    display: flex;
    align-items: center;
    margin-top: 8px;
    position: relative;
    cursor: pointer;
}

._css-detail-joineradding {
    width: 170px;
    height: 22px;
    margin-left: 8px;
    display: flex;
    align-items: center;
    margin-top: 8px;
}

._css-detail-joinersdata {
    margin-top: 8px;
    width: 100%;
    display: flex;
    flex-wrap: wrap;
    margin-left: -6px;
    max-height: 130px;
}

._css-detail-joinerstitle {
    height: 22px;
    line-height: 22px;
    margin-top: 17px;
    color: rgba(0, 0, 0, 0.85);
    font-size: 14px;
    text-align: left;
}

._css-detail-joiners-in {
    width: 100%;
    box-sizing: border-box;
    border-bottom: 1px dashed rgba(217, 217, 217, 1);
    min-height: 92px;
    padding-bottom: 18px;
}

._css-detail-joiners {
    box-sizing: border-box;
    width: 100%;
    padding-left: 16px;
    padding-right: 16px;
}

._css-issueitem-endtimeval {
    color: #1890ff;
    cursor: pointer;
}

._css-issueitem-endtimevalro {
    color: rgba(0, 0, 0, 0.45);
}

._css-issueitem-timetitle {
    height: 22px;
    margin-top: 21px;
    text-align: left;
    font-size: 14px;
    font-weight: 400;
}

._css-issueitem-timevalue {
    height: 20px;
    margin-top: 15px;
    text-align: left;
    line-height: 20px;
}

._css-issueitem-createtime {
    height: 100%;
    width: 150px;
}

._css-issueitem-surplustime {
    height: 100%;
    width: 150px;
}

._css-issueitem-endtime {
    height: 100%;
    width: 150px;
    margin-left: 14px;
}

._css-issueitemarea-in {
    height: 100%;
    box-sizing: border-box;
    border-bottom: 1px dashed rgba(217, 217, 217, 1);
    display: flex;
    align-items: center;
}

._css-issueitemarea {
    width: 100%;
    height: 96px;
    padding-left: 16px;
    padding-right: 16px;
    box-sizing: border-box;
}

._css-listbtn {
    font-size: 20px;
    width: 20px;
    height: 20px;
    cursor: pointer;
    margin-right: 16px !important;
}

._css-closebtn {
    font-size: 20px;
    width: 20px;
    height: 20px;
    cursor: pointer;
    margin-right: 18px;
}

._css-tc-number {
    margin-left: 4px;
    height: 16px;
    width: 16px;
    line-height: 16px;
    background-color: rgba(250, 84, 28, 0.1);
    color: #f5222d;
    font-size: 12px;
    text-align: center;
}

.c1890ff,
._css-tc-clicked {
    color: #1890ff !important;
}

._css-tc-item:hover {
    background-color: rgba(0, 0, 0, 0.04);
}

._css-tc-detail {
    height: 22px;
    padding-left: 11px;
    padding-right: 11px;
    padding-top: 1px;
    padding-bottom: 1px;
    line-height: 22px;
    text-align: left;
    font-size: 14px;
    cursor: pointer;
    margin-left: 16px;
    display: flex;
    align-items: center;
}

._css-tc-comments {
    height: 22px;
    /* width:50px; */
    padding-left: 11px;
    padding-right: 11px;
    padding-top: 1px;
    padding-bottom: 1px;
    line-height: 22px;
    text-align: left;
    font-size: 14px;
    color: rgba(0, 0, 0, 0.85);
    cursor: pointer;
    margin-left: 32px;
    display: flex;
    align-items: center;
}

._css-tc-activities {
    height: 22px;
    padding-left: 11px;
    padding-right: 11px;
    padding-top: 1px;
    padding-bottom: 1px;
    line-height: 22px;
    text-align: left;
    font-size: 14px;
    color: rgba(0, 0, 0, 0.85);
    cursor: pointer;
    margin-left: 32px;
    display: flex;
    align-items: center;
}

._css-global-hover {
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
    background-color: #202020;
    opacity: 0.3;
    position: fixed;
}

._css-global-hover_click {
    background: transparent;
}

._css-rightpart {
    position: absolute;
    left: 291px;
    top: 0px;
    background-color: rgba(0, 0, 0, 0.02);
    width: calc(100% - 292px);
    height: 100%;
    z-index: 2;
    overflow-x: hidden;
}

._css-rightopen ._css-rightpart {
    background-color: #fff;
}

._css-rightpart {
    border-radius: 8px;
}

.el-loading-mask {
    border: 0;
}

._css-rightparthead {
    height: 56px;
    background-color: rgba(0, 0, 0, 0.04);
    display: flex;
    align-items: center;
}

._css-rightpartheadleft {
    flex: none;
    min-width: 200px;
    height: 100%;
    display: flex;
    align-items: center;
}

._css-rightpartheadright {
    flex: 1;
    height: 100%;
    display: flex;
    flex-direction: row-reverse;
    align-items: center;
}

._css-rightpartbody {
    height: calc(100% - 56px);
    min-width: 510px;
    overflow-y: auto;
    overflow-x: hidden;
}

._css-issue-attachitemicon {
    height: 100%;
    width: 16px;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    color: rgba(0, 0, 0, 0.25);
}

._css-issue-attachitemicon div {
    margin-top: 1px;
}

._css-issue-attachitemnum {
    height: 100%;
    width: 16px;
    box-sizing: border-box;
    line-height: 20px;
    color: rgba(0, 0, 0, 0.25);
}

._css-issue-attachitem {
    width: 32px;
    height: 20px;
    display: flex;
    align-items: center;
    margin-right: 16px;
}

._css-issue-typeitemtext {
    overflow-x: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
}

._css-issue-typeitem {
    min-width: 28px;
    height: 20px;
    font-size: 12px;
    border-radius: 2px;
    padding-left: 16px;
    padding-right: 16px;
    max-width: 108px;
    display: flex;
    align-items: center;
    justify-content: space-around;
    margin-left: 8px;
}

._css-issue-type {
    width: 116px;
    flex: none;
    height: 100%;
    display: flex;
}

._css-issue-icons {
    flex: 1;
    height: 100%;
    display: flex;
    flex-direction: row-reverse;
    align-items: center;
}

._css-issue-issueitembleft {
    flex: 1;
    height: 100%;
    display: flex;
    align-items: center;
}

._css-issue-issueitembright {
    width: 24px;
    height: 24px;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    margin-right: 8px;
    border-radius: 2px;
    font-size: 12px;
    color: #fff;
    background-color: #202020;
    line-height: 24px;
}

._css-issue-issueitemareatop {
    margin-top: 11px;
    min-height: 22px;
    text-align: left;
    padding-right: 2px;
    box-sizing: border-box;
    padding-left: 8px;
}

._css-issue-issueitemareamiddle {
    height: 20px;
    margin-top: 7px;
    color: rgba(0, 0, 0, 0.45);
    text-align: left;
    margin-left: 8px;
}

._css-issue-issueitemareabottom {
    height: 24px;
    margin-top: 8px;
    display: flex;
    align-items: center;
}

._css-issue-issueitem {
    transition: margin 0.3s;
    min-height: 96px;
    margin-left: 16px;
    margin-right: 16px;
    margin-top: 16px;
    box-shadow: 0px 1px 1px 0px rgba(0, 21, 41, 0.12);
    border-radius: 2px;
    background: rgba(240, 242, 245, 1);
    border: 1px solid transparent;
    padding-bottom: 8px;
    user-select: none;
    width: 259px;
    position: relative;
}

._css-issue-issueitem._css-rightopen {
    transition: margin 0.3s;
    background-color: #fff;
    margin-right: 0;
    margin-left: 26px;
    border-left: 6px solid #1890ff;
    border-top-width: 0;
    border-bottom-width: 0;
    padding-top: 2px;
    z-index: 3;
    position: relative;
}

._css-issue-statusitembody {
    height: calc(100% - 40px);
    overflow-y: auto;
    box-sizing: border-box;
    padding-bottom: 16px;
    width: 292px;
    border: 0;
    border-bottom-left-radius: 8px;
    border-bottom-right-radius: 8px;
}

#id_09ed34b7-a937-4960-9252-93c75c84e137,
#id_83e7fc4a-6bf1-4eed-83a8-820d6ea982c5 {
    background: #D9E7F6;
    border: 0;
}

#id_352d98e2-1c49-450f-93cd-1aa08caba6cd,
#id_9744e84c-a1c0-460f-9ba0-ab88719931f0 {
    background: #F0EADE;
    border: 0;
}

._css-issue-statusitemhead-in {
    width: 100%;
    user-select: none;
    color: rgba(255, 255, 255, 1);
    font-size: 16px;
}

._css-issue-statusitemhead {
    height: 40px;
    border: 1px dotted transparent;
    /* border-right: 1px solid rgba(0, 0, 0, 0.02); */
    box-sizing: border-box;
    display: flex;
    align-items: center;
    background-color: rgba(0, 0, 0, 0.04);
    width: 292px;
    border-top-left-radius: 8px;
    border-top-right-radius: 8px;
}

._css-showandeven ._css-issue-statusitemhead {
    background: #3799F5;
}

._css-showandodd ._css-issue-statusitemhead {
    background: #FAAD14;
}

._css-issue-statusitem {
    transition: width 0.3s;
    height: 100%;
    box-sizing: border-box;
    width: 292px;
    border-radius: 8px;
    position: relative;
    flex: none;
    margin-right: 4px;
}

._css-issue-statusitem._css-rightopen {
    transition: width 0.3s;
    width: 802px;
}

._css-issue-newissue {
    margin-left: 24px;
    justify-content: center !important;
}

._css-createissue-btnparent {
    width: 120px;
    height: 40px;
    border-radius: 2px;
    cursor: pointer;
    margin-left: 16px;
    background-color: rgb(24, 144, 255);
    line-height: 40px;
    color: #fff;
    position: relative;
}

._css-issue-btns {
    position: absolute;
    width: 100%;
    left: 0;
    top: calc(100% + 2px);
    border-radius: 2px;
    border: 1px solid transparent;
    background-color: #fff;
    box-sizing: border-box;
    z-index: 4;
    padding-top: 2px;
    padding-bottom: 2px;
    box-shadow: 0px 0px 8px 0px rgba(0, 0, 0, 0.15);
}

._css-issue-btn {
    height: 32px;
    line-height: 32px;
    color: rgba(0, 0, 0, 0.65);
}

._css-issue-btn:hover {
    background-color: rgba(230, 247, 255, 1);
}

._css-issue-filterissue {
    margin-left: 24px;
    font-size: 20px !important;
    height: 20px;
    width: 20px;
    color: #007AFF;
}

._css-issue-headinneritem {
    height: 100%;
    display: flex;
    align-items: center;
}

._css-issue-headinner {
    height: 100%;
}

._css-issue-all {
    height: 100%;
    box-sizing: border-box;
}

._css-issue-head {
    box-sizing: border-box;
    height: 54px;
    padding-left: 0;
    display: flex;
    align-items: center;
    padding-right: 16px;
}

._css-head-left {
    display: flex;
    flex: 1;
    align-items: center;
}

._css-head-right {
    width: 240px;
}

._css-issue-body {
    height: calc(100% - 54px);
    /* height: 100%; */
    padding: 0 16px;
    box-sizing: border-box;
}

._css-issue-body-in {
    height: 100%;
    width: 100%;
    display: flex;
    overflow-x: auto;
}

#sq_drawer {
    position: fixed;
    left: -260px;
    top: 0;
    z-index: 55;
    width: 202px;
    height: 100%;
    background: #fff;
    transition: all 0.5s ease;
    overflow: auto;
}

#sq_drawer p {
    padding-left: 16px;
    text-align: left;
}

.el-row {
    margin-bottom: 20px;
}

.el-col {
    border-radius: 4px;
}

.bg-purple-dark {
    text-align: left;
    width: 260px;
    height: 56px;
    background: #F5F5F5;
}

.bg-purple-dark-header {
    font-size: 14px;
    padding-left: 35px;
    height: 56px;
    line-height: 56px;
    color: #000000;
    background: url("../../assets/images/left_guide.png") no-repeat 16px 18px;
    background-size: 20px 20px;
    cursor: pointer;
}

.bg-purple {
    background: #d3dce6;
}

.grid-content {
    line-height: 36px;
    min-height: 36px;
}

.row-bg {
    padding: 10px 0;
    background-color: #f9fafc;
}


/* 过滤 */

.checkoutbg {
    display: none;
    width: 24px;
    height: 24px;
    background-size: 100% 100%;
    float: right;
}

.checkoutitems {
    margin-bottom: 16px;
    margin-left: 12px;
    padding: 4px;
    width: 180px;
    height: 24px;
    font-size: 12px;
    font-family: PingFangSC-Medium;
    font-weight: 500;
    color: #FAFAFA;
    cursor: pointer;
}

.checkoutitems:hover {
    background: #FAFAFA;
}

.checkoutitems>div:first-of-type {
    float: left;
}

.checkoutitemname {
    width: 116px;
    height: 24px;
    float: left;
    line-height: 24px;
}

.checkoutitemname_people {
    width: 116px;
    height: 24px;
    float: left;
    color: black;
    line-height: 24px;
    text-align: left;
    padding-left: 5px;
}

.checkoutitems_people:hover {
    background: #FAFAFA;
}

.checkoutitem>.checkoutbg {
    display: block;
}


/* 排序按钮下拉框样式 */

.el-dropdown-menu {
    margin-right: -50px;
}

.el-dropdown-menu__item {
    height: 40px;
}


/* 排序按钮样式 */

._css-issue-all .el-dropdown-selfdefine {
    display: block;
    width: 24px;
    height: 26px;
    color: rgba(0, 0, 0, 0.25);
    background-color: rgba(0, 0, 0, 0);
    line-height: 24px;
    user-select: none;
    cursor: pointer;
    margin-left: 24px;
}


/* 排序按钮图标 */

.icon-interface-problem-sorting {
    vertical-align: middle;
    font-size: 20px !important;
    color: #007AFF;
}


/* 树型下拉样式 */

.el-menu-demo {
    background: rgba(0, 0, 0, 0) !important;
    text-align: center;
    width: 36px !important;
}

.pull-down-list div:first-of-type {
    display: none !important;
}

.el-menu--popup {
    min-width: 150px;
    text-align: left;
}

.el-menu--horizontal .el-menu .el-menu-item .el-menu--horizontal .el-menu .el-submenu__title {
    height: 40px;
    line-height: 40px;
    padding-left: 24px;
    color: rgba(0, 0, 0, 1);
}

.el-menu .el-menu-item:hover {
    background: rgba(0, 0, 0, 0.04);
}

.el-menu .el-submenu__title:hover {
    background: rgba(0, 0, 0, 0.04);
}

.el-submenu .el-menu-item {
    min-width: 120px;
}

._css-test ._css-test-test {
    text-align: left;
    margin-left: 20px;
    padding-left: 0;
}


/* 树型下拉下拉图标 */

.icon-arrow-right_outline-rightO {
    font-size: 13px;
    /* margin-left: 93px; */
    float: right;
    margin-top: 13px;
}

.icon-arrow-right_outline-rightT {
    font-size: 13px;
    /* margin-left: 120px; */
    float: right;
    margin-top: 13px;
}

._css-listbtn-i {
    margin-right: 10px;
}


/* 老版本hover显示 */

.father .child {
    display: none;
}

.father:hover .child {
    display: block;
}

._css-detail-joiner-joiner {
    position: fixed;
    z-index: 1;
    width: 240px;
    height: 180px;
    background-color: rgba(255, 255, 255, 1);
    box-shadow: 0px 1px 3px 0px rgba(0, 21, 41, 0.12);
    border-radius: 2px;
    padding-left: 16px;
    padding-right: 16px;
}

._css-joiner-juserhead {
    height: 80px;
    width: 100%;
    border-top: 1px dashed #d9d9d9;
    border-bottom: 1px dashed #d9d9d9;
    display: flex;
    position: relative;
}

._css-joiner-juserhead-right {
    margin-left: 12px;
    height: 66px;
    flex: 1;
    display: flex;
    flex-direction: column;
}

.archived-file {
    width: 60px;
    height: 60px;
    font-size: 60px !important;
    position: absolute;
    top: 18px;
    right: 0;
}


/* 问题列表轮播样式 */

.slideshow-picture-list {
    width: 100%;
    height: 186px;
    position: relative;
}


/* .slideshow-picture-list-title{
  } */

.slideshow-picture-list>.slideshow-top-list>.slideshow-top-list-ul {
    padding: 0;
    overflow: hidden;
}

.slideshow-top-list>.slideshow-top-list-ul {
    width: 100%;
    height: 186px;
    margin-top: 0;
    padding-inline-start: 0px;
}

.slideshow-top-list>.slideshow-top-list-ul>.slideshow-top-list-li>._css-image-img {
    width: 100%;
    height: 100%;
    float: left;
}

.slideshow-top-list>.slideshow-top-list-ul>.slideshow-top-list-ul-li {
    display: inline-block;
}

.slideshow-bottom-list {
    display: flex;
    width: 100%;
    height: 50px;
    border-radius: 0px 0px 2px 2px;
    background: rgba(0, 0, 0, 0.45);
    position: absolute;
    bottom: 0;
    padding: 0;
    align-items: center;
}

.slideshow-bottom-list>.slideshow-bottom-list-ul {
    display: flex;
    list-style: none;
    padding-inline-start: 0px;
    margin: 0 auto;
    padding: 0;
    width: 259px;
    height: 50px;
    align-items: center;
    overflow-y: hidden;
    overflow-x: auto;
}

.slideshow-bottom-list>.slideshow-bottom-list-ul>.slideshow-bottom-list-li {
    width: 28px;
    min-width: 28px;
    height: 28px;
    background: rgba(255, 255, 255, 1);
    margin-left: 12px;
    padding: 0;
    cursor: pointer;
    transition: transform 0.3s;
    box-shadow: 0px 1px 1px 0px rgba(0, 21, 41, 0.12);
    border-radius: 2px;
}

.slideshow-bottom-list>.slideshow-bottom-list-ul>.slideshow-bottom-list-li:hover {
    transform: scale(1.3);
}

.slideshow-bottom-list>.slideshow-bottom-list-ul>.slideshow-bottom-list-li>._css-image-img {
    width: 100%;
    height: 100%;
}


/* 详情页轮播样式 */

.slideshow-picture {
    width: 100%;
    height: 360px;
    position: relative;
    margin-top: 12px;
}

.slideshow-picture-title {
    width: 100%;
    height: 40px;
    line-height: 40px;
    border-radius: 2px 2px 0px 0px;
    background: rgba(0, 0, 0, 0.2);
    display: flex;
    align-items: center;
    position: relative;
}

.slideshow-picture>.slideshow-top>.slideshow-top-ul {
    list-style: none;
    padding: 0;
    overflow: hidden;
}

.slideshow-top>.slideshow-top-ul {
    width: 100%;
    height: 320px;
    margin-top: 0;
    padding-inline-start: 0px;
}

.slideshow-top-li {
    cursor: pointer;
}

.slideshow-bottom {
    display: flex;
    width: 100%;
    height: 80px;
    border-radius: 0px 0px 2px 2px;
    background: rgba(0, 0, 0, 0.45);
    position: absolute;
    bottom: 0;
    align-items: center;
    padding-left: 16px;
    padding-right: 16px;
    box-sizing: border-box;
}

.slideshow-bottom>.slideshow-bottom-ul {
    display: flex;
    padding-inline-start: 0px;
    align-items: center;
    overflow-x: auto;
    overflow-y: hidden;
    height: 100%;
}

.slideshow-bottom>.slideshow-bottom-ul>.slideshow-bottom-li {
    width: 50px;
    min-width: 50px;
    height: 50px;
    background: rgba(255, 255, 255, 1);
    margin-left: 24px;
    overflow: hidden;
    cursor: pointer;
    transition: all 300ms;
    box-shadow: 0px 1px 1px 0px rgba(0, 21, 41, 0.12);
    border-radius: 2px;
}

.slideshow-bottom>.slideshow-bottom-ul>.slideshow-bottom-li:hover {
    transform: scale(1.3);
}

.el-menu-item i {
    vertical-align: text-bottom;
}

._css-listbtn-i {
    vertical-align: middle;
}

._css-issue-filterissue>.icon-interface-filter {
    font-size: 24px !important;
}

._css-issue-filterissue>.icon-interface-filter：hover {
    color: blue !important;
}

.icon-interface-folder-issue {
    font-size: 24px !important;
}

.el-col-4 {
    width: 24px;
}


/* 详情添加附件和图片按钮样式 */

._css-detail-attachment-btnlocal>.icon-interface-model-picture {
    font-size: 20px;
}

._css-detail-attachment-btnlocal>.icon-interface-model-picture:hover {
    color: #1890ff;
}

._css-detail-attachment-btnlocal>.icon-interface-folder {
    font-size: 24px;
}

._css-detail-attachment-btnlocal>.icon-interface-folder:hover {
    color: #1890ff;
}


/* 详情页面添加参与人盒子样式 */

.add_joiner {
    width: 362px;
    height: 506px;
    background: red;
    position: absolute;
    z-index: 4;
    padding-left: 24px;
    padding-right: 24px;
}

.add_joiner_header {
    width: 362px;
    height: 64px;
    margin-top: 0;
}

.add_joiner_header-left {
    width: 100px;
    height: 24px;
    font-size: 20px;
    font-family: PingFangSC-Medium;
    font-weight: 500;
    color: rgba(0, 0, 0, 1);
    line-height: 24px;
    margin-top: 20px;
    float: left;
}

.add_joiner_header-right {
    width: 24px;
    height: 24px;
    float: right;
    margin-top: 20px;
    font-size: 24px;
}

.add_joiner_input {
    width: 362px;
    height: 40px;
    border-radius: 20px;
    box-shadow: 0px 1px 1px 0px rgba(0, 21, 41, 0.12);
    background: rgba(255, 255, 255, 1);
}

.add_joiner_center {
    width: 362px;
    height: 300px;
    background: blue;
    margin-top: 16px;
}

.add_joiner_footer {
    width: 362px;
    height: 86px;
}


/* 归档按钮组件形式 样式 */

._css-settingchecboxorswitch {
    width: 78px;
    height: 24px;
    background: rgba(0, 0, 0, 0);
    font-size: 24px;
    color: rgba(0, 0, 0, 0.65);
    margin-left: 26px;
    display: flex;
    align-items: center;
}


/* 详情中 剩余时间样式 */

._css-issueitem-timevalue-sy {
    font-size: 12px;
    font-family: PingFangSC-Regular;
    font-weight: 400;
    color: rgba(0, 0, 0, 0.45);
}

._css-issueitem-timevalue-cj {
    color: rgba(0, 0, 0, 0.45);
}


/* 树型二级下拉样式（手写下拉） */

.drop-down-tree {
    width: 43px;
    height: 22px;
    font-size: 14px !important;
    font-weight: 500;
    color: rgba(0, 0, 0, 1);
    line-height: 22px;
    cursor: pointer;
    margin-right: 30px;
    position: relative;
    overflow: visible;
}

.drop-down-tree-T {
    width: 150px;
    /* height: 160px; */
    position: absolute;
    top: 48px;
    right: 0;
    padding-top: 4px;
    padding-bottom: 4px;
    background: rgba(255, 255, 255, 1);
    /* background: blue; */
    cursor: pointer;
    z-index: 4;
}

.drop-down-tree-P {
    width: 150px;
    height: 31px;
    line-height: center;
    padding-top: 9px;
    display: flex;
    align-items: center;
    padding-left: 16px;
}

.drop-down-tree-P:hover {
    background-color: rgba(0, 0, 0, 0.02);
}

.drop-down-tree-P-center {
    display: inline-block;
    width: 66px;
    height: 22px;
    font-size: 14px;
    font-weight: 400;
    color: rgba(0, 0, 0, 1);
    line-height: 22px;
    text-align: left;
    overflow-x: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.drop-down-tree-P-left {
    display: inline-block;
    width: 16px;
    height: 16px;
    font-size: 16px;
    line-height: 16px;
    vertical-align: middle;
}

.drop-down-tree-P-right {
    display: inline-block;
    width: 12px;
    height: 12px;
    font-size: 12px;
}

.drop-down-tree-P-Slist {
    z-index: 4;
    position: fixed;
    background: #fff;
    text-align: left;
    padding-top: 4px;
    padding-bottom: 4px;
    cursor: pointer;
}

.drop-down-tree-P-Slist-Status {
    width: 96px;
    height: 40px;
    line-height: 40px;
    padding-left: 24px;
    overflow-x: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
}

.drop-down-tree-P-Slist-Status:hover {
    background: rgba(0, 0, 0, 0.04);
}

.drop-down-tree-P-Slist-Type {
    width: 96px;
    height: 40px;
    line-height: 40px;
    padding-left: 24px;
    overflow-x: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.drop-down-tree-P-Slist-Type:hover {
    background: rgba(0, 0, 0, 0.04);
}

._css-comment-attachbtn-new {
    display: flex;
    align-items: center;
    width: 70px;
    height: 20px;
    font-size: 12px;
    font-family: PingFangSC-Regular;
    font-weight: 400;
    color: rgba(0, 0, 0, 1);
    line-height: 20px;
    cursor: pointer;
    vertical-align: middle;
    margin-left: 24px;
}

._css-comment-attachbtn-new-icon {
    display: inline-block;
    width: 16px;
    height: 16px;
    font-size: 16px;
    margin-right: 2px;
}

.issue-details-deadline .el-date-editor--datetime {
    width: 100%;
}

.issue-details-deadline .el-date-editor--datetime .el-input__inner {
    cursor: pointer;
    padding: 0;
    line-height: inherit;
    color: #1890ff;
}


/* 详情页面活动区域样式 */

._css-activities {
    width: 478px;
    margin-top: 16px;
    margin-left: 16px;
}

._css-activities-container {
    height: 50px;
    display: flex;
    align-items: center;
}

._css-activities-joiner {
    height: 22px;
    font-size: 14px;
    font-family: PingFangSC-Regular;
    font-weight: 400;
    color: rgba(0, 0, 0, 1);
    line-height: 22px;
    text-align: left;
    margin-right: 8px;
}

._css-activities-ico {
    width: 24px;
    height: 24px;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    margin-right: 8px;
    border-radius: 2px;
    font-size: 12px;
    color: #fff;
    background-color: #202020;
    line-height: 24px;
    margin-left: 12px;
}

._css-activities-operation {
    max-width: 200px;
    height: 22px;
    font-size: 14px;
    font-family: PingFangSC-Regular;
    font-weight: 400;
    color: rgba(0, 0, 0, 1);
    line-height: 22px;
    text-align: left;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

._css-activities-time {
    width: 160px;
    height: 20px;
    font-size: 12px;
    font-weight: 400;
    color: rgba(0, 0, 0, 0.45);
    line-height: 20px;
    padding-left: 10px;
    text-align: left;
}


/* 分类上卡下拉提示 */

._css-issue-statusitembody-downtip {
    width: 292px;
    height: 16px;
    /* background: red; */
    background: rgba(0, 0, 0, 0.25);
    position: absolute;
    bottom: 0;
    border-radius: 8px;
    font-size: 16px;
    cursor: pointer;
}

._css-issue-statusitembody-uptip {
    width: 292px;
    height: 16px;
    background: rgba(0, 0, 0, 0.25);
    position: absolute;
    top: 40px;
    border-radius: 8px;
    font-size: 16px;
    cursor: pointer;
}


/* 问题详情模态框 蒙版 样式 */

._css-bgpicture-shade {
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 3;
    background: rgba(0, 0, 0, 0.45);
    position: fixed;
}


/* 问题详情模态框 样式 */

._css-bgpicture-model {
    width: 60%;
    height: 60%;
    background: rgba(255, 255, 255, 1);
    position: fixed;
    left: 20%;
    top: 20%;
    z-index: 3;
    display: flex;
    justify-content: space-around;
    align-items: center;
}

._css-bgpicture-model_close {
    width: 30px;
    height: 30px;
    line-height: 30px !important;
    font-size: 30px;
    background: rgba(255, 255, 255, 0.4);
    border-radius: 15px;
    position: absolute;
    top: -15px;
    right: -15px;
    cursor: pointer;
}

._css-bgpicture-model_close:hover {
    background: #1890FF;
}

._css-bgpicture-model_img {
    line-height: center;
    width: 100%;
    height: 100%;
}

._css-attach-actbtn {
    cursor: pointer;
}

._css-attach-actbtn:hover {
    color: #1890ff;
}

._css-issue-body-in._css-empty {
    justify-content: space-around;
}

.issue-content {
    height: calc(100% - 50px - 32px);
    margin: 16px 20px;
    background: #fff;
}

.is-set-icon {
    cursor: pointer;
    width: 20px;
    height: 20px;
    background-image: url('../../assets/images/p-bq.png');
    background-size: contain;
    background-repeat: no-repeat;
    margin: 0 20px;
}

.labelSetDialog {
    position: fixed;
    left: -240px;
    top: 0;
    z-index: 55;
    width: 240px;
    height: 100%;
    background: #fff;
    transition: all 0.5s ease;
    overflow: auto;
}