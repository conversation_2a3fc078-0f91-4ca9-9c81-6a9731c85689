/* 插件覆盖（class名以el开头） */

.el-main {
    padding: 0;
}

.el-header {
    padding-left: 0;
    padding-right: 0;
    background-color: #b3c0d1;
    color: #333;
    line-height: 60px;
}

.el-aside {
    color: #333;
}

.el-input__icon {
    line-height: 100%;
    display: flex;
    align-items: center;
}

.el-menu {
    border-right: 1px solid transparent;
}

.el-table td,
.el-table th {
    padding-top: 0;
    padding-bottom: 0;
    padding-left: 0;
}

.el-table th {
    width: 275px;
    height: 22px;
    font-weight: 500;
    color: rgba(0, 0, 0, 1);
    line-height: 22px;
}

.el-table td {
    border-color: transparent;
}

.el-button:not(.el-picker-panel__link-btn) {
    padding: 0;
    border-radius: 0;
}

.el-button.el-picker-panel__link-btn {
    margin-right: 10px;
}

.el-message-box__btns>.el-button {
    width: 56px;
    height: 32px;
}

.custom-eltable .is-right .cell {
    display: block;
}

.el-input__inner {
    height: unset;
    /* padding: 0; */
    border-radius: 0;
    border-width: 0;
    background-color: transparent;
    /* cursor: pointer; */
}

.h24 .el-input__inner {
    height: 24px;
}

.h30 .el-input__inner {
    height: 30px;
}

.el-table .cell {
    line-height: 50px;
    display: flex;
    align-items: center;
}

.el-table.css-miniline {
    font-size: 14px;
    color: #283A4F;
}

._css-customstyle .el-tree-node__content {
    margin-left: 8px;
}

.el-table.css-miniline .cell {
    line-height: 36px;
    display: flex;
    align-items: center;
    padding-left: 24px;
    padding-right: 24px;
    border-right: 1px solid rgba(0, 0, 0, 0.1);
}

.el-table.css-miniline th .cell {
    line-height: 50px;
    background-color: #F7F7F7;
    font-size: 14px;
    color: #616F7D;
    font-weight: unset !important;
    padding-left: 24px;
    padding-right: 24px;
}

.el-table.el-table.css-miniline .gutter {
    background-color: #f7f7f7;
}


/* .iconhide .el-submenu__icon-arrow{
    display: none !important;
  } */


/* 二级树型下拉组件里默认样式箭头 */

._css-test3 .el-submenu__icon-arrow {
    display: none !important;
}

._css-test4 .el-submenu__icon-arrow {
    display: none !important;
}

.el-input-group__append,
.el-input-group__prepend {
    padding: 0;
    margin: 0;
    border-width: 1px;
    border-left: 1px solid transparent;
}


/* .el-table__header-wrapper{
    height:32px;
  } */

.el-upload-dragger {
    border: 1px solid transparent;
}

.el-button+.el-button {
    margin-left: unset;
}


/* el-table__body-wrapper */


/* .el-table__body-wrapper ::-webkit-scrollbar {
    width: 4px;
    height: 4px;
  }
  .el-table__body-wrapper::-webkit-scrollbar-thumb {
    border-radius: 5px;
    -webkit-box-shadow: inset 0 0 5px rgba(0,0,0,0.2);
    background: rgba(0,0,0,0.2);
  }
  .el-table__body-wrapper::-webkit-scrollbar-track {
    -webkit-box-shadow: inset 0 0 5px rgba(0,0,0,0.2);
    border-radius: 0;
    background: rgba(0,0,0,0.1);
  } */

.css-fixedleftgbcolor .el-table__fixed {
    background-color: rgba(0, 0, 0, 0.02);
}

.css-fixedleftgbcolor-white .el-table__fixed {
    background-color: #fff;
}

.css-fixtreeicon .el-icon-arrow-right:before {
    content: url(../svgs_loadbyurl/suggested-plus_square.svg);
}

.css-fixtreeicon .el-table__expand-icon>.el-icon {
    left: 0 !important;
    top: 0 !important;
    margin-left: 0 !important;
    margin-top: 0 !important;
}

.css-fixtreeicon .el-table__expand-icon {
    width: 20px !important;
}

.el-table--scrollable-x .el-table__body-wrapper::-webkit-scrollbar {
    /*滚动条整体样式*/
    width: 6px;
    /*高宽分别对应横竖滚动条的尺寸*/
    height: 6px;
}

.el-table--scrollable-x .el-table__body-wrapper::-webkit-scrollbar-thumb {
    /*滚动条里面小方块*/
    border-radius: 5px;
    -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
    background: rgba(0, 0, 0, 0.2);
}

.el-table--scrollable-x .el-table__body-wrapper::-webkit-scrollbar-track {
    /*滚动条里面轨道*/
    -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
    border-radius: 5px;
    background: rgba(0, 0, 0, 0.1);
}

.el-table--scrollable-y .el-table__body-wrapper::-webkit-scrollbar {
    /*滚动条整体样式*/
    width: 6px;
    /*高宽分别对应横竖滚动条的尺寸*/
    height: 6px;
}

.el-table--scrollable-y .el-table__body-wrapper::-webkit-scrollbar-thumb {
    /*滚动条里面小方块*/
    border-radius: 5px;
    -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
    background: rgba(0, 0, 0, 0.2);
}

.el-table--scrollable-y .el-table__body-wrapper::-webkit-scrollbar-track {
    /*滚动条里面轨道*/
    -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
    border-radius: 5px;
    background: rgba(0, 0, 0, 0.1);
}

.css-nomask .el-loading-mask {
    background-color: transparent !important;
}

.el-screen-menu .el-input__inner {
    padding-left: 16px;
    height: 40px;
    border-radius: 4px;
    border: 1px solid rgba(0, 0, 0, .09);
    color: rgba(0, 0, 0, .85)
}

.el-screen-menu input::-webkit-input-placeholder {
    color: rgba(0, 0, 0, .85)
}

.el-screen-menu .el-state {
    height: 50px;
    line-height: 50px;
    display: flex;
    flex-direction: row
}

.el-screen-menu .el-state span {
    flex: 2;
    overflow: hidden;
}

.el-screen-menu .el-state span div {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.el-screen-menu .el-state>div {
    flex: 1
}

.el-screen-menu .el-element {
    height: 124px;
    display: flex;
    flex-direction: row;
    padding-top: 20px;
}

.el-screen-menu .el-left-element {
    flex: 1;
}

.el-screen-menu .el-right-element {
    flex: 2;
    display: flex;
    flex-direction: column;
}

.el-screen-menu .el-right-element label {
    margin-top: 18px;
    height: 22px;
}

.el-screen-menu .el-right-element label:nth-child(1) {
    margin: 0;
}

.el-screen-menu .el-btn {
    width: 312px;
    height: 40px;
    line-height: 40px;
    text-align: center;
    background: rgba(86, 98, 112, 0.1);
    color: rgba(0, 0, 0, .65);
    font-size: 14px;
    margin: auto;
    border-radius: 20px;
}

.el-screen-menu .el-btn:hover {
    background: #1890FF;
    color: #fff;
    cursor: pointer;
}

.state-poper {
    padding: 0;
}

.el-right-state ul {
    padding: 0;
}

.el-right-state li {
    padding: 0 10px 0 16px;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    height: 40px;
}

.el-right-state li div {
    width: 70px;
    height: 28px;
    border: 1px solid transparent;
    line-height: 28px;
    text-align: center;
    border-radius: 2px;
}

.el-right-state li:hover {
    background: rgba(0, 0, 0, .04)
}

.el-right-state .btn {
    width: 128px;
    height: 32px;
    line-height: 32px;
    background: rgba(24, 144, 255, 1);
    color: #fff;
    font-size: 14px;
    margin: 0 auto 16px;
    text-align: center;
    border-radius: 4px;
    cursor: pointer;
}

.el-list-poper {
    padding: 0;
}

.el-list-poper ul {
    padding: 0;
}

.el-list-poper li {
    height: 40px;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    padding: 0 12px 0 28px;
}

.el-list-poper li span:nth-child(2) {
    color: rgba(0, 0, 0, .25)
}

.el-list-poper li:hover {
    background: rgba(0, 0, 0, .04);
    cursor: pointer;
}

.el-list-table tr {
    height: 50px;
}

.el-list-table,
.el-list-table .el-table__expanded-cell,
.el-list-table th,
.el-list-table tr {
    background: transparent;
}

.el-list-table tbody tr td:nth-child(1) .cell,
.el-list-table tbody tr td:nth-child(4) .cell {
    line-height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.el-list-table tbody tr:hover .cell span,
.el-list-table tbody tr:hover .cell,
.el-list-table tbody tr:hover ._css-radio {
    color: #1890ff !important;
}

.el-list-table tbody tr.el-table__row:hover .el-radio__inner {
    border: 1px solid #1890ff !important;
}

.el-list-table tbody tr.hover-row:hover .cell .icon-interface-associated-component {
    color: #606266;
}

.el-list-table .el-table__fixed-right-patch {
    background-color: rgb(240, 242, 245) !important;
}

.el-progress-wp .el-tree,
.progress-list .el-tree {
    background: #f0f2f5;
    height: calc(100% - 50px);
    overflow: auto;
}

.el-progress-wp .el-tree-node:focus>.el-tree-node__content,
.progress-list .el-tree-node:focus>.el-tree-node__content,
.progress-list .el-tree-node__content:hover,
.el-progress-wp .el-tree-node__content:hover {
    background: rgba(24, 144, 255, 0.1);
    color: #1890FF;
}

._css-treebodyarea .el-tree-node__content:hover,
._css-treebodyarea .el-tree-node:focus>.el-tree-node__content {
    background: rgba(0, 0, 0, 0.04) !important;
}

.el-progress-wp .el-tree-node__content,
.progress-list .el-tree-node__content {
    height: 40px;
    display: flex;
}


/* .el-progress-wp .custom-tree-node{
  display: flex;
  flex: 1;
  justify-content: space-between;
  padding-right: 10px;
} */


/* .el-progress-wp .el-tree{
  height: calc(100% - 50px);
  overflow: auto;
} */


/* .el-progress-wp .el-tree .el-tree-node{
  height: 100%;
  overflow: auto;
} */

.el-progress-wp .el-tree .custom-tree-node {
    overflow: hidden;
    text-overflow: ellipsis;
}

.editTaskDialog .el-dialog__body {
    display: flex;
    align-items: center
}

.editTaskDialog .el-dialog__body input {
    flex: 1;
    margin-left: 20px;
    outline: none;
    padding-left: 20px;
    height: 35px;
    line-height: 35px;
}

.editTaskDialog .el-dialog__body input:-internal-autofill-selected {
    -webkit-box-shadow: 0 0 0px 1000px white inset;
}

.editTaskDialog .el-dialog__body input:focus {
    background-color: rgb(255, 255, 255) !important;
}

.editTaskDialog .el-button:not(.el-picker-panel__link-btn) {
    padding: 12px 20px;
    border-radius: 4px;
}

.el-progress-wp .el-tree--highlight-current .el-tree-node.is-current>.el-tree-node__content,
.progress-list .el-tree--highlight-current .el-tree-node.is-current>.el-tree-node__content {
    background: rgba(24, 144, 255, 0.1);
    color: #1890FF;
}

.el-dropdown-menu .active {
    color: red;
}

.auto-relation-wrapper .el-select {
    height: 40px;
    width: 100%;
    border-radius: 4px;
    border: 1px solid rgba(0, 0, 0, .09);
}

.auto-relation-wrapper .el-input--suffix .el-input__inner {
    padding-left: 16px;
}

.auto-relation-wrapper .auto-input {
    width: calc(100% - 16px);
    height: 40px;
    line-height: 40px;
    border-radius: 4px;
    border: 1px solid rgba(0, 0, 0, .09);
    margin-bottom: 50px;
    margin-top: 15px;
    padding-left: 16px;
}

.msr-admin-wp .el-input {
    border-radius: 4px;
    margin-top: 10px;
    border: 1px solid rgba(0, 0, 0, 0.09);
}

.msr-admin-wp .el-input input {
    padding-left: 16px;
}

.charts .el-carousel__container {
    height: 100%;
}

.progress-color-btn .el-color-dropdown__btns .el-button:not(.el-picker-panel__link-btn) {
    padding: 7px 15px !important;
    margin-left: 10px !important;
}


