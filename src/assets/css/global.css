/* 公共样式 */


/* 类多选角色对话框中的复选框 */


/*/////////////////////////*/

._css-showmodelphasestatistics-content td {
    box-sizing: border-box;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1) !important;
    border-right: 1px solid rgba(0, 0, 0, 0.1) !important;
}

._css-showmodelphasestatistics-content tr:nth-child(1) th:nth-child(1) {
    background: #2F72C0 !important;
    color: #fff !important;
}

._css-showmodelphasestatistics-content tr:nth-child(1) th:nth-child(1) div {
    height: 30px !important;
    line-height: 30px !important;
    font-size: 14px !important;
    text-align: center;
}

._css-showmodelphasestatistics-content tr:nth-child(2) th {
    background: #FAFAFA !important;
    color: #999 !important;
}

._css-showmodelphasestatistics-content tr:nth-child(2) th:nth-child(1) div {
    height: 50px !important;
    line-height: 50px !important;
    font-size: 14px !important;
}

.css-checkbox {
    width: 14px;
    height: 14px;
    border-radius: 4px;
    cursor: pointer;
    border: 1px solid #ecdfe6;
}

.css-checkbox.mulcolor-interface-checkbox-selected {
    border: 1px solid rgb(48, 146, 251);
}

.css-panoshowing-lineitem .el-timeline-item__content {
    color: #fff !important;
    text-align: left;
}

.css-panoshowing-lineitem.css-panoshowing-lineitemselected .el-timeline-item__content {
    color: #409eff !important
}

.css-panoshowing-lineitem .el-timeline-item__timestamp {
    color: rgba(255, 255, 255, 0.6) !important;
    text-align: left;
}

.css-panoshowing-lineitem.css-panoshowing-lineitemselected .el-timeline-item__timestamp {
    color: #409eff !important;
}


/* 元样式 */


/*///////*/

.css-f1 {
    flex: 1;
}

.css-closebtn {
    width: 24px;
    height: 24px;
    line-height: 24px !important;
    cursor: pointer;
    /* border: 1px dotted red; */
    font-size: 18px;
}


/* //公共样式 */

.el-date-editor.css-dtwidthfull {
    width: 100% !important;
}


/* 基于插件添加class样式 */

.css-input-button {
    height: 32px;
}


/* //基于插件添加class样式 */


/* 页面全局 */

#app {
    /* font-family: 'Avenir', Helvetica, Arial, sans-serif; */
    font-family: "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "微软雅黑", Arial, sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-align: center;
    color: #2c3e50;
    margin-top: 0px;
    height: 100%;
    min-width: 1366px;
    font-size: 14px;
}

[class^="icon-"],
[class*=" icon-"] {
    font-size: 18px;
}

.css-fs22[class^="icon-"],
.css-fs22[class*=" icon-"] {
    font-size: 22px;
}

.ta-right {
    text-align: right;
}

body {
    margin: 0;
    height: 100%;
}

.css-page .el-pager li {
    background-color: transparent;
    color: rgba(0, 0, 0, 0.65);
}

.css-page .el-pager li.active {
    background-color: #1890ff;
    color: #fff;
}

.css-page .el-pagination .btn-next,
.css-page .el-pagination .btn-prev {
    background-color: transparent;
    color: rgba(0, 0, 0, 0.65);
}

.css-page .el-pagination button:disabled {
    background-color: transparent;
    color: rgba(0, 0, 0, 0.25);
}

.css-page .el-pagination__jump .el-input__inner {
    margin-left: 2px;
    margin-right: 2px;
    border-radius: 2px;
    border: 1px solid rgba(0, 0, 0, 0.15);
}

.css-mulcontentbtn {
    background-color: #282E3D;
    border-color: #282E3D;
    color: #fff;
    padding-left: 12px !important;
    padding-right: 12px !important;
    width: auto;
    height: 24px;
}

.css-timeiconandtime:hover input.el-input__inner {
    cursor: pointer;
    color: rgba(24, 144, 255, 1);
}

.ignorefocus:focus {
    background-color: #282E3D;
    border-color: #282E3D;
    color: #fff;
}

.ignorefocus:hover {
    background-color: #1890FF;
    border-color: #1890ff;
    color: #fff;
}

.css-main {
    width: 100%;
}

.css-tar {
    text-align: right;
}

.css-tal {
    text-align: left;
}

.css-tac {
    text-align: center;
}

.css-t4 {
    top: 4px;
}

.css-l4 {
    left: 4px;
}

.css-bst {
    border: 1px solid transparent;
}

.css-w40 {
    width: 40px;
}

.css-w48 {
    width: 48px;
}

.css-w310 {
    width: 310px;
}

.css-sugitem {
    padding-right: 20px;
    padding-left: 20px;
    height: 34px;
    color: #606266;
    cursor: pointer;
}

.css-sugitem:hover {
    background-color: #f5f7fa;
}

.css-w-40 {
    width: calc(100% - 40px);
}

.css-w-70 {
    width: calc(100% - 70px);
}

.css-bsb {
    box-sizing: border-box;
}

.css-w80 {
    width: 80px;
}

.css-w100 {
    width: 100%;
}

.css-w100px {
    width: 100px;
}

.css-w196 {
    width: 196px;
}

.css-w-196 {
    width: calc(100% - 196px);
}

.css-mh-20__5 {
    max-height: calc(100% - 40px * 5);
}

.css-mh-20__6 {
    max-height: calc(100% - 40px * 6);
}

.css-h22 {
    height: 22px;
}

.css-h24 {
    height: 24px;
}

.css-h64 {
    height: 64px;
}

.css-w240 {
    width: 240px;
}

.css-w-240 {
    width: calc(100% - 240px);
}

.css-o05 {
    opacity: 0.5;
}

.el-table._css-table-ele2 th div {
    text-align: center !important;
    font-weight: 800;
    font-size: 14px;
    border-right: 1px solid rgba(0, 0, 0, 0.09);
}

.css-modalhover {
    position: fixed;
    top: 0;
    left: 0;
    height: 100%;
    width: 100%;
    z-index: 1;
    /* opacity:0.5; */
    /* background-color: #000; */
    background-color: rgba(0, 0, 0, 0.5);
}

.css-dis {
    cursor: not-allowed;
    opacity: 0.5;
}

.css-hide {
    display: none;
}


/* 'css-ha-tran':'css-h0-tran' */

.css-ha-tran {
    height: auto;
}

.css-h0-tran {
    height: 0;
}

html,
.ht100,
.css-h100 {
    height: 100%;
}

.css-ml8 {
    margin-left: 8px;
}

.css-mt8 {
    margin-top: 8px;
}

.css-ml10 {
    margin-left: 10px;
}

.css-mr10 {
    margin-right: 10px;
}

.css-ml12 {
    margin-left: 12px;
}

.css-mr12 {
    margin-right: 12px;
}

.css-mr85 {
    margin-right: 85px;
}


/* flex 列表 */

.css-flexlist {
    display: flex;
    flex-wrap: wrap;
    align-content: flex-start;
    overflow-y: auto;
}


/* //flex 列表 */

.css-p1024 {
    padding: 10px 24px 10px 24px;
}

.css-pt4 {
    padding-top: 4px;
}

.css-pb4 {
    padding-bottom: 4px;
}

.css-pb18 {
    padding-bottom: 18px;
}

.css-ml16 {
    margin-left: 16px;
}

.css-ml54 {
    margin-left: 54px;
}

.css-pl8 {
    padding-left: 8px;
}

.css-pr8 {
    padding-right: 8px;
}

.css-pl10 {
    padding-left: 10px;
}

.css-pl12 {
    padding-left: 12px;
}

.css-pl24 {
    padding-left: 24px;
}

.css-plr75 {
    padding-left: 75px;
    padding-right: 75px;
}

.css-hover-highlight:not(.css-dis):hover {
    background: rgba(230, 247, 255, 1);
}

.css-menuitemborder {
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.css-menuitemborder:last-child {
    border-bottom: 1px solid transparent;
}

.css-pr12 {
    padding-right: 12px;
}

.css-pr16 {
    padding-right: 10px;
}

.css-pl16 {
    padding-left: 16px;
}

.css-bl4 {
    border-top-left-radius: 4px;
    border-bottom-left-radius: 4px;
}

.css-br4 {
    border-top-right-radius: 4px;
    border-bottom-right-radius: 4px;
}

.css-blk {
    display: block;
}

.css-none {
    display: none;
}

.css-bl2 {
    border-top-left-radius: 2px;
    border-bottom-left-radius: 2px;
}

.css-br2 {
    border-top-right-radius: 2px;
    border-bottom-right-radius: 2px;
}


/* 面包屑 */

.css-crumbs-content {
    height: 22px;
    margin-top: 12px;
    display: flex;
    align-items: center;
}


/* //面包屑 */

.css-fc {
    display: flex;
    align-items: center;
}

.css-ffs {
    display: flex;
    align-items: flex-start;
}

.css-fc_r {
    display: flex;
    align-items: center;
    flex-direction: row-reverse;
}

.css-flxcol {
    display: flex;
    flex-direction: column;
}

.css-flexn {
    flex: none;
}

.css-flex1 {
    flex: 1;
}

.css-ml4 {
    margin-left: 4px;
}

.css-btn4832 {
    height: 32px;
    width: 48px;
}

.css-btn5024 {
    height: 24px;
    width: 50px;
}

.css-br2 {
    border-top-right-radius: 2px;
    border-bottom-right-radius: 2px;
}

.css-bl2 {
    border-top-left-radius: 2px;
    border-bottom-left-radius: 2px;
}

.css-btn8032 {
    height: 32px;
    width: 80px;
    border-radius: 4px;
}

.css-btn10632 {
    height: 32px;
    width: 106px;
    border-radius: 4px;
}

.css-mr4 {
    margin-right: 4px;
}

.css-mr5 {
    margin-right: 5px;
}

.css-mr8 {
    margin-right: 8px;
}

.css-mr14 {
    margin-right: 14px;
}

.css-mr16 {
    margin-right: 16px;
}

.css-mr18 {
    margin-right: 18px;
}

.css-mr20 {
    margin-right: 20px;
}

.css-mr24 {
    margin-right: 24px;
}

.css-mt4 {
    margin-top: 4px;
}

.css-mt7 {
    margin-top: 7px;
}

.css-mt12 {
    margin-top: 12px;
}

.css-mt16 {
    margin-top: 16px;
}

.css-mt20 {
    margin-top: 20px;
}

.css-mt24 {
    margin-top: 24px;
}

.css-mt26 {
    margin-top: 26px;
}

.css-mt34 {
    margin-top: 34px;
}

.css-mt53 {
    margin-top: 53px;
}

.css-mt58 {
    margin-top: 58px;
}

.css-h20 {
    height: 20px;
}

.css-h22 {
    height: 22px;
}

.css-h32 {
    height: 32px;
}

.css-h40 {
    height: 40px;
}

.css-h-40 {
    height: calc(100% - 40px);
}

.css-hoverunder:hover {
    /* text-decoration: underline; */
    color: #1890ff;
}

.css-mw530 {
    min-width: 530px;
}

.css-maw180 {
    max-width: 180px;
}

.css-nonvisibility {
    visibility: hidden;
}

.css-ml16 {
    margin-left: 16px;
}

.css-p24 {
    padding: 24px;
}

.css-b {
    border: 1px solid transparent;
    box-sizing: border-box;
}

.css-ml24 {
    margin-left: 24px;
}

.css-cb {
    border: 1px solid #dcdfe6;
    border-radius: 2px;
    font-size: 12px;
}

.css-bordernoneim {
    border: 1 solid transparent !important;
}

.css-s4422 {
    width: 44px;
    height: 22px;
}

.css-s5622 {
    width: 56px;
    height: 22px;
}

.css-s7624 {
    width: 76px;
    height: 24px;
}

.css-breadcrumb-item label {
    text-overflow: ellipsis;
    overflow-x: hidden;
    white-space: nowrap;
    height: 19px;
    line-height: 17px;
}

.css-breadcrumb-splitter {
    padding-left: 9px;
    padding-right: 9px;
    color: #c0c4cc;
}

.css-prel {
    position: relative;
}

.css-cp {
    cursor: pointer;
}

.css-def {
    cursor: default;
}

.css-h48 {
    height: 48px;
}

.css-h-48 {
    height: calc(100% - 48px);
}

.css-pabs {
    position: absolute;
}

.css-bg004 {
    background-color: rgba(0, 0, 0, 0.04);
}

.css-color-light {
    color: rgb(220, 223, 230);
}

.css-mauto {
    margin: auto;
}

.css-jcsa {
    justify-content: space-around;
}

.css-contextMenuContainer {
    background: rgba(255, 255, 255, 1);
    box-shadow: 0px 0px 8px 0px rgba(0, 0, 0, 0.15);
    border-radius: 4px;
}

.css-jcsb {
    justify-content: space-between;
}

.css-usn {
    user-select: none;
}

.css-lh100 {
    line-height: 100%;
}

.css-icon12 {
    width: 12px;
    height: 12px;
}

.css-vh {
    visibility: hidden;
}

.css-icon14 {
    width: 14px;
    height: 14px;
}

.css-icon16 {
    width: 16px;
    height: 16px;
}

.css-icon18 {
    width: 18px;
    height: 18px;
}

.css-icon20 {
    width: 20px;
    height: 20px;
}

.css-icon32 {
    width: 32px;
    height: 32px;
}

.css-icon40 {
    width: 36px;
    height: 40px;
}

.css-icon60 {
    width: 60px;
    height: 60px;
}

.css-h22 {
    height: 22px;
}

.css-h120 {
    height: 120px;
}

.css-h250 {
    height: 250px;
}

.css-fs12 {
    font-size: 12px;
}

.css-fs14 {
    font-size: 14px;
}

.css-fs16 {
    font-size: 16px;
}

.css-fs20 {
    font-size: 20px;
}

.css-fs32 {
    font-size: 32px;
}

.css-fs60 {
    font-size: 60px;
}

.css-pr24 {
    padding-right: 24px;
}

.css-ellipsis {
    text-overflow: ellipsis;
    overflow-x: hidden;
    white-space: nowrap;
}

.css-oxa {
    overflow-x: auto;
}

.css-fs30 {
    font-size: 30px;
}

.css-ml8 {
    margin-left: 8px;
}

.css-ml32 {
    margin-left: 32px;
}

.css-debug {
    /* border: 1px dotted rgba(240,242,245,1); */
    border: 1px dotted red;
    box-sizing: border-box;
}


/* 项目文档 文档表格区域的背景 */

.css-bggray {
    background-color: rgba(240, 242, 245, 1);
}


/* //项目文档 文档表格区域的背景 */


/* 项目文档 文档表格的左侧菜单 */

.css-bglgray {
    background-color: rgba(0, 0, 0, 0.02);
}


/* //项目文档 文档表格的左侧菜单 */

.css-bgfff {
    background-color: rgba(255, 255, 255, 1);
}

.css-b0 {
    bottom: 0;
}

.css-b26 {
    bottom: 26px;
}

.css-t0 {
    top: 0;
}

.css-l0 {
    left: 0;
}

.css-l200 {
    left: 200%;
}

.css-r0 {
    right: 0;
}

.css-r24 {
    right: 24px;
}

.css-l56 {
    left: 56px;
}

.css-r56 {
    right: 56px;
}

.css-flexcol {
    display: flex;
    flex-direction: column;
}

.css-flexrowcenter {
    display: flex;
    flex-direction: row;
    align-items: center;
}

.css-cht100>div:first-child {
    height: 100%;
}

.css-projectbootleftmenu {
    background-color: rgb(84, 92, 100);
}

.css-z1 {
    z-index: 1;
}

.css-pfix {
    position: fixed;
}

.css-logo {
    background-image: url('../images/logo.png');
    background-repeat: no-repeat;
    background-position: center;
    background-size: contain;
}

.css-btnprimary {
    border: none;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: space-around;
    background-color: #1890FF;
    color: #fff;
}

.css-btnnormal {
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: space-around;
    background-color: #fff;
    color: rgba(0, 0, 0, 0.65);
    border-radius: 2px;
    border: 1px solid rgba(0, 0, 0, 0.15);
}

._css-customstyle .el-icon-loading {
    display: none;
}

.css-oxh {
    overflow-x: hidden;
}

.css-oyh {
    overflow-y: hidden;
}

.css-oya {
    overflow-y: auto;
}

.css-tdunder>td {
    border-bottom: 1px solid rgba(0, 0, 0, 0.04);
}


/* .css-tdunder:hover>td{
    background-color:rgba(0,0,0,0.02);
  } */

.css-main-header {
    background-color: rgba(255, 255, 255, 1);
}


/* .css-tabrow-selected{
    background-color: #fafafa;
  } */

.css-folder {
    /* color: #FAAD14; */
    color: rgba(86, 167, 255);
}

.css-dropdown {
    top: calc(100% + 2px);
    background-color: #fff;
    border-radius: 4px;
    position: absolute;
    border: 1px solid rgba(0, 0, 0, 0.15);
    max-height: 200px;
    overflow-y: auto;
    padding-top: 4px;
    padding-bottom: 4px;
}

.css-miniscroll::-webkit-scrollbar {
    /*滚动条整体样式*/
    width: 4px;
    /*高宽分别对应横竖滚动条的尺寸*/
    height: 4px;
}

.css-miniscroll.css-noscroll::-webkit-scrollbar {
    /*滚动条整体样式*/
    width: 0px;
    /*高宽分别对应横竖滚动条的尺寸*/
    height: 0px;
}

.css-miniscroll::-webkit-scrollbar-thumb {
    /*滚动条里面小方块*/
    border-radius: 5px;
    -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
    background: rgba(0, 0, 0, 0.2);
}

.css-miniscroll::-webkit-scrollbar-track {
    /*滚动条里面轨道*/
    -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
    border-radius: 0;
    background: rgba(0, 0, 0, 0.1);
}

.css-miniscroll.css-littlescroll::-webkit-scrollbar {
    /*滚动条整体样式*/
    width: 0px;
    /*高宽分别对应横竖滚动条的尺寸*/
    height: 0px;
}

._css-table-ele.el-table th {
    /* background-color: rgba(0,0,0,0.02); */
    /* background-color:#EBEDf0; */
    background-color: transparent;
}

.css-tabrow-selected {
    /* background-color: rgba(0,0,0,0.04) !important; */
    background-color: #E6E8Eb !important;
}


/* 科学覆盖插件 */


/* tooltip */

.el-tooltip__popper.css-top-triangle[x-placement^=top] .popper__arrow::after {
    border-top-color: transparent;
}

.el-tooltip__popper.css-top-triangle[x-placement^=top] .popper__arrow {
    border-top-color: rgba(0, 0, 0, 0.65) !important;
}

.el-tooltip__popper,
.el-tooltip__popper.css-no-triangle.is-dark {
    background-color: rgba(0, 0, 0, 0.65) !important;
}

.el-tooltip__popper.css-no-triangle[x-placement^=left] .popper__arrow::after {
    border-left-color: transparent;
}

.el-tooltip__popper.css-no-triangle[x-placement^=left] .popper__arrow {
    border-left-color: transparent;
}

.el-tooltip__popper.tooltip-model-hover[x-placement^=right] /deep/ .popper__arrow {
    border-right-color: transparent;
}

.el-tooltip__popper.tooltip-model-hover[x-placement^=right] /deep/ .popper__arrow:after {
    border-right-color: rgba(0, 0, 0, 0.65) !important;
}

.el-tooltip__popper.css-no-triangle[x-placement^=right] .popper__arrow::after {
    border-right-color: transparent;
}

.el-tooltip__popper.css-no-triangle[x-placement^=right] .popper__arrow {
    border-right-color: transparent;
}

.el-tooltip__popper.css-no-triangle[x-placement^=top] .popper__arrow::after {
    border-top-color: transparent;
}

.el-tooltip__popper.css-no-triangle[x-placement^=top] .popper__arrow {
    border-top-color: transparent;
}

.el-tooltip__popper.css-no-triangle[x-placement^=bottom] .popper__arrow::after {
    border-bottom-color: transparent;
}

.el-tooltip__popper.css-no-triangle[x-placement^=bottom] .popper__arrow {
    border-bottom-color: transparent;
}


/* ******表格****** */

._css-customstyle.el-table,
._css-customstyle.el-table__expanded-cell {
    background-color: transparent;
    /* 表格背景，包括表头 */
}

._css-customstyle.el-table--enable-row-hover .el-table__body tr:hover>td {
    background-color: #EBEDF0;
    /* 表格行 hover */
}

._css-table-ele.el-table--border {
    border-color: transparent;
    /* 表格上及左border(其实右下也有，只不过被伪元素覆盖掉了) */
}

._css-table-ele.el-table--border th.gutter:last-of-type {
    border-color: rgba(0, 0, 0, 0.04);
    /* 表格表头-末尾单元格边 */
}

._css-customstyle th.is-leaf,
._css-customstyle .el-table__fixed-right-patch {
    border-right-color: transparent;
    border-bottom-color: rgba(0, 0, 0, 0.04);
    /* 表格表头 */
}

._css-customstyle.el-table {
    font-size: 12px;
}


/* .css-hoverunder
._css-customstyle .el-table_1_column_2{
  font-size:14px;
} */


/* 文档管理表格 第一列除表头外的文字大小 */

._css-customstyle.el-table th {
    font-size: 12px;
}

._css-customstyle::before,
._css-customstyle::after
/* 覆盖伪元素！ */

{
    height: 0;
    width: 0;
}


/* ******树****** */

._css-customstyle .el-tree-node:focus>.el-tree-node__content {
    background-color: rgba(0, 0, 0, 0.04);
}

._css-customstyle .el-tree-node>.el-tree-node__content:hover {
    background-color: rgba(0, 0, 0, 0.02);
}

._css-customstyle.el-tree {
    background-color: transparent;
}

._css-customstyle .el-tree-node__content {
    height: 36px;
}


/* //科学覆盖插件 */


/* 表格背景色相关 */

._css-treewhole .el-tree-node__content,
.css-common-tablehead,
._css-table-ele,
._css-table-ele.el-table th,
._css-customstyle th,
._css-customstyle .el-table__body-wrapper {
    /* background-color:rgba(240,242,245,1); */
    background-color: transparent;
}


/* 树节点选中，失去焦点的背景色 */

.el-tree--highlight-current .el-tree-node.is-current>.el-tree-node__content {
    /* background-color: rgba(0, 0, 0, 0.04) !important; */
    background-color: rgba(0, 0, 0, 0.06) !important;
}


/* 树节点选中，失去焦点的背景色 */


/* ._css-table-ele.el-table tr{
  background-color: #EBEDF0;
} */

._css-table-ele th,
.el-table tr {
    background-color: transparent;
}

.css-readonly {
    background-color: rgba(0, 0, 0, 0.1);
}

.css-readonly-in {
    background-color: rgba(0, 0, 0, 0);
}


/*  tr:hover>td {
  background-color:rgba(0,0,0,0.02) !important;
} */


/* ._css-table-ele.el-table--enable-row-hover .el-table__body tr:hover>td{
  background-color:rgba(0,0,0,0.02);
} */


/* //表格背景色相关 */


/* ._css-customstyle tr:hover>td { background-color:red !important;} */

.css-scroll ::-webkit-scrollbar {
    /*滚动条整体样式*/
    width: 5px;
    /*高宽分别对应横竖滚动条的尺寸*/
    height: 4px;
}

.css-scroll ::-webkit-scrollbar-thumb {
    /*滚动条里面小方块*/
    border-radius: 5px;
    -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
    background: rgba(0, 0, 0, 0.2);
}

.css-scroll ::-webkit-scrollbar-track {
    /*滚动条里面轨道*/
    -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
    border-radius: 0;
    background: rgba(0, 0, 0, 0.2);
}

.mulcolor-interface-morenxianmu {
    background-image: url('../svgs_loadbyurl/interface-morenxiangmu.svg');
    background-repeat: no-repeat;
    background-position: center;
    background-size: contain;
}

.mulcolor-logo {
    background-image: url('../svgs_loadbyurl/logo.svg');
    background-repeat: no-repeat;
    background-position: center;
}

.mulcolor-interface-checkbox-selected {
    background-image: url('../svgs_loadbyurl/checkbox-Checked.svg');
    background-repeat: no-repeat;
    background-position: center;
    background-size: 14px 14px;
    border-color: transparent;
}

.mulcolor-interface-checkbox-selected.css-dis2 {
    background-image: url('../svgs_loadbyurl/checkbox-Selected-Disabled.svg');
}

.mulcolor-interface-avi {
    background-image: url('../svgs_loadbyurl/interface-avi.svg');
    background-repeat: no-repeat;
    background-position: center;
}

.mulcolor-interface-bmp {
    background-image: url('../svgs_loadbyurl/interface-bmp.svg');
    background-repeat: no-repeat;
    background-position: center;
}


/*doc*/

.mulcolor-interface-doc {
    background-image: url('../svgs_loadbyurl/interface-doc.svg');
    background-repeat: no-repeat;
    background-position: center;
}

.mulcolor-interface-docx {
    background-image: url('../svgs_loadbyurl/interface-docx.svg');
    background-repeat: no-repeat;
    background-position: center;
}

.mulcolor-interface-dwg {
    background-image: url('../svgs_loadbyurl/interface-dwg.svg');
    background-repeat: no-repeat;
    background-position: center;
}

.mulcolor-interface-gif {
    background-image: url('../svgs_loadbyurl/interface-gif.svg');
    background-repeat: no-repeat;
    background-position: center;
}

.mulcolor-interface-jpeg {
    background-image: url('../svgs_loadbyurl/interface-jpeg.svg');
    background-repeat: no-repeat;
    background-position: center;
}

.mulcolor-interface-jpg {
    background-image: url('../svgs_loadbyurl/interface-jpg.svg');
    background-repeat: no-repeat;
    background-position: center;
}

.mulcolor-interface-mp3 {
    background-image: url('../svgs_loadbyurl/interface-mp3.svg');
    background-repeat: no-repeat;
    background-position: center;
}

.mulcolor-interface-mp4 {
    background-image: url('../svgs_loadbyurl/interface-mp4.svg');
    background-repeat: no-repeat;
    background-position: center;
}

.mulcolor-interface-pdf {
    background-image: url('../svgs_loadbyurl/interface-pdf.svg');
    background-repeat: no-repeat;
    background-position: center;
}

.mulcolor-interface-png {
    background-image: url('../svgs_loadbyurl/interface-png.svg');
    background-repeat: no-repeat;
    background-position: center;
}

.mulcolor-interface-ppt {
    background-image: url('../svgs_loadbyurl/interface-ppt.svg');
    background-repeat: no-repeat;
    background-position: center;
}

.mulcolor-interface-pptx {
    background-image: url('../svgs_loadbyurl/interface-pptx.svg');
    background-repeat: no-repeat;
    background-position: center;
}

.mulcolor-interface-rar {
    background-image: url('../svgs_loadbyurl/interface-rar.svg');
    background-repeat: no-repeat;
    background-position: center;
}

.mulcolor-interface-svg {
    background-image: url('../svgs_loadbyurl/interface-svg.svg');
    background-repeat: no-repeat;
    background-position: center;
}

.mulcolor-interface-txt {
    background-image: url('../svgs_loadbyurl/interface-txt.svg');
    background-repeat: no-repeat;
    background-position: center;
}

.mulcolor-interface-unknown {
    background-image: url('../svgs_loadbyurl/interface-unknown.svg');
    background-repeat: no-repeat;
    background-position: center;
}

.mulcolor-interface-xls {
    background-image: url('../svgs_loadbyurl/interface-xls.svg');
    background-repeat: no-repeat;
    background-position: center;
}

.mulcolor-interface-xlsx {
    background-image: url('../svgs_loadbyurl/interface-xlsx.svg');
    background-repeat: no-repeat;
    background-position: center;
}

.mulcolor-interface-zip {
    background-image: url('../svgs_loadbyurl/interface-zip.svg');
    background-repeat: no-repeat;
    background-position: center;
}

.illustration_empty_document {
    background-image: url('../svgs_loadbyurl/illustration_empty_document.svg');
    background-repeat: no-repeat;
    background-position: center;
}

.temp-muban {
    background-image: url('../svgs_loadbyurl/temp-muban.svg');
    background-repeat: no-repeat;
    background-position: center;
}

.temp-muban-material {
    background-image: url('../svgs_loadbyurl/temp-muban2.svg');
    background-repeat: no-repeat;
    background-position: center;
}

.temp-muban-issueimport {
    background-image: url('../svgs_loadbyurl/temp-muban2-issueimport.svg');
    background-repeat: no-repeat;
    background-position: center;
}

.temp-longlogo {
    background-image: url('../svgs_loadbyurl/longlogo.png');
    background-position: center;
    background-size: cover !important;
    background-repeat: no-repeat;
    height: 40px !important;
    width: 310px !important;
}

.template-Qualityimport {
    background-image: url('../svgs_loadbyurl/template-Qualityimport.svg');
    background-repeat: no-repeat;
    background-position: center;
}

.template-QualitySetimport {
    background-image: url('../svgs_loadbyurl/template-QualitySetimport.svg');
    background-repeat: no-repeat;
    background-position: center;
}

.gistype {
    background-image: url('../images/GISType.png');
    background-repeat: no-repeat;
}


/* //页面全局 */

.css-hoverable-container:hover div {
    opacity: 1;
}

.css-hoverable-container div {
    opacity: 0.9;
    cursor: pointer;
}

.css-hover-btn:hover {
    opacity: 0.8;
    cursor: pointer;
}


/* 页面临时 */

.temp-icon-checkbox-border16 {
    border: 1px solid rgb(30, 143, 255);
    border-radius: 4px;
    box-sizing: border-box;
}

.temp-icon-checkbox-border16n {
    border: 1px solid rgba(0, 0, 0, 0.25);
    border-radius: 4px;
    box-sizing: border-box;
}

.temp-icon-radio-border16 {
    box-sizing: border-box;
    border: 1px solid rgb(30, 143, 255);
    border-radius: 50%;
    background-color: rgb(30, 143, 255);
    padding: 1px;
}

.temp-icon-radio-border16-in {
    width: 14px;
    height: 14px;
    border-radius: 50%;
    border: 1px solid white;
    box-sizing: border-box;
}

.temp-icon-radio-border16n {
    box-sizing: border-box;
    border: 1px solid rgba(0, 0, 0, 0.25);
    border-radius: 50%;
    padding: 1px;
}

.css-bgtreecol {
    background-color: rgba(0, 0, 0, 0.02);
}

.css-bgtreecoli {
    background-color: rgba(0, 0, 0, 0.02);
}

.temp-extend {
    opacity: 0.5;
}


/* //页面临时 */

.basic-hoverlight:hover {
    color: rgba(24, 144, 255, 1);
}

.basic-fonticon-20 {
    width: 20px;
    height: 20px;
    font-size: 20px;
    line-height: 20px;
}

.basic-hover {
    width: 100%;
    height: 100%;
    position: fixed;
    top: 0;
    left: 0;
    display: flex;
    justify-content: space-around;
    align-items: center;
}


/* 基础规范样式:参考全屏按钮颜色 */

.basic-btn-normal-color {
    color: rgba(0, 0, 0, 0.45);
}

 :hover>.basic-btn-normal-color {
    color: #1890FF;
}

.basic-btn-normal-size {
    font-size: 22px;
}


/* //基础规范样式 */


/* 基础规范样式 参考邀请项目成员时，角色选择下拉框常规 */

.basic-font-size-content {
    /* 正文 */
    font-size: 14px;
}

.basic-font-color-normal {
    color: rgba(0, 0, 0, 0.65);
}

.basic-font-color-emphasize::-webkit-input-placeholder {
    color: rgba(0, 0, 0, 0.45);
}

.basic-font-color-emphasize::-moz-placeholder {
    /* Mozilla Firefox 19+ */
    color: rgba(0, 0, 0, 0.45);
}

.basic-font-color-emphasize:-moz-placeholder {
    /* Mozilla Firefox 4 to 18 */
    color: rgba(0, 0, 0, 0.45);
}

.basic-font-color-emphasize:-ms-input-placeholder {
    /* Internet Explorer 10-11 */
    color: rgba(0, 0, 0, 0.45);
}

.basic-font-color-emphasize {
    /* 强调 */
    color: rgba(0, 0, 0, 0.85);
}


/* //基础规范样式 参考邀请项目成员时，角色选择下拉框常规 */

.css-common-zdialogbtnctn {
    border-top: 1px solid rgba(0, 0, 0, 0.09);
    margin-top: 17px;
    height: 64px;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    padding-right: 12px;
}

.css-common-line {
    padding: 0 24px;
    box-sizing: border-box;
    margin: 8px 0 0 0;
    display: flex;
    align-items: center;
}

.css-common-fieldvaluename {
    flex: 1;
    height: 36px;
    display: flex;
    align-items: center;
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: 4px;
    box-sizing: border-box;
    padding: 4px 8px 4px 18px;
}

.css-common-fieldvaluename-in {
    width: 100%;
    outline: none;
    border: none;
    height: 32px;
    line-height: 32px;
}

.el-table.css-table-cellborder td {
    text-align: center !important;
    font-weight: 800;
    font-size: 12px;
    border-right: 1px solid rgba(0, 0, 0, 0.09);
}


/* 红点样式 */

.css-global-bell {
    position: relative;
}

.css-red-tip {
    position: absolute;
    font-size: 2px;
    width: 2px;
    height: 2px;
    top: -2px;
    right: 8px;
    transform: scale(0.8);
}


/* 红点样式 */

.css-body-tree {
    width: 270px;
    height: 100%;
    flex: none;
}

.css-body-table {
    width: calc(100% - 270px);
    height: 100%;
    flex: none;
}

.css-page-head {
    height: 54px;
    display: flex;
    align-items: center;
    box-sizing: border-box;
    padding: 0 24px;
}

.css-page-body {
    height: calc(100% - 54px);
    box-sizing: border-box;
    padding: 24px;
}

.css-dialogtitle-icon {
    margin-left: 24px;
    width: 24px;
    height: 24px;
    color: #283A4F;
    line-height: 24px !important;
    text-align: center;
}

.css-dialogtitle-title {
    margin-left: 4px;
    height: 18px;
    line-height: 18px;
    font-size: 16px;
    color: #283A4F;
}

.css-globalfont {
    font-family: Arial, Helveetica, sans-serif;
}


/*按钮水波效果 由自定义指令buttonClickRipple 触发*/

[ripple] {
    position: relative;
    overflow: hidden;
}

[ripple] .ripple--container {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
}

[ripple] .ripple--container span {
    transform: scale(0);
    border-radius: 100%;
    position: absolute;
    opacity: 0.08;
    background-color: #272727;
    animation: ripple 1000ms;
}

@-moz-keyframes ripple {
    to {
        opacity: 0;
        transform: scale(2);
    }
}

@-webkit-keyframes ripple {
    to {
        opacity: 0;
        transform: scale(2);
    }
}

@-o-keyframes ripple {
    to {
        opacity: 0;
        transform: scale(2);
    }
}

@keyframes ripple {
    to {
        opacity: 0;
        transform: scale(2);
    }
}

div::-webkit-scrollbar {
    width: 5px;
    height: 5px;
}

div::-webkit-scrollbar-track {
    background: transparent;
    border-radius: 2px;
}

div::-webkit-scrollbar-thumb {
    background: rgba(0, 0, 0, 0.19);
    border-radius: 10px;
}

div::-webkit-scrollbar-thumb:hover {
    background: rgba(0, 0, 0, 0.09);
}

div::-webkit-scrollbar-corner {
    background: rgba(0, 0, 0, 0.39);
}


/**/

.css-10padding .jingruizhang-probim-vue.css-zselect-text-line {
    padding: 0 10px 0 10px !important;
    background-color: #fff;
}

.css-bordernone .jingruizhang-probim-vue.css-zselect-all {
    border: none !important;
}

.css-bordernone .jingruizhang-probim-vue.css-zselect-icon {
    right: 10px !important;
}

.btn-initiating-header {
    line-height: 50px;
    height: 50px;
    background-color: #fff;
    padding-left: 16px;
    font-size: 16px;
    font-weight: 500;
    color: rgba(0, 0, 0, .9);
    border-bottom: 1px solid rgba(0, 0, 0, .15);
}

.el-tree-node__content>.el-tree-node__expand-icon {
    padding: 4px;
}

._css-addingnameinput-ctn ._css-line {
    padding: 0 24px;
    box-sizing: border-box;
    margin: 8px 0 0 0;
    display: flex;
    align-items: center;
}

._css-addingnameinput-ctn ._css-title-flowname {
    width: 25%;
    text-align: left;
}

._css-flowAddBtnCtn {
    display: flex;
    flex-direction: row-reverse;
    height: 64px;
    align-items: center;
    box-sizing: border-box;
    padding-right: 8px;
}

._css-fieldvaluename {
    flex: 1;
    height: 36px;
    display: flex;
    align-items: center;
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: 4px;
    box-sizing: border-box;
}

.divisionBg {
    background-image: url('../images/division.png');
    background-repeat: no-repeat;
    background-position: center;
}

.archivesBg {
    background-image: url('../images/archives.png');
    background-repeat: no-repeat;
    background-position: center;
}

.unify-btn {
    line-height: 40px;
    color: #fff;
    background: #007AFF;
    width: 106px;
    cursor: pointer;
    text-align: center;
    border-radius: 2px;
}
.unify-btn-disable {
    background: #c6cad0;
    pointer-events: none;
}

.c007AFF {
    color: #007AFF;
}