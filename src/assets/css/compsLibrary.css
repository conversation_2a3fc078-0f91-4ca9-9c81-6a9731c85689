/* 构件库的相关css */

.editTreeName {
  position: absolute;
  left: 270px;
  top: 100px;
  width: 160px;
  background: #fff;
  z-index: 9;
}

.editTreeName-rename {
  z-index: 10;
  width: 200px;
}

._css-edit-title {
  text-align: center;
  line-height: 36px;
}

._css-edit-title input {
  margin: 10px 0;
  line-height: 26px;
  border-radius: 2px;
  background: transparent;
  border: 1px solid rgba(0, 0, 0, .3);
  padding-left: 5px;
}

._css-edit-title i {
  position: absolute;
  left: 10px;
  top: 8px;
}

._css-edit-btn {
  margin: 10px auto 20px;
  width: 76%;
  height: 30px;
  color: #fff;
  background: #1890ff;
  text-align: center;
  line-height: 30px;
  border-radius: 2px;
}

.editTreeName ul {
  padding: 4px 0;
  text-align: left;
}

.editTreeName li {
  height: 40px;
  line-height: 40px;
  padding-left: 16px;
  color: rgba(0, 0, 0, 0.85);
  font-size: 14px;
}

.editTreeName li i {
  vertical-align: middle;
  padding-right: 12px;
}

.editTreeName li:hover {
  background: rgba(0, 0, 0, 0.04);
}

.button-hover:hover {
  opacity: 0.8;
}

.component-library {
  width: 100%;
  height: calc(100% - 54px);
  position: relative;
  z-index: 1;
  margin-top: 54px;
  display: flex;
}

.component-library .top-functional-area {
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: relative;
  z-index: 1;
  width: 100%;
  height: 40px;
  line-height: 40px;
  padding: 0 16px;
  text-align: left;
  box-sizing: border-box;
  background-color: rgba(0, 0, 0, 0.02);
  box-shadow: 0 1px 1px 0 rgba(0, 21, 41, 0.12);
}

.component-library .top-functional-area .icon,
._css-closebtn {
  cursor: pointer;
  color: #818181;
}

.component-library .top-functional-area .icon.active,
.component-library .top-functional-area .icon:hover {
  color: #409eff;
}

.component-library .top-functional-area .middle {
  vertical-align: middle;
}

.component-library .top-functional-area .search-input {
  width: 260px;
  height: 32px;
  line-height: 32px;
  margin: 4px 0 4px 24px;
  border-radius: 4px;
  padding: 0 15px;
  box-sizing: border-box;
  outline: none;
  border: 1px solid rgba(0, 0, 0, 0.09);
}

.component-library .top-functional-area .search-input:focus {
  border: 1px solid rgba(0, 0, 0, 0.3);
}

/*左侧*/
.component-library .library-left-tree {
  width: 270px;
  flex-shrink: 0;
  position: relative;
}

.component-library .library-left-tree .add-new-library-content {
  position: absolute;
  top: 0;
  right: -210px;
  background-color: #FFFFFF;
  width: 210px;
  padding: 20px 10px;
  z-index: 10;
  border-radius: 4px;
  box-sizing: border-box;
  box-shadow: 0px 0px 3px 1px rgba(77, 79, 80, 0.2);
}

.component-library .library-left-tree .add-new-library-content input {
  width: 100%;
  height: 40px;
  padding: 9px 16px;
  margin-bottom: 15px;
  box-sizing: border-box;
  border-radius: 4px;
  outline: none;
  border: 1px solid rgba(0, 0, 0, .09);
}

.component-library .library-left-tree .add-new-library-content textarea {
  min-width: 100%;
  max-width: 100%;
  border-radius: 4px;
  border: 1px solid rgba(0, 0, 0, 0.09);
  padding: 9px;
  box-sizing: border-box;
  outline: none;
  margin-bottom: 15px;
}

.component-library .library-left-tree .add-new-library-content input:focus {
  border: 1px solid rgba(0, 0, 0, 0.3);
}

.component-library .library-left-tree .add-new-library-content .button {
  width: 100%;
  height: 40px;
  cursor: pointer;
  display: inline-block;
  line-height: 40px;
  border-radius: 4px;
  background-color: #1890ff;
  color: #fff;
}

.component-library .library-left-tree .top-functional-area .title {
  font-weight: 500;
}

.component-library .library-left-tree .left-tree-container {
  height: calc(100% - 40px);
  background-color: #FFFFFF;
  padding: 12px 8px;
  box-sizing: border-box;
  overflow-y: auto;
  box-shadow: 1px 0px 1px 0px rgba(77, 79, 80, 0.1);
}

.component-library .library-left-tree .left-tree-container .custom-tree-node {
  display: flex;
  width: 100%;
  padding-right: 10px;
  align-items: center;
  justify-content: space-between;
}

.library-left-tree .left-tree-container .custom-tree-node .tree-node-content {
  display: flex;
  align-items: center;
}

.left-tree-container .custom-tree-node .tree-node-content .icon {
  color: #409eff
}

.left-tree-container .custom-tree-node .tree-node-content .number {
  color: rgba(0, 0, 0, 0.25);
}

/*中间*/
.component-library .library-middle-list {
  flex-grow: 1;
}

.component-library .library-middle-list .middle-list-container {
  height: calc(100% - 40px);
  padding: 0 16px 16px;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  overflow-y: auto;
}

.component-library .library-middle-list .middle-list-container .library-label-screen {
  position: sticky;
  top: 0;
  z-index: 1;
  text-align: left;
  margin: 0 -16px;
  padding: 10px 16px 0 16px;
  background-color: #FFFFFF;
  box-shadow: 0 1px 1px 0 rgba(0, 21, 41, 0.12);
}

.library-middle-list .middle-list-container .library-label-screen .label-item {
  border-radius: 2px;
  padding: 3px 12px;
  font-size: 12px;
  cursor: pointer;
  font-weight: 400;
  display: inline-block;
  margin: 0 12px 12px 0;
  border: 1px solid rgba(255, 255, 255, 0);
  background-color: rgba(0, 0, 0, 0.04);
}

.library-middle-list .middle-list-container .library-label-screen .label-item.active {
  color: #1890FF;
  border-color: #1890FF;
  background-color: rgba(24, 144, 255, 0.1);
}

.component-library .library-middle-list .middle-list-container>.title {
  margin-bottom: 10px;
  text-align: left;
  font-size: 13px;
  color: rgba(0, 0, 0, 0.65);
}

._css-batch-input {
  background: transparent;
  border: 1px solid transparent;
  width: 98%;
  text-align: center;
  font-size: 16px;
  font-weight: 400;
  color: rgba(0, 0, 0, 1);
  line-height: 24px;
  height: 24px;
  /* border: 1px solid #1890FF; */
}

.component-library .library-middle-list .middle-list-container .top-classify-container {
  margin-bottom: 20px;
}

.library-middle-list .middle-list-container .top-classify-container .classify-content {
  cursor: pointer;
}

.middle-list-container .top-classify-container .classify-content .document-icon {
  width: 100%;
}

.middle-list-container .top-classify-container .classify-content .library-list-img {
  /* width:180px; */
  height: 138px;
}

.library-list-img img {
  width: 100%;
  height: 100%;
}

.top-classify-container .classify-content .document-icon img {
  vertical-align: middle;
}

.middle-list-container .top-classify-container .classify-content .document-icon .icon {
  color: #FFFFFF;
}

.middle-list-container .top-classify-container .classify-content .name {
  margin: 6px auto;
  font-size: 16px;
  font-weight: 400;
  color: rgba(0, 0, 0, 0.85);
  text-align: center;
  width: 120px;
  overflow: hidden;
  height: 26px;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.middle-list-container .top-classify-container .classify-content .statistics {
  font-size: 12px;
  font-weight: 400;
  color: rgba(0, 0, 0, 0.25);
  text-align: center;
  position: relative;
  display: flex;
  align-items: center;
}

.middle-list-container .top-classify-container .classify-content .statistics span {
  padding: 0 5px;
  flex: 1;
}

.middle-list-container .top-classify-container .classify-content .statistics i {
  visibility: hidden;
}

.middle-list-container .top-classify-container .classify-content.active .statistics,
.middle-list-container .top-classify-container .classify-content.active .name {
  color: #1890FF;
  font-weight: 500;
}

.component-library .library-middle-list .middle-list-container .bottom-list-container {}

.library-middle-list .middle-list-container .bottom-list-container .list-container-item {
  cursor: pointer;
  background: rgba(255, 255, 255, 1);
  box-shadow: 0 1px 1px 0 rgba(0, 21, 41, 0.12);
  border-radius: 4px;
  padding: 6px;
  margin-bottom: 10px;
  box-sizing: border-box;
  border: 1px solid rgba(255, 255, 255, 0);
}

.library-middle-list .middle-list-container .bottom-list-container .list-container-item:hover {
  box-shadow: 0 1px 5px 0 rgba(0, 21, 41, 0.2);
}

.library-middle-list .middle-list-container .bottom-list-container .list-container-item:hover i {
  visibility: inherit;
}

.middle-list-container .bottom-list-container .list-container-item.item-active {
  border: 1px solid #1890FF;
  color: #1890FF;
}

.bottom-list-container .list-container-item.item-active .statistics span,
.bottom-list-container .list-container-item.item-active .name {
  color: #1890FF;
}


/*右侧*/
.component-library .library-right-details {
  width: 340px;
  flex-shrink: 0;
  display: flex;
  flex-direction: column;
}

.component-library .library-right-details .top-functional-area .title {
  flex-grow: 1;
  text-align: left;
  font-weight: 400;
  color: rgba(0, 0, 0, 0.65);
  cursor: pointer;
}

.component-library .library-right-details .top-functional-area .title.active {
  color: #1890FF;
}


.component-library .library-right-details .right-details-container {
  height: calc(100% - 40px);
  background-color: #FFFFFF;
  /* padding: 16px; */
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  overflow-y: auto;
  box-shadow: 0 1px 1px 0 rgba(0, 21, 41, 0.12);
}

.component-library .library-right-details .right-details-container .library-details {
  width: 100%;
}

.library-right-details .right-details-container .form-input input {
  width: 100%;
  height: 40px;
  border-radius: 4px;
  border: 1px solid rgba(0, 0, 0, 0.09);
  outline: none;
  padding: 9px 16px;
  box-sizing: border-box;
  margin-bottom: 16px;
}

.library-right-details .right-details-container .form-input textarea {
  min-width: 100%;
  max-width: 100%;
  border-radius: 4px;
  border: 1px solid rgba(0, 0, 0, 0.09);
  padding: 9px 16px;
  box-sizing: border-box;
  outline: none;
  margin-bottom: 16px;
}

.library-right-details .right-details-container .library-details textarea:focus,
.library-right-details .right-details-container .library-details input:focus {
  border: 1px solid rgba(0, 0, 0, 0.2);
}

.library-right-details .right-details-container .library-details .details-info .title {
  font-weight: 500;
  text-align: left;
  margin-bottom: 10px;
}

.right-details-container .library-details .details-info ul li {
  text-align: right;
  margin-bottom: 10px;
  color: rgba(0, 0, 0, 0.65);
}

.right-details-container .library-details .details-info ul li span {
  float: left;
}

._css-library-content {
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

._css-lable-info {
  height: 320px;
}

.component-library .library-right-details .right-details-container .library-select {
  width: 100%;
  display: flex;
  flex-direction: column;
}

._detail_content .component-thumb {
  position: relative;
  width: 90%;
  height: 243px;
  display: flex;
  align-items: center;
  border-radius: 4px;
  overflow: hidden;
  border: 1px solid rgba(0, 0, 0, 0.09);
  /* margin-bottom: 15px; */
  margin: 18px 5% 14px;
}

._detail_content .transform {
  position: relative;
  width: 100%;
  height: 255px;
}

._detail_content .transform .component-thumb {
  position: absolute;
  top: 0;
  left: 0;
  background-color: #FFF;
}

._detail_content .transform .component-thumb:nth-of-type(1) {
  z-index: 1;
}

._detail_content .transform .component-thumb:nth-of-type(2) {
  z-index: 2;
  transform: rotateZ(-3deg);
}

._detail_content .transform .component-thumb:nth-of-type(3) {
  z-index: 3;
  transform: rotateZ(3deg);
}

._detail_content .batch-select-list {
  margin-bottom: 8px;
  height: 40px;
  background-color: rgba(0, 0, 0, .04);
  border-radius: 4px;
  padding: 0 16px;
  display: flex;
  align-items: center;
}

._detail_content .batch-select-list .icon {
  color: #409eff;
}

._detail_content .batch-select-list .title {
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
  font-weight: 400;
  color: rgba(0, 0, 0, 0.65);
}

.library-right-details .right-details-container .library-select .component-thumb .show-3d-model {
  position: absolute;
  width: 90px;
  height: 32px;
  border-radius: 4px;
  background-color: #1890FF;
  line-height: 32px;
  color: #FFFFFF;
  font-size: 12px;
  top: 10px;
  right: 10px;
  cursor: pointer;
}

.library-right-details .right-details-container .library-select .function-menu {
  display: flex;
  justify-content: space-between;
  padding: 0 10px;
  color: #818181;
  /* margin-bottom: 15px; */
  height: 32px;
  background: rgba(0, 0, 0, .04);
  margin: 0 5% 14px;
}

._detail_content .function-menu {
  margin-top: 25px;
  /* margin-bottom: 25px; */
  padding: 0 22%;
}

.library-right-details .right-details-container .library-select .function-menu .icon {
  cursor: pointer;
  line-height: 32px;
}

.library-right-details .right-details-container .library-select .function-menu .icon:hover,
.tab-pan-conten .enclosure-list .tab-icon-down:hover {
  color: #409eff;
}

.tab-pan-conten .enclosure-list .tab-icon-down {
  margin-left: 19px;
  display: none;
}

.library-right-details .right-details-container .library-select .tab-pan {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
}

.right-details-container .library-select .tab-pan .tab-pan-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  /* margin: 0 -16px; */
  height: 40px;
  font-weight: 400;
  color: rgba(0, 0, 0, 0.15);
  background-color: rgba(0, 0, 0, 0.02);
  margin-bottom: 12px;
  border-bottom: 1px solid rgba(0, 0, 0, .04);
}

.right-details-container .library-select .tab-pan .tab-pan-title .title {
  text-align: center;
  flex-grow: 1;
  cursor: pointer;
  color: rgba(0, 0, 0, 0.65);
}

.right-details-container .library-select .tab-pan .tab-pan-title .title:hover {
  opacity: 0.8;
}

.right-details-container .library-select .tab-pan .tab-pan-title .title.active {
  color: #1890FF;
}

.right-details-container .library-select .tab-pan .tab-pan-conten {
  margin: 0 5%;
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.lable-tab-flex-content {
  flex: 1;
  height: calc(100% - 50px);
}

.right-details-container .library-select .tab-pan .tab-pan-conten .tab-pan-library-label .title {
  text-align: left;
  font-weight: 400;
  color: rgba(0, 0, 0, 0.65);
  margin-bottom: 10px;
}

.library-select .tab-pan .tab-pan-conten .tab-pan-library-label .add {
  text-align: left;
  font-size: 12px;
  color: rgba(0, 0, 0, 0.45);
  cursor: pointer;
  margin-bottom: 15px;
}

.library-select .tab-pan .tab-pan-conten .tab-pan-library-label .add:hover {
  opacity: 0.8;
}

.library-select .tab-pan .tab-pan-conten .tab-pan-library-label .add .icon {
  vertical-align: sub;
}

.tab-pan-conten .enclosure-list {
  margin-bottom: 8px;
  height: 40px;
  background-color: rgba(0, 0, 0, 0.04);
  border-radius: 4px;
  padding: 0 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.tab-pan-conten .enclosure-list:hover {
  background-color: rgba(24, 144, 255, 0.1);
}

.tab-pan-conten .enclosure-list:hover .tab-icon-down {
  /* visibility:inherit; */
  display: inline-block;
}

.tab-pan-conten .enclosure-list .thumb {
  width: 20px;
  height: 20px;
  flex-shrink: 0;
}

.tab-pan-conten .enclosure-list .name {
  text-align: left;
  flex-grow: 1;
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
  padding: 0 12px;
  color: rgba(0, 0, 0, 0.65);
}

.tab-pan-conten .enclosure-list .icon {
  cursor: pointer;
  flex-shrink: 0;
  color: rgba(0, 0, 0, 0.45);
}

.tab-pan-conten .enclosure-list .icon:hover {
  opacity: 0.8;
}

.tab-message {
  text-align: left;
}

._css-title {
  font-family: 'PingFangSC-Medium,PingFang SC';
  font-size: 14px;
  font-weight: 500;
  color: rgba(0, 0, 0, 1);
  line-height: 36px;

}

._css-lable {
  display: inline-block;
  font-size: 14px;
  font-family: 'PingFangSC-Regular,PingFang SC';
  font-weight: 400;
  color: rgba(0, 0, 0, .65);
  line-height: 36px;
  text-align: left;
  width: 300px;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  /* margin-bottom: 26px; */
}

.button.not-allowed {
  cursor: not-allowed !important;
  background-color: rgba(0, 0, 0, .25) !important;
}

._css-tab-lable {
  text-align: left;
}

._css-tab-lable-child {
  /* width: 74px; */
  height: 24px;
  margin-right: 12px;
  margin-bottom: 10px;
  background: rgba(0, 0, 0, .1);
  color: rgba(0, 0, 0, .45);
  line-height: 24px;
  display: inline-block;
}

._css-child-sure {
  height: 24px;
  margin-right: 12px;
  margin-bottom: 10px;
  color: rgba(0, 0, 0, .45);
  line-height: 24px;
  display: inline-block;
}

._css-tab-lable-child:hover {
  background: rgba(24, 144, 255, .1);
  cursor: pointer;
}

._css-tab-lable-child span {
  padding: 0 8px;
}

._css-tab-lable-child i._css-tab-icon {
  vertical-align: middle;
  line-height: 24px;
  padding-right: 8px;
}

._css-tab-select {
  position: absolute;
  right: 340px;
  bottom: 10px;
  width: 180px;
  height: 300px;
  background: rgba(255, 255, 255, 1);
  box-shadow: 0px 13px 24px -17px rgba(11, 41, 62, 0.8);
  border-radius: 4px;
  padding: 10px;
}

._css-select-search {
  width: 180px;
  height: 32px;
  background: rgba(255, 255, 255, 1);
  display: flex;
  line-height: 32px;
  border-radius: 2px;
  opacity: 0.65;
  border: 1px solid rgba(0, 0, 0, .15);
}

.search-icon {
  vertical-align: middle;
  line-height: 32px;
  padding: 0 8px;
}

._css-search-tab {
  width: 80%;
  border: none;
}

._css-select-list {
  text-align: left;
  /* margin-top: 10px; */
  height: 250px;
  overflow: auto;
}

._css-select-list /deep/ .el-checkbox-group {
  line-height: 24px;
  margin-top: 8px;
}

._css-select-list /deep/ .el-checkbox-group:hover {
  background: rgba(240, 242, 245, 1)
}

._css-select-list /deep/ .el-checkbox-group .el-checkbox__inner {
  margin: 0 8px;
}

.list-refguid {
  position: relative;
}

._css-refguid-shortcut {
  position: absolute;
  top: 5px;
  right: 5px;
  width: 20px;
  height: 20px;
}

._css-refguid-shortcut i {
  color: #1890FF;
}

.tab-attr-content-title {
  height: 32px;
  line-height: 32px;
  background: rgba(240, 242, 245, 1);
  border-radius: 2px;
  padding-left: 12px;
  text-align: left;
  margin: 4px 0;

}

._css-tab-list {
  text-align: left;
  margin-bottom: 6px;
}

._css-tab-list p {
  padding-top: 6px;
  font-size: 14px;
  font-style: normal;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: rgba(0, 0, 0, 1);
  line-height: 22px;
  display: flex;
}

._css-tab-list span:first-child {
  display: inline-block;
  width: 90px;
  text-align: left;
  margin-right: 12px;
  color: rgba(0, 0, 0, .45);
  padding-left: 8px;
}

._css-tab-list span._css-normal {
  width: 55%;
  flex: 1;
  color: rgba(0, 0, 0, .65);
}

._css-library-sort {
  position: fixed;
  border: 1px solid #1890FF;
  width: 120px;
  background: #fff;
  z-index: 9;
  border-radius: 4px;
}

._css-library-sort p {
  text-align: center;
  line-height: 32px;
}

._css-library-sort p:hover {
  color: #1890FF;
  cursor: pointer;
}

._css-select-down {
  margin-bottom: 10px;
  height: 40px;
  width: 98%;
  border-radius: 4px;
  text-align: center;
  line-height: 40px;
  background: rgba(24, 144, 255, 1);
  color: #fff;
  cursor: pointer;
}

._css-upload-down {
  /* margin-top:30px; */
  display: flex;
  align-items: center;
  cursor: pointer;
}

._css-down2 {
  flex: 1;
  margin: auto 20px;
}

.tab-pan-conten /deep/ .el-select {
  width: 98%;
  height: 32px;
  margin-bottom: 12px;
  border-radius: 2px;
  border: 1px solid rgba(0, 0, 0, .15);
}

.tab-pan-conten /deep/ .el-input__inner {
  line-height: 32px;
}

.tab-pan-conten /deep/ .el-input--suffix .el-input__inner {
  padding-left: 12px;
}

.component-model-iframe {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 100;
  background: #fff;
}

.component-model-iframe .model-title {
  height: 40px;
  width: 100%;
  background: rgba(0, 0, 0, .02);
  font-size: 14px;
  font-family: 'PingFangSC-Regular,PingFang SC';
  font-weight: 400;
  color: rgba(0, 0, 0, 0.65);
  line-height: 40px;
}

.model-iframe {
  height: calc(100% - 40px);
  width: 100%;
}

.component-model-iframe .model-title span {
  position: absolute;
  right: 15px;
  top: 12px;
}

._css-check-List {
  margin: 0 5%;
}

._css-tab-submit {
  height: 50px;
}

/* =======修改构件详情，源代码太乱，没有在原有的修改，一下是代码重构后的css======= */
._detail_content {
  display: flex;
  background-color: #fff;
  height: calc( 100% - 40px);
  flex-direction: column;
}

._detail_content ._detail-check-info {
  height: 320px;
}

._detail_content ._detail-tab {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
}

._detail_content ._detail-tab-title {
  height: 40px;
}

._detail_content ._detail-tab-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  margin: 0 5%;
  width: 90%;
  height: 100%;
  overflow-y: auto;
}

._detail_content ._detail-btn {
  height: 50px;
  width: 90%;
  margin: 10px 5%;
}

._detail_content ._detail-btn ._css-select-down {
  height: 40px;
}

._detail_content ._detail-tab-content .tab-pan-conten {
  /* flex:1; */
  display: flex;
  flex-direction: column;
}

._detail_content ._css-tab-submit {
  height: 50px;

}

._detail_content .lable-tab-flex-content {
  flex: 1;
  overflow-y: auto;

  /* height: calc( 100% - 50px ); */
}

._detail_content ._detail-check-img {
  position: relative;
  width: 90%;
  height: 243px;
  margin: 18px 5% 0;
  border-radius: 4px;
  overflow: hidden;
  border: 1px solid rgba(0, 0, 0, .09);
  display: flex;
  align-items: center;
}

._detail_content ._detail-check-img img {
  border: none;
  vertical-align: middle;
  width: 100%;
  border-radius: 4px;
  height: 100%;
}

._detail_content ._detail-check-img .button-hover {
  position: absolute;
  width: 90px;
  height: 32px;
  border-radius: 4px;
  background-color: #1890ff;
  line-height: 32px;
  color: #fff;
  font-size: 12px;
  top: 10px;
  right: 10px;
  cursor: pointer;
  z-index: 1;
}

._detail_content ._detail-check-icon {
  display: flex;
  -ms-flex-pack: justify;
  justify-content: space-between;
  padding: 0 10px;
  color: #818181;
  /* margin-bottom: 15px; */
  margin: 14px 5%;
  border-radius: 4px;
  /* border: 1px solid rgba(0, 0, 0, .15); */
  /* background: rgba(0, 0, 0, .04) */
}


._detail_content ._detail-check-icon .icon {
  line-height: 32px;
}
._detail_content ._detail-check-icon .icon:not(.css-dis) {
  cursor: pointer;
}
._detail_content ._detail-check-icon .icon:not(.css-dis):hover {
  color: #1890ff;
}

._detail_content ._detail-tab-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 40px;
  font-weight: 400;
  color: rgba(0, 0, 0, .15);
  background-color: rgba(0, 0, 0, .02);
  margin-bottom: 12px;
}

._detail_content ._detail-tab-title .title {
  text-align: center;
  flex-grow: 1;
  cursor: pointer;
  color: rgba(0, 0, 0, .65);
}

._detail_content ._detail-tab-title .title.active {
  color: #1890ff;
}

._detail_content .form-input input {
  width: 100%;
  height: 40px;
  border-radius: 4px;
  border: 1px solid rgba(0, 0, 0, 0.09);
  outline: none;
  padding: 9px 16px;
  box-sizing: border-box;
  margin-bottom: 16px;
}

._detail_content .form-input textarea {
  min-width: 100%;
  max-width: 100%;
  border-radius: 4px;
  border: 1px solid rgba(0, 0, 0, 0.09);
  padding: 9px 16px;
  box-sizing: border-box;
  outline: none;
  margin-bottom: 16px;
}

._detail_content .tab-pan-conten .tab-pan-library-label .add {
  text-align: left;
  font-size: 12px;
  color: rgba(0, 0, 0, 0.45);
  cursor: pointer;
  margin-bottom: 15px;
}

._detail_content .tab-pan-conten .tab-pan-library-label .add:hover {
  opacity: 0.8;
}

._detail_content .tab-pan-conten .tab-pan-library-label .add .icon {
  vertical-align: sub;
}
._detail_multiple ._detail-check-info{
  height: 360px;
}

._detail_multiple ._css-check-List{
  flex: 1;
  overflow-y: auto;
}
._detail_multiple .function-menu{
  display: flex;
  height: 30px;
  justify-content: space-between;
  padding: 0 10px;
  color: #818181;
  padding: 0 22%;
}
._css-detail-multiple-title{
  height: 40px;
  line-height: 40px;
}
._css-search-tag{
  color: #606266;
  line-height: 32px;
  text-align: center;
}
