<?xml version="1.0" encoding="UTF-8"?>
<svg width="382px" height="170px" viewBox="0 0 382 170" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <!-- Generator: Sketch 54 (76480) - https://sketchapp.com -->
    <title>temp-muban</title>
    <desc>Created with Sketch.</desc>
    <defs>
        <rect id="path-1" x="0" y="0" width="100" height="24"></rect>
        <rect id="path-3" x="0" y="0" width="100" height="24"></rect>
        <rect id="path-5" x="0" y="0" width="100" height="24"></rect>
        <rect id="path-7" x="0" y="0" width="140" height="24"></rect>
        <rect id="path-9" x="0" y="0" width="140" height="24"></rect>
        <rect id="path-11" x="0" y="0" width="140" height="24"></rect>
        <rect id="path-13" x="0" y="0" width="140" height="24"></rect>
        <rect id="path-15" x="0" y="0" width="140" height="24"></rect>
        <rect id="path-17" x="0" y="0" width="140" height="24"></rect>
        <rect id="path-19" x="0" y="0" width="100" height="24"></rect>
        <rect id="path-21" x="0" y="0" width="140" height="24"></rect>
        <rect id="path-23" x="0" y="0" width="140" height="24"></rect>
        <rect id="path-25" x="0" y="0" width="100" height="24"></rect>
        <rect id="path-27" x="0" y="0" width="140" height="24"></rect>
        <rect id="path-29" x="0" y="0" width="140" height="24"></rect>
        <rect id="path-31" x="0" y="0" width="100" height="24"></rect>
        <rect id="path-33" x="0" y="0" width="140" height="24"></rect>
        <rect id="path-35" x="0" y="0" width="140" height="24"></rect>
        <rect id="path-37" x="0" y="0" width="100" height="24"></rect>
        <rect id="path-39" x="0" y="0" width="140" height="24"></rect>
        <rect id="path-41" x="0" y="0" width="140" height="24"></rect>
    </defs>
    <g id="Page-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="物料管理-导入物资-各种状态同【机构管理导入成员】" transform="translate(-559.000000, -516.000000)">
            <g id="导入成员" transform="translate(468.000000, 361.000000)">
                <g id="temp-muban" transform="translate(92.000000, 156.000000)">
                    <g id="2.按钮/2.次要/小/基本">
                        <g id="6.元件/背景/无圆角">
                            <mask id="mask-2" fill="white">
                                <use xlink:href="#path-1"></use>
                            </mask>
                            <use id="outline" stroke="#000000" stroke-width="1" opacity="0.15" stroke-linecap="square" fill-rule="nonzero" xlink:href="#path-1"></use>
                            <g id="fillColor/primary" stroke-width="1" fill-rule="evenodd" mask="url(#mask-2)" fill="#FFFFFF">
                                <rect id="BG/fill" fill-rule="nonzero" x="0" y="0" width="100" height="24"></rect>
                            </g>
                        </g>
                        <text id="文字" opacity="0.45" font-family="PingFangSC-Regular, PingFang SC" font-size="12" font-weight="normal" line-spacing="20" fill="#000000">
                            <tspan x="0" y="15"> 问题标题</tspan>
                        </text>
                    </g>
                    <g id="2.按钮/2.次要/小/基本" transform="translate(0.000000, 24.000000)">
                        <g id="6.元件/背景/无圆角">
                            <mask id="mask-4" fill="white">
                                <use xlink:href="#path-3"></use>
                            </mask>
                            <use id="outline" stroke="#000000" stroke-width="1" opacity="0.15" stroke-linecap="square" fill-rule="nonzero" xlink:href="#path-3"></use>
                            <g id="fillColor/primary" stroke-width="1" fill-rule="evenodd" mask="url(#mask-4)" fill="#FFFFFF">
                                <rect id="BG/fill" fill-rule="nonzero" x="0" y="0" width="100" height="24"></rect>
                            </g>
                        </g>
                        <text id="文字" opacity="0.65" font-family="PingFangSC-Regular, PingFang SC" font-size="12" font-weight="normal" line-spacing="20" fill="#000000">
                            <tspan x="0" y="15"> 问题1</tspan>
                        </text>
                    </g>
                    <g id="2.按钮/2.次要/小/基本" transform="translate(0.000000, 48.000000)">
                        <g id="6.元件/背景/无圆角">
                            <mask id="mask-6" fill="white">
                                <use xlink:href="#path-5"></use>
                            </mask>
                            <use id="outline" stroke="#000000" stroke-width="1" opacity="0.15" stroke-linecap="square" fill-rule="nonzero" xlink:href="#path-5"></use>
                            <g id="fillColor/primary" stroke-width="1" fill-rule="evenodd" mask="url(#mask-6)" fill="#FFFFFF">
                                <rect id="BG/fill" fill-rule="nonzero" x="0" y="0" width="100" height="24"></rect>
                            </g>
                        </g>
                        <text id="文字" opacity="0.65" font-family="PingFangSC-Regular, PingFang SC" font-size="12" font-weight="normal" line-spacing="20" fill="#000000">
                            <tspan x="0" y="15"> 问题2</tspan>
                        </text>
                    </g>
                    <g id="2.按钮/2.次要/小/基本" transform="translate(100.000000, 0.000000)">
                        <g id="6.元件/背景/无圆角">
                            <mask id="mask-8" fill="white">
                                <use xlink:href="#path-7"></use>
                            </mask>
                            <use id="outline" stroke="#000000" stroke-width="1" opacity="0.15" stroke-linecap="square" fill-rule="nonzero" xlink:href="#path-7"></use>
                            <g id="fillColor/primary" stroke-width="1" fill-rule="evenodd" mask="url(#mask-8)" fill="#FFFFFF">
                                <rect id="BG/fill" fill-rule="nonzero" x="0" y="0" width="140" height="24"></rect>
                            </g>
                        </g>
                        <text id="文字" opacity="0.45" font-family="PingFangSC-Regular, PingFang SC" font-size="12" font-weight="normal" line-spacing="20" fill="#000000">
                            <tspan x="0" y="15">  截止时间</tspan>
                        </text>
                    </g>
                    <g id="2.按钮/2.次要/小/基本" transform="translate(100.000000, 24.000000)">
                        <g id="6.元件/背景/无圆角">
                            <mask id="mask-10" fill="white">
                                <use xlink:href="#path-9"></use>
                            </mask>
                            <use id="outline" stroke="#000000" stroke-width="1" opacity="0.15" stroke-linecap="square" fill-rule="nonzero" xlink:href="#path-9"></use>
                            <g id="fillColor/primary" stroke-width="1" fill-rule="evenodd" mask="url(#mask-10)" fill="#FFFFFF">
                                <rect id="BG/fill" fill-rule="nonzero" x="0" y="0" width="140" height="24"></rect>
                            </g>
                        </g>
                        <text id="文字" opacity="0.65" font-family="PingFangSC-Regular, PingFang SC" font-size="12" font-weight="normal" line-spacing="20" fill="#000000">
                            <tspan x="0" y="15"> 2012-12-12</tspan>
                        </text>
                    </g>
                    <g id="2.按钮/2.次要/小/基本" transform="translate(100.000000, 48.000000)">
                        <g id="6.元件/背景/无圆角">
                            <mask id="mask-12" fill="white">
                                <use xlink:href="#path-11"></use>
                            </mask>
                            <use id="outline" stroke="#000000" stroke-width="1" opacity="0.15" stroke-linecap="square" fill-rule="nonzero" xlink:href="#path-11"></use>
                            <g id="fillColor/primary" stroke-width="1" fill-rule="evenodd" mask="url(#mask-12)" fill="#FFFFFF">
                                <rect id="BG/fill" fill-rule="nonzero" x="0" y="0" width="140" height="24"></rect>
                            </g>
                        </g>
                        <text id="文字" opacity="0.65" font-family="PingFangSC-Regular, PingFang SC" font-size="12" font-weight="normal" line-spacing="20" fill="#000000">
                            <tspan x="0" y="15"> 2012-12-12</tspan>
                        </text>
                    </g>
                    <g id="2.按钮/2.次要/小/基本" transform="translate(240.000000, 0.000000)">
                        <g id="6.元件/背景/无圆角">
                            <mask id="mask-14" fill="white">
                                <use xlink:href="#path-13"></use>
                            </mask>
                            <use id="outline" stroke="#000000" stroke-width="1" opacity="0.15" stroke-linecap="square" fill-rule="nonzero" xlink:href="#path-13"></use>
                            <g id="fillColor/primary" stroke-width="1" fill-rule="evenodd" mask="url(#mask-14)" fill="#FFFFFF">
                                <rect id="BG/fill" fill-rule="nonzero" x="0" y="0" width="140" height="24"></rect>
                            </g>
                        </g>
                        <text id="文字" opacity="0.45" font-family="PingFangSC-Regular, PingFang SC" font-size="12" font-weight="normal" line-spacing="20" fill="#000000">
                            <tspan x="0" y="15">  发起人</tspan>
                        </text>
                    </g>
                    <g id="2.按钮/2.次要/小/基本" transform="translate(240.000000, 24.000000)">
                        <g id="6.元件/背景/无圆角">
                            <mask id="mask-16" fill="white">
                                <use xlink:href="#path-15"></use>
                            </mask>
                            <use id="outline" stroke="#000000" stroke-width="1" opacity="0.15" stroke-linecap="square" fill-rule="nonzero" xlink:href="#path-15"></use>
                            <g id="fillColor/primary" stroke-width="1" fill-rule="evenodd" mask="url(#mask-16)" fill="#FFFFFF">
                                <rect id="BG/fill" fill-rule="nonzero" x="0" y="0" width="140" height="24"></rect>
                            </g>
                        </g>
                        <text id="文字" opacity="0.65" font-family="PingFangSC-Regular, PingFang SC" font-size="12" font-weight="normal" line-spacing="20"></text>
                    </g>
                    <g id="2.按钮/2.次要/小/基本" transform="translate(240.000000, 48.000000)">
                        <g id="6.元件/背景/无圆角">
                            <mask id="mask-18" fill="white">
                                <use xlink:href="#path-17"></use>
                            </mask>
                            <use id="outline" stroke="#000000" stroke-width="1" opacity="0.15" stroke-linecap="square" fill-rule="nonzero" xlink:href="#path-17"></use>
                            <g id="fillColor/primary" stroke-width="1" fill-rule="evenodd" mask="url(#mask-18)" fill="#FFFFFF">
                                <rect id="BG/fill" fill-rule="nonzero" x="0" y="0" width="140" height="24"></rect>
                            </g>
                        </g>
                        <text id="文字" opacity="0.65" font-family="PingFangSC-Regular, PingFang SC" font-size="12" font-weight="normal" line-spacing="20"></text>
                    </g>
                    <g id="2.按钮/2.次要/小/基本" transform="translate(0.000000, 72.000000)">
                        <g id="6.元件/背景/无圆角">
                            <mask id="mask-20" fill="white">
                                <use xlink:href="#path-19"></use>
                            </mask>
                            <use id="outline" stroke="#000000" stroke-width="1" opacity="0.15" stroke-linecap="square" fill-rule="nonzero" xlink:href="#path-19"></use>
                            <g id="fillColor/primary" stroke-width="1" fill-rule="evenodd" mask="url(#mask-20)" fill="#FFFFFF">
                                <rect id="BG/fill" fill-rule="nonzero" x="0" y="0" width="100" height="24"></rect>
                            </g>
                        </g>
                        <text id="文字" opacity="0.65" font-family="PingFangSC-Regular, PingFang SC" font-size="12" font-weight="normal" line-spacing="20" fill="#000000">
                            <tspan x="0" y="15"> 问题1</tspan>
                        </text>
                    </g>
                    <g id="2.按钮/2.次要/小/基本" transform="translate(100.000000, 72.000000)">
                        <g id="6.元件/背景/无圆角">
                            <mask id="mask-22" fill="white">
                                <use xlink:href="#path-21"></use>
                            </mask>
                            <use id="outline" stroke="#000000" stroke-width="1" opacity="0.15" stroke-linecap="square" fill-rule="nonzero" xlink:href="#path-21"></use>
                            <g id="fillColor/primary" stroke-width="1" fill-rule="evenodd" mask="url(#mask-22)" fill="#FFFFFF">
                                <rect id="BG/fill" fill-rule="nonzero" x="0" y="0" width="140" height="24"></rect>
                            </g>
                        </g>
                        <text id="文字" opacity="0.65" font-family="PingFangSC-Regular, PingFang SC" font-size="12" font-weight="normal" line-spacing="20" fill="#000000">
                            <tspan x="0" y="15"> 2012-12-12</tspan>
                        </text>
                    </g>
                    <g id="2.按钮/2.次要/小/基本" transform="translate(240.000000, 72.000000)">
                        <g id="6.元件/背景/无圆角">
                            <mask id="mask-24" fill="white">
                                <use xlink:href="#path-23"></use>
                            </mask>
                            <use id="outline" stroke="#000000" stroke-width="1" opacity="0.15" stroke-linecap="square" fill-rule="nonzero" xlink:href="#path-23"></use>
                            <g id="fillColor/primary" stroke-width="1" fill-rule="evenodd" mask="url(#mask-24)" fill="#FFFFFF">
                                <rect id="BG/fill" fill-rule="nonzero" x="0" y="0" width="140" height="24"></rect>
                            </g>
                        </g>
                        <text id="文字" opacity="0.65" font-family="PingFangSC-Regular, PingFang SC" font-size="12" font-weight="normal" line-spacing="20"></text>
                    </g>
                    <g id="2.按钮/2.次要/小/基本" transform="translate(0.000000, 96.000000)">
                        <g id="6.元件/背景/无圆角">
                            <mask id="mask-26" fill="white">
                                <use xlink:href="#path-25"></use>
                            </mask>
                            <use id="outline" stroke="#000000" stroke-width="1" opacity="0.15" stroke-linecap="square" fill-rule="nonzero" xlink:href="#path-25"></use>
                            <g id="fillColor/primary" stroke-width="1" fill-rule="evenodd" mask="url(#mask-26)" fill="#FFFFFF">
                                <rect id="BG/fill" fill-rule="nonzero" x="0" y="0" width="100" height="24"></rect>
                            </g>
                        </g>
                        <text id="文字" opacity="0.65" font-family="PingFangSC-Regular, PingFang SC" font-size="12" font-weight="normal" line-spacing="20" fill="#000000">
                            <tspan x="0" y="15"> 问题2</tspan>
                        </text>
                    </g>
                    <g id="2.按钮/2.次要/小/基本" transform="translate(100.000000, 96.000000)">
                        <g id="6.元件/背景/无圆角">
                            <mask id="mask-28" fill="white">
                                <use xlink:href="#path-27"></use>
                            </mask>
                            <use id="outline" stroke="#000000" stroke-width="1" opacity="0.15" stroke-linecap="square" fill-rule="nonzero" xlink:href="#path-27"></use>
                            <g id="fillColor/primary" stroke-width="1" fill-rule="evenodd" mask="url(#mask-28)" fill="#FFFFFF">
                                <rect id="BG/fill" fill-rule="nonzero" x="0" y="0" width="140" height="24"></rect>
                            </g>
                        </g>
                        <text id="文字" opacity="0.65" font-family="PingFangSC-Regular, PingFang SC" font-size="12" font-weight="normal" line-spacing="20" fill="#000000">
                            <tspan x="0" y="15"> 2012-12-12</tspan>
                        </text>
                    </g>
                    <g id="2.按钮/2.次要/小/基本" transform="translate(240.000000, 96.000000)">
                        <g id="6.元件/背景/无圆角">
                            <mask id="mask-30" fill="white">
                                <use xlink:href="#path-29"></use>
                            </mask>
                            <use id="outline" stroke="#000000" stroke-width="1" opacity="0.15" stroke-linecap="square" fill-rule="nonzero" xlink:href="#path-29"></use>
                            <g id="fillColor/primary" stroke-width="1" fill-rule="evenodd" mask="url(#mask-30)" fill="#FFFFFF">
                                <rect id="BG/fill" fill-rule="nonzero" x="0" y="0" width="140" height="24"></rect>
                            </g>
                        </g>
                        <text id="文字" opacity="0.65" font-family="PingFangSC-Regular, PingFang SC" font-size="12" font-weight="normal" line-spacing="20"></text>
                    </g>
                    <g id="2.按钮/2.次要/小/基本" transform="translate(0.000000, 120.000000)">
                        <g id="6.元件/背景/无圆角">
                            <mask id="mask-32" fill="white">
                                <use xlink:href="#path-31"></use>
                            </mask>
                            <use id="outline" stroke="#000000" stroke-width="1" opacity="0.15" stroke-linecap="square" fill-rule="nonzero" xlink:href="#path-31"></use>
                            <g id="fillColor/primary" stroke-width="1" fill-rule="evenodd" mask="url(#mask-32)" fill="#FFFFFF">
                                <rect id="BG/fill" fill-rule="nonzero" x="0" y="0" width="100" height="24"></rect>
                            </g>
                        </g>
                        <text id="文字" opacity="0.65" font-family="PingFangSC-Regular, PingFang SC" font-size="12" font-weight="normal" line-spacing="20" fill="#000000">
                            <tspan x="0" y="15"> 问题3</tspan>
                        </text>
                    </g>
                    <g id="2.按钮/2.次要/小/基本" transform="translate(100.000000, 120.000000)">
                        <g id="6.元件/背景/无圆角">
                            <mask id="mask-34" fill="white">
                                <use xlink:href="#path-33"></use>
                            </mask>
                            <use id="outline" stroke="#000000" stroke-width="1" opacity="0.15" stroke-linecap="square" fill-rule="nonzero" xlink:href="#path-33"></use>
                            <g id="fillColor/primary" stroke-width="1" fill-rule="evenodd" mask="url(#mask-34)" fill="#FFFFFF">
                                <rect id="BG/fill" fill-rule="nonzero" x="0" y="0" width="140" height="24"></rect>
                            </g>
                        </g>
                        <text id="文字" opacity="0.65" font-family="PingFangSC-Regular, PingFang SC" font-size="12" font-weight="normal" line-spacing="20" fill="#000000">
                            <tspan x="0" y="15"> 2012-12-12</tspan>
                        </text>
                    </g>
                    <g id="2.按钮/2.次要/小/基本" transform="translate(240.000000, 120.000000)">
                        <g id="6.元件/背景/无圆角">
                            <mask id="mask-36" fill="white">
                                <use xlink:href="#path-35"></use>
                            </mask>
                            <use id="outline" stroke="#000000" stroke-width="1" opacity="0.15" stroke-linecap="square" fill-rule="nonzero" xlink:href="#path-35"></use>
                            <g id="fillColor/primary" stroke-width="1" fill-rule="evenodd" mask="url(#mask-36)" fill="#FFFFFF">
                                <rect id="BG/fill" fill-rule="nonzero" x="0" y="0" width="140" height="24"></rect>
                            </g>
                        </g>
                        <text id="文字" opacity="0.65" font-family="PingFangSC-Regular, PingFang SC" font-size="12" font-weight="normal" line-spacing="20"></text>
                    </g>
                    <g id="2.按钮/2.次要/小/基本" transform="translate(0.000000, 144.000000)">
                        <g id="6.元件/背景/无圆角">
                            <mask id="mask-38" fill="white">
                                <use xlink:href="#path-37"></use>
                            </mask>
                            <use id="outline" stroke="#000000" stroke-width="1" opacity="0.15" stroke-linecap="square" fill-rule="nonzero" xlink:href="#path-37"></use>
                            <g id="fillColor/primary" stroke-width="1" fill-rule="evenodd" mask="url(#mask-38)" fill="#FFFFFF">
                                <rect id="BG/fill" fill-rule="nonzero" x="0" y="0" width="100" height="24"></rect>
                            </g>
                        </g>
                        <text id="文字" opacity="0.65" font-family="PingFangSC-Regular, PingFang SC" font-size="12" font-weight="normal" line-spacing="20" fill="#000000">
                            <tspan x="0" y="15"> 问题4</tspan>
                        </text>
                    </g>
                    <g id="2.按钮/2.次要/小/基本" transform="translate(100.000000, 144.000000)">
                        <g id="6.元件/背景/无圆角">
                            <mask id="mask-40" fill="white">
                                <use xlink:href="#path-39"></use>
                            </mask>
                            <use id="outline" stroke="#000000" stroke-width="1" opacity="0.15" stroke-linecap="square" fill-rule="nonzero" xlink:href="#path-39"></use>
                            <g id="fillColor/primary" stroke-width="1" fill-rule="evenodd" mask="url(#mask-40)" fill="#FFFFFF">
                                <rect id="BG/fill" fill-rule="nonzero" x="0" y="0" width="140" height="24"></rect>
                            </g>
                        </g>
                        <text id="文字" opacity="0.65" font-family="PingFangSC-Regular, PingFang SC" font-size="12" font-weight="normal" line-spacing="20" fill="#000000">
                            <tspan x="0" y="15"> 2012-12-12</tspan>
                        </text>
                    </g>
                    <g id="2.按钮/2.次要/小/基本" transform="translate(240.000000, 144.000000)">
                        <g id="6.元件/背景/无圆角">
                            <mask id="mask-42" fill="white">
                                <use xlink:href="#path-41"></use>
                            </mask>
                            <use id="outline" stroke="#000000" stroke-width="1" opacity="0.15" stroke-linecap="square" fill-rule="nonzero" xlink:href="#path-41"></use>
                            <g id="fillColor/primary" stroke-width="1" fill-rule="evenodd" mask="url(#mask-42)" fill="#FFFFFF">
                                <rect id="BG/fill" fill-rule="nonzero" x="0" y="0" width="140" height="24"></rect>
                            </g>
                        </g>
                        <text id="文字" opacity="0.65" font-family="PingFangSC-Regular, PingFang SC" font-size="12" font-weight="normal" line-spacing="20"></text>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>