// 此文件配置接口地址，window.bim_config 来源于 /static/config/config.js。该文件将不会被压缩
//var base_MainSystemApi='https://www.probim.cn:8080';

const base_MainSystemApi = window.bim_config.webserverurl;
const base_ModelApi = window.bim_config.webserverurl;

// 登录
const Login = `${base_MainSystemApi}/api/User/Home/Login` // 登录
const RemoveLogin = `${base_MainSystemApi}/api/User/Home/RemoveToken` // 登录

const GetOrganizeList = `${base_MainSystemApi}/api/Admin/Organize/GetOrganizeList`; //获取机构列表，分页
const AddOrganize = `${base_MainSystemApi}/api/Admin/Organize/AddOrganize`; //添加机构
const GetOrganizeThumbnailBase64 = `${base_MainSystemApi}/api/Admin/Organize/GetOrganizeThumbnailBase64`; //  获取缩略图
const GetMachineCode = `${base_MainSystemApi}/api/Admin/Organize/GetMachineCode`; //获取机器码
const SaveCompanyAuth = `${base_MainSystemApi}/api/Admin/Organize/SaveCompanyAuth`; //保存机构授权码
const SetOrganzieEndTime = `${base_MainSystemApi}/api/Admin/Organize/SetOrganzieEndTime`; //设置机构的到期时间
const LoginByOrganizeId = '/api/Admin/Organize/LoginByOrganizeId'; //按机构Id登陆
const DeleteOrganize_Logic = `${base_MainSystemApi}/api/Admin/Organize/DeleteOrganize_Logic`; //逻辑删除机构
const LoginAdmin = `${base_MainSystemApi}/api/Admin/Organize/LoginAdmin`; //登陆超级管路员
const TestTokenIsComMgr = `${base_MainSystemApi}/api/User/User/TestTokenIsComMgr`; //得到当前用户是否是机构管理员
const GetOrganizeUserPaged = `${base_MainSystemApi}/api/User/User/GetOrganizeUserPaged`; // 获取机构下成员列表
const InviteUsers = `${base_MainSystemApi}/api/User/Role/InviteUsers`; // 邀请成员
const ImportUser = `${base_MainSystemApi}/api/Admin/Organize/ImportUser`; // 导入成员
const ModifyOrganizeLogo = `${base_MainSystemApi}/api/Admin/Organize/ModifyOrganizeLogo`; // 修改机构logo
const ModifyOrganizeName = `${base_MainSystemApi}/api/Admin/Organize/ModifyOrganizeName`; // 修改机构名称


// 菜单相关===
const CreateMenu = `${base_MainSystemApi}/api/Menu/Menu/CreateMenu`; // 创建菜单
const menuTree = `${base_MainSystemApi}/api/Menu/Menu/tree`; // 菜单树结构
const DeleteMenu = `${base_MainSystemApi}/api/Menu/Menu/DeleteMenu`; // 删除菜单
const menuInfo = `${base_MainSystemApi}/api/Menu/Menu/info`; // 菜单详情
const ModifyMenu = `${base_MainSystemApi}/api/Menu/Menu/ModifyMenu`; // 编辑菜单
const GetModelMenu = `${base_MainSystemApi}/api/Menu/Menu/GetModelMenu`; // 获取模型阶段菜单
const GetModelMenuTree = `${base_MainSystemApi}/api/Menu/Menu/GetModelMenuTree`; // 获取模型阶段菜单(返回树形结构)
const GetSystemButton = `${base_MainSystemApi}/api/Menu/Menu/GetSystemButton`; // 菜单按钮
const GetUserMenuTree = `${base_MainSystemApi}/api/User/User/GetUserMenuTree`; // 项目级菜单树
const AcceptInvite = `${base_MainSystemApi}/api/User/Role/AcceptInvite`; // 接受邀请


// 菜单相关===

// 项目
const ProjectPaged = `${base_MainSystemApi}/api/User/Project/Paged`; // 获取项目列表
const CreateProject = `${base_MainSystemApi}/api/User/Project/CreateProject`; // 创建项目

// 角色
const RoleAdd = `${base_MainSystemApi}/api/User/Role/Add`; // 新建角色
const RoleDelete = `${base_MainSystemApi}/api/User/Role/Delete`; // 删除角色
const RoleDisable = `${base_MainSystemApi}/api/User/Role/Disable`; // 禁用角色
const RoleEnable = `${base_MainSystemApi}/api/User/Role/Enable`; // 启用角色
const RoleUpdate = `${base_MainSystemApi}/api/User/Role/Update`; // 编辑角色名称
const RoleAddUser = `${base_MainSystemApi}/api/User/Role/AddUser`; // 添加成员到角色
const RoleRemoveUser = `${base_MainSystemApi}/api/User/Role/RemoveUser`; // 删除当前角色下某成员
const RoleMoveUser = `${base_MainSystemApi}/api/User/Role/MoveUser`; // 移动角色

const RoleTree = `${base_MainSystemApi}/api/User/Role/Tree`; // 获取角色权限树
const GetModelMenuTreeAll = `${base_MainSystemApi}/api/User/Role/GetModelMenuTreeAll`; // 获取模型权限树

const GetUserPaged = `${base_MainSystemApi}/api/User/User/GetUserPaged`; // 获取项目/角色下成员列表
const SetRoleAuth = `${base_MainSystemApi}/api/User/Role/SetRoleAuth`; // 修改功能项目菜单权限
const SetModelRoleAuth = `${base_MainSystemApi}/api/User/Role/SetModelRoleAuth`; // 修改模型权限


const QRCode = `${base_MainSystemApi}/api/Tool/File/QRCode`; // 二维码  原接口/api/User/Document/QRCode



// =====================分割线================
var base_ShareHeader = window.bim_config.shareurl;


var GetOrganizeTree_Url = '/api/Admin/Organize/GetOrganizeTree'; //获取所有机构部门树
var GetUsersByOrganizeIdOrDepartmentId = "/api/User/User/GetUsersByOrganizeIdOrDepartmentId"; //根据机构Id或部门Id得到用户
var GetUserByAccountOrRealName = '/api/User/User/GetUserByAccountOrRealName'; //根据账号或者真实姓名得到用户
var GetOrganize_Unit = '/api/Admin/Organize/NewMul_GetProject_ByOrganizeId';


var AddModel = '/api/prj/UploadModel'; //上传模型
var GetModeRelatedDocList = `${base_MainSystemApi}/api/v1/file/GetModelFileList`; //获取模型关联文档列表
var DelModel = '/API/Prj/DeleteModel'; //删除模型
var UpdateModel = '/api/prj/UpdateModel'; //更新模型
var ReNameModel = '/api/prj/UpdateModelName'; //重命名
var GetModelConfig = '/api/prj/GetProjectConfig'; //获取项目字典（阶段、现场数据字典）
var UpdateModelPhase = '/api/Prj/UpdateModelPhase'; //修改模型阶段
var UpdateModelThumbnail = '/api/prj/UpdateModelThumbnail'; //修改模型缩略图
var PostModelParams = '/api/Sys/PostURLParameters'; //打开模型时需要post的参数
var GetModelInfo = '/api/Prj/GetModel'; //得到模型详细信息（视图等）【统计视图数量】
var GetAllElementCount = '/api/Model/GetAllElementCount'; //得到所有构件【统计构件数量】
var GetSpaceCount = '/api/Model/GetAllSpaceCount'; //得到空间数量【统计空间数】
var GetSystemNum = '/api/Model/GetAllMEPSystemType'; //得到所有系统【统计系统数】
var GetAllViewPoint = '/api/Prj/GetAllViewpoint'; //得到所有视点【统计视点数量、批注数量（0是视点、1是批注）】
var GetFile = '/api/Model/GetFile'; //得到模型其他信息{FileType='PlanView'是2D}【统计2D】
var GetAllLevelCount = '/api/Model/GetAllLevelCount'; //得到所有楼层数【统计楼层】
var CreateMergeModel = '/api/Prj/CreateMergeModel'; //合并模型
var GetModelTargetObj = '/api/Migration/MRelation/GetTargetIDBySourceID'; //获得模型关联文档
var GetFileCountBySourceID = '/api/Doc/GetFileCountBySourceID'; //获得关联文档数量【统计文档数量】
var GetRelationIssueBySourceID = '/Project/GetRelationIssueBySourceID'; //获得问题数量【统计问题数量】
var GetAllVersions = '/api/Prj/GetAllVersions'; //得到所有模型版本
var UpdateProjectConfig = '/api/prj/UpdateProjectConfig'; //更新模型字典、现场数据 等字典信息

var GetProjectLayers = '/api/GIS/GetProjectLayers'; //得到当前项目的所有GIS图层数据
var UploadGISLayer = "/api/GIS/UploadGISLayer";
var UpdateGISLayer = "/api/GIS/UpdateGISLayer";
var DeleteGISLayer = '/api/GIS/DeleteProjectLayer'; //删除图层

var GetAllFileInfoByIDs = '/api/Doc/GetAllDocInfoByIDs';
var GetDocListBySourceID = '/api/Doc/GetFileRelationListBySourceID';
var DownLoadFile = '/api/Doc/GetFile'
var GetPrivilegeList = '/api/Privilege/List';


var GetDataItemListJson = '/api/System/Module/GetDataItemListJson'; //得到机构字典数据
var ExistOrganizeByName = '/api/Admin/Organize/ExistOrganizeByName'; //返回是否存在同名机构
var GetAllOrganizeName = '/api/Admin/Organize/GetAllOrganizeName';
var CreateModelShare = '/api/User/Model/CreateModelShare'; //创建模型分享链接
var GetOrganizeEndTime = '/api/Admin/Organize/GetOrganizeEndTime'; //获取机构或项目的到期时间

var GetProjectRoles = '/api/User/Role/GetOrgFuncAuth'; //获取当前登录人的项目权限

var GetSystemLog = '/api/Admin/AdminUser/GetPageListJson'; //获取系统日志
var GetProjectLog = '/api/Admin/AdminUser/GetLogDatas'; //获取项目日志



const getCalendarList = '/api/v1/meeting/calendar/list' // 获取会议看板

const getRoomList = '/api/v1/room/list' // 获取会议室列表

const createRoom = '/api/v1/room/save' // 创建会议室

const deleteRoom = '/api/v1/room/delete' // 删除会议室

const prohibitedRoom = '/api/v1/room/prohibited' // 冻结会议室

const enableRoom = '/api/v1/room/enable' // 启用会议室

const saveMeeting = '/api/v1/meeting/save' // 启用会议室

const getMeetingList = '/api/v1/meeting/paged' // 会议列表

const getMeetingInfo = '/api/v1/meeting' // 会议信息

const getMeetingAgenda = '/api/v1/meeting/agenda' // 会议议程

const getMeetingServices = '/api/v1/meeting/services' // 会议服务

const getMeetingSign = '/api/v1/meeting/sign-setting' // 会议服务

const MeetingResulotion = '/api/v1/meeting/resulotion' // 会议决议

const exportExcel = '/api/v1/meeting/export' // 会议决议

const deleteMeeting = '/api/v1/meeting/delete' // 删除会议

const getPersonList = '/api/v1/meeting/person' // 参会人员

const uploadFile = '/api/v1/meeting/upload-attached' // 参会人员

const getSigninfo = '/api/v1/meeting/sign-info' //签到信息

const getSummary = '/api/v1/meeting/my-summary' // 会议汇总




export default {
    base_MainSystemApi,
    base_ModelApi,


    Login,
    RemoveLogin,

    GetOrganizeList,
    AddOrganize,
    GetOrganizeThumbnailBase64,
    GetMachineCode,
    SaveCompanyAuth,
    SetOrganzieEndTime,
    LoginByOrganizeId,
    DeleteOrganize_Logic,
    LoginAdmin,
    TestTokenIsComMgr,
    GetOrganizeUserPaged,
    InviteUsers,
    ImportUser,
    ModifyOrganizeLogo,
    ModifyOrganizeName,




    CreateMenu,
    menuTree,
    DeleteMenu,
    menuInfo,
    ModifyMenu,
    GetModelMenu,
    GetModelMenuTree,
    GetSystemButton,
    GetUserMenuTree,
    AcceptInvite,

    ProjectPaged,
    CreateProject,

    RoleAdd,
    RoleDelete,
    RoleDisable,
    RoleEnable,
    RoleAddUser,
    RoleRemoveUser,
    RoleMoveUser,
    RoleUpdate,
    RoleTree,
    GetModelMenuTreeAll,
    GetUserPaged,
    SetRoleAuth,
    SetModelRoleAuth,
    QRCode,




    // =======================分割线=========
    base_ShareHeader,

    GetOrganizeTree_Url,
    GetUsersByOrganizeIdOrDepartmentId,
    GetUserByAccountOrRealName,
    GetOrganize_Unit,

    AddModel,
    DelModel,
    UpdateModel,
    ReNameModel,
    GetModelConfig,
    UpdateModelPhase,
    UpdateModelThumbnail,
    PostModelParams,
    GetModelInfo,
    CreateMergeModel,
    GetModelTargetObj,
    GetAllElementCount,
    GetSpaceCount,
    GetSystemNum,
    GetAllViewPoint,
    GetFile,
    GetAllLevelCount,
    GetFileCountBySourceID,
    GetRelationIssueBySourceID,
    GetAllVersions,
    UpdateProjectConfig,

    GetProjectLayers,
    UploadGISLayer,
    UpdateGISLayer,
    DeleteGISLayer,

    GetAllFileInfoByIDs,
    GetDocListBySourceID,
    DownLoadFile,
    GetPrivilegeList,


    ExistOrganizeByName,
    GetDataItemListJson,
    GetAllOrganizeName,
    CreateModelShare,
    GetOrganizeEndTime,

    GetProjectRoles,

    GetSystemLog,
    GetProjectLog,


    getCalendarList,
    getRoomList,
    createRoom,
    deleteRoom,
    prohibitedRoom,
    enableRoom,
    saveMeeting,
    getMeetingList,
    getMeetingInfo,
    getMeetingAgenda,
    getMeetingServices,
    getMeetingSign,
    MeetingResulotion,
    exportExcel,
    deleteMeeting,
    getPersonList,
    uploadFile,
    getSigninfo,
    getSummary,
    GetModeRelatedDocList
}
