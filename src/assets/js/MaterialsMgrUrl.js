const base_MainSystemApi = window.bim_config.webserverurl;
const base_planurl = window.bim_config.integrated_planurl;

const GetCategories = `${base_MainSystemApi}/api/Material/MaterialCategory/GetCategories`; //  获取项目构件分类
const GetMaterialStatus = `${base_MainSystemApi}/api/Material/Mtr/GetMaterialStatus`; // 获取物料状态数据
const EditCategory = `${base_MainSystemApi}/api/Material/MaterialCategory/EditCategory`; // 新增或编辑分类
const ModifyMaterialBasicInfo = `${base_MainSystemApi}/api/Material/Mtr/ModifyMaterialBasicInfo`; // 编辑详情

const EditCategoryField = `${base_MainSystemApi}/api/Material/MaterialCategory/EditCategoryField`; //  新增或修改一个物料分类中的自定义字段 fieldid 为空，空串，或空白字符串时，表示新增。否则表示修改
const GetMaterialCategoryExtJson = `${base_MainSystemApi}/api/Material/MaterialCategory/GetMaterialCategoryExtJson`; //  获取某分类下的 ext_json数据，该数据存储了构件分类下的所有字段
const RemoveCategory = `${base_MainSystemApi}/api/Material/MaterialCategory/RemoveCategory`; // 移除某一个构件分类
const TestCategoryIsLeaf = `${base_MainSystemApi}/api/Material/MaterialCategory/TestCategoryIsLeaf`; //  判断某项目中某节点是否为叶子节点


const AddMaterialItem = `${base_MainSystemApi}/api/Material/Mtr/AddMaterialItem`; //  新增物资数据
const AttachImageOrFile = `${base_MainSystemApi}/api/Material/Mtr/AttachImageOrFile`; //  添加附件或图片关联


const DownloadCategoryTemplate_down_get = `${base_MainSystemApi}/api/Material/Mtr/DownloadCategoryTemplate`; //  通过物料分类ID下载物料模板
const DownloadImportTemplate = `${base_MainSystemApi}/Menu/导入模板.xlsx`; //

const DownloadCategoryTemplate_post = `${base_MainSystemApi}/api/Material/Mtr/DownloadCategoryTemplate`; //  通过物料分类ID，及多个物料ID，导出数据
const DownloadCategoryTemplate_Get = `${base_MainSystemApi}/api/Material/Mtr/DownloadCategoryTemplate_Get`; //  post 后 get此接口获取文件
const ElementsRelate = `${base_MainSystemApi}/api/Material/Mtr/ElementsRelate`; //  添加与构件的关联
const InsertElementsRelate = `${base_MainSystemApi}/api/Material/Mtr/InsertElementsRelate`; //  添加与构件的关联（新增）
const ClearMaterialElements = `${base_MainSystemApi}/api/Material/Mtr/ClearMaterialElements`; //  取消关联
const GetMaterialDetail = `${base_MainSystemApi}/api/Material/Mtr/GetMaterialDetail`; // 获取物料详情信息
const GetMaterialList = `${base_MainSystemApi}/api/Material/Mtr/GetMaterialList`; // 获取物资列表数据
const GetMaterialList_Condition2 = `${base_MainSystemApi}/api/Material/Mtr/GetMaterialList_Condition2`; //
const GetMaterialTypes = `${base_MainSystemApi}/api/Material/Mtr/GetMaterialTypes`; // 获取物料的分类或状态数据
const RemoveMaterialItems = `${base_MainSystemApi}/api/Material/Mtr/RemoveMaterialItems`; // 移除多条物料
const UploadMaterials = `${base_MainSystemApi}/api/Material/Mtr/UploadMaterials`; //导入物料的excel文件
const UploadModelPhase = `${base_MainSystemApi}/api/Menu/Menu/ImportModelPhase`; //导入模型阶段的excel文件
const ImportScenTree = `${base_MainSystemApi}/api/Menu/Menu/ImportScenTree`; //导入模型阶段的excel文件
const ImportStructure = `${base_MainSystemApi}/api/Project/Structure/ImportStructureTree`; //导入项目结构的excel文件
const GetStructureTree = `${base_MainSystemApi}/api/Project/Structure/GetStructureTree`; // 获取项目结构树
const ExportStructureTree = `${base_MainSystemApi}/api/Project/Structure/ExportStructureTree`; // 导出项目结构的excel文件
const UpdateProjectStructure = `${base_MainSystemApi}/api/Project/Structure/UpdateProjectStructure`; // 更新项目结构
const MoveStructure = `${base_MainSystemApi}/api/Project/Structure/MoveStructure`; // // 移动项目结构
const UpDownGradeStructure = `${base_MainSystemApi}/api/Project/Structure/UpDownGradeStructure`; // // 升降级项目结构
const DeleteProjectStructure = `${base_MainSystemApi}/api/Project/Structure/DeleteProjectStructure`; // // 删除项目结构
const CreateProjectStructure = `${base_MainSystemApi}/api/Project/Structure/CreateProjectStructure`; // // 创建项目结构
const ConnectStructure = `${base_MainSystemApi}/api/Project/Structure/ConnectStructure`; // // 关联项目结构








const GetMaterialsElements = `${base_MainSystemApi}/api/Material/Mtr/GetMaterialsElements`; // 获取指定的多个构件 bm_guid 下的相关模型构件关联数据

const GetAllElements = `${base_MainSystemApi}/api/Material/Valuation/GetAllElements`; // 龙云大屏所有验评的绑定的相关构件，根据分类ID及模型ID获取该分类下所有关联的构件

// 进度关联

const AddMaterialRelation = `${base_MainSystemApi}/api/Plus/PlusTask/AddMaterialRelation`; // 手动关联
const GetTaskMaterialRelation = `${base_MainSystemApi}/api/Plus/PlusTask/GetTaskMaterialRelation`; // 查看已关联构件
const AutomaticCoreelationComponect = `${base_MainSystemApi}/api/Material/Mtr/AutomaticCoreelationComponect`; // 自动关联
const GetProject = `${base_MainSystemApi}/api/Schedual/Schedual/GetProject`; // 进度填报获取数据
const Addproject = `${base_MainSystemApi}/api/Schedual/Schedual/Addproject`; // 进度填报新增数据
const EditProject = `${base_MainSystemApi}/api/Schedual/Schedual/EditProject`; //  进度填报编辑数据
const GetUnitTextConfig = `${base_MainSystemApi}/api/Schedual/Schedual/GetUnitTextConfig`; // 进度填报获取设置单位
const SetUnitText = `${base_MainSystemApi}/api/Schedual/Schedual/SetUnitText`; //  进度填报设置单位
const submitAudit = `${base_MainSystemApi}/api/Schedual/Schedual/Audit`; //  进度填报审批
const GetMaterialEleInfoByPlanId = `${base_MainSystemApi}/api/Plus/PlusTask/GetMaterialEleInfoByPlanId`; // 进度填报模拟
const GetProgressByPlanId = `${base_MainSystemApi}/api/Schedual/Schedual/GetProgressByPlanId`; //
const SetPlusTaskStatisticsSort = `${base_MainSystemApi}/api/Plus/PlusTask/SetPlusTaskStatisticsSort` // 设置大屏统计和排序
    // const  = `${base_MainSystemApi}`; //

// 进度模块
const planGetList = `${base_planurl}/api/v1/plus/GetList`; // 获取进度list  原接口 api/Plus/PlusProject/NewComm_GetListByOrganizeId
const planAddPlan = `${base_planurl}/api/v1/plus/AddPlan`; // 新增进度列表  原接口 api/Plus/PlusProject/NewComm_Add2
const planRename = `${base_planurl}/api/v1/plus/Rename`; //  重命名进度列表  原接口 api/Plus/PlusProject/NewComm_Modify
const planDelPlan = `${base_planurl}/api/v1/plus/DelPlan`; // 删除进度列表  原接口 api/Plus/PlusProject/NewComm_Delete
const planGetTree = `${base_planurl}/api/v1/plus/GetTree`; // 获取树结构  原接口 /api/Schedual/Schedual/GetTree
const planList = `${base_planurl}/api/v1/plus/GetPlanTaskListByPlanId`; // 原接口 /api/Plus/PlusProject/GetPlanTaskListByPlanId
const planSave = `${base_planurl}/api/v1/plus/Save`; // 在进度甘特图中用到
const planImportProject = `${base_planurl}/api/v1/plus/ImportProject`; // 导入进度列表  原接口 api/Plus/PlusProject/ImportProject
const planExport = `${base_planurl}/api/v1/plus/Export`; // 导出进度列表  原接口 /demo/ExportProject.aspx
// const  = `${base_planurl}`; // 原接口

// 质量验收
const ValuationPaged = `${base_MainSystemApi}/api/Material/Valuation/Paged`; // 表格查询
const ValuationSave = `${base_MainSystemApi}/api/Material/Valuation/Save`; // 填报提交
const ValuationDetail = `${base_MainSystemApi}/api/Material/Valuation/Detail`; // 详情
const ValuationAudit = `${base_MainSystemApi}/api/Material/Valuation/Audit`; // 详情

export default {
    GetCategories,
    GetMaterialStatus,
    EditCategory,
    ModifyMaterialBasicInfo,
    EditCategoryField,
    GetMaterialCategoryExtJson,
    RemoveCategory,
    TestCategoryIsLeaf,
    AddMaterialItem,
    AttachImageOrFile,
    DownloadCategoryTemplate_down_get,
    DownloadCategoryTemplate_post,
    DownloadCategoryTemplate_Get,
    ElementsRelate,
    InsertElementsRelate,
    ClearMaterialElements,
    GetMaterialDetail,
    GetMaterialList,
    GetMaterialList_Condition2,
    GetMaterialTypes,
    RemoveMaterialItems,
    UploadMaterials,
    GetMaterialsElements,
    GetAllElements,

    AddMaterialRelation,
    GetTaskMaterialRelation,
    AutomaticCoreelationComponect,
    GetProject,
    Addproject,
    EditProject,
    GetUnitTextConfig,
    SetUnitText,
    submitAudit,
    GetMaterialEleInfoByPlanId,
    GetProgressByPlanId,
    SetPlusTaskStatisticsSort,

    planGetList,
    planAddPlan,
    planRename,
    planDelPlan,
    planGetTree,
    planList,
    planSave,
    planImportProject,
    planExport,

    ValuationPaged,
    ValuationSave,
    ValuationDetail,
    ValuationAudit,
  DownloadImportTemplate,
  UploadModelPhase,
  ImportScenTree,
  ImportStructure,
  GetStructureTree,
  ExportStructureTree,
  UpdateProjectStructure,
  MoveStructure,
  UpDownGradeStructure,
  DeleteProjectStructure,
  CreateProjectStructure,
  ConnectStructure,
}
