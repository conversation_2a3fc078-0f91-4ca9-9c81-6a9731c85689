const baseApi = window.bim_config.webserverurl;
// 投资模块需求接口
const categoryTree = `${baseApi}/api/invest/category/tree`; // 获取树结构
const categoryCreate = `${baseApi}/api/invest/category/create`; // 树结构创建
const categoryDelete = `${baseApi}/api/invest/category/delete`; // 树结构删除分类
const categoryModify = `${baseApi}/api/invest/category/modify`; // 树结构编辑分类

const investmentCreate = `${baseApi}/api/invest/investment/create`; // 创建主体投资
const investmentDelete = `${baseApi}/api/invest/investment/delete`; // 删除主体投资 
const investmentInfo = `${baseApi}/api/invest/investment/info`; // 获取主体投资详情
const investmentList = `${baseApi}/api/invest/investment/list`; // 获取主体投资
const investmentModify = `${baseApi}/api/invest/investment/modify`; // 编辑主体投资
const i_DetailCreate = `${baseApi}/api/invest/investment/detail/create`; // 创建主体投资详情
const i_DetailDelete = `${baseApi}/api/invest/investment/detail/delete`; // 删除投资详情
const i_DetailInfo = `${baseApi}/api/invest/investment/detail/info`; // 获取单个主体投资详细信息
const i_DetailList = `${baseApi}/api/invest/investment/detail/list`; // 获取单个主体投资详情
const i_DetailModify = `${baseApi}/api/invest/investment/detail/modify`; // 编辑某个主体投资详情
// const getFirstEstimate = `${baseApi}/api/invest/investment/GetFirstEstimate`; // 根据分类Id或者年度获取第一次填报的总概算及年度概算、（后端换接口了）
const getFirstEstimate = `${baseApi}/api/invest/investment/IsCanAdd`; // 根据分类Id或者年度获取第一次填报的总概算及年度概算



const otherCreate = `${baseApi}/api/invest/other/create`; // 其他-创建
const otherDelete = `${baseApi}/api/invest/other/delete`; //  其他-删除
const otherInfo = `${baseApi}/api/invest/other/info`; //  其他-获取信息
const otherList = `${baseApi}/api/invest/other/list`; //  其他-获取列表
const otherModify = `${baseApi}/api/invest/other/modify`; //  其他-编辑

const resettlementCreate = `${baseApi}/api/invest/resettlement/create`; // 移民安置-创建
const resettlementDelete = `${baseApi}/api/invest/resettlement/delete`; // 移民安置-删除
const resettlementInfo = `${baseApi}/api/invest/resettlement/info`; // 移民安置-获取单个信息
const resettlementList = `${baseApi}/api/invest/resettlement/list`; // 移民安置-获取列表
const resettlementModify = `${baseApi}/api/invest/resettlement/modify`; // 移民安置-编辑
const resettlementDetailCreate = `${baseApi}/api/invest/resettlement/detail/create`; // 移民安置-详情-创建
const resettlementDetailDelete = `${baseApi}/api/invest/resettlement/detail/delete`; // 移民安置-详情-删除
const resettlementDetailInfo = `${baseApi}/api/invest/resettlement/detail/info`; // 移民安置-详情-获取信息
const resettlementDetailList = `${baseApi}/api/invest/resettlement/detail/list`; // 移民安置-详情-获取列表
const resettlementDetailModify = `${baseApi}/api/invest/resettlement/detail/modify`; // 移民安置-详情-编辑 

// 查看报表
const investmentReport = `${baseApi}/api/invest/investment/report`; // 主体投资报表
const resettlementReport = `${baseApi}/api/invest/resettlement/report`; // 主体投资报表
const otherReport = `${baseApi}/api/invest/other/report`; // 主体投资报表
export default {
    categoryTree,
    categoryCreate,
    categoryDelete,
    categoryModify,
    investmentCreate,
    investmentDelete,
    investmentInfo,
    investmentList,
    investmentModify,
    i_DetailCreate,
    i_DetailDelete,
    i_DetailInfo,
    i_DetailList,
    i_DetailModify,
    getFirstEstimate,
    otherCreate,
    otherDelete,
    otherInfo,
    otherList,
    otherModify,
    resettlementCreate,
    resettlementDelete,
    resettlementInfo,
    resettlementList,
    resettlementModify,
    resettlementDetailCreate,
    resettlementDetailDelete,
    resettlementDetailInfo,
    resettlementDetailList,
    resettlementDetailModify,

    investmentReport,
    resettlementReport,
    otherReport,
}