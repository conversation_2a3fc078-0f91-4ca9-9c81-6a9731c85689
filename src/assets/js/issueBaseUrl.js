const base_MainSystemApi = window.bim_config.webserverurl;

const GetIssueTypes = `${base_MainSystemApi}/api/User/Issue/GetIssueTypes`; // 得指定机构下的所有问题分类
const GetIssueStatus = `${base_MainSystemApi}/api/User/Issue/GetIssueStatus`; // 获取某机构下的问题状态数据 
const GetToAddIssueJoiners = `${base_MainSystemApi}/api/User/User/GetToAddIssueJoiners`; // 获取可添加的问题参与人
const AddIssueJoiner = `${base_MainSystemApi}/api/User/Issue/AddIssueJoiner`; // 添加问题参与人
const AddIssue = `${base_MainSystemApi}/api/User/Issue/AddIssue`; // 发起问题
const ImportIssue = `${base_MainSystemApi}/api/User/Issue/ImportIssue`; // 导入问题追踪
const AddComment = `${base_MainSystemApi}/api/User/Issue/AddComment`; // 添加评论
const AddImageComment = `${base_MainSystemApi}/api/User/Issue/AddImageComment`; // 添加评论图片
const DownloadProjectIssueTemplate = `${base_MainSystemApi}/api/User/Issue/DownloadProjectIssueTemplate`; // 下载问题追踪导入模板
const AddOrModifyIssueOrganizeTag = `${base_MainSystemApi}/api/User/Issue/AddOrModifyIssueOrganizeTag`; // 添加或修改问题标签
const GetIssueDetail = `${base_MainSystemApi}/api/User/Issue/GetIssueDetail`; // 获取问题追踪详情
const GetIssueList = `${base_MainSystemApi}/api/User/Issue/GetIssueList`; // 分页获取问题追踪列表
const GetIssueOrganizeTags = `${base_MainSystemApi}/api/User/Issue/GetIssueOrganizeTags`; // 获取指定项目的问题标签
const IsPMOrIssueCreator = `${base_MainSystemApi}/api/User/Issue/IsPMOrIssueCreator`; // 判断当前人是否是指定项目的项目管理员或问题的创建者
const ModifyIssueEndDate = `${base_MainSystemApi}/api/User/Issue/ModifyIssueEndDate`; // 修改问题结束时间
const ModifyIssueStatus = `${base_MainSystemApi}/api/User/Issue/ModifyIssueStatus`; // 修改问题的状态
const ModifyIssueType = `${base_MainSystemApi}/api/User/Issue/ModifyIssueType`; // 修改问题的类型
const RemoveDicItem = `${base_MainSystemApi}/api/User/Issue/RemoveDicItem`; // 删除字典项
const RemoveIssue = `${base_MainSystemApi}/api/User/Issue/RemoveIssue`; // 删除问题
const RemoveIssueJoiner = `${base_MainSystemApi}/api/User/Issue/RemoveIssueJoiner`; // 移除问题参与人
const RemoveIssueTag = `${base_MainSystemApi}/api/User/Issue/RemoveIssueTag`; // 移除问题标签
const SaveDicItem = `${base_MainSystemApi}/api/User/Issue/SaveDicItem`; // 保存字典项
const SetIssueDeleteMark = `${base_MainSystemApi}/api/User/Issue/SetIssueDeleteMark`; // 设置某问题的状态，1为已删除，0为正常，2为归档
const Tag_OverrideIssueTag = `${base_MainSystemApi}/api/User/Issue/Tag_OverrideIssueTag`; // 直接覆盖问题相关标签
const UploadImage = `${base_MainSystemApi}/api/Tool/File/UploadImage`; // 上传图片
const Tag_RemoveTagFromIssue = `${base_MainSystemApi}/api/User/Issue/Tag_RemoveTagFromIssue`; // 将一个标签从问题中移除
const AddRelation = `${base_MainSystemApi}/api/User/Issue/AddRelation`; // 添加问题关联文档（或 base_file 中的图片）


// ======质量和安全
const AddMission = `${base_MainSystemApi}/api/Examine/Exam/AddMission`; // 发起任务
const CheckMission = `${base_MainSystemApi}/api/Examine/Exam/CheckMission`; //  对PC创建的任务进行检查操作
const GetMissions = `${base_MainSystemApi}/api/Examine/Exam/GetMissions`; // 移动端/PC端获取现场数据接口
const GetMission = `${base_MainSystemApi}/api/Examine/Exam/GetMission`; // 获取单个任务信息
const Exam_GetExamTypes = `${base_MainSystemApi}/api/Examine/Exam/Exam_GetExamTypes`; // 获取某项目的现场分类数据
const RemoveItems = `${base_MainSystemApi}/api/Examine/Exam/RemoveItems`; //  删除一个或多个质量数据
const UpdateItems = `${base_MainSystemApi}/api/Examine/Exam/UpdateItems`; // 修改状态为关闭
const ModifyMissionChecker = `${base_MainSystemApi}/api/Examine/Exam/ModifyMissionChecker`; // 修改检查人 
const ModifyMissionInfo = `${base_MainSystemApi}/api/Examine/Exam/ModifyMissionInfo`; // 编辑任务
const ExportMission = `${base_MainSystemApi}/api/Examine/Exam/ExportMission`; // 

export default {
    GetIssueTypes,
    GetIssueStatus,
    GetToAddIssueJoiners,
    AddIssueJoiner,
    AddIssue,
    ImportIssue,
    AddComment,
    AddImageComment,
    DownloadProjectIssueTemplate,
    AddOrModifyIssueOrganizeTag,
    GetIssueDetail,
    GetIssueList,
    GetIssueOrganizeTags,
    IsPMOrIssueCreator,
    ModifyIssueEndDate,
    ModifyIssueStatus,
    ModifyIssueType,
    RemoveDicItem,
    RemoveIssue,
    RemoveIssueJoiner,
    RemoveIssueTag,
    SaveDicItem,
    SetIssueDeleteMark,
    Tag_OverrideIssueTag,
    UploadImage,
    Tag_RemoveTagFromIssue,
    AddRelation,

    AddMission,
    CheckMission,
    GetMissions,
    GetMission,
    Exam_GetExamTypes,
    RemoveItems,
    UpdateItems,
    ModifyMissionChecker,
    ModifyMissionInfo,
    ExportMission,
}