<?xml version="1.0" encoding="UTF-8"?>
<svg width="100%" height="100%" viewBox="0 0 140 110" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <!-- Generator: Sketch 61.2 (89653) - https://sketch.com -->
    <title>编组</title>
    <desc>Created with Sketch.</desc>
    <defs>
        <rect id="path-1" x="0" y="0" width="80" height="100" rx="4"></rect>
        <rect id="path-3" x="0" y="0" width="110" height="100" rx="4"></rect>
        <rect id="path-5" x="0" y="0" width="140" height="100" rx="4"></rect>
    </defs>
    <g id="展示" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="列表/族库文件夹备份">
            <g id="编组">
                <g id="元件/背景/圆角4/全" transform="translate(25.000000, 0.000000)">
                    <g id="*/shape/rectangle/4px/RC-A">
                        <mask id="mask-2" fill="white">
                            <use xlink:href="#path-1"></use>
                        </mask>
                        <g id="outline" opacity="0" fill-rule="nonzero"></g>
                        <g id="fillColor/primary" mask="url(#mask-2)" fill="#1890FF" fill-opacity="0.1" fill-rule="nonzero">
                            <rect id="BG/fill" x="0" y="0" width="80" height="100"></rect>
                        </g>
                    </g>
                </g>
                <g id="元件/背景/圆角4/全" transform="translate(15.000000, 5.000000)">
                    <g id="*/shape/rectangle/4px/RC-A">
                        <mask id="mask-4" fill="white">
                            <use xlink:href="#path-3"></use>
                        </mask>
                        <g id="outline" opacity="0" fill-rule="nonzero"></g>
                        <g id="fillColor/primary" mask="url(#mask-4)" fill="#49B5FF">
                            <rect id="BG/fill" x="0" y="0" width="110" height="100"></rect>
                        </g>
                    </g>
                </g>
                <g id="元件/背景/圆角4/全" transform="translate(0.000000, 10.000000)">
                    <g id="*/shape/rectangle/4px/RC-A">
                        <mask id="mask-6" fill="white">
                            <use xlink:href="#path-5"></use>
                        </mask>
                        <g id="outline" opacity="0" fill-rule="nonzero"></g>
                        <g id="fillColor/primary" mask="url(#mask-6)" fill="#1890FF" fill-rule="nonzero">
                            <rect id="BG/fill" x="0" y="0" width="140" height="100"></rect>
                        </g>
                    </g>
                </g>
                <g id="元件/图标/7.文档/interface-folder" transform="translate(40.000000, 30.000000)" fill="#FFFFFF">
                    <g id="Icon/interface/interface-folder">
                        <path d="M19.2840123,30.239564 L54,30.239564 L54,45.977591 C53.6626652,47.3288245 52.5806692,48.5714286 51.2022494,48.5714286 L9.01856496,48.5714286 C7.35515537,48.5714286 6,47.2084452 6,45.5223408 L6,15.0490878 C6,13.3688583 7.34933925,12 9.01856496,12 L21.32077,12 L27.4276933,18.0923006 L47.399124,18.0923006 C48.2308288,18.0923006 48.9054985,18.7737923 48.9054985,19.6139071 L48.9054985,24.1846013 L18.2545595,24.1846013 L9.71861283,36.3377395 L15.1952817,36.3377395 L19.2840123,30.239564 Z" id="Path" fill-rule="nonzero"></path>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>
