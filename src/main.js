import Vue from 'vue'
import App from './App'
import router from './router'
import UmyUi from 'umy-ui'
import 'umy-ui/lib/theme-chalk/index.css'; // 引入样式
import TDesign from 'tdesign-vue'
// 引入组件库全局样式资源
import 'tdesign-vue/es/style/index.css'
import registerSvgIcon from './svgIcons'
registerSvgIcon()


Vue.use(UmyUi);
Vue.use(TDesign)

// ajax 相关
import axios from 'axios'
Vue.prototype.$axios = axios;
import qs from 'qs'
Vue.prototype.$qs = qs;
// //ajax 相关

// 加密
import CryptoJS from 'crypto-js'
Vue.prototype.$CryptoJS = CryptoJS;

import md5 from 'js-md5'
Vue.prototype.$md5 = md5;
// //加密


import zprobimvue from 'jingruizhang-probim-vue'
Vue.use(zprobimvue);


//全局过滤器
import vFilters from './filter/index'

//全局自定义指令
import directives from './directives/index'

// Element-UI
import ElementUI from 'element-ui';
import 'element-ui/lib/theme-chalk/index.css';
import './assets/css/elstyle.css'
import './assets/css/global.css'
import './assets/css/style.css'
// //Element-UI


//导入配置地址start
import PrpBIMApiUrl from './assets/js/apiAddress';
import issueBaseUrl from './assets/js/issueBaseUrl';
import MgrBaseUrl from './assets/js/MaterialsMgrUrl';
import investReportUrl from './assets/js/investReportUrl';

import formatData from './assets/js/formatData.js'
import api from './api'
//导入配置地址end

// 配置
Vue.prototype.$formatData = formatData
Vue.prototype.$api = api
Vue.prototype.$smallScreenWidth = 362.4 // 362.4
Vue.prototype.$configjson = window.bim_config;
Vue.prototype.$urlPool = PrpBIMApiUrl;
Vue.prototype.$issueBaseUrl = issueBaseUrl;
Vue.prototype.$MgrBaseUrl = MgrBaseUrl;
Vue.prototype.$IRUrl = investReportUrl;



Vue.prototype.$ip = baseURL => {
    if (process.env.NODE_ENV === 'production') {
        return window.bim_config[baseURL]
    } else {
        return require('../static/config/proxyIp')[baseURL]
    }
}

// 大屏按需引入
import { loading, fullScreenContainer } from '@jiaminghi/data-view'
Vue.use(loading).use(fullScreenContainer)
Vue.prototype.$staticmethod = window.tool;
// //配置

Vue.use(ElementUI);

import echarts from 'echarts'
import 'echarts-liquidfill'
Vue.prototype.$echarts = echarts
    //vue-echarts
import ECharts from 'vue-echarts'
// 手动引入 ECharts 各模块来减小打包体积
import 'echarts/lib/chart/bar' //柱状图
import 'echarts/lib/chart/line' //折线图
import 'echarts/lib/chart/pie' //饼状图
import 'echarts/lib/component/tooltip'
import 'echarts/lib/component/legend'
import 'echarts/lib/component/polar'
import 'echarts/lib/component/dataZoom'
import 'echarts/lib/component/markLine'
import 'echarts/lib/chart/radar'
import 'echarts/lib/component/legendScroll'
//import { debug } from 'webpack';
Vue.component('v-chart', ECharts);
Vue.prototype.$Echarts = ECharts


// 流程相关------
import store from './store'

import Moment from 'moment'
Vue.prototype.moment = Moment

import dayjs from 'dayjs'
import duration from 'dayjs/plugin/duration' // 按需加载插件
dayjs.extend(duration)
Vue.prototype.dayjs = dayjs


//设置全局广播事件
// Vue.prototype.$Bus = Vue.prototype.$Bus || new Vue();
import { EventBus } from "@static/event.js"
Vue.prototype.$Bus = EventBus

//请求工具函数
import { request } from "./utils/http.js"
Vue.prototype.$request = request

router.beforeEach((to, from, next) => {
    // axios.all parameters
    var axios_all_paras = [];
    var next_para = undefined;

    // 通过 to 对象， 调用方法，得到调用获取 BIMComposerId 接口的返回 axios.get
    // var _requrlres = Vue.prototype.$staticmethod.GetAxiosRes_GetBIMComposerId(to, Vue.prototype.$axios);
    // if (_requrlres) {
    //     axios_all_paras.push(_requrlres);
    //     Raxios_all_paras(axios_all_paras)
    // }

    // 依据 to 对象，对 axios_all_paras、next_para 进行修改
    //debugger;
    if (to.meta && to.meta.requiresAuth) {

        // 判断配置项中是否配置了登录过期时间，如果没有配置，则忽略，如果配置过期时间，则根据上一次跳转时的时间来判断是否已过期
        if (Vue.prototype.$configjson.login_expired_seconds &&
            Vue.prototype.$configjson.login_expired_seconds > 0) {

            // 拿到现在的时间对象、时间字符串
            var datenow = new Date();
            var datenow_str = Vue.prototype.$staticmethod.dtToString(datenow);

            // 1. 判断是否已过期
            var _LastActionTime = Vue.prototype.$staticmethod.Get("LastActionTime");
            if (!_LastActionTime) {
                _LastActionTime = datenow_str;
            }
            // 计算时间差毫秒数
            var expired_seconds = Vue.prototype.$configjson.login_expired_seconds;
            var datelastaction = new Date(_LastActionTime);
            var diffmsecs = datenow - datelastaction;
            if (diffmsecs > expired_seconds * 1000 &&
                !to.fullPath.includes('/Home/Boot') // 不是在boot页面
            ) {
                // 直接判定已过期
                Vue.prototype.$staticmethod.RemoveLocalStorage("LastActionTime");
                Vue.prototype.$message.warning('当前登录信息已过期，请重新登录');
                Vue.prototype.$staticmethod.Set("Token", '');
                next_para = { name: 'Login' }; //
                next(next_para);
                Vue.prototype.$staticmethod.jumpssomvc();
                return;
            }
            // 2. 如果没有过期，更新动作时间（界面上最后一次操作动作）
            Vue.prototype.$staticmethod.Set("LastActionTime", datenow_str);
        }

        if (to.params && to.params.Token) {
            // 调试信息
            // console.log('[route-auth][standard_usetokenkey]:' + window.bim_config.standard_usetokenkey);
            if (to.params.toGIS == 'longyun' || to.params.toGIS == 'SingleLogin') {
                // 广西龙云定制化免登陆、用他们的token请求后端接口、然后地址替换token跳转到对应项目首页
                let nologinToken = to.params.Token
                axios.post(`${window.bim_config.webserverurl}/api/User/Home/SingleLogin?Token=${nologinToken}`)
                    .then(res => {
                        window.location.href = `${window.bim_config.hasRouterFile}/#/Home/Boot/${res.data.Data.token}`
                        Vue.prototype.$staticmethod.Set("Token", res.data.Data.token);
                        window.location.reload()
                        return
                    })
                return
            }
            // 如果开启了使用 TokenKey，则尝试从 地址中拿出 TokenKey，并做验证（从接口）
            if (window.bim_config.standard_usetokenkey) {
                let axiosNum = 0
                    // axios_all_paras push get 请求返回的 Promise
                    // 然后在  axios.all 中处理
                    // tokenkey 从 localstorage 中以加密的形成存储
                var userId = Vue.prototype.$staticmethod.Get("UserId");
                var token = to.params.Token;
                var tokenkey = to.query.tokenkey;

                // 调试信息
                console.log('[userId]:' + userId, '[token]:' + token, '[tokenkey]:' + tokenkey);

                // 调用接口，判断这一对儿是否匹配
                // 发起请求，并将 promise 对象添加到数组中
                var promise = new Promise((resolve, reject) => {
                    axios.get(`${window.bim_config.webserverurl}/api/User/User/GetUserSession?Token=${token}&UserId=`)
                        .then(res => {
                            axiosNum = 0
                            resolve(res)
                        }).catch(() => {
                            axiosNum++
                            if (axiosNum < 2) {
                                EventBus.$emit('getUserSessionError')
                            }
                        })
                })
                promise.then(res => {
                    if (res.Ret == -1) {
                        _this.$staticmethod.Set("Token", "");
                        window.location.href = `${window.bim_config.hasRouterFile}/#/`;
                    }
                    if (res.data.Data && res.data.Data.apisession_userid) { userId = res.data.Data.apisession_userid.replace(/\+/g, '-'); }

                    if (res.data.Data && res.data.Data.apisession_token) { token = res.data.Data.apisession_token }

                    if (userId != '' && userId) {
                        Vue.prototype.$staticmethod.Set("UserId", userId);
                    }
                    if (token != '' && token) {
                        Vue.prototype.$staticmethod.Set("Token", token);
                    }
                    var testtoken_r = axios.get(`${window.bim_config.webserverurl}/api/User/User/TestTokenValid?TokenKey=${tokenkey}&UserId=${userId}&Token=${token}`);
                    axios_all_paras.push(testtoken_r);
                    Raxios_all_paras(axios_all_paras)
                })


            } else {
                // 调试信息
                // console.log('[route-auth2][token]:' + token);
                var token = to.params.Token;
                var testtoken_r = axios.get(`${window.bim_config.webserverurl}/api/User/User/TestTokenValid?TokenKey=${''}&UserId=${''}&Token=${token}`);
                axios_all_paras.push(testtoken_r);
                Raxios_all_paras(axios_all_paras)
            }
        } else {
            if (to.matched && to.matched.length > 0) {
                next_para = {
                    name: 'Login',
                    params: {
                        ReturnUrl: window.encodeURIComponent(to.fullPath)
                    }
                };
            } else {
                next_para = { name: 'Login' }; //
            }
            next(next_para);
            Vue.prototype.$staticmethod.jumpssomvc();
            return;
        }
    } else { Raxios_all_paras(axios_all_paras) }
    // //依据 to 对象，对 axios_all_paras、next_para 进行修改
    // 遍历 axios_all_paras、next_para 中的每个元素，对 next_para 进行修改（next_para可能在没有）
    function Raxios_all_paras(axios_all_paras) {
        axios.all(axios_all_paras).then(xall => {
            var i = 0;
            for (i = 0; i < xall.length; i++) {
                // 判断如果是 TestTokenValid 这个请求。
                if (xall[i].config.url.indexOf('/api/User/User/TestTokenValid') > 0) {
                    // 拿到 TestTokenValid 验证返回的结果
                    if (xall[i].data.Ret < 0) {
                        // 跳转到登录页，前提是没有 tokenKey 这个参数
                        // 但是这部分有个单点登录的问题
                        // 一个方案是，使用单点登录的环境，不配置 standard_usetokenkey
                        // 另外一个方案是，总的跳转地址添加个 tokenkey，强制让 TestTokenValid 合法
                        // 现阶段用第一个方案
                        Vue.prototype.$staticmethod.Set('Token', undefined);
                        Vue.prototype.$staticmethod.Set('RealName', '');
                        Vue.prototype.$staticmethod.Set('Account', ''); // 上传文件时，参数：CreateUserName。
                        Vue.prototype.$staticmethod.Set("UserId", ''); // Cache UserId
                        Vue.prototype.$staticmethod.Set("Email", '');
                        next_para = { name: 'Login' };
                        next(next_para);
                        return;

                    } else {
                        Vue.prototype.$staticmethod.Set('Token', to.params.Token);
                        Vue.prototype.$staticmethod.Set('RealName', xall[i].data.Data.UserName);
                        Vue.prototype.$staticmethod.Set('Account', xall[i].data.Data.Account); // 上传文件时，参数：CreateUserName。
                        Vue.prototype.$staticmethod.Set("UserId", xall[i].data.Data.ApisessionUserid); // Cache UserId
                        Vue.prototype.$staticmethod.Set("Email", xall[i].data.Data.Email); // Cache Email
                    }

                } else if (xall[i].config.url.indexOf('/api/User/Project/GetBIMComposerID') > 0) {
                    // 取 GetBIMComposerID 回调参数中的值，进行缓存，不涉及对 next_para 的写入
                    var x = xall[i];
                    Vue.prototype.$staticmethod._Set("organizeId", to.params.organizeId);
                    Vue.prototype.$staticmethod._Set("bimcomposerId", x.data.Data.BIMComposerID);
                    // //取 GetBIMComposerID 回调参数中的值，进行缓存，不涉及对 next_para 的写入
                } else if (xall[i].config.url.indexOf('/api/User/Home/OnlyTestToken') > 0) {
                    var x = xall[i];
                    if (x.data && x.data.Ret && x.data.Ret > 0) {
                        // Token 测试通过，将调用无参的 next()
                        Vue.prototype.$staticmethod.Set('Token', to.params.Token);
                        Vue.prototype.$staticmethod.Set('RealName', x.data.Data.RealName);
                        Vue.prototype.$staticmethod.Set('Account', x.data.Data.Account); // 上传文件时，参数：CreateUserName。
                        Vue.prototype.$staticmethod.Set("UserId", x.data.Data.apisession_userid); // Cache UserId
                        Vue.prototype.$staticmethod.Set("Email", x.data.Data.Email); // Cache Email
                        next_para = undefined;
                        // //Token 测试通过，将调用无参的 next()
                    } else {
                        if (to.matched && to.matched.length > 0 && to.matched[0].path != "/Admin/Valid/:Token?") {
                            // Token 测试未通过，将只跳转到 Login
                            next_para = { name: 'Login' }; //
                            next(next_para);
                            Vue.prototype.$staticmethod.jumpssomvc();
                            return;

                        } else if (to.matched && to.matched.length > 0 && to.matched[0].path == "/Admin/Valid/:Token?") {

                            // Token 测试未通过，但将要跳转到 Valid 页面，免 Valid 验证。
                            Vue.prototype.$staticmethod.Set('Token', to.params.Token);
                            next_para = undefined;

                        } else if (to.matched && to.matched.length > 0) {

                            // Token 测试未通过，将基于 to 的 match 情况，决定是否带回跳地址参数返回登录页

                            next_para = { name: 'Login' }; //

                            // 如果有回跳地址，设置带回跳地址的 next_para
                            var urlbeforeEncode = Vue.prototype.$staticmethod.getReturnUrl_If_has_ToMatched(to);
                            if (urlbeforeEncode) {
                                next_para = {
                                    name: 'Login',
                                    params: {
                                        ReturnUrl: urlbeforeEncode
                                    }
                                };
                            }
                            next(next_para);
                            Vue.prototype.$staticmethod.jumpssomvc();
                            return;
                        }
                    }
                }
            }
            // //遍历 axios_all_paras、next_para 中的每个元素，对 next_para 进行修改（next_para可能在没有）
            // 此处调用 next() 确保 localS1torage 的写入完成
            if (next_para) {
                next(next_para);
            } else {
                next();
            }
            // //此处调用 next() 确保 localS1torage 的写入完成

        }).catch(x => {
            console.warn('axios.all err!');
        });
    }
});

Vue.config.productionTip = false

//注册全局过滤器
for (let key in vFilters) {
    Vue.filter(key, vFilters[key])
}

/* eslint-disable no-new */
new Vue({
    el: '#app',
    router,
    store,
    directives,
    components: { App },
    template: '<App/>'
})