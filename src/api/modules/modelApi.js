import { request } from '@/utils/http'
export default {
    shareCreate: params => request({ url: `/api/v1/model/share/create`, method: 'post', params, }),

    deleteCovert: params => request({ url: `/api/v1/model/delete-covert`, method: 'post', params, }),

    postcancelbymodelid: id => request({ url: `/api/v1/model/share/cancelbymodelid?modelId=${id}`, method: 'post', }), // 删除模型时候顺便删除模型分享

    DeleteMaterialsConnModel: params => request({ url: `/api/Material/Mtr/DeleteMaterialsConnModel`, method: 'post', params, }),


}