import { request } from '@/utils/http'
export default {
    AddProjectMonthImage: params => request({ url: '/api/Tz/Project/AddProjectMonthImage', method: 'post', params }),
    DeleteProjectMonthImage: params => request({ url: '/api/Tz/Project/DeleteProjectMonthImage', method: 'post', params }),
    GetCompanyList: params => request({ url: '/api/Tz/Project/GetCompanyList', method: 'get', params }),
    GetDPProjectCurrentYearStatisticsData: params => request({ url: '/api/Tz/Project/GetDPProjectCurrentYearStatisticsData', method: 'get', params }),
    GetDPProjectOnBuildTotalTz: params => request({ url: '/api/Tz/Project/GetDPProjectOnBuildTotalTz', method: 'get', params }),
    GetDPProjectOnBuildYearStatisticsList: params => request({ url: '/api/Tz/Project/GetDPProjectOnBuildYearStatisticsList', method: 'get', params }),
    GetDPProjectOnBuildYearTotalStatistics: params => request({ url: '/api/Tz/Project/GetDPProjectOnBuildYearTotalStatistics', method: 'get', params }),
    GetDPProjectTotalStatisticsData: params => request({ url: '/api/Tz/Project/GetDPProjectTotalStatisticsData', method: 'get', params }),
    GetProjectBaseInfo: params => request({ url: '/api/Tz/Project/GetProjectBaseInfo', method: 'get', params }),
    GetProjectDetail: params => request({ url: '/api/Tz/Project/GetProjectDetail', method: 'get', params }),
    GetProjectMonthTzPlan: params => request({ url: '/api/Tz/Project/GetProjectMonthTzPlan', method: 'get', params }),
    GetProjectStatisticsDetail: params => request({ url: '/api/Tz/Project/GetProjectStatisticsDetail', method: 'get', params }),
    GetProjectStatisticsList: params => request({ url: '/api/Tz/Project/GetProjectStatisticsList', method: 'get', params }),
    UpdateProjectBaseInfo: params => request({ url: '/api/Tz/Project/UpdateProjectBaseInfo', method: 'post', params }),
    UpdateProjectDetail: params => request({ url: '/api/Tz/Project/UpdateProjectDetail', method: 'post', params }),
    UpdateProjectMonthContract: params => request({ url: '/api/Tz/Project/UpdateProjectMonthContract', method: 'post', params }),
    UpdateProjectMonthImage: params => request({ url: '/api/Tz/Project/UpdateProjectMonthImage', method: 'post', params })
}
