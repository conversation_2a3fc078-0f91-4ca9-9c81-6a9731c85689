import { request } from '@/utils/http'
export default {
    // 获取项目主页基本信息
    GetProject: params => request({ url: '/api/User/Project/GetProject', params }),

    postModifyProject: params => request({ url: '/api/User/Project/ModifyProject', method: 'post', params, }),

    // 获取项目公告列表
    getnoticeList: params => request({ url: '/api/project/notice/list', params }),
    // 创建项目公告列表
    postNoticeCreate: params => request({ url: '/api/project/notice/create', method: 'post', params, }),
    // 删除项目公告
    postNoticeDelete: (id) => request({ url: `/api/project/notice/delete?id=${id}`, method: 'post', }),
    // 编辑项目公告
    postNoticeUpdate: params => request({ url: '/api/project/notice/update', method: 'post', params }),
    // 获取某个项目公告详情
    getnoticeInfo: params => request({ url: '/api/project/notice/info', params }),

    // 获取参建单位
    getBaseCategoryList: params => request({ url: '/api/Global/Gcfg/GetBaseCategoryList', params }),
    // 获取参建单位列表
    getParticipatingList: params => request({ url: '/api/User/Home/GetParticipatingList', params }),
    // 创建参建单位
    postCreateParticipatingUnit: params => request({ url: '/api/User/Home/CreateParticipatingUnit', method: 'post', params, }),
    // 删除参建单位
    postDelParticipatingUnit: (id) => request({ url: `/api/User/Home/DelParticipatingUnit?id=${id}`, method: 'post', }),
    // 编辑参建单位
    postModifyParticipatingUnit: params => request({ url: '/api/User/Home/ModifyParticipatingUnit', method: 'post', params }),

    // 投资模块
    getHomeinvestment: params => request({ url: '/api/report/investment/home-investment', params }),
    // 进度
    getGetMilestones: params => request({ url: '/api/Plus/PlusTask/GetMilestones', params }),
    getquanlitylist: params => request({ url: '/api/report/quanlity/valuation-statistics', params }),
    // getquanlitylist: params => request({ url: '/api/report/quanlity/list', params }),

    // 质量
    getGetMissions: params => request({ url: '/api/Examine/Exam/GetMissions', params }),





    // postaaa: (params, userId) => request({ url: `/api/v1/scene/update?userId=${userId}`, method: 'post', params, }),

}