import { request } from '@/utils/http'
export default {

    // document.vue用到的请求==开始
    getfilehistory: params => request({ url: '/api/v1/file/history', params }), // 获取历史版本

    getfoldertree: params => request({ url: '/api/v1/folder/tree', params }), // 查询当前用户树结构

    getfilebyfileid: params => request({ url: '/api/v1/file/byfileid', params }), // 根据文件主键获取上传文件信息

    getfilebyfiletype: params => request({ url: '/api/v1/file/byfiletype', params }), // 获取允许转换饿类型

    getfilebyfilemodelid: params => request({ url: '/api/v1/file/byfilemodelid', params }), // 根据文件主键获取上传文件信息

    getfoldersubscribe: params => request({ url: '/api/v1/folder/subscribe', params }), // ==获取我的订阅列表

    getsharedetail: params => request({ url: '/api/v1/share/detail', params }), // 获取分享详情

    getsharelist: params => request({ url: '/api/v1/share/list', params }), // 获取分享列表

    getfoldercollection: params => request({ url: '/api/v1/folder/collection', params }), // 回收站信息

    getfilefolder: params => request({ url: '/api/v1/folder/file-folder', params }), // ==查询当前文件夹下的文件或文件夹

    getSummaryfilefolder: params => request({ url: '/api/v1/folder/summary-file-folder', params }), // ==查询当前文件夹下的文件或文件夹

    getfilesearch: params => request({ url: '/api/v1/file/search', params }), // 搜索文件

    postmodelcancel: (urlparams, postparams) => request({ url: `/api/v1/file/model/cancel?${urlparams}`, method: 'post', postparams, }), // 取消模型绑定

    postmodelbind: postparams => request({ url: `/api/v1/file/model/bind`, method: 'post', postparams, }), // 绑定模型

    postfilerestore: (id, postparams) => request({ url: `/api/v1/file/restore?userId=${id}`, method: 'post', postparams, }), // 还原文件或文件夹

    postfolderupdate: postparams => request({ url: `/api/v1/folder/update`, method: 'post', postparams, }), // 编辑文件或者文件夹

    postfoldersubscribe: params => request({ url: `/api/v1/folder/subscribe`, method: 'post', params, }), // 用户订阅、取消订阅

    postfoldermove: params => request({ url: `/api/v1/folder/move`, method: 'post', params, }), // 移动文件或者文件夹

    postdetaildelete: params => request({ url: `/api/v1/share/detail/delete?${params}`, method: 'post', }), // 删除分享详情 

    postfileclear: (id, postparams) => request({ url: `/api/v1/file/clear?userId=${id}`, method: 'post', postparams, }), // 彻底删除文件或者文件夹

    postfolderdelete: params => request({ url: `/api/v1/folder/delete`, method: 'post', params, }), // 删除文件夹

    postfiledelete: params => request({ url: `/api/v1/file/delete`, method: 'post', params, }), // 删除文件

    postsharecreate: params => request({ url: `/api/v1/share/create`, method: 'post', params, }), // 创建分享

    postdeletebatc: (id, postparams) => request({ url: `/api/v1/file/delete-batc?userId=${id}`, method: 'post', postparams, }), // 批量删除文件或文件夹

    postsharedelete: (urlparams) => request({ url: `/api/v1/share/delete?${urlparams}`, method: 'post', }), // 删除分享

    getFolderAuth: params => request({ url: '/api/v1/folder/folder-auth', params }), // 获取文件夹权限

    // document.vue用到的请求==结束



}