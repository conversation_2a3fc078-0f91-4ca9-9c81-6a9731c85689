import { request } from '@/utils/http'
export default {
    // 场景相关
    getscenepaged: params => request({ url: '/api/v1/scene/paged', params }),

    getSceneList: params => request({ url: '/api/v1/scene/QueryTreeScene', params }),

  postsceneadd: (id, params) => request({ url: `/api/v1/scene/add?userId=${id}`, method: 'post', params }),

    postsceneupdate: (id, params) => request({ url: `/api/v1/scene/update?userId=${id}`, method: 'post', params }),

    getscenedetail: params => request({ url: '/api/v1/scene/detail', params }),

    postscenedelete: (id, params) => request({ url: `/api/v1/scene/delete?userId=${id}`, method: 'post', params }),

    postsceneJsonDataupdate: (id, params) => request({ url: `/api/v1/scene/JsonData/update?userId=${id}`, method: 'post', params }),

    getscenesharedetail: params => request({ url: '/api/v1/scene/share/detail', params }),

    postsceneCreateShare: (id, params) => request({ url: `/api/v1/scene/CreateShare?userId=${id}`, method: 'post', params }),

    postsceneCancelShare: (id, params) => request({ url: `/api/v1/scene/CancelShare?userId=${id}`, method: 'post', params }),

    getscenesharedata: (id, params, psd) => request({ url: `/api/v1/scene/share/data?userId=${id}`, params, headers: psd ? { VisitPassword: psd } : null, }),

    // postaaa: (params, userId) => request({ url: `/api/v1/scene/update?userId=${userId}`, method: 'post', params, }),


    // 进度模拟设置相关
    getProgressScence: params => request({ url: '/api/v1/scene/GetProgressScence', params }),
    postAddProgressScenceAsync: params => request({ url: `/api/v1/scene/AddProgressScenceAsync`, method: 'post', params, }),

    // 进度填报模拟时候获取的数据
    getProgressByPlanIdOfDate: params => request({ url: `/api/Schedual/Schedual/GetProgressByPlanIdOfDate`, params }),
    getProgressFillingSettings: params => request({ url: `/api/v1/scene/GetProgressFillingSettings`, params }), // 进度填报获取设置模拟信息
    postAddProgressFillingSettingsAsync: params => request({ url: `/api/v1/scene/AddProgressFillingSettingsAsync`, method: 'post', params, }), // 进度填报设置模拟信息



}
