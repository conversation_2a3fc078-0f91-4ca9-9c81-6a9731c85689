import { request } from '@/utils/http'
export default {
    // postLogin: params => request({ url: '/api/User/Home/Login', method: 'post', params }),

    GetProjectPaged: params => request({ url: '/api/User/Project/Paged', params }),
    postSetProjectSort: params => request({ url: '/api/User/Project/SetProjectSort', method: 'post', params }), // 编辑项目上下移动'
    postSystemModifyProject: params => request({ url: '/api/User/Project/SystemModifyProject', method: 'post', params }), // 编辑项目上下移动'

    GetUserLogPaged: params => request({ url: '/api/User/User/GetUserLogPaged', params }), // 项目日志

    postCreateBaseCategory: params => request({ url: '/api/Global/Gcfg/CreateBaseCategory', method: 'post', params }), // 新建单位类型'
    postUpdateBaseCategory: params => request({ url: '/api/Global/Gcfg/UpdateBaseCategory', method: 'post', params }), // 新建单位类型'
    postDeleteBaseCategory: id => request({ url: `/api/Global/Gcfg/DeleteBaseCategory?id=${id}`, method: 'post', }), // 删除单位类型'
    GetBaseCategory: params => request({ url: '/api/Global/Gcfg/GetBaseCategory', params }), // 获取单位类型详情
    GetBaseCategoryList: params => request({ url: '/api/Global/Gcfg/GetBaseCategoryList', params }), // 获取单位类型列表

    postArchivesRoomCreate: params => request({ url: '/api/Archives/ArchivesRoom/Create', method: 'post', params }), // 新建档案室
    postArchivesRoomDelete: id => request({ url: `/api/Archives/ArchivesRoom/Delete?id=${id}`, method: 'post', }), // 删除档案室
    postArchivesRoomModify: params => request({ url: '/api/Archives/ArchivesRoom/Modify', method: 'post', params }), // 编辑档案室
    GetArchivesRoomList: params => request({ url: '/api/Archives/ArchivesRoom/List', params }), // 获取档案室列表
    GetArchivesRoomTree: params => request({ url: '/api/Archives/ArchivesRoom/Tree', params }), // 获取档案室列表
    GetArchivesRoomInfo: params => request({ url: '/api/Archives/ArchivesRoom/Info', params }), // 获取档案室详情


    // 分享管理中调用
    getsharepaged: params => request({ url: '/api/v1/model/share/paged', params }), // 获取分享列表
    postsharemodify: params => request({ url: '/api/v1/model/share/modify', method: 'post', params }), // 编辑分享
    postsharecancel: id => request({ url: `/api/v1/model/share/cancel?id=${id}`, method: 'post' }), // 取消分享
    postsharecancelbatch: params => request({ url: `/api/v1/model/share/cancel-batch`, method: 'post', params }), // 批量取消分享


    // 现场巡查
    postCreateExamineDataType: params => request({ url: '/api/Examine/Exam/CreateExamineDataType', method: 'post', params }), // 增加问题类型
    postDeleteExamineDataType: id => request({ url: `/api/Examine/Exam/DeleteExamineDataType/${id}`, method: 'post' }), // 删除问题类型
    postModifyExamineDataType: params => request({ url: '/api/Examine/Exam/ModifyExamineDataType', method: 'post', params }), // 增加问题类型
    GetExamineDataType: params => request({ url: '/api/Examine/Exam/GetExamineDataType', params }), // 获取单个

    // 质量划分

    GetDivisionDownloadTemplate: params => request({ url: '/api/Material/Division/DownloadTemplate', params }), //  下载导入模板 
    GetDivisionExport: params => request({ url: '/api/Material/Division/Export', params }), //  导出
    GetDivisionInfo: params => request({ url: '/api/Material/Division/Info', params }), //  单个数据
    GetDivisionPaged: params => request({ url: '/api/Material/Division/Paged', params }), //  表格
    GetDivisionTree: params => request({ url: '/api/Material/Division/Tree', params }), // 树
    postDivisionCreate: params => request({ url: '/api/Material/Division/Create', method: 'post', params }), //  创建质量划分 
    postDivisionDelete: params => request({ url: '/api/Material/Division/Delete', method: 'post', params }), //  批量删除 
    postDivisionImport: params => request({ url: '/api/Material/Division/Import', method: 'post', params }), //  导入 
    postDivisionModify: params => request({ url: '/api/Material/Division/Modify', method: 'post', params }), //  编辑
    postDivisionModifyImportance: params => request({ url: '/api/Material/Division/ModifyImportance', method: 'post', params }), //  编辑重要性
    postDivisionEvaluation: params => request({ url: '/api/Material/Division/Evaluation', method: 'post', params }),
    postDivisionAudit: params => request({ url: '/api/Material/Division/Audit', method: 'post', params }),

    // 项目档案
    GetCategoryTree: params => request({ url: '/api/Archives/Category/Tree', params }), // 获取档案分类树
    GetCategoryInfo: params => request({ url: '/api/Archives/Category/Info', params }), // 获取单个档案分类信息
    GetCategoryDownloadTemplate: params => request({ url: '/api/Archives/Category/DownloadTemplate', params }), // 下载导入模板
    GetCategoryChildren: params => request({ url: '/api/Archives/Category/Children', params }), // 获取子级档案分类列表
    postCategoryCreate: params => request({ url: '/api/Archives/Category/Create', method: 'post', params }), // 创建档案分类
    postCategoryDelete: id => request({ url: `/api/Archives/Category/Delete?id=${id}`, method: 'post' }), //  删除档案分类
    postCategoryImport: params => request({ url: '/api/Archives/Category/Import', method: 'post', params }), // 导入档案分类
    postCategoryModify: params => request({ url: '/api/Archives/Category/Modify', method: 'post', params }), // 编辑档案分类

    GetArchivesPaged: params => request({ url: '/api/Archives/Archives/Paged', params }), //  表格
    GetArchivesInfo: params => request({ url: '/api/Archives/Archives/Info', params }), // 详情
    postArchivesCreate: params => request({ url: '/api/Archives/Archives/Create', method: 'post', params }), //  创建
    postArchivesAudit: params => request({ url: '/api/Archives/Archives/Audit', method: 'post', params }), //  审批
    postArchivesDeleteBulk: params => request({ url: '/api/Archives/Archives/DeleteBulk', method: 'post', params }), //  批量删除 
    GetCurrentArchivesCode: params => request({ url: '/api/Archives/Archives/GetCurrentArchivesCode', params }), // 获取档案编码  
    GetHistorySelector: params => request({ url: '/api/Archives/Archives/GetHistorySelector', params }), // 获取档案编码  

    GetOrganizeUserPaged: params => request({ url: '/api/User/User/GetOrganizeUserPaged', params }), // 获取档案分类树

    postDeleteBulk: params => request({ url: '/api/v1/attach/delete-bulk', method: 'post', params }), //  批量删除附件



}