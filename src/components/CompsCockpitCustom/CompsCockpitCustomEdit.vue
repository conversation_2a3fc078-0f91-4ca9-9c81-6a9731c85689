<template>
    <div class="ERCustomEdit">
            <!-- 
              :init_id="'zdialog_id_addingformrename'"
              v-if="status_showingaddingrename"
             -->
        <zdialog-function
            :init_title="'自定义配置'"
            :init_zindex="1001"
            :init_innerWidth="950"
            :init_width="950"
            :debugmode="false"
            init_closebtniconfontclass="icon-suggested-close"
            @onclose="$emit('CloseERCustomEdit')"
          >
            <div slot="mainslot" 
            class="_css-line css-common-line"
            v-if="CockpitCustomEditStatus === 0">
              <div class="ERCustomConfig_container">
                  <ul class="container_left">
                      <li>投资成本:</li>
                      <li  v-for="(item,index) in ERCustomEditItem" :key="index">
                        <el-input 
                          v-model="ERCustomEditItem[index].ER_cont"
                          placeholder="请输入内容"
                          @mousedown.stop.native
                          class="capex_input"></el-input>
                        <i v-if="index>3" class="el-icon-remove" @click="ERCustomEditItemRemove(index,'ERCustomEditItem')"></i>
                      </li>
                  </ul>
                  <div class="css-hover-btn container_right" @click="ERCustomEditItemAdd('ERCustomEditItem')">
                      添加
                  </div>
              </div>
            </div>
            
            <div slot="mainslot" 
            class="_css-line css-common-line"
            v-else-if="CockpitCustomEditStatus === 1">
              <div class="ERCustomConfig_container">
                <el-container>
                  <ul class="ERDcontainer_left">
                      <span>进场情况:</span>
                      <li  v-for="(item,index) in ERD_Mobilization" :key="index">
                        <el-input 
                          v-model="ERD_Mobilization[index].ER_cont" 
                          @mousedown.stop.native 
                          placeholder="请输入内容" 
                          class="capex_input"></el-input>
                        <i class="el-icon-remove" @click="ERCustomEditItemRemove(index,'ERD_Mobilization')"></i>
                      </li>
                  </ul>
                  <div class="css-hover-btn container_right" @click="ERCustomEditItemAdd('ERD_Mobilization')">
                      添加
                  </div>
                </el-container>
                <el-container>
                  <ul class="ERDcontainer_left">
                      <span>安全文明:</span>
                      <li  v-for="(item,index) in ERD_Safety" :key="index">
                        <el-input 
                          v-model="ERD_Safety[index].ER_cont" placeholder="请输入内容" 
                          @mousedown.stop.native 
                          class="capex_input"></el-input>
                        <i class="el-icon-remove" @click="ERCustomEditItemRemove(index,'ERD_Safety')"></i>
                      </li>
                  </ul>
                  <div class="css-hover-btn container_right" @click="ERCustomEditItemAdd('ERD_Safety')">
                      添加
                  </div>
                </el-container>
                <el-container>
                  <ul class="ERDcontainer_left">
                      <span>现场情况:</span>
                      <li  v-for="(item,index) in ERD_Site" :key="index">
                        <el-input 
                          v-model="ERD_Site[index].ER_cont" placeholder="请输入内容"
                          @mousedown.stop.native
                          class="capex_input"></el-input>
                        <i class="el-icon-remove" @click="ERCustomEditItemRemove(index,'ERD_Site')"></i>
                      </li>
                  </ul>
                  <div class="css-hover-btn container_right" @click="ERCustomEditItemAdd('ERD_Site')">
                      添加
                  </div>
                </el-container>
              </div>
            </div>
            <div slot="buttonslot" class="_css-formEditnameBtnCtn" >
              <zbutton-function
                  :init_text="'保存'"
                  :init_fontsize="14"
                  :debugmode="true"
                  :init_height="'40px'"
                  :init_width="'120px'"
                  @onclick="SaveCustomEdit(CockpitCustomEditStatus)"
                  >
              </zbutton-function>
              <zbutton-function
                  :init_text="'取消'"
                  :init_color="'rgba(24, 144, 255)'"
                  :init_bgcolor="'#fff'"
                  :init_fontsize="14"
                  :debugmode="true"
                  :init_height="'40px'"
                  :init_width="'120px'"
                  @onclick="cancelCustomEdit"
                  >
              </zbutton-function>
            </div>
        </zdialog-function>
    </div>
</template>
<script>
export default {
    name:'CompsCockpitCustomEdit',
    props:{
        CockpitCustomEditStatus:{
            type:Number,
            required:true,
        },//0，1分别为总报、日报
        ChooseER_date:{
            required:true,
        },
        ER_data:{
            required:true,
        },
        organizeId:{
            required:true,
        }
    },
    data(){
      return {
        NewER_data:undefined,
        ERCustomEditItem:[
          {
            ER_cont:"工程投资总金额",
          },
          {
            ER_cont:"累计完成投资",
          },
          {
            ER_cont:"年度投资计划",
          },
          {
            ER_cont:"年度投资完成",
          },
          {
            ER_cont:"总合同额",
          },
          {
            ER_cont:"已执行合同额",
          },
        ],
        ERD_Mobilization:[//进场情况
          {
            ER_cont:"施工人员计划（人次）",
          },
          {
            ER_cont:"施工人员实际（人次）",
          },
          {
            ER_cont:"机械台班计划（台）",
          },
          {
            ER_cont:"机械台班实际（台）",
          },
          {
            ER_cont:"材料进场计划（批次）",
          },
          {
            ER_cont:"材料进场实际（批次）",
          },
        ],
        ERD_Safety:[//安全文明
          {
            ER_cont:"通风消杀计划（人次）",
          },
          {
            ER_cont:"通风消杀实际（人次）",
          },
          {
            ER_cont:"今日体温检测（人数）",
          },
          {
            ER_cont:"体温检测合格（人数）",
          },
          {
            ER_cont:"降尘喷洒（次数）",
          },
          {
            ER_cont:"车辆清洗（次数）",
          },
        ],
        ERD_Site:[//现场情况
          {
            ER_cont:"现场负责人员",
          },
          {
            ER_cont:"现场值班人员",
          },
        ],
      }
    },
    methods:{
      SaveCustomEdit(num){//保存编辑
        let _this = this
          if(num === 1){
            let DailyJSON = JSON.stringify({
              ERD_Mobilization:_this.ERD_Mobilization,
              ERD_Safety:_this.ERD_Safety,
              ERD_Site:_this.ERD_Site
            })
            if(DailyJSON === JSON.stringify(_this.NewER_data)){
              this.$message.warning('当前数据未修改，无需保存')
              return
            }
            // let ERD_MobilizationFill=_this.ERD_Mobilization.every(item => item.ER_cont!== '')
            // let ERD_SafetyFill=_this.ERD_Safety.every(item => item.ER_cont!== '')
            // let ERD_SiteFill=_this.ERD_Site.every(item => item.ER_cont!== '')
            // if(ERD_MobilizationFill === true&&ERD_SafetyFill === true&&ERD_SiteFill === true){
            //   _this.$confirm("保存新数据会取代原有的编辑页面数据，是否确认？", "操作确认", {
            //     confirmButtonText: "确定",
            //     cancelButtonText: "取消",
            //     type: "warning"
            //   }).then(() => {
            //     _this.$axios({
            //       method: "post",
            //       url: `${window.bim_config.webserverurl}/api/VisualView/VisualView/InsetDailyJSON`,
            //       data:_this.$qs.stringify({
            //         DailyJSON,
            //         Daily_Time: _this.ChooseER_date,
            //         organizeId:_this.organizeId
            //       })
            //     })
            //     .then(() => {
            //         this.$emit('CloseERCustomEdit')
            //         this.$emit('RefreshData')
            //     }).catch(() => {});
            //   }).catch(() => {});
            // }else{
            //   _this.$message.warning('请填写完整数据')
            // }
            _this.$confirm("保存新数据会取代原有的编辑页面数据，是否确认？", "操作确认", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning"
              }).then(() => {
                _this.$axios({
                  method: "post",
                  url: `${window.bim_config.webserverurl}/api/VisualView/VisualView/InsetDailyJSON`,
                  data:_this.$qs.stringify({
                    DailyJSON,
                    Daily_Time: _this.ChooseER_date,
                    organizeId:_this.organizeId
                  })
                })
                .then(() => {
                    this.$emit('CloseERCustomEdit')
                    this.$emit('RefreshData')
                }).catch(() => {});
              }).catch(() => {});
          }else if(num === 0){
            let SumJSON = JSON.stringify({
              ER_custom_item:_this.ERCustomEditItem,
            })
            if(SumJSON === JSON.stringify(_this.ER_data)){
              this.$message.warning('当前数据未修改，无需保存')
              return
            }
            // let ERCustomEditItemFill=_this.ERCustomEditItem.every(item => item.ER_cont!== '')
            // if(ERCustomEditItemFill === true){
            //   _this.$confirm("保存新数据会取代原有的编辑页面数据，是否确认？", "操作确认", {
            //     confirmButtonText: "确定",
            //     cancelButtonText: "取消",
            //     type: "warning"
            //   }).then(() => {
            //     _this.$axios({
            //       method: "post",
            //       url: `${window.bim_config.webserverurl}/api/VisualView/VisualView/InsetSumJSON`,
            //       data:_this.$qs.stringify({
            //         SumJSON,
            //         organizeId:_this.organizeId
            //       })
            //     })
            //     .then(() => {
            //         this.$emit('CloseERCustomEdit')
            //         this.$emit('RefreshData')
            //     }).catch(() => {});
            //   }).catch(() => {});
            // }else{
            //   _this.$message.warning('请填写完整数据')
            // }
              _this.$confirm("保存新数据会取代原有的编辑页面数据，是否确认？", "操作确认", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning"
              }).then(() => {
                _this.$axios({
                  method: "post",
                  url: `${window.bim_config.webserverurl}/api/VisualView/VisualView/InsetSumJSON`,
                  data:_this.$qs.stringify({
                    SumJSON,
                    organizeId:_this.organizeId
                  })
                })
                .then(() => {
                    this.$emit('CloseERCustomEdit')
                    this.$emit('RefreshData')
                }).catch(() => {});
              }).catch(() => {});
          }
        
      },
      cancelCustomEdit(){//取消编辑
        this.$emit('CloseERCustomEdit')
      },
      ERCustomEditItemAdd(c){//工程总报自定义配置添加
        if(c === "ERCustomEditItem"){
          this.ERCustomEditItem.push({ER_cont:""})
        }else if(c === "ERD_Mobilization"){
          this.ERD_Mobilization.push({ER_cont:""})
        }else if(c === "ERD_Safety"){
          this.ERD_Safety.push({ER_cont:""})
        }else if(c === "ERD_Site"){
          this.ERD_Site.push({ER_cont:""})
        }
      },
      ERCustomEditItemRemove(i,c){//工程总报删除自定义配置项
        if(c === "ERCustomEditItem"){
          this.ERCustomEditItem.splice(i,1)
        }else if(c === "ERD_Mobilization"){
          this.ERD_Mobilization.splice(i,1)
        }else if(c === "ERD_Safety"){
          this.ERD_Safety.splice(i,1)
        }else if(c === "ERD_Site"){
          this.ERD_Site.splice(i,1)
        }
      },
      GetCoustomCont(){
        if(this.CockpitCustomEditStatus === 1){
          if(!this.ER_data){
            this.$axios({
              method: "post",
              url: `${window.bim_config.webserverurl}/api/VisualView/VisualView/GetNewDaily`,
              data:this.$qs.stringify({
                organizeId:this.organizeId
              })
            }).then(res => {
              let data = res.data.Data
              if(Object.keys(data).length!=0){
                data.ERD_Mobilization.forEach(e => {
                  e.ER_custom_input = ''
                  e.Daily_Id = 0
                });
                data.ERD_Safety.forEach(e => {
                  e.ER_custom_input = ''
                  e.Daily_Id = 0
                });
                data.ERD_Site.forEach(e => {
                  e.ER_custom_input = ''
                  e.Daily_Id = 0
                });
                this.NewER_data = {
                  ERD_Mobilization:data.ERD_Mobilization,
                  ERD_Safety:data.ERD_Safety,
                  ERD_Site:data.ERD_Site
                }
                this.ERD_Mobilization = this.coppyArray(data.ERD_Mobilization)
                this.ERD_Safety = this.coppyArray(data.ERD_Safety)
                this.ERD_Site = this.coppyArray(data.ERD_Site)
              }else{
                return
              }
            }).catch(() => {
            });
          }else{
            this.NewER_data = this.ER_data
            this.ERD_Mobilization = this.coppyArray(this.NewER_data.ERD_Mobilization)
            this.ERD_Safety = this.coppyArray(this.NewER_data.ERD_Safety)
            this.ERD_Site = this.coppyArray(this.NewER_data.ERD_Site)
          }
        }else if(this.CockpitCustomEditStatus === 0){
          if(!this.ER_data) return
          this.ERCustomEditItem = this.coppyArray(this.ER_data.ER_custom_item)
        }
      },
      coppyArray(arr){
        return arr.map((e)=>{
          if(typeof e==='object'){
              return Object.assign({},e);
          }else{
              return e;
          }
        }) 
      }
    },
    created(){
      this.GetCoustomCont()
    }
}
</script>
<style scoped>
.ERCustomConfig_container {
    flex-direction: column;
    height: 550px;
    overflow: auto;
  }
._css-formEditnameBtnCtn {
    display: flex;
    flex-direction: row-reverse;
    height: 64px;
    align-items: center;
    box-sizing: border-box;
    padding-right: 8px;
}
.ERCustomConfig_container .container_left{
  float: left;
  padding:20px 0 0 30px;
  width: 750px;
}
.ERCustomConfig_container .container_right{
  background: linear-gradient(224deg,rgba(0,145,255,1) 0%,rgba(0,122,255,1) 100%);
  border-radius: 4px;
  color: #FFFFFF;
  font-size: 14px;
  padding: 9px 16px;
  cursor: pointer;
  float: right;
  height: 21px;
  margin-right: 30px;
}
.ERCustomConfig_container .container_left li{
  display: flex;
  align-items: center;
  padding: 5px 0;
  font-size: 18px;
}
.ERCustomConfig_container .ERDcontainer_left{
  float: left;
  padding:0px 0 0 30px;
  width: 750px;
}
.ERCustomConfig_container .ERDcontainer_left span{
  display: flex;
  align-items: center;
  padding: 15px 0;
  font-size: 18px;
}
.ERCustomConfig_container .ERDcontainer_left li{
  float: left;
  padding: 5px 0;
  font-size: 18px;
  margin-right: 65px;
}
.capex_input{
  border-radius: 4px;
  border: 1px solid #dcdfe6;
  background-color: #fff;
  cursor: pointer;
  width: 200px;
}
.el-icon-remove{
  color: #000;
  cursor: pointer;
  margin: 0 20px;
  line-height: 1;
}
</style>