<template>
    <div class="ERCustomEdit">
        <zdialog-function
            :init_title = ERW_coustom_title
            :init_zindex="1001"
            :init_innerWidth="950"
            :init_width="950"
            :debugmode="false"
            init_closebtniconfontclass="icon-suggested-close"
            @onclose="$emit('CloseERCustomEdit')"
          >
            <div slot="mainslot" 
            class="_css-line css-common-line"
            v-if="CockpitCustomEditStatus === 21">
              <div class="ERCustomConfig_container">
                  <el-container v-for="(item,index) in ERW_progress" :key="index">
                      <ul class="ERDcontainer_left" >
                        <div class="head">
                          <el-input 
                          v-model="item.ERW_progressHeader" 
                          placeholder="请输入内容" 
                          @mousedown.stop.native
                          class="head"></el-input>
                          <i class="el-icon-remove" v-if="index!=0" @click="ERCustomEditItemRemoveAll(index,CockpitCustomEditStatus)"></i>
                        </div>
                        <li v-for="(i,d) in item.ERW_progressSingle" :key="d">  
                          <el-input 
                          v-model="item.ERW_progressSingle[d].ER_cont" 
                          placeholder="请输入内容" 
                          @mousedown.stop.native
                          class="capex_input"></el-input>
                          <i class="el-icon-remove" @click="ERCustomEditItemRemove(index,d,CockpitCustomEditStatus)"></i>
                        </li>
                      </ul>
                      <div class="css-hover-btn container_right" @click="ERCustomEditItemAdd(index,CockpitCustomEditStatus)">
                          添加
                      </div>
                    </el-container>
              </div>
            </div>
            <div slot="mainslot" 
            class="_css-line css-common-line"
            v-else-if="CockpitCustomEditStatus === 22">
              <div class="ERCustomConfig_container">
                  <ul class="container_left">
                      <li  v-for="(item,index) in ERW_quality" :key="index">
                        <el-input 
                        v-model="ERW_quality[index].ER_cont" 
                        placeholder="请输入内容" 
                        @mousedown.stop.native
                        class="capex_input"></el-input>
                        <i v-if="index>4" class="el-icon-remove" @click="ERCustomEditItemRemove('',index,CockpitCustomEditStatus)"></i>
                      </li>
                  </ul>
                  <div class="css-hover-btn container_right" @click="ERCustomEditItemAdd('',CockpitCustomEditStatus)">
                      添加
                  </div>
              </div>
            </div>
            <div slot="mainslot" 
            class="_css-line css-common-line"
            v-else-if="CockpitCustomEditStatus === 23">
              <div class="ERCustomConfig_container">
                  <el-container v-for="(item,index) in ERW_Safty" :key="index">
                      <ul class="ERDcontainer_left" >
                        <div class="head">
                          <el-input 
                          v-model="item.ERW_SaftyHeader" 
                          placeholder="请输入内容"
                          @mousedown.stop.native 
                          class="head"></el-input>
                          <i class="el-icon-remove" v-if="index>2" @click="ERCustomEditItemRemoveAll(index,CockpitCustomEditStatus)"></i>
                        </div>
                        <li v-for="(i,d) in item.ERW_SaftySingle" :key="d">  
                          <el-input 
                          v-model="item.ERW_SaftySingle[d].ER_cont" 
                          placeholder="请输入内容"
                          @mousedown.stop.native
                          class="capex_input"></el-input>
                          <i class="el-icon-remove" @click="ERCustomEditItemRemove(index,d,CockpitCustomEditStatus)"></i>
                        </li>
                      </ul>
                      <div class="css-hover-btn container_right" @click="ERCustomEditItemAdd(index,CockpitCustomEditStatus)">
                          添加
                      </div>
                    </el-container>
              </div>
            </div>
            <div slot="mainslot" 
            class="_css-line css-common-line"
            v-else-if="CockpitCustomEditStatus === 24">
              <div class="ERCustomConfig_container">
                  <ul class="container_left">
                      <li  v-for="(item,index) in ERW_other" :key="index">
                        <el-input 
                        v-model="ERW_other[index].ER_cont" 
                        placeholder="请输入内容"
                        @mousedown.stop.native 
                        class="capex_input"></el-input>
                        <i class="el-icon-remove" @click="ERCustomEditItemRemove('',index,CockpitCustomEditStatus)"></i>
                      </li>
                  </ul>
                  <div class="css-hover-btn container_right" @click="ERCustomEditItemAdd('',CockpitCustomEditStatus)">
                      添加
                  </div>
              </div>
            </div>
            <div slot="buttonslot" class="_css-formEditnameBtnCtn" >
              <zbutton-function
                  :init_text="'保存'"
                  :init_fontsize="14"
                  :debugmode="true"
                  :init_height="'40px'"
                  :init_width="'120px'"
                  @onclick="SaveCustomEdit(CockpitCustomEditStatus)"
                  >
              </zbutton-function>
              <zbutton-function
                  :init_text="'取消'"
                  :init_color="'rgba(24, 144, 255)'"
                  :init_bgcolor="'#fff'"
                  :init_fontsize="14"
                  :debugmode="true"
                  :init_height="'40px'"
                  :init_width="'120px'"
                  @onclick="cancelCustomEdit"
                  >
              </zbutton-function>
              <i v-if="CockpitCustomEditStatus===21||CockpitCustomEditStatus===23" class="el-icon-circle-plus-outline addAll" @click="ERCustomEditItemAddAll(CockpitCustomEditStatus)"></i>
            </div>
        </zdialog-function>
    </div>
</template>
<script>
export default {
    name:'CompsCockpitCustomEditMonth',
    props:{
        CockpitCustomEditStatus:{
            type:Number,
            required:true,
        },//21为施工进度，22为工程质量，23为安全文明，24为其他
        ERW_coustom_title:{
            type:String,
            required:true,
        },
        ChooseER_date:{
            required:true,
        },
        ER_data:{
            required:true,
        },
        organizeId:{
            required:true,
        }
    },
    data(){
      return {
        NewER_data:undefined,
        ERW_progress:[//施工进度
          {
            ERW_progressHeader:"工区一",
            ERW_progressSingle:[
              {
                ER_cont:"基础结构（百分比）",
              },
              {
                ER_cont:"钢结构（百分比）",
              },
              {
                ER_cont:"屋面、雨棚（百分比）",
              },
              {
                ER_cont:"幕墙（百分比）",
              },
              {
                ER_cont:"二次结构（百分比）",
              },
              {
                ER_cont:"机电施工（百分比）",
              },
              {
                ER_cont:"装饰工程（百分比）",
              },
              {
                ER_cont:"市政工程（百分比）",
              },
            ],
          },
          {
            ERW_progressHeader:"工区二",
            ERW_progressSingle:[
              {
                ER_cont:"基础结构（百分比）",
              },
              {
                ER_cont:"钢结构（百分比）",
              },
              {
                ER_cont:"屋面、雨棚（百分比）",
              },
              {
                ER_cont:"幕墙（百分比）",
              },
              {
                ER_cont:"二次结构（百分比）",
              },
              {
                ER_cont:"机电施工（百分比）",
              },
              {
                ER_cont:"装饰工程（百分比）",
              },
              {
                ER_cont:"市政工程（百分比）",
              },
            ],
          },
        ],
        ERW_quality:[//工程质量
          {
            ER_cont:"本月质量检查（次数）",
            ER_custom_input:''
          },
          {
            ER_cont:"检查合格（次数）",
            ER_custom_input:''
          },
          {
            ER_cont:"本月发现缺陷待解决问题",
            ER_custom_input:''
          },
          {
            ER_cont:"本月发现缺陷已解决问题",
            ER_custom_input:''
          },
          {
            ER_cont:"工程待解决问题总数",
            ER_custom_input:''
          },
        ],
        ERW_Safty:[//安全文明
          {
            ERW_SaftyHeader:"安全隐患",
            ERW_SaftySingle:[
              {
                ER_cont:"物体打击",
              },
              {
                ER_cont:"火灾",
              },
              {
                ER_cont:"中毒和窒息",
              },
              {
                ER_cont:"坍塌",
              },
              {
                ER_cont:"机械伤害",
              },
              {
                ER_cont:"车辆伤害",
              },
              {
                ER_cont:"起重伤害",
              },
              {
                ER_cont:"火药爆炸",
              },
              {
                ER_cont:"触电",
              },
              {
                ER_cont:"灼伤",
              },
              {
                ER_cont:"高空坠落",
              },
              {
                ER_cont:"其他伤害",
              },
            ],
          },
          {
            ERW_SaftyHeader:"绿色施工",
            ERW_SaftySingle:[
              {
                ER_cont:"场地苫盖",
              },
            ],
          },
          {
            ERW_SaftyHeader:"疫情防控",
            ERW_SaftySingle:[
              {
                ER_cont:"本周核酸检测（检测人数）",
              },
              {
                ER_cont:"本周核酸检测（合格）",
              },
            ],
          },
        ],
        ERW_other:[//其他
          {
            ER_cont:"进度滞后原因",
            ER_custom_input:''
          },
          {
            ER_cont:"质量问题及整改措施",
            ER_custom_input:''
          },
        ],
      }
    },
    methods:{
      SaveCustomEdit(num){//保存编辑
        let _this = this
        if(num === 21){
          let MonthJSON = JSON.stringify({
            ERW_progress:_this.ERW_progress,
          })
          if(MonthJSON === JSON.stringify(_this.NewER_data)){
            this.$message.warning('当前数据未修改，无需保存')
            return
          }
          // for (let i = 0; i < _this.ERW_progress.length; i++) {
          //   const ele = _this.ERW_progress[i].ERW_progressSingle;
          //   let ERWFill=ele.every(item => item.ER_cont!== '')
          //   if(!ERWFill||_this.ERW_progress[i].ERW_progressHeader ===''){
          //     _this.$message.warning('请填写完整的数据')
          //     return
          //   }
          // }
          _this.$confirm("保存新数据会取代原有的编辑页面数据，是否确认？", "操作确认", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning"
          }).then(() => {
            _this.$axios({
              method: "post",
              url: `${window.bim_config.webserverurl}/api/VisualView/VisualView/InsetMonthERW_progressJSON`,
              data:_this.$qs.stringify({
                MonthJSON,
                Month_Time: _this.ChooseER_date+"-01",
                organizeId:_this.organizeId
              })
            })
            .then(() => {
                this.$emit('CloseERCustomEdit')
                this.$emit('RefreshData')
            }).catch(() => {});
          }).catch(() => {});
        }else if(num === 22){
          let MonthJSON = JSON.stringify({
            ERW_quality:_this.ERW_quality,
          })
          if(MonthJSON === JSON.stringify(_this.NewER_data)){
            this.$message.warning('当前数据未修改，无需保存')
            return
          }
          // let ERWFill=_this.ERW_quality.every(item => item.ER_cont!== '')
          // if(ERWFill === true){
          //   _this.$confirm("保存新数据会取代原有的编辑页面数据，是否确认？", "操作确认", {
          //     confirmButtonText: "确定",
          //     cancelButtonText: "取消",
          //     type: "warning"
          //   }).then(() => {
          //     _this.$axios({
          //       method: "post",
          //       url: `${window.bim_config.webserverurl}/api/VisualView/VisualView/InsetMonthERW_qualityJSON`,
          //       data:_this.$qs.stringify({
          //         MonthJSON,
          //         Month_Time: _this.ChooseER_date+"-01",
          //         organizeId:_this.organizeId
          //       })
          //     })
          //     .then(() => {
          //       this.$emit('CloseERCustomEdit')
          //       this.$emit('RefreshData')
          //     }).catch(() => {});
          //   }).catch(() => {});
          // }else{
          //   _this.$message.warning('请填写完整数据')
          // }
          _this.$confirm("保存新数据会取代原有的编辑页面数据，是否确认？", "操作确认", {
              confirmButtonText: "确定",
              cancelButtonText: "取消",
              type: "warning"
            }).then(() => {
              _this.$axios({
                method: "post",
                url: `${window.bim_config.webserverurl}/api/VisualView/VisualView/InsetMonthERW_qualityJSON`,
                data:_this.$qs.stringify({
                  MonthJSON,
                  Month_Time: _this.ChooseER_date+"-01",
                  organizeId:_this.organizeId
                })
              })
              .then(() => {
                this.$emit('CloseERCustomEdit')
                this.$emit('RefreshData')
              }).catch(() => {});
            }).catch(() => {});
        }else if(num === 23){
          let MonthJSON = JSON.stringify({
            ERW_Safty:_this.ERW_Safty,
          })
          if(MonthJSON === JSON.stringify(_this.NewER_data)){
            this.$message.warning('当前数据未修改，无需保存')
            return
          }
          // for (let i = 0; i < _this.ERW_Safty.length; i++) {
          //   const ele = _this.ERW_Safty[i].ERW_SaftySingle;
          //   let ERWFill=ele.every(item => item.ER_cont!== '')
          //   if(!ERWFill||_this.ERW_Safty[i].ERW_SaftyHeader===''){
          //     _this.$message.warning('请填写完整的数据')
          //     return
          //   }
          // }
          _this.$confirm("保存新数据会取代原有的编辑页面数据，是否确认？", "操作确认", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning"
          }).then(() => {
            _this.$axios({
              method: "post",
              url: `${window.bim_config.webserverurl}/api/VisualView/VisualView/InsetMonthERW_SaftyJSON`,
              data:_this.$qs.stringify({
                MonthJSON,
                Month_Time: _this.ChooseER_date+"-01",
                organizeId:_this.organizeId
              })
            })
            .then(() => {
              this.$emit('CloseERCustomEdit')
              this.$emit('RefreshData')
            }).catch(() => {});
          }).catch(() => {});
        }else if(num === 24){
          let MonthJSON = JSON.stringify({
            ERW_other:_this.ERW_other,
          })
          if(MonthJSON === JSON.stringify(_this.NewER_data)){
            this.$message.warning('当前数据未修改，无需保存')
            return
          }
          // let ERWFill=_this.ERW_other.every(item => item.ER_cont!== '')
          // if(ERWFill === true){
          //    _this.$confirm("保存新数据会取代原有的编辑页面数据，是否确认？", "操作确认", {
          //     confirmButtonText: "确定",
          //     cancelButtonText: "取消",
          //     type: "warning"
          //   }).then(() => {
          //     _this.$axios({
          //       method: "post",
          //       url: `${window.bim_config.webserverurl}/api/VisualView/VisualView/InsetMonthERW_otherJSON`,
          //       data:_this.$qs.stringify({
          //         MonthJSON,
          //         Month_Time: _this.ChooseER_date+"-01",
          //         organizeId:_this.organizeId
          //       })
          //     })
          //     .then(() => {
          //       this.$emit('CloseERCustomEdit')
          //       this.$emit('RefreshData')
          //     }).catch(() => {});
          //   }).catch(() => {});
          // }else{
          //   _this.$message.warning('请填写完整数据')
          // }
          _this.$confirm("保存新数据会取代原有的编辑页面数据，是否确认？", "操作确认", {
              confirmButtonText: "确定",
              cancelButtonText: "取消",
              type: "warning"
            }).then(() => {
              _this.$axios({
                method: "post",
                url: `${window.bim_config.webserverurl}/api/VisualView/VisualView/InsetMonthERW_otherJSON`,
                data:_this.$qs.stringify({
                  MonthJSON,
                  Month_Time: _this.ChooseER_date+"-01",
                  organizeId:_this.organizeId
                })
              })
              .then(() => {
                this.$emit('CloseERCustomEdit')
                this.$emit('RefreshData')
              }).catch(() => {});
            }).catch(() => {});
        }
      },
      cancelCustomEdit(){//取消编辑
        this.$emit('CloseERCustomEdit')
      },
      ERCustomEditItemAdd(i,c){//自定义配置添加
        if(c === 21){
          this.ERW_progress[i].ERW_progressSingle.push({ER_cont:""})
        }else if(c === 22){
          this.ERW_quality.push({ER_cont:""})
        }else if(c === 23){
          this.ERW_Safty[i].ERW_SaftySingle.push({ER_cont:""})
        }else if(c === 24){
          this.ERW_other.push({ER_cont:""})
        }
      },
      ERCustomEditItemRemove(index,i,c){//删除自定义配置项
      // console.log(index,i,c);
        if(c === 21){
          this.ERW_progress[index].ERW_progressSingle.splice(i,1)
        }else if(c === 22){
          this.ERW_quality.splice(i,1)
        }else if(c === 23){
          this.ERW_Safty[index].ERW_SaftySingle.splice(i,1)
        }else if(c === 24){
          this.ERW_other.splice(i,1)
        }
      },
      ERCustomEditItemRemoveAll(i,c){
        if(c === 21){
          this.ERW_progress.splice(i,1)
        }else if(c === 23){
          this.ERW_Safty.splice(i,1)
        }
      },
      ERCustomEditItemAddAll(c){//自定义配置添加
        if(c === 21){
          let addAll = {
            ERW_progressHeader:"",
            ERW_progressSingle:[
              {
                ER_cont:"",
              },
            ],
          }
          this.ERW_progress.push(addAll)
        }else if(c === 23){
          let addAll = {
            ERW_SaftyHeader:"",
            ERW_SaftySingle:[
              {
                ER_cont:"",
              },
            ],
          }
          this.ERW_Safty.push(addAll)
        }
      },
      GetCoustomCont(){
        if(!this.ER_data){
          this.$axios({
            method: "post",
            url: `${window.bim_config.webserverurl}/api/VisualView/VisualView/GetNewMonth`,
            data:this.$qs.stringify({
              organizeId:this.organizeId
            })
          }).then(res => {
            let data = res.data.Data
            if(Object.keys(data).length!=0){
              for (let i = 0; i < data.ERW_progress.length; i++) {
                const ele = data.ERW_progress[i].ERW_progressSingle;
                ele.forEach(e => {
                  e.ER_custom_input = ''
                  e.Month_Id = 0
                });
              }
              data.ERW_quality.forEach(e => {
                e.ER_custom_input = ''
                e.Month_Id = 0
              });
              for (let i = 0; i < data.ERW_Safty.length; i++) {
                const ele = data.ERW_Safty[i].ERW_SaftySingle;
                ele.forEach(e => {
                  e.ER_custom_input = ''
                  e.Month_Id = 0
                });
              }
              data.ERW_other.forEach(e => {
                e.ER_custom_input = ''
                e.Month_Id = 0
              });
              this.NewER_data = {}
              if(this.CockpitCustomEditStatus === 21){
                if(!data.ERW_progress) return
                this.NewER_data.ERW_progress = data.ERW_progress
                this.ERW_progress = JSON.parse(JSON.stringify(this.NewER_data.ERW_progress))
              }else if(this.CockpitCustomEditStatus === 22){
                if(!data.ERW_quality) return
                this.NewER_data.ERW_quality = data.ERW_quality
                this.ERW_quality = JSON.parse(JSON.stringify(this.NewER_data.ERW_quality))
              }else if(this.CockpitCustomEditStatus === 23){
                if(!data.ERW_Safty) return
                this.NewER_data.ERW_Safty = data.ERW_Safty
                this.ERW_Safty = JSON.parse(JSON.stringify(this.NewER_data.ERW_Safty))
              }else if(this.CockpitCustomEditStatus === 24){
                if(!data.ERW_other) return
                this.NewER_data.ERW_other = data.ERW_other
                this.ERW_other = JSON.parse(JSON.stringify(this.NewER_data.ERW_other))
              }
            }else{
              return
            }
          }).catch(() => {
          });
        }else{
          this.NewER_data = this.ER_data
          if(this.CockpitCustomEditStatus === 21){
            if(!this.NewER_data.ERW_progress) return
            this.ERW_progress = JSON.parse(JSON.stringify(this.NewER_data.ERW_progress))
          }else if(this.CockpitCustomEditStatus === 22){
            if(!this.NewER_data.ERW_quality) return
            this.ERW_quality = JSON.parse(JSON.stringify(this.NewER_data.ERW_quality))
          }else if(this.CockpitCustomEditStatus === 23){
            if(!this.NewER_data.ERW_Safty) return
            this.ERW_Safty = JSON.parse(JSON.stringify(this.NewER_data.ERW_Safty))
          }else if(this.CockpitCustomEditStatus === 24){
            if(!this.NewER_data.ERW_other) return
            this.ERW_other = JSON.parse(JSON.stringify(this.NewER_data.ERW_other))
          }
        }
      },
      coppyArray(arr){
        return arr.map((e)=>{
          if(typeof e==='object'){
              return Object.assign({},e);
          }else{
              return e;
          }
        }) 
      }
    },
    created(){
      this.GetCoustomCont()
    }
}
</script>
<style scoped>
.ERCustomConfig_container {
    flex-direction: column;
    height: 550px;
    overflow: auto;
  }
._css-formEditnameBtnCtn {
    display: flex;
    flex-direction: row-reverse;
    height: 64px;
    align-items: center;
    box-sizing: border-box;
    padding-right: 8px;
    position: relative;
}
.ERCustomConfig_container .container_left{
  float: left;
  padding:20px 0 0 30px;
  width: 750px;
}
.ERCustomConfig_container .container_right{
  background: linear-gradient(224deg,rgba(0,145,255,1) 0%,rgba(0,122,255,1) 100%);
  border-radius: 4px;
  color: #FFFFFF;
  font-size: 14px;
  padding: 9px 16px;
  cursor: pointer;
  float: right;
  height: 21px;
  margin-right: 30px;
}
.ERCustomConfig_container .container_left li{
  display: flex;
  align-items: center;
  padding: 5px 0;
  font-size: 18px;
}
.ERCustomConfig_container ul{
  float: left;
  padding:0px 0 0 10px;
  width: 750px;
}
.ERCustomConfig_container .ERDcontainer_left .head{
  display: flex;
  align-items: center;
  border-radius: 4px;
  background-color: #fff;
  cursor: pointer;
  margin: 8px 0;
}
.ERCustomConfig_container .ERDcontainer_left .head .el-input{
  border: 1px solid #dcdfe6;
  width: 200px;
}
.ERCustomConfig_container .ERDcontainer_left li{
  float: left;
  padding: 5px 0;
  font-size: 18px;
  margin: 0 35px 0 30px;
}
.capex_input{
  border-radius: 4px;
  border: 1px solid #dcdfe6;
  background-color: #fff;
  cursor: pointer;
  width: 200px;
}
.el-icon-remove{
  color: #000;
  cursor: pointer;
  margin: 0 20px;
  line-height: 1;
  font-size: 18px;
}
.addAll{
  font-size: 30px;
  font-weight: bold;
  margin-right: 220px;
  cursor: pointer;
}
</style>