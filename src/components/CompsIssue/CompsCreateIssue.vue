<template>
  <!-- 新增问题 hover -->
  <div class="_css-all-createissue" :style="styleobj">
    <!-- 带有标题，按钮有表单区域 -->
    <div class="_css-all-createissue-in" v-drag:[needDrag]="draggreet" :style="dragstylefunc()" @click="_stopPropagation($event)">
      <CompsDialogHeader @oncancel="_oncancel" title="发起问题">
        <div slot="btn" v-if="needMarkup">
          <el-tooltip
            class="box-item"
            effect="dark"
            content="添加批注"
            placement="right">
            <div class="slot-btn" @click="addMarkUp"></div>
          </el-tooltip>
        </div>
      </CompsDialogHeader>
      <!-- 高度100 问题标题输入区域，带有截止时间 mt24 -->
      <div class="_css-title-input">
        <div class="_css-title-input-in" name="InputForm">
          <compsAnimateInput
           class="_css-title-input-ta css-miniscroll"
            @keyup="titleinput_ku($event)"
            @searchInput="searchInput"
            v-model="datacompsAnimateInput"
            name="Input"
            id="myInput"
            :dataList="userList"
            height="70px"
            needAt
            setFocus
            placeholder="问题追踪内容,支持@参与人">
          </compsAnimateInput>
        </div>
      </div>
     
      <div class="_css-rel-btns"> 
        <div class="_css-title-input-deadline">
          <div class="_css-deadline-editbtn _css-deadline-editbtn-input">
            <el-date-picker
                :picker-options="pickerOptions"
            v-model="deadlinetimeval" type="datetime" prefix-icon="el-icon-time" placeholder="截止时间"></el-date-picker>
          </div>
        </div>
        <div class="_css-rel-localfile" @click.stop="addlocalrel">
          <div class="_css-prop-icon icon-interface-folder"></div>
          <div class="_css-prop-text"></div>
        </div>
        <div class="_css-rel-localfile" @click.stop="addlocalpic">
          <div class="_css-prop-icon icon-interface-model-picture"></div>
          <div class="_css-prop-text"></div>
        </div>

        <!-- 默认类别 最开始版本 类别选项在右侧 -->
        <div class="_css-rel-localfile _css-rel-localfile-type">
          <div class="_css-prop-icon icon-interface-list-fill"></div>
          <div class="_css-prop-text"  @click.stop="addlocaltype">{{issuetypeName}}</div>
          <div class="The-default-category-box" v-show="box_show" >
            <ul>
              <li 
              :title="item.ItemName"
              v-for="(item) in extdata._issuestatusitems" :key="item.ItemDetailId" @click.stop="recordtype(item)">{{item.ItemName}}</li>
            </ul>
          </div>
        </div>

      </div>
      <!-- //h20 mt14 -->
  
          <!-- 添加得文件如果是图片得话 展示出来 -->
          <div class="CompsCreateIssue-image"
            v-show="imgsrc || extdata.addingPic.length > 0"
          >
            <!-- 当前按模型相机位置截图 或者自己添加的图片大图-->
            <div class="_css-image-img-bg" v-if="imgsrc" :style="computeDetailMiniPic(bgpicture_src_DIY)">
            </div>
            <div class="small-model">
              <!-- 模型发起问题时带过来的模型图片 -->
              <div class="small-model-img _css-model-micon-container"
                   @click.stop="click_bgpicture(imgsrc)"
                    v-if="imgsrc"
                    :style="computeDetailMiniPic(imgsrc)">
                    <div class="_css-model-micon icon-interface-model_list" ></div>
                  </div>
              <!-- 后续自己添加的小图 -->
              <div
                   class="small-model-img"
                   v-for="(item) in extdata.addingPic" :key="item.bf_guid"
                   :style="getstyle_by_bfpath(item.bf_path)"
                   @click.stop="click_bgpicture_window(item.bf_guid, item.bf_path)"
                  >
                  <div
                    @click.stop="remove_temp_relation_pic(item.bf_guid)"
                    class="_css-deletecurrentbtn-small-pic icon-suggested-close_circle"
                    ></div>
                  </div>
            </div>
          </div>


      <!-- mt16 mh164 关联文档区域 -->
      <div class="_css-rel-docs css-miniscroll">
        <div class="_css-rel-docs-in">
          <div class="_css-rel-docs-item" v-for="item in extdata.addingFiles" :key="item.FileId">
            <div class="_css-item-leftpart" @click="row_filename_click(item)">
              <div
                class="_css-item-relfileicon"
                :class="$staticmethod.getIconClassByExtname(item.FileName, item.FileSize)"
              ></div>
              <div class="_css-item-relfiletext">{{item.FileName}}</div>
            </div>
            <div class="_css-item-closeitem" @click.stop="remove_temp_relation(item.FileId)">
              <div class="icon-suggested-close_circle"></div>
            </div>
          </div>
 
        </div>
      </div>
      <!-- //mt16 mh164 关联文档区域 -->

      <div class="issue-btn">
        <span class="btn-css" @click="_oncancel">取消</span>
        <span class="btn-css btn-ok" @click="_onok">确定</span>
      </div>
      

    </div>
    <!-- //带有标题，按钮有表单区域 -->

    <!-- 选人控件 -->
    <CompsUsersSearch
      @oncancel="userSearchCancel"
      @onok="userSearchOk"
      apiurlname="GetToAddIssueJoiners"
      :roleId="'-1'"
      v-if="extdata.isaddingmember"
      :innerWidth="-1"
      :hovercolor="'rgba(0,0,0,0.15)'"
    ></CompsUsersSearch>
    <!-- //选人控件 -->

    <input type="file" style="display:none" id="id_EditIssue_File" @change="fileEditIssueChange()" />

    <input
      type="file"
      style="display:none"
      id="id_CompsCreateIssue_File"
      accept="image/*"
      @change="fileControlChange()"
    />
  </div>
  <!-- //新增问题 hover -->
</template>
<script>
import CompsDialogHeader from "@/components/CompsDialog/CompsDialogHeader";
import CompsUsersSearch from "@/components/CompsAuth/CompsUsersSearch";
import CompsInput from "@/components/CompsCommon/CompsInput";
import compsAnimateInput from "@/components/CompsCommon/compsAnimateInput";
export default {
  data() {
    return {
      pickerOptions: {},
      val: "0",
      dragstyle: {
        position: "fixed",
        right: "calc(50% - 250px)",
        top: "calc(50% - (this.outertop + 'px') || 136px)"
      },
      extdata: {
        addingPic:[],  //正在添加的图片数据（字段:bf_filename, bf_guid, bf_md5, bf_path）
        fulldata:{},  //添加图片后返回的信息（已废弃，没有找到他下面的base_files）
        _issuestatusitems: [], //获取所有问题状态类别
        titleinput: "", // 正在添加问题的标题
        isaddingmember: false, // 正在@添加人
        addpeople: false, //@添加参与人
        addingjoiners: [], // 正在添加的joiners
        addingFiles: [], // 正在添加的文档库数据（字段:FileId, FileName, FileExtName, FileSize）
      },
      deadlinetimeval: "", // 截止时间
      datacompsAnimateInput: {}, //组件v-model绑定的空对象
      valve:'', //datacompsAnimateInput下得纯文本
      box_show: false, // 默认类别盒子的显示和隐藏 默认为false
      userList:null,
      imgsrc:'',  //图片地址
      issuetypeid:'',  //点击类别 选择的类别
      issuetypeName:'默认类别',
      ModelId:'', //模型ID
      ViewPointID:'', //子文API返回的对象的 str
      ImageUrl:'',  //ImageUrl,
      needDrag: true,
      bgpicture_src:'', //问题列表中图片路径
      bgpicture_src_DIY:'',  //自己添加的图片大图
      bgpicture_title:'', //问题列表中图片名称
      bgpicture_id:'',  //问题列表中图片ID
      R_lastTime:true,//第一次发起问题时的状态

    };
  },
  mounted() {
    var _this = this;
    window.icvue = _this;
    _this.extdata._issuestatusitems = _this.issuestatusitems;

    _this.pickerOptions = {
      disabledDate(time) {
        var curDate = new Date();
        var preDate = new Date(curDate.getTime() - 24*60*60*1000); //前一天
        return time.getTime() < preDate;
      }
    }

    _this.loadsugjoiners('');
    // console.log(_this.loadsugjoiners)

    if (!_this.extdata._issuestatusitems || _this.extdata._issuestatusitems.length == 0) {

      // 发送ajax 获取 issuestatusitems
      _this
        .$axios({
          method: "get",
          url: `${
            window.bim_config.webserverurl
          }/api/User/Issue/GetIssueTypes?organizeId=${_this.$staticmethod._Get(
            "_OrganizeId"
          )}&token=${_this.$staticmethod.Get("Token")}`
        })
        .then(x => {
          if (x.status == 200 && x.data.Ret > 0) {
            var arr = [];
            for (var i = 0; i < x.data.Data.length; i++) {
              arr.push({
                id: x.data.Data[i].ItemDetailId,
                text: x.data.Data[i].ItemName
              });
            }
            _this.extdata._issuestatusitems = x.data.Data
          } else {
          }
        })
        .catch(x => {
        });
    }
  },
  created() {

    var _this = this;
    if(_this.hasBaseUrl.length > 0){
      _this.imgsrc = _this.hasBaseUrl;
      // this.$emit("imgsrc", _this.hasBaseUrl);
      _this.bgpicture_src_DIY = _this.imgsrc;
      _this.ImageUrl =  _this.hasBaseUrl;
    }
    else{
      if(document.getElementById("scene-iframe").contentWindow){
        var thewin = document.getElementById("scene-iframe").contentWindow;
        //console.log(372);
        if(thewin.scene){
          thewin.scene.snapThumbnail().then(image_base64=>{
            this.imgsrc = image_base64
            // this.$emit("imgsrc", image_base64);
            _this.bgpicture_src_DIY = _this.imgsrc;
            _this.ImageUrl = image_base64;
          })
        }
      }
    } 
  
  },
  computed: {
    styleobj: {
      get() {
        var _this = this;
        var _obj = {};
        if (_this.zIndex) {
          _obj["z-index"] = _this.zIndex;
        } else {
          _obj["z-index"] = 2;
        }
        return _obj;
      }
    }
  },
  watch: {
    datacompsAnimateInput() {
    },
    zIndex (newVal, oldVal) {
      var _this = this;
      if(_this.hasBaseUrl.length > 0){
        _this.imgsrc = _this.hasBaseUrl;
        // this.$emit("imgsrc", _this.hasBaseUrl);
        _this.bgpicture_src_DIY = _this.imgsrc;
        _this.ImageUrl =  _this.hasBaseUrl;
      }
      else{
        if(document.getElementById("scene-iframe").contentWindow){
          var thewin = document.getElementById("scene-iframe").contentWindow;
          //console.log(372);
          if(thewin.scene){
            let eq = thewin.scene.viewpoints.findIndex(vp => vp.name == this.stayName)
            if (newVal == 2 && thewin.scene.viewpoints.length && eq !== -1) {
              let img = thewin.scene.viewpoints[eq].thumbnail
              this.imgsrc = img
              _this.bgpicture_src_DIY = _this.imgsrc;
              _this.ImageUrl = img;
            } else {
              thewin.scene.snapThumbnail().then(image_base64=>{
                this.imgsrc = image_base64
                // this.$emit("imgsrc", image_base64);
                _this.bgpicture_src_DIY = _this.imgsrc;
                _this.ImageUrl = image_base64;
              })
            }
            
          }
        }
      } 
    }
  },
  props: {
    zIndex: {
      type: Number,
      required: false,
      default: 2
    },
    hasImg: {
      type: Boolean,
      default: false
    },
    issuestatusitems: {
      type: Array,
      require: false
    },
    outertop:{
      type: Number,
      require: false
    },
    hasBaseUrl: {
      type: String,
      required: false,
      default: ''
    },
    needMarkup: {
      type: Boolean,
      default: false
    },
    stayName: {
      type: String,
      default: ''
    }
  },
  components: {
    CompsDialogHeader,
    CompsUsersSearch,
    CompsInput,
    compsAnimateInput,
  },
  methods: {

    dragstylefunc(){
      var _this = this;
      var _s = {};
      _s["position"] = "fixed";
      _s["right"] = "calc(50% - 250px)";
      //debugger;
      if (_this.outertop) {
        console.log(`calc(50% - ${_this.outertop}px)`);
        _s["top"] = `calc(50% - ${_this.outertop}px)`;
      } else {
        _s["top"] = `calc(50% - 136px)`;
      }
      // console.log(_s);
      return _s;
    },

    // 判断发起问题中是否有图片区域
    currentIssueHasImage(){
      var _this = this;

      if (imgsrc) {
        return true;
      }

      if (!_this.extdata.addingPic || _this.extdata.addingPic.length == 0) {
        return false;
      } else {
        return true;
      }
    },

    // 当前模型位置截图函数 （背景图）
    computeDetailMiniPic(url){
      var _this = this;
      var _s = {};
      _s["background-repeat"] = "no-repeat";
      _s["background-size"] = "contain";
      _s["background-position"] = "center";
      _s["background-image"] = `url('${url}')`;
      return _s;
    },

    _stopPropagation(ev){
      var _this = this;
      ev.stopPropagation();
    },

    // 使用 bf.bf_path 计算 style 样式对象
    getstyle_by_bfpath(bf_path){
      var _this = this;
      var _s = {};
      _s["background-repeat"] = "no-repeat";
      _s["background-size"] = "contain";
      _s["background-position"] = "center";
      _s["background-image"] = `url('${window.bim_config.webserverurl}${bf_path}')`;
      return _s;
    },

    // 发起问题点击小图切换
    click_bgpicture_window(bf_guid, bf_path) {
      var _this = this;
      _this.bgpicture_id = bf_guid;
      _this.bgpicture_src_DIY = window.bim_config.webserverurl + bf_path;
    },
    // 发起问题点击小图切换
    click_bgpicture(imgsrc) {
      var _this = this;
      _this.bgpicture_src_DIY = imgsrc;
    },

    addMarkUp () {
      this.$emit('addMarkUp', {value: this.datacompsAnimateInput.text})
    },
    searchInput(e) {
      this.loadsugjoiners(e);
    },
    loadsugjoiners(str) {
      var _this = this;
        _this
          .$axios({
            method: "get",
            url: `${
              window.bim_config.webserverurl
            }/api/User/User/GetToAddIssueJoiners?Token=${_this.$staticmethod.Get(
              "Token"
            )}&RoleId=-1&ProjectID=${_this.$staticmethod._Get(
              "organizeId"
            )}&encodedKeyWord=${str}`
          })
          .then(x => {
            if (x.status == 200 && x.data.Ret > 0) {
              // console.log(x)
              this.userList = x.data.Data;

              // 顺带将userList参与人列表传递给父组件Issue （这个子组件传递过去得方法先废弃掉  因为评论@时需要触发下发起问题组件  但是两个结构没有逻辑上的联系 所以不能这么做）
                // console.log(this.userList)
                // this.$emit("userList", this.userList);
            }

            if (x.data.Ret < 0) {
              _this.$message.error(x.data.Msg);
            }
          })
          .catch(x => {});
    },
    draggreet(val) {
      var _this = this;
      _this.val = val;
    },
    _stopPropagation(ev) {
      ev.stopPropagation();
    },
    titleinput_ku(ev) {
      var _this = this;

      //TODO 使用亚博的新组件来完成人员的下拉框展示。

      // // 判断如果此次输入与上一次输入多一个@，则会弹出选人框。
      // // //console.log(_this.extdata.titleinput);
      // if (ev.key == "@") {
      //   _this.extdata.isaddingmember = true;
      // } else {
      // }
    },

    // @人后弹出弹框，点击确定后：
    userSearchOk(list) {
      console.log(list);
      var _this = this;
      _this.extdata.isaddingmember = false;

      // 遍历 addingjoiners，如果没有添加则添加。
      for (var i = 0; i < list.length; i++) {
        var ifhas = _this.extdata.addingjoiners.filter(
          x => x.UserId == list[i].UserId
        );
        if (ifhas.length > 0) {
        } else {
          _this.extdata.addingjoiners.push(list[i]);
          _this.extdata.titleinput += list[i].RealName + " ";
        }
      }
    },
    userSearchCancel() {
      var _this = this;
      _this.extdata.isaddingmember = false;
    },
    remove_temp_relation(id) {
      var _this = this;
      _this.extdata.addingFiles = _this.extdata.addingFiles.filter(
        x => x.FileId != id
      );
    },
    remove_temp_relation_pic(id) {
      var _this = this;

      // 找到被删除图片的索引
      var _originindex =  _this.extdata.addingPic.findIndex(
        x => x.bf_guid == id
      );

      _this.extdata.addingPic = _this.extdata.addingPic.filter(
        x => x.bf_guid != id
      );
      console.log(_this.extdata.addingPic);

      //debugger;
      if (_this.bgpicture_id == id) {
        if (_this.extdata.addingPic.length == 0) {
          if (_this.imgsrc) {
            // 不剩下图片了，但是有模型图片
            //debugger;
            _this.click_bgpicture(_this.imgsrc);
          } else {
            // 不剩下图片了，也没有模型图片
            //debugger;
          }
        } else {
          if (_originindex == 0) {
            // 剩下图片了，删除的是原来的第一张
            if (_this.imgsrc) {
              // 显示模型图片
              //debugger;
              _this.click_bgpicture(_this.imgsrc);
            } else {
              // 显示现在的第一张
              //debugger;
              _this.click_bgpicture_window(_this.extdata.addingPic[0].bf_guid, _this.extdata.addingPic[0].bf_path);
            }
          } else {
            // 删除的不是原来的第一张，直接显示 _originindex - 1
            //debugger;
            _this.click_bgpicture_window(_this.extdata.addingPic[_originindex - 1].bf_guid, _this.extdata.addingPic[_originindex - 1].bf_path);
          }
        }
      }

      _this.$emit("imagechanged", _this.extdata.addingPic);
    },


    _onok() {
      var _this = this;
      // emit 向外抛
      var title = (_this.extdata.titleinput = this.datacompsAnimateInput.text);
       
      var subscript = _this.datacompsAnimateInput.subscript;
      var addingFiles = _this.extdata.addingFiles;
      var deadlinetimeval = _this.deadlinetimeval;

      var addingPic = _this.extdata.addingPic;

      var _ImageIds = '';
      if (addingPic) {
        for (var i = 0; i < addingPic.length; i++) {
          _ImageIds += `${addingPic[i].bf_guid}${i == addingPic.length - 1?"":","}`;
        }
      }
      var addingobj = {
        title: title,
        addingjoiners: subscript,
        addingFiles: addingFiles,
        deadlinetimeval: deadlinetimeval,
        IssueTypeId: _this.issuetypeid,
        // 模型ID
        ModelId:_this.ModelId,
        // 子文API返回的对象的 str
        ViewPointID:_this.ViewPointID,
        // 截图 base64字符串
        ImageUrl:_this.ImageUrl,
        //发起问题 自己添加的小图
        addingPic:  addingPic,
        //小图参数
        ImageIds: _ImageIds
      };

      //debugger;
      
      // console.log(addingobj);
      if(_this.R_lastTime){
          _this.R_lastTime = false
          _this.$emit("onok", addingobj);
          setTimeout(()=>{
            _this.R_lastTime = true
          },2000)
      }else{
        // console.log('请勿过快点击');
      }

    },
    _oncancel() {
      var _this = this;
      _this.$emit("oncancel");
    },
    // 添加本地图片按钮 事件
    addlocalpic() {
      var _this = this;

      // 重置 id 为 id_CompsCreateIssue_File 的 input 元素
      var dominputfile = document.getElementById("id_CompsCreateIssue_File");
      //dominputfile.outerHTML = dominputfile.outerHTML;
      dominputfile.value = "";

      // 点击 input 元素，弹出文件选择对话框
      dominputfile.click();
    },

    // 添加本地文件按钮 事件
    addlocalrel() {
      // 重置 id 为 id_EditIssue_File 的 input 元素
      var dominputfile = document.getElementById("id_EditIssue_File");
      //dominputfile.outerHTML = dominputfile.outerHTML;
      dominputfile.value = "";

      // 点击 input 元素，弹出文件选择对话框
      dominputfile.click();
    },

    // 判断有无相同文件名的附件已添加到附件列表中，如果有，则提示。
    // 否则进行上传，并返回文件名及FileId、FileSize、FileName 等信息。
    fileControlChange() {
      var _this = this;
      // console.log(178);

      // 发起问题中添加图片后，直接拿到图片，访问 UploadImage 接口，获取 bf_guid, bf_md5 和 bf_path
      var dominputfile = document.getElementById("id_CompsCreateIssue_File");
      if (dominputfile.files.length == 1) {
        // 拿到文件对象
        var file = dominputfile.files[0];

        // 判断是否已存在同名的附件列表
        if (
          _this.extdata.addingFiles.filter(x => x.FileName == file.name)
            .length > 0
        ) {
          //
          _this.$message.error("已存在相同名称的附件");
          return;
        } else {
          // 拿到其它参数
          var File = dominputfile.files[0];
          var _Token = _this.$staticmethod.Get("Token");
          // 提交给服务器
          var fd = new FormData();
          fd.append("IssueId", '');
          fd.append("FormFile", File);
          var config = {
            headers: {
              "Content-Type": "multipart/form-data"
            }
          };

          // 文件是图片格式传递Token和F0
          // var F0 = File
          //debugger;
          _this.$axios
            .post(
              `${this.$issueBaseUrl.UploadImage}?token=${ _this.$staticmethod.Get("Token")}`,
              fd,
              config,
            )
            .then(x => {
                if (x.data.Ret < 0) {
                  _this.$message.error(x.data.Msg);
                  return;
                }
              let _bf_path = '/'+ x.data.Data.bf_path
            // 设置 发起问题中，大图的名称及大图的地址
            _this.bgpicture_src = `${window.bim_config.webserverurl}${_bf_path}`;
            _this.bgpicture_title = x.data.Data.bf_filename;
            _this.bgpicture_id = x.data.Data.bf_guid;
 
              if (x.status == 200 && x.data) {
                //问题中，自己加得小图数组
                var datatopush = {
                  IssueId: '',
                  bf_filename: x.data.Data.bf_filename,
                  bf_guid: x.data.Data.bf_guid,
                  bf_md5: x.data.Data.bf_md5,
                  bf_path: _bf_path
                };
                _this.extdata.addingPic.push(datatopush);
                _this.click_bgpicture_window(x.data.Data.bf_guid, _bf_path);
                 _this.$emit("imagechanged", _this.extdata.addingPic);
                 
              }
            })
            .catch(x => {
              //_this.$message.error(`服务器处理发生错误，可能是文件类型不符合白名单规则，请检查文件`);
              console.error(x);
            });
        }
      } else {
      }
    },
    fileEditIssueChange() {
      var _this = this;
      var dominputfile = document.getElementById("id_EditIssue_File");
      if (dominputfile.files.length == 1) {
        // 拿到文件对象
        var file = dominputfile.files[0];

        // 判断是否已存在同名的附件列表
        if (
          _this.extdata.addingFiles.filter(x => x.FileName == file.name)
            .length > 0
        ) {
          //
          _this.$message.error("已存在相同名称的附件");
          return;
        } else {
          // ProjectID: c14075a2-6c91-1fdb-3fbe-76bf898c24cf
          // CreateUserID:
          // CreateUserName:
          // FileType: Issue
          // File: (binary)

          // 拿到其它参数
          var ProjectID = _this.$staticmethod._Get("organizeId");
          var CreateUserID = "";
          var CreateUserName = "";
          var FileType = "Issue";
          var File = file;
          var userid = _this.$staticmethod.Get("UserId");

          // 提交给服务器
          var fd = new FormData();
          let FileKey=this.$md5(new Date().getTime()+''+File.name);
          
          fd.append("ProjectID", ProjectID);
          fd.append("BusinessId", 0);
          fd.append("AttachmentType", '2');
          fd.append("FileName", File.name);
          fd.append("FileKey", FileKey);
          fd.append("FileSize", File.size);
          fd.append("ChunkNumber", 1);
          fd.append("Index", 1);
          fd.append("UserId", userid);
          fd.append("File", File);
          var config = {
            headers: {
              "Content-Type": "multipart/form-data"
            }
          };
          _this.$axios
            .post(
              `${window.bim_config.webserverurl}/api/v1/attach/upload?Token=${_this.$staticmethod.Get('Token')}`,
              fd,
              config
            )
            .then(x => {
              //console.log(235);
              // debugger;
              if (x.data.Ret == 1) {
                let _data = x.data.Data.Data
                // console.log(x.data.Data.Data,'===========xxxx', _data.AttachmentId)
                var datatopush = {
                  FileId: _data.AttachmentId,
                  FileName: _data.AttachmentName,
                  FileExtName: _data.AttachmentExtension,
                  FileSize: _data.AttachmentSize
                };
                _this.extdata.addingFiles.push(datatopush);
              } else {
                _this.$message.error(x.data.Msg);
              }
            })
            .catch(x => {
               _this.$message.error(`服务器处理发生错误，可能是文件类型不符合白名单规则，请检查文件`);
            });
        }
      } else {
      }
    },

    // 默认类别按钮点击事件  类别盒子的显示和隐藏
    addlocaltype() {
      this.box_show = !this.box_show;
    },

    // 点击类别列表 选择类别
    recordtype(item){
      // debugger
      var _this = this;
      _this.issuetypeid = item.ItemDetailId;
      _this.issuetypeName = item.ItemName;
      _this.box_show =  false;
    },

    // 添加图片或文件后 点击使其展示出来事件  begin_previewfile 方法后执行 （显示添加的文件和图片  判断文件类型）
    row_filename_click(item){
      var _this = this;
      console.log(item)
      // debugger
        // 打开在线预览
        var bimcomposerId = _this.$staticmethod._Get("organizeId");

        // var url = `${_this.$configjson.bimserverurl}/api/Doc/GetHideFile?ProjectID=${bimcomposerId}&FileId=${item.FileId}&FileType=Issue`;
        // 字符串模板 拼接文档在线地址
        var url = `${
          _this.$staticmethod.getDocServer()
        }/api/Doc/GetHideFile?ProjectID=${bimcomposerId}&FileId=${item.FileId}&FileType=Issue&Token=${this.$staticmethod.Get('Token')}`;
        console.log(url)

        // 根据扩展名获取在线浏览地址
      var url_iframe_all;

      if (item.FileName.toLowerCase().indexOf(".dwg") > 0) {
        // 修改当前预览的关闭按钮类
        _this.$emit("set_projectboot_extdata", "_docviewtype", "dwg");

        // dwg 在线预览
        url_iframe_all = `${
          _this.$configjson.dwgurl
        }/Home/Index2?dwgurlcfg=${encodeURIComponent(url)}&name=${
          item.FileName
        }`;
      } else {
        // debugger
        // 修改当前预览的关闭按钮类
        _this.$emit("set_projectboot_extdata", "_docviewtype", "office");
 
        console.log('发起问题时，关联文档的预览');
        url_iframe_all =  _this.$staticmethod.computeViewUrl(url, item.FileName);
      }
       //debugger
        _this.$emit("set_projectboot_extdata", "_show_idocview", true);
        _this.$emit("set_projectboot_extdata", "_idocviewurl", url_iframe_all);

    },
  }
};
</script>

<style>

._css-deadline-editbtn-input .el-input__inner{
  cursor:pointer;
}

._css-deadline-editbtn-input input.el-input__inner::-webkit-input-placeholder { /* WebKit browsers */
  color: #cc0000 !important;
  font-size: 14px;
}

._css-deadline-editbtn-input input.el-input__inner::-moz-placeholder { /* Mozilla Firefox 19+ */
  color:#cc0000 !important;
  font-size: 14px;
}
.el-input__prefix>.el-icon-time{
  color:#cc0000 !important;
}
</style>

<style scoped>
._css-item-relfileicon {
  height: 16px;
  width: 16px;
  margin-left: 8px;
}

._css-item-relfiletext {
  height: 20px;
  line-height: 20px;
  margin-left: 8px;
  color: rgba(0, 0, 0, 0.65);
}

._css-rel-docs-item:hover {
  color: #1890ff;
}

._css-rel-docs-item:hover ._css-item-relfiletext {
  color: #1890ff;
}

._css-rel-docs-item {
  cursor: pointer;
}

._css-item-closeitem {
  height: 100%;
  width: 48px;
  display: flex;
  align-items: center;
  justify-content: space-around;
  cursor: pointer;
}

._css-item-leftpart {
  flex: 1;
  height: 100%;
  display: flex;
  align-items: center;
}

._css-rel-docs-item {
  margin-top: 12px;
  height: 32px;
  display: flex;
  align-items: center;
  user-select: none;
}

._css-rel-docs-item:hover {
  background-color: rgba(0, 0, 0, 0.02);
  border-radius: 2px;
}

._css-rel-docs-in {
  width: 100%;
}

._css-rel-docs {
  /* mt 16 - every 12 = container 4 */
  margin-top: 4px;
  /* max-height: 164px; */
  overflow-y: auto;
  padding-left: 16px;
  padding-right: 16px;
  box-sizing: border-box;
  max-height: 256px;
}

._css-relmodel-modelicon {
  color: rgb(248, 171, 45);
}

._css-relmodel-btntext {
  margin-left: 2px;
}

._css-relmodel-nametext {
  margin-left: 2px;
}

._css-relmodel-modelname {
  display: flex;
  color: rgba(0, 0, 0, 0.45);
  height: 20px;
  line-height: 20px;
  position: absolute;
  top: 4px;
  left: 13px;
  align-items: center;
}

._css-relmodel-modelviewbtn {
  display: flex;
  color: rgba(0, 0, 0, 0.65);
  height: 20px;
  line-height: 20px;
  position: absolute;
  bottom: 4px;
  left: 13px;
  align-items: center;
}

._css-relmodel-rpart {
  height: 120px;
  position: relative;
  flex: 1;
}

._css-relmodel-imgarea {
  display: flex;
  align-items: center;
  width: 100%;
  padding-left: 16px;
  padding-right: 16px;
  box-sizing: border-box;
  margin-top: 12px;
}

._css-relmodel-img {
  width: 160px;
  height: 120px;
  box-sizing: border-box;
  border-radius: 2px;
  background-image: url(/static/img/test.5981312.png);
  background-repeat: no-repeat;
}

._css-prop-text {
  font-size: 14px;
  color: rgba(0, 0, 0, 0.65);
  height: 18px;
  line-height: 18px;
  margin-left: 2px;
}
._css-prop-text:hover {
  color: #1890ff;
}
.icon-interface-folder:hover {
  color: #1890ff;
}

._css-prop-icon {
  height: 16px;
  width: 16px;
  font-size: 16px;
  color: rgba(0, 0, 0, 0.65);
}

._css-prop-deadline {
  height: 20px;
  min-width: 68px;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  cursor: pointer;
  user-select: none;
}

._css-rel-localfile {
  height: 20px;
  min-width: 40px;
  box-sizing: border-box;
  /* margin-left: 24px; */
  display: flex;
  align-items: center;
  cursor: pointer;
  user-select: none;
}

._css-rel-linkfile {
  height: 20px;
  min-width: 68px;
  box-sizing: border-box;
  margin-left: 24px;
  display: flex;
  align-items: center;
  cursor: pointer;
  user-select: none;
}

._css-rel-btns {
  height: 20px;
  margin-top: 14px;
  display: flex;
  align-items: center;
  border-bottom: 1px dashed rgba(217, 217, 217, 1);
  padding-bottom: 14px;
  margin-left: 16px;
  margin-right: 16px;
}

/* 选择日期时间样式（在添加本地附件右边时） */
._css-title-input-deadline {
  font-size: 12px;
  color: rgba(0, 0, 0, 0.65);
  height: 20px;
  line-height: 20px;
  text-align: right;
  display: flex;
  align-items: center;
}

._css-title-input-ta {
  outline: none;
  resize: none;
  border: none;
  background-color: transparent;
  width: 100%;
  /*height: calc(100% - 36px);*/
  padding: 9px 12px 0px 12px;
  box-sizing: border-box;
  font-size: 14px;
  color: rgba(0, 0, 0, 0.65);
  text-align: left !important;
}
._css-title-input-in {
  height: 100%;
  width: 100%;
  /* background-color: rgba(0, 0, 0, 0.02); */
  position: relative;
}
._css-title-input {
  /*height: 100px;*/
  /*margin-top: 24px;*/
  width: 100%;
  padding-left: 16px;
  padding-right: 16px;
  box-sizing: border-box;
  max-height: 200px;
  overflow: auto;
}
._css-all-createissue-in {
  width: 410px;
  min-height: 294px;
  background: rgba(255, 255, 255, 1);
  box-shadow: 0px 13px 24px -17px rgba(11, 41, 62, 0.8);
  border-radius: 4px;
}
.slot-btn {
  margin-left: 8px;
  font-size: 14px;
  cursor: pointer;
  background: url('../../assets/images/markup.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
  width: 18px;
  height: 18px;
}

._css-all-createissue {
  display: flex;
  align-items: center;
  justify-content: space-around;
  background-color: rgba(0, 0, 0, 0.15);
  position: fixed;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
}
/* 后续添加样式 */
/* 默认类型下拉样式 */
._css-rel-localfile-type {
  position: relative;
  margin-top: 4px;
}
/* ._css-rel-localfile-type:hover {
  color: #1890ff;
} */
.The-default-category-box {
  position: absolute;
  top: -18px;
  left: 96px;
  width: 120px;
  background: rgba(255, 255, 255, 1);
}
.The-default-category-box > ul {
  padding: 0;
  margin: 0;
  list-style: none;
  height: 100%;
}
.The-default-category-box > ul > li {
  width: 100%;
  height: 40px;
  font-size: 14px;
  /* font-family: PingFangSC-Regular; */
  font-weight: 400;
  line-height: 40px;
  text-align: center;
  margin-top: 2px;

  overflow-x: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.The-default-category-box > ul > li:hover {
  background: rgba(0, 0, 0, 0.04);
  color: #1890ff;
}
.icon-interface-folder,
.icon-interface-model-picture {
  font-size: 20px;
}
.icon-interface-model-picture:hover {
  color: #1890ff;
}
.icon-interface-list-fill {
  font-size: 16px;
}
.CompsCreateIssue-image {
  width: 100%;
  height: 270px;
  position: relative;
}

/* 获取当前截图样式（老版本） */
._css-image-img{
  width: 100%;
  height: 100%;
}
.model-screenshot{
  width: 100%;
  height: 270px;
}
.model-screenshot>._css-image-img{
  width: 100%;
  height: 100%;
}
/* 获取当前截图样式（新版本） */
._css-image-img-bg{
  width: 100%;
  height: 270px;
}
.el-icon-circle-close{
  width:25px;
  color: red;
} 

/* 发起问题小图盒子样式 */
.small-model{
  display: flex;
  width: 100%;
  height: 80px;
  border-radius:0px 0px 2px 2px;
  background:rgba(0,0,0,0.45);
  position: absolute;
  bottom: 0;
  align-items: center;
}
.small-model-img{
  width: 50px;
  height: 50px;
  background: rgba(255,255,255,1);
  margin-left: 12px;
  overflow: hidden;
  cursor:pointer;
  transition: all 300ms;
  box-shadow:0px 1px 1px 0px rgba(0,21,41,0.12);
  border-radius:2px;
  position: relative;
}
.small-model-img:hover{
  transform: scale(1.3);
}
._css-model-micon {
    position: absolute;
    right:4px;
    top:2px;
    color:#1890FF;
    width:8px;
    height:8px;
    font-size:8px !important;
}

/* 新版添加图片删除图标样式 */
.small-model-img{
  position: relative;
  overflow: visible !important;
}
.small-model-img:hover ._css-deletecurrentbtn-small-pic{
  display: block;
}
._css-deletecurrentbtn-small-pic{
  width:20px;
  font-size:20px;
  height:20px;
  border-radius: 10px;
  margin-top: 0;
  margin-left: 30px;
  display: none;
  position: absolute;
  top: -5px;
  right: -5px;
  color: rgba(0, 0, 0, 0.65)
}
._css-deletecurrentbtn-small-pic:hover{
  color: #1890FF;
}
.issue-btn{
  text-align: end;
  margin: 16px 14px 14px 16px;
}
.btn-css{
  display: inline-block;
  line-height: 40px;
  padding: 0 25px;
  font-size: 14px;
  margin-right: 10px;
}
.btn-ok{
  color: #fff;
  background: #1890ff!important;
}
</style>
