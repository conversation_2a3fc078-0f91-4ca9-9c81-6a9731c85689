<template>
 
  <div 
  v-loading="m_globalloading && !bShowingConfig"
  id="id_issue_all"
  element-loading-text="加载中"
  class="_css-issue-all" @click="hidehover($event)">
    <div class="btn-initiating-header">{{ headerTitle }}</div>
    <template v-if="!bShowingConfig">
    <div class="issue-content">
      <!-- 上部分 -->
      <div class="_css-issue-head">
        <div class="_css-head-left">
            <!-- 发起问题改为弹出按钮，一个是发起问题，一个是导入问题 -->
            <div
            @click="switchIssueBtns($event)"
            class="_css-createissue-btnparent "
            :class="{'_css-dis': extdata.newissuedis}"
            >
              <div>发起问题</div>
              <div
              v-if="bShowIssueBtns == true"
              class="_css-issue-btns" >
                <div
                @click="btncreateissue_click"
                class="_css-issue-btn" >直接发起</div>
                <div
                  @click="btnimportissue_click($event)"
                class="_css-issue-btn" >导入问题</div>
              </div>
            </div>
            <!-- //发起问题改为弹出按钮，一个是发起问题，一个是导入问题 -->

            <!-- 归档按钮  组件形式 -->
            <div class="_css-settingchecboxorswitch" >
                  <CompsEloSwitch
                  v-bind:obj="{checkstate:extdata._containsArchive==0?0:1}"
                  v-bind:editable="true"
                  openval="显示归档"
                  closeval="隐藏归档"
                  @onchanging="archive_click"
                  :outerwidth="77"
                  ></CompsEloSwitch>
              </div>

                <div
                class="icon-interface-filter _css-issue-filterissue css-cp"
                @click.stop="filtration_click"
                >
                </div>

            <!--排序按钮 -->
              <el-dropdown trigger="click">
                <span class="el-dropdown-link">
                  <!-- <i class="el-icon-arrow-down el-icon--right"></i>排序 -->
                  <i class="icon-interface-problem-sorting"></i>
                </span>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item @click.native="sortlist(0)">默认</el-dropdown-item>
                  <el-dropdown-item @click.native='sortlist(1)'>发起时间(最新)</el-dropdown-item>
                  <el-dropdown-item @click.native='sortlist(2)'>发起时间(最早)</el-dropdown-item>
                  <el-dropdown-item @click.native='sortlist(3)'>剩余时间(最多)</el-dropdown-item>
                  <el-dropdown-item @click.native='sortlist(4)'>剩余时间(最少)</el-dropdown-item>
                  <el-dropdown-item @click.native='sortlist(5)'>问题状态(正序)</el-dropdown-item>
                  <el-dropdown-item @click.native='sortlist(6)'>问题状态(倒序)</el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
              <div v-if="extdata.newissuedis" class="is-set-icon" @click.stop="setLabelFun"></div>
              <div class="_css-issueitem-endtimeval icon-interface-download-fill _css-issue-filterissue" @click.stop="downloadIssusExport"></div>


            <!-- 点击过滤 弹出遮罩 -->
            <transition name="maskfade">
              <div v-show="show" class="sq_mask"></div>
            </transition>
            <!-- 点击过滤 弹出抽屉 -->
            <div id="sq_drawer" ref="sq_drawer">
              <el-row>
                <el-col :span="24"><div class="grid-content bg-purple-dark">
                  <div class="grid-content-header bg-purple-dark-header left_back" @click="filtration_click">过滤器
                      </div>
                    </div>
                  </el-col>
              </el-row>

              <div class="_css-leftfilter">
                  <div class="_css-leftfilter-top _css-leftfilter-part">

                  <p class="_checkoutitems-title">标签</p>
                  <div class="_checkoutitems-list">
                    <div class="checkoutitems"
                      @click="checkouttag('blanktagid')"
                      :class="selectedTagCheckbox.indexOf('blanktagid') == -1?'':'checkoutitem'"
                      >
                      <div class="checkoutitemname checkoutitemname_type"

                          :style="getTagStyle('rgba(0, 0, 0, 1)', 'black', '#fff')"
                      >{{'无标签'}}</div>
                      <div class="icon-checkbox-Selected-Disabled-dis-blue checkoutbg"></div>
                    </div>
                    <div class="checkoutitems"
                      :key="tag.it_guid+'AllRelationTagObjs'"
                      v-for="(tag, index) in AllRelationTagObjs"
                      @click="checkouttag(tag.it_guid)"
                      :class="selectedTagCheckbox.indexOf(tag.it_guid) == -1?'':'checkoutitem'"
                      >
                      <div class="checkoutitemname checkoutitemname_type"
                      :class="index%2 == 0 ? 'checkoutitemname_type-odd' : 'checkoutitemname_type-even'"
                      :style="getTagStyle(tag.it_color)"
                      >{{tag.it_name}}</div>
                      <div class="icon-checkbox-Selected-Disabled-dis-blue checkoutbg"></div>
                    </div>
                  </div>

                  </div>
                  <div class="_css-leftfilter-bottom _css-leftfilter-part">
                    <p class="_checkoutitems-title">状态</p>
                    <div class="_checkoutitems-list">
                      <div class="checkoutitems"
                        :key="item.ItemdetailId+'issueStatusItemsStatus'+index"
                        v-for="(item,index) in extdata.issueStatusItemsStatus"
                        @click="checkout(item.ItemDetailId)"
                        :class="selectedStatusCheckoutbox.indexOf(item.ItemDetailId) == -1?'':'checkoutitem'"
                        >
                        <div class="checkoutitemname checkoutitemname_type"
                        :class="index%2 == 0 ? 'checkoutitemname_type-odd' : 'checkoutitemname_type-even'"
                        :style="{background:`${item.ItemName == '关闭'? 'rgba(250,84,28,1)':item.ItemCode}`}"
                        >{{item.ItemName}}</div>
                        <div class="icon-checkbox-Selected-Disabled-dis-blue checkoutbg"></div>
                      </div>
                    </div>
                  </div>
                </div>

            </div>
            <!-- //两个按钮：发起问题，过滤 -->
        </div>   
        <div class="_css-head-right">
          <!-- 问题标题搜索框 -->
            <CompsUsersInput
              placeholder="搜索问题标题"
              iconclass="icon-interface-search"
              width="240px"
              :fontSize="14"
              @oninput="_onissueinput"
            ></CompsUsersInput>
            <!-- //问题标题搜索框 -->
        </div>
        
      </div>
      <!-- //上部分 -->
      <!-- 下部分 -->
      <div class="_css-issue-body">
        <!-- 内部，上下左右各有24px空白 -->
        <div 
        v-if="func_hasdata()"
        id="_id_issue_body_scroller" class="_css-issue-body-in"
        @scroll="leave_tree_onbodyscroll"
        >
          <!-- 宽度为292px的项 -->
          <div
            class="_css-issue-statusitem _css-nomask"
            :class="getParentClass(item)"
            v-for="(item,index) in extdata.issueStatusItems"
            v-show="item.IssueItems.length > 0"
            :key="item.ItemDetailId + 'issueStatusItems'"
          >
            <div
            class="_css-issue-statusitemhead"
            :class="index%2 == 0? '_css-issue-statusitemhead-even':'_css-issue-statusitemhead-odd'"
            :style="{background:item.ItemCode}"
            >
              <div class="_css-issue-statusitemhead-in">{{item.ItemName}}</div>
            </div>
            <!-- 问题分类body部分 css-miniscroll 加滚动条  css-littlescroll 隐藏滚动条（暂时去掉，实现可以告知用户本列是否可以上拉下拉）-->
            <div
              :id="'id_'+item.ItemDetailId"
              class="_css-issue-statusitembody css-miniscroll "
              :class="{'css-littlescroll':item.isshowscroll==false}"
              @scroll="handleScroll2($event, item)"
              :data-parentid="'id_'+item.ItemDetailId"
              :style="{background:item.ItemCode.replace(/\d\)/,'0.1)')}"
            >
              <!-- 一个问题追踪 -->
              <div
                @click="switchDetail(item, item2, $event)"
                @mouseenter.stop="rightpartenter(item2)"
                @mouseleave.stop="rightpartleave(item2)"
                class="_css-issue-issueitem"
                :class="{'_css-rightopen':item2.RightIsOpen,'Not-archiving': item2.DeleteMark != 2,'archiving': item2.DeleteMark == 2}"
                v-for="item2 in item.IssueItems"
                :key="item2.IssueId + 'IssueItems'"
                v-show="IfIssueShow(item2)"
              >

                <!-- 问题列表轮播 -->
                <div class="slideshow-picture-list" v-if="currentIssueHasImage2(item2)">
                  <!-- <div class="slideshow-picture-list-title">{{bgpicture_title}}</div> -->
                  <div class="slideshow-top-list">
                    <div class="slideshow-top-list-ul">
                      <div class="slideshow-top-list-li css-h100"
                        :style="computeDetailMiniPic_url(item2.bgpicture_src)">
                        <!-- <img class="_css-image-img" :src="item2.bgpicture_src" alt=""> -->
                      </div>
                    </div>
                  </div>
                  <div class="slideshow-bottom-list" >
                    <div class="slideshow-bottom-list-ul css-miniscroll css-littlescroll">

                    <!-- 模型发起问题时带过来的模型图片 -->
                    <div class="slideshow-bottom-list-li _css-model-micon-container"
                    @click.stop="click_bgpicture(item2, item2.ImageUrl)"
                    v-show="item2.ImageUrl"
                      :style="computeDetailMiniPic_url(item2.ImageUrl)">
                      <div class="_css-model-micon icon-interface-model_list" ></div>
                    </div>
                    <!-- //模型发起问题时带过来的模型图片 -->

                    <!-- 后续自己添加的小图 -->
                    <div
                    class="slideshow-bottom-list-li"
                    v-for="(item,bf_index) in item2.base_files" :key="index+'-'+item.bf_guid + 'item2_base_files'+bf_index"
                    :style="getstyle_by_bfpath(item.bf_path)"
                    @click.stop="click_bgpicture_window(item2, item.bf_path)"
                    >
                    </div>
                    <!-- //后续自己添加的小图 -->

                    </div>
                  </div>
                </div>

                <div class="_css-issue-issueitemareatop css-miniscroll">{{item2.Title}}</div>
                <div class="_css-issue-issueitemareamiddle">剩余时间：{{item2.EndDate | fltlefttime}}</div>

                <div class="archived-file icon-interface-yiguidang" v-show="item2.DeleteMark == 2">
                  <!-- <img src="../../../../static/images/archived.png" alt=""> -->
                  </div>

                <div class="_css-issue-issueitemareabottom">
                  <div class="_css-issue-issueitembleft">
                    <div class="_css-issue-type">
                      <!-- 问题分类显示 -->
                      <div class="_css-issue-typeitem"
                        :style="{background:`${item2.IssueStatusText == '关闭'? 'rgba(250,84,28,0.1)':item2.IssueStatusColor.replace(/\d\)/,'0.1)')}`,
                                  border:`${item2.IssueStatusText == '关闭'? '1px solid rgba(250, 84, 28, 0.45)':`1px solid ${item2.IssueStatusColor}`}`,
                                  color:`${item2.IssueStatusText == '关闭'? 'rgba(250, 84, 28, 1)':`${item2.IssueStatusColor}`}`}">
                        <!-- <div class="_css-issue-typeitemtext">{{item2.IssueTypeText || '无分类'}}</div> -->
                        <el-tooltip
                          popper-class="css-no-triangle"
                          effect="dark"
                          :content="item2.IssueStatusText || '无分类'"
                          placement="bottom"
                        >
                        <div class="_css-issue-typeitemtext">{{item2.IssueStatusText || '无分类'}}</div>
                        </el-tooltip>
                      </div>
                      <!-- //问题分类显示 -->
                    </div>
                    <div class="_css-issue-icons">
                      <!-- 32 * 20 (图标+个数)-->
                      <div class="_css-issue-attachitem">
                        <div class="_css-issue-attachitemicon">
                          <div class="icon-interface-attachment"></div>
                        </div>
                        <div class="_css-issue-attachitemnum">{{item2.FileCount}}</div>
                      </div>
                      <!-- //32 * 20 (图标+个数)-->

                      <!-- 32 * 20 (图标+个数)-->
                      <div class="_css-issue-attachitem">
                        <div class="_css-issue-attachitemicon">
                          <div class="icon-interface-comment"></div>
                        </div>
                        <!-- extdata.detaildata.comments.length    item2.TalkCount -->
                        <div class="_css-issue-attachitemnum">{{item2.TalkCount}}</div>
                      </div>
                      <!-- //32 * 20 (图标+个数)-->
                    </div>
                  </div>

                  <div class="_css-issue-issueitembright father"
                  @mouseenter="entercreatoricon($event, item2)"
                  @mouseleave="leavecreatoricon($event)"
                  >
                    <div>{{item2.CreateUserName | setAbbreviation}}</div>
                  </div>
                </div>
              </div>
            </div>
            <div
            :id="'id_rp_' + item.ItemDetailId"
              class="_css-rightpart"
              @click="closerightpart($event)"
              v-loading="extdata.isloadingcurrentdetail"
              element-loading-text="加载中"
              @mouseenter.stop="rightpartenter(item)"
              @mouseleave.stop="rightpartleave(item)"
            >
              <div class="_css-rightparthead">
                <div class="_css-rightpartheadleft">
                  <div
                    class="_css-tc-detail _css-tc-item"
                    :class="{'_css-tc-clicked':extdata.detailshowtype == 'dst_detail'}"
                    @click.stop="changedetailshowtype('dst_detail')"
                  >
                    详情
                  </div>
                  <div
                    class="_css-tc-comments _css-tc-item"
                    :class="{'_css-tc-clicked':extdata.detailshowtype == 'dst_comments'}"
                    @click.stop="changedetailshowtype('dst_comments')"
                  >
                    评论
                    <div class="_css-tc-number">{{extdata.detaildata.comments.length}}</div>
                  </div>
                  <!-- 详情页面活动区域 -->
                  <div
                    class="_css-tc-activities _css-tc-item"
                    :class="{'_css-tc-clicked':extdata.detailshowtype == 'dst_activities'}"
                    @click.stop="changedetailshowtype('dst_activities')"
                  >
                    活动
                    <div class="_css-tc-number">{{activitiesList.length}}</div>
                  </div>

                </div>
                <div class="_css-rightpartheadright">
                  <div
                    @click="closeIssueDetail(item, $event)"
                    class="_css-closebtn icon-suggested-close"
                  ></div>
                  <!-- 点击列表按钮（×号左边的）出现层级选择框 -->

                  <!-- 树型下拉（二级） -->
                  <div class="drop-down-tree icon-interface-set_se" @click.stop="open_tree_box" v-if="extdata.detaildata.fulldata.isIssueManager">更多</div>
                          <!-- v-if="extdata.detaildata.fulldata.isIssueManager" 选中一个问题 赋值进去  然后下面fulldata下面得issueObj.DeleteMark 就不用再去定义了-->
                    <div class="drop-down-tree-T" v-click-out-close="open_tree_box" v-show="tree_show" @mouseenter="enter_tree_common($event)" v-if="extdata.detaildata.fulldata.isIssueManager">
                      <div class="drop-down-tree-P archive-problem" @click="ispmorissuecreator()" v-if="extdata.detaildata.fulldata.issueObj.DeleteMark == 0">
                        <div class="drop-down-tree-P-left icon-interface-folder"></div>
                        <div class="drop-down-tree-P-center">归档问题</div>
                        <div class="drop-down-tree-P-right"></div>
                        </div>

                      <div class="drop-down-tree-P archive-problem" @mouseenter="enter_tree_common($event)" @click="remove_ispmorissuecreator()" v-if="extdata.detaildata.fulldata.issueObj.DeleteMark == 2">
                        <div class="drop-down-tree-P-left icon-interface-folder"></div>
                        <div class="drop-down-tree-P-center">移出归档</div>
                        <div class="drop-down-tree-P-right"></div>
                      </div>

                      <div class="drop-down-tree-P remove-problem" @mouseenter="enter_tree_common($event)" @click="removeselfissue()">
                        <div class="drop-down-tree-P-left icon-interface-model-delete"></div>
                        <div class="drop-down-tree-P-center">删除问题</div>
                        <div class="drop-down-tree-P-right"></div>
                        </div>
                      <div 
                      :title="extdata.detaildata.IssueTypeText"
                      class="drop-down-tree-P problem-Type" @click.stop="" @mouseenter="enter_tree_Type($event)" v-if="extdata.detaildata.fulldata.issueObj.DeleteMark == 0">
                        <!-- @mouseleave="leave_tree_Type($event)" -->
                        <div class="drop-down-tree-P-left icon-interface-list-fill"></div>
                        <div class="drop-down-tree-P-center">{{extdata.detaildata.IssueTypeText}}</div>
                        <div class="drop-down-tree-P-right icon-arrow-right_outline"></div>
                        <!-- 下拉二级菜单 -->
                        </div>
                      <div 
                      :title="extdata.detaildata.IssueStatusText"
                      class="drop-down-tree-P problem-Status" @click.stop="" @mouseenter="enter_tree_Status($event)" v-if="extdata.detaildata.fulldata.issueObj.DeleteMark == 0">
                        <!-- @mouseleave="leave_tree_Status($event)" -->
                        <div class="drop-down-tree-P-left icon-interface-cloud-download_could"></div>
                        <div 
                        class="drop-down-tree-P-center">{{extdata.detaildata.IssueStatusText}}</div>
                        <div class="drop-down-tree-P-right icon-arrow-right_outline"></div>
                        <!-- 下拉二级菜单 -->
                        </div>
                    </div>
                </div>
              </div>

              <div class="_css-rightpartbody css-miniscroll">
                <template v-if="extdata.detailshowtype == 'dst_comments'">
                  <div class="_css-commentdiv-container">
                    <div data-comment="64 t16" class="_css-comment-textinput">
                      <div class="_css-comment-textinput-in">
                        <compsAnimateInput
                            v-if="item.ItemDetailId == extdata.detaildata.fulldata.issueObj.IssueTypeID"
                            :ref="'commentAtInput'"
                            v-model="add_comment_text"
                            name="Input"
                            id="myInput"
                            :dataList="userList_currentIssueDetail"
                            height="120px"
                            needAt
                            placeholder="评论内容，可以@参与人">
                          </compsAnimateInput>
                      </div>
                    </div>

                    <div class="_css-comment-subbtn">
                      <CompsEloButton
                        :height="24"
                        :width="60"
                        color="#fff"
                        text="评论"
                        :fontSize="12"
                        @onclick="add_comment"
                      ></CompsEloButton>
                      <div
                        class="_css-comment-attachbtn-new"
                        @click.stop="addimgcomment"
                      >
                        <span class="_css-comment-attachbtn-new-icon icon-interface-image"></span>添加图片
                      </div>
                    </div>

                    <div class="_css-comments css-miniscroll">
                      <div class="_css-comments-in">
                        <div
                          class="_css-comments-item"
                          v-for="comm in extdata.detaildata.comments"
                          :key="comm.issue_talkid + 'issue_talkid'"
                        >
                          <template v-if="comm.contenttype == 'text'">
                            <div class="_css-commentitem-headiconline">
                              <div
                                class="_css-commentitem-headicon"
                              >{{comm.realname | setAbbreviation}}</div>
                              <div class="_css-commentitem-headname">{{comm.realname}}</div>
                              <div class="_css-commentitem-time">{{comm.createdate | fltpasttime}}</div>
                              <div
                                class="_css-commentitem-rm icon-interface-delete"
                                v-if="equaluser(comm) || extdata.detaildata.fulldata.isIssueManager"
                                @click.stop="removeselfcomment(comm.issue_talkid)"
                              ></div>
                            </div>
                            <div class="_css-filenameorcontent">{{comm.content}}</div>
                          </template>
                          <template v-else-if="isimage(comm.contenttype)">
                            <div class="_css-commentitem-headiconline">
                              <div
                                class="_css-commentitem-headicon"
                              >{{comm.realname | setAbbreviation}}</div>
                              <div class="_css-commentitem-headname">{{comm.realname}}</div>
                              <div class="_css-commentitem-time">{{comm.createdate | fltpasttime}}</div>
                              <div
                                class="_css-commentitem-rm icon-interface-delete"
                                v-if="equaluser(comm)"
                                @click.stop="removeselfcomment(comm.issue_talkid)"
                              ></div>
                            </div>
                            <div class="_css-filenameorcontent _css-fname">{{comm.content}}</div>
                            <div class="_css-fileimg _css-cp" @click="openImageOrModel_new(imgurlbycommcontent(comm.contenttype), $event,'comm')">
                              <img
                                :src="imgurlbycommcontent(comm.contenttype)"
                                class="_css-fileimg-img"
                              >
                            </div>
                          </template>
                        </div>

                      </div>
                    </div>
                  </div>
                </template>

                <template v-if="extdata.detailshowtype == 'dst_detail'">
                  <!-- 高度为96的显示多个时间的区域 -->
                  <div class="_css-issueitemarea">
                    <div class="_css-issueitemarea-in">
                      <!-- 从左向右依次显示：创建时间，结束时间等 -->
                      <div class="_css-issueitem-createtime">
                        <div class="_css-issueitem-timetitle">创建时间</div>
                        <div class="_css-issueitem-timevalue _css-issueitem-timevalue-cj">{{extdata.detaildata.createtime}}</div>
                      </div>
                      <div class="_css-issueitem-endtime">
                        <div class="_css-issueitem-timetitle">截止日期</div>
                        <div
                        v-if="!extdata.newissuedis && extdata.detaildata.fulldata.isIssueManager && extdata.detaildata.fulldata.issueObj.DeleteMark != 2"
                        class="_css-issueitem-timevalue _css-issueitem-endtimeval issue-details-deadline">
                          <el-date-picker
                            @change="change_time"
                            v-model="extdata.detaildata.endtime"
                            format="yyyy-MM-dd HH:mm"
                            type="datetime"
                            prefix-icon="icon"
                            :picker-options="pickerOptions"
                            placeholder="选择日期时间">
                          </el-date-picker>
                        </div>
                        <div
                        v-else
                        class="_css-issueitem-timevalue _css-issueitem-endtimevalro issue-details-deadline">
                        {{extdata.detaildata.endtime}}
                        </div>
                      </div>
                      <!-- 加入剩余时间 -->
                    <div class="_css-issueitem-surplustime">
                        <div class="_css-issueitem-timetitle">剩余时间</div>
                        <!-- <div class="_css-issueitem-timevalue _css-issueitem-timevalue-sy">{{selectitem.EndDate | fltlefttime}}</div> -->
                        <div class="_css-issueitem-timevalue _css-issueitem-timevalue-sy">{{extdata.detaildata.endtime | fltlefttime}}</div>
                      </div>
                      <!-- //从左向右依次显示：创建时间，结束时间等 -->
                    </div>
                  </div>
                  <!-- //高度为96的显示多个时间的区域 -->

                  <!-- 显示标签的区域，此区域显示前提是有权限编辑，或存在标签。注意标签的新增和删除，受权限控制 -->
                  <div class="_css-detail-tags" >
                    <div class="_css-detail-tags-in" >
                      <div class="_css-detail-tagtitle">标签</div>
                      <div class="_css-detail-taglist">
                        <div
                        :style="computeTagStyle(tagi)"
                        :title="tagi.it_name"
                        v-for="tagi in issuedetailTags"
                        :key="tagi.it_guid + 'issuedetailTags'"
                        class="_css-detail-tagi">
                          <div class="_css-detail-tagi-text" >{{tagi.it_name}}</div>
                          <div
                          @click="removeTagFromIssue(tagi, $event)"
                          :style="{'color': tagi.it_color}"
                          class="_css-detail-tagi-delbtn icon-suggested-close_circle" ></div>
                        </div>
                        <div
                        @click="addIssueTag($event)"
                        class="_css-detail-tagi _css-tag-addbtn">
                          <div class="_css-tagbtn-icon icon-suggested-plus" ></div>
                          <div class="_css-tagbtn-text" >添加标签</div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <!-- //显示标签的区域，此区域显示前提是有权限编辑，或存在标签。注意标签的新增和删除，受权限控制 -->

                  <!-- 最小高度为92的显示参与人的区域 -->
                  <div class="_css-detail-joiners">
                    <div class="_css-detail-joiners-in">
                      <div class="_css-detail-joinerstitle">参与人</div>
                      <!-- 参与人：数据 -->
                      <div class="_css-detail-joinersdata css-miniscroll">
                        <div
                          class="_css-detail-joiner"
                          @click.stop="clickjoiner(user, $event)"
                          v-for="user in extdata.detaildata.joiners"
                          :key="user.Id + 'UserId'"
                        >
                          <div class="_css-detail-joinericon">{{user.RealName | setAbbreviation}}</div>
                          <div class="_css-detail-joinertext">{{user.RealName}}</div>
                          <!-- 当前人员的详情内容，也可进行从参与人列表移除操作 -->
                          <div
                            @click.self.prevent=""
                            class="_css-detail-joinerdetail"
                            :class="{'_css-showingitem': extdata.detaildata.hoveringuserId == user.Id}"
                            :style="{'top':extdata.detaildata.y+'px','left':extdata.detaildata.x+'px'}"
                          >

                            <!-- 第一部分：头像及姓名 -->
                            <div class="_css-juserhead">
                              <div class="_css-juserhead-icon">{{user.RealName | setAbbreviation}}</div>
                              <div class="_css-juserhead-right">
                                <div class="_css-juserhead-rtop">{{user.RealName}}</div>
                                <div class="_css-juserhead-rbottom">{{user.Email || '-'}}</div>
                              </div>
                            </div>
                            <!-- //第一部分：头像及姓名 -->

                            <!-- 第二部分：操作 -->
                            <div class="_css-juserbody"
                                @click="_stopPropagation($event)">
                              <div class="_css-juserbody-top">
                                <div class="_css-juserbody-icon icon-interface-phone"></div>
                                <div class="_css-juserbody-content ">{{user.Mobile || '-'}}</div>
                              </div>
                              <div
                              v-if="extdata.detaildata.fulldata.issueObj.CreateUserId == user.Id"
                              class="_css-juserbody-bottom"
                              @click="_stopPropagation($event)"
                              @mouseenter="extdata.canblur=false"
                              @mouseleave="extdata.canblur=true"
                              >
                                <div class="_css-juserbody-icon "></div>
                                <div class="_css-juserbody-content ">&lt;问题创建者&gt;</div>
                              </div>

                              <div
                              v-else-if="extdata.detaildata.fulldata.isIssueManager && extdata.detaildata.fulldata.issueObj.DeleteMark != 2"
                              class="_css-juserbody-bottom"
                              @click.stop="removefromjoiners(user)"
                              @mouseenter="extdata.canblur=false"
                              @mouseleave="extdata.canblur=true"
                              >
                                <div class="_css-juserbody-icon icon-suggested-close-fill"></div>
                                <div class="_css-juserbody-content ">从参与人中移除</div>
                                <div class="_css-juserbody-righticon icon-arrow-right_outline"></div>
                              </div>

                              <div
                              v-else
                                class="_css-juserbody-bottom css-dis"
                                @click="_stopPropagation($event)"
                                @mouseenter="extdata.canblur=false"
                                @mouseleave="extdata.canblur=true"
                                >
                                <div class="_css-juserbody-icon icon-suggested-close-fill"></div>
                                <div class="_css-juserbody-content ">从参与人中移除</div>
                                <div class="_css-juserbody-righticon icon-arrow-right_outline"></div>
                              </div>

                            </div>
                            <!-- //第二部分：操作 -->
                            <input
                              v-if="extdata.detaildata.hoveringuserId == user.Id"
                              v-onfocus
                              type="text"
                              style="position:absolute;
                            left:2000px;
                            top:2000px;"
                              @blur="closeallpopup($event)"
                            >
                          </div>
                          <!-- //当前人员的详情内容，也可进行从参与人列表移除操作 -->
                        </div>

                        <div
                          @click="begin_addjoiner(ev)"
                          v-if="extdata.detaildata.fulldata.isIssueManager && extdata.detaildata.fulldata.issueObj.DeleteMark != 2"
                          v-show="!extdata.detaildata.isaddingjoiner"
                          class="_css-detail-joiner _css-detail-joineradd"
                        >添加</div>

                      </div>
                      <!-- //参与人：数据 -->
                    </div>
                  </div>
                  <!-- //最小高度为92的显示参与人的区域 -->

                  <!-- 附件区域，弹性高度 -->
                  <div class="_css-detail-attachment">
                    <div id="id_detail_attachoparea" class="_css-detail-attachment-in">
                      <div class="_css-detail-attachment-title">附件</div>
                      <div
                      v-show="!extdata.newissuedis && extdata.detaildata.fulldata.isIssueManager && extdata.detaildata.fulldata.issueObj.DeleteMark != 2"
                      class="_css-detail-attachment-btns">
                        <div class="_css-detail-attachment-btnlocal" @click.stop="addlocalrel">
                          <div class="_css-detail-localattachmenticon icon-interface-folder"></div>
                          <div class="_css-detail-localattachmenttext">添加文件</div>
                        </div>
                        <div class="_css-detail-attachment-btnlocal _css-notfirst" @click.stop="detail_add_basefileimage">
                          <div class="_css-detail-localattachmenticon icon-interface-model-picture"></div>
                          <div class="_css-detail-localattachmenttext">添加图片</div>
                        </div>
                      </div>

                      <!-- 详情页面图片轮播 -->
                      <div class="slideshow-picture" v-if="currentIssueHasImage(selectitem)">
                        <div class="slideshow-picture-title">
                          <div class="_css-deletecurrentbtn css-ml12" v-show="!extdata._detailshowingmodel" ></div>
                          <div class="_css-current-imgtitle css-w100" >{{bgpicture_title}}</div>
                        </div>
                        <div class="slideshow-top">
                          <div class="slideshow-top-ul">

                          <!-- <el-tooltip popper-class="css-no-triangle" effect="dark" :content="extdata._detailshowingmodel?'打开模型':'查看图片'" placement="top">
                          </el-tooltip> -->
                          <div class="slideshow-top-li css-h100"
                            :style="computeDetailMiniPic_url(bgpicture_src)"
                            @mousemove="movetip(extdata._detailshowingmodel, $event)"
                            @mouseenter="showtip(extdata._detailshowingmodel, $event)"
                            @mouseleave="hidetip(extdata._detailshowingmodel)"
                            @click="openImageOrModel_new(bgpicture_src, $event,extdata)"
                            >
                          </div>

                        </div>
                        </div>
                        <div class="slideshow-bottom">
                            <div
                            @click.stop="detail_image_move($event, -30)"
                            :data-parentid="'jsclass_' + item.ItemDetailId"
                            class="_css-prevnext-detail-image _css-prev-detail-image icon-arrow-left_outline" ></div>
                          <div
                          id="id_detail_image_container"
                          class="slideshow-bottom-ul css-miniscroll css-littlescroll"
                          :class="'jsclass_' + item.ItemDetailId"
                          >
                            <!-- 详情页面轮播GZ
                            详情页面轮播GZ -->

                            <!-- 详情模型带来的图片 -->
                            <div class="slideshow-bottom-li _css-model-micon-container"
                            @click.stop="click_bgpicture_details_model()"
                            v-show="selectitem.ImageUrl"
                              :style="computeDetailMiniPic_url(selectitem.ImageUrl)">
                              <div class="_css-model-micon _css-model-m18 icon-interface-model_list" ></div>
                            </div>
                            <!-- 详情中的非模型图片列表，使用 base_files 新属性 -->
                            <template
                            v-if="extdata.detaildata.fulldata.base_files && extdata.detaildata.fulldata.base_files.length > 0"
                            >
                              <div
                              :style="getstyle_by_bfpath(bf.bf_path)"
                              @click.stop="changedetailbigimg(bf)"
                                class="slideshow-bottom-li"
                                v-for="(bf,bf_guid_eq) in extdata.detaildata.fulldata.base_files"
                                :key="index+bf.bf_guid + 'base_files'+bf_guid_eq"
                              >
                                <div
                                  @click.stop="begin_removedocrel(selectitem.IssueId, bf.bf_guid)"
                                  class="_css-deletecurrentbtn-small-pic icon-suggested-close_circle"
                                  :style="{'visibility': extdata.detaildata.fulldata.isIssueManager && extdata.detaildata.fulldata.issueObj.DeleteMark != 2?'visible':'hidden'}"
                                  v-show="!extdata._detailshowingmodel"></div>
                              </div>
                              <div class="_css-detail-imageitem-patch" ></div>
                            </template>
                          </div>
                          <div
                          @click.stop="detail_image_move($event, 30)"
                          class="_css-prevnext-detail-image _css-next-detail-image icon-arrow-right_outline"
                          :data-parentid="'jsclass_' + item.ItemDetailId" ></div>
                        </div>
                      </div>

                      <!-- 附件和图片显示名称 -->
                      <div
                        class="_css-detail-attachment-file"
                        v-for="doc in extdata.detaildata.relationFiles"
                        :key="doc.FileId + 'relationFiles'"
                      >
                        <div class="_css-detail-attachment-fleft">
                          <div
                            class="_css-detail-attachmentitem-icon"
                            :class="$staticmethod.getIconClassByExtname(doc.FileName, doc.FileSize)"
                          ></div>
                          <div class="_css-detail-attachmentitem-name" @click.stop="openDocument(doc)">{{doc.FileName}}</div>
                        </div>
                        <div
                          data-debug="CompsProBimIssueList_vue_1042"
                          v-if="extdata.detaildata.fulldata.isIssueManager && extdata.detaildata.fulldata.issueObj.DeleteMark != 2"
                          class="_css-attach-actbtn _css-detail-attachment-fright icon-suggested-close_circle"
                          @click.stop="begin_removedocrel_D(extdata.detaildata.issueId, doc.FileId)"
                        ></div>
                        <!-- 下载按钮 -->
                        <div
                          @click="downloadDocument(doc, $event)"
                          class="_css-attach-actbtn _css-attach-download icon-interface-download-fill"
                        ></div>
                        <!-- //下载按钮 -->
                      </div>
                    </div>
                  </div>
                  <!-- //附件区域，弹性高度 -->
                </template>

                <!-- 详情页面活动区域 -->
                <template v-if="extdata.detailshowtype == 'dst_activities'">
                  <div class="_css-activities">
                    <div class="_css-activities-container" v-for="item in activitiesList" :key="item.index">
                      <div class="_css-activities-ico">{{item.RealName | setAbbreviation}}</div>
                      <div class="_css-activities-joiner">{{item.RealName}}</div>
                      <div class="_css-activities-operation">{{item.ol_content}}</div>
                      <div class="_css-activities-time">{{item.ol_datetime | fltpasttime}}</div>
                    </div>
                  </div>
                </template>

              </div>
            </div>
          </div>
          <!-- //宽度为292px的项 -->
        </div>
        <!-- //内部，上下左右各有24px空白 -->

        <!-- 内部，上下左右各有24px空白empty -->
        <div
        class="_css-issue-body-in _css-empty"
        v-else>
          暂无数据
        </div>
        <!-- //内部，上下左右各有24px空白empty -->

      </div>
      <!-- //下部分 -->
    </div>

    <!--蒙版 判断当鼠标在extdata.showhover中时 显示灰色蒙版 点击hidehover是点击页面其他位置详情页关闭-->
    <div class="_css-global-hover" v-if="extdata.showhover"></div>

    <!-- <div class="_css-global-hover _css-global-hover_click" v-if="mask" @click="hidehover()"></div> -->

    <!-- 创建问题对话框 -->
    <CompsCreateIssue 
    @set_projectboot_extdata="opendocview"
    v-if="extdata.showcreate" 
    @imagechanged="changeoutertop" 
    :outertop="outertopval" 
    @oncancel="cancelcreate" @onok="createok" 
    :issuestatusitems="extdata.issueStatusItems" 
    :zIndex="3"></CompsCreateIssue>
    <!-- //创建问题对话框 -->

    <!-- 问题列表创建人头像 hover 弹层 -->
    <div class="_css-issue-list-creator-tip"
    :style="getcreatortipstyle()"
    >
      <div class="_css-issue-list-ctip-top" >
        <div class="_css-issue-listctop-iconarea" >
          <div class="_css-issue-liconarea-icon " >{{extdata.CreatorRealName | setAbbreviation}}</div>
          <div class="_css-issue-liconarea-rightpart" >
            <div class="_css-issue-lrightpart-name" >{{extdata.CreatorRealName}}</div>
            <div class="_css-issue-lrightpart-email" >{{extdata.CreatorEmail}}</div>
          </div>
        </div>
      </div>
      <div class="_css-issue-list-ctip-bottom" >
        <div class="_css-issue-cbottom-content" >
          <div class="_css-issue-ccontent-icon icon-interface-institution " ></div>
          <div class="_css-issue-ccontent-text" >{{extdata.CreatorComFullName}}</div>
        </div>
      </div>
    </div>
    <!-- //问题列表创建人头像 hover 弹层 -->

    <CompsSimpleSearchList
        v-if="isaddingmember"
        @oncancel="isaddingmember = false"
        title="添加参与人"
        :projectId="$staticmethod._Get('organizeId')"
        showType="project"
        :zIndex="3"
        @onok="addToRole_OK"
        :willnotshowUsers="extdata.detaildata.joiners"
      ></CompsSimpleSearchList>

  <!-- 点击addlocalpic添加本地附加 限制图片文件 -->
 <input
      type="file"
      style="display:none"
      id="id_detail_add_basefileimage"
      accept="image/*"
      @click.stop="_stopPropagation($event)"
      @change="detail_basefileimage_change()"
    />

    <input
      type="file"
      style="display:none"
      id="id_CompsCreateIssue_File"
      accept="image/*"
      @click.stop="_stopPropagation($event)"
      @change="fileEditIssueChange()"
    />

  <!-- 点击addlocalrel添加本地附加 不限制图片文件  为所有文件 -->
  <input
      type="file"
      style="display:none"
      id="id_EditIssue_File"
      @click.stop="_stopPropagation($event)"
      @change="fileControlChange()"
    >

    <input
    @click="_stopPropagation($event)"
      type="file"
      style="display:none"
      id="id_AddComment_File"
      accept="image/*"
      @click.stop="_stopPropagation($event)"
      @change="fileAddCommentChange()"
    >

   

    <!-- <bim-viewer
      ref="ref_bimviewer"
      v-if="showModelView"
      :BIM_Session="BIM_Session"
      :ProjectID="ProjectId"
      :ModelID="SelModelObj.ID"
      :modelName="SelModelObj.Name"
      :SelModel="SelModelObj"
      :autoSetView="compdata_autoSetView"
      @ItemopenModelInfo="openModelInfo"
      @setModelInfoIndex="setTranIndex"
      @CloseModelView="onmodelview_close"
      @notmtr_onload="issue_bimviewerload"
      class="mv">
    </bim-viewer> -->
    <scene-management-detail
      :ProjectIDProps='ProjectIDProps'
      :userIDProps='userID'
      :SceneIdProps='sceneIdProps'
      :sceneDataJson="sceneDataJson"
      :sceneSelectElements="sceneSelectElements"
      :markupName="markupName"
      @closeDetail="onmodelview_close"
      fromPage="issue"
      v-if="showModelView"
    ></scene-management-detail>

    <!-- <ModelInfo class="mi"
    v-if="showModelInfo"
    :lastOpenVersionNo="lastOpenVersionNo"
    :ProjectID='ProjectId'
    :ModelID="SelModelObj.ID"
    :Phase="(SelModelObj.Phase || '').toString()"
    :Creator="SelModelObj.Creator"
    :CreateTime="SelModelObj.CreateTime"
    :IsMerge="SelModelObj.IsMerge"
    :tranIndex="tranIndex"
    :SelModel="SelModelObj"
    @openVersion="openModel_Version"
    @CloseModelInfo='showModelInfo=false;
    IsDialogCovery=false;'>
    </ModelInfo> -->

    <!-- 问题详情中模型或普通图片的tooltip -->
    <div class="_css-tooltip" :style="computetipstyle()">
      <div class="_css-tiptext">
        {{imageormodeltip.text}}
      </div>
    </div>
    <!-- //问题详情中模型或普通图片的tooltip -->

    <!-- 问题详情分类的下拉菜单二级 -->
    <div class="drop-down-tree-P-Slist" :style="enterstatus(0)" v-if="listhover_type && tree_show"
    @mouseleave="leave_tree_Type($event)"
    >
      <div 
      :title="issueStatusItemsitem.ItemName"
      class="drop-down-tree-P-Slist-Type"
        v-for="(issueStatusItemsitem,issueStatusItemsindex) in extdata.issueStatusItems"
        :key="issueStatusItemsindex"
        @click="type(issueStatusItemsitem)">{{issueStatusItemsitem.ItemName}}
      </div>
    </div>
    <!-- //问题详情分类的下拉菜单二级 -->

    <!-- 问题详情状态的下拉菜单二级 -->
    <div class="drop-down-tree-P-Slist" :style="enterstatus(4)" v-if="listhover_status && tree_show"
    @mouseleave="leave_tree_Status($event)"
    >
      <!-- @mouseenter="enter_Slist_Status($event)" @mouseleave="leave_Slist_Status($event)" -->
      <div 
      :title="issueStatusItemsitem.ItemName"
      class="drop-down-tree-P-Slist-Status"
          v-for="(issueStatusItemsitem,issueTypeItemsindex) in extdata.issueStatusItemsStatus"
          :key="issueTypeItemsindex"
          @click.stop="status(issueStatusItemsitem)">{{issueStatusItemsitem.ItemName}}
      </div>
    </div>
    <!-- //问题详情状态的下拉菜单二级 -->

    <!-- 问题详情图片预览模态框 蒙版-->
    <div class="_css-bgpicture-shade" v-if="comment_picture_showhover" @click.stop="shade_click">

    <!-- 问题详情图片预览模态框 -->
    <div class="_css-bgpicture-model" v-if="comment_picture_model">
      <div class="_css-bgpicture-model_close icon-suggested-close" @click.stop="comment_picture_model_close"></div>
      <div class="_css-bgpicture-model_img"
      :style="getimgsrcstyle(comment_picture_src)"
      >
      <!-- <img :src="bgpicture_src" alt=""> -->
      </div>
    </div>
    <!-- 问题详情图片预览模态框 -->

    </div>
    <!-- 问题详情图片预览模态框 蒙版-->

    <!-- 问题评论图片预览模态框 -->

        <!-- 问题评论图片预览模态框 蒙版-->
        <div class="_css-bgpicture-shade" v-if="bgpicture_showhover" @click.stop="shade_click">

        <!-- 问题评论图片预览模态框 -->
        <div class="_css-bgpicture-model" v-if="bgpicture_model">
          <div class="_css-bgpicture-model_close icon-suggested-close" @click.stop="bgpicture_model_close"></div>
          <div class="_css-bgpicture-model_img"
          :style="getimgsrcstyle(bgpicture_src)"
          >
          <!-- <img :src="bgpicture_src" alt=""> -->
          </div>
        </div>
        <!-- 问题评论图片预览模态框 -->

        </div>
        <!-- 问题评论图片预览模态框 蒙版-->
    
    <!-- //问题评论图片预览模态框 -->

    <!-- 标签下拉项 -->
    <div
    id="id_taglistoptions"
    class="_css-taglistoptions"
    v-if="tagList_show"
    :style="getTagListStyle()"
    >
      <div class="_css-taglist-options">
        <div
        @click="switchEditingTag(tag.it_guid, $event)"
        class="_css-taglist-options-i"
        v-for="tag in allTags"
        :key="tag.it_guid"
        >
          <div
          :title="tag.it_name"
          :style="computeTagStyle(tag)"
          class="_css-taglist-opti-icon">{{tag.it_name}}</div>
          <div class="_css-taglist-opti-flag "
          :class="{'icon-checkbox-Selected-Disabled-dis':editingTagsContains(tag.it_guid)}"
          ></div>
        </div>
      </div>
      <div class="_css-taglist-btn" >
        <div
        @click="saveTagsForIssue($event)"
        class="_css-taglist-btn-in" >
          确定
        </div>
      </div>
    </div>
    <!-- 标签下拉项 -->

    <!-- 导入问题 -->
    <CompsStepTip2
    mubanimgclass="temp-muban-issueimport"
      :useNewClick="true"
      v-if="bShowingImportTemplate == true"
      width="504"
      @oncancel="bShowingImportTemplate = false"
      @onok="_onimportok"
      @ontemplate="_ontemplate"
      title="导入问题"
    ></CompsStepTip2>
    <!-- //导入问题 -->

    </template>

        <CompsIssueConfig
   v-else
    @onclose="_onclose"
    ></CompsIssueConfig>
    <transition v-show="drawerEditShow">
      <div class="labelSetDialog" ref="labelSetDialog">
        <ComIssueSetLable 
          :tagOptions="allTags"
          @close="setLabelFun"
        ></ComIssueSetLable>
      </div>

    </transition>
  </div>
  <!-- //上：64，下：100% - 64 -->

</template>
<script>
import { EventBus } from "@static/event.js";
import CompsUsersInput from "@/components/CompsAuth/CompsUsersInput";
import CompsEloButton from "@/components/CompsElOverride/CompsEloButton";
import CompsCreateIssue from "@/components/CompsIssue/CompsCreateIssue";
import CompsEloSelect from "@/components/CompsElOverride/CompsEloSelect";
import CompsSimpleSearchList from "@/components/CompsCommon/CompsSimpleSearchList";
import CompsEloSwitch from '@/components/CompsElOverride/CompsEloSwitch';
import compsAnimateInput from "@/components/CompsCommon/compsAnimateInput";
import CompsIssueConfig from '@/components/CompsIssue/CompsIssueConfig';
import CompsStepTip2 from "@/components/CompsCommon/CompsStepTip2";
import sceneManagementDetail from "@/components/Home/ProjectBoot/sceneManagementDetail"
import ComIssueSetLable from "@/components/CompsIssue/ComIssueSetLable"
export default {
  components: {
    CompsUsersInput,
    CompsEloButton,
    CompsCreateIssue,
    CompsEloSelect,
    CompsSimpleSearchList,
    CompsEloSwitch,
    compsAnimateInput,
    CompsIssueConfig,
    CompsStepTip2,
    sceneManagementDetail,
    ComIssueSetLable
  },
  watch:{
    $route(to,from) {
      var _this = this;
      console.log(to.params.showconfig);
      // _this.bShowingConfig = (to.params.showconfig == 1);
      if (to.params.showconfig == "1") {
        console.log('显示配置层');
        _this.bShowingConfig = true;

      } else {
        console.log('隐藏配置层');
        _this.bShowingConfig = false;

      }
    }
  },
  data() {
    return {
      headerTitle: '',
      drawerEditShow: false, // 标签设置显示
      R_lastTime:true,//第一次提交时的状态
      // 问题列表的数据区域正在显示加载中
      m_globalloading: false,

      AllRelationTagObjs: [],        // 所有标签对象
      bShowingImportTemplate: false, // 是否显示导入对话框
      bShowIssueBtns: false,   // 是否显示问题下拉按钮
      detailShowingTypeId: '', // 打开的详情的所属分类
      tagList_x: 0,
      tagList_y: 0,
      tagList_show: false,
      editingTagSelectedIds:[], // 呼出标签菜单后，已经勾选了哪些标签（对于当前问题）。第一次呼出后，此值从当前问题的详情中获取。
      allTags:[], // 当前项目全部的可用问题标签池。
      issuedetailTags:[], // 问题详情的标签数组
      bShowingConfig: true, // 显示配置层界面
      imageormodeltip:{
        inarea:false,
        lasttipH: 0,
        text:'',
        isshowing:false,
        left:0,
        top:0
      },

      outertopval: 0,
      pickerOptions: {},
      showModelView:false,  // 场景显示隐藏
      extdata: {
        timerScroll: null,
        bIsShowCreatorTip: false, // 列表创建人头像的hover是否显示
        CreatorTipLeft: 0,
        CreatorTipTop: 0,
        TreeTipLeft: 0, //树型下拉二级菜单 X轴位置
        TreeTipTop: 0,  //树型下拉二级菜单 y轴位置
        CreatorComFullName: '', // 列表创建人机构名称
        CreatorRealName: '', //列表创建人姓名
        CreatorEmail: '', //列表创建人邮箱
        newissuedis: false, // 无发起问题权限 true没权限  false有权限
        newissueBXQZ: false, // 标签设置权限
        _detailshowingmodel: undefined, // 标识当前详情中，正展示的是模型(true)，文档（false）
        _containsArchive: 0, // 加载时是否包含归档问题(否)
        canblur: false, // canblur
        keyword: "", // 搜索关键字
        issueStatusItems: [],
        issueStatusItemsStatus: [], // 过滤抽屉中状态类型(打开和关闭)
        issueStatusDropItems: [], // 问题详情右上角下拉项的数据源
        showhover: false,
        showcreate: false,
        isloadingcurrentdetail: false, // 正在加载当前问题的详情
        detailshowtype: "dst_detail", // 问题单个条目详情的显示tab选项卡情况
        comment_input_content: "", // 当前正在新增的评论标题。旧版本评论
        detaildata: {
          fulldata:{},
          x: 0, //
          y: 0, // 问题详情中已参与人的详情信息层的位置
          hoveringuserId: "", // 正在hover的参与人。
          joiner_suglist: [], // 问题详情添加参与人时，输入后显示的建议人员列表
          isaddingjoiner: false, // 正在添加（正在输入）参与人姓名以检索，并后续执行添加操作。
          issueId: "",  //问题id
          endtime: "", // 截止日期
          createtime: "", // 创建时间
          IssueStatusText: "", // 问题状态
          comments: [], // 评论数据
          joiners: [], // 问题参与者（目前不包含负责人）
          relationFiles: [] // 关联文档条目
        }
      },
      item:'',
      selectitem:'',
      mask:false,
      comment_picture_src: '', //正在显示的评论图片的路径
      comment_picture_showhover: false, // 是否显示评论图片的遮罩
      comment_picture_model: false, // 是否显示评论图片的预览
      bgpicture_src:'', //详情页面小图路径
      bgpicture_id:'', // 详情页面被点击过的小图FileId
      bgpicture_title:'', //详情页面小图名字
      isaddingmember: false, // 正在添加人员，即正在显示人员备选对话框
      picture:[], //接受传入图片的数组
      selectedTagCheckbox:[],         //选中的标签，不选则显示全部问题
      selectedStatusCheckoutbox:[],   //类型数组
      tree_show:false,  //树型下拉盒子 默认隐藏
      listhover_status:false,  //用v-if控制二级下拉得显隐
      listhover_type:false,
      activitiesList: [], //接收详情页面活动区域消息
      show:false,  //抽屉默认为关闭
      userList: null,  //评论@出来的列表，全的
      userList_currentIssueDetail:[], // 评论@出来的列表，当前问题详情的
      add_comment_text:{
        text:''
      },  //组件添加评论绑定的对象
      iscroll: 0, //参数判断鼠标滚动事件 向上 向下
      bgpicture_showhover: false, //图片预览模态框 蒙版 默认false
      bgpicture_model: false, //图片预览模态框 默认fasle
      ParticipanInfoPtanelDom: null,
      organizeId: '',  // 问题追踪打开模型所需参数==project
      userID: '',  // 问题追踪打开模型所需参数=userID
      sceneIdProps: '',  // 问题追踪打开模型所需参数=sceneID
      sceneDataJson: '',  // 问题追踪打开模型所需参数=场景JSON
      sceneSelectElements: '',  // 问题追踪打开模型所需参数=场景选中的构件
      markupName: '' // 问题追踪打开模型所需参数=批注的名字
    };
  },
  filters: {
    // 格式化已过去的时间，例：12小时前。如果大于1天，则显示日期 yyyy-MM-dd HH:mm
    fltpasttime(input) {
      //debugger;
      var _this = this;
      var now = new Date();
      var inputdate = new Date(input);
      var begintime_ms = Date.parse(now); //begintime 为开始时间
      var endtime_ms = Date.parse(inputdate); // endtime 为结束时间
      var msdiff = begintime_ms - endtime_ms; // 用现在减去过去的时间点

      // UTC时间转为北京时间，时间戳转为时间
    // var utc_datetime = "2017-03-31T08:02:06Z";
      function utc2beijing(utc_datetime) {
            // 转为正常的时间格式 年-月-日 时:分:秒
            var T_pos = utc_datetime.indexOf('T');
            var Z_pos = utc_datetime.indexOf('Z');
            var year_month_day = utc_datetime.substr(0,T_pos);
            var hour_minute_second = utc_datetime.substr(T_pos+1,Z_pos-T_pos-1);
            var new_datetime = year_month_day+" "+hour_minute_second; // 2017-03-31 08:02:06

            // 处理成为时间戳
            timestamp = new Date(Date.parse(new_datetime));
            timestamp = timestamp.getTime();
            timestamp = timestamp/1000;

            // 增加8个小时，北京时间比utc时间多八个时区
            var timestamp = timestamp+8*60*60;

            // 时间戳转为时间
            var beijing_datetime = new Date(parseInt(timestamp) * 1000).toLocaleString().replace(/年|月/g, "-").replace(/日/g, " ");
            return beijing_datetime; // 2017-03-31 16:02:06
        }



      if (msdiff < 0) {
        return input;
      }
      // 计算出天数
      var daydiff = Math.floor(msdiff / (24 * 3600 * 1000));
      msdiff -= daydiff * (24 * 3600 * 1000);
      // 计算出小时
      var hdiff = Math.floor(msdiff / (3600 * 1000));
      msdiff -= hdiff * (3600 * 1000);
      // 计算出分钟
      var mdiff = Math.floor(msdiff / (60 * 1000));
      // debugger
      // 大于1天，直接显示时间
      if (daydiff > 1) {
        // return input;
        return input;//utc2beijing(input);
      } else if (daydiff > 0) {
        return "1天前";
      } else if (hdiff > 0) {
        return `${hdiff}小时前`;
      } else if (mdiff > 0) {
        return `${mdiff}分钟前`;
      } else {
        return "刚刚";
      }
    },

    fltlefttime(input) {
      var _this = this;
      var now = new Date();
      var inputdate = new Date(input);
      var begintime_ms = Date.parse(now); //begintime 为开始时间
      var endtime_ms = Date.parse(inputdate); // endtime 为结束时间
      var msdiff = endtime_ms - begintime_ms;

      if (msdiff < 0) {
        return `已过期`;
      }
      // 计算出天数
      var daydiff = Math.floor(msdiff / (24 * 3600 * 1000));
      msdiff -= daydiff * (24 * 3600 * 1000);
      // 计算出小时
      var hdiff = Math.floor(msdiff / (3600 * 1000));
      msdiff -= hdiff * (3600 * 1000);
      // 计算出分钟
      var mdiff = Math.floor(msdiff / (60 * 1000));
      return `${daydiff}天${hdiff}时${mdiff}分`;
    }, 
  },

  created() {
    // 判断 url 参数：showconfig
    var _this = this;
    if (_this.$route.params.showconfig == 1) {
      _this.bShowingConfig = true;
    } else {
      _this.bShowingConfig = false;
    }

    this.$Bus.$on('UpdateAuth',this.updateAuth)
  },

  mounted() {
    var _this = this;
    this.userID = this.$staticmethod.Get("UserId");
    window.issuevue = _this;
    this.headerTitle = this.$staticmethod._Get("menuText") || '';
    //_this.extdata.newissuedis = !this.$staticmethod.hasSomeAuth('WTXT_Edit') // 发起问题和标签编辑，都是编辑权限
     
    // 加载所有问题的状态 
    _this.loadStatusItemsTypes();
    // 加载项目所有参与人 
    _this.loadsugjoiners('');
    // 加载所有问题类型
    _this.loadStatusItemsStatus();
    this.pickerOptions = {
      disabledDate(time) {
        return time.getTime() <= new Date(_this.extdata.detaildata.createtime).getTime();
      }
    };
    this.setLeftmenuShow();
    this.updateAuth()
    this.handleRoueChange() 
  },
  beforeDestroy() {
    this.$Bus.$off('UpdateAuth',this.updateAuth)
  },
  directives: {
    onfocus: {
      inserted: function(el) {
        issuevue.extdata.canblur = false;
        el.select();
        setTimeout(function() {
          issuevue.extdata.canblur = true;
        }, 50);
      }
    }
  },
  computed: {
    
  },
  methods: { 
    updateAuth() {
      try {
        _this.extdata.newissuedis = !this.$staticmethod.hasSomeAuth('WTXT_Edit') // 发起问题和标签编辑，都是编辑权限
      } catch(e) {}
    },
    // 点击消息进入本页面的处理逻辑
    handleRoueChange() {
      const urlChangedBy = sessionStorage.getItem("UrlChangedBy")
      if(urlChangedBy) {
        const segments = urlChangedBy.split("-")
        if(segments && segments.length) {
            const flag = segments[0]
            if(flag && flag === 'Msg') {
              console.log('UnFoldMenuAndSelect-WTXT')
              this.$Bus.$emit('UnFoldMenuAndSelect','WTXT') // ProjectBoot.vue中注册该事件:更新左侧菜单展开节点并选择菜单
              this.$staticmethod._Set("UrlChangedBy","")  // 处理完成重置
            }
        }
      }
    },
    // 判断是否有数据
    func_hasdata() {
      var _this = this;
      var len = _this.extdata.issueStatusItems.filter(x => x.IssueItems.length).length;
      return len > 0;
    },
    setLeftmenuShow(){
      let _css_leftmenu = document.getElementsByClassName('_css-leftmenu')[0];
      let _css_righttop_group = document.getElementsByClassName('_css-righttop-group')[0];
      _css_leftmenu.removeAttribute("style");
      _css_righttop_group.removeAttribute("style");
      _css_leftmenu.nextElementSibling.removeAttribute("style");
    },
    // 发起问题，打开在线预览
    opendocview(paratype, para){
      var _this = this;
      _this.$emit("setProjectbootExtdata", paratype, para);
    },
    getTagStyle(it_color, fontcolor, bgcolor){
      var _this = this;
      var _s = {};
      _s["color"] = it_color;
      if (fontcolor) {
        _s["color"] = fontcolor;
      }
      _s["background-color"] = it_color.replace(', 1)', ', 0.1)');
      if (bgcolor) {
        _s["background-color"] = bgcolor;
      }
      _s["border"] = `1px solid ${it_color}`;
      return _s;
    },

    IfIssueShow(item2){
      var _this = this;
      // 判断当前问题的状态是否在 selectedStatusCheckoutbox 范围内，或者 selectedStatusCheckoutbox.length 为 0
      var status_condition_true = !_this.selectedStatusCheckoutbox.length
        || _this.selectedStatusCheckoutbox.indexOf(item2.IssueStatus) != -1;

      // 判断当前问题的所有标签是否与 selectedTagCheckbox 存在交集 或 selectedTagCheckbox.length 为0
      // 又或者当前问题没有标签，且已选中了标签：blanktagid
      var tag_condition_true = !_this.selectedTagCheckbox.length
        || _this.selectedTagCheckbox.filter(x => item2.TagIdList.includes(x)).length > 0
        || (item2.TagIdList.length == 0 && _this.selectedTagCheckbox.includes('blanktagid'));

      return status_condition_true && tag_condition_true;
    },
    _ontemplate(){
      var _this= this;
      var _organizeId = _this.$staticmethod._Get("organizeId");
      window.location.href = `${this.$issueBaseUrl.DownloadProjectIssueTemplate}?organizeId=${_organizeId}&Token=${_this.$staticmethod.Get("Token")}`;
    },
    _onimportok(fileinput){
      var _this = this;
      var files = fileinput.files;
      if (files.length == 0) {
        _this.$message.error('请先选择导入文件');
        return;
      }
      // 发post请求，进行导入。
      var fd = new FormData();
      fd.append("OrganizeId", _this.$staticmethod._Get("organizeId"));
      fd.append("Token", _this.$staticmethod.Get("Token"));
      fd.append("IssueFile", files[0]);
      var config = {
        headers: {
          "Content-Type": "multipart/form-data"
        }
      };
      _this.$axios
        .post(
          `${this.$issueBaseUrl.ImportIssue}`,
          fd,
          config
        )
        .then(x => {
          if (x.data.Ret > 0) {
            _this.$message.success('导入成功');
            _this.bShowingImportTemplate = false;
            _this.refreshbystatus();
          } else {
            _this.$message.error(x.data.Msg);
          }
        })
        .catch(x => {
          console.error(x);
        });
    },
    btnimportissue_click(ev){
      var _this = this;
      if (ev) {
        ev.stopPropagation();
      }
      _this.bShowIssueBtns = false;
      _this.bShowingImportTemplate = true;
    },
    switchIssueBtns(ev) {
      var _this = this;
      if (_this.extdata.newissuedis == true) {
        _this.bShowIssueBtns = false;
        return;
      }
      if (ev) {
        ev.stopPropagation();
      }
      // 判断是否是弹出下拉按钮的状态
      if (_this.bShowIssueBtns == true) {
        _this.bShowIssueBtns = false;
      } else {
        _this.bShowIssueBtns = true;
      }
    },
    saveTagsForIssue(ev){
      var _this = this;
      if (ev) {
        ev.stopPropagation();
      }
      var _LoadingIns = _this.$loading({
        text: "加载中",
        target: document.getElementById(`id_rp_${_this.detailShowingTypeId}`)
      });
      var _Token = _this.$staticmethod.Get("Token");
      var _IssueId = _this.extdata.detaildata.issueId;
      var _rit_tagIds = _this.editingTagSelectedIds.join(',');
      _this.$axios({
        method:'post',
        url: `${this.$issueBaseUrl.Tag_OverrideIssueTag}`,
        data: {
          Token: _Token,
          IssueId: _IssueId,
          rit_tagIds: _rit_tagIds
        }
      }).then(x => {
        if (x.data.Ret > 0) {
          // 移除小弹框
          _this.tagList_show = false;
          // 修改数组
          // 修改 issuedetailTags 及 editingTagSelectedIds
          _this.issuedetailTags = x.data.Data;
          _this.editingTagSelectedIds = _this.issuedetailTags.map(x => x.it_guid);
          // modify当前问题的tagIdList
          var typeIndex = _this.extdata.issueStatusItems.findIndex(x => x.ItemDetailId == _this.item.ItemDetailId);
          if (typeIndex >= 0) {
            var issues = _this.extdata.issueStatusItems[typeIndex].IssueItems;
            var issueIndex = issues.findIndex(x => x.IssueId == _IssueId);
            if (issueIndex >= 0) {
              issues[issueIndex].TagIdList = _this.$staticmethod.DeepCopy(_this.editingTagSelectedIds);
            }
          }
          _this.$message.success('操作成功');
        } else {
          _this.$message.error(x.data.Msg);
        }
        _LoadingIns.close();
      }).catch(x => {
        _LoadingIns.close();
      });
    },
    do_removeTagFromIssue(it_guid) {
      var _this = this;
      var _Token = _this.$staticmethod.Get("Token");
      var _IssueId = _this.extdata.detaildata.issueId;
      var _it_guid = it_guid;
      var _LoadingIns = _this.$loading({
        text: "加载中",
        target: document.getElementById(`id_rp_${_this.detailShowingTypeId}`)
      });
      _this.$axios({
        method:'post',
        url: `${_this.$issueBaseUrl.Tag_RemoveTagFromIssue}`,
        data: {
          Token: _Token,
          IssueId: _IssueId,
          it_guid: it_guid
        }
      }).then(x => {
        if (x.data.Ret > 0) {
          _this.$message.success('操作成功！');
          // 修改 issuedetailTags 及 editingTagSelectedIds
          _this.issuedetailTags = _this.issuedetailTags.filter(x => x.it_guid != _it_guid);
          _this.editingTagSelectedIds = _this.issuedetailTags.map(x => x.it_guid);

          // 修改对应问题的 TagIdList
          var typeIndex = _this.extdata.issueStatusItems.findIndex(x => x.ItemDetailId == _this.item.ItemDetailId);
          if (typeIndex >= 0) {
            var issues = _this.extdata.issueStatusItems[typeIndex].IssueItems;
            var issueIndex = issues.findIndex(x => x.IssueId == _IssueId);
            if (issueIndex >= 0) {
              issues[issueIndex].TagIdList = _this.$staticmethod.DeepCopy(_this.editingTagSelectedIds);
            }
          }
        } else {
          _this.$message.error(x.data.Msg);
        }
        _LoadingIns.close();
      }).catch(x => {
        console.error(x);
        _LoadingIns.close();
      });
    },
    removeTagFromIssue(tagi, ev) {
      var _this = this;
      if (ev) {
        ev.stopPropagation();
      }
      if (_this.extdata.newissuedis) {
        _this.$message.error('当前用户无编辑权限');
        return;
      }
      _this.$confirm(`确认移除标签“${tagi.it_name}”？`, '操作确认', {
        cancelButtonText: '取消',
        confirmButtonText: '确定',
        type: 'warning'
      }).then(x => {
        _this.do_removeTagFromIssue(tagi.it_guid);
      }).catch(x => {

      });
    },
    switchEditingTag(it_guid, ev){
      var _this = this;
      if (ev) {
        ev.stopPropagation();
      }
      if (_this.editingTagsContains(it_guid)) {
        _this.editingTagSelectedIds = _this.editingTagSelectedIds.filter(x => x!= it_guid);
      } else {
        _this.editingTagSelectedIds.push(it_guid);
      }
    },
    editingTagsContains(it_guid){
      var _this = this;
      return _this.editingTagSelectedIds.indexOf(it_guid) >= 0;
    },
    // 问题详情，添加标签时的可用标签数据加载
    getAllTags() {
      var _this = this;
      var _organizeId = _this.$staticmethod._Get("organizeId");
      // 问题详情，添加标签时的可用标签数据加载，数据区域的loading
      var _LoadingIns = _this.$loading({
        text: "加载中",
        target: document.getElementById("id_taglistoptions")
      });
      var _Url =  `${this.$issueBaseUrl.GetIssueOrganizeTags}?organizeId=${_organizeId}&token=${_this.$staticmethod.Get("Token")}`
      _this.$axios.get(_Url).then(x => {
        if (x.data.Ret == 1) {
          if( x.data.Data.length > 0){
            _this.allTags = x.data.Data;
            // 显示标签选项列表
            _this.tagList_show = true;
          }else{
            _this.$message.warning('暂无标签');
          }
        } else {
          _this.$message.error(x.data.Msg);
        }
        _LoadingIns.close();
      }).catch(x => {
        console.error(x);
        _LoadingIns.close();
      });
    },
    closerightpart(ev){
      var _this = this;
      _this.tagList_show = false;
      _this.bShowIssueBtns = false;
      this.closeMoreListDialog();
      if (ev) {
        ev.stopPropagation();
      }
    },
      //关闭问题详情 更多下拉列表
    closeMoreListDialog() {
      this.tree_show = false;
      this.listhover_status = false;
      this.listhover_type = false;
    },
    getTagListStyle(){
      var _this = this;
      var _s = {};
      _s["left"] = _this.tagList_x + "px";
      _s["top"] = (_this.tagList_y + 28) + "px";
      return _s;
    },
    // 点击添加标签按钮
    addIssueTag(ev){
      var _this = this;
      if (ev) {
        ev.stopPropagation();
      }
      if (_this.tagList_show == true) {
        _this.tagList_show = false;
        return;
      }
      if (_this.extdata.newissuedis) {
        _this.$message.error('当前用户无编辑权限');
        return;
      }
      // 设置位置并弹出标签选择下拉框
      var offset = 0;
      if (ev.target.classList.contains('_css-tagbtn-icon')) {
        offset = 10;
      } else if (ev.target.classList.contains('_css-tagbtn-text')) {
        offset = 40;
      }
      var bcrect = ev.target.getBoundingClientRect();
      _this.tagList_x = bcrect.x - offset;
      _this.tagList_y = bcrect.y;
      // 详情中已添加的标签，赋值给 editingTagSelectedIds
      _this.editingTagSelectedIds = _this.issuedetailTags.map(x => x.it_guid);
      
      // 调用接口，得到当前项目全部的标签
      _this.$nextTick(()=>{
        _this.getAllTags();
      });
    },
    computeTagStyle(tagi){
      var _this = this;
      var _s = {};
      _s["color"] = tagi.it_color;
      _s["border-color"] = tagi.it_color;
      _s["background-color"] = tagi.it_color.replace(", 1)", ", 0.1)");
      return _s;
    },
    _onclose(ev){
      var _this = this;
      _this.bShowingConfig = false;
      // 让菜单再手动点击一下“问题追踪”
      var _organizeId = _this.$staticmethod._Get("organizeId");
      var _Token = _this.$staticmethod.Get("Token");
      window.location.href = `${window.bim_config.hasRouterFile}/#/Home/ProjectBoot/Issue/${_organizeId}/${_Token}`;
      _this.$emit("onmounted", "issue");
    },
    handleScroll2(ev, item){
      item.isshowscroll = true
      clearTimeout(item.timerScroll);
      item.timerScroll = setInterval(() => {
        item.isshowscroll = false;
      }, 1000)
    },
    getimgsrcstyle(url){
      var _this = this;
      var _s = {};
      _s["background-image"] = `url(${url})`;
        _s["background-repeat"] = "no-repeat";
      _s["background-size"] = "contain";
      _s["background-position"] = "center";
      return _s;
    }, 
    computetipstyle(){
      var _this = this;
      var _s = {};
      _s["left"] = _this.imageormodeltip.left + 'px';
      _s["top"] = _this.imageormodeltip.top + 'px';
      if (_this.imageormodeltip.isshowing) {
        _s["display"] = 'flex';
      } else {
        _s["display"] = 'none';
      }
      return _s;
    },
    // 隐藏图片的tooltip
    hidetip(ifshowingmodel) {
      // 设置tip为不显示
      var _this = this;
      _this.imageormodeltip.isshowing = false;
       _this.imageormodeltip.inarea = false;
    },
    // 显示或移动图片或模型的tooltip
    showormovetip(ifshowingmodel, x, y) {
      // 确定显示的文本
      var _this = this;
      var _strtip = ifshowingmodel?'打开模型':'查看图片';
      _this.imageormodeltip.text = _strtip;
      // 设置tip位置
      _this.imageormodeltip.left = x;
      _this.imageormodeltip.top = y;
      // 设置tip为显示
      _this.imageormodeltip.isshowing = true;
    },
    // 显示图片的tooltip
    showtip(ifshowingmodel, ev){
      // 鼠标移入时，设置当前鼠标在区域范围内
      var _this = this;
      _this.imageormodeltip.inarea = true;
    },
    // 移动鼠标时图片的tooltip移动
    movetip(ifshowingmodel, ev){
      var _this = this;
      // 鼠标动时，直接隐藏。
      _this.imageormodeltip.isshowing = false;
      clearTimeout(_this.imageormodeltip.lasttipH);
      _this.imageormodeltip.lasttipH = setTimeout(()=>{
        // 鼠标不在区域范围内了，不要再次显示及改变tip的位置了。
        if (!_this.imageormodeltip.inarea) {
          return;
        }
        _this.showormovetip(ifshowingmodel, ev.clientX, ev.clientY);
      }, 400);
    }, 
    changeoutertop(piccount){
      var _this = this;
      console.log(piccount);
      if (piccount.length == 0) {
        _this.outertopval = 0;
      } else {
        _this.outertopval = 300;
      }
    },
    onmodelview_close(){
      var _this = this;
      _this.showModelView=false; 
      setTimeout(function(){
        _this.$staticmethod._Set("needpausegetmsg", 0);
      }, 1000);
    },
    detail_image_move(ev, moveval){
      var _this = this;
      var classid = ev.target.getAttribute("data-parentid");
      var doms = document.getElementsByClassName(classid);
      if (doms.length > 0) {
        var dom = doms[0];
        dom.scrollLeft += moveval;
      } else {
        console.error('classid 不存在');
      }
    }, 
    setCommentAtUserList(joiners){
      var _this = this;
      //debugger;
      console.assert(_this.userList, "_this.userList 不能为空！");
      _this.userList_currentIssueDetail = [];
      for (var i = 0; i < _this.userList.length; i++) {
        // 判断如果正在遍历的 userList[i] 在 joiners 中，则添加到 userList_currentIssueDetail 中。
        var index = joiners.findIndex(x => x.Id == _this.userList[i].UserId);
        if (index >= 0) {
          _this.userList_currentIssueDetail.push(_this.userList[i]);
        }
      }
    },
    entercreatoricon(ev, item2){
      var _this = this; 
      var bcrect = ev.target.getBoundingClientRect();
      // 设置数据（不要ajax)
      _this.extdata.CreatorComFullName = item2.com_FullName;
      _this.extdata.CreatorRealName = item2.CreateUserName;
      _this.extdata.CreatorEmail = item2.bu_Email;
      // 设置位置
      _this.extdata.CreatorTipLeft = bcrect.x;
      _this.extdata.CreatorTipTop = bcrect.y;
      // 显示
      _this.extdata.bIsShowCreatorTip = true;
    },
    leavecreatoricon(ev){
      var _this = this;
      // 隐藏
      _this.extdata.bIsShowCreatorTip = false;
    },
    enter_tree_common(){
      var _this = this;
      _this.listhover_status = false;
      _this.listhover_type = false;
    },
    leave_tree_onbodyscroll(){
      var _this = this;
      _this.listhover_status = false;
      _this.listhover_type = false;
    },
    //手写树型下拉二级 移入移出事件
    enter_tree_Status(ev){
      var _this = this;
      var bcrect = ev.target.getBoundingClientRect();
      _this.extdata.TreeTipLeft = bcrect.x + ev.target.clientWidth;
      _this.extdata.TreeTipTop = bcrect.y;
      _this.listhover_status = true;
      _this.listhover_type = false;
    },
    leave_tree_Status(ev){
      var _this = this;
       _this.listhover_status = false;
    },
    enter_tree_Type(ev){
      var _this = this;
      var bcrect = ev.target.getBoundingClientRect();
      _this.extdata.TreeTipLeft = bcrect.x + ev.target.clientWidth;
      _this.extdata.TreeTipTop = bcrect.y;
      _this.listhover_type = true;
      _this.listhover_status = false;
    },
    leave_tree_Type(ev){
      var _this = this;
      _this.listhover_type = false;
    },
    enterstatus(number){
      var _this = this;
      var _s = {};
      _s["top"] = (_this.extdata.TreeTipTop + 0) + "px";
      _s["left"] = (_this.extdata.TreeTipLeft + number) + "px";
      if (_this.extdata.TreeTipTop + 20 > document.body.clientHeight - 124) {
        _this.extdata.TreeTipTop -= 124;
      }
      if (_this.extdata.TreeTipLeft + 20 > document.body.clientWidth - 240) {
        _this.extdata.TreeTipLeft -= (240 + 40);
      }
      return _s;
    },
    getcreatortipstyle(){
      var _this = this;
      var _s = {};
      _s["display"] = _this.extdata.bIsShowCreatorTip?"block":"none";
      _s["top"] = (_this.extdata.CreatorTipTop + 20) + "px";
      _s["left"] = (_this.extdata.CreatorTipLeft + 20) + "px";
      if (_this.extdata.CreatorTipTop + 20 > document.body.clientHeight - 124) {
        _this.extdata.CreatorTipTop -= 124;
      }
      if (_this.extdata.CreatorTipLeft + 20 > document.body.clientWidth - 240) {
        _this.extdata.CreatorTipLeft -= (240 + 40);
      }
      return _s;
    },
    getIssueObj(issueId){
      var _this = this;
      for (var i = 0; i < _this.extdata.issueStatusItems.length; i++) {
        if (_this.extdata.issueStatusItems[i].IssueItems.length == 0) {
          continue;
        }
        var theindex = _this.extdata.issueStatusItems[i].IssueItems.findIndex(x => x.IssueId == issueId);
        if (theindex >= 0) {
          return _this.extdata.issueStatusItems[i].IssueItems[theindex];
        }
      }
      return undefined;
    },
    // 点击详情中的小图，切换大图的url及文本
    changedetailbigimg(bf){
      var _this = this;
      _this.bgpicture_src = window.bim_config.webserverurl +'/'+ bf.bf_path;
      _this.bgpicture_title = bf.bf_filename;
      _this.bgpicture_id = bf.bf_guid;

      // 标识当前正在显示的是图片文件，而不是模型
      _this.extdata._detailshowingmodel = false;
    }, 
    // 问题详情中添加图片
    detail_basefileimage_change(ele){
      var _this = this;
      // 问题详情，图片区域的 loading
      var _LoadingIns = _this.$loading({
        text: "加载中",
        target: document.getElementById("id_detail_attachoparea")
      });

      // 详情中添加图片后，直接拿到图片，访问 UploadImage 接口，获取 bf_guid, bf_md5 和 bf_path
      var dominputfile = document.getElementById("id_detail_add_basefileimage");
      if (ele) {
        dominputfile = ele;
      }
      if (dominputfile.files.length == 0) {
        return;
      }
      var File = dominputfile.files[0];
      var fd = new FormData();
      fd.append("FormFile", File); 
      fd.append("IssueId", _this.extdata.detaildata.issueId);
      var config = {
        headers: {
            "Content-Type": "multipart/form-data"
        }
      };
      _this.$axios
      .post(
        `${this.$issueBaseUrl.UploadImage}?token=${ _this.$staticmethod.Get("Token")}`,
        fd,
        config
      )
      .then(x => {
        if (x.status == 200) {
          if (x.data.Ret < 0) {
              _LoadingIns.close();
            _this.$message.error(x.data.Msg);
            return;
          }
          if (x.data.Ret > 0) {
            x.data.Data.bf_path = '/' + x.data.Data.bf_path
            // 设置 详情中，大图的名称及大图的地址
            _this.bgpicture_src = `${window.bim_config.webserverurl}${x.data.Data.bf_path}`;
            _this.bgpicture_title = x.data.Data.bf_filename;
            _this.bgpicture_id = x.data.Data.bf_guid;
            // 当前展示的是图片不是模型
            _this.extdata._detailshowingmodel = false;
            // 设置 详情中，自己加的小图数组。
            var topush = {
              IssueId: _this.extdata.detaildata.issueId,
              bf_filename: x.data.Data.bf_filename,
              bf_guid: x.data.Data.bf_guid,
              bf_md5: x.data.Data.bf_md5,
              bf_path: x.data.Data.bf_path
            };
            //console.log(topush);
            _this.extdata.detaildata.fulldata.base_files.push(topush);
            // 列表中的也要加
            var issueobj = _this.getIssueObj(_this.extdata.detaildata.issueId);
            if (issueobj) {
              issueobj.FileCount = _this.extdata.detaildata.fulldata.base_files.length
              +
              // 非模型，非图片的文件个数
              _this.extdata.detaildata.fulldata.relationFiles.length;

              // 缓存这个问题在列表中，原来有多少张非模型图片
              var originlength = issueobj.base_files.length;
              issueobj.base_files = _this.$staticmethod.DeepCopy(_this.extdata.detaildata.fulldata.base_files);
              if (originlength == 0) {
                issueobj.bgpicture_src = _this.bgpicture_src;
              }
            } 
          } else {
             _this.$message.error(x.data.Msg);
          }
        } 
        _LoadingIns.close();
      })
      .catch(x => { 
        _LoadingIns.close();
      });
    },
    // 详情中添加图片
    detail_add_basefileimage(){
      this.closeMoreListDialog();
      var dominputfile = document.getElementById("id_detail_add_basefileimage");
      dominputfile.value = "";
      dominputfile.click();
    },
    // 打开图片或模型预览 新需求 用div预览
    openImageOrModel_new(base64orsrc, ev,comm){
      var _this = this;
      this.closeMoreListDialog();
      if (ev) {
        ev.stopPropagation();
      }
      _this.tagList_show = false;
      var str = base64orsrc.slice(-3);
      if (base64orsrc.indexOf('http') == 0 && base64orsrc.indexOf('ModelImages') < 0) {
        _this.comment_picture_src = base64orsrc;
        _this.comment_picture_showhover = true;
        _this.comment_picture_model = true;
      } else if(str != 'bmp'){
        _this.bgpicture_showhover = true;
        _this.bgpicture_model = true;
      } else{
          if (_this.extdata._detailshowingmodel) {
          // 直接在这个地方打开新=场景，应该获取到projectid sceneid userid,sceneDataJson,sceneSelectElements
          // this.extdata.detaildata.fulldata保存的是api/User/Issue/GetIssueDetail这个接口返回值
          let _isobj = _this.extdata.detaildata.fulldata.issueObj;
          this.ProjectIDProps = _isobj.ProjectID;
          this.sceneIdProps = _isobj.ModelId;
          this.sceneDataJson = _isobj.Isoverride;
          this.sceneSelectElements = _isobj.ViewpointID;
          this.markupName = _isobj.Title;
          this.$nextTick(()=>{
            this.showModelView = true;
          })
        } else {
          // 打开在线预览 
          _this.$emit("setProjectbootExtdata", "_docviewtype", "office");
          // 宽屏问题详情中预览关联的bmp图片 
          var url_iframe_all = _this.$staticmethod.computeViewUrl(base64orsrc, _this.bgpicture_title);
          _this.$emit("setProjectbootExtdata", "_show_idocview", true);
          _this.$emit("setProjectbootExtdata", "_idocviewurl", url_iframe_all);
        }
      }
    },
    // 下载 
    downloadDocument(doc, ev) {  
      window.location.href = `${window.bim_config.webserverurl}/api/v1/attach/download?attachmentId=${doc.FileId}&Token=${this.$staticmethod.Get('Token')}`;
    },
    // 打开文档预览
    openDocument(doc){
      var _this = this;
      console.log(doc) 
        var url = `${window.bim_config.webserverurl}/api/v1/attach/preview?id=${doc.FileId}&Token=${this.$staticmethod.Get('Token')}`
        // 根据扩展名获取在线浏览地址
      var url_iframe_all;
      if (doc.FileName.toLowerCase().indexOf(".dwg") > 0) {
        // 修改当前预览的关闭按钮类
        _this.$emit("setProjectbootExtdata", "_docviewtype", "dwg");

        // dwg 在线预览
        url_iframe_all = `${
          _this.$configjson.dwgurl
        }/Home/Index2?dwgurlcfg=${encodeURIComponent(url)}&name=${
          doc.FileName
        }`;
      } else {
        // 修改当前预览的关闭按钮类
        _this.$emit("setProjectbootExtdata", "_docviewtype", "office");
        // office 在线预览
        // 展开问题的详情，点击其中的非dwg文档附件以预览
        console.log('展开问题的详情，点击其中的非dwg文档附件以预览');
        url_iframe_all = _this.$staticmethod.getHuangNewcomputeViewUrl(url, doc.FileName);
      }
        // debugger
        _this.$emit("setProjectbootExtdata", "_show_idocview", true);
        _this.$emit("setProjectbootExtdata", "_idocviewurl", url_iframe_all);
    },
    // 设置 bgpicture_src 及 bgpicture_title
    initdetailSrcAndName(resdata){
      var _this = this;
      if (resdata.issueObj && resdata.issueObj.ImageUrl) {
        _this.bgpicture_src = resdata.issueObj.ImageUrl;
        _this.bgpicture_title = resdata.modelname;
        return true;
      } else {
        if (resdata.base_files && resdata.base_files.length > 0) {
          _this.bgpicture_title = resdata.base_files[0].bf_filename;
          _this.bgpicture_src = window.bim_config.webserverurl +'/'+ resdata.base_files[0].bf_path;
          _this.bgpicture_id = resdata.base_files[0].bf_guid;
        }
        return false;
      }
    },
   
    // 使用 bf.bf_path 计算 style 样式对象
    getstyle_by_bfpath(bf_path){
      var _s = {};
      _s["background-repeat"] = "no-repeat";
      _s["background-size"] = "contain";
      _s["background-position"] = "center";
      _s["background-image"] = `url('${window.bim_config.webserverurl}/${bf_path}')`;
      return _s;
    },
    computeDetailMiniPic_url(url){
      var _this = this;
      var _s = {};
      _s["background-repeat"] = "no-repeat";
      _s["background-size"] = "contain";
      _s["background-position"] = "center";
      _s["background-image"] = `url('${url}')`;
      return _s;
    },
    computeDetailMiniPic(item){
      var _this = this;
      return _this.computeDetailMiniPic_url(_this.filterimg(item.FileId));
    },
    // 判断列表中每项问题是否有图片区域
    currentIssueHasImage2(issueobj){
      // issueobj.ImageUrl 有值表示该问题是从模型发起的。则列表中的这一项问题是有图片区域的。
      if (issueobj.ImageUrl) {
        return true;
      }
      if (issueobj.base_files && issueobj.base_files.length > 0) {
        return true;
      } else {
        return false;
      }
    },
    // 判断详情里是否有图片区域
    currentIssueHasImage(issueobj){
      var _this = this;
      if (issueobj.ImageUrl) {
        return true;
      }
      if (!_this.extdata.detaildata.fulldata.base_files || _this.extdata.detaildata.fulldata.base_files.length == 0) {
        return false;
      } else {
        return true;
      }
    },
     // 阻止事件冒泡
    _stopPropagation(ev){
      ev.stopPropagation();
    },
    getParentClass(item){
      var _this = this;
      var _classobj = {};
      if (item.RightIsOpen) {
        _classobj["_css-rightopen"] = true;
      } else {
        _classobj["_css-rightopen"] = false;
      }
      // 其分类下问题个数大于0
      //var computedhasIssue = _this.extdata.issueStatusItems.filter(x => x.IssueItems.length > 0);
      var computedhasIssue = _this.extdata.issueStatusItems.filter(x => x.IssueItems.length > 0);
      if (computedhasIssue.length > 0) {
        var selfindex = computedhasIssue.findIndex(x => x.ItemDetailId == item.ItemDetailId);
        if (selfindex!=-1 && selfindex % 2 == 0) {
          _classobj["_css-showandeven"] = true;
        } else if (selfindex!=-1 && selfindex % 2 != 0){
          _classobj["_css-showandodd"] = true;
        }
      }
      return _classobj;
    },
    // 执行移除参与人，并刷新后定位该问题
    removejoiner(user){
      // 调用接口，并执行刷新后定位
      var _this = this;
      var _Token = _this.$staticmethod.Get("Token");
      var _Id = user.Id;
      var _IssueId = _this.extdata.detaildata.issueId;
      var _Name = '';
      _this.$axios({
        method:'post',
        url:`${this.$issueBaseUrl.RemoveIssueJoiner}`,
        data: {
          Token: _Token,
          Name: _Name,
          Id: _Id,
          IssueId: _IssueId
        }
      }).then(x => {
        if (x.status == 200) {
          if (x.data.Ret > 0) { 
            //刷新问题分类
              // 起点分类
              var nowTypeId = _this.extdata.detaildata.fulldata.issueObj.IssueTypeID;
              // 目标分类
              var targetTypeId = nowTypeId;
              //刷新问题 目标分类
              _this.loadSometypeissues(targetTypeId, _this.extdata.detaildata.issueId,
                () => {
                  // 横向定位
                  var allhasissueTypes = _this.extdata.issueStatusItems.filter(x => x.IssueItems.length > 0);
                  var afternewtypeindex = allhasissueTypes.findIndex(x => x.ItemDetailId == targetTypeId);
                  var eleissuebody = document.getElementById("_id_issue_body_scroller");
                  eleissuebody.scrollLeft = 292 * (afternewtypeindex);
                }
              );
          } else {
            _this.$message.error(`服务器错误：(${x.data.Msg})`)
          }
        } 
      }).catch(x => {})
    },
    // 从问题详情的参与人列表中移除掉某参与人
    removefromjoiners(user){
      // 弹出确定操作提示
      var _this = this;
      // 关闭弹出框
      _this.extdata.detaildata.hoveringuserId = "";
      _this.$confirm("确定移除该参与人？", "移除参与人", {
        confirmButtonText:'确定',
        cancelButtonText:'取消',
        type:'warning'
      }).then(x => {
        _this.removejoiner(user);
      }).catch(x => {
      });
    },
    // 关闭所有弹层
    closeallpopup(ev) {
      // 关闭问题详情参与人的下拉层
      var _this = this;
      if (_this.extdata.canblur == true) {
        _this.extdata.detaildata.hoveringuserId = "";
      } else {
        // 应该再focus回去
        ev.preventDefault();
      }
    },
    clickjoiner(user, ev) {
      var _this = this;
      var bcrect = ev.target.getBoundingClientRect();
      this.closeMoreListDialog();
      //保存当前打开的参与人的信息版的dom
      if (ev.target.lastElementChild == null) {
        this.ParticipanInfoPtanelDom = ev.target.parentNode.lastChild;
      } else {
        this.ParticipanInfoPtanelDom = ev.target.lastChild;
      }
      if (_this.extdata.detaildata.hoveringuserId == user.Id) {
        _this.extdata.detaildata.hoveringuserId = "";
      } else {
        _this.extdata.detaildata.x = bcrect.x;
        _this.extdata.detaildata.y = bcrect.y + 22;
        _this.extdata.detaildata.hoveringuserId = user.Id;
      }
    },
    // 获取（问题详情中的所有参与人中的）某一人员是否显示详情
    getJoinerDetailClass(user) {
      var _objclass = {};
      if (!user.showuserdetail) {
        _objclass["_css-showitem"] = false;
      } else {
        _objclass["_css-showitem"] = true;
      }
      return _objclass;
    },
    // 根据输入的关键字，搜索出可添加到这个问题的参与人建议数据
    loadsugjoiners(str) {
      // 确保 str 不为 undefined
      var _this = this;
      if (str == undefined) {
        return;
      }
      // 请求数据 
      _this
          .$axios({
            method: "get",
            url: `${
              this.$issueBaseUrl.GetToAddIssueJoiners
            }?token=${_this.$staticmethod.Get(
              "Token"
            )}&projectId=${_this.$staticmethod._Get(
              "organizeId"
            )}&encodedKeyWord=${str}`
          })
          .then(x => {
            if (x.status == 200 && x.data.Ret > 0) {
              this.userList = x.data.Data;
              //debugger;
              var arr = [];
              for (var i = 0; i < x.data.Data.length; i++) {
                if (
                  _this.extdata.detaildata.joiners.filter(
                    y => y.UserId == x.data.Data[i].Id
                  ).length <= 0
                ) {
                  arr.push(x.data.Data[i]);
                }
              }
              _this.extdata.detaildata.joiner_suglist = arr;
            }

            if (x.data.Ret < 0) {
              _this.$message.error(x.data.Msg);
            }
          })
          .catch(x => {});
    }, 
    //
    begin_addjoiner(ev) {
      var _this = this;
      if (ev){
        ev.stopPropagation();
      }
      if (_this.extdata.newissuedis) {
        _this.$message.error('当前用户无编辑权限');
        return;
      }
      _this.tagList_show = false;
      // 引入组件版本正在添加参与人
      _this.isaddingmember = true;
      // 清空“添加参与人”控件的下拉建议项 
      _this.extdata.detaildata.joiner_suglist = [];
    },
    // 选中问题的某一种状态（进行修改）
    issuedetailstatusselected(para) {
      var _this = this;
      // 起点分类
      var nowTypeId = _this.extdata.detaildata.fulldata.issueObj.IssueTypeID;
       // 目标分类
      var targetTypeId = nowTypeId;
      _this
        .$axios({
          method: "post",
          url: `${this.$issueBaseUrl.ModifyIssueStatus}`,
          data: {
            Token: _this.$staticmethod.Get("Token"),
            IssueId: _this.extdata.detaildata.issueId,
            Id: para.id
          }
        })
        .then(x => {
          if (x.status == 200) {
            if (x.data.Ret > 0) {
              //刷新问题 目标分类
              _this.loadSometypeissues(targetTypeId, _this.extdata.detaildata.issueId,
                () => {
                  // 横向定位
                  var allhasissueTypes = _this.extdata.issueStatusItems.filter(x => x.IssueItems.length > 0);
                  var afternewtypeindex = allhasissueTypes.findIndex(x => x.ItemDetailId == targetTypeId);
                  var eleissuebody = document.getElementById("_id_issue_body_scroller");
                  eleissuebody.scrollLeft = 292 * (afternewtypeindex);
                }
              );
              _this.$refs.compsEloSelect.extdata._showoptions = false;
            } else {
              _this.$message.error(`接口错误：(${x.data.Msg})`);
            }
          } else {
          }
        })
        .catch(x => {});
    },
    // 选中问题的某一种类型（进行修改）
    issuedetailselectedType(para) {
      var _this = this;
      // 起点分类
      var nowTypeId = _this.extdata.detaildata.fulldata.issueObj.IssueTypeID;
      // 目标分类
      var targetTypeId = para.id;
      if (nowTypeId == targetTypeId) {
        console.log('当前问题分类与目标分类相同');
        //_this.$refs.compsEloSelect.extdata._showoptions = false;
        return;
      }
      _this
        .$axios({
          method: "post",
          url: `${this.$issueBaseUrl.ModifyIssueType}`,
          data: {
            Token: _this.$staticmethod.Get("Token"),
            IssueId: _this.extdata.detaildata.issueId,
            Id: para.id
          }
        })
        .then(x => {
          if (x.status == 200) {
            if (x.data.Ret > 0) {
              // 只加载两个分类中的问题数据
              //刷新问题 起点分类
              // debugger
              _this.loadSometypeissues(nowTypeId,'');
              //刷新问题 目标分类
              _this.loadSometypeissues(targetTypeId, _this.extdata.detaildata.issueId,

              () => {
                // 横向定位
                var allhasissueTypes = _this.extdata.issueStatusItems.filter(x => x.IssueItems.length > 0);
                var afternewtypeindex = allhasissueTypes.findIndex(x => x.ItemDetailId == targetTypeId);
                var eleissuebody = document.getElementById("_id_issue_body_scroller");
                eleissuebody.scrollLeft = 292 * (afternewtypeindex);
              }
              );
              _this.$refs.compsEloSelect.extdata._showoptions = false;
            } else {
              _this.$message.error(`接口错误：(${x.data.Msg})`);
            }
          } else {
          }
        })
        .catch(x => {});
    },
    imgurlbycommcontent(contenttype) {
      try { 
        var _obj = JSON.parse(contenttype);
        var toreturl = `${window.bim_config.webserverurl}/api/v1/attach/preview?id=${_obj.id}&Token=${this.$staticmethod.Get('Token')}`
        console.log(toreturl);
        return toreturl;
      } catch (e) {
        return "";
      }
    },
    // 详情页面图片地址拼接
    filterimg(id) {
      try {
        return `${window.bim_config.webserverurl}/api/v1/attach/preview?id=${id}&Token=${this.$staticmethod.Get('Token')}`;
      } catch (e) {
        return "";
      }
    },
    isimage(comm) { 
      try {
        var _obj = JSON.parse(comm);
        if (_obj.type == "image") {
          return true;
        }
      } catch (e) {
        return false;
      }
      return false;
    },

    // 请求服务器，添加图片类型评论
    add_imagecomment(filename, fileid) {
      var _this = this;
      var _Token = _this.$staticmethod.Get("Token");
      var _Name = filename;
      var _Id = fileid;
      var _IssueId = _this.extdata.detaildata.issueId;
      _this
        .$axios({
          method: "post",
          url: `${this.$issueBaseUrl.AddImageComment}`,
          data: {
            Token: _Token,
            Name: _Name,
            Id: _Id,
            IssueId: _IssueId
          }
        })
        .then(x => {
          if (x.status == 200 && x.data.Ret > 0) {
            // 添加当前正在显示的评论数据
            var toinsertBeforeObj = {
              content: x.data.Data.Content,
              contenttype: x.data.Data.ContentType,
              createdate: x.data.Data.CreateDate,
              deletemark: x.data.Data.DeleteMark,
              innerlist: [],
              issue_ptalkid: x.data.Data.Issue_PTalkId,
              issue_talkid: x.data.Data.Issue_TalkId,
              issueid: x.data.Data.IssueId,
              realname: _this.$staticmethod.Get("RealName"),
              targetids: "",
              userid: x.data.Data.UserId,
              userofunread: x.data.Data.UserOfUnRead
            };
            // .extdata.detaildata.comments
            _this.extdata.detaildata.comments.unshift(toinsertBeforeObj);
          }
        })
        .catch(x => {});
    },
    fileAddCommentChange() {
      var _this = this;
      var dominputfile = document.getElementById("id_AddComment_File");
      if (dominputfile.files.length == 1) {
        // 拿到文件对象通过上传接口，上传到modelapi再返回fileId
        var file = dominputfile.files[0];
        var ProjectID = _this.$staticmethod._Get("organizeId");
        var File = file;
        // 提交给服务器
        var userid = _this.$staticmethod.Get("UserId");
        var fd = new FormData();
        let FileKey=this.$md5(new Date().getTime()+''+File.name);
        fd.append("ProjectID", ProjectID);
        fd.append("BusinessId", 0);
        fd.append("AttachmentType", '2');
        fd.append("FileName", File.name);
        fd.append("FileKey", FileKey);
        fd.append("FileSize", File.size);
        fd.append("ChunkNumber", 1);
        fd.append("Index", 1);
        fd.append("UserId", userid);
        fd.append("File", File);
        var config = {
          headers: {
            "Content-Type": "multipart/form-data"
          }
        };
        _this.$axios
          .post(
            `${window.bim_config.webserverurl}/api/v1/attach/upload?Token=${_this.$staticmethod.Get('Token')}`,
            fd,
            config
          )
          .then(x => {
            if (x.data.Ret == 1 && x.data) {
              let _data = x.data.Data.Data
              _this.$message.success('添加成功'); 
              _this.add_imagecomment(_data.AttachmentName,_data.AttachmentId);
            } else {
              _this.$message.error('添加失败，请刷新再试');
            }
          })
          .catch(x => {
             _this.$message.error(`服务器处理发生错误，可能是文件类型不符合白名单规则，请检查文件`);
          });
      } else {
      }
    },
    // 开始添加图片类型的评论（弹出文件选择）
    addimgcomment() {
      var _this = this;
      var domaddcommentfile = document.getElementById("id_AddComment_File");
      domaddcommentfile.value = "";
      domaddcommentfile.click();
      this.closeMoreListDialog();
    },
    // 问题标题搜索
    _onissueinput(str) {
      var _this = this;
      // 延迟发请求
      if (str.length >= 0) {
        clearTimeout(_this.timeoutId);
        _this.timeoutId = setTimeout(function() {
          _this.extdata.keyword = str;
          _this.loadStatusItemsTypes();
        }, 500);
      } else {
        _this.extdata.keyword = "";
        _this.loadStatusItemsTypes();
      }
    },

    // 问题标题搜索(修改状态展示类型接口)
    _onissueinputType(str) {
      var _this = this;
      // 延迟发请求
      if (str.length >= 0) {
        clearTimeout(_this.timeoutId);
        _this.timeoutId = setTimeout(function() {
          _this.extdata.keyword = str;
          _this.loadStatusItemsStatus();
        }, 500);
      } else {
        _this.extdata.keyword = "";
        _this.loadStatusItemsStatus();
      }
    },
    checkouttag(id) {
      var _this = this;
      if (_this.selectedTagCheckbox.indexOf(id) >= 0) {
        _this.selectedTagCheckbox = _this.selectedTagCheckbox.filter(x => x != id);
      } else {
         _this.selectedTagCheckbox.push(id);
      }
      console.log(this.selectedTagCheckbox,'this.selectedTagCheckbox')
    },
    //抽屉复选框 可选可不选
    checkout(id){
      if(this.selectedStatusCheckoutbox.indexOf(id) == -1){
        this.selectedStatusCheckoutbox.push(id);
      }else{
          this.selectedStatusCheckoutbox.splice(this.selectedStatusCheckoutbox.indexOf(id),1);
      }
    },
    // 提交给服务器端，添加文档关联给问题
    EditIssueAddRel(fileobj, issueId) {
      var _this = this;
      var _Token = _this.$staticmethod.Get("Token");
      var _IssueId = issueId;
      var _FileId = fileobj.AttachmentId;
      // 调用接口，添加文档关联
      _this
        .$axios({
          method: "post",
          url: `${this.$issueBaseUrl.AddRelation}`,
          data: {
            Token: _Token,
            IssueId: _IssueId,
            FileId: _FileId,
            OrganizeId: this.$staticmethod._Get("organizeId"),
            // LogSourceName: ''
          }
        })
        .then(x => {
          if (x.status == 200 && x.data.Ret > 0) {
            // 判断是图片还是文件，然后追加到 selectitem 或 detaildata.RelationFiles。
            if (_this.$staticmethod.testFileExtensionsIsImage(fileobj.FileExtName)) {
              var uimageobj = {
                FileId: x.data.Data.FileId,
                  FileSize: fileobj.FileSize,
                  FileName: fileobj.FileName,
                  FileExtensions: fileobj.FileExtName
              };
              // 把刚添加的图片，添加到详情的图片列表中
              _this.extdata.detaildata.relationFiles.push(uimageobj);
              // 如果之前没有图片，设置该详情的大图的显示
              // ==1 说明没图片，刚有一张。
              if (_this.extdata.detaildata.relationFiles
              .filter(x => _this.$staticmethod.testFileExtensionsIsImage(x.FileExtensions))
              .length == 1
              && !_this.selectitem.ImageUrl) {
                _this.bgpicture_src = _this.filterimg(x.data.Data.FileId);
                _this.bgpicture_title = fileobj.FileName;
              }
            } else {
              var ufileobj = {
                FileId: x.data.Data.FileId,
                FileSize: fileobj.FileSize,
                FileName: fileobj.FileName,
              };
              _this.extdata.detaildata.relationFiles.unshift(ufileobj);
            }
          }
          if (x.data.Ret < 0) {
            _this.$message.error(x.data.Msg)
          }
        })
        .catch(x => {});
    },
    // 判断有无相同文件名的附件已添加到附件列表中，如果有，则提示。
    // 否则进行上传，并返回文件名及FileId、FileSize、FileName 等信息。
    fileControlChange() {
      var _this = this;
      // console.log(178);
      var dominputfile = document.getElementById("id_EditIssue_File");
      if (dominputfile.files.length == 1) {
        // 拿到文件对象
        var file = dominputfile.files[0];
        // 判断是否已存在同名的附件列表
        // issuevue.extdata.detaildata.relationFiles
        if (
          _this.extdata.detaildata.relationFiles.filter(
            x => x.FileName == file.name
          ).length > 0
        ) { 
          _this.$message.error("已存在相同名称的附件");
          return;
        } else {
          // 提交给服务器
          // 拿到其它参数
          var ProjectID = _this.$staticmethod._Get("organizeId");
           
          var File = file;
          var FileId = FileId;
          // 判断如果当前文件是图片，则直接调用另一个方法，当图片传递上。
          //debugger;
          if (file.type.indexOf("image") >= 0) {
            _this.detail_basefileimage_change(dominputfile);
            return;
          }
          // 提交给服务器
          // 显示loading
          _this.extdata.isloadingcurrentdetail = true;

          var fd = new FormData();
          var userid = _this.$staticmethod.Get("UserId");
          let FileKey=this.$md5(new Date().getTime()+''+File.name);
          fd.append("ProjectID", ProjectID);
          fd.append("BusinessId", 0);
          fd.append("AttachmentType", '2');
          fd.append("FileName", File.name);
          fd.append("FileKey", FileKey);
          fd.append("FileSize", File.size);
          fd.append("ChunkNumber", 1);
          fd.append("Index", 1);
          fd.append("UserId", userid);
          fd.append("File", File);
          var config = {
            headers: {
              "Content-Type": "multipart/form-data"
            }
          };
          _this.$axios
            .post(
              `${window.bim_config.webserverurl}/api/v1/attach/upload?Token=${_this.$staticmethod.Get('Token')}`,
              fd,
              config
            )
            .then(x => {
              // 隐藏loading
              _this.extdata.isloadingcurrentdetail = false;
              if (x.data.Ret == 1 && x.data) {
                _this.$message.success('添加成功');
                let _data = x.data.Data.Data;
                this.extdata.detaildata.relationFiles.push(
                  {FileId: _data.AttachmentId,
                  FileName: _data.AttachmentName}
                )
                _this.EditIssueAddRel(_data, _this.extdata.detaildata.issueId);
                // 关联文档数目及时更新 增加数量及时增加
               var _RelationIssueId = _this.extdata.detaildata.issueId;
              _this.extdata.issueStatusItems.map((itemarr)=>{
                itemarr.IssueItems.some(item => {
                  if(item.IssueId === _RelationIssueId){
                    item.FileCount ++;
                    return true
                  }
                });
              })
              } else {
                _this.$message.error('添加失败，请刷新再试');
              }
            })
            .catch(x => {
              // 隐藏loading
               _this.$message.error(`服务器处理发生错误，可能是文件类型不符合白名单规则，请检查文件`);
              _this.extdata.isloadingcurrentdetail = false;
            });
        }
      }
    },

    fileEditIssueChange() {
      var _this = this;
      var dominputfile = document.getElementById("id_CompsCreateIssue_File");
      if (dominputfile.files.length == 1) {
        // 拿到文件对象
        var file = dominputfile.files[0];
        // 判断是否已存在同名的附件列表
        // issuevue.extdata.detaildata.relationFiles
        if (
          _this.extdata.detaildata.relationFiles.filter(
            x => x.FileName == file.name
          ).length > 0
        ) {
          _this.$message.error("已存在相同名称的附件");
          return;
        } else {
          // 提交给服务器
          // 拿到其它参数
          var ProjectID = _this.$staticmethod._Get("organizeId");
          var File = file;
          var FileId = FileId;

          // 提交给服务器
          var fd = new FormData();
          var userid = _this.$staticmethod.Get("UserId");
          let FileKey=this.$md5(new Date().getTime()+''+File.name);
          fd.append("ProjectID", ProjectID);
          fd.append("BusinessId", 0);
          fd.append("AttachmentType", '2');
          fd.append("FileName", File.name);
          fd.append("FileKey", FileKey);
          fd.append("FileSize", File.size);
          fd.append("ChunkNumber", 1);
          fd.append("Index", 1);
          fd.append("UserId", userid);
          fd.append("File", File);
          var config = {
            headers: {
              "Content-Type": "multipart/form-data"
            }
          };
          _this.$axios
            .post(
              `${window.bim_config.webserverurl}/api/v1/attach/upload?Token=${this.$staticmethod.Get('Token')}`,
              fd,
              config
            )
            .then(x => {
              if (x.data.Ret == 1 && x.data) {
                let _data = x.data.Data.Data
                _this.EditIssueAddRel(_data, _this.extdata.detaildata.issueId);
                // 关联文档数目及时更新 增加数量及时增加
               var _RelationIssueId = _this.extdata.detaildata.issueId;
              _this.extdata.issueStatusItems.map((itemarr)=>{
                itemarr.IssueItems.some(item => {
                  if(item.IssueId === _RelationIssueId){
                    item.FileCount ++;
                    return true
                  }
                });
              })
              }
            })
            .catch(x => {
               _this.$message.error(`服务器处理发生错误，可能是文件类型不符合白名单规则，请检查文件`);
            });
        }
      }
    },

    // 添加本地附件
    addlocalrel() {
      var _this = this;
      this.closeMoreListDialog();
      // 重置 id 为 id_CompsCreateIssue_File 的 input 元素
      var dominputfile = document.getElementById("id_EditIssue_File");
      //dominputfile.outerHTML = dominputfile.outerHTML;
      dominputfile.value = "";
      // 点击 input 元素，弹出文件选择对话框
      dominputfile.click();
    },
    // 添加本地图片按钮 事件
    addlocalpic() {
      var _this = this;
      // 重置 id 为 id_CompsCreateIssue_File 的 input 元素
      var dominputfile = document.getElementById("id_CompsCreateIssue_File");
      //dominputfile.outerHTML = dominputfile.outerHTML;
      dominputfile.value = "";
      // 点击 input 元素，弹出文件选择对话框
      dominputfile.click();

    },
    // 根据主键删除评论
    removecomment(issue_talkid, issueId) {
      var _this = this;
      //console.log(issue_talkid, issueId);
      _this
        .$axios({
          method: "post",
          url: `${window.bim_config.webserverurl}/api/User/Issue/RemoveComment`,
          data: {
            Token: _this.$staticmethod.Get("Token"),
            IssueId: issueId,
            Issue_TalkId: issue_talkid
          }
        })
        .then(x => {
          // 删除后刷新
          if (x.status == 200 && x.data.Ret > 0) {
            _this.extdata.detaildata.comments = _this.extdata.detaildata.comments.filter(
              y => y.issue_talkid != issue_talkid
            );

          // 删除评论 页面评论数量及时更新（改好后）
          var _RelationIssueId = _this.extdata.detaildata.issueId;
            _this.extdata.issueStatusItems.map((itemarr)=>{
              itemarr.IssueItems.some(item => {
                if(item.IssueId === _RelationIssueId){
                  item.TalkCount = _this.extdata.detaildata.comments.length;
                  return true
                }
              });
            })
          }
        })
        .catch(x => {});
    },

    // 移除自己发的评论
    removeselfcomment(issue_talkid) {
      var _this = this;
      _this
        .$confirm("确认删除该条评论？", "操作确认", {
          confirmButtonText: "确认",
          cancelButtonText: "取消",
          type: "warning"
        })
        .then(x => {
          _this.removecomment(issue_talkid, _this.extdata.detaildata.issueId);
        })
        .catch(x => {
          // 不做任何操作
        });
    },
    equaluser(comm) {
      var _this = this;
      var userId = _this.$staticmethod.Get("UserId");
      var userIdComm = comm.userid;
      return userIdComm == userId;
    },
    // 添加评论
    add_comment() {
      var _this = this;
      // 新版本评论 新组件
      var _Title = _this.add_comment_text.text;
      if(_this.R_lastTime){
        _this.R_lastTime = false 
        if (!_Title || _Title.length == 0 || _Title.trim().length == 0) {
        _this.$message.error("评论内容不能为空！");
        _this.R_lastTime = true 
        return;
      }

      var _Token = _this.$staticmethod.Get("Token");
      var _organizeId = _this.$staticmethod._Get("organizeId");
      var _RelationIssueId = _this.extdata.detaildata.issueId;
      var AtUserIds = '';
      var atcontrolusers = _this.add_comment_text.subscript;
      for (var i = 0; i < atcontrolusers.length; i++) {
        AtUserIds += `${atcontrolusers[i].UserId}${i == atcontrolusers.length - 1?'':','}`
      }
      _this
        .$axios({
          method: "post",
          url: `${this.$issueBaseUrl.AddComment}`,
          data: {
            Token: _Token,
            Title: _Title,
            organizeId: _organizeId,
            RelationIssueId: _RelationIssueId,
            AtUserIds: AtUserIds
          }
        })
        .then(x => {
        _this.R_lastTime = true 
          if (x.status == 200 && x.data.Ret > 0) {
            // 评论追加dom 或重新请求评论
            // 清空输入 
            // 清空评论输入框，遍历了下 只显示当前展开问题类型得评论 clearText包含了获取焦点
            for (var i = 0; i < _this.$refs.commentAtInput.length; i++) {
              _this.$refs.commentAtInput[i].clearText();
            }

            //清空评论输入框得同时 title也清空
            _this.add_comment_text.text = '',
            console.log(3408,_this.add_comment_text)
            // document.getElementById('myInput').test='';
            // console.log(document.getElementById('myInput').test)
            // debugger
            // 添加当前正在显示的评论数据
            var toinsertBeforeObj = {
              content: x.data.Data.Content,
              contenttype: x.data.Data.ContentType,
              createdate: x.data.Data.CreateDate,
              deletemark: x.data.Data.DeleteMark,
              innerlist: [],
              issue_ptalkid: x.data.Data.Issue_PTalkId,
              issue_talkid: x.data.Data.Issue_TalkId,
              issueid: x.data.Data.IssueId,
              realname: _this.$staticmethod.Get("RealName"),
              targetids: "",
              userid: x.data.Data.UserId,
              userofunread: x.data.Data.UserOfUnRead
            };
            _this.extdata.detaildata.comments.unshift(toinsertBeforeObj);

            // 同步主页面评论数 及时更新
            _this.extdata.issueStatusItems.map((itemarr)=>{
              itemarr.IssueItems.some(item => {
                if(item.IssueId === _RelationIssueId){
                  item.TalkCount = _this.extdata.detaildata.comments.length;
                  return true
                }
              });
            })
          }
        })
        .catch(x => {});
      }
    },
    timeToString(date) {
      var year = date.getFullYear();
      var month = date.getMonth() + 1;
      var dat = date.getDate();
      var h = date.getHours();
      var m = date.getMinutes();
      var s = date.getSeconds();
      var tostr = `${year}-${month < 10 ? "0" + month : month}-${
        dat < 10 ? "0" + dat : dat
      } ${h < 10 ? "0" + h : h}:${m < 10 ? "0" + m : m}:${
        s < 10 ? "0" + s : s
      }`;
      return tostr;
    },
    // 创建问题：确定按钮
    createok(obj) {
      var _this = this;
      if (!obj.title || obj.title == "" || obj.title.trim() == '') {
        _this.$message.error("请输入标题");
        EventBus.$emit("R_InitiateProblem",true);
        return;
      }
      if (obj.deadlinetimeval == "") {
        _this.$message.error("请选择截止时间");
        EventBus.$emit("R_InitiateProblem",true);
        return;
      }
      // 参数：截止时间
      var timestr = _this.timeToString(obj.deadlinetimeval);
      // 参数：问题标题
      var title = obj.title;
      // 参数：参与人
      var joinerstr = "";
      for (var i = 0; i < obj.addingjoiners.length; i++) {
        joinerstr += `${i == 0 ? "" : ","}${obj.addingjoiners[i].UserId}`;
      }
      // 参数：附件FileId
      var fileidstr = "";
      for (var i = 0; i < obj.addingFiles.length; i++) {
        fileidstr += `${i == 0 ? "" : ","}${obj.addingFiles[i].FileId}`;
      }
      // debugger
      // 调用问题接口，添加问题，在回调中刷新问题（依据当前过滤器进行刷新）。
      
      _this
        .$axios({
          method: "post",
          url: `${this.$issueBaseUrl.AddIssue}`,
          data: {
            Token: _this.$staticmethod.Get("Token"),
            Title: title,
            JoinerIds: joinerstr,
            FileIds: fileidstr,
            ImageIds: obj.ImageIds,
            EndDateStr: timestr,
            OrganizeId: _this.$staticmethod._Get("organizeId"),
            RealName: _this.$staticmethod.Get("RealName"),
            ModelID:obj.ModelID,
            ImageUrl:obj.ImageUrl,
            ViewPointID:obj.ViewPointID,
            IssueTypeId: obj.IssueTypeId || '',
            addingPic:  obj.addingPic,
          }
        })
        .then(x => {
          if (x.status == 200 && x.data && x.data.Ret < 0) {
            _this.$message.error(`${x.data.Msg}`);
            
            return; 
          }
          // 刷新
          _this.refreshbystatus();
          _this.$message.success('发起问题成功！');
          EventBus.$emit("R_InitiateProblem",true);
        })
        .catch(x => {
          // 刷新
          _this.refreshbystatus();
        });
    },

    // 依据当前过滤器（仅过滤器）刷新数据
    refreshbystatus(autoShowIssueId) {
      var _this = this;
      _this.extdata.showcreate = false;
      _this.extdata.showhover = false;
      _this.mask = false;
      //关闭二级下拉
      // _this.tree_show = false;
      _this.loadStatusItemsTypes(autoShowIssueId);
      _this.loadStatusItemsStatus(autoShowIssueId);
    },
    // 开始执行某问题的文档移除关联操作
    removedocrel(issueId, fileId, isremovingfile) {
      var _this = this;
      _this
        .$axios({
          method: "post",
          url: `${
            window.bim_config.webserverurl
          }/api/User/Issue/RemoveIssueDocRel`,
          data: {
            Token: _this.$staticmethod.Get("Token"),
            IssueId: issueId,
            FileId: fileId
          }
        })
        .then(x => {
          var removingindex = 0;
          if (x.status == 200 && x.data.Ret > 0) {
            // 判断所移除的是详情中的关联图片，还是关联的文件
            // console.log(`当前移除的是${isremovingfile?'文件':'图片'}`);
            // 判断 isremovingfile 来操作 base_files 或 relationFiles
            if (isremovingfile) {
              _this.extdata.detaildata.fulldata.relationFiles
              = _this.extdata.detaildata.fulldata.relationFiles.filter(x => x.FileId != fileId);
              //debugger
              // 删除文件后详情页面文档跟着消失（就是过滤掉doc数组中删除得文档）
              _this.extdata.detaildata.relationFiles
              = _this.extdata.detaildata.relationFiles.filter(x => x.FileId != fileId);
              // console.log(_this.extdata.detaildata.relationFiles)
            } else {
              // 记录原来被删图片的原索引位置
              removingindex = _this.extdata.detaildata.fulldata.base_files.findIndex(x => x.bf_guid == fileId);
              // 0 1 2 3 4
              // 排除
              _this.extdata.detaildata.fulldata.base_files
              = _this.extdata.detaildata.fulldata.base_files.filter(x => x.bf_guid != fileId);
              // debugger
              // console.log('移除的是图片', _this.bgpicture_id, fileId);
              // 当前移除的是关联图片而非文件
              // 只需要处理移除掉详情中正在查看的大图的情况
              if (_this.bgpicture_id == fileId) {
                // 判断剩下的图片的个数
                if (_this.extdata.detaildata.fulldata.base_files.length == 0) {
                  // 已经没有关联图片了
                  // 判断有无关联模型
                  // 简易写法，直接赋值
                  _this.bgpicture_src = _this.extdata.detaildata.fulldata.issueObj.ImageUrl || '';
                  _this.bgpicture_title = _this.extdata.detaildata.fulldata.modelname || '';
                  // 不显示删除按钮，最多也就有个模型，或是没有
                  _this.extdata._detailshowingmodel = true;
                } else {
                  // 还剩下非文件，非关联模型的图片呢。（至少一张）
                  // 需要显示哪张
                  var _bfs_ = _this.extdata.detaildata.fulldata.base_files;
                  if (removingindex >= _bfs_.length) {
                    _this.bgpicture_src = `${window.bim_config.webserverurl}/${_bfs_[_bfs_.length - 1].bf_path}`;
                    _this.bgpicture_title = _bfs_[_bfs_.length - 1].bf_filename;
                    _this.bgpicture_id = _bfs_[_bfs_.length - 1].bf_guid;
                  } else {
                    _this.bgpicture_src = `${window.bim_config.webserverurl}/${_bfs_[removingindex].bf_path}`;
                    _this.bgpicture_title = _bfs_[removingindex].bf_filename;
                    _this.bgpicture_id = _bfs_[removingindex].bf_guid;
                  }
                }
              }
            }
            // 更新附件数
            var issueObj = _this.getIssueObj(issueId);
            if (!issueObj) {
              // console.error(`从详情中移除图片或文件后，未找到指定问题:${issueId}`);
              return;
            }
            issueObj.base_files = _this.$staticmethod.DeepCopy(_this.extdata.detaildata.fulldata.base_files);
            issueObj.FileCount
            = _this.extdata.detaildata.fulldata.base_files.length
            + _this.extdata.detaildata.fulldata.relationFiles.length;
          } else {
             _this.$message.error(x.data.Msg)
          }
        })
        .catch(x => {});
    },
    // 准备提示用户是否确认移除该关联
    // 删除图片关联
    begin_removedocrel(issueId, fileId) {
      var _this = this;
      //console.log(issueId, fileId);
      // 弹出提示判断是否确认操作
      _this
        .$confirm("确认移除此图片", "操作确认", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
        .then(x => {
          _this.removedocrel(issueId, fileId, false);
        })
        .catch(x => {
          // 取消了操作
        });
    },
    // 删除非图片关联文档
    begin_removedocrel_D(issueId, fileId) {
      var _this = this;
      console.log(issueId, fileId);
      // 弹出提示判断是否确认操作
      _this
        .$confirm("确认移除此文件", "操作确认", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
        .then(x => {
          _this.removedocrel(issueId, fileId, true);
        })
        .catch(x => {
          // 取消了操作
        });
    },
    // 指定当前正在加载 laoding
    // 通过问题ID查询问题详情相关数据
    // 切换到详情选项卡。
    loadIssueDetail(issueId) {
      var _this = this;
      _this.extdata.isloadingcurrentdetail = true;
      _this.extdata.detailshowtype = "dst_detail";
      var token = _this.$staticmethod.Get("Token");

      // 请求数据
      _this
        .$axios({
          method: "get",
          url: `${this.$issueBaseUrl.GetIssueDetail}?token=${token}&issueId=${issueId}`
        })
        .then(x => {
          if(x.data.Ret != 1){
            _this.extdata.isloadingcurrentdetail = false;
            console.error(x.data.Msg)
            return
          }
          _this.extdata.detaildata.fulldata = x.data.Data; 
          _this.extdata.detaildata.issueId = issueId;

          // 清空评论内容
          _this.add_comment_text.text = '';

          // 创建时间
          var createDate = x.data.Data.issueObj.CreateDate.replace(/T/g, " ");
          createDate = createDate.substr(0, createDate.length - 3);
          _this.extdata.detaildata.createtime = createDate;

          // 截止时间
          var endDate = x.data.Data.issueObj.EndDate.replace(/T/g, " ");
          endDate = endDate.substr(0, endDate.length - 3);
          _this.extdata.detaildata.endtime = endDate;

          // 问题状态
          _this.extdata.detaildata.IssueStatusText =
            x.data.Data.issueObj.IssueStatusText;

          //问题类型
            _this.extdata.detaildata.IssueTypeText =
            x.data.Data.issueObj.IssueTypeText;

          // 参与人
          _this.extdata.detaildata.joiners = x.data.Data.joiners;

          // 获取这个参与人后，把参与人作为 评论中@ 的人员池的依据
          // _this.userList = _this.extdata.detaildata.joiners;
          _this.setCommentAtUserList(_this.extdata.detaildata.joiners);
          // 评论
          _this.extdata.detaildata.comments = x.data.Data.comments;
          // 问题关联文档
          _this.extdata.detaildata.relationFiles = x.data.Data.relationFiles;
          // 详情中显示默认图片
          // 设置 bgpicture_src 及 bgpicture_title
          if (x.status == 200 && x.data.Ret && x.data.Data) {
            // 初始化详情中的标题位置显示的名称及图片路径，并返回当前问题是否是从模型中发起的
            _this.extdata._detailshowingmodel = _this.initdetailSrcAndName(x.data.Data);

          }
          // 把接口返回的数据存储到selectItem中
          _this.selectitem.extendData = x.data.Data;
          //      _this.activitiesList = x.data.Data
          _this.activitiesList = x.data.Data.activities;
          // taglist
          _this.issuedetailTags = x.data.Data.tagList;
          // 隐藏 loading
          _this.extdata.isloadingcurrentdetail = false;
        })
        .catch(x => {
          // debugger;
          _this.extdata.isloadingcurrentdetail = false;
        });
    },
    changedetailshowtype(dst) {
      var _this = this;
      if(dst == 'dst_comments'){
        if (_this.extdata.newissuedis) {
          _this.$message.error('当前用户无编辑权限');
          return;
        }
      }
      this.closeMoreListDialog();
      _this.extdata.detailshowtype = dst;
    },
    /**
     * 计算两个时间差，比如计算剩余时间 window.zdate.getdateTimeDiff('2012-12-01T12:12:12', '2012-12-03T12:12:11');
     * @param {any} datetime1 开始时间或现在时间
     * @param {any} datetime2 结束时间或截止时间
     */
    getdateTimeDiff(datetime1, datetime2) {
      var begintime_ms = Date.parse(datetime1); //begintime 为开始时间
      var endtime_ms = Date.parse(datetime2); // endtime 为结束时间
      var msdiff = endtime_ms - begintime_ms;
      // 计算出天数
      var daydiff = Math.floor(msdiff / (24 * 3600 * 1000));
      msdiff -= daydiff * (24 * 3600 * 1000);
      // 计算出小时
      var hdiff = Math.floor(msdiff / (3600 * 1000));
      msdiff -= hdiff * (3600 * 1000);
      // 计算出分钟
      var mdiff = Math.floor(msdiff / (60 * 1000));
      return `${daydiff}天${hdiff}时${mdiff}分`;
    },

    /**
     * 将 date 对象转为时间字符串
     * @param {any} dt
     */
    convertDateToStr(dt) {
      var year = dt.getFullYear();
      var month = dt.getMonth() + 1;
      var date = dt.getDate();
      var hour = dt.getHours();
      var minute = dt.getMinutes();
      var second = dt.getSeconds();
      return (
        year +
        "-" +
        month +
        "-" +
        date +
        " " +
        hour +
        ":" +
        minute +
        ":" +
        second
      );
    },

    // 点击问题详情右上角关闭按钮
    closeIssueDetail(item, ev) {
      var _this = this;
      if (ev) {
        ev.stopPropagation();
      }
      // 隐藏标签池下拉框
      _this.tagList_show = false;

      item.RightIsOpen = false;
      var items = item.IssueItems;
      for (var i = 0; i < items.length; i++) {
        items[i].RightIsOpen = false;
      }
      _this.extdata.showhover = false;
      _this.mask = false;
      //关闭二级下拉
      _this.tree_show = false;
      _this.add_comment_text.text = '';
    },
    // 点击页面其他位置已展开的详情页关闭
    hidehover(el){
      // debugger;
      var _this = this;
      if (el.target.contains(this.ParticipanInfoPtanelDom)) {
        return false;
      }
      // 点击添加参与人后 成功添加参与人 再次点击页面其他位置 详情关闭
      for (var i = 0; i < _this.extdata.issueStatusItems.length; i++) {
        for (
          var j = 0;
          j < _this.extdata.issueStatusItems[i].IssueItems.length;
          j++
        ) {
          _this.extdata.issueStatusItems[i].IssueItems[j].RightIsOpen = false;
        }
        _this.extdata.issueStatusItems[i].RightIsOpen = false;
      }
      _this.extdata.showhover = false;
      _this.mask = false;
      //树型下拉二级菜单关闭
      _this.tree_show = false;
      _this.tagList_show = false;
      _this.bShowIssueBtns = false;
      _this.listhover_status = false;
      _this.listhover_type = false;
    },
    // 取消创建问题
    cancelcreate() {
      var _this = this;
      _this.extdata.showcreate = false;
    },
    // 创建问题按钮点击
    btncreateissue_click() {
      var _this = this;
      _this.extdata.showcreate = true;
    },
    rightpartenter(item_item2) {
      var _this = this;
      if (item_item2.RightIsOpen == false) {
        return;
      }
      _this.extdata.showhover = true;
      _this.mask = true;
    },
    rightpartleave(item_item2) {
      var _this = this;
      if (item_item2.RightIsOpen == false) {
        return;
      }
      _this.extdata.showhover = false;
    },
    // 过滤按钮点击 控制抽屉的显示和隐藏
    filtration_click(){
      this.show = !this.show;
      if(this.show){
        this.$refs.sq_drawer.style.left = 0;
        this.selectedStatusCheckoutbox = [];
      }else{
        this.$refs.sq_drawer.style.left = "-260px"
      }
    },
    // 切换当前问题的父容器的详情显示状态
    switchDetail(item, item2, ev) {
      var _this = this;
      if (ev) {
        ev.stopPropagation();
      }
      _this.tagList_show = false;
      _this.bShowIssueBtns = false;
      //debugger;
      _this.extdata.detaildata.fulldata.isIssueManager = false;
      this.extdata.detaildata.isaddingjoiner = false;
      this.item = item;
      this.selectitem = item2;
      // debugger
      // 重置其它问题项的 RightIsOpen 属性
      for (var i = 0; i < _this.extdata.issueStatusItems.length; i++) {
        for (
          var j = 0;
          j < _this.extdata.issueStatusItems[i].IssueItems.length;
          j++
        ) {
          if (
            _this.extdata.issueStatusItems[i].IssueItems[j].IssueId !=
            item2.IssueId
          ) {
            _this.extdata.issueStatusItems[i].IssueItems[j].RightIsOpen = false;
          }
        }
        _this.extdata.issueStatusItems[i].RightIsOpen = false;
      }
      // 切换当前问题项的 RightIsOpen 属性
      if (item2.RightIsOpen == false) {
        item2.RightIsOpen = true;
        // 显示loading，并查询 item2.IssueId 对应的详情数据，切换到详情标签页。
        _this.loadIssueDetail(item2.IssueId);
        item2.Parent.RightIsOpen = true;
        // detailShowingTypeId
        _this.detailShowingTypeId = item2.Parent.ItemDetailId;
        _this.extdata.showhover = true;
        //打开另一个问题时关闭 已打开的二级下拉
        _this.tree_show = false;
        _this.listhover_status = false;
        _this.listhover_type = false;
        // 问题列表设置边距 最边上的不要顶出去
        setTimeout(function(){
          // region
          // 判断 item 是第几个，如果是最后两个，直接滚动到最右面
          var allhasissueTypes = _this.extdata.issueStatusItems.filter(x => x.IssueItems.length > 0);
          var index = allhasissueTypes.findIndex(x => x.ItemDetailId == item2.Parent.ItemDetailId);
          var eleissuebody = document.getElementById("_id_issue_body_scroller");
          eleissuebody.scrollLeft = 292 * (index);
        }, 300);
        _this.mask = true;
      } else {
        item2.RightIsOpen = false;
        item2.Parent.RightIsOpen = false;
        _this.extdata.showhover = false;
        _this.mask = false;
        //关闭二级下拉
        _this.tree_show = false;
        _this.listhover_status = false;
        _this.listhover_type = false;
      }
      _this.add_comment_text.text = '';
    },

    // 加载某一种状态的问题数据 （list修改为status）
    // 添加参数：是否包含归档问题
    loadSometypeissues(ItemDetailId, autoOpenRight, callbackafterload) {
      var _this = this;
      _this.m_globalloading = true;
      _this
        .$axios({
          url: `${this.$issueBaseUrl.GetIssueList}?ContainsArchive=${_this.extdata._containsArchive}&OrganizeId=${_this.$staticmethod._Get(
            "organizeId"
          )}&Token=${_this.$staticmethod.Get(
            "Token"
          )}&IssueTypeId=${ItemDetailId}&IssueStatusId=&Keyword=${
            encodeURIComponent(_this.extdata.keyword)
          }&UserType=`,
          method: "get"
        })
        .then(x => {
          _this.m_globalloading = false;
          if (x.status == 200 && x.data.Ret > 0) {
            // 得到所有标记对象
            _this.AllRelationTagObjs = x.data.Data.AllRelationTagObjs;
            // 得到所有的问题分类的 ItemDetailId，并遍历
            var IssueTypeIdArr = [];
            for (var i = 0; i < x.data.Data.Items.length; i++) {
              if (IssueTypeIdArr.indexOf(x.data.Data.Items[i].IssueTypeID) < 0) {
                IssueTypeIdArr.push(x.data.Data.Items[i].IssueTypeID);
              }
            }
            if (IssueTypeIdArr.length == 0) {
              return;
            }
            // 遍历每一个分类处理 RightIsOpen Parent 等
            for (var tpe of IssueTypeIdArr) {
              var index = _this.extdata.issueStatusItems.findIndex(
                y => y.ItemDetailId == tpe
              );
              if (index >= 0) {
                // 附加属性
                // 提出当前分类的所有问题
                var thistypeissues = x.data.Data.Items.filter(x => x.IssueTypeID
                == _this.extdata.issueStatusItems[index].ItemDetailId);
                // 遍历这些问题，处理属性
                for (var i = 0; i < thistypeissues.length; i++) {
                  thistypeissues[i].RightIsOpen = false;
                  thistypeissues[i].Parent =
                  _this.extdata.issueStatusItems[index];
                  //autoOpenRight
                  if (autoOpenRight == thistypeissues[i].IssueId) {
                    //debugger;
                    thistypeissues[i].RightIsOpen = true;
                    thistypeissues[i].Parent.RightIsOpen = true;
                    _this.detailShowingTypeId = thistypeissues[i].Parent.ItemDetailId;
                    if (callbackafterload){
                      setTimeout(()=>{
                         callbackafterload();
                      }, 500);
                    }
                    _this.loadIssueDetail(thistypeissues[i].IssueId);
                  }
                }
                _this.extdata.issueStatusItems[index].IssueItems =
                  thistypeissues;
              }
            } 
                _this.$nextTick(()=>{
                    for (var tp of IssueTypeIdArr) {
                      var ele = document.getElementById(`id_${tp}`); 
                      if(ele!= null && ele.scrollHeight - ele.scrollTop == ele.clientHeight){
                        for(var i of _this.extdata.issueStatusItems){
                          if(i.ItemDetailId === tp){
                            i.scroll_down = false;
                            break;
                          }
                        }
                      }else{
                        for(var i of _this.extdata.issueStatusItems){
                          if(i.ItemDetailId === tp){
                            i.scroll_down = true;
                            break;
                          }
                        }
                      } 
                    }
                });

          } else {
            _this.ajaxerror(x);
          }
        })
        .catch(x => {
          _this.m_globalloading = false;
          _this.ajaxerror(x);
        });
    }, 
    // 为每一种问题状态加载对应的问题数据。
    loadEveryStatusIssues(type, autoOpenRight) {
      var _this = this;
      _this.loadSometypeissues('', autoOpenRight);

    },
    // 加载所有问题的类型
    loadStatusItemsTypes(autoShowIssueId) {
      var _this = this;
      // 新版本已修改
      _this
        .$axios({
          method: "get",
          url: `${this.$issueBaseUrl.GetIssueTypes}?organizeId=${_this.$staticmethod._Get(
            "_OrganizeId"
          )}&token=${_this.$staticmethod.Get("Token")}`
        })
        .then(x => {
          if (x.status == 200 && x.data.Ret > 0) {
            // 强制为 x.data.Data 中的每一个元素添加上 IssueItems为[]
            // 强制为 x.data.Data 中的每一个元素添加上 RightIsOpen = false
            //debugger;
            for (var i = 0; i < x.data.Data.length; i++) {
              x.data.Data[i].IssueItems = [];
              x.data.Data[i].RightIsOpen = false;
              //加载问题类型时赋值showdown
              x.data.Data[i].scroll_up=false;
              x.data.Data[i].scroll_down=false;
              x.data.Data[i].isshowscroll = false;
            }
            var arr = [];
            for (var i = 0; i < x.data.Data.length; i++) {
              arr.push({
                id: x.data.Data[i].ItemDetailId,
                text: x.data.Data[i].ItemName
              });
            }
            _this.extdata.issueStatusDropItems = arr;
            _this.extdata.issueStatusItems = x.data.Data;
            // 先赋予属性 IssueItems
            for (var i = 0; i < _this.extdata.issueStatusItems.length; i++) {
              _this.extdata.issueStatusItems[i].IssueItems = [];
            }
            // 传入所有的问题状态，依次调用接口，获取对应的问题
            _this.loadEveryStatusIssues(x.data.Data, autoShowIssueId);
          } else {
            _this.ajaxerror(x);
          }
        })
        .catch(x => {
          _this.ajaxerror(x);
        });
    },
    // 加载所有问题的类型
    loadStatusItemsStatus(autoOpenRight) {
      var _this = this;
      _this
        .$axios({
          method: "get",
          url: `${
            this.$issueBaseUrl.GetIssueStatus
          }?organizeId=${_this.$staticmethod._Get(
            "_OrganizeId"
          )}&token=${_this.$staticmethod.Get("Token")}`
        })
        .then(x => {
          if (x.status == 200 && x.data.Ret > 0) {
            // 强制为 x.data.Data 中的每一个元素添加上 IssueItems为[]
            // 强制为 x.data.Data 中的每一个元素添加上 RightIsOpen = false
            //debugger;
            for (var i = 0; i < x.data.Data.length; i++) {
              x.data.Data[i].IssueItems = [];
              x.data.Data[i].RightIsOpen = false;
            }
            _this.extdata.issueStatusItemsStatus = x.data.Data;
            var arr = [];
            for (var i = 0; i < x.data.Data.length; i++) {
              arr.push({
                id: x.data.Data[i].ItemDetailId,
                text: x.data.Data[i].ItemName
              });
            }
            _this.extdata.issueStatusDropItems = arr; 
          } else {
            _this.ajaxerror(x);
          }
        })
        .catch(x => {
          _this.ajaxerror(x);
        });
    },
    sortlist(type){
      if(type){
        for(var i=0;i<this.extdata.issueStatusItems.length;i++){
          if(type == 1){
            this.extdata.issueStatusItems[i].IssueItems.sort(this.Sort('CreateDate',1))
          }else if(type == 2){
            this.extdata.issueStatusItems[i].IssueItems.sort(this.Sort('CreateDate',0))
          }else if(type == 3){
            this.extdata.issueStatusItems[i].IssueItems.sort(this.Sort('Residue',1))
          }else if(type == 4){
            this.extdata.issueStatusItems[i].IssueItems.sort(this.Sort('Residue',0))
          }else if(type == 5){
            // 问题状态正序
            // debugger;
            // this.extdata.issueStatusItems[i].IssueItems.sort(this.Sort('bdid_status_SortCode',1))
            this.extdata.issueStatusItems[i].IssueItems
            .sort((x,y) => x.bdid_status_SortCode - y.bdid_status_SortCode);

          }else if(type == 6){
            // 问题状态倒序
            //this.extdata.issueStatusItems[i].IssueItems.sort(this.Sort('bdid_status_SortCode',0))
             this.extdata.issueStatusItems[i].IssueItems
            .sort((x,y) => y.bdid_status_SortCode - x.bdid_status_SortCode);
          }
        }
      }else{
        var _this = this;
        _this.loadStatusItemsTypes();
      }
    },
    Sort(attr,PositiveOrReverse) {//指定某个属性的排序
        if(attr == "CreateDate"){
          return function (a,b) {
            if(PositiveOrReverse){
              return Date.parse(b[attr]) - Date.parse(a[attr])
            }
            return Date.parse(a[attr]) - Date.parse(b[attr])
          }
        }else if(attr == 'Residue'){
          return function (a,b) {
            var x = Date.parse(b.EndDate)-Date.parse(new Date());
            var z = Date.parse(a.EndDate)-Date.parse(new Date());
            if(PositiveOrReverse){
              return x-z;
            }
            return z-x;
          }
        }else if(attr == 'bdid_status_SortCode'){
          return function (a,b) {
            if(PositiveOrReverse){
              return b[attr] - a[attr]
            }
            return a[attr] - b[attr]
          }
        }

    },
    status(issueStatusItemsitem){
      //debugger;
      this.issuedetailstatusselected({
        id:issueStatusItemsitem.ItemDetailId
      });
    },
    type(issueStatusItemsitem){
      this.issuedetailselectedType({
        id:issueStatusItemsitem.ItemDetailId
      });
    },
    // 删除问题定义接口
    removeissue(issueId) {
      var _this = this;
      _this
        .$axios({
          method: "post",
          url: `${this.$issueBaseUrl.RemoveIssue}`,
          data: {
            Token: _this.$staticmethod.Get("Token"),
            IssueId: issueId
          }
        })
        .then(x => {
          var _this = this;
          _this.loadStatusItemsTypes();
          _this.extdata.showhover = false;
        })
        .catch(x => {});
    },
    // 删除问题
    removeselfissue() {
      var _this = this;
      var issueId = this.selectitem.IssueId;
      _this
        .$confirm("确认删除该条问题？", "操作确认", {
          confirmButtonText: "确认",
          cancelButtonText: "取消",
          type: "warning"
        })
        .then(x => {
          _this.removeissue(issueId);
        })
        .catch(x => {
         
        });
    },

    // 归档按钮点击 重新掉接口刷新数据
    archive_click(){
      var _this = this;
      if (_this.extdata._containsArchive == 1){
        _this.extdata._containsArchive = 0;
      } else {
        _this.extdata._containsArchive = 1;
      }
      setTimeout(()=>{
        _this.loadStatusItemsTypes();
      }, 500);
      // console.log(_this.extdata._containsArchive)
    },
    // 归档问题权限限制 项目管理员 问题创建者 只有关闭状态得问题才可以归档
    ispmorissuecreator(){
      this.remove_ispmorissuecreator('1')
    },
    // 归档问题定义接口
    archiveissue(issueId, IntVal) {
      this
        .$axios({
          method: "post",
          url: `${this.$issueBaseUrl.SetIssueDeleteMark}`,
          data: {
            Token: this.$staticmethod.Get("Token"),
            Id: issueId,
            IntVal: IntVal + ''
          }
        })
        .then(x => {
          if (x.data.Ret < 0) {
            this.$message.error(x.data.Msg)
          } else {
            // 归档后刷新
            this.refreshbystatus(issueId);          
          }
        })
        .catch(x => {});
    },

    // 归档问题
    archiveselfissue() {
      var _this = this;
      var issueId = this.selectitem.IssueId;
      _this
        .$confirm("确认归档该条问题？", "操作确认", {
          confirmButtonText: "确认",
          cancelButtonText: "取消",
          type: "warning"
        })
        .then(x => {
          _this.archiveissue(issueId, 2);
        })
        .catch(x => {
          // 不做任何操作
        });
    },

  // 移出归档问题权限限制 项目管理员 问题创建者 只有关闭状态得问题才可以归档
    remove_ispmorissuecreator(number){
      var _this = this;
      _this
          .$axios({
            method: "get",
            url: `${this.$issueBaseUrl.IsPMOrIssueCreator}?token=${_this.$staticmethod.Get("Token")}&organizeId=${_this.$staticmethod._Get(
              "organizeId"
            )}&issueId=${_this.extdata.detaildata.issueId}`
          })
          .then(x => {
            if(x.status == 200 && x.data.Ret > 0){
              if(!number){
                _this.remove_archiveselfissue();
                return
              }
              if(number == '1' && this.extdata.detaildata.IssueStatusText === '关闭'){
                _this.archiveselfissue();
              }else{
                _this.$message.error('该问题未关闭，不能归档');
              }
              
            }else{
              _this.$message.error('您不具备权限');
            } 
          })
          .catch(x => {
            var _this = this;
            _this.$message.error('访问失败');
          });
    },
    // 移出归档问题
    remove_archiveselfissue() {
      var _this = this;
      var issueId = this.selectitem.IssueId;
      _this
        .$confirm("确认移出归档该条问题？", "操作确认", {
          confirmButtonText: "确认",
          cancelButtonText: "取消",
          type: "warning"
        })
        .then(x => {
          _this.archiveissue(issueId, 0);
        })
        .catch(x => {
          // 不做任何操作
        });
    },
    click_bgpicture_window(item2, bf_path) {
      item2.bgpicture_src = window.bim_config.webserverurl + '/' + bf_path;
    },
    // 点击小图记录下表 还有图片name
    click_bgpicture(item2, src){
      var _this = this;
      item2.bgpicture_src = src;
    },

    // 点击详情中的模型小图
    click_bgpicture_details_model(){
      var _this = this;
      console.log('click_bgpicture_details_model');
      // 标识当前详情中，正展示的是模型。
      _this.extdata._detailshowingmodel = true;
      _this.bgpicture_title = _this.selectitem.extendData.modelname;
      _this.bgpicture_src = _this.selectitem.extendData.issueObj.ImageUrl;
    },

    click_bgpicture_details(item){
      var _this = this;
      console.log('click_bgpicture_details');
      // 标识当前详情中，正展示的是图片文件。
      _this.extdata._detailshowingmodel = false;
      // item2.bgpicture_src = src;
      _this.bgpicture_title = item.FileName;
      _this.bgpicture_src = _this.filterimg(item.FileId);
      _this.bgpicture_id = item.FileId;
    },
    // 组件正在添加参与人提示框 确认按钮事件
    addToRole_OK(userId, userName){
      // 弹出提示：确认添加该成员到角色中？
      //console.log(userId, userName);
      var _this = this;
      // 判空
      if (!userId) {
        _this.$message.error('请先选择参与人');
        return;
      }
      // 判重
      var hasIndex = _this.extdata.detaildata.joiners.findIndex(x => x.UserId == userId);
      if (hasIndex >= 0) {
        _this.$message.error('当前参与人已存在，请勿重复添加');
        return;
      }else{
        // 添加参与人（不用再次提示确认添加）
        _this.addToJoiner({UserId: userId});
      }
    },
    addToJoiner(user){
      var _this = this;
        _this
        .$axios({
          method: "post",
          url: `${this.$issueBaseUrl.AddIssueJoiner}`,
          data: {
            Token: _this.$staticmethod.Get("Token"),
            Id: user.UserId,
            IssueId: _this.extdata.detaildata.issueId
          }
        })
        .then(x => {
          if (x.status == 200) {
            if (x.data.Ret > 0) { 
              //刷新问题分类
              // 起点分类
              var nowTypeId = _this.extdata.detaildata.fulldata.issueObj.IssueTypeID;
              // 目标分类
              var targetTypeId = nowTypeId;
              //刷新问题 目标分类
              _this.loadSometypeissues(targetTypeId, _this.extdata.detaildata.issueId,
                () => {
                  // 横向定位
                  var allhasissueTypes = _this.extdata.issueStatusItems.filter(x => x.IssueItems.length > 0);
                  var afternewtypeindex = allhasissueTypes.findIndex(x => x.ItemDetailId == targetTypeId);
                  var eleissuebody = document.getElementById("_id_issue_body_scroller");
                  eleissuebody.scrollLeft = 292 * (afternewtypeindex);
                }
              );
            } else {
              _this.$message.error(`服务器错误 (${x.data.Msg})`);
            }
          }
          _this.isaddingmember = false;
        })
        .catch(x => {
          _this.isaddingmember = false;
        });
    },
    // 原生写树型下拉盒子点击显示隐藏事件
    open_tree_box(){
      var _this = this;
      if (_this.extdata.newissuedis) {
        _this.$message.error('当前用户无编辑权限');
        return;
      }
      _this.tree_show = !_this.tree_show;
      //关闭下拉 二级下拉
      if(_this.tree_show == false){
        _this.listhover_status = false;
        _this.listhover_type = false;
        // debugger
      }
    },
    // 问题详情更改截止时间事件
    change_time(val){
      // 调用接口，修改问题截止时间
      var _this = this;
      var _EndDate = _this.timeToString(val);
      var _IssueId = _this.extdata.detaildata.fulldata.issueObj.IssueId;
      var _Token = _this.$staticmethod.Get("Token");
      _this.$axios({
        method:'post',
        url:`${this.$issueBaseUrl.ModifyIssueEndDate}`,
        data:{
          Token: _Token,
          IssueId: _IssueId,
          EndDate: _EndDate
        }
      }).then(x => {
        if (x.status == 200) {
          if (x.data.Ret > 0) {
             _this.refreshbystatus(_IssueId);
          } else {
            _this.$message.error(`服务器错误(${x.data.Msg})`);
          }
        } else {
          console.error(x);
        }
      }).catch(x => {
        console.error(x);
      })
    },
  
    // UTC时间转为北京时间，时间戳转为时间
    // var utc_datetime = "2017-03-31T08:02:06Z";
    utc2beijing(utc_datetime) {
        // 转为正常的时间格式 年-月-日 时:分:秒
        var T_pos = utc_datetime.indexOf('T');
        var Z_pos = utc_datetime.indexOf('Z');
        var year_month_day = utc_datetime.substr(0,T_pos);
        var hour_minute_second = utc_datetime.substr(T_pos+1,Z_pos-T_pos-1);
        var new_datetime = year_month_day+" "+hour_minute_second; // 2017-03-31 08:02:06

        // 处理成为时间戳
        timestamp = new Date(Date.parse(new_datetime));
        timestamp = timestamp.getTime();
        timestamp = timestamp/1000;

        // 增加8个小时，北京时间比utc时间多八个时区
        var timestamp = timestamp+8*60*60;

        // 时间戳转为时间
        var beijing_datetime = new Date(parseInt(timestamp) * 1000).toLocaleString().replace(/年|月/g, "-").replace(/日/g, " ");
        return beijing_datetime; // 2017-03-31 16:02:06
    },
    // 图片评论模态框 关闭
    comment_picture_model_close() {
      var _this = this;
      _this.comment_picture_model = false;
      _this.comment_picture_showhover = false;
    },
    //图片预览模态框 关闭事件
    bgpicture_model_close(){
      var _this = this;
      _this.bgpicture_model = false;
      _this.bgpicture_showhover = false;
    },
    // ajaxerror
    ajaxerror(x) {
      var _this = this;
      _this.$staticmethod.debug(x);
    },
    // 标签设置
    setLabelFun(){
      // 设置标签的时候刷新标签列表
      this.getAllTags();
      this.drawerEditShow = !this.drawerEditShow
      if(this.drawerEditShow){
        this.$refs.labelSetDialog.style.left = 0;
      }else{
        this.$refs.labelSetDialog.style.left = "-240px"
      }
    },
    // 导出文件
    downloadIssusExport(){
      let IssueTypeId = '' //  this.extdata.detaildata.fulldata.issueObj.IssueTypeID || '';

      let downloadurl = `${window.bim_config.webserverurl}/api/User/Issue/Export?OrganizeId=${this.$staticmethod._Get("organizeId")}&Token=${this.$staticmethod.Get("Token")}&ContainsArchive=${this.extdata._containsArchive}&IssueStatusId=${this.selectedTagCheckbox}&IssueTypeId=${IssueTypeId}&Keyword=${encodeURIComponent(this.extdata.keyword)}&UserType=&PageNum=&PageSize`
      // ContainsArchive 是否归档   1是；IssueStatusId问题状态ID；IssueTypeId问题分类ID、Keyword问题关键字；UserType：Creator或Manager，不传时为null；PageNum 页码 PageSize 页大小
      console.log(`ContainsArchive=${this.extdata._containsArchive}&IssueStatusId=${this.selectedStatusCheckoutbox}&IssueTypeId=${IssueTypeId}&Keyword=${encodeURIComponent(this.extdata.keyword)}&UserType=&PageNum=&PageSize`,'======')
      window.location.href = downloadurl
    }
  }, 
};
</script>

<style>
.issue-details-deadline .el-date-editor--datetime .el-input__inner {
  cursor: pointer;
  padding: 0;
  line-height: inherit;
  color: #1890ff;
}
</style>
<style scoped>
@import url("../../assets/css/issue.css");
</style>