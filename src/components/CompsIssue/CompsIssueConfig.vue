<template>
  <div class="_css-issueconfig-all">
    <div class="_css-top64">
      <div @click="_onclose($event)" class="_css-topicon icon-arrow-left_outline"></div>
      <div class="_css-toptitle">问题追踪设置</div>

      <!-- tabcontrol -->
      <div class="_css-toptabcontrol">
        <div class="_cs-toptab">标签维护</div>
      </div>
      <!-- //tabcontrol -->
    </div>
    <div class="_css-body">
      <div class="_css-body-in" id="id_body_in">
        <div class="_css-body-in-title">
          <div class="_css-content-label">标签维护</div>
          <div @click="addItem($event)" class="_css-addbtn">
            <div class="_css-addbtn-icon icon-interface-addnew"></div>
            <div class="_css-addbtn-text">添加新标签</div>
          </div>
        </div>
        <div class="_css-body-in-body">
          <div class="_css-content-list">
            <div v-for="l in listData" :key="l.it_guid" class="_css-content-i">
              <div class="_css-content-in">
                <div class="_css-content-icon" :style="overrideTagItemStyle(l.it_color)">
                  <div :style="_css_content_text(l.it_name)" :title="l.it_name">{{l.it_name}}</div>
                </div>
                <div
                @click="editTagItem(l, $event)"
                  class="_css-content-btn icon-interface-edit_se"
                  :style="overrideTagItemStyle(l.it_color)"
                ></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <CompsColorItem
        ref="cci_item"
      :inittext="CompsColorItem_inittext"
      :initcolor="CompsColorItem_initcolor"
      
      :title="pEditingFormTitle"
      :fieldname="'标签名称'"
      :fieldplaceholder="'请输入标签名称'"
      v-if="bShowingEditingForm"
      @onok="coloritem_onok"
      @oncancel="coloritem_oncancel"
      @onwarning="coloritem_onwarning"
    ></CompsColorItem>
  </div>
</template>
<script>
import CompsColorItem from "@/components/CompsCommon/CompsColorItem";
export default {
  components: {
    CompsColorItem
  },
  data() {
    return {
        CompsColorItem_inittext:'',
        CompsColorItem_initcolor:'',
        editing_it_guid:'',
      listData: [],
      bShowingEditingForm: false, // 显示add 或 edit 弹层
      pEditingFormTitle: "" // add 或 edit 弹层的标题
    };
  },
  computed: {},
  mounted() {
    var _this = this;
    _this.getData();
  },
  methods: {

      do_coloritem_del(){

          // 执行删除操作，成功后记得刷新（_this.getData)
          var _this = this;
          console.log(_this.editing_it_guid);

          // 
          var _Token = _this.$staticmethod.Get("Token");
          var _it_guid = _this.editing_it_guid;


          _this.$axios({
            method: 'post',
              url: `${this.$issueBaseUrl.RemoveIssueTag}`,
              data: {
                  Token: _Token,
                  it_guid: _it_guid
              }
          }).then(x => {
              if (x.data.Ret > 0) {

                  _this.getData();
              } else {
                  _this.$message.error(x.data.Msg);
              }
              _this.bShowingEditingForm = false;
          }).catch(x => {
              console.error(x);
              _this.bShowingEditingForm = false;
          });
      },

      coloritem_onwarning(){

          // 操作确认

          var _this = this;
          _this.$confirm('确定删除该标签？', '操作确认', {
              confirmButtonText: '确定',
              cancelButtonText:'取消',
              type:'warning'
          }).then(x => {
             _this.do_coloritem_del();
          }).catch(x => {
              
          });
      },

      editTagItem(item, ev){

          var _this = this;

          _this.CompsColorItem_inittext = item.it_name;
          //_this.CompsColorItem_initguid = item.it_guid;
          _this.CompsColorItem_initcolor = item.it_color;
          _this.editing_it_guid = item.it_guid;


      // 设置标题
      _this.pEditingFormTitle = "修改标签";

      // 设置显示
      _this.bShowingEditingForm = true;

        _this.$nextTick(()=>{
            _this.$refs.cci_item.setWarningShow(true);
        });




      },
    overrideTagItemStyle(color) {
      var _s = {};
      _s["background-color"] = color;
      return _s;
    },
    _css_content_text(t){
      let _s = {}
      if(t.length>6){
        _s["overflow"] = 'hidden';
        _s["textOverflow"] = 'ellipsis';
        _s["whiteSpace"] = 'nowrap';
        _s["width"] = '96px';
        _s["marginLeft"] = '28px';
      }
      return _s;
    },

    getData() {
      // 显示 loading
      var _this = this;
      var _OrganizeId = _this.$staticmethod._Get("organizeId");
      var _LoadingIns = _this.$loading({
        text: "操作中",
        target: document.getElementById("id_body_in")
      });

      // 请求，完成后隐藏 loading
      _this.$axios
        .get(
          `${this.$issueBaseUrl.GetIssueOrganizeTags}?organizeId=${_OrganizeId}&token=${_this.$staticmethod.Get("Token")}`
        )
        .then(x => {
          if (x.data.Ret > 0) {
            _this.listData = x.data.Data;
          } else {
            _this.$message.error(x.data.Msg);
          }
          _LoadingIns.close();
        })
        .catch(x => {
          console.error(x);
          _LoadingIns.close();
        });
    },
    coloritem_onok(txt, colr) {
      // 服务器端判断文本、颜色是否为空，以及相同项目下是否存在相同名称的问题标签
      var _this = this;

      var _it_guid = "";
      var _Token = _this.$staticmethod.Get("Token");
      var _it_organizeId = _this.$staticmethod._Get("organizeId");
      var _it_color = colr;
      var _it_name = txt;

      var _Url = `${this.$issueBaseUrl.AddOrModifyIssueOrganizeTag}`;

      _this
        .$axios({
          method: "post",
          url: _Url,
          data: {
            it_guid: _this.editing_it_guid,
            Token: _Token,
            it_organizeId: _it_organizeId,
            it_color: _it_color,
            it_name: _it_name
          }
        })
        .then(x => {
          if (x.data.Ret > 0) {
            _this.$message.success("保存成功");

            // 隐藏弹层
            _this.bShowingEditingForm = false;
            _this.getData();
          } else {
            _this.$message.error(x.data.Msg);
          }
        })
        .catch(x => {
          console.error(x);
        });
    },
    addItem(ev) {
      var _this = this;

      // 显示新增窗体
      // -----------

      _this.editing_it_guid = '';
           _this.CompsColorItem_inittext = '';
          //_this.CompsColorItem_initguid = item.it_guid;
          _this.CompsColorItem_initcolor = '';
   

      // 设置标题
      _this.pEditingFormTitle = "添加新标签";

      // 设置显示
      _this.bShowingEditingForm = true;

          
        _this.$nextTick(()=>{
            _this.$refs.cci_item.setWarningShow(false);
        });
    },
    coloritem_oncancel() {
      var _this = this;
      _this.bShowingEditingForm = false;
    },
    _onclose(ev) {
      var _this = this;
      _this.$emit("onclose", ev);
    }
  },
  props: {}
};
</script>
<style scoped>
._css-addbtn {
  display: flex;
  align-items: center;
  position: absolute;
  right: 0;
  margin-right: 24px;
  cursor: pointer;
  color: #1890ff;
}
._css-addbtn-icon {
  margin-right: 8px;
}
._css-addbtn-text {
  font-size: 14px;
  height: 24px;
  line-height: 24px;
}
._css-content-btn {
  width: 28px;
  height: 28px;
  background-color: black;
  border-radius: 4px;
  position: absolute;
  right: 24px;
  line-height: 28px;
  text-align: center;
  color: #fff;
  cursor: pointer;
  display: none;
}

._css-content-in:hover ._css-content-btn {
  display: block;
}

._css-content-icon {
  width: 155px;
  height: 40px;
  border-radius: 4px;
  background-color: black;
  margin-left: 64px;
  line-height: 40px;
  color: #fff;
  text-align: center;
}
._css-content-label {
  font-size: 16px;
  color: rgba(0, 0, 0, 0.85);
  margin-left: 24px;
  line-height: 24px;
  height: 24px;
}
._css-body-in-title {
  height: 64px;
  box-shadow: 0px 1px 3px 0px rgba(0, 21, 41, 0.12);
  display: flex;
  align-items: center;
  position: relative;
}
._css-body-in-body {
  height: calc(100% - 64px);
  display: flex;
  align-items: center;
  justify-content: space-around;
}
._css-content-list {
  height: calc(100% - 64px);
  width: calc(100% - 0px);
  overflow-y: auto;
}
._css-content-i {
  height: 77px;
  display: flex;
  justify-content: space-around;
}
._css-content-in {
  height: 100%;
  width: calc(100% - 64px);
  border-bottom: 1px solid rgba(0, 0, 0, 0.09);
  display: flex;
  align-items: center;
  position: relative;
}
._css-content-in:hover {
  background-color: rgba(0, 0, 0, 0.04);
}
._css-body-in {
  width: 800px;
  height: calc(100% - 48px);
  box-sizing: border-box;
  border: 1px solid transparent;
  background-color: #fff;
}
._cs-toptab {
  height: 40px;
  width: 200px;
  box-sizing: border-box;
  line-height: 40px;
  border-bottom: 2px solid #1890ff;
  text-align: center;
}
._css-toptabcontrol {
  position: absolute;
  display: flex;
  justify-content: center;
  align-items: flex-end;
  height: 100%;
  width: 100%;
}
._css-topicon {
  margin-left: 24px;
  cursor: pointer;
  z-index: 1002;
  position: relative;
}
._css-toptitle {
  font-size: 16px;
  line-height: 24px;
  margin-left: 24px;
}
._css-body {
  height: calc(100% - 64px);
  display: flex;
  justify-content: space-around;
  align-items: center;
  background-color: rgb(247, 247, 247);
}
._css-top64 {
  height: 64px;
  box-shadow: 0 1px 3px 0 rgba(0, 21, 41, 0.12);
  display: flex;
  align-items: center;
  position: relative;
}
._css-issueconfig-all {
  top: 0;
  left: 0;
  position: fixed;
  width: 100%;
  height: 100%;
  z-index: 1000; /* 必须是1000以上，不然无法遮盖右上角的内容 */
  background-color: #fff;
}
</style>