<template>
    <div class="bottom-menu-container">
        <template v-for="(item, index) in menuList" >
            <div :class="{active: activeMenu.includes(index) && item.highlight}"
                 :key="item.label"
                 @click="toggleMenu(item)"
                 class="menu-item cursor-btn">
                <el-tooltip
                        class="box-item"
                        effect="dark"
                        :content="item.name"
                        placement="top">
                    <el-color-picker
                            v-if="item.colorPicker"
                            popper-class="bottom-menu-color-picker"
                            size="mini"
                            @change="setMarkupColor"
                            v-model="selectColor">
                    </el-color-picker>
                    <span v-else>
                        <CommonSVG color="#FFFFFF" :icon-class="item.icon" :size="16"></CommonSVG>
                    </span>
                </el-tooltip>
            </div>

            <div v-if="item.afterBar" class="vertical-split-line"></div>

            <template v-if="item.children">
                <transition name="sliderFade">
                    <div class="item-slider" v-show="item.show">
                        <span class="cursor-btn child-menu"
                              @click="toggleChildMenu(child,item)"
                              v-for="child in item.children" :key="child.label">
                            <CommonSVG
                                    :color="activeChildMenu[item.label]==child.label ? '#2680FE' : '#FFFFFF'"
                                    :size="child.size" :icon-class="child.icon">
                            </CommonSVG>
                        </span>
                    </div>
                </transition>
            </template>
        </template>
    </div>
</template>

<script>
    export default {
        //漫游的功能按钮
        name: "MarkupMenu",
        components:{
            CommonSVG: () =>import('@/components/Common/CommonSVG')
        },
        data () {
            return {
                menuList: [
                    {
                        id: 0,
                        name: '工具',//工具
                        label: 'tool', icon: 'notation_feature',show: true,highlight: true,
                        children: [
                            {
                                name: '矩形',//矩形
                                label: 'rectangle', icon: 'tree_hidden_feature',size: 20
                            },
                            {
                                name: '圆形',//圆形
                                label: 'circular', icon: 'round_feature',size: 20
                            },
                            {
                                name: '文字',//文字
                                label: 'text', icon: "text_feature",size: 20
                            },
                            {
                                name: '箭头',//箭头
                                label: 'arrow', icon: "arrow_feature",size: 20
                            },
                            {
                                name: '画线',//画线
                                label: 'line', icon: 'free_marking_feature',size: 20
                            },
                        ]
                    },
                    {
                        id: 1,
                        name: '画笔大小',//画笔大小
                        label: 'brush', icon: 'line_coarse_feature',show: false,highlight: true,
                        children: [
                            {
                                name: '小',//小
                                label: 'small', icon: 'point', size: 16
                            },
                            {
                                name: '中',//中
                                label: 'middle', icon: 'point', size: 24
                            },
                            {
                                name: '大',//大
                                label: 'big', icon: 'point', size: 32
                            },
                        ]
                    },
                    {id: 2, name: '颜色', label: 'color', icon: 'rectangular',afterBar: true,colorPicker: true},//颜色
                    {
                        id: 3,
                        name: '清除画布',//清除画布
                        label: 'clear', icon: "clear_feature"
                    },
                    {id: 5, name: '退出', label: 'quit', icon: 'quit',},//退出
                    {
                        id: 4, name: '保存',//保存
                        label: 'save', icon: "save_feature"
                    }
                ],

                selectColor: '#409eff',
                activeMenu: [0],
                brushSize: {small: 2,middle: 4, big: 8},//画笔大小
                activeChildMenu: {
                    tool: 'rectangle',//工具子菜单,默认让“矩形”选中
                    brush: 'small',//画笔粗细子菜单，默认选“小”
                }
            }
        },

        created() {
            try {window.scene.mv.tools.markup.destroyMarkup()}catch(e){}
        },
        mounted() {
            //默认开启矩形
            window.scene.mv.tools.markup.drawRectangle(4,this.selectColor,this.brushSize.small);

            // 追加一个清除画布的调用
            window.scene.mv.tools.markup.clear();
        },

        methods: {
            //设置画笔颜色
            setMarkupColor(color) {
                console.log(color)
                //获取当前选择的工具
                let tool = this.activeChildMenu.tool
                //获取当前画笔的大小
                let size = this.brushSize[this.activeChildMenu.brush]
                this.handleMarkupFn(tool,size)
            },

            //子菜单点击
            toggleChildMenu(child,item) {
                //使当前点击的子菜单选中
                this.activeChildMenu[item.label] = child.label
                //获取当前画笔的大小
                let size = this.brushSize[this.activeChildMenu.brush]

                this.handleMarkupFn(child.label,size)
            },

            //子菜单方法处理
            handleMarkupFn(label,size) {
                switch (label) {
                    case 'rectangle':
                        //矩形
                        window.scene.mv.tools.markup.drawRectangle(4,this.selectColor,size);
                        break;
                    case 'circular':
                        //圆形
                        window.scene.mv.tools.markup.drawCircle(this.selectColor,size);
                        break;
                    case 'text':
                        //文字
                        window.scene.mv.tools.markup.drawText(this.selectColor,size*6,'SimSun');
                        break;
                    case 'arrow':
                        //箭头
                        window.scene.mv.tools.markup.drawArrow(this.selectColor);
                        break;
                    case 'line':
                        //画线
                        window.scene.mv.tools.markup.drawPath(this.selectColor,size);
                        break;

                    case 'small':
                    case 'middle':
                    case 'big':{
                        //如果点击的是画笔大小
                        let tool = this.activeChildMenu.tool
                        let num = this.brushSize[label]
                        this.handleMarkupFn(tool,num)
                        break;
                    }
                }
            },

            toggleMenu(item) {
                let eq = item.id

                if (this.activeMenu.includes(eq)) {
                    //如果当前点击按钮已经处于激活状态，则将activeMenu设为-1，使按钮变成未选中
                    this.$set(this.activeMenu,eq,-1)
                } else {
                    //如果当前按钮未激活， 就激活它
                    this.$set(this.activeMenu,eq,eq)
                }

                //显隐子菜单
                if (item.hasOwnProperty('show')) {
                    this.$set(this.menuList[eq],'show',!this.menuList[eq].show)
                }

                switch (item.label) {
                    case 'clear':
                        window.scene.mv.tools.markup.clear();
                        break;
                    case 'save':
                        this.$emit('saveData');
                        break;
                    case 'quit':
                        //关闭菜单栏
                        this.$emit('closeMarkUpMenu');
                        break;
                }

            }
        },

        beforeDestroy() {
            // document.querySelector('#markup-canvas')
            window.scene.mv.tools.markup.clear();
            window.scene.mv.tools.markup.endEditMode();
            window.scene.mv.tools.markup.destroyMarkup();
            window.scene.render()
        }
    }
</script>

<style scoped lang="scss">
    .bottom-menu-container {
        position: fixed;
        bottom: 15px;
        left: 0;
        width: 100%;
        z-index: 10;
        display: flex;
        justify-content: center;
        align-items: center;
        .menu-item {
            width: 40px;
            height: 40px;
            background-color: #13141a;
            border-radius: 100%;
            display: flex;
            justify-content: center;
            align-items: center;
            margin: 0 8px;
            position: relative;
            z-index: 2;

            &.active {
                background-color: #2680FE;
                opacity: 1;
            }

        }

        .item-slider {
            background-color: #13141a;
            /*width: 120px;*/
            width: auto;
            height: 40px;
            border-radius: 0 20px 20px 0;
            padding: 0 15px 0 35px;
            position: relative;
            margin-left: -26px;
            z-index: 1;
            display: flex;
            align-items: center;

            .child-menu {
                margin-right: 12px;
            }
        }

        .vertical-split-line {
            width: 2px;
            height: 25px;
            margin: 0 15px;
            background-color: #000000;
        }
    }

    .sliderFade-enter-active,.sliderFade-leave-active {
        transition: width 0.08s;
        width: 120px !important;
    }

    .sliderFade-enter,.sliderFade-leave-to {
        width: 0 !important;
    }

    .sliderFade-enter-to,.sliderFade-leave  {
        width: 120px !important;
    }

</style>

<style>
    .bottom-menu-container .menu-item .el-color-picker .el-color-picker__trigger .el-color-picker__icon{
        display: none;
    }
    .bottom-menu-container .menu-item .el-color-picker .el-color-picker__trigger {
        border: none;
    }
    .bottom-menu-color-picker .el-button {
        padding: 7px 15px!important;
        border-radius: 3px!important;
    }
    .bottom-menu-color-picker .el-input__inner {
        border-radius: 4px;
        border-width: 1px;
        background: #fff;
    }
</style>
