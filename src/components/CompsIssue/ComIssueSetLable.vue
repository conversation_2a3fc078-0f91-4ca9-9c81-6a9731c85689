<template>
	<div class="_css-pano-lable">
		<div class="set-title">
			<p @click.stop="close"><i class="icon-arrow-left_outline"></i>标签管理</p>
			<p class="fc" @click="addMaterial">
				<i class="icon-interface-addnew"></i>
				添加
			</p>
		</div>
		<div class="set-list">
			<ul class="_css_bugs_and_bb_noWork css-miniscroll">
				<li v-for="(item,index) in tagOptionsArr" :key="index">
					<div class="li-name" :style="getListStyle(item)">
						{{ item.it_name }}
					</div>
					<div class="edit">
						<i
							class="icon-interface-edit_se"
							@click.stop="editMaterial(item)"
						></i>
						<i
							class="icon-suggested-close i_close"
							@click.stop="predel(item)"
						></i>
					</div>
				</li>
			</ul>
		</div>
		<div
			class="maker"
			@click.stop="dialogConfig.show = false"
			v-show="dialogConfig.show"
		>
			<div class="center" @click.stop>
				<header>
					<span>{{ dialogConfig.type == 0 ? "添加新标签" : "修改标签" }}</span>
					<i
						class="icon-suggested-close"
						@click.stop="dialogConfig.show = false"
					></i>
				</header>
				<main>
					<p>标签名称<el-input
						v-model="dialogConfig.input"
						placeholder="请输入标签名称"
					></el-input></p>
					
					<div class="color-list">
						<div
							v-for="item in colorList"
							:key="item"
							:style="{ background: item }"
							@click.stop="colorListClick(item)"
						>
							<i
								class="icon-suggested-check"
								v-show="dialogConfig.selectColor == item"
							></i>
						</div>
					</div>
					<div class="btn-wp">
						<div class="btns-right">
							<div @click.stop="cancelediting">取消</div>
							<div @click.stop="submit">确定</div>
						</div>
					</div>
				</main>
			</div>
		</div>
	</div>
</template>
<script>
export default {
	name: "ComIssueSetLable",
	components: {},
	data() {
		return {
			tagOptionsArr: [], // 标签
			dialogConfig: {
				show: false,
				input: "",
				id: "",
				selectColor: "",
				type: 0, //0新增
			},
			colorList: [ 
				"rgba(28, 50, 75, 1)",
				"rgba(86, 98, 112, 1)",
				"rgba(127, 179, 210, 1)",
				"rgba(151, 107, 61, 1)",
				"rgba(184, 158, 123, 1)",
				"rgba(29, 164, 140, 1)",
				"rgba(115, 113, 157, 1)",
			],
		};
	},
	props: {
		tagOptions: {
			type: Array,
			default: [],
		},
	},
	watch: {
		tagOptions(val) {
			this.tagOptionsArr = val;
		},
	},
	mounted() {
		this.tagOptionsArr = this.tagOptions;
	},
	methods: {
		close() {
			this.$emit("close");
		},
		// 点击添加标签
		addMaterial() {
			this.dialogConfig.selectColor = "rgba(28, 50, 75, 1)";
			this.dialogConfig.input = "";
			this.dialogConfig.type = 0;
			this.dialogConfig.show = true;
		},
		// 标签弹窗取消
		cancelediting() {
			this.dialogConfig.show = false;
		},
		// 标签弹窗确定
		submit() {
			if (this.dialogConfig.input.length > 0) {
				var _this = this;
				var _Token = _this.$staticmethod.Get("Token");
				var _it_organizeId = _this.$staticmethod._Get("organizeId");

				let _labelId = "";
				let _msg = "";
				this.dialogConfig.type == 0
					? (_labelId = "")
					: (_labelId = this.dialogConfig.id);
				this.dialogConfig.type == 0 ? (_msg = "添加成功") : (_msg = "修改成功");
				var _Url = `${this.$issueBaseUrl.AddOrModifyIssueOrganizeTag}`;

				_this
					.$axios({
						method: "post",
						url: _Url,
						data: {
							it_guid: _labelId,
							Token: _Token,
							it_organizeId: _it_organizeId,
							it_color: this.dialogConfig.selectColor,
							it_name: this.dialogConfig.input,
						},
					})
					.then((x) => {
						if (x.data.Ret > 0) {
							this.$message({
								message: _msg,
								type: "success",
							});
							_this.dialogConfig.show = false;
							_this.$parent.getAllTags();
						} else {
							_this.$message.error(x.data.Msg);
						}
					})
					.catch((x) => {
						console.error(x);
					});
			} else {
				this.$message.error("请输入标签名称");
			}
		},
		colorListClick(item) {
			this.dialogConfig.selectColor = item;
		},
		editMaterial(item) {
			this.dialogConfig.selectColor = item.it_color;
			this.dialogConfig.input = item.it_name;
			this.dialogConfig.type = 1;
			this.dialogConfig.id = item.it_guid;
			this.dialogConfig.show = true;
		},
		getListStyle(item) {
			let _s = {};
			let _ind = item.it_color.lastIndexOf(",");
			let str = item.it_color.slice(0, _ind) + ",0.1)";
			_s["background"] = str;
			_s["border"] = `1px solid  ${item.it_color}`;
			_s["color"] = item.it_color;
			return _s;
		},
		predel(item) {
			var _this = this;
			_this
				.$confirm("确认删除该状态？", "操作确认", {
					confirmButtonText: "确定",
					cancelButtonText: "取消",
					type: "warning",
				})
				.then((x) => {
					_this.del(item);
				})
				.catch((x) => {});
		},
		del(item) {
			var _Token = this.$staticmethod.Get("Token");
			var _it_guid = item.it_guid;
			this
				.$axios({
					method: "post",
					url: `${this.$issueBaseUrl.RemoveIssueTag}`,
					data:  {
						Token: _Token,
						it_guid: _it_guid,
					},
				})
				.then((x) => {
					if (x.data.Ret > 0) {
						this.$message({
							message: "删除成功",
							type: "success",
						});
						this.$parent.getAllTags();
						this.dialogConfig.show = false;
					} else {
						this.$message.error(x.data.Msg);
					}
				})
				.catch((x) => {
					console.error(x);
				});
		},
	},
};
</script>
<style lang="stylus" scoped rel="stylesheet/stylus">
._css-pano-lable {
	display: flex;
	flex-direction: column;

	.set-title {
		height: 48px;
		line-height: 48px;
		background: #F5F5F5;
		display: flex;
		align-content: center;
		padding: 0 16px 0 20px;
		justify-content: space-between;
		width: calc(100% - 36px);

		p {
			font-size: 14px;
			font-family: PingFangSC-Regular, PingFang SC;
			font-weight: 400;
			color: #222222;

			i {
				cursor: pointer;
				vertical-align: middle;
				padding-right: 4px;
			}
		}

		p.fc {
			color: #007AFF;

			i {
				padding-right: 8px;
			}
		}
	}

	.set-list {
		._css_bugs_and_bb_noWork li:hover .edit {
			display: flex;
		}

		ul {
			li {
				display: flex;
				margin-top: 20px;
				margin-right: 16px;
			}

			.li-name {
				height: 30px;
				line-height: 30px;
				width: 122px;
				border-radius: 2px;
				margin: 0 14px 0 16px;
				overflow: hidden;
				text-overflow: ellipsis;
				white-space: nowrap;
				padding: 0 10px;
			}

			.edit {
				display: none;

				i {
					color: #007AFF;
					font-size: 20px;
					margin-top: 5px;
					margin-bottom: 5px;
					cursor: pointer;
				}

				.i_close {
					margin-left: 10px;
				}
			}
		}
	}
}

.maker {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.5);
	z-index: 1001;

	.center {
		width: 410px;
		height: 274px;
		background: #ffffff;
		border-radius: 4px;
		box-shadow: 0px 13px 24px -17px rgba(11, 41, 62, 0.8);
		position: absolute;
		top: 50%;
		left: 50%;
		transform: translate(-50%, -50%);
		display: flex;
		flex-direction: column;

		header {
			height: 64px;
			line-height: 64px;
			display: flex;
			flex-direction: row;
			justify-content: space-between;
			align-items: center;
			padding: 0 24px;

			span {
				font-size: 20px;
				color: rgba(0, 0, 0, 0.85);
			}

			i {
				color: #bfbfbf;
				cursor: pointer;
			}
		}

		main {
			padding: 0 24px;
			flex: 1;

			.btn-wp {
				display: flex;
				flex-direction: row;
				justify-content: flex-end;

				&>div {
					color: red;
					line-height: 40px;
					cursor: pointer;

					&.btns-right {
						display: flex;
						flex-direction: row;

						div {
							color: #ffffff;
							width: 76px;
							height: 40px;
							line-height: 40px;
							background: #1890FF;
							border-radius: 2px;
							margin-left: 16px;
							opacity: 0.8;

							&:hover {
								opacity: 1;
							}

							&:first-child {
								color: rgba(0, 0, 0, 0.85);
								background: #fff;
							}
						}
					}
				}
			}

			p {
				text-align: left;
        margin-top: 10px;
        font-size: 14px;
        color: rgba(0,0,0,.65);
        display: flex;
        line-height: 50px;
        background: #f8f8f8;
        padding-left: 20px;
        border-radius: 4px;
        /deep/ .el-input{
          width: calc(100% - 80px);
        }
			}

			.color-list {
				height: 80px;
				display: flex;
				flex-direction: row;
				align-items: center;

				div {
					width: 40px;
					height: 40px;
					border-radius: 4px;
					cursor: pointer;
					margin-left: 12px;
					display: flex;
					align-items: center;
					justify-content: center;
					color: #fff;

					&:first-child {
						margin-left: 5px;
					}
				}
			}
		}
	}
}
</style>