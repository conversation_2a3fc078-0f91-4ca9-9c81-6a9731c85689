<template >
    <div class="_css-hover">
        <div class="_css-front">
            <CompsDialogHeader @oncancel="dialogbtns_oncancel" :title="data.title" :title_left="data.title_left"
            :title_right="data.title_right"
            ></CompsDialogHeader>
            <CompsFolderTree
            class="_css-treebodyarea css-miniscroll"
            :bimcomposerId="data.bimcomposerId"
            @onnodeclick="_onnodeclick"
            ></CompsFolderTree>
            <CompsDialogBtns @onok="dialogbtns_onok" @oncancel="dialogbtns_oncancel"  ></CompsDialogBtns>
        </div>
    </div>
</template>
<script>


import CompsDialogBtns from '@/components/CompsDialog/CompsDialogBtns'
import CompsDialogHeader from '@/components/CompsDialog/CompsDialogHeader'
import CompsFolderTree from '@/components/CompsDocument/CompsFolderTree'
export default {
  components:{
      CompsDialogBtns,
      CompsDialogHeader,
      CompsFolderTree
  },
  props: {
    data: {
      type: Object,
      required: true
    },
    AlreadyinFolderId:{
      type:String,
      required: false
    }
  },
  methods: {

      _onnodeclick(nodekey){
        let _this = this;
        _this.data._selectednodekey = nodekey;
      },
      dialogbtns_onok(){
        let _this = this;
        _this.$emit("onok", _this.data._selectednodekey, _this.AlreadyinFolderId);
      },
      dialogbtns_oncancel(){
        let _this = this;
        _this.$emit("oncancel");
      }
  }
};
</script>
<style scoped>
    ._css-hover{
        position:fixed;
        top:0;
        left:0;
        background-color: rgba(0, 0, 0, 0.45);
        z-index:3;
        width:100%;
        height:100%;
        display: flex;
        align-items: center;
        justify-content: space-around;
    }
    ._css-front{
        width:500px;
        height:auto;
        border-radius: 4px;
        background-color: #fff;
    }
    ._css-treebodyarea{
        overflow-y: auto;
        height: 294px;
    }
</style>