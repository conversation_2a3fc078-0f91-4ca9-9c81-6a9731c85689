<template>
  <div>
          <!-- :defaultExpandedKeys="openstacks" -->
    <el-tree
      :data="treedata"
      lazy
      :load="loadNodeChild"
      node-key="Id"
      :props="props1"
      :expand-on-click-node="false"
      @node-collapse="node_collapse"
      @node-expand="node_expand"
      @node-click="_send_nodeclick"
      ref="doctree"
      :default-expanded-keys="defexpandkeys"
    >
      <span class="css-fc" slot-scope="{ node, data }">
        <i class="css-icon20 css-fs18 css-fc css-jcsa css-folder" :class="data.classname"></i>
        <span :title="data.FolderName" class="css-ml4">{{ node.label }}</span>
      </span>
    </el-tree>
  </div>
</template>
<script>
/*
datainput:
    data: []
events:
    onnodeclick(nodekey)
    onrootloaded()
*/
export default {
  data() {
    return {
      treedata: [],
      defexpandkeys: [],
      props1: {
        children: "Children",
        label: "FolderName"
      },
      v_Id:undefined
    };
  },
  mounted() {
    var _this = this;
    //_this.getRootFolders();
    _this.getRoot();
  },
  methods: {

    // _send_nodeclick
    _send_nodeclick(){
        var _this = this;
        var _currentKey = _this.$refs.doctree.getCurrentKey();
        if(_currentKey==0){
          _currentKey = _this.v_Id
        }
        _this.$emit("onnodeclick", _currentKey);
    },

    // 展开节点 回调
    node_expand(itemi, node, comp) {
      itemi.classname = "icon-interface-folder";
    },

    node_collapse(itemi, node, comp) {
      itemi.classname = "icon-interface-unfolder";
    },

    // 某节点展开时，加载子节点的途径
    loadNodeChild(node, resolve) {
      if(node.data.Id == undefined) return
      // 设置必要的参数
      var _this = this;
      let v_Id = node.data.Id

      // 请求接口，获取当前正在展开的节点的子节点数据
      // _this
      //   .$axios({
      //     method: "get",
      //     url: `${
      //       _this.$staticmethod.getDocServer()
      //     }/api/Doc/GetAllFolderAndFileByFolderID?ProjectID=${_bimcomposerId}&LikeName=&NormalOrDrawings=Normal&FolderID=${_FolderID}`
      //   })
      //   .then
      _this.$axios
        .get(`${window.bim_config.webserverurl}/api/v1/folder/tree?projectId=${_this.$staticmethod._Get("organizeId")}&Token=${_this.$staticmethod.Get('Token')}&parentId=${v_Id}&userId=${_this.$staticmethod.Get("UserId")}`)
        .then(x => {
          if (x.data.Ret == 1) {
            var _arrgot 
            // 默认情况是加载当前正在展开的节点内的全部文件及文件夹，此处过滤出文件夹
            if(v_Id == 0){
              this.v_Id = x.data.Data[0].Id
              _this.$axios
              .get(`${window.bim_config.webserverurl}/api/v1/folder/tree?projectId=${_this.$staticmethod._Get("organizeId")}&Token=${_this.$staticmethod.Get('Token')}&parentId=${x.data.Data[0].Id}&userId=${_this.$staticmethod.Get("UserId")}`)
              .then(y => {
                if(y.data.Ret == 1){
                  _arrgot = y.data.Data;
                  // var _arrgot = x.data.Data;
  
                  for (var i = 0; i < _arrgot.length; i++) {
                    // 对于每一个即将拼接的新子文件夹，设置及 chilren 使其带有展开按钮图标
                    _arrgot[i].Children = [];
  
                    // 使该文件夹项带有文件夹图标
                    _arrgot[i].classname = "icon-interface-folder";
  
                    // 设置其 chain 属性。
                    // 记录了从根级节点到当前节点的路径（有序节点数组）
                    _arrgot[i].chain = [];
  
                    // 添加父节点的 chain
                    if (node.data.chain && node.data.chain.length) {
                      for (var j = 0; j < node.data.chain.length; j++) {
                        _arrgot[i].chain.push(node.data.chain[j]);
                      }
                    }
  
                    // 添加自己节点的 chain item
                    _arrgot[i].chain.push({
                      Id: _arrgot[i].Id,
                      FolderName: _arrgot[i].FolderName
                    });
                  }
  
                  // 将获取到并处理过的数据加载到正在展开的节点上
                  resolve(_arrgot);
                }
              })
            }else{
              _arrgot = x.data.Data;
            // var _arrgot = x.data.Data;

            for (var i = 0; i < _arrgot.length; i++) {
              // 对于每一个即将拼接的新子文件夹，设置及 chilren 使其带有展开按钮图标
              _arrgot[i].Children = [];

              // 使该文件夹项带有文件夹图标
              _arrgot[i].classname = "icon-interface-folder";

              // 设置其 chain 属性。
              // 记录了从根级节点到当前节点的路径（有序节点数组）
              _arrgot[i].chain = [];

              // 添加父节点的 chain
              if (node.data.chain && node.data.chain.length) {
                for (var j = 0; j < node.data.chain.length; j++) {
                  _arrgot[i].chain.push(node.data.chain[j]);
                }
              }

              // 添加自己节点的 chain item
              _arrgot[i].chain.push({
                Id: _arrgot[i].Id,
                FolderName: _arrgot[i].FolderName
              });
            }

            // 将获取到并处理过的数据加载到正在展开的节点上
            resolve(_arrgot);
            }

          } else {
            console.warn(x);
          }
        })
        .catch(x => {
          console.warn(x);
        });
    },
    getRoot(){
      var _this = this;
      _this.treedata = [
        {
          Id: 0,
          FolderName: '项目文档'
        }
      ];
      _this.defexpandkeys = [0];
    },
    getRootFolders() {
      var _this = this;
      var _bimcomposerId = _this.bimcomposerId;
      var _LoadingIns = _this.$loading({
        text: "加载中"
      });
      _this
        .$axios({
          method: "get",
          url: `${
            _this.$staticmethod.getDocServer()
          }/api/Doc/GetAllFolderAndFileByProjectID?ProjectID=${_bimcomposerId}&Token=${_this.$staticmethod.Get('Token')}&LikeName=&NormalOrDrawings=Normal`
        })
        .then(x => {
          var _arrgot = x.data.filter(x => x.FileSize == 0);
          for (var i = 0; i < _arrgot.length; i++) {
            // 确保当前新加载的文件夹节点左侧有展开图标
            _arrgot[i].Children = [];
            _arrgot[i].Children.push({
              Id: "-1",
              FolderName: "",
              Children: [],
              classname: "icon-interface-unfolder"
            });
            _arrgot[i].classname = "icon-interface-unfolder";

            // 由于当前是第一级节点，所以无需 pusharray 父节点的 chain
            _arrgot[i].chain = [];
            _arrgot[i].chain.push({
              Id: _arrgot[i].Id,
              FolderName: _arrgot[i].FolderName
            });
          }
          _this.treedata = _arrgot;
          _LoadingIns.close();
        })
        .catch(x => {
          console.warn(x);
          _LoadingIns.close();
        });
    }
  },
  props: {
    bimcomposerId: {
      type: String,
      required: true
    }
  }
};
</script>
<style scoped>
</style>