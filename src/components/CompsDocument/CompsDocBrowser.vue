<template>
    <div class="_css-docbrowser" :style="{'z-index': m_zIndex || 1001}"
    >
        <div class="_css-docbrowser-in" :style="getFrontStyle()"
        v-drag="greet"  
        >

            <!-- 标题区域 -->
            <div
            class="_css-docin-title css-fc"
            >
                <!-- 对话框标题及关闭按钮 -->
                <div class="_css-title-content css-f1">
                    {{init_title}}
                </div>
                <div 
                @click="$emit('onclose')"
                class="_css-title-closebtn css-closebtn icon-suggested-close"></div>
                <!-- //对话框标题及关闭按钮 -->

            </div>
            <!-- //标题区域 -->
            
            <!-- 内容区域 -->
            <div
            @mousedown="_stopPropagation($event)"
            class="_css-docin-content"
            >
                <!-- 左树235右列表 -->
                <div class="_css-docin-tree" >
                    <el-tree :data="m_rootfolders" :props="treem_props" lazy
                    ref="ref_wftc"
                    class="_css-customstyle"
                    :expand-on-click-node="false"
                    @node-collapse="node_collapse"
                    @node-expand="node_expand"
                    @node-click="node_click"
                    :load="treefunc_loadChild"

                    node-key="wftc_guid"
                    :highlight-current="true"
                    :auto-expand-parent="true"
                    v-if="RoleId"
                    >
                    <span class="css-fc _css-treenode-content" slot-scope="{ node, data }">
                        <i class="css-icon20 css-fs18 css-fc css-jcsa css-folder" :class="data.classname" ></i>
                        <span :title="node.label" class="css-ml4 _css-treenodelabel">{{node.label}}</span>
                    </span>
                    </el-tree>
                </div>
                <div class="_css-docin-list"
                v-loading="m_tableloading"
                element-loading-text="加载中"
                >

                    <!-- 面包屑显示区域 -->
                    <div v-if="m_openstacks.length" class="_css-openstack-ctn">

                        <div 
                        @click="func_openparent($event)"
                        class="_css-openstack-item _css-first">返回上一级</div>
                        <div class="css-breadcrumb-splitter _css-openstack-splitter">/</div>

                        <div 
                        @click="func_openroot($event)"
                        class="_css-openstack-item _css-first">项目文档</div>
                        <div class="css-breadcrumb-splitter _css-openstack-splitter">/</div>

                        <template v-for="(ositem, index) in m_openstacks"
                        >
                            <div 
                            @click="func_openstackitem($event, index)"
                            :key="ositem.FileId"
                            class="_css-openstack-item">{{ositem.FileName}}</div>
                            <div 
                            :key="ositem.FileId + '__2'"
                            class="css-breadcrumb-splitter _css-openstack-splitter">/</div>
                        </template>
                        
                    </div>
                    <!-- //面包屑显示区域 -->

                    <el-table
                    ref="doctable"
                    :highlight-current-row="false"
                    @row-contextmenu="row_contextmenu"
                    @row-click="row_click"
                    @row-dblclick="on_row_dblclick"
                    :border="true"
                    :stripe="false"
                    :data="m_currentfolderfiles"
                    style="width: 565px"
                    :default-sort="{prop: 'date', order: 'descending'}"
                    height="500"
                    class="_css-table-ele _css-table-ele2 css-scroll _css-customstyle"
                    :row-class-name="tableRowClassName"
                    :header-cell-style="{'background-color':'transparent'}"
                    >

                            <!-- 复选框列 -->
                            <el-table-column
                            :resizable="false"
                            width="40">
                                <template slot="header" slot-scope="scope">
                                <span
                                    class="css-cb css-icon12 css-cp css-blk"
                                    :class="{'mulcolor-interface-checkbox-selected':func_testcontainsAll()}"
                                    @click="func_switchall($event)"
                                ></span>
                                </template>
                                <template slot-scope="scope">
                                <span
                                    class="css-cb css-icon12 css-cp"
                                    :class="{'mulcolor-interface-checkbox-selected':func_testcontainsThis(scope.row)}"
                                    @click="func_switchitem($event, scope.row)"
                                ></span>
                                </template>
                            </el-table-column>
                            <!-- 复选框列 -->

                           <el-table-column
                            :resizable="true"
                            class="_css-col-filename"
                            prop="FileName"
                            label="文件名称"
                            min-width="168" 
                            >
                                <template slot-scope="scope" class="abc123">
                                    <i
                                        :class="'css-icon20 css-fs18 css-fc css-jcsa ' + $staticmethod.getIconClassByExtname(scope.row.FileName, scope.row.FileSize)"
                                    ></i>

                                     <span
                                        class="css-cp css-hoverunder css-ml10 css-ellipsis basic-font-color-emphasize"
                                        @click.stop="item_enter(scope.row, $event)"
                                     >
                                        {{scope.row.FileName}}
                                     </span>
                                </template>
                            </el-table-column>

                            <el-table-column
                                :formatter="FileSize_formatter"
                                :resizable="true"
                                class="_css-col-filesize"
                                prop="FileSize"
                                label="文件大小"
                                min-width="48"

                            ></el-table-column>

                    </el-table>
                </div>
                <!-- //左树235右列表 -->

            </div>
            <!-- //内容区域 -->
            
            <!-- 对话框按钮区域 -->
            <div
            class=" css-common-zdialogbtnctn _css-docin-btnctn"
            >
                <!-- 取消及确定按钮 -->
                <zbutton-function
                    :init_text="'取消'"
                    :init_fontsize="14"
                    :debugmode="true"
                    :init_height="undefined"
                    :init_width="'76px'"
                    :init_bgcolor="'#fff'"
                    :init_color="'#1890FF'"
                    @onclick="$emit('onclose')"
                    >
                </zbutton-function>
                    <zbutton-function
                    :init_text="'确定'"
                    :init_fontsize="14"
                    :debugmode="true"
                    :init_height="undefined"
                    :init_width="'76px'"
                    @onclick="_emit_onok()"
                    >
                </zbutton-function>
                <!-- //取消及确定按钮 -->

            </div>
            <!-- //对话框按钮区域 -->

        </div>
    </div>
</template>
<script>
export default {
    data() {
        return {
            RoleId:'',//获取权限
            m_tableloading: false,

            // 拖动相关
            // --------
            val:'123',

            // 接收传入参数
            // -----------
            m_zIndex: 0,
            m_height: 0,
            m_width: 0,

            // 根级文件夹
            // ---------
            m_rootfolders: [],

            // 当前正在显示的文件夹及文件
            // 当前选中了哪些文件
            // 记录的打开栈
            // -----------
            m_currentfolderfiles: [],
            m_selectedfiles:[],
            m_openstacks:[],

            // 树控件的 props 属性对象
            // ----------------------
            treem_props:{
                children: "children",
                label: "FileName",
                isLeaf: "isLeaf"
            },
            //记录上一次点击的时间
            lastTime:0,
            time:null
        };
    },
    created(){
        var _this = this;
    },
    mounted(){
        var _this = this;
        window.docbrowervue = this;
        _this.m_zIndex = _this.init_zIndex;
        _this.m_width = _this.init_width;
        _this.m_height = _this.init_height;
        _this.m_bimcomposerId = _this.init_bimcomposerId;

        var _LoadingIns = _this.$loading({
          text: "加载中"
        });
      
        var UserId=_this.$staticmethod.Get('UserId');
        var OrganizeId=_this.$staticmethod._Get('organizeId');
        _this.$axios.get(`${window.bim_config.webserverurl}/api/User/User/GetUserALLRoles?organizeId=${OrganizeId}&Token=${this.$staticmethod.Get('Token')}`).then(res=>{
            let UserId_filter = res.data.Data.filter(item => item.UserId == UserId)
            let roleId
            if(UserId_filter.find(item => item.br_FullName == '项目管理员')){
              roleId = UserId_filter.filter(item => item.br_FullName == '项目管理员')[0].roleId
            }
            else{
              roleId = UserId_filter.map(x => {return x.roleId}).join(",")
            }
            // 加载根级目录
            // -----------
            _this.func_getRootDirectories(roleId);

            // 加载根级文件夹及文件
            // ------------------
            _this.func_readroot(roleId);
            _this.RoleId = roleId
            _LoadingIns.close();
        }).catch(x => {
              _LoadingIns.close();  
        });
    },
    methods: {

        // 根据 folderid 及 动作（如open）获取指定 token 的所有角色及是否有权限
        // ----------------------------------------------------------------
        func_TestRoleAuthByFolderIdAndAction(FolderId, authName, successcallback) {
            var _this = this;
            var _Token = _this.$staticmethod.Get("Token");
            var _OrgId = _this.$staticmethod._Get("organizeId");
            var _BIMComposerId = _this.$staticmethod._Get("bimcomposerId");
            var _FolderId = FolderId;
            var _authName = authName;
            var _url = `${window.bim_config.webserverurl}/api/Document/Doc/GetDocRolesAuthByName?Token=${_Token}&OrgId=${_OrgId}&BIMComposerId=${_BIMComposerId}&FolderId=${_FolderId}&authName=${_authName}`;
            _this.$axios.get(_url).then(x => {
                if (x.data.Ret > 0) {
                    successcallback(x.data.Data);
                } else {
                    _this.$message.error(x.data.Msg);
                }
            }).catch(x => {
                console.error(x);
            });

        },

        //判断获取权限的FolderId和FolderName
        func_TestRoleAuthFolderIdAndFolderName(folderId,folderName){
            var _this = this;
            _this.func_TestRoleAuthByFolderIdAndAction(folderId, 'open', (Data)=>{
                
                var hasauth = false;
                if (Data.Data && Data.Data.length) {
                    for (var i = 0; i < Data.Data.length; i++) {
                        var _thisRoleAuth = Data.Data[i];
                        if (_thisRoleAuth.RoleDocAuths && _thisRoleAuth.RoleDocAuths.length) {
                            for (var j = 0; j < _thisRoleAuth.RoleDocAuths.length; j++) {
                                if (_thisRoleAuth.RoleDocAuths[j].RoleDocAuthName == 'open' && _thisRoleAuth.RoleDocAuths[j].RoleDocAuthValue == true) {
                                    hasauth = true;
                                    break;
                                }
                            }
                        }
                        if (hasauth) {
                            break;
                        }
                    }
                }

                // 如果没有权限，提示，否则执行打开文件夹操作
                // --------------------------------------
                if (hasauth) {
                    _this.enterdir(folderId, folderName);
                } else {
                    _this.$message.error('该文件夹无打开权限');
                }
            });
        },

        // 切换全部的选中状态
        // _this.m_selectedfiles
        // m_currentfolderfiles
        // ----------------
        func_switchall(ev) {

            // 如果 m_currentfolderfiles 长度为0
            // 不操作
            // ------
            var _this = this;
            _this._stopPropagation(ev);
            if (_this.m_currentfolderfiles.length <= 0) {
                return;
            }

            // 如果 m_selectedfiles 长度小于 m_currentfolderfiles
            // 则直接赋予（深拷贝）
            // -----------------
  
            let m_currentfolderfiles = _this.m_currentfolderfiles.filter(x => x.FileSize != 0); //筛选出非文件夹的文档

            if (_this.m_selectedfiles.length < m_currentfolderfiles.length) {
                _this.m_selectedfiles = _this.$staticmethod.DeepCopy(m_currentfolderfiles);
            } else {
                _this.m_selectedfiles = [];
            }

            // 否则清空
            // --------
        },

        // 切换单条的选中状态
        // _this.m_selectedfiles
        // m_currentfolderfiles
        // -----------------
        func_switchitem(ev, item) {
            clearTimeout(this.time)
            this.time = setTimeout(()=>{
                let now = new Date().valueOf()
                if(item.FileSize == 0){
                    if(this.lastTime == 0){
                        this.$message.warning('不能关联文件夹，请选择单独的文件')
                        this.lastTime = now
                    }else{
                        if((now-this.lastTime) > 2000){
                          this.lastTime = now
                          this.$message.warning('不能关联文件夹，请选择单独的文件')
                        }
                    }
                }
            },400)
            // 如果 m_currentfolderfiles 长度为0
            // 不操作
            // ------
            var _this = this;
            _this._stopPropagation(ev);
            if (_this.m_currentfolderfiles.length <= 0) {
                return;
            }
            // 如果包含它就移除掉它，否则加进来
            // -----------------------------
            if (_this.func_testcontainsThis(item)) {
                _this.m_selectedfiles = _this.m_selectedfiles.filter(x => x.FileId != item.FileId);
            } else {
                if(item.FileSize!=0 ){
                  _this.m_selectedfiles.push(item);
                }
            }

        },

        // 对于非全选框，判断当前是否包含了此条数据
        // -------------------------------------
        func_testcontainsThis(item) {
            if (item.FileSize == 0) {
                return false;
            }else{
                var _this = this;
                if (_this.m_currentfolderfiles.length <= 0) {
                    return false;
                }
                var hasIndex = _this.m_selectedfiles.findIndex(x => x.FileId == item.FileId);
                // console.log(hasIndex >= 0)
                return hasIndex >= 0;
            }
        },
        func_testcontainsAll() {
            var _this = this;
            if (_this.m_currentfolderfiles.length <= 0) {
                return false;
            }
            
            let m_currentfolderfiles = _this.m_currentfolderfiles.filter(x => x.FileSize != 0); //筛选出非文件夹的文档
            
            var equalall = m_currentfolderfiles.length == _this.m_selectedfiles.length;
            return equalall;
        },

        _emit_onok() {
            var _this = this;
            _this.$emit("onok", _this.m_selectedfiles);
        },

        // 单击节点，加载里面的文件夹及文件
        // -----------------------------
        node_click(itemi, node, comp) {
            var _this = this;
            var folderId = node.data.FileId
            var folderName = node.data.FileName

            // 设置 openStacks
            // ---------------
            _this.m_openstacks = _this.$staticmethod.DeepCopy(node.data.chain);

            // 读取文件夹及文件
            // ---------------
            _this.func_TestRoleAuthFolderIdAndFolderName(folderId,folderName)
        },

        // 文件大小格式化
        // -------------
        FileSize_formatter(row, column) {
            var _this = this;

            // 0B
            if (row.iszerobytes) {
            return "0B";
            }

            let size = row.FileSize;
            if (size) {
            if (size == 0) {
                // 文件夹不显示大小
                return "-";
            } else {
                // 显示文件的大小，需要转换为字符串
                return _this.$staticmethod.convertToSizeStr(size);
            }
            } else {
            // size is undefined.
            return "";
            }
        },

        // 打开指定的 openstack 中的一项
        // ---------------------------
        func_openstackitem(ev, index) {

            // slice(0, index + 1)
            // -------------------
            var _this = this;
            _this.m_openstacks = _this.m_openstacks.slice(0, index + 1);

            // 读取文件夹
            // ---------
            if (_this.m_openstacks.length > 0) {
                var folderId = _this.m_openstacks[_this.m_openstacks.length - 1].FileId;
                var folderName = _this.m_openstacks[_this.m_openstacks.length - 1].FileName
                _this.func_TestRoleAuthFolderIdAndFolderName(folderId,folderName)
            } else {
                _this.func_openroot();
            }

        },

        // 返回上一级
        // ----------
        func_openparent(ev) {

            // 设置打开栈
            // ---------
            var _this = this;
            _this.m_openstacks = _this.m_openstacks.slice(0, _this.m_openstacks.length - 1);

            // 打开指定文件夹
            // -------------
            if (_this.m_openstacks.length > 0) {
                var folderId = _this.m_openstacks[_this.m_openstacks.length - 1].FileId;
                var folderName = _this.m_openstacks[_this.m_openstacks.length - 1].FileName;
                _this.func_TestRoleAuthFolderIdAndFolderName(folderId,folderName)
            } else {
                _this.func_openroot();
            }

        },

        // 点击“项目文档”打开根级目录
        // ------------------------
        func_openroot(ev) {

            // 清空“打开栈”
            // 获取数据
            // -------
            var _this = this;
            _this.m_openstacks = [];
            _this.func_readroot(_this.RoleId);
        },

        // 获取并展示根级的文件夹及文件
        // --------------------------
        func_readroot(roleId) {
            var _this = this;
            _this.m_tableloading = true;
            var _url = `${
                _this.$staticmethod.getDocServer()
                }/api/Doc/GetAllFolderAndFileByFolderID?ProjectID=${_this.m_bimcomposerId}&Token=${this.$staticmethod.Get('Token')}&LikeName=&NormalOrDrawings=Normal&FolderID=${_this.m_bimcomposerId}&RoleId=${roleId}`;
            _this.$axios.get(_url).then(x => {

                // 赋予表格的数据对象
                // -----------------
                _this.m_currentfolderfiles = x.data;
                
                _this.m_tableloading = false;
            }).catch(x => {
                _this.m_tableloading = false;
                // console.log('catch line 156');
            })
        },

        // 获取并展示某个文件夹下的文件夹或文件
        // ---------------------------------
        func_readfolder(folderId) {
            var _this = this;
            _this.m_tableloading = true;

            setTimeout(()=>{
                  var _url = `${
                      _this.$staticmethod.getDocServer()
                      }/api/Doc/GetAllFolderAndFileByFolderID?ProjectID=${_this.m_bimcomposerId}&Token=${this.$staticmethod.Get('Token')}&LikeName=&NormalOrDrawings=Normal&FolderID=${folderId}&RoleId=${_this.RoleId}`;
                _this.$axios.get(_url).then(x => {

                    // 赋予表格的数据对象
                    // -----------------
                    _this.m_currentfolderfiles = x.data;

                    _this.m_tableloading = false;
                    

                }).catch(x => {
                    _this.m_tableloading = false;
                    // console.log('catch line 156');
                })
            }, 10);
          
        },

        // 打开文件夹前的权限判断
        // --------------------
        pre_enterdir(folderId, folderName) {
            var _this = this;
            _this.func_TestRoleAuthFolderIdAndFolderName(folderId,folderName)
        },

        // 打开文件夹
        // ---------
        enterdir(folderId, folderName) {
            var _this = this;

            // 设置 openStacks
            // ---------------
            var fl = Array.from(_this.m_openstacks).filter(item => item.FileId == folderId)
            if(fl.length == 0){
              _this.m_openstacks.push({
                  FileId: folderId,
                  FileName: folderName
              });
            }

            // 读取文件夹及文件数据
            _this.func_readfolder(folderId);
        },

        // 打开某一行数据（打开某个文件夹 or 选中某个文件并直接点击“确定”）
        // 表格、列表视图的文件（夹）进入方法（包括回收站不可进的逻辑）。
        // ----------------------------------------------------------
        item_enter(row){

            // 进行文件进入动作
            // ---------------
            var _this = this;
            if (row.FileSize == '0') {

                // 进入文件夹
                // ---------
                //_this.enterdir(row.FileId, row.FileName, null);
                _this.pre_enterdir(row.FileId, row.FileName, null);

            } else {

                // 打开或预览文件
                // -------------
                _this.begin_previewfile(row, null);

            }

        },

        // 打开的是文件
        // -----------
        begin_previewfile(row) {
            // console.log('打开了文件');
        },

        // 被触发的双击事件
        // ---------------
        on_row_dblclick(row, column, ev) {
            clearTimeout(this.time)
            var _this = this;
            ev.stopPropagation();
            _this.item_enter(row);
        },

        // 单击的不是复选框，而是行时，切换当前行的选中状态，同时确保其它行没有被选中。
        // --------------------------------------------------------------------
        row_click(row, column, ev) {
            var _this = this;
            // _this.m_selectedfiles = [row];
            // ev.stopPropagation();

            _this.func_switchitem(ev, row);
        },

        // 表格行右击
        // ---------
        row_contextmenu(row, column, ev) {
            // console.log('表格行右击');
            var _this = this;
        },

        // 根据是否选中了某一行，返回类字符串
        // -------------------------------
        tableRowClassName({ row, rowIndex }) {
        var _this = this;
        var _sel_hasThisRow =
            _this.m_selectedfiles.filter(x => x.FileId == row.FileId).length >
            0;
        return "css-tdunder " + (_sel_hasThisRow ? "css-tabrow-selected" : "");
        },

        // 加载某个文件夹下的子文件夹
        // ------------------------
        treefunc_loadChild(node, resolve) {

            // 获取所展开节点下的子文件夹
            // ------------------------
            var _this = this;
            var _nodedata = node.data;
            var _FolderId = _nodedata.FileId;
            var _bimcomposerId = _this.m_bimcomposerId;
            if (!_bimcomposerId) {
                return;
            }
            var _url = `${
                _this.$staticmethod.getDocServer()
                }/api/Doc/GetAllFolderAndFileByFolderID?ProjectID=${_bimcomposerId}&Token=${this.$staticmethod.Get('Token')}&LikeName=&NormalOrDrawings=Normal&FolderID=${_FolderId}&RoleId=${_this.RoleId}`;
            _this.$axios.get(_url).then(x => {
                // 拿到当前文件夹的子级，resolve 到下面
                // ----------------------------------
                var onlyFolder = x.data.filter(x => x.FileSize == "0");

                // 挂上 classname
                // --------------
                for (var i = 0; i < onlyFolder.length; i++) {
                    onlyFolder[i].classname = 'icon-interface-unfolder';

                    // 并赋予打开栈
                    // -----------
                    onlyFolder[i].chain = [];
                    for (var j = 0; j < _nodedata.chain.length; j++) {
                        onlyFolder[i].chain.push(_nodedata.chain[j]);
                    }
                    onlyFolder[i].chain.push({
                        FileId: onlyFolder[i].FileId,
                        FileName: onlyFolder[i].FileName
                    });
                }

                _this.func_analysisfolders(onlyFolder, (toval)=>{
                    resolve(toval);
                });
                

            }).catch(x => {
                // console.log('catch line 156');
            })
        },

        // 收起节点 回调
        // 展开节点 回调
        // ------------
        node_collapse(itemi, node, comp) {

            // 修改 classname
            // ---------------
            var _this = this;
            itemi.classname = "icon-interface-unfolder";
        },
        node_expand(itemi, node, comp) {
            
            // 修改 classname
            // ------------------------------------
            var _this = this;
            itemi.classname = "icon-interface-folder";
        },

        // 分析 表示多个文件夹的数组 maybeRootNotFile 是否有子文件夹
        // ------------------------------------------------------
        func_analysisfolders(maybeRootNotFile, callback) {

            // 先取出 GetChildrenFoldersCounts 接口要用到的第二个参数值 folderIds
            // ----------------------------------------------------------------
            var _this = this;
            var folderIds = maybeRootNotFile.map(x => x.FileId).join(',');

            // 接口地址
            // -------
            var _url = `${
                _this.$staticmethod.getDocServer()
                }/api/Doc/GetChildrenFoldersCounts?ProjectID=${_this.m_bimcomposerId}&Token=${this.$staticmethod.Get('Token')}&ParentIds=${folderIds}`;
            _this.$axios.get(_url).then(x => {

                // 遍历【当前需要解析是否有子文件夹的】所有文件夹
                // ------------------------------------------
                for (var i = 0; i < maybeRootNotFile.length; i++) {

                    // 从 x.data.Data 中找到 ParentId 与 maybeRootNotFile[i].FileId 相同的
                    // ------------------------------------------------------------------
                    var getTheFolderChildCntIndex = x.data.Data.findIndex(y => y.ParentId == maybeRootNotFile[i].FileId);
                    if (getTheFolderChildCntIndex >= 0) {
                        var getTheFolderChildCnt = x.data.Data[getTheFolderChildCntIndex];
                        
                        // maybeRootNotFile[i] 这个文件夹是有子文件夹的
                        // ------------------------------------------
                        if (getTheFolderChildCnt.Cnt) {

                            // 有子文件夹
                            // ---------
                            maybeRootNotFile[i].isLeaf = false;
                            maybeRootNotFile[i].children = [{
                                FileId: "-1",
                                FileName: "",
                                children: [],
                                classname: "icon-interface-unfolder"
                            }];

                        } else {

                            // 这个文件夹是没有子文件夹的
                            // ------------------------
                            maybeRootNotFile[i].isLeaf = true;
                            maybeRootNotFile[i].children = [];

                        }
                    }
                }

                // 赋值以渲染根级目录
                // -----------------
                if (callback) {
                    callback(maybeRootNotFile);
                }

            }).catch(x => {

                // 赋值以渲染根级目录
                // 忽略文件夹是否有子文件夹
                // ----------------------
                if (callback) {
                    callback(maybeRootNotFile);
                }
            });


        },

        // 提供方法，获取第一级文件夹
        // ------------------------
        func_getRootDirectories(roleId) {
            var _this = this;
            var _url = `${
                _this.$staticmethod.getDocServer()
                }/api/Doc/GetAllFolderAndFileByProjectID?ProjectID=${_this.m_bimcomposerId}&Token=${this.$staticmethod.Get('Token')}&LikeName=&NormalOrDrawings=Normal&RoleId=${roleId}`;
            _this.$axios.get(_url).then(x => {

                // 绑定根级文件夹到 tree
                // 先拿到所有非文件的数据
                // 绑定 icon-interface-unfolder 图标类
                // ----------------------------------
                var maybeRootNotFile = x.data.filter(y => y.FileSize == 0);

                // 遍历添加 classname
                // -----------------
                var i = 0;
                for (i = 0; i < maybeRootNotFile.length; i++) {

                    // 赋予 classname 以展示树节点图标
                    // -----------------------------------------------------------------
                    maybeRootNotFile[i].classname = 'icon-interface-unfolder';

                    // 赋予 chain 属性（以提供给“打开栈”）
                    // --------------------------------
                    maybeRootNotFile[i].chain = [];
                    maybeRootNotFile[i].chain.push({
                        FileId: maybeRootNotFile[i].FileId,
                        FileName: maybeRootNotFile[i].FileName
                    });
                }

                _this.func_analysisfolders(maybeRootNotFile, (valtoset) => {
                    _this.m_rootfolders = valtoset;
                });

            }).catch(x => {
                debugger;
            });
        },

        // 前景样式
        // --------
        _stopPropagation(ev) {
            ev && ev.stopPropagation && ev.stopPropagation();
        },
        greet(val){
            var _this = this;
            _this.val = val;
        },
        getFrontStyle() {
            var _this = this;
            var _s = {};
            _s["width"] = _this.m_width;
            _s["height"] = _this.m_height;
            _s["position"] = 'fixed';
            _s["right"] = `calc(50% - ${parseInt(_this.m_width.toString().replace('px', 'px')) / 2}px)`;
            _s["top"] = `calc(50% - ${parseInt(_this.m_height.toString().replace('px', 'px')) / 2}px)`;
            return _s;
        }
    },
    props: {

        // 前景高度
        // 前景宽度
        // 总体的 zindex
        // 标题
        // -------------
        init_height: {
            type: String,
            required: true
        },
        init_width: {
            type: String,
            required: true
        },
        init_zIndex: {
            type: Number,
            required: true
        },
        init_title: {
            type: String,
            required: true
        },
        init_bimcomposerId: {
            type: String,
            required: true
        }
    }
}
</script>
<style scoped>
._css-openstack-splitter {
     float:left;
}
._css-openstack-item {
    /* height: 18px;
    line-height: 18px;
    cursor: pointer;
    font-size: 12px;
    float:left; */

    height: 18px;
    line-height: 18px;
    cursor: pointer;
    font-size: 12px;
    float: left;
    /* max-width: 400px;
    min-width: 26px;
    overflow-x: hidden; */
    /* text-overflow: ellipsis; */
    white-space: nowrap;
}
._css-openstack-item:hover {
    text-decoration: #1890FF;
    color:#1890FF;
}
._css-openstack-ctn {
    height: 36px;
    display: flex;
    align-items: center;
    padding-left: 12px;
    overflow-x: auto;
}
._css-docin-tree {
    font-size: 12px;
    width:235px;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.02);
    overflow-y: auto;
}
._css-docin-list {
    /* flex: 1; */
    width:calc(100% - 235px);
    height: 100%;
    display: flex;
    flex-direction: column;
}
._css-title-content {
    font-size: 16px;
    margin: 0 24px 0 24px;
    flex:1;
    text-align: left;
}
._css-docin-content {
    width:100%;
    flex:1;
    display: flex;
    height: calc(100% - 50px - 64px);
}
._css-docin-btnctn {
    border-top: 1px solid rgba(0, 0, 0, 0.09);
    height:50px;
    margin-top: 0;
    flex:none;
    box-sizing: border-box;
}
._css-docin-title {
    height: 64px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.09);
    flex:none;
    box-sizing: border-box;
}
._css-title-closebtn {
    margin-right: 24px;
}
._css-docbrowser-in {
    background-color: #fff;
    border-radius: 4px;
    display: flex;
    flex-direction: column;
}
._css-docbrowser {
    width: 100%;
    height:100%;
    position: fixed;
    background: rgba(0, 0, 0, 0.3);
    display: flex;
    align-items: center;
    justify-content: space-around;
}
</style>