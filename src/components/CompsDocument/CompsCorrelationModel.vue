<template>
  <el-dialog
    title="关联模型"
    class="text-left correlationModelDialog"
    :visible.sync="data.visible"
    width="32%"
    min-width="410px">
    <div class="model-list" v-loading=loading :element-loading-text="loadingMsg">
      <div :class="[ eq == index ? 'active' : '','list-item']" v-for="(item,index) in modelMsg" :key="item.ID" @click="selectCurrentItem(index)">
        <img v-if="item.Thumbnail != ''" :src="'data:image/png;base64,'+item.Thumbnail" alt="">
        <img v-else :src="imgUrl" alt="">

        <div class="model-msg">
          <p>{{ item.Name }}</p>
          <span>更新时间：{{ item.CreateTime | formatModelCreatTime }}</span>
        </div>

        <div class="has-checked">
          <span class="icon-checkbox-Selected-Disabled-dis-blue"></span>
        </div>
        <div class="clear"></div>

      </div>
    </div>
    <span slot="footer" class="dialog-footer">
    <el-button @click="data.visible = false">取 消</el-button>
    <el-button type="primary" @click="correlationTo">关 联</el-button>
  </span>
  </el-dialog>
</template>

<script>
    export default {
      name: "CompsCorrelationModel",
      data() {
        return {
          modelMsg: [],//模型列表数据
          eq: -1,//当前选中的模型的索引
          loading: false,
          loadingMsg: '数据请求中',
          imgUrl: require('../../assets/images/test.png'),
        }
      },

      props: {
        data: {
          type: Object,
        }
      },

      created() {
        this.getModelList();
      },

      methods: {
        //关联模型
        correlationTo() {
          var _this = this;
          this.loading = true;
          this.loadingMsg = '模型关联中';
          let data = {
            ProjectID: this.data.projectID,//<bimcomposerId>
            SourceID: this.modelMsg[this.eq].ID,//<模型ID>
            SourceName: this.modelMsg[this.eq].Name,//
            SourceTypeName: '模型',//模型
            TargetId: this.data.fileMsg[0].FileId,//<文档ID>
            TargetName: this.data.fileMsg[0].FileName,//文件名称(需加后缀)
            TargetTypeName: '文档',//文档
            SourceModelID: this.modelMsg[this.eq].ID,//<模型ID>
            CreateUserName: this.data.CreateUserName,//操作人姓名
            Token: this.$staticmethod.Get("Token"),
          };

          this.$axios.post(`${
            this.$staticmethod.getDocServer()
          }/api/Doc/AddFileRelation`,this.$qs.stringify(data)).then(res => {
            console.log(res);
            if (res.data == 'success' && res.status == 200) {
              this.$message({
                message: '关联成功',
                type: 'success'
              });

              // 需要刷新外面的列表。
              this.$emit("onok", _this.data.fileMsg[0].FileId);

              this.data.visible = false;

            } else {
              this.$message({
                message: '关联失败，请稍后再试',
                type: 'error'
              });
            }
            this.loading = false;
          }).catch(res=>{
            console.log(`模型关联出错：${res}`);
            this.loading = false;
          });
        },

        //获取模型列表数据
        getModelList() {
          this.loading = true;
          this.$axios.get(`${this.$configjson.bimserverurl}/Api/Prj/GetAllModels`,{
            params: {
              ProjectID: this.data.projectID
            }
          }).then(res => {
            if (res.status == 200) {
              this.modelMsg = JSON.parse(res.data);
              // console.log(this.modelMsg,'模型列表数据');
            } else {
              this.$message({
                type: 'error',
                message: '请求数据失败，请稍后再试。'
              })
            }

            this.loading = false;
          }).catch(res => {
            console.log(`获取模型列表数据出错：${res}`);
            this.loading = false;
          });
        },

        //点击选中当前模型
        selectCurrentItem(eq) {
          this.eq = eq;
        }
      },

      filters: {
        formatModelCreatTime(date) {
          let step1 = date.replace('T',' ');
          let step2 = step1.split('.');
          return step2[0];
        }
      }
    }
</script>

<style>
  .correlationModelDialog .el-dialog__body {
    padding: 20px 0;
  }
</style>

<style scoped>
  .correlationModelDialog {

  }

  .correlationModelDialog .model-list {
    max-height: 300px;
    min-height: 100px;
    overflow: auto;
  }

  .correlationModelDialog .model-list ul {
    padding: 0;
    margin: 0;
    overflow: auto;
    max-height: 300px;
  }

  .correlationModelDialog .model-list .list-item {
    position: relative;
    list-style: none;
    cursor: pointer;
    margin: 0 15px;
    padding: 12px 48px 12px 12px;
  }

  .correlationModelDialog .model-list .list-item > img {
    width: 120px;
    height: 90px;
    float: left;
    border-radius:2px 2px 0 0;
    box-shadow: 0 1px 3px -1px rgba(0,21,41,0.22);
  }

  .correlationModelDialog .model-list .list-item:hover{
    background:rgba(0,0,0,0.02);
  }

  .correlationModelDialog .model-list .list-item.active {
    background:rgba(0,0,0,0.04);
  }

  .correlationModelDialog .model-list .list-item .model-msg {
    height: 90px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    padding: 5px 0 5px 10px;
    box-sizing: border-box;
  }

  .correlationModelDialog .model-list .list-item .model-msg p {
    font-weight: 400;
    color: rgba(0,0,0,0.65);
    margin: 0;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    text-overflow: ellipsis;
    overflow: hidden;
  }

  .correlationModelDialog .model-list .list-item .model-msg span {
    font-size: 12px;
    font-weight: 400;
    color: rgba(0,0,0,0.25);
  }

  .correlationModelDialog .model-list .list-item .has-checked {
    display: none;
    width:48px;
    height: 114px;
    position: absolute;
    right: 0;
    top: 0;
    text-align: center;
    line-height: 114px;
    border-radius: 2px;
  }

  .correlationModelDialog .model-list .list-item.active .has-checked {
    display: block;
  }

  .correlationModelDialog .el-button {
    width: 56px;
    height: 32px;
    margin: 0 0 0 16px;
    border-radius: 2px;
  }
</style>
