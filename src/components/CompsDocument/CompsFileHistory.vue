<template>
  <div class="_css-hover">
    <div class="_css-front">
      <CompsDialogHeader @oncancel="dialogbtns_oncancel" 
      :title_maxwidth="data.title_maxwidth"
      :title="data.title"
      :title_left="data.title_left"
      :title_right="data.title_right"
      ></CompsDialogHeader>
      <!-- 中间的显示所有版本信息，暂无其它地方使用 -->
      <div class="_css-middle-itemcontainer css-miniscroll"  >
          <!-- 该文件的某一个历史版本 -->
          <div class="_css-middle-item" v-for="(item) in data.dataSource" :key="item.FileVersion">
              <!-- 文字信息行 -->
              <div class="_css-middle-itemtext" >
                  <!-- 版本图标 -->
                  <div class="_css-middle-itemtexticon">V{{item.FileVersion}}</div>
                  <!-- //版本图标 -->
                  <!-- 版本说明：更新时间 -->
                  <div class="_css-middle-itemtextcontent" >
                      <div :title="item.RealName" class="_css-updater" >{{item.RealName}}</div>
                      <div class="_css-updatedesc">于 {{item.CreateDateStr}} 更新</div>
                  </div>
                  <!-- //版本说明：更新时间 -->
                  <!-- 是否是当前版本的显示 -->
                  <div class="_css-middle-itemtextcurrent" v-if="item.IsCurrent">当前</div>
                  <!-- //是否是当前版本的显示 -->
              </div>
              <!-- //文字信息行 -->
              <!-- 按钮行 -->
              <div class="_css-middle-itembtn" >
                  <!-- <button class="_css-dis _css-btn _css-download" disabled="disabled" >下载</button> -->
                    <button class="_css-btn _css-download" @click.stop="_download_click(item)" >下载</button>
                  <button class="_css-btn _css-preview" @click.stop="_preview_click(item)">预览</button>
              </div>
              <!-- //按钮行 -->
          </div>
          <!-- //该文件的某一个历史版本 -->
      </div>
      <!-- //中间的显示所有版本信息，暂无其它地方使用 -->
      <CompsDialogBtns v-bind:data="CompsDialogBtns_data" @onok="dialogbtns_onok" @oncancel="dialogbtns_oncancel"></CompsDialogBtns>
    </div>
  </div>
</template>
<script>
/*
datainput:
    data: {
    }
events:
    oncancel()
    onitemdownload(item)
    onitempreview(item)
*/
import CompsDialogBtns from '@/components/CompsDialog/CompsDialogBtns'
import CompsDialogHeader from '@/components/CompsDialog/CompsDialogHeader'
export default {
  components: {
    CompsDialogBtns,
    CompsDialogHeader
  },
  data() {
    return {
        CompsDialogBtns_data: {
            btnstype: 'close'
        }
    };
  },
  mounted(){
      var _this = this;
  },
  props: {
    data: {
      type: Object,
      required: true
    }
  },
  methods: {
    _download_click(item){
      var _this = this;
      _this.$emit('onitemdownload', item);
    },
    _preview_click(item){
      var _this = this;
      _this.$emit('onitempreview', item);
    },
    dialogbtns_onok() {
      var _this = this;
      _this.$emit("onok", _this.data._selectednodekey);
    },
    dialogbtns_oncancel() {
      var _this = this;
      _this.$emit("oncancel");
    }
  }
};
</script>
<style scoped>
._css-updatedesc{
    margin-left: 4px;
}
._css-download{
    margin-left:60px;
}
._css-preview{
    margin-left:16px;
}
._css-btn:not(._css-dis):hover{
    color:#1890FF;
    border-color:#1890FF;
}
._css-btn{
    width:40px;
    height:20px;
    font-size: 12px;
    border-radius:2px;
    border:1px solid rgba(0,0,0,0.25);
    background-color: transparent;
    font-size:12px;
    outline: none;
}
._css-btn:not(._css-dis){
    cursor:pointer;
}
._css-dis{
    color:rgba(0,0,0,0.25);
    cursor: not-allowed;
}
._css-middle-itemtextcurrent{
    width:45px;
    height:20px;
    margin-left: 8px;
    border-radius: 2px;
    background-color:rgba(19,194,194,0.1);
    color:rgba(19,194,194,1);
    font-size: 12px;
    border:1px solid rgba(19,194,194,0.45);
    display: flex;
    align-items: center;
    justify-content: space-around;
}
._css-updater{
    font-weight: 500;
    width:45px;
    overflow-x: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}
._css-middle-itemtextcontent{
    width:250px;
    height:100%;
    margin-left: 12px;
    display: flex;
    align-items: center;
    font-size:14px;
}
._css-middle-itemtexticon{
    height:24px;
    width:24px;
    margin-left: 24px;
    background-color: #1890FF;
    border-radius: 4px;
    font-size:12px;
    color:#fff;
    display: flex;
    align-items: center;
    justify-content: space-around;
}
._css-middle-itembtn{
    height:20px;
    margin-top: 8px;
    align-items: center;
    display: flex;
}
._css-middle-itemtext{
    height:24px;
    margin-top:14px;
    display: flex;
    align-items: center;
}
._css-middle-item{
    height:80px;
    background-color: #fff;
    border-top:1px solid transparent;
    box-sizing: border-box;
}
._css-middle-item:hover{
    background-color: rgba(0,0,0,0.04);
}
._css-middle-itemcontainer{
    padding-top:12px;
    padding-bottom: 12px;
    max-height: 240px;
    overflow-y: auto;
}
._css-hover {
  position: fixed;
  top: 0;
  left: 0;
  background-color: rgba(0, 0, 0, 0.45);
  z-index: 3;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: space-around;
}
._css-front {
  width: 500px;
  height: auto;
  border-radius: 4px;
  background-color: #fff;
}
</style>