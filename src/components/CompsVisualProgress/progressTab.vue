<template>
    <div class="project-tab-wampper">
        <ul class="tab-tilte">
            <li class="tab-list font-FFFFFF-50 font-5-family"
                :class="{active:index===changeIndex}"
                v-for="(item,index) in tabList"
                :key="index"
                @click="toggleTab(index)">{{item.name}}</li>
        </ul>
        <div v-for='(item,numindex) in tabList' :key='numindex'>
            <div v-if="changeIndex==numindex">
                <superviseList :changeIndex="numindex"></superviseList>
            </div>
        </div>

    </div>
</template>
<script>
import superviseList from './superviseList'
export default {
    name: 'projectTab',
    components: {
        superviseList
    },
    data() {
        return{
            progressActive: 'first',
            tabList: [
                {
                name: '监管库1'
                },
                {
                name: '监管库2'
                },
                {
                name: '监管库3'
                },
                {
                name: '监管库4'
                }
                
            ],
            changeIndex: 0,
            active: 0,
        }
    },
    mounted(){
        
    },
    methods:{
        handleClickProgressTab(tab, event) {
            console.log(tab, event);
        },
        toggleTab(index) {
            this.active = index
            this.changeIndex = index
        },	
    }
}
</script>
<style lang="scss" scoped>
.project-tab-wampper{
    .tab-tilte{
        display: flex;
        .tab-list{
            width: 25%;
            background: url('~@/assets/images/VisualPanel/progress_tab.png') no-repeat;
            line-height: 30px;
            background-size: cover;
            text-align: center;
            cursor: pointer;
        }
        .active{
            color: #26ABFF;
        }  
        .tab-list:hover{
            color: #26ABFF;
        }
    }
}
</style>