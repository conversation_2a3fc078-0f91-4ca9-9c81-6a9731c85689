<template>
    <div class="supervise-list public-bg">
        <p class="font-26ABFF font-5-family">施工完成产值进度（万元）</p>
        <p class="list-icon font-5-family">
            <span><i></i>计划完成</span>
            <span><i class="C81C784"></i>上月完成</span>
            <span><i class="C43A047"></i>累计完成</span>
        </p>
        <!-- <div style="width:100%;height:125px;overflow-y:hidden;overflow-x: scroll;margin-bottom:10px;">
            <vue-seamless-scroll :data="modelTabList" class="seamless-warp" :class-option="classOption">
                <v-chart ref="barEcharts" :options="barOption" style="width:850px;height:125px;"></v-chart>
            </vue-seamless-scroll>
        </div> -->
        <div style="width:400px;height:100px;">
            <v-chart ref="barEcharts" :options="barOption" style="width:400px;height:100px;"></v-chart>
        </div>
    </div>
</template>
<script>
// import vueSeamlessScroll from "vue-seamless-scroll";

export default {
    name: 'superviseList',
    components: {
        // vueSeamlessScroll
    },
    data() {
        return{
            // ,'钢结构','金属屋面 雨棚','金属幕墙','二次结构','机电施工','装饰工程','市政工程'
            modelTabList: ['基础结构'],
            arrayTwo: [],
            series0:[],
            series1:[],
            series2:[],
            series3:[],
            barOption: {
                tooltip: {
                    show:false,
                },
                grid: {
                    left: '-18',
                    right: '5%',
                    top: '0',
                    bottom: '1',
                    containLabel: true
                },
                dataZoom: [
                    {
                        type: 'inside',
                        start:0,
                        end: 40,
                        height:'1',
                        zoomLock: true,
                    },
                    {
                        type: 'slider',
                        zoomLock: true,
                        bottom: 1,
                        backgroundColor:'#C7D1DA',
                        borderColor: '#C7D1DA',
                        height: '3',
                        dataBackground: {
                            lineStyle: {
                            color:'transparent'
                            },
                            areaStyle: {
                            color: 'transparent',
                            }
                        },
                        selectedDataBackground: {
                            lineStyle: {
                            color:'transparent'
                            },
                            areaStyle: {
                            color: 'transparent',
                            }
                        },
                        handleStyle: {
                            color:'transparent',
                            borderColor:'transparent',
                        },
                        moveHandleSize: 2,
                        moveHandleStyle: {
                            color:'transparent',

                        },
                        textStyle: {
                            color:'transparent',
                        }
                    }
                ],
                xAxis: [
                    {
                        type: 'category',
                        axisLine: {
                            show: false,  
                        },
                        axisTick: {
                            show: false,    
                        },
                        axisLabel: {
                            color: 'rgba(255,255,255,0.5)',
                            interval:0
                        },
                        data: ['基础结构','钢结构','金属屋面 雨棚','金属幕墙','二次结构','机电施工','装饰工程','市政工程']
                    }
                ],
                yAxis: [
                    {
                        type: 'value',
                        show: false,
                    }
                ],
                series: [
                    {
                        name: '计划完成',
                        type: 'bar',
                        emphasis: {
                            focus: 'series'
                        },
                        label: {
                            show: true,
                            color: 'rgba(255,255,255,0.5)',
                            position: 'inside',
                            rotate: 90,
                        },
                        color: '#536DFE',
                        barGap: 0,
                        barWidth: 16,
                        data: [320, 332, 301, 334, 390]
                    },
                    {
                        name: '上月完成',
                        type: 'bar',
                        emphasis: {
                            focus: 'series'
                        },
                        label: {
                            show: true,
                            color: 'rgba(255,255,255,0.5)',
                            position: 'inside',
                            rotate: 90,
                        }, 
                        color: '#81C784',
                        barWidth: 16,
                        data: [120, 132, 101, 134, 90]
                    },
                    {
                        name: '累计完成',
                        type: 'bar',
                        emphasis: {
                            focus: 'series'
                        },
                        label: {
                            show: true,
                            color: 'rgba(255,255,255,0.5)',
                            position: 'inside',
                            rotate: 90,
                        },
                        barWidth: 16,
                        color: '#43A047',
                        data: [220, 182, 191, 234, 290]
                    }
                ]
            }
        }
    },
    computed: {
        classOption() {
            return {
                step: 0.6, // 数值越大速度滚动越快
                limitMoveNum: 1, // 开始无缝滚动的数据量 this.dataList.length
                hoverStop: true, // 是否开启鼠标悬停stop
                direction: 2, // 0向下 1向上 2向左 3向右
                openWatch: true, // 开启数据实时监控刷新dom
                singleHeight: 0, // 单步运动停止的高度(默认值0是无缝不停止的滚动) direction => 0/1
                singleWidth: 0, // 单步运动停止的宽度(默认值0是无缝不停止的滚动) direction => 2/3
                waitTime: 1000 // 单步运动停止的时间(默认值1000ms)
            };
        }
    },
    props : {
        changeIndex: {
            type: Number,
            required: false
        }
    },
    mounted(){
        this.getTabAxios();
        this.tabtimer = setInterval(() => {
            this.getTabAxios()
        }, 5000)
        // this.changeOptionDataZoom();
    },
    methods: {
        changeOptionDataZoom(){
            if(this.barOption.dataZoom.length <= 0) return;
            this.dataZoomTimer = setInterval(() => {
                // 每次向后滚动一个，最后一个从头开始。
            let startV = 0, endV = 48;
                if (this.barOption.dataZoom[0].end >= 100 ) {
                    this.barOption.dataZoom[0].start = 0;
                    this.barOption.dataZoom[0].end = 40; 
                    startV = 0; endV = 40;
                }else {
                    startV = 50; endV = 100;
                }
                this.$set(this.barOption.dataZoom[0],'start',startV);
                this.$set(this.barOption.dataZoom[0],'end',endV);
            }, 4000);
        },
        changeSeriesData(){
            let _s = [];
            let _this = this;
            switch (this.changeIndex){
                case 0:
                    _s = _this.series0
                    break;
                case 1:
                    _s = _this.series1
                    break;
                case 2:
                    _s = _this.series2
                    break;
                case 3:
                    _s = _this.series3
                    break;
            }

            _s.forEach((item,index) => {
                _this.$set(_this.barOption.series[index],'data',item)
            })
            // console.log(this.barOption)
        },
        getTabAxios(){
            let _this = this;
            let url = `${window.pubilc_config.webserverurl}/api/Daxing/Daxing/`;
            _this.$axios
                .get(url+'Getimage')
                .then(res=>{
                    if (res.status == 200) {
                        let beforeData = res.data;

                        // 返回的数据为一维数组，先按照image_type分类 得到arrayTwo为分类后的二维数组
                        // 转换后的二维数组
                        _this.arrayTwo = Object.values(beforeData.reduce((res, item) => {
                            res[item.image_type] ? res[item.image_type].push(item) : res[item.image_type] = [item];
                            return res;
                        }, {}));
                        // console.log(_this.arrayTwo,'==')
                        for(let i =0; i < _this.arrayTwo.length; i++){
                            let series = 'series' +String(i)
                            let arr = ['image_complete','image_lastweek','image_accumulative']
                            for(let j =0; j < arr.length; j++){
                              _this[series][j] = _this.getarr(_this.arrayTwo[i],arr[j])
                            }
                        }

                        this.changeSeriesData();

                    }
                }).catch(err=>{
                    console.log(err,'===api/Daxing/Daxing/Getimage')
                })
        },
        getarr(arr,params){
            let _a = []
            arr.forEach(item=>{
                _a.push(item[params]);
            })
            return _a
        },
    },
    
    destroyed () {
        clearTimeout(this.tabtimer)
        clearTimeout(this.dataZoomTime)
    }
}
</script>
<style lang="scss" scoped>
 * {
  margin: 0;
  padding: 0;
  list-style: none;
  -moz-user-select: none;
  -o-user-select:none;
  -khtml-user-select:none;
  -webkit-user-select:none;
  -ms-user-select:none;
  user-select:none;
}

html,body {
  height: 100%;
  font-size: 14px;
  font-family: 'SourceHanSansCN'
}
.public-bg{
  background: linear-gradient(rgba(7, 17, 41, 0.8), rgba(19, 31, 54, 0.8));
  margin-bottom: 10px;
  border-radius: 2px;
}

.supervise-list{
    p{
        padding: 20px 20px 0 20px;
        line-height: 34px;
        text-align: left;
    }
    .list-icon{
        padding-top:0;
        span{
            display: inline-block;
            margin: 5px 12px 0px 0px;
            i{
                display: inline-block;
                width: 10px;
                height: 10px;
                background: #536DFE;
                margin-right: 6px;
                border-radius: 1px 0px 0px 0px;
            }
            i.C81C784{
                background:#81C784;
            }
            i.C43A047{
                background:#43A047;
            }
        }
    }
}
</style>