<template>
    <div class="all-cost-list">
        <zdialog-function
            init_title="填报历史"
            :init_zindex="1001"
            :init_innerWidth="1200"
            :init_width="1200"
            :element-loading-text="loadText"
            v-loading="hasLoad"
            init_closebtniconfontclass="icon-suggested-close"
            @onclose="closeCostDialog()">
            <!-- 输入区域 -->
            <div slot="mainslot">

            <el-container>
                <el-aside width="260px">
                    <div class="cost-list-left">
                        <div class="_css-line">
                            <div class="_css-title">构件名称：</div>
                            <div class="_css-fieldvalue">{{currentData.structure}}</div>
                        </div>

                        <div class="_css-line">
                            <div class="_css-title">项目编码：</div>
                            <div class="_css-fieldvalue">{{currentData.code}}</div>
                        </div>


                        <div class="_css-line">
                            <div class="_css-title">计量标准：</div>
                            <div class="_css-fieldvalue">{{currentData.standard}}</div>
                        </div>

                        <div class="_css-line">
                            <div class="_css-title">工程单位：</div>
                            <div class="_css-fieldvalue">{{currentData.Company}}</div>
                        </div>

                        <div class="_css-line">
                            <div class="_css-title">填报合价</div>
                            <div class="_css-fieldvalue">{{ currentData.fillTotalPrice || 0 }}</div>
                        </div>

                        <div class="_css-line">
                            <div class="_css-title">预算工程量：</div>
                            <div class="_css-fieldvalue">{{currentData.Quantities}}</div>
                        </div>

                        <div class="_css-line">
                            <div class="_css-title">预算合价：</div>
                            <div class="_css-fieldvalue">{{currentData.totalPrice}}</div>
                        </div>

                        <div class="_css-line">
                            <div class="_css-title">预算总费用：</div>
                            <div class="_css-fieldvalue">{{currentData.totalCost}}</div>
                        </div>

                        <div class="_css-line">
                            <div class="_css-title">计划填报总费用：</div>
                            <!-- <div class="_css-fieldvalue">{{ currentData.fillTotalCost }}</div> -->
                            <div class="_css-fieldvalue">{{ getPlanPrice }}</div>
                        </div>

                        <div class="_css-line">
                            <div class="_css-title">实际填报总费用：</div>
                            <!-- <div class="_css-fieldvalue">{{ currentData.RealSumPrice}}</div> -->
                            <div class="_css-fieldvalue">{{ allCostPrice }}</div>
                        </div>

                        <div class="_css-line">
                            <div class="_css-title">状态：</div>
                            <!-- 预算总费用 - 实际填报总费用 < 0 ? '超支' : '正常' -->
                            <div :class="[currentData.totalCost - allCostPrice < 0  ? 'cost-item-overspending' : 'cost-item-normal','_css-fieldvalue']">
                                {{ currentData.totalCost - allCostPrice < 0 ? '超支' : '正常' }}
                            </div>
                        </div>
                    </div>
                </el-aside>
                <el-container>
                    <el-table
                        :data="currenAllData.records"
                        style="width: 100%;"
                        max-height="410"
                        row-key="cmr_guid"
                        border
                        default-expand-all
                        class="_css-table-ele css-scroll _css-customstyle css-fixedleftgbcolor-white">

                        <el-table-column
                            label="填报日期">
                            <template slot-scope="scope">
                                <div class="_css-costitem" >{{scope.row.cmr_RecordDateTime.split('T')[0]}}</div>
                            </template>
                        </el-table-column>

                        <el-table-column
                            label="计划完成工程量">
                            <template slot-scope="scope">
                                <div class="_css-costitem" >{{scope.row.cmr_PlanEngQua}}</div>
                            </template>
                        </el-table-column>

                        <el-table-column
                            label="实际完成工程量">
                            <template slot-scope="scope">
                                <div class="_css-costitem" >{{scope.row.cmr_RealEngQua}}</div>
                            </template>
                        </el-table-column>

                        <!-- <el-table-column
                            label="填报合价">
                            <template slot-scope="scope">
                                <div class="_css-costitem" >{{scope.row.cmr_RealGroupPrice}}</div>
                            </template>
                        </el-table-column> -->

                        <el-table-column
                            label="计划填报费用">
                            <template slot-scope="scope">
                                <div class="_css-costitem" >{{scope.row.cmr_PlanEngQua * currenAllData.maindata.cmm_RealGroupPrice}}</div>
                            </template>
                        </el-table-column>

                        <el-table-column
                            label="实际填报费用">
                            <template slot-scope="scope">
                                <div class="_css-costitem" >{{scope.row.cmr_RealEngQua * currenAllData.maindata.cmm_RealGroupPrice}}</div>
                            </template>
                        </el-table-column>

                        <el-table-column
                            width="130"
                            label="操作">
                            <template slot-scope="scope">
                                <div class="_css-costitem">
                                    <span class="css-hover-btn cost-tree-btn css-mr8" @click="editCost(scope.row)">编辑</span>
                                    <span class="css-hover-btn cost-tree-btn" @click="deleteCost(scope.row,scope.$index,currenAllData.records)">删除</span>
                                </div>
                            </template>
                        </el-table-column>
                    </el-table>
                </el-container>
            </el-container>

            </div><!-- //输入区域 -->
        </zdialog-function>
    </div>
</template>

<script>
export default {
    //查看所有填报
    name: 'CompsAllCostList',
    props: ['currentData','currenAllData'],
    data() {
        return {
            tableData: [],
            allCostPrice: 0,//右侧列表 填报总费用之和
            getPlanPrice: 0,
            needRefresh: false,//是否需要刷新
            hasLoad: false,
            loadText: '',
        }
    },

    methods: {
        closeCostDialog() {
            let params = {
                fillTotalCost: this.getPlanPrice,//计划填报总费用
                RealSumPrice: this.allCostPrice,//实际填报总费用
            }
            this.$emit('close',this.needRefresh,params);
        },

        //删除某条数据
        deleteCost(item,index,arr) {
            // console.log(item,index,arr)

            this.$confirm('删除此条填报？', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                let params = {
                    Token: this.$staticmethod.Get("Token"),
                    cmr_guid: item.cmr_guid,
                }
                this.loadText = '数据同步中...'
                this.hasLoad = true
                this.$axios.post(`${this.$configjson.webserverurl}//api/CostMgr2Controllers/CostMgrList/DeleteRecord?Token=${this.$staticmethod.Get('Token')}`,
                this.$qs.stringify(params)).then(res=>{
                    this.hasLoad = false;
                    // console.log(res,'删除')
                    if (res.data.Ret == 1) {
                        arr.splice(index, 1);
                        this.$message.success('删除成功')
                        this.needRefresh = true;
                        this.allCostPrice -= item.cmr_RealEngQua * this.currenAllData.maindata.cmm_RealGroupPrice;//实际填报总费用
                        this.getPlanPrice -= item.cmr_PlanEngQua * this.currenAllData.maindata.cmm_RealGroupPrice;//计划填报总费用
                        let p ={
                              fillTotalCost: this.getPlanPrice,//计划填报总费用
                              RealSumPrice: this.allCostPrice,//实际填报总费用
                              totalPlanned: 0 - item.cmr_PlanEngQua,
                              totalAmount:  0 - item.cmr_RealEngQua
                          }
                        this.$emit('deleteR',this.needRefresh,p)
                    } else {
                        this.$message.error(res.data.Msg)
                    }
                });
            }).catch(() => {this.hasLoad = false});
        },

        editCost(item) {
            // console.log(item,'编辑');
            let params = {
                fillTotalCost: this.getPlanPrice,//计划填报总费用
                RealSumPrice: this.allCostPrice,//实际填报总费用
            }
            this.$emit('editCost',item,this.currentData,params);
        }
    },

    watch: {
        currenAllData(n) {
            let total = 0;
            if (n.records.length > 0) {
                this.allCostPrice = 0;
                this.getPlanPrice = 0;
                n.records.forEach(item=>{
                    //实际填报总费用
                    this.allCostPrice += item.cmr_RealEngQua * n.maindata.cmm_RealGroupPrice;
                    //计划填报总费用
                    this.getPlanPrice += item.cmr_PlanEngQua * n.maindata.cmm_RealGroupPrice;
                })
            }
        }
    }
};
</script>

<style scoped>
    .cost-list-left {
        padding: 10px 0;
        border-right: 1px solid #DCDCDC;
    }

    .cost-list-left ._css-line {
        height: 35px;
        padding: 0 24px;
        box-sizing: border-box;
        display: flex;
        align-items: center;
    }

    .cost-list-left ._css-line:nth-child(odd) {
        background-color: #f9f9f9;
    }

    ._css-line ._css-title {
        text-align: left;
        width: 115px;
    }

    ._css-line  ._css-fieldvalue {
        text-align: left;
        flex: 1;
        text-overflow: ellipsis;
        white-space: nowrap;
        overflow-x: hidden;
    }

    ._css-costitem {
        text-overflow: ellipsis;
        white-space: nowrap;
        overflow-x: hidden;
        font-size: 14px;
        font-weight: 100;
    }

    .cost-tree-btn {
        border-radius: 4px;
        color: #fff;
        font-size: 12px;
        padding: 5px 10px;
        background: linear-gradient(224deg,#0091ff,#007aff);
    }

    .cost-item-normal {
        color: #67C23A;
    }

    .cost-item-overspending {
        color: #CC0000;
    }
</style>
