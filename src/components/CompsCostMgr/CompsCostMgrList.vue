<template>
    <div class="wrapper"
        
    >

        <!-- 大列表 -->
        <div 
        v-if="m_showlargepage"
        class="_css-largepage-all">
        
            <!-- 64 -->
            <div class="_css-large-head" @click="func_hideinnerall($event)">

                <div
                @click="func_hidelargepage($event)"
                 class="icon-arrow-left_outline _css-back">
                </div>

                <!-- 当前方案名称 -->
                <div class="_css-lefttopname">{{m_currentcostmgrname}}</div>
                <!-- //当前方案名称 -->

            </div>
            <!-- //64 -->

            <!-- 组件列表 -->
            <div class="_css-large-body">
                <CompsCostMgr
                :init_hidetree="true"
                :init_specificCate="m_specificcate"
                :init_specificmodelid="m_specificmodelid"
                :init_specificmodelname="m_specificmodelname"
                ref="ref_costmgr"
                ></CompsCostMgr>
            </div>
            <!-- //组件列表 -->

        </div>
        <!-- //大列表 -->

        <CompsDialogProgress
            @close=closeAll()
            v-show="dialogConfig.mainShow"
            @centerClick=centerClick
            @maskClick="maskClick"
            title="成本管理方案"
            >
            <div slot="btn"
            >
                <template v-if="getIsDisEdit()">
                    <div class="_pe-dis" >创建方案</div>
                    <div class="_pe-dis" >导入方案</div>
                </template>
                <template v-else>
                    <div @click="addProgressUI">创建方案</div>
                    <div @click="clickcostplanimport" >导入方案</div>
                </template>
              
            </div>
            <div slot="main"
            v-loading="m_listloading"
            element-loading-text="加载中"
            >
                <ul class="entry-list" :style="{height:'244px'}" v-show="m_costmgrList.length>0" ref="entryUl">
                    <li click.stop v-for="(item, index) in m_costmgrList" :key="index" @click="func_costmgritemclick(item)">
                        <i class="icon-interface-component_cost"></i>
                        <span>{{item.label}}</span>
                        <i class="icon-interface-more" @click.stop="submenuShowClick(index,item,$event)"></i>
                    </li>
                </ul>
                <div v-show="m_costmgrList.length==0" class="no-data">
                    <img src="../../../static/images/progress-no-data.svg" alt="">
                    <p>暂无方案</p>
                </div>
                <div class="sub-menu" :style="{'top':submenuTop + 'px'}" v-show="submenuShow">
                    <div @click="editProgressUI"
                    v-if="!getIsDisEdit()"
                    >
                        <i class="icon-interface-edit"></i>
                        重命名方案
                    </div>


                    <div @click="exportPlan">
                        <i class="icon-interface-download-fill"></i>
                        导出方案
                    </div>


                    <div @click="delProgressUI"
                    v-if="!getIsDisEdit()"
                    >
                        <i class="icon-interface-model-delete"></i>
                        删除方案
                    </div>
                </div>
            </div>
        </CompsDialogProgress>
        <CompsDialogProgress
            v-if="dialogConfig.addShow"
            :isBack=true
            @close=closeAll()
            @back=back
            title="创建方案">
            <div slot="main" style="height:134px">
                <div class="entry-list">
                    <p>方案名称</p>
                    <input type="text" v-model="dialogConfig.addInputData">
                </div>
            </div>
            <div slot="btn">
                <div style="background:#1890FF;color:#fff;" @click="ProgressBtnClick(0)">创建方案</div>
            </div>
        </CompsDialogProgress>
        <CompsDialogProgress
            v-if="dialogConfig.delShow"
            :isBack=true
            @close=closeAll()
            @back=back
            title="删除方案">
            <div slot="main" style="height:134px">
                <div class="entry-list">
                    <p>此操作将清空内部清单数据，确认删除？</p>
                </div>
            </div>
            <div slot="btn">
                <div style="background:#F5222D;color:#fff;border:1px solid #F5222D;" @click="ProgressBtnClick(1)">删除</div>
            </div>
        </CompsDialogProgress>
        <CompsDialogProgress
            v-if="dialogConfig.editShow"
            :isBack=true
            @close=closeAll()
            @back=back
            title="重命名分类">
            <div slot="main" style="height:134px">
                <div class="entry-list">
                    <p>方案名称</p>
                    <input type="text" v-model="dialogConfig.addInputData">
                </div>
            </div>
            <div slot="btn">
                <div style="background:#1890FF;color:#fff;" @click="ProgressBtnClick(2)">确定</div>
            </div>
        </CompsDialogProgress>

        <input
            type="file"
            style="display:none"
            id="id_importcostplan"
            accept="*.xlsx"
        @change="importcostplan_change()" />

    </div>
</template>

<script>
import CompsDialogProgress from "../CompsDialog/CompsDialogProgress"
import { debug } from 'util';
import CompsCostMgr from "@/components/CompsCostMgr/CompsCostMgr";

export default {
    components: { CompsDialogProgress, CompsCostMgr },
    props: {
        currentModelID:{
            type: String
        },
        currentModelName: {
            type: String
        },
        phaseID:{
            type: String
        },
        disedit:{
            type: Boolean,
            required: false
        }
    },
    data() {
        return {

            m_listloading: false,

            // 模型ID
            // ------
            m_specificmodelid: '',
            m_specificmodelname: '',

            // 哪个成本管理方案
            // ---------------
            m_specificcate: undefined,

            // 是否显示大列表
            // -------------
            m_showlargepage: false,

            // 当前大列表正在显示的方案名称和id
            // ------------------------------
            m_currentcostmgrname: '',
            m_currentcostmgrid:'',

            failurenum:0,

            dialogConfig:{
                mainShow:true,
                addShow:false,
                editShow:false,
                delShow:false,
                addInputData:''
            },
            
            submenuTop: 62,
            submenuShow:false,
            m_costmgrList:[],
            projectid:'',
            currentPlan:{
                data:null,
                index:null
            }
        }
    },
    watch: {
    },
    computed: {},
    methods: {

        // 关闭内部页面的全部
        // ----------------
        func_hideinnerall(ev) {
            var _this = this;
            _this.$refs.ref_costmgr && _this.$refs.ref_costmgr.func_closeall();
        },

        // 移除方案
        // --------
        do_delcostmgrplan(id) {
            var _this = this;
            var _Token = _this.$staticmethod.Get("Token");
            var _cmp_guid = id;
            _this.$axios({
                method:'post',
                data: _this.$qs.stringify({
                    cmp_guid: _cmp_guid,
                    Token: _Token
                }),
                url: `${window.bim_config.webserverurl}/api/CostMgrControllers/Cost/DelTreeData`
            }).then(x => {
                if (x.data.Ret > 0) {
                    _this.$message.success('操作成功');
                    _this.dialogConfig.delShow = false;
                    _this.dialogConfig.mainShow = true;
                    _this.reload();
                } else {
                    _this.$message.error(x.data.Msg);
                     _this.dialogConfig.delShow = false;
                    _this.dialogConfig.mainShow = true;
                    //_this.reload();
                }
            }).catch(x => {
                _this.$message.error(x);
            });
        },

        // 弹出操作确认
        // -----------
        func_delcostmgrplan(id) {
            var _this = this;
            _this.do_delcostmgrplan(id);
        },

        // 添加或重命名成本管理方案
        // ----------------------
        func_addcostmgrplan(name, modelid, cmpguid) {
            var _this = this;
            var _Token = _this.$staticmethod.Get("Token");
            var _organizeId = _this.$staticmethod._Get("organizeId");
            var _modelId = modelid;
            var _name = name;
            var _cmp_guid = '';
            if (cmpguid) {
                _cmp_guid = cmpguid;
            }
            var _url = `${window.bim_config.webserverurl}/api/CostMgrControllers/Cost/EditTreeData`;
            var _postdata = {
                Token: _Token,
                organizeId: _organizeId,
                modelId: _modelId,
                name: _name,
                cmp_guid: _cmp_guid
            };
            _this.$axios({
                method:'post',
                url: _url,
                data: _this.$qs.stringify(_postdata)
            }).then(x => {
                if (x.data.Ret > 0) {

                    // 弹出添加成功，并刷新，隐藏添加、编辑界面，显示列表页面
                    // --------------------------------------------
                    _this.$message.success("保存成功");
                    _this.dialogConfig.addShow = false;
                    _this.dialogConfig.editShow = false;
                    _this.dialogConfig.mainShow = true;
                    _this.reload();

                } else {
                    _this.$message.error(x.data.Msg);
                }
            }).catch(x => {
                _this.$message.error(x);
            })
        },

        // 隐藏大（方案详情）页面
        // --------------------
        func_hidelargepage(ev) {
            var _this = this;
            _this.m_showlargepage = false;
        },

        getIsDisEdit() {
            var _this = this;
            if (_this.disedit) {
                return true;
            } else {
                return false;
            }
        },

        clickcostplanimport(){
            var _this = this;
            var dom = document.getElementById("id_importcostplan");
            dom.value = '';
            dom.click();
        },

        importonce(){

            // 触发它点击之前 先设置 value = '';
            var _this = this;
            var dom = document.getElementById("id_importcostplan");
            var files = dom.files;            
            if (files.length == 0) {
                _this.$message.error('未选择文件');
                return;
            }
            var file = files[0];
            //debugger;
            // 调用 webapi，将project文件发送到服务器
            // webapi 中：http://localhost:9095/API/importProject.ashx DFile， 返回 {"Ret":1,"Msg":"OK","Data":"8f6982af-2a48-4592-8eb9-5e5465d6c294"}
            // 在成功的回调中，刷新列表。

            // 调用接口
            // --------
            var _url = `${window.bim_config.webserverurl}/api/CostMgrControllers/Cost/ImportTreeTableDataWithPlanCreating`;
            var fd = new FormData();
            fd.append("Token", _this.$staticmethod.Get("Token"));
            fd.append("cmp_guid", '');
            fd.append("F1", file);
            fd.append("cmp_modelId", _this.currentModelID);
            fd.append("cmp_organizeId", _this.$staticmethod._Get("organizeId"));
            var config = 
            {
                headers: {
                "Content-Type": "multipart/form-data"
                }
            };
            _this.m_listloading = true;
            _this.$axios
                .post(
                    _url,
                    fd,
                    config
                ).then(x => {
                    _this.m_listloading = false;
                    if (x.data.Ret > 0) {
                        
                        // 提示添加成功，并在外面的数据中 unshift
                        // -----------------------------------
                        _this.$message.success('导入成功');
                        var newdata = {
                            children: [],
                            classname: "icon-interface-component_cost _css-plan-icon",
                            id: x.data.Data.Item.cmp_guid,
                            label: x.data.Data.Item.cmp_name
                        };
                        _this.m_costmgrList.unshift(newdata);

                    } else {
                        _this.$message.error(x.data.Msg);
                    }
                }).catch(x => {
                    _this.m_listloading = false;
                    console.error(x);
                });
        },

        iffailureretry(){
            var _this = this;
            if (_this.failurenum <= 5) {
                _this.failurenum++;
                _this.importonce();
            } else {
                _this.failurenum = 0;
                _this.$message.error('导入失败');
            }
        },

        importcostplan_change(){
            var _this = this;

            // 清空失败次数
            _this.failurenum = 0;

            _this.importonce();
        },

        // 导出当前成本方案
        // ---------------
        exportPlan(){
            var _this = this;
            var _url = `${window.bim_config.webserverurl}/api/CostMgrControllers/Cost/ExportCurrentPlan?cmp_guid=${_this.currentPlan.data.id}&token=${this.$staticmethod.Get("Token")}`;
            window.location.href = _url;
        },

        // 弹出一个全屏页面，上方留出 64 高度（返回和保存按钮）
        // 页面写在本文件中
        // ---------------
        func_costmgritemclick(item){
            // let data = {
            //     projectid:this.projectid,
            //     modelid:this.$props.currentModelID,
            //     planid:item.bop_planId,
            //     title:item.label
            // }
            // this.$emit('getCurrentPlanListData',data)

            var _this = this;
            _this.m_currentcostmgrname = item.label;
            _this.m_currentcostmgrid = item.id;
            _this.m_specificcate = {
                id:item.id,
                label:item.label
            };

            // 模型也传进去
            // -----------
            _this.m_specificmodelid = _this.currentModelID;
            _this.m_specificmodelname = _this.currentModelName;

            _this.m_showlargepage = true;
            _this.$nextTick(() => {
                _this.$refs.ref_costmgr.func_gettabledata(item.id);
            });


        },
        back(judge){
            this.dialogConfig.mainShow = true
            switch(judge){
                case 0: this.dialogConfig.addShow = false;
                        this.dialogConfig.addInputData = '';
                break;
                case 1: this.dialogConfig.delShow = false;
                break;
                case 2: this.dialogConfig.editShow = false;
                        this.dialogConfig.addInputData = '';
                break;
            }
        },
        closeAll(){
            this.$emit('onclose')
        },
        delProgressUI(){
            this.dialogConfig.mainShow = false
            this.dialogConfig.delShow = true
        },
        editProgressUI(){
            this.dialogConfig.mainShow = false
            this.dialogConfig.editShow = true
        },
        addProgressUI(){
            this.dialogConfig.addInputData = '';
            this.dialogConfig.mainShow = false;
            this.dialogConfig.addShow = true;
        },
        ProgressBtnClick(judge){
            var _this = this

            if(judge != 1){
                if (this.dialogConfig.addInputData == "") {
                    this.$message({
                        message: "输入名称",
                        type: "warning"
                    })
                    return false
                }
                // this.m_costmgrList.forEach(item=>{
                //     if(item.label == this.dialogConfig.addInputData){
                //         this.$message({
                //             message: "名称重复",
                //             type: "warning"
                //         })
                //         return false
                //     }
                // })
            }
            
            switch (judge) {
                case 0 :
                    _this.func_addcostmgrplan(this.dialogConfig.addInputData, this.currentModelID, undefined);
                    break;
                case 1 :
                    _this.func_delcostmgrplan(this.currentPlan.data.id);
                    break;
                case 2 :
                    var currentcmp = _this.currentPlan.data;
                    _this.func_addcostmgrplan(this.dialogConfig.addInputData, this.currentModelID, currentcmp.id);
                    break;

            }
            
        },
        maskClick(){
            if(this.submenuShow){
                this.submenuShow = false
            }else{
                this.$emit('progressMaskClick')
            }
        },
        centerClick(){
            this.submenuShow = false
        },
        submenuShowClick(index,item) {
            
            this.submenuTop = 62 + 40*index - (this.$refs.entryUl.scrollTop)
        
            this.currentPlan.data = item;
            this.currentPlan.index = index;

            // 重命名时，将旧名称填写到输入框
            // ---------------------------
            this.dialogConfig.addInputData = item.label;

            // 显示重命名对话框
            // ---------------
            this.submenuShow = !this.submenuShow;
        },

        // 刷新数据（当前模型有哪些成本管理方案）
        // ----------------------------------
        reload(){

            this.m_listloading = true;

            //https://www.probim.cn:8080/api/CostMgrControllers/Cost/GetTreeData?organizeId=7134ca62-0e9b-4051-8136-312f8751becf&bimcomposerId=0fcfb8cc-175b-417a-927e-2cf0c1877c02
            this.projectid = this.$staticmethod._Get("bimcomposerId")
            // this.$axios.get(`${window.bim_config.webserverurl}/api/Plus/PlusProject/NewComm_GetList?bimcomposerId=${this.projectid}&modelId=${this.$props.currentModelID}`).then(res=>{
            //     this.m_costmgrList = res.data.Data
            // })
            var _this = this;
            var _organizeId = _this.$staticmethod._Get("organizeId");
            var _bimcomposerId = _this.$staticmethod._Get("bimcomposerId");
            var _url = `${window.bim_config.webserverurl}/api/CostMgrControllers/Cost/GetTreeData?organizeId=${_organizeId}&bimcomposerId=${_bimcomposerId}&token=${this.$staticmethod.Get("Token")}`;
            _this.$axios.get(_url).then(x => {
                this.m_listloading = false;
                if (x.data.Ret > 0) {
                    if (x.data.Data && x.data.Data.List) {
                        
                        // 遍历 phases 再遍历里面的 children, 找到相同的模型id
                        // -------------------------------------------------
                        var phases = x.data.Data.List;
                        var phase_;
                        var modelindex_;
                        for (var i = 0; i < phases.length; i++) {
                            if (phases[i].children && phases[i].children.length) {
                                var modelindex = phases[i].children.findIndex(y => y.id == this.currentModelID);
                                if (modelindex >= 0) {
                                    phase_ = phases[i];
                                    modelindex_ = modelindex;
                                    break;
                                }
                            }
                        }
                        if (phase_) {
                            var modelandcostmgrs = phase_.children[modelindex_];
                            if (!modelandcostmgrs) {
                                _this.$message.error('未找到指定模型的方案数据');
                            } else {
                                _this.m_costmgrList = modelandcostmgrs.children;
                            }
                        } else {
                            _this.$message.error('未找到指定模型的所属阶段');
                        }
                    }
                } else {
                    _this.$message.error(x.data.Msg);
                }
            }).catch(x => {
                this.m_listloading = false;
                console.error(x);
            });
        }
    },
    created() {

    },
    mounted() {
        // this.projectid = this.$staticmethod._Get("bimcomposerId")
        // this.$axios.get(`${window.bim_config.webserverurl}/api/Plus/PlusProject/NewComm_GetList?bimcomposerId=${this.projectid}&modelId=${this.$props.currentModelID}`).then(res=>{
        //     this.m_costmgrList = res.data.Data
        // })
        var _this = this;
        _this.reload();
    }
}
</script>
<style scoped>
._css-lefttopname {
    height: 50px;
    line-height: 50px;
    margin-left: 12px;
}
._css-back {
    margin-left:24px;
    cursor:pointer;
}
._css-large-head {
    height: 64px;
    display: flex;
    align-items: center;
}
._css-large-body {
    height:calc(100% - 64px);
    box-sizing: border-box;
    padding:0 24px 24px 24px;
}
._css-largepage-all {
    position: fixed;
    width:100%;
    height:100%;
    top:0;
    left:0;
    background-color: #fff;
    z-index:1001;
}
</style>
<style lang="stylus" scoped rel="stylesheet/stylus">
.no-data{
    img{
        width 120px
        height 115px
        margin-top 36px
    }
    p{
        margin-top 25px
        color rgba(0,0,0,.45)
        font-size 14px
        margin-bottom 46px
    }
}
.sub-menu {
    position: absolute;
    background: #fff;
    top: 62px;
    left: 301px;
    width: 180px;
    border-radius: 4px;
    padding: 4px 0;

    div {
        height: 40px;
        line-height: 40px;
        display flex
        flex-direction row
        align-items center
        padding 0 16px
        text-align left 
        color rgba(0,0,0,.85)
        i {
            margin-right 8px
        }

        &:hover {
            background: rgba(0, 0, 0, 0.04);
            cursor: pointer;
        }
    }
}

.entry-list {
    overflow-y: auto;
    padding: 16px 8px;
    color: rgba(0, 0, 0, 0.65);
    p{
        text-align left 
        font-size 14px
        color rgba(0,0,0,.85)
    }
    input {
        width calc(100% - 16px)
        height 40px
        line-height 40px
        padding-left 16px
        border-radius 4px
        border 1px solid rgba(0,0,0,.09)
        outline none 
        margin-top 5px
    }
    li {
        height: 40px;
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
        padding: 0 12px 0 8px;
        position: relative;
        cursor pointer

        span {
            flex: 1;
            text-align: left;
            margin: 0 12px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        i:last-child {
            cursor: pointer;
            opacity: 0.8;

            &:hover {
                opacity: 1;
            }
        }

        &:hover {
            background: rgba(0, 0, 0, 0.04);
        }
    }
}
</style>