<template>
    <div class="statistical-chart">
        <div class="cost-chart-content">
            <p class="subtitle">
                成本曲线
                <span class="css-hover-btn icon el-icon-close" @click="closeDialog"></span>
            </p>
            <p v-if="noDataShow" class="no-data-show">暂无数据</p>
            <v-chart ref="costChart" :options="polar"/>
        </div>
        <div class="chart-mask" @click="closeDialog"></div>
    </div>

</template>

<script>
export default {
    //成本曲线统计图
    name: 'CompsCostStatisticalChart',
    props: ['markLineData'],//markLineData 概算总费用
    data() {
        return {
            polar: {
                title: {
                    show: true,
                    text: '成本曲线',
                },
                tooltip: {
                    trigger: 'axis'
                },
                xAxis: {
                        type: 'category',
                        axisTick: {
                            alignWithLabel: true
                        },
                        // data: ["2014-07-14", "2014-07-15", "2014-07-16", "2014-07-17", "2014-07-18", "2014-07-19", "2014-07-20", "2014-07-21", "2014-07-22", "2014-07-23", "2014-07-24", "2014-07-25", "2014-07-26", "2014-07-27", "2014-07-28", "2014-07-29", "2014-07-30", "2014-07-31", "2014-08-01", "2014-08-02", "2014-08-03", "2014-08-05", "2014-08-06", "2014-08-07", "2014-08-08", "2014-08-09", "2014-08-10", "2014-08-11", "2014-08-12", "2014-08-13", "2014-08-14", "2014-08-15", "2014-08-16", "2014-08-17", "2014-08-18", "2014-08-19", "2014-08-20", "2014-08-21", "2014-08-22", "2014-08-23", "2014-08-24", "2014-08-25", "2014-08-26", "2014-08-27", "2014-08-28", "2014-08-29", "2014-08-30", "2014-08-31", "2014-09-01", "2014-09-03", "2014-09-04", "2014-09-05", "2014-09-06", "2014-09-07", "2014-09-08", "2014-09-09", "2014-09-10", "2014-09-11", "2014-09-12", "2014-09-13", "2014-09-14", "2014-09-15", "2014-09-16", "2014-09-17", "2014-09-18", "2014-09-19", "2014-09-20", "2014-09-21", "2014-09-22", "2014-09-23", "2014-09-24", "2014-09-25", "2014-09-26", "2014-09-27", "2014-09-28", "2014-09-29", "2014-09-30", "2014-10-01", "2014-10-02", "2014-10-03", "2014-10-04", "2014-10-05", "2014-10-06", "2014-10-07", "2014-10-08", "2014-10-09", "2014-10-10", "2014-10-11", "2014-10-14", "2014-10-15", "2014-10-16", "2014-10-17", "2014-10-18", "2014-10-19", "2014-10-20", "2014-10-21", "2014-10-22", "2014-10-23", "2014-10-24", "2014-10-25", "2014-10-26", "2014-10-27", "2014-10-28", "2014-10-29", "2014-10-30", "2014-10-31", "2014-11-01", "2014-11-03", "2014-11-04", "2014-11-05", "2014-11-07", "2014-11-08", "2014-11-09", "2014-11-10", "2014-11-11", "2014-11-13", "2014-11-14", "2014-11-15", "2014-11-16", "2014-11-17", "2014-11-18", "2014-11-19", "2014-11-23", "2014-11-24", "2014-11-25", "2014-11-26", "2014-11-27", "2014-11-28", "2014-11-29", "2014-12-01", "2014-12-02", "2014-12-03", "2014-12-05", "2014-12-06", "2014-12-07", "2014-12-08", "2014-12-09", "2014-12-10", "2014-12-11", "2014-12-13", "2014-12-14", "2014-12-15", "2014-12-17", "2014-12-19", "2014-12-22", "2014-12-23", "2014-12-25", "2014-12-26", "2014-12-27", "2014-12-28", "2014-12-29", "2014-12-30", "2015-01-01", "2015-01-02", "2015-01-03", "2015-01-04", "2015-01-05", "2015-01-06", "2015-01-07", "2015-01-08", "2015-01-09", "2015-01-10", "2015-01-11", "2015-01-12", "2015-01-13", "2015-01-14", "2015-01-15", "2015-01-16", "2015-01-17", "2015-01-18", "2015-01-19", "2015-01-20", "2015-01-22", "2015-01-23", "2015-01-24", "2015-01-25", "2015-01-26", "2015-01-28", "2015-01-29", "2015-01-31", "2015-02-01", "2015-02-02", "2015-02-03", "2015-02-05", "2015-02-06", "2015-02-09", "2015-02-10", "2015-02-11", "2015-02-12", "2015-02-13", "2015-02-14", "2015-02-15", "2015-02-16", "2015-02-18", "2015-02-19", "2015-02-20", "2015-02-21", "2015-02-22", "2015-02-23", "2015-02-24"]
                        data: []
                    },
                yAxis: {
                    type: 'value',
                    show: true,
                    splitLine: {
                        show: false
                    }
                },
                dataZoom: [{
                    type:"slider",//slider表示有滑动块的，
                    show:true,
                    xAxisIndex:[0],//表示x轴折叠
                }],
                series: [
                    {
                        name: '计划填报总费用',
                        data: [],
                        type: 'line',
                        smooth: true,
                        itemStyle: {
                            opacity: 0,
                            color: '#0087FF'
                        },
                    },

                    {
                        name: '实际填报总费用',
                        data: [],
                        type: 'line',
                        smooth: true,
                        itemStyle: {
                            opacity: 0,
                            color: '#00A044'
                        },

                        markLine: {
                            data: [
                                { name: '', xAxis: 0, yAxis: 680122646, symbol: 'circle'},
                                { name: '', xAxis: 0, yAxis: 680122646, symbol: 'circle' },

                            ],
                            label: {
                                normal: {
                                    show: true,
                                    position: 'end',
                                    formatter: '项目成本总概算',
                                    fontSize: '20px'
                                },
                            },
                            lineStyle: {
                                normal: {
                                    type: 'dashed',
                                    color: '#FF0000',
                                    width: 3,
                                },
                            },
                            silent: true
                        },
                    },
                ],


            },

            allDatas: [],
            noDataShow: false,
        }
    },

    mounted() {
        let _this = this;
        window.onresize = function() {
            _this.$refs.costChart.resize();
        }

        this.getGraphicData();
    },

    methods: {
        closeDialog() {
            this.$emit('close');
        },

        getGraphicData() {
            this.$axios.get(`${this.$configjson.webserverurl}/api/CostMgr2Controllers/CostMgrList/GetGraphicData`,{
                params: {
                    organizeId: this.$staticmethod._Get("organizeId"),
                    token: this.$staticmethod.Get("Token")
                }
            }).then(res=>{
                if (res.data.Ret == 1) {
                    this.allDatas = res.data.Data.List;
                    console.log(this.allDatas,'获取成本曲线图相关数据')
                    if (this.allDatas.length == 0) {
                        this.noDataShow = true;
                        return;
                    }
                    this.setStatisticalData();
                } else {
                    this.$message.error(res.data.Msg);
                }
            })
        },

        setStatisticalData() {
            //设置概算总费用基准线
            console.log(this.markLineData,'概算总费用基准线')
            this.$set(this.polar.series[1].markLine.data[0],'yAxis',this.markLineData);
            this.$set(this.polar.series[1].markLine.data[1],'yAxis',this.markLineData)

            this.allDatas.sort((a,b)=>{
                //按时间从下到大排序
                return new Date(a['record_datetimestr']).getTime() - new Date(b['record_datetimestr']).getTime();
            });

            let newArr=[];
            this.allDatas.forEach(item=>{
                let dataItem =item
                if(newArr.length>0){
                    let filterValue = newArr.filter(v=>{
                        return new Date(v.record_datetimestr).getTime() == new Date(dataItem.record_datetimestr).getTime()
                    })
                    if(filterValue.length>0){
                        newArr.forEach(n=>{
                            if( new Date(n.record_datetimestr).getTime() == new Date(filterValue[0].record_datetimestr).getTime()){
                                n.plan_prices =  parseInt(filterValue[0].plan_prices) + parseInt(dataItem.plan_prices);
                                n.real_prices =  parseInt(filterValue[0].real_prices) + parseInt(dataItem.real_prices);
                            }
                        })
                    }else{
                        newArr.push(dataItem)
                    }
                }else{
                    newArr.push(dataItem)
                }
            });
            // console.log(newArr,'筛选');

            let resultArr = [];
            // let total = 0;
            let planPart = 0;
            let realPart = 0;
            for(var i=0;i<newArr.length;i++)
            {
                planPart+=newArr[i].plan_prices;
                newArr[i].plan_prices = planPart;

                realPart+=newArr[i].real_prices;
                newArr[i].real_prices = realPart;
                // console.log(planPart)
            }

            // console.log(newArr,'结果')
            newArr.forEach((item,index,arr)=>{
                // this.polar.xAxis.data[index] = arr[index].record_datetimestr.split(' ')[0];
                // this.polar.series[0].data[index] = arr[index].TotalPrice;
                this.$set(this.polar.xAxis.data,index,arr[index].record_datetimestr.split(' ')[0]);
                this.$set(this.polar.series[0].data,index,arr[index].plan_prices);
                this.$set(this.polar.series[1].data,index,arr[index].real_prices);
            });

            //如果概算总费用比计划、实际总费用大，则设置y轴的max为概算总费用的值
            //防止概算总费用过大时，统计图上不会出现基准线
            if (this.markLineData > planPart && this.markLineData > realPart) {
                this.polar.yAxis.max = this.markLineData;
            }
            

            console.log(this.polar.xAxis.data)
            console.log(this.polar.series[0].data)

        }
    },

    destroyed(){
        window.onresize = null;
    }

}
</script>

<style>
    .cost-chart-content .echarts{
        width: 100%;
        height: calc(100% - 50px);
    }
</style>
<style scoped>
    .statistical-chart {
        position: fixed;
        top: 0;
        right: 0;
        bottom: 0;
        left: 0;
        z-index: 1000;
    }

    .cost-chart-content {
        width: 80%;
        height: calc(100% - 100px);
        position: fixed;
        left: 10%;
        top: 50px;
        z-index: 10;
        background-color: #FFFFFF;
        z-index: 2;
    }

    .cost-chart-content .no-data-show {
        position: absolute;
        width: 100%;
        top: 20%;
        text-align: center;
    }

    .subtitle {
        position: relative;
        line-height: 45px;
        text-align: left;
        font-size: 16px;
        padding: 0 20px;
    }

    .subtitle .icon {
        position: absolute;
        right: 10px;
        top: 10px;
    }

    .chart-mask {
        width: 100%;
        height: 100%;
        position: absolute;
        left: 0;
        top: 0;
        z-index: 1;
        background-color: rgba(0,0,0,0.45);
    }
</style>
