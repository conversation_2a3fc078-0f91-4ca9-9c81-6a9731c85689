<template>
    <div :class="headerHide?'_css-model-progress':'_css-model-progress-big'" >
        <div class="_css-time-line"  v-if="isProgress"></div>
        
        <modelPlayTimerControl
            v-if="!isProgress"
            ref="refmodelPlayTimerControl"
            :sliderReturnValue='sliderReturnValue'
            :loadingChange1="loadingChange1"
            @dropdownChange="dropdownChange"
        >
            <div class="_css-time-detail">
                <div 
                    class="_css-div-btn playmodel"
                    @click="backTheTop">
                    <i class="icon-arrow-left_outline"></i>
                    退出模拟
                </div>
            </div>
        <!-- @playstart='mockprogressclick'
            @playpause='pausemodelClick'
            @playstop='stopPlayModel' -->
            <!-- <p v-if="smallWidth">{{sliderMinTime | flt_datetimefromList}}</p>
            <div class="_css-el-slider">
                <el-slider
                    v-model="timeSlidervalue" 
                    :format-tooltip="timestepToolTip"
                    @change="sliderChange"
                    >
                </el-slider>
            </div>
            <p v-if="smallWidth">{{sliderMaxTime | flt_datetimefromList}}</p> -->
        </modelPlayTimerControl>
        <div class="model-iframe">
            <!-- :src="modelIframeSrc"  -->
            <iframe
                v-if="headerHide"
                width="100%" 
                height="100%" 
                ref="modelRefTime" 
                @load="imageProLoad" 
                :src="`${getBimConfig().bimviewerurl}?projectId=${projectID}&model=${changemoreModelIds}`"
                frameborder="0"></iframe>
        </div>
        <div v-show="!isProgress">
            <modelPlayLeftStateDetail
                :isProgress="isProgress"
                :headerHide="headerHide"
                :listSetColorLength='costlistSetColor1.length' 
                :changeTime="changeTime"
                @loadingChange='loadingChange'
                ref="listProgress">
                <div class="list-progress">
                    <div v-for="(item,index) in costlistSetColor1" :key="index">
                        <p>工程结构：{{item.bm_materialname}}</p>
                        <p>项目编码：{{item.bm_materialcode}}</p>
                        <p>计量标准：{{item.cmm_measurestandard}}</p>
                        <p>工程单位：{{item.cmm_engunit}}</p>
                        <p :class="dropdownVal? 'cD0FF2E': ''" v-show='dropdownVal'>预算工程量：{{item.cmm_EstEngQua}}</p>
                        <p :class="!dropdownVal? 'cD0FF2E': ''" v-show='!dropdownVal'>计划完成工程量：{{item.cmr_PlanEngQua | thousandBit}}</p>
                        <p class="c60FFFF">实际完成工程量：{{item.cmr_RealEngQua | thousandBit}}</p>
                        <p :class="dropdownVal? 'cD0FF2E': ''" v-show='dropdownVal'>预算合价：{{item.cmm_EstGroupPrice | thousandBit}}</p>
                        <p>填报合价：{{item.cmm_RealGroupPrice | thousandBit}}</p>
                        <p :class="!dropdownVal? 'cD0FF2E': ''" v-show='!dropdownVal'>计划填报费用：{{item.TotalPlanPrice | thousandBit}}</p>
                        <p class="c60FFFF">实际填报费用：{{item.TotalRealPrice | thousandBit}}</p>
                        <p :class="dropdownVal? 'cD0FF2E': ''" v-show='dropdownVal'>预算总费用：{{item.EstTotalPrice | thousandBit}}</p>
                        <!-- <p :class="!dropdownVal? 'cD0FF2E': ''">计划填报总费用：{{item.Sum_TotalPlanPrice | thousandBit}}</p> -->
                        <p :class="!dropdownVal? 'cD0FF2E': ''" v-show='!dropdownVal'>当前累加计划填报费用：{{item.Time_SumTotalPlanPrice | thousandBit}}</p>
                        <p class="c60FFFF">当前累加实际填报费用：{{item.Time_SumTotalRealPrice | thousandBit}}</p>
                        <!-- <p class="c60FFFF">实际填报总费用：{{item.Sum_TotalRealPrice | thousandBit}}</p> -->
                        <p :class="item.compare_budget=='normal'? 'c27FF7D' : 'cFFAD9E'" v-show="dropdownVal">状态：{{item.compare_budget | flt_compare_plan}}</p>
                        <p :class="item.compare_plan=='normal'? 'c27FF7D' : 'cFFAD9E'" v-show="!dropdownVal">状态：{{item.compare_plan | flt_compare_plan}}</p>
                        <el-divider v-if="index<costlistSetColor1.length-1"></el-divider>
                    </div>
                </div>
                <div class="cost-legend">
                    <p>超支</p>
                    <p>正常</p>
                </div>
            </modelPlayLeftStateDetail>
        </div>

    </div>
</template>
<script> 
import Vue from "vue"
let vue = Vue
import { EventBus } from "@static/event.js";
import modelPlayLeftStateDetail from '../CompsModelPlay/modelPlayLeftStateDetail'
import modelPlayTimerControl from '../CompsModelPlay/modelPlayTimerControl'
export default {
    name: 'modelPlaySimulationShow',
    data() {
        return { 
            sliderReturnValue: null,
            smallWidth: true,  // 判断屏幕宽度小于900  不显示时间
            timeSlidervalue:  0,
            // marks: {},
            dateMin: '',
            diffStep: 1,
            changeTime: null,
            timeResNull: false, 
            isProgress: true,
            dataDetailVisible:false,
            alllistSetColor: [],
            listSetColor: [],
            legendColor: [
                {color: 'rgba(111,111,111,.5)', num: '0%'},
                {color: 'rgba(255,255,0,.5)', num: '1%-50%'}, 
                {color: 'rgba(255,0,0,.5)', num: '51%-100%'}, 
                {color: 'rgba(30,255,0,.5)', num: '100%'},
            ],
            elementArr: [], // 着色的构件和颜色

            playsign: 'end', // 当前的模型模拟状态 
            bStartDis: false, // 刚点击完开始模拟时，立即dis掉，当 playSign 修改为 start 时，立即取消dis.
            loadingModelEnd: true,
            playTimeHandler: 0,  
            setValue: 0,  // 模拟时候记录时间值
            timeAutoData: 1000,   // 播放秒数
            playingBtn: true,
            sliderMinTime: '',  // 播放的最小时间
            sliderMaxTime: '',  // 播放的最大时间
            sliderTimeDiff: 0,
            revalue: 0,
            allPro_ModelID: [],  // 该值为allPro_ModelID的存值，大屏项目时候该值为全部的allPro_ModelID，平台形象进度进入时候为传入的值
            Pro_bmtypeId: [],  // 该值为Pro_bmtypeId的存值，大屏项目时候该值为全部的Pro_bmtypeId，平台形象进度进入时候为传入的值
            Pro_ToleranceValues: [],  // 该值为Pro_ToleranceValues的存值，大屏项目时候该值为全部的Pro_ToleranceValues，平台形象进度进入时候为传入的值
            headerHide: true, // 大屏显示使用该组件，header不展示 
            modelInputEdit: false, // 编辑描述输入框状态
            playTime: null,  // 输入框的时间秒数
            ToleranceValues:0 ,
            dropdownVal: true,
            screenWidth: 0,
            modelAllList: [],
            organizeIdtxt: null,
            allChangeList: [],  // 以bm_guid为key的键值对
            havemodelList: [],
            costlistSetColor:[],
            costlistSetColor1: [],
            allsetcolor:[],
            iframeModelEle: [], // 模型的所有构件 
            changemoreModelIds: [],  // 多模型加载使用|分割
            projectID: '',
            allGetColor: [], // 已经着色的构件ele

            // ==============
            modelEles_Axios: [],  // 存放所有关于模型返回的参数，请求一次接口保存下来的值
            State_Ele_Color: [],  // 存放数组包括所有时间的模型构件和状态，模型构件为着色构件，状态为着色构件颜色
            timeListData: [],     // timeList 接口返回的所有关于的日期和状态的数据
            loadingChange1: true,

        };
    },
    props: {
        modelid: {
            type: String,
            required: false
        },
        bmtypeId: {
            type: String,
            required: false
        },
    },
    filters: {
        flt_datetimefromserver(str) {
            var _this = this;
            var str1 = str;
            if (str) {
                str1 = str.replace("T", " ");
            }
            return str1;
        },
        flt_datetimefromList(str) {
            var _this = this;
            var str1 = str;
            if (str) {
                str1 = str.substr(0,4) + '年' + str.substr(5,2) + '月' + str.substr(8,2) + '日'
            }
            return str1;
        }, 
        thousandBit(str){ 
            str = str + '';
            var re = /(?=(?!\b)(\d{3})+$)/g; 
            return str.replace(re, ','); 
        },
        flt_compare_plan(str){ 
            let n_str = ''
            if(str == "normal"){
                n_str = '正常';
            }else{
                n_str = "超支";
            }
            return n_str;
        },

    },
    components: {
        modelPlayLeftStateDetail,
        modelPlayTimerControl
    },
    watch:{
        costlistSetColor(val){
            this.costlistSetColor1 = val;
        }
    }, 
    mounted() {
        let _this = this;
        _this.organizeIdtxt = _this.$staticmethod._Get("organizeId");
        _this.projectID = _this.$staticmethod._Get("bimcomposerId");
       
        if(_this.$route.query.title == '成本模拟'){
            // 预留大屏项目接入的判断
            _this.headerHide = false;
        }else{
            _this.headerHide = true;
            _this.$nextTick(()=>{
                _this.allPro_ModelID = _this.allModelID;
                _this.Pro_bmtypeId = _this.bmtypeId;
                _this.Pro_ToleranceValues = _this.ToleranceValues;
                // _this.getTailTimes();
            })
        }
        _this.GetCostRelModelEles_Simulate();
    },
    methods: {
        // 接口返回的所有数据
        Simulate_GetImportedAndAddedDatas(){
            let _this = this;
            _this.$axios
                .get(
                `${window.bim_config.webserverurl}/api/CostMgr2Controllers/CostMgrList/Simulate_GetImportedAndAddedDatas?organizeId=${this.organizeIdtxt}&Token=${this.$staticmethod.Get('Token')}`
                )
                .then(x => {
                if (x.status == 200) {
                    if (x.data.Ret > 0) {
                        if (x.data.Data) {
                            let dataAll = x.data.Data;
                            let timearr = [];  // 存放时间
                            let guid_key= [];  // guid作为key的键值对
                            let dataList = x.data.Data.addeddatas;       // 计算的数据
                            let importedDatas = x.data.Data.importdatas; // 填报的固定数据
                            let timeList = x.data.Data.timesplitdatas;   // 状态值
                            _this.timeListData = timeList;

                            dataList.forEach((item)=>{
                                timearr.push(item.cmr_RecordDateTime.substr(0,10));
                                guid_key[item.bm_guid] = item;
                            })
                            _this.sliderReturnValue = timearr;  // 时间轴 

                            // ================
                           
                            let imported_bm_guid_arr = [];  // 通过bm_guid在importedDatas中查找出来的两个值，值是列表中不变的数据,方便两个数据合并
                            for(let i=0;i<importedDatas.length;i++){
                                if(guid_key[importedDatas[i].bm_guid]){
                                    imported_bm_guid_arr.push(importedDatas[i]);
                                }
                            }
                            // console.log(imported_bm_guid_arr,'======当前位置是合并了填报的，不变的imported_bm_guid_arr')

                            let allColorList = [];  // 把两个接口拿到的所有数据通过bm_guid关联 用Object.assign方法合并到一个对象中
                            for(let j=0;j<dataList.length;j++){
                                for(let i_m=0; i_m<imported_bm_guid_arr.length; i_m++){
                                    if(dataList[j].bm_guid == imported_bm_guid_arr[i_m].bm_guid){
                                        let returnedTarget = Object.assign({},imported_bm_guid_arr[i_m],dataList[j]);
                                        
                                        allColorList.push(returnedTarget);
                                    }
                                }
                            }
                            // console.log(allColorList,'======当前位置是合并了填报的，不变的allColorList');
                            // =========================
                            let modelcolorList = [];// 通过bm_guid过滤后的相关构件信息
                            for(let i = 0; i < timeList.length; i++){
                                for(let k = 0; k < dataList.length; k++){
                                    if(
                                        timeList[i].bm_guid == dataList[k].bm_guid &&
                                        timeList[i].DateTimeEndPoint == dataList[k].cmr_RecordDateTime
                                    ){
                                        modelcolorList.push(timeList[i]);
                                    }
                                }
                            }
                            // console.log(modelcolorList,'=====modelcolorList')

                            let modelallColorList = []; // 把elementid和modelid添加到list中
                            let time_key_arr = [];   // 时间为key的键值对
                            for(let n = 0;n < allColorList.length; n++){
                                for(let m=0; m < modelcolorList.length; m++){
                                    if(allColorList[n].bm_guid == modelcolorList[m].bm_guid  && allColorList[n].cmr_RecordDateTime == modelcolorList[m].DateTimeEndPoint){
                                        let returnedTarget1 = Object.assign({},modelcolorList[m],allColorList[n]);
                                        modelallColorList.push(returnedTarget1);
                                    }
                                }
                            }
                            // console.log(modelallColorList,'======添加了ele和状态modelallColorList')
                            _this.havemodelList = modelallColorList;   // 左边列表用到的数据
                        }
                    }
                }
                })
                .catch(x => {});
        },
        // 请求模型model
        GetCostRelModelEles_Simulate(){
            let _this = this;
            _this.$axios
                .get(
                `${window.bim_config.webserverurl}/api/CostMgr2Controllers/CostMgrList/GetCostRelModelEles_Simulate?organizeId=${this.organizeIdtxt}&Token=${this.$staticmethod.Get('Token')}`
                )
                .then(x => {
                    if (x.status == 200) {
                        if (x.data.Ret > 0) {
                            if (x.data.Data) {
                                let modelList = x.data.Data;
                                _this.modelEles_Axios = modelList;
                                
                                let allModelid = [];
                                for(let i = 0; i < modelList.length; i++){
                                    allModelid.push(modelList[i].modelid);
                                }

                                allModelid = [...new Set(allModelid)];
                                
                                // 模型model 过模型通过|拼接
                                let str = '';
                                for(var i = 0; i < allModelid.length-1; i++){
                                    str+=allModelid[i]+"|";
                                }
                                _this.changemoreModelIds = str + allModelid[allModelid.length-1];
                            }
                            else{
                                _this.$message.error('当前列表暂无模型')
                                return;
                            }
                        }
                    }else{
                        console.log(x)
                    }
                })
                .catch(x => {});
        },
        getListTimePoint(time,value){
            let _this = this;
            // 根据时间得到左边list的数组
            let modelRefTime = null;
            if(_this.headerHide){
                modelRefTime = _this.$refs.modelRefTime.contentWindow;
            }else{
                modelRefTime = window;
            }
            let colorUtility = _this.$staticmethod.bimhelper_getColorUtility(modelRefTime); 
            let resetcolor = _this.allGetColor.flat(Infinity);
            // 将所有的颜色再次重置为黑色
            //colorUtility.resetElementColor(resetcolor)
            colorUtility.setElementColor(resetcolor,0,0,0,.02,false,false)

            _this.changeTime = time;
            let findTime = []; // 当前时间下的构件对象 
            _this.havemodelList.forEach((item)=>{
                if(item.cmr_RecordDateTime.substr(0,10) == time){
                    findTime.push(item);
                }
            })
            // console.log(findTime,'=================findTimefindTime');
            
            let findEleColor = [];  //  当前时间下所有的构件和状态
            _this.timeListData.forEach((item)=>{
                if(item.DateTimeEndPoint.substr(0,10) == time){
                    findEleColor.push(item);
                }
            })
            // console.log(findEleColor,'======findEleColor');

            let stateListArr = [];  // 存放关于状态、构件、日期，在模拟时候每天在这里查找当天的构件和状态，然后着色
             
            for(let timek = 0; timek < _this.modelEles_Axios.length; timek++){
                for(let timei = 0; timei < findEleColor.length; timei++){
                    if(
                        _this.modelEles_Axios[timek].bm_guid == findEleColor[timei].bm_guid
                    ){ 
                        let returnedTargetEleList = Object.assign({},_this.modelEles_Axios[timek],findEleColor[timei]);
                        stateListArr.push(returnedTargetEleList); 
                    }
                    
                }
            }
            this.costlistSetColor = findTime;

            let element_AllArr = [];
            // 只获取当前播放的构件   是循环findTime
            stateListArr.forEach(element => {
                let _col = '';
                if(_this.dropdownVal){
                    if(element.compare_budget == "overspend"){
                        _col = 0;   // 0红色
                    }else{
                        _col = 1;   // 1绿色
                    }
                }else{
                    if(element.compare_plan == "overspend"){
                        _col = 0;   // 0红色
                    }else{
                        _col = 1;   // 1绿色
                    }
                }
                element_AllArr.push({ele:element.eleid,color:_col,modelid: element.modelid});
            });
            element_AllArr = [...new Set(element_AllArr)];
            let arrReset = _this.getElement(element_AllArr);  //  所有的构件
                
            _this.$nextTick(()=>{
                _this.$refs.listProgress.scrollTop = 0;
            })
            _this.setColorBIM1(modelRefTime,arrReset);

        },
        setColorBIM1(thewin) {
            var _this = this; 
            var colorUtility = _this.$staticmethod.bimhelper_getColorUtility(thewin); 
            // 着色是根据不同的完成百分比，来确定当前构件的颜色，将构件循环着色
            
            _this.allGetColor = [];
            _this.elementArr.forEach(res=>{
                let r,g,b,a;
                let num = res.color;
                if(res.ele && res.ele.length>0){
                    if(num==0){
                        r = 254;
                        g=61;
                        b=106;
                        a=0.4;
                    }else{
                        r = 0;
                        g = 146;
                        b = 94,
                        a = 0.3
                    }
                    _this.allGetColor.push(res.ele);
                    colorUtility.setElementColor(res.ele,r,g,b,a,false,false)
                }

            })
        },
        // 添加千位符
        thousandBit(str){ 
            str = str + '';
            var re = /(?=(?!\b)(\d{3})+$)/g; 
            return str.replace(re, ','); 
        },
        backTheTop() {
            this.$emit('close');
        },
        getBimConfig() {
            var _this = this
            return window.bim_config
        },
        dropdownChange(val){
            var _this = this;
            if(val == 'budget'){
                _this.dropdownVal = true;
            }else{
                _this.dropdownVal = false;
            }
        },
        DateDiff(sDate1, sDate2) {
            var aDate, oDate1, oDate2, iDays;
            oDate1 = new Date(sDate1).getTime();
            oDate2 = new Date(sDate2).getTime();
            iDays = parseInt((oDate1 - oDate2) / 1000 / 60 / 60 / 24) //把相差的毫秒数转换为天数   
            return  iDays;
        },
        
        // iframe 加载完毕后
        imageProLoad() {
            let _this  = this;
            let iframeWindow = _this.$refs.modelRefTime.contentWindow;
            
            iframeWindow.model.BIM365API.Events.finishRender.on('default',()=>{
                iframeWindow.model.BIM365API.Options.isShowTexture(false);  // 关闭模型材质显示
                _this.iframeModelEle = iframeWindow.model.BIM365API.Selector.getAllElementOrSpaceID('element')
                iframeWindow.model.BIM365API.Controller.setElementColor(_this.iframeModelEle,0,0,0,.02,false,false);
                _this.$nextTick(()=>{
                    _this.isProgress = false;
                    _this.Simulate_GetImportedAndAddedDatas();
                })
            })
        },
        getElement(listElements) {
            let arr = [];
            let _ele_=[];
            listElements.forEach(item => {
                let item_ele = [];
                item.ele.forEach(ids => {
                    arr.push(`${item.modelid}^${ids}`);
                    item_ele.push(`${item.modelid}^${ids}`);
                })
                _ele_.push({ele:item_ele,color:item.color})
            })
            this.elementArr = _ele_;
            return arr;
        },
        loadingChange(val){
            this.loadingChange1 = val;
        }
    },
    beforeDestroy() {
        clearInterval(this.playTimeHandler);
    },
    destroyed() {
        clearInterval(this.playTimeHandler);
    }
}
</script>
<style scoped>
._css-time-line{
    pointer-events: all;
    height: 45px;
    padding: 15px 20px 0;
    background: transparent;
    border-radius: 4px;
}
._css-model-progress{
    width: 100%;
    height: 100%;
    position: fixed;
    z-index: 1000;
    top:0;
    left: 0;
    /* background: #fff; */
    background: #f7f7f7;
}
._css-model-progress-big{
    position: absolute;
    z-index: 1000;
    top:20px;
    left: 20px;
    right: 20px;
    bottom: 20px;
    pointer-events: none;
}
.model-iframe{
    width: 100%;
    height: calc(100% - 100px);
}
._css-el-slider{
    flex:1;
}
._css-el-slider /deep/ .el-slider__button{
    width: 4px;
    height: 16px;
    border: none;
    background: #007AFF;
    border-radius: 0;
}
._css-el-slider /deep/ .el-slider__runway{
    border: 12px solid #ECEEF0;
    margin:0;
    width:98%;
    background: #A6AEB6;
}
</style>
<style lang="stylus" rel="stylesheet/stylus">
.cost-legend{
    p{
        width: 64px;
        height: 28px;
        line-height: 28px;
        background: #F41515;
        border-radius: 4px;
        opacity: 0.5;
        text-align:center;
        color:#fff;
        margin-top:10px;
    }
    p:last-child{
        background: #008D7F;
    }
}
</style>