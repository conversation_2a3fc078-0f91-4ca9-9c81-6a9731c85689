<template>
    <div></div>
</template>

<script>
import { EventBus } from "@static/event.js";
import Vue from "vue";
let vue = Vue;
export default {
    name: '',
    data() {
        return {
            countPerRequest: null,//总的构件数
            webperCount: 10000,//每次请求需要拿多少条数据（根据页面响应情况 自定义）
            totalCount: null,//需请求多少次（ 通过this.getTotalCount()计算得出 ）
            currentCount: 0,//当前是第几次请求,从0开始，小于 totalCount
        }
    },

    mounted() {
        this.getCostMgrModelIds();

        // console.log('mountedmounted','---------')
        EventBus.$once("modelMounted", (model) => {
            console.log(model,'监听modelMounted');
            // this.fullScreenModel = model.model;
            vue.prototype.$model = model.model;
            window.model =  model.model;
            //如果没有加载完毕  则需监听加载事件
            if (!model.isFinishRender) {
                this.fullScreenHandleModel();
            }

            //如果加载完毕则开始获取构件id
            if (model.isFinishRender) {
                // //先对所有构件进行隔离
                // this.$model.Controller.isolateElementByElementId();
                this.getCostMgrPageData();
            }
        });

    },

    methods: {

        //获取所有的模型id
        getCostMgrModelIds() {
            this.$axios.get(`${this.$configjson.webserverurl}/api/CostMgr2Controllers/CostMgrList/GetCostMgrModelIds`,{
                params: {
                    organizeId: this.$staticmethod._Get("organizeId"),
                    token: this.$staticmethod.Get("Token")
                }
            }).then(res=>{
                // console.log(res,'获取所有的模型id');
                //构件总数
                this.countPerRequest = res.data.Data.CountPerRequest;
                this.totalCount = this.getTotalCount();

                let model_ids = [];
                if (res.data.Data.List.length > 0) {
                    model_ids = res.data.Data.List;
                }

                if (model_ids.length == 1) {
                    EventBus.$emit('setModel',model_ids[0]);
                } else {
                    EventBus.$emit('setModel',model_ids);
                }
                console.log(model_ids,'发送 setModel')
            })
        },

        //分批获取构件id 和 模型id
        getCostMgrPageData() {
            //当前请求的次数 小于 总请求次数 才可继续请求获取构件
            if (this.currentCount < this.totalCount) {
                this.$axios.get(`${this.$configjson.webserverurl}/api/CostMgr2Controllers/CostMgrList/GetCostMgrPageData`,{
                    params: {
                        organizeId: this.$staticmethod._Get("organizeId"),
                        hadRequestCountTimes: this.currentCount,//第几次取数据
                        // getCount: this.webperCount,//每次取几条数据
                        getCount: this.countPerRequest,//每次取几条数据
                        token: this.$staticmethod.Get("Token")
                    }
                }).then(res=>{
                    // console.log(this.currentCount,'第几次取数据');
                    if (res.data.Ret == 1) {
                        // this.currentCount += 1;
                        this.modelDataHandl(res.data.Data.List);
                    }
                });
            }
        },

        //模型数据处理
        modelDataHandl(datas) {
            let modelComponentStateArray = [[],[]];
            let allElementsId= [];
            if (datas.length > 0) {
                //遍历筛选
                datas.forEach(element=>{
                    let ids = `${element.modelid}^${element.elementid}`
                    //所有模型id^构件id  隔离使用
                    allElementsId.push(ids);

                    //按状态把构件分类，state的值大于0的是超支，其他的则为正常
                    if (element.state == 1) {
                        //下标为1的所有元素 状态为“超支”，需着红色
                        modelComponentStateArray[1].push(ids);
                    } 
                    if (element.state == null) {
                        //下标为0的所有元素 状态为“正常”，需着绿色
                        modelComponentStateArray[0].push(ids);
                    }
                });

                //先对即将着色的构件进行隔离
                this.$model.Controller.isolateElementByElementId(allElementsId);

                //按类别甄选完毕，开始着色
                this.setFullScreenAllElementColor(modelComponentStateArray);
            }

        },

        //大屏模型所有构件着色
        setFullScreenAllElementColor(classifying) {
            let elementColor = [
                {r: 103,g: 194,b: 58,a: 1},//绿色
                {r: 204,g: 0,b: 0,a: 1}//红色
            ];
            // console.log(classifying,'即将着色的构建集合')
            for (let j = 0; j < classifying.length; j++) {
                if (classifying[j].length > 0) {
                    //进行着色
                    this.$model.Controller.setElementColor(classifying[j],elementColor[j].r,elementColor[j].g,elementColor[j].b,elementColor[j].a)
                }
            }
            // this.getCostMgrPageData();
        },

        //计算请求次数
        getTotalCount() {
            if (this.countPerRequest % this.webperCount == 0) {
                return this.countPerRequest / this.webperCount;
            } else {
                let _t = Math.trunc(this.countPerRequest / this.webperCount) + 1;
                return _t;
            }
        },

        showModel() {


        },

        //大屏模型相关处理
        fullScreenHandleModel() {
          this.$model.Events.finishRender.once('default',(val)=>{
            console.log('当前模型加载并渲染完成！');
            // //先对所有构件进行隔离
            // this.$model.Controller.isolateElementByElementId();
            this.getCostMgrPageData();
          });
        },

    },

    computed: {
    },

    watch: {
    //     'relationModelItems': {
    // 　　　　handler(n,o) {
    //             if (n.RelationModelItems.length > 0 || o.RelationModelItems.length > 0) {
    //                 this.modelDataHandl();
    //             }
    //         },

    //         deep: true
    //     }
    },

    beforeDestroy() {
        // this.handleModel.BIM365API.Context.dispose();
        // this.handleModel = null
    }
}
</script>

<style scoped>
    .cost-model-window {
        flex: 1;
        width: 100%;
        position: relative;
    }

    #costModelWindow {
        width: 100%;
        height: 100%;
        position: relative;
    }

    .close-model {
        position: absolute;
        z-index: 10;
        top: 0;
        right: 0;
        font-size: 23px;
        cursor: pointer;
    }

    .close-model:hover {
        color: #1890ff;
    }
</style>
