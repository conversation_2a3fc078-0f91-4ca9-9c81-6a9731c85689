<template>
    <div class="cost-model-window" v-if="!fromFullScreen">
        <!-- <div id="costModelWindow" v-loading="modelLoad"> -->
        <div id="costModelWindow">
            <!-- 通过load（）方法加载模型 需注释下方iframe -->
            <!-- <iframe 
                ref="costModelIframe"
                @load="iframeWpload" 
                :src="bimViewerUrl" 
                frameborder="0"></iframe> -->
            <modelNewIframeLoading
                v-if="featureID.length > 0"
                :VaultID='VaultID'
                :featureID='featureID'
            ></modelNewIframeLoading>
        </div>
        <div class="close-model" @click="closeModel">
            <span class="el-icon-close"></span>
        </div>
        <div class="no-model-datas" v-if="noModelDatas">暂无模型数据</div>
    </div>
</template>

<script>
import modelNewIframeLoading from '@/components/Home/ProjectBoot/modelNewIframeLoading'
import { EventBus } from "@static/event.js";
import { mapGetters } from 'vuex' 
import Vue from "vue"
let vue = Vue
export default {
    name: '',
    props: ['relationModelItems','fromFullScreen'],
    data() {
        return {
            projectID: '',
            modelidArray: [],//模型id的集合
            modelComponentArray: [],//筛选后的模型和构件的集合（数组每个元素为：模型id和该id下所有的构件id集合  ）
            modelComponentStateArray:[[],[]],//按状态把构件分类;: null,//模型对象句柄
            fullScreenModel: null,//大屏相关的模型对象
            noModelDatas: true,//是否有模型数据 true没有  false有
            countPerRequest: null,//构件总数
            allModelAndElements: [],//所有模型id和构件id的数据的集合
            modelLoad: true,
            zoomElementids: [],//需要聚焦的构件集

            // bimViewerUrl: '',//通过iframe加载模型时，需要该变量
            VaultID: '',
            featureID: '',
        }
    },
    watch: {
        getModelLoad(data){
            // 使用vuex==设置模型加载状态
            if(data.state == "end"){
                this.modelloadingfun();
            }
        }
    },
    computed: {
        ...mapGetters([
            'getModelLoad'
        ]),
    },
    mounted() {
        this.projectID = this.$staticmethod._Get("bimcomposerId");
        //如果不是大屏 是从主平台访问的 走这里。 如果是大屏  走下方watch
        if (!this.fromFullScreen) {
            this.getCostMgrModelIds();
        }

    },
    components: {
        modelNewIframeLoading,
    },

    methods: {
        // 新模型=全部隔离
        newModelIsolate(boolean){
            // true为全部隔离，false为全部取消隔离
            let eles = scene.findFeature(this.featureID).objects
            eles.forEach(i=>{i.isolate = boolean})
        },

        // 新模型=着色
        newModelsetElementColor(eles,r,g,b,a){
            this.newModelresetElement(eles)
            window.scene.execute('color',{objectIDs:eles,color:`rgb(${r},${g},${b})`,opacity:`${a}`})
        },
        // 新模型=删除着色
        newModelresetElement(r_eles){
            for(let i = 0; i < r_eles.length; i++ ){
                let element = window.scene.findObject(r_eles[i]);
                element.resetColor();
                window.scene.render();
            }
        },
        // 新模型=高亮
        newModelselectorElementByElementId(eles){
            let elements = scene.selectedObjects;
            // 判断集合是否为空
            if (elements.size > 0) {
                let elementIds = Array.from(elements.keys())

                for(let i = 0; i < elementIds.length; i++ ){
                    let element = window.scene.findObject(elementIds[i])
                    element.selected = false
                    window.scene.render();                 
                }
            }
            for(let i = 0; i < eles.length; i++ ){
                let element = window.scene.findObject(eles[i])
                element.selected = true
                window.scene.render();                 
            } 
            let elements_zoom = window.scene.selectedObjects;
            window.scene.fit(Array.from(elements_zoom.keys()));
        },
        closeModel() {
            this.$emit('close')
        },

        //模型数据处理（旧方法）
        modelDataHandl_old() {
            if (this.relationModelItems.RelationModelItems && this.relationModelItems.RelationModelItems.length > 0) {
                let storages = [];
                //遍历取出所有存在的modelid
                this.relationModelItems.RelationModelItems.forEach(element=>{
                    storages.push(element.modelid)
                    //按状态把构件分类
                    if (element.state > 0) {
                        //下标为1的所有元素 状态为“超支”，着红色
                        this.modelComponentStateArray[1].push(element);
                    } else {
                        //下标为0的所有元素 状态为“正常”，着绿色
                        this.modelComponentStateArray[0].push(element);
                    }
                });
                //modelid可能存在重复，所以去重
                this.modelidArray = Array.from(new Set(storages));
                // console.log(this.modelidArray,'模型id的集合')

                //取出所有模型所对应的构件id，放到与modelid对应的元素下
                this.modelidArray.forEach(item=>{
                    this.modelComponentArray.push({modelid:item,elementids:[]});
                });

                this.modelComponentArray.forEach(item=>{
                    this.relationModelItems.RelationModelItems.forEach(element=>{
                        if (item.modelid == element.modelid) {
                            item.elementids.push.apply(item.elementids,element.elementids);
                        }
                    })
                });

                // console.log(this.modelComponentArray,'筛选后的模型id和构件id');

                this.showModel();
            } else {
                this.noModelDatas = true;
            }

        },

        getCostMgrModelIds() {
             //获取该成本关联的所有的模型id
            this.$axios.get(`${this.$configjson.webserverurl}/api/CostMgr2Controllers/CostMgrList/GetCostMgrModelIds`,{
                params: {
                    organizeId: this.$staticmethod._Get("organizeId"),
                    token: this.$staticmethod.Get("Token")
                }
            }).then(res=>{
                // console.log(res,'获取所有的模型id');
                //构件总数
                this.countPerRequest = res.data.Data.CountPerRequest;
                // this.totalCount = this.getTotalCount();

                if (res.data.Data.List.length > 0) {
                    this.modelidArray = res.data.Data.List;
                    this.noModelDatas = false;
                } else {
                    this.noModelDatas = true;
                }
                this.showModel();
            })
        },

        modelloadingfun(){
            this.modelLoad = false;
        },
        //load方法 加载模型
        // showModel_old_old() {
        //     let model_ids = JSON.parse(JSON.stringify(this.modelidArray))
        //     let dom = document.getElementById('costModelWindow')
        //     let obj = {
        //         // modelID: ['1a5ad378-f6ec-4b21-934e-bc74ca6872f5','e1f9e668-9b42-4ccc-afc0-745ea4501a88'],
        //         modelID:  model_ids,
        //         projectID: this.projectID,
        //         versionNO: '',
        //         viewID: '',
        //         DOM:  dom//this.$refs.playModelView
        //     }
        //     // console.log(obj);

        //     let model = new bim365.BIMModel(obj);
        //     model.load();
        //     vue.prototype.$model = model.BIM365API
        //     model.BIM365API.Events.finishRender.on('default',(val)=>{
        //         console.log('当前模型加载并渲染完成！');
        //         // this.setAllElementColor();
        //         this.modelLoad = false;
        //         this.getCostMgrPageData();
        //         model.BIM365API.Context.onCanvasResize();
        //     });

        // },

        //iframe方法加载模型
        showModel() {
            if (this.noModelDatas) {
                return;
            }

            let model_ids = JSON.parse(JSON.stringify(this.modelidArray))
            let projectID = this.projectID;
            let modelIds = model_ids.join('|');
            this.VaultID = this.$staticmethod._Get("organizeId");
            this.featureID = modelIds
            // this.bimViewerUrl = `${window.bim_config.bimviewerurl}?projectId=${projectID}&model=${modelIds}&ver=`;

            // console.log(this.bimViewerUrl,'iframe 加载模型的url');
        },

        // iframeWpload() {
        //     let costModelIframe = this.$refs.costModelIframe.contentWindow;

        //     if (costModelIframe.model) {
        //         this.$staticmethod.bimhelper_finishrender(costModelIframe, () => {
        //             console.log('当前模型加载并渲染完成！');
        //             // this.setAllElementColor();
        //             this.modelLoad = false;
        //             this.getCostMgrPageData();        
        //             costModelIframe.model.BIM365API.Context.onCanvasResize();        
        //         });                
        //     }

        // },

        //分批获取构件id 和 模型id
        getCostMgrPageData() {
            this.$axios.get(`${this.$configjson.webserverurl}/api/CostMgr2Controllers/CostMgrList/GetCostMgrPageData`,{
                params: {
                    organizeId: this.$staticmethod._Get("organizeId"),
                    hadRequestCountTimes: 0,//第几次取数据
                    // getCount: this.webperCount,//每次取几条数据
                    getCount: this.countPerRequest,//每次取几条数据
                    token: this.$staticmethod.Get("Token"),
                }
            }).then(res=>{
                // console.log(this.currentCount,'第几次取数据');
                if (res.data.Ret == 1) {
                    // this.currentCount += 1;
                    this.allModelAndElements = res.data.Data.List;
                    // console.log(res.data.Data.List,'模型id和构件id');

                    // 下方函数是对模型数据处理：自动隔离，以及根据状态自动着色
                    // 但是2020-12-8号这天，老板提出新的交互逻辑
                    // 点击某个工程结构的时候 才去着色+聚焦
                    // 所以注释掉该函数
                    // this.modelDataHandl(res.data.Data.List);
                }
            });

        },

        //模型数据处理
        modelDataHandl(datas) {
            let modelComponentStateArray = [[],[]];
            let allElementsId= [];
            if (datas.length > 0) {
                //遍历筛选
                datas.forEach(element=>{
                    let ids = `${element.modelid}^${element.elementid}`
                    //所有模型id^构件id  隔离使用
                    allElementsId.push(ids);

                    //按状态把构件分类，state的值大于0的是超支，其他的则为正常
                    if (element.state == 1) {
                        //下标为1的所有元素 状态为“超支”，需着红色
                        modelComponentStateArray[1].push(ids);
                    } 
                    if (element.state == 0) {
                        //下标为0的所有元素 状态为“正常”，需着绿色
                        modelComponentStateArray[0].push(ids);
                    }
                });

                //先对即将着色的构件进行隔离
                // this.$model.Controller.isolateElementByElementId(allElementsId);
                
                // 隔离==新模型
                this.newModelIsolate(true);
                //按类别甄选完毕，开始着色
                this.setFullScreenAllElementColor(modelComponentStateArray);
            }

        },


        //大屏模型所有构件着色
        setFullScreenAllElementColor(classifying) {
            let elementColor = [
                {r: 103,g: 194,b: 58,a: 1},//绿色
                {r: 204,g: 0,b: 0,a: 1}//红色
            ];
            // console.log(classifying,'即将着色的构建集合')
            for (let j = 0; j < classifying.length; j++) {
                if (classifying[j].length > 0) {
                    //进行着色
                    this.newModelsetElementColor(classifying[j],elementColor[j].r,elementColor[j].g,elementColor[j].b,elementColor[j].a)
                    // this.$model.Controller.setElementColor(classifying[j],elementColor[j].r,elementColor[j].g,elementColor[j].b,elementColor[j].a)
                }
            }
        },

        //所有构件着色
        setAllElementColor() {
            let classifying = [[],[]];//模型id^构件id 的集合
            let elementColor = [
                {r: 103,g: 194,b: 58,a: 1},//绿色
                {r: 204,g: 0,b: 0,a: 1}//红色
            ];
            let onlyElementsArr = [];//纯模型id^构件id的集合，用以隔离
            console.log(this.modelComponentStateArray,'分类集合')
            for(let i = 0; i < 2; i++) {
                //判断状态为“正常”或“超支”的构件数组不为空
                //下标为0的元素都为正常  下标为1的元素都为超支
                if (this.modelComponentStateArray[i].length > 0) {
                    this.modelComponentStateArray[i].forEach(item=>{
                        item.elementids.forEach(element=>{
                            //循环拼接模型id和构件id，格式为  模型id^构件id
                            classifying[i].push(`${item.modelid}^${element}`);
                            //取出所有的模型id^构件id，放到一个数组，隔离使用
                            onlyElementsArr.push(`${item.modelid}^${element}`);
                        });

                    });
                    // console.log(classifying[i],'模型id^构件id')
                }
            }

            //先对要着色的构件进行隔离
            // this.$model.Controller.isolateElementByElementId(onlyElementsArr)
            
            // 隔离，新模型隔离
            this.newModelIsolate(true);

            for (let j = 0; j < classifying.length; j++) {
                //隔离后进行着色
                this.newModelsetElementColor(classifying[j],elementColor[j].r,elementColor[j].g,elementColor[j].b,elementColor[j].a)
            }


        },

        
        //点击左侧工程结构单独构件 聚焦并着色
        handleZoomModelElementSingle(row,ElementColors){
          if(row.modelids){
            
              // // 当前选中构件着色
              // let allCheckRemoveColor = [...new Set(checked_data.eleids.concat(this.resetCheckedTree))]
              // colorUtility.resetElementColor(allCheckRemoveColor);

              // this.resetCheckedTree = [];
              // colorUtility.setElementColor(checked_data.eleids,30,255,0,0.5)
              // this.resetCheckedTree = checked_data.eleids;
            // if (this.modelLoad) {
            //   this.$message.error('模型还未加载完毕，请稍后2');
            //   return
            // }
            //如不使用iframe方式加载模型，则需注释掉该代码
            // let costModelIframe = this.$refs.costModelIframe.contentWindow;
            // if(ElementColors==0){
            //   costModelIframe.model.BIM365API.Controller.setElementColor(row.eleids,103,194,58,1)
            // }else{
            //   costModelIframe.model.BIM365API.Controller.setElementColor(row.eleids,204,0,0,1)
            // }
            
            // costModelIframe.model.BIM365API.Controller.zoomElementByElementId(row.eleids);
            this.newModelselectorElementByElementId(row.eleids)
          }else{
            this.$message({
              type: "warning",
              message: "当前分类下暂无关联构件，请确保选择的为末级节点"
            });
          }
        },
        //点击左侧工程结构 聚焦并着色构件
        handleZoomModelElement(row) {
            // console.log(row)
            if (this.noModelDatas) {
                return;
            }

            if (this.modelLoad) {
                this.$message.error('模型还未加载完毕，请稍后1');
            }

            //如不使用iframe方式加载模型，则需注释掉该代码
            let costModelIframe = this.$refs.costModelIframe.contentWindow;

            if (this.zoomElementids.length > 0) {
                // this.$model.Controller.resetElementColor(this.zoomElementids);

                // costModelIframe.model.BIM365API.Controller.resetElementColor(this.zoomElementids);
                this.newModelresetElement(this.zoomElementids)
                this.zoomElementids = [];//聚焦用
            }

            
            let colorElements = [[],[]];//着色用（可能是绿色，可能是红色）
            let bmccode = row.Bmc_Code;
            let elementColor = [
                {r: 103,g: 194,b: 58,a: 1},//绿色
                {r: 204,g: 0,b: 0,a: 1}//红色
            ];


            this.allModelAndElements.forEach(item=>{
                //找到当前点击的构件
                //也有可能点击的是父级节点，
                //所以要用indexOf找到所有以bmccode开头的编码的子节点的构件
                if (item.bmc_code.indexOf(bmccode) == 0) {
                    let ids = `${item.modelid}^${item.elementid}`;
                    this.zoomElementids.push(ids);

                    //按状态把构件分类，state的值大于0的是超支，其他的则为正常
                    if (item.state == 1) {
                        //下标为1的所有元素 状态为“超支”，需着红色
                        colorElements[1].push(ids);
                        // this.$model.Controller.setElementColor(204,0,0,1,ids);
                    } 
                    if (item.state == 0) {
                        //下标为0的所有元素 状态为“正常”，需着绿色
                        colorElements[0].push(ids);
                        // this.$model.Controller.setElementColor(103,194,58,1,ids);
                    }
                }
                
            });
            
            // console.log(colorElements,'着色数组1')
            for (let j = 0; j < colorElements.length; j++) {
                if (colorElements[j].length > 0) {
                    //进行着色
                    // this.$model.Controller.setElementColor(colorElements[j],elementColor[j].r,elementColor[j].g,elementColor[j].b,elementColor[j].a)
                    
                    //iframe 方式加载模型，需使用下面方式
                    this.newModelsetElementColor(colorElements[j],elementColor[j].r,elementColor[j].g,elementColor[j].b,elementColor[j].a)
                    // costModelIframe.model.BIM365API.Controller.setElementColor(colorElements[j],elementColor[j].r,elementColor[j].g,elementColor[j].b,elementColor[j].a)
                }
            }
            
            // console.log(zoomElementids,'聚焦构件')
            // this.$model.Controller.zoomElementByElementId(this.zoomElementids)
            // costModelIframe.model.BIM365API.Controller.zoomElementByElementId(this.zoomElementids)
            //设置聚焦的构件的颜色
            // this.$model.Options.sethighlightColor(109,194,255,0.4);
            this.newModelselectorElementByElementId(this.zoomElementids)
            // costModelIframe.model.BIM365API.Controller.highlightElementByElementId(this.zoomElementids);
        },

    },

    computed: {
    },

    beforeDestroy() {
        // this.handleModel.BIM365API.Context.dispose();
        // this.handleModel = null
    }
}
</script>

<style scoped>
    .cost-model-window {
        flex: 1;
        width: 100%;
        position: relative;
    }

    #costModelWindow {
        width: 100%;
        height: 100%;
        position: relative;
    }

    #costModelWindow iframe {
        width: 100%;
        height: 100%;
    }

    .close-model {
        position: absolute;
        z-index: 10;
        top: 0;
        right: 0;
        font-size: 23px;
        cursor: pointer;
    }

    .close-model:hover {
        color: #1890ff;
    }

    .no-model-datas {
        position: absolute;
        top: 40%;
        left: 0;
        width: 100%;
        text-align: center;
    }
</style>
