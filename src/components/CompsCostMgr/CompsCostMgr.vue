<template>
    <div class="cost-management-container" :style="setCostManagementContainerStyle">
        <template v-if="!fromFullScreen">
            <div class="container-body-right"
                :element-loading-text="loadText"
                element-loading-background="rgba(255, 255, 255, 0.9)"
                v-loading="hasLoad">
                <div class="right-top-menu" >
                    <span class="css-hover-btn cost-tree-btn inline-block css-mr12" @click="toggleImportCostDialog()">导入成本预算</span>
                    <span class="css-hover-btn cost-tree-btn inline-block css-mr12" @click="toggleCostStatisticalChart()">查看成本曲线</span>
                    <span class="css-hover-btn cost-tree-btn inline-block css-mr12" @click="showInTheModel()">在模型中查看</span>
                    <span class="css-hover-btn cost-tree-btn inline-block css-mr12" @click="costModelPlay()">成本模拟</span>
                </div>
                <div class="bg-white right-bottom-content" v-if="!fromFullScreen">
                    <!-- :data="costTableData" -->
                    <!-- @row-click="test" -->
                    <!-- https://www.umyui.com/umycomponent/virtualTreeTable -->
                    <u-table
                        ref="plTreeTable"
                        border
                        stripe
                        fixed-columns-roll
                        header-drag-style
                        :height='uTableHeight'
                        :treeConfig="{
                            children: 'children',
                            iconClose: 'icon-interface-folder-copy utable-icon',
                            iconOpen: 'icon-interface-folder utable-icon',
                            expandAll: false}"
                        use-virtual
                        row-id="id"
                        :row-class-name="tableRowClassName"
                        class="_css-table-ele _css-table-ele2 css-scroll _css-customstyle css-fixedleftgbcolor-white css-table-cellborder"
                        :tree-props="{children: 'children', hasChildren: 'hasChildren'}">

                        <u-table-column
                            :tree-node="true"
                            label="工程结构"
                            fixed
                            width="380">
                            <template slot-scope="scope">
                                <el-tooltip class="item" effect="dark" :content="scope.row.structure" placement="bottom-start">
                                    <div class="_css-costitem" >{{scope.row.structure}}</div>
                                </el-tooltip>
                            </template>
                        </u-table-column>

                        <u-table-column
                            label="项目编码">
                            <template slot-scope="scope">
                                <div class="_css-costitem" >{{scope.row.code}}</div>
                            </template>
                        </u-table-column>

                        <u-table-column
                            label="计量标准">
                            <template slot-scope="scope">
                                <div class="_css-costitem" >{{scope.row.standard}}</div>
                            </template>
                        </u-table-column>

                        <u-table-column
                            label="工程单位">
                            <template slot-scope="scope">
                                <div class="_css-costitem" >{{scope.row.Company}}</div>
                            </template>
                        </u-table-column>

                        <u-table-column
                            width="95"
                            label="预算工程量">
                            <template slot-scope="scope">
                                <div class="_css-costitem" :title="scope.row.Quantities">
                                    {{scope.row.Quantities | numTofixed | amountMoney }}
                                </div>
                            </template>
                        </u-table-column>

                        <u-table-column
                            label="预算合价">
                            <template slot-scope="scope">
                                <div class="_css-costitem" :title="scope.row.totalPrice">
                                    {{scope.row.totalPrice | numTofixed | amountMoney }}
                                </div>
                            </template>
                        </u-table-column>

                        <u-table-column
                            width="95"
                            label="预算总费用">
                            <template slot-scope="scope">
                                <div class="_css-costitem" :title="scope.row.totalCost">
                                    {{scope.row.totalCost | numTofixed | amountMoney }}
                                </div>
                            </template>
                        </u-table-column>

                        <u-table-column
                            width="120"
                            label="计划完成工程量">
                            <template slot-scope="scope">
                                <div class="_css-costitem" :title="scope.row.totalPlanned">
                                    {{scope.row.totalPlanned | numTofixed | amountMoney }}
                                </div>
                            </template>
                        </u-table-column>

                        <u-table-column
                            width="120"
                            label="实际完成工程量">
                            <template slot-scope="scope">
                                <div class="_css-costitem" :title="scope.row.totalAmount">
                                    {{scope.row.totalAmount | numTofixed | amountMoney }}
                                </div>
                            </template>
                        </u-table-column>

                        <u-table-column
                            label="填报合价">
                            <template slot-scope="scope">
                                <div class="_css-costitem" :title="scope.row.fillTotalPrice">
                                    {{scope.row.fillTotalPrice | numTofixed | amountMoney}}
                                </div>
                            </template>
                        </u-table-column>

                        <u-table-column
                            width="120"
                            label="计划填报总费用">
                            <template slot-scope="scope">
                                <!-- 填报合价 * 计划完成工程量 -->
                                <!-- <div class="_css-costitem" >{{scope.row.totalPlanned * scope.row.fillTotalPrice }}</div> -->
                                <div class="_css-costitem" :title="scope.row.fillTotalCost">
                                    {{ scope.row.fillTotalCost | numTofixed | amountMoney }}
                                </div>
                            </template>
                        </u-table-column>

                        <u-table-column
                            width="120"
                            label="实际填报总费用">
                            <template slot-scope="scope">
                                <div class="_css-costitem" :title="scope.row.RealSumPrice">
                                    {{scope.row.RealSumPrice | numTofixed | amountMoney }}
                                </div>
                            </template>
                        </u-table-column>

                        <!-- <u-table-column
                            width="110"
                            label="最新填报日期">
                            <template slot-scope="scope">
                                <div class="_css-costitem" >{{scope.row.date}}</div>
                            </template>
                        </u-table-column> -->

                        <u-table-column
                            width="60"
                            style="background-color: #FFFFFF"
                            fixed="right"
                            label="状态">
                            <template slot-scope="scope">
                                <div v-if="scope.row.totalAmount != null && scope.row.totalAmount != '' && scope.row.totalAmount != 0"
                                    :class="[{'cost-item-normal':scope.row.RealSumPrice - scope.row.totalCost<=0},'_css-costitem']" >
                                    {{(scope.row.RealSumPrice - scope.row.totalCost) <= 0 ? '正常' : '超支'}}
                                </div>
                                <div v-else>-</div>
                            </template>
                        </u-table-column>

                        <u-table-column
                            width="170"
                            fixed="right"
                            label="操作">
                            <template slot-scope="scope">
                                <div class="_css-costitem" v-if="scope.row._bmcguid == null || scope.row._bmcguid == ''">
                                    <span class="css-hover-btn cost-tree-btn css-mr8" @click="addNewReport(scope.row)">填报</span>
                                    <span class="css-hover-btn cost-tree-btn" @click="showAllReport(scope.row)">查看所有填报</span>
                                </div>
                            </template>
                        </u-table-column>
                    </u-table>
                </div>

                <!-- 填报成本对话框 -->
                <zdialog-function
                    v-if="newCostDialogState"
                    :init_title="newCostDialogTitle"
                    :init_zindex="1010"
                    :init_innerWidth="1050"
                    :init_width="1050"
                    init_closebtniconfontclass="icon-suggested-close"
                    @onclose="closeCostDialog()">
                    <!-- 输入区域 -->
                    <div slot="mainslot" class="css-fc css-jcsb">
                        <!-- 左侧 -->
                        <div style="flex:1;opacity: 0.5;" title="导入数据,无需填写">
                            <div class="_css-line css-common-line">
                                <div class="_css-title css-left-title-name">预算工程量：</div>
                                <div class="_css-fieldvalue css-common-fieldvaluename">
                                    <input
                                        v-model="costFormData.Quantities"
                                        readonly
                                        type="text"
                                        class="css-common-fieldvaluename-in cursor-disable">
                                </div>
                            </div>

                            <div class="_css-line css-common-line">
                                <div class="_css-title css-left-title-name">预算合价：</div>
                                <div class="_css-fieldvalue css-common-fieldvaluename">
                                    <input
                                        v-model="costFormData.totalPrice"
                                        readonly
                                        type="text"
                                        class="css-common-fieldvaluename-in cursor-disable">
                                </div>
                            </div>

                            <div class="_css-line css-common-line">
                                <div class="_css-title css-left-title-name">预算总费用：</div>
                                <div class="_css-fieldvalue css-common-fieldvaluename">
                                    <input
                                        v-model="costFormData.totalCost"
                                        readonly
                                        type="text"
                                        class="css-common-fieldvaluename-in cursor-disable">
                                </div>
                            </div>

                            <div class="_css-line css-common-line">
                                <div class="_css-title css-left-title-name">填报合价：</div>
                                <div class="_css-fieldvalue css-common-fieldvaluename">
                                    <input
                                        v-model="costFormData.fillTotalPrice"
                                        readonly
                                        @keyup="NumberCheck"
                                        type="text"
                                        class="css-common-fieldvaluename-in">
                                </div>
                            </div>
                        </div>

                        <!-- 中间 -->
                        <div style="flex:0 0 425px">
                            <div class="_css-line css-common-line">
                                <div class="_css-title _css-title-name">填报日期：</div>
                                <div class="_css-fieldvalue css-common-fieldvaluename">
                                    <el-date-picker
                                        :picker-options="pickerOptions"
                                        v-model="costFormData.date"
                                        type="date"
                                        value-format="yyyy-MM-dd"
                                        placeholder="选择日期">
                                    </el-date-picker>
                                </div>
                            </div>

                            <div class="_css-line css-common-line">
                                <div class="_css-title _css-title-name">计划完成工程量：</div>
                                <div class="_css-fieldvalue css-common-fieldvaluename">
                                    <input
                                        v-model="costFormData.totalPlanned"
                                        @keyup="NumberCheck"
                                        @mousedown.stop
                                        type="text"
                                        class="css-common-fieldvaluename-in">
                                </div>
                                <div class="unit-content">
                                    <span class="left css-ml10">{{ costFormData.standard }}</span>
                                    {{ costFormData.Company }}
                                </div>
                            </div>

                            <div class="_css-line css-common-line">
                                <div class="_css-title _css-title-name">实际完成工程量：</div>
                                <div class="_css-fieldvalue css-common-fieldvaluename">
                                    <input
                                        v-model="costFormData.totalAmount"
                                        @keyup="NumberCheck"
                                        @mousedown.stop
                                        type="text"
                                        class="css-common-fieldvaluename-in">
                                </div>
                                <div class="unit-content">
                                    <span class="left css-ml10">{{ costFormData.standard }}</span>
                                    {{ costFormData.Company }}
                                </div>
                            </div>
                        </div>

                        <!-- 右侧 -->
                        <div style="flex:0 0 320px;opacity: 0.5;" title="计算数据,无需填写">
                            <div class="_css-line css-common-line">
                                <div class="_css-title _css-title-name">本次计划填报费用：</div>
                                <div class="_css-fieldvalue css-common-fieldvaluename">
                                    <input
                                        :value="getTotalPlanned"
                                        readonly
                                        type="text"
                                        class="css-common-fieldvaluename-in cursor-disable">
                                </div>
                            </div>
                            <div class="_css-line css-common-line">
                                <div class="_css-title _css-title-name">计划填报总费用：</div>
                                <div class="_css-fieldvalue css-common-fieldvaluename">
                                    <input
                                        :value="getFillTotalPriceR"
                                        readonly
                                        @keyup="NumberCheck"
                                        type="text"
                                        class="css-common-fieldvaluename-in cursor-disable">
                                </div>
                            </div>

                            <div class="_css-line css-common-line">
                                <div class="_css-title _css-title-name">本次实际填报费用：</div>
                                <div class="_css-fieldvalue css-common-fieldvaluename">
                                    <input
                                        :value="getFillTotalPrice"
                                        readonly
                                        @keyup="NumberCheck"
                                        type="text"
                                        class="css-common-fieldvaluename-in cursor-disable">
                                </div>
                            </div>
                            <div class="_css-line css-common-line">
                                <div class="_css-title _css-title-name">实际填报总费用：</div>
                                <div class="_css-fieldvalue css-common-fieldvaluename">
                                    <input
                                        :value="getRealSumPriceR"
                                        readonly
                                        type="text"
                                        class="css-common-fieldvaluename-in cursor-disable">
                                </div>
                            </div>

                            <div class="_css-line css-common-line">
                                <div class="_css-title _css-title-name">状态：</div>
                                <div class="_css-fieldvalue css-common-fieldvaluename">
                                    <input
                                        :class="{'color-red' : costFormData.state=='超支'}"
                                        v-model="getState"
                                        readonly
                                        type="text"
                                        class="css-common-fieldvaluename-in cursor-disable">
                                </div>
                            </div>
                        </div>

                    </div><!-- //输入区域 -->

                    <div slot="buttonslot" class="css-common-zdialogbtnctn" >
                        <zbutton-function
                            :init_text="'取消'"
                            :init_fontsize="14"
                            :debugmode="true"
                            :init_height="undefined"
                            :init_width="'76px'"
                            :init_bgcolor="'#fff'"
                            :init_color="'#1890FF'"
                            @onclick="closeCostDialog()"
                            >
                        </zbutton-function>
                        <zbutton-function
                            :init_text="'保存'"
                            :init_fontsize="14"
                            :debugmode="true"
                            :init_height="undefined"
                            :init_width="'76px'"
                            @onclick="evt_submititemedit()"
                            >
                        </zbutton-function>
                    </div>

                </zdialog-function>
                <!-- //条目编辑对话框 -->

            </div>

            <!-- 导入成本预算对话框 -->
            <zdialog-function
                v-if="importCostDialogState"
                init_title="导入成本预算"
                :init_zindex="1001"
                :init_innerWidth="450"
                :init_width="450"
                init_closebtniconfontclass="icon-suggested-close"
                @onclose="toggleImportCostDialog()">
                <!-- 输入区域 -->
                <div slot="mainslot">
                    <div class="_css-line css-common-line">
                        步骤一:&nbsp;&nbsp;
                        <p @click="downloadImportTemplate"
                            class="import-cost-link css-hover-btn">点击此处下载成本预算导入模板（必须）</p>
                    </div>
                    <div class="_css-line css-common-line">
                        步骤二:&nbsp;&nbsp;
                        <p class="import-cost-link css-hover-btn">
                            <label class="desc" for="uploadCostTemplate">点击此处上传编辑好的成本预算模板</label>
                            <input type="file" ref="uploadCostTemplate" id="uploadCostTemplate" accept=".xlsx,.xls" class="hidden" @change="getCostFile">
                        </p>
                    </div>
                </div><!-- //输入区域 -->
            </zdialog-function>
            <!-- //条目编辑对话框 -->

            <!-- 查看所有填报 -->
            <CompsAllCostList
                @close="closeAllCostDialog"
                @editCost="allCostListEdit"
                @deleteR="ListDeleteR"
                :currentData = "currentSelectData"
                :currenAllData = "currenCostAllData"
                v-if="allCostListDialogState">
            </CompsAllCostList>

            <CompsCostStatisticalChart
                :markLineData="defaultMarkLineData"
                @close="toggleCostStatisticalChart()"
                v-if="statisticalChartState"></CompsCostStatisticalChart>


            <!--在模型中查看-->
            <div :class="[{'full-screen-model-window':fromFullScreen,'css-bgfff' : !fromFullScreen},'show-in-model']" v-if="costModelWindowState">
                <div class="container-body-right model-window-show">
                    <div class="bg-white right-bottom-content">
                        <u-table
                            ref="ref_table_2"
                            :data="costTableData"
                            @row-click="zoomModelComponent"
                            border
                            stripe
                            fixed-columns-roll
                            header-drag-style
                            :height='uModelTableHeight'
                            :treeConfig="{
                                children: 'children',
                                iconClose: 'el-icon-folder-add',
                                iconOpen: 'el-icon-folder-remove',
                                expandAll: false}"
                            use-virtual
                            row-id="id"
                            :row-class-name="tableRowClassName"
                            class="_css-table-ele _css-table-ele2 css-scroll _css-customstyle css-fixedleftgbcolor-white css-table-cellborder"
                            :tree-props="{children: 'children', hasChildren: 'hasChildren'}">

                            <u-table-column
                                :tree-node="true"
                                label="工程结构"
                                fixed
                                width="180">
                                <template slot-scope="scope">
                                <div class="_css-costitem" >{{scope.row.structure}}</div>
                                </template>
                            </u-table-column>

                            <u-table-column
                                width="120"
                                label="计划填报总费用">
                                <template slot-scope="scope">
                                    <div class="_css-costitem" :title="scope.row.totalAmount">
                                        {{ scope.row.fillTotalCost | numTofixed | amountMoney }}
                                    </div>
                                </template>
                            </u-table-column>

                            <u-table-column
                                width="120"
                                label="实际填报总费用">
                                <template slot-scope="scope">
                                    <div class="_css-costitem" :title="scope.row.totalAmount">
                                        {{scope.row.RealSumPrice | numTofixed | amountMoney }}
                                    </div>
                                </template>
                            </u-table-column>

                            <!-- <el-table-column
                                width="90"
                                label="是否填报">
                                <template slot-scope="scope">
                                <div class="_css-costitem" >
                                    {{scope.row.totalAmount > 0 ? '已填报' : '-'}}
                                </div>
                                </template>
                            </el-table-column> -->

                            <u-table-column
                                width="60"
                                style="background-color: #FFFFFF"
                                fixed="right"
                                label="状态">
                                <template slot-scope="scope">
                                    <div v-if="scope.row.totalAmount != null && scope.row.totalAmount != '' && scope.row.totalAmount != 0"
                                        :class="[{'cost-item-normal':scope.row.RealSumPrice - scope.row.totalCost<=0},'_css-costitem']" >
                                        {{(scope.row.RealSumPrice - scope.row.totalCost) <= 0 ? '正常' : '超支'}}
                                    </div>
                                    <div v-else>-</div>
                                </template>
                            </u-table-column>

                            <u-table-column
                                v-if="costModelWindowState"
                                label="关联模型">
                                <template slot-scope="scope">
                                <div class="css-w100">
                                    <!-- <span @click="zoomModelComponent(scope.row)">-</span> -->
                                    <span v-if="scope.row.Bm_HasRel < 1">-</span>
                                    <span v-else
                                        class="_css-relele _css-relele-center icon-interface-guanlianmoxing">
                                    </span>
                                </div>
                                </template>
                            </u-table-column>

                        </u-table>
                    </div>
                </div>
                <!-- 模型窗口 -->
                <CompsCostModel
                @close="showInTheModel"
                ref="costModelComps"
                :fromFullScreen="fromFullScreen"
                :relationModelItems="costTableData[0]"></CompsCostModel>

            </div>
        </template>
        <template v-else>
            <!-- 大屏显示模型用 -->
            <CompsFullScreenModel></CompsFullScreenModel>
        </template>
        <modelPlaySimulationShow
            v-if="modelSimulationShow"

            bmtypeId='-1000'
            modelid=''
            @close="modelSimulationShow=false"
        ></modelPlaySimulationShow>
<!-- modelIframeSrc='../../../static/BIMComposer/index.html?projectId=81b643a5-0af1-4c46-8a2f-1531245626fb&model=b2ffdefe-90be-4e01-94d4-27aebe8acba8|cba8f7b4-06f8-46f6-bcc6-6f193315596e&ver=' -->
    </div>
</template>

<script>
import CompsAllCostList from '@/components/CompsCostMgr/CompsAllCostList';
import CompsCostStatisticalChart from '@/components/CompsCostMgr/CompsCostStatisticalChart';
import CompsCostModel from '@/components/CompsCostMgr/CompsCostModel';
import CompsFullScreenModel from '@/components/CompsCostMgr/CompsFullScreenModel';
import modelPlaySimulationShow from '@/components/CompsCostMgr/modelPlaySimulationShow';
export default {
    name: "CompsCostMgr",
    components: {
        CompsAllCostList,
        CompsCostStatisticalChart,
        CompsCostModel,
        CompsFullScreenModel,//大屏模型
        modelPlaySimulationShow, // 成本模拟
    },
    data() {
        return {
            uTableHeight: 0,
            hasLoad: false,
            loadText: '',
            pickerOptions: {
                // disabledDate: (time) => {
                //     return this.dealDisabledDate(time)
                // }
            },
            fromFullScreen: false,//是否是从大屏跳转过来的
            costModelWindowState: false,//模型窗口状态
            costTableData: [
                // {
                //     id: '2',
                //     structure: '工程结构',//工程结构
                //     _bmccode: '001',//项目编码
                //     standard: '',//计量标准
                //     Company: '',//工程单位
                //     Quantities: '',//预算工程量
                //     totalPrice: '',//预算合价
                //     totalCost: '',//预算总费用
                //     totalPlanned: '',//计划完成工程量
                //     totalAmount: '',//实际完成工程量
                //     fillTotalPrice: '',//填报合价
                //     fillTotalCost: '4434324324',//计划填报总费用
                //      RealSumPrice: '', //实际填报总费用
                //     date: '',//最新填报日期
                //     state: '',//状态
                //     stateCode: 1,//状态码
                //     hasChildren: false,
                //     children: [],
                // },
            ],

            costFormData: {//成本填报
                Quantities: 0,//预算工程量
                totalPrice: 0,//预算合价
                totalCost: 0,//预算总费用
                date: new Date(),//最新填报日期
                totalPlanned: 0,//计划完成工程量
                totalAmount: 0,//实际完成工程量
                fillTotalPrice: 0,//填报合价
                fillTotalCost: 0,//计划填报总费用
                RealSumPrice: 0, //实际填报总费用
                currentFillTotalCost: 0,//本次计划填报总费用
                currentRealSumPrice: 0,//本次实际填报总费用
                state: '',//状态
            },
            beforeCostFormData: {//编辑改变前填报信息
                totalPlanned: 0,//计划完成工程量
                totalAmount: 0,//实际完成工程量
            },

            dialogAction: 'add',//add 或 edit
            newCostDialogTitle: '填报成本',//填报成本对话框标题
            editCmrGuid: '',//编辑某条数据的guid

            newCostDialogState: false,//填报 对话框状态
            allCostListDialogState: false,//查看所有填报 对话框状态
            currentSelectData: null,//当前点选的行的数据
            statisticalChartState: false,//曲线统计图弹窗状态
            importCostDialogState: false,//导入成本预算弹窗状态
            currenCostAllData: {maindata:[],records:[]},//当前填报的成本的所有信息

            needRefresh: false,//是否需要刷新页面
            cumulativeCostForState: 0,//计算状态用的累加费用
            initExpandRow: [],
            defaultMarkLineData: 0,
            currentCostTimeArr: [],//当前点击填报或者编辑的节点的 所有填报的创建时间集
            modelSimulationShow: false, // 点击成本模拟展示模拟部分内容
            newRealSumPriceR:0
        };
    },

    mounted() {
        let query = this.$route.query;
        if (query.sign && query.sign == 'fullScreen') {
            this.fromFullScreen = true;

        } else {
            this.getCostMgrList();
            this.setUtableHeight();
        }

        // setInterval(()=>{
        //     this.$refs.ref_table && this.$refs.ref_table.doLayout();
        // }, 2000);
    },

    methods: {
        test(row, column, event) {
            console.log(row, column, event)
        },

        //设置表格高度
        setUtableHeight() {
            let pHeight = document.getElementsByClassName('container-body-right')[0].offsetHeight;
            this.uTableHeight = pHeight - 40;
            this.$refs.plTreeTable.doLayout();
        },

        getCurrentRouter () {
            if (this.fromFullScreen) {
                this.showInTheModel();
            }
        },

        //设置时间选择范围
        dealDisabledDate(time) {
            if (this.getTimeStampArray.length>0) {
                let costTime =  Math.max.apply(null, this.getTimeStampArray);
                return time.getTime() < costTime;
            }
        },

        //聚焦构件
        zoomModelComponent(row,a,b) {
          let ElementColors = (row.totalCost-row.RealSumPrice)>=0?0:1;
          if (b.target.className != "el-icon-folder-remove" && b.target.className != "el-icon-folder-add") {//判断点击的不是折叠按钮
            if(row._bm_guid){
              let _this = this;
              _this.$axios({
                url:`${this.$MgrBaseUrl.GetMaterialsElements}?Token=${this.$staticmethod.Get('Token')}`,
                method:'post',
                data: {
                  bm_guids: row._bm_guid,
                  token:this.$staticmethod.Get("Token")
                }
              }).then(x => {
                if(x.data.Data){
                  // this.checked_data = x.data.Data;
                  this.$refs.costModelComps.handleZoomModelElementSingle(x.data.Data,ElementColors);
                }
              }).catch(x => {
                _this.$message.error(x);
              });
            }
            // else{
            //   this.$refs.costModelComps.handleZoomModelElement(row);
            // }
          }
        },

        //设置
        setRowKey(row) {
            return row.id;
        },

        //在模型中查看（展示页面左右布局）
        showInTheModel() {
            this.costModelWindowState = !this.costModelWindowState;
            let pHeight = document.getElementsByClassName('cost-management-container')[0].offsetHeight;
            console.log(pHeight)
            this.uModelTableHeight = pHeight + 45;
        },

        //选择上传成本预算模板
        getCostFile(e) {
            if (e.target.files[0] != '' && e.target.files[0] != undefined) {
                // this.issueTemplateMsg.name = e.target.files[0].name;
                // this.issueTemplateMsg.fileInfo = e.target.files[0];
                let fd = new FormData();
                fd.append('organizeId',this.$staticmethod._Get("organizeId"));
                fd.append('Token',this.$staticmethod.Get("Token"))
                fd.append('file',e.target.files[0]);
                this.$axios.post(`${this.$configjson.webserverurl}/api/CostMgr2Controllers/CostMgrList/ImportDatas`,fd).then(res=>{
                    // console.log(res);
                    if (res.data.Ret == 1) {
                        this.$message.success('上传成功');
                        this.getCostMgrList();
                        this.toggleImportCostDialog();
                    } else {
                        this.$message.error(res.data.Msg);
                    }
                });
            }
        },

        //下载成本预算导入模板
        downloadImportTemplate() {
            let url = `${this.$configjson.webserverurl}/api/CostMgr2Controllers/CostMgrList/DownloadImportTemplate?organizeId=${this.$staticmethod._Get("organizeId")}&token=${this.$staticmethod.Get("Token")}`;
            window.open(url,'_blank');
        },

        //切换导入成本预算
        toggleImportCostDialog() {
            this.importCostDialogState = !this.importCostDialogState;
        },

        //获取成本数据
        getCostMgrList() {
            this.hasLoad = true;
            this.loadText = '请求数据量可能较大，请耐心等候...';
            this.$axios.get(`${this.$configjson.webserverurl}/api/CostMgr2Controllers/CostMgrList/GetCostMgrList2?Token=${this.$staticmethod.Get('Token')}`,{
                params: {
                    organizeId: this.$staticmethod._Get("organizeId"),
                }
            }).then(res=>{
                // console.log(res,'获取成本数据')
                // this.setParent(this.costTableData[0])
                // setTimeout(()=>{
                //     this.recursiveCalculation_a( this.costTableData[0])
                // });
                if (res.data.Ret == 1) {
                    this.costTableData = res.data.Data.List;
                    this.treeData = this.costTableData;

                    //设置表格数据
                    this.$nextTick(()=>{
                        this.loadText = '数据表格树生成中，请稍后...';
                        // document.getElementsByClassName('el-loading-text')[0].innerHTML = '数据表格树生成中，请稍后...';
                        this.$refs.plTreeTable.reloadData(this.treeData).then(res=>{
                            // console.log(res,'加载完毕');
                            this.hasLoad = false;
                        }).catch(res=>{
                            this.hasLoad = false;
                            // console.log(res);
                        })
                    });
                    // this.hasLoad = false;
                } else {
                    this.$message.error(res.data.Msg)
                    this.hasLoad = false;
                }

                this.needRefresh = false;
            });
        },

        setParent(data) {
            if(!data.children) return
            data.children.forEach(d=>{
                d.parentData = data
                // d.parentData.RelationModelItems = [];
                if(d.children){
                    this.setParent(d)
                }
            })
        },

        recursiveCalculation_a(data){
            data.children.forEach(d => {
                if(d.children){
                    this.recursiveCalculation_a(d)
                }
                if(d.parentData){
                    d.parentData.totalCost += d.totalCost;//预算总费用
                    // d.parentData.totalPlanned += d.totalPlanned;//计划完成工程量
                    // d.parentData.totalAmount += d.totalAmount;//实际完成工程量
                    d.parentData.fillTotalCost += d.fillTotalCost;//计划填报总费用
                    d.parentData.RealSumPrice += d.RealSumPrice;//实际填报总费用
                    // if (d.RelationModelItems != null && d.RelationModelItems.length > 0) {
                    //     // for(let i = 0; i < d.RelationModelItems.length; i++){
                    //     //     //实际填报总费用 - 预算总费用 > 0 ? 超支  ： 正常
                    //     //     // state:1为超支， 0为正常
                    //     //     d.RelationModelItems[i].state = d.RealSumPrice - d.totalCost > 0 ? 1 : 0;
                    //     //     console.log( d.RealSumPrice ,'---', d.totalCost,'--',d.structure,'----',d.RelationModelItems[i])
                    //     // }


                    //     d.parentData.RelationModelItems = d.parentData.RelationModelItems.concat(d.RelationModelItems);
                    // }

                }
            })
        },

        arrayUniquePublic (arr,key){
            let hash = {};
            return arr.reduce(function(item, next) {
                hash[next[key]] ? "" : (hash[next[key]] = true && item.push(next));
                return item;
            }, []);
        },

        tableRowClassName({row}) {
            let className = '';
            //实际填报总费用 - 预算总费用 > 0 ? 超支  ： 正常
            if (row.RealSumPrice - row.totalCost > 0) {
                className = 'cost-list-overspending';
            }
            return `css-tdunder ${className}`;
        },

        //打开填报成本对话框
        addNewReport(item) {
            // this.currentSelectData = this.$staticmethod.DeepCopy(item);
            this.currentSelectData = item;
            // console.log(item,'填报')
            // costFormData: {//成本填报
            //     Quantities: 0,//预算工程量
            //     totalPrice: 0,//预算合价
            //     totalCost: 0,//预算总费用
            //     date: new Date(),//最新填报日期
            //     totalPlanned: 0,//计划完成工程量
            //     totalAmount: 0,//实际完成工程量
            //     fillTotalPrice: 0,//填报合价
            //     fillTotalCost: 0,//计划填报总费用
            //     RealSumPrice: 0, //实际填报总费用
            //     currentFillTotalCost: 0,//本次计划填报总费用
            //     currentRealSumPrice: 0,//本次实际填报总费用
            //     state: '',//状态
            // },

            this.costFormData.Quantities = item.Quantities;//预算工程量
            this.costFormData.totalPrice = item.totalPrice;//预算合价
            this.costFormData.totalCost = item.totalCost;//预算总费用
            this.costFormData.fillTotalPrice = item.fillTotalPrice;//填报合价
            this.costFormData.fillTotalCost = item.fillTotalCost;//计划填报总费用
            this.costFormData.RealSumPrice = item.RealSumPrice;//实际填报总费用
            this.costFormData.date = '';//最新填报日期
            this.costFormData.standard = item.standard;
            this.costFormData.Company = item.Company;
            this.beforeCostFormData.totalAmount = 0;
            this.beforeCostFormData.totalPlanned = 0;

            this.loadRecords(item,'add');

            this.newCostDialogState = true;
        },

        //获取当前选中的或者准备填报的构件的所有信息
        loadRecords(item,type){
            this.$axios.get(`${this.$configjson.webserverurl}/api/CostMgr2Controllers/CostMgrList/LoadRecords?Token=${this.$staticmethod.Get('Token')}`,{
                params: {
                    bm_guid: item.id
                }
            }).then(res=>{

                if (res.data.Ret == 1) {
                    this.currenCostAllData = res.data.Data;
                    this.currentCostTimeArr = [];
                    // debugger;
                    // console.log(res,'获取当前选中的或者准备填报的构件的所有信息')
                    //如果点击的是“填报”
                    if (type == 'add' && this.currenCostAllData.records.length > 0) {
                        let total = 0;
                        this.currenCostAllData.records.forEach(element => {
                            //获取列表中 所有成本的创建时间
                            this.currentCostTimeArr.push(element.cmr_RecordDateTime.split('T')[0]);
                            if (this.editCmrGuid == element.cmr_guid) {
                                //如果是当前编辑的数据，则跳过该次运算，避免重复计算该条数据
                                return
                            }
                            //实际完成工程量 * 填报合价
                            total += element.cmr_RealEngQua*element.cmr_RealGroupPrice
                        });
                        this.cumulativeCostForState = total;

                        this.computationalDisplayState(0,'----1');

                    } else {
                        this.costFormData.state = '-';
                    }
                } else {
                    this.$message.error(res.data.Msg);
                }
            })
        },

        computationalDisplayState(total,test) {
          total += this.cumulativeCostForState;
          // console.log(total,test)
          //状态 = 预算总费用 - 实际填报总费用 < 0 为超支  >=0 为正常
          if (this.currentSelectData.totalCost - total < 0) {
            this.costFormData.state = '超支';
          } else {
            this.costFormData.state = '正常';
          }
        },
        // 成本模拟
        costModelPlay(item){
            this.GetCostRelModelEles_Simulate();

        },
        GetCostRelModelEles_Simulate(){
            let _this = this;
            let organizeIdtxt = _this.$staticmethod._Get("organizeId");
            _this.$axios
                .get(
                `${window.bim_config.webserverurl}/api/CostMgr2Controllers/CostMgrList/GetCostRelModelEles_Simulate?organizeId=${organizeIdtxt}&Token=${this.$staticmethod.Get('Token')}`
                )
                .then(x => {
                    if (x.status == 200) {
                        if (x.data.Ret > 0) {
                            if (x.data.Data) {
                                let modelList = x.data.Data;
                                _this.modelEles_Axios = modelList;

                                let allModelid = [];
                                for(let i = 0; i < modelList.length; i++){
                                    allModelid.push(modelList[i].modelid);
                                }

                                allModelid = [...new Set(allModelid)];
                                if(allModelid.length <= 0){
                                    _this.$message.error('当前列表暂无模型');
                                    _this.modelSimulationShow = false;
                                    return;
                                }else{
                                    _this.modelSimulationShow = true;
                                }
                            }
                            else{
                                _this.$message.error('当前列表暂无模型');
                                _this.modelSimulationShow = false;
                                return;
                            }
                        }
                    }else{
                        console.log(x)
                    }
                })
                .catch(x => {});
        },
        //查看所有填报
        showAllReport(item) {
            // this.currentSelectData = this.$staticmethod.DeepCopy(item);
            // console.log(item);
            this.currentSelectData = item;
            this.loadRecords(item,'showAll');
            this.allCostListDialogState = true;
        },

        //关闭 填报 对话框
        closeCostDialog() {
            //清空这次点击的构件的相关数据
            if (this.dialogAction != 'edit') {
                this.currenCostAllData = [];
            }
            this.resetCostFormData();
            this.newCostDialogState = false;
            // this.currentSelectData.totalCost = 0;

            if (this.dialogAction == 'edit') {
                this.dialogAction = 'add';//add 或 edit
                this.newCostDialogTitle = '填报成本';//填报成本对话框标题
                this.editCmrGuid = '';//编辑某条数据的guid
            }

        },

        //关闭 查看所有填报 对话框
        closeAllCostDialog() {
            this.allCostListDialogState = false;
            // if (this.needRefresh || state) {
            //     this.loadText = '数据同步中...';
            //     this.hasLoad = true;
            //     setTimeout(()=>{
            //         //编辑/删除后
            //         //求出 计划填报总费用/实际填报总费用  跟  编辑或删除之前的差值
            //         //该差值，就是本次编辑/删除后  增加的或者减少的费用
            //         //可能是负值，也可能是正值
            //         params.fillTotalCost -= this.currentSelectData.fillTotalCost;//计划填报总费用
            //         params.RealSumPrice -= this.currentSelectData.RealSumPrice;//实际填报总费用

            //         //获取当前编辑或添加的填报的节点编码，并转换为编码数组
            //         let Bmc_Code = this.hanldeBmcCode(this.currentSelectData.Bmc_Code);

            //         //递归遍历修改数据 
            //         this.updateCostMgrList(Bmc_Code,params,this.costTableData);
            //         console.log(this.costTableData,'更新完看看')
            //         //重新载入修改完毕的数据
            //         this.reLoadCostMgrList();
            //     },500)
            // }
        },
        //删除填报时的回调
        ListDeleteR(state,params){
            if (this.needRefresh || state) {
                params.fillTotalCost -= this.currentSelectData.fillTotalCost  //本次删除的计划填报费用
                params.RealSumPrice -= this.currentSelectData.RealSumPrice    //本次删除的实际填报费用
                this.currentSelectData.fillTotalCost += params.fillTotalCost  //删除后的计划填报总费用
                this.currentSelectData.RealSumPrice += params.RealSumPrice    //删除后的实际填报总费用
                let countParams = {
                  fillTotalCost:params.fillTotalCost,
                  RealSumPrice:params.RealSumPrice,
                  totalPlanned: params.totalPlanned,
                  totalAmount:  params.totalAmount
                }
                this.updateAllPrice(countParams)

            }
        },
        //统一更新数据
        updateAllPrice(params){
            //获取当前编辑或添加的填报的节点编码，并转换为编码数组
            let Bmc_Code = this.hanldeBmcCode(this.currentSelectData.Bmc_Code)
            //获取当前变动的数据id
            let Bmc_Id   = this.currentSelectData.id
            //递归遍历修改数据
            setTimeout(()=>{
              this.updateCostMgrList(Bmc_Code,params,this.costTableData,Bmc_Id);
              //重新载入修改完毕的数据
              this.reLoadCostMgrList();
            },500);
        },
        //曲线统计图
        toggleCostStatisticalChart() {
            //成本曲线图的基准线（值为 预算总费用）
            this.defaultMarkLineData = this.costTableData[0].totalCost;
            this.statisticalChartState = !this.statisticalChartState;
        },

        NumberCheck(e) {
            let str = e.target.value;
            let len1 = str.substr(0, 1);
            let len2 = str.substr(1, 1);
            //如果第一位是0，第二位不是点，就用数字把点替换掉
            if (str.length > 1 && len1 == 0 && len2 != ".") {
                str = str.substr(1, 1);
            }
            //第一位不能是.
            if (len1 == ".") {
                str = "";
            }
            //限制只能输入一个小数点
            if (str.indexOf(".") != -1) {
                let str_ = str.substr(str.indexOf(".") + 1);
                if (str_.indexOf(".") != -1) {
                str = str.substr(0, str.indexOf(".") + str_.indexOf(".") + 1);
                }
            }
            //正则替换，保留数字和小数点
            e.target.value = str.replace(/[^\d^\.]+/g,'');
            //如果需要保留小数点后两位，则用下面公式
            // str = str.replace(/\.\d\d$/,'')

        },

        //提交填报成本
        evt_submititemedit() {
            if (this.costFormData.totalPlanned == '') {
                this.$message.error('计划完成工程量 不可为空');
                return
            }

            if (this.costFormData.totalAmount == '') {
                this.$message.error('实际完成工程量 不可为空');
                return
            }
            let params = {
                Token: this.$staticmethod.Get("Token"),
                RecordDateTimeStr: this.costFormData.date,//最新填报日期
                PlanEngQua: this.func_toFixed(this.costFormData.totalPlanned,2),//计划完成工程量
                RealEngQua: this.func_toFixed(this.costFormData.totalAmount,2),//实际完成工程量
                RealGroupPrice: '',//填报合价
            }

            let eq = this.currentCostTimeArr.indexOf(this.costFormData.date.split('T')[0]);
            if (eq > -1) {
                this.$message.error('填报日期已存在，请选择其他日期');
                return;
            }
            if (this.dialogAction == 'add') {
                params.bm_guid = this.currentSelectData.id;
                this.loadText = '数据同步中...';
                this.hasLoad = true;
                this.$axios.post(`${this.$configjson.webserverurl}/api/CostMgr2Controllers/CostMgrList/FillInRecord`,
                this.$qs.stringify(params)).then(res=>{
                    // console.log(res,'填报数据')
                    if (res.data.Ret == 1) {

                        this.$message.success('填报成功');
                    
                        this.closeCostDialog();
                        let countParams = {
                            fillTotalCost: params.PlanEngQua * this.currentSelectData.fillTotalPrice,
                            RealSumPrice: params.RealEngQua * this.currentSelectData.fillTotalPrice,
                            totalPlanned: params.PlanEngQua,
                            totalAmount: params.RealEngQua
                        }
                        this.updateAllPrice(countParams)
                            // //递归遍历修改数据
                            // this.updateCostMgrList(Bmc_Code,countParams,this.costTableData);
                            // // console.log(this.costTableData,'更新完看看')

                            // //重新载入修改完毕的数据
                            // this.reLoadCostMgrList();
                            // // this.$refs.plTreeTable.reloadData(this.costTableData)
                            
                        // setTimeout(()=>{},500);

                    } else {
                        this.hasLoad = false;
                        this.$message.error(res.data.Msg);
                    }
                })
            } else {
                params.cmr_guid = this.editCmrGuid;
                this.loadText = '数据同步中...'
                this.hasLoad = true
                this.$axios.post(`${this.$configjson.webserverurl}/api/CostMgr2Controllers/CostMgrList/ModifyRecord`,
                this.$qs.stringify(params)).then(res=>{
                    // console.log(res,'编辑数据s')
                    if (res.data.Ret == 1) {
                        this.$message.success('修改成功');
                        this.closeCostDialog();
                        this.needRefresh = true;
                        this.loadRecords(this.currentSelectData,'showAll');
                        let countParams = {
                                fillTotalCost: (params.PlanEngQua - this.beforeCostFormData.totalPlanned)* this.currentSelectData.fillTotalPrice,
                                RealSumPrice: (params.RealEngQua - this.beforeCostFormData.totalAmount) * this.currentSelectData.fillTotalPrice,
                                totalPlanned: params.PlanEngQua - this.beforeCostFormData.totalPlanned,
                                totalAmount: params.RealEngQua - this.beforeCostFormData.totalAmount
                            }
                        this.updateAllPrice(countParams)
                        this.currentSelectData.fillTotalCost += countParams.fillTotalCost  //修改后的计划填报总费用
                        this.currentSelectData.RealSumPrice += countParams.RealSumPrice    //修改后的实际填报总费用

                    } else {
                        this.hasLoad = false
                        this.$message.error(res.data.Msg);
                    }
                })
            }


        },

        //将bmc_code，处理成结果为["001", "001-002", "001-002-003", "001-002-003-004"]的格式
        hanldeBmcCode(code) {
            let codeArr = code.split('-');
            let result = [];

            for (let i = 1; i <= codeArr.length; i++) {
                let str = '';
                str = codeArr.slice(0,i).join('-');
                result.push(str)
            }
            // console.log(result)
            return result;
        },

        //更新列表数据（所有相关系列节点的填报费用更新）
        updateCostMgrList(codeArr,params,obj,id) {
            obj.forEach(item=>{
                //为空说明是根级，indexOf结果不等于-1，说明是父节点或者是当前节点
                if (item.Bmc_Code == '' || codeArr.indexOf(item.Bmc_Code) != -1) {
                    if(item.children !==null ){
                        item.fillTotalCost += params.fillTotalCost;//计划填报总费用
                        item.RealSumPrice += params.RealSumPrice;//实际填报总费用
                    }
                    if(item.children ===null&&item.id ===id){
                        item.fillTotalCost += params.fillTotalCost;//计划填报总费用
                        item.RealSumPrice += params.RealSumPrice;//实际填报总费用
                        item.totalPlanned += params.totalPlanned;//计划完成工程量
                        item.totalAmount += params.totalAmount;//实际完成工程量
                    }
                    // this.$set(item,'fillTotalCost',parseInt(item.fillTotalCost += params.fillTotalCost));//计划填报总费用
                    // this.$set(item,'RealSumPrice',parseInt(item.RealSumPrice += params.RealSumPrice));//实际填报总费用
                }

                if (item.children) {
                    this.updateCostMgrList(codeArr,params,item.children,id);
                }
            });
        },

        reLoadCostMgrList() {
            //获取之前已经展开的
            let eExpand = this.$refs.plTreeTable.getTreeExpandRecords()
            //获取之前滚动的位置
            let scrollTop = this.$refs.plTreeTable.tableExample().scrollTop
            //重新载入数据
            this.$nextTick(()=>{
                this.$refs.plTreeTable.doLayout()
                this.$refs.plTreeTable.reloadData(this.costTableData).then(() => {
                    //重新展开之前展开的节点
                    eExpand.forEach(row => {
                        this.$refs.plTreeTable.setTreeExpansion(row, true)
                    });
                    this.$refs.plTreeTable.pagingScrollTopLeft(scrollTop+60)
                    setTimeout(()=>{
                      this.hasLoad = false;
                      this.loadText = '';
                    },500)
                })
            })
        },

        resetCostFormData() {
            this.costFormData = {//成本填报
                Quantities: 0,//预算工程量
                totalPrice: 0,//预算合价
                totalCost: 0,//预算总费用
                date: new Date(),//最新填报日期
                totalPlanned: 0,//计划完成工程量
                totalAmount: 0,//实际完成工程量
                fillTotalPrice: 0,//填报合价
                fillTotalCost: 0,//计划填报总费用
                RealSumPrice: 0, //实际填报总费用
                currentFillTotalCost: 0,//本次计划填报总费用
                currentRealSumPrice: 0,//本次实际填报总费用
                state: '',//状态
            };
        },

        //从查看所有成本中编辑某条数据
        allCostListEdit(item,datas,params) {
            // console.log(item,datas,'从查看所有成本中编辑某条数据')
            this.newCostDialogTitle = "编辑";
            this.dialogAction = 'edit';
            this.currentCostTimeArr = [];
            this.editCmrGuid = item.cmr_guid;
            this.costFormData.Quantities = datas.Quantities;//预算工程量
            this.costFormData.totalPrice = datas.totalPrice;//预算合价
            this.costFormData.totalCost = datas.totalCost;//预算总费用

            this.costFormData.date = item.cmr_RecordDateTime.split(' ')[0];//时间
            this.costFormData.totalPlanned = item.cmr_PlanEngQua;//计划完成工程量
            this.costFormData.totalAmount = item.cmr_RealEngQua;//实际完成工程量
            this.costFormData.fillTotalPrice = this.currentSelectData.fillTotalPrice;//填报合价
            this.costFormData.standard = datas.standard;
            this.costFormData.Company = datas.Company;

            this.costFormData.fillTotalCost = params.fillTotalCost//计划填报总费用
            this.costFormData.RealSumPrice = params.RealSumPrice//实际填报总费用
            this.beforeCostFormData.totalPlanned = item.cmr_PlanEngQua 
            this.beforeCostFormData.totalAmount = item.cmr_RealEngQua 
            if (this.currenCostAllData.records.length > 0) {
                this.currenCostAllData.records.forEach(element => {
                    //获取列表中 所有成本的创建时间
                    if (this.editCmrGuid == element.cmr_guid) {
                        //如果是当前编辑的数据，则跳过该次运算，避免重复push
                        return
                    }
                    this.currentCostTimeArr.push(element.cmr_RecordDateTime.split('T')[0]);
                });
            }
            // this.loadRecords(datas,'add');
            this.newCostDialogState = true;
        },

        //num 目标数字   s  保留几位
        func_toFixed(num,s) {
            if (num % 1 == 0) {
                return parseInt(num);
            } else {
                return Math.round(num * 100) / 100;
            }

        },
    },

    computed: {

        //本次实际填报费用
        getFillTotalPrice() {
            //本次实际填报费用 = 实际完成工程量 * 填报合价
            if (this.costFormData.totalAmount != '' && this.costFormData.fillTotalPrice != '') {
               this.costFormData.currentRealSumPrice = this.costFormData.totalAmount * this.costFormData.fillTotalPrice;
                this.computationalDisplayState(this.costFormData.RealSumPrice,'----2');
                // this.computationalDisplayState(this.costFormData.RealSumPrice + this.costFormData.currentRealSumPrice,'----2');
            } else {
                this.costFormData.currentRealSumPrice = 0;
            }

            return this.costFormData.currentRealSumPrice;
        },

        //本次计划填报费用
        getTotalPlanned() {
            //本次计划完成工作量 = 计划完成工程量 * 填报合价
            if (this.costFormData.totalPlanned != '' && this.costFormData.fillTotalPrice != '') {
               this.costFormData.currentFillTotalCost = this.costFormData.totalPlanned * this.costFormData.fillTotalPrice;
            } else {
                this.costFormData.currentFillTotalCost = 0;
            }
            return this.costFormData.currentFillTotalCost;
        },
        //计划填报总费用
        getFillTotalPriceR() {
            //计划填报费用 += （输入计划工程量 - 编辑前计划工程量） * 填报合价
            let newFillTotalCost= this.costFormData.fillTotalCost  //每次计算前等于计划填报总费用
            if(this.costFormData.fillTotalCost === null){
              newFillTotalCost = 0
            }
            if (this.costFormData.totalPlanned != '' && this.costFormData.fillTotalPrice != ''){
               newFillTotalCost += (this.costFormData.totalPlanned - this.beforeCostFormData.totalPlanned)* this.costFormData.fillTotalPrice
            }
            if(this.costFormData.totalPlanned === '') {
              newFillTotalCost += 0 - this.beforeCostFormData.totalPlanned* this.costFormData.fillTotalPrice
            }
            return newFillTotalCost 
        },
        //实际填报总费用
        getRealSumPriceR() {
            //实际填报费用 += （输入实际工程量 - 编辑前实际工程量） * 填报合价
            let newRealSumPrice= this.costFormData.RealSumPrice  //每次计算前等于实际填报总费用
            if(this.costFormData.RealSumPrice === null){
              newRealSumPrice = 0
            }
            if (this.costFormData.totalAmount != '' && this.costFormData.fillTotalPrice != ''){ 
               newRealSumPrice += (this.costFormData.totalAmount - this.beforeCostFormData.totalAmount)* this.costFormData.fillTotalPrice
            }
            if(this.costFormData.totalAmount === '') {
              newRealSumPrice += 0 - this.beforeCostFormData.totalAmount * this.costFormData.fillTotalPrice
            }
            this.newRealSumPriceR = newRealSumPrice
            return newRealSumPrice 
        },
        //编辑时状态的变化
        getState() {
            //判断实际填报费用是否小于等于预算费用
            if(this.newRealSumPriceR <= this.costFormData.totalCost){
              this.costFormData.state = "正常"
            }else{
              this.costFormData.state = "超支"
            }
            return this.costFormData.state
        },

        getTimeStampArray() {
            let storages = [];
            storages = this.currentCostTimeArr.map(item=>{
                return new Date(item).getTime()
            });

            return storages;
        },

        setCostManagementContainerStyle() {
          if (this.fromFullScreen) {
            return 'z-index: 1';
          }
        }
    },

    filters: {
        numTofixed(value) {
            // debugger
            let num = 0;
            if (value != null && value != '' && value != 0) {
                if (value % 1 == 0) {
                    num = value;
                } else {
                    num = Math.round(value * 100) / 100;
                }
                // num = this.func_toFixed(parseFloat(value),2)
                // num = parseFloat(value).toFixed(2);
            } else {
                num = '-';
            }
            return num;
        },

        amountMoney(parm, n = 3) { //数字加逗号(去掉尾部多余的0)
            if (parm == 0 || parm == '-' || parm == '' || parm == null) {
                return parm;
            } else {
                //使用Number强转一下再toString
                parm = Number((parm || 0)).toString();
                let integer = ``,
                    isNegativeNumber = false,
                    decimal = ``,
                    result = ``;
                isNegativeNumber = parm / 1 > 0 ? false : true;
                parm = parm.replace('-', '');
                integer = parm.split(`.`)[0];
                if (parm.split(`.`).length > 1) {
                    decimal = parm.split(`.`)[1];
                }
                while (integer.length > 3) {
                    result = `,${integer.slice(-n)}${result}`;
                    integer = integer.slice(0, integer.length - 3);
                }
                if (integer) {
                    result = decimal ? `${integer}${result}.${decimal}` : `${integer}${result}`;
                }

                if (isNegativeNumber && (parm / 1) != 0) {
                    result = `-${result}`;
                }
                return result;
            }
        }
    }
};
</script>

<style>
    .cost-list-overspending {
        color: #CC0000
    }

    .cost-management-container .el-table__header,
    .cost-management-container .el-table__fixed-body-wrapper {
      background-color: #FFFFFF;
    }

    .cost-management-container .utable-icon {
        color: #1890ff;
    }
</style>

<style scoped>
    .cost-management-container {
        width: 100%;
        height: 100%;
        display: flex;
        position: relative;
    }

    .cost-management-container .container-body-right {
        width: 100%;
        height: 100%;
    }

    .right-bottom-content {
        width: 100%;
        /* width: calc(100% - 10px);
        padding-right: 10px;
        box-sizing: border-box; */
        height: calc(100% - 38px);
        /* overflow: auto; */
    }

    .container-body-right.model-window-show {
        flex: 0 0 565px;
        width: 565px;
    }

    ._css-costitem {
        text-overflow: ellipsis;
        white-space: nowrap;
        overflow-x: hidden;
        font-size: 14px;
        font-weight: 400;
    }

    .cost-tree-btn {
        border-radius: 4px;
        color: #fff;
        font-size: 12px;
        padding: 5px 10px;
        background: linear-gradient(224deg,#0091ff,#007aff);
    }

    .cost-item-normal {
        color: #67C23A;
    }

    .container-body-right .right-top-menu {
        text-align: left;
        margin-bottom: 12px;
    }

    ._css-title-name {
        width: 128px;
        text-align: left;
    }

    .css-left-title-name {
        width: 86px;
        text-align: left;
    }

    .unit-content {
        width: 105px;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .import-cost-link {
        margin: 10px;
        text-decoration: underline;
        font-style: italic;
    }

    .import-cost-link .hidden {
        display: none;
    }

    .import-cost-link .desc {
        cursor: pointer;
    }

    /* ._css-customstyle {
        height: calc(100% - 0px) !important;
    } */

    .cursor-disable {
        cursor: not-allowed;
    }

    ._css-relele-center {
        margin: 0 auto;
    }

  .show-in-model {
    display: flex;
    width: 100%;
    height: calc(100% + 45px);
    position: absolute;
    top: -43px;
    left: 0;
    /*background-color: #FFFFFF;*/
    z-index: 1000;
  }

  .full-screen-model-window {
      height: 100%;
      top: 0;
  }

</style>
