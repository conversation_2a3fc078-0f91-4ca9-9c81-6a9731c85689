<template>
  <div class="_css-compscostmgr-all" @click="func_closeall">

      <!-- 条目编辑对话框 -->
      <zdialog-function
      v-if="status_showitemeditor"
      :init_title="func_getEditingTitle()"
      :init_zindex="1001"
      :init_innerWidth="400"
      :init_width="400"
      init_closebtniconfontclass="icon-suggested-close"
      @onclose="evt_closeitemedit()"
    
      >
            <!-- 输入区域 -->
            <div slot="mainslot" @mousedown="_stopPropagation($event)">
                <div class="_css-line css-common-line">
                    <div class="_css-title _css-title-name">
                        编码：
                    </div> 
                    <div class="_css-fieldvalue css-common-fieldvaluename"
                    >
                        <input type="text" 
                        v-model="m_editingitem.objinfo.cmi_number"
                        class="css-common-fieldvaluename-in">
                    </div>
                </div>

                <div class="_css-line css-common-line">
                    <div class="_css-title _css-title-name">
                        项目：
                    </div> 
                    <div class="_css-fieldvalue css-common-fieldvaluename">
                        <input type="text" 
                        v-model="m_editingitem.objinfo.cmi_name"
                        class="css-common-fieldvaluename-in">
                    </div>
                </div>

                <div class="_css-line css-common-line">
                    <div class="_css-title _css-title-name">
                        单位：
                    </div> 
                    <div class="_css-fieldvalue css-common-fieldvaluename">
                        <input type="text" 
                        v-model="m_editingitem.objinfo.cmi_unit"
                        class="css-common-fieldvaluename-in">
                    </div>
                </div>

                <div class="_css-line css-common-line">
                    <div class="_css-title _css-title-name">
                        数量：
                    </div> 
                    <div class="_css-fieldvalue css-common-fieldvaluename">
                        <input type="text" 
                        v-model="m_editingitem.objinfo.cmi_count"
                        class="css-common-fieldvaluename-in">
                    </div>
                </div>

                <div class="_css-line css-common-line">
                    <div class="_css-title _css-title-name">
                        单价：
                    </div> 
                    <div class="_css-fieldvalue css-common-fieldvaluename">
                        <input type="text" 
                        v-model="m_editingitem.objinfo.cmi_unitprice"
                        class="css-common-fieldvaluename-in">
                    </div>
                </div>

                <div class="_css-line css-common-line">
                    <div class="_css-title _css-title-name">
                        合价：
                    </div> 
                    <div class="_css-fieldvalue css-common-fieldvaluename">
                        <input type="text" 
                        v-model="m_editingitem.objinfo.cmi_totalprice"
                        class="css-common-fieldvaluename-in">
                    </div>
                </div>

                <div class="_css-line css-common-line">
                    <div class="_css-title _css-title-name">
                    备注：
                    </div>
                    <div class="_css-fieldvalue css-common-fieldvaluename _css-textareavalue">
                        <textarea 
                        v-model="m_editingitem.objinfo.cmi_biminfo"
                        class="css-common-fieldvaluename-in _css-teatarea-in css-miniscroll css-globalfont _css-textareaself">
                        </textarea>
                    </div>
                </div>

            </div><!-- //输入区域 -->

            <div slot="buttonslot" class="css-common-zdialogbtnctn" >
                <zbutton-function
                    :init_text="'取消'"
                    :init_fontsize="14"
                    :debugmode="true"
                    :init_height="undefined"
                    :init_width="'76px'"
                    :init_bgcolor="'#fff'"
                    :init_color="'#1890FF'"
                    @onclick="evt_closeitemedit()"
                    >
                </zbutton-function>
                <zbutton-function
                    :init_text="'保存'"
                    :init_fontsize="14"
                    :debugmode="true"
                    :init_height="undefined"
                    :init_width="'76px'"
                    @onclick="evt_submititemedit()"
                    >
                </zbutton-function>
            </div>
      
      </zdialog-function>
      <!-- //条目编辑对话框 -->

        <CompsAutoRel
        v-if="status_showautorel"
        loadingId="id_costautorel_outer"
        title="自动关联构件"
        init_field1name="成本清单中的属性名称"
        :init_field1options="func_getField1Options()"
        :modelid="init_specificmodelid"
        :modelname="init_specificmodelname"
        @oncancel="evt_autorelcancel"
        @onrel="evt_autorelrel"
        ></CompsAutoRel>

      <!-- 右键菜单 -->
      <div class="_css-contextmenus" 
      :style="style_showorgetposition()"
      :class="{'_css-noleft': _hidetree}"
      >

          <div 
          @click="func_viewrelation_left($event)"
        
          class="_css-contextmenu-i" >
            <div class="_css-contextmenu-icon" >
            </div>
            <div class="_css-contextmenu-text" >
                查看已关联构件
            </div>
          </div>

          <div v-if="_hidetree"
          @click="func_appendrelation_left($event)"
          :title="!func_gethasselectedeles()?'请先打开模型并选中构件':''"
          :class="{'css-dis':!func_gethasselectedeles()}"
          class="_css-contextmenu-i" >
            <div class="_css-contextmenu-icon" >
            </div>
            <div class="_css-contextmenu-text" >
                追加关联所选构件
            </div>
          </div>

          <div v-if="_hidetree"
          @click="func_overriderelation_left($event)"
          :title="!func_gethasselectedeles()?'请先打开模型并选中构件':''"
          class="_css-contextmenu-i" 
          :class="{'css-dis':!func_gethasselectedeles()}"
          >
            <div class="_css-contextmenu-icon" >
            </div>
            <div class="_css-contextmenu-text" >
                覆盖关联所选构件
            </div>
          </div>

          <div v-if="_hidetree"
          @click="func_clearelements($event)"
          class="_css-contextmenu-i" >
            <div class="_css-contextmenu-icon" >
            </div>
            <div class="_css-contextmenu-text" >
                清空已关联构件
            </div>
          </div>

      </div>
      <!-- //右键菜单 -->

<!-- 
application/vnd.openxmlformats-officedocument.spreadsheetml.sheet -->
    <input
        type="file"
        id="id_import"
        accept=".xlsx"
        @click.stop="_stopPropagation($event)"
        @change="on_import_change()"
      />

    <!-- 左树，右表格树 -->
    <div class="_css-ccostmgr-lefttree "
    :class="{'_css-noleft': _hidetree}"
    
    >
      <!-- 头固定40 树体滚动 -->
      <div class="_css-ccostmgr-treehead">
          成本管理方案
      </div>
      <div class="_css-ccostmgr-treebody ">
        <el-tree
          :data="m_treedata"
          :props="defaultProps"
          class="_css-customstyle"
          :expand-on-click-node="false"
          :highlight-current="true"
          :auto-expand-parent="true"
          :default-expand-all="true"
          @node-click="evt_nodeclick"
        >
          <span class="css-fc _css-treenode-content" slot-scope="{ node, data }">
            <i class="css-icon20 css-fs18 css-fc css-jcsa" :class="data.classname"></i>
            <span :title="node.label" class="css-ml4 _css-treenodelabel">{{node.label}}</span>
          </span>
        </el-tree>
      </div>
      <!-- //头固定40 树体滚动 -->
    </div>
    <div 
    class="_css-ccostmgr-righttreetab"
     :class="{'_css-noleft': _hidetree}"
    >

        <!-- 表格上方的按钮 -->
        <div class="_css-ccostmgr-tabtop"
        :class="{'_css-noleft': _hidetree}"
        >
            <div class="_css-btngroup">
                <div 
                @click="func_downloadimporttemplate($event)"
                class="_css-btnimport"
                 :class="{'_css-noleft': _hidetree}"
                >
                    <div v-if="_hidetree" class="icon-interface-download-fill _css-btn-icon"></div>
                    <div>下载导入模板</div>
                </div>
                <div v-if="_hidetree"
                @click="func_begintoimport($event)"
                class="_css-btnimport"
                 :class="{'_css-noleft': _hidetree}"
                >
                    
                    <div class="_css-btn-noicon">导入</div>
                </div>

                <!-- 新增、编辑、删除、导出当前方案 -->
                <div v-if="_hidetree"
                @click="func_begintoadd($event)"
                class="_css-btnimport"
                 :class="{'_css-noleft': _hidetree}"
                >
                    
                    <div class="_css-btn-noicon">新增</div>
                </div>
                <div v-if="_hidetree"
                @click="func_begintoedit($event)"
                class="_css-btnimport"
                 :class="{'_css-noleft': _hidetree}"
                >
                    
                    <div class="_css-btn-noicon">编辑</div>
                </div>
                <div v-if="_hidetree"
                @click="func_begintodelete($event)"
                class="_css-btnimport"
                 :class="{'_css-noleft': _hidetree}"
                >
                    
                    <div class="_css-btn-noicon">删除</div>
                </div>
                <div 
                 @click="func_exportcurrentplan($event)"
                class="_css-btnimport"
                 :class="{'_css-noleft': _hidetree}"
                >
                    
                    <div class="_css-btn-noicon">导出当前方案</div>
                </div>
                <!-- //新增、编辑、删除、导出当前方案 -->

            </div>

            <!-- 右侧的按钮区域：打开模型，自动关联 -->
            <div 
            class="_css-btngroup2">
                <div 
                @click="func_switchbim($event)"
                class="_css-btnimport"
                 :class="{'_css-noleft': _hidetree}"
                >
                    <div v-if="_hidetree" class="icon-interface-guanlianmoxing _css-btn-icon"></div>
                    <div>{{status_showbim?'关闭模型':'打开模型'}}</div>
                </div>

                <div 
                v-if="_hidetree"
                @click="func_showautorel($event)"
                class="_css-btnimport"
                 :class="{'_css-noleft': _hidetree}"
                >
                    <div v-if="_hidetree" class="icon-interface-setting _css-btn-icon"></div>
                    <div>自动关联</div>
                </div>

            </div>
            <!-- //右侧的按钮区域：打开模型，自动关联 -->

        </div>
        <!-- //表格上方的按钮 -->


        <!-- 表格外面的容器，右侧要加模型子页面 -->
        <div 
        element-loading-text="加载中"
        v-loading="m_tableloading"
        class="_css-table-bim-ctn"
         :class="{'_css-noleft': _hidetree}"
        >
            <el-table
            ref="ref_table"
            :data="m_tabledata"
            style="width: 100%;margin-bottom: 20px;"
            row-key="id"
            border
            :tree-props="{children: 'children', hasChildren: 'hasChildren'}"
            class="_css-table-ele _css-table-ele2 css-scroll _css-customstyle css-fixedleftgbcolor-white css-table-cellborder"
            :class="{'_css-noleft': _hidetree}"
            height="500"
            :row-class-name="tableRowClassName"
            :highlight-current-row="true"
            :header-cell-style="{'background-color':'transparent'}"
            @current-change="evt_currentChange"
            @row-contextmenu="evt_rowContextmenu"
            >
                <el-table-column
                :resizable="true"
                :fixed="false"
                border
                min-width="140"
                label="编码"
                class="_css-celllongcolumn"
                >
                <template slot-scope="scope">
                    <div 
                    class="_css-costitem" >{{scope.row.objinfo.cmi_number}}</div>
                </template>
                </el-table-column>

                <el-table-column
                :resizable="true"
                min-width="120"
                label="项目"
                class="_css-celllongcolumn"
                >
                <template slot-scope="scope">
                    <div
                    @click="func_viewelements(scope.row, $event)"
                    :title="scope.row.Eles && scope.row.Eles.length?'点击查看关联构件':''"
                    class="_css-costitem " 
                    :class="{'_css-modellink': scope.row.Eles && scope.row.Eles.length}"
                    >{{scope.row.objinfo.cmi_name}}</div>
                </template>
                </el-table-column>

                <el-table-column
                :resizable="true"
                width="50"
                label="单位"
                class="_css-celllongcolumn"
                >
                <template slot-scope="scope">
                    <div
                    class="_css-costitem" >{{scope.row.objinfo.cmi_unit}}</div>
                </template>
                </el-table-column>

                <el-table-column
                :resizable="true"
                min-width="80"
                label="数量"
                class="_css-celllongcolumn"
                >
                <template slot-scope="scope">
                    <div :title="func_col_cmicount(scope.row)"
                    class="_css-costitem _css-numbercell" >{{func_col_cmicount(scope.row)}}</div>
                </template>
                </el-table-column>

                <el-table-column
                :resizable="true"
                min-width="80"
                label="单价"
                class="_css-celllongcolumn"
                >
                <template slot-scope="scope">
                    <div :title="func_col_cmiunitprice(scope.row)"
                    class="_css-costitem _css-numbercell" >{{func_col_cmiunitprice(scope.row)}}</div>
                </template>
                </el-table-column>

                <el-table-column
                :resizable="true"
                min-width="80"
                label="合价"
                class="_css-celllongcolumn"
                >
                <template slot-scope="scope">
                    <div :title="func_col_cmitotalprice(scope.row)"
                    class="_css-costitem _css-numbercell" >{{func_col_cmitotalprice(scope.row)}}</div>
                </template>
                </el-table-column>

                <el-table-column
                :resizable="false"
                min-width="200"
                label="备注"
                class="_css-celllongcolumn"
                >
                <template slot-scope="scope">
                    <div :title="scope.row.objinfo.cmi_biminfo"
                    class="_css-costitem" >{{scope.row.objinfo.cmi_biminfo}}</div>
                </template>
                </el-table-column>


                <!-- <el-table-column
                :resizable="false"
                min-width="80"
                label="关联模型"
                class="_css-celllongcolumn"
                >
                <template slot-scope="scope">

                     <el-tooltip
                     v-if="scope.row.objinfo && scope.row.objinfo.Model"
                    popper-class="css-no-triangle" effect="dark" :content="scope.row.objinfo.Model.Name" placement="left">
                        <div 
                        @click="func_showrelmodel($event, scope.row)"
                        class="_css-costitem _css-model"
                        :class="{'icon-interface-guanlianmoxing': scope.row.objinfo.cmi_elementids}"
                        ></div>
                     </el-tooltip>

                </template>
                </el-table-column> -->

            </el-table>

            <!-- bim 页面 -->
            <div class="_css-bim-viewer"
            v-loading="m_bimloading"
            element-loading-text="加载中"
            v-if="status_showbim"
            >
                <iframe
                    id="id_iframe_bimviewer"
                    class="_css-bimviewer-ifr"
                    @load="evt_bimviewerload"
                    :src="`${cfg_getBimConfig().bimviewerurl}?projectId=${func_getbimcomposerId()}&model=${func_getmodelid()}`"
                    frameborder="0"
                    ref="playModelView"
                ></iframe>
            </div>




            <!-- //bim 页面 -->

        </div>
        <!-- //表格外面的容器，右侧要加模型子页面 -->

   
    </div>
    <!-- //左树，右表格树 -->
  </div>
</template>
<script>
import CompsAutoRel from '@/components/CompsProgress/CompsAutoRel'
export default {
    components: {
        CompsAutoRel
    },
    data() {
        return {

            // 最后一次打开的模型ID（仅限有左侧tree的界面）
            // ----------------------------------------
            m_haslefttree_lastmodelid:'',

            // 加载模型完成后，要 zoom 的数据
            // ----------------------------
            m_zoomafterrendered: [],

            // 当前正在编辑或新增的数据（如果是新增，则cmi_guid 为 '')
            // ----------------------------------------------------
            m_editingitem: {},

            // 是否显示成本条目新增或编辑对话框
            // ------------------------------
            status_showitemeditor: false,

            // 是否显示自动关联对话框
            // --------------------
            status_showautorel: false,

            // 左侧菜单是否可用
            // 查看已关联构件是否可用
            // ---------------------
            m_viewrelation_left: false,

            // 已选中的构件
            // -----------
            m_selectedelements:[],

            // 右键菜单
            // --------
            m_context_display: 'none',
            m_context_left: 0,
            m_context_top: 0,

            // 是否显示右侧模型窗口
            // ------------------
            status_showbim: false,

            // 模型是否已加载完成
            // -----------------
            status_bimrenders: false,

            m_bimloading: false,
            m_tableloading: false,
            m_bimmenus:[],

            // 表格当前选中的数据
            // -----------------
            m_currentselected: undefined,

            // 左侧树选择的节点
            // ---------------
            m_selectednode: undefined,
            m_treedata:[],
            m_tabledata:[],
            defaultProps: {
                children: 'children',
                label: 'label' 
            },

            _hidetree: false,
            _specificCate: undefined,
            _specificmodelid: undefined
        };
    }
    ,props: {
        init_hidetree : {
            type: Boolean,
            default: false
        },
        init_specificCate: {
            type: Object,
            default: undefined
        },
        init_specificmodelid: {
            type: String,
            default: undefined
        },
        init_specificmodelname: {
            type: String,
            default: undefined
        }
    }
    ,created() {

        // 字段值获取
        // ---------
        var _this = this;

        _this._hidetree = _this.init_hidetree;
        _this._specificCate = _this.init_specificCate;
        _this._specificmodelid = _this.init_specificmodelid;

        _this.func_gettreedata();
        _this.func_gettabledata();
    },
    mounted() {
        var _this = this;
        window.cmvue = this;

        setInterval(()=>{
            _this.$refs.ref_table && _this.$refs.ref_table.doLayout();
        }, 1000);
    },
    methods:{

        // 显示合价
        // -------
        func_col_cmitotalprice(row) {
            var _this = this;
            if (!row || !row.objinfo) {
                return '';
            }
            return row.objinfo.cmi_totalprice || '';
        },

        // 显示单价
        // -------
        func_col_cmiunitprice(row) {
            var _this = this;
            if (!row || !row.objinfo) {
                return '';
            }
            return row.objinfo.cmi_unitprice || '';
        },

        // 显示数量
        // --------
        func_col_cmicount(row) {
            var _this = this;
            if (!row || !row.objinfo) {
                return '';
            }
            return row.objinfo.cmi_count || '';
        },

        // [20200730_1638]点击查看关联构件
        // ------------------------------
        func_viewelements(row, ev) {

            // 判断当前模型是否已经打开
            // ----------------------
            var _this = this;

            // 设置表格选中行
            // -------------
            _this.$refs.ref_table.setCurrentRow(row);

            // 拿到模型ID
            // ----------
            if (row.Eles.length == 0) {
                return;
            }
            var modelid = row.Eles[0].split('^')[0];

            if (_this.status_showbim) {

                // 如果已经打开，需要判断此次打开的模型是否与
                // 最后一次打开的模型ID相同
                // ----------------------
                if (_this.m_haslefttree_lastmodelid == modelid) {

                    // 模型ID 相同，直接高亮 zoom
                    // -------------------------
                    _this.func_sethightlightandzoom(row.Eles);

                } else {

                    // 模型ID 不同，先关闭模型，再设置modelid，再打开模型，再高亮
                    // ------------------------------------------------------
                    _this.status_showbim = false;
                    _this.status_bimrenders = false;
                    _this._specificmodelid = modelid;
                    _this.m_zoomafterrendered = _this.$staticmethod.DeepCopy(row.Eles);
                    _this.m_haslefttree_lastmodelid = modelid;
                    _this.func_showbim();

                }

            } else {

                // 如果没有打开
                // 设置模型ID，打开模型，再高亮
                // --------------------------
                _this.status_bimrenders = false;
                _this._specificmodelid = modelid;
                _this.m_zoomafterrendered = _this.$staticmethod.DeepCopy(row.Eles);
                _this.func_showbim();

            }


        },

        // 导出当前方案
        // -----------
        func_exportcurrentplan() {
            
            // 如果选中的不是方案，提示用户
            // --------------------------
            var _this = this;
            var _node = _this.func_getleftselectednode();
            if (!_node) {
                _this.$message.error('未选择成本方案，请选择后重新操作');
                return;
            }
            if (_node.classname.indexOf('_css-plan-icon') < 0) {
                _this.$message.error('选中的节点不是成本方案，请重新选择后继续操作');
                return;
            }

            var _url = `${window.bim_config.webserverurl}/api/CostMgrControllers/Cost/ExportCurrentPlan?cmp_guid=${_node.id}`;
            window.location.href = _url;
        },

        // 得到左侧选中的 node
        // ------------------
        func_getleftselectednode() {
            var _this = this;
            if (_this._hidetree && _this._specificCate) {
                _this.m_selectednode = _this.$staticmethod.DeepCopy(_this._specificCate);
                _this.m_selectednode.classname = 'icon-interface-component_cost _css-plan-icon';
            }
            return _this.m_selectednode;
        },

        // 无论哪种情况，获取模型id
        // ----------------------
        func_getmodelid() {

            var _this = this;
            return _this._specificmodelid;
        },

        // 查看关联模型
        // -----------
        func_showrelmodel(ev, row) {
            var _this = this;
            _this.$refs.ref_table.setCurrentRow(row);
            _this.$nextTick(() => {
                _this.func_viewrelation_left();
            });
            
        },

        // 保存当前正在进行编辑的内容
        // 如果调用接口没问题，则：如果是编辑，直接更新，如果是新增，刷新列表？
        // -------------------------------------------------------------
        evt_submititemedit() {
            
            // 参数准备
            // --------
            var _this = this;
            var _para = {
                "cmi_count": _this.m_editingitem.objinfo.cmi_count,
                "Token": _this.$staticmethod.Get("Token"),
                "cmi_number": _this.m_editingitem.objinfo.cmi_number,
                "cmi_name": _this.m_editingitem.objinfo.cmi_name,
                "cmi_guid": _this.m_editingitem.objinfo.cmi_guid || '',
                "cmp_guid": _this.m_editingitem.objinfo.cmp_guid,
                "cmi_biminfo": _this.m_editingitem.objinfo.cmi_biminfo,
                "cmi_totalprice": _this.m_editingitem.objinfo.cmi_totalprice,
                "cmi_unit": _this.m_editingitem.objinfo.cmi_unit,
                "cmi_unitprice": _this.m_editingitem.objinfo.cmi_unitprice
            };
            var _url = `${window.bim_config.webserverurl}/api/CostMgrControllers/Cost/EditCostItem`;

            // 调用接口
            // -------
            _this.$axios({
                url: _url,
                method: 'post',
                data: _this.$qs.stringify(_para)
            }).then(x => {
                if (x.data.Ret > 0) {

                    // 如果是更新，则直接更新对应数据，如果是新增，则刷新
                    // ----------------------------------------------
                    _this.status_showitemeditor = false;
                    if (x.data.Data && x.data.Data.Data) {
                        _this.$message.success('修改成功');
                        var theitemToFind = _this.func_recursivefind(_this.m_tabledata, _this.m_currentselected.id);
                        if (theitemToFind) {

                            // 修改关联模型属性
                            // ---------------
                            theitemToFind.objinfo = _this.$staticmethod.DeepCopy(x.data.Data.Data);  
                        }
                    } else {

                        // 如果是新增，直接刷新
                        // ------------------
                        _this.$message.success('添加成功');

                        _this.func_refresh2();
                    }

                } else {
                    _this.$message.error(x.data.Msg);
                }
            }).catch(x => {
                console.error(x);
            });

        },

        _stopPropagation(ev) {
            ev && ev.stopPropagation && ev.stopPropagation();
        },

        // 关闭条目编辑对话框
        // -----------------
        evt_closeitemedit() {
            var _this = this;
            _this.status_showitemeditor = false;
        },

        // 获得新增或编辑时的标题
        // --------------------
        func_getEditingTitle() {
            var _this = this;
            if (_this.m_editingitem && _this.m_editingitem.id && _this.m_editingitem.id != '') {
                return '编辑内容';
            }
            return '新增内容';
        },

        // 第一个字段的下拉框中的选项集合
        // ---------------------------
        func_getField1Options() {
            var options = [
                {
                    value: 'cmi_number',
                    label: '编码'
                },
                {
                    value:'cmi_name',
                    label:'项目'
                }
            ];
            return options;
        },

        // 显示自动关联
        // -----------
        func_showautorel(ev) {
            var _this = this;
            _this.status_showautorel = true;
        },

        // 关闭自动关联对话框
        // -----------------
        evt_autorelcancel() {
            var _this = this;
            _this.status_showautorel = false;
        },

        // 调用接口 /api/CostMgrControllers/Cost/AutoElementRelation
        // ---------------------------------------------------------
        do_autorelrel(arr, prop_fromitem, prop_frommodel) {

            // 参数准备
            // --------
            var _this = this;
            var _data = {
                Token: _this.$staticmethod.Get("Token"),
                ModelListJson: JSON.stringify(arr),
                CostPropName: prop_fromitem,
                ElementPropName: prop_frommodel,
                bimcomposerId: _this.$staticmethod._Get("bimcomposerId"),
                cmp_guid: _this._specificCate.id
            };
            var _url = `${window.bim_config.webserverurl}/api/CostMgrControllers/Cost/AutoElementRelation`;
            _this.$axios({
                url: _url,
                method: 'post',
                data: _this.$qs.stringify(_data)
            }).then(x => {
                if (x.data.Ret > 0) {
                    
                    // 弹出消息，并关闭对话框
                    // --------------------
                    var msg = `完成${x.data.Data.SuccessList.length + x.data.Data.IgnoreList.length}条记录自动匹配，其中成功${x.data.Data.SuccessList.length}条`;
                    _this.$message.success(msg);
                    _this.status_showautorel = false;

                    // 刷新左侧
                    // --------
                    _this.func_refresh2();

                } else {
                    _this.$message.error(x.data.Msg);
                }
            }).catch(x => {
                console.error(x);
            });
        },

        // 点击 “开始关联”
        // --------------
        evt_autorelrel(arr, prop_fromitem, prop_frommodel) {
            
            // 操作确认
            // --------
            var _this = this;
            console.log(arr);
            console.log(prop_fromitem);
            console.log(prop_frommodel);
            _this.$confirm("即将自动关联当前方案下的清单项与当前模型构件，确认操作？", "操作确认").then(x => {
                _this.do_autorelrel(arr, prop_fromitem, prop_frommodel);
            }).catch(x => {

            });
        },

        // 根据某一个条目，向下取所有 children。从而取出所有 构件（bimapi格式）
        // ---------------------------------------------------------------
        func_recursivegeteles(item) {
            var _this = this;
            if (item.children && item.children.length) {

                // 取出每一条数据的，再把它们合并起来
                // -------------------------------
                var totalarr = [];
                for (var i = 0; i < item.children.length; i++) {
                    var tarr = _this.func_recursivegeteles(item.children[i]);
                    totalarr = totalarr.concat(tarr);
                    totalarr = Array.from(new Set(totalarr));
                }

                return totalarr;

            } else {

                // 仅取出当前数据的
                // ---------------
                if (item.objinfo.cmi_elementids) {
                    var arr = JSON.parse(item.objinfo.cmi_elementids);
                    var bimapiarr = _this.func_arrToElementArr(arr);
                    return bimapiarr;
                }
                return [];
            }
        },

        // 递归设置每条数据的 Eles 属性
        // --------------------------
        func_recursivesetEles(titem) {
            var _this = this;

            // 
            //titem.Eles = _this.func_recursivegeteles(titem);

            if (titem.children) {
                titem.Eles = _this.func_recursivegeteles(titem);
                for (var i = 0; i < titem.children.length; i++) {
                    _this.func_recursivesetEles(titem.children[i]);
                }
            } else {

                // 如果自己没有 children
                // --------------------
                var _cmi_elementids = titem.objinfo.cmi_elementids;
                var jsonarr = JSON.parse(_cmi_elementids);
                var bimjsonarr = _this.func_arrToElementArr(jsonarr);
                titem.Eles = bimjsonarr;

            }
        },

        // 查看已关联构件
        // -------------
        func_viewrelation_left() {
            var _this = this;

            // 拿到已关联的构件的数据，并调用bimviewer api 来高亮及 zoom
            // ------------------------------------------------------
            var data = _this.m_currentselected;
            var _cmi_elementids = data.objinfo.cmi_elementids;
            if (!_cmi_elementids || _cmi_elementids.trim() == '[]') {

                // 调用方法，得到数组
                // -----------------
                var children_elearr = _this.func_recursivegeteles(data);
                if (children_elearr.length) {
                    //_this.func_sethightli1ghtandzoom(bimjsonarr);

                    // 先确保模型已打开
                    // ---------------
                    if (!_this._specificmodelid) {
                        _this._specificmodelid = children_elearr[0].split('^')[0];
                    }
                    _this.func_makesureshowbimandhighlight(children_elearr);


                } else {
                    _this.$message.warning('当前条目及子条目没有关联构件');
                    return;
                }
                
            }

            var jsonarr = JSON.parse(_cmi_elementids);
            var bimjsonarr = _this.func_arrToElementArr(jsonarr);

            // 高亮 及 zoom
            // ------------
            if (bimjsonarr && bimjsonarr.length) {

                // 确保有 modelid 值
                // -----------------
                if (!_this._specificmodelid) {
                    _this._specificmodelid = bimjsonarr[0].split('^')[0];
                }

                _this.func_makesureshowbimandhighlight(bimjsonarr);
            }

        },

        // 高亮及 zoom
        // -----------
        func_sethightlightandzoom(modelid_eles) {

            // 拿到 iframe 对象
            // ---------------
            var _this = this;
            var ifrbimviewer = document.getElementById("id_iframe_bimviewer");

            // 高亮
            // ----
            var highlightobj = _this.$staticmethod.bimhelper_highlight(ifrbimviewer.contentWindow);
            _this.$staticmethod.bimhelper_callhighlight(highlightobj, modelid_eles);

            // 调用 zoom
            // ---------
            _this.$staticmethod.bimhelper_getzoomer(ifrbimviewer.contentWindow).zoomElementByElementId(modelid_eles);

        },

        func_arrToElementArr(arr) {
            var _this = this;
            var _arrret = [];
            for (var i = 0; i < arr.length; i++) {
                if (arr[i].modelid && arr[i].elementids 
                && arr[i].elementids.length) {
                    for (var j = 0; j < arr[i].elementids.length; j++) {
                        var _t = `${arr[i].modelid}^${arr[i].elementids[j]}`;
                        _arrret.push(_t);
                    }
                }
            }
            return _arrret;
        },

        // 获取是否“已选中的一些构件” 
        // ------------------------
        func_gethasselectedeles() {
            var _this = this;
            if (!_this.status_showbim) {
                return false;
            }
            return _this.m_selectedelements && _this.m_selectedelements.length;
        },

        // 访问接口，清空已关联构件
        // ----------------------
        do_clearelements() {
            var _this = this;
            var _url = `${window.bim_config.webserverurl}/api/CostMgrControllers/Cost/ClearElements`;
            _this.$axios({
                method: 'post',
                url: _url,
                data: _this.$qs.stringify({
                    Token: _this.$staticmethod.Get("Token"),
                    cmi_guid: _this.m_currentselected.id
                })
            }).then(x => {
                if (x.data.Ret > 0) {

                    // 定位那一条数据，然后把构件的字段改为[]
                    // -----------------------------------
                     _this.$message.success('操作成功');

                    var theitemToFind = _this.func_recursivefind(_this.m_tabledata, _this.m_currentselected.id);
                    if (theitemToFind) {

                        // 修改关联模型属性
                        // ---------------
                        theitemToFind.objinfo.cmi_elementids = undefined;
                        theitemToFind.objinfo.Model = undefined;    
                    }

                } else {
                    _this.$message.error(x.data.Msg);
                }
            }).catch(x => {
                console.error(x);
            })
        },

        // 清空已关联构件
        // -------------
        func_clearelements() {

            // 获取左侧已选中的数据
            // ------------------
            var _this = this;

            // 操作确认
            // --------
            _this.$confirm("确认移除该条目所关联的构件？", "操作确认").then(x => {
                _this.do_clearelements();
            }).catch(x => {

            });
        },

        // 计算右键菜单的样式
        // -----------------
        style_showorgetposition() {
            var _this = this;
            var _s = {};
            _s["display"] = _this.m_context_display;
            _s["left"] = _this.m_context_left + 'px';
            _s["top"] = _this.m_context_top + 'px';
            return _s;
        },

        // 关闭全部
        // --------
        func_closeall() {
            var _this = this;
            _this.m_context_display = 'none';
        },

        // 右键菜单
        // -------
        evt_rowContextmenu(row, column, event) {

            // 先设此行为选中行
            // ---------------
            var _this = this;
            event.preventDefault();
            _this.$refs.ref_table.setCurrentRow(row);
            
            _this.m_context_left = event.clientX;
            _this.m_context_top = event.clientY;

            // 根据当前屏幕确保位置不超出，并显示出来
            // -----------------------------------
            var _menuheight = 160;
            var _menuwidth = 140;
            _this.func_makesureInScreen(_menuheight, _menuwidth);
        },

        // 根据当前屏幕确保位置不超出，并显示出来
        // 显示之前，设定 m_viewrelation_left
        // -----------------------------------
        func_makesureInScreen(menuheight, menuwidth) {
            var _this = this;
            if (_this.m_context_top > document.body.clientHeight - menuheight) {
                _this.m_context_top = document.body.clientHeight - menuheight;
            }
            if (_this.m_context_top < 0) {
                _this.m_context_top = 0;
            }

            if (_this.m_context_left > document.body.clientWidth - menuwidth) {
                _this.m_context_left = document.body.clientWidth - menuwidth;
            }
            if (_this.m_context_left < 0) {
                _this.m_context_left = 0;
            }

            // 刷新表格树呼出按钮可用性
            // ----------------------
            _this.func_refreshleftbtnEnabled();

            _this.m_context_display = 'block';
        },

        // 刷新表格树呼出按钮可用性
        // ----------------------
        func_refreshleftbtnEnabled() {
            var _this = this;
            if (_this.status_showbim && _this.status_bimrenders) {
                // todo: 必须是叶子？
                _this.m_viewrelation_left = true;
            } else {
                _this.m_viewrelation_left = false;
            }
        },

        // 表格选中行改变
        // -------------
        evt_currentChange(data) {
            var _this = this;
            _this.m_currentselected = data;
            _this.func_closeall();

            _this.func_viewelements(data, null);
        },

        // 尝试显示 bim 页面
        // 如果不成功（一般是在外面的页面）
        // 则提示信息
        // ----------
        func_showbim() {
            var _this = this;
            _this.status_showbim = true;
        },

        // 尝试得到模型ID
        // -------------
        func_trygetmodelid() {



            // 取出所有“模型”节点
            // -----------------
            var _this = this;

            // 选中的节点
            // ---------
            var nodeid = _this.m_selectednode?_this.m_selectednode.id:undefined;

            if (!nodeid) {
                return undefined;
            }

            var _modelnodearr = [];
            for (var i = 0; i < _this.m_treedata.length; i++) {
                if (_this.m_treedata[i].children && _this.m_treedata[i].children.length) {
                    for (var j = 0; j < _this.m_treedata[i].children.length; j++) {
                        _modelnodearr.push(_this.m_treedata[i].children[j]);
                    }
                }
            }
            
            // 找出第一个 nodeid 与 id 或 children中id相同的数据
            // -----------------------------------------------
            var index = _modelnodearr.findIndex(x => x.id == nodeid || x.children.filter(y => y.id == nodeid).length > 0);
            if (index >= 0) {
                return _modelnodearr[index].id;
            } else {
                return undefined;
            }
        },

        func_switchbim(ev) {
            var _this = this;
            if (_this.status_showbim) {
                _this.status_bimrenders = false;
                _this.status_showbim = false;
            } else {

                // 在执行此之前，要判断 modelid 是否已经拿到了
                // ----------------------------------------
                if (!_this._hidetree) {

                    // 只要尝试点击打开模型，就再次从左侧子节点中尝试获取
                    // ----------------------------------------------
                    _this._specificmodelid = _this.func_trygetmodelid();

                    // 如果是只读页面，需要确保有 modelid
                    // --------------------------------
                    if (!_this._specificmodelid) {

                        // 提示
                        // ----
                        _this.$message.error('请先从选择选择模型或方案节点');
                        return;
                    }
                }


                _this.func_showbim();
                _this.m_bimloading = true;
            }
        },

        // 向 bimviewer 中添加菜单
        // ----------------------
        func_addbimmenu(win) {
            var _this = this;
            _this.m_bimmenus = [
                {
                    label:'追加关联到单个条目', callback:_this.func_appendrelation
                },
                {
                    label:'覆盖关联到单个条目', callback:_this.func_overriderelation
                },
                {
                    label:'查看已关联条目', callback:_this.func_showcounterpart
                },
                {
                    label:'从相关条目中移除', callback:_this.func_rmfromcounterpart
                }
            ];

            //win.B1IMe.control.BIMeUtility.addRightMenu(_this.m_bimmenus);
            _this.$staticmethod.bimhelper_addRightMenu(win, _this.m_bimmenus);

        },

        do_rmfromcounterpart(model_element_arr) {
            
            var _this = this;
            var _Token = _this.$staticmethod.Get("Token");
            var _cmp_guid = _this._specificCate.id;
            var _cmi_elementids = _this.func_elementarrToString(model_element_arr);
            var _url = `${window.bim_config.webserverurl}/api/CostMgrControllers/Cost/RemoveFromSelectedCostItem`;

            var _data = {
                Token: _Token,
                cmp_guid: _cmp_guid,
                cmi_elementids: _cmi_elementids
            };
            
            _this.$axios({
                url: _url,
                method: 'post',
                data: _this.$qs.stringify(_data)
            }).then(x => {
                if (x.data.Ret > 0) {
                    _this.$message.success('操作成功');
                    _this.func_refresh2();
                } else {
                    _this.$message.error(x.data.Msg);
                }
            }).catch(x => {
                console.error(x);
            });
        },

        // 从相关条目中移除
        // ---------------
        func_rmfromcounterpart(model_element_arr) {

            // 将 model_element_arr 这些构件从 _specificCate.id 这个方案下的所有条目中移除掉
            // -------------------------------------------------------------------------
            var _this = this;
            _this.$confirm("确定将这些构件从该方案所有条目中移除？", "操作确认").then(x => {
                _this.do_rmfromcounterpart(model_element_arr);
            }).catch(x => {

            });
        },

        // 查看相关的条目
        // -------------
        func_showcounterpart(model_element_arr) {

            // 调用接口，获取与这些构件相关的清单条目
            // -----------------------------------
            var _this = this;
            var _url = `${window.bim_config.webserverurl}/api/CostMgrControllers/Cost/GetCostItemByElements`;
            var _data = {
                cmp_guid: _this._specificCate.id,
                cmi_elementids: _this.func_elementarrToString(model_element_arr)
            };
            _this.$axios({
                url: _url,
                method: 'post',
                data: _this.$qs.stringify(_data)
            }).then(x => {
                if (x.data.Ret > 0) {
                    if (x.data.Data && x.data.Data.Item) {

                        // 自动选中这一条数据
                        // -----------------
                        console.log(x.data.Data.Item.cmi_guid);
                        _this.func_highlightrowbycmiguid(x.data.Data.Item.cmi_guid);
                    
                    } else {
                        _this.$message.warning(`未找到关联的条目`);
                    }
                } else {
                    _this.$message.error(x.data.Msg);
                }
            }).catch(x => {
                console.error(x);
            });
        },

        // cmvue.$refs.ref_table.setCurrentRow(cmvue.$refs.ref_table.data[2]);
        // 根据 cmi_guid 高亮表格的一行
        // 暂未支持自动展开
        // --------------
        func_highlightrowbycmiguid(cmi_guid) {
            var _this = this;
            var item = _this.func_recursivefind(_this.$refs.ref_table.data, cmi_guid);
            if (item) {
                _this.$message.success(`已找到关联的条目：${item.objinfo.cmi_name}【${item.objinfo.cmi_number}】`);
                _this.$refs.ref_table.setCurrentRow(item);
            }
        },

        // 将 bimviewer 返回的构件id数组格式转换为字符串格式
        // [{"modelid":"a27e2945-3277-4577-a029-379f9847cc07","elementids":["316960","317013"]}]
        // -------------------------------------------------------------------------------------
        func_elementarrToString(model_element_arr) {
            var _this = this;
            if (!model_element_arr || !model_element_arr.length) {
                return '[]';
            }
            var i = 0;
            var _themodeleleitem;
            var _theitem_modelid;
            var _theitem_elementid;
            var _repeatmodelidarr = [];
            for (i = 0; i < model_element_arr.length; i++) {

                // 一条 c6b4dbce-8fef-40b5-8e09-cc394b0343f5^466241
                // ------------------------------------------------
                _themodeleleitem = model_element_arr[i];

                var splitedstr = _themodeleleitem.split('^');
                if (splitedstr.length == 2) {
                    _theitem_modelid = splitedstr[0];
                    _theitem_elementid = splitedstr[1];

                    _repeatmodelidarr.push({
                        modelid: _theitem_modelid,
                        elementid: _theitem_elementid
                    });
                }
            }

            // 判断 _repeatmodelidarr 是否有元素
            // --------------------------------
            if (!_repeatmodelidarr.length) {
                return '[]';
            }

            // 构造成为非重复的
            // ---------------
            var puremodelelementarr = [];
            for (i = 0; i < _repeatmodelidarr.length; i++) {

                // 判断相同的 modelid 是否存在
                // --------------------------
                var index_samemodelid = puremodelelementarr.findIndex(x => x.modelid == _repeatmodelidarr[i].modelid);
                if (index_samemodelid >= 0) {
                    if (puremodelelementarr[index_samemodelid].elementids.indexOf(_repeatmodelidarr[i].elementid) < 0) {
                        puremodelelementarr[index_samemodelid].elementids.push(_repeatmodelidarr[i].elementid);
                    }
                } else {
                    puremodelelementarr.push({
                        modelid: _repeatmodelidarr[i].modelid,
                        elementids: [_repeatmodelidarr[i].elementid]
                    });
                }
            }
            return JSON.stringify(puremodelelementarr);
        },

        // 追加关联到单个条目
        // ----------------
        func_appendrelation(model_element_arr) {
            var _this = this;
            var _cmi_elementids = _this.func_elementarrToString(model_element_arr);
            var _cmi_guid = _this.m_currentselected.id;
            var _type = 'append';//override
            _this.func_writerelation(_cmi_elementids, _cmi_guid, _type);
        },

        // 从左侧操作：追加关联所选构件
        // -------------------------
        func_appendrelation_left() {
            var _this = this;
            if (!_this.func_gethasselectedeles()) {
                _this.$message.error('请先打开模型并选中构件');
                return;
            }
            var _cmi_elementids = _this.func_elementarrToString(_this.m_selectedelements);
            var _cmi_guid = _this.m_currentselected.id;
            var _type = 'append';//override
            _this.func_writerelation(_cmi_elementids, _cmi_guid, _type);
        },

        // 调用接口，写入关系
        // ----------------
        func_writerelation(elementidstr, cmi_guid, type) {
            var _this = this;
            var _Token = _this.$staticmethod.Get("Token");
            var _type = type;
            var _cmi_guid = cmi_guid;
            var _cmi_elementids = elementidstr;
            var _url = `${window.bim_config.webserverurl}/api/CostMgrControllers/Cost/WriteElements`;
            _this.$axios({
                url: _url,
                method: 'post',
                data: _this.$qs.stringify({
                    Token: _Token,
                    type: _type,
                    cmi_guid: _cmi_guid,
                    cmi_elementids: _cmi_elementids
                })
            }).then(x => {
                if (x.data.Ret > 0) {

                    // 关联成功，刷新列表
                    // -----------------
                    _this.$message.success('操作成功');

                    var theitemToFind = _this.func_recursivefind(_this.m_tabledata, _cmi_guid);
                    if (theitemToFind) {

                        // 修改关联模型属性
                        // ---------------
                        if (x.data.Data && x.data.Data.cmi_elementids) {
                            theitemToFind.objinfo.cmi_elementids = x.data.Data.cmi_elementids;
                        }
                        theitemToFind.objinfo.Model = {
                            ID: _this.init_specificmodelid,
                            Name: _this.init_specificmodelname
                        };    
                    }

                } else {
                    _this.$message.error(x.data.Msg);
                }
            }).catch(x => {
                console.error(x);
            })
        },

        // 递归从 m_tabledata 中查找指定 id 的条目，如果未找到返回 undefind
        // -------------------------------------------------------------
        func_recursivefind(arr, id) {

            // 遍历 arr
            // --------
            var _this = this;
            if (arr && arr.length) {
                for (var i = 0; i < arr.length; i++) {

                    // 如果它本身是，直接返回，否则遍历它的 children
                    // ------------------------------------------
                    if (arr[i].id == id) {
                        return arr[i];
                    } else {
                        var theitem = _this.func_recursivefind(arr[i].children, id);
                        if (theitem) {
                            return theitem;
                        }
                    }
                }
            } else {
                return undefined;
            }

        },

        // 覆盖关联到单个条目
        // -----------------
        func_overriderelation(model_element_arr) {
            var _this = this;
            var _cmi_elementids = _this.func_elementarrToString(model_element_arr);
            var _cmi_guid = _this.m_currentselected.id;
            var _type = 'override';
            _this.func_writerelation(_cmi_elementids, _cmi_guid, _type);
        },

        // 覆盖关联到单个条目（从左侧操作）
        // ---------------------------- 
        func_overriderelation_left() {
            var _this = this;
            if (!_this.func_gethasselectedeles()) {
                _this.$message.error('请先打开模型并选中构件');
                return;
            }
            var _cmi_elementids = _this.func_elementarrToString(_this.m_selectedelements);
            var _cmi_guid = _this.m_currentselected.id;
            var _type = 'override';
            _this.func_writerelation(_cmi_elementids, _cmi_guid, _type);
        },

        // 高亮一些构件（父子关联）
        // 高亮及 zoom 前确保模型页面已打开
        // ------------------------------
        func_makesureshowbimandhighlight(bimarr) {
            var _this = this;
            if (_this.status_showbim) {
                _this.func_sethightlightandzoom(bimarr);
            } else {

                // 先保存，然后直接打开模型
                // 打开模型加载完成后，先调用 func_sethi1ghtlightandzoom
                // 再重置这个属性
                // ----------------------
                _this.m_zoomafterrendered = _this.$staticmethod.DeepCopy(bimarr);
                _this.func_showbim();
            }
        },

        // 根据当前选中的条目
        // 若当前模型页面已打开，则直接zoom并高亮
        // 若当前模型页面未打开，则先打开模型，并zoom及高亮
        // --------------------------------------------
        func_zoomandhighlight() {
            
            // 如果当前没有选中的数据，则直接跳过
            // -------------------------------
            var _this = this;
            if (!_this.m_currentselected || !_this.m_currentselected.id) {
                console.warn('当前没有选中的数据(1269)');
                return;
            }

            // 如果当前选中的数据没有关联模型，则跳过
            // ----------------------------------
            var elejson = _this.m_currentselected.objinfo.cmi_elementids;
            if (!elejson) {
                console.warn('当前选中的数据没有关联模型(1277)');
                return;
            }

            // 先转换数据，再调用 bimapi
            // ------------------------
            var bimarr = _this.func_arrToElementArr(JSON.parse(elejson));

            // 先判断当前是在哪个页面
            // --------------------
            if (_this._hidetree) {

                // 如果模型已打开，则直接 zoom 并 高亮
                // ---------------------------------
                if (_this.status_showbim) {
                    _this.func_sethightlightandzoom(bimarr);
                } else {

                    // 先保存，然后直接打开模型
                    // 打开模型加载完成后，先调用 func_sethight1lightandzoom
                    // 再重置这个属性
                    // ----------------------
                    _this.m_zoomafterrendered = _this.$staticmethod.DeepCopy(bimarr);
                    _this.func_showbim();
                }
                
            } else {

                // 如果模型已打开，则直接 zoom 并 高亮
                // ---------------------------------
                if (_this.status_showbim) {
                    _this.func_sethightlightandzoom(bimarr);
                } else {

                    // 先保存，然后直接打开模型
                    // 打开模型加载完成后，先调用 func_sethightlightandzoom
                    // 再重置这个属性
                    // ----------------------
                    _this.m_zoomafterrendered = _this.$staticmethod.DeepCopy(bimarr);
                    
                    // 这里的问题就是，modelid 是多少？
                    // -----------------------------
                    _this._specificmodelid = _this.m_currentselected.objinfo.Model.ID;

                    _this.func_showbim();
                }

                
            }

            

        },

        // 注册 bimviewer 加载完成的事件处理
        // -------------------------------
        func_bimsubscribe() {
            var _this = this;
            var ifrbimviewer = document.getElementById("id_iframe_bimviewer");

            console.log('开始调用 bimhelper_finishrender');
            _this.$staticmethod.bimhelper_finishrender(ifrbimviewer.contentWindow, ()=>{

                console.log('调用了 finishrender');
              

                // 注册选择构件事件
                // ---------------
                _this.$staticmethod.bimhelper_onSelect(ifrbimviewer.contentWindow, elementid => {
                    //console.log(elementid);
                    _this.m_selectedelements = elementid;
                });

               

                // 注册取消选中构件事件
                // ------------------
                console.warn('cancleSelectEvent 可能暂无法生效');
                _this.$staticmethod.bimhelper_cancelSelect(ifrbimviewer.contentWindow, () => {
                        _this.m_selectedelements = [];
                });

             
                
                // 关闭多选后出现的对话框
                //ifrbimviewer.contentWindow.B1IMe.view.BIMeSelection.isShowMultipleSelection(false);
                // ------------------------------------------------------
                console.warn('isShowMultipleSelection 暂不支持');

                // 向 bimviewer 中添加菜单
                // ----------------------
                if (_this._hidetree) {
                    _this.func_addbimmenu(ifrbimviewer.contentWindow);
                }

               

                // 标识模型已加载完成
                // -----------------
                _this.status_bimrenders = true;

                // 刷新右键菜单按钮可用性
                // --------------------
                _this.func_refreshleftbtnEnabled();

                 //return;

                // 如果 m_zoomafterrendered 有值且.length
                // --------------------------------------
                if (_this.m_zoomafterrendered && _this.m_zoomafterrendered.length) {

                    var temparr = _this.$staticmethod.DeepCopy(_this.m_zoomafterrendered);

                    // 调用高亮并 zoom
                    // ---------------
                    _this.func_sethightlightandzoom(temparr);

                    _this.m_zoomafterrendered = [];
                }

            });
        },

        evt_bimviewerload() {
            var _this = this;
            console.log('bimviewer\'s iframe has loaded');

            // 先进行 finishRender 订阅，再关闭 loading
            // ---------------------------------------
            _this.func_bimsubscribe();

            _this.m_bimloading = false;
        },

        func_getbimcomposerId() {
            var _this = this;
            var bimcomposerId = _this.$staticmethod._Get("bimcomposerId");
            return bimcomposerId;
        },

        cfg_getBimConfig() {
            var _this = this
            return window.bim_config
        },

        func_refresh() {
            var _this = this;
            _this.func_gettabledata(_this.m_selectednode.id);
        },

        func_getcmpguid() {
            var _this = this;
            if (_this._hidetree) {
                return _this._specificCate.id;
            } else {
                return _this.m_selectednode?_this.m_selectednode.id:undefined;
            }
        },

        func_refresh2() {
            var _this = this;
            if (_this._hidetree) {
                _this.func_gettabledata(_this._specificCate.id);
            } else {
                _this.func_gettabledata(_this.m_selectednode.id);
            }
           
        },

        // 调用接口，导入数据
        // -----------------
        func_postFileToImport(file) {
            var _this = this;
            var _url = `${window.bim_config.webserverurl}/api/CostMgrControllers/Cost/ImportTreeTableData`;
            var fd = new FormData();
            fd.append("Token", _this.$staticmethod.Get("Token"));
            fd.append("cmp_guid", _this.m_selectednode.id);
            fd.append("F1", file);
            var config = {
                headers: {
                    "Content-Type": "multipart/form-data"
                }
            };
            _this.$axios.post(_url, fd, config).then(x => {
                if (x.data.Ret > 0) {
                    _this.$message.success('导入成功');
                    _this.func_refresh();
                } else {
                    _this.$message.error(x.data.Msg);
                }
            }).catch(x => {
                console.error(x);
            });
        },

        _stopPropagation(ev) {
            ev && ev.stopPropagation && ev.stopPropagation();
        },

        // 开始新增方案清单条目
        // ------------------
        func_begintoadd(ev) {
            var _this = this;
            _this._stopPropagation(ev);

            // 判断是否选中了左侧的分类
            // 如果 _specificCate 有值，则赋予 m_selectednode
            // ---------------------------------------------
            if (_this._hidetree && _this._specificCate) {
                _this.m_selectednode = _this.$staticmethod.DeepCopy(_this._specificCate);
                _this.m_selectednode.classname = 'icon-interface-component_cost _css-plan-icon';
            }
            if (!_this.m_selectednode) {
                _this.$message.error('未选择成本方案，请选择后重新操作');
                return;
            }
            if (_this.m_selectednode.classname.indexOf('_css-plan-icon') < 0) {
                _this.$message.error('选中的节点不是成本方案，请重新选择后继续操作');
                return;
            }

            // 初始化空值
            // ---------
            _this.m_editingitem = {
                children: [],
                id: '',
                objinfo: {
                    cmi_biminfo: "",
                    cmi_code: "",
                    cmi_count: '',
                    cmi_createtime: "",
                    cmi_elementids: '',
                    cmi_guid: "",
                    cmi_name: "",
                    cmi_number: "",
                    cmi_numberthislevel: '',
                    cmi_totalprice: '',
                    cmi_unit: "",
                    cmi_unitprice: '',
                    cmp_guid: _this._hidetree?_this._specificCate.id:_this.m_selectednode.id
                }
            };

            // 显示对话框
            // ---------
            _this.func_showdialogbyeditingitem();

        },

        // 显示新增或编辑清单条目的对话框，并根据 m_editingitem 的值来渲染表单上的数据
        // ----------------------------------------------------------------------
        func_showdialogbyeditingitem() {
            
            // 初始化字段值
            // -----------
            var _this = this;

            // 显示对话框
            // ---------
            _this.status_showitemeditor = true;
            
        },

        // 调用接口，移除数据（或及子数据）
        // ----------------------------
        do_begintodelete() {

            // 准备参数
            // --------
            var _this = this;
            //console.log(_this.m_currentselected.id);
            var cmp_guid = _this.func_getcmpguid();
            //console.log(cmp_guid);
            var _url = `${window.bim_config.webserverurl}/api/CostMgrControllers/Cost/DelCostItem`;
            var _para = {
                Token: _this.$staticmethod.Get("Token"),
                cmi_guid: _this.m_currentselected.id,
                cmp_guid: cmp_guid
            };
            _this.$axios({
                url: _url,
                method: 'post',
                data: _this.$qs.stringify(_para)
            }).then(x => {
                if (x.data.Ret > 0) {
                    _this.$message.success('删除成功');
                    _this.func_refresh2();
                } else {
                    _this.$message.error(x.data.Msg);
                }
            }).catch(x => {
                console.error(x);
            })
        },

        // 删除选中的数据
        // -------------
        func_begintodelete(ev) {

            // 操作确认
            // -------
            var _this = this;
            if (!_this.m_currentselected) {
                _this.$message.warning('未选择任何数据');
                return;
            }

            // 判断有没有 children
            // ------------------
            var confirmmsg;
            if (_this.m_currentselected.children && _this.m_currentselected.children.length) {
                confirmmsg = '确认移除选中的数据及其下所有子级数据？';
            } else {
                confirmmsg = '确认移除选中的数据？';
            }
            _this.$confirm(confirmmsg, "操作确认").then(x => {
                _this.do_begintodelete();
            }).catch(x => {

            });

        },

        // 开始编辑方案清单条目
        // ------------------
        func_begintoedit(ev) {
            var _this = this;
            _this._stopPropagation(ev);

            // 判断是否选中了左侧的分类
            // 如果 _specificCate 有值，则赋予 m_selectednode
            // ---------------------------------------------
            if (_this._hidetree && _this._specificCate) {
                _this.m_selectednode = _this.$staticmethod.DeepCopy(_this._specificCate);
                _this.m_selectednode.classname = 'icon-interface-component_cost _css-plan-icon';
            }
            if (!_this.m_selectednode) {
                _this.$message.error('未选择成本方案，请选择后重新操作');
                return;
            }
            if (_this.m_selectednode.classname.indexOf('_css-plan-icon') < 0) {
                _this.$message.error('选中的节点不是成本方案，请重新选择后继续操作');
                return;
            }

            // 从表格中拿到数据
            // ---------------
            if (!_this.m_currentselected) {
                _this.$message.warning('未选择任何数据');
                return;
            }

            // 如果是删除，且有子级的话，提示中也要加上相关文本
            // 深拷贝给 m_editingitem
            // ----------------------
            _this.m_editingitem = _this.$staticmethod.DeepCopy(_this.m_currentselected);

            // 显示对话框
            // ---------
            _this.func_showdialogbyeditingitem();
        },

        //
        func_begintoimport(ev) {
            var _this = this;
            _this._stopPropagation(ev);

            // 判断是否选中了左侧的分类
            // 如果 _specificCate 有值，则赋予 m_selectednode
            // ---------------------------------------------
            if (_this._hidetree && _this._specificCate) {
                _this.m_selectednode = _this.$staticmethod.DeepCopy(_this._specificCate);
                _this.m_selectednode.classname = 'icon-interface-component_cost _css-plan-icon';
            }
            if (!_this.m_selectednode) {
                _this.$message.error('未选择成本方案，请选择后重新操作');
                return;
            }
            if (_this.m_selectednode.classname.indexOf('_css-plan-icon') < 0) {
                _this.$message.error('选中的节点不是成本方案，请重新选择后继续操作');
                return;
            }

            var domFile = document.getElementById("id_import");
            domFile.value = "";
            try {
                domFile.click();
            } catch (e) {

            }
        },

        // 选择文件后点击确定
        // ----------------
        on_import_change() {

            // 先拿到选择的文件
            // ---------------
            var _this = this;
            var files = document.getElementById("id_import").files;
            if (files.length == 0) {
                _this.$message.error('请先选择文件');
                return;
            }

            // 判断文件扩展名
            // -------------
            var _file = files[0];
            var name = _file.name;
            var lastdot = name.lastIndexOf('.');
            var nameext = name.substr(lastdot);
            if (nameext.toLowerCase() != '.xlsx') {
                _this.$message.error('请选择xlsx文件后重试');
                return;
            }

            // 拿着 file 请求接口导入
            // ---------------------
            _this.func_postFileToImport(_file);

        },

        // 下载导入模板
        // -----------
        func_downloadimporttemplate(ev) {
            var _this = this;
            var urldownload = `${window.bim_config.webserverurl}/Content/Resource/Template/成本导入模板.xlsx`;
            var urldownload_en = encodeURI(urldownload);
            window.location.href = urldownload_en;
        },

        // 树节点点击
        // ---------
        evt_nodeclick(data, node, self) {
            var _this = this;
            if (data && data.id) {
                _this.m_selectednode = data;
                _this.func_gettabledata(data.id);

                // 点击后先关闭模型？
                // 点击后先尝试获取当前状态的选中节点的模型ID，如果与目前相同，则不作关闭再打开的操作
                // ---------------------------------------------------------------------------
                if (!_this._hidetree) {
                    var modelidtemp = _this.func_trygetmodelid();
                    if (_this.m_haslefttree_lastmodelid != modelidtemp) {

                        // 先看现在模型是否已经打开
                        // ----------------------
                        var old_showbim = _this.status_showbim;

                        // 先关闭
                        // ------
                        if (old_showbim) {
                            _this.status_showbim = false;
                            _this.status_bimrenders = false;
                        }

                        // 再修改
                        // ------
                        _this.m_haslefttree_lastmodelid = modelidtemp;
                        _this._specificmodelid = modelidtemp;

                        // 再打开
                        // ------
                        if (old_showbim) {
                            _this.func_showbim();
                        }
                        
                    }
                }
            }
        },

        // 根据是否选中了某一行，返回类字符串
        // -------------------------------
        tableRowClassName({ row, rowIndex }) {
            return 'css-tdunder';
        },

        // 调用接口，获取数据
        // ----------------
        func_gettabledata(cmp_guid) {

            var _this = this;
            var _cmp_guid = cmp_guid;
            if (!cmp_guid) {
                return;
            }
            var _url = `${window.bim_config.webserverurl}/api/CostMgrControllers/Cost/GetTreeTableData?cmp_guid=${_cmp_guid}`;
            _this.m_tableloading = true;
            _this.$axios.get(_url).then(x => {
                _this.m_tableloading = false;
                if (x.data.Ret > 0) {
                    var t_m_tabledata = _this.$staticmethod.DeepCopy(x.data.Data.List);
                    
                    // 处理 t_m_tabledata，设置其 已关联构件的属性值
                    // ------------------------------------------
                    for (var i = 0; i < t_m_tabledata.length; i++) {
                        //t_m_tabledata[i].Eles = _this.func_recursivegeteles(t_m_tabledata[i]);
                    
                        // 递归设置每条数据的 Eles 属性
                        // --------------------------
                        _this.func_recursivesetEles(t_m_tabledata[i]);
                    }

                    //debugger;

                    _this.m_tabledata = _this.$staticmethod.DeepCopy(t_m_tabledata); 
                    
                    _this.$nextTick(()=>{
                        _this.$refs.ref_table.doLayout();
                    });
                } else {
                    //_this.$message.error(x.data.Msg);
                    _this.m_tabledata = [];
                }
            }).catch(x => {
                _this.m_tableloading = false;
                console.error(x);
            });

        },
        func_gettreedata() {

            // 调用接口，获取模型成本方案数据
            // ----------------------------
            var _this = this;
            var _organizeId = _this.$staticmethod._Get("organizeId");
            var _bimcomposerId = _this.$staticmethod._Get("bimcomposerId");
            var _url = `${window.bim_config.webserverurl}/api/CostMgrControllers/Cost/GetTreeData?organizeId=${_organizeId}&bimcomposerId=${_bimcomposerId}`;
            _this.$axios.get(_url).then(x => {
                if (x.data.Ret > 0) {
                    if (x.data.Data.List) {
                        _this.m_treedata = _this.$staticmethod.DeepCopy(x.data.Data.List);
                    }
                } else {
                    _this.$message.error(x.data.Msg);
                }
            }).catch(x => {
                console.error(x);
            });
        }
    }
}
</script>
<style scoped>

._css-costitem._css-modellink {
    color: #1890FF;
    cursor: pointer;
}

._css-notallowouter{
  background-color: rgba(0,0,0,0.1);
}
._css-notallowinner{
  background-color: rgba(0,0,0,0);
}

._css-title-name {
    width: 52px;
    text-align: right;
}

._css-fieldvalue._css-textareavalue {
  height: auto;
  position: relative;
}

._css-textareaself {
    resize: none;
    height: 120px;
}

._css-model {
    text-align: center;
    width:100%;
    cursor:pointer;
}
._css-contextmenus {
    position: fixed;
    z-index: 1001;
    background-color: rgba(255,255,255,1);
    box-shadow: 0px 0px 8px 0px rgba(0,0,0,0.15);
    border-radius: 4px;
    width: 140px;
}
/* ._css-contextmenus:not(._css-noleft) {
    display: none !important;
} */

._css-contextmenu-i {
    height:40px;
    display: flex;
    align-items: center;
    color:rgba(0, 0, 0, 0.85);
    padding:0 8px 0 8px;
    box-sizing: border-box;
    cursor:pointer;
}
._css-contextmenu-i.css-dis {
    cursor:not-allowed;
}
._css-contextmenu-i:not(.css-dis):hover {
    background-color: #f5f5f5;
}
._css-bimviewer-ifr {
    width:100%;
    height:100%;
}
._css-bim-viewer {
    width:50%;
    height:100%;
}
._css-table-bim-ctn {
    height:calc(100% - 40px);
    display: flex;
}
._css-table-bim-ctn._css-noleft {
    height:calc(100% - 64px);
}
._css-btngroup2 {
    flex:1;
    display: flex;
    flex-direction: row-reverse;
    padding-right: 0px;
    align-items: center;
}
._css-btn-noicon {
    width:100%;
}
._css-btnimport._css-noleft {
    height: 34px;
    padding: 4px 8px 4px 8px;
    font-size: 14px;
    line-height: 26px;
    min-width: 64px;
    box-sizing: border-box;
    text-align: center;
    display: flex;
}
._css-btn-icon {
    display: flex;
    align-items: center;
    margin-right: 4px;
}
._css-numbercell {
    width: 100%;
    text-align: right;
}
#id_import {
    display: none;
}
._css-btnimport {
    font-size: 12px;
    color: #1890FF;
    border: 1px solid #1890FF;
    border-radius: 4px;
    padding: 4px 6px 4px 6px;
    margin-right: 12px;
    cursor: pointer;
}
._css-btnimport:not(._css-noleft):hover {
    color:#fff;
    background-color: #1890FF;
}

._css-btnimport._css-noleft:hover {
    background-color: rgba(24, 144, 255, 0.1);
}

._css-btngroup {
    display: flex;
    align-items: center;
}
._css-ccostmgr-tabtop {
    height: 40px;
    display: flex;
    flex-direction: row;
    padding-left: 12px;
    border:1px solid rgba(0, 0, 0, 0.05);
}
._css-ccostmgr-tabtop._css-noleft {
    height:64px;
}

._css-costitem {
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow-x: hidden;
    font-size: 14px;
    font-weight: 100;
}
._css-customstyle {
  height: calc(100% - 0px) !important;
}
._css-ccostmgr-treehead {
    line-height: 40px;
    text-align: left;
    padding-left: 8px;
    font-size: 14px;
    color: rgba(0,0,0,0.65);
    background-color: rgba(0,0,0,0.02);
}
._css-plan-icon {
  color: #faad14;
}
._css-ccostmgr-treebody {
  overflow-y: auto;
  height: calc(100% - 40px);
}
._css-ccostmgr-treehead {
  height: 40px;
}
._css-ccostmgr-righttreetab {
  flex: none;
  height: 100%;
  width: calc(100% - 260px);
  background-color: #fff;
  border-radius: 4px;
  margin-left: 8px;
}
._css-ccostmgr-righttreetab._css-noleft {
    width:100%;
}
._css-ccostmgr-lefttree {
  flex: none;
  height: 100%;
  width: 260px;
}
._css-ccostmgr-lefttree._css-noleft {
    display: none;
}
._css-compscostmgr-all {
  width: 100%;
  height: 100%;
  display: flex;
}
</style>