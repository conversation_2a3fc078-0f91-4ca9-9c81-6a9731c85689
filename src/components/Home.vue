<template>
  <!-- 项目内页整体 -->
  <el-container class="ht100">

    <!-- 消息模块 -->
    <!-- <zwebsocket-function
        :ref="'homevuews'"
        @onopen="ws_onopen"
        @onmessage="ws_onmessage"
        @onerror="ws_onerror"
        @onclose="ws_onclose"
        v-if="getWebsocketUrl()"
        :init_url="getWebsocketUrl()"
        :init_createobjOnMounted="true"
    ></zwebsocket-function> -->
    <!-- //消息模块 -->

    <router-view
      ref="bootOrProjectBoot"
      v-bind:_homedata="extdata"
      @set_extdata="_set_extdata"
      @set_extdata_collapse="_set_extdata_collapse"
      @set_extdata_hidecollapsebtn="_set_extdata_hidecollapsebtn"
      @set_projectboot_extdata="_set_projectboot_extdata"
      @onload="onhomeload"
    />
    <CompsInvite
    @oncancel="CompsInvite_oncancel"
    @onok="CompsInvite_onok"
    v-if="extdata.showinvite == true"></CompsInvite>

    <!-- 此组件为轮询获取消息组件 -->
    <!-- 当轮询到数据后，内部向外触发 regetplease，从而调用Home的 _regetplease -->
    <!-- _regetplease 中，调用了 refreshmsgcnt，Boot或ProjectBoot更新数字及消息数据 -->
    <!-- <CompsMessage
    @regetplease="_regetplease"
    ></CompsMessage> -->
    <!-- //此组件为轮询获取消息组件 -->
    <!-- idocview , dwg 预览 iframe 载体 -->
    <div class="_css-doc-preview" :class="extdata._show_idocview?'css-fc':'css-hide'">
      <div class="_css-doc-preview-beforeiframe">
        <div class="_css-doc-preview-beforeiframe-01"></div>
        <div class="_css-doc-preview-beforeiframe-02"></div>
        <div class="_css-doc-preview-beforeiframe-03"></div>

        <!-- 新标签打开按钮 -->
        <div
          :title="'在新标签页中查看'"
          class="icon-interface-attributes _css-docpreview-newtab _css-canfull"
          :class="{'_css-isfulling':m_docPreviewIsFull}"
          @click="func_openDocNewtab($event)"
        ></div><!-- //新标签打开按钮 -->

        <!-- 当前页全屏按钮 -->
        <div
          @click="func_switchfull($event)"
          :title="m_docPreviewIsFull?'取消全屏':'全屏'"
          :class="{'_css-docpreview-fullscreen':true
          , '_css-isfulling':m_docPreviewIsFull
          , '_css-canfull': true
          , 'icon-arrow-fullscreen_exit':m_docPreviewIsFull
          , 'icon-arrow-fullscreen':!m_docPreviewIsFull}"
        ></div><!-- //当前页全屏按钮 -->

        <!-- 关闭预览 -->
        <div
          :title="'关闭预览'"
          class="icon-suggested-close _css-canfull"
          :class="(m_docPreviewIsFull?'_css-isfulling':'')  + ' '+'_css-doc-preview-closebtn-' + extdata._docviewtype"
          @click="close_idocview($event)"
        ></div><!-- //关闭预览 -->

        <div class="_css-doc-preview-beforeiframe-04 __web-inspector-hide-shortcut__"></div>
      </div>
      <iframe class="_css-doc-preview-iframe"
              :class="{'_css-previewfull':m_docPreviewIsFull}"
              :src="extdata._idocviewurl"></iframe>
    </div>
  </el-container>
  <!-- //项目内页整体 -->
</template>
<script>
import CompsInvite from "@/components/CompsProject/CompsInvite"
import CompsUsersInput from "@/components/CompsAuth/CompsUsersInput"
// import CompsMessage from "@/components/CompsCommon/CompsMessage"
export default {
  data() {
    return {
      msgduration: 5000,
      extdata: {
        loadtype: '', // 通过 onhomeload 获取的值。可能为 'projectlist', 'projectdetail'
        loadtype_child: '', // 通过 onhomeload 获取的值，可能为 'document' 等
        shownametype: '', // 如果为：project。则显示各个项目名称，否则显示机构名称
        showname: '', // 显示的机构名称或项目名称
        isprojectmgr: false, // 当前人是进入的项目的项目管理员
        showinvite: false, // 是否显示“邀请”对话框
        showtopbtns: true, // 显示顶部按钮
        hidemsgalert: false, // 隐藏提醒图标
        hideinvite: false, // 隐藏邀请图标
        _IsFullScreen: false, // 当前处于全屏状态
        personalcenter_open: false, // 右上角的下拉操作按钮是否已展开
        tabindex: 1, // 0:单位主页 1：项目列表 2：系统管理 3：单位组织 4：项目管理
        currentusername: "", // 当前登录人的姓名
        hidecollapsebtn: 0,
        leftmenustate: 0,
        ellipsislogotext: true, // true为让logo长度过长时显示省略号，false为显示全部
        _show_idocview: false, // 显示 idocview
        _idocviewurl: "about:blank", // 在线预览文档地址
        _docviewtype: "document" // ''默认， 'office', 'dwg'
      },
      // 在线预览是否已全屏
      m_docPreviewIsFull: false,
    };
  },
  components:{
    CompsInvite,
    CompsUsersInput,
    // CompsMessage,
  },
  mounted() {
    var _this = this;
    window.homevue = this;
    // document.onclick = function() {
    //   _this.extdata.personalcenter_open = false;
    // };

    // 以 /Home 开头的路径页面，在此处赋予 姓名显示
    _this.extdata.currentusername = _this.$staticmethod.Get('RealName');

    // 重写 onmessage

    // 监听F11
    _this.func_fullchange();

    // let that = this
    // window.onresize = function(){
    //   if(!window.projectbootvue) return
    //   if (that.checkFull()) {
    //     window.projectbootvue.extdata._isfullscreen = true;
    //   } else {
    //     window.projectbootvue.extdata._isfullscreen = false;
    //   }
    // }

    if(window.bim_config.custom_hidebootbell) { // 和Boot.vue、ProjectBoot.vue中getConfigBell方法一致
      this.initSignalR()
    }

    document.addEventListener('click',this.onDocumentClick)
    window.addEventListener('resize',this.onWindowResize)
  },
  beforeDestroy() {
    this.disposeSignalR()
    window.removeEventListener('resize',this.onWindowResize)
    document.removeEventListener('click',this.onDocumentClick)
  },
  methods: {
    // 全屏切换
    func_switchfull(ev) {
      var _this = this;
      if (_this.m_docPreviewIsFull) {
        _this.m_docPreviewIsFull = false;
      } else {
        _this.m_docPreviewIsFull = true;
      }
    },

    // 在新标签页中打开在线预览
    func_openDocNewtab(ev) {
      var _this = this;
      window.open(_this.extdata._idocviewurl, '_blank');
    },
    // 关闭 idocview 在线预览
    close_idocview(ev) {
      ev.stopPropagation();
      var _this = this;
      _this.extdata._idocviewurl = "about:blank";
      _this.extdata._show_idocview = false;
    },
    // 开放给子页面的针对 projectboot.extdata 修改的方法
    _set_projectboot_extdata(prop, val) {
      var _this = this;
      _this.extdata[prop] = val;
    },
    // 初始化signalR
    initSignalR() {
      if (window.signalR) {
        const signalRUrl = window.bim_config.webserverurl.replace(/^\s*http(?:s)?:/ig,"") + "/chathub"
        const connection = new window.signalR.HubConnectionBuilder()
            .withUrl(signalRUrl)
            .configureLogging(signalR.LogLevel.Information)
            .build()
        if (connection) {
            this.bindEventsHandler(connection)
            try {
                connection
                    .start()
                    .then(() => {
                        const userId = localStorage.getItem("UserId")
                        // console.log("AddUser:UserId",userId)
                        connection.invoke("AddUser",userId).then(() => {
                          console.log("SignalR Connected")
                        }).catch((err) => {
                          console.log('捕捉到signalR错误',err);
                        })
                    })
                    .catch((err) => {
                        console.log("then-err", err)
                        this.unBindEventsHandler(connection)
                    })
            } catch (err) {
                console.log("catch-err", err)
                this.unBindEventsHandler(connection)
            }
        }
      }
    },
    // 给signalR绑定消息处理函数
    bindEventsHandler(connection) {
      connection.on("ReceiveMessage", this.onSignalrReceiveMsg)
    },
    // 给signalR解绑消息处理函数
    unBindEventsHandler(connection) {
      connection.off("ReceiveMessage", this.onSignalrReceiveMsg)
    },
    // 消息处理函数
    onSignalrReceiveMsg(...args) {
      if(args.length) {
        const data = JSON.parse(args[0])
        this.$staticmethod._Set("moduleFromSignalR",data.Module) // 将推送消息的Module保存到sessionStorage中

        /**
         * /api/User/Message/List接口
         * Module有效值: 站内信、问题、质量安全、进度、流程
         * Type有效值:-1全部、0查询、1新增、2更新、3删除
         * */
        this.$Bus.$emit("UpdateMsg", {Module: data.Module, Type: -1, HasRead: false, Msg: data})
        console.log("SignalR客户端收到消息:", data)
      }
    },
    // 释放signalR相关资源
    disposeSignalR() {

    },
    onWindowResize() {
      if(!window.projectbootvue) return
      if (this.checkFull()) {
        window.projectbootvue.extdata._isfullscreen = true;
      } else {
        window.projectbootvue.extdata._isfullscreen = false;
      }
    },
    onDocumentClick() {
      this.extdata.personalcenter_open = false;
    },
    checkFull(){
      var isFull = document.fullscreenElement && document.fullscreenElement.tagName == 'HTML';
      return isFull;
	  },
    // 触发全屏按钮的点击动作
    trig_fullbtn(){
      if (this.checkFull()) {
        projectbootvue.$staticmethod.ExitFullScreen();
      } else {
        projectbootvue.$staticmethod.FullScreen();
      }
    },
    // 进行 F11 的监听
    func_fullchange(){
      var _this = this;
      document.onkeydown = function(event){
        if(event.keyCode == 122) {
          event.cancelBubble = true;

          // 去触发按钮的点击
          _this.trig_fullbtn();
          return false;
        }
      }
    },

    // getWebsocketUrl(){
    //   if (window.bim_config.websocketurl) {
    //     return window.bim_config.websocketurl;
    //   } else {
    //     console.error('未配置 websocketurl');
    //     return null;
    //   }
    // },
    // ws_onopen(para){
    //   //debugger;
    //   //console.warn('ws_onopen called');
    // },
    // ws_onmessage(para){
    //   var _this = this;
    //   if (!para) {
    //     console.warn('para is undefined!');
    //     return;
    //   }
    //   if (!para.data) {
    //     console.warn('para.data is undefined!');
    //     return;
    //   }
    //   var dataobj;
    //   try {
    //     dataobj = JSON.parse(para.data);
    //   } catch (e) {
    //     console.warn('para.data cannot be parseJSON');
    //     return;
    //   }
    //   // 调试输出
    //   console.log('接收到消息：' + para.data);
    //   // Receiver 不存在，直接 return
    //   if (!dataobj || !dataobj.Receiver) {
    //     return;
    //   }

    //   // 如果 UserId 不等，跳过
    //   if ( dataobj.Receiver != 'all'
    //   &&
    //     _this.$staticmethod.Get("UserId").toUpperCase() != dataobj.Receiver.toUpperCase()
    //     ) {
    //     return;
    //   }

    //   // 弹出
    //   var title = dataobj.Title;
    //   var content = dataobj.Content;
    //   _this.$notify({
    //       title: title,
    //       message: content,
    //       duration: (_this.msgduration||5000)
    //   });

    //   // 弹出后刷新
    //   window.projectbootvue && window.projectbootvue.refreshmsgcnt && window.projectbootvue.refreshmsgcnt();

    // },
    // ws_onerror(para){
    //   console.warn('ws_onerror called');
    // },
    // ws_onclose(para){
    //   console.warn('ws_onclose called');
    // },
    // 告诉子组件，该获取消息数字了
    _regetplease(){
      var _this = this;
      _this.$refs.bootOrProjectBoot.refreshmsgcnt();
    },

    // 项目邀请人员对话框：确定
    CompsInvite_onok(defaultobj, objs){
      // 邀请对话框中，预设的角色选择判空
      var _this = this;
      // 找到所有选择了角色并输入了邮箱地址的数据，如果没有这样的数据，则提示。
      var arr_role_emails = [];
      // 判断预设是否进行了正确填写
      if (defaultobj.selectedId != "" && defaultobj.email != '') {
        arr_role_emails.push(defaultobj);
      }

      // 判断其它的是否进行了正确填写
      for (var i = 0; i < objs.length; i++) {
        if (objs[i].selectedId != "" && objs[i].email != '') {
          arr_role_emails.push(objs[i]);
        }
      }

      // 如果没有任何一项进行了正确填写，提示并跳出
      if (arr_role_emails.length == 0) {
        _this.$message({
          message: '请至少指定一个角色和一个邮箱地址',
          type: 'warning'
        });
        return;
      }

      // 对每一个邮箱地址进行正则验证
      var convertToServerModelArr = [];
      for (var i = 0; i < arr_role_emails.length; i++) {
        if (!_this.$staticmethod.validEmail(arr_role_emails[i].email)) {
          _this.$message({
            message: '邮箱地址不正确：【'+arr_role_emails[i].email+'】',
            type: 'warning'
          });
          return;
        } else {
          convertToServerModelArr.push({
            RoleId: arr_role_emails[i].selectedId,
            Email: arr_role_emails[i].email
          });
        }
      }

      // 将数据提交给接口，进行邀请。
      // var arr_role_emails_str = JSON.stringify(convertToServerModelArr);
      var _Token = _this.$staticmethod.Get("Token");
      _this.$axios({
        method: 'post',
        url: `${_this.$urlPool.InviteUsers}?token=${_Token}`,
        data: {
          RoleAndUsers: convertToServerModelArr,
          OrganizeId: _this.$staticmethod._Get("organizeId"),
        }
      }).then(x => {
          if (x.status != 200) {
              _this.$message({
                  message: '服务器错误',
                  type: 'error'
              });
              _this.$staticmethod.debug(x);
          } else if (x.data.Ret < 0) {
              _this.$message({
                  message: x.data.Msg,
                  type: 'error'
              });
              _this.$staticmethod.debug(x);
          } else {
            // // 邀请成功，弹出提示，关闭对话框
            // // 已成功给……发送邀请。// 已给xxx发送邀请，是否需要弹出个会自动关闭的对话框？
            _this.$message({
              message: '已成功发送邀请',
              type: 'success'
            });
            _this.extdata.showinvite = false;

          }
      }).catch(x => {
          _this.$message({
              message: '服务器错误',
              type: 'error'
          });
          _this.$staticmethod.debug(x);
      });
    },
    // 项目邀请人员对话框：取消
    CompsInvite_oncancel(){
      // 隐藏“邀请设置”对话框
      var _this = this;
      _this.extdata.showinvite = false;
    },
    onhomeload(type, isprojectmgr, ProjectObj){
      var _this = this;
      _this.extdata.loadtype = type;
      if (type == 'admin') {

        // 超级管理员页面，隐藏折叠按钮，公司名称全部显示，隐藏消息提醒图标
        _this.extdata.hidecollapsebtn = 1;
        _this.extdata.ellipsislogotext = false;
        _this.extdata.hidemsgalert = true;
        _this.extdata.hideinvite = true;
        // 隐藏顶部按钮
        _this.extdata.showtopbtns = false;
        _this.extdata.shownametype = '';
        _this.extdata.showname = '';
      } else if (type == 'projectdetail') {

        // 项目内页，显示折叠按钮，公司名称超出隐藏，显示消息提醒图标
        _this.extdata.hidecollapsebtn = 0;
        _this.extdata.ellipsislogotext = true;
        _this.extdata.hidemsgalert = false;
        _this.extdata.hideinvite = false;
        // 显示顶部按钮
        _this.extdata.showtopbtns = true;
        // 设置当前人是否为项目管理员，影响到 Home 页面上。
        _this.extdata.isprojectmgr = isprojectmgr;

        // 设置当前的项目名称。
        _this.extdata.shownametype = 'project';
        _this.extdata.showname = ProjectObj.FullName;

      } else if (type == 'projectlist') {
        // 项目列表页面，隐藏折叠按钮，公司名称全部显示，显示消息提醒图标
        _this.extdata.hidecollapsebtn = 1;
        _this.extdata.ellipsislogotext = false;
        _this.extdata.hidemsgalert = false;
        _this.extdata.hideinvite = true;
        // 显示顶部按钮
        _this.extdata.showtopbtns = true;
        _this.extdata.shownametype = '';
        _this.extdata.showname = '';
      } else if (type == 'organizemgr') {
        // 机构管理员页面，隐藏折叠按钮，公司名称全部显示，隐藏消息提醒图标
        _this.extdata.hidecollapsebtn = 1;
        _this.extdata.ellipsislogotext = false;
        _this.extdata.hidemsgalert = true;
        _this.extdata.hideinvite = true;

        // 隐藏顶部按钮
        _this.extdata.showtopbtns = false;
        _this.extdata.shownametype = '';
        _this.extdata.showname = '';
      }
    },
    // 跳转到项目列表
    jump_to_project_list(ev){
      var _this = this;
      var _token = _this.$staticmethod.Get("Token");
      ev.stopPropagation();
      if(_token && _token.length > 0){
        window.location.href = `${window.bim_config.hasRouterFile}/#/Home/Boot/${_token}`;
      }else{
        window.location.href = `${window.bim_config.hasRouterFile}/#/`;
      }

    },
    // 注销
    logout() {
      // 显示 loading 从 localSt1orage 中获取 To1ken，并提交给服务删除该 Tok1en ，成功后跳转到登录页，失败会仅隐藏 loading.
      var _this = this;
      let LoadingIns = _this.$loading({
        text: "执行中"
      });
      let _tval = _this.$staticmethod.Get("Token");
      _this
        .$axios({
          method: "post",
          url: `${_this.$urlPool.RemoveLogin}`,
          data: { Token: _tval }
        })
        .then(res => {
          _this.$staticmethod.Set("Token", "");
          window.location.href = `${window.bim_config.hasRouterFile}/#/`;
          LoadingIns.close();
        })
        .catch(res => {
          console.warn(res);
          _this.$message.error("服务器错误");
          _this.$staticmethod.Set("Token", "");
          window.location.href = `${window.bim_config.hasRouterFile}/#/`;
          LoadingIns.close();
        });
      // 显示 loading 从 local1Storage 中获取 To1ken，并提交给服务删除该 Tok1en ，成功后跳转到登录页，失败会仅隐藏 loading.
    },
    _set_extdata(prop, val) {
      var _this = this;
      _this.extdata[prop] = val;
    },
    _set_extdata_hidecollapsebtn(val) {
      var _this = this;
      _this.extdata.hidecollapsebtn = val;
    },
    // 0:展开   1：收起
    _set_extdata_collapse(val) {
      var _this = this;
      _this.extdata.leftmenustate = val;
    },
  }
};
</script>
<style >
/* 右上角小按钮 */
._css-bell-outer{
  width:32px;
  height:32px;
  justify-content: space-around;
  margin-right: 14px;
  cursor:pointer;
  border-radius: 2px;
}

._css-invite-outer{
  width:32px;
  height:32px;
  justify-content: space-around;
  border-radius: 50%;
  background-color: rgba(0, 0, 0, 0.02);
  margin-right: 24px;
  cursor:pointer;
}

._css-full-outer{
  width:32px;
  height:32px;
  justify-content: space-around;
  margin-right: 14px;
  cursor:pointer;
  border-radius: 2px;
}

._css-full-outer:hover, ._css-bell-outer:hover{
  background-color: rgba(0, 0, 0, 0.02);
}

._css-menui {
  color: rgba(255, 255, 255, 0.65);
  cursor: pointer;
}
._css-menui:hover .item-content{
  background: rgba(255,255,255,0.2);
  border-radius: 8px;
}
._css-menui.clicked .item-content{
  color: #007AFF;
  background-color: #FFFFFF;
  border-radius: 8px;
}
._css-menui.css-collapsing{
  color: rgba(255, 255, 255, 1);
  background: #0D1640;
}
.iconlevel-1{
  height:44px;
  padding: 0 8px;
  box-sizing: border-box;
}
._css-menui.iconlevel-2, ._css-menui.iconlevel-3 {
  background: #0D1640;
  color: #DDDDDD;
  cursor: pointer;
  height:40px;
  padding: 4px 10px;
  box-sizing: border-box;
}
._css-menui.iconlevel-3{
  padding: 4px 15px;
}
._css-menui.iconlevel-2:hover .item-content,._css-menui.iconlevel-2:hover .item-content{
  background: rgba(255,255,255,0.2);
  color: rgba(255, 255, 255, 1);
  border-radius: 8px;
}
._css-menui.iconlevel-3 ._css-menu-text{
  margin-left: 20px;
}
._css-menui.iconlevel-2.clicked .item-content,._css-menui.iconlevel-3.clicked .item-content{
  color: #007AFF;
  background-color: #fff;
  border-radius: 8px;
}
._css-personactlist {
  z-index: 1;
  top: 100%;
  right: 0;
  width: 120px;
  color: rgba(0, 0, 0, 0.65);
  background: rgba(255, 255, 255, 1);
  box-shadow: 0px 0px 8px 0px rgba(0, 0, 0, 0.15);
  border-radius: 4px;
}
._css-personactlist_none {
  display: none;
}
._css-personalacti:hover {
  background-color: rgba(230, 247, 255, 1);
}
/* 左侧菜单 */
._css-leftmenu {
  z-index: 0;
  color: rgba(255, 255, 255, 0.65);
}
/* //左侧菜单 */

/* 项目内页页面顶部高度 */
.css-main-header {
  height: 64px !important;
}

._css-page-body {
  height: calc(100% - 0px) !important;
}

._css-toptab {
  color: #000;
  font-size: 12px;
  box-sizing: border-box;
  width: 56px;
  height: 22px;
  margin-left: 36px;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-around;
  cursor: pointer;
}

._css-toptab ._css-toptabin {
  box-sizing: border-box;
  position: absolute;
  margin: auto;
  width: 76px;
  height: 24px;
  border-radius: 2px;
  cursor: pointer;
  border: 1px solid transparent;
}

._css-toptab.clicked ._css-toptabin,
._css-toptab:hover ._css-toptabin {
  background-color: #282E3D;
  color: rgba(255, 255, 255, 0.85);
}

._css-head-icon {
  background-color: black;
  border-radius: 50%;
  color: #fff;
}
/* //项目内页页面顶部高度 */

/* 折叠左边主菜单时，上半部分的样式过渡 */
._css-left-state_0 {
  transition: all 0.2s;
  width: 200px;
  background-image: url('../assets/images/menu-bg.png');
  background-position: left top;
  background-size: 100% 100%;
}
._css-right-state_0 {
  transition: all 0.2s;
  width: calc(100% - 200px);
}
._css-left-state_1 {
  transition: all 0.2s;
  width: 64px;
}
._css-right-state_1 {
  transition: all 0.2s;
  width: calc(100% - 64px);
}

._css-logo {
  position: absolute;
}

._css-logotext_0 {
  transition: margin-left 0.2s;
  margin-left: 44px;
}

._css-logotext_1 {
  transition: margin-left 0.2s;
  margin-left: 98px;
}

/* 显示折叠左侧按钮的情况 */
._css-logo-text_ellipsis {
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}
/* //显示折叠左侧按钮的情况 */

/* 不显示折叠左侧按钮的情况 */
._css-logo-text_allvisible {
  width:156px;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow-x: hidden;
}
/* //不显示折叠左侧按钮的情况 */

._css-left-state_1 ._css-logo-text {
  transition: margin-left 0.2s;
  margin-left: 100px;
}
/* //折叠左边主菜单时，上半部分的样式过渡 */
</style>
<style scoped>
._css-doc-preview {
  z-index: 70000;
  position: fixed;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  background-color: rgba(0, 0, 0, 0.45);
  opacity: 1;
  justify-content: space-around;
}
._css-doc-preview-iframe {
  height: 80%;
  width: 80%;
  border-width: 0;
  background-color: #fff;
}
._css-doc-preview-iframe._css-previewfull {
  height: 100%;
  width: 100%;
}
._css-docpreview-newtab {
  font-size: 20px;
  flex: none;
  width: 30px;
  height: 30px;
  position: fixed;
  background-repeat: no-repeat;
  cursor: pointer;
  color: rgba(0, 0, 0, 0.4);
  border: 1px solid transparent;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.8);
  right: calc(10% + 55px);
  top: calc(10% - 15px);
  line-height: 30px;
}

._css-docpreview-fullscreen {
  font-size: 20px;
  flex: none;
  width: 30px;
  height: 30px;
  position: fixed;
  background-repeat: no-repeat;
  cursor: pointer;
  color: rgba(0, 0, 0, 0.4);
  border: 1px solid transparent;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.8);
  right: calc(10% + 20px);
  top: calc(10% - 15px);
  line-height: 30px;
}

._css-doc-preview-closebtn-,
._css-doc-preview-closebtn-office {
  font-size: 20px;
  flex: none;
  width: 30px;
  height: 30px;
  position: fixed;
  background-repeat: no-repeat;
  cursor: pointer;
  color: rgba(0, 0, 0, 0.4);
  border: 1px solid transparent;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.8);
  right: calc(10% - 15px);
  top: calc(10% - 15px);
  line-height: 30px;
}

._css-doc-preview-closebtn-dwg {
  font-size: 20px;
  flex: none;
  width: 30px;
  height: 30px;
  position: fixed;
  background-image: url(/Content/images/ProjectManage/Project/close_normal.png);
  background-repeat: no-repeat;
  cursor: pointer;
  color: rgba(0, 0, 0, 0.4);
  border: 1px solid transparent;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.8);
  right: calc(10% - 15px);
  top: calc(10% - 15px);
  line-height: 30px;
}
._css-canfull._css-isfulling {
  top:0;
  right: 0px;
}
._css-docpreview-newtab._css-isfulling {
  right: 68px;
}
._css-docpreview-fullscreen._css-isfulling {
  right: 34px;
}
._css-doc-preview-beforeiframe {
  position: fixed;
  width: 30px;
  height: 40px;
  top: 0;
  right: 35px;
  background-color: transparent;
  display: flex;
  align-items: center;
  font-family: "微软雅黑";
}
._css-doc-preview-beforeiframe-01 {
  flex: none;
  width: 20px;
}
._css-doc-preview-beforeiframe-02 {
  flex: none;
  font-size: 20px;
  font-weight: normal;
  font-stretch: normal;
  letter-spacing: 0px;
  color: #ffffff;
}
._css-doc-preview-beforeiframe-03 {
  flex: 1;
  height: 100%;
}
._css-doc-preview-beforeiframe-04 {
  flex: none;
  width: 25px;
  height: 100%;
}
</style>
