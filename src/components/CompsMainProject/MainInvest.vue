<template>
	<div class="main-project"  :class="isScreenParams ? 'main-max-width': 'main-min-width'" >
		
        <div class="bg-white module-progress">
            <div class="progress-title">
                <div class="text">进度里程碑</div>
                <div class="legend">
                    <el-select
                        v-model="prograssValue"
                        @change="handelChangeProgress"
                        placeholder="请选择进度方案"
                    >
                        <el-option
                            v-for="(item, index) in progressListData"
                            :label="item.NAME_"
                            :value="item.bop_planId"
                            :key="index"
                        ></el-option>
                    </el-select>
                    <p class="legend-color"><i class="bgColorOK"></i>已完成</p>
                    <p class="legend-color"><i class="bgColoring"></i>进行中</p>
                    <p class="legend-color"><i class="bgColorNoStart"></i>未开始</p>
                </div>
            </div>
            <div class="critical-route-list">
                <div class="list" v-for="item in criticalRouteList" :key="item.Id">
                    <div class="time" :class="getBgColor(item.TaskState)"><i class="icon"></i>{{ item.TaskEndDate }}</div>
                    <div class="text"  v-if="item.TaskName.length <= 12">{{ item.TaskName }}</div>
                    <el-tooltip class="item" v-if="item.TaskName.length > 12" effect="dark" :content="item.TaskName" placement="top-start">
                        <div class="text overflow-point">{{ item.TaskName }}</div>
                    </el-tooltip>
                </div>
            </div>
        </div>
        <div class="invest-bottom">
            <div class="bg-white module-quality quality-left" v-if="sizeShow">
                <v-chart id="pieChartQuality" ref="pieChartQuality" style="width:100%;height:100%;" :options="QualityManageOption"/>
            </div>
            <div class="bg-white module-quality quality-left quality-center" v-if="sizeShow">
                <v-chart id="pieChartSafety" ref="pieChartSafety" style="width:100%;height:100%;" :options="QualitySafetyOption"/>
            </div>
            <div class="bg-white module-quality quality-right" v-if="sizeShow">
                <v-chart id="barChart"  ref="qualityAcceptance" style="width:100%;height:100%;" :options="qualityAcceptanceBar"/>
            </div>
        </div>
	</div>
</template>
<script>
import ECharts from 'echarts';
export default {
	name: "MainInvest",
	data() {
		return {
            prograssValue: '',
			progressListData:[],
            criticalRouteList:[],
            pieOption: {
                
                color: ['#1890ff','#FFD500'],
                title: {
                    left: 'left'
                },
                tooltip: {
                    trigger: 'item', // 设置触发类型为饼图项
                    formatter: '{b} : {c} ' // 设置提示框格式，{b} 表示数据项名称，{c} 表示数据项值，{d} 表示百分比
                },
                legend: {
                    right: 'right',
                    orient: 'vertical',
                    itemWidth: 12,
                    itemHeight: 12,
                },
                grid: {
                    left: '3%',
                    right: '4%',
                    top: '10%',
                    containLabel: true
                },
                series: [
                    {
                        type: 'pie',
                        roseType: 'radius',
                        radius: [30, 60],
                        center: ['50%', '50%'],
                        itemStyle: {
                            borderRadius: 0
                        },
                        hoverOffset: 0,
                        labelLine: {
                            show: false,
                        },
                        label: {
                            show: false
                        },
                        emphasis: {
                            label: {
                            show: false
                            }
                        },
                        data: []
                    },
                    
                ]
            },
            QualityManageOption:{},
            QualitySafetyOption: {},
            qualityAcceptanceBar:{
                title: {
                    text: '质量验收',
                    left: 'left'
                },
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                    type: 'shadow'
                    }
                },
                legend: {
                    left: 'right',
                    itemWidth: 12,
                    itemHeight: 12,
                },
                
                grid: {
                    left: '3%',
                    right: '4%',
                    bottom: '3%',
                    containLabel: true
                },
                xAxis: [{
					type: "category",
					axisLine: {
						show: false,
						lineStyle: {
							color: "rgba(54, 133, 244, 0.3);",
						},
					},
					axisTick: {
						lineStyle: {
							color: "transparent",
						},
                        interval: 10, 
                        length: 5,     
					},
                    
					nameTextStyle: {
						borderWidth: 30,
					},
					data: [],
				}],
				yAxis: [{
					type: "value",
					axisLabel: {
						color: "rgba(102, 102, 102, 1)",
						margin: 15,
					},
                    minInterval:1,
					splitLine: {
						lineStyle: {
							color: "rgba(54, 133, 244, 0.3)",
						},
					},
					axisLine: {
						show: false,
					},
					axisTick: {
						lineStyle: {
							color: "transparent",
						},
					},
				}],
                series: [
                    {
                        name: '总量',
                        type: 'line',
                        emphasis: {
                            focus: 'series'
                        },
                        itemStyle: {
                            normal: {
                                color: 'rgba(255, 117, 25, 1)',
                            },
                        },
                        data: []
                    },
                    {
                        name: '已验收',
                        type: 'bar',
                        stack: 'Ad',
                        emphasis: {
                            focus: 'series'
                        },
                        barWidth: 40,
                        itemStyle: {
                            normal: {
                                color: new this.$echarts.graphic.LinearGradient(
                                    0,
                                    0,
                                    0,
                                    1,
                                    [
                                        { offset: 0, color: "rgba(51, 169, 255, 1)" },
                                        { offset: 1, color: "rgba(204, 234, 255, 1)" },
                                    ]
                                ),
                            },
                        },
                        data: []
                    },
                    {
                        name: '优良',
                        type: 'bar',
                        stack: 'Ad',
                        emphasis: {
                            focus: 'series'
                        },
                        barWidth: 40,
                        itemStyle: {
                            normal: {
                                color: new this.$echarts.graphic.LinearGradient(
                                    0,
                                    0,
                                    0,
                                    1,
                                    [
                                        { offset: 0, color: "rgba(255, 213, 0, 1)" },
                                        { offset: 1, color: "rgba(255, 242, 179, 1)" },
                                    ]
                                ),
                            },
                        },
                        data: []
                    },
                ]
            },
            sizeShow: true,
		};
	},
	props: {
		currentisProjectManager: {
			type: Boolean,
			required: false,
		},
        isScreenParams:{
            type: Boolean,
			required: false,
        }
	},
    filters:{
        
    },
	created() {},
	mounted() {
        this.getProgressTreeData() 
        this.getquanlitylist();
        this.getPieOption()
         window.addEventListener('resize', this.handleResize);
	},
	methods: {
       
        // 进度数据
        getProgressTreeData(){
            this.$axios.get(`${this.$MgrBaseUrl.planGetList}?organizeId=${this.$staticmethod._Get("organizeId")}&Token=${this.$staticmethod.Get('Token')}`).then(res=>{
                if(res.data.Data.length > 0){
                    this.progressListData = res.data.Data;
                    this.prograssValue = res.data.Data[0].NAME_;
                    this.getProgressRoute(res.data.Data[0].bop_planId)
                }
            })
        },
        async getProgressRoute(value){
            let params = {
                organizeId: this.$staticmethod._Get("organizeId"),
                projectUID: value,
            }
            const res = await this.$api.getGetMilestones(params)

            this.criticalRouteList = res.Data
        },
        // 质量验收柱状图
        async getquanlitylist(value){
            let params = {
                organizeId: this.$staticmethod._Get("organizeId"),
                projectUID: value,
            }
            const res = await this.$api.getquanlitylist(params)
            // this. = res.Data;
            let name = res.Data.map(item=>item.ItemName) 
            let total = res.Data.map(item=>item.Total) 
            let verified = res.Data.map(item=>item.VerifiedNumber) 
            let excellent = res.Data.map(item=>item.ExcellentNumber) 
            
            this.qualityAcceptanceBar.xAxis[0].data = name;
            this.qualityAcceptanceBar.series[0].data = total;
            this.qualityAcceptanceBar.series[1].data = verified;
            this.qualityAcceptanceBar.series[2].data = excellent;
            setTimeout(()=>{
                this.renderChart('barChart',this.qualityAcceptanceBar)
            },500);
        },
        // 质量、安全、俩饼图
        async getPieOption(){
            let params = {
                OrganizeId: this.$staticmethod._Get("organizeId"),
                LinkType: 1
            }
            const res = await this.$api.getGetMissions(params);
            let qualityList = res.Data.List;
            let check = qualityList.filter(item=>item.ExamineResult == 'A_ToBeCheck').length; // 待检查
            let rectified = qualityList.filter(item=>item.ExamineResult == 'B_ToBeRectified').length; // 待整改

            

            this.QualityManageOption = this.$staticmethod.DeepCopy(this.pieOption);
            this.QualityManageOption.title.text = "质量巡检";
            if(check == 0 && rectified == 0){
                this.QualityManageOption.series[0].roseType = false;
            }
            this.QualityManageOption.series[0].data = [
                { value: check, name: '待检查', },
                { value: rectified, name: '待整改' },
            ]
           

            let params1 = {
                OrganizeId: this.$staticmethod._Get("organizeId"),
                LinkType: 2
            }
            const res1 = await this.$api.getGetMissions(params1);
            let qualityList1 = res1.Data.List;
            let check1 = qualityList1.filter(item=>item.ExamineResult == 'A_ToBeCheck').length; // 待检查
            let rectified1 = qualityList1.filter(item=>item.ExamineResult == 'B_ToBeRectified').length; // 待整改
            this.QualitySafetyOption = this.$staticmethod.DeepCopy(this.pieOption);
            this.QualitySafetyOption.title.text = "安全巡检";
            if(check1 == 0 && rectified1 == 0){
                this.QualitySafetyOption.series[0].roseType = false;
            }
            this.QualitySafetyOption.series[0].data = [
                { value: check1, name: '待检查', },
                { value: rectified1, name: '待整改' },
            ]
            setTimeout(()=>{
                this.renderChart('pieChartQuality',this.QualityManageOption)
                this.renderChart('pieChartSafety',this.QualitySafetyOption)
            },1000);
        },
        renderChart(id,option) { 
            let chart = ECharts.init(document.getElementById(id));
            chart.setOption(option);
        },
        handelChangeProgress(val){
            this.getProgressRoute(val)
        },
        getBgColor(type){
            return type === 0 ? 't-bgColorNoStart' : type === 1 ? 't-bgColoring' : type === 2 ? 't-bgColorOK' : 'bgColorNoStart';
        },
        handleResize() {
            this.sizeShow = false
            setTimeout(()=>{
                this.sizeShow = true
            },50)
        }
    },
    destroyed() {
        window.removeEventListener('resize', this.handleResize);
    },
};
</script>
<style lang="scss" scoped>
.main-project{
    height: 100%;
    display: flex;
    flex-direction: column;
}
.module-type{
    border-radius: 8px;
    padding: 16px 20px;
}
.module-progress{
    border-radius: 8px;
    padding: 8px 20px 16px 20px;
    .bgColorOK{
        background: #BFDFFF;
    }
    .bgColorNoStart{
        background: #E6EBF2;
    }
    .bgColoring{
        background: #FFEE99;
    }
    .t-bgColorOK{
        background: linear-gradient(90deg, rgba(191,223,255,0) 0%, #BFDFFF 100%);
        border-radius: 2px;
    }
    .t-bgColorNoStart{
        background: linear-gradient(90deg, rgba(230,235,242,0) 0%, #E6EBF2 100%);
        border-radius: 2px;
    }
    .t-bgColoring{
        background: linear-gradient(90deg, rgba(255,244,191,0) 0%, #FFEE99 100%);
        border-radius: 2px;
    }
}
.module-invest{
    display: flex;
    // height: 90px;
}

.big-size{
    .invest-icon{
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        width: 78px;
        height: 82px;
        background: #F6FAFE;
        border-radius: 4px;
        .icon-bg{
            width: 32px;
            height: 32px;
        }
    }
}
.small-size{
    display: flex;
    // flex-direction: column;
    .invest-list{
        display: flex; 
       flex-direction: column;
        .invest-icon{
            display: flex;
            align-items: center;
            // justify-content: space-between;
            .icon-bg{
                width: 20px;
                height: 20px;
            }
            .title{
                margin-left: 8px;
            }
        }
        .content{
            margin: 0;
            .content-text{
                .number{
                    line-height: 26px;
                }
            }
        }
    }
}
.invest-list{
    display: flex;
    flex: 1;
    .invest-icon{
        .icon-bg{
            background-image: url('../../assets/images/m-invest1.png');
            background-size: 100%;
            background-repeat: no-repeat;
        }
        .icon1{
            background-image: url('../../assets/images/m-invest1.png');
        }
        .icon2{
            background-image: url('../../assets/images/m-invest2.png');
        }
        .icon3{
            background-image: url('../../assets/images/m-invest3.png');
        }
        .icon4{
            background-image: url('../../assets/images/m-invest4.png');
        }
        .title{
            font-size: 16px;
            font-family: PingFangSC, PingFang SC;
            font-weight: 400;
            color: rgba(7,28,72,0.9);
            line-height: 32px;
        }
    }
    .content{
        margin-left: 10px;
        .content-text{
            .text{
                font-size: 14px;
                font-family: PingFangSC, PingFang SC;
                font-weight: 500;
                color: #222222;
                margin-right: 8px;
            }
            .number{
                font-size: 18px;
                font-family: PingFangSC, PingFang SC;
                font-weight: 500;
                color: #1890FF;
                line-height: 36px;
                i{
                    padding-left: 2px;
                    font-size: 12px;
                    font-style: initial;
                }
            }
        }
    }
}

.progress-title{
    margin-bottom: 10px;
    line-height: 40px;
    display: flex;
    justify-content: space-between;
    .legend{
        display: flex;
        align-items: center;
        // justify-content: space-between;
    }
    .legend-color{
        margin-left: 14px;
        i{
            margin-right: 8px;
            display: inline-block;
            width: 10px;
            height: 10px;
            border: 1px solid #FFFFFF;
        }
    }
}
.critical-route-list{
    display: flex;
    // justify-content: space-between;
    overflow-x: auto;
    min-height: 86px;
    .list{
        margin-right: 10px;
        width: 238px;
        height: 86px;
        background: #F6FAFE;
        box-shadow: 0px 2px 4px 0px rgba(0,0,0,0.08);
        border-radius: 4px;
        flex: 0 0 238px;
        .time{
            margin: 8px 8px 4px;
            height: 36px;
            line-height: 36px;
            display: flex;
            align-items: center;
            i{
                display: inline-block;
                width: 20px;
                height: 20px;
                background-size: 100%;
                background-image: url('../../assets/images/m-biaoji.png');
                background-repeat: no-repeat;
                margin-right: 8px;
            }
        }
        .text{
            margin-left: 20px;
            line-height: 28px;
            color: #071C48;
        }
    }
}
/deep/ .el-input__inner{
    border-width: 1px;
    line-height: 32px;
    height: 32px;
}
.invest-bottom{
    display: flex;
    flex:1;
    
    .module-quality{
        border-radius: 8px;
        width: 220px;
        min-height: 220px;
        // height: 220px;
        padding-top: 10px;
        background: #fff;
    }
    
    .quality-center{
        margin: 0 12px;
    }
    .quality-right{
        flex: 1;
    }
}
.main-max-width{
    .invest-bottom{
       margin-top: 20px;
    }
    .module-quality.quality-left{
        border-radius: 8px;
        width: 290px;
        min-height: 220px;
        // height: 220px;
        padding-top: 10px;
        background: #fff;
    }
}
.main-min-width{
    .invest-bottom{
        margin-top: 14px;
    }
    .module-quality.quality-left{
        border-radius: 8px;
        width: 200px;
        min-height: 220px;
        // height: 220px;
        padding-top: 10px;
        background: #fff;
    }
}
</style>