<template>
	<div class="main-project"  :class="isScreenParams ? 'main-max-width': 'main-min-width'" >
		<div class="item-top-bar">
			<span class="title">基本信息</span>
			<div class="top-bar-option" v-if="currentisProjectManager">
				<span class="modify-btn" @click="toggleEditState(projectInformation)"> 编辑 </span>
			</div>
		</div>
		<div class="project-info-content">
			<div class="info-top margin-top-15 margin-bottom-20">
                <div class="content-msg">
                    <div class="project-info-img">
                        <img :src="projectInformation.Thumbnail" alt="" />
                    </div>
                    <div class="info-content-title-msg">
                        <div class="project-little css-fs14">{{ projectInformation.ProjectName }}</div>
                        <div class="msg-bottom overflow-point">
                            <span class="color666">创建时间：</span>
                            {{ projectInformation.CreateTime | filterTime }}
                        </div>
                        <div class="msg-bottom overflow-point">
                            <span class="color666">隶属于：</span>
                            {{ projectInformation.OrganizeName }}
                        </div>

                        <div class="msg-bottom overflow-point">
                            <span class="color666">机构管理员：</span>
                            {{ projectInformation.Manager }}
                        </div>
                    </div>
                </div>
                <div class="_css-descli ">
                    <span class="color666">项目简介：</span>{{ projectInformation.Description }}
                </div>
                </div>
            <div class="info-bottom">
                <div class="bottom-flex ">
                    <div class="flex-icon icon-zgs"></div>
                    <div class="flex-text">
                        <span class="text">总概算</span>
                        <span class="number">{{ projectInformation.TotalEstimate  || '-'}} <i>{{ projectInformation.TotalEstimateUnitText }}</i></span>
                    </div>
                </div>
                <div class="bottom-flex">
                    <div class="flex-icon icon-zgq"></div>
                    <div class="flex-text">
                        <span class="text">总工期</span>
                        <span class="number">{{ getTotalDay(projectInformation.PlanStartTime,projectInformation.PlanEndTime, 1) }} <i>日历天</i></span>
                    </div>
                </div>
                <!-- 总工期和距离目标天数+1是因为开始时间和结束时间都加进去了 -->
                <div class="bottom-flex">
                    <div class="flex-icon icon-aq"></div>
                    <div class="flex-text">
                        <span class="text">安全施工天数</span>
                        <span class="number">{{ getTotalDay(projectInformation.SafeProductionStartDate,new Date(), 0) }} <i>天</i></span>
                    </div>
                </div>
                <div class="bottom-flex margin0">
                    <div class="flex-icon icon-day"></div>
                    <div class="flex-text">
                        <span class="text">{{ isScreenParams ? '距离目标建设天数' : '距目标建设天数' }}</span>
                        <span class="number">{{ getTotalDay(new Date(),projectInformation.SafeProductionEndDate, 1) }} <i>天</i></span>
                    </div>
                </div>
            </div>
		</div>
        <zdialog-function
            :init_title="'编辑项目信息'"
            :init_zindex="1003"
            :init_innerWidth="450"
            :init_width="450"
            :init_height="500"
            init_closebtniconfontclass="icon-suggested-close"
            :init_usecustomtitlearea="false"
            @onclose="closeprojectAddDialog"
            v-if="projectDialogShow"
        >

        <div slot="mainslot" class="_css-zdialog project-dialog" @mousedown="_stopPropagation($event)">
            <el-form label-position="left" :model="projectAddRuleForm" :rules="projectAddRules" ref="projectrefRuleForm">
                <el-form-item prop="Title" class="css-dialog-label">
                    <p class="title-label">项目名称<span class="color-red">*</span>:</p>
                    <div class="dialog-content css-bg004">
                        <el-input :readonly="true" @mousedown="_stopPropagation($event)" placeholder="请输入项目名称" v-model="projectAddRuleForm.ProjectName"></el-input>
                    </div>
                </el-form-item>
                <el-form-item prop="Content" class="css-dialog-label">
                    <p class="title-label">项目简介:</p>
                    <div class="dialog-content">
                        <el-input :rows="3" @mousedown="_stopPropagation($event)" placeholder="请输入项目简介" type="textarea" v-model="projectAddRuleForm.Description"></el-input>
                    </div>
                </el-form-item>
                <el-form-item prop="Thumbnail" class="css-dialog-label">
                    <p class="title-label">项目缩略图:</p>
                    <div class="dialog-content">
                        <el-upload
                            ref="uploadLogo"
                            class="avatar-uploader"
                            action=""
                            accept="image/*"
                            :show-file-list="false"
                            :auto-upload="false"
                            :on-change="changeFile"
                        >
                            <el-tooltip class="item" effect="dark" content="编辑项目缩略图" placement="top-start">
                                <img
                                v-if="headImg"
                                :src="
                                    headImg ? headImg : require('../../assets/images/default.jpg')
                                "
                                class="avatar"
                                />
                                <i v-else class="el-icon-plus avatar-uploader-icon"></i>
                            </el-tooltip>
                        </el-upload>
                    </div>
                </el-form-item>
                <el-form-item prop="Title" class="css-dialog-label">
                    <p class="title-label">总概算:</p>
                    <div class="dialog-content flex-total">
                        <el-input @mousedown="_stopPropagation($event)" placeholder="请输入总概算" v-model="projectAddRuleForm.TotalEstimate"></el-input>
                        <el-select
                            v-model="projectAddRuleForm.TotalEstimateUnitText"
                            placeholder="请选择单位"
                        >
                            <el-option
                                v-for="(item, index) in unitSelectOption"
                                :label="item.label"
                                :value="item.value"
                                :key="index"
                            ></el-option>
                        </el-select>
                    </div>
                </el-form-item>
                <el-form-item prop="Title" class="css-dialog-label">
                    <p class="title-label">总工期:</p>
                    <div class="dialog-content">
                        <el-date-picker
                        :clearable="false"
                        value-format="yyyy-MM-dd"
                        style="margin-bottom:10px"
                        v-model="projectAddRuleForm.PlanStartTime"
                        type="date"
                        placeholder="请选择计划开始时间">
                        </el-date-picker>
                        <el-date-picker
                        :clearable="false"
                        value-format="yyyy-MM-dd"
                        v-model="projectAddRuleForm.PlanEndTime"
                        type="date"
                        placeholder="请选择计划结束时间">
                        </el-date-picker>
                    </div>
                </el-form-item>
                <el-form-item prop="Title" class="css-dialog-label">
                    <p class="title-label">安全施工天数:</p>
                    <div class="dialog-content">
                        <el-date-picker
                        :clearable="false"
                        v-model="projectAddRuleForm.SafeProductionStartDate"
                        type="date"
                        value-format="yyyy-MM-dd"
                        placeholder="请选择实际开始时间">
                        </el-date-picker>
                    </div>
                </el-form-item>
                <el-form-item prop="Title" class="css-dialog-label">
                    <p class="title-label">距离目标建设天数:</p>
                    <div class="dialog-content">
                        <el-date-picker
                        :clearable="false"
                        value-format="yyyy-MM-dd"
                        v-model="projectAddRuleForm.SafeProductionEndDate"
                        type="date"
                        placeholder="请选择预计结束时间">
                        </el-date-picker>
                    </div>
                </el-form-item>
            </el-form>
        </div>

        <div slot="buttonslot"  class="_css-dialog-btn" >
            <zbutton-function
                :init_text="'确定'"
                :init_fontsize="14"
                :debugmode="true"
                :init_height="'40px'"
                :init_width="'120px'"
                @onclick="editProjectMsg()"
                >
            </zbutton-function>
            <zbutton-function
                :init_text="'取消'"
                :init_color="'rgba(24, 144, 255)'"
                :init_bgcolor="'#fff'"
                :init_fontsize="14"
                :debugmode="true"
                :init_height="'40px'"
                :init_width="'120px'"
                @onclick="closeprojectAddDialog"
                >
            </zbutton-function>
        </div>

        </zdialog-function>
	</div>
</template>
<script>
export default {
	name: "mainProject",
	data() {
		return {
			projectInformation: {}, // 项目信息
            projectDialogShow: false,
            projectAddRuleForm:{
                ProjectName: '',
                Description: '',
                SafeProductionStartDate:"万元",
            },

            projectAddRules:{
                ProjectName:[
                    { required: true, message: '请输入项目名称', trigger: 'change' }
                ],
            },
            headImg: "",
            unitSelectOption: [
                {
                    value: '万元',
                    lable: '万元',
                },
                {
                    value: '亿元',
                    lable: '亿元',
                }
            ]
		};
	},
	props: {
		currentisProjectManager: {
			type: Boolean,
			required: false,
		},
        isScreenParams:{
            type: Boolean,
			required: false,
        }
	},
    filters:{
        filterTime(str) {
            if (str) {
                return str.substr(0, 10)
            }
        },
    },
	created() {},
	mounted() {
        this.getProjectInformation()
	},
	methods: {
		// 点击编辑
		toggleEditState() {
            this.projectAddRuleForm = this.$staticmethod.DeepCopy(this.projectInformation)
            if(!this.projectAddRuleForm.TotalEstimateUnitText){
                this.projectAddRuleForm.TotalEstimateUnitText = "万元"
            }
            this.projectDialogShow = true;
        },
        // 获取详情
        async getProjectInformation(){
            let params = {
                projectid: this.$staticmethod._Get("organizeId")
            }
            const res = await this.$api.GetProject(params);
            this.projectInformation = res.Data;
            this.$emit('time',this.projectInformation.ExpiryTime)
            this.headImg = res.Data.Thumbnail;
        },
        // 编辑项目信息
        async editProjectMsg(){
            console.log(this.projectAddRuleForm)

            let _s = new Date(this.projectAddRuleForm.PlanStartTime).getTime();
            let _e = new Date(this.projectAddRuleForm.PlanEndTime).getTime();
            // let sps = new Date(this.projectAddRuleForm.SafeProductionStartDate).getTime();
            let spe = new Date(this.projectAddRuleForm.SafeProductionEndDate).getTime();

            if(_s && _e && _s > _e && this.projectAddRuleForm.PlanEndTime != null){
                this.$message.error('计划开始时间不得大于计划结束时间');
                return
            }
            if(_s && spe && _s > spe && this.projectAddRuleForm.PlanEndTime != null){
                this.$message.error('距离目标建设天数：计划结束时间必须大于计划开始时间');
                return
            }

            if (!(parseInt(this.projectAddRuleForm.TotalEstimate) >= 0)) {
                this.projectAddRuleForm.TotalEstimate = 0;
            }

            let params = {
                ProjectId: this.$staticmethod._Get("organizeId"),
                ManagerId:this.projectInformation.ManagerId,
                ...this.projectAddRuleForm
            }
            const res = await this.$api.postModifyProject(params);
            this.$message.success(res.Msg)
            this.closeprojectAddDialog()

            this.getProjectInformation();
            // setTimeout(()=>{
            // },1000)
        },
        // 修改缩略图上传文件
        changeFile(file, fileList) {
            const _this = this;
            const reader = new FileReader();
            if(file.size > 204800){
                this.$message.error('图片不能大于200KB，请重新上传');
                return;
            }
            reader.readAsDataURL(file.raw);
            reader.onload = function (e) {
                _this.headImg = reader.result;
                _this.projectAddRuleForm.ImageBase64 = reader.result;
                _this.projectAddRuleForm.Thumbnail = reader.result;
            };
        },
        closeprojectAddDialog(){
            this.projectDialogShow = false;
            this.$refs.projectrefRuleForm.resetFields();
        },
        getTotalDay(start,end,num){
            // num，总工期和距离目标天数计算开始时间和结束时间，所以+1，num代表的是加的天数
            if(start && end){
                let day = this.$formatData.DateDiff(start,end)
                return day + num
            }else{
                return '-'
            }
        },
        _stopPropagation(ev) {
            ev && ev.stopPropagation && ev.stopPropagation();
        },
	},
};
</script>
<style lang="scss" scoped>
.main-project{
    line-height: 1;
    padding: 16px 20px;
}
.avatar{
    width: 124px;
    height: 90px;
}
.color666{
    color: #666666;
}
.project-info-content{
    .info-top{
        display: flex;
        justify-content: space-between;
        .content-msg{
            color: #222222;
            display: flex;
            .project-info-img,.project-info-img img{
                width: 200px;
                height: 140px;
                border-radius: 2px;
            }
            .info-content-title-msg{
                margin: 8px 12px;
                max-width: 258px;
                div{
                    line-height: 26px;
                }
            }
        }
        ._css-descli{
            height: 116px;
            overflow-y: scroll;
            background: #F8F8F8;
            border-radius: 4px;
            padding: 12px;
            line-height: 20px;
        }
    }
    .info-bottom{
        display: flex;
        flex: 1;
        justify-content: space-between;
        .bottom-flex.margin0{
            margin-right: 0 !important;
        }
        .bottom-flex{
            flex: 1;
            background: #F6FAFE;
            border-radius: 4px;
            display: flex;
            .flex-text{
                font-family: PingFangSC, PingFang SC;
                display: flex;
                flex-direction: column;
                .text{
                        color: #071C48;
                }
                .number{
                    font-size: 20px;
                    font-weight: 500;
                    color: #1890FF;
                    i{
                        font-style: initial;
                    }
                }
            }
        }
    }
}
.main-max-width{
    .project-info-content{
        .info-top{
            ._css-descli{
                width: 48%;
            }
        }
        .info-bottom{
            .bottom-flex{
                margin-right: 24px;
                height: 120px;
                .flex-icon{
                    width: 54px;
                    height: 54px;
                    margin: 32px 30px 0 20px;
                }
                .icon-zgs{
                    background-image: url('../../assets/images/b-zgs.png');
                    background-repeat: no-repeat;
                    background-size: 100%;
                }
                .icon-zgq{
                    background-image: url('../../assets/images/b-zgq.png');
                    background-repeat: no-repeat;
                    background-size: 100%;
                }
                .icon-aq{
                    background-image: url('../../assets/images/b-aq.png');
                    background-repeat: no-repeat;
                    background-size: 100%;
                }
                .icon-day{
                    background-image: url('../../assets/images/b-day.png');
                    background-repeat: no-repeat;
                    background-size: 100%;
                }

                .flex-text{
                    .text{
                        font-size: 16px;
                        font-weight: 400;
                        padding: 32px 0 16px 0;
                    }
                    .number{
                        i{
                            font-size: 16px;
                        }
                    }
                }
            }
        }
    }
}
.main-min-width{
    .project-info-content{
        .info-top{
            ._css-descli{
                width: 308px;
            }
        }
        .info-bottom{
            .bottom-flex{
                margin-right: 16px;
                height: 80px;
                .flex-icon{
                    width: 32px;
                    height: 32px;
                    margin: 28px 15px 0 10px;
                }
                .icon-zgs{
                    background-image: url('../../assets/images/s-zgs.png');
                    background-repeat: no-repeat;
                    background-size: 100%;
                }
                .icon-zgq{
                    background-image: url('../../assets/images/s-zgq.png');
                    background-repeat: no-repeat;
                    background-size: 100%;
                }
                .icon-aq{
                    background-image: url('../../assets/images/s-aq.png');
                    background-repeat: no-repeat;
                    background-size: 100%;
                }
                .icon-day{
                    background-image: url('../../assets/images/s-day.png');
                    background-repeat: no-repeat;
                    background-size: 100%;
                }
                .flex-text{
                    .text{
                        font-size: 13px;
                        font-weight: 500;
                        padding: 16px 0 15px 0;
                    }
                    .number{
                        i{
                            font-size: 12px;
                        }
                    }
                }
            }
        }
    }
}
.main-project /deep/ .el-form-item__content{
    margin: 0 !important;
    justify-content: start;
    display: -webkit-box;
    .title-label{
        width: 30%;
        span{
            padding: 2px;
        }
    }
    .el-form-item__error{
        left: 150px;
    }
    .css-dialog-label{
        display: flex;
        .title-label{
            width: 130px;
        }
        .dialog-content{
            display: flex !important;
            flex: 1;
        }
        .flex-total{
            .el-input{
                width:70%;
            }
        }

    }
}

</style>
