<template>
	<div class="main-project" :class="isScreenParams ? 'main-max-width': 'main-min-width'">
		<div class="item-top-bar">
			<span class="title">项目公告</span>
			<div class="top-bar-option" v-if="currentisProjectManager">
				<span class="modify-btn" @click.stop="handelClickAddNotice(0)">增加</span>
			</div>
		</div>
        <div class="notice-list">
            <div class="list">
                <div class="announcement-item" @click.stop="detailNoticeInfo(item)" v-for="item in noticeList" :key='item.Id'>
                    <p class="text">
                        <span class="title">{{ item.Title }}</span>
                        <span class="item-btn" v-if="currentisProjectManager">
                            <i class="icon-btn edit" @click.stop="handelClickAddNotice(item)"></i>
                            <i class="icon-btn delete" @click.stop="handelCliakdeleteNotice(item.Id)"></i>
                        </span>
                    </p>
                    <p class="announcement-content" v-if="item.Content.length <= 50">
                        {{ filterContent(item.Content) }}
                    </p>
                    <el-tooltip class="item" v-if="item.Content.length > 50" popper-class="notice-tooltip-width1"  effect="dark" :content="item.Content" placement="top-start">
                        <p class="announcement-content">
                            {{ filterContent(item.Content) }}
                        </p>
                    </el-tooltip>
                    <p class="content-time" style="text-aling-right">{{ item.CreaeTime }}</p>
                </div>
            </div>
        </div>

        <zdialog-function
        @mousedown="_stopPropagation($event)"
            :init_title="noticeTitle"
            :init_zindex="1003"
            :init_innerWidth="450"
            :init_width="450"
            :init_height="500"
            init_closebtniconfontclass="icon-suggested-close"
            :init_usecustomtitlearea="false"
            @onclose="closeNoticeAddDialog"
            v-if="noticeDialogShow"
        >
        
        <div slot="mainslot" class="_css-zdialog" @mousedown="_stopPropagation($event)">
            <el-form label-position="left" :model="noticeAddRuleForm" :rules="noticeAddRules" ref="ruleFormRef"  @mousedown="_stopPropagation($event)"> 
                <el-form-item prop="Title" class="css-dialog-label">
                    <label class="title-label">公告标题<span class="color-red">*</span>:</label>
                    <el-input @mousedown="_stopPropagation($event)" placeholder="请输入公告标题" v-model="noticeAddRuleForm.Title"></el-input>
                </el-form-item>
                <el-form-item prop="Content" class="css-dialog-label">
                    <label class="title-label">公告内容:</label>
                    <el-input :rows="3" @mousedown="_stopPropagation($event)" placeholder="请输入公告详情" type="textarea" v-model="noticeAddRuleForm.Content"></el-input>
                </el-form-item>
            </el-form>
        </div>

        <div slot="buttonslot"  class="_css-dialog-btn" >
            <zbutton-function
                :init_text="'确定'"
                :init_fontsize="14"
                :debugmode="true"
                :init_height="'40px'"
                :init_width="'120px'"
                @onclick="addNoticeMsg('ruleFormRef')"
                >
            </zbutton-function>
            <zbutton-function
                :init_text="'取消'"
                :init_color="'rgba(24, 144, 255)'"
                :init_bgcolor="'#fff'"
                :init_fontsize="14"
                :debugmode="true"
                :init_height="'40px'"
                :init_width="'120px'"
                @onclick="closeNoticeAddDialog"
                >
            </zbutton-function>
        </div>
        
        </zdialog-function>
        <zdialog-function
            :init_title="noticeContentObj.Title"
            :init_zindex="1003"
            :init_innerWidth="650"
            :init_width="650"
            :init_height="500"
            init_closebtniconfontclass="icon-suggested-close"
            :init_usecustomtitlearea="false"
            @onclose="noticeContentShow=false"
            v-if="noticeContentShow"
        >
        
        <div slot="mainslot" class="_css-zdialog" @mousedown="_stopPropagation($event)">
            <div class="content-dialog">
                {{ noticeContentObj.Content }}
            </div>
        </div>

        
        </zdialog-function>
	</div>
</template>
<script>
export default {
	name: "mainNotice",
	data() {
		return {
			noticeList: [], // 公告列表
            noticeTitle: '新增项目公告',
            noticeType: 0, // 0 新增  1 编辑
            noticeDialogShow: false, // 新增项目公告dialog
            noticeAddRuleForm: {
                Title: "",
                Content: "",
            },
            noticeAddRules: {
                Title: [
                    { required: true, message: '请输入公告内容', trigger: 'change' }
                ],
            },
            noticeContentShow: false,
            noticeContentObj: {}, // 点击查看详情内容
		};
	},
	props: {
		currentisProjectManager: {
			type: Boolean,
			required: false,
		},
        isScreenParams:{
            type: Boolean,
			required: false,
        }
	},
	created() {},
	mounted() {
        this.getNoticeList()
	},
	methods: {
        // 截取显示
        filterContent(str){
            let number = this.isScreenParams ? 55 : 35
            if(str.length > number){
                return str.slice(0,number) + '...'
            }else{
                return str
            }
        },
        // 获取项目公告信息
        async getNoticeList(){
            let params = {
                organizeId: this.$staticmethod._Get("organizeId")
            }
            const res = await this.$api.getnoticeList(params)
            this.noticeList = res.Data
        },
        handelClickAddNotice(item){
            // if(item == 0){
            //     this.noticeDialogShow = true;
            //     this.noticeType = 0
            //     this.noticeTitle = '新增项目公告';
            //     this.noticeAddRuleForm = {}
            // }else{
            //     this.noticeDialogShow = true;
            //     this.noticeTitle = '编辑项目公告';
            //     this.noticeAddRuleForm = item;
            //     this.noticeType = 1
            // }
            this.noticeDialogShow = true;
            this.noticeType = item === 0 ? 0 : 1;
            this.noticeTitle = item === 0 ? '新增项目公告' : '编辑项目公告';
            this.noticeAddRuleForm = item === 0 ? {} : this.$staticmethod.DeepCopy(item);

        },
        // 新增项目公告
		addNoticeMsg(formName){
            this.$refs[formName].validate((valid) => {
                if (valid) {
                   this.toggleEditState()
                } else {
                    console.log('error submit!!');
                    return false;
                }
            });
        },
        // 编辑项目公告
        async toggleEditState(){
            let params = {
                organizeId: this.$staticmethod._Get("organizeId"),
                ...this.noticeAddRuleForm
            } 

            const apiCall = this.noticeType == 0 ? this.$api.postNoticeCreate : this.$api.postNoticeUpdate;

            const res = await apiCall(params);

            if(res.Ret == 1){
                this.$message.success(res.Msg);
                this.getNoticeList();
                this.closeNoticeAddDialog();
            }
        },
        closeNoticeAddDialog(){
            this.noticeTitle = '新增项目公告';
            this.noticeDialogShow = false;
            this.resetForm('ruleFormRef')
        },
        resetForm(formName) {
            this.$refs[formName].resetFields();
        },
        // 删除项目公告
        async handelCliakdeleteNotice(id){
            this.$confirm("确定删除当前公告", {
                confirmButtonText:'确定',
                cancelButtonText:'取消',
                type:'warning'
            }).then(() => {
                this.deleteNoticeFun(id)
            }).catch((err) => {
                console.log(err)
            })
        },
        // 删除调用接口
        async deleteNoticeFun(id){
            const res = await this.$api.postNoticeDelete(id)
            if(res.Ret == 1){
                this.$message.success(res.Msg);
                this.getNoticeList()
            }
        },
        // 查看详情
        async detailNoticeInfo(item){
            let params = {
                id: item.Id
            }
            const res = await this.$api.getnoticeInfo(params)
            if(res.Ret = 1){
                this.noticeContentShow = true;
                this.noticeContentObj = res.Data;
            }
        },
        _stopPropagation(ev) {
            ev && ev.stopPropagation && ev.stopPropagation();
        },
	},
};
</script>
<style lang="scss" scoped>
.main-project{
    padding: 16px 20px;
    line-height: 1;
    .notice-list{
        margin: 15px 0;
        .list{
            overflow-y: auto;
            .announcement-item{
                position: relative;
                display: flex;
                flex-direction: column;
                border-bottom: 1px solid #EFEFEF;
                padding-bottom: 13px;
                color: #222222;
                .text:hover{
                    .item-btn{
                        display: block;
                    }
                }
                .text{
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    .title{
                        font-weight: 500;
                        line-height: 34px;
                    }
                    .title::before{
                       content: " ";
                        display: inline-block;
                        width: 6px;
                        height: 6px;
                        border-radius: 50%;
                        background: #FFFFFF;
                        border: 2px solid #007AFF;
                        margin-right: 4px;
                    }
                    .item-btn{
                        display: none;
                        i{
                            display: inline-block;
                            width: 18px;
                            height: 17px;
                            background-size: 100%;
                            margin-right: 10px;
                            cursor: pointer;
                        }
                        i.edit{
                            background-image: url('../../assets/images/s-re.png');
                            background-repeat: no-repeat;
                        }
                        i.delete{
                            background-image: url('../../assets/images/s-del.png');
                            background-repeat: no-repeat;
                        }
                    }
                }
                .announcement-content{
                    line-height: 20px;
                    max-height: 48px;
                    overflow: hidden;
                    margin-bottom: 5px;
                    cursor: pointer;
                    span{
                        display: -webkit-box;
                        -webkit-box-orient: vertical;
                        overflow: hidden;
                        text-overflow: ellipsis;
                        -webkit-line-clamp: 2;
                    }
                }
                .content-time {
                    align-self: flex-end;
                    position: absolute;
                    bottom: 0;
                    line-height: 24px;
                }
            }
        }
    }
}

/deep/ .el-form-item__content{
    margin: 0 !important;
    display: flex;
    justify-content: space-between;
    .title-label{
        width: 100px;
        span{
            padding: 2px;
        }
    }
    .el-form-item__error{
        left: 100px;
    }
}
.content-dialog{
    margin: 20px 24px;
    line-height: 20px;
    max-height: 300px;
    overflow-y: auto;
}
.main-max-width{
    .notice-list{
       .list{
            height:445px;
       } 
    }
}
.main-min-width{
    .notice-list{
       .list{
            height: 404px;

       } 
    }
}
</style>
<style lang="scss">
.notice-tooltip-width1{
    max-width: 300px;
    min-width: 200px;
    line-height: 20px;
    min-height: 200px;
    max-height: 200px;
    overflow-y: auto;
}
</style>