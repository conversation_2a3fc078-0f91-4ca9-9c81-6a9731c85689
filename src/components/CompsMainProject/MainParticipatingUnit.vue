<template>
	<div class="main-project">
		<div class="item-top-bar">
			<span class="title">参建单位信息一览</span>
			<div class="top-bar-option" v-if="currentisProjectManager">
				<span class="modify-btn" @click.stop="handelClickAddUnit(0)">增加</span>
			</div>
		</div>
		<div class="unit-list">
			<div class="unit-title">
				<el-row class="detail-list">
					<el-col :span="6"><div class="">参建单位</div></el-col>
					<el-col :span="6"><div class="">单位名称</div></el-col>
					<el-col :span="5"><div class="">负责人</div></el-col>
					<el-col :span="7"><div class="">联系电话</div></el-col>
				</el-row>
			</div>
			<div class="unit-list-content">
				<el-row
					v-for="(item, index) in unitListArr"
					:key="index"
					class="detail-list"
                    :class="currentisProjectManager ? 'detail-list-hover':''"
				>
					<el-col :span="6">
                        <div class="overflow-point" v-if="item.UnitTypeText && item.UnitTypeText.length <= 6">{{ item.UnitTypeText }}</div>
                        <el-tooltip class="item" popper-class="notice-tooltip-width"  v-if="item.UnitTypeText && item.UnitTypeText.length > 6" effect="dark" :content="item.UnitTypeText" placement="top-start">
                            <div class="overflow-point">{{ item.UnitTypeText }}</div>
                        </el-tooltip>
                    </el-col>
					<el-col :span="6">
                        <div class="overflow-point" v-if="item.UnitName.length <= 6">{{ item.UnitName }}</div>
                        <el-tooltip class="item" v-if="item.UnitName.length > 6" effect="dark" :content="item.UnitName" placement="top-start">
                            <div class="overflow-point">{{ item.UnitName }}</div>
                        </el-tooltip>
                    </el-col>
					<el-col :span="5">
                        <div class="overflow-point">{{ item.UnitLeader }}</div>
                    </el-col>
					<el-col :span="7">
						<div class="overflow-point mobile-btn">
							<span class="mobile-css">
								{{ item.UnitMobile }}
							</span>
							<span class="item-btn" v-if="currentisProjectManager">
								<i
									class="icon-btn edit"
									@click.stop="handelClickAddUnit(item)"
								></i>
								<i
									class="icon-btn delete"
									@click.stop="handelCliakdeleteUnit(item.Id)"
								></i>
							</span>
						</div>
					</el-col>
				</el-row>
			</div>
		</div>
		<zdialog-function
			@mousedown="_stopPropagation($event)"
			:init_title="unitTitle"
			:init_zindex="1003"
			:init_innerWidth="400"
			:init_width="400"
			:init_height="500"
			init_closebtniconfontclass="icon-suggested-close"
			:init_usecustomtitlearea="false"
			@onclose="closeunitAddDialog"
			v-if="unitDialogShow"
		>
			<div
				slot="mainslot"
				class="_css-zdialog"
				@mousedown="_stopPropagation($event)"
			>
				<el-form
					label-position="left"
					:model="unitAddRuleForm"
					:rules="unitAddRules"
					ref="ruleFormRef"
					@mousedown="_stopPropagation($event)"
				>
					<el-form-item prop="UnitTypeId" class="css-dialog-label">
						<label class="title-label"
							>参建单位<span class="color-red">*</span>:</label
						>
						<el-select
							v-model="unitAddRuleForm.UnitTypeId"
							placeholder="请选择参建单位"
						>
							<el-option
								v-for="(item, index) in typeSelectOption"
								:label="item.CategoryName"
								:value="item.Id"
								:key="index"
							></el-option>
						</el-select>
					</el-form-item>
					<el-form-item prop="UnitName" class="css-dialog-label">
						<label class="title-label"
							>单位名称<span class="color-red">*</span>:</label
						>
						<el-input
							@mousedown="_stopPropagation($event)"
							placeholder="请输入单位名称"
							v-model="unitAddRuleForm.UnitName"
						></el-input>
					</el-form-item>
					<el-form-item prop="UnitLeader" class="css-dialog-label">
						<label class="title-label"
							>负责人<span class="color-red">*</span>:</label
						>
						<el-input
							@mousedown="_stopPropagation($event)"
							placeholder="请输入负责人"
							v-model="unitAddRuleForm.UnitLeader"
						></el-input>
					</el-form-item>
					<el-form-item prop="UnitMobile" class="css-dialog-label">
						<label class="title-label"
							>联系电话<span class="color-red">*</span>:</label
						>
						<el-input
							@mousedown="_stopPropagation($event)"
							placeholder="请输入联系电话"
							v-model="unitAddRuleForm.UnitMobile"
						></el-input>
					</el-form-item>
				</el-form>
			</div>

			<div slot="buttonslot" class="_css-dialog-btn">
				<zbutton-function
					:init_text="'确定'"
					:init_fontsize="14"
					:debugmode="true"
					:init_height="'40px'"
					:init_width="'120px'"
					@onclick="addunitMsg"
				>
				</zbutton-function>
				<zbutton-function
					:init_text="'取消'"
					:init_color="'rgba(24, 144, 255)'"
					:init_bgcolor="'#fff'"
					:init_fontsize="14"
					:debugmode="true"
					:init_height="'40px'"
					:init_width="'120px'"
					@onclick="closeunitAddDialog"
				>
				</zbutton-function>
			</div>
		</zdialog-function>
	</div>
</template>
<script>
export default {
	name: "MainParticipatingUnit",
	data() {
		return {
			unitListArr: [],
			unitDialogShow: false,
			unitTitle: "",
			unitAddRuleForm: {},
			unitAddRules: {
                UnitTypeId: [
                    { required: true, message: '请选择参建单位', trigger: 'blur' },
                ],
                UnitName: [
                    { required: true, message: '请输入单位名称', trigger: 'blur' },
                ],
                UnitLeader: [
                    { required: true, message: '请输入负责人', trigger: 'blur' },
                ],
                UnitMobile: [
                    { required: true, message: '电话号码不能为空', trigger: 'blur' },
                    { pattern: /^1[3-9]\d{9}$/, message: '电话号码格式不正确', trigger: 'blur' }
                ]
            },
			unitType: 0, // 0 新增  1 编辑
			typeSelectOption: [],
		};
	},
	props: {
		currentisProjectManager: {
			type: Boolean,
			required: false,
		},
        isScreenParams:{
            type: Boolean,
			required: false,
        }
	},
	created() {},
	mounted() {
        this.getCategoryList();
        this.getunitList()
    },
	methods: {
		// 获取参建单位select
        async getCategoryList(){
            let params = {
				organizeId: this.$staticmethod._Get("organizeId"),
                categoryType: 'participating_unit'
			};
			const res = await this.$api.getBaseCategoryList(params);
            this.typeSelectOption = res.Data;
        },
		handelClickAddUnit(item) {
			this.unitType = item === 0 ? 0 : 1;
			this.unitTitle = item === 0 ? "新增参建单位" : "编辑参建单位";
			this.unitAddRuleForm = item === 0 ? {} : this.$staticmethod.DeepCopy(item);
			this.unitDialogShow = true;
		},
		// 获取参建单位列表
		async getunitList() {
			let params = {
				organizeId: this.$staticmethod._Get("organizeId"),
			};
			const res = await this.$api.getParticipatingList(params);
			this.unitListArr = res.Data;
		},
		addunitMsg() {
			this.$refs.ruleFormRef.validate((valid) => {
				if (valid) {
					this.toggleEditState();
				} else {
					console.log("error submit!!");
					return false;
				}
			});
		},
		//  编辑
		async toggleEditState() {
			let hide_m = this.hidePhoneNumber(this.unitAddRuleForm.UnitMobile);
			let params = {
				OrganizeId: this.$staticmethod._Get("organizeId"),
				UnitMobileSensitive: hide_m,
				...this.unitAddRuleForm,
			};
			const apiCall =
				this.unitType == 0
					? this.$api.postCreateParticipatingUnit
					: this.$api.postModifyParticipatingUnit;

			const res = await apiCall(params);

			if (res.Ret == 1) {
				this.$message.success(res.Msg);
				this.getunitList();
				this.closeunitAddDialog();
			}
		},
		handelCliakdeleteUnit(id) {
			this.$confirm("确定删除当前信息", {
				confirmButtonText: "确定",
				cancelButtonText: "取消",
				type: "warning",
			})
				.then(() => {
					this.deleteNoticeFun(id);
				})
				.catch((err) => {
					console.log(err);
				});
		},
		// 删除
		async deleteNoticeFun(id) {
			const res = await this.$api.postDelParticipatingUnit(id);
			if (res.Ret == 1) {
				this.$message.success(res.Msg);
				this.getunitList();
			}
		},
		closeunitAddDialog() {
			this.unitDialogShow = false;
			this.$refs.ruleFormRef.resetFields();
		},
		// 电话号码加密
		hidePhoneNumber(phoneNumber) {
			return phoneNumber.replace(/(\d{3})\d{4}(\d{4})/, "$1****$2");
		},
		_stopPropagation(ev) {
			ev && ev.stopPropagation && ev.stopPropagation();
		},
	},
};
</script>
<style lang="scss" scoped>
.main-project {
	line-height: 1;
	padding: 16px 20px;
}
/deep/ .el-form-item__content {
	margin: 0 !important;
	display: flex;
	justify-content: start;
	.el-select {
		width: 100%;
	}
	.title-label {
		width: 100px;
		span {
			padding: 2px;
		}
	}
	.el-form-item__error {
		left: 100px;
	}
}
.unit-list {
	/deep/ .el-row{
		white-space: nowrap;
	}
	height: 100%;
	margin: 20px 0;
	.unit-title {
		line-height: 40px;
		height: 40px;
		background: #f8f8f8;
		font-size: 14px;
		font-family: PingFangSC, PingFang SC;
		font-weight: 500;
		color: #222220;
	}
	.unit-list-content {
		height: 80%;
		overflow-y: auto;
		background: #fff;
		font-size: 14px;
		font-family: PingFangSC, PingFang SC;
		font-weight: 400;
		color: #222222;
		.detail-list {
			line-height: 48px;
			border-bottom: 1px solid #efefef;
			height: 48px;
		}
		.mobile-btn {
			display: flex;
			justify-content: space-between;
			position: relative;
			.item-btn {
				display: none;
				i {
					display: inline-block;
					width: 18px;
					height: 17px;
					background-size: 100%;
					margin-right: 10px;
					cursor: pointer;
					// position: absolute;
					// right: 0;
					// bottom: 10px;
				}
				i.edit {
					right: 25px;
					background-image: url("../../assets/images/s-re.png");
					background-repeat: no-repeat;
				}
				i.delete {
					background-image: url("../../assets/images/s-del.png");
					background-repeat: no-repeat;
				}
			}
		}
		.detail-list:hover {
			background: rgba(0, 122, 255, 0.05);
		}
        .detail-list-hover:hover {
            .mobile-css {
                display: inline-block;
                overflow: hidden;
                width: 40%;
                text-overflow: ellipsis;
            }
            .item-btn {
				display: inline-block;
			}
        }

	}
	.detail-list {
		div {
			padding-left: 10px;
		}
	}
}
</style>