<template>
  <div class="_css-materialinfo-all" :style="gethoverstyle()">
    <div v-drag="draggreet" :style="dragstyle" class="_css-materialinfo-front">1</div>
  </div>
</template>
<script>
export default {
    data(){
        return {
            val: "0",
            dragstyle: {
                position: "fixed",
                right: "calc(50% - 440px)",
                top: "32px"
            },
            extdata:{

            }
        };
    },
    mounted(){
        var _this = this;
        _this.windowsizechange();
        window.onresize = function() {
            console.log(177);
            _this.windowsizechange(); 
        };
    },
    methods:{
        windowsizechange(){
            var _this = this;
            _this.dragstyle.right = `calc(50% - ${((_this.frontWidth || 880) / 2)}px)`;
            _this.dragstyle.top =  `32px`;
        },
        draggreet(val) {
            var _this = this;
            _this.val = val;
        },
        gethoverstyle() {
            var _this = this;
            var _s = {};
            _s["z-index"] = _this.zIndex || 1001; // 0 -> 3
            return _s;
        }
    },
    props:{
        zIndex:{
            type:Number,
            required: false
        },
        frontWidth:{
            type:Number,
            required: false // 880
        }
    }
};
</script>
<style scoped>

._css-materialinfo-all {
    position:fixed;
    width:100%;
    height:100%;
    top:0;
    left:0;
    background-color: rgba(0,0,0,0.3);
}

._css-materialinfo-front {
  width: 880px;
  background-color: rgba(255, 255, 255, 1);
  box-shadow: 0px 13px 24px -17px rgba(11, 41, 62, 0.8);
  border-radius: 4px;
  display: flex;
  flex-direction: column;
  height:calc(100% - 64px);
}

</style>