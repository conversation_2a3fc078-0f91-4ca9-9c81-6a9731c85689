<template>
  <div class="circle-section">
    <!--饼状图部分-->
        <v-chart
          :options="barOption"
          style="width: 140px; height: 140px"
        ></v-chart>
  </div>
</template>
<script type="text/javascript">
export default {
  name: "PieCharts", // 商机状态
  components: {},
  props: {
    PieChartsData: {
      type: Array,
    },
  },
  data() {
    return {
      barOption: {
        series :
            {
              // name: '访问来源',
              type: 'pie',    // 设置图表类型为饼图
              radius: '75%',  // 饼图的半径，外半径为可视区尺寸（容器高宽中较小一项）的 55% 长度。
              data:[          // 数据数组，name 为数据项名称，value 为数据项值
                  
              ],
              labelLine:{
                show:false
              },
					    color:['#FFC30D','#5B4DFF']
          }
      },
    };
  },
  // created() {
  //   this.changebarOption()
  // },
  methods: {
    changebarOption(){
      this.barOption.series.data = this.PieChartsData
    }
  },
  watch: {
    PieChartsData(){
      this.changebarOption();
    }
  }
};
</script>
<style lang="scss" scoped>
</style>

