<template>
  <div class="circle-section">
    <!--饼状图部分-->
        <v-chart
          :options="tabBarChartsList"
          style="width: 90%;"
        ></v-chart>
  </div>
</template>
<script type="text/javascript">
export default {
  name: "BarCharts", // 商机状态
  components: {},
  props: {
    BarCharts: {
      type: Object,
    },
  },
  data() {
    return {
      tabBarChartsList:{
        color:['#FFBF00'],
        title: {
          left:'-15',
          text: '',
          textStyle: {
              fontWeight: 500,
              rich: {
                  a: {
                      padding:[0,0,0,20],                      
                      color: '#fff',//设置 动态数据字体的颜色
                      fontSize: '16',//设置 动态数据字体的大小
                      height: 22,
                      width: 140,
                      backgroundColor: {
                          image:require('../../assets/images/VisualPanel/subtitle.png'),
                      },
                  },
              }
          }
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {

          axisLabel: {
            color:'#FFF',  
             formatter: '{value}', // y轴1%-100%格式化     
          },
          splitLine:{
            lineStyle:{
              color:'rgba(12, 108, 242, 0.4)'
            }
          },
          axisLine:{
            lineStyle:{
              color:'rgba(12, 108, 242, 0.4)'
            }
          },
          type: 'value',
          min: 0, 
          max: 100, 
          boundaryGap: [0, 0.01]
        },
        yAxis: {
          axisLabel: {
            color:'#FFF',       
          },
          axisLine:{
            lineStyle:{
              color:'rgba(12, 108, 242, 0.4)'
            }
          },
          type: 'category',
          data: []
        },
        series:{
          label: {
            show: true,
            position: 'inside',
            formatter:'{c}%'
          },
          itemStyle:{
            normal:{
              barBorderRadius:[0,8,8,0]
            }
          },
          barWidth:16,
          type: 'bar',
          data: [],
          
        },
      }
    };
  },
  created() {
    this.BarChartsList(this.BarCharts)
  },
  methods: {
    BarChartsList(c){
      this.tabBarChartsList.title.text = `' {a| ${c.ERW_progressHeader}}'`
      this.tabBarChartsList.yAxis.data = c.ERW_progressSingle.map(item => item.ER_cont)
      let ER_custom_input = c.ERW_progressSingle.map(item => item.ER_custom_input)
      this.tabBarChartsList.series.data = ER_custom_input.map(Number)

    }
  },
  watch: {
    BarCharts(){
      this.BarChartsList(this.BarCharts)
    }
  }
};
</script>
<style lang="scss" scoped>
</style>

