<template>
  <div class="circle-section">
      <v-chart
        :options="barOption"
        style="width: 110px; height: 110px"
      ></v-chart>
  </div>
</template>
<script type="text/javascript">
export default {
  name: "CircleCharts", 
  components: {},
  props: {
    circleChartsData: {
      type: Object,
    },
  },
  data() {
    return {
      barOption: {
        color: [
          "rgba(123, 211, 198, 1)",
          "rgba(221, 82, 82, 1)",
          "rgba(212, 218, 223, 0.1)",
        ],
        title: {
          text: "",
          left: "center",
          bottom: "35%",
          textStyle: {
            color: "#FFFFFF",
            fontSize: "18",
            fontWeight: 300,
          },
        },
        grid: {
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
        },
        series:{
            type: "pie",
            radius: ["70%", "90%"],
            itemStyle: {
              borderRadius: 0,
              borderColor: "#000",
              borderWidth: 0,
            },
            hoverAnimation: false,
            label: {
              show: false,
              position: "center",
            },
            emphasis: {
              scale: false,
              label: {
                show: true,
                color: "#fff",
                fontSize: "16",
                fontWeight: "normal",
                formatter: "",
              },
            },
            labelLine: {
              show: false,
            },
            data: [],
            color:['rgba(255, 114, 13, 0.2)','#FF720D']
          },
      },
    };
  },
  computed: {},
  // created() {
  //   this.$nextTick(()=>{
  //     this.changebarOption();
  //   })
  // },
  methods: {
    changebarOption(){
      let Data = this.circleChartsData
      if(Object.keys(Data).length!=0){
        this.barOption.series.data = [
          {
            value: Data.MonthResolved2.ER_custom_input,
            name: Data.MonthResolved2.ER_cont,
          },
          {
            value: Data.MonthUnResolved2.ER_custom_input,
            name: Data.MonthUnResolved2.ER_cont,
          },
        ]
        this.barOption.title.text =
          Data.MonthUnResolved2.ER_custom_input +
          "/" +
          Data.MonthResolved2.ER_custom_input;
      }else{
        this.barOption.title.text = 0 +"/" +0;
        this.barOption.series.data = [
          {
            value: 0,
            name: '',
          },
          {
            value: 0,
            name: '',
          },
        ]
      }
    }
  },
  watch: {
    circleChartsData(){
      this.changebarOption();
    }
  }
};
</script>
<style lang="scss" scoped>

</style>

