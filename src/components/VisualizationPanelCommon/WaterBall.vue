<template>
  <div class="content" :style="{ width: bSize + 'px',height: bSize + 'px'}">
    <div class='chart' :id='"chart"+BallIndex' :style="{ width: bSize + 'px',height: bSize + 'px'}"></div>
  </div>
</template>

<script>
export default {
  name: "WaterBall",
  data() {
    return {
      seriesDataNum:0
    };
  },

  props: {
    bSize: {
      type: [Number, String], // 球的宽高
      default: 100,
    },
    borderWidth:{    // 球外边框宽度
      type: Number,
      default:8,
    },
    seriesData: {   // 球占比值
      type: Number,
      default:0
    },
    BallIndex: {  // 球的ID ，不同的id名字
      type: Number,
      default:100000,
    },

  },

  methods: {
    drawChart () {
      // 基于准备好的dom，初始化echarts实例
      let chart = this.$echarts.init(document.getElementById('chart'+this.BallIndex))
      // 监听屏幕变化自动缩放图表
      window.addEventListener('resize', function () {
        chart.resize()
      })
      // 绘制图表
      chart.setOption({
        grid: [{
          left: 0,
          right: 0,
          top: 0,
          bottom: 0
        }],
        series: [{
          type: 'liquidFill',
          waveAnimation: true,
          amplitude: 4,
          // waveLength: 30,
          period: 1800,  // 水球图的速度
          radius: '90%', // 水球图的半径

          animationEasing: 'linear',
          animationEasingUpdate: 'linear',
          animationDuration: 2000,
          animationDurationUpdate: 1000,
          waveLength: '80%', 
          // period: 'auto',

          center: ['50%', '50%'], // 水球图的中心（圆心）坐标，数组的第一项是横坐标，第二项是纵坐标
          // 水填充图的形状 circle 默认圆形  rect 圆角矩形  triangle 三角形  
          // diamond 菱形  pin 水滴状 arrow 箭头状  还可以是svg的path
          shape: 'circle',
          phase:  'auto', // 1000, // 波的相位弧度 不设置  默认自动
          direction: 'right', // 波浪移动的速度  两个参数  left 从右往左 right 从左往右
          outline: {
            show: false, // 显示外边框
            borderDistance: 4, // 边框线与图表的距离 数字
            itemStyle: {
              opacity: 0.1, // 边框的透明度   默认为 1
              borderWidth: this.borderWidth, // 边框的宽度
              shadowBlur: 8, // 边框的阴影范围 一旦设置了内外都有阴影
              shadowColor: '#fff', // 边框的阴影颜色,
              borderColor: '#D4DADF' // 边框颜色
            }
          },
          // 图形样式
          itemStyle: {
            // color: '#4564FF', // 水球显示的背景颜色
            // opacity: 0.6, // 波浪的透明度
            color: 'rgba(12, 108, 242, 1)', 
            shadowBlur: 10 // 波浪的阴影范围
          },
          backgroundStyle: {
            // color: '#0B1A35', // 水球未到的背景颜色
            // opacity: 0.7,
            color: 'rgba(12, 108, 242, 0.2)', 
          },
          // 图形的高亮样式
          emphasis: {
            itemStyle: {
              opacity: 0.8 // 鼠标经过波浪颜色的透明度
            }
          },
          // 图形上的文本标签
          label: {
            fontSize: 30,
            fontWeight: 400,
            color: '#fff',  // 文字颜色-水球覆盖-未到
            insideColor: '#fff',  // 文字颜色-水球覆盖-ga已到
          },
          // data: [0.6] // 系列中的数据内容数组
          data: this.seriesDataNum 
        }]
      })
    },
    seriesDataGet(){
      this.seriesDataNum = (this.seriesData *0.01).toFixed(2);
      this.seriesDataNum =Array.of(this.seriesDataNum);
      this.drawChart();
    }
  },
  mounted() {
    this.seriesDataGet()
  },
  watch:{
    seriesData(){
      this.seriesDataGet()
    }
  }
};
</script>
<style scoped>
/*  */
</style>
