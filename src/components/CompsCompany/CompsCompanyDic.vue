<template>
	<div class="_css-companydic" :class="classname" :style="styleobj">
		<div class="_css-companydic-in" :style="instyleobj">
			<!-- 左右分栏 -->
			<div class="_css-comdic-leftandright">
				<div class="_css-comdic-left">
					<div class="_css-comdic-lefttitle">
						<div class="_css-comdic-lefttitle-text">字典分类</div>
					</div>
					<div class="_css-comdic-leftbody css-miniscroll">
						<div
							class="_css-comdic-diccategory"
							@click.stop="
								_dicshowtype_select({ id: 'IssueStatue', text: '问题状态' })
							"
							:class="{ clicked: extdata.dictype_select_id == 'IssueStatue' }"
						>
							<div class="_css-comdic-diccateitemtext">问题状态</div>
							<div class="_css-comdic-diccateitemnumber">
								{{ extdata.status_count }}
							</div>
						</div>

						<div
							class="_css-comdic-diccategory"
							@click.stop="
								_dicshowtype_select({ id: 'IssueType', text: '问题类型' })
							"
							:class="{ clicked: extdata.dictype_select_id == 'IssueType' }"
						>
							<div class="_css-comdic-diccateitemtext">问题类型</div>
							<div class="_css-comdic-diccateitemnumber">
								{{ extdata.type_count }}
							</div>
						</div>
						<div
							class="_css-comdic-diccategory"
							@click.stop="
								_dicshowtype_select({ id: 'unitType', text: '单位类型' })
							"
							:class="{ clicked: extdata.dictype_select_id == 'unitType' }"
						>
							<div class="_css-comdic-diccateitemtext">单位类型</div>
							<div class="_css-comdic-diccateitemnumber">
								{{ extdata.type_unitcount }}
							</div>
						</div>
						<div
							class="_css-comdic-diccategory"
							@click.stop="
								_dicshowtype_select({ id: 'ArchivesRoom', text: '档案位置' })
							"
							:class="{ clicked: extdata.dictype_select_id == 'ArchivesRoom' }"
						>
							<div class="_css-comdic-diccateitemtext">档案位置</div>
							<div class="_css-comdic-diccateitemnumber">
								{{ extdata.type_ArchivesRoom }}
							</div>
						</div>
					</div>
				</div>
				<div class="_css-comdic-right">
					<div class="_css-comdic-righttitle">
						<div
							class="_css-addwholebtn"
							@click.stop="itemclick('-1', '', '', '', 'rgba(28, 50, 75, 1)')"
						>
							<div class="_css-addbtnicon icon-suggested-plus_square"></div>
							<div class="_css-addbtntext">
								新建{{ extdata.dictype_select_text }}
							</div>
						</div>
					</div>

							
					<div class="_css-comdic-rightbody css-miniscroll" v-if="extdata.dictype_select_id == 'IssueStatue' || extdata.dictype_select_id == 'IssueType'">
						<div
							class="_css-comdic-rightitem"
							v-for="(item) in extdata.dicitems"
							:key="item.ItemDetailId"
							@click.stop="itemclick(item.ItemDetailId,item.ItemName,item.ItemValue,item.SortCode,item.ItemCode)"
						>
							<div class="_css-comdic-item-handle"></div>
							<div
								class="_css-comdic-item-text"
								:class="{
									close:
										extdata.dictype_select_id == 'IssueStatue' &&
										item.ItemName == '关闭',
									custom:
										extdata.dictype_select_id == 'IssueType' ||
										extdata.dictype_select_id == 'IssueTab' ||
										extdata.dictype_select_id == 'materialstatus',
								}"
								:style="getDicIssueTypeStyle(item)"
							>
								<div :title="item.ItemName" class="_css-comdic-item-text-in">
									{{ item.ItemName }}
								</div>
							</div>

							<!-- 上下移 谢鹏说这功能不要了-->
							
						</div>
					</div>
					<div class="_css-comdic-rightbody css-miniscroll" v-if="extdata.dictype_select_id == 'unitType'">
						<div
							class="_css-comdic-rightitem"
							v-for="(item) in extdata.dicitems"
							:key="item.ItemDetailId"
							@click.stop="itemclick(item.ItemDetailId,item.ItemName,item.ItemValue,item.SortCode,item.ItemCode)"
						>
							<div class="_css-comdic-item-handle"></div>
							<div
								class="_css-comdic-item-text custom"
								:style="getDicIssueTypeStyle(item)"
							>
								<div :title="item.ItemName" class="_css-comdic-item-text-in">
									{{ item.ItemName }}
								</div>
							</div>
						</div>
					</div>

					<div class="_css-comdic-rightbody css-miniscroll" v-if="extdata.dictype_select_id == 'ArchivesRoom'" @click.stop="closeAll">
						<el-tree
							:data="archivesRoomData"
							node-key="Id"
							ref="tree" 
							:props="defaultProps"
							class="_css-customstyle"
							:highlight-current="true"
							:expand-on-click-node="true"  
							@node-expand="nodeExpand"
							@node-collapse="nodeCollapse"
							:default-checked-keys="defaultCheckedKeys"
							:default-expanded-keys="defaultExpandedKeys"
							@current-change="treefunc_current_change"
						>
							<div class="css-fc _css-treenode-content" slot-scope="{ node, data }">
								<div :title="node.label" class="css-ml4 _css-treenodelabel">
									<el-tooltip popper-class="css-no-triangle" v-if="data.RoomName.length > 5" effect="dark" :content="data.RoomName" placement="top-left">
										<div class="_css-treenodellabelname overflow-point">
											{{ data.RoomName }}
										</div>
									</el-tooltip>
									<div class="_css-treenodellabelname" v-else>{{data.RoomName}}</div>
								</div> 
								<div @click.stop="func_tree_showmenu($event, data)" class="_css-treenode-menubtn icon-interface-list" ></div>
							</div>
						</el-tree>
						 <!-- <el-tree lazy :load="loadNode" :props="props" node-key="id">
      					</el-tree> -->
						
						<div class="first-dialog" :style="treeAddDialogStyle()" v-if="treeAddDialogShow">
							<div class="_css-flow-contextmenu-in" @click.stop="addArchivesRoom('clickID')">
								<div class="_css-flow-contextmenu-inicon icon-interface-addnew"></div>
								<div class="_css-flow-contextmenu-intext">新增</div>
							</div>
							<div class="_css-flow-contextmenu-in" @click.stop="addArchivesRoom('clickID','edit')">
								<div class="_css-flow-contextmenu-inicon icon-interface-edit"></div>
								<div class="_css-flow-contextmenu-intext">编辑</div>
							</div>
							<div class="_css-flow-contextmenu-in" @click.stop="delArchivesRoomSure">
								<div class="_css-flow-contextmenu-inicon icon-interface-model-delete"></div>
								<div class="_css-flow-contextmenu-intext">删除</div>
							</div> 
						</div>
					</div>
				</div>
			</div>
			<!-- //左右分栏 -->

			<!-- 高度为40px的按钮区域 -->
			<div class="_css-btns" v-if="false">
				<div class="_css-btns-inner">
					<!-- 新增及导入按钮 -->
					<div class="_css-btn-add">
						新增 / 导入
						<div class="_css-btn-add-items">
							<div
								class="_css-btn-add-item"
								@click.stop="itemclick('-1', '', '', '', 'rgba(28, 50, 75, 1)')"
							>
								新增字典项
							</div>
							<div class="_css-btn-add-item">导入字典项</div>
						</div>
					</div>
					<!-- //新增及导入按钮 -->

					<!-- 右侧的筛选项 -->
					<CompsSelect2
						:positionstyleobj="{ position: 'absolute', right: '0' }"
						height="40px"
						width="276px"
						backgroundColor="rgba(0,0,0,0.02)"
						:dataSource="[
							{ id: 'IssueStatue', text: '问题状态' },
							{ id: 'IssueType', text: '问题分类' },
						]"
						classname="_css-changedic-showtype"
						:showtext="extdata.dictype_select_text"
						@onselected="_dicshowtype_select"
					></CompsSelect2>
					<!-- //右侧的筛选项 -->
				</div>
			</div>
			<!-- //高度为40px的按钮区域 -->

			<!-- 数据区域 -->
			<div class="_css-dataarea css-miniscroll" v-if="false">
				<div
					class="_css-dataitem"
					v-for="bdid in extdata.dicitems"
					:key="bdid.ItemDetailId"
					@click.stop="
						itemclick(
							bdid.ItemDetailId,
							bdid.ItemName,
							bdid.ItemValue,
							bdid.SortCode,
							bdid.ItemCode
						)
					"
				>
					<div
						class="_css-dataitem-icon"
						:style="getStyleOfBdiditem(bdid)"
					></div>
					<div class="_css-dataitem-main">
						<div class="_css-dataitem-main-top">{{ bdid.ItemName }}</div>
						<div class="_css-dataitem-main-bottom">{{ bdid.ItemValue }}</div>
					</div>
				</div>
			</div>
			<!-- //数据区域 -->
		</div>

		<!-- 对话框区域 -->
		<CompsModifyDicItem
			v-if="extdata.isshowing_editdicitem == true"
			:title="extdata.isshowing_editdicitem_title"
			@oncancel="_oncancel"
			@onwarning="_warning"
			@onok="_onok"
			:id="extdata.isshowing_dicitemid"
			:placeholdername="extdata.isshowing_dicitemplaceholdertext"
			:name="extdata.isshowing_dicitemtext"
			:val="extdata.isshowing_dicitemval"
			:sort="extdata.isshowing_dicitemsort"
			:colorstr="extdata.isshowing_dicitemcolor"
			:hidecoloredit="extdata.isshowing_hidecoloredit"
		></CompsModifyDicItem>
		<!-- //对话框区域 -->

		<zdialog-function
			:init_title="extdata.isshowing_editdicitem_title"
			:init_zindex="1003"
			:init_innerWidth="350"
			:init_width="350"
			init_closebtniconfontclass="icon-suggested-close"
			@onclose="archivesRoomShowEdit = false"
			v-if="archivesRoomShowEdit"
		>
			<div
				slot="mainslot"
				class="_css-addingnameinput-ctn"
				@mousedown="_stopPropagation($event)"
			>
				<div class="_css-line _css-line-name">
					<div class="_css-title _css-title-flowname">位置名称：</div>
					<div class="_css-fieldvalue _css-fieldvaluename _css-selectednode">
						<div class="_css-fieldvalue _css-fieldvaluename _css-selectednode">
							<el-input
								@mousedown="_stopPropagation($event)"
								placeholder="请输入位置名称"
								v-model="RoomName">
							</el-input>
						</div>
					</div>
				</div>  
			</div>

			<div slot="buttonslot" class="_css-flowAddBtnCtn">
				<zbutton-function
					:init_text="'保存'"
					:init_fontsize="14"
					:debugmode="true"
					:init_height="'40px'"
					:init_width="'120px'"
					@onclick="func_saveedit"
				>
				</zbutton-function>
				<zbutton-function
					:init_text="'取消'"
					:init_color="'rgba(24, 144, 255)'"
					:init_bgcolor="'#fff'"
					:init_fontsize="14"
					:debugmode="true"
					:init_height="'40px'"
					:init_width="'120px'"
					@onclick="func_canceledit"
				>
				</zbutton-function>
			</div>
		</zdialog-function> 
	</div>
</template>
<script>
import CompsEloSelect from "@/components/CompsElOverride/CompsEloSelect";
import CompsSelect2 from "@/components/CompsCommon/CompsSelect2";
import CompsModifyDicItem from "@/components/CompsCommon/CompsModifyDicItem";
export default {
	components: {
		CompsEloSelect,
		CompsSelect2,
		CompsModifyDicItem,
	},
	data() {
		return {
			extdata: {
				status_count: 0, // 问题状态条目数
				type_count: 0, // 问题类型条目数
				type_unitcount: 0, // 单位类型条数
				type_ArchivesRoom: 0, // 档案
				dicitems: [], // 当前已加载的数据（同一时间加载一种类型数据，比如问题分类或问题状态
				isshowing_editdicitem_title: "", // 修改字典项或添加字典项
				dictype_select_id: "IssueStatue", // 选中的字典类型ID
				dictype_select_text: "问题状态", // 选中的字典类型文本
				isshowing_dicitemid: "-1", // 正在显示字典项的编辑对话框的bdid_id，-1为新增
				isshowing_dicitemtext: "", // 正在显示字典项的编辑对话框的bdid_text
				isshowing_dicitemplaceholdertext: "", // 正在显示字典项的编辑对话框的提示文字
				isshowing_dicitemval: "", // 正在显示字典项的编辑对话框的bdid_val
				isshowing_dicitemsort: "", // 正在显示字典项的编辑对话框的bdid_sort
				isshowing_dicitemcolor: "", // 正在显示字典项的编辑对话框的bdid_colorstr
				isshowing_editdicitem: false, // 正在显示字典项的编辑对话框
				isshowing_hidecoloredit: false, // 正在显示字典项的编辑颜色
			},
			archivesRoomData: [],
			defaultProps: {
				children: "Children",
				label: "RoomName",
				isLeaf: "isLeaf",
			},
			defaultCheckedKeys: [],
			defaultExpandedKeys: [],
			RoomName: '',
			archivesRoomShowEdit: false,
			treeAddDialogShow: false,
			selectTreeData: {},
			treeAddDialogPos: {
				top: 0,
				left: 0,
			},
			editDialogTrue: false, // true 编辑   false 新增
			editRoomId: '',
		};
	},
	computed: {
		 
		instyleobj: {
			get() {
				var _this = this;
				var _so = {};
				_so["width"] = _this.inwidth;
				_so["height"] = _this.inheight;
				return _so;
			},
		},
		styleobj: {
			get() {
				var _this = this;
				var _so = {};
				_so["height"] = _this.height;
				_so["width"] = _this.width;
				_so["background-color"] = _this.backgroundColor;
				return _so;
			},
		},
	},
	methods: {
		func_sortchange(ev, item, type) {
			ev && ev.stopPropagation && ev.stopPropagation();
			var _this = this;
			console.log(item, type);

			// 调用接口：提交修改，重新加载
			var _para = {
				Token: _this.$staticmethod.Get("Token"),
				Type: type.toString(),
				ItemDetailId: item.ItemDetailId,
			};
			var _url = `${window.bim_config.webserverurl}/api/User/Issue/ExchangeStatusDicSort`;
			_this
				.$axios({
					url: _url,
					method: "post",
					data: _this.$qs.stringify(_para),
				})
				.then((x) => {
					if (x.data.Ret > 0) {
						// 刷新当前列表
						_this.loadCompanyDicDatas();
					} else {
						_this.$message.error(x.data.Msg);
					}
				})
				.catch((x) => {
					debugger;
					console.error(x);
				});
		},

		func_cannotup(index) {
			var _this = this;
			if (index == 1) {
				return true;
			}
			return false;
		},

		func_cannotdown(index) {
			var _this = this;
			if (index == _this.extdata.dicitems.length - 2) {
				return true;
			}
			return false;
		},

		func_notheadeithertail(item, index) {
			var _this = this;
			if (index == 0) {
				return false;
			}
			if (index == _this.extdata.dicitems.length - 1) {
				return false;
			}
			return true;
		},
		_stopPropagation(ev) {
			ev && ev.stopPropagation && ev.stopPropagation();
		},
		getDicIssueTypeStyle(item) {
			var _this = this;
			var _s = {};
			if (_this.extdata.dictype_select_id == "IssueStatue") {
				// 如果是“关闭（问题状态）”
				if (item.ItemName == "关闭") {
					return _s;
				} else {
					// 背景色，文字色，边框
					if (item.ItemCode) {
						_s["color"] = item.ItemCode;
            			_s['background-color'] = item.ItemCode.replace('1)', '0.1)');
						_s["border"] = "1px solid " + item.ItemCode.replace("1)", "0.45)"); //rgba(28, 50, 75,1)
						return _s;
					}
				}
			} else if (_this.extdata.dictype_select_id == "IssueTab") {
				_s["background-color"] = "rgba(24,144,255,.1)";
				_s["color"] = "#1890FF";
				return _s;
			} else if(_this.extdata.dictype_select_id == "unitType"){
				if (item.ItemCode) {
					_s["color"] = item.ItemCode;
					_s['background-color'] = item.ItemCode.replace('1)', '0.1)');
					_s["border"] = "1px solid " + item.ItemCode.replace("1)", "0.45)"); //rgba(28, 50, 75,1)
					return _s;
				}else{
					_s["background-color"] = "rgba(24,144,255,.1)";
					_s["color"] = "#1890FF";
					return _s;
				}
			}else {
				_s["background-color"] = item.ItemCode;
				return _s;
			}
		},
		getStyleOfBdiditem(bdid) {
			let _s = {};
			if (bdid.ItemCode != "") {
				_s["background-color"] = bdid.ItemCode;
			}
			return _s;
		},
		dodelete(id) {
			let _this = this;
			let _Token = _this.$staticmethod.Get("Token");
			let _dictype_select_id = _this.extdata.dictype_select_id;
			let _item_id = id;

			_this
				.$axios({
					method: "post",
					url: `${this.$issueBaseUrl.RemoveDicItem}?token=${_Token}`,
					data: {
						DictypeSelectId: _dictype_select_id,
						ItemId: _item_id,
            OrganizeId: _this.companyId,
					},
				})
				.then((x) => {
					if (x.status == 200) {
						if (x.data.Ret > 0) {
							_this.$message.success("删除成功");
							_this.extdata.isshowing_editdicitem = false;
							_this.loadCompanyDicDatas();
						} else {
							_this.$message.error(`删除失败: (${x.data.Msg})`);
						}
					} else {
						_this.$message.error(`删除失败(${x.status})`);
					}
				})
				.catch((x) => {});
		},
		dodeleteTab(id) {
			let _this = this;
			let _Token = _this.$staticmethod.Get("Token");
			let _bct_guid = id;
			_this
				.$axios({
					method: "post",
					url: `${_this.$configjson.webserverurl}/api/BIMComp/BIMCompTag/RemoveTag`,
					data: _this.$qs.stringify({
						Token: _Token,
						bct_guid: _bct_guid,
					}),
				})
				.then((x) => {
					if (x.status == 200) {
						if (x.data.Ret > 0) {
							_this.$message.success("删除成功");
							_this.extdata.isshowing_editdicitem = false;
							_this.loadCompanyDicDatas();
						} else {
							_this.$message.error(`删除失败: (${x.data.Msg})`);
						}
					} else {
						_this.$message.error(`删除失败(${x.status})`);
					}
				})
				.catch((x) => {});
		},
		// 即将删除
		_warning(id) {
			var _this = this;
			var _item_id = id;

			_this
				.$confirm("确认删除该数据", "操作确认", {
					confirmButtonText: "确定",
					cancelButtonText: "取消",
					type: "warning",
				})
				.then((x) => {
					if (_this.extdata.dictype_select_id == "IssueTab") {
						_this.dodeleteTab(_item_id);
					} else if( _this.extdata.dictype_select_id == 'unitType'){
						//  新增
						this.delUnit(_item_id)
						
					}else {
						_this.dodelete(_item_id);
					}
				})
				.catch((x) => {});
		},

		dosave(saveobj) {
			let _this = this;
			let _Token = _this.$staticmethod.Get("Token");
			let _dictype_select_id = _this.extdata.dictype_select_id;
			let _item_id = saveobj.item_id;
			let _item_name = saveobj.item_name;
			let _item_val = saveobj.item_val;
			let _item_sort = saveobj.item_sort;
			let _item_color_ItemCode = saveobj.item_color_ItemCode;
			let _companyId = _this.companyId;

			_this
				.$axios({
					method: "post",
					url: `${this.$issueBaseUrl.SaveDicItem}?token=${_Token}`,
					data: {
						DictypeSelectId: _dictype_select_id,
						ItemId: _item_id,
						ItemName: _item_name,
						ItemVal: _item_val,
						ItemSort: 0,
						ItemColorItemCode: _item_color_ItemCode,
						OrganizeId: _companyId,
					},
				})
				.then((x) => {
					if (x.status == 200) {
						if (x.data.Ret > 0) {
							_this.$message.success("保存成功");
							_this.loadCompanyDicDatas();
							_this.extdata.isshowing_editdicitem = false;
						} else {
							_this.$message.error(`保存失败 (${x.data.Msg})`);
						}
					} else {
						_this.$message.error(`保存失败 (${x.status})`);
					}
				})
				.catch((x) => {
					_this.$message.error(`保存失败`);
				});
		},
		dosaveTab(saveobj) {
			let _this = this;
			let _token = _this.$staticmethod.Get("Token");
			let _bct_name = saveobj.item_name;
			let _bct_organizeId = _this.companyId;
			let _bct_guid = null;
			if (saveobj.item_id == "-1") {
				_bct_guid = "";
			} else {
				_bct_guid = saveobj.item_id;
			}

			_this
				.$axios({
					method: "post",
					url: `${_this.$configjson.webserverurl}/api/BIMComp/BIMCompTag/AddOrModifyTag`,
					data: _this.$qs.stringify({
						Token: _token,
						bct_name: _bct_name,
						bct_organizeId: _bct_organizeId,
						bct_guid: _bct_guid,
					}),
				})
				.then((x) => {
					if (x.status == 200) {
						if (x.data.Ret > 0) {
							_this.$message.success("保存成功");
							_this.loadCompanyDicDatas();
							_this.extdata.isshowing_editdicitem = false;
						} else {
							_this.$message.error(`保存失败 (${x.data.Msg})`);
						}
					} else {
						_this.$message.error(`保存失败 (${x.status})`);
					}
				})
				.catch((x) => {
					_this.$message.error(`保存失败`);
				});
		},
		// _onok
		_onok(saveobj) {
			var _this = this;
			// 保存确认提示
			_this
				.$confirm("确认保存该数据", "操作确认", {
					confirmButtonText: "确定",
					cancelButtonText: "取消",
					type: "warning",
				})
				.then((x) => {
					if (_this.extdata.dictype_select_id == "IssueTab") {
						_this.dosaveTab(saveobj);
					} else if( _this.extdata.dictype_select_id == 'unitType'){
						//  item_id == '-1'=新增
						if(saveobj.item_id == '-1'){
							this.createUnit(saveobj)
						}else{
							this.updateUnit(saveobj)
						}
					}else {
						_this.dosave(saveobj);
					}
				})
				.catch((x) => {});
		},

		// 取消字典项编辑
		_oncancel() {
			var _this = this;
			_this.extdata.isshowing_editdicitem = false;
		},

		// 点击某一个字典项（弹出这个字典项的编辑窗口）
		itemclick(bdid_id, bdid_text, bdid_val, bdid_sort, bdid_color) {
			var _this = this;
			console.log(bdid_id);
			if (bdid_id == -1) {
				_this.extdata.isshowing_editdicitem_title = "新增" + this.extdata.dictype_select_text;
			} else {
				_this.extdata.isshowing_editdicitem_title = "修改" + this.extdata.dictype_select_text;
			}

			if(this.extdata.dictype_select_id == 'ArchivesRoom'){
				this.addArchivesRoom('0')
				return
			}
			
			_this.extdata.isshowing_dicitemid = bdid_id;
			_this.extdata.isshowing_dicitemtext = bdid_text;
			_this.extdata.isshowing_dicitemval = bdid_val;
			_this.extdata.isshowing_dicitemsort = (bdid_sort || "").toString();
			_this.extdata.isshowing_dicitemcolor = bdid_color;
			
			let str = ''
			if( this.extdata.dictype_select_text == '问题类型'){
				str = '类型名称'
			}else if( this.extdata.dictype_select_text == '问题状态'){
				str = '状态名称'
			}else if( this.extdata.dictype_select_text == '单位类型'){
				str = '单位名称'
				_this.extdata.isshowing_dicitemcolor = '';
			}
			_this.extdata.isshowing_dicitemplaceholdertext = str;
			
			if (
				_this.extdata.dictype_select_id == "IssueStatue" &&
				_this.extdata.isshowing_dicitemsort == "99999"
			) {
				_this.extdata.isshowing_hidecoloredit = true;
			} else if (_this.extdata.dictype_select_id == "IssueTab") {
				_this.extdata.isshowing_hidecoloredit = true;
			} else if (_this.extdata.dictype_select_id == "unitType") {
				_this.extdata.isshowing_hidecoloredit = true;
			}else {
				_this.extdata.isshowing_hidecoloredit = false;
			}

			_this.extdata.isshowing_editdicitem = true;
		},
		_dicshowtype_select(obj) {
			var _this = this;
			_this.extdata.dictype_select_id = obj.id;
			_this.extdata.dictype_select_text = obj.text;

			_this.loadCompanyDicDatas();
		},

		// 根据 extdata.dictype_select_id 加载企业字典数据，存储于 dicitems 中
		loadCompanyDicDatas() {
			var _this = this;
			_this.extdata.dicitems = [];
			if (_this.extdata.dictype_select_id == "IssueStatue") {
				_this.loadCompanyDicDatas_IssueStatus();
			} else if (_this.extdata.dictype_select_id == "IssueType") {
				_this.loadCompanyDicDatas_IssueType();
			}else if(_this.extdata.dictype_select_id == "unitType"){
				// 单位类型
				_this.loadCompanyDicDatas_unit()
			}else if(_this.extdata.dictype_select_id == "ArchivesRoom"){
				this.loadCompanyDicDatas_ArchivesRoom()
			}
		},
		// 加载 企业字典数据：单位类型
		async loadCompanyDicDatas_unit(){
			let data = {
				organizeId: this.companyId,
				categoryType:"participating_unit", // 后端说固定值
			}
			const res = await this.$api.GetBaseCategoryList(data)
			if(res.Ret == 1){
				this.extdata.type_unitcount = res.Data.length;
				let _listdata = res.Data;
				const modifiedArray = _listdata.map(item => ({
					ItemName: item.CategoryName,
					CategoryType: item.CategoryType,
					ItemCode: item.ExtJson,
					ItemDetailId: item.Id,
					OrganizeId: item.OrganizeId,
					ItemValue: '',
				}));
				// 为了数据渲染方便。在这里转换obj的key
				this.extdata.dicitems = modifiedArray
			}
		},
		async createUnit(param){
			let _data = {
				OrganizeId: this.companyId,
				CategoryName: param.item_name,
				CategoryType: "participating_unit",
				ExtJson: param.item_color_ItemCode, 
			}
			const res = await this.$api.postCreateBaseCategory(_data);
			if(res.Ret == 1){
				this.loadCompanyDicDatas_unit();
				this.$message.success('添加成功')
				this.extdata.isshowing_editdicitem = false;
			}
		},
		async updateUnit(param){
			let _data = {
				Id: param.item_id,
				CategoryName: param.item_name,
				ExtJson: param.item_color_ItemCode
			}
			const res = await this.$api.postUpdateBaseCategory(_data);
			if(res.Ret == 1){
				this.loadCompanyDicDatas_unit();
				this.$message.success('编辑成功')
				this.extdata.isshowing_editdicitem = false;
		}
	
		},
		async delUnit(id){
			const res = await this.$api.postDeleteBaseCategory(id);
			if(res.Ret == 1){
				this.loadCompanyDicDatas_unit();
				this.$message.success('删除成功')
				this.extdata.isshowing_editdicitem = false;
			}
		},
		// 档案字典
		async loadCompanyDicDatas_ArchivesRoom(id,node, resolve){
			let data = {
				OrganizeId: this.companyId,
				ParentId: id || '', 
			}
			const res = await this.$api.GetArchivesRoomTree(data);
			if(res.Ret == 1){
				this.extdata.type_ArchivesRoom = res.Data.length;
				this.archivesRoomData = res.Data; 
			}
		},
		// 创建档案
		async createArchivesRoom(){
			let data = {
				OrganizeId: this.companyId,
				ParentId: this.editRoomId,
				RoomName: this.RoomName
			}
			const res = await this.$api.postArchivesRoomCreate(data);
			if(res.Ret == 1){
				this.$message.success(res.Msg)
				this.loadCompanyDicDatas_ArchivesRoom()
				this.func_canceledit()
			}else{
				this.$message.error(res.Msg)
			}
		},
		// 编辑档案
		async modifyArchivesRoom(id){
			let data = {
				Id: id,
				RoomName: this.RoomName
			}
			const res = await this.$api.postArchivesRoomModify(data);
			if(res.Ret == 1){
				this.$message.success(res.Msg)
				this.loadCompanyDicDatas_ArchivesRoom()
				this.func_canceledit()
			}else{
				this.$message.error(res.Msg)
			}
		},
		delArchivesRoomSure(){
			this.treeAddDialogShow = false
			this
				.$confirm("确定删除选定的数据？", "操作确认", {
					confirmButtonText: "确认",
					cancelButtonText: "取消",
					type: "warning"
				})
			.then(x => {
				this.delArchivesRoom(this.selectTreeData.Id);
			})
			.catch(x => {});
		},
		// 删除档案
		async delArchivesRoom(){
			const res = await this.$api.postArchivesRoomDelete(this.selectTreeData.Id);
			if(res.Ret == 1){
				this.loadCompanyDicDatas_ArchivesRoom()
				this.$message.success(res.Msg) 
				this.closeAll()
			}else{
				this.$message.error(res.Msg)
			}
		},
		// 档案详情
		async infoArchivesRoom(id){
			let data = {id: id}
			const res = await this.$api.GetArchivesRoomInfo(data);
			if(res.Ret == 1){
				this.RoomName = res.Data.RoomName;
				this.editDialogTrue = true
			}
		},
		// 新增档案
		addArchivesRoom(id,edit){
			if(id == 'clickID'){
				id = this.selectTreeData.Id;
			}
			if(edit && edit == 'edit'){
				this.extdata.isshowing_editdicitem_title = "修改" + this.extdata.dictype_select_text;
				this.infoArchivesRoom(id)
				// this.RoomName = this.selectTreeData.RoomName;
				// this.editDialogTrue = true
			}else{
				this.extdata.isshowing_editdicitem_title = "新增" + this.extdata.dictype_select_text;
				this.editDialogTrue = false
			}
			this.archivesRoomShowEdit = true
			this.editRoomId = id
			this.treeAddDialogShow = false

		},
		func_saveedit(){ 
			if(this.editDialogTrue){
				this.modifyArchivesRoom(this.editRoomId);
			}else{
				this.createArchivesRoom();
			}
		},
		func_canceledit(){
			this.archivesRoomShowEdit = false;
			this.treeAddDialogShow = false;
			this.RoomName = '';
		},
		treefunc_current_change(data){ 
			this.treeAddDialogShow = false;
		},
		treeAddDialogStyle(){
			let _s = {};
			_s["left"] = this.treeAddDialogPos.left + 'px';
			_s["top"] = this.treeAddDialogPos.top + 'px';
			return _s;
		},
 
		openSavedNodes(treeEl) {
			// 从缓存中获取需要展开的节点的id列表
			const savedNodeIds = JSON.parse(localStorage.getItem('expandedNodes')) || [];
			// 递归展开节点
			const expandNode = (node, ids) => {
				if (ids.includes(node.id)) {
				this.$refs.tree.$tree.store.setExpanded(node, true);
				}
				if (node.children) {
				node.children.forEach(child => {
					expandNode(child, ids);
				});
				}
			};
			this.treeData.forEach(node => {
				expandNode(node, savedNodeIds);
			});
		},
		nodeExpand(data){
			this.defaultExpandedKeys.push(data.Id) 
		},
		nodeCollapse(data){
			let index = this.defaultExpandedKeys.indexOf(data.Id)
			this.defaultExpandedKeys.splice(index, 1);
		},
		func_tree_showmenu(ev,data){
			this.treeAddDialogPos.top = ev.clientY - 30;
			this.treeAddDialogPos.left = ev.clientX + 20; 
			this.treeAddDialogShow = true
			this.selectTreeData = data;
		},
		closeAll(){
			this.archivesRoomShowEdit = false;
			this.treeAddDialogShow = false;

		},
		// 加载 企业字典数据：问题分类
		loadCompanyDicDatas_IssueType(onlyloadcount) {
			var _this = this;
			var _Token = _this.$staticmethod.Get("Token");
			var _OrganizeId = _this.companyId;
			_this
				.$axios({
					method: "get",
					url: `${window.bim_config.webserverurl}/api/User/Issue/GetIssueTypes?organizeId=${_OrganizeId}&token=${_Token}`,
				})
				.then((x) => {
					if (x.status == 200) {
						if (x.data.Ret > 0) {
							//debugger;
							if (onlyloadcount) {
								_this.extdata.type_count = x.data.Data.length;
							} else {
								_this.extdata.dicitems = x.data.Data;
								_this.extdata.type_count = x.data.Data.length;
							}
						}
					}
				})
				.catch((x) => {});
		},

		// 加载 企业字典数据：问题状态
		loadCompanyDicDatas_IssueStatus() {
			let _this = this;
			let _Token = _this.$staticmethod.Get("Token");
			let _OrganizeId = _this.companyId;
			_this
				.$axios({
					method: "get",
					url: `${this.$issueBaseUrl.GetIssueStatus}?organizeId=${_OrganizeId}&token=${_Token}`,
				})
				.then((x) => {
					if (x.status == 200) {
						if (x.data.Ret > 0) {
							//debugger;
							_this.extdata.dicitems = x.data.Data;
							_this.extdata.status_count = x.data.Data.length;
						}
					}
				})
				.catch((x) => {});
		},
	},
	mounted() {
		var _this = this;
		window.dicvue = _this;
		_this.loadCompanyDicDatas_IssueType(true);
		_this.loadCompanyDicDatas();
		_this.loadCompanyDicDatas_unit();
		_this.loadCompanyDicDatas_IssueStatus();
		_this.loadCompanyDicDatas_ArchivesRoom()
	},
	props: {
		classname: {
			type: String,
			required: true,
		},
		width: {
			type: String,
			required: true,
		},
		height: {
			type: String,
			required: true,
		},
		backgroundColor: {
			type: String,
			required: true,
		},
		inwidth: {
			type: String,
			required: true,
		},
		inheight: {
			type: String,
			required: true,
		},
		companyId: {
			type: String,
			required: true,
		},
	},
};
</script>
<style scoped>
._css-issuestatus-sortbtn {
	min-width: 24px;
	padding: 0 8px 0 8px;
	border-radius: 4px;
	margin-right: 12px;
	color: #1890ff;
	border: 1px solid #1890ff;
	font-size: 12px;
	height: 20px;
	line-height: 20px;
	background-color: transparent;
	cursor: pointer;
}

._css-issuestatus-sortbtn:not(._css-dis):hover {
	color: #fff;
	background-color: #1890ff;
}

._css-issuestatus-sortbtn._css-dis {
	cursor: not-allowed;
	opacity: 0.3;
}

._css-issuestatus-sortbtns {
	flex: 1;
	height: 100%;
	margin-left: 12px;
	display: flex;
	justify-content: flex-end;
	align-items: center;
}
._css-textareavalue {
	height: auto;
	position: relative;
}
._css-textareaself {
	resize: none;
	height: 120px;
}
._css-title._css-title-name {
	width: 80px;
}
._css-addwholebtn {
	margin-left: 24px;
	height: 22px;
	display: flex;
	align-items: center;
	color: rgba(24, 144, 255, 1);
	cursor: pointer;
}
._css-comdic-item-handle {
	height: 20px;
	width: 20px;
	margin-left: 12px;
}
._css-comdic-item-text {
	height: 30px;
	width: 113px;
	margin-left: 6px;
	box-sizing: border-box;
	font-size: 12px;
	border-radius: 2px;
	display: flex;
	align-items: center;
	justify-content: space-around;
}
._css-comdic-item-text-in {
	width: 100%;
	text-overflow: ellipsis;
	overflow-x: hidden;
	white-space: nowrap;
	padding-left: 4px;
	padding-right: 4px;
}
._css-comdic-item-text:not(.custom).close {
	color: rgba(250,84,28,1);
	background-color: rgba(250, 84, 28, 0.1);
	border: 1px solid rgba(250, 84, 28, 0.45);
}
._css-comdic-item-text:not(.custom) {
	color: rgba(24, 144, 255, 1);
	background-color: rgba(24, 144, 255, 0.1);
	border: 1px solid rgba(24, 144, 255, 0.45);
}
._css-comdic-item-text.custom {
	color: #fff;
	background-color: #000;
}
._css-comdic-diccateitemtext {
	flex: 1;
	margin-left: 16px;
	text-align: left;
	height: 22px;
	line-height: 22px;
	color: rgba(0, 0, 0, 0.85);
	font-size: 14px;
	font-weight: 400;
}
._css-comdic-diccateitemnumber {
	margin-left: 16px;
	margin-right: 16px;
	width: 24px;
	height: 20px;
	border-radius: 4px;
	background-color: rgba(24, 144, 255, 1);
	color: #fff;
	font-size: 12px;
	line-height: 20px;
}
._css-comdic-righttitle {
	height: 48px;
	width: calc(100% - 24px);
	margin: 0 24px;
	display: flex;
	align-items: center;
	position: relative;
	background: #F5F6FA;
}
._css-addbtnicon {
	height: 20px;
	width: 20px;
	font-size: 20px;
}
._css-addbtntext {
	height: 20px;
	margin-left: 8px;
	font-size: 14px;
	color: rgba(24, 144, 255, 1);
	line-height: 20px;
}
._css-comdic-rightbody {
	height: calc(100% - 78px);
	width: calc(100% - 24px);
	overflow-y: auto;
	/* padding-left: 24px; */
	box-sizing: border-box;
	background: #F5F6FA;
    margin: 0 24px;
	border-top: 1px solid #DDDDDD;
	padding: 10px 0;
}
._css-comdic-rightitem {
	width: 100%;
	height: 64px;
	box-sizing: border-box;
	border-bottom: 1px solid rgba(0, 0, 0, 0.04);
	cursor: pointer;
	display: flex;
	align-items: center;
}
._css-comdic-rightitem:hover {
	background-color: rgba(0, 0, 0, 0.04);
}
._css-comdic-lefttitle {
	height: 48px;
	width: 100%;
	position: relative;
}
._css-comdic-lefttitle-text {
	position: absolute;
	top: -2px;
	left: 0;
	width: 196px;
	height: 28px;
	line-height: 28px;
	color: rgba(0, 0, 0, 0.85);
	text-align: left;
	font-size: 20px;
	font-weight: 600;
}
/* 字典分类左侧项 */
._css-comdic-diccategory {
	color: rgba(0, 0, 0, 0.65);
	height: 50px;
	display: flex;
	align-items: center;
	cursor: pointer;
}
/* 字典分类左侧项 hover 时仅改变背景色 */
._css-comdic-diccategory:hover {
	background-color: rgba(0, 0, 0, 0.04);
}
/* 字典分类左侧项选中时仅改变字色 */
._css-comdic-diccategory.clicked {
	background-color: rgba(0, 0, 0, 0.06);
}
._css-comdic-leftbody {
	height: calc(100% - 48px);
	width: 100%;
	box-sizing: border-box;
	padding-right: 24px;
	overflow-y: auto;
}
._css-comdic-left {
	width: 264px;
	height: 100%;
}
 
._css-comdic-right {
	height: 100%; 
	width: calc(100% - 264px);
	box-sizing: border-box;
	height: 100%;
	border-left: 1px solid rgba(0, 0, 0, 0.09);
}
._css-comdic-leftandright {
	width: 100%;
	height: 100%;
	display: flex;
}
._css-single-sel2 {
	height: 100%;
	width: 100%;
}
._css-dataitem-main {
	height: 44px;
	margin-left: 8px;
	display: flex;
	flex-direction: column;
	justify-content: space-between;
}
._css-dataitem-main-top {
	width: 100%;
	height: 22px;
	text-align: left;
	font-size: 12px;
	color: rgba(0, 0, 0, 0.85);
	font-weight: 600;
	line-height: 22px;
}
._css-dataitem-main-bottom {
	width: 100%;
	height: 22px;
	text-align: left;
	line-height: 22px;
	color: rgba(0, 0, 0, 0.45);
}
._css-dataitem-icon {
	width: 40px;
	height: 40px;
	border-radius: 4px;
	margin-left: 24px;
	border: 1px solid rgba(0, 0, 0, 0.2);
}
._css-dataitem {
	height: 64px;
	width: 240px;
	display: flex;
	align-items: center;
	position: relative;
	cursor: pointer;
}
._css-dataitem:hover {
	background-color: rgba(0, 0, 0, 0.02);
}
._css-dataarea {
	flex: 1;
	margin-top: 32px;
	overflow-y: auto;
	display: flex;
	flex-wrap: wrap;
	align-content: flex-start;
}
._css-btn-add-item {
	height: 40px;
	line-height: 40px;
	width: 100%;
	color: rgba(0, 0, 0, 0.85);
	cursor: pointer;
}
._css-companydic {
	display: flex;
	align-items: center;
	justify-content: space-around;
	background-color: #fff;
	margin: 16px auto;
}
._css-btns {
	height: 40px;
}
._css-btns-inner {
	height: 100%;
	margin-left: auto;
	margin-right: auto;
	position: relative;
}
._css-btn-add-item:hover {
	background-color: rgba(0, 0, 0, 0.04);
}
._css-btn-add-items {
	position: absolute;
	padding-top: 4px;
	padding-bottom: 4px;
	background-color: rgba(255, 255, 255, 1);
	box-shadow: 0px 1px 3px 0px rgba(0, 21, 41, 0.12);
	border-radius: 2px;
	width: 100%;
	top: calc(100% + 0px);
	left: 0;
	display: none;
	z-index: 2;
}
._css-btn-add:hover ._css-btn-add-items {
	display: block;
}
._css-btn-add {
	width: 120px;
	height: 40px;
	border-radius: 2px;
	background-color: #1890ff;
	color: #fff;
	line-height: 40px;
	font-size: 14px;
	cursor: pointer;
	position: absolute;
}
._css-companydic-in {
	display: flex;
	flex-direction: column;
	width: 100%;
	height: calc(100% - 100px);
	margin: 0 16px;
}
.first-dialog{
	position: absolute;
	background-color: #fff;
	width: 120px;
	box-shadow: 0 0 8px 0 rgba(0, 0, 0, .15);
    border-radius: 4px;
	z-index: 5;
}
._css-flow-contextmenu-in {
    height: 100%;
    width: 100%;
    cursor: pointer;
    display: flex;
    align-items: center;
}
._css-flow-contextmenu-inicon {
    width: 16px;
    line-height: 40px;
    height: 100%;
    margin-left: 24px;
	color: #666666;
    font-size: 16px;
}

._css-flow-contextmenu-intext {
    margin-left: 8px;
    font-size: 14px;
    height: 100%;
    line-height: 40px;
	color: #666666;
}
._css-flow-contextmenu-in:not(._css-dis):hover {
    background-color: rgba(0, 0, 0, 0.04);
}
._css-customstyle{
	position: relative;
}

._css-treenode-menubtn {
    /* display: none; */
    visibility: hidden;
    font-size: 16px;
    height: 24px;
    width: 24px;
    line-height: 24px;
    text-align: center;
    margin-right: 8px;
	position: absolute;
    right: 20px;
}
._css-treenode-content,._css-treenodelabel{
	width: 98%;
}
._css-treenode-content:hover ._css-treenode-menubtn {
    /* display: block; */
    visibility: inherit;
}
._css-comdic-rightbody /deep/ .el-tree--highlight-current .el-tree-node.is-current>.el-tree-node__content{
	background-color: #FFFFFF !important;
	width: 98%;
}

._css-treenodellabelname{
	/* width: 790px; */
	text-align:left;
	font-family: PingFangSC, PingFang SC;
	font-weight: 400;
	font-size: 14px;
	color: rgba(0,0,0,0.9);
}
._css-comdic-rightbody /deep/ ._css-customstyle .el-tree-node__content{
	height: 40px;
	width: 98%;
}
</style>