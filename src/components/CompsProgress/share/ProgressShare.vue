<template>
<div class="Model_share" v-loading="isLoading">
    <div class="title">分享：<font class="l">{{shareName}}</font><i class="icon-suggested-close close" @click="$emit('CloseShare')"></i></div>
    <div class="con">
        <div class="p1">
            <div class="WX" id="qrcode">
              <img :src="shareImage" alt="">
            </div>
            <div class="info">
                <span class="t">请复制以下链接地址</span>
                <input id="txtWXUrl" v-model="WXUrl" type="text" />
                <!--<span class="t">{{(RoleInfo.public==1?'公开':'加密(密码：'+ShareObj.pwd+')')+' 且'+ EnableDaytext}}<font class="link" @click="openShareInfo">点击修改权限</font></span>-->
            </div>
        </div>
    </div>
    <div class="btns">
        <button class="white" @click="$emit('CloseShare')">取消</button>
        <button class="blue" @click="copyToTemp">复制链接</button>
    </div>

</div>
</template>

<script>
// import ShareInfo from '@/components/CompsProgress/share/ShareInfo'
// import QRCode from 'qrcodejs2'

export default {
    name:'ModelShare',
    components:{
        // ShareInfo,
    },
    data(){
        return{
            showShareInfo:false,
            RoleInfo:{
                public:1,
                enableDay:-1
            },//当前选择的分享设置
            EnableDaytext:"",
            WXUrl:"",
            ShareObj:{},//分享的对象 链接、是否公开、密码
            isLoading:false,
          shareImage: '',
        };
    },
    props:{
        shareName:{
            type:String,
            required:false
        },
        progressData: {
          type:Object,
          required:false
        }
    },
    methods:{
        copyToTemp()
        {
            var txt=document.getElementById('txtWXUrl');
            txt.select();
            document.execCommand("Copy");
            this.$message({type:"success",message:"复制成功"});
        },

        //获取分享的链接
        getShareLink() {
          this.WXUrl = `${window.location.origin}${window.bim_config.hasRouterFile}/#/Home/ProjectBoot/indexSingle2/${this.progressData.bop_organizeId}/${this.progressData.bop_modelId}/${this.progressData.bop_planId}`;
          this.shareImage = `${this.$urlPool.QRCode}?encodedUrl=${this.WXUrl}&token=${this.$staticmethod.Get("Token")}`;
          console.log(this.WXUrl)
        }
    },
    created(){
      this.getShareLink();
    }
}
</script>


<style scoped>
ul{padding: 0px;margin: 0px;}li{padding: 0px;margin: 0px;}
.Model_share{background-color: #fff;box-shadow:0px 13px 24px -17px rgba(11,41,62,0.8);border-radius:4px;font-family:PingFangSC-Medium;padding:0px 16px 16px 16px;}
.Model_share .title{width:100%;height:48px;font-size:16px;color:#333;line-height:48px;text-align:left;font-weight:bold; position: relative;overflow: hidden;}
.Model_share .title .close{width:18px;height:18px;position: absolute;right:15px;top:15px;display:block;}
.Model_share .title .close::before{float:left;width:18px;height:18px;display:block;text-align:left;text-indent:0px;cursor: pointer;}
.Model_share .con{height:calc(100% - 38px - 48px - 22px);margin-top:5px;overflow:hidden;line-height:50px;}

.Model_share .con .p1{width:100%;height:120px;}
.Model_share .con .p1 .WX{width:120px;height:100%;float:left;background-color: #f0f0f0;}
.Model_share .con .p1 .info{width:calc(100% - 120px - 30px);height:100%;float:left;margin-left:30px;font-size:0px;line-height:0px;}
.Model_share .con .p1 .info .t,.Model_share .con .p1 .info input{display:inline-block;width:100%;height:40px;line-height:40px;font-size:14px;text-align:left;}
.Model_share .con .p1 .info input{width:calc(100% - 2px);height:32px;border:1px solid rgba(24,144,255,1);outline:none;text-indent: 10px;}
.Model_share .con .p1 .info .link{color:rgba(24,144,255,1);cursor: pointer;margin-left:20px;}.Model_share .con .p1 .info .link:hover{opacity: 0.8;}

.Model_share .btns{width:100%;height:38px;line-height:38px;text-align:right;}
.Model_share .btns button{width:auto;height: 32px;border:none;outline: none;border-radius: 3px;cursor: pointer;font-size:12px; letter-spacing:1px;padding:0px 10px 0px 10px;}
.Model_share .btns button:hover{opacity:0.8;}
.Model_share .btns .blue{background-color:#1890FF;color:#fff; }
.Model_share .btns .white{background-color:transparent;color:#666;}
::-webkit-scrollbar{width:20px; height:8px;}
::-webkit-scrollbar-thumb{border-radius: 12px;border:6px solid transparent;box-shadow: 8px 0px 0px #A5ADB7 inset;}
::-webkit-scrollbar-thumb:hover {box-shadow: 8px 0px 0px #4A4A4A inset;}

.SI{width:612px;position: absolute;top:50%;left:50%;transform:translate(-50%,-50%);z-index:1000;background-color: #fff}
</style>
