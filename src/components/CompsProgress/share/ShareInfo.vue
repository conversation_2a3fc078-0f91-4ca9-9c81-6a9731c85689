<template>
    <div class="ShareInfo">
        <div class="title">修改权限：<font class="l">{{ModelName}}</font><i class="icon-suggested-close close" @click="$emit('CloseChangeRole')"></i></div>
        <div class="con">
            <div class="unit">
                <div class="t">分享形式</div>
                <div class="c">
                    <span class="option"><i :class="currentRoleInfo.public==1?'icon-checkbox-Radio opacity':'icon-checkbox-Radio-Selected'" @click="ChangePublic(0)"></i><font class="word1">加密</font><font class="word2">需输入访问密码访问</font></span>
                    <span class="option"><i :class="currentRoleInfo.public==0?'icon-checkbox-Radio opacity':'icon-checkbox-Radio-Selected'" @click="ChangePublic(1)"></i><font class="word1">公开</font><font class="word2">所有人都可以直接访问</font></span>
                </div>
            </div>
            <div class="unit">
               <div class="t">有效时间</div>
                <div class="c">
                    <span class="option"><i :class="currentRoleInfo.enableDay==-1?'icon-checkbox-Radio-Selected':'icon-checkbox-Radio opacity'" @click="currentRoleInfo.enableDay=-1"></i><font class="word1">永久有效</font><font class="word2">适用于演示模型</font></span>
                    <span class="option"><i :class="currentRoleInfo.enableDay==7?'icon-checkbox-Radio-Selected':'icon-checkbox-Radio opacity'" @click="currentRoleInfo.enableDay=7"></i><font class="word1">一周</font><font class="word2">适用于小组或部门内分享模型</font></span>
                    <span class="option"><i :class="currentRoleInfo.enableDay==1?'icon-checkbox-Radio-Selected':'icon-checkbox-Radio opacity'" @click="currentRoleInfo.enableDay=1"></i><font class="word1">一天</font><font class="word2">适用于隐私性较强的内部模型</font></span>
                </div>
            </div>
        </div>
        <div class="btns">
            <button class="white" @click.stop="_cancel" >取消</button>
            <button class="blue" @click="SaveRoleInfo">确定</button>
        </div>
    </div>
</template>
<script>
export default {
    name:"ShareInfo",
    data(){
        return{
            currentRoleInfo:{
                public:1,
                enableDay:-1
            },
            cacheroleinfo:{

            }
        };
    },
    mounted(){
        var _this = this;
        window.shareinfovue = _this;
              //debugger;
        _this.cacheroleinfo = _this.$staticmethod.DeepCopy(_this.RoleInfo);

        // 将 RoleInfo 的值，传递给用于显示的或正在编辑的权限值
        _this.currentRoleInfo = _this.$staticmethod.DeepCopy(_this.RoleInfo);
    },
    props:{
        RoleInfo:{
            type:Object,
            required:false
        },
        ModelName:{
            type:String,
            required:false
        }
    },
    methods:{
        ChangePublic(val) {
            this.currentRoleInfo.public=val;
        },
        _cancel() {
            this.$emit("ChangeRoleInfo",this.cacheroleinfo);
            this.$emit('CloseChangeRole');
        },
        SaveRoleInfo() {
            this.$emit("ChangeRoleInfo",this.currentRoleInfo);
            this.$emit('CloseChangeRole');
        }
    }
}
</script>
<style scoped>
ul{padding: 0px;margin: 0px;}li{padding: 0px;margin: 0px;}
.ShareInfo{overflow: hidden;background-color: #fff;box-shadow:0px 13px 24px -17px rgba(11,41,62,0.8);border-radius:4px;font-family:PingFangSC-Regular;padding:0px 16px 16px 16px;}
.ShareInfo .title{width:100%;height:48px;font-size:16px;color:#333;line-height:48px;text-align:left;font-weight:bold; position: relative;overflow: hidden;}
.ShareInfo .title .close{width:18px;height:18px;position: absolute;right:15px;top:15px;display:block;}
.ShareInfo .title .close::before{float:left;width:18px;height:18px;display:block;text-align:left;text-indent:0px;cursor: pointer;}
.ShareInfo .con{height:calc(100% - 38px - 48px - 22px);margin-top:5px;overflow:hidden;line-height:50px;height:auto;padding-bottom: 22px;}


.ShareInfo .con .unit{width:100%;height:auto;overflow: hidden;margin:22px 0px 0px 0px;}
.ShareInfo .con .unit .t{width:146px;height:22px;float:left;line-height:22px;}
.ShareInfo .con .unit .c{width:calc(100% - 146px - 24px);margin-left:24px;float: left;font-size:0px;line-height:0px;height:auto;}
.ShareInfo .con .unit .c .option{display: inline-block;width:calc(100% - 36px);margin-bottom:18px;text-align: left;padding-left:36px; position: relative;height:22px;line-height: 22px;}.ShareInfo .con .unit .c .option font{display: inline-block;}
.ShareInfo .con .unit .c .option i{position:absolute;left:0px;top:3px;} .opacity{opacity: 0.5;}
.ShareInfo .con .unit .c .option .word1{margin-right:8px;width:60px;height:100%;font-size:14px;}
.ShareInfo .con .unit .c .option .word2{font-size:12px;opacity: 0.7;}

.ShareInfo .btns{width:100%;height:38px;line-height:38px;text-align:right;}
.ShareInfo .btns button{width:auto;height: 32px;border:none;outline: none;border-radius: 3px;cursor: pointer;font-size:12px; letter-spacing:1px;padding:0px 10px 0px 10px;}
.ShareInfo .btns button:hover{opacity:0.8;}
.ShareInfo .btns .blue{background-color:#1890FF;color:#fff; }
.ShareInfo .btns .white{background-color:transparent;color:#666;}
::-webkit-scrollbar{width:20px; height:8px;}
::-webkit-scrollbar-thumb{border-radius: 12px;border:6px solid transparent;box-shadow: 8px 0px 0px #A5ADB7 inset;}
::-webkit-scrollbar-thumb:hover {box-shadow: 8px 0px 0px #4A4A4A inset;} 
</style>
