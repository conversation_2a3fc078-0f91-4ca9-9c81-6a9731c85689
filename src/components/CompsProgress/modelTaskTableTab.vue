<template>
  <div class="table-model-task">
    <el-tabs v-model="activeName" @tab-click="handleClick" class="table-tab-list">
      <el-tab-pane :label="startOrPause ? '实际已完成':'计划已完成'" name="first">
        <el-table
          fixed
          border
          :data="tableDatafinished"
          highlight-current-row
          :height="tabTableHeight"
          style="width: 100%">
          <el-table-column
            prop="TaskName"
            fixed
            :resizable="true"
            width=“600”
            label="任务名称">
            <template slot-scope="scope">
              <el-tooltip placement="right" popper-class="tooltip-model-hover">
                <div slot="content">{{scope.row.TaskName}}</div>
                <div class="_css-costitem" >{{scope.row.TaskName}}</div>
              </el-tooltip>
            </template>
          </el-table-column>
          <el-table-column
            prop="TaskStart"
            label="计划开始时间" >
            <template slot-scope="scope">
              <div class="_css-costitem" >{{scope.row.TaskStart | filterTimer}}</div>
            </template>
          </el-table-column>
          <el-table-column
            prop="TaskFinish"
            label="计划完成时间" >
            <template slot-scope="scope">
              <div class="_css-costitem" >{{scope.row.TaskFinish | filterTimer}}</div>
            </template>
          </el-table-column>
          <el-table-column
            prop="TaskActualStart"
            label="实际开始时间" >
            <template slot-scope="scope">
              <div class="_css-costitem" >{{scope.row.TaskActualStart | filterTimer}}</div>
            </template>
          </el-table-column>
          <el-table-column
            prop="TaskActualFinish"
            label="实际完成时间" >
            <template slot-scope="scope">
              <div class="_css-costitem" >{{scope.row.TaskActualFinish | filterTimer}}</div>
            </template>
          </el-table-column>
          <el-table-column
            prop="TaskName"
            label="任务状态"
            width="150">
            <template slot-scope="scope">
              <div class="_css-costitem" 
                :class="{'cF008000': DateDiff(scope.row.TaskFinish,scope.row.TaskActualFinish)[0] == '超前',
                'cFff0000': DateDiff(scope.row.TaskFinish,scope.row.TaskActualFinish)[0] == '滞后'}">

                {{DateDiff(scope.row.TaskFinish,scope.row.TaskActualFinish)[0]}}
              </div>
              <span v-if="DateDiff(scope.row.TaskFinish,scope.row.TaskActualFinish)[0] != '正常'">
                &nbsp;{{DateDiff(scope.row.TaskFinish,scope.row.TaskActualFinish)[1]}}&nbsp;天
              </span>
            </template>
          </el-table-column>
        </el-table>
      </el-tab-pane>
      <el-tab-pane :label="startOrPause ? '实际正在进行':'计划正在进行'" name="second">
        <el-table
          fixed
          border
          :height="tabTableHeight"
          :data="tableDatainProgress"
          style="width: 100%">
          <el-table-column
            prop="TaskName"
            :resizable="true"
            label="任务名称">
            <template slot-scope="scope">
              <el-tooltip placement="right" effect="light" popper-class="table-hover-setcss tooltip-model-hover">
                <div slot="content">{{scope.row.TaskName}}</div>
                <div class="_css-costitem" >{{scope.row.TaskName}}</div>
                </el-tooltip>
          </template>
          </el-table-column>
          <el-table-column
            prop="TaskStart"
            label="计划开始时间"
            width="300">
            <template slot-scope="scope">
              <div class="_css-costitem" >{{scope.row.TaskStart | filterTimer}}</div>
            </template>
          </el-table-column>
          <el-table-column
            prop="TaskFinish"
            label="计划完成时间"
            width="300">
            <template slot-scope="scope">
              <div class="_css-costitem" >{{scope.row.TaskFinish | filterTimer}}</div>
            </template>
          </el-table-column>
          <el-table-column
            prop="TaskActualStart"
            label="实际开始时间"
            width="300">
            <template slot-scope="scope">
              <div class="_css-costitem" >{{scope.row.TaskActualStart | filterTimer}}</div>
            </template>
          </el-table-column> 
          <el-table-column
            prop="TaskActualStart"
            label="完成比率"
            width="200">
            <template slot-scope="scope">
              <div class="_css-costitem">
                {{scope.row.PERCENTCOMPLETE_}}%
              </div>
            </template>
          </el-table-column>
        </el-table>
      </el-tab-pane>
      <el-tab-pane :label="startOrPause ? '实际未开始':'计划未开始'" name="third">
        <el-table
          fixed
          border
          :data="tableDatanotStarted"
          :height="tabTableHeight"
          style="width: 100%">
          <el-table-column
            prop="TaskName"
            :resizable="true"
            label="任务名称">
            <template slot-scope="scope">
              <el-tooltip placement="right"  effect="light" popper-class="table-hover-setcss tooltip-model-hover">
                <div slot="content">{{scope.row.TaskName}}</div>
                <div class="_css-costitem" >{{scope.row.TaskName}}</div>
                  
                </el-tooltip>
          </template>
          </el-table-column>
          <el-table-column
            prop="TaskStart"
            label="计划开始时间">
            <template slot-scope="scope">
              <div class="_css-costitem" >{{scope.row.TaskStart | filterTimer}}</div>
            </template>
          </el-table-column>
          <el-table-column
            prop="TaskFinish"
            label="计划完成时间">
            <template slot-scope="scope">
              <div class="_css-costitem" >{{scope.row.TaskFinish | filterTimer}}</div>
            </template>
          </el-table-column>
        </el-table>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>
<script>
export default {
  name: 'modelTaskTable',
  data() {
    return {
      activeName: 'first',
      tabTableHeight:600,
      tableDatafinished: [],
      tableDatainProgress: [],
      tableDatanotStarted: [],
      startOrPause: false,
    }
  },
  filters: {
    filterTimer(inputtime) {
      if (!inputtime || inputtime.trim().length == 0) {
        return inputtime;
      }
      if (inputtime.length >= "2019-09-16T11:14".length) {
        return inputtime.substr(0, 10);
      }
    },
  },
  watch:{
    currentProgressLegend(val){
      console.log(val,'=====')
      this.startOrPause = val;
    },
    finished(val){
      this.tableDatafinished = val;
    },
    inProgress(val){
      this.tableDatainProgress = val;
    },
    notStarted(val){
      this.tableDatanotStarted = val;
    },
  },
  props: {
    currentProgressLegend: {
      type: Boolean,
      defaults: true
    },
    finished: {
      type: Array,
      defaults: true
    },
    inProgress: {
      type: Array,
      defaults: true
    },
    notStarted: {
      type: Array,
      defaults: true
    }
  },
  created(){
    window.onresize =()  =>{
        return (()=>{
          let h = window.innerHeight || document.documentElement.clientHeight || document.body.clientHeight;
          this.tabTableHeight = h - 130;
        })()
    }
  },
  mounted(){
    this.tableDatafinished = this.finished;
    this.tableDatainProgress = this.inProgress;
    this.tableDatanotStarted = this.notStarted;
    this.startOrPause = this.currentProgressLegend;
    // this.tableData = this.moreprojectID.list;
    // this.changeListforTable(this.moreprojectID.list);
  },
  methods: {
    handleClick(tab, event) {
      console.log(tab, event);
    },
    DateDiff(sDate1, sDate2) {
      if (!sDate1 || sDate1.trim().length == 0) {
        return
      }
      if (!sDate2 || sDate2.trim().length == 0) {
        return
      }
      let _sDate1 = sDate1.substr(0,10)
      let _sDate2 = sDate2.substr(0,10)
      let oDate1, oDate2, iDays ,state;
      oDate1 = new Date(_sDate1).getTime();
      oDate2 = new Date(_sDate2).getTime();
      iDays = parseInt((oDate1 - oDate2) / 1000 / 60 / 60 / 24) //把相差的毫秒数转换为天数
      if(iDays > 0){
        state = '超前';
      }else if(iDays == 0){
        state = '正常';
      }else if(iDays < 0){
        state = '滞后';
      }
      return  [state,Math.abs(iDays)];
    },
    
  }
}
</script>
<style scoped>
.table-model-task{
  margin: 10px 2px;
  /* height: calc(100% - 60px); */
  /* overflow-y: scroll; */
}
.table-tab-list /deep/ .el-tabs__header{
  padding-left: 10px;
  margin: 0;
}
.table-tab-list /deep/ .el-tabs__item{
  width: 96px;
  height: 32px;
  line-height: 32px;
  background: rgba(0, 0, 0, 0.2);
  border-radius: 4px 4px 0px 0px;
  margin-right: 4px;
  color: #fff;
  font-weight: 500;
  padding: 0;
}
.table-tab-list /deep/ .el-tabs__active-bar{
  background: transparent;
}
.table-tab-list /deep/ .el-tabs__item.is-active{
  color: #fff;
  background: #409EFF;
}
.table-model-task /deep/ .el-table td{
  border-color: #D4D6D9;
}
.table-model-task /deep/ .el-table tr:first-child th{
  background: #FAFAFA;
}
.table-model-task /deep/ .el-table:before{
  height: 0;
}
.table-model-task /deep/ .el-table .cell{
  height: 42px;
}
/* .table-model-task /deep/ .el-table td, .table-model-task /deep/ .el-table th.is-leaf{
  border-right: none;
  border-top: none;
} */
._css-costitem{
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.c333333{
  color: #333333;
}
.cF008000{
  color: #008000;
}
.cFff0000{
  color: #ff0000;
}
</style>