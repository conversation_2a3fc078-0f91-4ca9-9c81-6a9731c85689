<template>
  <div class="progress-list" :class="proLeftIn ? 'p-list-out' : ''" ref="proList" @click.stop="closeAll">
      <div class="pro-in-out" @click.stop="handleClickInOut" :class="proLeftIn ? 'p-out' : ''"></div>
      <div class="left-progress">

        <h2 v-show="!proLeftIn">
            <!-- <i class="icon-interface-problem-status" style="margin-right:8px;"></i> -->
            <span>进度方案</span>
            <!-- <i class="icon-interface-addnew" v-if="isAdd" @click="openDialog"></i> -->
            <!-- <i class="icon-interface-addnew" @click="openDialog"></i> -->
            <div class="_css-materhead-addbtn" @click.stop="func_showmgr($event)"
            ><i class="icon-suggested-plus size-weight"></i></div>
        </h2>
        <el-tree :data="treeData"
            :default-expanded-keys=defaultExpandedKeys
            node-key="ID"
            :props="defaultProps"
            :expand-on-click-node=false
            :load="loadNode"
            :lazy=isLazy
            :highlight-current=true
            :default-expand-all=expandAll
            :ref="refData"
            @node-click="treeClick">
            <span class="custom-tree-node" slot-scope="{ node }">
                <span class="_css-tree-label">{{ node.label }}</span>
                <span @click.stop="func_showmenu($event, node)" class="_css-treenode-menubtn icon-interface-set_se" ></span>
            </span>
        </el-tree>
        <div class="_css-progress-btn" :style="getProgressStyle()" v-if="showProgressStyle">
          <div @click.stop="addProgressUI">
            <span class="icon icon-suggested-plus"></span>
            创建进度方案
          </div>
          <div @click.stop="clickprogressimport" >
            <span class="icon icon-interface-cloud-upload"></span>
            导入进度方案
          </div>
        </div>
        <div class="tree-sub-menu" :style="getTreeListStyle()" v-if="showTreeListStyle">

            <div @click.stop="MovePlusTaskStatisticsSort(0)" v-if="clickNodeIndex != 0 ">
                <i class="icon icon-new-up icon-set-font" :title="recordsTreeData.length +'-'+clickNodeIndex"></i>
                上移
            </div>
            <div @click.stop="MovePlusTaskStatisticsSort(1)" v-if="clickNodeIndex != recordsTreeData.length - 1 " >
                <i class="icon icon-new-down icon-set-font" :title="recordsTreeData.length +'-'+clickNodeIndex"></i>
                下移
            </div>
            <div @click.stop="editProgressUI" >
                <i class="icon icon-interface-edit"></i>
                编辑
            </div>
            <div @click.stop="exportPlan">
                <i class="icon icon-interface-download-fill"></i>
                导出
            </div>
            <div @click.stop="delProgressUI">
                <i class="icon icon-interface-model-delete"></i>
                删除
            </div>
        </div>
        <!-- <progressdialog
            v-if="dialogConfig.addShow"
            :isBack=true
            @close=closeAll()
            @back=back
            title="创建进度方案">
            <div slot="main" style="height:134px">
                <div class="entry-list">
                    <p>进度方案名称</p>
                    <input type="text" v-model="dialogConfig.addInputData">
                </div>
            </div>
            <div slot="btn">
                <div style="background:#1890FF;color:#fff;" @click="ProgressBtnClick(0)">创建进度方案</div>
            </div>
        </progressdialog> -->
        <zdialog-function
            :init_title="getTitle()"
            :init_zindex="1003"
            :init_innerWidth="400"
            :init_width="400"
            init_closebtniconfontclass="icon-suggested-close"
            @onclose="dialogConfig.addShow = false;dialogConfig.editShow=false"
            v-if="dialogConfig.addShow || dialogConfig.editShow"
          >
            <div
              slot="mainslot"
              class="_css-addingnameinput-ctn"
              @mousedown="_stopPropagation($event)"
            >

              <div class="_css-line _css-line-name">
                <div class="_css-title _css-title-flowname">进度方案名称：</div>
                <div class="_css-fieldvalue _css-fieldvaluename _css-selectednode">
                  <el-input
                    @mousedown="_stopPropagation($event)"
                    placeholder="请输入"
                    v-model="dialogConfig.addInputData">
                  </el-input>
                </div>
              </div>
              <div class="_css-line _css-line-name">
                <div class="_css-title _css-title-flowname">进度推送：</div>
                <div class="_css-fieldvalue _css-fieldvaluename">
                  <el-select v-model="dialogConfig.IsStatistics" placeholder="请选择">
                    <el-option
                      v-for="item in IsStatisticsOptions"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    >
                    </el-option>
                  </el-select>
                </div>
              </div>
            </div>

            <div slot="buttonslot" class="_css-flowAddBtnCtn">
              <zbutton-function
                :init_text="'保存'"
                :init_fontsize="14"
                :debugmode="true"
                :init_height="'40px'"
                :init_width="'120px'"
                @onclick="ProgressBtnClick(addOrEditNum)"
              >
              </zbutton-function>
              <zbutton-function
                :init_text="'取消'"
                :init_color="'rgba(24, 144, 255)'"
                :init_bgcolor="'#fff'"
                :init_fontsize="14"
                :debugmode="true"
                :init_height="'40px'"
                :init_width="'120px'"
                @onclick="dialogConfig.addShow = false;dialogConfig.editShow = false"
              >
              </zbutton-function>
            </div>
        </zdialog-function>
         <!-- <progressdialog
            v-if="dialogConfig.delShow"
            :isBack=true
            @close=closeAll()
            @back=back
            title="删除进度方案">
            <div slot="main" style="height:134px">
                <div class="entry-list">
                    <p>确认删除此进度方案吗</p>
                </div>
            </div>
            <div slot="btn">
                <div style="background:#F5222D;color:#fff;border:1px solid #F5222D;" @click="ProgressBtnClick(1)">删除</div>
            </div>
        </progressdialog> -->
        <!-- <progressdialog
            v-if="dialogConfig.editShow"
            :isBack=true
            @close='dialogConfig.editShow=false'
            @back=back
            title="进度方案重命名">
            <div slot="main" style="height:134px">
                <div class="entry-list">
                    <p>进度方案名称</p>
                    <input type="text" v-model="dialogConfig.addInputData">
                </div>
            </div>
            <div slot="btn">
                <div style="background:#1890FF;color:#fff;" @click.stop="ProgressBtnClick(2)">确定</div>
            </div>
        </progressdialog> -->
      </div>
        <!-- <div class="_css-select" v-if="clickRelevance" :style="getRelevanceStyle()">
            <p @click.stop="ProgressAutoRel">手动关联</p>
            <p @click.stop="clickAutoRelevance">自动关联</p>
        </div> -->
        <!-- 进度 -->
          <transition name="el-zoom-in-bottom">
              <div class="transition-dialog">
                <CompsFullScreenDialog
                    class="el-edit-progress-wp"
                    :title="currentGanttDataInfo.NAME_"
                    @closeCallBack="ganttDialogCallBack"
                    :init_hidebackbtn="!headerHide"
                    :bigBimTop='bigBimTop'
                    :bigBimLeft='bigBimLeft'
                    v-if="editGanttShow"
                >
                    <section slot="btn" class="top-btn" v-if="!clickRightPlay">
                      <div
                      v-if="playsign == 'end'"
                      :class="{'_css-vhidden':progressEditDis}"
                      @click="tiggerGanttBtns('id_abtn_save')">保存进度</div>
                    </section>

                    <div slot="btn" class="btn-wp" style="width:auto;background:transparent;" v-if="clickRightPlay">
                      <div class="right-btns">
                        <div

                          class="_css-white-btn _css-modelplay-cfgbtn show-table-detail"
                          @click="clickOpenTableTab">
                            统计详情
                        </div>
                        <div
                        :class="{'_css-reverse':showplaycomps == true}"
                        class="_css-white-btn _css-modelplay-cfgbtn " @click="mockConfigClick"><i class="icon-interface-set_se"></i>
                            {{getPlayCfgBtnText()}}
                        </div>
                        <div
                          v-if="playsign == 'end' && addOpenModel"
                          class="playmodel"
                          @click="mockprogressclick"
                          :class="{'ban-click':bStartDis}"><i class="icon-interface-play"></i>
                            开始模拟
                        </div>
                          <div
                        v-if="playsign == 'pause'"
                          class="playmodel"
                          @click="mockprogressclick"
                          :class="{'ban-click':false && loadingModelEnd == true}"><i class="icon-interface-play"></i>
                            继续模拟
                        </div>
                        <div
                        v-if="playsign == 'start'"
                          class="pausemodel"
                          @click="pausemodelClick"
                          :class="{'ban-click':false && loadingModelEnd == true}"><i class="icon-interface-time-out"></i>
                            暂停模拟
                        </div>
                        <div
                          v-if="playsign != 'end'"
                          class="stopmodel"
                          @click="stopPlayModel()"
                          :class="{'ban-click':false && loadingModelEnd == true}"><i class="icon-interface-stop"></i>
                            停止模拟
                        </div>
                      </div>
                    </div>
                    <section slot="main" class="full-screen-main el-progress-wp">
                        <div class="btn-wp" v-if="!clickRightPlay && playsign == 'end'">
                          <div class="left-btns"
                          :class="{'_css-vhidden':progressEditDis}"
                          >
                            <div class="_css-white-btn" @click="tiggerGanttBtns('id_abtn_addtask')"><i class='icon-interface-addnew'></i> 新建任务</div>
                            <div class="_css-white-btn" @click="tiggerGanttBtns('id_abtn_updatetask')"><i class="icon-interface-edit"></i>编辑任务</div>
                            <div class="_css-white-btn" @click="tiggerGanttBtns('id_abtn_removetask')"><i class="icon-interface-delete"></i>删除任务</div>
                            <div class="_css-white-btn" @click="tiggerGanttBtns('id_abtn_upgradetask')"><i class="icon-interface-descending"></i>升级</div>
                            <div class="_css-white-btn" @click="tiggerGanttBtns('id_abtn_downgradetask')"><i class="icon-interface-problem-sorting"></i>降级</div>
                            <!-- <div @click="tiggerGanttBtns('')"><i class="icon-interface-Merge"></i>显示未关联到任务的构件</div> -->
                          </div>
                          <div class="right-btns">
                            <!-- <div
                            v-if="!progressEditDis"
                            @click="ProgressEditMgr()"
                            class="_css-auto-rel _css-white-btn" >
                              <div class="_css-nothover icon-interface-setting _css-auto-rel-icon" ></div>
                              <div class="_css-nothover _css-auto-rel-text" >编辑构件</div>
                            </div> -->
                            <div
                            v-if="!addOpenModel"
                            @click.stop="clickSelectRelevance($event)"
                            class="_css-select-rel _css-auto-rel _css-white-btn " >
                              <div class="_css-nothover _css-auto-rel-text">关联工程结构<i class="icon-arrow-down"></i></div>
                              <div class="_css-select" v-if="clickRelevance" :style="getRelevanceStyle()">
                                <p @click.stop="ProgressAutoRel">手动关联</p>
                                <p @click.stop="clickAutoRelevance">自动关联</p>
                            </div>
                            </div>

                            <div class="_css-white-btn" @click="handelClickSelectScene"><i class="icon-interface-guanlianmoxing"></i>选择场景</div>
                            <div class="_css-white-btn" v-if="!addOpenModel && hasSelectScene" @click.stop="openmodelview2"><i class="icon-interface-guanlianmoxing"></i>{{'打开场景'}}</div>
                            <div class="_css-white-btn" v-if="addOpenModel && hasSelectScene" @click.stop="closemodelview2"><i class="icon-interface-guanlianmoxing"></i>{{'关闭场景'}}</div>
                            <div
                              v-if="addOpenModel"
                            :class="{'_css-reverse':showplaycomps == true}"
                            class="_css-white-btn _css-modelplay-cfgbtn " @click="mockConfigClick"><i class="icon-interface-set_se"></i>
                                {{getPlayCfgBtnText()}}
                            </div>
                            <div
                              v-if="playsign == 'end' && addOpenModel"
                              class="playmodel"
                              @click="mockprogressclick"
                              :class="{'ban-click':bStartDis}"><i class="icon-interface-play"></i>
                                开始模拟
                            </div>
                              <div
                              v-if="playsign == 'pause' && addOpenModel"
                              class="playmodel"
                              @click="mockprogressclick"
                              :class="{'ban-click':false && loadingModelEnd == true}"><i class="icon-interface-play"></i>
                                继续模拟
                            </div>
                            <div
                              v-if="playsign == 'start' && addOpenModel"
                              class="pausemodel"
                              @click="pausemodelClick"
                              :class="{'ban-click':false && loadingModelEnd == true}"><i class="icon-interface-time-out"></i>
                                暂停模拟
                            </div>
                            <div
                              v-if="playsign != 'end' && addOpenModel"
                              class="stopmodel"
                              @click="stopPlayModel()"
                              :class="{'ban-click':false && loadingModelEnd == true}"><i class="icon-interface-stop"></i>
                                停止模拟
                            </div>
                          </div>
                        </div>
                        <div class="iframes-wp">
                          <div style="flex:1; width:100%;">
                            <div class="iframe-all-show">
                              <iframe
                                @load="ganttIframeLoad"
                                v-if="currentGanttDataInfo !== null"
                                :src="`${getBimConfig().integrated_planurl}/demo/Project.html?id=${currentGanttDataInfo.bop_planId}&bcheckcolumn=1&pagetype=${gttEdit}&Token=${this.$staticmethod.Get('Token')}`"
                                frameborder="0"
                                ref="addOpenPlanView"
                                id="addOpenPlanView"
                                :class="addOpenModel && clickRightPlay && !tableTaskShow && playsign != 'end'? 'small-iframe' : 'full-iframe'"
                              ></iframe>
                              <!-- 任务状态开始 -->
                              <!-- 暂时注释当前进度表格 -->
                                <!-- :class="addOpenModel && clickRightPlay && !tableTaskShow && playsign != 'end'? 'small-iframe' : 'full-iframe'" -->
                               <!-- :class="tableTaskShow? 'small-iframe' : 'full-iframe'" -->
                               <!-- :class="tableTaskShow ? 'table-model-table-hide' : 'table-model-state'" -->
                              <div
                                v-if="addOpenModel && clickRightPlay && playsign != 'end'"
                                class="table-model-state-css"
                                :class="tableTaskShow? 'table-model-table-hide' : 'table-model-state'"
                                >
                                <div class="table-downOrup" @click="clickTableShow" >
                                  <i class="table-show-hide-btn" :class="tableTaskShow ? 'table-up' : 'table-down'"></i>
                                </div>
                                <transition name="el-zoom-in-bottom">
                                  <modelTaskTable
                                    v-if="!tableTaskShow"
                                    :finished="finished"
                                    :inProgress="inProgress"
                                    :notStarted="notStarted"
                                    :currentProgressLegend="currentProgressLegend"
                                    ></modelTaskTable>
                                </transition>
                              </div>
                              <!-- 任务状态开始 -->
                            </div>

                          </div>

                          <CompsdragWp style="width:50%;" v-if="addOpenModel">
                              <modelPlay
                              ref="ref_modelPlay"
                                slot="main"
                                :selectSceneID="selectOpenSceneObj.SceneId"
                                :getSceneSetObj="getDataSceneAndModelPlayObj"
                                :inProgress="inProgress"
                                :clickRightPlay="clickRightPlay"
                                @openScenefail="openProjectSceneFail"
                                @progressPlayLoad="modeliframeload"
                                :moreprojectID='moreprojectID'
                                :moreModelIds='moreModelIds'
                                :changemoreModelIds="changemoreModelIds"
                                :modelid=currentGanttDataInfo.bop_modelId
                                :planid=currentGanttDataInfo.bop_planId
                                :iscache=false
                                :showplaycomps='showplaycomps'
                                :modelInputEdit="modelInputEdit"
                                :setAllPlanListArr="setAllPlanListArr"
                                @inputChange="_inputChange()"
                                @playStart="_playStateSign('start')"
                                @playPause="_playStateSign('pause')"
                                @playEnd="_playStateSign('end')"
                                @stopPlayModel="stopPlayModel"
                                @noDataFun="noDataFun"
                                @clickCurrentProgress="clickCurrentProgress(arguments)"
                                :playEnd="playEndFun"
                              ></modelPlay>

                          </CompsdragWp>
                        </div>

                    </section>
                </CompsFullScreenDialog>
              </div>

          </transition>
        <!-- 进度 -->
        <!-- 进度任务表格详情开始 -->
        <transition name="el-zoom-in-bottom">
          <div class="transition-dialog">
            <CompsFullScreenDialog
              class="el-edit-progress-wp"
              :title="currentGanttDataInfo.NAME_"
              @closeCallBack="backTableTaskTabHide"
              :init_hidebackbtn="!headerHide"
              :bigBimTop='bigBimTop'
              :bigBimLeft='bigBimLeft'
              v-if="tableTaskTabOpen"
            >
            <section slot="main" class="full-screen-main el-progress-wp">
              <model-task-table-tab
                :finished="finished"
                :inProgress="inProgress"
                :notStarted="notStarted"
                :currentProgressLegend="currentProgressLegend"
                >

              </model-task-table-tab>
            </section>
            </CompsFullScreenDialog>
          </div>
        </transition>
        <!-- 进度任务表格详情结束 -->
        <!-- 构件浏览器 -->
        <div class='_css-masking-edit-component' v-if="status_materialbrowser">
          <CompsMaterialBrowser
          :isProgress="true"
          :init_zIndex="1002"
          :init_width="'1000px'"
          :init_height="'640px'"
          :init_bimcomposerId="getOrganizeId()"
          :init_organizeId="getOrganizeId()"
          init_title="请选择关联构件"
          :selectedObjList='selectedObjList'
          @clearSelected="clearMgrList"
          @onRemoveList="onRemoveList"
          @onclose="evt_materialbrowserclose"
          @onok="evt_materialbrowseronok"
          @clickedit="ProgressEditMgr"
          ></CompsMaterialBrowser>
        </div>
        <div class='_css-masking-edit-component' v-if="status_AutoRelevance">
          <progressAutoRelevance
            @onclose="closeAutoRel"
            :init_bimcomposerId="getOrganizeId()"
            :init_organizeId="getOrganizeId()"
            @onsubmit="PjtAutoRelevance"
          >
          </progressAutoRelevance>
        </div>

        <!-- //构件浏览器 -->
        <!-- 编辑构件 -->
        <!-- <CompsMaterialEdit
        v-if="status_materialedit"
        :init_zIndex="1002"
        :init_width="'800px'"
        :init_height="'640px'"
        :init_bimcomposerId="getOrganizeId()"
        :init_organizeId="getOrganizeId()"
        init_title="编辑关联构件"
        :selectedObjList='selectedObjList'
        @onclose="evt_materialEditclose"

        @addMgr="ProgressAutoRel"
        @onRemoveList="onRemoveList"
        @onclearMgrList="clearMgrList"
        ></CompsMaterialEdit> -->
        <!-- 编辑构件 -->

        <input
            type="file"
            style="display:none"
            id="id_importprogress"
            accept="text/xml,application/xml,application/vnd.ms-project"
        @change="importprogress_change()" />
        <div class='_css-masking-edit-component' v-if="sceneListDialog">
          <!-- 打开场景 -->
          <progressRelationSceneList
            v-if="sceneListDialog"
            :bindingModelIDs="bindingModelIDs"
            @open="handelClickOpenScene"
            @close="sceneListDialog=false"
          ></progressRelationSceneList>
        </div>

        <div class='_css-masking-edit-component' v-if="showModelSetDialog && playsign == 'end' && showplaycomps">
          <!-- 模拟设置 -->
          <progressModelPlaySet
            :SetSceneObj="getDataSceneAndModelPlayObj"
            @ok="modelPlaySetParams"
            @close="closeModelPlaySetDialog"
            :beginTime="moreprojectID.start"
            :endTime="moreprojectID.end"
          ></progressModelPlaySet>

        </div>

  </div>
</template>

<script>
import progressdialog from "../CompsDialog/CompsDialogProgress"
import CompsFullScreenDialog from '@/components/CompsDialog/CompsFullScreenDialog'
import CompsdragWp from '@/components/CompsDialog/CompsdragWp'
import modelPlay from '@/components/CompsProgress/modelPlay'
import modelTaskTable from '@/components/CompsProgress/modelTaskTable'


import CompsMaterialBrowser from '@/components/CompsCommon/CompsMaterialBrowser'
import ModelTaskTableTab from './modelTaskTableTab.vue'
import progressAutoRelevance from './progressAutoRelevance'

import progressRelationSceneList from './progressRelationSceneList'
import progressModelPlaySet from './progressModelPlaySet'

// import CompsMaterialEdit from '@/components/CompsCommon/CompsMaterialEdit'

export default {
  components:{
    progressdialog,
    CompsFullScreenDialog,
    CompsdragWp,
    modelPlay,
    CompsMaterialBrowser,
    modelTaskTable,
    ModelTaskTableTab,
    progressAutoRelevance,
    progressRelationSceneList,
    progressModelPlaySet,
    // CompsMaterialEdit
  },
  props:{
    treeData:{
      type:Array,
      default:[]
    },
    isAdd:{
      type:Boolean,
      default:false
    },
    isLazy:{
      type:Boolean,
      default:true
    },
    expandAll:{
      type:Boolean,
      default:false
    },
    refData:{
      type:String,
      default:'tree'
    },
    defaultExpandedKeys:{
      type:Array,
    },
    r_hasAuthEdit:{
      type:String,
    },
  },
  data(){
    return {
      proLeftIn: false,
      defaultProps:{
          children: 'children',
          label: 'name',
          isLeaf:'leaf',
      },
      progressStyle:{
        left:0,
        top:0,
      },
      treeListStyle:{
        left:100,
        top:100,
      },
      showProgressStyle: false,
      showTreeListStyle: false,
      dialogConfig: {
        addShow: false,
        addInputData: '',
        IsStatistics: false,
        delShow: false,
        editShow: false
      },
      currentGanttDataInfoObj: null,
      currentGanttDataInfo:null,
      editGanttShow:false,

      progressEditDis: false, // 是否禁用进度管理的编辑权限
      currentModelInfo:null,
      currentModelInfo_ID:'',
      currentModelInfo_Name:'',
      addOpenModel:false, //判断是否打开模型
      showplaycomps:false,//模拟按钮
      bStartDis: false, // 刚点击完开始模拟时，立即dis掉，当 playSign 修改为 start 时，立即取消dis.
      loadingModelEnd: true,
      modelInputEdit: false,
      playsign: 'end', // 当前的模型模拟状态
      // 甘特图在接收到 OnSaved 后，是否需要关闭，并返回。
      bNeedCloseAfterSavedPlus: false,
      currentGanttDataInfotitle: '',
      ganttBtnsConfig:{
        btnName:'打开模型'
      },
      projectid:'',
      editModelIframeWindow:null,
      status_materialbrowser: false,
      status_materialedit: false,
      // 左侧甘特图中已选中的任务的 UID 数组
      gannCurrentSelectData:[],

      // 左侧甘特图中已选中的任务的数组
      gannCurrentSelectData_full: [],

      selectedObjList: [],
      moreprojectID: '', // 模拟的数据，接口返回的所有数据
      moreModelIds: [],
      changemoreModelIds: [], // 多模型加载使用|分割
      clickRightPlay: false,  // 点击右边播放模型的控制按钮，没有编辑权限
      headerHide: false, // 大屏显示使用该组件，header不展示
      bigBimLeft: 0,   // 获取元素距离浏览器顶部的距离，在大屏展示时使用
      bigBimTop: 0,   // 获取元素距离浏览器顶部的距离，在大屏展示时使用
      setAllPlanListArr: null,
      selectGTTPlan: null, // 暂存甘特图中当前选中的任务
      noChoose:true, //是否选择
      tableTaskShow: false, // 任务表格的收起与展示
      tableTaskTabOpen: false, // 任务表格详情打开
      finished: [],
      inProgress: [],
      notStarted: [],
      currentProgressLegend: false,// 该参数为当前进度还是正在模拟的状态
      gttEdit: 'readwrite',  // 当点击编辑任务入口尽然是可以编辑 readwrite，直接模拟是不可以编辑的readonly，
      removeDialog: 1,  // 因为删除任务时候会多次弹出（因为保存导致），使用该值来限定，只弹窗一次
      clickRelevance: false, // 关联工程结构
      clickRelevanceStyle: {
        left: 0,
        top: 0,
      },
      status_AutoRelevance: false,  // 自动关联弹窗
      clickNodeIndex: 0, // 上下移动用的index
      recordsTreeData: [],
      sceneList: [], // 当前进度关联模型、根据模型ID查找所包含的对应场景列表
      sceneListDialog: false, // 打开场景
      bindingModelIDs: '', // 当前进度任务所关联的构件
      selectOpenSceneObj: {}, // 选中打开场景的场景相关值
      showModelSetDialog: false, // 模拟设置弹窗关闭
      setModelSetDialogObj: {}, // 模拟设置所传值
      getDataSceneAndModelPlayObj: {}, // 接口记录的关于选择场景和模拟设置的值
      hasSelectScene: false, // getDataSceneAndModelPlayObj返回值判断true和false
      IsStatisticsOptions: [
				{
					value: false,
					label: '否'
				},
				{
					value: true,
					label: '是'
				}
			],
      addOrEditNum: 0,
      hasEditAuth: false,// 删除权限
    }

  },
  watch:{

  },
  computed:{},
  methods:{
      getTitle(){
        let str = "创建进度方案"
        if(this.dialogConfig.editShow){
          str = "编辑进度方案"
          this.addOrEditNum = 2
        }
        if(this.dialogConfig.addShow){
          str = "创建进度方案"
          this.addOrEditNum = 0
        }
        return str;
      },
      _stopPropagation(ev) {
        ev && ev.stopPropagation && ev.stopPropagation();
      },
      closeAll(){
        this.showTreeListStyle = false
        this.showProgressStyle = false
        // this.dialogConfig.addShow = false
        this.dialogConfig.delShow = false
        // this.dialogConfig.editShow = false
      },
      openDialog(){
        this.$emit('addDialogShow')
      },
      treeClick(data,node){
        this.closeAll()
        this.currentGanttDataInfoObj = {
          data,
          node
        }
        this.currentGanttDataInfo = node.data;    // 添加这个赋值是点击右边模拟时候使用的值
        this.$staticmethod.Set("currentGanttDataInfo", JSON.stringify(this.currentGanttDataInfo));

        this.$emit('nodeClick',this.currentGanttDataInfoObj)
      },
      loadNode(node, resolve){
        this.closeAll()
        this.$emit('loadNode',{node,resolve})
      },
      // 点击展开--折叠表格
      clickTableShow(){
        this.tableTaskShow = !this.tableTaskShow;
      },
      // 点击查看任务表格详情
      clickOpenTableTab(){
        if(!this.addOpenModel == true){
          this.$message.error('请先打开模型');
          return
        }
        if(this.playsign == 'pause'){
          this.tableTaskTabOpen = true;
        }else{
          this.$message.error('只有在暂停状态下可以查看详情');
        }

      },
      backTableTaskTabHide(){
        this.tableTaskTabOpen = false;
      },
      // 点击查看编辑
      func_showmenu(ev,node){
        this.closeAll()
        this.recordsTreeData = []
        if(this.treeData.children){
          this.recordsTreeData = this.treeData.children
        }else{
          this.recordsTreeData = this.treeData
        }
        let i_index = this.recordsTreeData.findIndex(item=>item.bop_planId == node.data.bop_planId)
        this.clickNodeIndex = node.data.indexNum || i_index; // 记录当前选中的index

        if(this.r_hasAuthEdit == '0'){
          this.showTreeListStyle = false
          this.$message.warning("没有相关权限")
          return
        }
        if(this.headerHide){
          this.treeListStyle.left = ev.clientX - 230;
          this.treeListStyle.top = ev.clientY - 75;
        }else{
          this.treeListStyle.left = ev.clientX - this.bigBimLeft + 37;
          this.treeListStyle.top = ev.clientY - this.bigBimTop - 25;
        }
        this.showTreeListStyle = true;
        this.dialogConfig.addInputData = '';
        this.currentGanttDataInfo = node.data;

      },
      // 点击展示创建、导入
      func_showmgr(ev){
        if(this.r_hasAuthEdit == '0'){
          this.showProgressStyle = false
          this.$message.warning("没有相关权限")
          return
        }
        this.closeAll()
        if(this.headerHide){
          this.progressStyle.left = ev.clientX - 230 ;
          this.progressStyle.top = ev.clientY - 75;
        }else{
          this.progressStyle.left = ev.clientX - this.bigBimLeft + 37;
          this.progressStyle.top = ev.clientY - this.bigBimTop - 15;
        }

        this.showProgressStyle = true

      },
      //创建进度方案
      addProgressUI(){
        if(!this.hasEditAuth){
          this.$message.warning("没有相关权限")
          return
        }
        let _this = this
        _this.showProgressStyle = false
        _this.dialogConfig.addShow = true
      },
      // 导入进度方案
      clickprogressimport(){
          var _this = this;
          var dom = document.getElementById("id_importprogress");
          dom.value = '';
          dom.click();
      },
      importprogress_change(){
          var _this = this;

          // 清空失败次数
          _this.failurenum = 0;

          _this.importonce();
      },
      importonce(){
          // 触发它点击之前 先设置 value = '';
          var _this = this;
          var dom = document.getElementById("id_importprogress");
          var files = dom.files;
          if (files.length == 0) {
              _this.$message.error('请选择 MSProject 文件');
              return;
          }
          var file = files[0];
          //debugger;
          // 调用 webapi，将project文件发送到服务器
          // webapi 中：http://localhost:9095/API/importProject.ashx DFile， 返回 {"Ret":1,"Msg":"OK","Data":"8f6982af-2a48-4592-8eb9-5e5465d6c294"}
          // 在成功的回调中，刷新列表。

          // 调用接口
          var config = {
              headers: {
                  "Content-Type": "application/json"
              },
              onUploadProgress: progressEvent => {
              }
          };
          var fd = new FormData();
          // fd.append("Token", _this.$staticmethod.Get("Token"));
          fd.append("OrganizeId", this.projectid);
          fd.append("ModelId", '');
          fd.append("PhaseVal", '');
          fd.append("DFile", file);

          // 这里要加一个参数，判断文件扩展名来决定值 ExtName
          if (file.name.substr(file.name.lastIndexOf('.')).toLowerCase() == '.xml') {
              fd.append("ExtName", "xml");
          }

          var _LoadingIns = _this.$loading({
              text: "处理中",
              target: document.getElementById("id_compsdialogProgress")
          });
          _this.$axios.post(
              `${this.$MgrBaseUrl.planImportProject}?Token=${_this.$staticmethod.Get('Token')}`,
              fd
          ).then(x => {
              if (x.status == 200) {
                  if (x.data.Ret > 0) {
                      this.$message({
                          message: '导入完成',
                          type: 'success'
                      });
                      console.log(x);
                      // 刷新
                      this.$parent._initTreeData()

                  } else {
                      _this.iffailureretry(x.data.Msg);
                  }
              } else {
                  _this.iffailureretry(x.data.Msg);
              }
              _LoadingIns.close();
          }).catch(x =>  {
              //debugger;
              _LoadingIns.close();

              _this.iffailureretry(x.response.data.Msg);
          });
      },
      iffailureretry(msg){
          var _this = this;
          if (_this.failurenum <= 5) {
              _this.failurenum++;
              _this.importonce();
          } else {
              _this.failurenum = 0;
              _this.$message.error(msg);
          }
      },
      // 查看详情
      detailProgressUI(num){
        // 在这里请求接口获取当前进度是否有选择过场景、以及相关模拟设置的值

        // 0为模拟，1为点击tree中编辑模拟
        if(num == 1){
          this.gannCurrentSelectData = [];  // 每次重新进入编辑甘特图时，要清空选中的构件
          this.clickRightPlay = false;
          this.gttEdit = 'readwrite';
          this.getModelPlaySetAttribute()
        }else{
          this.clickRightPlay = true;
          this.gttEdit = 'readonly';
          // 直接模拟，如果是有选择场景、直接打开场景、展示开始播放按钮、如果没有场景、只是打开、提示当前场景没有关联模型、不显示播放按钮

          // 选择打开场景、请求接口获取场景详情、场景加载完毕后，才能进行模拟
          this.getModelPlaySetAttribute('readonly');
        }
        this.$staticmethod.Set("editGanttShow", "1");
        this.$staticmethod.Set("gttEdit", this.gttEdit);
        this.editGanttShow = true;
      },
      MovePlusTaskStatisticsSort(num){
        if(!this.hasEditAuth){
          this.$message.warning("没有相关权限")
          return
        }
        // 上移0  下移1
        let params = {}
        if(num == 0){
          // 上移、获取当前点击的和上一个的planid
          let upUUID = this.treeData[this.clickNodeIndex].bop_planId
          let upUUIDOtner = this.treeData[this.clickNodeIndex - 1].bop_planId
          params =  {
            Token: this.$staticmethod.Get("Token"),
            plusProjectSettings:[
              {
                UID: upUUID,
                IsStatistics: true,
                Sort: this.clickNodeIndex - 1
              },
              {
                UID: upUUIDOtner,
                IsStatistics: true,
                Sort: this.clickNodeIndex
              }
            ]
          }
          this.SetPlusTaskStatisticsSort(params)
        }else{
          // 下移、获取当前点击的和下一个的planid
          let dowmUUID = this.treeData[this.clickNodeIndex].bop_planId
          let dowmUUIDOtner = this.treeData[this.clickNodeIndex + 1].bop_planId
          params =  {
            Token: this.$staticmethod.Get("Token"),
            plusProjectSettings:[
              {
                UID: dowmUUID,
                IsStatistics: true,
                Sort: this.clickNodeIndex + 1
              },
              {
                UID: dowmUUIDOtner,
                IsStatistics: true,
                Sort: this.clickNodeIndex
              }
            ]
          }
          this.SetPlusTaskStatisticsSort(params)
        }
      },
      // 上移下移请求接口
      SetPlusTaskStatisticsSort(params){
        this.$axios({
            method: 'post',
            data: params,
            url: `${this.$MgrBaseUrl.SetPlusTaskStatisticsSort}`
        }).then(x => {
            if (x.data.Ret > 0) {
              this.$message.success('编辑成功');
              this.showTreeListStyle = false
              this.$parent._initTreeData()
            } else {
              this.$message.error(x.data.Msg);
            }
            this.setTaskSortCancel();
        }).catch(x => {
            console.error(x);
        });
      },
      // 重命名
      editProgressUI(){
        if(!this.hasEditAuth){
          this.$message.warning("没有相关权限")
          return
        }
        this.dialogConfig.editShow = true;
        this.showTreeListStyle = false;
        this.dialogConfig.addInputData = this.currentGanttDataInfo.NAME_;
        this.dialogConfig.IsStatistics = this.currentGanttDataInfo.IsStatistics;
      },
      // 删除
      delProgressUI(){
        let hasDelAuth = this.$staticmethod.hasSomeAuth('JDFA_Delete')
        if(!hasDelAuth){
          this.$message.warning("没有相关权限")
          return
        }

        this.showTreeListStyle = false;
        this
          .$confirm("确定删除选定的数据？", "操作确认", {
            confirmButtonText: "确认",
            cancelButtonText: "取消",
            type: "warning"
          })
        .then(x => {
          this.ProgressBtnClick(1)
        })
        .catch(x => {});

      },
      // 导入
      exportPlan(){
            var _this = this;

            // 默认 xml 如果配置了使用 mpp，再导出 mpp
            var formatstr;
            if (window.bim_config.plus_usempp == true) {
                formatstr = 'mpp';
            } else {
                formatstr = 'xml';
            }
            var _url = `${this.$MgrBaseUrl.planExport}?type=${formatstr}&id=${_this.currentGanttDataInfo.bop_planId}`;
              window.location.href = _url;
            // _this.$axios.get(_url).then(x => {
            //   console.log(x,'=====')
            //     // if (x.status == 200 && x.data.Ret > 0) {
            //     //     var _urlreal = `${window.bim_config.integrated_planurl}/API/realexport.ashx?fileurl=${window.encodeURIComponent(x.data.Msg)}`;
            //     //     window.location.href = _urlreal;
            //     // } else {
            //     //   console.warn('未导出成功');

            //     //   // setTimeout(()=>{
            //     //   //     console.warn('未导出成功，正在重试');
            //     //   //     _this.exportPlan();
            //     //   // }, 1000);
            //     // }
            // }).catch(x => {
            //     //console.log(134, x.data);
            //     //debugger;
            // });
      },
      // 创建的style
      getProgressStyle(){
        let _this = this;
        let _s = {};
        _s["left"] = _this.progressStyle.left + 'px';
        _s["top"] = _this.progressStyle.top + 'px';
        return _s;
      },
      // 点击编辑的style
      getTreeListStyle(){
        let _this = this;
        let _s = {};
        if(_this.treeListStyle.top > document.body.clientHeight - 220 && this.headerHide){
          _this.treeListStyle.top = document.body.clientHeight - 220
        }
        _s["left"] = _this.treeListStyle.left + 'px';
        _s["top"] = _this.treeListStyle.top + 'px';
        return _s;
      },
      back(judge){
          // 0 创建   1删除
          // this.dialogConfig.mainShow = true
          switch(judge){
              case 0: this.dialogConfig.addShow = false;
                      this.dialogConfig.addInputData = '';
              break;
              case 1: this.dialogConfig.delShow = false;
              break;
              case 2: this.dialogConfig.editShow = false;
                      this.dialogConfig.addInputData = '';
              break;
          }
      },
      ProgressBtnClick(judge){
          let _this = this
          // 0创建   1 删除  2 重命名
          if(judge != 1){
              if (this.dialogConfig.addInputData == "") {
                  this.$message({
                      message: "输入名称",
                      type: "warning"
                  })
                  return false
              }
          }

          switch (judge) {
              case 0 :
                  let postData = {
                      planName: this.dialogConfig.addInputData,
                      IsStatistics: this.dialogConfig.IsStatistics,
                      organizeId: this.projectid,
                      modelId: '',
                      phaseVal:''
                  }
                  this.$axios.post(`${this.$MgrBaseUrl.planAddPlan}?Token=${_this.$staticmethod.Get('Token')}`,postData).then(res=>{
                      // 如果有重复，提示
                      if (res.data.Ret == -1) {
                          this.$message.error(res.data.Msg);
                          return;
                      }

                      this.$message({
                          message: '添加成功',
                          type: 'success'
                      });
                      // res.data.Data['NAME_'] = this.dialogConfig.addInputData
                      // debugger;
                      // this.progressList.push(res.data.Data)
                      this.dialogConfig.addShow = false
                      this.$parent._initTreeData()
                      this.dialogConfig.mainShow = true
                      this.dialogConfig.addInputData = ''
                  })
                  break;
              case 1 :
                  // debugger
                  let postData1 = {
                      token: this.$staticmethod.Get("Token"),
                      planID: this.currentGanttDataInfo.bop_planId
                  }
                  this.$axios.post(`${this.$MgrBaseUrl.planDelPlan}`,postData1).then(res=>{
                      this.$message({
                          message: '删除成功',
                          type: 'success'
                      });
                      // this.progressList.splice(this.currentPlan.index,1)
                      this.dialogConfig.delShow = false
                      this.dialogConfig.mainShow = true
                      let listSelectData = null
                      this.$emit('listSelectData',listSelectData)
                      this.$parent._initTreeData()

                  })
                  break;
              case 2 :
                  let postData2 = {
                      planID: this.currentGanttDataInfo.bop_planId,
                      planName: this.dialogConfig.addInputData,
                      organizeId: this.projectid,
                      IsStatistics: this.dialogConfig.IsStatistics,
                      modelId: '',
                  }
                  this.$axios.post(`${this.$MgrBaseUrl.planRename}?Token=${this.$staticmethod.Get('Token')}`,postData2).then(res=>{
                      if (res.data.Ret == -1) {
                          this.$message.error(res.data.msg);
                          return;
                      }

                      this.$message({
                        message: '修改成功',
                          type: 'success'
                      });
                      // this.progressList[this.currentPlan.index].NAME_ = this.dialogConfig.addInputData
                      this.dialogConfig.editShow = false
                      this.dialogConfig.mainShow = true
                      this.$parent._initTreeData()
                  })
                  break;

          }

      },

      // 从进度的任务列表界面返回模型列表页面
      ganttDialogCallBack(){

        var _this = this;
        _this.status_materialbrowser = false;
        _this.clickRelevance = false;
        // 判断当前是否已经编辑过甘特图且当前人有编辑权限
        if (this.bPlusEdited && !this.progressEditDis) {

            // 提示用户进行操作确认
            this.$confirm("是否保存任务列表？", "操作确认", {
              confirmButtonText: '保存',
              cancelButtonText: '放弃',
              type: 'warning'
            }).then(x => {

                // 触发保存按钮点击事件
                _this.tiggerGanttBtns('id_abtn_save');

                _this.bNeedCloseAfterSavedPlus = true;

            }).catch(x => {
                if(_this.currentGanttDataInfoObj){
                  _this.$parent.treeClick(_this.currentGanttDataInfoObj)
                }

                // 直接关闭甘特图而不保存
                _this.closeGtt();
                _this.reloadGan()
            });

        } else {
          if(_this.currentGanttDataInfoObj){
            _this.$parent.treeClick(_this.currentGanttDataInfoObj)
          }

            // 直接关闭甘特图而不保存
            _this.closeGtt();
            _this.reloadGan()
        }


      },
      tiggerGanttBtns(str){
        this.noChoose = true
        let ifa = this.$refs.addOpenPlanView
        let data = {act:'reqToGtt_triggerBtn',type:str}
        ifa.contentWindow.postMessage(data,'*')
        // debugger
        if(str == 'id_abtn_addtask'){
          this.tiggerGanttBtns('id_abtn_save')
        }
        if(str == 'id_abtn_removetask'){
          this.removeDialog = 1
        }
        setTimeout(()=>{
            // console.log(this.noChoose);

            if(this.noChoose == true){
              if(str== "id_abtn_downgradetask" || str == "id_abtn_upgradetask"){
                  this.bPlusEdited = true;
              }
              return
            }
        },300)
      },
      // 甘特图关闭操作
      closeGtt() {
        var _this = this;

        // 刚点击开始模拟或模拟未停止时
        if (_this.playsign != 'end' || _this.bStartDis) {
          _this.$message.error('请先停止进度模拟');
          return;
        }
        _this.editGanttShow = false;
        _this.$staticmethod.Set("editGanttShow", "0");
        _this.$staticmethod.Set("currentGanttDataInfo", "");


        _this.addOpenModel = false;
        _this.ganttBtnsConfig.btnName = '打开模型';
        _this.showplaycomps = false;
      },
      evt_materialEditclose() {
        let _this = this;
        _this.status_materialedit = false
      },
      // 点击关联工程结构
      clickSelectRelevance(ev){
        this.clickRelevanceStyle.left = ev.x - 10 ;
        this.clickRelevanceStyle.top = ev.y + 20;
        this.clickRelevance = !this.clickRelevance;
      },
      getRelevanceStyle(){
        let _m = {};
        _m["top"] = this.clickRelevanceStyle.top + "px";
        _m["left"] = this.clickRelevanceStyle.left + "px";
        return _m;
      },
      // 点击自动关联
      clickAutoRelevance(){
        let _this = this;
        _this.clickRelevance = false;
        _this.status_AutoRelevance = true;
      },
      // 手动关联
      ProgressAutoRel() {
        // debugger
        var _this = this;

        if (_this.playsign != 'end') {
          _this.$message.error('请先停止模拟');
          return false;
        }
        if(_this.gannCurrentSelectData.length == 0){
          _this.$message.error('请先勾选至少一个任务');
          return false;
        } else if(_this.gannCurrentSelectData.length > 1){
          _this.$message.error('仅支持同时关联一个任务');
          return false;
        }

        if (_this.testHasChildren(_this.gannCurrentSelectData[0], _this.gannCurrentSelectData_full)) {
          _this.$message.error('选择的任务必须是叶子节点任务');
          return false;
        }

        // 分部主任务无法进行关联，通过判断是否有children来区分是否为分部主任务
        // if(_this.selectGTTPlan[0].children){
        //   _this.$message.error('该任务为分部主任务，无法进行关联');
        //   return false;
        // }else{
        //   _this.status_materialbrowser = true;
        //   _this.status_materialedit = false
        // }

        _this.status_materialbrowser = true;
        _this.status_materialedit = false;
        _this.clickRelevance = false;
      },

      testHasChildren(theItem, allItems) {
        var _this = this;
        if (allItems[0] && allItems[0].children && allItems[0].children.length > 0) {
          return true;
        }
        return false;
      },

      ProgressEditMgr() {
        let _this = this;
        if (_this.playsign != 'end') {
          _this.$message.error('请先停止模拟');
          return false;
        }
        if(_this.gannCurrentSelectData.length == 0){
          _this.$message.error('请先勾选至少一个任务');
          return false;
        }else if(_this.gannCurrentSelectData.length > 1){
          _this.$message.error('仅支持编辑一个任务');
          return false;
        }else{
          let _taskid =  _this.gannCurrentSelectData.join(',')
          var _Url = `${this.$MgrBaseUrl.GetTaskMaterialRelation}?planId=${_this.currentGanttDataInfo.bop_planId}&taskId=${_taskid}&Token=${_this.$staticmethod.Get("Token")}`;
          _this.$axios.get(_Url).then(x => {
            if(x.data.Data.List.length>0){
              _this.selectedObjList = x.data.Data.List;
              // _this.s1tatus_materialbrowser = false;
              // _this.status_materialedit = true;
            }else{
              _this.selectedObjList = []
              _this.$message.warning('暂无关联构件数据');
            }
          }).catch(x => {
              // console.log(x)
          });

        }
      },
      openmodelview2(){
        var _this = this;
        this.selectOpenSceneObj.SceneId = this.getDataSceneAndModelPlayObj.SceneId;
        this.selectOpenSceneObj.SceneModelId = this.getDataSceneAndModelPlayObj.SceneModelId;
        _this.getMoreModel();
      },
      do_closemodelview2() {
        var _this = this;

        // 关闭模型小窗口，播放控制界面
        _this.$refs.ref_modelPlay.forceStop(false, true);
        _this.showplaycomps = false;
        _this.addOpenModel = false;
        _this.playsign = 'end';
        _this.bStartDis = false;
        _this.stopPlayModel();
        // 置选中的构件数为0
        _this.modelCurrentSelectData = [];

      },

      closemodelview2(){
        var _this = this;

        // 判断当前的播放状态
        if (_this.playsign != 'end') {

          // 给出操作提示
          _this.$confirm('当前正在模拟，是否立即关闭？', '操作确认', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type:'warning'
          }).then(x => {
            _this.do_closemodelview2();
          }).catch(x => {

          });

        } else {
          _this.do_closemodelview2();
        }



      },
      // 模拟设置
      mockConfigClick() {
        let _this = this
        // 如果模型页面已经打开了，判断是否正在模拟
        if(_this.bStartDis){
          _this.modelInputEdit = false
        }
        if (_this.addOpenModel == true) {
            _this.showplaycomps = !_this.showplaycomps
            _this.showModelSetDialog = true; // 模拟设置的新样式弹窗
            // 显示控制界面
            // _this.playModelControlShow();

        } else {
            _this.$message.error('请先打开模型');
            return;
        }

      },
      // 模拟设置按钮文本
      getPlayCfgBtnText(){
        var _this = this;
        if (_this.showplaycomps == true) {
          return '关闭设置';
        } else {
          return '模拟设置';
        }
      },

      // 点击开始模拟按钮
      mockprogressclick(){
        let _this = this;
        if (_this.bStartDis) {
          return;
        }
        _this.bStartDis = true;
        _this.modelInputEdit = true

        // 如果模型页面已经打开了，判断是否正在模拟
        if (_this.addOpenModel == true) {

          // 确保控制界面已经显示
          _this.playModelControlShowAndPlay();

        } else {

          // 设置：当模型渲染完成后，则开始进行模拟
          _this.bPlayAfterFinishRender = true;

          // 打开模型，多模型展示
          _this.getMoreModel();

        }

        // if(_this.ganttBtnsConfig.btnName == '关闭模型'){
        //   _this.showplaycomps = !_this.showplaycomps
        //   if(_this.showplaycomps){
        //     _this.editModelIframeWindow.B1IMe.control.BIMeIsolate.isolateElementByElementId('');
        //   }else{
        //     _this.playEndFun()
        //   }
        // }

      },
      // 暂停模拟
      pausemodelClick() {
        var _this = this;
        setTimeout(()=>{
          _this.$refs.ref_modelPlay.stopMovie(true);
        }, 10);
      },
      // 停止模拟
      stopPlayModel(){

        // 如果模型模拟播放控制台打开了，关掉
        var _this = this;
        // if (_this.showplaycomps) {
        //   }
        _this.showplaycomps = false;
        _this.playsign = 'end';
        _this.modelInputEdit = false;
        // 之前没有停止操作
        setTimeout(()=>{
          _this.$refs.ref_modelPlay.forceStop(false, true);
        }, 10);

      },
      getBimConfig() {
          return window.bim_config
      },
      _inputChange(){
        let _this = this
        _this.modelInputEdit = false
      },
      _playStateSign(sign) {
        var _this = this;
        _this.playsign = sign;
        if(sign == 'end'){
          this.showplaycomps = false
        }
        //_this.$message.warning(sign);
      },
      ganttIframeLoad(){
        // let ifa = this.$refs.addOpenPlanView
        // let data = {
        //   act: 'reqToGtt_setShowGanttView', msg: {}, datas: false
        // }
        // ifa.contentWindow.postMessage(data,'*')


      },
      modeliframeload(){
       this.modeliframeload_onFinishRender();
      },
      // 打开场景失败
      openProjectSceneFail(){
        // 打开场景失败的时候，停止模拟、模拟状态为start、
        this.stopPlayModel()
      },
      // 确保控制界面已经显示
      playModelControlShowAndPlay() {

        // 如果此时控制界面没有显示，立即显示
        var _this = this;
        if (!_this.showplaycomps) {
          _this.showplaycomps = true;
        }
        //
        // 开始模拟测试
        _this.$refs.ref_modelPlay.playMovie();
        _this.bStartDis = false;

      },
      modeliframeload_onFinishRender() {
        // debugger
        var _this = this;
        _this.loadingModelEnd = false;

        // 是否在模型加载完成后立即开始模拟
        if (_this.bPlayAfterFinishRender == true) {

            // 立即模拟及恢复默认值
            _this.bPlayAfterFinishRender = false;
            _this.playModelControlShowAndPlay();
        }

        // 是否在模型加载完成后立即显示控制台
        if (_this.bShowControlAfterFinishRender == true) {

            // 立即显示控制台并恢复为false
            _this.bShowControlAfterFinishRender = false;
            _this.playModelControlShow();
        }

        // 是否在模型加载完成后立即高亮构件及 zoom
        if (_this.bSelectAndZoomAfterRender == true) {

            // 高亮及 zoom，及恢复默认值

            _this.newModelselectorElementByElementId(_this.arrSelectAndZoom);
            _this.newModelZoomCurrentElement();

            _this.arrSelectAndZoom = [];
            _this.bSelectAndZoomAfterRender = false;
        }
      },
      // 进度模拟播放结束取消隔离
      playEndFun() {
        let ifa = this.$refs.addOpenPlanView
        let data = {act:'clickPlayerModel',type:'startPlaying'}
        ifa.contentWindow.postMessage(data,'*')
      },
      // 处理以下内容：
      // 接收从甘特图组件发送来的消息并处理：显示某些任务的关联构件并高亮、聚焦
      ganttShowRelation(){

        let _this = this;
        // console.log('ganttShowRelation has been called!');

        // 如果左侧并没有选择任务，直接提示并跳出
        if(_this.gannCurrentSelectData.length == 0){
          _this.$message.error('请先勾选至少一个任务');
          return false;
        }else{

          // 补充计划ID
          var _planid = this.currentGanttDataInfo.planid;

          // 先请求与这些任务相关联的构件，再判断是否已经打开了模型
          let postdata = {
            taskIds: this.gannCurrentSelectData.join(','),
            planId: _planid,
            Token: this.$staticmethod.Get("Token")
          }
          this.$axios.post(`${window.bim_config.webserverurl}/api/Plus/PlusTask/GetRelationElementsByTaskIds`,this.$qs.stringify(postdata)).then(res=>{

            let data = res.data.Data.list
            if(data.length>0){
              let elements = []
              data.forEach(item=>{
                let elementinfos = JSON.parse(item.pte_elementids)
                elementinfos.forEach(info=>{
                  info.elementids.forEach(el=>{
                    elements.push(`${info.modelid}^${el}`)
                  })
                })

              })

              if(this.addOpenModel){

                // 显示某些任务的关联构件并高亮、聚焦
                // this.editModelIframeWindow.BI1Me.control.BIM1eZoom.zoomElem1entByElementId(elements[0])
                // this.editModelIframeWindow.BI1Me.control.BIMeSelector.selectorElementByElementId(elements);
                _this.$refs.ref_modelPlay.BIMZoom(elements[0]);

              }else{
                // this.$message.error('请先打开模型');
                // return false

                // 如果此时没有打开模型，标记“打开模型后需要设置构件的高亮及聚焦”属性，再设置 addOpenModel 为 true
                _this.bSelectAndZoomAfterRender = true;
                _this.arrSelectAndZoom = elements;
                _this.addOpenModel = true;

              }

            }
          });

        }
      },
      // 甘特图保存操作
      onPlusSaved(){
        // 弹出提示，保存成功！
        var _this = this;
        _this.$message.success('保存成功');

        // 判断是否需要关闭甘特图
        if (_this.bNeedCloseAfterSavedPlus) {
            _this.closeGtt();
                if(_this.currentGanttDataInfoObj){
                  _this.$parent.treeClick(_this.currentGanttDataInfoObj)
                }
        } else {
          // 刷新   这个刷新是双击编辑甘特图后重新加载甘特图，
          // _this.reloadGan();
        }
          _this.bNeedCloseAfterSavedPlus=false
          _this.bPlusEdited = false;

      },
      clearRelationAjax(){
        let _this = this

        let postData = {
          Token:_this.$staticmethod.Get('Token'),
          TaskIds: _this.gannCurrentSelectData.join(","),
          Token: this.$staticmethod.Get("Token")
        }
        _this.$axios.post(
            `${window.bim_config.webserverurl}/api/Plus/PlusTask/ClearTasksElements`,
            postData
        )
        .then(res => {
                _this.$nextTick(()=>{
                    if (res.data.Ret == 1 && res.data.Msg == "OK") {
                        _this.$message({
                            message: "取消关联成功",
                            type: "success"
                        })
                        let iframe = document.getElementById('addOpenPlanView').contentWindow
                        iframe.postMessage(
                            { act: "reqToGtt_setSelecteds", datas: [] },
                            "*"
                        )
                      _this.reloadGan()
                    }
                })

            })
      },
      // 刷新甘特图
      reloadGan() {
        var _this = this;
        // console.log('refresh gtt');
        let iframe_gtt = document.getElementById("addOpenPlanView");
        let gtt_src = iframe_gtt.getAttribute("src");
        iframe_gtt.setAttribute("src", gtt_src);
        _this.bPlusEdited = false;
        if(_this.addOpenModel){
          _this.$refs.ref_modelPlay.getElementByModle();
        }
        if(!_this.status_materialbrowser){
          _this.gannCurrentSelectData = [];
        }
      },
      // 单元格提交内容-处理
      onPlusCommitEdit(){

        // 标记当前是正在编辑的状态，以提供退出时的提示
        var _this = this;
        _this.bPlusEdited = true;

      },
      attachrelationAjax() {

        let _this = this;

        if (this.bPlusEdited && !this.progressEditDis) {
          _this.$message.error('请先保存任务列表');
          return;
        }

        // 尝试在左侧进行关联时，如果右侧未选中（或已关闭模型），则给出提示
        //debugger;

        if (_this.modelCurrentSelectData.length == 0) {
          _this.$message.error('请选中一个或多个构件再进行关联操作');
          return;
        }
        if(_this.gannCurrentSelectData.length == 0){
          _this.$message.error('请选择甘特图任务');
          return;
        }

        let postData = {
              planId: _this.currentGanttDataInfo.bop_planId,
              taskIds: _this.gannCurrentSelectData.join(","),
              elementIds: [],
              Token: this.$staticmethod.Get("Token")
            }
            _this.modelCurrentSelectData.forEach(el => {
                let elementarr = el.split("^")
                if (postData.elementIds.length > 0) {
                    postData.elementIds.forEach(id => {
                        if (id.modelid == elementarr[0]) {
                            id.elementids.push(elementarr[1])
                        }
                    })
                } else {
                    postData.elementIds.push({
                        modelid: elementarr[0],
                        elementids: [elementarr[1]]
                    })
                }
            })
          // debug1ger
          postData.elementIds = JSON.stringify(postData.elementIds)
          // console.log(postData,'====postData')
            _this.$axios.post(
                `${window.bim_config.webserverurl}/api/Plus/PlusTask/AppendRelationElements`,
                postData
            )
            .then(res => {
                _this.$nextTick(()=>{
                  if (res.data.Ret == 1 && res.data.Msg == "OK") {
                    _this.$message({
                      message: "添加成功",
                            type: "success"
                        })
                        // debu1gger
                        let iframe = document.getElementById('addOpenPlanView').contentWindow
                        iframe.postMessage(
                          { act: "reqToGtt_setSelecteds", datas: [] },
                            "*"
                        )
                    }
                    _this.reloadGan()
                })

            })
      },
      // 覆盖关联：特别注意如果右侧模型关闭了，则现在的 modelCurrentSelectData 应为 []
      reRelationAjax(){

        // 尝试在左侧进行关联时，如果右侧未选中（或已关闭模型），则给出提示
        let _this = this;

        if (this.bPlusEdited && !this.progressEditDis) {
          _this.$message.error('请先保存任务列表');
          return;
        }

        if (_this.modelCurrentSelectData.length == 0) {
          _this.$message.error('请选中一个或多个构件再进行关联操作');
          return false;
        }
        if(_this.gannCurrentSelectData.length == 0){
          _this.$message.error('请选择甘特图任务');
          return false;
        }

        let postData = {
              planId: _this.currentGanttDataInfo.planid,
              taskIds: _this.gannCurrentSelectData.join(","),
              elementIds: [],
              Token: this.$staticmethod.Get("Token")
            }
            _this.modelCurrentSelectData.forEach(el => {
                let elementarr = el.split("^")
                if (postData.elementIds.length > 0) {
                    postData.elementIds.forEach(id => {
                        if (id.modelid == elementarr[0]) {
                            id.elementids.push(elementarr[1])
                        }
                    })
                } else {
                    postData.elementIds.push({
                        modelid: elementarr[0],
                        elementids: [elementarr[1]]
                    })
                }
            })

          postData.elementIds = JSON.stringify(postData.elementIds)
            _this.$axios.post(
                `${window.bim_config.webserverurl}/api/Plus/PlusTask/RelationElements`,
                postData
            )
            .then(res => {
                _this.$nextTick(()=>{
                  if (res.data.Ret == 1 && res.data.Msg == "OK") {
                    _this.$message({
                      message: "关联成功",
                            type: "success"
                        })
                        let iframe = document.getElementById('addOpenPlanView').contentWindow
                        iframe.postMessage(
                          { act: "reqToGtt_setSelecteds", datas: [] },
                            "*"
                        )
                    }
                    _this.reloadGan()
                })

            })
      },
      setSliderDate(date) {
        var _this = this;
        if (_this.$refs.ref_modelPlay) {
          _this.$refs.ref_modelPlay.triggerSliderDate(date);
        }
      },

      getOrganizeId() {
        return this.$staticmethod._Get("organizeId");
      },

      evt_materialbrowserclose() {
          var _this = this;
          _this.status_materialbrowser = false;
          _this.clickRelevance = false;
          // _this.reloadGan();   取消时候不刷新甘特图
          // _this.gannCurrentSelectData = [];
      },
      // 关闭构件浏览对话框点击确定后
      evt_materialbrowseronok(arr) {
          var _this = this;
          // console.log('line1481')
          if (!arr.length) {
              _this.$message.warning('未选中构件');
              return;
          }

          // 确保 m_formmaterialselectedObj 是个有效数组，并与 arr 数组合成并集
          _this.getChangeMaterialRelation('2',arr)

          // debugger;
          // _this.m_formdocselectedObj = fileobj[0];
          _this.status_materialbrowser = false;
          _this.clickRelevance = false;
          // _this.gannCurrentSelectData = [];
          _this.status_materialedit = false;

          _this.reloadGan()

      },
      getChangeMaterialRelation(_type,arr) {
        // 0 时为新增， 1为移除，2为全覆盖
        let _this = this;
        let _url = `${this.$MgrBaseUrl.AddMaterialRelation}`;
        let selectedarr = []
        for (var i = 0; i < arr.length; i++) {
            selectedarr.push(arr[i]);
        }
        //console.log('======点击选中0 时为新增， 1为移除，2为全覆盖',_type)
        let _para = {
          Token: _this.$staticmethod.Get("Token"),
          PlanID: _this.currentGanttDataInfo.bop_planId,
          TaskID: _this.gannCurrentSelectData.join(','),
          MaterialJson: selectedarr,
          Type: _type
        }
        _this.$axios({
            url: _url,
            method: 'post',
            dataType: "json",
            contentType: "application/json",
            data: _para
        }).then(x => {
          this.$message({
              message: '操作成功',
              type: 'success'
          });

          if(_type != 2){
            _this.ProgressEditMgr()
          }
          if(_this.addOpenModel) {
            _this.getMoreModel()
          }
        }).catch(x => {

        })
      },
      clearMgrList(arr) {
        let _this = this;
        // console.log('reload 后要把已选中的设置回去');
        _this.getChangeMaterialRelation('1',arr)
        _this.reloadGan();
        // _this.s1tatus_materialbrowser = false;
      },
      onRemoveList(arr){
        let _this=this;
        let _arr = [];
        // this.s1tatus_materialbrowser = false;
        _arr.push(arr);
        if(_this.addOpenModel){
          _this.$refs.ref_modelPlay.getElementByModle();
        }
        _this.getChangeMaterialRelation('1',_arr)
        _this.reloadGan();
      },
      // 点击获取场景列表（获取的是包含模型ID对应的的场景）
      handelClickSelectScene(){
        let _this = this;
        let _url = `${window.bim_config.webserverurl}/api/Plus/PlusTask/GetMaterialModelIDByPlanId`;
        let _moreParam = {
            PlanId:  _this.currentGanttDataInfo.bop_planId,
            Token: this.$staticmethod.Get('Token'),
        }
        _this.$axios({
            url: _url,
            method: 'post',
            data: _moreParam
        }).then(x => {
            if(x.data.Ret == 1){
                let models = x.data.Data.ModelIds;
                models = models.join(',')
                this.bindingModelIDs = models;
                if(models.length == 0){
                  this.$message.warning('当前进度任务暂无关联工程结构')
                  return
                }
                this.sceneListDialog = true;
                // 这里的bindingModelIDs主要是为了点击之后模拟后，查找场景传参搜索使用、
            }else{
              _this.$message.error('服务器异常，请稍后再试')
            }
        }).catch(x => {

        })


      },
      // 选择打开场景
      handelClickOpenScene(item){
        // 选择打开场景、请求接口获取场景详情、场景加载完毕后，才能进行模拟
        this.selectOpenSceneObj = JSON.parse(item); // 当前选中场景的场景ID

        this.sceneListDialog = false;
        this.saveModelPlaySetAttribute('sceneSet');
        this.getMoreModel();
      },
      // 模拟设置点击确定传值
      modelPlaySetParams(obj){
        this.setModelSetDialogObj = obj;
        this.saveModelPlaySetAttribute('playSet');
        this.closeModelPlaySetDialog()
      },
      closeModelPlaySetDialog(){
        this.showModelSetDialog=false;
        this.showplaycomps=false;
      },
      // 获取当前场景模拟下的一些设置值，是否选择过场景、和模拟的设置值
      async getModelPlaySetAttribute(type){
        let params = {
          projectId: this.$staticmethod._Get("organizeId"),
          progressId: this.currentGanttDataInfo.bop_planId
          // progressId: this.currentGanttDataInfo.bop_planId,
        }
        const res = await this.$api.getProgressScence(params)
        if(res.Ret == 1){
          this.getDataSceneAndModelPlayObj = res.Data;
          this.getDataSceneAndModelPlayObj.SceneId && this.getDataSceneAndModelPlayObj.SceneId.length > 0 ? this.hasSelectScene = true : this.hasSelectScene = false;
          if(type == 'readonly' && res.Data.SceneId && res.Data.SceneId.length > 0){
            this.selectOpenSceneObj.SceneId = res.Data.SceneId
            this.selectOpenSceneObj.SceneModelId = res.Data.SceneModelId
            this.getMoreModel();
          }else if(type == 'readonly' ){
            this.$message.warning('当前进度暂无选定场景，请在编辑进度数据进行选择')
          }
        }
      },
      // 保存当前模拟场景的相关设置及当前选中的场景
      async saveModelPlaySetAttribute(type){
        let params = {}
        if(type == 'playSet'){
          // 模拟设置
          params = {
            SceneId:  this.getDataSceneAndModelPlayObj.SceneId || this.selectOpenSceneObj.SceneId,
            SceneModelId: this.getDataSceneAndModelPlayObj.SceneModelId || this.selectOpenSceneObj.SceneModelId,
            ProjectId: this.$staticmethod._Get("organizeId"),
            ProgressId: this.currentGanttDataInfo.bop_planId,
            ...this.setModelSetDialogObj
          }
        }else{
          params = {
            SceneId:  this.selectOpenSceneObj.SceneId,
            SceneModelId:  this.selectOpenSceneObj.SceneModelId,
            ProjectId: this.$staticmethod._Get("organizeId"),
            ProgressId: this.currentGanttDataInfo.bop_planId,
            UnstartState: this.getDataSceneAndModelPlayObj.UnstartState,
            UnstartColor: this.getDataSceneAndModelPlayObj.UnstartColor,
            UnderwayState: this.getDataSceneAndModelPlayObj.UnderwayState,
            UnderwayColor:  this.getDataSceneAndModelPlayObj.UnderwayColor,
            EndState: this.getDataSceneAndModelPlayObj.EndState,
            EndColor: this.getDataSceneAndModelPlayObj.EndColor,
            ProgressTime: this.getDataSceneAndModelPlayObj.ProgressTime,
          }
        }

        const res = await this.$api.postAddProgressScenceAsync(params)
        this.showModelSetDialog = false;
        this.getModelPlaySetAttribute();
      },

      // 打开模型，多模型展示
      getMoreModel() {

        //debugger;
        let _this = this;
        let _url = `${window.bim_config.webserverurl}/api/Plus/PlusTask/GetMaterialEleInfoFastByPlanId`;
        const loading = this.$loading({
            lock: true,
            text: '数据加载中，请您耐心等待...',
            spinner: 'el-icon-loading',
            background: 'rgba(0, 0, 0, 0.8)'
        });
        let _moreParam = {
          PlanId: _this.currentGanttDataInfo.bop_planId,
          Token: this.$staticmethod.Get('Token'),
          // TaskIds: _this.gannCurrentSelectData.join(',')
        }
        _this.$axios({
            url: _url,
            method: 'post',
            data: _moreParam
        }).then(x => {
          if(x.data.Ret == 1){
            let _data = x.data.Data;
            let sceneAndmodelidArr = JSON.parse(this.selectOpenSceneObj.SceneModelId)
            // console.log(sceneAndmodelidArr,'=sceneAndmodelidArr')

            let underWayList = _data.UnderWayList
            let allEles = []; // 当前任务所有的构件
            underWayList.forEach(task => {
                if (task.Elements) {
                    task.ElementsByModel = [];
                    // 循环处理task.Elements，把当前任务的相关构件存放在ElementsByModel中
                    task.Elements.forEach(elementGroup => {
                        let modelToSceneID = sceneAndmodelidArr.find(item => item.modelid === elementGroup.ModelId)
                        if(!modelToSceneID) return;
                        const modelId = modelToSceneID.sceneModelid;
                        const updatedElements = elementGroup.Elements.map(element => `${modelId}^${element}`);
                        elementGroup.Elements = updatedElements;
                        task.ElementsByModel.push(...updatedElements);
                        allEles.push({
                          TaskEles:task.ElementsByModel,
                          PERCENTCOMPLETE_: task.PERCENTCOMPLETE_,
                          TaskFinish: task.TaskFinish,
                          TaskId: task.TaskId,
                          TaskName:task.TaskName,
                          TaskStart: task.TaskStart,
                        })
                    });
                }else{
                  allEles.push({
                    TaskEles:[],
                    PERCENTCOMPLETE_: task.PERCENTCOMPLETE_,
                    TaskFinish: task.TaskFinish,
                    TaskId: task.TaskId,
                    TaskName:task.TaskName,
                    TaskStart: task.TaskStart,
                  })
                }
            });

            let newList = [];

        try{
            _data.List.forEach((item,index)=>{
              let progressTime = item.ProgressTime;
              let allIngelements = [];  // 当前日期下所有的构件
              let allTask = []  // 当前日期下所有的任务
              if(item.TaskIDs && item.TaskIDs.length > 0){
                item.TaskIDs.forEach(taskId => {
                    // 在 alleles 数组中找到对应的对象
                    let found = allEles.find(item => item.TaskId === taskId);
                    allTask.push(found);
                    if (found) {
                      // 将找到的对象的 taskEles 追加到结果数组中
                      allIngelements.push(...found.TaskEles);
                    }
                });
                let newItem = {
                  ProgressTime: progressTime,
                  Elements: allIngelements,
                  TaskNames: item.TaskNames,
                  TaskIDs: item.TaskIDs,
                  underWayList:allTask,
                };
                newList.push(newItem);
              }
            })
          } catch(e) {
            console.log('error',e)
          }
            _data.List = newList;

            _this.moreprojectID = _data;

            _this.finished = [];
            _this.inProgress = [];
            _this.notStarted = [];
            _this.changeListforTable(x.data.Data.List)
            if(x.data.Data.ModelIds.length <1){
              _this.$message.error('当前计划方案下暂无关联模型');
              _this.bStartDis = false;
              loading.close()
              return
            }
            let data_ModelIds = this.$staticmethod.DeepCopy(x.data.Data.ModelIds)
            let newModelIDs = []


            for (let i=0; i<sceneAndmodelidArr.length; i++) {
              if (data_ModelIds.includes(sceneAndmodelidArr[i].modelid)) {
                data_ModelIds[data_ModelIds.indexOf(sceneAndmodelidArr[i].modelid)] = sceneAndmodelidArr[i].sceneModelid;
              }
            }

            data_ModelIds.forEach(dataId => {
                sceneAndmodelidArr.forEach(item => {
                    if (item.sceneModelid === dataId) {
                        newModelIDs.push(item.sceneModelid);
                    }
                });
            });

            _this.moreModelIds = newModelIDs  // 关联的所有的模型ID,这个地方经过了过滤 把模型id转换成了对应的场景中的sceneModelid、只保留在场景中有这个模型的sceneModelid、否则在打开场景模拟的时候会因为找不见元素报错，
            // 处理modelid 使用|分割
            let str = '';
            for(var i=0;i<_this.moreModelIds.length-1;i++){
                str+=_this.moreModelIds[i]+"|";
            }
            _this.changemoreModelIds = str + _this.moreModelIds[_this.moreModelIds.length-1]
            _this.addOpenModel = true;
            loading.close()
          }else{
            loading.close()
            _this.$message.error('服务器异常，请稍后再试')
          }

          // console.log(data)

        }).catch(x => {
          loading.close()

        })
      },
      // 原来的关联模型改为关联构件之后，当返回无关联数据时候，改为不打开模型
      noDataFun(){
        let _this = this;
        _this.showplaycomps = false;
        _this.addOpenModel = false;
        _this.playsign = 'end'
        _this.bStartDis = false;
      },
      clickCurrentProgress(msg){
        this.finished = msg[0];
        this.inProgress = msg[1];
        this.notStarted = msg[2];
        this.currentProgressLegend = msg[3]; // 该参数为当前进度还是正在模拟的状态
      },
      changeListforTable(listData){
        let _this = this;
        listData.forEach((list)=>{
          if(list.Elements.length > 0){
            if(list.TaskActualFinish != null && list.TaskActualStart != null){
              _this.finished.push(list); // 有结束时间有开始时间
            }else if(list.TaskActualFinish == null && list.TaskActualStart != null){
              _this.inProgress.push(list); // 有开始时间没有结束时间
            }else if(list.TaskActualFinish  == null && list.TaskActualStart == null){
              _this.notStarted.push(list);  // 未开始
            }
          }
        })
      },
      closeAutoRel(){
        this.status_AutoRelevance = false;
      },
      PjtAutoRelevance(p1,p2){
        let _this = this;
        // console.log(p1)
        // console.log(p2)
        if(p1.length == 0){
          _this.$message.error('请选择工程结构');
          return;
        }
        if(p2.length == 0){
          _this.$message.error('请选择关联内容');
          return;
        }

         const loading = this.$loading({
          lock: true,
          text: '正在进行自动关联...',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        });

        _this.$axios({
            url: `${this.$MgrBaseUrl.AutomaticCoreelationComponect}`,
            method: 'post',
            data: {
              bm_Automatictype: p2, // plusk_codename
              bc_guid_materialtype: p1[0].bmc_guid,
              planId: _this.currentGanttDataInfo.bop_planId,
              organizeId: _this.$staticmethod._Get("organizeId"),
              Token: this.$staticmethod.Get("Token"),
              bm_materialname: ''
            }
        }).then(x => {
            if (x.data.Ret > 0) {
              _this.$message.success(x.data.Msg);
              _this.status_AutoRelevance = false;
              loading.close();
              _this.reloadGan();
            } else {
              _this.$message.error(x.data.Msg);
              loading.close();
              _this.reloadGan();
            }
        })

      },
      // 新模型=高亮
      newModelselectorElementByElementId(eles){
          let elements = scene.selectedObjects;
          // 判断集合是否为空
          if (elements.size > 0) {
              let elementIds = Array.from(elements.keys())

              for(let i = 0; i < elementIds.length; i++ ){
                  let element = window.scene.findObject(elementIds[i])
                  element.selected = false
                  window.scene.render();
              }
          }
          for(let i = 0; i < eles.length; i++ ){
              let element = window.scene.findObject(eles[i])
              element.selected = true
              window.scene.render();
          }
      },
      // 新模型=zoom聚焦
      newModelZoomCurrentElement(){
        let elements = window.scene.selectedObjects;
        window.scene.fit(Array.from(elements.keys()));
      },
      // 左边list折叠
      handleClickInOut(){
        this.proLeftIn = !this.proLeftIn;
      },

  },
  created(){},
  mounted(){
    this.hasEditAuth = this.$staticmethod.hasSomeAuth('JDFA_Edit')

    let _this = this;
    if(_this.$staticmethod.Get('editGanttShow') == '1'){
      this.editGanttShow = true;
      // console.log(this.$staticmethod.Get("gttEdit") == 'readwrite' ,'=this.$staticmethod.Get("gttEdit") == ')
      let num = -1
      if(this.$staticmethod.Get("gttEdit") == 'readwrite'){
        this.clickRightPlay = false;
        num = 1;
      }else{
        this.clickRightPlay = true;
        num = 0
      }
      this.currentGanttDataInfo = JSON.parse(_this.$staticmethod.Get('currentGanttDataInfo'))
      this.detailProgressUI(num)
    }
    if(this.$route.query.title == '进度方案'){

        _this.bigBimTop = this.$refs.proList.getBoundingClientRect().top;
        _this.bigBimLeft = this.$refs.proList.getBoundingClientRect().left;
        this.headerHide = false;
        if(this.$route.query.modelQuery){
          this.currentGanttDataInfo = this.$route.query.modelQuery
        }else{
          this.currentGanttDataInfo = {
            NAME_: "柱_墙_按计划模拟",
            bop_guid: "9f1395e4-4c39-4b7f-8950-d72a345588f5",
            bop_modelId: "undefined",
            bop_modelphase: "undefined",
            bop_organizeId: "81b643a5-0af1-4c46-8a2f-1531245626fb",
            bop_planId: "a713da99-2136-4e52-be4a-3adfc450d85e",
            name: "柱_墙_按计划模拟",
          }
        }
        this.detailProgressUI(0);

    }else{
        this.headerHide = true
    }

    this.projectid = this.$staticmethod._Get("organizeId");
    // 定义全局消息，统一接收获取 arr？
    window.addEventListener('message',(msg)=>{
      // console.log(msg.data,'====addEventListener')

      // 全局处理
      let arr = []

      // 甘特图传来的所有任务，setAllPlanListArr暂存该值
      if (msg.data.act == "setAllPlanList"){
        this.setAllPlanListArr = msg.data.datas;
      }


      if (msg.data.act == "evFromGtt_Selected"){
        this.selectGTTPlan = msg.data.datas;
        if(msg.data.datas){
          _this.gannCurrentSelectData_full = [];
          msg.data.datas.forEach(item => {
              arr.push(item.UID);

              _this.gannCurrentSelectData_full.push(item);
          })

          _this.$staticmethod.debugshowlog(`gannCurrentSelectData 接收到的arr的长度为${arr.length}，消息类型为${msg.data.act}`);
          _this.gannCurrentSelectData = arr;
          // console.log(`gannCurrentSelectData 接收到的arr的长度为${arr.length}，消息类型为${msg.data.act}`)
          // console.log(arr)
        }
      }

      // 甘特图内部保存操作
      if (msg.data.act == "resFromGtt_OnSaved") {
        _this.onPlusSaved();
        return;
      }

      // 单元格提交内容
      if (msg.data.act == "resFromGtt_CellCommitEdit") {
        _this.onPlusCommitEdit();
        return;
      }


      if(msg.data.act == "resFromGtt_OnAttachrelation"){//附加关联
        // _this.attachrelationAjax();    // 这是之前的旧模型逻辑，已废弃

        _this.ProgressAutoRel();
        return;
      }
      if(msg.data.act == "resFromGtt_OnReRelation"){//新增关联==覆盖关联
        _this.reRelationAjax();
        return;
      }
      if(msg.data.act == "reqFromGtt_triggerBtn"){//编辑任务
        _this.tiggerGanttBtns('id_abtn_updatetask');
        return;
      }

      // 双击当前行进行编辑任务
      if(msg.data.act == "evFromGtt_db_Selected"){//编辑任务
        _this.tiggerGanttBtns('id_abtn_updatetask');
        return;
      }

      if(msg.data.act == "evFromGtt_editend_ok"){   //编辑任务后点击的确定
        _this.tiggerGanttBtns('id_abtn_save');
        return;
      }

      if(msg.data.act == "noChooseTask"){ //没有选择任务
        this.noChoose = false
        _this.$message.warning('请选择需要操作的任务');
        return;
      }

      if(msg.data.act == "removeChooseTask"){ //删除选中任务
        // alert('收到了')
        // console.log('收到了id_abtn_removetask')
        console.log(_this.removeDialog)
        if(_this.removeDialog != 1) return
        _this.removeDialog = 1
        _this.$confirm('确定删除选中任务？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          _this.removeDialog = 2;
          let ifa = this.$refs.addOpenPlanView
          let data = {act:'reqToGtt_removeChooseTask',type:'reqToGtt_removeChooseTask'}
          ifa.contentWindow.postMessage(data,'*')
          setTimeout(()=>{
            _this.tiggerGanttBtns('id_abtn_save')
          },500)
          return
        }).catch(() => {
          _this.removeDialog = 2;
          // _this.$message({
          //   type: 'info',
          //   message: '已取消删除'
          // });
          return
        });
      }

      // 接收从甘特图组件发送来的消息并处理：显示某些任务的关联构件并高亮、聚焦
      // if (msg.data.act == 'resFromGtt_linkclick') {
      //   _this.msgevt1_linkclick(msg.data);
      //   return;
      // }


      if(msg.data.act == 'resFromGtt_OnShowRelation'){
        _this.ganttShowRelation();
        return;
      }
      if(msg.data.act == "resFromGtt_OnClearRelation"){//取消关联
        _this.clearRelationAjax()
        return;
      }
      if (msg.data.act == "resFromGtt_getSelecteds"){
        if(msg.data.type == 'add'){
          _this.reRelationAjax()
          return;
        }else if(msg.data.type == 'show'){
          _this.ganttShowRelation()
          return;
        }else{
            _this.clearRelationAjax()
          return;
        }
      }
      if (msg.data.act == 'resFromGtt_setSliderDate') {
        _this.setSliderDate(msg.data.Date);
      }


    })
  },
  destroyed(){
    this.$staticmethod.Set("editGanttShow", "");
    this.$staticmethod.Set("currentGanttDataInfo", "");
  }
}
</script>
<style lang="stylus" scoped rel="stylesheet/stylus">
    .wzw-wp{
        display inline-block
        background red
        width 20px
        height 20px
    }
    .progress-list{
        width 270px
        margin-right 20px
        height calc(100vh - 64px)
        background #fff
        position relative
        h2{
            height 50px
            line-height 50px
            font-size 14px
            font-weight 500
            color rgba(0,0,0,.85)
            display flex
            margin: 0 8px 0 16px;
            justify-content space-between
            align-items center
            span{
                flex 1
                text-align left
            }
            i{
              cursor pointer
            }
        }

    }
    .entry-list {
    overflow-y: auto;
    padding: 16px 8px;
    color: rgba(0, 0, 0, 0.65);
    p{
        text-align left
        font-size 14px
        color rgba(0,0,0,.85)
    }
    input {
        width calc(100% - 16px)
        height 40px
        line-height 40px
        padding-left 16px
        border-radius 4px
        border 1px solid rgba(0,0,0,.09)
        outline none
        margin-top 5px
    }
    li {
        height: 40px;
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
        padding: 0 12px 0 8px;
        position: relative;
        cursor pointer

        span {
            flex: 1;
            text-align: left;
            margin: 0 12px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        i:last-child {
            cursor: pointer;
            opacity: 0.8;

            &:hover {
                opacity: 1;
            }
        }

        &:hover {
            background: rgba(0, 0, 0, 0.04);
        }
    }

}
</style>
<style scoped>
._css-materhead-addbtn {
  height: 32px;
  width: 32px;
  line-height: 32px;
  cursor: pointer;
  position: relative;
}
._css-materhead-addbtn:hover {
  cursor: pointer;
  color: rgba(24, 144, 255, 1);
  background-color: rgba(24, 144, 255, 0.1);
}
.custom-tree-node{
  display: flex;
  flex: 1;
  text-align: left;
}
._css-tree-label{
  flex:1;
  display: inline-block;
  width: calc(100% - 48px);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
._css-treenode-menubtn{
  display: inline-block;
  width: 30px;
}
._css-progress-btn{
  z-index:2;
  position: absolute;
  top: 35px;
  left:10px;
  width: 150px;
  border: 1px solid rgba(0,0,0,.1);
  border-radius: 3px;
  background: #fff;
}
.tree-sub-menu{
  z-index:2;
  position: absolute;
  top: 35px;
  left:10px;
  width: 150px;
  background: #fff;
  border: 1px solid rgba(0,0,0,.1);
  border-radius: 3px;
  text-align: left;
}
.tree-sub-menu div,._css-progress-btn div{
  display: flex;
  align-items: center;
  line-height: 40px;
  padding-left: 15px;
  cursor: pointer;
}
.tree-sub-menu div:hover,._css-progress-btn div:hover{
  background: rgba(0,0,0,.1)
}
.icon{
  padding-right: 10px;
}
/* 关于进度模拟css */
.el-edit-progress-wp .top-btn{
  display: flex;
  flex-direction: row;
}
.el-edit-progress-wp .top-btn div{
  height: 40px;
  line-height: 40px;
  color: #fff;
  background: rgb(24, 144, 255);
  cursor: pointer;
  padding: 0 8px;
  border-radius: 4px;
  margin-left: 16px;
  opacity: .8;
}
.el-edit-progress-wp .top-btn div:hover{
  opacity: 1;
}
._css-vhidden {
  visibility: hidden;
}
.full-screen-main {
  display: flex;
}
._css-white-btn {
  color:#1890FF;
}

._css-white-btn:hover{
  background-color: rgba(24, 144, 255, 0.1);
}

._css-white-btn._css-reverse {
  background-color: rgba(24, 144, 255, 1);
  color:#fff;
  opacity: 0.7;
}

._css-white-btn._css-reverse:hover {
  opacity: 1;
}

.el-progress-wp{
  display: flex;
  flex-direction: column;
}
.el-progress-wp .btn-wp{
  height: 64px;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  padding: 0 24px;
}
.el-progress-wp .btn-wp i{
  margin-right: 8px;
}
.iframes-wp{
  flex: 1;
  display: flex;
  flex-direction: row;
}
.iframe-all-show{
  flex:1;
  height:100%;
}
.iframe-hide{
  height:60%;
}
.small-iframe{
  width:100%;
  height: 50%;
}
.full-iframe{
  width:100%;
  height:100%;
}
.table-model-state{
  width: 100%;
  height: 50%;
  margin-top: -20px;

}
.table-model-table-hide{
  width: 100%;
  height: 50%;
  margin-top: -20px;
}
.table-downOrup{
  height: 20px;
}

.table-down{
  background: url('../../assets/images/model-table-down.png') no-repeat 100% 100%;
  background-size:100% 100%;
}
.table-up{
  background: url('../../assets/images/model-table-up.png') no-repeat 100% 100%;
  background-size:100% 100%;
}
.table-model-state-css{
  display: flex;
  flex-direction: column;
  position: relative;
}
.table-show-hide-btn{
  display:inline-block;
  width:16px;
  height:16px;
  cursor: pointer;
  text-align: center;
}
.el-progress-wp .btn-wp>div{
  display: flex;
  flex-direction: row;
}
.el-progress-wp .btn-wp>div div{
  padding: 0 8px;
  height: 32px;
  display: flex;
  flex-direction: row;
  align-items: center;
  border-radius: 4px;
  border: 1px solid #1890ff;
  margin-left: 16px;
  cursor: pointer;
}
.el-progress-wp .btn-wp>div div.show-table-detail{
  padding: 0 21px;
}
.el-progress-wp .btn-wp>div div.show-ban-click{
  cursor: pointer;
  pointer-events: initial;
}
.el-progress-wp .btn-wp>div div.playmodel{
  background: #1DA48C;
  color: #fff;
  border-color: #1DA48C;
   opacity:.7;
}

.el-progress-wp .btn-wp>div div.stopmodel{
  background: #F5222D;
  color: #fff;
  border-color: #F5222D;
   opacity:.7;
}

.el-progress-wp .btn-wp>div div.pausemodel{
  background: #FAAD14;
  color: #fff;
  border-color: #FAAD14;
   opacity:.7;
}

.el-progress-wp .btn-wp>div div.playmodel:hover{
    opacity:1;
}

.el-progress-wp .btn-wp>div div.stopmodel:hover{
    opacity:1;
}

.el-progress-wp .btn-wp>div div.pausemodel:hover{
    opacity:1;
}

.el-progress-wp .btn-wp>div div.ban-click{
  background: rgba(0, 0, 0, .25);
  color:#fff;
  cursor: not-allowed;
  border: 1px solid transparent;
}

.el-progress-wp .btn-wp>div div._css-btn-dis{
  opacity: 0.3;
  cursor: not-allowed;
}

.el-progress-wp .btn-wp>div div:not(._css-nothover):not(._css-btn-dis):hover{
  opacity: 1;
}

._css-select-rel{
  position: relative !important;
  display: block;
}
.el-progress-wp .btn-wp>div div._css-select{
  position: fixed;
  background:#fff;
  border: 1px solid #1890ff;
  border-radius: 4px;;
  z-index: 99999;
  display: block;
  padding: 8px;
  height: 60px;
}
.el-progress-wp .btn-wp>div div._css-select p{
  display: block;
  line-height: 30px;
  padding: 0 18px;
}
._css-select p:hover {
  background:#1890ff;
  color:#fff;
}
.el-progress-wp .btn-wp>div div:first-child{
  margin-left: 0;
}
.el-progress-wp .el-tree-node:focus>.el-tree-node__content, .el-progress-wp .el-tree-node__content:hover, .progress-list .el-tree-node:focus>.el-tree-node__content, .progress-list .el-tree-node__content:hover{
  background: rgba(245, 245, 245, 1) !important;
}

._css-auto-rel-icon {
    padding-left: 0 !important;
    padding-right: 0 !important;
    border: none !important;
}
._css-auto-rel-text{
      padding-left: 0 !important;
    padding-right: 0 !important;
    border: none !important;
    margin-left:8px !important;
    font-size: 14px !important;
    line-height: 14px !important;
    color:#1890FF !important;
}
/* 关于进度模拟css */


._css-masking-edit-component{
  position:fixed;
  top:0;
  left:0;
  bottom:0;
  right:0;
  width:100%;
  height:100%;
  background:rgba(0,0,0,.4);
  z-index:1002;
}
.size-weight{
  font-size: 18px;
  font-weight: 800;
  vertical-align: middle;
}
.progress-list .el-tree{
  background: #fff !important;
}
.pro-in-out{
  position: absolute;
  top: calc(50% - 120px);
  left:270px;
  width: 16px;
  height: 120px;
  background-image: url(../../assets/images/p-in.png) ;
  background-size: 100%;
  background-repeat: no-repeat;
  cursor: pointer;
}
.pro-in-out.p-out{
  background-image: url(../../assets/images/p-out.png) ;
  background-repeat: no-repeat;
  background-size: 100%;
  left: 0;
}
.p-list-out{
  width:0;
}
.icon-set-font.icon-new-down:before,.icon-set-font.icon-new-up:before{
  color: #2c3e50;
  font-size: 14px;
  font-weight:500;
  padding:2px
}
._css-addingnameinput-ctn ._css-title-flowname {
    width: 30%;
    text-align: left;
}
</style>
