<template>
  <div class="_css-docbrowser" :style="{ 'z-index': m_zIndex || 1003 }">
    <div class="_css-docbrowser-in" :style="getFrontStyle()">
      <!-- 标题区域 -->
      <div class="_css-docin-title css-fc">
        <div class="_css-title-content css-f1">
          {{ init_title }}
        </div>
        <div
          @click="$emit('onclose')"
          class="_css-title-closebtn css-closebtn icon-suggested-close"
        ></div>
      </div>
      <!-- //标题区域 -->

      <div @mousedown="_stopPropagation($event)" class="_css-docin-content">
        <div
          class="_css-docin-tree"
          v-loading="m_treeloading"
          element-loading-text="加载中..."
        >
          <el-tree
            :data="m_rootfolders"
            :props="treem_props"
            lazy
            ref="ref_wftc"
            class="_css-customstyle"
            :expand-on-click-node="false"
            @node-collapse="node_collapse"
            @node-expand="node_expand"
            @node-click="node_click"
            :load="treefunc_loadChild"
            node-key="bmc_guid"
            :highlight-current="true"
            :auto-expand-parent="true"
          >
            <span class="css-fc _css-treenode-content" slot-scope="{ data }"> 
              <i class="css-icon20 css-fs18 css-fc css-jcsa" :class="data.classname" ></i>
              <span :title="data.bmc_name" class="css-ml4 _css-treenodelabel">{{
                data.bmc_name
              }}</span>
            </span>
          </el-tree>
        </div>
      </div>

      <!-- 对话框按钮区域 -->
      <div class="css-common-zdialogbtnctn _css-docin-btnctn">
        <zbutton-function
          :init_text="'取消'"
          :init_fontsize="14"
          :debugmode="true"
          :init_height="undefined"
          :init_width="'76px'"
          :init_bgcolor="'#fff'"
          :init_color="'#1890FF'"
          @onclick="$emit('onclose')"
        >
        </zbutton-function>
        <zbutton-function
          :init_text="'确定'"
          :init_fontsize="14"
          :debugmode="true"
          :init_height="undefined"
          :init_width="'76px'"
          @onclick="_emit_onok()"
        >
        </zbutton-function>
      </div>
      <!-- //对话框按钮区域 -->
    </div>
  </div>
</template>
<script>
export default {
  data() {
    return {
      m_treeloading: false,
      m_organizeId: "",
      m_bimcomposerId: "",

      // 接收传入参数
      // -----------
      m_zIndex: 0,
      m_height: 0,
      m_width: 0,
      // 根级文件夹
      m_rootfolders: [],
      m_selectedfiles: {},
      // 树控件的 props 属性对象
      treem_props: {
        children: "children",
        label: "FileName",
        isLeaf: "isLeaf",
      },
    };
  },
  mounted() {
    let _this = this;
    _this.m_zIndex = _this.init_zIndex;
    _this.m_width = _this.init_width;
    _this.m_height = _this.init_height;
    _this.m_bimcomposerId = _this.init_bimcomposerId;
    _this.m_organizeId = _this.init_organizeId;

    // 加载根级分类（不带“全部分类”)
    _this.func_getRootCategories();
  },

  methods: {
    _emit_onok() {
      var _this = this;
      _this.$emit("onok", _this.m_selectedfiles);
    },
    // 单击节点，加载里面的文件夹及文件
    node_click(itemi, node, comp) {
      console.log("========happy");
      console.log(itemi);
      this.m_selectedfiles = itemi
    },

    // 加载某个文件夹下的子文件夹
    treefunc_loadChild(node, resolve) {
      // 获取所展开节点下的子文件夹
      var _this = this;
      var _nodedata = node.data;

      if (!_nodedata.bmc_code) {
        return;
      }

      // 调用接口，获取子项
      // -----------------
      var _url = `${window.bim_config.webserverurl}/api/Material/MaterialCategory/GetCategories?organizeId=${_this.m_organizeId}&baseCode=${_nodedata.bmc_code}&Token=${_this.$staticmethod.Get('Token')}`;
      _this.$axios
        .get(_url)
        .then((x) => {
          // 拿到当前文件夹的子级，resolve 到下面
          // ----------------------------------
          var onlyFolder = x.data.Data.list; //.filter(x => x.FileSize == "0");

          // 挂上 classname
          // --------------
          for (var i = 0; i < onlyFolder.length; i++) {
            onlyFolder[i].classname = "icon-interface-component_classification";
          }

          _this.func_analysisfolders(onlyFolder, (toval) => {
            resolve(toval);
          });
        })
        .catch((x) => {
          console.log("catch line 156");
        });
    },

    // 收起节点 回调
    // 展开节点 回调
    // ------------
    node_collapse(itemi) {
      itemi.classname = "icon-interface-component_classification";
    },
    node_expand(itemi) {
      itemi.classname = "icon-interface-component_classification";
    },

    // 分析 表示多个文件夹的数组 maybeRootNotFile 是否有子文件夹
    func_analysisfolders(maybeRootNotFile, callback) {
      // 遍历【当前需要解析是否有子文件夹的】所有文件夹
      for (var i = 0; i < maybeRootNotFile.length; i++) {
        // 有子文件夹
        if (maybeRootNotFile[i].DirectChildrenCount == 0) {
          maybeRootNotFile[i].isLeaf = true;
          maybeRootNotFile[i].classname =
            "icon-interface-associated-component _css-1890ff";
        } else {
          maybeRootNotFile[i].isLeaf = false;
        }
      }

      // 赋值以渲染根级目录
      if (callback) {
        callback(maybeRootNotFile);
      }
    },

    // 提供方法，获取第一级文件夹
    func_getRootCategories() {
      var _this = this;
      _this.m_treeloading = true;
      var _url = `${window.bim_config.webserverurl}/api/Material/MaterialCategory/GetCategories?organizeId=${_this.m_organizeId}&baseCode=&Token=${_this.$staticmethod.Get('Token')}`;
      _this.$axios
        .get(_url)
        .then((x) => {
          _this.m_treeloading = false;

          // 绑定根级文件夹到 tree
          // 先拿到所有非文件的数据
          // 绑定 icon-interface-unfolder 图标类
          var maybeRootNotFile = x.data.Data.list; // .filter(y => y.FileSize == 0);

          // 遍历添加 classname
          // -----------------
          var i = 0;
          for (i = 0; i < maybeRootNotFile.length; i++) {
            // 赋予 classname 以展示树节点图标
            maybeRootNotFile[i].classname =
              "icon-interface-component_classification";
          }

          _this.func_analysisfolders(maybeRootNotFile, (valtoset) => {
            _this.m_rootfolders = valtoset;
          });
        })
        .catch((x) => {
          _this.m_treeloading = false;
          debugger;
        });
    },

    // 前景样式
    _stopPropagation(ev) {
      ev && ev.stopPropagation && ev.stopPropagation();
    },
    greet(val) {
      var _this = this;
      _this.val = val;
    },
    getFrontStyle() {
      var _this = this;
      var _s = {};
      _s["width"] = _this.m_width;
      _s["height"] = _this.m_height;
      _s["position"] = "fixed";
      _s["right"] = `calc(50% - ${
        parseInt(_this.m_width.toString().replace("px", "px")) / 2
      }px)`;
      _s["top"] = `calc(50% - ${
        parseInt(_this.m_height.toString().replace("px", "px")) / 2
      }px)`;
      return _s;
    },
  },
  props: {
    // 前景高度
    // 前景宽度
    // 总体的 zindex
    // 标题
    init_height: {
      type: String,
      required: true,
    },
    init_width: {
      type: String,
      required: true,
    },
    init_zIndex: {
      type: Number,
      required: true,
    },
    init_title: {
      type: String,
      required: true,
    },
    init_bimcomposerId: {
      type: String,
      required: true,
    },
    init_organizeId: {
      type: String,
      required: true,
    },
  },
};
</script>
<style scoped>
._css-1890ff {
  color: #1890ff;
}
._css-docin-tree {
  font-size: 12px;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.02);
  overflow-y: auto;
}
._css-title-content {
  font-size: 16px;
  margin: 0 24px 0 24px;
  flex: 1;
  text-align: left;
}
._css-docin-content {
  width: 100%;
  flex: 1;
  display: flex;
  height: calc(100% - 50px - 64px);
}
._css-docin-btnctn {
  border-top: 1px solid rgba(0, 0, 0, 0.09);
  height: 50px;
  margin-top: 0;
  flex: none;
  box-sizing: border-box;
}
._css-docin-title {
  height: 64px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.09);
  flex: none;
  box-sizing: border-box;
}
._css-title-closebtn {
  margin-right: 24px;
}
._css-docbrowser-in {
  border: 1px solid rgba(0, 0, 0, 0.09);
  background-color: #fff;
  border-radius: 4px;
  display: flex;
  flex-direction: column;
}
._css-docbrowser {
  width: 100%;
  height: 100%;
  position: fixed;
  background: rgba(0, 0, 0, 0);
  display: flex;
  align-items: center;
  justify-content: space-around;
}
</style>