<template>
  <div @click="closeall($event)" class="_css-autorel-all">
    <div :id="loadingId" class="_css-autorel-all-in" v-drag="draggreet" :style="dragstyle">
      <CompsDialogHeader @oncancel="_oncancel" :title="title"></CompsDialogHeader>

      <div class="_css-content" @mousedown="_stopPropagation($event)">
        <!-- 请选择模型，标题和按钮 -->
        <div class="_css-modelsel-title _css-title">请选择模型</div>
        <div class="_css-modelsel-btn">
          <div @click="showModelList($event)" class="_css-modelsel-btn-in">
            <div class="_css-modelsel-btn-icon icon-interface-addnew"></div>
            <div class="_css-modelsel-btn-text">添加模型</div>
          </div>
        </div>
        <!-- //请选择模型，标题和按钮 -->

        <!-- 已选择的模型 -->
        <div v-if="arrModelAdded.length > 0" class="_css-modelchose-list">
          <div v-for="amd in arrModelAdded" :key="amd.modelid" class="_css-modelchose-i">
            <div class="_css-modelchosei-icon icon-interface-associated-component"></div>
            <div :title="amd.modelname" class="_css-modelchosei-text">{{amd.modelname}}</div>
            <div
              @click="removeModelAdded(amd.modelid, $event)"
              class="_css-modelchosei-rmbtn icon-suggested-close_circle"
            ></div>
          </div>
        </div>
        <!-- //已选择的模型 -->

        <!-- 任务列表中的属性名称 -->
        <div class="_css-title">{{func_getfield1desc()}}</div>
        <div class="_css-input">
          <div @click="switchTaskOptions($event)" class="_css-input-in _css-input-sel">
            <input
              readonly
              class="_css-input-in-input _css-input-sel"
              type="text"
              :placeholder="'请选择' + func_getfield1desc()"
              :value="getTaskPropertyText()"
            />
            <div class="_css-input-in-icon icon-arrow-down_outline"></div>

            <div v-if="bShowOptions" class="_css-input-options">
              <div
                @click="selectTaskProperty(opt.value, $event)"
                v-for="opt in taskPropertyOptions"
                :key="opt.value"
                class="_css-input-options-i"
              >{{opt.label}}</div>
            </div>
          </div>
        </div>
        <!-- //任务列表中的属性名称 -->

        <!-- 模型中的属性名称 -->
        <div class="_css-title">模型中的属性名称</div>
        <div class="_css-input">
          <div class="_css-input-in">
            <input
              v-model="modelPropertyInput"
              class="_css-input-in-input"
              type="text"
              placeholder="请输入模型属性名称"
            />
          </div>
        </div>
        <!-- //模型中的属性名称 -->
      </div>

      <!-- 开始关联按钮 -->
      <div class="_css-begin-autorel">
        <div @click="beginRel" class="_css-begin-autorel-in">开始关联</div>
      </div>
      <!-- //开始关联按钮 -->
    </div>
    <CompsSelectModel
      @getModelInfo="getModelInfo"
      v-if="bShowSelectModel"
      @close="closeSelectModel"
    ></CompsSelectModel>
  </div>
</template>
<script>
import CompsDialogHeader from "@/components/CompsDialog/CompsDialogHeader";
import CompsSelectModel from "@/components/CompsMaterial/CompsSelectModel";
export default {
  components: {
    CompsDialogHeader,
    CompsSelectModel,
  },
  created() {
    var _this = this;
    if (_this.init_field1options) {
      _this.taskPropertyOptions = _this.$staticmethod.DeepCopy(_this.init_field1options);
    }
  },
  data() {
    return {
      // 拖动相关
      val2: "0",
      dragstyle: {
        position: "fixed",
        right: "calc(50% - 195px)",
        top: "calc(50% - 170px)",
      },
      // //拖动相关
      extdata: {},
      bShowSelectModel: false,
      bShowOptions: false,
      arrModelAdded: [], // 已经添加到列表的选择的模型
      taskPropertyOptions: [
        {
          value: "NAME_",
          label: "任务名称",
        },
      ],
      taskPropertySelected: "",
      modelPropertyInput: "", //
    };
  },
  methods: {
    func_getfield1desc() {
      var _this = this;
      if (_this.init_field1name) {
        return _this.init_field1name;
      } else {
        return "计划任务中的属性名称";
      }
    },

    beginRel() {
      var _this = this;
      var arr = [{ modelid: _this.modelid, modelname: _this.modelname }];
      _this.$emit(
        "onrel",
        arr,
        _this.taskPropertySelected,
        _this.modelPropertyInput
      );
    },

    getTaskPropertyText() {
      var _this = this;
      //var propertyObj = _this.taskPropertyOptions.find()
      var propertyIndex = _this.taskPropertyOptions.findIndex(
        (x) => x.value == _this.taskPropertySelected
      );
      if (propertyIndex >= 0) {
        return _this.taskPropertyOptions[propertyIndex].label;
      }
      return "";
    },

    selectTaskProperty(val, ev) {
      var _this = this;
      _this.taskPropertySelected = val;
    },

    removeModelAdded(modelid, ev) {
      var _this = this;
      //console.log(modelid);
      _this.arrModelAdded = _this.arrModelAdded.filter(
        (x) => x.modelid != modelid
      );
    },

    getModelInfo(modelid_modelname) {
      var _this = this;
      //console.log(modelid_modelname);

      // 判断是否已经添加了这个模型，如果已添加报提示。
      if (
        _this.arrModelAdded.findIndex(
          (x) => x.modelid == modelid_modelname.modelid
        ) >= 0
      ) {
        _this.$message.error("该模型已添加到自动关联列表，请选择其它模型");
        return;
      }

      _this.arrModelAdded.push(modelid_modelname);
      _this.bShowSelectModel = false;
    },

    //
    closeall(ev) {
      var _this = this;
      _this.bShowOptions = false;
    },

    //
    switchTaskOptions(ev) {
      var _this = this;
      if (_this.bShowOptions == true) {
        _this.bShowOptions = false;
      } else {
        _this.bShowOptions = true;
        if (ev) {
          ev.stopPropagation();
        }
      }
    },

    //
    _stopPropagation(ev) {
      if (ev) {
        ev.stopPropagation();
      }
    },

    //
    closeSelectModel() {
      var _this = this;
      _this.bShowSelectModel = false;
    },

    //
    showModelList(ev) {
      var _this = this;
      _this.bShowSelectModel = true;
    },

    // 拖动相关
    draggreet(val2) {
      var _this = this;
      _this.val2 = val2;
    },
    // //拖动相关
    _oncancel() {
      var _this = this;
      _this.$emit("oncancel");
    },
  },
  props: {
    title: {
      type: String,
      required: true,
    },
    loadingId: {
      type: String,
      required: true,
    },
    modelid: {
      type: String,
      required: true,
    },
    modelname: {
      type: String,
      required: true,
    },
    init_field1name: {
      type: String,
      required: false,
    },
    init_field1options: {
      type: Array,
      required: false
    }
  },
};
</script>
<style scoped>
._css-modelchosei-rmbtn {
  margin-left: 8px;
  margin-right: 8px;
  cursor: pointer;
}
._css-modelchosei-icon {
  margin-left: 16px;
}
._css-modelchosei-text {
  flex: 1;
  text-align: left;
  margin-left: 16px;
  text-overflow: ellipsis;
  overflow-x: hidden;
  white-space: nowrap;
}
._css-modelchose-list {
  box-sizing: border-box;
  padding-left: 24px;
  padding-right: 24px;
}
._css-modelchose-i {
  margin-top: 8px;
  height: 40px;
  display: flex;
  align-items: center;
  color: rgba(0, 0, 0, 0.65);
  background-color: #f7f7f7;
}
._css-input-options-i {
  margin-top: 5px;
  margin-bottom: 5px;
  margin-left: 6px;
  margin-right: 6px;
  height: 30px;
  line-height: 30px;
  text-align: left;
  box-sizing: border-box;
  padding-left: 12px;
  padding-right: 12px;
}
._css-input-options-i:hover {
  background-color: rgba(0, 0, 0, 0.04);
}
._css-input-options {
  position: absolute;
  width: 100%;
  top: calc(100% + 4px);
  min-height: 40px;
  max-height: 200px;
  border: 1px solid rgba(0, 0, 0, 0.3);
  border-radius: 4px;
  background-color: #fff;
}
._css-input-sel {
  cursor: pointer;
  position: relative;
}
._css-content {
  height: calc(100% - 64px - 54px);
  overflow-y: auto;
  padding-bottom: 24px;
  box-sizing: border-box;
}
._css-input {
  margin-top: 16px;
  display: flex;
  justify-content: space-around;
}
._css-input-in {
  height: 100%;
  width: 342px;
  height: 40px;
  border-radius: 4px;
  border: 1px solid rgba(0, 0, 0, 0.3);
  display: flex;
  align-items: center;
}
._css-input-in-icon {
  font-size: 14px;
  width: 14px;
  height: 14px;
  margin-right: 8px;
  margin-left: 8px;
}
._css-input-in-input {
  width: 100%;
  margin-left: 16px;
  margin-right: 16px;
  border: none;
  outline: none;
}
._css-modelsel-btn-icon {
  margin-left: 16px;
}
._css-modelsel-btn-text {
  margin-left: 16px;
}
._css-modelsel-btn {
  margin-top: 16px;
  display: flex;
  justify-content: space-around;
}
._css-modelsel-btn-in {
  height: 100%;
  width: 342px;
  height: 40px;
  border-radius: 4px;
  border: 1px solid #1890ff;
  color: #1890ff;
  display: flex;
  align-items: center;
  cursor: pointer;
}
._css-modelsel-title {
  display: none;
}

._css-modelsel-btn {
  display: none;
}

._css-title {
  margin-top: 15px;
  height: 19px;
  font-size: 14px;
  line-height: 14px;
  color: rgba(0, 0, 0, 0.65);
  padding-left: 24px;
  padding-right: 24px;
  box-sizing: border-box;
  text-align: left;
}

._css-begin-autorel-in {
  background-color: rgba(24, 144, 255, 1);
  border-radius: 4px;
  width: 326px;
  height: 42px;
  line-height: 42px;
  color: #fff;
  cursor: pointer;
  opacity: 0.7;
}
._css-begin-autorel-in:hover {
  opacity: 1;
}
._css-begin-autorel {
  display: flex;
  align-items: center;
  justify-content: space-around;
}
._css-autorel-all {
  z-index: 1001;
  width: 100%;
  height: 100%;
  position: fixed;
  top: 0;
  left: 0;
  background-color: rgba(0, 0, 0, 0.3);
  display: flex;
  align-items: center;
  justify-content: space-around;
}
._css-autorel-all-in {
  width: 390px;
  /* height: 480px; */
  height: 340px;
  position: absolute;
  background-color: #fff;
}
</style>