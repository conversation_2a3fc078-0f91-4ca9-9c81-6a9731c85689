<template>
  <div class="progress-list">
        <h2>
            <i class="icon-interface-problem-status" style="margin-right:8px;"></i>
            <span>进度计划</span>
            <i class="icon-interface-addnew" v-if="isAdd" @click="openDialog"></i>
        </h2>
        <el-tree :data="treeData"
            node-key="ID"
            :props="defaultProps"
            :expand-on-click-node=false
            :load="loadNode"
            :lazy=isLazy
            :highlight-current=true
            :default-expand-all=expandAll
            :ref="refData"
            @node-click="treeClick">
            <span class="custom-tree-node" slot-scope="{ node }">
                <span>{{ node.label }}</span>
                <!-- <span>{{ node.childNodes.length }}</span> -->
            </span>
        </el-tree>
        
  </div>
</template>

<script>
export default {
  components:{},
  props:{
    treeData:{
      type:Array,
      default:[]
    },
    isAdd:{
      type:<PERSON><PERSON><PERSON>,
      default:false
    },
    isLazy:{
      type:Boolean,
      default:true
    },
    expandAll:{
      type:Boolean,
      default:false
    },
    refData:{
      type:String,
      default:'tree'
    }
  },
  data(){
    return {
      defaultProps:{
          children: 'children',
          label: 'name',
          isLeaf:'leaf',

      },
    }
  },
  watch:{
  },
  computed:{},
  methods:{
      openDialog(){
        this.$emit('addDialogShow')
      },
      treeClick(data,node){
        let obj = {
          data,
          node
        }
        this.$emit('nodeClick',obj)
      },
      loadNode(node, resolve){
        this.$emit('loadNode',{node,resolve})
      }
  },
  created(){},
  mounted(){
  }
}
</script>
<style lang="stylus" scoped rel="stylesheet/stylus">
    .wzw-wp{
        display inline-block
        background red
        width 20px
        height 20px
    }
    .progress-list{
        padding-left 24px
        width 270px
        padding-right 56px
        height calc(100vh - 64px)
        background #f0f2f5
        h2{
            height 50px
            line-height 50px
            font-size 16px
            font-weight 500
            color rgba(0,0,0,.85)
            display flex
            justify-content space-between
            align-items center
            span{
                flex 1
                text-align left 
            }
            i{
              cursor pointer
            }
        }

    }  
</style>