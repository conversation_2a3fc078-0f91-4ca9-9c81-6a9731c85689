<template>
  <div class="scene-css">
    <iframe id="pure-scene-iframe" width="100%" height="100%" class="content-frame" src="" frameborder="0"></iframe>
  </div>
</template>

<script>
// 根据场景ID获取场景详情、加载场景
export default {
  name: "progressSceneLoading",
  data() {
    return {

    }
  },
  props:{
    // sceneID
    SceneId:{
      type:String,
      required: false,
      default: ''
    },
  },
 
  created(){
    this.getDetailItem()
  },
  mounted(){
    window.addEventListener('message', this.listeningMessage, false)
  },
  methods:{
    // 监听是否加载完毕
    listeningMessage(event){
      if(event.data == 'featureLoad'){
        this.loadScene(this.sceneItemData.SceneEventDataJson);
      }
    },
    // 获取场景详情
    async getDetailItem(){
      let _data = {
        projectID:this.$staticmethod._Get("organizeId"),
        SceneId: this.SceneId,
        userId: this.$staticmethod.Get("UserId"),
      }
      const res = await this.$api.getscenedetail(_data)
      this.sceneItemData = res.Data;
      const iframe = document.getElementById('pure-scene-iframe')
      const ifrSrc = this.getHttpUrl();
      iframe.src = ifrSrc + `?&projectId=${this.projectId}&lang=cn&edit=false`

    },
    loadScene(sceneEventDataJson){
      // console.log('=====场景加载进度ing')
      this.iframeWindow = document.getElementById('pure-scene-iframe').contentWindow
      this.iframeWindow.toggleSceneManageEditMode(false)
      window.scene = this.iframeWindow.scene
      // 还原场景 及 左侧场景管理树
      this.iframeWindow.saveSceneJson2Store(sceneEventDataJson)
      this.sceneload = true;
      // 移除postmessage事件监听
      window.removeEventListener('message', this.listeningMessage)
      // console.log('=====场景加载完毕')
      this.$emit('sceneLoadEnd')
    },
    getHttpUrl(){
      return window.bim_config.newModelApi;
    },
  },
  destroyed(){
    window.removeEventListener('message', this.listeningMessage)
  },

}
</script>

<style scoped>
.scene-css{
  width: 100%;
  height: 100%;
}
</style>