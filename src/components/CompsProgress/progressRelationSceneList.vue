<template>
	<div class="relation-list">
    <div class="scene-content">
      <div class="scene-title">
        <span class="title-text">选择场景</span>
        <span class="txtSearch">
          <i class="icon-interface-search cursor" @click.stop="SearchList($event, 1)"></i>
          <input
            type="text"
            v-model="scenesearchName"
            placeholder="搜索场景"
            @keyup="SearchList($event)"/>
        </span>
        <span class="cursor icon-suggested-close" @click="cancelScene"></span>
      </div>
      <div class="scene-list">
        <div class="list-child" 
          v-for="(item,index) in sceneList" 
          :key="item.SceneId" 
          @click="selectSceneList(item,index)"
          :class="{ active: selectedIndex === index }">
          <img :src="getItemImg(item)" alt="" />
          <span class="scene-name overflow-point">{{ item.SceneName }}</span>
        </div>
      </div>
      <div class="_css-addBtnCtn">
        <zbutton-function
          :init_text="'确定'"
          :init_fontsize="14"
          :debugmode="true"
          :init_height="'40px'"
          :init_width="'80px'"
          @onclick="openScene"
        >
        </zbutton-function>
        <zbutton-function
          :init_text="'取消'"
          :init_color="'rgba(0, 122, 255, 1)'"
          :init_bgcolor="'transparent'"
          :init_fontsize="14"
          :debugmode="true"
          :init_height="'40px'"
          :init_width="'80px'"
          @onclick="cancelScene"
        >
        </zbutton-function>
      </div>
    </div>
    
	</div>
</template>

<script>
// 进度模块使用的当前进度下相关联的场景
export default {
	name: "progressRelationSceneList",
	data() {
		return {
      selectedIndex: null,
      selectSceneItem: '',
      scenesearchName: '', // 搜索
      sceneList: [], // 当前进度关联模型、根据模型ID查找所包含的对应场景列表
    };
	},
  props: {
    bindingModelIDs: {
      type: String,
      default:''
    }
  },
  mounted(){
    this.getSceneList();
  },
	methods: {
		async getSceneList() {
			let _this = this;
			let _LoadingIns = _this.$loading({
				text: "",
				target: document.getElementsByClassName("scene-content")[0],
			});
			let sceneIncludeModelID = this.bindingModelIDs || '' // 接口获取到的当前进度所关联的模型ID
			// let sceneIncludeModelID = '' // 接口获取到的当前进度所关联的模型ID，后台接口还未处理好，先传值’‘
      console.log(sceneIncludeModelID,'====',this.bindingModelIDs)

        let params = {
          PageIndex: 1,
          PageSize: 99999,
          Key: this.scenesearchName,
          ProjectId: this.$staticmethod._Get("organizeId"),
          userId: this.$staticmethod.Get("UserId"),
          SceneIncludeModelID: sceneIncludeModelID,
        }
        const respage = await this.$api.getscenepaged(params)
        if(respage.Ret == 1){
          this.sceneList = respage.Data.Data;
        }
        _LoadingIns.close();
		},
    getItemImg(item) {
      if (item.SceneLogo === "") {
        return require("../../assets/images/products.png");
      } else {
        return item.SceneLogo;
      }
    },
    SearchList(e, jd) {
      if (e.keyCode == 13 || jd == 1) {
        this.getSceneList();
      }
    },
    selectSceneList(item,index){
      this.selectedIndex = index;
      this.selectSceneItem = item
    },
    openScene(){
      console.log(this.selectSceneItem,'=======选择的场景')
      if(!this.selectSceneItem){
        this.$message.warning('请选择要打开的场景')
        return
      }
      this.$emit('open',JSON.stringify(this.selectSceneItem))
    },
    cancelScene(){
      this.$emit('close')
    }
	},
};
</script>

<style lang="scss" scoped>
.relation-list{
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.03);
  z-index: 1111;
  .scene-content{
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    bottom: 0;
    width: 500px;
    height: 600px;
    margin: -300px auto 0;
    border-radius: 2px;
    backdrop-filter: blur(10px);
    background: rgba(255, 255, 255, 0.95);
    .scene-title{
      display: flex;
      align-items: center;
      margin: 24px 24px 24px 16px;
      .title-text{
        font-size: 16px;
        font-family: PingFangSC, PingFang SC;
        font-weight: 500;
        color: rgba(7,28,72,0.9);
      }
      .txtSearch {
        flex: 1;
        margin: 0 24px 0 150px;
        display: inline-block;
        position: relative;
        width: 200px;
        height: 36px;
        background-color: #ffffff;
        border: none;
        border-radius: 2px;
        border: 1px solid #D8D8D8;
        text-indent: 26px;
        font-size: 13px;
        outline: none;
      }
      .txtSearch i {
        position: absolute;
        top: 8px;
        left: 10px;
        width: 18px;
        height: 18px;
        text-align: left;
      }
      .txtSearch i::before {
        float: left;
        width: auto;
        height: auto;
        text-indent: 0px;
      }
      .txtSearch input {
        width: calc(100% - 36px);
        margin-left: 32px;
        float: left;
        height: calc(100% - 2px);
        border: none;
        outline: none;
        background-color: #ffffff;
      } 
      .txtSearch input::-webkit-input-placeholder {
        font-size: 13px;
        font-family: PingFangSC-Regular;
        line-height: 32px;
      }


    }
    .scene-list{
      margin: 0 24px;
      height: 440px;
      display: flex;
      flex-wrap: wrap;
      align-content: flex-start;
        margin-bottom: 24px;
      overflow-y: auto;
      .list-child{
        height: 128px;
        width: 33.33%; /* 每行三个，所以宽度为1/3 */
        box-sizing: border-box; /* 考虑边框和填充 */
        // padding: 10px 8px; /* 为每个项目添加一些填充 */
        margin-bottom: 24px;
        display: flex;
        flex-direction: column;
        cursor: pointer;
        img{
          border:2px solid transparent;
          width: 140px;
          height: 98px;
          border-radius: 4px;
        }
        .scene-name{
          margin-top: 10px;
        }
      }
      
      .list-child.active{
        img{
          border:2px solid #007AFF;
        }
      }
    }
    .cursor{
      cursor: pointer;
    }
    ._css-addBtnCtn{
      margin: 0 24px;
      display: flex;
      flex-direction: row-reverse;
    }
  }

}
</style>