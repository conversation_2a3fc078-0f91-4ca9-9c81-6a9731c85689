<template>
    
  <div class="auto-relation-wrapper">
      	<select-tree
					:init_zIndex="1003"
					:init_width="'500px'"
          :init_height="'640px'"
					init_title="请选择工程结构"
					:init_bimcomposerId="init_bimcomposerId"
          :init_organizeId="init_organizeId"
					@onok="getModelInfo"
					@onclose="closeTree"
					v-if="selectTreeShow"
				></select-tree>
        <dialogwp
        style="z-index:1001"
        width="390px"
        @close="closeAutoRel"
        title="自动关联模型">
            <div slot="btn"><div style="background:rgba(24,144,255,1);color:#fff;" @click="submit">开始关联</div></div>
            <div slot="main" class="auto-relation-main">
                <p>请选择工程结构</p>
                <div class="add" @click="openSelectView"><i class="icon-interface-addnew"></i> <span>工程结构</span></div>
                <ul class="chose-list" v-show="choseModelList.length>0">
                    <li v-for="(item,index) in choseModelList" :key="item.bmc_guid">
                        <i class="icon-interface-associated-component"></i>
                        <span>{{item.bmc_name}}</span>
                        <i class="icon-suggested-close_circle" @click="delModelList(index)"></i>
                    </li>
                </ul>
                <p>关联内容</p>
                <el-select v-model="elementpropertyval" placeholder="请选择" style="margin-top:15px;">
                    <el-option
                    v-for="item in options"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value">
                    </el-option>
                </el-select>
            </div>
        </dialogwp>
  </div>
</template>

<script>
import selectTree from './selectTree'
import dialogwp from '../CompsDialog/CompsDialogProgress'
export default {
  components:{dialogwp,selectTree},
  props:{
		init_bimcomposerId: {
				type: String,
				required: true
		},
		init_organizeId: {
				type: String,
				required: true
		},
	},
  data(){
    return {
        elementpropertyval:'',
        options:[
            {
            value: 'plusk_code',
            label: '编码'
            },
            {
            value: 'plusk_codename',
            label: '任务项'
            }

        ],
        selectTreeShow:false,
        choseModelList:[]
    }
  },
  watch:{},
  computed:{},
  methods:{
      delModelList(index){
          this.choseModelList.splice(index,1)
      },
      openSelectView(){
          this.selectTreeShow = true
      },
      close(){
          this.selectTreeShow = false
      },
      getModelInfo(info){
          console.log(info,'===info')
          this.choseModelList = [];   //  原逻辑为添加模型时候是多选(多个模型)，现将自动关联改为了只可关联一个模型
          this.choseModelList.push(info)
          this.selectTreeShow = false
      },
      closeAutoRel(){
          this.$emit("onclose");
      },
      submit(){
          console.log(this.choseModelList);
          console.log(this.elementpropertyval);
          this.$emit("onsubmit", this.choseModelList, this.elementpropertyval,);
      },
			closeTree(){
				this.selectTreeShow = false;
			}
  },
  created(){},
  mounted(){}
}
</script>
<style lang="stylus" scoped rel="stylesheet/stylus">
    .auto-relation-main{
        padding 0 24px
				margin-bottom: 20px
        p{
            text-align left 
            margin-top 15px
            font-size 14px
            color rgba(0,0,0,.65)
        }
        .chose-list{
            width 100%
            max-height 200px
            overflow auto
            li{
                background rgba(247,247,247,1) 
                height 40px
                line-height 40px
                border-radius 4px
                margin-top 8px
                display flex
                flex-direction row
                align-items center
                justify-content space-between
                padding 0 16px
                color rgba(0,0,0,.65)
                span{
                    margin-left 12px
                    flex 1
                    text-align left 
                    overflow hidden
                    text-overflow ellipsis
                    white-space nowrap
                }
                i:last-child{
                    cursor pointer
                }
            }
        }
        .add{
            cursor pointer
            height 40px
            line-height 40px
            border-radius 4px
            border 1px solid rgba(24,144,255,1)
            color #1890FF
            margin-top 15px
            display flex
            flex-direction row
            align-items center
            i {
                margin-left 16px
            }
            span{
                margin-left 12px
            }
        }

    }
</style>