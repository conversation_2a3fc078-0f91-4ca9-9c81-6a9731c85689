<template>
	<div class="relation-list">
    <div class="set-content">
      <div class="set-title">
        <span class="font-css css-fs16 f-w-500">模拟设置</span>
        <span class="cursor-pointer icon-suggested-close" @click="cancelSet"></span>
      </div>
      <div class="set-list">
        <div class="list-title margin-bottom-16 font-css css-fs14 f-w-500">模型渲染状态设置</div>
        <div class="list-content margin-bottom-16">
          <span class="css-mr24 font-css css-fs14 f-w-500">未开始</span>
          <div class="state-slider">
            <span>透明度</span>
            <div class="slider"><el-slider v-model="NostartOpacity" :formatTooltip="formatTooltip"></el-slider></div>
          </div>
        </div>
        <div class="list-content margin-bottom-16">
          <span class="css-mr24 font-css css-fs14 f-w-500">进行中</span>
          <div class="state">
            <p class="state-picker">
              <span>超前</span>
              <el-color-picker popper-class="progress-color-btn"  v-model="AdvancedColor" show-alpha size="mini"></el-color-picker>
            </p>
            <p class="state-picker">
              <span>正常</span>
              <el-color-picker popper-class="progress-color-btn"  v-model="NormalColor" show-alpha size="mini"></el-color-picker>
            </p>
            <p class="state-picker">
              <span>滞后</span>
              <el-color-picker popper-class="progress-color-btn"  v-model="LagColor" show-alpha size="mini"></el-color-picker>
            </p>
          </div>
           
        </div>
        <div class="list-content margin-bottom-24">
          <span class="css-mr24 font-css css-fs14 f-w-500">已完成</span>
          <div>
            原色
          </div>
        </div>
        <div class="list-title margin-bottom-16 font-css css-fs14 f-w-500">时间设置</div>
        <div class="list-time">
          <span>模拟时间</span>
          <span class="text-number">
            <el-input 
              class="time-input" 
              maxlength="4"
              onkeyup="this.value=this.value.replace(/[^\d.]/g,'');"
              v-model="PlayTime" 
              placeholder="模拟时间"></el-input><i>秒</i>
          </span>
        </div>
      </div>
      <div class="_css-addBtnCtn">
        <zbutton-function
          :init_text="'确定'"
          :init_fontsize="14"
          :debugmode="true"
          :init_height="'40px'"
          :init_width="'80px'"
          @onclick="openSet"
        >
        </zbutton-function>
        <zbutton-function
          :init_text="'取消'"
          :init_color="'rgba(0, 122, 255, 1)'"
          :init_bgcolor="'transparent'"
          :init_fontsize="14"
          :debugmode="true"
          :init_height="'40px'"
          :init_width="'80px'"
          @onclick="cancelSet"
        >
        </zbutton-function>
      </div>
    </div>
	</div>
</template>

<script>
// 进度模块使用的模拟设置
export default {
	name: "progressModelPlaySet",
	data() {
		return {
      NostartOpacity: 30,
      NormalColor:'rgba(22, 132, 252, 0.5)',
      AdvancedColor: 'rgba(6, 240, 68, 0.5)',
      LagColor: 'rgba(247, 29, 8, 0.5)',
      PlayTime: 0,
    };
	},
  props: {
    // 时间差
    timeDifference:{
      type: Number
    },
    SetSceneObj:{
      type: Object,
      default: {}
    },
    beginTime:{
      type: String,
    },
    endTime:{
      type: String,
    },
  },
  watch: { 
    SetSceneObj: {
        handler(val) {
            this.opinionInitData(val)
        },
        deep: true

    }
  },
  created(){
    this.opinionInitData(this.SetSceneObj);
  },
  mounted(){
    //  初始化进入页面、在接口获取当前页面的值、如果没值、radio是默认1，时间默认是两个时间差
    this.opinionInitData(this.SetSceneObj);
  },
	methods: {
    formatTooltip(val) {
      return val / 100;
    },
    opinionInitData(val){

      this.NostartOpacity = val.NostartOpacity ? val.NostartOpacity * 1 : 30;
      this.NormalColor = val.NormalColor && val.NormalColor.length > 0 ? val.NormalColor : 'rgba(22, 132, 252, 0.5)';
      this.AdvancedColor = val.AdvancedColor && val.AdvancedColor.length > 0 ? val.AdvancedColor : 'rgba(6, 240, 68, 0.5)';
      this.LagColor = val.LagColor && val.LagColor.length > 0 ? val.LagColor : 'rgba(247, 29, 8, 0.5)';
      this.PlayTime = val.PlayTime ? val.PlayTime * 1 : this.getDaysBetween(this.beginTime,this.endTime)
    },
    rgbaString(str) {
      // 将字符串转换为数组
      const arr = JSON.parse(str);
      // 使用解构赋值得到数组中的各个元素
      const [r, g, b, a] = arr;
      // 构建rgba字符串
      return `rgba(${r},${g},${b},${a})`;
    },
    cancelSet(){
      this.$emit('close')
    },
    openSet(){
      // 点击确定的时候要先计算时间，时间在0.3s-1s之间
      let totalTimeDiff = this.getDaysBetween(this.beginTime,this.endTime)  // 总天数
      // let totalTimeDiff = this.timeDifference; // this.timeDifference
      this.PlayTime = parseInt(this.getInFactSeconds(totalTimeDiff, this.PlayTime));
     
      let setobj = {
        NostartOpacity:this.NostartOpacity + '',
        NormalColor: this.NormalColor,
        AdvancedColor: this.AdvancedColor,
        LagColor: this.LagColor,
        PlayTime: this.PlayTime+'',
      }
      this.$emit('ok',setobj)
    },
    getDaysBetween(sTime,eTime){
        let startDate = Date.parse(sTime);
        let endDate = Date.parse(eTime);
        let days=(endDate - startDate)/(1*24*60*60*1000);
        return  days;
    },
    getInFactSeconds(totalTimeDiff, inputSeconds) {    
        let _this = this
        var inFactSeconds; // 实际总秒数
        var leastDelay = parseInt(0.3 * totalTimeDiff);
        var mostDelay = parseInt(1 * totalTimeDiff);
        if (inputSeconds && inputSeconds < leastDelay ) {
            _this.$message.warning(`当前进度模拟时间系统已进行最优计算，模拟时间为${leastDelay}秒`)
            inFactSeconds = leastDelay ;
        } else if (inputSeconds && inputSeconds > mostDelay ) {
            _this.$message.warning(`当前进度模拟时间系统已进行最优计算，模拟时间为${mostDelay}秒`)
            inFactSeconds = mostDelay
        } else {
            inputSeconds? inFactSeconds = inputSeconds : inFactSeconds = mostDelay 
        }
        return inFactSeconds;
    },
    stringToArray(color){
        const match = color.match(/[\d.]+/g);
        const r = match[0]*1;
        const g = match[1]*1;
        const b = match[2]*1;
        const a = match[3]*1;
        let arr = [r,g,b,a]
        return JSON.stringify(arr);
    },
  },
};
</script>

<style lang="scss" scoped>
.relation-list{
  position: relative;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.03);
  .set-content{
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    bottom: 0;
    width: 500px;
    height: 375px;
    margin: -187px auto 0;
    border-radius: 2px;
    backdrop-filter: blur(10px);
    background: rgba(255, 255, 255, 0.95);
    text-align: left;
    .set-title{
      display: flex;
      align-items: center;
      margin: 24px 24px 24px 16px;
      justify-content: space-between;
      
    }
    .set-list{
      margin: 0 12px 0 24px;
      .list-title::before{
        content: '';
        display: inline-block;
        width: 2px;
        height: 12px;
        background: #1890FF;
        margin-right: 4px;
      }
      .margin-bottom-16{
        margin-bottom: 16px;
      }
       .margin-bottom-24{
        margin-bottom: 24px;
      }
      .list-content{
        display: flex;
        align-items: baseline;
        .state{
          display: flex;
          flex: 1;
          align-items: center;
          .state-picker{
            span{
              padding-right: 6px;
            }
            margin-right: 24px;
          }
        }
        .state-slider{
            flex: 1;
          display: flex;
          align-items: center;
          .slider{
            width: 60%;
            margin: 0 20px;
          }
        }
      }
    }
  }
  /deep/ .el-radio{
    font-weight: 400;
    margin-right: 16px;
  }
  /deep/ .el-radio__inner:after{
    width: 0;
    height: 0;
  }
  /deep/ .state-picker .el-color-picker--mini{
    line-height: 30px;
  }
  /deep/ .el-radio__input.is-checked .el-radio__inner{
    border-color: rgba(152, 163, 179, 1);
  }
  
  .list-content /deep/ .el-input__inner{
    border-width: 1;
  }
  ._css-addBtnCtn{
    margin: 0 24px;
    display: flex;
    flex-direction: row-reverse;
  }
  .list-time{
    display: flex;
    align-items: center;
    .text-number{
      background: #FFFFFF;
      border-radius: 2px;
      margin-left: 10px;
      border: 1px solid #B8B8B8;
      .time-input{
        width: 100px;
        /deep/ .el-input__inner{
          line-height: 24px;
          padding: 0 8px;
        }
      }
      i{
        content: '';
        display: inline-block;
        width: 27px;
        height: 22px;
        background: rgba(184, 184, 184, 0.2);
        border-radius: 0px 1px 1px 0px;
        text-align: center;
      }
    }
  }
}
.f-w-400{
  font-weight: 400;
}
.f-w-500{
  font-weight: 500;
}
.font-css{
  font-family: PingFangSC, PingFang SC;
  color: rgba(7,28,72,0.9);
}
.state-picker /deep/ .el-color-picker--mini .el-color-picker__trigger{
  height: 14px;
  width: 14px;
  box-sizing: inherit;
}
.state-picker /deep/ .el-color-picker__trigger{
  border: none;
  padding: 0;
}
.state-picker /deep/ .el-color-picker__icon{
  color: transparent;
}
.slider /deep/ .el-slider__bar{
  height: 12px;
  border-radius: 8px;
}
.slider /deep/ .el-slider__button{
  width: 10px;
  height: 10px;
  border: none;
}
.slider /deep/ .el-slider__runway{
  height: 12px;
}
.slider /deep/ .el-slider__button-wrapper{
    top: -12px;
    -webkit-transform: translateX(-60%);
    transform: translateX(-60%);
}
._css-addBtnCtn{
    margin: 0 24px;
    display: flex;
    flex-direction: row-reverse;
  }
</style>
