<template>
    <div class="air-wampper">
        <div class="air-header">
            <div class="title-logo" :title="Visual_title">{{Visual_title}}</div>
            <div class="title-list">
                <el-menu :default-active="activeIndex" class="el-menu-demo" mode="horizontal" @select="handleSelect">
                    <el-menu-item index="0">项目概况</el-menu-item>
                    <el-menu-item index="1">工程看板</el-menu-item>
                    <el-menu-item index="2">形象进度</el-menu-item>
                    <el-menu-item index="3">工程现场</el-menu-item>
                    <el-menu-item index="4">工区全景</el-menu-item> 
                </el-menu>
            </div>
        </div>
        <div class="air-content">
            <ProjectOverview
                @title_logo = title_logo
                v-if="activeIndex==0">
            </ProjectOverview>
            <EngineeringBoard 
                v-if="activeIndex==1">
            </EngineeringBoard>
            <ImageProgress 
                v-if="activeIndex==2">
            </ImageProgress>
            <Productionsafety 
                v-if="activeIndex==3"
                :startDate = start_date>
            </Productionsafety>
            <panoramic 
                v-if="activeIndex==4">
            </panoramic>
        </div>

    </div>
</template>
<script>
import '@/assets/css/VisualPublic/public.css';
import ProjectOverview from './ProjectOverview'
import EngineeringBoard from './EngineeringBoard'
import ImageProgress from './ImageProgress'
import Productionsafety from './Productionsafety'
import panoramic from './panoramic'
export default {
    name: "VisualizationPanel",
    components: {
        ProjectOverview,
        EngineeringBoard,  
        ImageProgress,
        Productionsafety,
        panoramic,
    },
    data(){
        return{
            organizeId: '',
            Token: '',
            activeName: '0',
            activeIndex: '0',
            EngineeringImage:'',
            Visual_title:'',
            start_date:''
        }
    },
    mounted(){
      this.organizeId = sessionStorage.getItem('organizeId');
      this.Token = this.$staticmethod.Get("Token");
      this.GetNewER();
    },
    methods: {
      handleSelect(key) {
          this.activeIndex = key;
          this.activeName = key
      },
      //获取工程总报信息
      GetNewER(){
        this.$axios({
          method: "post",
          url: `${window.bim_config.webserverurl}/api/VisualView/VisualView/GetNewSumOutImage`,
          data:this.$qs.stringify({
            organizeId:this.organizeId
          })
        })
        .then(res => {
          let data = res.data.Data
          if(Object.keys(data).length!=0){
            this.start_date = data.start_date
          }
        })
        .catch(() => {
        });
      },
      title_logo(c){
        this.Visual_title = c
      }
    },
}
</script>
<style scoped>
.icon{
  display: inline-block;
  margin: 28px 20px 0;
  cursor: pointer;
}
.title-list{
  float: right;
  min-width: 1020px;
}
.air-header /deep/ .el-menu{
    background: transparent;
}
.air-header /deep/ .el-menu.el-menu--horizontal{
    border: none;
}
.air-header /deep/ .el-menu--horizontal > .el-menu-item.is-active{
    background-image: url("~@/assets/images/VisualPanel/nav_back2.png");
    color: #fff;
}
.air-header /deep/ .el-menu--horizontal > .el-menu-item{
    margin: 0;
    border-bottom: 2px solid transparent;
    color: rgba(255, 255, 255, 0.72);
    background-image: url("~@/assets/images/VisualPanel/nav_back1.png");
    background-repeat: no-repeat;
    margin: 15px 0px;
    width: 158px;
    line-height: 49px;

    
}
.air-header /deep/ .el-menu-item{
    font-size: 18px;
}
.air-header /deep/ .el-menu--horizontal > .el-menu-item:not(.is-disabled):hover, 
.air-header /deep/ .el-menu--horizontal > .el-menu-item:not(.is-disabled):focus {
    color: #fff;
    background-color: transparent;
    background-image: url("~@/assets/images/VisualPanel/nav_back2.png");
}
.air-content /deep/ .el-tabs__header{
    margin: 0;
    height: 0;
}
.air-content /deep/ .el-tabs__nav-wrap::after{
    content: "";
    height: 0;
}
#liquidfill-chart {
    width: 100%;
    height: 140px;
    margin-bottom: 10px;
  }
  
</style>
