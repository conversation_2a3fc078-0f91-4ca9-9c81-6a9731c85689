<template> 
  <div class="project-overview project-wampper bg071129">
    <div class="content-left">
      <div class="progress-left">
        <div class="progress_echart">
            <br>
            <div class="subtitle">工程总进度</div>
            <WaterBall
                class="overall_progressBall"
                :seriesData='totalprogress' 
                :borderWidth='14'
                :BallIndex='20'
                :bSize="160"
                >
            </WaterBall>
        </div>
        <div class="progress_other">
          <div class="work_area" v-if="tabBarChartsList.length === 0">
            <div class="nothing">暂无数据</div>
          </div>
          <div class="work_area" v-else>
            <div v-for='(item,index) in tabBarChartsList' :key='index'>
              <div v-if="changeIndex==index">
                <BarCharts class="BarCharts" :BarCharts='item'></BarCharts>
              </div> 
            </div>
            <ul class="tabBarCharts">
              <li v-for="(item,index) in tabBarChartsList"
                  :key="index"
                  @click="toggleTab(index)"
                  :class="[changeIndex==index?'active':'tabDot']">
              </li>
            </ul>
          </div>
          <div class="Lag_reason" v-if="Lag_reason.length !== 0">
              <div class="subtitle">其他</div>
              <div class="notice" v-for="(item,index) in Lag_reason" :key="item.Month_Id">
                <div class="notice_title">{{ index+1}}.{{ item.ER_cont }}</div>
                <div class="notice_cont">{{ item.ER_custom_input}}</div>
              </div>
          </div>
        </div>
      </div>
    </div>
    <div class="content-right progress-right-bim" v-if="imageModelVisible">
      <ProgressPlayActual
          :modelIframeSrc='modelIframeSrc'
          :getModelData="getModelData"
          :allModelID="allModelID"
          :getPlayingData="getPlayingData"
          :closeHover = false
          :defaultModel = defaultModel
          @changeModel = "changeModel"
      ></ProgressPlayActual>
    </div>
    <div class="content-right progress-right-bim" v-else-if="ActualModelVisible">
      <progressLoading 
          :modelIframeSrc='modelIframeSrc'
          :changemoreModelIds='changemoreModelIds'
          :getModelData="getModelData"
          :allModelID="allModelID"
          :closeHover = false
          :defaultModel = defaultModel
          @changeModel = "changeModel"
      >
      </progressLoading>
    </div>
    <div class="content-right" v-else-if="progressloading">
      <div class="Covery" :style="{'width':  getContainsLeft('timeline')}">
        <div class="nothing" style="color:#fff">加载中</div>
      </div>
    </div>
    <div class="content-right" v-else>
      <div class="nothing" style="color:#000">暂无数据</div>
    </div>
  </div> 
</template>

<script>
import WaterBall from '@/components/VisualizationPanelCommon/WaterBall'
import progressTab from '@/components/CompsVisualProgress/progressTab'
import vueSeamlessScroll from 'vue-seamless-scroll'
import progressLoading from '@/components/CompsVisualProgress/progressLoading'
import BarCharts from "@/components/VisualizationPanelCommon/BarCharts";
import ProgressPlayActual from "@/components/Home/ProjectBoot/ProgressPlayActual"
export default {
  name: "ImageProgress",
  components: {
    WaterBall,
    progressTab,
    progressLoading,
    vueSeamlessScroll,
    BarCharts,
    ProgressPlayActual
  },
  data() {
    return {
      organizeId: '',
      Token: '',
      countDay: '', // 倒计时天数
      lagList: ['空间和胡椒粉大苏打','空间和胡椒粉大苏打','空间和胡椒粉大苏打'], // 左侧--进度滞后原因
      finishList: [], // 左侧--完成情况
      arrayTwo: [],
      totalprogress:0,// 本月进度
      tabBarChartsList:[],//施工进度（月）详细数据
      changeIndex: 0,
      active: 0,
      Lag_reason:[],//进度滞后原因
      imageModelVisible:false,//实际模拟页面展示
      ActualModelVisible:false,//计划模拟页面展示
      getPlayingData: [], // 这个值为模拟时候的数据，是根据填报数据来的
      getModelData: null,  // 实际进度模拟请求接口返回的数据
      allModelID: [], // 多个模型的modelid 
      modelIframeSrc:{},
      changemoreModelIds:'',
      actualId:'',//实际模拟id
      planId:'',//计划模拟id
      progressloading:true,//加载页面展示
      defaultModel:'计划模拟'
    };
  },
  props: {
    
	},
  created() {
    this.getTreeData()
    this.getPlanData()
  },
  mounted() {
    this.getRelatedParameters();
    this.GetNewMonth();
    this.GetNewWeek();
  },
  watch:{
    planId(){
      this.$nextTick(()=>{
        this.getProjectPlanModel(this.planId)
      })
    }
  },
  methods: {
    getContainsLeft() {
      return (document.body.offsetWidth - 418)+'px'
    },
    changeModel(val){
      this.imageModelVisible=false
      this.ActualModelVisible=false
      this.progressloading = true
      if(val==='1'){
        this.defaultModel='计划模拟'
        this.getProjectPlanModel(this.planId)
      }
      if(val==='2'){
        this.defaultModel='实际模拟'
        this.getProjecActualIdtModel(this.actualId)
      }
    },
    toggleTab(index) {
        this.active = index
        this.changeIndex = index
    },
    //获取最新月进度
    GetNewMonth(){
      this.$axios({
        method: "post",
        url: `${window.bim_config.webserverurl}/api/VisualView/VisualView/GetNewMonth`,
        data:this.$qs.stringify({
          organizeId:this.organizeId
        })
      })
      .then(res => {
        let data = res.data.Data
        if(data.Month_totalprogress&&data.Month_totalprogress!==""){
          this.totalprogress = Number(data.Month_totalprogress)
        }else{
          this.totalprogress = 0
        }
        if(data.ERW_other.length!=0){
          this.Lag_reason = this.coppyArray(data.ERW_other);
        }
      })
      .catch(() => {
      });
    },
    //获取最新周数据
    GetNewWeek() {
      this.$axios({
        method: "post",
        url: `${window.bim_config.webserverurl}/api/VisualView/VisualView/GetNewWeek`,
        data:this.$qs.stringify({
          organizeId:this.organizeId
        })
      })
      .then(res => {
        let data = res.data.Data
        if(data.ERW_progress.length!=0){
          this.tabBarChartsList = this.coppyArray(data.ERW_progress);
        }
      })
      .catch(() => {
      });
    },
    coppyArray(arr){
      return arr.map((e)=>{
        if(typeof e==='object'){
            return Object.assign({},e);
        }else{
            return e;
        }
      }) 
    },
    getRelatedParameters() {
      this.organizeId = sessionStorage.getItem('organizeId');
      this.Token = this.$staticmethod.Get("Token");
    },
    // 根据进度计划的计划模拟
    getProjectPlanModel(_model_project){
      let _this = this;
      let _url = `${window.bim_config.webserverurl}/api/Plus/PlusTask/GetMaterialEleInfoByPlanId`;
      let _projectId = _this.$staticmethod._Get("organizeId");
      let _moreParam = {
          PlanId: _model_project,
          Token: this.$staticmethod.Get('Token'),
      }
      // 这个接口获取的值是点击当前进度使用的值
      _this.$axios({
          url: _url,
          method: 'post',
          data: _moreParam
      }).then(x => {
          if(x.data.Ret == 1){
            _this.getModelData = x.data.Data;
            _this.allModelID = x.data.Data.ModelIds;
            _this.modelIframeSrc = { projectID: _projectId,modelID:x.data.Data.ModelIds};  
            let str = '';
            for(var i=0;i<_this.allModelID.length-1;i++){
                str+=_this.allModelID[i]+"|";
            }
            _this.changemoreModelIds = str + _this.allModelID[_this.allModelID.length-1]
            _this.progressloading = false;
            _this.ActualModelVisible = true;
          }else{
              _this.progressloading = false;
              _this.ActualModelVisible = false;
              _this.$message.error(x.data.Msg);
          }
      }).catch(x => {
      })
    }, 
    // 根据进度方案的实际模拟
    getProjecActualIdtModel(_model_project){
      let _this = this;
      let _url = `${window.bim_config.webserverurl}/api/Plus/PlusTask/GetMaterialEleInfoByPlanId`;
      let _projectId = _this.$staticmethod._Get("organizeId");
      let _moreParam = {
          PlanId: _model_project,
          Token: this.$staticmethod.Get('Token'),
      }
      // 这个接口获取的值是点击当前进度使用的值
      _this.$axios({
          url: _url,
          method: 'post',
          data:_moreParam
      }).then(x => {
          if(x.data.Ret == 1){
            _this.getModelData = x.data.Data;
            _this.allModelID = x.data.Data.ModelIds;
            _this.modelIframeSrc = { projectID: _projectId,modelID:x.data.Data.ModelIds};  
            _this.$axios({
                url: `${window.bim_config.webserverurl}/api/Schedual/Schedual/GetProgressByPlanId`,
                method: 'post',
                data: _this.$qs.stringify(_moreParam)
            }).then(x => {
              if(x.data.Ret == 1){
                if(x.data.Data.List.length == 0){
                    _this.$message.warning('无数据')
                    _this.imageModelVisible = false;
                    _this.progressloading = false;
                    return
                }
                _this.getPlayingData = x.data.Data;
                _this.imageModelVisible = true;
                _this.progressloading = false;
              }else{
                _this.$message.error(x.data.Msg);
              } 
            }).catch(x => {
            })
          }else{
              _this.$message.error(x.data.Msg);
          }
      }).catch(x => {
      })
    }, 
    //进度方案获取
    getTreeData(){
      let _this = this;
      let _OrganizeId = this.$staticmethod._Get("organizeId")  
      let _url = `${this.$MgrBaseUrl.planGetTree}?organizeId=${_OrganizeId}`;
      _this.$axios.get(_url)
      .then((x) => {
          if (x.data.Ret > 0) {
            _this.actualId = x.data.Data[0].UID
          } else {
              _this.$message.error(x.data.Msg);
          }
      })
      .catch((x) => {
          console.error(x);
      });
    },
    //进度计划获取
    getPlanData(){
      let _this = this;
      let _OrganizeId = this.$staticmethod._Get("organizeId")  
      let _url = `${window.bim_config.webserverurl}/api/Plus/PlusProject/NewComm_GetListByOrganizeId?organizeId=${_OrganizeId}`;
      _this.$axios.get(_url)
      .then((x) => {
          if (x.data.Ret > 0) {
            this.planId = x.data.Data[0].bop_planId
          } else {
              _this.$message.error(x.data.Msg);
          }
      })
      .catch((x) => {
          console.error(x);
      });
    },
  },

};
</script>

<style lang="scss" scoped>
.content-left{
  // background: rgba(7, 17, 41, 0.85); 
}
.content-right{
  flex:1;
  margin: 20px 0 0 0;
  background: #f0f2f5;
}
.content-left{
  overflow-x: auto !important;
}
.progress-left{
  .progress_echart{
    margin: 20px 8px 0px 0px;
    width: 410px;
    height: 250px;
    cursor: pointer;
    background: rgba(7, 28, 72, 0.9);
      .overall_progressBall{
        margin: 20px auto;
      }
  }
  .progress_other{
    margin: 10px 8px 0px 0px;
    width: 410px;
    cursor: pointer;
    background: rgba(7, 28, 72, 0.9);
    overflow-x: auto !important;
    .work_area{
      margin: 20px;
      .BarCharts{
        padding-bottom: 1rem;
      }
      .tabBarCharts{
        margin: 0 auto;
        height: 1rem;
        line-height: 1rem;
        display: flex;
        align-items: center;
        justify-content: center;
        .active{
          background: #0C6CF2;
          float: left;
          margin: 0 5px;
          width: 10px;
          height: 10px;
          border-radius: 50%;
          cursor: pointer;
        }
        .tabDot{
          float: left;
          margin: 0 5px;
          width: 10px;
          height: 10px;
          background: rgba(12, 108, 242, 0.6);
          border-radius: 50%;
          cursor: pointer;
        }
        .tabDot:hover{
          background: #0C6CF2;
        }
      }
    }
    .Lag_reason{
      margin: 30px 0;
      .notice{
        margin: 15px 20px;
        text-align: left;
        font-weight: 400;
        color: #FFFFFF;
        line-height: 24px;
        .notice_title{
          font-size: 14px;
          font-weight: bold;
        }
        .notice_cont{
          text-indent: 15px;
          font-size: 12px;
        }
      }
    }
  }
  .progress-lag{
    padding-bottom: 20px;
    .seamless-warp{
      height: 190px;
      overflow: hidden;
      .font-width{
        display: inline-block;
        width: 40px;
      }
    }
    ul{
      margin: 0 0px 0px 30px;
      padding: 20px 0 0 0;
      max-height: 130px;
      overflow-y: auto;
      text-align: left;
      li{
        line-height: 20px;
        padding-top: 8px;
        .list-num{
          display: inline-block;
        }
      }
    }
  }
  .project-finish{
    .lag-list-title{
      margin: 20px 10px 0 20px;
      text-align: left;
    }
    ul{
      margin: 0 10px 0 20px;
      padding-bottom:40px;
      max-height: 170px;
      overflow-x: auto;
      text-align: left;
      li{
        span{
          padding-right: 4px;
        }
      }
    }
    .finish-loading{
      width: 80px;
      display: inline-block;
      /deep/ .el-progress-bar .el-progress-bar__outer{
        background-color: #1b283d !important;
      }
      /deep/ .el-progress-bar__outer,/deep/ .el-progress-bar .el-progress-bar__inner{
        border-radius: 0 !important;
      }
      
    }
    .finish-loading-actual{
      display: inline-block;
      position: absolute;
      top: 4px;
      right: 0;
      z-index: 9;
      /deep/ .el-progress-bar .el-progress-bar__outer{
        background-color: transparent !important;
      }
    }
    .finish-loading-lastactual{
      position: absolute;
      top: 4px;
      right: 0;
      z-index: 8;
      width: 80px;
      
    }
  }
}
.subtitle{
  background-image: url("~@/assets/images/VisualPanel/subtitle.png");
  background-size: 100% 100%;
  width: 140px;
  height: 22px;
  font-size: 16px;
  color: #fff;
  font-weight: bold;
  line-height: 15px;
  text-align: left;
  text-indent: 16px;
  margin: 5px 0 0 20px;
}
.nothing{
  width: 100%;
  height: 100px;
  font-size: 18px;
  color: #fff;
  text-align: center;
  line-height: 100px;
}
.Covery{
  width:100%;
  height:100%;
  background-color:rgba(0,0,0,0.1);
  position: fixed;
  top:100px;
  left: 418px;
  z-index: 11;
}
</style>