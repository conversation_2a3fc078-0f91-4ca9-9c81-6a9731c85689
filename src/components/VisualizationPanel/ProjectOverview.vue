<template>
  <div class="project-wampper" :style="{'backgroundImage':'url('+EngineeringImage+')'}" >
    <div class="projectoverview-left">
      <div class="survey">
          <div class="title">工程概况</div>
          <ul>
            <li class="cont">
              <div class="subtitle">项目位置</div>
              <div class="subcont" :title="projectInformation.Address">{{projectInformation.Address}}</div>
              <div class="line"></div>
            </li>
            <li class="cont">
              <div class="subtitle">项目简介</div>
              <div class="subcont1" :title="projectInformation.Description">{{projectInformation.Description}}</div>
              <div class="line"></div>
            </li>
            <li class="cont">
              <div class="subtitle">工程开工时间</div>
              <div class="time" v-if="start_date.length===3">
                <div class="back" style="width:111px">
                  <div class="left">{{start_date[0]}}</div>
                  <span>年</span>
                </div>
                <div class="back">
                  <div class="left">{{start_date[1]}}</div>
                  <span>月</span>
                </div>
                <div class="back">
                  <div class="left">{{start_date[2]}}</div>
                  <span>日</span>
                </div>
              </div>
              <div class="time" v-else>
                  未设置
              </div>
            </li>
            <li class="cont">
              <div class="subtitle">目标完工时间</div>
              <div class="time" v-if="Target_completion_date.length===3">
                <div class="back" style="width:111px">
                  <div class="left">{{Target_completion_date[0]}}</div>
                  <span>年</span>
                </div>
                <div class="back">
                  <div class="left">{{Target_completion_date[1]}}</div>
                  <span>月</span>
                </div>
                <div class="back">
                  <div class="left">{{Target_completion_date[2]}}</div>
                  <span>日</span>
                </div>
              </div>
              <div class="time" v-else>
                  未设置
              </div>
            </li>
          </ul>
      </div>
      <div class="plan">
          <div class="title">投资计划</div>
          <div class="cont" v-for="(item,index) in ER_custom_item" :key="index">
            <div class="cont_item">
              <div class="left">{{item.ER_custom_item1.ER_cont}}</div>
              <div class="right">{{item.ER_custom_item1.ER_custom_input}}</div>
            </div>
            <div class="cont_item">
              <div class="left">{{item.ER_custom_item2.ER_cont}}</div>
              <div class="right2">{{item.ER_custom_item2.ER_custom_input}}</div>
            </div>
            <el-progress :percentage="percentage(item.ER_custom_item1.ER_custom_input,item.ER_custom_item2.ER_custom_input)" :stroke-width="14" color="#FF720D" :text-inside="true"></el-progress>            
          </div>
      </div>
    </div>
    <div class="projectoverview-right" v-if="publicAnnouncement.length!==0">
      <div class="title">项目公告</div>
      <div class="notice" v-for="(item,index) in publicAnnouncement" :key="item.Bpn_guid">
        <div class="notice_title">{{ index+1}}.{{ item.Bpn_title }}</div>
        <div class="notice_cont">{{ item.Bpn_content}}</div>
        <div class="notice_time">{{item.Bpn_extjson.timestrshort }}</div> 
      </div>
    </div>
    <div class="projectoverview-right" v-else>
      <div class="title">项目公告</div>
      <div class="notice">
        <div class="notice_title">暂无公告</div>
      </div>
    </div>
    <div class="defaultModel" v-if="cockpit_radio=='2' ">
      <div 
        element-loading-text="模型加载中..."
        element-loading-spinner="el-icon-loading"
        element-loading-background="rgba(0, 0, 0, 0.2)"
        v-loading="modelLoading" 
        class="progress-model"></div>
    </div>
  </div>
</template>

<script>
export default {
  name: "ProjectOverview",
  data() {
    return {
      organizeId: '',
      Token: '',
      publicAnnouncement: [],//公告
      projectInformation:{},//项目简介
      start_date:[],//工程开工时间：
      Target_completion_date:[], //目标完成时间：
      ER_custom_item:[],//投资成本
      modelLoading: false,
      iframeWindow: null,  // 模型对象
      EngineeringImage:'',
      cockpit_radio:''
    };
  },
  props: {},
  methods:{
    //获取公告
    getPublicAnnouncement() {
      this.$axios.get(`${this.$configjson.webserverurl}/api/User/Project/IGetProjectNews`,{
        params: {
          OrganizeId:this.organizeId
        }
      }).then(res=>{
        if (res.data.Ret > 0) {
          let storage = res.data.Data;
          this.publicAnnouncement = this.descSortByTime(storage);//storage;
        }
      });
    },
    descSortByTime(storage) {
      for (var i = 0; i < storage.length; i++) {
        storage[i].Bpn_extjson = JSON.parse(storage[i].Bpn_extjson);
        storage[i].DT = new Date(storage[i].Bpn_extjson.timestr);
      }
      // 逆序排序
      storage = storage.sort((x,y) => y.DT - x.DT);
      return storage;
    },
    //获取项目信息
    getProjectInformation() {
      this.$axios.get(`${this.$configjson.webserverurl}/api/User/Project/GetProjectDetail`,{
        params: {
          organizeId: this.organizeId,
          Token: this.Token,
        }
      }).then(res => {
        if (res.data.Ret > 0) {
          this.projectInformation = res.data.Data;
          this.$emit('title_logo',res.data.Data.FullName)
        }
      }).catch(() => {
      });
    },
    getRelatedParameters() {
      this.organizeId = sessionStorage.getItem('organizeId');
      this.Token = this.$staticmethod.Get("Token");
    },
    //获取工程总报信息
    GetNewER(){
      this.$axios({
        method: "post",
        url: `${window.bim_config.webserverurl}/api/VisualView/VisualView/GetNewSum`,
        data:this.$qs.stringify({
          organizeId:this.organizeId
        })
      })
      .then(res => {
        let data = res.data.Data
        if(Object.keys(data).length!=0){
          this.start_date = data.start_date.split('-');
          this.Target_completion_date = data.Target_completion_date.split('-');
          if(data.cockpit_radio=="1"){
              this.EngineeringImage = data.EngineeringImage
          }
          let obj = {
            ER_custom_item1 : data.ER_custom_item.filter((i,d)=>d%2 ===0),
            ER_custom_item2 : data.ER_custom_item.filter((i,d)=>d%2 ===1)
          }
          let newArr = []
          obj.ER_custom_item1.map((o,i)=>newArr.push({
            ER_custom_item1:o,
            ER_custom_item2:obj.ER_custom_item2[i]
          }))
          this.ER_custom_item = newArr
          this.cockpit_radio = data.cockpit_radio
          if(data.cockpit_radio=="2"){
              this.getDefaultModel()
          }
        }
      })
      .catch(() => {
      });
    },
    //计算百分比
    percentage(a,b){
      return Number(((Number(b)/Number(a))*100).toFixed(2))
    },
    //获取默认模型
    getDefaultModel(){
      var _bimcomposerId = sessionStorage.getItem("bimcomposerId");
      var _this = this;
      var _Url = `${
      _this.$staticmethod.getBIMServer()
      }/api/prj/GetPhaseAndCount?ProjectID=${_bimcomposerId}`;
      _this.$axios.get(_Url).then(x => {
        if (x.data && x.data.length) {
          let m_phasecnt = [];
          for (var i = 0; i < x.data.length; i++) {
           m_phasecnt.push(x.data[i].Phase)
          }
          let Phases = m_phasecnt.join()
          this.$axios({
                url: `${this.$staticmethod.getBIMServer()}/api/Prj/GetSpecificPhaseModels`,
                method:'post',
                data: this.$qs.stringify({
                    ProjectID: _bimcomposerId,
                    thumbnail: 1,
                    Phases
                })
              }).then(res =>{
              let data = JSON.parse(res.data)
              let IsDefault = data.filter(item=>item.IsDefault == '1')
              if(IsDefault.length>0){
                this.modelLoading = true;
                this.modelloadingfun(IsDefault[0].ID,IsDefault[0].ProjectID)
              }else{
                this.$message.warning('未获取到默认模型，请检查是否设置')
              }
            }).catch(x=>{
                this.$message.warning(x.data.msg)
            })
        }else{
          this.$message.warning('未获取到模型列表')
        }
      });
    },
    //打开模型
    modelloadingfun(modelID,projectID){ 
        let obj = {
            modelID: modelID,//模型id
            projectID: projectID,//项目id
            versionNO: '',//版本 ""为最新版本
            viewID: '',//指定视图id，""为默认视图  model-more
            DOM: document.querySelector('.defaultModel')//渲染容器（请提前确定容器含有宽高）
        }

        model = window.model = new window.bim365.BIMModel(obj)
        model.load()
        //model.load 之后实例化BIM365API 请在load后获取BIM365API
        this.iframeWindow = window
        model.BIM365API.Events.finishRender.on('default',()=>{//监听模型加载完成事件
            console.log('模型加载完成','======')
            this.modelLoading = false;
            // this.isProgress = false;
            this.iframeWindow.model.BIM365API.Context.onCanvasResize()

            // this.iframeWindow.model.BIM365API.Controller.isolateElementByElementId();

        })
    },
  },
  created(){
    this.getRelatedParameters();
    this.getPublicAnnouncement();
    this.getProjectInformation();
    this.GetNewER();
  },
};
</script>

<style lang="scss" scoped>
::-webkit-scrollbar {
    width: 0;
    height: 0;
}
.project-wampper{
  position: relative;
  background-size: 100% 100%;
}
.projectoverview-left{
    position: absolute;
    top:15px;
    left: 20px;
    overflow-x: auto;
    height: 95%;
    z-index:11;
  .survey{
    cursor: pointer;
    width: 450px;
    background: rgba(7, 28, 72, 0.9);
    margin-bottom: 20px;
    padding-bottom: 10px;
    .title{
      background-image: url("~@/assets/images/VisualPanel/back_title.png");
      background-size: 100% 100%;
      text-align: center;
      height: 40px;
      line-height: 40px;
      font-size: 20px;
      font-weight: 500;
    }
    ul{
      margin-left: 20px;
      .cont{
        .subtitle{
          background-image: url("~@/assets/images/VisualPanel/subtitle.png");
          background-size: 100% 100%;
          text-align: center;
          width: 140px;
          height: 22px;
          font-size: 16px;
          font-weight: 500;
          color: #FFFFFF;
          line-height: 15px;
          margin-top: 20px;
        }
        .subcont{
          font-size: 14px;
          text-align: left;
          line-height: 50px;
          width: 388px;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }
        .subcont1{
              font-size: 14px;
              text-align: left;
              line-height: 25px;
              width: 388px;
              overflow: hidden;
              text-overflow: ellipsis;
              -webkit-line-clamp: 5;
              -webkit-box-orient: vertical;
              display: -webkit-box;
              margin: 10px 0;
        }
        .time{
          display: flex;
          .back{
            width: 77px;
            height: 46px;
            background: #0C6CF2;
            border-radius: 3px;
            float: left;
            margin: 10px 10px 10px 0;
            .left{
              font-size: 32px;
              font-weight: bold;
              color: #FFFFFF;
              line-height: 46px;
              letter-spacing: 1px;
              margin-left: 10px;
              float: left;
            }
            span{
              width: 15px;
              height: 20px;
              font-size: 14px;
              font-weight: 600;
              color: #FFD500;
              line-height: 20px;
            }
          }
        }
      }
    }
  }
  .line{
    width: 450px;
    height: 2px;
    background: linear-gradient(45deg, rgba(26, 141, 244, 0) 0%, #0E5EE8 49%, rgba(11, 84, 230, 0) 100%);
  }
  .plan{
    cursor: pointer;
    width: 450px;
    min-height: 300px;
    background: rgba(7, 28, 72, 0.9);
    padding-bottom: 10px;
    z-index:11;
    .title{
      background-image: url("~@/assets/images/VisualPanel/back_title.png");
      background-size: 100% 100%;
      text-align: center;
      height: 40px;
      line-height: 40px;
      font-size: 20px;
      font-weight: 500;
    }
    .cont{
      margin: 10px 20px;
      .cont_item{
        height: 50px;
        line-height: 50px;
        .left{
          float: left;
          font-size: 16px;
          color: #C8C8C8;
        }
        .right{
          float: right;
          font-size: 32px;
          font-weight: bold;
          color: #FFD500;
        }
        .right2{
          float: right;
          font-size: 24px;
          font-weight: bold;
          color: #fff;
        }
      }
    }
  }
}
.projectoverview-right{
  position: absolute;
  bottom:24px;
  right: 24px;
  width: 370px;
  height: 300px;
  overflow-x: auto;
  cursor: pointer;
  z-index:11;
  background: rgba(7, 28, 72, 0.9);
  .title{
    background-image: url("~@/assets/images/VisualPanel/back_title1.png");
    background-size: 100% 100%;
    text-align: center;
    height: 40px;
    line-height: 40px;
    font-size: 20px;
    font-weight: 500;
  }
  .notice{
    margin: 15px 20px;
    text-align: left;
    font-weight: 400;
    color: #FFFFFF;
    line-height: 24px;
    .notice_title{
      font-size: 16px;
      font-weight: bold;
    }
    .notice_cont{
      font-size: 14px;
    }
    .notice_time{
      font-size: 12px;
      color: #C8C8C8;
    }
  }
}
.defaultModel{
  width: 100%;
  height: 100%;
  .progress-model{
    width: 100%; 
    height: 100%;
  }
}
</style>