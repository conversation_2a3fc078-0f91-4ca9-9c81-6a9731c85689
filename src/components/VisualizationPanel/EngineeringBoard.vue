<template>
  <div class="project-wampper air-data-board">
    <el-row :gutter="24" class="row-box" style="margin:0">
      <el-col :span="6" class="row-col-span" style="padding: 0 6px;">
        <div class="col-box">
          <div class="col_box_title">
            <span class="col_box_name">
              <div class="position_title">投资成本</div>
            </span>
          </div>
          <div class="col_content-text" v-if="ER_custom_item!==[]">
            <div v-for="(item,index) in ER_custom_item" :key="index">
              <div class="total">
                <div class="total_title">{{item.ER_custom_item1.ER_cont}}</div>
                <div class="total_num">{{item.ER_custom_item1.ER_custom_input}}</div>
              </div>
              <div class="total">
                <div class="total_title">{{item.ER_custom_item2.ER_cont}}</div>
                <div class="total_num" style="background:#FF720D;color:#fff">{{item.ER_custom_item2.ER_custom_input}}</div>
              </div>
              <div class="total" v-if="index === 0">
                <div class="total_title">投资完成率（%）</div>
                <el-progress type="circle" :percentage="percentage(item.ER_custom_item1.ER_custom_input,item.ER_custom_item2.ER_custom_input)" :stroke-width="14" color="#0DD7FF"></el-progress>
              </div>
            </div>
          </div>
          <div class="col_content-text" v-else>
              <div class="getDataFalse">
                  未获取到工程填报数据
              </div>
          </div>
        </div>
      </el-col>
      <el-col :span="6" class="row-col-span" style="padding: 0 6px;">
        <div class="col-box">
          <div class="col_box_title">
            <span class="col_box_name">
              <div class="position_title">施工进度</div>
            </span>
          </div>
          <div class="col_content-text">
            <div class="progress">
              <div class="progress_header">
                <div class="progress_header_left">施工时间</div>
                <div class="progress_header_right">
                  <el-date-picker
                    v-model="choose_month"
                    type="month"
                    :clearable = false
                    placeholder="选择月"
                    :format="'yyyy-MM'"
                    value-format="yyyy-MM"
                    prefix-icon="prefix-icon-class">
                  </el-date-picker>
                  <div class="tab_month">
                    <div class="tab_month_last" @click="tab_month_choose('last')"></div>
                    <div class="tab_month_next" @click="tab_month_choose('next')"></div>
                  </div>
                </div>
              </div>
              <div class="overall_progress">
                <div class="subtitle">工程总进度</div>
                <WaterBall
                    class="overall_progressBall"
                    :seriesData='Month_totalprogress' 
                    :borderWidth='14'
                    :BallIndex='20'
                    :bSize="200"
                    >
                </WaterBall>
              </div>
              <div class="work_area">
                <div v-for='(item,index) in tabBarChartsList' :key='index'>
                  <div v-if="changeIndex==index">
                    <BarCharts class="BarCharts" :BarCharts='item'></BarCharts>
                  </div> 
                </div>
                <ul class="tabBarCharts">
                  <li v-for="(item,index) in tabBarChartsList"
                      :key="index"
                      @click="toggleTab(index)"
                      :class="[changeIndex==index?'active':'tabDot']">
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </el-col>
      <el-col :span="6" class="row-col-span" style="padding: 0 6px;">
        <div class="col-box">
          <div class="col_box_title">
            <span class="col_box_name">
              <div class="position_title">工程质量</div>
            </span>
          </div>
          <div class="col_content-text">
            <div class="quality">
              <div class="quality_week">
                <div class="subtitle">本周情况</div>
                <div class="quality_week_title">本周发现缺陷</div>
                <div class="sec-content">
                  <div class="chart-sec">
                    <PieCharts :PieChartsData='PieChartsWeek'></PieCharts>
                  </div>
                  <div class="chart_right">
                    <div class="resolved">
                      <div class="resolvedDot unresolvedDot"></div>
                      <div class="resolvedCont">未解决</div>
                      <div class="resolvedNum">{{weekUnResolved}}</div>
                    </div>
                    <div class="resolved">
                      <div class="resolvedDot"></div>
                      <div class="resolvedCont">已解决</div>
                      <div class="resolvedNum">{{weekResolved}}</div>
                    </div>
                  </div>
                </div>
                <div class="quality_other" v-for="(item,index) in qualityWeekCustom" :key="index">
                  <div class="quality_other_title">{{item.ER_cont}}</div>
                  <div class="quality_other_num">{{item.ER_custom_input}}</div>
                </div>
              </div>
              <div class="quality_week quality_month">
                <div class="subtitle">本月情况</div>
                <div class="sec-content" style="marginTop: 1rem;">
                  <div class="chart-sec">
                    <CircleCharts :circleChartsData='circleChartsData'></CircleCharts>
                  </div>
                  <div class="chart_right" style="marginTop: .5rem;" v-for="(item,index) in circleChartsData" :key="index">
                    <div class="resolved">
                      <div class="resolvedCont">{{item.ER_cont}}</div>
                      <div class="resolvedNum">{{item.ER_custom_input}}</div>
                    </div>
                    <!-- <div class="resolved">
                      <div class="resolvedCont">{{item.ER_cont}}</div>
                      <div class="resolvedNum">{{item.ER_custom_input}}</div>
                    </div> -->
                  </div>
                </div>
                <div class="quality_week_title">本月发现缺陷</div>
                <div class="sec-content">
                  <div class="chart-sec">
                    <PieCharts :PieChartsData='PieChartsMonth'></PieCharts>
                  </div>
                  <div class="chart_right">
                    <div class="resolved">
                      <div class="resolvedDot unresolvedDot"></div>
                      <div class="resolvedCont">未解决</div>
                      <div class="resolvedNum">{{MonthUnResolved}}</div>
                    </div>
                    <div class="resolved">
                      <div class="resolvedDot"></div>
                      <div class="resolvedCont">已解决</div>
                      <div class="resolvedNum">{{MonthResolved}}</div>
                    </div>
                  </div>
                </div>
                <div class="quality_other" v-for="(item,index) in qualityMonthCustom" :key="index">
                  <div class="quality_other_title">{{item.ER_cont}}</div>
                  <div class="quality_other_num">{{item.ER_custom_input}}</div>
                </div>
              </div>
            </div>
            <!-- <PieCharts :PieChartsData='PieChartsData'></PieCharts> -->
          </div>
        </div>
      </el-col>
      <el-col :span="6" class="row-col-span" style="padding: 0 6px;">
        <div class="col-box">
          <div class="col_box_title">
            <span class="col_box_name">
              <div class="position_title">安全文明</div>
            </span>
          </div>
          <div class="col_content-text">
            <div class="safetyAll">
              <div v-for="(item) in Safty" :key="item.ERW_SaftyHeader">
                <div class="subtitle">{{item.ERW_SaftyHeader}}</div>
                <el-row :gutter="20">
                  <el-col :span="6" v-for="(i,d) in item.ERW_SaftySingle" :key="d">
                    <div class="grid-content bg-purple">
                      <div :class="[i.ER_custom_input=='0'?'safetyNum0':'safetyNum1']" :title="i.ER_custom_input">{{i.ER_custom_input}}</div>
                      <div class="safetyDesc" :title="i.ER_cont">{{i.ER_cont}}</div>
                    </div>
                  </el-col>
                </el-row>
              </div>
              <div class="Green_construction" v-for="(item) in Green_construction" :key="item.ERW_SaftyHeader">
                <div class="subtitle">{{item.ERW_SaftyHeader}}</div>
                <el-row :gutter="20">
                  <el-col :span="8" v-for="(i,d) in item.ERW_SaftySingle" :key="d">
                    <div class="grid-content bg-purple">
                      <div class="safetyNum0" style="color:#0BD9D9" :title="i.ER_custom_input">{{i.ER_custom_input}}</div>
                      <div class="safetyDesc" :title="i.ER_cont">{{i.ER_cont}}</div>
                    </div>
                  </el-col>
                </el-row>
              </div>
              <div class="Green_construction" v-for="(item) in Epidemic_prevent" :key="item.ERW_SaftyHeader">
                <div class="subtitle">{{item.ERW_SaftyHeader}}</div>
                <div class="safety_cont" v-for="(i,d) in item.ERW_SaftySingle" :key="d">
                  <div class="safety_cont_left" :title="i.ER_cont">{{i.ER_cont}}</div>
                  <div class="safety_cont_right" :title="i.ER_custom_input">{{i.ER_custom_input}}</div>
                </div>
              </div>
              <template v-if="Safty_custom.length!==0">
                <div class="Green_construction" v-for="(item) in Safty_custom" :key="item.ERW_SaftyHeader">
                  <div class="subtitle">{{item.ERW_SaftyHeader}}</div>
                  <div class="safety_cont" v-for="(i,d) in item.ERW_SaftySingle" :key="d">
                    <div class="safety_cont_left" :title="i.ER_cont">{{i.ER_cont}}</div>
                    <div class="safety_cont_right" :title="i.ER_custom_input">{{i.ER_custom_input}}</div>
                  </div>
                </div>
              </template>
            </div>
          </div>
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import WaterBall from '@/components/VisualizationPanelCommon/WaterBall'
import InvestSection from "@/components/CompsVisualAirDataBoard/InvestSection";
import ProgressSection from "@/components/CompsVisualAirDataBoard/ProgressSection";
import SafeCulture from "@/components/CompsVisualAirDataBoard/SafeCulture";
import CircleCharts from "@/components/VisualizationPanelCommon/CircleCharts";
import PieCharts from "@/components/VisualizationPanelCommon/PieCharts";
import BarCharts from "@/components/VisualizationPanelCommon/BarCharts";
export default {
  name: "AirDataBoard",
  components: {
    WaterBall,
    InvestSection,
    ProgressSection,
    SafeCulture,
    CircleCharts,
    PieCharts,
    BarCharts,
  },
  data() {
    return {
      organizeId: '',
      Token: '',
      choose_month:'',//获取时间月份
      ERD_week:'',//获取时间周
      countDownTime: "",
      secData: [], // 投资成本相关值
      circleChartsData: {}, // 工程质量 环形图质量统计
      PieChartsWeek:[],//工程质量本周信息
      PieChartsMonth:[],//工程质量本月信息
      childrenBuild: [], // 工程质量--分部工程
      builditemsData: [],  // 施工进度--工程进度
      qualityTotalData: [], // 工程质量总工程
      thirdPartyNum: [], // 工程质量第三方检测报告
      changeIndex: 0,
      active: 0,
      tabBarChartsList:[],//施工进度（月）数据
      Safty:[],//安全文明第一项数据
      Green_construction:[],//安全文明第二项数据
      Epidemic_prevent:[],//安全文明第三项数据
      Safty_custom:[],//安全文明其他数据
      Month_totalprogress:0,//施工总进度
      ER_custom_item:[],//投资成本
      qualityWeekCustom:[],//本周缺陷自定义项
      weekResolved:0,//本周已解决问题
      weekUnResolved:0,//本周待解决问题
      qualityMonthCustom:[],//本月缺陷自定义项
      MonthResolved:0,//本月已解决问题
      MonthUnResolved:0,//本月待解决问题
    };
  },
  props: {},
  methods:{
    toggleTab(index) {
        this.active = index
        this.changeIndex = index
    },
    tab_month_choose(c){
      if(c==="next"){
        var date = new Date(new Date(this.choose_month).getTime());
        var y = date.getFullYear();
        var m = date.getMonth() + 2;
        m = m < 10 ? "0" + m : m; //月小于10，加0
        this.choose_month  =  y + "-" + m
      }else{
        var date = new Date(new Date(this.choose_month).getTime());
        var y = date.getFullYear();
        var m = date.getMonth();
        m = m < 10 ? "0" + m : m; //月小于10，加0
        this.choose_month  =  y + "-" + m
      }
    },
    //清空数据
    resetSafetyAll(){
      this.Safty = []
      this.Green_construction= []
      this.Epidemic_prevent= []
      this.Safty_custom = []
      this.tabBarChartsList = []
      this.MonthUnResolved = 0
      this.MonthResolved = 0
      this.Month_totalprogress = 0
      this.circleChartsData={}
      this.qualityMonthCustom = []
    },
    //根据月份获取信息
    ERD_WatchMonth(n){
      if(n === null) return
      let Month_Time =n+"-01"
      this.$axios({
        method: "get",
        url: `${window.bim_config.webserverurl}/api/VisualView/VisualView/GetMonthJSON?Month_Time=${Month_Time}&organizeId=${this.organizeId}&Token=${this.$staticmethod.Get('Token')}`,
      })
      .then(res => {
        this.resetSafetyAll()
        let data = res.data.Data
        if(Object.keys(data).length!=0){
          this.Month_totalprogress = Number(data.Month_totalprogress)
          this.tabBarChartsList = this.coppyArray(data.ERW_progress);
          this.circleChartsData={
            MonthUnResolved2:data.ERW_quality[0],
            MonthResolved2:data.ERW_quality[1],
          }
          this.MonthUnResolved = data.ERW_quality[2].ER_custom_input
          this.MonthResolved = data.ERW_quality[3].ER_custom_input
          let PieChartsMonth =[]
          PieChartsMonth.push(
            {value:data.ERW_quality[2].ER_custom_input},
            {value:data.ERW_quality[3].ER_custom_input}
          )
          this.PieChartsMonth = PieChartsMonth
          if(data.ERW_quality.length>4){
            let Safty_custom = this.coppyArray(data.ERW_quality)
            Safty_custom.splice(0,4)
            this.qualityMonthCustom = Safty_custom
          }
          this.Safty.push(data.ERW_Safty[0])
          this.Green_construction.push(data.ERW_Safty[1])
          this.Epidemic_prevent.push(data.ERW_Safty[2])
          if(data.ERW_Safty.length>3){
            let Safty_custom = this.coppyArray(data.ERW_Safty)
            Safty_custom.splice(0,3)
            this.Safty_custom = Safty_custom
          }
        }else{
          this.$message.warning('未获取到本月数据')
        }
      })
      .catch(() => {
      });
    },
    //获取本周信息
    ERD_WatchDate(n) {
      this.$axios({
        method: "post",
        url: `${window.bim_config.webserverurl}/api/VisualView/VisualView/GetNewWeek?Token=${this.$staticmethod.Get('Token')}`,
        data:this.$qs.stringify({
          organizeId:this.organizeId
        })
      })
      .then(res => {
        let data = res.data.Data
        if(Object.keys(data).length!=0){
          this.weekUnResolved = data.ERW_quality[0].ER_custom_input
          this.weekResolved = data.ERW_quality[1].ER_custom_input
          this.PieChartsWeek.push(
            {value:data.ERW_quality[0].ER_custom_input},
            {value:data.ERW_quality[1].ER_custom_input}
          )
          if(data.ERW_quality.length>2){
            let Safty_custom = this.coppyArray(data.ERW_quality)
            Safty_custom.splice(0,2)
            this.qualityWeekCustom = Safty_custom
          }
        }else{
          this.PieChartsWeek.push(
            {value:0},
            {value:0}
          )
        }
      })
      .catch(() => {
      });
    },
    //获取本周时间
    GetNewWeek() {
      var date = new Date();
      var y = date.getFullYear();
      var m = date.getMonth() + 1;
      var d = date.getDate();
      m = m < 10 ? "0" + m : m; //月小于10，加0
      d = d < 10 ? "0" + d : d; //day小于10，加0
      let nowDay = y + "-" + m + "-" + d;
      this.ERD_WatchDate(nowDay)
    },
    //获取本月时间
    GetNewMonth() {
      this.$axios({
        method: "post",
        url: `${window.bim_config.webserverurl}/api/VisualView/VisualView/GetNewMonth?Token=${this.$staticmethod.Get('Token')}`,
        data:this.$qs.stringify({
          organizeId:this.organizeId
        })
      })
      .then(res => {
        let data = res.data.Data
        if(data.Month_Time){
          this.choose_month=data.Month_Time.substring(0,data.Month_Time.length-3);
        }else{
          var date = new Date(new Date().getTime());
          var y = date.getFullYear();
          var m = date.getMonth() + 1;
          m = m < 10 ? "0" + m : m; //月小于10，加0
          this.choose_month  =  y + "-" + m
        }
      })
      .catch(() => {
      });
    },
    //获取工程总报信息
    GetNewEROutImage(){
      this.$axios({
        method: "post",
        url: `${window.bim_config.webserverurl}/api/VisualView/VisualView/GetNewSumOutImage?Token=${this.$staticmethod.Get('Token')}`,
        data:this.$qs.stringify({
          organizeId:this.organizeId
        })
      })
      .then(res => {
        let data = res.data.Data
        if(Object.keys(data).length!=0){
          let obj = {
            ER_custom_item1 : data.ER_custom_item.filter((i,d)=>d%2 ===0),
            ER_custom_item2 : data.ER_custom_item.filter((i,d)=>d%2 ===1)
          }
          let newArr = []
          obj.ER_custom_item1.map((o,i)=>newArr.push({
            ER_custom_item1:o,
            ER_custom_item2:obj.ER_custom_item2[i]
          }))
          this.ER_custom_item = newArr
        }
      })
      .catch(() => {
      });
    },
    //计算百分比
    percentage(a,b){
      return Number(((Number(b)/Number(a))*100).toFixed(2))
    },
    coppyArray(arr){
      return arr.map((e)=>{
        if(typeof e==='object'){
            return Object.assign({},e);
        }else{
            return e;
        }
      }) 
    },
    getRelatedParameters() {
      this.organizeId = sessionStorage.getItem('organizeId');
      this.Token = this.$staticmethod.Get("Token");
    },
  },
  mounted(){
    this.getRelatedParameters()
    this.GetNewMonth();
    this.GetNewWeek();
    this.GetNewEROutImage();
  },
  watch: {
    choose_month(n){
      this.ERD_WatchMonth(n)
    },
  },
};
</script>

<style lang="scss" scoped>
* {
    margin: 0;
    padding: 0;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
}
.air-data-board {
  width: 100%;
  height: 100%;
  padding: 30px 20px 15px 20px;
  box-sizing: border-box;
}
.col_content-text{
  width: 100%;
  overflow-x: auto !important;
  .total{
    width: 90%;
    margin: 0 auto;
    .total_title{
      font-size: 1rem;
      margin: 0.8rem 0;
      text-align: left;
      color: #C8C8C8;
    }
    .total_num{
      height: 2.4rem;
      line-height: 2.4rem;
      background: rgba(12, 108, 242, 0.25);
      border-radius: 2px;
      font-size: 2rem;
      color: #66B3FF;
    }
  }
  .progress{
    width: 90%;
    margin: 0 auto;
    .progress_header{
    height: 3rem;
    background: #08244D;
    box-shadow: inset 0px 0px 20px 10px rgba(11, 84, 230, 0.35);
    border-radius: 2px;
    border: 1px solid #0C6CF2;
    cursor: pointer;
      .progress_header_left{
        font-size: 1rem;
        color: #C8C8C8;
        line-height: 3rem;
        float: left;
        margin-left: 0.8rem;
      }
      .progress_header_right{
        font-size: 1rem;
        line-height: 3rem;
        float: right;
        margin-left: 0.8rem;
        width: 8rem;
        text-align: left;
        display: flex;
        position: relative;
        .tab_month{
          .tab_month_last{
            position: absolute;
            top: .8rem;
            right: .9rem;
            width: 0;
            height: 0;
            border-bottom: 7px solid #fff;
            border-left: 7px solid transparent;
            border-right: 7px solid transparent;
            cursor: pointer;
          }
          .tab_month_next{
            position: absolute;
            top: 1.6rem;
            right: .9rem;
            border-top: 7px solid #fff;
            border-left: 7px solid transparent;
            border-right: 7px solid transparent;
            cursor: pointer;
          }
        }
      }
    }
    .overall_progress{
      margin-top: 2rem;
      .overall_progressBall{
        margin: 1.5rem auto;
      }
    }
    .work_area{
      .BarCharts{
        padding-bottom: 1rem;
      }
      .tabBarCharts{
        margin: 0 auto;
        height: 1rem;
        line-height: 1rem;
        display: flex;
        align-items: center;
        justify-content: center;
        .active{
          background: #0C6CF2;
          float: left;
          margin: 0 5px;
          width: 10px;
          height: 10px;
          border-radius: 50%;
          cursor: pointer;
        }
        .tabDot{
          float: left;
          margin: 0 5px;
          width: 10px;
          height: 10px;
          background: rgba(12, 108, 242, 0.6);
          border-radius: 50%;
          cursor: pointer;
        }
        .tabDot:hover{
          background: #0C6CF2;
        }
      }
    }
  }
  .quality{
    width: 90%;
    margin: 0 auto;
    .quality_week{
      .quality_week_title{
        font-size: 0.9rem;
        color: #C8C8C8;
        text-align: left;
        height: 2.5rem;
        line-height: 2.5rem;
      }
      .quality_other{
        display: flex;
        margin-bottom: 1rem;
        .quality_other_title{
          font-size: 0.9rem;
          color: #C8C8C8;
          text-align: left;
          height: 2.5rem;
          line-height: 2.5rem;    
        }
        .quality_other_num{
          text-indent: 1rem;
          line-height: 2.5rem;   
          font-size: 1rem;
          color: #FFD500;
        }
      }
    }
  }
  .safetyAll{
    width: 90%;
    margin: 0 auto;
    // .safety{
    // }
    .Green_construction{
      margin-top: 2.5rem;
    }
      .grid-content {
        margin-top: 20px;
        text-align: center;
        .safetyNum0{
          font-size: 1.5rem;
          font-family: DINAlternate-Bold, DINAlternate;
          color: #0C6CF2;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
          cursor: pointer;
        }
        .safetyNum1{
          font-size: 1.5rem;
          font-family: DINAlternate-Bold, DINAlternate;
          color: #FF0D34;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
          cursor: pointer;
        }
        .safetyDesc{
          font-size: 0.8rem;
          font-family: PingFangSC-Medium, PingFang SC;
          font-weight: 500;
          color: #C8C8C8;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
          cursor: pointer;
        }
      }
      .safety_cont{
        margin-top: 1.5rem;
        height: 2rem;
        line-height: 2rem;
        font-size: .9rem;
        color:#C8C8C8;
        .safety_cont_left{
          width: 80%;
          float: left;
          text-align: left;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
          cursor: pointer;
        }
        .safety_cont_right{
          width: 15%;
          float: right;
          font-size: 1.5rem;
          text-align: right;
          color: #FFD500;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
          cursor: pointer;
        }
      }
  }
}
.row-box {
  height: 100%;
  width: 100%;
  .col-box {
    background-color: rgba(7, 17, 41, 0.85);
    padding-bottom: 20px;
    height: 100%;
    padding-top: 3.5rem;
    overflow: auto;
    display: flex;
    flex-direction: column ; 
    .col_box_title {
      display: flex;
      flex-direction: row;
      justify-content: center;
      align-items: center;
      width: 100%;
    }
  }
}
.sec-content{
    display: flex;
    // .chart-sec{
    // }
    .chart_right{
      margin-top: 1.5rem;
      .resolved{
        margin: 1rem;
        height: 1.5rem;
        color:#C8C8C8;
        .resolvedDot{
          float: left;
          width: 5px;
          height: 5px;
          background: #5B4DFF;
          border-radius: 50%;
          margin: .5rem;
        }
        .unresolvedDot{
          background: #FFC30D;
        }
        .resolvedCont{
          float: left;
        }
        .resolvedNum{
          text-indent: 1rem;
          line-height: 1.3rem;
          float: left;
          font-size: 1rem;
          color: #FFD500;
        }
      }
    }
  }
.subtitle{
  background-image: url("~@/assets/images/VisualPanel/subtitle.png");
  background-size: 100% 100%;
  width: 140px;
  height: 22px;
  font-size: 1rem;
  color: #fff;
  line-height: 15px;
  text-align: left;
  text-indent: 1rem;
}
.getDataFalse{
  height: 10rem;
  line-height: 10rem;
}
.el-progress /deep/ path:first-child {
  stroke: rgba(13, 215, 255, 0.2);
}
 /deep/ .el-input__inner {
  color: #fff;
}
.el-progress__text {
  color: #ffffff
}
.prefix-icon-class{
  display: none;
}
.row-col-span{
  height: 100%;
  position: relative;
  .col_box_name {
    position: absolute;
    top: -15px;
    width: 210px;
    line-height: 50px;
    height: 50px;
    background-image: url("~@/assets/images/VisualPanel/position_title.png");
    background-size: 100% 100%;
    .position_title{
      font-size: 24px;
      font-family: SourceHanSansCN;
      font-weight: 500;
      color: #ffffff;
    }
  }
}
</style>