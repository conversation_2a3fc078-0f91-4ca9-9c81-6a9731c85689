<template>
  <div class="project-wampper production-safety">
    <div class="content-left">
      <div class="safety-left">
        <div class="safety_people">
          <div class="subtitle">现场情况</div>
          <div class="SafeDays">
            <span>安全施工/天</span>
            <span v-if="startDate">{{getSafetyDays()}}</span>
            <span v-else>未设置开工时间</span>
          </div>
          <div class="Personnel_information" v-for="item in ERD_Site" :key="item.Daily_Id">
            <!-- <div class="PI_cont">{{item.ER_cont}}</div>
            <div class="PI_input">{{item.ER_custom_input}}</div> -->
          </div>
        </div>
        <div class="mobilization">
          <div class="subtitle">进场情况</div>
          <div v-if="ERD_Mobilization.length == 0" class="nothing nodata" >
              未获取到填报数据
          </div>
          <el-row :gutter="20" style="margin:0 20px" v-else>
            <el-col :span="8" class="el_col_center" v-for="i in ERD_Mobilization" :key="i.Daily_Id">
              <div class="progressall">
                  <div class="progressContainer" :style="{'height':(i.ER_custom_item1.ER_custom_input==0||i.ER_custom_item1.ER_custom_input==null? 0:'100px')}">
                    <div class="progressTop" :title="(i.ER_custom_item1.ER_custom_input==null? 0:i.ER_custom_item1.ER_custom_input)">{{(i.ER_custom_item1.ER_custom_input==null? 0:i.ER_custom_item1.ER_custom_input)}}</div>
                  </div>
                  <div class="progress" :style="{height:percentage(i.ER_custom_item1.ER_custom_input,i.ER_custom_item2.ER_custom_input)+ '%' }">                  
                    <div class="progressTop" :title="i.ER_custom_item2.ER_custom_input">{{i.ER_custom_item2.ER_custom_input}}</div>
                  </div>
              </div>
              <div class="progressDes" :title="i.ER_custom_item1.ER_cont">
                <div class="progressDes_cont">{{i.ER_custom_item1.ER_cont}}<span></span></div>
              </div>
              <div class="progressDes" :title="i.ER_custom_item2.ER_cont">
                <div class="progressDes_cont">{{i.ER_custom_item2.ER_cont}}<span style="background:#ffd500"></span></div>
              </div>
            </el-col>
          </el-row>
        </div>
        <div class="mobilization">
          <div class="subtitle">安全文明</div>
          <vue-seamless-scroll v-if="ERD_Safety.length > 5"
            :data="ERD_Safety" class="seamless-warp" :class-option="classOption">
            <el-row :gutter="20" style="margin:0 20px">
              <el-col :span="12" class="el_col_center" v-for="i in ERD_Safety" :key="i.Daily_Id">
                <div class="ERD_Safety_all">
                  <div class="ERD_Safety_all_cont">{{i.ER_cont}}</div>
                  <div class="ERD_Safety_all_input" :title="i.ER_custom_input">{{i.ER_custom_input}}</div>
                </div>
              </el-col>
            </el-row>
          </vue-seamless-scroll>
          <div v-else-if="ERD_Safety.length == 0" class="nothing nodata" >
              未获取到填报数据
          </div>
          <el-row :gutter="20" style="margin:0 20px" v-else>
            <el-col :span="12" class="el_col_center" v-for="i in ERD_Safety" :key="i.Daily_Id">
              <div class="ERD_Safety_all">
                <div class="ERD_Safety_all_cont">{{i.ER_cont}}</div>
                <div class="ERD_Safety_all_input" :title="i.ER_custom_input">{{i.ER_custom_input}}</div>
              </div>
            </el-col>
          </el-row>
        </div>
        <div class="mobilization mobilization_bottom" v-for="(item,index) in ERW_Safty" :key="index">
          <div class="subtitle">{{item.ERW_SaftyHeader}}</div>
          <div class="mobilization_information mobilization_information_bottom" v-for="(i,d) in item.ERW_SaftySingle" :key="d">
            <div class="cont_item">
              <div class="left">{{i.ER_custom_item1.ER_cont}}</div>
              <div class="right">{{i.ER_custom_item1.ER_custom_input}}</div>
            </div>
            <div class="cont_item">
              <div class="left">{{i.ER_custom_item2.ER_cont}}</div>
              <div class="right2">{{i.ER_custom_item2.ER_custom_input}}</div>
            </div>
            <el-progress :percentage="percentage_b(i.ER_custom_item1.ER_custom_input,i.ER_custom_item2.ER_custom_input)" 
            :stroke-width="20" 
            color="#0BD9D9" 
            :text-inside="true"
            ></el-progress>
          </div>
        </div>
      </div>
    </div>
    <div class="content-right" v-if="currentTableData.length!=0">
      <ModelViewer :tableData=currentTableData
        :params=ajaxParams
        :closeHover = false
        v-if="showModel"></ModelViewer>
    </div>
    <div class="content-right" v-else>
      <div class="nothing">加载中,如长时间未加载成功请检查现场管理是否绑定模型</div>
    </div>
  </div>
</template>

<script>
import ModelViewer from '@/components/CompsQuality/CompsQualityModel'
import vueSeamlessScroll from 'vue-seamless-scroll'
export default {
  name: "Productionsafety",
  data() {
    return {
      BIMComposerId:'',
      organizeId: '',
      Token: '',
      ajaxParams: {},
      currentTableData:[],
      showModel:false,
      buildDate: "",  // 安全施工时间
      progressSafetyBIMSrc: '',   // BIM地址
      ERD_Site:[],//日报现场情况信息
      ERD_Mobilization:[],//日报进场情况信息
      ERD_Safety:[],//日报安全文明信息
      ERW_Safty:[],//周报信息
      progress:80
      
    };
  },
  components: {
    ModelViewer,
    vueSeamlessScroll
  },
  props: {
    startDate:{
      type:String
    }
  },
  mounted() {
    let _this = this
    this.getRelatedParameters();
    this.GetNewDaily();
    this.GetNewWeek();
      
    _this.getTableData().then((data) => {
    _this.ajaxParams={
      BIMComposerId: _this.BIMComposerId,
      Token: _this.Token,
      OrganizeId: _this.organizeId,}
      _this.currentTableData = data;
      _this.showModel = true
    })
  },
  methods: {
    //获取日报信息
    GetNewDaily(){
      this.$axios({
        method: "post",
        url: `${window.bim_config.webserverurl}/api/VisualView/VisualView/GetNewDaily`,
        data:this.$qs.stringify({
          organizeId:this.organizeId
        })
      })
      .then(res => {
        let data = res.data.Data
        if(data.Daily_Time){
          this.ERD_Site = data.ERD_Site;
          this.ERD_Safety = data.ERD_Safety;
          let ERD_Mobilization = data.ERD_Mobilization
          for (let i = 0; i < ERD_Mobilization.length; i++) {
            const ele = ERD_Mobilization[i];
            if(ele.ER_custom_input == ''){
              ele.ER_custom_input = 0
            }
            if(ele.ER_cont == ''){
              ele.ER_cont = 0
            }
          }
          let obj = {
            ER_custom_item1 : ERD_Mobilization.filter((i,d)=>d%2 ===0),
            ER_custom_item2 : ERD_Mobilization.filter((i,d)=>d%2 ===1)
          }
          let newArr = []
          obj.ER_custom_item1.map((o,i)=>newArr.push({
            ER_custom_item1:o,
            ER_custom_item2:obj.ER_custom_item2[i]
          }))
          this.ERD_Mobilization = newArr
        }else{
        }
      })
      .catch(() => {
      });
    },
    //获取周报信息
    GetNewWeek() {
      this.$axios({
        method: "post",
        url: `${window.bim_config.webserverurl}/api/VisualView/VisualView/GetNewWeek`,
        data:this.$qs.stringify({
          organizeId:this.organizeId
        })
      })
      .then(res => {
        let data = res.data.Data
        if(data.Week_Time){
          let ERW_Safty = []
          ERW_Safty.push(JSON.parse(JSON.stringify(data.ERW_Safty[2])))
          this.ERW_Safty = ERW_Safty
          let obj = {
            ER_custom_item1 : data.ERW_Safty[2].ERW_SaftySingle.filter((i,d)=>d%2 ===0),
            ER_custom_item2 : data.ERW_Safty[2].ERW_SaftySingle.filter((i,d)=>d%2 ===1)
          }
          let newArr = []
          obj.ER_custom_item1.map((o,i)=>newArr.push({
            ER_custom_item1:o,
            ER_custom_item2:obj.ER_custom_item2[i]
          }))
          this.ERW_Safty[0].ERW_SaftySingle = newArr
        }else{
        }
      })
      .catch(() => {
      });
    },
    coppyArray(arr){
      return arr.map((e)=>{
        if(typeof e==='object'){
            return Object.assign({},e);
        }else{
            return e;
        }
      }) 
    },
    //计算百分比
    percentage(a,b){
      let numa = a==null?0:Number(a)
      let numb = b==null?0:Number(b)
      let num = Math.round((b/a)*100)
      if(numa==0 && numb!=0){
          return 100
      }else if(num>100){
          return 110
      }else{
          return num
      }
    },
    percentage_b(a,b){
      return Number(((Number(b)/Number(a))*100).toFixed(2))
    },
    // classOption () {
    //   return {
    //     step: 0.05, // 数值越大速度滚动越快
    //     limitMoveNum: 2, // 开始无缝滚动的数据量 this.dataList.length
    //     hoverStop: true, // 是否开启鼠标悬停stop
    //     direction: 1, // 0向下 1向上 2向左 3向右
    //     openWatch: true, // 开启数据实时监控刷新dom
    //     singleHeight: 0, // 单步运动停止的高度(默认值0是无缝不停止的滚动) direction => 0/1
    //     singleWidth: 0, // 单步运动停止的宽度(默认值0是无缝不停止的滚动) direction => 2/3
    //     waitTime: 1000 // 单步运动停止的时间(默认值1000ms)
    //   }
    // },
    //计算安全施工时间
    getSafetyDays(){
      if(!this.startDate) return
      let startdate =  Date.parse(this.startDate);
      var date = new Date();
      var y = date.getFullYear();
      var m = date.getMonth() + 1;
      var d = date.getDate();
      m = m < 10 ? "0" + m : m; //月小于10，加0
      d = d < 10 ? "0" + d : d; //day小于10，加0
      y + "-" + m + "-" + d;
      let todaydate = Date.parse( y + "-" + m + "-" + d);
      return (todaydate-startdate)/(24*60*60*1000)

    },
    getTableData() {
      return new Promise((resolve, rej) => {
        // let params = {
        //   keyword: "", //关键字
        //   Token: this.Token,
        //   bimcomposerId: this.BIMComposerId,
        //   StateType: "", //针对状态的过滤，使用英文逗号来拼接，可选值：A_ToBeCheck（待检查） B_ToBeRectified（待整改） C_ToBeRecheck（待验收） D_Qualified（已合格） ,
        //   AuthorType: "", //针对操作人的过滤，传空字符串或以下其中之一，可选值：AsChecker（我检查）AsRechecker（我复检）AsRectifier（我整改） ,
        //   Severitylevel: "", //针对严重等级的过滤，使用英文逗号来拼接，可选值为：一般、严重、非常严重 ,
        //   Types: "", //针对检查类型的过滤，使用英文逗号来拼接，可选值为：Exam_GetExamTypes 接口返回的数据中的 aedt_guid 字段 ,
        //   SortField: "", //排序依赖的字段，有：创建时间 CreateDate/''， 结束时间 RectificateDate， 状态 State ,
        //   SortIsAsc: "", //传1为正序，其它为倒序 ,
        //   Skip: "", //跳过多少条数据。若转换失败则取全部 ,
        //   Take: "", //取多少条数据。若 Skip 无效或转换为数字失败则取剩余全部
        // };

        this.$axios
          .get(`${this.$issueBaseUrl.GetMissions}?keyword=&Token=${this.ajaxParams.Token}&LinkType=${this.$staticmethod._Get('typelist')}&OrganizeId=${this.ajaxParams.OrganizeId}StateType=&AuthorType=&Severitylevel=&Types=&SortField=&SortIsAsc=&Skip=&Take=`)
          .then((res) => {
            // console.log(res.data.Data.List,'获取任务列表');

            if (res.data.Ret == 1) {
              resolve(res.data.Data.List);
            } else {
              rej("数据失败");
            }
          })
          .catch((res) => {
            rej(res);
          });
      });
    },
    //获取相关参数
    getRelatedParameters() {
      let _this = this
      _this.BIMComposerId = sessionStorage.getItem("bimcomposerId");
      _this.organizeId = sessionStorage.getItem('organizeId');
      _this.Token = _this.$staticmethod.Get("Token");
    },
  },
  computed: {
    classOption () {
      return {
        step: 0.3, // 数值越大速度滚动越快
        limitMoveNum: 2, // 开始无缝滚动的数据量 this.dataList.length
        hoverStop: true, // 是否开启鼠标悬停stop
        direction: 1, // 0向下 1向上 2向左 3向右
        openWatch: true, // 开启数据实时监控刷新dom
        singleHeight: 0, // 单步运动停止的高度(默认值0是无缝不停止的滚动) direction => 0/1
        singleWidth: 0, // 单步运动停止的宽度(默认值0是无缝不停止的滚动) direction => 2/3
        waitTime: 1000 // 单步运动停止的时间(默认值1000ms)
      }
    }
  }, 
  watch: {},
  beforeDestroy(){
  },
  destroyed () {
  }
};
</script>

<style lang="scss" scoped>
.content-left{
  margin: 20px 8px 0px 0px;
  width: 410px;
  cursor: pointer;
  background: rgba(7, 28, 72, 0.9);
}
.content-right{
  flex:1;
  margin: 20px 0 0 0;
  background: #f0f2f5;
}
.nothing{
  width: 100%;
  height: 100px;
  font-size: 18px;
  color: #000;
  text-align: center;
  line-height: 100px;
}
.nodata{
  color: #c8c8c8;
}
.content-left{
  overflow-x: auto !important;
}
.subtitle{
  background-image: url("~@/assets/images/VisualPanel/subtitle.png");
  background-size: 100% 100%;
  width: 140px;
  height: 22px;
  font-size: 16px;
  color: #fff;
  line-height: 15px;
  text-align: left;
  text-indent: 16px;
  margin: 5px 0 0 20px;
}
.common_cont{
  width: 80%;
  float: left;
  text-align: left;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  cursor: pointer;
}
.common_input{
  width: 15%;
  float: right;
  font-size: 30px;
  text-align: center;
  color: #FFD500;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  cursor: pointer;
}
.safety-left {
  margin: 20px 8px 0px 0px;
  width: 410px;
  cursor: pointer;
  background: rgba(7, 28, 72, 0.9);
  .safety_people{
    .SafeDays{
      line-height: 50px;
      display: flex;
      padding-left: 20px;
      span{
        font-size: 14px;
        color: #C8C8C8;
      }
      span:nth-child(2){
        margin-left: 20px;
        color: #FFD500;
        font-size: 32px;
      }
    }
    .Personnel_information{
      margin: 10px 20px;
      display: flex;
      line-height: 24px;
      .PI_cont{
        font-size: 14px;
        color: #C8C8C8;
      }
      .PI_input{
        margin-left: 20px;
        font-size: 18px;
        font-weight: bold;
        color: #0DD7FF;
      }
    }
  }
  .mobilization{
    margin-top: 30px;
    .mobilization_information{
        margin:0 20px;
        line-height: 40px;
        height: 40px;
        font-size: 16px;
        color:#C8C8C8;
        .cont_item{
        height: 40px;
        line-height: 40px;
        .left{
          float: left;
          font-size: 14px;
          color: #C8C8C8;
        }
        .right{
          float: right;
          font-size: 24px;
          font-weight: bold;
          color: #FFD500;
        }
        .right2{
          float: right;
          font-size: 18px;
          font-weight: bold;
          color: #fff;
        }
      }
    }
  }
  .mobilization_bottom{
    padding-bottom: 20px;
  }
  .in-case {
    padding: 13px 0 10px 20px;
    margin: 0px 20px 20px 20px;
    text-align: left;
    p {
      line-height: 34px;
      span {
        padding-right: 30px;
      }
      .padd10{
        font-family: 'AlibabaPuHuiTi-Regular';
        display: inline-block;
        margin-right: 10px;
      }
      .padd5{
        width: 10px;
      }
    }
  }
}
.mobilization_information_bottom{
  height: auto !important;
}
/deep/ .el-progress-bar .el-progress-bar__outer{
  background-color: rgba(11, 217, 217, 0.2) !important;
}
.p-r10{
  padding-right: 10px;
}
.p-l-r10{
  padding: 0 19px;
}
.safety-model{
  width: 100%;
  height: 100%;
  background: #b3c1cd;
}
.seamless-warp{
  height: 140px;
  overflow: hidden;
}
.el_col_center{
  font-size: 14px;
}
.progressDes{
  margin: 10px 0 0 0px;
  position: relative;
  .progressDes_cont{
    width: 120px;
    font-size: 12px;
    color: #c8c8c8;
    line-height: 17px;
    text-align: left;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    cursor: pointer;
  }
  span{
    position: absolute;
    width: 5px;
    height: 5px;
    background: #0C6CF2;
    border-radius: 50%;
    top: 6px;
    left: -8px;
  }
}
.progressall{
  margin: 40px auto 0;
  min-height: 100px;
  width: 50px;
  position: relative;
  .progressContainer {
    left: 0;
    bottom: 0;
    width: 16px;
    background-color: #0C6CF2;
    border-top-left-radius: 8px;
    border-top-right-radius: 8px;
    position: absolute;
    .progressTop{
      position: absolute;
      color: #0c6cf2;
      top: -35px;
      left: -9px;
      width: 32px;
      line-height: 40px;
      text-align: center;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
  }
  .progress {
    right: 0;
    bottom: 0;
    background-color: #ffd500;
    width: 16px;
    border-top-left-radius: 8px;
    border-top-right-radius: 8px;
    position: absolute;
    .progressTop{
      position: absolute;
      color: #ffd500;
      top: -35px;
      left: -9px;
      width: 32px;
      line-height: 40px;
      text-align: center;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
      
    }
  }
}
.ERD_Safety_all{
  margin-top: 20px;
  .ERD_Safety_all_cont{
    float: left;
    width:60%;
    text-align: center;
    font-size: 14px;
    color: #C8C8C8;
  }
  .ERD_Safety_all_input{
    float: right;
    width: 40%;
    text-align: center;
    height: 36px;
    font-size: 24px;
    color: #FFD500;
    line-height: 36px;
    border-right: 1px solid rgba(12, 108, 242, 0.4);
    box-sizing: border-box;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    cursor: pointer;
  }
}
</style>