<template>
  <div class="project-wampper panoramic-wampper">
    <div class="contentbg">
      <iframe
        frameborder="0"
        width="100%"
        height="100%"  
        :src="panoramicSrc">
      </iframe>
      <div class="Covery" v-if="loading == false">
        <div class="nothing">加载中</div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "panoramic",
  components: {},
  data() {
    return {
      panoramicSrc: '',
      loading:false
    };
  },
  mounted() {
    this.getList()
  },
  methods: {
    // 获取数据
    getList(){
      var _this = this;
      var _organizeId = _this.$staticmethod._Get("organizeId");
      //旧全景图获取
      // _this.$axios({
      //   method:'get',
      //   url:`${window.bim_config.webserverurl}/api/Panorama/PanoramaFile/GetList?organizeId=${_organizeId}`,
      //   data:{}
      // }).then(x => {
      //   if (x.status == 200 && x.data.Ret > 0) {
      //     let PbUrl = x.data.Data[0].PbUrl
      //     var viewurl = _this.$staticmethod.getPanoUrl(PbUrl, _this.$staticmethod._Get('organizeId'), '');
      //     _this.loading = true
      //     _this.panoramicSrc = viewurl;
      //   } else {
      //     _this.$message.error(x.data.Msg);
      //   }
      // }).catch(()=> {
      // });
      //新全景图获取
      _this.$axios({
        method:'get',
        url:`${window.bim_config.webserverurl}/api/Panorama/PanoramaFile/GetListByLabelGroup?organizeId=${_organizeId}&labelId=&pbName=`,
        data:{}
      }).then(x => {
        if (x.status == 200 && x.data.Ret > 0) {
          let PbUrl = x.data.Data[0].Panoramas[0].PbUrl
          var viewurl = _this.$staticmethod.getPanoUrl(PbUrl, _this.$staticmethod._Get('organizeId'), '');
          _this.loading = true
          _this.panoramicSrc = viewurl;
        } else {
          _this.$message.error(x.data.Msg);
        }
      }).catch(()=> {
      });
    },
  },
  watch: {},
  beforeDestroy(){
    this.panoramicSrc = ''
  }
};
</script>

<style lang="scss" scoped>
.panoramic-wampper{
  width:100%;
  height: 100%;
  position: relative;
}
.contentbg{
  width:100%;
  height:100%;
  background: #fff;
  position: relative;
}
.Covery{
  width:100%;
  height:100%;
  background-color:rgba(0,0,0,0.1);
  position: absolute;
  top:0;
  left: 0;
  z-index: 11;
}
.nothing{
  width: 100%;
  height: 100px;
  font-size: 18px;
  color: #000;
  margin-top: 150px;
  text-align: center;
  line-height: 100px;
}
</style>