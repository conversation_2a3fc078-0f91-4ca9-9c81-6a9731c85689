<template>
    <div class="dagang-form">
        <el-form 
            :inline="true"
            :model="ruleForm" 
            :rules="rules" 
            ref="ruleForm" 
            label-width="140px" 
            class="demo-ruleForm">
            <el-form-item label="项目名称" prop="name" class="project-name-select" size="small">
                <el-select 
                @change="func_projectItemChange"
                v-model="ruleForm.name" placeholder="请选择项目" :disabled="!dagangFormState">
                    <el-option 
                    v-for="item in projectItems"
                    :key="item.dni_guid"
                    :label="func_computeDniName(item)" :value="item.dni_guid">
                        <span>{{func_computeDniName2(item)}}</span>
                    </el-option>
                    <!-- <el-option label="Bime试用项目" value="name2"></el-option> -->
                </el-select>
            </el-form-item>

            <!-- <el-form-item label="建设单位" prop="constructionUnit" class="project-name-select" size="small">
                <el-select v-model="ruleForm.constructionUnit" placeholder="请选择建设单位" :disabled="!dagangFormState">
                    <el-option label="建设单位1" value="constructionUnit1"></el-option>
                    <el-option label="建设单位2" value="constructionUnit2"></el-option>
                </el-select>
            </el-form-item> -->
            <el-form-item label="建设单位" prop="constructionUnit" size="small">
                <el-input :disabled="!dagangFormState" v-model="ruleForm.constructionUnit"></el-input>
            </el-form-item>
            
            <el-form-item label="目录编号" prop="CatalogNumber" size="small">
                <el-input :disabled="!dagangFormState" v-model="ruleForm.CatalogNumber"></el-input>
            </el-form-item>

            <el-form-item label="项目编号" prop="ProjectNumber" size="small">
                <el-input disabled v-model="ruleForm.ProjectNumber"></el-input>
            </el-form-item>

            <el-form-item label="建设地点" prop="address" size="small">
                <el-input :disabled="!dagangFormState" v-model="ruleForm.address"></el-input>
            </el-form-item>

            <el-form-item label="是否重点" prop="important" size="small">
                <el-input disabled v-model="ruleForm.important"></el-input>
            </el-form-item>

            <!-- <el-form-item label="项目类型" prop="projectType" class="project-name-select" size="small">
                <el-select :disabled="!dagangFormState" v-model="ruleForm.projectType" placeholder="请选择项目类型">
                    <el-option label="项目类型1" value="projectType1"></el-option>
                    <el-option label="项目类型2" value="projectType2"></el-option>
                </el-select>
            </el-form-item> -->
            <el-form-item label="项目类型" prop="projectType" size="small">
                <el-input :disabled="!dagangFormState" v-model="ruleForm.projectType"></el-input>
            </el-form-item>

            <el-form-item label="费用类型" prop="expenseType" size="small">
                <el-input disabled v-model="ruleForm.expenseType"></el-input>
            </el-form-item>

            <!-- <el-form-item label="负责人" prop="personCharge" class="project-name-select" size="small">
                <el-select :disabled="!dagangFormState" v-model="ruleForm.personCharge" placeholder="请选择负责人">
                    <el-option label="负责人1" value="personCharge"></el-option>
                    <el-option label="负责人2" value="personCharge2"></el-option>
                </el-select>
            </el-form-item> -->
             <el-form-item label="负责人" prop="personCharge" size="small">
                <el-input :disabled="!dagangFormState" v-model="ruleForm.personCharge"></el-input>
            </el-form-item>

            <el-form-item label="批复投资(万元)" prop="pfInvestment" size="small">
                <el-input :disabled="!dagangFormState" v-model="ruleForm.pfInvestment"></el-input>
            </el-form-item>

            <el-form-item label="可研批复文号" prop="kyApprovalNumber" size="small">
                <el-input :disabled="!dagangFormState" v-model="ruleForm.kyApprovalNumber"></el-input>
            </el-form-item>

            <el-form-item label="文号批复估算(万元)" prop="approvalNumberEstimate" size="small">
                <el-input :disabled="!dagangFormState" v-model="ruleForm.approvalNumberEstimate"></el-input>
            </el-form-item>

            <el-form-item label="初设批复文号" prop="firstApprovalNumber" size="small">
                <el-input :disabled="!dagangFormState" v-model="ruleForm.firstApprovalNumber"></el-input>
            </el-form-item>

            <el-form-item label="初设批复概算(万元)" prop="firstBudgetEstimate" size="small">
                <el-input :disabled="!dagangFormState" v-model="ruleForm.firstBudgetEstimate"></el-input>
            </el-form-item>

            <el-form-item class="dagang-form-time" label="计划开工时间" prop="startTime" required size="small">
                <el-date-picker :disabled="!dagangFormState" type="date" placeholder="选择日期" v-model="ruleForm.startTime" style="width: 100%;"></el-date-picker>
            </el-form-item>

            <el-form-item class="dagang-form-time" label="计划竣工时间" prop="endTime" required size="small">
                <el-date-picker :disabled="!dagangFormState" type="date" placeholder="选择日期" v-model="ruleForm.endTime" style="width: 100%;"></el-date-picker>
            </el-form-item>

            <el-form-item label="计划工期(天)" prop="PlannedConstructionPeriod" size="small">
                <el-input :disabled="!dagangFormState" v-model="ruleForm.PlannedConstructionPeriod"></el-input>
            </el-form-item>

            <el-form-item label="数据状态" prop="dataStatus" size="small">
                <el-input :disabled="!dagangFormState" v-model="ruleForm.dataStatus"></el-input>
            </el-form-item>

            <el-form-item label="登记人" prop="registrant" size="small">
                <el-input :disabled="!dagangFormState" v-model="ruleForm.registrant"></el-input>
            </el-form-item>

            <!-- <el-form-item label="登记时间" prop="registrationTime" size="small">
                <el-input :disabled="!dagangFormState" v-model="ruleForm.registrationTime"></el-input>
            </el-form-item> -->
            <el-form-item class="dagang-form-time" label="登记时间" prop="registrationTime" required size="small">
                <el-date-picker :disabled="!dagangFormState" type="date" placeholder="选择登记时间" v-model="ruleForm.registrationTime" style="width: 100%;"></el-date-picker>
            </el-form-item>

            <!-- 概述 -->
            <el-form-item label="工程信息概述" prop="dggs" size="small"
            class="_css-crossing"
             >
                <textarea  :disabled="!dagangFormState"   style="resize:none;" v-model="ruleForm.dggs"></textarea>
            </el-form-item>

            <!-- 概述 -->

            <!-- <el-form-item>
                <el-button type="primary" @click="submitForm('ruleForm')">立即创建</el-button>
                <el-button @click="resetForm('ruleForm')">重置</el-button>
            </el-form-item> -->
        </el-form>
    </div>
</template>

<script>

export default {
    name: 'CompsDagang',
    props: ['dagangFormState'],
    data() {
        return {

            m_alliskey: [{value:'I',text:'是（I）'}, {value:'F',text:'否（F）'}],
            m_allfundssrc: [{value:'T',text:'投资（T）'}, {value:'F',text:'费用（F）'}],

            projectItems:[],

            rawData: null,//原始数据

            ruleForm: {
                name: '',//项目名称
                constructionUnit: '',//建设单位
                CatalogNumber: '',//目录编号
                ProjectNumber: '',//项目编号
                address: '',//建设地点
                important: '',//是否重点
                projectType: '',//项目类型
                expenseType: '',//费用类型
                personCharge: '',//负责人
                pfInvestment: '',//批复投资
                kyApprovalNumber: '',//科研批复文号
                approvalNumberEstimate: '',//文号批复估算
                firstApprovalNumber: '',//初设批复文号
                firstBudgetEstimate: '',//初设批复概算
                startTime: '',//计划开工时间
                endTime: '',//计划竣工时间
                PlannedConstructionPeriod: '',//计划工期
                dataStatus: '',//数据状态
                registrant: '',//登记人
                registrationTime: '',//登记时间
                dggs: '' // 工程信息概述
            },

            rules: {
                name: [
                    { required: true, message: '请选择项目名称', trigger: 'change' }
                ],

                constructionUnit: [
                    { required: true, message: '请选择建设单位', trigger: 'change' }
                ],

                CatalogNumber: [
                    { required: true, message: '请填写目录编号', trigger: 'blur' }
                ],

                ProjectNumber: [
                    { required: true, message: '请填写项目编号', trigger: 'blur' }
                ],

                address: [
                    { required: true, message: '请填写建设地点', trigger: 'blur' }
                ],

                important: [
                    { required: true, message: '是否重点', trigger: 'blur' }
                ],

                projectType: [
                    { required: true, message: '请选择项目类型', trigger: 'change' }
                ],

                expenseType: [
                    { required: true, message: '费用类型', trigger: 'blur' }
                ],

                personCharge: [
                    { required: true, message: '请选择负责人', trigger: 'change' }
                ],

                pfInvestment: [
                    { required: true, message: '请填写批复投资', trigger: 'blur' }
                ],

                startTime: [
                    { type: 'date', required: true, message: '请选择计划开工时间', trigger: 'change' }
                ],

                registrationTime:[
                    { type: 'date', required: true, message: '请选择登记时间', trigger: 'change' }
                ],

                endTime: [
                    { type: 'date', required: true, message: '请选择计划竣工时间', trigger: 'change' }
                ],
            }
        }
    },

    created() {
        this.setFormData();
    },

    methods: {

        // 下拉框选中
        // ---------
        func_projectItemChange(dni_guid) {
            
            // 根据选中的dni_guid找到对应的item数据
            // ----------------------------------
            var _this = this;
            var _index = _this.projectItems
                .findIndex(x => x.dni_guid == dni_guid);
            if (_index >= 0) {

                // 找到对应的数据
                // -------------
                var item = _this.projectItems[_index];

                // 修改名称
                // --------
                //_this.ruleForm.name = item.dni_name;

                // 联动修改三个值
                // =============

                // 第一个是项目编号
                // ----------------------------------------
                _this.ruleForm.ProjectNumber = item.dni_flownum;

                // 是否重点、费用类型相关
                // 从 m_alliskey 及 m_allfundssrc
                // 中取出对应值的item
                // -----------------
                var _indexIsKey = _this.m_alliskey
                    .findIndex(x => x.value == item.dni_iskey);
                var _indexFundsSrc = _this.m_allfundssrc
                    .findIndex(x => x.value == item.dni_fundsSrc);
                if (_indexIsKey >= 0) {
                    var isKeyItem = _this.m_alliskey[_indexIsKey];
                    _this.ruleForm.important = isKeyItem.text;
                }
                if (_indexFundsSrc >= 0) {
                    var fundsSrcItem = _this.m_allfundssrc[_indexFundsSrc];
                    _this.ruleForm.expenseType = fundsSrcItem.text;
                }

            }

        },

        func_computeDniName(item) {
            var _this = this;
            var text = `${item.dni_name}`;
            return text;
        },

        func_computeDniName2(item) {
            var _this = this;
            var text = `${item.dni_name} - ${item.dni_previewname}`;
            return text;
        },

        setFormData() {

            // 拿到项目ID
            // ---------
            var _this = this;
            var _organizeId = _this.$staticmethod._Get("organizeId");

            // 请求接口，获取该项目所有属机构的所有机构字典数据
            // --------------------------------------------
            var _url = `${window.bim_config.webserverurl}/api/DGCC/DgDic/GetCodeByOrganizeId?organizeId=${_organizeId}`;
            _this.$axios(_url).then(x => {
                if (x.data.Ret > 0) {
                    _this.projectItems = x.data.Data.List;
                } else {
                    _this.$staticmethod.error(x.data.Msg);
                }
            }).catch(x => {
                debugger;
                console.error(x);
            })

            //请求接口，将获取到的数据 赋值给ruleForm里的各元素
            //将获取到的原始数据，另外保存一份给rawData
            //如果点击“取消”，则要把rawData的值 覆盖ruleForm
            // -------------------------------------------
            _this.$axios.get(`${window.bim_config.webserverurl}/api/DGCC/DgDic/GetDaGangJson?organizeId=${
                _organizeId}`).then(x => {
                    if (x.data.Ret > 0) {

                        // 尝试反序列化 boee_dagangjson
                        // ---------------------------
                        var dgjsonobj = {};
                        try {
                            dgjsonobj = JSON.parse(x.data.Data.boee_dagangjson);
                        } catch (e) {

                        }

                        if (dgjsonobj == null) {
                            dgjsonobj = {};
                        }

                        // 赋予给 _this.rawData ruleForm
                        // 做两份深拷贝
                        // -----------
                        // var objcopy1 = _this.$staticmethod.DeepCopy(dgjsonobj);
                        // var objcopy2 = _this.$staticmethod.DeepCopy(dgjsonobj);
                        // objcopy1.startTime = new Date(objcopy1.startTime);
                        // objcopy1.endTime = new Date(objcopy1.endTime);
                        // objcopy1.registrationTime = new Date(objcopy1.registrationTime);
                        // objcopy2.startTime = new Date(objcopy2.startTime);
                        // objcopy2.endTime = new Date(objcopy2.endTime);
                        // objcopy2.registrationTime = new Date(objcopy2.registrationTime);
                        _this.rawData = dgjsonobj;
                        _this.ruleForm = dgjsonobj;

                        // 重新把三个时间字段赋一下值
                        // ------------------------
                        //debugger;
                        _this.ruleForm.startTime = new Date(_this.ruleForm.startTime);
                        _this.ruleForm.endTime = new Date(_this.ruleForm.endTime);
                        _this.ruleForm.registrationTime = new Date(_this.ruleForm.registrationTime);
                        
                    } else {
                        _this.$message.error(x.data.Msg);
                    }
                }).catch(x => {
                    console.error(x);
                });

        },

        // 拿取 data 的值，得到json字符串
        // ----------------------------
        func_getjsonvalue(){

            // 将 ruleForm 深拷贝给 obj
            // -----------------------
            var _this = this;
            var obj = _this.$staticmethod.DeepCopy(_this.ruleForm);
            
            // obj 其中的日期字段转为字符串
            // --------------------------
            // obj.startTime = _this.$staticmethod.dtToString(obj.startTime);
            // obj.endTime = _this.$staticmethod.dtToString(obj.endTime);
            // obj.registrationTime = _this.$staticmethod.dtToString(obj.registrationTime);

            // 转为字符串并返回
            // ---------------
            var objstr = JSON.stringify(obj);
            return objstr;
        },

        //提交数据
        submitForm() {

            var _this = this;

            //验证
            this.$refs['ruleForm'].validate((valid) => {
            if (valid) {

                //验证通过
                //这里ajax提交数据
                // ==============

                // 拿取 data 的值，得到json字符串
                // ----------------------------
                var jsonvalue = _this.func_getjsonvalue();

                // 构造数据体
                // ---------
                var fd = {
                    "OrganizeId": _this.$staticmethod._Get("organizeId"),
                    "Token": _this.$staticmethod.Get("Token"),
                    "DagangJson": jsonvalue
                };

                // 提交
                // ----
                _this.$axios({
                    url: `${window.bim_config.webserverurl}/api/DGCC/DgDic/EditDaGangJson`,
                    method: 'post',
                    data: _this.$qs.stringify(fd)
                }).then(x => {
                    if (x.data.Ret > 0) {
                        
                        // 将 ruleForm 写给 rawData
                        // 弹出消息：保存成功
                        // 回到未编辑状态
                        // -------------
                        _this.$message.success('保存成功');
                        _this.rawData = _this.$staticmethod.DeepCopy(_this.ruleForm);

                        window.mainvue_zjr.setDagangFormState(false);

                    } else {
                        _this.$message.error(x.data.Msg);
                    }
                }).catch(x => {
                    debugger;
                    console.error(x);
                });

            } else {
                //未验证通过，return
                return false;
            }
            });
        },
        
        //取消重置数据
        resetForm() {

            //点击“取消”则要重置数据
            //将rawData的值 覆盖ruleForm，达到取消复原效果
            // ----------------------------------------
            var _this = this;
            _this.ruleForm = _this.$staticmethod.DeepCopy(_this.rawData);
        },
    }
}
</script>

<style>




    .dagang-form .demo-ruleForm .el-input__inner {
        background-color: #FFF;
        border-radius: 4px;
        border: 1px solid #DCDFE6;
        padding: 0 15px;
    }

    .dagang-form .demo-ruleForm .el-input.is-disabled .el-input__inner {
        background-color: #F5F7FA;
    }

    .dagang-form .demo-ruleForm .dagang-form-time .el-input__inner {
        padding: 0 22px;
    }

    .dagang-form .demo-ruleForm .el-form-item__content {
        width: calc(100% - 170px);
    }

    .dagang-form .demo-ruleForm .el-form-item__content .el-select {
        width: 100%;
    }
</style>

<style scoped>
    .dagang-form .el-form-item {
        width: 45%;
    }

    .dagang-form  .el-form-item._css-crossing.el-form-item--small {
        width: 92%;
    }

    ._css-crossing textarea {
        width: 100%;
        border-radius: 4px;
        border:1px solid #DCDFE6;
        min-height: 64px;
        outline: none;
        resize: none;
        font-family:Arial, Helveetica, sans-serif;
        padding:4px 12px;
        box-sizing: border-box;
    }

    ._css-crossing textarea:disabled {
       background-color: #F5F7FA;
       color: #C0C4CC;
       cursor: not-allowed;
    }


    /* .dagang-form textarea.el-textarea__inner {
        outline: none !important;
        resize: none !important;
    } */

    .project-name-select .el-select-dropdown__item {
        height: auto;
        line-height: normal;
    }
</style>