<template>
  <div class="component-library" @click.stop="clickAll()"
  :class="getExtendClass()"
  >
   <!-- 左上角按钮：返回列表 -->
      <div class="_css-backtolist" @click.stop="backtolist" v-if="this.$route.name == 'OrganizationComponentLibrary'">
        <div class="icon-suggested-close-fill _css-backtolist-icon"></div>
        <div class="_css-backtolist-text">项目列表</div>
      </div>
    <div class="component-model-iframe" v-if="lookModelState">  <!--   -->
      <div class="model-title">
        <p>{{iframeName}}</p>
        <span class="_css-closebtn icon-suggested-close" @click.stop="closeRightLookModelState"></span>
      </div>
      <div class="model-iframe">
        <iframe width="100%" height="100%" ref="lookModel" :src="modelIframeSrc" frameborder="0"></iframe>
      </div>
    </div>
    <!--左侧树-->
    <div class="library-left-tree">
      <div class="top-functional-area">
        <span class="title">构件库</span>

        <!-- 新建构件库按钮，当页面处于项目中时不显示 -->
        <el-tooltip
        v-if="!injectclass"
        content="新建构件库" placement="top">
          <span class="icon icon-interface-addnew" @click.stop="toggleAddNewLibraryDiv"></span>
        </el-tooltip><!-- //新建构件库按钮，当页面处于项目中时不显示 -->

      </div>

      <div
        v-if="addNewLibraryState"
        class="add-new-library-content"
        @click.stop=""
        v-libraryClickClose="toggleAddNewLibraryDiv">
        <input v-model="newLibraryName" placeholder="请输入构件分类名称" />
        <textarea v-model="newLibraryDesc" placeholder="请输入构件分类描述" cols="30" rows="5"></textarea>
        <span :class="[{'not-allowed' : newLibraryName == ''},'button button-hover']" @click.stop="addCategory">确定</span>
      </div>

      <div class="left-tree-container">
        <el-tree
          ref="libraryTree"
          :props="defaultProps"
          lazy
          :load="loadTreeNode"
          :expand-on-click-node="false"
          node-key="bcc_guid"
          @node-expand = "onNodeExpand"
          @node-collapse = "onNodeCollapse"
          @node-click = "handleNodeClick"
          :default-expanded-keys="defaultExpandedKeys"
        >
          <div class="custom-tree-node" slot-scope="{ node, data }" @mouseover="mouseoverLeaf(data,$event)" @mouseleave="mouseleaveLeaf(data,$event)">
            <div class="tree-node-content">
              <span v-if="data.nodeState" class="css-mr10 icon icon-interface-folder"></span>
              <span v-else class="css-mr10 icon icon-interface-unfolder"></span>
              <span>{{ node.label }}</span>
            </div>
            <div class="tree-node-content">
              <!-- 当前状态“逻辑或”上是项目中的页面 -->
              <span class="number" v-if="!data.nodeleafShowState || injectclass" ></span>
              <span class="number icon-interface-more" v-else @click.stop="leafMoreClick($event,data,node)"></span>
            </div>
          </div>
        </el-tree>
      </div>
      <div class="editTreeName" v-if="hoverTreeLeaf" :style="getEditStyle()">
        <ul>
          <li @click.stop="treeRename">
            <i class="icon-interface-edit"></i>重命名
          </li>
          <li @click.stop="treeDeltel">
            <i class="icon-interface-delete"></i>删除
          </li>
        </ul>
      </div>

      <div class="editTreeName editTreeName-rename" v-if="treeRenameChange" @click.stop="" :style="getEditStyle()">
        <p class="_css-edit-title _css-bottom-line">
          重命名
          <i class="icon-interface-back" @click.stop="clickAll()"></i>
        </p>
        <p class="_css-edit-title">
          <input class="search-input" placeholder="请输入文件名称" v-model="treerenameModel"/>
        </p>
        <p class="_css-edit-btn" @click.stop="sureRenameFun">确定</p>
      </div>


    </div>



    <!--中间 构件/分类 列表-->
    <div class="library-middle-list">
      <div class="top-functional-area">
        <div>

          <!-- 必须是机构级页面才可以上传构件 -->
          <el-tooltip
          v-if="!injectclass"
          content="上传构件" placement="top">
            <span class="icon icon-interface-cloud-upload" @click.stop="uploadFamilyFile"></span>
          </el-tooltip><!-- //必须是机构级页面才可以上传构件 -->

          <el-checkbox style="marginLeft:20px" v-model="Allchecked" @change="AllbatchSelectionLibrary">全选</el-checkbox>
          <!-- <el-tooltip content="创建分类" placement="top">
            <span class="icon icon-interface-folder-copy css-ml24" @click.stop="handelClickCreatingCategories"></span>
          </el-tooltip> -->
        </div>

        <div>
          <el-tooltip content="批量选择构件" placement="top">
            <span
              @click.stop="batchSelectionLibrary"
              :class="[{active: batchSelectionState},'icon icon-interface-Merge middle']"></span>
          </el-tooltip>

          <el-tooltip content="排序" placement="top">
            <span class="icon icon-interface-ascending middle css-ml24" @click.stop="handleClickSort"></span>
          </el-tooltip>

          <el-tooltip content="筛选标签" placement="top">
            <span
              :class="[{ active : libraryLabelScreenState }, 'icon icon-interface-filter css-ml24 middle']"
              @click.stop="libraryLabelScreenState=!libraryLabelScreenState">
            </span>
          </el-tooltip>
          <el-input
            class="search-input components-search-input"
            placeholder="搜索"
            v-model.trim="componentsSearch"
            @input="componentsSearchChange"
            clearable>
          </el-input>
          <!-- <input class="search-input" placeholder="搜索" clearable v-model="componentsSearch" @change="componentsSearchChange" /> -->
        </div>
      </div>
      <div class="middle-list-container">
        <!--构件标签筛选-->
        <div v-if="libraryLabelScreenState" class="library-label-screen" @click.stop="handleSelectLibraryLabel">
          <span
            :data-sign="index"
            v-for="(item,index) in searchCheckArr"
            :key="index"
            class="label-item button-hover"
            :class="[(hasSelectLibraryLabelData.indexOf(index+'') != -1) ? 'active' : '']">
            {{ item.bct_name }}
          </span>
        </div>

        <!-- <div v-if="!batchSelectionState" class="title margin-top-15">分类（{{batchList.length}}）</div>
        <div v-if="!batchSelectionState" class="top-classify-container">
          <el-row :gutter="10">
            <el-col :xs="8" :sm="8" :md="5" :lg="4" :xl="3" v-for="(item,index) in batchList" :key="'classify'+index">
              <div :class="[{active : currentSelectClassify==index},'classify-content button-hover']" @click.stop="selectClassifyItem(index,item)">
                <div class="document-icon">
                  <img :src="librarySvg" alt="" width="100%">
                </div>
                <div class="name">
                  {{item.bcc_name}}
                </div>
                <div class="statistics">{{item.ItemCount}}个族文件</div>
              </div>
            </el-col>
            <el-col :xs="8" :sm="8" :md="5" :lg="4" :xl="3" v-if="addNewCategories">
              <div class="classify-content button-hover">
                <div class="document-icon">
                  <img :src="librarySvg" alt="" width="100%">
                </div>
                <div class="name">
                  <input type="text"
                    ref="addinputs"
                    @blur="inputblur()"
                    :disabled="diasabledInput"
                    v-model="addNewCategoriesName"
                    class="_css-batch-input">
                </div>
                <div class="statistics">0个族文件</div>
              </div>
            </el-col>
          </el-row>
        </div> -->

        <div class="title margin-top-15">构件&nbsp;&nbsp; {{LibraryList.length !== 0?'('+LibraryList.length+')':""}}</div>
        <div class="bottom-list-container top-classify-container">
          <el-row :gutter="10"
          class="_css-extend-itemctn"
          :class="getExtendClass()">
            <el-col :xs="12" :sm="12" :md="componentDetailsShow==1? 6:5" :lg="componentDetailsShow==1? 6:5" :xl="componentDetailsShow==1? 5.5:4" v-for="(item,index) in LibraryList" :key="'list'+index"
            :class="getExtendClass()"
            >
              <div :class="{'item-active' :listItemSelectState.indexOf(index) != -1}"
                   class="classify-content list-container-item list-refguid" @mouseover="hoverContainerItem(index,item)" @click.stop="selectListContainerItem(index,item)">
                <div class="document-icon library-list-img">
                  <img :src="GetModelImg(item.imgpath,item.bcfa_base64image)">
                  <!-- <img :src="item.imgpath == '' ? itemSvg : dataUrl + item.imgpath " alt="" width="100%"> -->
                </div>
                <div class="_css-refguid-shortcut" v-if="item.bcfa_refguid.length>0">
                  <!-- <i class="icon-interface-cloud-upload"></i> -->
                  <!-- <i class="icon-interface-shortcut"></i> -->
                  <img :src="shortcutSvg" alt="" width="100%">
                </div>
                <div class="name">
                  {{item.bcfa_name}}
                </div>
                <div class="statistics">
                  <i
                  v-if="!injectclass"
                  class="icon-interface-delete" @click.stop="hoverChangeModelDel(item)"></i>
                  <span>更新时间:{{item.bcfa_updatetime | flttimeshorter}}</span>
                  <i class="icon-interface-download-fill" @click.stop="hoverhandelClickDownFile(item.bcfa_guid)"></i>
                </div>
              </div>
            </el-col>
          </el-row>
        </div>
      </div>
    </div>

    <!--右侧 构件/分类 详情-->
    <div class="library-right-details" v-if="componentDetailsShow==1">
      <div class="top-functional-area"
         :class="getExtendClass()"
      >
        <span class="title button-hover"
        :class="getExtendClass()"
        >
          构件详情
        </span>
        <span class="_css-closebtn icon-suggested-close" @click.stop="closeRightDetails"></span>
      </div>

      <div class="_detail_content"
      :class="getExtendClass()"
       v-if="currentSelectClassify==null && !batchSelectionState && listItemSelectState.length > 0">
        <div class="_detail-check-info">
          <div class="_detail-check-img">
            <img v-if="isModelShow=='上传'" :src="imgpathFile == '' ? itemSvg : dataUrl + imgpathFile" alt="" width="100%">
            <div v-if="isModelShow=='查看'" ref="modelRefTime" id='model-more' class="model-more-class"></div>
            <!-- <iframe v-if="isModelShow=='查看'"  width="100%" height="100%" ref="lookModel" :src="modelIframeSrc" frameborder="0"></iframe> -->
            <span
            v-if="isModelShow=='查看' || !injectclass"
            class="button-hover show-3d-model" @click.stop="handelUplodModel()">{{isModelShow}} 3D 模型</span>
            <!-- <span
            v-if="isModelShow=='查看'"
            class="button-hover show-3d-model" style="top:58px" @click.stop="handelUplodModel()">更新 3D 模型</span> -->
          </div>
          <div class="_detail-check-icon">

            <template v-if="!injectclass">
                  <el-tooltip content="上传3D模型" v-if="isModelShow=='上传'" placement="top">
                    <span class="icon icon-interface-model_list" @click.stop="handelUplodModel()"></span>
                  </el-tooltip>

                  <el-tooltip content="更新3D模型" placement="top" v-if="isModelShow=='查看'" >
                    <span class="icon icon-interface-model_list" @click.stop="handelUplodModel('更新')"></span>
                  </el-tooltip>

                  <el-tooltip content="更新缩略图" placement="top">
                    <span class="icon icon-interface-model-picture" @click.stop="changeModelthumbnail()"></span>
                  </el-tooltip>

                  <el-tooltip content="更新构件" placement="top">
                    <span class="icon icon-interface-associated-component" @click.stop="changeModelFile()"></span>
                  </el-tooltip>

                  <el-tooltip content="创建快捷方式" placement="top">
                    <span class="icon icon-interface-restore" @click.stop="changeModelshortcut(0)"></span>
                  </el-tooltip>

                  <el-tooltip content="移动文件到" placement="top">
                    <span class="icon icon-suggested-close-fill" @click.stop="changeModelMove(0)"></span>
                  </el-tooltip>

                  <el-tooltip content="删除" placement="top">
                    <span class="icon icon-interface-delete" @click.stop="changeModelDel()"></span>
                  </el-tooltip>
            </template>

            <template v-else>
                <span class="icon icon-interface-model-picture css-dis" ></span>
                <span class="icon icon-interface-model_list css-dis" ></span>
                <span class="icon icon-interface-restore css-dis" ></span>
                <span class="icon icon-suggested-close-fill css-dis" ></span>
                <span class="icon icon-interface-delete css-dis" ></span>
            </template>

            <el-tooltip content="下载构件" placement="top">
              <span class="icon icon-interface-download-fill" @click.stop="handelClickDownFile()"></span>
            </el-tooltip>
          </div>
        </div>
        <div class="_detail-tab">
          <div class="_detail-tab-title" @click.stop="handleTabPanToggle">
            <span :class="[{active:tabPanToggleState==0},'title']" data-sign="0">信息</span>
            <span>|</span>
            <span :class="[{active:tabPanToggleState==1},'title']" data-sign="1">属性</span>
            <span>|</span>
            <span :class="[{active:tabPanToggleState==2},'title']" data-sign="2">附件</span>
          </div>
          <div class="_detail-tab-content">
            <div class="tab-pan-conten tab-message" v-if="tabPanToggleState==0">
              <div class="lable-tab-flex-content">
                <div class="tab-pan-library-label form-input" v-if="editMessage">
                  <p class="_css-title">构件名称</p>
                  <span class="_css-lable">{{selectTabPanData.information.title}}</span>

                  <p class="_css-title">构件描述</p>
                  <span class="_css-lable">{{selectTabPanData.information.desc}}</span>
                </div>
                <div class="tab-pan-library-label form-input" v-if="!editMessage">
                  <p class="_css-title">构件名称</p>
                  <input v-model="selectTabPanData.information.title" placeholder="构件名称"/>
                  <p class="_css-title">构件描述</p>
                  <textarea v-model="selectTabPanData.information.descEdit" placeholder="描述" cols="30" rows="8"></textarea>
                </div>

                <div class="tab-pan-library-label" v-if="editMessage">
                  <div class="_css-title">构件标签</div>
                  <div class="_css-tab-lable">
                    <div class="_css-child-sure"
                      v-for="(item,index) in tabLaberArr"
                      :key="index">
                      <span :title="item.bct_guid">{{item.bct_name}}</span>
                    </div>
                  </div>
                </div>
                <div class="tab-pan-library-label" v-if="!editMessage">
                  <div class="_css-title">构件标签</div>
                  <div class="add" @click.stop="addTagFun">
                    <span class="icon icon-suggested-plus_circle"></span>
                    添加标签
                  </div>
                  <div class="_css-tab-lable">
                    <div class="_css-tab-lable-child"
                      v-for="(item,index) in tabLaberArr"
                      :key="index">
                      <span :title="item.bct_guid">{{item.bct_name}}</span>
                      <i class="_css-tab-icon icon-suggested-close_circle" @click.stop="delTabTagFun(item.bct_guid)"></i>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="tab-pan-conten _css-tab-attrall" v-if="tabPanToggleState==1">
              <div class="lable-tab-flex-content">
                <el-select
                  v-model="attrValue"
                  placeholder="请选择"
                  @change="selectAttrChange">
                  <el-option
                    v-for="item in attributeListSelect"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                    >
                  </el-option>
                </el-select>
                <div class="tab-attr-content" v-for="(item,key,index) in attributeList" :key='index'>
                  <p class="tab-attr-content-title" v-for="(children,childkey,index) in item" :key="index">
                    {{childkey}}
                  </p>
                  <div class="_css-tab-list" v-for="(child,childkey1) in item" :key="childkey1">
                    <p v-for="(itemchild,itemkey) in child" :key="itemkey">
                      <span>{{itemchild.bcfap_name}}</span>
                      <span class="_css-normal">{{itemchild.bcfap_value}}</span>
                    </p>
                  </div>
                </div>
              </div>
            </div>
            <div class="tab-pan-conten" v-if="tabPanToggleState==2">
              <div class="lable-tab-flex-content">
                <div
                @click="func_emitout($event, item)"
                class="_css-add-list" v-for="(item,index) in attachmentList" :key="index">
                  <div class="enclosure-list">
                    <span class="thumb mulcolor-interface-jpg"></span>
                    <span class="name">{{item.originname}}</span>
                    <span class="icon tab-icon-down icon-interface-delete" @click.stop="handelClickRemoveAttachment(item)"></span>
                    <span class="icon tab-icon-down icon-interface-download-fill" @click.stop="handelClickDownttachment(item)"></span>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div

          class="_detail-btn">
            <div class="_css-tab-submit" v-if="tabPanToggleState==0 && !injectclass">
              <div class="_css-select-down" v-if="editMessage" @click.stop="handelClickEdit(0)">编辑信息</div>
              <div class="_css-select-down" v-if="!editMessage" @click.stop="handelClickEdit(1)">保存</div>
            </div>
            <div class="_css-tab-submit _css-upload-down" v-if="tabPanToggleState==1">
              <el-tooltip class="item" effect="dark" content="上传属性" placement="top-start">
                <span
                v-if="!injectclass" style="margin-right:10px;"
                class="icon-interface-cloud-upload" @click.stop="handelClickUploadAttr()"></span>

              </el-tooltip>
              <el-tooltip class="item" effect="dark" content="下载属性模板" placement="top-start">
                <span
                  v-if="!injectclass"
                  class="icon-interface-download-fill" @click.stop="handelClickDownDemo()"></span>

              </el-tooltip>
              <div class="_css-down2 _css-select-down" @click.stop="handelClickDownAttr">下载属性</div>
              <span
              v-if="!injectclass"
              class="icon-interface-delete" @click.stop="handelClickDeleteAttr"></span>
            </div>
            <div class="_css-tab-submit _css-select-down" v-if="tabPanToggleState==2 && !injectclass" @click.stop="handelClickAddAttachment">添加附件</div>
          </div>
        </div>
      </div>
      <div class="_detail_content _detail_multiple" v-if="batchSelectionState && listItemSelectState.length > 0">
        <div class="_detail-check-info">
          <div class="transform">
            <div class="component-thumb" v-if="listItemSelectState.length>=1">
              <img :src="multipleImgArr[0] == '' ? itemSvg : dataUrl + multipleImgArr[0]" alt="" width="100%">
            </div>
            <div class="component-thumb" v-if="listItemSelectState.length>=2">
              <img :src="multipleImgArr[1] == '' ? itemSvg : dataUrl + multipleImgArr[1]" alt="" width="100%">
            </div>
            <div class="component-thumb" v-if="listItemSelectState.length > 2">
              <img :src="multipleImgArr[2] == '' ? itemSvg : dataUrl + multipleImgArr[2]" alt="" width="100%">
            </div>
          </div>

          <div class="function-menu">
            <template  v-if="!injectclass">
              <el-tooltip content="创建快捷方式" placement="top">
                <span class="icon icon-interface-restore" @click.stop="changeModelshortcut(1)"></span>
              </el-tooltip>

              <el-tooltip content="移动文件到" placement="top">
                <span class="icon icon-suggested-close-fill" @click.stop="changeModelMove(1)"></span>
              </el-tooltip>

              <el-tooltip content="删除" placement="top">
                <span class="icon icon-interface-delete" @click.stop="changeModelDels()"></span>
              </el-tooltip>
            </template>
            <template v-else>
                <span class="icon icon-interface-restore css-dis" ></span>
                <span class="icon icon-suggested-close-fill css-dis" ></span>
                <span class="icon icon-interface-delete css-dis" ></span>
            </template>
            <el-tooltip content="下载" placement="top">
              <span class="icon icon-interface-download-fill" @click.stop="changeModelClickDownFile()"></span>
            </el-tooltip>
          </div>

          <div class="_css-detail-multiple-title">{{ listItemSelectState.length }} 个构件已选</div>
        </div>

        <div class="_css-check-List">
          <div class="batch-select-list" v-for="(item,index) in multipleNameArr" :key="'select-list'+index">
            <span class="icon icon-interface-superimposed css-mr12"></span>
            <span class="title">{{item}}</span>
          </div>
        </div>

      </div>
    </div>
    <compsFileBrowsers
      v-if="CompsFileBrowser_data.visible"
      :data="CompsFileBrowser_data"
      @oncancel="folderBrowser_oncancel"
      @onok="folderBrowser_onok"
    ></compsFileBrowsers>
    <div class="_css-tab-select" v-if="selectSearchTab" @mouseleave="selectMouseover" @click.stop="">
      <p class="_css-select-search">
        <i class="search-icon icon-interface-search"></i>
        <el-input
          class="search-input components-search-input"
          placeholder="搜索标签"
          v-model.trim="searchTags"
          @input="searchTagsChange"
          clearable>
        </el-input>
        <!-- <input class="_css-search-tab" type="text" placeholder="搜索标签" > -->
      </p>
      <div class="_css-select-list" v-if="serachChangeTagsList.length>0">
        <el-checkbox-group
          v-model="searchCheckList"
          v-for="(item,index) in serachChangeTagsList"
          :key="index"
          @change="searchCheckChange">
          <el-checkbox :label="item.bct_guid">{{item.bct_name}}</el-checkbox>
        </el-checkbox-group>
      </div>
      <div class="_css-select-list _css-search-tag" v-else>暂无此标签</div>
    </div>

    <div class="_css-input" style="display:none">
      <!-- 更换缩略图 -->
      <input
        type="file"
        id="id_Modelthumbnail"
        accept="image/*"
        @click.stop="_stopPropagation($event)"
        @change="on_Modelthumbnail_change()"
      />
      <!-- 更换族文件 -->
      <input
        type="file"
        id="id_ModelFile"
        accept="*"
        @click.stop="_stopPropagation($event)"
        @change="on_ModelFile_change()"
      />
      <!-- 上传族文件 -->
      <input
        type="file"
        id="id_uploadFamilyFile"
        accept="*"
        multiple="multiple"
        @click.stop="_stopPropagation($event)"
        @change="on_uploadFamilyFile_change()"
      />
      <!-- 添加附件 -->
      <input
        type="file"
        id="id_uploadAttachment"
        accept="*"
        multiple="multiple"
        @click.stop="_stopPropagation($event)"
        @change="on_uploadAttachment_change()"
      />

      <!-- 上传属性 -->
      <input
        type="file"
        id="id_branchAttr"
        accept="*"
        multiple="multiple"
        @click.stop="_stopPropagation($event)"
        @change="on_branchAttr_change()"
      />
      <!-- 上传模型 -->
      <input
        type="file"
        id="id_uploadModel"
        accept="*"
        multiple="multiple"
        @click.stop="_stopPropagation($event)"
        @change="on_uploadModel_change()"
      />
    </div>
    <div class="_css-library-sort" v-if="clickSortShow" :style="getSortStyle()">
      <p @click.stop="handelClickSortFun('asc')">更新时间正序</p>
      <p @click.stop="handelClickSortFun('desc')">更新时间倒序</p>
    </div>
  </div>
</template>

<script>
  import compsFileBrowsers from "@/components/CompsFile/compsFileBrowsers";
  import '@/assets/css/compsLibrary.css';

  export default {
    //构件库分类管理
    name: "CompsComponentLibrary",
    components : {
      compsFileBrowsers
    },
    props:{

      // 注入的类
      // -------
      injectclass:{
        type: String,
        required: false
      }

    },
    data() {
      return {
        m_injectclass:undefined,
        hoverTreeLeaf: false,
        loadTreeResolve: null,
        clickTreeNodeCode: null,
        batchList: [],  //  分类列表数据
        famileFileList: [],  // 族文件列表数据
        batchDetailNum: '', // 分类明细
        batchDetailSize: '',
        batchDetailTime: '',
        LibraryList: [], // 族文件列表数据
        oldbcc_guid_click: '',
        tabLaberArr: [],  // 族标签数组
        librarySvg: require('../../assets/images/library.svg'),
        shortcutSvg: require('../../assets/images/interface-shortcut.svg'),
        itemSvg: require('../../assets/images/test.png'),
        dataUrl: '',
        componentsSearch: '',//搜索
        listItemSelectState: [],//族文件列表 当前选中的文件的索引
        multipleImgArr: [],  // 族文件详情图片存放
        multipleNameArr: [],  // 族文件详情名称存放
        componentDetailsShow: -1,//0为"分类详情" 1为"已选择"
        detailsForm: {//分类详情
          name: '',
          desc: '',
        },
        tabPanToggleState: 0,//“构件详情”选项卡切换
        selectTabPanData: {
          information: {
            title: '',
            desc: '',
          }
        },
        libraryLabelScreenState: false,//族标签筛选框状态
        hasSelectLibraryLabelData: [],//已选择的族标签的索引
        currentSelectClassify: null,//当前选中的分类文件的索引
        batchSelectionState: false,//批量选择功能是否开启
        addNewLibraryState: false,//新建构件库弹窗状态
        newLibraryName: '',//新建构件库的名称
        newLibraryDesc: '',//新建构件库的描述

        currentTreeNodeData: {//当前操作的树节点的数据
          node: {},
          data: {}
        },
        initTreeNodeData: {//初始化时的树节点的数据
        },
        childTreeNode: {},
        parentTreeNodeData: {},//当前点击的叶子节点的父节点数据
        defaultProps: {
          children: 'children',
          label: 'bcc_name',
          isLeaf: 'leaf'
        },

        OrganizeId: '',
        Token: '',
        UserId: '',
        file_bcfa_guid: '',
        multiplefile_bcfa_guid: [],
        multipleNum: null,  //0为单选  1为多选
        fileChange: null,  // true 是移动   false是创建快捷方式
        file_bcc_guid: '',
        imgpathFile: '',
        bcc_guidFile: '',
        bcc_codeFile: '',
        selectFileItem: null,
        CompsFileBrowser_data: {
          visible: false, // 是否显示 CompsFileBrowser 组件
          dataSource: [],
          organizeId: '',
          title: "",
          title_left:'移动',
          title_right:'到'
        },
        searchCheckList: [], // 选择标签
        selectSearchTab: false,  // 添加标签
        searchCheckArr: [],
        checkedItem: null, // 储存点击的当前族文件item
        willdoubleclick:false,// 判断是否双击
        File_bct_guid: '',
        attachmentList: [],  // 附件
        attributeList: [] , // 属性
        addNewCategories: false,  // 添加新分类
        addNewCategoriesName: '新建分类', // 新建分类
        diasabledInput: false,
        creatingparentCode: '',
        sortMenu: {
          top: 0,
          left: 0
        },
        clickSortShow: false,
        clickSortMethod: 'asc',
        downFilePath: '',  // 下载族文件src
        inside_bcfa_userid:'',//获取打开构件userid
        inside_bcfa_name:'',//获取打开构件名称
        isModelShow: '',  // 查看 3D 模型   or  上传模型
        attributeListSelect: [],
        attrValue: '',
        attributeArr: null,
        nodeAll: null,
        editMessage:true,
        selectTabAllMess: null,
        hover_bcfa_guid: null,
        hover_bcfa_userid: null,
        hover_filepath : null,
        tempDelTabArr: [],
        tempAddTabArr: [],
        typemenu_css: {
          top:0,
          left:0
        },
        hoverTreebcc_guid: '',
        treerenameModel: '',
        treeRenameChange: false,
        clickcode: 0,
        lookModelState: false,
        modelIframeSrc: '',
        iframeModelId: '',  // 查看3D模型modelID
        iframeProId:'',  // 查看3D模型projectID
        iframeName: '',
        searchTags: '',
        serachChangeTagsList: [],
        defaultExpandedKeys:[],
        Allchecked:false,//全选操作删除和下载
      }
    },
    filters: {
      flttimeshorter(inputtime) {
        let _this = this;
        if (!inputtime || inputtime.trim().length == 0) {
          return inputtime;
        }
        if (inputtime.length >= "2019-09-16T11:14".length) {
          return inputtime.substr(0, "2019-09-16T11:14".length).replace("T", " ");
          // return inputtime.substr(0, 10);
        }
      }
    },
    created() {
      this.getRelatedParameters();
      this.dataUrl = this.$configjson.webserverurl

    },
    mounted() {
      this.getAllTabFun();
      this.UserId = this.$staticmethod.Get("UserId")

      // props => data
      // -------------
      this.m_injectclass = this.injectclass;
    },
    methods: {
      reloadMoreModel(_projectId,_modelId) {
          document.getElementById('model-more').innerHTML = "";
          
          let model = null
          let api = null
          let obj = {
              modelID: _modelId,//模型id
              projectID: _projectId,//项目id
              versionNO: '',//版本 ""为最新版本
              viewID: '',//指定视图id，""为默认视图  model-more
              DOM: document.querySelector('.model-more-class')//渲染容器（请提前确定容器含有宽高）
          }
          model = window.model = new window.bim365.BIMModel(obj)
          model.load()
          //model.load 之后实例化BIM365API 请在load后获取BIM365API
          api = model.BIM365API
          api.Events.finishRender.on('default',()=>{//监听模型加载完成事件
              // console.log('模型加载完成')
              // document.querySelector('.example-model-load').remove();
              document.getElementById('viewCube').remove();

          })
         
      },
      // 返回到项目列表
      backtolist() {
        var _this = this;
        window.location.href = `${window.bim_config.hasRouterFile}/#/Home/Boot/${_this.$staticmethod.Get("Token")}`;
      },
      // 文件点击事件
      // -----------
      func_emitout(ev, item) {
        // var _this = this;
        // _this.$emit("onitemclick", item);
        

      // 判断类型，决定预览方式
      // --------------------
      var _this = this;
      var extnamewithdot = item.originname.substr(item.originname.lastIndexOf('.')).toLowerCase();

      // 所有 office 类型
      // ---------------
      var allofficeext = [
        '.docx'
        ,'.doc'
        ,'.pptx'
        ,'.ppt'
        ,'.xlsx'
        ,'.xls'
        ,'.png'
        ,'.jpg'
        ,'.gif'
        ,'.bmp'
        ,'.jpeg'
      ];

      // dwg
      // ---
      //debugger;
      var downloadurl = window.bim_config.webserverurl + item.bcf_path;
      var url_iframe_all;
      if ('.dwg' == extnamewithdot) {
        _this._docviewtype = 'dwg';
        url_iframe_all = `${
          _this.$configjson.dwgurl
        }/Home/Index2?dwgurlcfg=${encodeURIComponent(downloadurl)}&name=${
          item.originname
        }`;
      } else if (allofficeext.indexOf(extnamewithdot) >= 0) {
        _this._docviewtype = 'office';
        url_iframe_all =
          _this.$staticmethod.computeViewUrl(downloadurl, item.originname);
      } else {
        _this.$message.warning('暂不支持预览该类型文件');
        return;
      }

      // set value to open preview frame
      // -------------------------------
      // _this.extdata._idocviewurl = 'about:blank';
      // _this.extdata._idocviewurl = url_iframe_all;
      // _this.extdata._show_idocview = true;
      },

      // 获取注入类对象
      // -------------
      getExtendClass(){
        var _this = this;
        var _c = {};
        if (_this.m_injectclass) {
          _c[_this.m_injectclass] = true;
        }
        return _c;
      },

      mouseleaveLeaf(data,$event){
        this.$set(data,'nodeleafShowState',false)
      },
      mouseoverLeaf(data,$event){
        this.$set(data,'nodeleafShowState',true)
      },
      leafMoreClick(event,data,node){
        let _this = this
        _this.clickAll()
        _this.treerenameModel = node.label
        // if(data.nodeState){
          let bcrect = event.target.getBoundingClientRect();
          _this.typemenu_css.left = 270;
          _this.typemenu_css.top = bcrect.y - 70;
          this.hoverTreebcc_guid = data.bcc_guid
          this.hoverTreeLeaf = true
        // }
      },
      getEditStyle(){
        let _this = this;
        let _s = {};
        _s["left"] = _this.typemenu_css.left + 0 + "px";
        _s["top"] = _this.typemenu_css.top + 0 + "px";
        return _s;
      },
      // 重命名
      treeRename(){
        this.treeRenameChange = true
      },
      sureRenameFun(){
        // console.log(this.batchList,this.treerenameModel)
        let _this = this;
        if(_this.batchList.findIndex(item => item.bcc_name == _this.treerenameModel) >= 0){
          this.$message.error('文件名称已存在')
          return
        }
        
        if(this.treerenameModel.length<1){
          this.$message.error('文件名称不能为空')
          return
        }

        _this.$axios({
          method: 'post',
          url: `${this.$configjson.webserverurl}/api/BIMComp/BIMCompCategory/ModifyCategoryName`,
          data: _this.$qs.stringify({
            Token: this.Token,
            bcc_organizeId: this.OrganizeId,
            bcc_name: this.treerenameModel,
            bcc_guid:this.hoverTreebcc_guid,
          })
        }).then(res=>{
          if(res && res.data.Ret == 1){
            // this.clickcode = 1
            this.reloadTreeFun();
            // this.childTreeNode.node.expand();
            this.$message.success('修改成功')
            this.clickAll()
          }
        }).catch(err=>{
          console.log(err)
        })

      },
      // 删除族文件
      treeDeltel(){
        let _this = this;
        _this.$axios({
          method: 'post',
          url: `${this.$configjson.webserverurl}/api/BIMComp/BIMCompCategory/DeleteCategory`,
          data: _this.$qs.stringify({
            Token: this.Token,
            bcc_guid:this.hoverTreebcc_guid,
          })
        }).then(res=>{
          if(res && res.data.Ret == 1){
            if(res.data.Data.IsUsing){
              this.$message.warning('当前分类存在族文件')
              return
            }else{
              this.reloadTreeFun();
              this.$message.success('删除成功')
              this.clickAll()
            }
          }
        }).catch(err=>{
          console.log(err)
        })
      },
      reloadTreeFun() {
        this.initTreeNodeData.node.childNodes = []
        this.loadTreeNode(this.initTreeNodeData.node, this.initTreeNodeData.resolve)
      },
      clickAll() {
        this.addNewLibraryState = false;
        this.selectSearchTab = false;
        this.clickSortShow = false;
        this.hoverTreeLeaf = false;
        this.treeRenameChange = false;
        this.treerenameModel = ''
        // this.$refs.libraryTree.setCurrentKey(0)
        // document.getElementsByClassName('is-current').style.background="#f00"
        // this.currentTreeNodeData={ //操作的树节点的数据
        //   node: {},
        //   data: {}
        // }
      },
      inputblur() {
        if(this.addNewCategoriesName.length<1){
          this.$message.error('名称不能为空，请重新输入');
          this.$nextTick((x)=>{
            this.$refs.addinputs.focus();
          })
          return;
        }
        let data = {
          "Token": this.Token,
          "bcc_organizeId": this.OrganizeId,
          "parentcode": this.creatingparentCode,
          "bcc_name": this.addNewCategoriesName,
          "bcc_description": ''
        }
        this.addCategory(data)
      },
      handelClickCreatingCategories(){
        // 创建分类
        this.addNewCategories = true
        this.$nextTick((x)=>{
          this.$refs.addinputs.focus();
        })
      },
      getRelatedParameters() {
        document.body.style.overflow = 'auto';
        this.Token = this.$staticmethod.Get("Token");

        let _o = window.location.href;

        // 珠三角平台项目要修改全线，判断是珠三角平台，机构ID为0   
        if(_o.indexOf('guangdongwater') != -1 || _o.indexOf('zsj') != -1 ){  // 则是珠三角环境
          this.OrganizeId = '00000000-0000-0000-0000-000000000000';   //  _OrganizeId 取得机构的OrganizeId   在boot.vue中存储的
          this.CompsFileBrowser_data.organizeId = '00000000-0000-0000-0000-000000000000';
        }else{
          this.OrganizeId = sessionStorage['_OrganizeId'];   //  _OrganizeId 取得机构的OrganizeId   在boot.vue中存储的
          this.CompsFileBrowser_data.organizeId = sessionStorage['_OrganizeId'];
        }
        
      },

      loadTreeNode(node, resolve) {
        // console.log(node)
        let data = node.data
        // console.log(data)
        let _this = this
        this.nodeAll = node;
        this.loadTreeResolve = resolve

        //首次加载  获取顶级节点
        if (node.level === 0) {
          this.initTreeNodeData.node = node;
          this.initTreeNodeData.resolve = resolve;
          this.getCategoies('',resolve);
          // this.getCompsFamilyFile('',resolve,this.File_bct_guid);
        }

        //打开父节点
        if (node.level >= 1) {
          this.childTreeNode.node = node
          this.childTreeNode.resolve = resolve
          //传父节点的bcc_code
          this.childTreeNode.node.childNodes = [];
          this.file_bcc_guid = node.data.bcc_guid
          let bcc_guid = node.data.bcc_guid
          let likepara = node.data.bcc_code;
          this.clickTreeNodeCode = node.data.bcc_code
          this.getCategoies(likepara,resolve);
          // this.getCompsFamilyFile(bcc_guid,resolve,this.File_bct_guid);
        }
      },

      //获取构件库列表 及 子集分类数据
      getCategoies(pcode,resolve) {
        let _this = this
        this.$axios.get(`${this.$configjson.webserverurl}/api/BIMComp/BIMCompCategory/GetCategoies`,{
          params: {
            pcode: pcode,//父编码
            organizeId: this.OrganizeId,
          }
        }).then(res=>{
          let data = null
          if (res.data.Ret == 1) {

            data = res.data.Data;
            resolve(data);
            _this.batchList = data
          }
        }).catch(err=>{

        })
      },

      // 获取族文件列表的内容
      getCompsFamilyFile(bcc_guid,resolve,bct_guids) {
        let _this = this;
        _this.$axios({
          method: 'post',
          url: `${this.$configjson.webserverurl}/api/BIMComp/BIMCompFamily/GetComps_Post`,
          data: _this.$qs.stringify({
            bcc_guid: bcc_guid,   //bcc_guid,'3fb6d16f-6c7e-4327-8220-3591ad0bbd42'
            bct_guids: bct_guids,
            sortfield: "",
            Keyword:this.componentsSearch,
            sorttype: _this.clickSortMethod,
            Token: this.Token,
          })
        }).then(res=>{
          if(res && res.data.Ret == 1){
            let data =  res.data.Data
            _this.LibraryList = data
            _this.Allchecked = false
          }
        })
      },
      //缩略图展示
      GetModelImg(imgpath,bcfa_base64image){
          if(imgpath != ''){
            return this.dataUrl+imgpath
          }else if(imgpath == '' && bcfa_base64image){
            return 'data:image/png;base64,'+ bcfa_base64image
          }else{
            return this.itemSvg
          }
      },


      //点击某个节点
      handleNodeClick(data,node,current) {
        let _this = this
        this.clickAll()
        // this.$set(data,'nodeleafShowState',true)
        _this.hoverTreeLeaf = false;

        // console.log(data)
        // console.log(node)
        _this.file_bcc_guid = data.bcc_guid;
        //点击左侧族类树 或点击某个文件夹 则需要展示“分类详情”
        this.componentDetailsShow = 0;

        //点击左侧族类树时，需要清除已选择的构件
        this.listItemSelectState = [];
        this.multipleNameArr = [];
        //清除选中的分类文件
        //这里需要赋值为-1
        //防止如果在这之前有点击族文件 右侧“已选择”会展示不正确信息
        this.currentSelectClassify = -1;
        _this.clickSortMethod = "asc"

        this.currentTreeNodeData.data = data;
        this.currentTreeNodeData.node = node;
        _this.creatingparentCode = node.data.bcc_code;
        _this.likeparaFlie = node.data.bcc_code;
        _this.bcc_guidFlie = node.data.bcc_guid

        // this.getCategoies(_this.likeparaFlie,this.loadTreeResolve);
        this.getCompsFamilyFile(_this.bcc_guidFlie,this.loadTreeResolve,this.File_bct_guid);

        _this.batchDetailNum = data.ItemCount
        _this.batchDetailSize = _this.bytesToSize(data.TotalSize)
        _this.batchDetailTime = data.bcc_updatetime
        _this.detailsForm.name = data.bcc_name
        _this.detailsForm.desc = data.bcc_description
        _this.multiplefile_bcfa_guid=[]

      },

      //当节点展开时
      onNodeExpand(data,node,current) {
        this.$set(data,'nodeState',true)
        if(!this.defaultExpandedKeys.includes(data.bcc_guid)){
          this.defaultExpandedKeys.push(data.bcc_guid)
        }
      },

      //当节点关闭时
      onNodeCollapse(data,node,current) {
        this.$set(data,'nodeState',false)
        if(this.defaultExpandedKeys.includes(data.bcc_guid)){
          let index = this.defaultExpandedKeys.findIndex(x=>x === data.bcc_guid)
          this.defaultExpandedKeys.splice(index,1)
        }
      },

      //新建构件族分类
      addCategory() {
        let parentCode = '';
        //如果currentTreeNodeData的属性为空，说明是添加根节点
        if (Object.keys(this.currentTreeNodeData.node).length == 0) {
          //如果是根节点 parentCode传空
          parentCode = '';
        } else {
          //获取父节点的bcc_code
          parentCode = this.currentTreeNodeData.data.bcc_code;
        }

        let data = null
        // if(dataRes){
        //   data = dataRes
        // }else{}
        data = {
          "Token": this.Token,
          "bcc_organizeId": this.OrganizeId,
          "parentcode": parentCode,
          "bcc_name": this.newLibraryName,
          "bcc_description": this.newLibraryDesc
        };
        this.$axios.post(`${this.$configjson.webserverurl}/api/BIMComp/BIMCompCategory/AddCategory`,
          this.$qs.stringify(data)).then(res=>{
          // console.log(res);
          if (res.data.Ret == 1) {
            if (!res.data.Data.IsRepeat) {

              //如果parentCode为空 则传根级node，否则传当前选中的node
              if (parentCode == '') {
                this.initTreeNodeData.node.childNodes = [];
                this.loadTreeNode(this.initTreeNodeData.node, this.initTreeNodeData.resolve)
              } else {
                this.loadTreeNode(this.childTreeNode.node, this.childTreeNode.resolve)
              }

              this.$message.success('创建成功');
              this.addNewLibraryDialogClose();
              // if(dataRes){
              //   this.diasabledInput = true;
              //   this.addNewCategories = false;
              //   this.addNewCategoriesName = "新建分类"
              // }
            } else {
              this.$message.error('构件库名称重复！请重新输入');
              this.diasabledInput = false;
              this.$nextTick((x)=>{
                this.$refs.addinputs.focus();
              })
              // this.addNewCategories = false;
              // this.addNewCategoriesName = ""
            }
          } else {
            this.$message.error('创建失败，请稍后再试');

          }
        })

      },

      //切换 新建构件库弹窗状态
      toggleAddNewLibraryDiv() {
        this.addNewLibraryState = !this.addNewLibraryState;
        this.newLibraryName = ''
      },

      //批量选择功能
      batchSelectionLibrary() {
        this.batchSelectionState = !this.batchSelectionState;
        if(this.batchSelectionState == false){
          this.Allchecked = false
        }
        //批量选择功能开启或关闭的时候，要还原右侧详情的内容为提示信息
        this.componentDetailsShow = -1;
        //需要清除已选择的族文件
        this.listItemSelectState = [];
        //清除选中的分类文件
        this.currentSelectClassify = null;
        this.multipleNameArr = []
      },
      //批量选择功能
      AllbatchSelectionLibrary() {
        this.batchSelectionState = false
        //批量选择功能开启或关闭的时候，要还原右侧详情的内容为提示信息
        this.componentDetailsShow = -1;
        //需要清除已选择的族文件
        this.listItemSelectState = [];
        //清除选中的分类文件
        this.currentSelectClassify = null;
        this.multipleNameArr = []
        this.multipleImgArr = []
        this.listItemSelectState = []
        this.multiplefile_bcfa_guid = []
        if(this.Allchecked == true){
          this.batchSelectionState = true
          let eq = 0
          for (let i = 0; i < this.LibraryList.length; i++) {
            const ele = this.LibraryList[i];
            this.listItemSelectState.push(eq++);
            this.multipleImgArr.push(ele.imgpath)
            this.multiplefile_bcfa_guid.push(ele.bcfa_guid)
            this.multipleNameArr.push(ele.bcfa_name)
          }
          this.componentDetailsShow = 1;
        }
      },

      //点击选中分类文件
      selectClassifyItem(eq,item) {
        let _this = this;
        // console.log(eq)
        // console.log(item)
        if (this.willdoubleclick == false){
          // 等待第二次点击
          this.willdoubleclick = true;
          setTimeout(()=>{
            this.willdoubleclick = false
            //清空已选择的族文件
            this.listItemSelectState = [];

            //如果点击的分类文件已选中
            if (this.currentSelectClassify == eq) {
              //取消选中该文件
              this.currentSelectClassify = null;
              //清空右侧详情展示
              this.componentDetailsShow = -1;
            } else {
              //选中该文件
              this.currentSelectClassify = eq;
              this.selectFileItem = item
              //展示分类详情
              this.componentDetailsShow = 0;
              _this.batchDetailNum = item.ItemCount
              _this.batchDetailSize = _this.bytesToSize(item.TotalSize)
              _this.batchDetailTime = item.bcc_updatetime
              _this.detailsForm.name = item.bcc_name
              _this.detailsForm.desc = item.bcc_description
            }
          }, 400);
        } else {
          // 双击进入子集
          _this.doubleclickItem(item)
        }
      },
      // 双击分类进入文件夹’
      doubleclickItem(item){
        let _this = this;
        _this.creatingparentCode = item.bcc_code
        this.$axios.get(`${this.$configjson.webserverurl}/api/BIMComp/BIMCompCategory/GetCategoies`,{
          params: {
            pcode: item.bcc_code,//父编码
            organizeId: this.OrganizeId,
          }
        }).then(res=>{
          if (res.data.Ret == 1 &&  res.data.Data) {
            let data = res.data.Data;
            _this.batchList = data
          }
        }).catch(err=>{

        })
        _this.bcc_guidFlie = item.bcc_guid
        // this.getCategoies(_this.likeparaFlie,this.loadTreeResolve);
        this.getCompsFamilyFile(_this.bcc_guidFlie,this.loadTreeResolve,this.File_bct_guid);
      },
      // 容量单位的换算
      bytesToSize(bytes) {
          if (bytes === 0) return '0 B';
          let k = 1024, // or 1024
              sizes = ['B', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'],
              i = Math.floor(Math.log(bytes) / Math.log(k));

        return (bytes / Math.pow(k, i)).toPrecision(3) + ' ' + sizes[i];
      },
      // componentsSearchChange搜索
      componentsSearchChange(val){
        // console.log(val)
        // console.log(value.srcElement.value)
        this.componentsSearch = val
        this.getCompsFamilyFile(this.bcc_guidFlie,this.loadTreeResolve,this.File_bct_guid);
      },
      //选择族标签
      handleSelectLibraryLabel(e) {
        let _this = this
        let sign = e.target.getAttribute('data-sign');
        if (sign != '' && sign != null) {
          let eq = this.hasSelectLibraryLabelData.indexOf(sign);
          if (eq == -1) {
            this.hasSelectLibraryLabelData.push(sign);
          } else {
            this.hasSelectLibraryLabelData.splice(eq,1);
          }
        }

        _this.File_bct_guid = []
        this.hasSelectLibraryLabelData.forEach(item=>{
          _this.File_bct_guid.push(this.searchCheckArr[item].bct_guid)
        })
        _this.File_bct_guid = _this.File_bct_guid.toString()
        if(_this.bcc_guidFlie){
          this.getCompsFamilyFile(_this.bcc_guidFlie,this.loadTreeResolve,_this.File_bct_guid);
        }else{
          _this.$message.warning('请先选中构件')
          this.hasSelectLibraryLabelData = []
        }
      },
      //右侧详情部分切换
      toggleRightDetailsShow(eq) {
        if (this.batchSelectionState && eq == 0) {
          this.$message({
            showClose: true,
            type: 'error',
            message: '构件批量选择操作已开启，如需查看分类详情，请退出批量操作',
            duration: 7000
          });
          // return
        }
        this.componentDetailsShow = eq;
      },

      //单击选中一个族文件
      selectListContainerItem(eq,item) {
        //清空选中的分类文件
        this.selectSearchTab = false
        this.tabPanToggleState = 0;
        this.currentSelectClassify = null;
        this.editMessage = true
        this.checkedItem = item
        let indexNum = this.listItemSelectState.indexOf(eq);
        let _this = this
        //如果 批量选择未开启
        if (!this.batchSelectionState) {
          //清空数组 取消选择
          this.listItemSelectState = [];
          if (indexNum != -1) {
            //当取消选择某个构件的时候，要还原右侧详情的内容为提示信息
            this.componentDetailsShow = -1;
          } else {
            //如果点击的族文件 没被选择  则追加进数组  使其选中
            this.listItemSelectState.push(eq);
            //单击某个构件 则需展示“已选择”
            this.componentDetailsShow = 1;

            // 点击族文件 请求接口
            _this.libraryItemClick(item)

          }
        } else {
          //如果批量选择功能开启
          this.oldbcc_guid_click = item.bcc_guid
          if (indexNum != -1) {
            //如果点击选择的族文件已经被选择 则删除其所在数组的索引 取消选择该文件
            this.listItemSelectState.splice(indexNum,1);
            if(this.listItemSelectState.length!=this.LibraryList.length){
              this.Allchecked = false
            }
            this.multipleImgArr.splice(indexNum,1);
            this.multipleNameArr.splice(indexNum,1)
            this.multiplefile_bcfa_guid.splice(indexNum,1);
            if (this.listItemSelectState.length == 0) {
              //如果已选择的族文件数组为空 则清空右侧详情列表
              this.componentDetailsShow = -1;
            }
          } else {
            //如果点击的族文件 没被选择  则追加进数组  使其选中
            this.listItemSelectState.push(eq);
            if(this.listItemSelectState.length==this.LibraryList.length){
              this.Allchecked = true
            }
            this.multipleImgArr.push(item.imgpath)
            this.multiplefile_bcfa_guid.push(item.bcfa_guid)
            this.multipleNameArr.push(item.bcfa_name)
          }
          this.componentDetailsShow = 1;
          // if(item.bcfa_userid == this.UserId || this.hover_bcfa_userid == null){
          // }else{
          //   this.$message.warning('只有上传者才能选择')
          // }
        }

        // if (this.listItemSelectState == eq) {
        //   this.listItemSelectState = -1;
        //   //当取消选择某个构件的时候，要还原右侧详情的内容为提示信息
        //   this.componentDetailsShow = -1;
        // } else {
        //   this.listItemSelectState = eq;
        //   //单击某个构件 则需展示“已选择”
        //   this.componentDetailsShow = 1;
        // }
      },
      // 点击关闭右侧详情

      closeRightDetails(){
        this.componentDetailsShow = 0
        this.listItemSelectState=[]
      },
      closeRightLookModelState(){
        this.lookModelState = false
      },
      // 点击族文件请求==单选
      libraryItemClick(item) {
        let _this = this;
        _this.file_bcfa_guid = item.bcfa_guid
        _this.file_bcc_guid = item.bcc_guid
        _this.searchCheckList = []
        this.attributeList = []
        this.attributeListSelect = []
        this.attributeArr = null
        this.attrValue = ""
        this.$axios.get(`${this.$configjson.webserverurl}/api/BIMComp/BIMCompFamily/GetComp`,{
          params: {
            bcfa_guid: item.bcfa_guid  // '99ec88b2-7f7e-44ba-ba97-239cf4f57b6e'   item.bcfa_guid
            // bimcomposerId: _this.$staticmethod._Get("bimcomposerId")
          }
        }).then(res=>{
          // console.log(res);
          if (res.data.Ret == 1 && res.data.Data != null) {
            let dataList = res.data.Data;
            _this.attachmentList = dataList.Attachments
            _this.tabLaberArr = dataList.Tags;
            _this.tabLaberArr.forEach(val=>{
              _this.searchCheckList.push(val.bct_guid)
            })
            _this.selectTabAllMess = dataList.BaseData
            _this.selectTabPanData.information.title = dataList.BaseData.bcfa_name
            _this.selectTabPanData.information.desc = dataList.BaseData.bcfa_description
            _this.imgpathFile = dataList.BaseData.imgpath
            _this.downFilePath = dataList.BaseData.filepath
            _this.inside_bcfa_userid = dataList.BaseData.bcfa_userid
            _this.inside_bcfa_name = dataList.BaseData.bcfa_name
            if(dataList.Modelid){
              // _this.$refs.modelRefTime.innerHTML = "";
              _this.isModelShow =  '查看';  // 查看 3D 模型   or  上传模型
              _this.iframeName = dataList.BaseData.bcfa_name;
              _this.modelIframeSrc = `${_this.$configjson.bimviewerurl}?projectId=${dataList.BaseData.BIMComposerID}&model=${dataList.Modelid}&ver=`
              setTimeout(() => {
                _this.reloadMoreModel(dataList.BaseData.BIMComposerID,dataList.Modelid);
              }, 100);
            }else{
              _this.isModelShow =  '上传'
            }
            let map = new Map();
            if(dataList.Properties.length>0){

              _this.attributeArr = dataList.Properties
              let newArr = []
              let _arrList = []
              dataList.Properties.forEach(item => {
                for(let key1 in item){
                  newArr.push({value:key1,label:key1})
                }
              })
              _arrList = newArr
              _this.attributeListSelect = _arrList
              // console.log(_this.attributeListSelect)

              // console.log(_this.attrValue)
              if(_this.attrValue == ""){
                _this.attrValue = _this.attributeListSelect[0].value
                this.selectAttrChange(this.attributeListSelect[0].label)

                let selectArr = []
                _this.attributeArr.forEach((item,key) => {
                  if(item[_this.attrValue]){
                    selectArr = item[_this.attrValue]
                  }
                })
                selectArr.forEach(item => {
                  map.has(item.bcfapc_guid) ? map.get(item.bcfapc_guid).push(item) : map.set(item.bcfapc_guid, [item]);
                })
                _this.attributeList = seattributeListlectArr
                _this.selectAttrChange(_this.attributeList[0])

              }
            }
          }
        }).catch(err=>{

        })
      },
      //“构件详情”选项卡切换
      handleTabPanToggle(e) {
        let sign = e.target.getAttribute('data-sign');
        // console.log(sign)
        if (sign != '' && sign != null) {
          this.tabPanToggleState = sign;
        }
        if(sign == 1){
          this.selectAttrChange(this.attributeListSelect[0].label)
        }
      },

      //关闭 新建族库 弹窗
      addNewLibraryDialogClose() {
        this.addNewLibraryState = false;
        this.newLibraryName = '';
        this.newLibraryDesc = '';
      },
      // 族文件操作==更新缩略图
      changeModelthumbnail(){
        let dom = document.getElementById("id_Modelthumbnail");
        dom.value = "";
        dom.click();
        // if(this.inside_bcfa_userid == this.UserId || this.inside_bcfa_userid == null){
        //   let dom = document.getElementById("id_Modelthumbnail");
        //   dom.value = "";
        //   dom.click();
        // }else{
        //   this.$message.warning('只有上传者才有操作权限')
        // }

      },
      // 族文件更新缩略图上传==input
      on_Modelthumbnail_change() {
        let _this = this;

        // 调用上传图片方法（其内部调用接口）
        let dom = document.getElementById("id_Modelthumbnail");
        // 显示loading
        // let _LoadingIns = _this.$loading({
        //   text: "操作中",
        //   target: document.getElementById("id_middle_scrollable")
        // });

        _this.uploadimg(
          dom,
          function(x) {
            //debugger;
            if (x.status == 200) {
              if (x.data.Ret > 0) {
                if (x.data.Data) {
                }
              } else {
              }
            } else {
            }
            // _LoadingIns.close();
          },
          function(x) {
            // _LoadingIns.close();
          }
        );
      },
      uploadimg(ele, successcb, errorcb) {
        let _this = this;
        if (!ele || !ele.files || !ele.files.length || ele.files.length == 0) {
          return;
        }
        let File = ele.files[0];
        let fd = new FormData();
        fd.append("Token", this.Token);
        fd.append("bcfa_guid", _this.file_bcfa_guid);
        fd.append("FileUploading", File);
        let config = {
          headers: {
            "Content-Type": "multipart/form-data"
          }
        };
        _this.$axios
          .post(
            `${this.$configjson.webserverurl}/api/BIMComp/BIMCompFamily/UploadImg`,
            fd,
            config
          )
          .then(x => {
            if (successcb) {
              this.getCompsFamilyFile(this.bcc_guidFlie,this.loadTreeResolve,this.File_bct_guid);
              this.selectClassifyItem(this.currentSelectClassify,this.selectFileItem)
            } else {
              console.warn("not specify successcb");
            }
          })
          .catch(x => {
            if (errorcb) {
              // errorcb(x);
            } else {
              console.warn("not specify errorcb");
            }
          });
      },
      // 族文件操作==更新族文件
      changeModelFile(){
        let domFile = document.getElementById("id_ModelFile");
        domFile.value = "";
        domFile.click();
        // if(this.inside_bcfa_userid == this.UserId || this.inside_bcfa_userid == null){
        //   let domFile = document.getElementById("id_ModelFile");
        //   domFile.value = "";
        //   domFile.click();
        // }else{
        //   this.$message.warning('只有上传者才有操作权限')
        // }
      },
      // 族文件操作-更新族文件上传input
      on_ModelFile_change() {
        let _this = this;
        let dom = document.getElementById("id_ModelFile");
        let url = `${this.$configjson.webserverurl}/api/BIMComp/BIMCompFamily/UpdateCompFile`;
        if (dom.files && dom.files.length > 0) {
          // 调用上传文件接口，得到fileid
          _this.uploadfile(
            dom,
            url,
            function(x) {
              if (x.status == 200) {
                // console.log(x)
              } else {
                console.error(x);
              }
            },
            function(x) {
              console.error(x);
            }
          );
        }

      },
      uploadfile(ele,url, successcb, errorcb) {
        let _this = this;
        if (!ele || !ele.files || !ele.files.length || ele.files.length == 0) {
          return;
        }
        let File = ele.files[0];
        let fd = new FormData();
        fd.append("Token", this.Token);
        fd.append("bcfa_guid", _this.file_bcfa_guid);
        fd.append("key", File);
        let config = {
          headers: {
            "Content-Type": "multipart/form-data"
          }
        };
        let _file = ele.files;
        for(let i = 0;i<_file.length;i++){
          // 上传类型判断
          let imgName = _file[i].name;
          let idx = imgName.lastIndexOf(".");
          if (idx != -1){
              let ext = imgName.substr(idx+1).toUpperCase();
              ext = ext.toLowerCase( );
              if (ext!='rfa'){
                _this.$message.warning('仅支持上传rfa的文件格式');
                return;
              }else{

              }
          }else{

          }
        }
        _this.$axios
          .post(
            url,
            fd,
            config
          )
          .then(x => {
            if (successcb) {
              let _data = x.data.Data
              _this.$message.success('已上传成功');
              this.getCategoies(_this.likeparaFlie,this.loadTreeResolve);
              this.getCompsFamilyFile(_this.bcc_guidFlie,this.loadTreeResolve,this.File_bct_guid);
              this.reloadTreeFun()
              _this.libraryItemClick(_this.checkedItem)
            } else {
              console.warn("not specify successcb");
            }
          })
          .catch(x => {
            if (errorcb) {
              errorcb(x);
            } else {
              console.warn("not specify errorcb");
            }
          });
      },
      // 族文件操作==创建快捷方式
      changeModelshortcut(num){
        // 0为单选  1为多选
        let _this = this;
        _this.multipleNum = num
        _this.CompsFileBrowser_data.title_left = "创建快捷方式"
        this.fileChange = false
        _this.CompsFileBrowser_data.visible = true;
        // if(this.inside_bcfa_userid == this.UserId || this.inside_bcfa_userid == null){         
        //   // 0为单选  1为多选
        //   let _this = this;
        //   _this.multipleNum = num
        //   _this.CompsFileBrowser_data.title_left = "创建快捷方式"
        //   this.fileChange = false
        //   _this.CompsFileBrowser_data.visible = true;
        // }else{
        //   this.$message.warning('只有上传者才有操作权限')
        // }
      },
      /*// 族文件多选==创建快捷方式
      changeModelshortcuts() {
        let _this = this;
        _this.CompsFileBrowser_data.title_left = "创建快捷方式"
        this.fileChange = false
        _this.CompsFileBrowser_data.visible = true;
      },*/
      // 族文件操作==移动
      changeModelMove(num){
        let _this = this;
        _this.multipleNum = num
        this.fileChange = true
        _this.CompsFileBrowser_data.title_left = "移动文件夹"
        _this.CompsFileBrowser_data.visible = true;
        // if(this.inside_bcfa_userid == this.UserId || this.inside_bcfa_userid == null){
        //   let _this = this;
        //   _this.multipleNum = num
        //   this.fileChange = true
        //   _this.CompsFileBrowser_data.title_left = "移动文件夹"
        //   _this.CompsFileBrowser_data.visible = true;
        // }else{
        //   this.$message.warning('只有上传者才有操作权限')
        // }
      },
      hoverContainerItem(index,item){
        this.hover_bcfa_guid = item.bcfa_guid
        this.hover_bcfa_userid = item.bcfa_userid
        this.hover_filepath = item.filepath
      },
      // hover 删除族文件
      hoverChangeModelDel() {
          this.changeModelDel(this.hover_bcfa_guid)
      },
      hoverhandelClickDownFile(bcfa_guid) {
        let downSrc = `${this.$configjson.webserverurl}/api/BIMComp/BIMCompFamily/DownComp?Token=${this.Token}&bcfa_guid=${bcfa_guid}`;
        // console.log(downSrc);
        window.location.href=downSrc
          // this.$axios({
          //   method: 'post',
          //   url: `${this.$configjson.webserverurl}/api/BIMComp/BIMCompFamily/DowloadComp`,
          //   data: this.$qs.stringify({
          //     Token: this.Token,
          //     bcfa_name: bcfa_name
          //   })
          // }).then(res=>{
          //   if(res && res.data.Ret == 1){
          //     let downSrc = `${this.$configjson.webserverurl}` + this.hover_filepath
          //     window.location.href=downSrc
          //   }else{
          //     this.$message.err('请求失败，请稍后重试')
          //   }
          // }).catch(err=>{
          //   console.log(err)
          // })
      },
      // 族文件批量下载
      changeModelClickDownFile(){
        let downFileAll = ''
        for (let i = 0; i < this.multipleNameArr.length; i++) {
          const ele = this.multipleNameArr[i];
          let downFile = this.LibraryList.filter(x => x.bcfa_name == ele)[0].bcfa_guid
          if(downFileAll == ''){
            downFileAll = downFile
          }else{
            downFileAll = downFileAll+','+downFile
          }
        }
        let downSrc = `${this.$configjson.webserverurl}/api/BIMComp/BIMCompFamily/DownCompBulk?Token=${this.Token}&bcfa_guid=${downFileAll}`;
        window.location.href=downSrc
      },
      // 族文件操作==删除
      changeModelDel(params){
        let _bcfa_guid = null
        let _this = this;
        if(params){
          _bcfa_guid = params
        }else{
          _bcfa_guid = _this.file_bcfa_guid
        }
        _this
          .$confirm("确定删除该文件？", "操作确认", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning"
          })
          .then(y => {
            _this.$axios({
              method: 'post',
              url: `${this.$configjson.webserverurl}/api/BIMComp/BIMCompFamily/RemoveComp`,
              data: _this.$qs.stringify({
                Token: this.Token,
                bcfa_guid: _bcfa_guid
                // involedrm: ""
              })
            }).then(res=>{
              if(res && res.data.Ret == 1){
                // this.getCategoies(_this.likeparaFlie,this.loadTreeResolve);
                this.getCompsFamilyFile(_this.bcc_guidFlie,this.loadTreeResolve,this.File_bct_guid);
                this.reloadTreeFun()
                // this.loadTreeNode('', this.initTreeNodeData.resolve)
                _this.$message.success('已成功删除文件');
                this.listItemSelectState = [];
                this.componentDetailsShow=0
              }
            }).catch(err=>{
              console.log(err)
            })
          })
          .catch(y => {});
        // if(this.hover_bcfa_userid == this.UserId || this.hover_bcfa_userid == null){
        // }else{
        //   this.$message.warning('只有上传者才有操作权限')
        // }
      },

      // 族文件多选=删除
      changeModelDels() {
        let _this = this;
        _this
        .$confirm("确定删除选中的文件？", "操作确认", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
        .then(y => {
          _this.$axios({
            method: 'post',
            url: `${this.$configjson.webserverurl}/api/BIMComp/BIMCompFamily/RemoveComps`,
            data: _this.$qs.stringify({
              Token: this.Token,
              bcfa_guids: _this.multiplefile_bcfa_guid.toString(),
              refremove: '2', // 是否连带删除，如果为1，则表示连带删除，对于其中的快捷方式，会删除引用的数据及其它快捷方式，如果不为1，则对于其中的快捷方式，将仅删除此快捷方式
            })
          }).then(res=>{
            if(res && res.data.Ret == 1){
              this.getCategoies(_this.likeparaFlie,this.loadTreeResolve);
              this.getCompsFamilyFile(_this.bcc_guidFlie,this.loadTreeResolve,this.File_bct_guid);
              this.reloadTreeFun()
              this.listItemSelectState = []
              this.multipleNameArr = []
              _this.$message.success('已成功删除文件');
            }
          }).catch(err=>{
            console.log(err)
          })
        })
        .catch(y => {});

      },
      _stopPropagation(ev) {
        if (ev) {
          ev.stopPropagation();
        }
      },
      folderBrowser_oncancel() {
        let _this = this;
        _this.CompsFileBrowser_data.visible = false;
      },
      // 文件夹树打开后，选择文件夹并点击确定
      folderBrowser_onok(nodekey) {
        let _this = this;

        // 移动窗口确定按钮，弹出确定操作提示
        _this.$confirm('确定进行此操作？', '', {
          confirmButtonText:'确认',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(x => {
          // multipleNum = 0为单选 1为多选
          // fileChange: false=创建快捷方式   true=移动
          if(this.fileChange){
            let dataStr = null
            let urlSrc = ''
            if(this.multipleNum == 0){
              dataStr = _this.$qs.stringify({
                Token: this.Token,
                bcfa_guid: _this.file_bcfa_guid,
                bcc_guid: nodekey
              })
              urlSrc = `${this.$configjson.webserverurl}/api/BIMComp/BIMCompFamily/MoveComp`
            }else{
               dataStr = _this.$qs.stringify({
                Token: this.Token,
                bcfa_guids: _this.multiplefile_bcfa_guid.toString(),
                bcc_guid: nodekey,
                oldbcc_guid: this.oldbcc_guid_click
              })
              urlSrc = `${this.$configjson.webserverurl}/api/BIMComp/BIMCompFamily/MoveComps`
            }
            this.MoveCompChange(dataStr,urlSrc)
            this.CompsFileBrowser_data.visible = false
          }else{
            let cutdata = null;
            let cutUrl = ''
            if(this.multipleNum == 0){
              cutdata =  _this.$qs.stringify({
                            Token: this.Token,
                            bcfa_guid: _this.file_bcfa_guid,
                            bcc_guid: nodekey
                          })
              cutUrl = `${this.$configjson.webserverurl}/api/BIMComp/BIMCompFamily/CreateShortcut`

            }else{
              cutdata =  _this.$qs.stringify({
                              Token: this.Token,
                              bcfa_guids: _this.multiplefile_bcfa_guid.toString(),
                              bcc_guid: nodekey
                            })
              cutUrl = `${this.$configjson.webserverurl}/api/BIMComp/BIMCompFamily/CreateShortcutList`
            }
            this.CreateShortcutChange(cutdata,cutUrl)
            // this.CompsFileBrowser_data.visible = false
          }

        }).catch(x => {

        });
      },
      // 点击快捷方式接口请求
      CreateShortcutChange(cutdata,cutUrl){
        let _this = this;
        _this.$axios({
          method: 'post',
          url: cutUrl,
          data:cutdata
        }).then(res=>{
          if(res && res.data.Ret == 1){
            _this.$message.success('已成功创建快捷方式')
            _this.CompsFileBrowser_data.visible = false;
            this.getCompsFamilyFile(this.bcc_guidFlie,this.loadTreeResolve,this.File_bct_guid);
            this.reloadTreeFun();
          }else{
            _this.$message.warning(res.data.Msg)
          }
        }).catch(err=>{
          console.log(err)
        })
      },
      // 点击移动文件接口请求
      MoveCompChange(dataStr,urlSrc) {
        let _this = this

        // let _LoadingIns = _this.$loading({
        //   text: "操作中" //,
        //   //target: document.getElementById("id_datamain")
        // });
        _this.$axios({
          method: 'post',
          url: urlSrc,
          data: dataStr
        }).then(res=>{
          if(res && res.data.Ret == 1){
            this.getCategoies(_this.likeparaFlie,this.loadTreeResolve);
            this.getCompsFamilyFile(_this.bcc_guidFlie,this.loadTreeResolve,this.File_bct_guid);
            this.reloadTreeFun()

            _this.$message.success('已成功移动文件')
            // _this.getCompsFamilyFile(_this.bcc_guidFlie,_this.loadTreeResolve,this.File_bct_guid);
            // _LoadingIns.close();
            _this.CompsFileBrowser_data.visible = false;
          }else{
            _this.$message.warning(res.data.Msg)
          }
        }).catch(err=>{
          console.log(err)
          _LoadingIns.close();

        })
      },
      addTagFun() {
        // 点击添加标签
        let _this = this
        _this.selectSearchTab = true;
        _this.searchTags = ''
        _this.serachChangeTagsList = _this.searchCheckArr
      },
      // 删除标签
      delTabTagFun(parms_bct_guid) {
        let _this = this;

        this.tempDelTabArr.push(parms_bct_guid)
        let sameindex = null
        this.tabLaberArr.forEach((item,index)=>{
          if(item.bct_guid == parms_bct_guid){
            sameindex = index
          }
        })
        this.tabLaberArr.splice(sameindex,1)
        this.searchCheckList.splice(sameindex,1)




        // _this.
        // .$confirm("确定删除选中标签吗？", "操作确认", {
        //   confirmButtonText: "确定",
        //   cancelButtonText: "取消",
        //   type: "warning"
        // })
        // .then(y => {
        //   _this.tabChangeFun(2,parms_bct_guid)
        // })
        // .catch(y => {});

      },
      tabChangeFun(isadd,bct) {

        let _this = this
        _this.$axios({
          method: 'post',
          url: `${this.$configjson.webserverurl}/api/BIMComp/BIMCompFamily/AddOrRMTag`,
          data: _this.$qs.stringify({
            Token: _this.Token,
            IsAdd: isadd,
            bct_guid: bct,
            bcfa_guid: _this.file_bcfa_guid
          })
        }).then(res=>{
          if(res && res.data.Ret == 1){

            // // 重新加载添加标签部分
            // _this.$message.success('操作成功')
            // _this.libraryItemClick(_this.checkedItem)
          }
        }).catch(err=>{

        })
      },
      tabChangeFunNew(bct) {

        let _this = this
        _this.$axios({
          method: 'post',
          url: `${this.$configjson.webserverurl}/api/BIMComp/BIMCompFamily/OverrideTag`,
          data: _this.$qs.stringify({
            Token: _this.Token,
            bct_guids: bct,
            bcfa_guid: _this.file_bcfa_guid
          })
        }).then(res=>{
          if(res && res.data.Ret == 1){

            // // 重新加载添加标签部分
            // _this.$message.success('操作成功')
            // _this.libraryItemClick(_this.checkedItem)
          }
        }).catch(err=>{

        })
      },
      // 添加标签
      searchCheckChange(val){
        let _this = this
        let changesearchCheckList = _this.searchCheckList
        let bct = changesearchCheckList.slice(-1).toString()

        let sameList = []
        this.searchCheckArr.forEach((item,index)=>{
          this.searchCheckList.forEach((item1)=>{
            if(item.bct_guid == item1 ){
              sameList.push(item)
            }
          })

        })
        this.tabLaberArr = sameList

      },
      // 上传族文件
      uploadFamilyFile() {
        let _this = this;
        let domFile = document.getElementById("id_uploadFamilyFile");
        domFile.value = "";
        domFile.click();
      },

      // 获取全部标签
      getAllTabFun() {
        let _this = this;
        _this.$axios.get(`${_this.$configjson.webserverurl}/api/BIMComp/BIMCompTag/GetTagsByOrganizeId`,{
          params: {
            organizeId: _this.OrganizeId,
          }
        }).then(res=>{
          if (res.data.Ret == 1) {
            // console.log(res.data.Data)
            _this.searchCheckArr = res.data.Data
            _this.serachChangeTagsList = res.data.Data
          }
        }).catch(err=>{

        })
      },
      selectMouseover(){
        let _this = this
        // _this.selectSearchTab=false
      },
      on_uploadFamilyFile_change() {
        let _this = this;
        let dom = document.getElementById("id_uploadFamilyFile");
        let url = `${this.$configjson.webserverurl}/api/BIMComp/BIMCompFamily/UploadComp`;
        if (dom.files && dom.files.length > 0) {
          _this.uploadfileFamily(
            dom,
            url,
            function(x) {
              if (x.status == 200) {

              } else {
                console.error(x);
              }
            },
            function(x) {
              console.error(x);
            }
          );
        }
      },
      uploadfileFamily(ele,url, successcb, errorcb) {
        let _this = this;
        if (!ele || !ele.files || !ele.files.length || ele.files.length == 0) {
          return;
        }
        let File = ele.files[0];
        let fd = new FormData();
        fd.append("Token", this.Token);
        fd.append("bcc_guid", _this.file_bcc_guid);
        let config = {
          headers: {
            "Content-Type": "multipart/form-data"
          }
        };
        let _file = ele.files;
        let upFileName = []
        for(let i = 0;i<_file.length;i++){
          // 上传类型判断
          let imgName = _file[i].name;
          fd.append("key",_file[i])
          let idx = imgName.lastIndexOf(".");
          if (idx != -1){
              let ext = imgName.substr(idx+1).toUpperCase();
              ext = ext.toLowerCase( );
              if (ext!='rfa'){
                _this.$message.warning('仅支持上传rfa的文件格式');
                return;
              }else{

              }
          }else{

          }
        }
        _this.$axios
          .post(
            url,
            fd,
            config
          )
          .then(x => {
            if (successcb) {
              let _data = x.data.Data
              if(_data.RepeatList.length<1){

                _this.$message.success('已上传成功');
              }else{
                let warnStr = _data.RepeatList.join()
                  // debugger
                this.$message({
                  showClose: true,
                  dangerouslyUseHTMLString: true,
                  message: '<p>已上传成功'+_data.SuccessCount+'个构件</p><p>'+_data.RepeatList.length+'个文件上传失败</p><p>重复文件：'+warnStr+'</p>',
                  type: 'warning'
                });
              }
                this.reloadTreeFun()
                // this.getCategoies(_this.likeparaFlie,this.loadTreeResolve);
                this.getCompsFamilyFile(_this.bcc_guidFlie,this.loadTreeResolve,this.File_bct_guid);
            } else {
              console.warn("not specify successcb");
            }
          })
          .catch(x => {
            if (errorcb) {
              errorcb(x);
            } else {
              console.warn("not specify errorcb");
            }
          });
      },
      //删除附件
      handelClickRemoveAttachment(item){
        let _this = this
        let _bca_guid = item.bca_guid
        _this
        .$confirm("确定删除该附件吗？", "操作确认", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
        .then(y => {
          _this.requestRemoveAttr(_bca_guid)
        })
        .catch(y => {});
      },
      // 下载附件
      handelClickDownttachment(item){
        let downSrc = `${this.$configjson.webserverurl}` + item.bcf_path
        window.location.href=downSrc
      },
      // 删除请求接口
      requestRemoveAttr(_bca_guid) {
        let _this = this
        _this.$axios({
          method: 'post',
          url: `${this.$configjson.webserverurl}/api/BIMComp/BIMCompFamily/RemoveAttachment`,
          data: _this.$qs.stringify({
            Token: _this.Token,
            bca_guid: _bca_guid,
          })
        }).then(res=>{
          if(res && res.data.Ret == 1)
          // 重新加载添加标签部分
          _this.libraryItemClick(_this.checkedItem)
        }).catch(err=>{

        })
      },
      // 添加附件
      handelClickAddAttachment() {
        let domFile = document.getElementById("id_uploadAttachment");
        domFile.value = "";
        domFile.click();
        // if(this.inside_bcfa_userid == this.UserId || this.inside_bcfa_userid == null){
        //   let domFile = document.getElementById("id_uploadAttachment");
        //   domFile.value = "";
        //   domFile.click();
        // }else{
        //   this.$message.warning('只有上传者才有操作权限')
        // }
      },
      on_uploadAttachment_change() {
        let _this = this;
        let dom = document.getElementById("id_uploadAttachment");
        let url = `${this.$configjson.webserverurl}/api/BIMComp/BIMCompFamily/AddAttachment`;
        if (dom.files && dom.files.length > 0) {
          _this.uploadAttachmentFile(
            '',
            dom,
            url,
            function(x) {
              if (x.status == 200) {

              } else {
                console.error(x);
              }
            },
            function(x) {
              console.error(x);
            }
          );
        }
      },
      uploadAttachmentFile(num,ele,url, successcb, errorcb) {
        let _this = this;
        if (!ele || !ele.files || !ele.files.length || ele.files.length == 0) {
          return;
        }
        let File = ele.files[0];
        let fd = new FormData();
        if(num == 1){
          // 上传属性
          fd.append("Token", this.Token);
          // fd.append("bimcomposerId",1 );
          fd.append("bcfa_guid", _this.file_bcfa_guid);
          fd.append("key", File);
        }else if(num == 2){
          // 上传模型
          fd.append("Token", this.Token);
          fd.append("OrganizeId", this.OrganizeId );
          fd.append("bcfa_guid", _this.file_bcfa_guid);
          fd.append("F1", File);
          let _file = ele.files;
          for(let i = 0;i<_file.length;i++){
            // 上传类型判断
            let imgName = _file[i].name;
            let idx = imgName.lastIndexOf(".");
            if (idx != -1){
                let ext = imgName.substr(idx+1).toUpperCase();
                ext = ext.toLowerCase( );
                if (ext!='pbc'){
                  _this.$message.warning('仅支持上传pbc的文件格式');
                  return;
                }
            }
          }
        }else{
          // 添加附件
          fd.append("Token", this.Token);
          fd.append("bimcomposerId",1 );
          fd.append("bcfa_guid", _this.file_bcfa_guid);
          fd.append("key", File);
          let _file = ele.files;
          for(let i = 0;i<_file.length;i++){
            // 上传类型判断
            let imgName = _file[i].name;
            let idx = imgName.lastIndexOf(".");
            if (idx != -1){
                let ext = imgName.substr(idx+1).toUpperCase();
                ext = ext.toLowerCase( );
                if (ext!='rfa'){
                  _this.$message.warning('仅支持上传rfa的文件格式');
                  return;
                }
            }
          }
        }
        let config = {
          headers: {
            "Content-Type": "multipart/form-data"
          }
        };
        _this.$axios
          .post(
            url,
            fd,
            config
          )
          .then(x => {
            if (successcb) {
              let _data = x.data.Data;
              if(x && x.data.Ret == 1){
                if(num == 1){
                  // 上传属性成功
                  _this.$message.success('已成功上传属性');
                }else if(num==2){
                  // 上传模型
                  _this.$message.success('已成功上传模型');
                  this.getCompsFamilyFile(this.bcc_guidFlie,this.loadTreeResolve,this.File_bct_guid);
                  this.selectClassifyItem(this.currentSelectClassify,this.selectFileItem)
                }else{
                  // 添加附件
                  _this.$message.success('已成功添加附件');
  
                  // this.getCategoies(_this.likeparaFlie,this.loadTreeResolve);
                  // this.getCompsFamilyFile(_this.bcc_guidFlie,this.loadTreeResolve,this.File_bct_guid);
                }
                this.libraryItemClick(_this.checkedItem);
              }else{
                this.$message.error(x.data.Msg)
              }
            } else {
              console.warn("not specify successcb");
            }
          })
          .catch(x => {
            if (errorcb) {
              errorcb(x);
            } else {
              console.warn("not specify errorcb");
            }
          });
      },
      getSortStyle() {
        let _this = this;
        let _s = {};
        _s["top"] = _this.sortMenu.top + "px";
        _s["left"] = _this.sortMenu.left + "px";
        return _s;
      },
      handleClickSort(e){
        let _this = this;
        let client = e.target.getBoundingClientRect();
        _this.sortMenu.top = client.y + 30;
        _this.sortMenu.left = client.x;
        _this.clickSortShow = true;
      },
      handelClickSortFun(type){
        this.clickSortMethod = type
        this.getCompsFamilyFile(this.bcc_guidFlie,this.loadTreeResolve,this.File_bct_guid);
      },
      // 下载族文件
      handelClickDownFile(){
        this.$axios({
          method: 'post',
          url: `${this.$configjson.webserverurl}/api/BIMComp/BIMCompFamily/DowloadComp`,
          data: this.$qs.stringify({
            Token: this.Token,
            bcfa_name: this.inside_bcfa_name
          })
        }).then(res=>{
          if(res && res.data.Ret == 1){
            let downSrc = `${this.$configjson.webserverurl}` + this.downFilePath
            window.location.href=downSrc
          }else{
            this.$message.err('请求失败，请稍后重试')
          }
        }).catch(err=>{
          console.log(err)
        })
      },
      // 上传属性
      handelClickUploadAttr(){
        let domFile = document.getElementById("id_branchAttr");
        domFile.value = "";
        domFile.click();
        // if(this.inside_bcfa_userid == this.UserId || this.inside_bcfa_userid == null){
        //   let domFile = document.getElementById("id_branchAttr");
        //   domFile.value = "";
        //   domFile.click();
        // }else{
        //   this.$message.warning('只有上传者才有操作权限')
        // }
      },
      // 下载属性模板
      handelClickDownDemo(){
        let downSrc = `${this.$configjson.webserverurl}/Content/Resource/Template/%E6%9E%84%E4%BB%B6%E5%BA%93%E5%B1%9E%E6%80%A7%E5%AF%BC%E5%85%A5%E6%A8%A1%E6%9D%BF.xlsx`
          window.location.href=downSrc
        // if(this.inside_bcfa_userid == this.UserId || this.inside_bcfa_userid == null){
        //   let downSrc = `${this.$configjson.webserverurl}/Content/Resource/Template/%E6%9E%84%E4%BB%B6%E5%BA%93%E5%B1%9E%E6%80%A7%E5%AF%BC%E5%85%A5%E6%A8%A1%E6%9D%BF.xlsx`
        //   window.location.href=downSrc
        // }else{
        //   this.$message.warning('只有上传者才有操作权限')
        // }
      },
      // 下载属性
      handelClickDownAttr(){
        if(this.attributeListSelect.length > 0){
          let downSrc = `${this.$configjson.webserverurl}/api/BIMComp/BIMCompFamily/ExportFamilyProps?bcfa_guid=${this.selectTabAllMess.bcfa_guid}`
          // console.log(downSrc)
          window.location.href=downSrc
        }else{
          this.$message.warning('暂无属性，请先上传；')
        }
        // if(this.inside_bcfa_userid == this.UserId || this.inside_bcfa_userid == null){
        //   if(this.attributeListSelect.length > 0){
        //     let downSrc = `${this.$configjson.webserverurl}/api/BIMComp/BIMCompFamily/ExportFamilyProps?bcfa_guid=${this.selectTabAllMess.bcfa_guid}`
        //     // console.log(downSrc)
        //     window.location.href=downSrc
        //   }else{
        //     this.$message.warning('暂无属性，请先上传；')
        //   }
        // }else{
        //   this.$message.warning('只有上传者才有操作权限')
        // }
      },
      // 删除属性
      handelClickDeleteAttr(){
          let _this = this;
          if(_this.attributeArr.length<1) return
          _this
          .$confirm("确定删除所有属性？", "操作确认", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning"
          })
          .then(y => {
            _this.$axios({
              method: 'post',
              url: `${this.$configjson.webserverurl}/api/BIMComp/BIMCompFamily/RemoveFamilyProps`,
              data: _this.$qs.stringify({
                Token: this.Token,
                bcfa_guid: _this.file_bcfa_guid,
              })
            }).then(res=>{
              if(res && res.data.Ret == 1){
                this.$message.success('删除成功')
                this.libraryItemClick(_this.checkedItem);
              }else{
                this.$message.success('删除失败，请稍后重试')
              }
            }).catch(err=>{
              console.log(err)
            })
          })
          .catch(y => {});
        // if(this.inside_bcfa_userid == this.UserId || this.inside_bcfa_userid == null){
        // }else{
        //   this.$message.warning('只有上传者才有操作权限')
        // }
      },
      on_branchAttr_change() {
        let _this = this;
        let dom = document.getElementById("id_branchAttr");
        let url = `${this.$configjson.webserverurl}/api/BIMComp/BIMCompFamily/ImportFamilyProps`;
        if (dom.files && dom.files.length > 0) {
          _this.uploadAttachmentFile(
            1,
            dom,
            url,
            function(x) {
              if (x.status == 200) {

              } else {
                console.error(x);
              }
            },
            function(x) {
              console.error(x);
            }
          );
        }
      },
      handelUplodModel(e) {
        if(this.isModelShow == '查看' && e != '更新'){
          // 查看模型
          this.lookModelState = true
        }else{
          // 上传模型
            let domFile = document.getElementById("id_uploadModel");
            domFile.value = "";
            domFile.click();
          // if(this.inside_bcfa_userid == this.UserId || this.inside_bcfa_userid == null){
          //   let domFile = document.getElementById("id_uploadModel");
          //   domFile.value = "";
          //   domFile.click();
          // }else{
          //   this.$message.warning('只有上传者才有操作权限')
          // }
        }
      },
      on_uploadModel_change() {
        let _this = this;
        let dom = document.getElementById("id_uploadModel");
        let url = `${this.$configjson.webserverurl}/api/BIMComp/BIMCompFamily/UploadBIMFamilyModel`;
        if (dom.files && dom.files.length > 0) {
          _this.uploadAttachmentFile(
            2,
            dom,
            url,
            function(x) {
              if (x.status == 200) {

              } else {
                console.error(x);
              }
            },
            function(x) {
              console.error(x);
            }
          );
        }
      },
      selectAttrChange(val) {
        // console.log(val)
        let _this = this;
        let selectArr = []
        this.attributeArr.forEach((item,key) => {
          if(item[val]){
            selectArr = item[val]
          }
        })
        let map = new Map();
        selectArr.forEach(item => {
          map.has(item.bcfapc_guid) ? map.get(item.bcfapc_guid).push(item) : map.set(item.bcfapc_guid, [item]);
        })
        this.attributeList = selectArr
      },
      // 详情信息编辑
      handelClickEdit(num){
          let _this = this
          if(num>0){
            // 获取输入框的值
            // 请求后台保存
          _this
            .$confirm("确认对修改内容进行保存？", "操作确认", {
              confirmButtonText: "确定",
              cancelButtonText: "取消",
              type: "warning"
            })
            .then(y => {
              this.editNameFun(this.selectTabPanData.information.title, this.selectTabPanData.information.descEdit)
              _this.selectSearchTab = false
            })
            .catch(y => {
              this.selectTabPanData.information.descEdit = this.selectTabPanData.information.desc
              this.editMessage = true
              _this.selectSearchTab = false
            });
          }else{
            this.selectTabPanData.information.descEdit = this.selectTabPanData.information.desc
            this.editMessage = !this.editMessage;
          }
        // if(this.inside_bcfa_userid == this.UserId || this.inside_bcfa_userid == null){
        // }else{
        //   this.$message.warning('只有上传者才有操作权限')
        // }        
      },
      editNameFun(name,desc) {
        let _this = this;
        _this.$axios({
            method: 'post',
            url: `${this.$configjson.webserverurl}/api/BIMComp/BIMCompFamily/ModifyFamilyName`,
            data: _this.$qs.stringify({
              Token: this.Token,
              bcfa_guid: _this.selectTabAllMess.bcfa_guid,
              bcfa_name: name,
              bcc_guid: _this.selectTabAllMess.bcc_guid
            })
          }).then(res=>{
            if(res && res.data.Ret == 1){
              if(!res.data.Data.IsRepeat){
                this.tempAddTabArr = this.searchCheckList
                this.tabChangeFunNew(this.tempAddTabArr.toString())
                _this.$nextTick(()=>{
                  this.editDescriptionFun(desc)
                  this.getCompsFamilyFile(_this.bcc_guidFlie,this.loadTreeResolve,this.File_bct_guid);
                })
              }else{
                this.$message.error('构件名称重复，请重新输入')
              }
            }else{
              this.$message.error('请求失败，请稍后重试')
            }
          }).catch(err=>{
            console.log(err)
          })

      },
      editDescriptionFun(desc) {
        let _this = this;
        _this.$axios({
            method: 'post',
            url: `${this.$configjson.webserverurl}/api/BIMComp/BIMCompFamily/ModifyFamilyDescription`,
            data: _this.$qs.stringify({
              Token: this.Token,
              bcfa_guid: _this.file_bcfa_guid,
              bcfa_description: desc
            })
          }).then(res=>{
            if(res && res.data.Ret == 1){
            _this.libraryItemClick(_this.checkedItem)
              _this.$message.success('操作成功')
              this.editMessage = true
              this.tempAddTabArr = []
              this.tempDelTabArr = []

            }else{
              this.$message.err('请求失败，请稍后重试')
            }
          }).catch(err=>{
            console.log(err)
          })
      },
      // 搜索标签

      searchTagsChange(val) {
        let searchinput = ''
        let _this = this
        _this.serachChangeTagsList=[]
        if(val.length>0){
          searchinput = _this.searchCheckArr.filter(function (product) {
              if(product.bct_name.indexOf(val) > -1){
                // console.log(product.bct_name)
                _this.serachChangeTagsList.push(product)
              }
          });

          // console.log(_this.serachChangeTagsList,'====')
        }else{
          _this.serachChangeTagsList = _this.searchCheckArr
        }
      }
    },

    directives: {
      'libraryClickClose': function (el) {
        document.body.addEventListener('click', function (e) {
          if (el == e.target || el.contains(e.target)) {
            return false;
          }
          // 判断指令中是否绑定了函数
          if (binding.expression) {
            // 如果绑定了函数 则调用那个函数，此处binding.value就是handleClose方法
            binding.value(e);
          }
        });
      }
    },

    destroyed() {
      document.body.removeAttribute('style');
    }

  }
</script>

<style>
  .component-library .library-left-tree .left-tree-container .el-tree .el-tree-node__content {
    height: 40px;
  }

  .component-library .library-left-tree .left-tree-container .el-tree .el-tree-node__content:hover,
  .component-library .library-left-tree .left-tree-container .el-tree-node.is-current>.el-tree-node__content {
    background-color: rgba(24,144,255,0.1);
  }
</style>
<style scoped>
.left-tree-container /deep/ .el-tree-node__expand-icon.expanded {
  transform: rotate(0deg) !important;
}
.left-tree-container /deep/ .el-icon-caret-right:before {
  content: "\E602" !important;
  font-size: 18px !important;
}
.left-tree-container /deep/ .el-tree-node__expand-icon.expanded.el-icon-caret-right:before {
  content: "\E63c" !important;
  font-size: 18px !important;
}
.components-search-input /deep/ .el-input__inner{
  line-height: 30px;
  height: 30px;
  /* background:#fff; */
}
.components-search-input{
  background: #fff;
}
.model-more-class{
    /* position:fixed; */
    width:100%;
    /* height:calc( 100% - 160px); */
    height: 100%;
    /* background: #f00; */
}
  ._css-backtolist {
  min-width: 98px;
  height: 32px;
  position: absolute;
  /* top: 20px; */
  top: -40px;
  left: 20px;
  display: flex;
  align-items: center;
  cursor: pointer;
  z-index: 2;
}
._css-backtolist:hover {
  background-color: rgba(0, 0, 0, 0.04);
}
._css-backtolist-icon {
  height: 20px;
  width: 20px;
  margin-left: 8px;
  font-size: 20px;
}
._css-backtolist-text {
  height: 22px;
  margin-left: 1px;
  line-height: 22px;
  color: rgba(0, 0, 0, 0.85);
  font-size: 14px;
}
.function-menu .icon{
  cursor: pointer;
}
</style>
