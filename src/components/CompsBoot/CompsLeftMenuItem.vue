<template>
    <div
    @click.stop="_onclick"
    :class="getclass_()"
    class="_css-leftmenuitem css-h40 css-w100 css-bsb _css-menui   css-fc _css-menui iconlevel-1"
    >
            <div  class="css-icon16 css-fs16 css-fc css-jcsa css-b css-ml24 "
            :class="iconClass"
            >
            </div>
            <div  class="css-h22 css-fs14 css-ml8 css-fc _css-menu-text">{{itemname}}</div>
          </div>
</template>
<script>
/*
input:
    
events:
    onclick(itemid)
*/
export default {
    data(){
        return {
            
        }
    },
    methods:{
        _onclick(){
            var _this = this;
            _this.$emit("onclick", _this.itemid);
        },
        getclass_(){
            var _this = this;
            var obj = {};
            obj.clicked = _this.isclicked;
            return obj;
        }
    },
    props:{
        isclicked:{
            type: Boolean,
            required: false
        },
        itemid:{
            type:String,
            required: true
        },
        itemname:{
            type: String,
            required: false
        },
        iconClass:{
            type: String,
            required: false
        }
    }
}
</script>
<style scoped>

</style>