<template>
  <div class="comp-organiza">
    
      <!-- 左上角按钮：返回列表 -->
      <div class="_css-backtolist" @click.stop="backtolist">
        <div class="icon-suggested-close-fill _css-backtolist-icon"></div>
        <div class="_css-backtolist-text">项目列表</div>
      </div>
    <div class="_css-log-btn">
      <div class="_css-log-btn-left">
        <p class="_css-litter" :class="{'_css-checked': num==0}" @click.stop="checkDataChange(0)">今天</p>
        <p class="_css-litter" :class="{'_css-checked': num==1}"  @click.stop="checkDataChange(1)">近七天</p>
        <p :class="{'_css-checked': num==2}"  @click.stop="checkDataChange(2)">近一个月</p>
        <p :class="{'_css-checked': num==3}"  @click.stop="checkDataChange(3)">近三个月</p>
      </div>
      <div class="_css-log-btn-right">
        <i class="_css-icon-his icon-interface-history"></i>
        <el-date-picker
          v-model="selectData"
          type="daterange"
          @change="dataPickerChange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期">
        </el-date-picker>
      </div>
    </div>
    <div class="_css-log-table">
      <el-table
        :data="tableData"
        border
        height="500"
        style="width:99.9%"
        v-loading="loading"
        >
        <el-table-column
          type="index"
          label="序号"
          width="70" 
          :resizable="false" >
        </el-table-column>
        <el-table-column
          prop="UserName"
          label="操作人"
          :resizable="false" 
          width="150">
        </el-table-column><el-table-column
          prop="ModuleName"
          label="操作模块"
          :resizable="false" 
          width="180">
          <template slot-scope="scope">{{scope.row.ModuleName}}</template>
        </el-table-column>
        <el-table-column
          prop="Description"
          label="操作详情"
          :resizable="false" >
        </el-table-column>
        <el-table-column
          prop="LogTime"
          label="操作时间"
          :resizable="false" 
          width="180">
        </el-table-column>
      </el-table>
    </div>
    <div class="_css-log-pagenum">
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="currentPage"
        :page-sizes="[100, 200, 300, 400]"
        :page-size="pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="pageTotal">
      </el-pagination>
    </div>
  </div>
</template>

<script>
  export default {
    //机构日志
    name: "compsOrganizationLog",
   
    data() {
      return {
        num: 0,
        selectData: '',  // 选择日期
        tableData: [],
        currentPage: 1,
        pageTotal: 400,
        organizeId: '',
        pageIndex: 1,
        pageSize: 100,
        tableParams: null,
        startTime: '',
        endTime: '',
        loading: true,
      }
    },
    filters: {
      flttimeshorter(inputtime) {
        let _this = this;
        if (!inputtime || inputtime.trim().length == 0) {
          return inputtime;
        }
        if (inputtime.length >= "2019-09-16T11:14".length) {
          return inputtime.substr(0, "2019-09-16T11:14".length).replace("T", " ");
          // return inputtime.substr(0, 10);
        }
      },
      flttimemodule(val){
        if(val == 'company_bimcomp'){
          return '构件库'
        }
      },
      flttimetype(val){
        if(val=="deleted"){
          return '删除'
        }
        if(val=="modifyed"){
          return '修改'
        }
        if(val=="added"){
          return '添加'
        }
        if(val=="downloaded"){
          return '下载'
        }

      }
    },
    watch: {
      rangeDate: function (newVal, oldVal) {
        if (newVal !== null) {
          this.tableParams.beginDate = newVal[0]
          this.tableParams.endDate = newVal[1]
        } else {
          this.tableParams.beginDate = null
          this.tableParams.endDate = null
        }
      }
    },
    created() {
      this.organizeId = sessionStorage['_OrganizeId'] 
      this.requeatTableFun()
      this.checkDataChange(0)
    },
    mounted() {
      
    },
    methods: {
      handleSizeChange(val) {
        this.pageSize = val
        this.requeatTableFun()
      },
      handleCurrentChange(val) {
        this.pageIndex = val
        this.requeatTableFun()
      },
      dataPickerChange(val) {
        this.startTime = this.getTime(val[0]).substr(0,10)
        this.endTime = this.getTime(val[1]).substr(0,10)
        this.requeatTableFun()
      },
      checkDataChange(num){
        this.num = num
        this.endTime = this.getTime(new Date()).substr(0,10)
        if(num==0){
          // 今天
          this.startTime = this.getTime(new Date()).substr(0,10)
        }else if(num == 1){
          // 近七天
          this.startTime =  this.getDiffDay(-7)
        }else if(num==2){
          // 近一个月 
          this.startTime =  this.getDiffDay(-30)
        }else if(num = 3){
          // 近三个月
          this.startTime =  this.getDiffDay(-90) 
        }
        this.requeatTableFun()
      },
      getTime(date){
        let now = new Date(date);
        let y = now.getFullYear();
        let m = now.getMonth() + 1;
        let d = now.getDate();
        return y + "-" + (m < 10 ? "0" + m : m) + "-" + (d < 10 ? "0" + d : d) + " " + now.toTimeString().substr(0, 8);
      },
      getDiffDay(day){  
        let today = new Date();  
        let targetday_milliseconds=today.getTime() + 1000*60*60*24*day;          
        today.setTime(targetday_milliseconds); 
        let tYear = today.getFullYear();  
        let tMonth = today.getMonth();  
        let tDate = today.getDate();  
        tMonth = this.doHandleMonth(tMonth + 1);  
        tDate = this.doHandleMonth(tDate);  
        return tYear+"-"+tMonth+"-"+tDate;  
      } ,
      doHandleMonth(month){  
        let m = month;  
        if(month.toString().length == 1){  
            m = "0" + month;  
        }  
        return m;  
      },
      // requeatTableFun() {
      //   let _this = this; 
      //   this.$axios.get(`${this.$configjson.webserverurl}/api/Admin/Organize/GetLogs`,{
      //     params: {
      //       organizeId: this.organizeId,
      //       startTime: this.startTime,// '1901-12-12',
      //       endTime: this.endTime,   //'2222-12-12',//
      //       pageIndex: this.pageIndex,
      //       pageSize: this.pageSize,
      //       Token: this.$staticmethod.Get("Token"),
      //     }
      //   }).then(res=>{
          
      //     if (res.data.Ret == 1) {
      //       this.tableData = res.data.Data.List
      //       this.pageTotal = res.data.Data.Total
      //       this.loading = false
      //     }
      //   }).catch(err=>{

      //   })
      // },
      async requeatTableFun() {
        let data = {
          PageNum: this.pageIndex,
          PageSize: this.pageSize,
          OrganizeId: this.organizeId,
          // KeyWord: this.KeyWord,
          // RequestMethod: '',
          // ModuleName:'',
          StartTime: this.startTime + ' 00:00:00',
          EndTime: this.endTime + ' 23:59:59',
        };
        let res = await this.$api.GetUserLogPaged(data);
        this.tableData = res.Data.Data;
        this.pageTotal = res.Data.Total;
        this.loading = false
      },
      // 返回到项目列表
      backtolist() {
        var _this = this;
        window.location.href = `${window.bim_config.hasRouterFile}/#/Home/Boot/${_this.$staticmethod.Get("Token")}`;
      },
    },

    directives: {
      
    },

    destroyed() {
      document.body.removeAttribute('style');
    }

  }
</script>

<style scoped>
  .comp-organiza{
    width: 100%;
    height: calc(100% - 64px); 
    position: relative;
    z-index: 1;
    padding: 64px 10% 40px;
    display: flex;
    flex-direction:column;
    background-color: #f0f2f5;
  }
  ._css-log-btn{
    display: flex;
    align-items: center;
    justify-content:space-between;
    margin:24px 0;
    width: 99.9%;
  }
  ._css-log-btn-left{
    display: flex;
  }
  ._css-log-btn-left p{
    width: 76px;
    line-height: 40px;
    background:rgba(0,0,0,.04);
    color:#1890ff;
    text-align: center;
    margin-right:16px;
  }
  ._css-log-btn-left p:hover,  ._css-log-btn-left ._css-checked{
    background: #1890ff;
    color:#fff;
    cursor: pointer;
    border-radius: 2px;
  }
  ._css-log-btn-left p._css-litter{
    width:70px;
  }
  ._css-log-btn-right{
    width:340px;
    height:40px;
    background:#fff;
    position: relative;
    border-radius: 4px;
  }
  ._css-log-table{
    flex: 1;
    width: 100%;
    overflow:initial;
  }
  ._css-log-pagenum{
    height: 40px;
    margin-bottom:40px;
  }
  ._css-log-table /deep/ .el-table .cell,
  ._css-log-table /deep/ .el-table th div, 
  ._css-log-table /deep/ .el-table--border td:first-child .cell,
  ._css-log-table /deep/ .el-table--border th:first-child .cell{
    padding-left:16px;
  }
  ._css-log-table /deep/ .el-table, 
  ._css-log-table /deep/ .el-table__expanded-cell{
    background: transparent;
  }
  ._css-log-table /deep/ .el-table--border td,
  ._css-log-table /deep/ .el-table--border th, 
  ._css-log-table /deep/ .el-table__body-wrapper .el-table--border.is-scrolling-left~.el-table__fixed{
    border: none;
  }
  ._css-log-table /deep/ .el-table td, 
  ._css-log-table /deep/ .el-table th.is-leaf{
    border-bottom: 1px solid rgba(0,0,0,.04);
  }
  ._css-log-table /deep/ .el-table .cell{
    line-height: 40px;
  }
  ._css-log-table /deep/ .el-table--border, .el-table--group{
    border:none;
  }
  ._css-log-table /deep/ .el-table th, .el-table tr{
    background: rgba(247,247,247,1)
  }
  ._css-log-pagenum /deep/ .el-pagination button:disabled,
  ._css-log-pagenum /deep/ .el-dialog, 
  ._css-log-pagenum /deep/ .el-pager li{
    background: transparent;
  }
  ._css-log-btn /deep/ .el-date-editor .el-range__icon{
    color: transparent;
  }
  ._css-log-btn /deep/ .el-icon-date:before {
    content: "\e96e";
  }
  ._css-log-btn-right ._css-icon-his{
    position: absolute;
    top:10px;
    left:8px;
  }
  ._css-backtolist {
  min-width: 98px;
  height: 32px;
  position: absolute;
  top: 4%;
  left: 20px;
  display: flex;
  align-items: center;
  cursor: pointer;
  z-index: 2;
}
._css-backtolist:hover {
  background-color: rgba(0, 0, 0, 0.04);
}
._css-backtolist-icon {
  height: 20px;
  width: 20px;
  margin-left: 8px;
  font-size: 20px;
}
._css-backtolist-text {
  height: 22px;
  margin-left: 1px;
  line-height: 22px;
  color: rgba(0, 0, 0, 0.85);
  font-size: 14px;
}
</style>