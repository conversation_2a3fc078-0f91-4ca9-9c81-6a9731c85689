<template>
  <div>
    <el-tree
      :data="treedata"
      :props="props1"
      lazy
      :load="loadNodeChild"
      node-key="FileId"
      :expand-on-click-node="false"
      @node-collapse="node_collapse"
      @node-expand="node_expand"
      @node-click="_send_nodeclick"
      ref="doctree"
      :default-expanded-keys="defexpandkeys"
    >
      <span class="css-fc" slot-scope="{ node, data }">
        <i class="css-icon20 css-fs18 css-fc css-jcsa css-folder" :class="data.classname"></i>
        <span :title="data.FileName" class="css-ml4">{{ node.label }}</span>
      </span>
    </el-tree>
  </div>
</template>
<script> 
export default {
  data() {
    return {
      treedata: [],
      defexpandkeys: [],
      props1: {
        children: "children",
        label: "bcc_name"
      },
      initTreeNodeData: [],
      OrganizeId: ""
    };
  },
  mounted() {
    let _this = this;
    this.OrganizeId = sessionStorage["_OrganizeId"];
  },
  methods: {
    _send_nodeclick(data,node,current) {
      let _this = this;
      _this.$emit("onnodeclick", data.bcc_guid);
    },

    // 展开节点 回调
    node_expand(itemi, node, comp) {
      itemi.classname = "icon-interface-folder";
    },

    node_collapse(itemi, node, comp) {
      itemi.classname = "icon-interface-unfolder";
    },

    // 某节点展开时，加载子节点的途径
    loadNodeChild(node, resolve) {
      // 设置必要的参数
      let _this = this;
      if (node.level === 0) {
        this.initTreeNodeData.node = node;
        this.initTreeNodeData.resolve = resolve;
        this.getCategoies("", resolve);
      }

      //打开父节点
      if (node.level >= 1) {
        //传父节点的bcc_code
        let likepara = node.data.bcc_code;
        this.getCategoies(likepara, resolve);
      }
    },
    
    getCategoies(pcode, resolve) {
      let _this = this;
      this.$axios
        .get(
          `${this.$configjson.webserverurl}/api/BIMComp/BIMCompCategory/GetCategoies?Token=${_this.$staticmethod.Get('Token')}`,
          {
            params: {
              pcode: pcode, //父编码
              organizeId: this.organizeId
            }
          }
        )
        .then(res => {
          if (res.data.Ret == 1) {
            let res_data = res.data.Data;
            for (let i = 0; i < res_data.length; i++) {
              // 确保当前新加载的文件夹节点左侧有展开图标
              res_data[i].classname = "icon-interface-unfolder";

            }
            resolve(res_data);
          }
        })
        .catch(err => {});
    }
  },
  props: {
    organizeId: {
      type: String,
      required: true
    }
  }
};
</script>
<style scoped>
</style>