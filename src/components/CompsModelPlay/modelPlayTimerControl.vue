<template>
    <div class="_css-time-line" ref="toptimerControl">
        <div>
            <div class="_css-time-slider">
                <slot></slot>
                <div class='_css-time-btn' v-if="!timeResNull">
                    <div class="_css-timeline">
                        <p v-if="smallWidth">{{sliderMinTime | flt_datetimefromList}}</p>
                        <div class="_css-el-slider">
                            <el-slider
                                :min='0'
                                :max="sliderTimeValue.length-1"
                                v-model="timeSlidervalue" 
                                show-tooltip
                                :disabled="loadingChange"
                                :format-tooltip="timestepToolTip"
                                @change="sliderChange"
                                @input="sliderInputChange"
                                >
                            </el-slider>
                        </div>
                        <p v-if="smallWidth">{{sliderMaxTime | flt_datetimefromList}}</p>
                    </div>
                    <div class="_css-time-detail">
                        <div class="_css-playtime-input" :class="{'ban-click':loadingChange}">
                            <el-input 
                                class="_css-playtime"  
                                maxlength="3"  
                                :disabled="modelInputEdit"
                                onkeyup="this.value=this.value.replace(/[^\d.]/g,'');"
                                v-model="playTime" 
                                placeholder=""
                            ></el-input>秒
                        </div>
                        <div class="_css-div-btn " :class="playsign == 'end' && !loadingChange? '_css-dropdown' : '_css-dropdown-disable'">
                            <el-dropdown @command="handleCommand" trigger="click">
                                <el-button type="primary">
                                    按{{filterdropdownVal(dropdownVal) }}模拟<i class="el-icon-arrow-down el-icon--right"></i>
                                </el-button>
                                <el-dropdown-menu slot="dropdown">
                                    <el-dropdown-item command="budget" :class="{active:dropdownVal == 'budget'}">实际与预算</el-dropdown-item>
                                    <el-dropdown-item command="plan" :class="{active:dropdownVal == 'plan'}">实际和计划对比</el-dropdown-item>
                                </el-dropdown-menu>
                            </el-dropdown>
                        </div>
                        
                        <div
                            v-if="playsign == 'end'"
                            class="_css-div-btn playmodel"
                            :class="{'ban-click':loadingChange}"
                            @click="mockprogressclick" >
                            <i class="icon-interface-play"></i>
                            开始模拟
                        </div>
                        <div
                            v-if="playsign == 'pause'"
                            class="_css-div-btn playmodel"
                            @click="mockprogressclick"
                            :class="{'ban-click':false && loadingModelEnd == true}"><i class="icon-interface-play"></i>
                            继续模拟
                        </div>
                        <div
                            v-if="playsign == 'start'"
                            class="_css-div-btn pausemodel"
                            @click="pausemodelClick"
                            :class="{'ban-click':false && loadingModelEnd == true}"><i class="icon-interface-time-out"></i>
                            暂停模拟
                        </div>
                        <div
                            v-if="playsign != 'end'"
                            class="_css-div-btn stopmodel"
                            @click="stopPlayModel()"
                            :class="{'ban-click':false && loadingModelEnd == true}"><i class="icon-interface-stop"></i>
                            停止模拟
                        </div>
                    </div>
                </div>
                <div class="_css-null" v-if="timeResNull" >当前项目不存在形象进度数据</div>
            </div>
        </div>
    </div>
</template>
<script>
export default {
    name: 'modelPlaySimulationShow',
    data() {
        return { 
            smallWidth: true,  // 判断屏幕宽度小于900  不显示时间
            timeSlidervalue:  0,
            // marks: {},
            dateMin: '', 
            timeResNull: false, 
            playsign: 'end', // 当前的模型模拟状态 
            bStartDis: true, // 刚点击完开始模拟时，立即dis掉，当 playSign 修改为 start 时，立即取消dis.
            loadingModelEnd: true,
            playTimeHandler: 0,  
            setValue: 0,  // 模拟时候记录时间值
            timeAutoData: 1000,   // 播放秒数
            // playingBtn: true, 

            diffStep: 1,
            sliderMinTime: '',  // 播放的最小时间
            sliderMaxTime: '',  // 播放的最大时间
            sliderTimeDiff: 0,
            revalue: 0,

            // allPro_ModelID: [],  // 该值为allPro_ModelID的存值，大屏项目时候该值为全部的allPro_ModelID，平台形象进度进入时候为传入的值
            // Pro_bmtypeId: [],  // 该值为Pro_bmtypeId的存值，大屏项目时候该值为全部的Pro_bmtypeId，平台形象进度进入时候为传入的值
            // Pro_ToleranceValues: [],  // 该值为Pro_ToleranceValues的存值，大屏项目时候该值为全部的Pro_ToleranceValues，平台形象进度进入时候为传入的值
            headerHide: false, // 大屏显示使用该组件，header不展示 
            modelInputEdit: false, // 编辑描述输入框状态
            playTime: null,  // 输入框的时间秒数
            // ToleranceValues:0 ,
            dropdownVal: 'budget',
            screenWidth: 0,
            totalFrameCnt: 0,
            sliderTimeValue: [], // 时间轴
            inputchange: true,
            loadingChange: true,
        };
    },
    props: { 
        // sliderReturnValue为时间轴的时间，目前是最大最小时间，如果是所有时间的话，后续会处理逻辑，逻辑目前还没写；
        sliderReturnValue: {
            type: Array,
            required: false
        },
        isProgress: {
            type: Boolean,
            required: false
        },
        loadingChange1: {
            type: Boolean,
            required: false
        },
    },
    watch:{
        screenWidth (val){
            this.screenWidth = val; 
            if(val > 900){
                this.smallWidth = true;
            }else{
                this.smallWidth = false;
            }
        }, 
        sliderReturnValue(val){
            let _this = this; 
            _this.sliderTimeValue = [...new Set(val)]
            
            if(_this.sliderTimeValue.length<=0){
                _this.timeResNull = true;
            }else{
                _this.timeResNull = false;
            }
            _this.sliderTimeValueFun();
            _this.$nextTick(() => {
                _this.sliderChange(0);
            })
        },
        loadingChange1(val){ 
            this.loadingChange = val;
        }
    },
    filters: {
        flt_datetimefromserver(str) {
            var _this = this;
            var str1 = str;
            if (str) {
                str1 = str.replace("T", " ");
            }
            return str1;
        },
        flt_datetimefromList(str) {
            var _this = this;
            var str1 = str;
            if (str) {
                str1 = str.substr(0,4) + '年' + str.substr(5,2) + '月' + str.substr(8,2) + '日'
            }
            return str1;
        }, 
        
    },
    components: {
        
    },

    mounted() {
        if(this.sliderReturnValue) {
            this.sliderTimeValue = [...new Set(this.sliderReturnValue)]
        }else{
            this.sliderTimeValue  = 1
        }
        this.sliderTimeValueFun();
        window.onresize =()  =>{
            return (()=>{
                this.screenWidth = this.$refs.toptimerControl.clientWidth;
            })()
        }
        if(this.$refs.toptimerControl.clientWidth > 900){
            this.smallWidth = true;
        }else{
            this.smallWidth = false;
        }
        this.$nextTick(() => {
            this.sliderChange(0);
        })
    },
    methods: {
        // 时间轴接收的传值进行处理
        sliderTimeValueFun(){
            let _this = this;
            let _min = this.sliderTimeValue[0];
            let _max = this.sliderTimeValue[this.sliderTimeValue.length-1];
            _this.sliderMinTime = _min;
            _this.sliderMaxTime = _max;
            _this.playTime = _this.sliderTimeValue.length;
            _this.totalFrameCnt = _this.sliderTimeValue.length; 
        },
        // 点击返回
        backTheTop() {
            clearInterval(this.playTimeHandler);
            this.$emit('close');
        },
        filterdropdownVal(val) {
            if(val == 'budget'){
                return '实际与预算'
            }else if(val == 'plan'){
                return '实际和计划对比'
            }
        },
        timestepToolTip(value){ 
            let _this = this;
            if(value>this.sliderTimeValue.length){
                clearInterval(_this.playTimeHandler);
                _this.playsign = 'end';
                return;
            }
            let datestrTool = this.sliderTimeValue[value]
            return datestrTool;
        },
        sliderInputChange(val){
            let _this = this;
            // this.pausemodelClick();
            // _this.setValue = _this.revalue;
            // clearInterval(this.playTimeHandler);
            // this.inputchange = false;
            // if(!this.inputchange){
            //     this.pausemodelClick()
            // }
        },
        sliderChange(value) { 
            // console.log(value,'change256==change');
            let _this = this;
            if(value>=this.sliderTimeValue.length){
                clearInterval(_this.playTimeHandler);
                _this.playsign = 'end';              
                return;
            }
            this.setValue = value;
            this.timeSlidervalue = this.setValue;
            this.changeTime = this.$formatData.dateInit(this.sliderTimeValue[value]); 
            this.$parent.getListTimePoint(this.changeTime,value);
        },
        handleCommand(command){
            this.dropdownVal = command;
            this.$emit('dropdownChange',command);
        }, 
        // 开始模拟，继续模拟
        mockprogressclick() {
            let _this = this;
            let totalTimeDiff = this.$formatData.DateDiff(_this.sliderMinTime,_this.sliderMaxTime) ; //计算两者时间差，现在的需求是不同时间段，应该为返回所有时间的length
            // playTimerAuto 通过当前秒数和天数，根据getInFactSeconds方法计算获得当前秒数，*1000是该值用于倒计时使用，倒计时单位是ms
            let playTimerAuto = _this.getInFactSeconds(this.totalFrameCnt, _this.playTime) * 1000;   //  也就是timeAutoData
            _this.playTime = parseInt(_this.getInFactSeconds(this.totalFrameCnt, _this.playTime));  // 计算的时间，值为多少秒
             
            let timeauto = playTimerAuto / this.totalFrameCnt ; // 每一秒播放的时间应该 输入的秒数（0.3-1s计算后的值）除以总数
            
            _this.playsign = 'start'; 
            _this.modelInputEdit = true;
            _this.loadingChange = false;
            // console.log(_this.timeSlidervalue, this.sliderTimeValue.length)
            if(_this.timeSlidervalue == this.sliderTimeValue.length - 1){
                _this.timeSlidervalue = 0;
                _this.setValue = 0;
                _this.sliderChange(0);
                _this.timestepToolTip(0);
            }
            
            _this.playTimeHandler = setTimeout(()=>{
                _this.frameExecuteUnit(timeauto,_this.setValue);
            }, timeauto);
        },
        frameExecuteUnit(timeauto,val){
            let _this = this;
            _this.revalue = val;
            if(val > this.sliderTimeValue.length){
                _this.timeSlidervalue = this.sliderTimeValue.length;
                clearInterval(_this.playTimeHandler);
                _this.playsign = 'end';
                _this.modelInputEdit = false;
                _this.setValue = this.sliderTimeValue.length;
                _this.sliderChange(this.sliderTimeValue.length);
                _this.timestepToolTip(this.sliderTimeValue.length);
                return
            }
            _this.setValue = (val + 1);
            
            _this.playTimeHandler = setTimeout(()=>{
                _this.sliderChange(_this.setValue);
                _this.timestepToolTip(_this.setValue);
                _this.timeSlidervalue = _this.setValue;
                _this.frameExecuteUnit(timeauto,_this.setValue);
            }, timeauto);
        },
        // 暂停模拟
        pausemodelClick() {
            let _this = this;
            _this.setValue = _this.revalue;
            _this.playsign = 'pause';
            clearInterval(_this.playTimeHandler);
        },
        // 停止模拟
        stopPlayModel(value) {
            let _this = this;
            
            _this.sliderChange(0);
            _this.timestepToolTip(0);
            _this.timeSlidervalue = 0;
            _this.setValue = 0;
            _this.playsign = 'end';
            _this.modelInputEdit = false;
            clearInterval(_this.playTimeHandler);
            // _this.$parent.modelplaystart();
        }, 
        // totalFrameCnt // 总（天/周/月）数;   // 通过甘特图计算
        // inputSeconds  // 输入的秒数
        // return: 返回实际的总秒数
        getInFactSeconds(totalFrameCnt, inputSeconds) {
            let _this = this;
            var inFactSeconds; // 实际总秒数
            var leastDelay = 0.3 * totalFrameCnt;
            var mostDelay = 1 * totalFrameCnt;
            if (inputSeconds && inputSeconds < leastDelay ) {
                _this.$message.warning('已为您自动计算接近输入数值的秒数')
                inFactSeconds = leastDelay ;
            } else if (inputSeconds && inputSeconds > mostDelay ) {
                _this.$message.warning('已为您自动计算接近输入数值的秒数')
                inFactSeconds = mostDelay;
            } else {
                inputSeconds? inFactSeconds = inputSeconds : inFactSeconds = mostDelay;
            }
            return inFactSeconds;
        },
    },
    beforeDestroy() {
        clearInterval(this.playTimeHandler);
    },
    destroyed() {
        clearInterval(this.playTimeHandler);
        window.onresize = null
    }
}
</script>
<style scoped>

._css-time-line{
    pointer-events: all;
    height: 45px;
    padding: 15px 20px 0;
    background: #FFFFFF;
    border-radius: 4px;
}
._css-time-slider{
    display: flex;
}
._css-time-slider /deep/ .el-slider__marks-text{
    white-space: nowrap;
}
._css-null{
    margin: 0 5%;
    text-align: left;
}
._css-time-detail{
    display: flex;
    text-align: right;
    line-height: 30px;
    font-size: 14px;
    font-weight: 500;
    
}
._css-playtime-input /deep/ .el-input.is-disabled .el-input__inner{
    background:transparent;
}

._css-playtime-input{
    margin-right:25px;
}
._css-playtime{
    border:1px solid rgba(0,0,0,.09);
    border-radius:4px;
    width:30px;
    height:30px;
    line-height:30px;
    margin-right:5px;
    /* padding:0 10px; */
}
._css-playtime /deep/ .el-input__inner{
    line-height:30px;
    width: 24px;
    padding:0;
}
._css-time-detail:hover{
    cursor: pointer;
}
._css-time-detail div._css-dropdown{
    background:#007AFF;
    /* pointer-events:none;  */
}
._css-time-detail div._css-dropdown-disable{
    background:#A6AEB6;
    pointer-events:none; 
    cursor:not-allowed;
    border: 1px solid #A6AEB6 !important;
}
._css-time-detail div._css-dropdown /deep/ .el-button--primary{
    background-color: #007AFF;
    border-color: #007AFF;
}
._css-time-detail div._css-dropdown /deep/ .el-button{
    border:1px solid transparent;
}

._css-time-detail div._css-dropdown-disable /deep/ .el-button--primary{
    background-color:#A6AEB6;
    border-color:#A6AEB6;
}
._css-time-detail div._css-dropdown-disable /deep/ .el-button{
    border:1px solid transparent;
    cursor:not-allowed;    
} 
._css-time-detail div._css-div-btn{
    padding: 0 8px;
    height: 28px;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
    -ms-flex-direction: row;
    flex-direction: row;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    border-radius: 4px;
    border: 1px solid #1890ff;
    /* color: #1890ff; */
    /* background: #f7f7f7; */
    margin-left: 16px;
    opacity: .7;
    cursor: pointer;
}
._css-time-detail div._css-playtime-input{
    border: 1px solid #D8D9DB;
    padding: 0 8px;
    border-radius: 4px;
    margin: 0;
}
._css-time-detail div.el-input{
    border:none !important;
    padding: 0 4px 0 0;
    margin: 0;
}
._css-time-detail div.pausemodel:hover{
    opacity:1;
}
._css-time-detail div.pausemodel{
  background: #FAAD14;
  color: #fff;
  border-color: #FAAD14;
   opacity:.7;
}
._css-time-detail div.stopmodel{
  background: #F5222D;
  color: #fff;
  border-color: #F5222D;
   opacity:.7;
}
._css-time-detail div.stopmodel:hover{
    opacity:1;
}
._css-time-detail div.playmodel{
  background: #007AFF;
  color: #fff;
  border-color: #007AFF;
   opacity:.7;
}
._css-time-detail div.playmodel:hover{
    opacity:1;
}
._css-timeline{
    flex:1;
    display:flex;
    background: #ECEEF0;
    border-radius: 4px;
    margin: 0 20px;
}
._css-time-btn{
    flex:1;
    display:flex;
}
._css-timeline p{
    margin: 6px 0 6px 10px;  
    font-weight: 500;
    color: #283A4F;
    font-size: 15px;
} 
._css-timeline p:last-child{
    margin: 6px 10px 6px 20px;  
    font-weight: 500;
    color: #283A4F;
    font-size: 15px;
} 
._css-el-slider{
    flex:1;
}
._css-el-slider /deep/ .el-slider__button{
    width: 4px;
    height: 16px;
    border: none;
    background: #007AFF;
    border-radius: 0;
}
._css-el-slider /deep/ .el-slider__runway{
    border: 12px solid #ECEEF0;
    border-right: 10px solid #ECEEF0;
    border-left: 10px solid #ECEEF0;
    margin:0;
    width: 100%;
    background: #A6AEB6;
}

._css-legend{
    position: fixed;
    top: 20%;
    left: 2%;
    text-align: left;
}
._css-legend-big{
    position: absolute;
    top: 20%;
    left: 3%;
    text-align: left;
}
._css-legend li,._css-legend-big li{
    line-height: 26px;
}
._css-color{
    display: inline-block;
    width: 20px;
    height: 8px;
    border-radius: 1px;
    margin-right: 5px;
}

.slider-list-content{
    position: fixed;
    top: 35%;
    left: 2%;
    text-align: left; 
    min-width:300px;
    max-height: 500px;
    max-width: 600px;
    background: rgba(0, 0, 0, 0.5);
    border-radius: 10px;
    color: #fff;
    padding: 12px;
}
.slider-list-content-big{
    position: absolute;
    top: 35%;
    left: 3%;
    text-align: left; 
    min-width:300px;
    max-height: 500px;
    max-width: 600px;
    background: rgba(0, 0, 0, 0.5);
    border-radius: 10px;
    color: #fff;
    padding: 12px;
    pointer-events: all;
}
.list-progress{
    max-height: 500px;
    overflow: auto;
}
.slider-list-content p,.slider-list-content-big p {
    line-height: 24px;
}
.model-loading{
   width: 250px;
   height: 100px;
   overflow: hidden;
}
div.ban-click{
  background: rgba(0, 0, 0, .25) !important;
  color:#fff !important;
  cursor: not-allowed !important;
  border: 1px solid transparent !important;
}
._css-play-time-text{
    position: absolute;
    left: 3%;
    top: 27px;
    width: 215px;
    height: 34px;
    background: rgba(0,0,0,0.65);
    color: #fff;
    line-height: 34px;
    border-radius: 6px;
    font-size: 15px;
}
/* 
._css-model-progress{
    width: 100%;
    height: 100%;
    position: fixed;
    z-index: 1000;
    top:0;
    left: 0;
    background: #f7f7f7;
}
._css-model-progress-big{
    position: absolute;
    z-index: 1000;
    top:20px;
    left: 20px;
    right: 20px;
    bottom: 20px;
    pointer-events: none;
}
._css-model-close{
    position: fixed;
    right: 10px;
    top: 10px;
    z-index:1000;
}
._css-model-close:hover{
    cursor: pointer;
}
.model-iframe{
    width: 100%;
    height: calc(100% - 100px);
}
.content-list p{
    line-height: 30px;
}
.content-list{
    height: 200px;
    overflow-y: auto;
}
.content-list /deep/ .el-divider--horizontal{
    margin: 10px 0;    
}
 */
</style>
<style lang="stylus" rel="stylesheet/stylus">
.cost-legend{
    p{
        width: 64px;
        height: 28px;
        line-height: 28px;
        background: #FE3D6A;
        border-radius: 4px;
        opacity: 0.5;
        text-align:center;
        color:#fff;
        margin-top:10px;
    }
    p:last-child{
        background: #00925E;
    }
}
</style>