<template>
    <div :class="headerHide ? '_css-legend' : '_css-legend-big'">
        <slot></slot>
    </div>
</template>
<script> 
// 模拟图例组件
export default {
    name: 'modelPlayLeftLegend',
    data() {
        return { 
            
        };
    },
    props: {
        headerHide: {
            type: Boolean,
            required: false
        },
    },
    filters: {
    },
    components: {
         
    },
    
    mounted() {

    },
    methods: {
        
    },
    beforeDestroy() { 
    },
    destroyed() { 
    }


}
</script>
<style scoped>
._css-legend{
    position: fixed;
    top: 80%;
    left: 20px;
    text-align: left;
}
._css-legend-big{
    position: absolute;
    top: 80%;
    left: 3%;
    text-align: left;
}
._css-legend li,._css-legend-big li{
    line-height: 26px;
}






.slider-list-content{
    position: fixed;
    top: 35%;
    left: 2%;
    text-align: left; 
    min-width:300px;
    max-height: 500px;
    max-width: 600px;
    background: rgba(0, 0, 0, 0.5);
    border-radius: 10px;
    color: #fff;
    padding: 12px;
}
.slider-list-content-big{
    position: absolute;
    top: 35%;
    left: 3%;
    text-align: left; 
    min-width:300px;
    max-height: 500px;
    max-width: 600px;
    background: rgba(0, 0, 0, 0.5);
    border-radius: 10px;
    color: #fff;
    padding: 12px;
    pointer-events: all;
}
.slider-list-content p,.slider-list-content-big p {
    line-height: 24px;
}
.list-progress{
    max-height: 500px;
    overflow: auto;
}
._css-model-progress{
    width: 100%;
    height: 100%;
    position: fixed;
    z-index: 1000;
    top:0;
    left: 0;
    /* background: #fff; */
    background: #f7f7f7;
}
._css-model-progress-big{
    position: absolute;
    z-index: 1000;
    top:20px;
    left: 20px;
    right: 20px;
    bottom: 20px;
    pointer-events: none;
}
._css-model-close{
    position: fixed;
    right: 10px;
    top: 10px;
    z-index:1000;
}
._css-model-close:hover{
    cursor: pointer;
}
._css-time-line{
    pointer-events: all;
    height: 60px;
    /* padding: 60px 2% 0 3%; */
}
.model-iframe{
    width: 100%;
    height: calc(100% - 100px);
}

._css-time-slider{
    /* margin: 60px 2% 0 3%; */
    display: flex;
}

._css-time-slider /deep/ .el-slider__marks-text{
    white-space: nowrap;
}
._css-null{
    margin: 0 5%;
    text-align: left;
}
._css-time-detail{
    /* width: 150px; */
    display: flex;
    text-align: right;
    line-height: 40px;
    font-size: 14px;
    font-weight: 500;
    margin-left: 35px;
    
}
._css-time-detail:hover{
    cursor: pointer;
}
._css-time-detail div{
    padding: 0 8px;
    height: 32px;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
    -ms-flex-direction: row;
    flex-direction: row;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    border-radius: 4px;
    border: 1px solid #1890ff;
    /* color: #1890ff; */
    /* background: #f7f7f7; */
    margin-left: 16px;
    /* opacity: .7; */
    cursor: pointer;
}

._css-time-detail div.pausemodel:hover{
    opacity:1;
}
._css-time-detail div.pausemodel{
  background: #FAAD14;
  color: #fff;
  border-color: #FAAD14;
   opacity:.7;
}
._css-time-detail div.stopmodel{
  background: #F5222D;
  color: #fff;
  border-color: #F5222D;
   opacity:.7;
}
._css-time-detail div.stopmodel:hover{
    opacity:1;
}
._css-time-detail div.playmodel{
  background: #1DA48C;
  color: #fff;
  border-color: #1DA48C;
   opacity:.7;
}
._css-time-detail div.playmodel:hover{
    opacity:1;
}
._css-timeline{
    flex:1;
}
.content-list p{
    line-height: 30px;
}
.content-list{
    height: 200px;
    overflow-y: auto;
}
.content-list /deep/ .el-divider--horizontal{
    margin: 10px 0;    
}

._css-color{
    display: inline-block;
    width: 20px;
    height: 8px;
    border-radius: 1px;
    margin-right: 5px;
}



.model-loading{
   width: 250px;
   height: 100px;
   overflow: hidden;
}
div.ban-click{
  background: rgba(0, 0, 0, .25);
  color:#fff;
  cursor: not-allowed;
  border: 1px solid transparent;
}
._css-play-time-text{
    position: absolute;
    left: 3%;
    top: 27px;
    width: 215px;
    height: 34px;
    background: rgba(0,0,0,0.65);
    color: #fff;
    line-height: 34px;
    border-radius: 6px;
    font-size: 15px;
}
</style>