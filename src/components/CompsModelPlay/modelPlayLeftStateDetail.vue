<template>
    <div >    
        <div :class="headerHide?'slider-list-content':'slider-list-content-big'">
            <div v-if="!showLoading">
                <div class="_css-play-time-text">
                    时间: {{changeTime | flt_datetimefromList}}
                    &nbsp;&nbsp;&nbsp;
                    当前构件数量: {{listSetColorLength}} 
                </div>
                <slot></slot>
            </div>
            <div class="model-loading" v-if="showLoading">
                <model-loading-progress 
                    class="model-loading" 
                    :percentage="loadingPercentage" 
                    :loadingtxt='showLoading'>
                </model-loading-progress>
            </div>
        </div> 
    </div>
</template>
<script> 
import modelLoadingProgress from '../Home/ProjectBoot/modelLoadingProgress'
//  模拟组件，左边展示详情
export default {
    name: 'modelPlayLeftStateDetail',
    data() {
        return { 
            loadingPercentage: 0,  // 模型加载loading
            showLoading: true,
        };
    },
    props: {
        /*
        headerHide: 是否是大屏进入
        listSetColorLength: 当前构件数量，应该为slot中数组的length
        changeTime: 播放时候当前时间
        */
        headerHide: {
            type: Boolean,
            required: false
        }, 
        listSetColorLength: {
            type: Number,
            required: false
        },
        changeTime: {
            type: String,
            required: false
        } 
    },
    filters: {
        flt_datetimefromList(str) {
            var _this = this;
            var str1 = str;
            if (str) {
                str1 = str.substr(0,4) + '年' + str.substr(5,2) + '月' + str.substr(8,2) + '日'
            }
            return str1;
        }, 
    },
    watch: {
        changeTime(val){ 
            let _this = this;
            if(val.length<=0){
                _this.$emit('loadingChange',true)
                return
            } ;
           
            _this.loadingPercentage = 100;
            setTimeout(()=>{
                _this.showLoading = false;
                _this.$emit('loadingChange',false)
            },200)

        }, 
    },
    components: {
        modelLoadingProgress
    },
    
    mounted() {},
    methods: {
        
    },
    beforeDestroy() { 
    },
    destroyed() { 
    }


}
</script>
<style lang="stylus" rel="stylesheet/stylus">
.cost-legend{
    p{
        width: 64px;
        height: 28px;
        line-height: 28px;
        background: #F41515;
        border-radius: 4px;
        opacity: 0.5;
        text-align:center;
        color:#fff;
        margin-top:10px;
    }
    p:last-child{
        background: #008D7F;
    }
}
</style>
<style scoped>

.slider-list-content{
    position: absolute;
    top: 72px;
    left: 20px;
    text-align: left; 
    max-height: 500px; 
}
.slider-list-content-big{
    position: absolute;
    top: 72px;
    left: 3%;
    text-align: left; 
    min-width:300px; 
    pointer-events: all;
}
.slider-list-content p,.slider-list-content-big p {
    line-height: 28px;
}
.list-progress{
    max-height: 500px;
    background: rgba(40, 58, 79, 0.5);
    overflow: auto;
    border-radius: 4px;
    color: #fff;
    padding: 10px 20px;
    min-width:300px;
    max-width: 600px;
}
.list-progress p.cD0FF2E{
    color:#D0FF2E;
}
.list-progress p.c60FFFF{
    color:#60FFFF;
}
.list-progress p.c27FF7D{
    color:#27FF7D;
}
.list-progress p.cFFAD9E{
    color:#FFAD9E;
}
.list-progress .el-divider--horizontal{
    margin: 18px 0;
}
._css-play-time-text{
    width: auto;
    padding:0 12px;
    height: 34px;
    background: #283A4F;
    border-radius: 4px;
    color: #fff;
    line-height: 34px;
    font-size: 15px;
    margin-bottom: 10px;
}
.model-loading{
   width: 300px;
   height: 100px;
   overflow: hidden;
   top:10px;
   left:20px;
}
</style>