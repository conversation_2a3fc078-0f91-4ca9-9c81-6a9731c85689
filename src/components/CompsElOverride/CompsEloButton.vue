<template>
    <div 
    @click.stop="_onclick"
    class="_css-all"
    :class="getclassobj()"
    :style="{'width':width + 'px', 'height': height + 'px', 'background-color':bgcolor?bgcolor:'#1890FF', 'border':border?border:'1px solid transparent'}">
        <div v-if="iconclass" :class="iconclass" style="font-size:12px;
    height: 28px;
    line-height: 30px;" ></div>
        <div
        :style="{'font-size':fontSize + 'px', 'color':color}"
        >{{text}}</div>
    </div>
</template>
<script>
/*
events: {
    onclick();
}
*/
export default {
    data(){
        return {
            
        }
    },
    methods:{
        getclassobj(){
            var _this = this;
            var toretclassobj = {};
            if (_this.disabled && _this.disabled == true) {
                toretclassobj.disabled = true;
            }
            if (_this.extraclassobj){
                for (var attr in _this.extraclassobj) {
                    if (_this.extraclassobj[attr]){
                        toretclassobj[attr] = true;
                    }
                }
            }
            return toretclassobj;
        },
        _onclick(){
            var _this = this;
            if (_this.disabled && _this.disabled == true) {
                return;
            } else {
                _this.$emit("onclick");
            }
        }
    },
    props:{
        width:{
            type: Number,
            required: true
        },
        height:{
            type: Number,
            required: true
        },
        fontSize:{
            type: Number,
            required: true
        },
        color: {
            type: String,
            required: true
        },
        bgcolor: {
            type: String,
            required: false
        },
        text: {
            type: String,
            required: true
        },
        border:{
            type:String,
            required: false
        },
        disabled:{
            type: Boolean,
            required:false
        },
        extraclassobj:{
            type: Object,
            required: false
        },
        iconclass:{
            type: String,
            required: false
        }
    }
}
</script>
<style scoped>
._css-all{
    border-radius: 2px;
    /* background-color: #1890FF; */
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: space-around;
    box-sizing: border-box;
}
._css-all.disabled{
    cursor: not-allowed;
    opacity: 0.3;
}
</style>