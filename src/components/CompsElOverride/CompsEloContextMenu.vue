<template>
    <div class="_css-all">
        <div
        @click.stop="onitemclick(item.id)"
         class="_css-item" v-for="item in obj.items" :key="item.id" >
            <div class="_css-item-inner" >
                <div class="_css-item-textformat">{{item.text}}</div>
            </div>
        </div>
    </div>
</template>
<script>
/*
inputdata:
    obj: {
        items:[
            id: '',
            text: ''
        ]
    }
events:
    onitemclick(id)
*/
export default {
    data(){
        return {
            
        }
    },
    props:{
        obj:{
            type: Object,
            required: true
        }
    },
    methods:{
        onitemclick(itemid){
            var _this = this;
            _this.$emit("onitemclick", itemid);
        }
    }
}
</script>
<style scoped>
._css-all{
    width:110px;
    background:rgba(255,255,255,1);
    box-shadow:0px 1px 2px 0px rgba(0,21,41,0.12);
    border-radius:2px;
    box-sizing: border-box;
    padding-top: 4px;
    padding-bottom: 4px;
}
._css-item{
    border-bottom: 1px solid rgba(0,0,0,0.08);
    cursor: pointer;
    height:32px;
    padding-top:2px;
    padding-bottom: 2px;
    box-sizing: border-box;
    display: flex;
    align-items: center;
}

._css-item:last-child{
    border-bottom: 1px solid transparent;
}

._css-item-inner{
    color:#000;
    flex:1;
    font-size: 12px;
    text-align: left;
    /* padding-left:20px; */
    box-sizing:border-box;
    height:100%;
    display: flex;
    align-items: center;
    justify-content: space-around;
}

._css-item-inner:hover{
    background-color: rgba(0,0,0,0.04);
}

._css-item-textformat{
    width:80px;
    overflow-x: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

</style>