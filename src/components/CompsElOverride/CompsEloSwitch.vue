<template>
  <div
    class="_css-outer"
    :style="getouterStyle()"
    @click.stop="_switchstatus"
    :class="{'_css-open':obj.checkstate == '1', '_css-close':obj.checkstate != 1, '_css-notediting':editable==false}"
  >
    <div class="_css-sopen" v-if="obj.checkstate == '1'">{{openval || '开'}}</div>
    <div class="_css-sclose" v-else>{{closeval || '关'}}</div>
    <div class="_css-picker" :class="obj.checkstate == '1'?'_css-open':'_css-close'"></div>
  </div>
</template>
<script>
/*
datainput:
    status: 0
events:
    onchanging(value)
*/
export default {
  data() {
    return {};
  },
  methods: {
    getouterStyle(){
      var _this = this;
      var _s = {};
      _s["width"] = (_this.outerwidth || '44') + "px";
      return _s;
    },

    _switchstatus() {
      var _this = this;
      if (_this.editable == false) {
        return;
      }
      if (_this.obj.checkstate == "1") {
        _this.obj.checkstate = "0";
      } else {
        _this.obj.checkstate = "1";
      }
      _this.$emit("onchanging", _this.obj.checkstate);
    }
  },
  mounted() {
    var _this = this;
    window.switchvue = _this;
  },
  props: {
    obj: {
      type: Object,
      required: true
    },
    editable:{
      type: Boolean,
      required: true
    },
    openval:{
      type: String,
      required: false
    },
    closeval:{
      type: String,
      required: false
    },
    outerwidth:{
      type:Number,
      required: false
    }
  }
};
</script>
<style scoped>
._css-sopen {
  left: 8px;
  position: absolute;
}
._css-sclose {
  right: 8px;
  position: absolute;
}
._css-picker {
  border-radius: 50%;
  background-color: #fff;
  position: absolute;
  width: 18px;
  height: 18px;
  /* right:2px; */
}
._css-outer {
  
  height: 22px;
  /* background:rgba(24,144,255,1); */
  /* background-color: rgba(0,0,0,0.25); */
  border-radius: 40px;
  display: flex;
  align-items: center;
  font-size: 12px;
  color: #fff;
  justify-content: space-around;
  padding-left: 8px;
  padding-right: 8px;
  box-sizing: border-box;
  cursor: pointer;
  user-select: none;
  position: relative;
}
._css-picker._css-open {
  transition: left 500ms;
  left: calc(100% - 20px);
}
._css-outer._css-open {
  transition: background-color 500ms;
  background-color: rgba(24, 144, 255, 1);
}
._css-outer._css-open._css-notediting {
  transition: background-color 500ms;
  background-color: rgba(24, 144, 255, 0.35);
  cursor: not-allowed;
}

._css-picker._css-close {
  transition: left 500ms;
  left: 2px;
}
._css-outer._css-close {
  transition: background-color 500ms;
  background-color: rgba(0, 0, 0, 0.25);
}
._css-outer._css-close._css-notediting {
  transition: background-color 500ms;
  background-color: rgba(0, 0, 0, 0.15);
  cursor: not-allowed;
}



</style>