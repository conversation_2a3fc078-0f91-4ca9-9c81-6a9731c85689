<template>
    <div 
    @click.stop="showorhideoptions"
    class="_css-all-compsRoleSelect">
        <div class="_css-texticon-compsRoleSelect" >
            <div class="_css-text-compsRoleSelect">
                <div class="_css-text-compsRoleSelect_inner basic-font-size-content basic-font-color-normal"
                :style="{width: (innerwidth||'64px')}"
                :class="{'basic-font-color-emphasize':selectedId!=''}"
                >{{text}}</div>
            </div>
            <div class="_css-textother-compsRoleSelect">
                <div class="_css-icon-compsRoleSelect">
                    <i class="_css-icon-inner" :class="selectorClass&&selectorClass!=''?selectorClass:'icon-arrow-down_outline'"
                    :style="{color:(selecticoncolor || 'rgba(0,0,0,1)'),fontSize:(selectorSize&&selectorSize!=''?selectorSize:'12px')}"
                     ></i>
                </div>
            </div>
        </div>
        <div class="_css-options-compsRoleSelect "
        v-clickOutClose="function(){extdata._showoptions = false;}"
        :class="computeclass()"
         :style="{width:(itemWitdh&&itemWitdh!=''?itemWitdh:(width||'12px')), 'z-index':(zIndex || 1)}" >
            <div @click="_onselected(role)"  class="_css-option-outer"
            :class="clearinnersplitter==true?'_css-option-outer2':'_css-option-outer'"
            
             v-for="role in datas" :key="role.id" >
                <div class="_css-option-inner">{{role.text}}</div>
            </div>
         </div>
    </div>
</template>
<script>
/*
input:
    datas:[]
events:
    onselected(role, indexnumber)
*/
export default {
    data(){
        return {
            extdata:{
                _showoptions: false // 是否显示下拉框，即下拉框是否已处于展开状态
            }
        }
    },
    mounted(){
        var _this = this;
        window.selvue = _this;
    },
    props:{
        text:{
            type: String,
            required: false
        },
        datas:{
            type: Array,
            required: true
        },
        width:{
            type:String,
            required:true
        },
        itemWitdh:{
            type:String,
            required:false
        },
        indexnumber:{
            type: Number,
            required:false
        },
        selectedId:{
            type: String,
            required:false
        },
        innerwidth:{
            type: String,
            required:false
        },
        clearinnersplitter:{
            type: Boolean,
            required: false
        },
        zIndex:{
            type: Number,
            required:false
        },
        selecticoncolor:{
            type: String,
            required: false
        },
        selectorClass:{
            type:String,
            required: false
        },
        selectorSize:{
            type:String,
            required: false
        }
    },
    methods:{
        _onselected(role){
            var _this = this;
            _this.$emit("onselected", role);
        },
        showorhideoptions(){
            var _this = this;
            if (_this.extdata._showoptions == true) {
                _this.extdata._showoptions = false;
            } else {
                _this.extdata._showoptions = true;
            }
        },
        computeclass(){
            var _this = this;
            var obj = {};
            obj["_css-option-showing"] = _this.extdata._showoptions;
            return obj;
        }
    },
    mounted(){
        // console.log(this.selectorSize,'dsad');
    }
}
</script>
<style scoped>
._css-option-outer{
    height:32px;
    display: flex;
    align-items: center;
    cursor: pointer;
    border-top:1px solid rgba(0,0,0,0.09);
    border-bottom:1px solid rgba(0,0,0,0.09);
}

._css-option-outer2{
    height:32px;
    display: flex;
    align-items: center;
    cursor: pointer;
    border-top:0px solid rgba(0,0,0,0.09);
    border-bottom:0px solid rgba(0,0,0,0.09);
}

._css-option-outer:last-child{
    border-bottom:0px solid rgba(0,0,0,0.09);
}

._css-option-outer:first-child{
    border-top:0px solid rgba(0,0,0,0.09);
}

._css-option-inner{
    height: 24px;
    line-height: 24px;
    width: 100%;
    overflow-x: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    text-align: left;
    padding-left: 15px;
}

._css-option-outer:hover ._css-option-inner{
    background-color: rgba(0,0,0,0.04);
}

._css-icon-inner{
    width: 12px;
    height: 12px;
    font-size: 12px;
}
/* ._css-icon-compsRoleSelect{
    
} */
._css-text-compsRoleSelect{
    margin-left: 12px;
    height:100%;
    display: flex;
    align-items: center;
    width:calc(100% - 28px);
}
._css-text-compsRoleSelect_inner{
    /* width:64px;     */
    overflow-x:hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    text-align: left;
    /* color: rgba(255,255,255,1); */
    color: rgba(0,0,0,0.65);
}
._css-textother-compsRoleSelect{
    /* flex:1; */
    width:28px;
    text-align: center;
    height:100%;
    display: flex;
    align-items: center;
    justify-content: space-around;
}
._css-all-compsRoleSelect{
    width:100%;
    height:100%;
    border-radius:2px;
    cursor: pointer;
    user-select: none;
}
._css-options-compsRoleSelect{
    position:absolute;
    width:250px;
    height:0px;
    box-sizing:border-box;

    background-color:rgba(255,255,255,1);
    box-shadow:0px 1px 3px 0px rgba(0,21,41,0.12);
    border-radius:2px;
    overflow-y: auto;
}

._css-options-compsRoleSelect::-webkit-scrollbar {/*滚动条整体样式*/
  width: 6px;     /*高宽分别对应横竖滚动条的尺寸*/
  height: 6px;
}
._css-options-compsRoleSelect::-webkit-scrollbar-thumb {/*滚动条里面小方块*/
  border-radius: 5px;
  -webkit-box-shadow: inset 0 0 5px rgba(0,0,0,0.2);
  background: rgba(0,0,0,0.2);
}
._css-options-compsRoleSelect::-webkit-scrollbar-track {/*滚动条里面轨道*/
  -webkit-box-shadow: inset 0 0 5px rgba(0,0,0,0.2);
  border-radius: 5px;
  background: rgba(0,0,0,0.1);
}

._css-options-compsRoleSelect._css-option-showing{
    height:auto;
    margin-top:4px;
}

._css-texticon-compsRoleSelect{
    display: flex;
    align-items: center;
    height: 100%;
}
</style>