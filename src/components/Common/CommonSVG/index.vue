<template>
    <svg :class="svgClass" aria-hidden="true" :style="{fontSize: svgSize+'px', color: color}">
        <use :xlink:href="iconName" />
    </svg>
</template>

<script>
    export default {
        name: "CommonSVG",
        props: {
            iconClass: {//svg的名字，需要使用时只需传入svgIcons/svg目录下的svg文件的名称即可
                type: String,
                required: true
            },
            className: {
                type: String,
                default: ""
            },
            color: {
                type: String,
                default: "#98A2B3"
            },
            size: {//svg的大小
                type:[String,Number],
                default: "23"
            }
        },
        computed: {
            svgSize () {
                return this.size
            },
            iconName () {
                return `#icon-${this.iconClass}`
            },
            svgClass () {
                if (this.className) {
                    return `svg-icon ${this.className}`
                }
                return 'svg-icon'
            },
        }
    }
</script>

<style scoped lang="scss">
    .svg-icon {
        width: 1em;
        height: 1em;
        vertical-align: -0.15em;
        fill: currentColor;
        overflow: hidden;
        &:focus {
            outline: none;
        }

        &>use:focus {
            outline: none;
        }
    }
</style>