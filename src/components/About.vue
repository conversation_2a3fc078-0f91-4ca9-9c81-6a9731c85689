<template>
    <div class="_css-all">
        <div class="_css-patchitem"  v-for="patch in patchdatas" :key="patch.webpatch">
            <div class="_css-textleft _css-mainpatchshow">平台版本：{{patch.webpatch}}</div>
            <div class="_css-textleft">模型服务版本：{{patch.bimserverpatch}}</div>
            <div class="_css-textleft">平台接口版本：{{patch.webserverpatch}}</div>
            <div class="_css-desc">
                <div class="_css-descitem" v-for="(descitem,index) in patch.desc" :key="descitem.key">
                    <div>{{index + 1}}.{{descitem.text}}</div>
                    <div class="_css-descitem-inner"  v-for="(inneritem, indexin) in descitem.inneritems" :key="inneritem.key" >
                        {{indexin + 1}}.{{inneritem.text}}
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>
<script>
export default {
     data(){
         return {
             patchdatas:[]
         };
     },
     mounted(){
         var _this = this;
         _this.patchdatas = _this.getPatches();
     },
     methods:{
         getPatches(){
             var Patches = [
                  {
                     webserverpatch:'5.0.12.6209',
                     bimserverpatch:'5.0.12.441',
                     webpatch:'5.0.12.2341',
                     desc:[
                         {key:'1', text:'项目流程',
                         inneritems:[
                             {key:'1', text:'项目流程功能模块功能更新：流程模板功能维护界面及流程实例相关功能'}
                         ]
                         },
                         {key:'2', text:'项目主页',
                         inneritems:[
                             {key:'1', text:'修复了项目主页显示的模型个数与项目阶段上显示个数不匹配的问题'},
                             {key:'2', text:'项目主页补充流程数量的显示'}
                         ]}
                     ]
                 },
                 {
                     webserverpatch:'5.0.12.6168',
                     bimserverpatch:'5.0.12.437',
                     webpatch:'5.0.12.2259',
                     desc:[
                         {key:'1', text:'项目流程',
                         inneritems:[
                             {key:'1', text:'新增项目流程功能模块'}
                         ]
                         },
                         {key:'2', text:'项目主页',
                         inneritems:[
                             {key:'1', text:'项目主页的模型阶段菜单上将显示模型个数，当鼠标悬浮时，显示操作按钮'}
                         ]}
                     ]
                 },
                 {
                     webserverpatch:'5.0.12.6137', 
                     bimserverpatch:'5.0.12.434',
                     webpatch: '5.0.12.2221_Alpha',
                     desc:[
                         {key:'1',text:'项目列表',
                         inneritems:[
                             {key:'1', text:'修复项目列表搜索关键字结果不准确的问题'},
                             {key:'2', text:'项目列表排序按钮再加两种：名称正序和名称倒序，之前的配置项只影响到“默认”'}
                         ]}
                         ,{key:'2',text:'项目主页',
                         inneritems:[
                             {key:'1', text:'项目公告改为按发布时间倒序排列'},
                             {key:'2', text:'修复BUG：项目内邀请人员，同一人员可多次邀请至同一角色'}
                         ]}
                         ,{key:'3',text:'机构管理',
                         inneritems:[
                             {key:'1', text:'纠正机构管理中按账号邀请人员时，未选择人员直接点击确定的提示文本'}
                         ]}
                         ,{key:'4',text:'全景图',
                         inneritems:[
                             {key:'1', text:'全景图列表页面添加loading及无数据时的文字提示'},
                             {key:'2', text:'修复了全景图在上传单张（或小于3张）图片时不生成gis按钮的bug'},
                             {key:'3', text:'全景图批量上传改为逐张图片处理，解决了因图片总大小太大导致无法生成全景图子站点的bug'}
                             ,{key:'4', text:'修复了编辑权限不生效的问题'}
                         ]}
                        ,{key:'5',text:'问题追踪',
                         inneritems:[
                             {key:'1', text:'问题追踪标签长度过长时，详情页中显示的问题'},
                             {key:'2', text:'已修复BUG：问题追踪加载时不显示loading，无数据时，不显示文字'}
                         ]}
                        ,{key:'6',text:'项目设置',
                         inneritems:[
                             {key:'1', text:'项目设置-分享管理中添加loading显示'}
                         ]}
                         ,{key:'7',text:'构件管理',
                         inneritems:[
                             {key:'1', text:'修复构件管理分类移除后不刷新的问题'},
                             {key:'2', text:'构件管理加载时不显示loading，分类无数据时，不显示文字'}
                             ,{key:'3', text:'构件库附件现在可以点击进行在线预览了'}
                         ]}
                          ,{key:'8',text:'进度计划',
                         inneritems:[
                             {key:'1', text:'修复进度计划无法导出的问题'}
                         ]}
                     ]
                 },
                 {
                     webserverpatch:'5.0.11.5629', 
                     bimserverpatch:'5.0.11.371',
                     webpatch:'5.0.11.1641_Beta',
                       desc:[
                        {
                             key:'1',text:'机构人员管理',
                             inneritems:[
                                 {key:'1', text:'添加人员时需要指定用户名及密码'}
                             ]
                        },
                        {
                             key:'2',text:'模型列表',
                             inneritems:[
                                 {key:'1', text:'修复了模型阶段呼出菜单不收起的问题'}
                             ]
                        },
                        {
                             key:'3',text:'项目设置',
                             inneritems:[
                                 {key:'1', text:'补充了项目设置中角色名称的tooltip'}
                             ]
                        }
                    ]
                 },
                 {
                    webserverpatch:'5.0.4.5305', 
                    bimserverpatch:'5.0.4.371',
                    webpatch:'5.0.4.999_Beta',
                    desc:[
                        {
                             key:'1',text:'说明',
                             inneritems:[
                                 {key:'1', text:'版本更新5.0.4'}
                             ]
                        }
                    ]
                 }
             ];
             return Patches;
         }
     }
}
/*
?. 文档权限设置页面无法上下滚动
*/
</script>

<style scoped>
._css-mainpatchshow{
    font-size: 20px;
}
._css-textleft{
    text-align: left;
    margin-top:4px;
}
._css-descitem-inner{
    padding-left: 40px;
    font-weight: 400;
}
._css-descitem{
    text-align: left;
    margin-top:4px;
    font-weight: 600;
}
._css-patchitem{
    padding-bottom: 20px;
}
._css-all{
    padding-top:8px;
    padding-bottom:8px;
    padding-left:16px;
    padding-right:16px;
    box-sizing: border-box;
}
</style>
