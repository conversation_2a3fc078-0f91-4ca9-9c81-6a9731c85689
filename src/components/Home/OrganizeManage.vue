<template>
  <div class="_css-omall">
    <!-- 高度为100px的头部 -->
    <div class="_css-omhead">
      <!-- 左上角按钮：返回列表 -->
      <div class="_css-backtolist" @click.stop="backtolist">
        <div class="icon-suggested-close-fill _css-backtolist-icon"></div>
        <div class="_css-backtolist-text">项目列表</div>
      </div>
      <!-- //左上角按钮：返回列表 -->

      <!-- 企业设置按钮 -->
      <div class="_css-entset">
        <div class="icon-interface-set_se _css-entset-icon"></div>
        <div class="_css-entset-text">企业设置</div>

        <!-- 企业设置下方的选项 -->
        <div class="_css-entset-list">
          <div class="_css-entset-item" @click.stop="extdata.isshowing_entedit = true">设置企业名称</div>
          <div class="_css-entset-item" @click.stop="extdata.isshowing_entlogoedit = true">设置LOGO</div>
          <div class="_css-entset-item" @click.stop="extdata.isshowing_transorg = true">移交机构</div>
        </div>
        <!-- //企业设置下方的选项 -->
      </div>
      <!-- //企业设置按钮 -->

      <!-- 可切换的整体 -->
      <div class="_css-head-tabcontrol">
        <div
          class="_css-head-tabcontrol-item"
          :class="{'selected': extdata.showtype == 'teammgr'}"
          @click.stop="changeshowtype('teammgr')"
        >团队</div>
        <div
          class="_css-head-tabcontrol-item"
          :class="{'selected': extdata.showtype == 'pjmgr'}"
          @click.stop="changeshowtype('pjmgr')"
        >项目</div>
        <div
          class="_css-head-tabcontrol-item"
          :class="{'selected': extdata.showtype == 'dic'}"
          @click.stop="changeshowtype('dic')"
        >字典</div>
        <div
          class="_css-head-tabcontrol-item"
          :class="{'selected': extdata.showtype == 'form'}"
          @click.stop="changeshowtype('form')"
        >流程管理</div>
        <div
          class="_css-head-tabcontrol-item"
          :class="{'selected': extdata.showtype == 'Frms'}"
          @click.stop="changeshowtype('Frms')"
        >表单管理</div>
        <div
          class="_css-head-tabcontrol-item"
          :class="{'selected': extdata.showtype == 'menuList'}"
          @click.stop="changeshowtype('menuList')"
        >菜单管理</div>

      </div>
      <!-- //可切换的整体 -->
    </div>
    <!-- //高度为100px的头部 -->

    <!-- body 区域 -->

    <div class="_css-ombody" v-if="extdata.showtype == 'dic'">
      <!-- 直接使用字典相关组件 -->
      <CompsCompanyDic
      classname="_css-companydic"
      width="1200px"
      height="calc(100% - -16px)"
      inwidth="1200px"
      inheight="calc( 100% - 32px)"
      backgroundColor="rgba(255, 255, 255, 1)"
      :companyId="extdata.userpage_companyId"
      ></CompsCompanyDic>
      <!-- //直接使用字典相关组件 -->
    </div>
    <div class="_css-ombody" v-if="extdata.showtype == 'form'">
      <div class="css-iframe-workflow" id="id_formlist">
        <iframe v-if="workFlowUrl" width="99.5%" height="99.5%" :src="workFlowUrl" frameborder="0"  ></iframe>
      </div>
    </div>
    <div class="_css-ombody" v-if="extdata.showtype == 'Frms'">
      <div  class="css-iframe-workflow">
        <iframe v-if="workFlowUrl" width="99.5%" height="99.5%" :src="workFlowUrl" frameborder="0"  ></iframe>
      </div>
    </div>
    <div class="_css-ombody" v-if="extdata.showtype == 'pjmgr'">
      <!-- 高度为40px的按钮区域 -->
      <div class="_css-btns">
        <div class="_css-btns-inner-pj">
          <!-- 搜索项目输入框 -->
          <div class="_css-btns-search-pj">
            <CompsUsersInput
              @oninput="_oninput_project"
              placeholder="搜索项目"
              iconclass="icon-interface-search"
              :is100percent="true"
            ></CompsUsersInput>
          </div>
          <div class="right-wrapper">
            <div class="_css-pjcntshow2">已建项目：{{extdata.projectpage_usedpjcnt}}</div>
            <div class="_css-pjcntshow">授权项目：{{extdata.projectpage_authpjcnt}}</div>
          </div>

        </div>
      </div>
      <!-- //高度为40px的按钮区域 -->

      <div class="_css-data-inner css-miniscroll" id="id_pjlist">
        <div
          class="_css-user-item"
          v-for="(pj,index) in extdata.projectpage_list"
          :key="componentKey"
          @click.stop="showSetProListStyle=false"
        >
          <div class="_css-user-item-icon">
            <img class="css-h100 css-w100" :src="pj.Thumbnail">
          </div>
          <div class="_css-user-item-middlearea">
            <div class="_css-user-item-middle-top">
              <div class="_css-user-item-middle-top-name">{{pj.ProjectName}}</div>
              <div
                class="_css-user-item-middle-top-status"
                :class="pj.IsPublic ? 'ispublic':'nopublic'"
              >{{pj.IsPublic == true?'公开':'非公开'}}</div>
            </div>
            <div class="_css-user-item-middle-bottom">{{pj.CreateTime}}</div>
          </div>
          <div
            class="_css-user-item-number"
            :class="pj.IsPublic ? 'ispublic':'nopublic'"
          >{{pj.OrganizeName}}</div>
          <div class="_css-user-item-configbtn icon-interface-set_se icone-colorful" id="setting-icon" ref="gearIcon" @mouseenter="showpjinfo($event,pj,index)" @mouseleave="showSetProListStyle = false"></div>
        </div>

      </div>
    </div>

    <div class="_css-ombody" v-if="extdata.showtype == 'menuList'">
      <siteLevelMenuList :info_companyname="info_companyname" :OrganizeId="p_organizeId"></siteLevelMenuList>
    </div>
    <div class="_css-ombody" v-if="extdata.showtype == 'teammgr'">
      <!-- 高度为40px的按钮区域 -->
      <div class="_css-btns">
        <div class="_css-btns-inner">
          <!-- 新增及导入按钮 -->
          <div class="_css-btn-add">
            新增成员
            <div class="_css-btn-add-items">
              <div
                class="_css-btn-add-item"
                @click.stop="extdata.isshowing_newmember=true;extdata.adding_emailerror=false;"
              >新增成员</div>
              <div class="_css-btn-add-item" @click.stop="extdata.isshowing_import=true">导入成员</div>
              <!--<div class="_css-btn-add-item" @click="show_inviteAccount($event)">邀请账号</div>--><!-- 功能未实现，暂时注释掉 -->
              <!-- <div style="cursor:not-allowed;opacity:0.3;" class="_css-btn-add-item" >导入成员</div> -->
            </div>
          </div>
          <!-- //新增及导入按钮 -->

          <!-- 搜索成员输入框 -->
          <div class="_css-btns-search">
            <CompsUsersInput
              @oninput="_oninput"
              placeholder="搜索成员"
              iconclass="icon-interface-search"
              :is100percent="true"
            ></CompsUsersInput>
          </div>
        </div>
      </div>
      <!-- //高度为40px的按钮区域 -->

      <!-- 下方的数据区域及分页显示区域 -->
      <div class="_css-dataandpage">
        <div class="_css-data">
          <div class="_css-data-inner css-miniscroll" id="id_memlist">
            <div
              class="_css-user-item"
              v-for="item in extdata.userpage_list"
              :key="item.UserId"
              @click.stop="showmemberinfo(item)"
            >
              <div class="_css-user-item-icon">{{item.RealName | setAbbreviation}}</div>
              <div class="_css-user-item-middlearea">
                <div class="_css-user-item-middle-top">
                  <div class="_css-user-item-middle-top-name">{{item.RealName}}</div>
                  <div
                    class="_css-user-item-middle-top-status"
                    :class="getComMemClass(item)"
                  >{{getComMemText(item)}}</div>
                </div>
                <div
                  class="_css-user-item-middle-bottom"
                >{{item.Email?item.Email + '(Email)':(item.Account?item.Account+'(账号)':'-')}}</div>
              </div>
              <div
                class="_css-user-item-number"
                :class="{'enabled':item.EnabledMark != 0, 'disabled':item.EnabledMark == 0}"
              >
                {{item.EnabledMark == 0?'已禁用':'已启用'}}
              </div>
            </div>
          </div>
        </div>
        <div
          class="_css-page css-page"
          :style="{'visibility':extdata.userpage_loaded?'visible':'hidden'}"
        >
          <el-pagination
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="extdata.userpage_pageindex"
            :page-sizes="extdata.userpage_psizeoptions"
            :page-size="extdata.userpage_pagesize"
            layout="total, sizes, prev, pager, next, jumper"
            :total="extdata.userpage_total"
          ></el-pagination>
        </div>
      </div>
      <!-- //下方的数据区域及分页显示区域 -->
    </div>
    <!-- //body 区域 -->

    <div class="set-sub-menu" :style="getProListStyle()" v-if="showSetProListStyle" @mouseenter="showSetProListStyle = true" @mouseleave="showSetProListStyle = false">
      <div @click.stop="moveProjectSort(0)" v-if="clickSetIndex != 0 ">
          <i class="icon icon-new-up icon-set-font" :title="extdata.projectpage_list.length +'-'+clickSetIndex"></i>
          上移
      </div>
      <div @click.stop="moveProjectSort(1)" v-if="clickSetIndex != extdata.projectpage_list.length - 1 " >
          <i class="icon icon-new-down icon-set-font" :title="extdata.projectpage_list.length +'-'+clickSetIndex"></i>
          下移
      </div>
      <div @click.stop="editProject" >
          <i class="icon icon-interface-edit"></i>
          编辑
      </div>
      <div @click.stop="delProject">
          <i class="icon icon-interface-model-delete"></i>
          删除
      </div>
    </div>
     <CompsSingleField
      :zIndex="2"
      title="删除项目"
      warningtext="该项目内的所有内容将无法找回，请谨慎操作"
      inputicon="icon-interface-creditcard"
      placeholder="请输入该项目的名称"
      v-if="extdata.isshowing_delpj"
      @oncancel="extdata.isshowing_delpj = false"
      @onok="delpj_ok"
      @oninput="delpj_confirminput"
    ></CompsSingleField>

    <zdialog-function
			@mousedown="_stopPropagation($event)"
			:init_title="'编辑项目信息'"
			:init_zindex="1003"
			:init_innerWidth="400"
			:init_width="400"
			:init_height="500"
			init_closebtniconfontclass="icon-suggested-close"
			:init_usecustomtitlearea="false"
			@onclose="closeEditDialog"
			v-if="extdata.isshowing_pjinfo"
		>
			<div
				slot="mainslot"
				class="_css-zdialog"
				@mousedown="_stopPropagation($event)"
			>
				<div class="_css-line _css-line-name">
            <div class="_css-title ">
              项目名称：
            </div>
            <div class="_css-fieldvalue">
                <el-input
                  @mousedown="_stopPropagation($event)"
                  placeholder="请输入单位名称"
                  v-model="editInfo.name"
                ></el-input>
            </div>
        </div>
        <div class="_css-line _css-line-name">
            <div class="_css-title ">
              项目编码：
            </div>
            <div class="_css-fieldvalue">
                <el-input
                  @mousedown="_stopPropagation($event)"
                  placeholder="请输入编码"
                  v-model="editInfo.ProjectCode"
                ></el-input>
            </div>
        </div>
        <div class="_css-line _css-line-name">
            <div class="_css-title">
                项目属性：
            </div>
            <div class="_css-fieldvalue">
              <el-select
                  v-model="editInfo.isPublicValue"
                  placeholder="请选择参建单位"
                >
                  <el-option
                    v-for="(item, index) in isPublicOption"
                    :label="item.text"
                    :value="item.id"
                    :key="index"
                  ></el-option>
              </el-select>
            </div>
        </div>
        <div class="_css-line _css-line-name">
            <div class="_css-title ">
              到期时间：
            </div>
            <div class="_css-fieldvalue">
                <el-date-picker
                  v-model="editInfo.ExpiryTime"
                  type="date"
                  placeholder="到期时间">
                </el-date-picker>
            </div>
        </div>
        <div class="_css-line _css-line-name">
            <div class="_css-title">
                数据汇总：
            </div>
            <div class="_css-fieldvalue">
              <el-select
                  v-model="editInfo.IsDataSummary"
                  placeholder="请选择数据汇总"
                >
                  <el-option
                    v-for="(item, index) in isDataSummaryOption"
                    :label="item.text"
                    :value="item.id"
                    :key="index"
                  ></el-option>
              </el-select>
            </div>
        </div>
			</div>

			<div slot="buttonslot" class="_css-dialog-btn">
				<zbutton-function
					:init_text="'确定'"
					:init_fontsize="14"
					:debugmode="true"
					:init_height="'40px'"
					:init_width="'120px'"
					@onclick="editProjectFunction"
				>
				</zbutton-function>
				<zbutton-function
					:init_text="'取消'"
					:init_color="'rgba(24, 144, 255)'"
					:init_bgcolor="'#fff'"
					:init_fontsize="14"
					:debugmode="true"
					:init_height="'40px'"
					:init_width="'120px'"
					@onclick="closeEditDialog"
				>
				</zbutton-function>
			</div>
		</zdialog-function>
    <!-- 对话框区域 -->
    <CompsThreeFields
      @oninput="addmember_input"
      @onok="addmemberok"
        :init_name="''"
        :init_email="''"
        :init_mobile="''"
        :init_account="''"
        :init_pwd="''"
      :ismodify="false"
      :zIndex="2"
      :show_emailerror="extdata.adding_emailerror"
      title="新增成员"
      v-if="extdata.isshowing_newmember"
      @oncancel="_oncancel"
    ></CompsThreeFields>
    <CompsThreeFields
      @oninput="addmember_input"
      @onok="addmemberok"
      :init_name="extdata.userdetail_obj.RealName"
      :init_email="extdata.userdetail_obj.Email"
      :init_mobile="extdata.userdetail_obj.Mobile"
      :init_account="extdata.userdetail_obj.Account"
      :init_pwd="extdata.userdetail_obj.originpwd"
      :ismodify="true"
      :zIndex="3"
      title="编辑成员信息"
      v-if="extdata.isshowing_editmember"
      @oncancel="_oneditcancel"
    ></CompsThreeFields>
    <CompsStepTip
      :zIndex="2"
      title="导入成员"
      width="504px"
      v-if="extdata.isshowing_import"
      @oncancel="extdata.isshowing_import = false"
      :companyId="extdata.userpage_companyId"
      @onfinish="import_finish"
    ></CompsStepTip>
    <CompsMemberInfo
      :zIndex="2"
      :detailobj="extdata.userdetail_obj"
      :EnabledMark="extdata.userdetail_pjcountloaded"
      title="成员信息"
      width="600px"
      v-if="extdata.isshowing_memberinfo"
      @onok="saveuser"
      @oncancel="extdata.isshowing_memberinfo = false"
      @cfgbtnclick="_cfgbtn_click()"
      @onwarning="removeomUser"
    ></CompsMemberInfo>

    <CompsSingleField
      :zIndex="2"
      @onok="begin_setentname"
      :organizeShowSet="true"
      title="设置企业名称"
      :inittext="extdata.userpage_companyname"
      v-if="extdata.isshowing_entedit"
      @oncancel="extdata.isshowing_entedit = false"
    ></CompsSingleField>
    <CompsSimpleSearchList
      :zIndex="2"
      :companyId="extdata.userpage_companyId"
      showType="company"
      title="移交机构"
      tipstr="一个机构只有一位机构管理员，移交机构后现账号不再是机构管理员"
      v-if="extdata.isshowing_transorg"
      @oncancel="extdata.isshowing_transorg = false"
      @onok="transorg_ok"
    ></CompsSimpleSearchList>

    <!-- 按账号邀请 -->
    <CompsSimpleSearchList
      :zIndex="2"
      :companyId="extdata.userpage_companyId"
      showType="inviteByAccount"
      title="邀请账号"
      init_inputplaceholder="搜索账号"
      tipstr="选择人员发送邀请站内信"
      v-if="extdata.isshowing_inviteaccount"
      @oncancel="oncancel_inviteaccount"
      @onok="onok_inviteaccount"
    ></CompsSimpleSearchList>
    <!-- //按账号邀请 -->

     <div class="_css-pjinfo-imageedit" v-if="extdata.isshowing_entlogoedit == true">
      <div class="_css-pjinfo-imageedit-in" v-drag="draggreet"  :style="dragstyle">
        <!-- 在线裁切标题栏 -->
        <CompsDialogHeader @oncancel="_oncancel_entlogo" title="更换企业Logo"></CompsDialogHeader>
        <!-- //在线裁切标题栏 -->

        <!-- 裁切组件部分 -->
        <div class="_css-clipcontainer">
          <CompsImageClippingUpload
            ref="cropImgEntLogo"
            @onCrop="_onok_editentlogo_callback"
            :circularPreview="true"
            :width="368"
            :height="300"
          ></CompsImageClippingUpload>
        </div>

        <!-- //裁切组件部分 -->

        <!-- 在线裁切底部按钮 -->
        <CompsDialogBtns
          @onok="_onok_editentlogo"
          @oncancel="_oncancel_entlogo"
          :warningbtn="true"
          @onwarning="_reset_editentlogoimage"
          warningbtntext="恢复默认"
        ></CompsDialogBtns>
        <!-- //在线裁切底部按钮 -->
      </div>
    </div>


    <!-- //对话框区域 -->
  </div>
</template>
<script>
import { EventBus } from "@static/event.js";
import CompsUsersInput from "@/components/CompsAuth/CompsUsersInput";
import CompsThreeFields from "@/components/CompsCommon/CompsThreeFields";
import CompsSingleField from "@/components/CompsCommon/CompsSingleField";
import CompsStepTip from "@/components/CompsCommon/CompsStepTip";
import CompsMemberInfo from "@/components/CompsCommon/CompsMemberInfo";
import CompsSimpleSearchList from "@/components/CompsCommon/CompsSimpleSearchList";
import CompsImageClippingUpload from "@/components/CompsCommon/CompsImageClippingUpload";
import CompsCompanyDic from "@/components/CompsCompany/CompsCompanyDic";
import CompsDialogHeader from "@/components/CompsDialog/CompsDialogHeader";
import CompsDialogBtns from "@/components/CompsDialog/CompsDialogBtns";
import siteLevelMenuList from "@/components/Admin/Index/siteLevelMenuList"   // 菜单配置
export default {
  components: {
    CompsDialogHeader,
    CompsDialogBtns,
    CompsUsersInput,
    CompsThreeFields,
    CompsStepTip,
    CompsMemberInfo,
    CompsSingleField,
    CompsSimpleSearchList,
    CompsImageClippingUpload,
    CompsCompanyDic,
    siteLevelMenuList,
  },
  data() {
    return {
      componentKey: 0,
      p_organizeId: '', // 机构ID
      val:'0',
      dragstyle: {
          position: 'fixed',
          right: 'calc(50% - 255px)',
          top: 'calc(50% - 205px)'
      },
      extdata: {
        projectpage_usedpjcnt: 0, // 当前机构已使用项目个数
        projectpage_authpjcnt: 0, // 当前机构已授权项目个数
        projectpage_list: [], // 项目管理数据
        projectpage_filter_kw: "", // 项目管理关键字数据
        projectpage_projectId: "", // 项目page正在显示项目信息的项目ID
        editInfoData: {}, // 当前选中编辑的data
        userdetail_pjcountloaded: 0, // 弹出编辑的窗口时，是否启动
        userdetail_pjcountmax: 0, // 正在编辑的人员此时可被分配项目总个数（包含已分配的）
        userdetail_obj: undefined, // 正在编辑的人员的相关信息。
        adding_emailerror: false, // 正在添加人员时，邮箱错误！
        userpage_loaded: false, // 初始化加载完团队人员数据了
        userpage_psizeoptions: [], // 页码规格选项
        userpage_list: [], // 团队管理人员数据
        userpage_companyId: "", // 当前Token管理的机构ID
        userpage_companyname: "", // 当前Token管理的机构名称
        userpage_pageindex: 1, // 团队管理当前页码 （由于需要初始化userpage_total进行前端控件渲染，则需要初始化为1）
        userpage_pagesize: 100, // 团队管理pageSize （由于需要初始化userpage_total进行前端控件渲染，则需要初始化为100）
        userpage_total: 0, // 团队管理一共多少条（需要初始化时赋值！）
        showtype: "teammgr", // teammgr 团队管理， pjmgr 项目管理
        isshowing_newmember: false, // 是否正在显示新增成员对话框
        isshowing_editmember: false, // 是否正在显示编辑成员信息
        isshowing_import: false, // 是否正在显示导入对话框
        isshowing_memberinfo: false, // 是否正在显示成员信息的对话框
        isshowing_entedit: false, // 是否正在显示企业名称编辑框
        isshowing_entlogoedit: false, // 是否正在显示企业logo编辑框
        isshowing_transorg: false, // 是否正在显示移交机构对话框
        isshowing_inviteaccount: false, // 是否正在显示邀请账号对话框
        isshowing_pjinfo: false, // 是否正在显示成员信息的对话框
        filter_kw: "", // 关键字
        filter_timeoutId: "", // 搜索时使用的时间句柄
        isshowing_delpj: false, // 删除项目
      },
      showSetProListStyle: false,
      clickSetIndex: 0, // 上下移动用的index
      editListStyle:{
        left: 100,
        top: 10,
      },
      workFlowUrl: '', // 流程表单
      projectSelectOrg: "", // 选择的项目
      info_companyname: '', // 获取当前机构名称
      isPublicOption: [
        {
          text: '公开',
          id: true
        },
        {
          text: '非公开',
          id: false
        }
      ],
      isDataSummaryOption: [
        {
          text: '否',
          id: false
        },
        {
          text: '是',
          id: true
        },
      ],
      editInfo: {
        name: '',
        isPublicValue: '',
        ProjectCode: '',
        ExpiryTime: '',
        IsDataSummary: false,
      }
    };

  },
  created() {
    var _this = this;
    // 页码规格
    _this.extdata.userpage_psizeoptions = [100, 200, 300];

    // userpage_pagesize
    _this.extdata.userpage_pagesize = _this.extdata.userpage_psizeoptions[0];

    // 判断当前人是否是机构管理员，如果不是，直接返回登录页面
    // 如果是，在其内部调用函数，加载团队人员数据
    _this.testIsComMgr();
  },
  mounted() {
    this.p_organizeId =  sessionStorage['_OrganizeId']
    this.getFullName();
  },
  methods: {
    getWorkFlowToken(type) {
      let _this = this;

      // let _OrganizeId = _this.projectSelectOrg;
      let _username = _this.$staticmethod.Get("username");
      let _Token = _this.$staticmethod.Get("Token");
      let _url = `${window.bim_config.webserverurl}/api/User/User/CCFlowLogin`;
      let _para = {
          Token: _Token,
          organizeId: this.p_organizeId
      };
      _this
          .$axios({
              method: "post",
              url: _url,
              data: _para
          })
          .then(x => {
              if (x.data.Ret > 0) {
                _this.workFlowUrl= `${window.bim_config.CCFlowUrl}/WF/Portal/${type}.htm?Token=${x.data.Data}&OrgNo=${this.p_organizeId}&UserNo=${_username}`
                // window.open(_this.workFlowUrl)
              } else {
                _this.$message.error(x.data.Data);
              }
          })
          .catch(x => {
              console.error(x);
          });
    },
    // 生成guid
    S4() {   
       return (((1+Math.random())*0x10000)|0).toString(16).substring(1);   
    },
    NewGuid() {
       var _this = this;   
       return (_this.S4()+_this.S4()+"-"+_this.S4()+"-"+_this.S4()+"-"+_this.S4()+"-"+_this.S4()+_this.S4()+_this.S4());   
    },

    // 邀请账号对话框：onok
    onok_inviteaccount(toInviteUserId){
      var _this = this;
      // 为空验证
      if (!toInviteUserId) {
        _this.$message.warning(`未指定邀请账号`);
        return;
      }
      var _toInviteInOrgId = _this.extdata.userpage_companyId;
      // 调用接口（接口内容：添加一些邀请账号的记录，添加一条站内信类型的消息）
      var _url = `${window.bim_config.webserverurl}/api/Message/JPush/InviteByAccount`;
      _this.$axios({
        url: _url,
        method:'post',
        data: {
          Token: _this.$staticmethod.Get("Token"),
          UserId: toInviteUserId,
          OrganizeId: _toInviteInOrgId
        }
      }).then(x => {
        if (x.status == 200) {
          if (x.data.Ret > 0) {
            _this.$message.success('邀请成功！');
            _this.extdata.isshowing_inviteaccount = false;
          } else {
            _this.$message.error(x.data.Msg);
          }
        } else {
          console.error(x);
          debugger;
        }
      }).catch(x => {
        console.error(x);
        debugger;
      });
    },

    // 关闭邀请账号对话框
    oncancel_inviteaccount(){
      this.extdata.isshowing_inviteaccount = false;
    },

    // 显示邀请账号对话框
    show_inviteAccount(ev){
      this.extdata.isshowing_inviteaccount = true;
      if (ev) {
        ev.stopPropagation();
      }
    },

    _cfgbtn_click(){
      this.extdata.isshowing_editmember = true
    },

    draggreet(val){
      this.val = val;
    },

    //_onok_editentlogo
    // 裁切好后，点击确定
    _onok_editentlogo(){
      var _this =this;
      _this.$refs.cropImgEntLogo.cropImage();
    },

    modifyCompanyLogo(base){
      // 准备参数，调用接口修改企业logo
      var _this = this;
      _this.$axios({
        method:'post',
        url:`${this.$urlPool.ModifyOrganizeLogo}`,
        data: {
          Token: _this.$staticmethod.Get("Token"),
          ProjectId: _this.extdata.userpage_companyId,
          Thumbnail: base
        }
      }).then(x => {
        if (x.status == 200 ){
          if (x.data.Ret > 0) {
            _this.$message.success(`修改成功`);
            _this.extdata.isshowing_entlogoedit = false;
          } else {
            _this.$message.error(`修改失败(${x.data.Ret})`);
          }
        } else {
          _this.$message.error(`修改失败(${x.status})`);
        }
      }).catch(x => {
        _this.$message.error(`服务器错误`);
      });
    },

    // 在线裁切项目缩略图——确定——回调
    _onok_editentlogo_callback(base, binary) {
      // 操作确认
      var _this = this;
      _this.$confirm("确定修改当前企业Logo?", "操作确认", {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(x => {
        _this.modifyCompanyLogo(base);
      }).catch(x => {

      });
    },

    // 重置编辑企业Logo的内部控件
    _reset_editentlogoimage(){
      this.$refs.cropImgEntLogo.resetImg();
    },

    // 取消编辑企业logo
    _oncancel_entlogo(){
      var _this = this;
      _this.extdata.isshowing_entlogoedit = false;
    },

    import_finish(issuc, message) {
      var _this = this;
      if (issuc) {
        _this.$message.success(message);
      } else {
        _this.$message.error(message);
      }
      _this.extdata.isshowing_import = false;
      _this.loadCompanyMember();
    },
    // 删除项目
    confirmremove(pjid) {
      let _this = this;
      let _Token = _this.$staticmethod.Get("Token");
      let _ProjectId = pjid;
      _this
        .$axios({
          method: "post",
          url: `${
            window.bim_config.webserverurl
          }/api/User/Project/DeleteProject`,
          data: {
            Token: _Token,
            ProjectId: _ProjectId
          }
        })
        .then(x => {
          _this.$message.success("删除成功");
          _this.deleteVault(_ProjectId)
          _this.loadProjects();
        })
        .catch(x => {});
    },
    // ==新模型删除VaultID
    deleteVault(VaultID){
      this.$axios
        .get(`${this.$ip('newModelHttpUrl')}/Vault/DeleteVault?VaultID=${VaultID}`)
        .then(res => {
          // 删除新模型存储，不需要有任何提示
          if (res.status === 200) {}
        })
        .catch(err => {
          console.log(err)
        })
    },

    // 开始执行移交机构动作，并关闭对话框，然后返回到项目列表页。
    transorg_do(userId) {
      var _this = this;
      var _Token = _this.$staticmethod.Get("Token");
      _this
        .$axios({
          method: "post",
          url: `${
            window.bim_config.webserverurl
          }/api/Admin/Organize/ModifyManager`,
          data: {
            Token: _Token,
            Str0: userId,
            organizeId: _this.extdata.userpage_companyId
          }
        })
        .then(x => {
          if (x.status == 200 && x.data.Ret > 0) {
            //debugger;
            _this.$message.success("移交成功");
            window.location.href = `${window.bim_config.hasRouterFile}/#/Home/Boot/${_Token}`;
          }
        })
        .catch(x => {});
    },

    // 移交机构的 ok 动作
    transorg_ok(userId) {
      var _this = this;
      _this
        .$confirm("确定移交当前机构管理员给指定人？", "移交机构确定", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
        .then(x => {
          // 开始执行移交机构动作，并关闭对话框
          _this.transorg_do(userId);
        })
        .catch(x => {});
    },

    // 开始修改机构
    setentname(str,organizeShow) {
      // 获取必要的修改机构名称参数
      var _this = this;
      var _Token = _this.$staticmethod.Get("Token");
      var _Str0_name = str;
      var _organizeId = _this.extdata.userpage_companyId;

      _this
        .$axios({
          method: "post",
          url: `${this.$urlPool.ModifyOrganizeName}`,
          data: {
            Token: _Token,
            OrganizeName: _Str0_name,
            ProjectId: _organizeId,
            IsShowName: organizeShow
          }
        })
        .then(x => {
          if (x.status == 200 && x.data.Ret > 0) {
            // 赋值并关闭
            _this.extdata.userpage_companyname = str;
            _this.$message.success("修改成功");
            _this.extdata.isshowing_entedit = false;
          } else if (x.data.Ret < 0) {
            _this.$message.error(x.data.Msg);
          }
        })
        .catch(x => {});
    },

    // begin_setentname_ok
    begin_setentname(str,organizeShow) {
      // 弹出提示，确认是否修改
      var _this = this;
      _this
        .$confirm("确认修改当前机构为该名称？", "修改机构名称", {
          confirmButtonText: "确认",
          cancelButtonText: "取消",
          type: "warning"
        })
        .then(x => {
          _this.setentname(str,organizeShow);
        })
        .catch(x => {});
    },

    //
    saveuser(tomodifypjcnt, checkstate) {
      // 收集要修改的所有信息，准备访问接口
      var _this = this;
      var _Str0_userId = _this.extdata.userdetail_obj.UserId;
      var _Str1_name = _this.extdata.userdetail_obj.RealName;
      var _Str2_email = _this.extdata.userdetail_obj.Email;
      var _Str3_mobile = _this.extdata.userdetail_obj.Mobile;
      var _Str4_pjcnt = tomodifypjcnt;

      if (checkstate == "1") {
        _Str4_pjcnt = 1;
      } else {
        _Str4_pjcnt = 0;
      }

      var _Token = _this.$staticmethod.Get("Token");

      // 编辑用户信息
      _this
        .$axios({
          method: "post",
          url: `${window.bim_config.webserverurl}/api/User/User/ModifyUser?token=${this.$staticmethod.Get("Token")}`,
          data: {
            UserId: _Str0_userId,
            RealName: _Str1_name,
            Email: _Str2_email,
            Mobile: _Str3_mobile,
            OrganizeId: _this.extdata.userpage_companyId,
            Account: _this.extdata.userdetail_obj.Account,
            Password: _this.extdata.userdetail_obj.originpwd == ''? '': _this.extdata.userdetail_obj.Password,
            EnableMark: _Str4_pjcnt,    // 是否启用。启用=1  不启用=0
            // Password 如果填写的密码为空，则直接传空，否则传加密后的新密码
          }
        })
        .then(x => {
          if (x.status == 200) {
            if (x.data.Ret > 0) {
              // 刷新
              _this.extdata.isshowing_memberinfo = false;
              _this.$message.success("修改成功！");
              _this.loadCompanyMember();
            } else {
              _this.extdata.isshowing_memberinfo = false;
              _this.$message.error(x.data.Msg);
              _this.loadCompanyMember();
            }
          } else {
            _this.$message.error(x.data.Msg);
          }
        })
        .catch(x => {});
    },

    // 删除人员并刷新
    removeUser_Refresh(userId) {
      let _this = this;
      let _Token = _this.$staticmethod.Get("Token");
      _this
        .$axios({
          method: "post",
          url: `${window.bim_config.webserverurl}/api/User/User/DeleteUser?userId=${userId}&token=${_Token}`,
        })
        .then(x => {
          if (x.status == 200 && x.data.Ret > 0) {
            _this.$message.success("删除成功");
            _this.extdata.isshowing_memberinfo = false;
            _this.loadCompanyMember();
          } else {
          }
        })
        .catch(x => {});
    },

    // 开始操作删除人员
    removeomUser() {
      // 弹出提示，确认是否删除当前人员
      var _this = this;

      _this
        .$confirm("确定删除该账号？", "操作确认", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
        .then(x => {
          // 开始执行删除人员，执行后刷新
          _this.removeUser_Refresh(_this.extdata.userdetail_obj.UserId);
        })
        .catch(x => {});
    },

    // addmember_input  ====这是干啥的  还没开始请求就return
    addmember_input(typestr, str) {
      var _this = this;
      return;
      console.log(typestr, str);

      if (typestr == "email" && str && str != "") {
        // 发请求判断 email 是否已存在其它机构
        var _Token = _this.$staticmethod.Get("Token");
        var _email = str;

        _this
          .$axios({
            method: "get",
            url: `${
              window.bim_config.webserverurl
            }/api/User/User/OnlyTestEmailExists?Token=${_Token}&email=${_email}`
          })
          .then(x => {
            if (x.status == 200) {
              if (x.data.Ret > 0) {
                if (x.data.Data.IsExists == true) {
                  // 邮箱重复
                  _this.extdata.adding_emailerror = true;
                } else {
                  _this.extdata.adding_emailerror = false;
                }
              } else {
              }
            } else {
            }
          })
          .catch(x => {});
      }
    },

    // addmemberok
    addmemberok(name, email, mobile, account, pwd, ismodify) {
      // 计算用于网络传输的 _Password
      var _this = this;
      var _Password = _this.$staticmethod.computepwdstr(
          pwd + _this.$configjson.publickey,
          _this.$CryptoJS
      );
      EventBus.$emit("R_InitiateProblem",true);

      // 新增
      if (ismodify == false) {
        // 直接调用接口保存数据
        _this.addmember_add(name, email, mobile, account, pwd, _Password);
      } else {
        // 将填写的值记入内存
        _this.editmember_edit(name, email, mobile, account, pwd, _Password);
      }
    },

    editmember_edit(name, email, mobile, account, pwd, _Password) {
      var _this = this;
      if (
        name == undefined ||
        name == "" ||
        email == undefined ||
        email == ""
      ) {
        _this.$message.error("姓名及邮箱地址不能为空");
        return;
      }

      //debugger; // 只是修改弹层中的内容，并没有保存到DB
      _this.extdata.userdetail_obj.RealName = name;
      _this.extdata.userdetail_obj.Email = email;
      _this.extdata.userdetail_obj.Mobile = mobile;
      _this.extdata.userdetail_obj.Account = account;
      _this.extdata.userdetail_obj.Password = _Password;
      _this.extdata.userdetail_obj.originpwd = pwd;
      _this.extdata.isshowing_editmember = false;

    },

    // 进行新增人员操作
    addmember_add(name, email, mobile, account, pwd, _Password) {
      var _this = this;
      var _Token = _this.$staticmethod.Get("Token");
      var _organizeId = _this.extdata.userpage_companyId;
      // 新增-创建用户信息
      _this
        .$axios({
          method: "post",
          url: `${
            window.bim_config.webserverurl
          }/api/User/User/CreateUser?token=${_Token}`,
          data: {
            RealName: name,
            Email: email,
            Mobile: mobile,
            organizeId: _organizeId,
            Account: account,
            Password: _Password,
          }
        })
        .then(x => {
          if (x.status == 200) {
            if (x.data.Ret > 0) {
              // 刷新
              _this.extdata.isshowing_newmember = false;
              _this.$message.success("添加成功！");
              _this.loadCompanyMember();
            } else {
              //debugger;
              _this.$message.error(x.data.Msg);
            }
          } else {
          }
        })
        .catch(x => {});
    },

    // 计算人员激活的class
    // 只要DeleteMark为1，就为无效
    getComMemText(item) {
      if (item.DeleteMark == 1) {
        return "无效"; // 无效
      } else {
        // EnabledMakr == 1时，为已激活，否则为待激活
        if (item.EnabledMark == 1) {
          return "已激活"; // 已激活
        } else {
          return "待激活"; // 待激活
        }
      }
    },
    // 计算人员激活的class
    // 只要DeleteMark为1，就为无效
    getComMemClass(item) {
      if (item.DeleteMark == 1) {
        return "invalid"; // 无效
      } else {
        // EnabledMakr == 1时，为已激活，否则为待激活
        if (item.EnabledMark == 1) {
          return "actived"; // 已激活
        } else {
          return ""; // 待激活
        }
      }
    },

    // 内部调用的根据机构ID加载人员
    loadCompanyMember() {
      // axios
      var _this = this;
      var LoadingIns = _this.$loading({
        text: "加载中",
        target: document.getElementById("id_memlist")
      });

      var _Token = _this.$staticmethod.Get("Token");
      var _organizeId = _this.extdata.userpage_companyId;
      var _pageIndex = _this.extdata.userpage_pageindex;
      var _pageSize = _this.extdata.userpage_pagesize;

      _this
        .$axios({
          method: "get",
          url: `${this.$urlPool.GetOrganizeUserPaged}?Token=${_Token}&OrganizeId=${_organizeId}&PageNum=${_pageIndex}&PageSize=${_pageSize}&KeyWord=${
            _this.extdata.filter_kw
          }`
        })
        .then(x => {
          if (x.status == 200) {
            if (x.data.Ret > 0) {
              _this.extdata.userpage_total = x.data.Data.total;
              _this.extdata.userpage_list = x.data.Data.list;
              //console.log(_this.extdata.userpage_list);
              LoadingIns.close();
            } else {
              _this.$message.error(`服务器错误(${x.data.Ret})`);
              LoadingIns.close();
            }
          } else {
            _this.$message.error(`服务器错误(${x.status})`);
            LoadingIns.close();
          }
          _this.extdata.userpage_loaded = true;
        })
        .catch(x => {
          _this.$message.error(`服务器错误`);
          LoadingIns.close();
        });
    },

    // 处理不是机构管理员的情况
    processNotComMgr() {
      window.location.href = `${window.bim_config.hasRouterFile}/#/`;
    },

    // 需要考虑system
    testIsComMgr() {
      var _this = this;
      var _Token = _this.$staticmethod.Get("Token");
      _this
        .$axios({
          method: "get",
          url: `${this.$urlPool.TestTokenIsComMgr}?Token=${_Token}`
        })
        .then(x => {
          if (x.status == 200 && x.data.Ret > 0) {
            if (x.data.Data != null && x.data.Data.OrganizeId != null) {
              // 是机构管理员
              _this.extdata.userpage_companyId = x.data.Data.OrganizeId;
              _this.extdata.userpage_companyname = x.data.Data.FullName;
              _this.loadCompanyMember();
            } else {
              // 不是机构管理员
              _this.processNotComMgr();
            }
          } else {
            // 不是机构管理员
            _this.processNotComMgr();
          }
        })
        .catch(x => {
          // 不是机构管理员
          _this.processNotComMgr();
        });
    },

    // showmemberinfo
    showmemberinfo(item) {
      let  userId = item.UserId;
      // 根据 userId 查询到用户所有相关信息
      var _this = this;
      _this.extdata.userdetail_obj = undefined;

      // 从当前页面中找到对应的人员数据
      // 但是 userdetail_pjcountmax 需要通过接口获取
      var index = _this.extdata.userpage_list.findIndex(
        x => x.UserId == userId
      );
      if (index < 0) {
        return;
      }
      var userobj = _this.extdata.userpage_list[index];
      userobj.originpwd = '';
      //debugger;
      _this.extdata.userdetail_obj = _this.$staticmethod.DeepCopy(userobj);
      _this.extdata.userdetail_pjcountloaded = item.EnabledMark;
      _this.extdata.isshowing_memberinfo = true;

    },

    // showpjinfo
    showpjinfo(ev,pj,index) {
      const gearElement = this.$refs.gearIcon;
      if (gearElement) {
        // 获取元素相对于视口的位置
        const rect = gearElement[index].getBoundingClientRect();
        // 获取页面滚动距离
        const scrollLeft = window.pageXOffset || document.documentElement.scrollLeft;
        const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
        // 计算绝对位置（相对于页面）
        const gearPosition = {
          x: Math.round(rect.left + scrollLeft) - 230,
          y: Math.round(rect.top + scrollTop)
        };
        this.editListStyle.left = gearPosition.x;
        this.editListStyle.top = gearPosition.y;
      }
      this.clickSetIndex = index;
      this.extdata.projectpage_projectId = pj.ProjectId;
      this.extdata.editInfoData = pj;
      this.showSetProListStyle = true;
    },

    // _oncancel
    _oncancel() {
      var _this = this;
      _this.extdata.isshowing_newmember = false;
    },

    // _oneditcancel
    _oneditcancel() {
      var _this = this;
      _this.extdata.isshowing_editmember = false;
    },

    // handleCurrentChange
    handleCurrentChange(pageindex) {
      var _this = this;
      _this.extdata.userpage_pageindex = pageindex;
      _this.loadCompanyMember();
    },

    // handleSizeChange
    handleSizeChange(pagesize) {
      var _this = this;
      _this.extdata.userpage_pageindex = 1;
      _this.extdata.userpage_pagesize = pagesize;
      _this.loadCompanyMember();
    },

    // 搜索项目输入框oninput
    _oninput_project(str) {
      var _this = this;

      // 延时请求接口
      if (str.length >= 0) {
        clearTimeout(_this.timeoutId);
        _this.timeoutId = setTimeout(function() {
          _this.extdata.projectpage_filter_kw = str;
          _this.loadProjects();
        }, 500);
      } else {
        _this.extdata.projectpage_filter_kw = "";
        _this.loadProjects();
      }
    },

    // 搜索成员输入框oninput
    _oninput(str) {
      var _this = this;

      // 延时请求接口
      if (str.length >= 0) {
        clearTimeout(_this.timeoutId);
        _this.timeoutId = setTimeout(function() {
          _this.extdata.filter_kw = str;
          _this.loadCompanyMember();
        }, 500);
      } else {
        _this.extdata.filter_kw = "";
        _this.loadCompanyMember();
      }
    },

    // 返回到项目列表
    backtolist() {
      var _this = this;
      window.location.href = `${window.bim_config.hasRouterFile}/#/Home/Boot/${_this.$staticmethod.Get("Token")}`;
    },
    loadAuthAndUsedPjCnt() {
      var _this = this;
      _this
        .$axios({
          method: "get",
          url: `${
            window.bim_config.webserverurl
          }/api/Admin/Organize/GetAuthAndUsedCnt?ProjectId=${
            _this.extdata.userpage_companyId
          }&token=${this.$staticmethod.Get("Token")}`
        })
        .then(x => {
          if (x.status == 200 && x.data.Ret > 0 && x.data.Data) {
            _this.extdata.projectpage_authpjcnt = x.data.Data.total;
            _this.extdata.projectpage_usedpjcnt = x.data.Data.used;
          }
        })
        .catch(x => {});
    },

    // 加载项目数据
    loadProjects() {
      // 准备接口必备参数
      let _this = this;
      let _Token = _this.$staticmethod.Get("Token");
      let _CompanyId = _this.extdata.userpage_companyId;
      let _keyWord = _this.extdata.projectpage_filter_kw;
      _this.loadAuthAndUsedPjCnt();
      _this
        .$axios({
          method: "get",
          url: `${
            window.bim_config.webserverurl
          }/api/User/Project/QueryProjectPaged?organizeId=${_CompanyId}&token=${_Token}&keyword=${_keyWord}&sort=&isPublic=&pageNum=1&pageSize=3000`
        })
        .then(x => {
          if (x.status == 200 && x.data.Ret > 0) {
            _this.componentKey += 1 ;
            _this.extdata.projectpage_list = x.data.Data.rows;

          }
        })
        .catch(x => {});
    },

    // 切换当前显示的是团队管理还是项目管理
    changeshowtype(typestr) {
      var _this = this;
      _this.extdata.showtype = typestr;

      // 切换时需要再次加载数据
      if (typestr == "pjmgr") {
        // 加载项目数据
        _this.loadProjects();
      } else if (typestr == "teammgr") {
      } else if (typestr == "dic") {
      } else if (typestr == 'form') {
        // 点击流程引擎先加载表单模块
        this.getWorkFlowToken('FlowTree');
      } else if (typestr == 'Frms') {
        // 点击表单模块
        this.getWorkFlowToken('Frms');
      } else if (typestr == 'menuList'){
        // 点击菜单
      }
    },
    getFullName(){
      this
        .$axios({
          method: "get",
          url: `${window.bim_config.webserverurl}/api/User/User/GetTokenComInfo?Token=${this.$staticmethod.Get("Token")}`
        })
        .then(x => {
          //debugger;
          if (x.status == 200 && x.data.Ret > 0 && x.data.Data) {
            if(x.data.Data.IsShowname) {
              this.info_companyname = x.data.Data.FullName;
            }else{
              this.info_companyname = ''
            }
          }
        })
        .catch(x => {});
    },
    // 上移下移
    moveProjectSort(num){
        // 上移0  下移1
        let params = {}
        if(num == 0){
          // 上移、获取当前点击的和上一个的planid
          let upUUID = this.extdata.projectpage_list[this.clickSetIndex].ProjectId
          let upUUIDOtner = this.extdata.projectpage_list[this.clickSetIndex - 1].ProjectId

          let _upsort = this.extdata.projectpage_list[this.clickSetIndex - 1].Sort
          let _oldsort = this.extdata.projectpage_list[this.clickSetIndex].Sort
          params =  {
            baseProjectSetting:[
              {
                ProjectId: upUUID,
                Sort: _upsort
              },
              {
                ProjectId: upUUIDOtner,
                Sort: _oldsort
              }
            ]
          }
          this.SetProjectSort(params)
        }else{
          // 下移、获取当前点击的和下一个的planid
          let dowmUUID = this.extdata.projectpage_list[this.clickSetIndex].ProjectId
          let dowmUUIDOtner = this.extdata.projectpage_list[this.clickSetIndex + 1].ProjectId

          let dowmSort = this.extdata.projectpage_list[this.clickSetIndex + 1].Sort
          let dowmSortOtner = this.extdata.projectpage_list[this.clickSetIndex].Sort

          params =  {
            baseProjectSetting:[
              {
                ProjectId: dowmUUID,
                Sort: dowmSort
              },
              {
                ProjectId: dowmUUIDOtner,
                Sort: dowmSortOtner
              }
            ]
          }
          this.SetProjectSort(params)
        }

    },
    async SetProjectSort(params){
      const res = await this.$api.postSetProjectSort(params)
      if(res.Ret == 1){
        this.$message.success('编辑成功');
        this.showSetProListStyle = false
        this.loadProjects()
      }
    },

    getProListStyle(){
      let _this = this;
      let _s = {};
      if(_this.editListStyle.top > document.body.clientHeight - 80){
        _this.editListStyle.top = document.body.clientHeight - 80
      }
      _s["left"] = _this.editListStyle.left + 250 + 'px';
      _s["top"] = _this.editListStyle.top + 'px';
      return _s;
    },
    // 编辑项目
    async editProject(){
      let params = {
        ProjectId:this.extdata.editInfoData.ProjectId,
      }
      const res = await this.$api.GetProject(params)
      if(res.Ret == 1){
        let _data = res.Data;
        this.editInfo = {
          name: _data.ProjectName,
          isPublicValue: _data.IsPublic,
          ExpiryTime: _data.ExpiryTime,
          IsDataSummary: _data.IsDataSummary,
          ProjectCode: _data.ProjectCode,
        }
        this.extdata.isshowing_pjinfo = true;
      }

    },
    async editProjectFunction(){
      let params = {
        ProjectId:this.extdata.editInfoData.ProjectId,
        ProjectName: this.editInfo.name,
        IsPublic: this.editInfo.isPublicValue,
        ExpiryTime: this.editInfo.ExpiryTime,
        IsDataSummary: this.editInfo.IsDataSummary,
        ProjectCode: this.editInfo.ProjectCode,
      }
      const res = await this.$api.postSystemModifyProject(params)
      if(res.Ret == 1){
        this.$message.success('编辑成功');
        this.closeEditDialog()
        this.showSetProListStyle = false
        this.loadProjects()
      }
    },
    // 删除项目
    delProject(){
      this.extdata.isshowing_delpj = true;
    },
     // 删除项目的确认提示框中输入文本的回调
    delpj_confirminput(str) {
      this.extdata.delpj_inputting = str;
    },

    // 点击删除项目的确定按钮（暂未判断输入文本及按钮可用性）
    delpj_ok() {
      var _this = this;
      if (_this.extdata.delpj_inputting != this.extdata.editInfoData.ProjectName) {
        _this.$message.error("输入的名称不正确");
        return;
      }
      _this.extdata.isshowing_delpj = false;

      this.confirmremove(this.extdata.projectpage_projectId);
      this.showSetProListStyle = false;
    },
    _stopPropagation(ev) {
			ev && ev.stopPropagation && ev.stopPropagation();
		},
    closeEditDialog(){
      this.extdata.isshowing_pjinfo=false;
      this.showSetProListStyle=false
    }

  }
};
</script>
<style scoped lang='scss'>
._css-omall /deep/ .el-button:not(.el-picker-panel__link-btn){
  padding: 12px 20px;
  margin-right: 20px;
  border-radius: 4px;
}
._css-omall /deep/ .el-select{
  border: 1px solid #dcdfe6;
  border-radius: 4px;
}
._css-clipcontainer {
  padding-left: 16px;
  padding-right: 16px;
}
._css-pjinfo-imageedit-in {
  width: 510px;
  min-height: 410px;
  background-color: #fff;
  box-shadow: 0px 13px 24px -17px rgba(11, 41, 62, 0.8);
  border-radius: 4px;
}
._css-pjinfo-imageedit {
  position: fixed;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.3);
  align-items: center;
  justify-content: space-around;
  display: flex;
  z-index: 2;
}
._css-pjcntshow2 {
  height: 38px;
  margin-right: 16px;
  line-height: 38px;
  color: rgba(0, 0, 0, 0.45);
  font-size: 12px;
}
._css-pjcntshow {
  height: 38px;
  line-height: 38px;
  color: rgba(0, 0, 0, 0.45);
  font-size: 12px;
}
._css-entset:hover ._css-entset-list {
  display: block;
}
._css-entset-list {
  position: absolute;
  padding-top: 4px;
  padding-bottom: 4px;
  background-color: rgba(255, 255, 255, 1);
  box-shadow: 0px 1px 3px 0px rgba(0, 21, 41, 0.12);
  border-radius: 2px;
  width: 100%;
  top: calc(100% + 0px);
  left: 0;
  display: none;
  z-index: 2;
}
._css-user-item-configbtn {
  position: absolute;
  right: 24px;
  font-size: 20px;
  display: none;
  cursor: pointer;
  width: 32px;
  height: 32px;
  line-height: 32px;
  text-align: center;
}
._css-user-item:hover ._css-user-item-configbtn {
  display: block;
}
._css-user-item-configbtn:hover {
  background-color: rgba(0, 0, 0, 0.04);
}
._css-user-item:hover {
  background-color: rgba(0, 0, 0, 0.02);
}
._css-user-item-number {
  font-size: 12px;
}
._css-user-item-number.enabled {
  color: rgba(24, 144, 255, 1);
}
._css-user-item-number.disabled {
  color: rgba(245, 34, 45, 1);
}
._css-user-item {
  height: 64px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.04);
  width: 100%;
  display: flex;
  align-items: center;
  position: relative;
  cursor: pointer;
}
._css-user-item-icon {
  background-color: #282e3d;
  color: #fff;
  font-size: 12px;
  width: 40px;
  height: 40px;
  line-height: 40px;
  border-radius: 4px;
  margin-left: 24px;
}
._css-user-item-middlearea {
  height: 44px;
  margin-left: 8px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
._css-user-item-middle-top {
  height: 20px;
  width: 100%;
  display: flex;
  align-items: center;
}
._css-user-item-middle-top-name {
  font-size: 12px;
  color: rgba(0, 0, 0, 0.85);
  font-weight: 600;
}
._css-user-item-middle-top-status,
._css-user-item-middle-top-status.ispublic {
  font-size: 12px;
  margin-left: 16px;
  width: 60px;
  height: 20px;
  line-height: 22px;
  border-radius: 2px;
  border: 1px solid rgba(24, 144, 255, 0.45);
  background-color: rgba(24, 144, 255, 0.1);
  color: rgba(24, 144, 255, 1);
}
._css-user-item-middle-top-status.actived {
  border: 1px solid rgba(24, 144, 255, 1);
  background-color: rgba(24, 144, 255, 1);
  color: #fff;
}
._css-user-item-middle-top-status.invalid,
._css-user-item-middle-top-status.nopublic {
  border: 1px solid rgba(250, 84, 28, 0.45);
  background-color: rgba(250, 84, 28, 0.1);
  color: #fa541c;
}
._css-user-item-middle-bottom {
  height: 20px;
  width: 100%;
  line-height: 20px;
  color: rgba(0, 0, 0, 0.45);
  text-align: left;
}
._css-user-item-number {
  position: absolute;
  width: 128px;
  text-align: center;
  right: 68px;
}
._css-data {
  flex: 1;
  width: 100%;
  height: 90%;
}
.css-iframe-workflow{
  margin: 16px auto;
  height: calc( 100% - 10px )!important;
  width: 1200px;
}
._css-data-inner {
  height: 100%;
  width: 1200px;
  margin: 0px auto 16px;
  overflow-y: auto;
  overflow-x: hidden;
  background: #fff;
}
._css-page {
  width: 100%;
  height: 10%;
  max-height: 80px;
  display: flex;
  align-items: center;
  justify-content: space-around;
}
._css-dataandpage {
  flex: 1;
  height: 0;
  display: flex;
  flex-direction: column;
  background: #fff;
  width: 1200px;
  margin: 0 auto 16px;
  padding: 10px 0;
}
._css-btns-search {
  position: absolute;
  right: 0;
  width: 225px;
  height: 40px;
}
._css-btns-search-pj {
  width: 450px;
  height: 40px;
}
.right-wrapper{
  right: 0;
  display: flex;
  position: relative;
}
._css-btn-add-item {
  height: 40px;
  line-height: 40px;
  width: 100%;
  color: rgba(0, 0, 0, 0.85);
  cursor: pointer;
}
._css-entset-item:hover {
  background-color: rgba(0, 0, 0, 0.04);
}
._css-entset-item {
  height: 40px;
  line-height: 40px;
  width: 100%;
  color: rgba(0, 0, 0, 0.85);
  cursor: pointer;
}
._css-btn-add-item:hover {
  background-color: rgba(0, 0, 0, 0.04);
}
._css-btn-add-items {
  position: absolute;
  padding-top: 4px;
  padding-bottom: 4px;
  background-color: rgba(255, 255, 255, 1);
  box-shadow: 0px 1px 3px 0px rgba(0, 21, 41, 0.12);
  border-radius: 2px;
  width: 100%;
  top: calc(100% + 0px);
  left: 0;
  display: none;
  z-index: 2;
}
._css-btn-add:hover ._css-btn-add-items {
  display: block;
}
._css-btn-add {
  width: 120px;
  height: 40px;
  border-radius: 2px;
  background-color: #1890ff;
  color: #fff;
  line-height: 40px;
  font-size: 14px;
  cursor: pointer;
  position: absolute;
}
._css-btns {
  height: 40px;
  margin-top: 16px;
  background: #fff;
  width: 1200px;
  margin: 16px auto 0;
  padding: 12px 0 6px;
}
._css-btns._css-btns-top{
  margin-top: 0;
}
._css-btns-inner {
  height: 100%;
  width: calc(1200px - 20px);
  margin: 0 auto;
  position: relative;
}
._css-btns-inner-pj {
  height: 100%;
  width: calc(1200px - 24px);
  margin: 0 12px;
  position: relative;
  display: flex;
  justify-content: space-between;
}
._css-btns-inner-pj.flow-inner{
  justify-content: flex-start;
}
._css-backtolist {
  min-width: 98px;
  height: 32px;
  position: absolute;
  top: 20%;
  left: 20px;
  display: flex;
  align-items: center;
  cursor: pointer;
  z-index: 2;
}
._css-backtolist:hover {
  background-color: rgba(0, 0, 0, 0.04);
}
._css-backtolist-icon {
  height: 20px;
  width: 20px;
  margin-left: 8px;
  font-size: 20px;
}
._css-backtolist-text {
  height: 22px;
  margin-left: 1px;
  line-height: 22px;
  color: rgba(0, 0, 0, 0.85);
  font-size: 14px;
}
._css-entset {
  min-width: 98px;
  height: 32px;
  position: absolute;
  top: 20%;
  right: 20px;
  display: flex;
  align-items: center;
  cursor: pointer;
  z-index: 2;
}
._css-head-tabcontrol {
  position: absolute;
  bottom: 0;
  width: 100%;
  height: 40px;
  display: flex;
  justify-content: center;
}
._css-head-tabcontrol-item {
  height: 100%;
  width: 160px;
  line-height: 40px;
  box-sizing: border-box;
  cursor: pointer;
}
._css-head-tabcontrol-item.selected {
  border-bottom: 2px solid #1890ff;
  color: #1890ff;
}
._css-entset:hover {
  background-color: rgba(0, 0, 0, 0.04);
}
._css-entset-icon {
  height: 20px;
  width: 20px;
  margin-left: 8px;
  font-size: 20px;
}
._css-entset-text {
  height: 22px;
  margin-left: 1px;
  line-height: 22px;
  color: rgba(0, 0, 0, 0.85);
  font-size: 14px;
}
._css-omall {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  user-select: none;
}
._css-omhead {
  height: 10%;
  position: relative;
  background-color: rgba(255, 255, 255, 1);
  box-shadow: 0px 1px 1px 0px rgba(0, 21, 41, 0.12);
}
._css-ombody {
  height: 90%;
  background-color: rgba(240, 242, 245, 1);
  display: flex;
  flex-direction: column;
}
.body-css-width{
  width:1200px;
  margin: 16px auto;
}
.set-sub-menu{
  z-index:2;
  position: absolute;
  top: 35px;
  left:10px;
  width: 130px;
  background: #fff;
  border: 1px solid rgba(0,0,0,.1);
  border-radius: 3px;
  text-align: left;
}
.set-sub-menu div,._css-progress-btn div{
  display: flex;
  align-items: center;
  line-height: 40px;
  padding-left: 15px;
  cursor: pointer;
}
.set-sub-menu div:hover,._css-progress-btn div:hover{
  background: rgba(0,0,0,.1)
}
.icon-set-font.icon-new-down:before,.icon-set-font.icon-new-up:before{
  color: #2c3e50;
  font-size: 14px;
  font-weight:500;
  padding:2px
}
.icon{
  padding-right: 10px;
}
._css-dialog-btn {
    display: flex;
    flex-direction: row-reverse;
    height: 64px;
    align-items: center;
    box-sizing: border-box;
    padding-right: 8px;
}
._css-line {
    padding-right:24px;
    box-sizing: border-box;
    margin: 8px 0 0 0;
    display: flex;
    align-items: center;
}
._css-title{
  color: #2c3e50;
}
._css-zdialog{
  margin: 20px 20px 0;
  line-height: 1;
  ._css-fieldvalue{
    flex: 1;
    margin-left: 12px;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
  }
}
._css-omall ._css-zdialog .el-select{
  border: none;
  width: 100%;
}
</style>
