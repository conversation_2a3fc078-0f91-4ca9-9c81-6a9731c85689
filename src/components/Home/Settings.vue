<template>
  <div class="_css-omall">


    <!-- 高度为100px的头部 -->
    <div class="_css-omhead">
      <!-- 左上角按钮：返回列表 -->
      <div class="_css-backtolist" @click.stop="backtolist">
        <div class="icon-suggested-close-fill _css-backtolist-icon"></div>
        <div class="_css-backtolist-text">协作平台</div>
      </div>
      <!-- //左上角按钮：返回列表 -->

      <!-- 个人设置 -->
      <div class="_css-head-tabcontrol">
        <div class="_css-head-tabcontrol-item">个人设置</div>
      </div>
      <!-- 个人设置 -->

    </div>
    <!-- //高度为100px的头部 -->


    <!-- //body 区域 -->
    <div class="_css-ombody">
      <div class="_css-ombody-box">
        <div class="_css-box-useravatar">
          <div>{{username | setAbbreviation}}</div>
        </div>
        
        <!-- 更改头像  暂时先注掉 -->
        <!-- <div class="_css-box-changeavatar">更改头像</div> -->
        <!-- 更改头像  暂时先注掉 -->

        <div class="_css-box-text">
          <div class="_css-text-username">真实姓名</div>
          <div class="_css-text-value">{{username}}</div>
          <div class="_css-text-redact" @click.stop="redact_username">编辑用户名</div>
        </div>
        <div class="_css-box-text">
          <div class="_css-text-email">邮箱</div>
          <div class="_css-text-value">{{Email}}</div>
          <div class="_css-text-redact" @click.stop="redact_email">修改邮箱</div>
        </div>
        <div class="_css-box-text">
          <div class="_css-text-phone">手机号</div>
          <div class="_css-text-value">{{Mobile_Obj}}</div>
          <div class="_css-text-redact" @click.stop="redact_phone">修改手机号</div>
        </div>
        <div class="_css-box-text">
          <div class="_css-text-password">密码</div>
          <div class="_css-text-value">***********</div>
          <div class="_css-text-redact" @click.stop="redact_password">修改密码</div>
        </div>
      </div>     
    </div>
    <!-- //body 区域 -->


    <!-- 蒙版 -->
    <div class="_css-omall-shade" v-if="extdata.showhover" @click.stop="shade_click"></div>
    <!-- 蒙版 -->


    <!-- 模态框部分 -->

    <!-- 编辑用户名 模态框 -->
    <div class="_css-model-username" v-if="extdata.show_username">
      <div class="_css-model-header">
        <div class="_css-model-header-text">编辑用户名</div>
        <div class="_css-model-header-icon icon-suggested-close" @click.stop="close_username"></div>
      </div>
      <div class="_css-model-body">
        <compsAnimateInput
          :autofocus="true"
          v-model="input_username"
          @keyup.enter.native="enterup($event);"
          :defaultValue="input_username.value"
          prefix-icon="el-icon-search"
          labelFootnote=""
          height="70px"
          fontSize="12px"
          label="用户名">
        </compsAnimateInput>
      </div>
      <div class="_css-model-footer">
        <div class="_css-model-footer-cancel" @click.stop="close_username">取消</div>
        <div class="_css-model-footer-ok" @click.stop="username_ok">确定</div>
      </div>
    </div>
    <!-- 编辑用户名 模态框 -->

    <!-- 修改邮箱 模态框 -->
    <div class="_css-model-email _css-model-email_amend" v-if="extdata.show_email">
      <div class="_css-model-header">
        <div class="_css-model-header-text">修改邮箱</div>
        <div class="_css-model-header-icon icon-suggested-close" @click.stop="close_username"></div>
      </div>
      <div class="_css-model-body">
        <!-- <compsAnimateInput
          :autofocus="true"
          v-model="input_email"
          @keyup.enter.native="enterup($event);"
          :defaultValue="input_email.value"
          prefix-icon="el-icon-search"
          labelFootnote=""
          height="70px"
          fontSize="12px"
          label="邮箱">
        </compsAnimateInput> -->
        <compsAnimateInput
          :autofocus="true"
          v-model="input_password"
          @keyup.enter.native="enterup($event);"
          :defaultValue="input_password.value"
          prefix-icon="el-icon-search"
          labelFootnote=""
          height="70px"
          type="password"
          fontSize="12px"
          label="请输入原密码">
        </compsAnimateInput>
      </div>
      <div class="_css-model-footer">
        <div class="_css-model-footer-cancel" @click.stop="close_username">取消</div>
        <div class="_css-model-footer-ok" @click.stop="Validation_email">验证</div>
      </div>
    </div>
    <!-- 修改邮箱 模态框 -->
    
    <!-- 验证邮箱 模态框 -->
    <div class="_css-model-email" v-if="extdata.show_email_validation">
      <div class="_css-model-header">
        <div class="_css-model-header-text">修改邮箱</div>
        <div class="_css-model-header-icon icon-suggested-close" @click.stop="close_username"></div>
      </div>
      <div class="_css-model-body _css-model-body-email_validation">
        <compsAnimateInput
          :autofocus="true"
          v-model="input_new_email"
          @keyup.enter.native="enterup($event);"
          :defaultValue="input_new_email.value"
          prefix-icon="el-icon-search"
          labelFootnote=""
          height="70px"
          fontSize="12px"
          label="新邮箱">
        </compsAnimateInput>
        <!-- <compsAnimateInput
          :autofocus="true"
          v-model="input_auth_code"
          @keyup.enter.native="enterup($event);"
          :defaultValue="input_auth_code.value"
          prefix-icon="el-icon-search"
          labelFootnote=""
          height="70px"
          fontSize="12px"
          label="验证码">
        </compsAnimateInput> -->

        <!-- 发送验证码提示 -->
        <!-- <div class="send-email-code" v-show="extdata.sendAuthCode" @click.stop="Validation_email_code">发送验证码</div>
        <div class="send-email-code _css-notvalid" v-show="!extdata.sendAuthCode" >重新发送(<span>{{extdata.auth_time}}</span>S)</div> -->
        <!-- 发送验证码提示 -->

        <!-- 发送验证码后得提示信息 -->
        <!-- <div class="send-email-msg" v-if="false && extdata.validation_email_msg">验证码（已发送，未收到邮件，请查看邮箱是否正确并重新发送）</div> -->
        <!-- 发送验证码后得提示信息 -->

      </div>
      <div class="_css-model-footer">
        <div class="_css-model-footer-cancel" @click.stop="close_username">取消</div>
        <div class="_css-model-footer-ok" @click.stop="Validation_email_ok">确定</div>
      </div>
    </div>
    <!-- 验证邮箱 模态框 -->


    <!-- 修改手机号 模态框 -->
    <div class="_css-model-phone" v-if="extdata.show_phone">
      <div class="_css-model-header">
        <div class="_css-model-header-text">编辑手机号</div>
        <div class="_css-model-header-icon icon-suggested-close" @click.stop="close_username"></div>
      </div>
      <div class="_css-model-body">
        <compsAnimateInput
          :autofocus="true"
          v-model="input_phone"
          @keyup.enter.native="enterup($event);"
          :defaultValue="input_phone.value"
          prefix-icon="el-icon-search"
          labelFootnote=""
          height="70px"
          fontSize="12px"
          label="手机号">
        </compsAnimateInput>
      </div>
      <div class="_css-model-footer">
        <div class="_css-model-footer-cancel" @click.stop="close_username">取消</div>
        <div class="_css-model-footer-ok" @click.stop="phone_ok">确定</div>
      </div>
    </div>
    <!-- 修改手机号 模态框 -->

    <!-- 修改密码 模态框 -->
    <div class="_css-model-password-validation" v-if="extdata.show_password">
      <div class="_css-model-header">
        <div class="_css-model-header-text">修改密码</div>
        <div class="_css-model-header-icon icon-suggested-close" @click.stop="close_username"></div>
      </div>
      <div class="_css-model-body _css-model-body-password">
        <compsAnimateInput
          :autofocus="true"
          v-model="input_password"
          @keyup.enter.native="enterup($event);"
          :defaultValue="input_password.value"
          prefix-icon="el-icon-search"
          labelFootnote=""
          type="password"
          height="70px"
          fontSize="12px"
          label="请输入原密码">
        </compsAnimateInput>
        <compsAnimateInput
          :autofocus="true"
          v-model="input_password_new"
          @keyup.enter.native="enterup($event);"
          :defaultValue="input_password_new.value"
          prefix-icon="el-icon-search"
          labelFootnote=""
          type="password"
          height="70px"
          fontSize="12px"
          label="请输入新密码">
        </compsAnimateInput>

        <!-- 忘记密码 -->
        <!-- <div class="forget_password">忘记密码</div> -->
        <!-- 忘记密码 -->

      </div>
      <div class="_css-model-footer">
        <div class="_css-model-footer-cancel" @click.stop="close_username">取消</div>
        <div class="_css-model-footer-ok" @click.stop="validation_password">确定</div>
      </div>
    </div>
    <!-- 修改密码 模态框 -->

    <!-- 验证密码 模态框 -->
    <div class="_css-model-password-validation" v-if="extdata.show_password_validation">
      <div class="_css-model-header">
        <div class="_css-model-header-text">修改密码</div>
        <div class="_css-model-header-icon icon-suggested-close" @click.stop="close_username"></div>
      </div>
      <div class="_css-model-body _css-model-body-password">
        <compsAnimateInput
          :autofocus="true"
          v-model="input_pwd_password"
          @keyup.enter.native="enterup($event);"
          :defaultValue="input_pwd_password.value"
          prefix-icon="el-icon-search"
          labelFootnote=""
          type="password"
          height="70px"
          fontSize="12px"
          label="密码">
        </compsAnimateInput>
        <compsAnimateInput
          :autofocus="true"
          v-model="input_pwdrepeat_password"
          @keyup.enter.native="enterup($event);"
          :defaultValue="input_pwdrepeat_password.value"
          prefix-icon="el-icon-search"
          labelFootnote=""
          height="70px"
          type="password"
          fontSize="12px"
          label="重复密码">
        </compsAnimateInput>
      </div>
      <div class="_css-model-footer">
        <div class="_css-model-footer-cancel" @click.stop="close_username">取消</div>
        <div class="_css-model-footer-ok" @click.stop="validation_password_ok">确定</div>
      </div>
    </div>
    <!-- 验证密码 模态框 -->

    <!-- 模态框部分 -->
    </div>
</template>
<script>
import compsAnimateInput from "@/components/CompsCommon/compsAnimateInput"; //引入输入框组件

export default {
  components: {
    compsAnimateInput,
  },
  data() {
    return {
      extdata: {
        showhover: false,  //蒙版 默认false
        show_username: false, //模态框 编辑用户名 默认false
        show_email: false,  //模态框 修改邮箱 默认false        
        show_phone: false,  //模态框 修改手机号 默认false
        show_password: false, //模态框 修改密码 默认false
        show_email_validation: false, //模态框 验证邮箱 默认false
        show_password_validation: false,  //模态框 验证密码 默认false
        validation_email_msg: false,  //验证邮箱 提示信息  默认false
        sendAuthCode:true,/*布尔值，通过v-show控制显示‘获取按钮’还是‘倒计时’ */
        auth_time: 0, /*倒计时 计数器*/
      },
      input_username: {}, //输入框 用户名
      input_phone: {}, //输入框 手机号
      input_email: {}, //输入框 邮箱
      input_password: {}, //输入框 密码
      input_password_new: {}, //输入框 密码
      input_pwd_password: {}, //输入框 新密码
      input_pwdrepeat_password: {}, //输入框 确认密码
      input_new_email: {}, //输入框 新邮箱
      input_auth_code: {}, //输入框 验证码
      username: '', //用户名
      Email: '', //邮箱
      Mobile_Obj: '', //手机号
      IsRight: undefined, //验证密码 判断原密码是否正确 true or false
      allObjInfo: {},
    };
  },
  created() {
    this.getUserInfo()
    // var _this = this;
    // _this.username = _this.$staticmethod.Get("RealName"); //用户名
    // _this.Email = _this.$staticmethod.Get("Email"); //邮箱
    // console.log(_this.$staticmethod.Get("RealName"))
    // console.log(_this.$staticmethod.Get("Email"))
    // _this.GetUserMobile();  //手机号
  },
  mounted() {

  },
  methods: {
    getUserInfo(){
      let _this = this;
      let _Token = _this.$staticmethod.Get("Token");
      let _UserId = _this.$staticmethod.Get("UserId")
      _this.$axios.get(`${window.bim_config.webserverurl}/api/User/User/GetUserInfo?token=${_Token}&userId=${_UserId}`)
          .then(x => {
            if (x.data.Ret > 0) {
              let _data = x.data.Data;
              _this.username = _data.RealName
              _this.Email = _data.Email
              _this.Mobile_Obj = _data.Mobile
              _this.allObjInfo = _data;  // 提交的接口用到这个参数
            } 
          }).catch(x => {
            debugger;
          });
    },
    // 提交修改内容
    submitChangeInfo(RealName,Email,Mobile){
      let _this = this;
      _this
        .$axios({
          method: "post",
          url: `${window.bim_config.webserverurl}/api/User/User/ModifyUser?token=${this.$staticmethod.Get("Token")}`,
          data: {
            UserId: _this.$staticmethod.Get("UserId"),
            RealName: RealName,
            Email: Email,
            Mobile: Mobile,
            OrganizeId: this.allObjInfo.OrganizeId,
            Account: this.allObjInfo.Account,
            // Password: '',// 修改密码是另一个接口直接为''
            EnableMark: this.allObjInfo.EnabledMark,    // 是否启用
          }
        })
        .then(x => {
          if (x.status == 200) {
            if (x.data.Ret > 0) {
              // 刷新
              this.getUserInfo();
              _this.extdata.show_username = false;
              _this.extdata.show_email_validation = false;
              _this.extdata.showhover = false;
              _this.extdata.show_phone = false;
            } else {
              this.getUserInfo()
            }
          } else {
            _this.$message.error(x.data.Msg);
          }
        })
        .catch(x => {});
    },
    // 返回到项目列表
    backtolist() {
      var _this = this;
      window.location.href = `${window.bim_config.hasRouterFile}/#/Home/Boot/${_this.$staticmethod.Get("Token")}`;
    },
    // 遮罩
    shade_click(){
      var _this = this;
      _this.extdata.showhover = false;
    },
    // 更改头像

    // 编辑用户名
    redact_username(){
      var _this = this;
      _this.extdata.showhover = true;
      _this.extdata.show_username = true;
    },
    close_username(){
      var _this = this;
      _this.extdata.showhover = false;
      _this.extdata.show_username = false;
      _this.extdata.show_email = false;
      _this.extdata.show_phone = false;
      _this.extdata.show_password = false;
      _this.extdata.show_email_validation = false;
      _this.extdata.validation_email_msg = false;
      _this.extdata.show_password_validation = false;
      
    },
    // 修改邮箱
    redact_email(){
      var _this = this;
      _this.input_password = {};
      _this.extdata.showhover = true;
      // _this.extdata.show_email = true;   // 旧版逻辑


      this.extdata.show_email_validation = true;  // 新版去掉了在邮箱时候验证密码
    },
    // Validation_email(){
    //   var _this = this;
    //   _this.extdata.show_email = false;
    //   _this.extdata.show_email_validation = true;
    // },
    // Validation_e1mail_code(){
    //   var _this = this;
    //   console.log(370);
    //   _this.extdata.sendAuthCode = false;
    //   _this.extdata.validation_email_msg = true;      
    //   // 倒计时
    //   _this.extdata.auth_time = 32;
    //   var auth_timetimer =  setInterval(()=>{
    //     _this.extdata.auth_time--;
    //       if(_this.extdata.auth_time<=0){
    //         _this.extdata.sendAuthCode = true;
    //         _this.extdata.validation_email_msg = false;
    //         clearInterval(auth_timetimer);
    //         }
    //     }, 1000);
    //   // 倒计时

    // },
    // 修改手机号
    redact_phone(){
      var _this = this;
      _this.extdata.showhover = true;
      _this.extdata.show_phone = true;
    },
    // 修改密码
    redact_password(){
      var _this = this;
      _this.extdata.showhover = true;
      _this.extdata.show_password = true;
    },
    // validation_password(){
    //   var _this = this;
    //   _this.extdata.showhover = true;
    //   _this.extdata.show_password = false;
    //   _this.extdata.show_password_validation = true;
    // },


    //发送Token获取到手机号
    // GetUserMobile() {
    //   var _this = this;
    //   _this
    //     .$axios({
    //       method: "get",
    //       url: `${window.bim_config.webserverurl}/api/User/User/GetUserMobile?Token=${_this.$staticmethod.Get("Token")}`
    //     })
    //     .then(x => {
    //       if (x.status == 200 && x.data.Ret > 0) {
    //           _this.Mobile_Obj = x.data.Data;
    //           // debugger
    //         }
    //     })
    //     .catch(x => {

    //     });
    // },
    //发送Token获取到手机号 尾

    // 修改用户名确认
    
    // 修改用户名确认
    //修改用户名 邮箱 手机号 密码 接口
    //修改用户名
    username_ok() {
      this.submitChangeInfo(this.input_username.value,this.allObjInfo.Email,this.allObjInfo.Mobile)
      /*
      // 旧版本的逻辑
      var _this = this;
      var RealName = _this.input_username.value;
      console.log(_this.input_username.value)
      _this
        .$axios({
          method: "post",
          url: `${window.bim_config.webserverurl}/api/User/User/ModifyUserInfo`,
          data: _this.$qs.stringify({
            Token: _this.$staticmethod.Get("Token"),
            RealName: RealName,
            // Email: "",
            // Mobile: "",
            // PwdOld: "",
            // Pwd: "",
            // PwdRepeat: "",
            Type: "1"
          })
        })
        .then(x => {
          if (x.status == 200 && x.data.Ret > 0) {         
            _this.username = _this.input_username.value;
            _this.input_username = {};
            _this.extdata.show_username = false;
            _this.extdata.showhover = false;
            // debugger

            // Set 全局
            _this.$staticmethod.Set("RealName", RealName);
            _this.$message.success('修改用户名成功');
          }else{
            
          }
          
        })
        .catch(x => {});
      */
    },

    //修改邮箱 验证
    Validation_email() {
      /*
      var _this = this;
      _this
        .$axios({
          method: "post",
          url: `${window.bim_config.webserverurl}/api/User/User/ValidEmailAndPwd`,
          data: _this.$qs.stringify({
            Token: _this.$staticmethod.Get("Token"),
            // Email: _this.input_email.value,
            Pwd: _this.input_password.value,
          })
        })
        .then(x => {
          if (x.status == 200 && x.data.Ret > 0) {
            _this.IsRight = x.data.Data.IsRight;
            if(_this.IsRight == true){
              _this.input_email = {};
              _this.input_password = {};
              _this.extdata.show_email = false;
              _this.extdata.showhover = false;
              _this.input_new_email = {};
              _this.input_auth_code = {};
              _this.extdata.show_email_validation = true;
            // debugger
            }else{
              _this.$message.error('原密码错误');
            }      
            
          }else{
            _this.$message.error('参数错误');
          }
          
        })
        .catch(x => {});
       */ 
    },

    //发送邮箱验证码
    Validation_email_code() {
      var _this = this;
      _this.$staticmethod.debugshowlog('Settings.vue');
      _this.$staticmethod.debugshowlog(_this.input_new_email.value);
      _this
        .$axios({
          method: "get",
          url: `${
            window.bim_config.webserverurl
          }/api/User/User/SendEmailReBindCode?account=${_this.$staticmethod.Get("Account")}&Token=${this.$staticmethod.Get('Token')}&emailnew=${_this.input_new_email.value}`
        })
        .then(x => {

          // debugger;
          // _this.extdata.sendAuthCode = false;
          // _this.extdata.validation_email_msg = true;
          // _this.$message.success('验证码已发送，请查收邮件');
          if (x.status == 200) {

            if (x.data.Ret > 0) {
              _this.extdata.sendAuthCode = false;
              _this.extdata.validation_email_msg = true;
              _this.$message.success('验证码已发送，请查收邮件');
            } else {
              _this.$message.error(x.data.Msg);
            }

            // 倒计时
            _this.extdata.auth_time = 32;
            var auth_timetimer =  setInterval(()=>{
              _this.extdata.auth_time--;
                if(_this.extdata.auth_time<=0){
                  _this.extdata.sendAuthCode = true;
                  _this.extdata.validation_email_msg = false;
                  clearInterval(auth_timetimer);
                  }
              }, 1000);
            // 倒计时

          } else {
            console.error(x);
          }
        })
        .catch(x => {
          // _this.ajaxerror(x);
        });
    },


    //修改邮箱 确认
    Validation_email_ok() {
      this.submitChangeInfo(this.allObjInfo.RealName,this.input_new_email.value,this.allObjInfo.Mobile);
      /*
      var _this = this;
      _this
        .$axios({
          method: "post",
          url: `${window.bim_config.webserverurl}/api/User/User/ModifyUserInfo`,
          data: _this.$qs.stringify({
            Token: _this.$staticmethod.Get("Token"),
            Email: _this.input_new_email.value,
            EmailRebindValidateCode: _this.input_auth_code.value,
            Type: "2",
          })
        })
        .then(x => {
          if (x.status == 200 && x.data.Ret > 0) {         
            _this.Email = _this.input_new_email.value;
            _this.input_new_email = {};
            _this.extdata.show_email_validation = false;
            _this.extdata.showhover = false;
            _this.$staticmethod.Set("Email", _this.input_new_email.value);
            _this.$message.success('修改邮箱成功');
            // debugger
          }else if (x.status == 200) {
            //
            _this.$message.error(x.data.Msg);
          } else {
            console.error(x);
          }
          
        })
        .catch(x => {});
      */
    },
    
    //修改手机号
    phone_ok() {
      this.submitChangeInfo(this.allObjInfo.RealName,this.allObjInfo.Email,this.input_phone.value);

      /*
      var _this = this;
      _this
        .$axios({
          method: "post",
          url: `${window.bim_config.webserverurl}/api/User/User/ModifyUserInfo`,
          data: _this.$qs.stringify({
            Token: _this.$staticmethod.Get("Token"),
            Mobile: _this.input_phone.value,
            Type: "3"
          })
        })
        .then(x => {
          if (x.status == 200 && x.data.Ret > 0) {         
            _this.Mobile_Obj = _this.input_phone.value;
            _this.input_phone = {};
            _this.extdata.show_phone = false;
            _this.extdata.showhover = false;
            // debugger
            _this.$message.success('修改手机号成功');
          }else{
            
          }
          
        })
        .catch(x => {});
      */
    },

    //修改密码 验证
    validation_password() {
      let _this = this;
      let oldp = _this.$staticmethod.computepwdstr(
          _this.input_password.value + _this.$configjson.publickey,
          _this.$CryptoJS
      );
      let newp = _this.$staticmethod.computepwdstr(
          _this.input_password_new.value + _this.$configjson.publickey,
          _this.$CryptoJS
      );
      _this
        .$axios({
          method: "post",
          url: `${window.bim_config.webserverurl}/api/User/User/ModifyPassword`,
          data: {
            Token: _this.$staticmethod.Get("Token"),
            OldPassword: oldp,
            NewPassword: newp,
          }
        })
        .then(x => {
          if (x.status == 200 && x.data.Ret > 0) {    
            _this.$message.success(x.data.Msg);      
            _this.extdata.show_password = false;
            _this.extdata.showhover = false;
          }else{
            _this.$message.error(x.data.Msg);      
          }
          
        })
        .catch(x => {});
      /*
      var _this = this;
      _this
        .$axios({
          method: "post",
          url: `${window.bim_config.webserverurl}/api/User/User/ModifyUserInfo`,
          data: _this.$qs.stringify({
            Token: _this.$staticmethod.Get("Token"),
            PwdOld: _this.input_password.value,
            Type: "5"
          })
        })
        .then(x => {
          if (x.status == 200 && x.data.Ret > 0) {         
            // _this. = _this.input_password.value;
            _this.IsRight = x.data.Data.IsRight;
            if(_this.IsRight == true){
              _this.input_password = {};
              _this.extdata.show_password = false;
              _this.extdata.showhover = false;
              _this.extdata.show_password_validation = true;
            }else{
              _this.$message.warning('原密码错误，请重新输入');
            }

          }else{
            
          }
          
        })
        .catch(x => {});
      */
    },

    //修改密码 确认
    validation_password_ok() {
      var _this = this;
      _this
        .$axios({
          method: "post",
          url: `${window.bim_config.webserverurl}/api/User/User/ModifyUserInfo`,
          data: {
            Token: _this.$staticmethod.Get("Token"),
            Pwd: _this.input_pwd_password.value,
            PwdRepeat: _this.input_pwdrepeat_password.value,
            Type: "4"
          }
        })
        .then(x => {
          if (x.status == 200 && x.data.Ret > 0) {         
              _this.input_pwd_password = {};
              _this.input_pwdrepeat_password = {};            
              _this.extdata.showhover = false;
              _this.extdata.show_password_validation = false;
              _this.input_password.value = _this.input_pwdrepeat_password.value;  
              _this.$message.success('修改密码成功');      
          }else{
            if (x.status == 200) {
              _this.$message.error(x.data.Msg);
            } else {
               _this.$message.error('服务器错误，请查看输出日志');
               console.error(x);
            }
          }
          
        })
        .catch(x => {});
    },
    

    



  }
};
</script>
<style scoped>

._css-omall {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  user-select: none;
}

._css-omhead {
  height: 100px;
  position: relative;
  background-color: rgba(255, 255, 255, 1);
  box-shadow: 0px 1px 1px 0px rgba(0, 21, 41, 0.12);
}

._css-backtolist {
  min-width: 98px;
  height: 32px;
  position: absolute;
  /* top: 20px; */
  top: 20%;
  left: 20px;
  display: flex;
  align-items: center;
  cursor: pointer;
  z-index: 2;
}
._css-backtolist:hover {
  background-color: rgba(0, 0, 0, 0.04);
}
._css-backtolist-icon {
  height: 20px;
  width: 20px;
  margin-left: 8px;
  font-size: 20px;
}
._css-backtolist-text {
  height: 22px;
  margin-left: 1px;
  line-height: 22px;
  color: rgba(0, 0, 0, 0.85);
  font-size: 14px;
}

._css-head-tabcontrol {
  position: absolute;
  bottom: 0;
  width: 100%;
  height: 40px;
  display: flex;
  justify-content: center;
}
._css-head-tabcontrol-item {
  height: 100%;
  width: 200px;
  line-height: 40px;
  box-sizing: border-box;
  cursor: pointer;
  border-bottom: 2px solid #1890ff;
  color: #1890ff;
}

._css-ombody {
  height: calc(100% - 100px);
  background-color: rgba(240, 242, 245, 1);
  display: flex;
  flex-direction: column;
  /* border:1px solid red;
  box-sizing: border-box; */
}

._css-ombody-box{
  width: 488px;
  margin: 0 auto;
}

._css-box-useravatar {
  width: 60px;
  height: 60px;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  border-radius: 2px;
  font-size: 12px;
  color: #fff;
  background-color: #202020;
  line-height: 60px;
  /* display: flex;
  align-items: center;
  justify-content: space-around; */
  margin: 0 auto;
  margin-top: 32px;
}

._css-box-changeavatar{
  width:80px;
  height:22px;
  font-size:14px;
  font-family:PingFangSC-Regular;
  font-weight:400;
  color:rgba(24,144,255,1);
  line-height:22px;
  margin: 0 auto;
  margin-top: 16px;
  cursor: pointer;
}

._css-box-text{
  width: 100%;
  height: 22px;
  /* background: green; */
  display: flex;
  justify-content: space-between;
  text-align: left;
  margin-top: 34px;
}

._css-text-username{
  width:60px;
  height:22px;
  font-size:14px;
  font-family:PingFangSC-Regular;
  font-weight:400;
  color:rgba(0, 0, 0, 0.45);
  line-height:22px;
  /* background: red; */
}
._css-text-email{
  width:60px;
  height:22px;
  font-size:14px;
  font-family:PingFangSC-Regular;
  font-weight:400;
  color:rgba(0, 0, 0, 0.45);
  line-height:22px;
  /* background: red; */
}
._css-text-phone{
  width:60px;
  height:22px;
  font-size:14px;
  font-family:PingFangSC-Regular;
  font-weight:400;
  color:rgba(0, 0, 0, 0.45);
  line-height:22px;
  /* background: red; */
}
._css-text-password{
  width:60px;
  height:22px;
  font-size:14px;
  font-family:PingFangSC-Regular;
  font-weight:400;
  color:rgba(0, 0, 0, 0.45);
  line-height:22px;
  /* background: red; */
}

._css-text-value{
  width:300px;
  height:22px;
  font-size:14px;
  font-family:PingFangSC-Medium;
  font-weight:500;
  color:rgba(0,0,0,1);
  line-height:22px;
  /* background: white; */
}

._css-text-redact{
  width:80px;
  height:22px;
  font-size:14px;
  font-family:PingFangSC-Regular;
  font-weight:400;
  color:rgba(24,144,255,1);
  line-height:22px;
  /* background: blue; */
  cursor: pointer;
}


._css-omall-shade {
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
  background-color: #202020;
  opacity: 0.3;
  position: fixed;
}

/* 编辑用户名 */
._css-model-username{
  width:410px;
  height:218px;
  background:rgba(255,255,255,1);
  position: fixed;
  left: calc(50% - 205px);
  top: calc(50% - 109px);
  z-index: 2;
}

._css-model-header{
  width: 100%;
  height: 64px;
  /* background: yellow; */
  display: flex;
  align-items: center;
  /* justify-content: space-between;
  padding-left: 24px;
  padding-right: 24px; */
}
._css-model-body{
  width: 362px;
  /* height: 56px; */
  margin-left: 24px;
  /* background: red; */
  padding-top: 14px;
  text-align: left;
}
._css-model-footer{
  width: 100%;
  height: 84px;
  /* background: green; */
  display: flex;
  align-items: center;
}

._css-model-header-text{
  width:100px;
  height:24px;
  font-size:20px;
  font-family:PingFangSC-Medium;
  font-weight:500;
  color:rgba(0,0,0,1);
  line-height:24px;
  /* background: red; */
  margin-left: 24px;
  text-align: left;
}

._css-model-header-icon{
  width:24px;
  height:24px;
  font-size: 24px;
  color: rgba(0, 0, 0, 0.09);
  margin-left: 238px;
  cursor: pointer;
}

._css-model-footer-cancel{
  width:76px;
  height:40px;
  background:rgba(255,255,255,1);
  font-size:14px;
  font-family:PingFangSC-Regular;
  font-weight:400;
  color:rgba(0,0,0,1);
  line-height:40px;
  margin-left: 218px;
  cursor: pointer;
}
._css-model-footer-ok{
  width:76px;
  height:40px;
  background:rgba(24,144,255,1);
  font-size:14px;
  font-family:PingFangSC-Regular;
  font-weight:400;
  color:rgba(255,255,255,1);
  line-height:40px;
  margin-left: 16px;
  cursor: pointer;
}
/* 编辑用户名 */


/* 修改邮箱 */
._css-model-email{
  width:410px;
  height:238px;
  background:rgba(255,255,255,1);
  position: fixed;
  left: calc(50% - 205px);
  top: calc(50% - 144px);
  z-index: 2;
}
._css-model-email_amend{
  width:410px;
  height:218px;
  background:rgba(255,255,255,1);
  position: fixed;
  left: calc(50% - 205px);
  top: calc(50% - 109px);
  z-index: 2;
}
/* 修改邮箱 */


/* 修改手机号 */
._css-model-phone{
  width:410px;
  height:218px;
  background:rgba(255,255,255,1);
  position: fixed;
  left: calc(50% - 205px);
  top: calc(50% - 109px);
  z-index: 2;
}
/* 修改手机号 */

/* 修改密码 */
._css-model-password{
  width:410px;
  height:218px;
  background:rgba(255,255,255,1);
  position: fixed;
  left: calc(50% - 205px);
  top: calc(50% - 109px);
  z-index: 2;
}
._css-model-body-password{
  position: relative;
}
.forget_password{
  width:48px;
  height:20px;
  font-size:12px;
  font-family:PingFangSC-Regular;
  font-weight:400;
  color:rgba(0,0,0,0.45);
  line-height:20px;
  position: absolute;
  bottom: -30px;
  right: 0;
}
/* 修改密码 */

/* 验证邮箱 */
._css-model-body-email_validation{
  position: relative;
}
.send-email-code{
  width:110px;
  height:32px;
  background:rgba(24,144,255,1);
  font-size:14px;
  font-family:PingFangSC-Regular;
  font-weight:400;
  color:rgba(255,255,255,1);
  line-height:32px;
  text-align: center;
  cursor: pointer;
  position: absolute;
  bottom: 7px;
  right: 0;
}

.send-email-code._css-notvalid{
  opacity: 0.7;
  cursor:not-allowed;
}

.send-email-msg{
  width:362px;
  height:20px;
  font-size:12px;
  font-family:PingFangSC-Regular;
  font-weight:400;
  color:rgba(0,0,0,1);
  line-height:20px;
  position: absolute;
  bottom: 35px;
  right: 0;
}
/* 验证邮箱 */

/* 验证密码 */
._css-model-password-validation{
  width:410px;
  height:288px;
  background:rgba(255,255,255,1);
  position: fixed;
  left: calc(50% - 205px);
  /* top: 50%; */
  top: calc(50% - 144px);
  z-index: 2;
}
/* 验证密码 */


</style>