<template>
  <div class="model-iframe-content">
    <iframe id="scene-iframe" width="50%" height="100%" class="content-frame" src="" frameborder="0"></iframe>
    <!-- <iframe id="aaa" src="static/modelPreview.html" frameborder="0"></iframe> -->
    <iframe id="scene-iframe1" v-if="isOpenDrawing" width="50%" height="100%" class="content-frame" src="../../../static/scenemanager/#/?projectId=54b29076-a963-4a75-8b62-873d2498f828" frameborder="0"></iframe>

    <!-- 校准 -->
    <div class="center-box" v-if="isOpenDrawing">
        <img src="../../../assets/images/calibrate.png" @click="openCalibrate" />
      </div>
    <div class="calibrate-box" v-if="isOpenCalibrate">
      <div class="left-box">
        <div class="list-item">
          <div class="flex-box">
            <div class="input-item">
              坐标基点:
            </div>
            <div class="input-item mg-l8">
              <div class="input-box">
                <input class="text-input" readonly v-model="formData.longitude" />
                <span class="prefix">经度</span>
              </div>
            </div>
          </div>
          <div class="flex-box mg-t4">
            <div class="input-item">
              <div class="input-box">
                <input class="text-input" readonly v-model="formData.altitude" />
                <span class="prefix">高程</span>
              </div>
            </div>
            <div class="input-item mg-l8">
              <div class="input-box">
                <input class="text-input" readonly v-model="formData.latitude" />
                <span class="prefix">纬度</span>
              </div>
            </div>
          </div>
        </div>
        <div class="line"></div>

        <div class="list-item">
          <div class="flex-box">
            <div class="input-item">
              旋转:
            </div>
            <div class="input-item mg-l8">
              <div class="input-box">
                <input class="text-input" readonly v-model="formData.rotation[1]" />
                <span class="prefix">Y轴</span>
              </div>
            </div>
          </div>
          <div class="flex-box mg-t4">
            <div class="input-item">
              <div class="input-box">
                <input class="text-input" readonly v-model="formData.rotation[0]" />
                <span class="prefix">X轴</span>
              </div>
            </div>
            <div class="input-item mg-l8">
              <div class="input-box">
                <input class="text-input" readonly v-model="formData.rotation[2]" />
                <span class="prefix">Z轴</span>
              </div>
            </div>
          </div>
        </div>
        <div class="line"></div>

        <div class="list-item">
          <div class="flex-box">
            <div class="input-item">
              偏移量:
            </div>
            <div class="input-item mg-l8">
              <div class="input-box">
                <input class="text-input" readonly v-model="formData.offset[1]" />
                <span class="prefix">Y</span>
              </div>
            </div>
          </div>
          <div class="flex-box mg-t4">
            <div class="input-item">
              <div class="input-box">
                <input class="text-input" readonly v-model="formData.offset[0]" />
                <span class="prefix">X</span>
              </div>
            </div>
            <div class="input-item mg-l8">
              <div class="input-box">
                <input class="text-input" readonly v-model="formData.offset[2]" />
                <span class="prefix">Z</span>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="right-box">

        <div class="content">
          <div class="list-item">
            <div class="flex-box">
              <div class="input-item">
                坐标基点:
              </div>
              <div class="input-item mg-l8">
                <div class="input-box">
                  <input class="text-input" v-model="dwgFormData.longitude" />
                  <span class="prefix">经度</span>
                </div>
              </div>
            </div>
            <div class="flex-box mg-t4">
              <div class="input-item">
                <div class="input-box">
                  <input class="text-input" v-model="dwgFormData.altitude" />
                  <span class="prefix">高程</span>
                </div>
              </div>
              <div class="input-item mg-l8">
                <div class="input-box">
                  <input class="text-input" v-model="dwgFormData.latitude" />
                  <span class="prefix">纬度</span>
                </div>
              </div>
            </div>
          </div>
          <div class="line"></div>

          <div class="list-item">
            <div class="flex-box">
              <div class="input-item">
                旋转:
              </div>
              <div class="input-item mg-l8">
                <div class="input-box">
                  <input class="text-input" v-model="dwgFormData.rotation[1]" />
                  <span class="prefix">Y轴</span>
                </div>
              </div>
            </div>
            <div class="flex-box mg-t4">
              <div class="input-item">
                <div class="input-box">
                  <input class="text-input" v-model="dwgFormData.rotation[0]" />
                  <span class="prefix">X轴</span>
                </div>
              </div>
              <div class="input-item mg-l8">
                <div class="input-box">
                  <input class="text-input" v-model="dwgFormData.rotation[2]" />
                  <span class="prefix">Z轴</span>
                </div>
              </div>
            </div>
          </div>
          <div class="line"></div>

          <div class="list-item">
            <div class="flex-box">
              <div class="input-item">
                偏移量:
              </div>
              <div class="input-item mg-l8">
                <div class="input-box">
                  <input class="text-input" v-model="dwgFormData.offset[1]" />
                  <span class="prefix">Y</span>
                </div>
              </div>
            </div>
            <div class="flex-box mg-t4">
              <div class="input-item">
                <div class="input-box">
                  <input class="text-input" v-model="dwgFormData.offset[0]" />
                  <span class="prefix">X</span>
                </div>
              </div>
              <div class="input-item mg-l8">
                <div class="input-box">
                  <input class="text-input" v-model="dwgFormData.offset[2]" />
                  <span class="prefix">Z</span>
                </div>
              </div>
            </div>
          </div>
        </div>


        <div class="btns">
          <div class="btn reset" @click="isOpenCalibrate = false"></div>
          <div class="btn submit" @click="submitFormat"></div>
        </div>
      </div>
    </div>

    <div class="mask" v-if="docListHidden"></div>
    <div class="doc-dialog" v-if="docListHidden">
      <div class="header">
        <span class="text">关联文档</span>
        <span class="el-icon-close" @click="closeDocList()"></span>
      </div>

      <div class="content">
        <div class="left">
          <div class="title">项目文档</div>
          <el-tree
                :data="treedata.items"
                class="_css-customstyle"
                :class="extdata._doc_showtree?'':'css-hide'"
                lazy
                :load="loadNodeChild"
                node-key="Id"
                :props="treedata.props1"
                :expand-on-click-node="false"
                @node-collapse="node_collapse"
                @node-expand="node_expand"
                @node-click="node_click"
                ref="tree"
                :defaultExpandedKeys="_openstacks_withprocessing"
              >
                <span class="css-fc" slot-scope="{ node, data }">
                  <i class="css-icon20 css-fs18 css-fc css-jcsa css-folder icon-interface-unfolder"></i>
                  <span :title="data.FolderName" class="css-ml4">{{ node.label }}</span>
                </span>
              </el-tree>
        </div>
        <div class="right">
          <div class="_css-keyword-inputbtn css-fc css-usn">
              <input :placeholder="'请输入文档名称'"
              class="search-input"
              v-model="keyword"
              @keydown.enter="_ondocnameinput" />
              <i class="icon-interface-search"></i>
            </div>
          <div class="css-h100" style="width:374px" id="id_datamain">
            <!-- 共？个文件，选中的文件个数，操作按钮区域 -->
            <div class="_css-table-top css-usn" v-if="keyword == '' && extdata._openstacks.length > 0">
              <div class="css-h100 css-fc">
                <!-- 面包屑 -->
                <div
                  class="_css-table-top-left css-h100 css-fc css-usn css-bsb css-miniscroll css-oxa css-flex1"
                >
                  <!-- 返回上一级及分隔符 -->
                  <div
                    v-if="extdata._openstacks.length > 0"
                    @click="open_parentfolder($event);"
                    class="css-breadcrumb-item css-cp"
                  >
                    <label class="css-cp _css-backpfolder">返回上一级</label>
                  </div>
                  <div v-if="extdata._openstacks.length > 0" class="css-breadcrumb-splitter">/</div>
                  <!-- //显示“项目文档/搜索结果”及分隔符 -->
                  <!-- 所有打开过的文件夹栈 -->
                  <template v-for="(item,index) in extdata._openstacks">
                    <div
                      class="css-breadcrumb-item css-cp"
                      :key="item.FileId"
                      @click="open_current_history(index, $event);"
                    >
                      <label class="css-cp"
                        :class="{'_css-parentsfolder':index != extdata._openstacks.length - 1
                        , '_css-currentfolder':index == extdata._openstacks.length - 1}"
                      >{{item.FileName}}</label>
                    </div>
                    <div
                      :key="'_' + item.FileId"
                      v-if="index != extdata._openstacks.length - 1"
                      class="css-breadcrumb-splitter"
                    >/</div>
                  </template>
                </div>

              </div>
            </div>
            <!-- //共？个文件，选中的文件个数，操作按钮区域 -->
            <!-- 表格体区域 -->
            <div
              class="_css-table-body css-bsb"
            >
              <div
                class="css-h100 css-w100 css-bsb css-usn"
                :class="extdata.showtype != 1?'css-none':''"
              >
                <!-- 高度为40的列表头 -->
                <div class="css-h40 css-w100 css-bsb css-fc css-bglgray">
                  <!-- 复选框 -->
                  <div class="css-h100 css-ml10 css-fc">
                    <div
                      @click="list_item_head_check($event)"
                      class="css-cb css-icon14 css-bsb css-cp"
                      :class="(extdata._selectedobjs.length == extdata.tableData.length) && extdata.tableData.length > 0?'mulcolor-interface-checkbox-selected':''"
                    ></div>
                  </div>
                </div>
                <!-- //高度为100-40的列表体 -->
              </div>
              <!-- //列表显示 -->
              <div
                data-debugflag="line192"
                class="css-h100 css-w100 css-bsb css-usn css-prel"
                :class="extdata.showtype != 0?'css-none':''"
              >
                <el-table
                  ref="doctable"
                  :highlight-current-row="false"
                  @row-click="row_click"
                  :border="true"
                  :stripe="false"
                  :data="extdata.tableData"
                  style="width: 100%"
                  :default-sort="{prop: 'date', order: 'descending'}"
                  height="500"
                  class="_css-table-ele css-scroll _css-customstyle"
                  :row-class-name="tableRowClassName"
                  :header-cell-style="{'background-color':'transparent'}"
                >
                  <el-table-column
                  :resizable="false"
                  width="40">
                    <!-- <template slot="header" slot-scope="scope">
                      <span
                        class="css-cb css-icon12 css-cp css-blk"
                        :data-use1="typeof scope"
                        :class="(extdata.tableData.length == extdata._selectedobjs.length) && extdata.tableData.length > 0?'mulcolor-interface-checkbox-selected':''"
                        @click.stop="head_check_toggle($event)"
                        @dblclick="_stopPropagation($event)"
                      ></span>
                    </template> -->
                    <template slot-scope="scope">
                      <span
                        class="css-cb css-icon12 css-cp"
                        @click.stop="row_check_toggle(scope.row, $event)"
                        @dblclick="_stopPropagation($event)"
                        :class="selectedContains(scope.row.FileId)?'mulcolor-interface-checkbox-selected':''"
                      ></span>
                    </template>
                  </el-table-column>

                  <el-table-column
                    :resizable="true"
                    class="_css-col-filename"
                    prop="FileName"
                    label="文件名称"
                    min-width="168"

                  >
                    <template slot-scope="scope">
                      <i
                        :class="'css-icon20 css-fs18 css-fc css-jcsa ' + $staticmethod.getIconClassByExtname(scope.row.FileName, scope.row.FileSize)"
                      ></i>

                      <template>
                        <span
                          class="css-cp css-hoverunder css-ml10 css-ellipsis basic-font-color-emphasize"
                          @click="row_filename_click(scope.row, $event)"
                          :title="scope.row.FileName"
                        >{{scope.row.FileName}}</span>
                      </template>

                    </template>
                  </el-table-column>
                </el-table>
              </div>

            </div>
            <!-- //表格体区域 -->
          </div>
        </div>
      </div>

      <div class="footer">
        <div class="handle-btn cancle" @click="cancle()">取消</div>
        <div class="handle-btn confirm" @click="confirm()">确定</div>
      </div>
    </div>
    <!-- idocview , dwg 预览 iframe 载体 -->
    <div class="_css-doc-preview" :class="extdata._show_idocview?'css-fc':'css-hide'">
      <div class="_css-doc-preview-beforeiframe">
        <div class="_css-doc-preview-beforeiframe-01"></div>
        <div class="_css-doc-preview-beforeiframe-02"></div>
        <div class="_css-doc-preview-beforeiframe-03"></div>

        <!-- 新标签打开按钮 -->
        <div
          :title="'在新标签页中查看'"
          class="icon-interface-attributes _css-docpreview-newtab _css-canfull"
           :class="{'_css-isfulling':m_docPreviewIsFull}"
           @click="func_openDocNewtab($event)"
        ></div><!-- //新标签打开按钮 -->

        <!-- 当前页全屏按钮 -->
        <div
        @click="func_switchfull($event)"
        :title="m_docPreviewIsFull?'取消全屏':'全屏'"
          :class="{'_css-docpreview-fullscreen':true
          , '_css-isfulling':m_docPreviewIsFull
          , '_css-canfull': true
          , 'icon-arrow-fullscreen_exit':m_docPreviewIsFull
          , 'icon-arrow-fullscreen':!m_docPreviewIsFull}"
        ></div><!-- //当前页全屏按钮 -->

        <!-- 关闭预览 -->
        <div
          :title="'关闭预览'"
            class="icon-suggested-close _css-canfull"
            :class="(m_docPreviewIsFull?'_css-isfulling':'')  + ' '+'_css-doc-preview-closebtn-' + extdata._docviewtype"
            @click="close_idocview($event)"
          ></div><!-- //关闭预览 -->

        <div class="_css-doc-preview-beforeiframe-04 __web-inspector-hide-shortcut__"></div>
      </div>
      <iframe class="_css-doc-preview-iframe"
      :class="{'_css-previewfull':m_docPreviewIsFull}"
      :src="extdata._idocviewurl"></iframe>
    </div>
    <template v-if="visible">
      <AttributeView ref="AttributeView" :visible.sync="visible" @cancel="visible = false"
      @addFile="addFile"
      @previewfile="previewfile"
      @removeFile="removeFile"
      @context_download='context_download' />
    </template>
    <template v-if="ModelDialog">
      <Drawing :visible.sync="ModelDialog" @closeDialog="closeDialog" :featureID="featureID" @openDrawing="openDrawing" />
    </template>
  </div>
</template>
<script>
let parentDom
  // 锚点平移相关变量
  let TranslateParams

  // 锚点旋转相关变量
  let RotateParams
  let annotion = null
import AttributeView from './AttributeView'
import Drawing from './drawing'
export default {
  name: 'modelNewIframeLoading',
  props: {
    featureID:{
      type: String,
      default: ''
    },
    VaultID: {
      type: String,
      default: ''
    },
    modelVersion:{
      type: [String,Number],
      default: ''
    },
    isModelDrawing: [String,Number],
    extension: {
      type: String,
      default: '{}'
    }
  },
  components: {
    AttributeView,
    Drawing
  },
  data(){
    return{
      isOpenCalibrate: false,
      value: '',
      isLoad: 0,
      modelId: '',
      version: '',
      docmenuitems: [],
      elementFileData: [],
      drawingId: '',
      drawingViewId: '',
      viewType: '',
      visible: false,
      ModelDialog: false,
      isOpenDrawing: false,
      // 树形数据
      treedata: {
        props1: {
          children: "Children",
          label: "FolderName",
          isLeaf:"isLeaf"
        },
        items: [
        ] // classname: 默认为未展开的文件夹图标
      },
      docListHidden: false,
      // 在线预览是否已全屏
      m_docPreviewIsFull: false,
      keyword: '',
      v_Id:undefined, //当前选中的文档Id
      extdata: {
        _doc_showtree: false, // 是否显示目录树
        _lastclickedFileId: "", // 最后一次点击的 FileId
        _lastkeyword: "", // 最后一次搜索使用的关键字
        _selectedobjs: [], // 选中的所有数据（对象数组）
        _openstacks: [
          //{FileId:'a', FileName: 'a'}
        ], // 当前打开的所有文件夹（栈结构）
        //_waitforsecondc1lickfileid: undefined, // 等待第二次点击的FileId
        /* declare */ doclisttype: "doclib", // 'doclib':项目文档 'mysuscribe'：我的收藏 'myshared'：我的分享 'recycle'：回收站
        //btngrouptip: "选中了个文件，可以执行右侧操作",
        showtype: 0, // 0为表格， 1为列表
        tableData: [], // 加载到数据主区域的表格/列表数据
        _docviewtype: '',
        _show_idocview: false,
        _idocviewurl: ''
      },
      formData: {
        longitude: 0,
        latitude: 0,
        altitude: 0,
        rotation: [0,0,0],
        offset: [0,0,0]
      },
      dwgFormData: {
        longitude: 0,
        latitude: 0,
        altitude: 0,
        rotation: [0,0,0],
        offset: [0,0,0]
      },
      timeout: null,
      timeout1: null,
        cameraPosZ: 0
    }
  },
  watch: {
    isModelDrawing (newVal, old) {
      if (newVal) {
        if (!this.isOpenDrawing) {
          this.ModelDialog = true
        } else {
          this.isOpenDrawing = false
          this.isOpenCalibrate = false
          this.$emit('closeDrawingDialog')
          this.drawingId = ''
          parentDom = null
          // 锚点平移相关变量
          TranslateParams = null

          // 锚点旋转相关变量
          RotateParams = null
          annotion = null
          const leftcontentWindow = document.getElementById('scene-iframe').contentWindow
          const rightcontentWindow = document.getElementById('scene-iframe').contentWindow
          leftcontentWindow.scene.mv.events.cameraChanged.off('change', this.leftCameraMainly)

          leftcontentWindow.document.querySelector('#renderDom').onmouseleave = null
          leftcontentWindow.document.querySelector('#renderDom').onmouseenter = null
          if (this.viewType === '3D') {
            document.querySelector('#scene-iframe').onmouseenter = null
            document.querySelector('#scene-iframe1').onmouseenter = null
            document.querySelector('#scene-iframe').onmouseleave = null
            document.querySelector('#scene-iframe1').onmouseleave = null
            leftcontentWindow.scene.mv.controller.removeEventListener('update', this.leftCameraMainlyChange)
            rightcontentWindow.scene.mv.controller.removeEventListener('update', this.rightCameraMainlyChange)
            rightcontentWindow.scene.mv.events.pickFinished.off('default',this.selectedFun)

          }
          this.viewType = ''
        }
      }

    }
  },
  computed: {
    // 带处理的 _openstacks
    _openstacks_withprocessing:{
      get(){
        var _this = this;
        return _this.extdata._openstacks;
      }
    },
    doclisttypename: {
      get() {
        // 根据 doclisttype 得到面包屑第一项的内容
        var _this = this;
        var docitem = _this.docmenuitems.filter(
          x => x.doclisttype == _this.extdata.doclisttype
        );
        if (docitem.length > 0) {
          return docitem[0].text;
        } else {
          return "项目文档";
        }
      }
    },
  },
  created(){
    if(this.$route.params.vaultID && this.$route.params.vaultID.length > 0){
      this.VaultID = this.$route.params.vaultID;
      this.featureID = this.$route.params.featureID;
    }
    this.version = this.modelVersion;
    const _extension = this.extension || '{}'
    let storage = JSON.parse(_extension)
      console.log(storage,'内建基点')
      //经纬度
      if (storage.origin) {
          this.formData.longitude = storage.origin[1];//经度
          this.formData.latitude = storage.origin[0];//纬度
      }

      //高程
      if (storage.altitude) {
          this.formData.altitude = storage.altitude;//高程
      }

      //旋转
      if (storage.rotation) {
          this.formData.rotation = storage.rotation
      }

      //偏移量
      if (storage.offset) {
          this.formData.offset = storage.offset
      }
  },
  mounted(){
    // aaa.src = aaa.src

    // window.addEventListener('message', ()=>{
    //   if (message == '') {
    //     iframe.addModelFeature(params)
    //     iframe.removeModel()
    //   }


    // })

    this.version = this.modelVersion;

    window.addEventListener('message', this.receiveMessageFromIndex, false)

    this.$store.commit({
      type:'setModelLoad',
      state: 'start'
    })
    this.modelId = this.featureID
    const iframe = document.getElementById('scene-iframe')
    const ifrSrc = this.getHttpUrl();
    const _this = this
    iframe.src = ifrSrc + `?projectId=${this.VaultID}&isBime=1?lang=cn&edit=false`;
  },
  methods: {

    closeDialog () {
      this.ModelDialog = false
      this.$emit('closeDrawingDialog')
      if (!this.isOpenDrawing) {
        const contentWindow = document.getElementById('scene-iframe').contentWindow
      }

    },

    confirm () {
      // 判断当前选中是否有文件夹，不支持关联文件夹
      const filterSize = this.extdata._selectedobjs.filter(v=>v._FileSize==0)
      if (filterSize.length) {
        this.$message({
          message: '暂不支持关联文件夹',
          type: 'warning'
        })
        return
      }
      const ids = this.extdata._selectedobjs.filter(v=>v._FileSize!==0)
      if (ids.length) {
        let _this = this
        const contentWindow = document.getElementById('scene-iframe').contentWindow

        const getSelectionEle = contentWindow.scene.getSelection()
        if (getSelectionEle.length === 0) {
          return
        }
        const elementId = getSelectionEle[0].id.split('^')[1]
        _this.$axios({
          method: "post",
          url: `${window.bim_config.webserverurl}/api/v1/file/addelementrelation?userId=${_this.$staticmethod.Get("UserId")}&Token=${this.$staticmethod.Get('Token')}`,
          data: {
            ElementId: elementId,
            FileIds: ids.map(v=>v.FileId)
          }
        }).then(x => {
          if(x.data.Ret ==1 && x.data.Data){
            _this.$message({
              message: x.data.Msg,
              type: 'success'
            })
            this.docListHidden = false
            this.keyword = ''
            this.ElementMenuClick({detail:{name:'document'}})
          }else{
            _this.$message({
              message: x.data.Msg,
              type: 'warning'
            })
          }
          this.extdata._selectedobjs = []
        }).catch(x => {
          console.log(x);
        });
      }
    },
    cancle () {
      this.docListHidden = false
      this.keyword = ''
      this.extdata._selectedobjs = []
    },
    // 判断已选中的数据（指Vue内存数据）是否包含某一FileId
    selectedContains(fileid, use_el_select1ion) {
      var _this = this;
      if (use_el_select1ion) {
        return (
          _this.$refs.doctable.selection.filter(x => x.FileId == fileid)
            .length > 0
        );
      } else {
        return (
          _this.extdata._selectedobjs.filter(x => x.FileId == fileid).length > 0
        );
      }
    },

    receiveMessageFromIndex (event) {
      // console.log(event,'====event',event.data)
      if(event.data == 'featureLoad' && !this.isOpenDrawing){
        this.loadmodel2(this.modelId);

      }
      if (event.data == 'featureLoad' && this.isOpenDrawing && this.drawingId) {
        // this.resetFormat(true)
        this.loadmodel3(this.drawingId);
      }
    },
    getHttpUrl(){
      return window.bim_config.newModelApi
    },
    // 加载模型
    loadmodel2 (modelID) {
      const contentWindow = document.getElementById('scene-iframe').contentWindow
      contentWindow.renderSceneMainMenu({name: 'widgetSource', attr: 'isShow', value: false})
      contentWindow.renderSceneMainMenu({name: 'setup', attr: 'isShow', value: false})
      contentWindow.toggleSceneManageEditMode(false);
      window.scene = contentWindow.scene
      // 添加中午天空盒
      const sk1 = window.scene.addFeature('skybox')
      sk1.name = '中午'
      sk1.load()
      sk1.setTime('noon')
      // 场景编辑器数据同步 天空盒
      contentWindow.deepUpdateScene('skybox')
      window.model3 = window.scene.addFeature('model',modelID)
      // 指定模型服务地址
      // window.model3.server = process.env.NODE_ENV === 'production' ? window.IP_CONFIG.MODEL_URL : 'http://localhost:8080/MODEL_URL'
      window.model3.server = window.bim_config.newModelHttpUrl // this.httpUrl//window.bim_config.newModelHttpUrl ;//"https://multiverse-server.vothing.com"; //'/newModelApi'
      // 指定vaultID
      window.model3.vaultID = this.VaultID
      // window.model3.modelID = modelID
      // 模型版本
      console.log(window.model3.version,'=收到',this.version)
      window.model3.version = this.version + ''
      console.log(window.model3.version,'=修改',this.version)
      // 基点的经纬度坐标
      window.model3.origin = [this.formData.longitude, this.formData.latitude]
      // 基点的高程
      window.model3.altitude = this.formData.altitude
      // 基点的正北旋转角度
      window.model3.rotation = this.formData.rotation
      // 相对于基点的XYZ偏移
      window.model3.offset = this.formData.offset
      this.dwgFormData = JSON.parse(JSON.stringify(this.formData))
      // 加载模型
      window.model3.load().then(() => {

        // 加载模型主视图
        window.model3.activeView().then(() => {
          // 加载成功后，定位到当前模型
          // window.scene.fit2Feature(window.model3)
          // 场景编辑器数据同步 模型结构树
          // contentWindow.deepUpdateScene('model');
          //  this.cameraPosZ = window.model3.AABBWorld.realCenter.z + 1

          const skybox = window.model3.config.skybox
          const sk1 = window.scene.addFeature('skybox')
          sk1.load()
          // 设置环境：morning-早晨、noon-中午、evening-傍晚、night-夜间
          if (skybox) {
            sk1.setTime(skybox)
          } else {
            // 默认设置为中午
            sk1.setTime('noon')
          }
          this.skybox = sk1
          // 场景编辑器数据同步 天空盒
          contentWindow.deepUpdateScene('skybox')
          // 设置默认视角
          const defaultViewpoint = window.model3.config.defaultViewpoint
          // 加载成功后，定位到当前模型
          if (defaultViewpoint) {
            window.scene.resetCamera(defaultViewpoint)
            // window.scene.setCamera(defaultViewpoint)
          } else {
            window.scene.fit2Feature(window.model3)
          }

          this.$store.commit({
            type:'setModelLoad',
            state: 'end'
          });
        })
        window.model3.loadSystem().then(prop => {

        })
        // 场景编辑器数据同步 模型结构树
        contentWindow.deepUpdateScene('model')

        contentWindow.toggleModelOnlyPreview(false)
      })

      contentWindow.extendBottomElementMenu([
        // {
        //   btnName: '关联文档',
        //   icon: 'document',
        //   name: 'document',
        //   type: 'extend',//固定穿extend，表示该菜单为扩展项
        //   isShow: true
        // },
        {
          btnName: '构件属性',
          icon: 'attribute_feature',
          name: 'attribute',
          type: 'extend',//固定穿extend，表示该菜单为扩展项
          isShow: true
        }
      ])

      contentWindow.addEventListener('onExtendElementMenuClick', this.ElementMenuClick)

      contentWindow.scene.mv.events.pickFinished.on('default',this.elementPickFinished)

    },

    openCalibrate () {
      this.isOpenCalibrate = !this.isOpenCalibrate
      const rightcontentWindow = document.getElementById('scene-iframe1').contentWindow
      const scene = rightcontentWindow.scene
      const model = scene.features.get(this.drawingId)
      const _model = JSON.parse(JSON.parse(JSON.stringify(model)))
      this.dwgFormData = {
        longitude: _model.origin[0],
        latitude: _model.origin[1],
        altitude: _model.altitude,
        rotation: _model.rotation,
        offset: _model.offset
      }
    },


    submitFormat () {
      const leftcontentWindow = document.getElementById('scene-iframe').contentWindow
      const rightcontentWindow = document.getElementById('scene-iframe1').contentWindow
      const scene = rightcontentWindow.scene
      const model = scene.features.get(this.drawingId)
      let storage = this.dwgFormData
      //经纬度
      model.longitude = this.dwgFormData.longitude;//经度
      model.latitude = this.dwgFormData.latitude;//纬度
      model.origin = [this.dwgFormData.longitude, this.dwgFormData.latitude]
      //高程
      if (storage.altitude) {
          model.altitude = storage.altitude;//高程
      }

      //旋转
      if (storage.rotation) {
          model.rotation = storage.rotation
      }

      //偏移量
      if (storage.offset) {
          model.offset = storage.offset
      }
      if (this.viewType !== '3D') {
        annotion.origin = [this.dwgFormData.longitude, this.dwgFormData.latitude]; //经纬度
        annotion.altitude = this.dwgFormData.altitude; //高程
        annotion.offset = this.dwgFormData.offset;
        annotion.rotation = this.dwgFormData.rotation
        if (this.timeout) clearTimeout(this.timeout)
        this.timeout = setTimeout(() => {
            let pos = leftcontentWindow.scene.mv.THREE_Camera.position
            //传入三维坐标，返回地理坐标（经纬度）
            let geographical = leftcontentWindow.scene.mv.tools.coordinate.vector2mercator(pos, false)

            // 改变锚点位置
            annotion.origin = [geographical[0], geographical[1]]; //经纬度
        }, 500)
      }
      this.$notify({
        title: '成功',
        message: '已完成校准',
        type: 'success'
      });

      this.isOpenCalibrate = false
      model.fit()
      scene.render()
    },

    resetFormat (flag) {
      let _this = this;
			_this.$axios
				.get(
					`${this.$ip("newModelHttpUrl")}/Vault/GetFeature?VaultID=${
						_this.VaultID
					}&FeatureID=${this.drawingId}&Token=${this.$staticmethod.Get('Token')}`
				)
				.then((res) => {
					if (res.status === 200) {
            if (res.data.extension) {
              let storage = JSON.parse(res.data.extension || '{}')
              console.log(storage,'内建基点')
              //经纬度
              if (storage.origin) {
                  this.dwgFormData.longitude = storage.origin[1];//经度
                  this.dwgFormData.latitude = storage.origin[0];//纬度
              } else {
                this.dwgFormData.longitude = 0//经度
                this.dwgFormData.latitude = 0//纬度
              }

              //高程
              if (storage.altitude) {
                  this.dwgFormData.altitude = storage.altitude;//高程
              } else {
                this.dwgFormData.altitude = 0//高程
              }

              //旋转
              if (storage.rotation) {
                this.dwgFormData.rotation = storage.rotation
              } else {
                this.dwgFormData.rotation = [0,0,0]
              }

              //偏移量
              if (storage.offset) {
                this.dwgFormData.offset = storage.offset
              } else {
                this.dwgFormData.offset = [0,0,0]
              }
            } else {
              this.dwgFormData = {
                longitude: 0,
                latitude: 0,
                altitude: 0,
                rotation: [0,0,0],
                offset: [0,0,0]
              }
            }
            if (flag) {
            } else {
              this.submitFormat()
            }
					}
				})
				.catch((err) => {});

    },


    openDrawing (data) {
      const {modelId, viewId, viewType} = data
      this.isOpenDrawing = true
      const contentWindow = document.getElementById('scene-iframe').contentWindow
      this.$nextTick(() => {
        const iframe = document.getElementById('scene-iframe1')
        const ifrSrc = this.getHttpUrl();
        iframe.src = ifrSrc + `?projectId=${this.VaultID}&isBime=1?lang=cn&edit=false`;
        this.drawingId = modelId
        this.drawingViewId = viewId
        this.viewType = viewType
      })
    },

    // 拖动左侧场景时
    leftCameraMainly (){
      if (!annotion) return
      const leftcontentWindow = document.getElementById('scene-iframe').contentWindow
      const rightcontentWindow = document.getElementById('scene-iframe1').contentWindow

        let pos = leftcontentWindow.scene.mv.THREE_Camera.position
        //传入三维坐标，返回地理坐标（经纬度）
        let geographical = leftcontentWindow.scene.mv.tools.coordinate.vector2mercator(pos, false)
        // 改变锚点位置
        annotion.origin = [geographical[0], geographical[1]]; //经纬度

        // 根据相机的弧度，转换为角度
        let angle = leftcontentWindow.scene.mv.controller.azimuthAngle * 180 / Math.PI

        // 设置二维相机锚点的旋转角度
        rightcontentWindow.document.querySelector('.twoDimensionCamera').style.transform = `rotate(${-1 * angle}deg)`
        RotateParams.resultAngle = -1 * angle
    },

    // 拖动锚点时，会不断触发该方法，并传入锚点的left和top值
    rightCameraMainly (clientX, clientY) {
      const leftcontentWindow = document.getElementById('scene-iframe').contentWindow

      const rightcontentWindow = document.getElementById('scene-iframe1').contentWindow
        //传入屏幕坐标，返回三维坐标
        let position = rightcontentWindow.scene.mv.tools.coordinate.screen2world(
            [clientX, clientY],
            false
        )

        // 控制左侧三维场景相机移动
        let THREE = leftcontentWindow.scene.mv._THREE;
        let direction = leftcontentWindow.scene.mv.THREE_Camera.getWorldDirection(new THREE.Vector3());

        // 相机的高度
        // position.y += 5;
        position.y = this.cameraPosZ;
        let pp = new THREE.Vector3();
        pp.set(position.x, position.y, position.z)
        let newTarget = pp.add(direction);
        newTarget.y = position.y;

        leftcontentWindow.scene.mv.controller.setFocalOffset(0, 0, 0, false);
        leftcontentWindow.scene.mv.controller.setLookAt(position.x, position.y, position.z, newTarget.x, newTarget.y, newTarget
            .z, false);
        leftcontentWindow.scene.mv.controller.update(0);
        leftcontentWindow.scene.mv.THREE_Camera.updateMatrixWorld();
        leftcontentWindow.scene.mv.events.cameraChanged.emit('change');
        leftcontentWindow.scene.render();
    },

    // 传入三维坐标，返回屏幕坐标
    getScreenCoords (position) {
      const leftcontentWindow = document.getElementById('scene-iframe').contentWindow

        let camera = leftcontentWindow.scene.mv.THREE_Camera
        let rect = leftcontentWindow.scene.mv.mainCanvas.getBoundingClientRect();
        let widthHalf = rect.width / 2;
        let heightHalf = rect.height / 2;
        let vector = new leftcontentWindow.scene.mv._THREE.Vector3().copy(position);
        vector.project(camera);

        return new leftcontentWindow.scene.mv._THREE.Vector2((vector.x * widthHalf) + widthHalf + rect.left,
            -(vector.y * heightHalf) + heightHalf + rect.top);
    },


    addAnchor () {

      const leftcontentWindow = document.getElementById('scene-iframe').contentWindow
      const rightcontentWindow = document.getElementById('scene-iframe1').contentWindow

        //将经纬度转换为三维坐标
        annotion = rightcontentWindow.scene.addFeature('annotation');
        annotion.origin = [this.dwgFormData.longitude, this.dwgFormData.latitude]; //经纬度
        annotion.altitude = this.dwgFormData.altitude; //高程
        annotion.offset = this.dwgFormData.offset;
        annotion.rotation = this.dwgFormData.rotation

        let data = {
            content: '',
            htmlCode: `
            <div class="twoDimensionCamera" style="width: 30px;
        height: 30px;
        position: relative;
        transform-origin: bottom center;">
                <div class="operationArea" style="width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        position: relative;
        z-index: 1;">
                    <div class="rotate" style="flex-grow: 1;
        cursor: ew-resize;"></div>
                    <div class="translate" style="flex-grow: 1;
        cursor: move;
        pointer-events: auto;"></div>
                </div>
                <img style="width:100%;height:100%;position: absolute;
        left: 0;
        top: 0;" id="anchor-cyb" src="http://example.vothing.com/img/CameraAngleView.png">
            </div>
        `,
            cssCode: '',
            jsCode: '',
            visibleDistance: 1 / 0,
            setType: {
                anchorType: "custom",
                annotationRadio: "0",
                checkedImg: 1,
                checkedImgSize: {
                    width: 30,
                    height: 30
                },
                type: "panel"
            }
        }

        annotion.dataKey = 'annotation-' + annotion.id
        rightcontentWindow.scene.postData(data, annotion.dataKey)
        annotion.load();
        if (this.timeout1) clearTimeout(this.timeout1)
        this.timeout1 = setTimeout(() => {
            let pos = leftcontentWindow.scene.mv.THREE_Camera.position
            //传入三维坐标，返回地理坐标（经纬度）
            let geographical = leftcontentWindow.scene.mv.tools.coordinate.vector2mercator(pos, false)

            // 改变锚点位置
            annotion.origin = [geographical[0], geographical[1]]; //经纬度
            this.setAnchorEvent()
        }, 1000)

    },
    // 设置锚点的拖动事件
    setAnchorEvent () {
      const rightcontentWindow = document.getElementById('scene-iframe1').contentWindow
      const leftcontentWindow = document.getElementById('scene-iframe').contentWindow

      let parentDom = rightcontentWindow.document.querySelector('#renderDom')
      const _this = this


        let movement = ''; //当前操作的类型：平移。旋转。
        parentDom.onmousedown = function(ev) {
            if (ev.target.className == 'translate') {
                movement = 'translate'
                // 锚点拖拽平移
                _this.setAnchorMoveTranslate(ev, TranslateParams)
            }

            if (ev.target.className == 'rotate') {
                movement = 'rotate'
                rightcontentWindow.document.querySelector('.twoDimensionCamera').style.transform =
                `rotate(${RotateParams.resultAngle}deg)`;
                //锚点拖拽旋转
                _this.setAnchorMoveRotate(ev, RotateParams)
            }
        }

        parentDom.onmouseup = function(ev) {
            parentDom.onmousemove = null;

            if (movement == 'translate') {
                // 方法1、取锚点的二维中心点坐标，转换为经纬度，重新给锚点赋值
                // // 获取鼠标抬起的那一刻的位置。根据屏幕坐标 返回三维坐标
                // let originEndPos = scene1.mv.tools.coordinate.screen2world(
                //  TranslateParams.endMoveClient,
                //  false
                // )

                // 方法二，取左侧三维相机的位置，转换为经纬度，重新给锚点赋值
                let pos = leftcontentWindow.scene.mv.THREE_Camera.position
                //传入三维坐标，返回地理坐标（经纬度）
                let geographical = leftcontentWindow.scene.mv.tools.coordinate.vector2mercator(pos, false)

                // 改变锚点位置
                annotion.origin = [geographical[0], geographical[1]]; //经纬度
                rightcontentWindow.scene.render()

                rightcontentWindow.document.querySelector('.twoDimensionCamera').style.transform = `rotate(${RotateParams.resultAngle}deg)`;
            }

            if (movement == 'rotate') {
                // 旋转完毕记录当前锚点的位置(因为锚点可能发生移动，坐标发生变化)
                let box = rightcontentWindow.document.querySelector('.twoDimensionCamera')
                RotateParams.endMoveOffset.x = box.parentElement.offsetLeft
                RotateParams.endMoveOffset.y = box.parentElement.offsetTop
            }

            movement = ''
        }
    },

    //锚点拖拽平移事件
    setAnchorMoveTranslate (ev, TranslateParams) {

      const rightcontentWindow = document.getElementById('scene-iframe1').contentWindow
      const leftcontentWindow = document.getElementById('scene-iframe').contentWindow
      let parentDom = rightcontentWindow.document.querySelector('#renderDom')
      const _this = this

        // 更新一下相机位置
        rightcontentWindow.scene.mv.THREE_Camera.updateMatrixWorld();

        // 获取鼠标按下的那一刻的位置。根据屏幕坐标 返回三维坐标
        TranslateParams.originStartPos = rightcontentWindow.scene.mv.tools.coordinate.screen2world(
            [ev.clientX, ev.clientY],
            false
        )
        // 转换为真实坐标
        TranslateParams.startPos = rightcontentWindow.scene.mv.tools.coordinate.realPosition(TranslateParams.originStartPos)

        let anchorDom = rightcontentWindow.document.querySelector('.twoDimensionCamera').parentElement;
        ev = window.event || ev;
        //获取左部和顶部的偏移量
        let originX = ev.clientX - anchorDom.offsetLeft
        let originY = ev.clientY - anchorDom.offsetTop

        parentDom.onmousemove = function(ev) {
            ev = window.event || ev;

            // 鼠标拖动过程中，如果移出了二维视图，则给图片设置为边界值
            let offsetX = TranslateParams.boundaryLeft
            let offsetY = TranslateParams.boundaryTop

            // 在二维视图内拖动，改变图片的top值
            if (ev.clientX - TranslateParams.boundaryLeft > 0) {
                offsetX = ev.clientX - originX
            }

            // 在二维视图内拖动，改变图片的left值
            if (ev.clientY - TranslateParams.boundaryTop > 0) {
                offsetY = ev.clientY - originY
            }


            let anchorDom = rightcontentWindow.document.querySelector('.twoDimensionCamera').parentElement;
            anchorDom.style.left = offsetX + 'px'
            anchorDom.style.top = offsetY + 'px'
            TranslateParams.endMoveClient = [offsetX+15, offsetY+25]

            _this.rightCameraMainly(offsetX+15, offsetY+26)
        }
    },

    //锚点拖拽旋转事件
    setAnchorMoveRotate (ev, RotateParams) {

      const rightcontentWindow = document.getElementById('scene-iframe1').contentWindow
      const leftcontentWindow = document.getElementById('scene-iframe').contentWindow
      let parentDom = rightcontentWindow.document.querySelector('#renderDom')
        let box = rightcontentWindow.document.querySelector('.twoDimensionCamera')
        // 鼠标按下，重新计算中心点位置(因为锚点可能发生移动，坐标发生变化)
        RotateParams.pointA.X = box.parentElement.clientWidth / 2 + box.parentElement.offsetLeft
        RotateParams.pointA.Y = box.parentElement.clientHeight / 2 + box.parentElement.offsetTop

        // 以鼠标第一次落下的点为起点
        if (RotateParams.count < 1) {
            RotateParams.pointB.X = ev.pageX;
            RotateParams.pointB.Y = ev.pageY;
            RotateParams.count++
        } else {
            // 重新计算旋转点参考量(因为锚点可能发生移动，坐标发生变化)
            RotateParams.pointB.X = RotateParams.pointB.X + (box.parentElement.offsetLeft-RotateParams.endMoveOffset.x);
            RotateParams.pointB.Y = RotateParams.pointB.Y + (box.parentElement.offsetTop-RotateParams.endMoveOffset.y);
        }

        parentDom.onmousemove = function(ev) {
            RotateParams.pointC.X = ev.pageX;
            RotateParams.pointC.Y = ev.pageY; // 获取结束点坐标

            // 计算出旋转角度
            let AB = {};
            let AC = {};
            AB.X = (RotateParams.pointB.X - RotateParams.pointA.X);
            AB.Y = (RotateParams.pointB.Y - RotateParams.pointA.Y);
            AC.X = (RotateParams.pointC.X - RotateParams.pointA.X);
            AC.Y = (RotateParams.pointC.Y - RotateParams.pointA.Y); // 分别求出AB,AC的向量坐标表示
            let direct = (AB.X * AC.Y) - (AB.Y * AC.X); // AB与AC叉乘求出逆时针还是顺时针旋转
            let lengthAB = Math.sqrt(Math.pow(RotateParams.pointA.X - RotateParams.pointB.X, 2) +
                Math.pow(RotateParams.pointA.Y - RotateParams.pointB.Y, 2));
            let lengthAC = Math.sqrt(Math.pow(RotateParams.pointA.X - RotateParams.pointC.X, 2) +
                Math.pow(RotateParams.pointA.Y - RotateParams.pointC.Y, 2));
            let lengthBC = Math.sqrt(Math.pow(RotateParams.pointB.X - RotateParams.pointC.X, 2) +
                Math.pow(RotateParams.pointB.Y - RotateParams.pointC.Y, 2));
            let cosA = (Math.pow(lengthAB, 2) + Math.pow(lengthAC, 2) - Math.pow(lengthBC, 2)) /
                (2 * lengthAB * lengthAC); //   余弦定理求出旋转角
            let angleA = Math.round(Math.acos(cosA) * 180 / Math.PI);
            if (direct < 0) {
                RotateParams.resultAngle = -angleA; //叉乘结果为负表示逆时针旋转， 逆时针旋转减度数
            } else {
                RotateParams.resultAngle = angleA; //叉乘结果为正表示顺时针旋转，顺时针旋转加度数
            }

            RotateParams.resultAngle += RotateParams.anchorAngle
            rightcontentWindow.document.querySelector('.twoDimensionCamera').style.transform = `rotate(${RotateParams.resultAngle}deg)`;

            // 将角度转换为弧度 传给三维相机
            leftcontentWindow.scene.mv.controller.azimuthAngle = -1 * (Math.PI / 180) * RotateParams.resultAngle
        }

    },











    // 加载模型
    loadmodel3 (modelID) {
      const leftcontentWindow = document.getElementById('scene-iframe').contentWindow
      const contentWindow = document.getElementById('scene-iframe1').contentWindow
      contentWindow.renderSceneMainMenu({name: 'widgetSource', attr: 'isShow', value: false})
      contentWindow.renderSceneMainMenu({name: 'setup', attr: 'isShow', value: false})
      contentWindow.toggleSceneManageEditMode(false);
      window.scene1 = null
      window.scene1 = contentWindow.scene
      const _this = this

      // 添加中午天空盒
      const sk1 = window.scene1.addFeature('skybox')
      sk1.name = '中午'
      sk1.load()
      sk1.setTime('noon')
      // 场景编辑器数据同步 天空盒
      contentWindow.deepUpdateScene('skybox')
      const model = window.scene1.addFeature('model',modelID)
      // 指定模型服务地址
      // model.server = process.env.NODE_ENV === 'production' ? window.IP_CONFIG.MODEL_URL : 'http://localhost:8080/MODEL_URL'
      model.server = window.bim_config.newModelHttpUrl // this.httpUrl//window.bim_config.newModelHttpUrl ;//"https://multiverse-server.vothing.com"; //'/newModelApi'
      // 指定vaultID
      model.vaultID = this.VaultID
      // model.modelID = modelID
      // 模型版本
      model.version = this.version + ''
      // 基点的经纬度坐标
      model.origin = [this.dwgFormData.longitude, this.dwgFormData.latitude]

      // 基点的高程
      model.altitude = this.dwgFormData.altitude
      // 基点的正北旋转角度
      model.rotation = this.dwgFormData.rotation
      // 相对于基点的XYZ偏移
      model.offset = this.dwgFormData.offset
      // 加载模型
      model.load().then(() => {
        // 加载模型主视图
        model.activeView(_this.drawingViewId).then(() => {
          // 加载成功后，定位到当前模型
          // window.scene1.fit2Feature(model)
          // 场景编辑器数据同步 模型结构树
          // contentWindow.deepUpdateScene('model');
          this.cameraPosZ = model.AABBWorld.realCenter.z + 1

          this.$notify({
            title: this.viewType === '3D' ? '三维视图' : '二维视图',
            message: '已添加至场景，联动比对模式已开启',
            type: 'success'
          });

          const skybox = model.config.skybox
          const sk1 = window.scene1.addFeature('skybox')
          sk1.load()
          // 设置环境：morning-早晨、noon-中午、evening-傍晚、night-夜间
          if (skybox) {
            sk1.setTime(skybox)
          } else {
            // 默认设置为中午
            sk1.setTime('noon')
          }
          this.skybox = sk1
          // 场景编辑器数据同步 天空盒
          contentWindow.deepUpdateScene('skybox')
          // 设置默认视角
          const defaultViewpoint = model.config.defaultViewpoint
          // 加载成功后，定位到当前模型
          if (defaultViewpoint) {
            window.scene1.resetCamera(defaultViewpoint)
            // window.scene1.setCamera(defaultViewpoint)
          } else {
            window.scene1.fit2Feature(model)
          }

          this.$store.commit({
            type:'setModelLoad',
            state: 'end'
          });
        })
        model.loadSystem().then(prop => {

        })
        // 场景编辑器数据同步 模型结构树
        contentWindow.deepUpdateScene('model')

        contentWindow.toggleModelOnlyPreview(false)

      })

      //鼠标移入三维场景，初始化监听相机变化
      leftcontentWindow.document.querySelector('#renderDom').onmouseenter = function() {
          leftcontentWindow.scene.mv.events.cameraChanged.on('change', _this.leftCameraMainly)
      }

      //鼠标移出三维场景，注销相机变化监听
      //目的是为了在操作二维视图时，防止事件进入无限循环
      leftcontentWindow.document.querySelector('#renderDom').onmouseleave = function() {
          leftcontentWindow.scene.mv.events.cameraChanged.off('change', _this.leftCameraMainly)
      }

      if (this.viewType !== '3D') {
        // 添加锚点
        this.addAnchor()
        // 禁止场景旋转
        contentWindow.scene.mv.controller.enableRotate = false
        contentWindow.scene.mv.status.rotatable = false
        this.selectedSynchronization()

        parentDom = contentWindow.document.querySelector('#renderDom')
        TranslateParams = {
            boundaryLeft: parentDom.offsetLeft,
            boundaryTop: parentDom.offsetTop, // 获取二维视图dom的边界
            originStartPos: null,
            startPos: null, // 开始拖动时的真实坐标
            endMoveClient: [0, 0],
        }
        RotateParams = {
            pointA: {
                X: 0, // 元素中心点 元素1/2自身宽高 + 元素的定位
                Y: 0
            }, //中心点坐标
            pointB: {}, //起始点坐标
            pointC: {}, //结束点坐标
            count: 0,
            anchorAngle: 0,
            resultAngle: 0, //最终旋转角度
            endMoveOffset: {
                x: 0 ,y: 0
            },//每次旋转完毕后，锚点的offset
        }
      } else {
        // 监听相机视角
        document.querySelector('#scene-iframe').onmouseenter = function () {
          leftcontentWindow.scene.mv.controller.addEventListener('update', _this.leftCameraMainlyChange)
        }

        document.querySelector('#scene-iframe').onmouseleave = function () {
          leftcontentWindow.scene.mv.controller.removeEventListener('update', _this.leftCameraMainlyChange)
        }

        document.querySelector('#scene-iframe1').onmouseenter = function () {
          contentWindow.scene.mv.controller.addEventListener('update', _this.rightCameraMainlyChange)
        }

        document.querySelector('#scene-iframe1').onmouseleave = function () {
          contentWindow.scene.mv.controller.removeEventListener('update', _this.rightCameraMainlyChange)
        }
      }


    },

    selectedFun (e) {
      console.log(12341234)
      const leftcontentWindow = document.getElementById('scene-iframe').contentWindow
      const rightcontentWindow = document.getElementById('scene-iframe1').contentWindow
      console.log(e)
      //清除选中效果
      leftcontentWindow.toggleElementMenu('')
      rightcontentWindow.toggleElementMenu('')
      leftcontentWindow.scene.clearSelection()
      leftcontentWindow.scene.render()
      if (e != undefined) {
          // 根据id获取构件对象
          let selected = leftcontentWindow.scene.findObject(e)
          // 如果拾取了二维注释，比如轴网、标高
          if (selected == undefined) {

            this.$notify({
              title: "提示",
              message: "您点选了二维注释，无对应构件。",
              type: 'info'
            });
            return false;
          }
          leftcontentWindow.toggleElementMenu('model')
          // 使三维模型构件选中
          leftcontentWindow.scene.findObject(e).selected = true
          // 聚焦构件
          leftcontentWindow.scene.fit([e])
      }
    },

    // 拾取同步
    selectedSynchronization () {
      const leftcontentWindow = document.getElementById('scene-iframe').contentWindow
      const rightcontentWindow = document.getElementById('scene-iframe1').contentWindow
      console.log(111111)
      //开启二维视图对象单击选中监听，返回被选中的对象的ID。
      rightcontentWindow.scene.mv.events.pickFinished.on('default',this.selectedFun)
    },

    leftCameraMainlyChange () {
      const leftcontentWindow = document.getElementById('scene-iframe').contentWindow
      const rightcontentWindow = document.getElementById('scene-iframe1').contentWindow
      const info = this.getCameraInfo(leftcontentWindow.scene)
      this.setCameraInfo(rightcontentWindow.scene, info.position, info.target, info.focaloffset)
    },
    rightCameraMainlyChange () {
      const leftcontentWindow = document.getElementById('scene-iframe').contentWindow
      const rightcontentWindow = document.getElementById('scene-iframe1').contentWindow
      const info = this.getCameraInfo(rightcontentWindow.scene)
      this.setCameraInfo(leftcontentWindow.scene, info.position, info.target, info.focaloffset)
    },

    // 获取相机位置
    getCameraInfo (obj) {
      var position = obj.mv.controller.getPosition()
      var target = obj.mv.controller.getTarget()
      var focaloffset = obj.mv.controller.getFocalOffset()
      return {
        position, target, focaloffset
      }
    },

    // 设置相机位置
    setCameraInfo (obj, position, target, focaloffset) {
      obj.mv.controller.setFocalOffset(focaloffset.x, focaloffset.y, focaloffset.z, false)
      obj.mv.controller.setLookAt(position.x, position.y, position.z, target.x, target.y, target.z, false)
    },

    elementPickFinished () {
      const contentWindow = document.getElementById('scene-iframe').contentWindow
      const getSelectionEle = contentWindow.scene.getSelection()
      if (getSelectionEle.length > 1) {
        contentWindow.extendBottomElementMenu([])
      } else {
        contentWindow.extendBottomElementMenu([
          // {
          //   btnName: '关联文档',
          //   icon: 'document',
          //   name: 'document',
          //   type: 'extend',//固定穿extend，表示该菜单为扩展项
          //   isShow: true
          // },
          {
            btnName: '构件属性',
            icon: 'attribute_feature',
            name: 'attribute',
            type: 'extend',//固定穿extend，表示该菜单为扩展项
            isShow: true
          }
        ])
      }

    },
    ElementMenuClick (e) {
      const {name} = e.detail
      if (name === 'document') {
        // elementFileData 当前构件的文档数据
        // const contentWindow = document.getElementById('scene-iframe').contentWindow

        // const getSelectionEle = contentWindow.scene.getSelection()
        // if (getSelectionEle.length === 0) {
        //   return
        // }
        // const elementId = getSelectionEle[0].id.split('^')[1]
        this.v_Id = undefined
        this.extdata._openstacks = []
        this.$refs.AttributeView.getDocumentList()
      } else {
        this.visible = true
      }

    },
    // 下载单个文件
    context_download(fileid) {
      var filedownloadurl = `${window.bim_config.webserverurl}/api/v1/file/download?fileId=${fileid}&userId=${this.$staticmethod.Get("UserId")}&Token=${this.$staticmethod.Get('Token')}`;
      window.location.href = filedownloadurl;
    },
    addFile () {
      this.docListHidden = true
      this.v_Id = undefined
      this.extdata._openstacks = []
      this.$nextTick(() => {
        this.load_tree_firstlevelfolder()
      })
    },
    removeFile (id) {
      let _this = this
      _this.$axios({
        method: "post",
        url: `${window.bim_config.webserverurl}/api/v1/file/removeelementrelation?Token=${this.$staticmethod.Get('Token')}`,
        data: [
          id
        ]
      }).then(x => {
        if(x.data.Ret == 1){
          this.$message({
            message: x.data.Msg,
            type: 'success'
          })
          this.$refs.AttributeView.getDocumentList()
          // const contentWindow = document.getElementById('scene-iframe').contentWindow
          // const getSelectionEle = contentWindow.scene.getSelection()

          // const elementId = getSelectionEle[0].id.split('^')[1]

          // let _this = this
          // _this.$axios({
          //   method: "get",
          //   url: `${window.bim_config.webserverurl}/api/v1/file/elementrelation?elementId=${elementId}&Token=${this.$staticmethod.Get('Token')}`
          // }).then(x => {
          //   if(x.data.Ret == 1){
          //     _this.elementFileData = x.data.Data;
          //   }else{
          //     _this.elementFileData= []

          //     _this.$message({
          //       message: x.data.Msg,
          //       type: 'warning'
          //     })
          //   }
          // }).catch(x => {
          //   console.log(x);
          // });
        }else{

          _this.$message({
            message: x.data.Msg,
            type: 'warning'
          })
        }
      }).catch(x => {
        console.log(x);
      });
    },
    closeDocList () {
      this.docListHidden = false
      this.keyword = ''

    },
    _ondocnameinput(str, unknown, ev){
      // 通过 ev 判断是否按的是回车
      var _this = this;
      // 将 str 作为关键字进行文档搜索
      _this.search_doc_bykeyword(undefined, undefined);
    },
    // 搜索按钮点击
    search_doc_bykeyword(ev, newdirid) {
      var _this = this;
      if (ev) {
        ev.stopPropagation();
      }
      _this.doc_file_search(_this.keyword)
      // 保存最后一次搜索使用的关键字
      // _this.extdata._lastkeyword = _this.keyword;
    },
    //搜索文档
    doc_file_search(fileName){
      let _this = this
      _this.$axios({
        method: "get",
        url: `${window.bim_config.webserverurl}/api/v1/file/search?projectId=${_this.$staticmethod._Get("organizeId")}&fileName=${fileName}&userId=${_this.$staticmethod.Get("UserId")}&Token=${this.$staticmethod.Get('Token')}`
      }).then(x => {
        if(x.data.Ret == 1){
          _this.extdata.tableData = x.data.Data;
        }else{
          _this.extdata.tableData = []
          _this.$message({
            message: x.data.Msg,
            type: 'warning'
          })
        }
      }).catch(x => {
        console.log(x);
      });
    },
    // 打开某一层级（从面包屑）
    open_current_history(index, ev, newdirid) {
      if (ev) {
        ev.stopPropagation();
      }
      var _this = this;
      // // 判断如果是最后一个，不执行动作
      if (index == _this.extdata._openstacks.length - 1) {
        return;
      }
      // _this.collapseoptions();

      var newhisarr = [];
      for (var i = 0; i <= index; i++) {
        newhisarr.push(_this.extdata._openstacks[i]);
      }
      var bimcomposerId = _this.$staticmethod._Get("bimcomposerId");
      _this.extdata._openstacks = newhisarr;
      _this.v_Id = _this.extdata._openstacks[index].FileId
      _this.doc_file_folder(this.v_Id)
    },
    // 仅改变单行的选中状态
    row_check_toggle(row, ev) {
      ev.stopPropagation();
      var _this = this;
      _this.list_item_checkbox_click(row.FileId, ev);
    },
    // 列表视图，点击一个文件的c heckbox
    list_item_checkbox_click(FileId, ev) {
      ev.stopPropagation();
      var _this = this;
      //_this.$refs.doctable.toggleRowSele1ction(row);
      if (_this.selectedContains(FileId)) {
        _this.extdata._selectedobjs = _this.extdata._selectedobjs.filter(
          x => x.FileId != FileId
        );
      } else {
        var topush = _this.extdata.tableData.filter(x => x.FileId == FileId)[0];
        if (topush) {
          _this.extdata._selectedobjs.push(topush);
        }
      }
    },
    // 打开上一级文件夹
    open_parentfolder(ev) {
      var _this = this;
      ev.stopPropagation();
      // 当没有目录栈时，直接跳出
      if (_this.extdata._openstacks.length == 0) {
        return;
      }

      var bimcomposerId = _this.$staticmethod._Get("bimcomposerId");
      var newstacks = [];
      // 打开栈只剩一个的时候，即将回到最初始的位置，即：项目文档根目录
      if (_this.extdata._openstacks.length == 1) {
        // 判断最后一次搜索使用的关键字
        if (_this.extdata._lastkeyword && _this.extdata._lastkeyword != "") {
          // 进入到根目录，但带有搜索关键字，等同于直接搜索
          _this.keyword = _this.extdata._lastkeyword;
          _this.GetAllFolderAndFileByProjectID({
            ProjectID: bimcomposerId,
            LikeName: _this.keyword,
            NormalOrDrawings: "Normal",
            FolderID: undefined,
            _if_dir_change_suc_openstacks: []
          });
        } else {
          // 进入到根目录
          _this.GetAllFolderAndFileByProjectID({
            ProjectID: bimcomposerId,
            LikeName: "",
            NormalOrDrawings: "Normal",
            FolderID: 0,
            _if_dir_change_suc_openstacks: []
          });
        }
      } else {
        // 进入到上一级目录，通过 FileId，并将 extdata._openstacks 弹栈
        newstacks = [];
        for (var i = 0; i < _this.extdata._openstacks.length - 1; i++) {
          newstacks.push(_this.extdata._openstacks[i]);
        }
        _this.extdata._openstacks = newstacks;
        _this.v_Id = newstacks[newstacks.length - 1].FileId
        _this.doc_file_folder(this.v_Id)
      }
    },

    // 全屏切换
    func_switchfull(ev) {
      this.m_docPreviewIsFull = !this.m_docPreviewIsFull
    },
    // 关闭 idocview 在线预览
    close_idocview(ev) {
      ev.stopPropagation();
      var _this = this;
      _this.extdata._idocviewurl = "about:blank";
      _this.extdata._show_idocview = false;
      _this.m_docPreviewIsFull = false
    },

    // 在新标签页中打开在线预览
    func_openDocNewtab(ev) {
      var _this = this;
      window.open(_this.extdata._idocviewurl, '_blank');
    },
    //tableRowClassName
    tableRowClassName({ row, rowIndex }) {
      var _this = this;
      var _sel_hasThisRow =
        _this.extdata._selectedobjs.filter(x => x.FileId == row.FileId).length >
        0;
      return "css-tdunder " + (_sel_hasThisRow ? "css-tabrow-selected" : "");
    },
    row_filename_click(obj, ev) {
      var _this = this;
        _this.on_row_dblclick(obj, null, ev);
        ev.stopPropagation();
    },
    // 被触发的双击事件
    on_row_dblclick(row, column, ev) {
      var _this = this;
      ev.stopPropagation();
      _this.item_enter(row);
    },
    // 表格、列表视图的文件（夹）进入方法（包括回收站不可进的逻辑）。
    item_enter(row){
      // 判断是否是处于回收站状态
      var _this = this;
      if (_this.extdata.doclisttype == 'recycle') {
        if (_this.$configjson.debugmode == '1') {
          console.log('当前为回收站，不进行文件（夹）进入动作');
        }
        return;
      }
      // 进行文件进入动作
      if (row.FileSize == '0') {
        if (_this.extdata.doclisttype == 'mysuscribe') {
          _this.turntodocmain(row.FileId);
        } else {
          _this.enterdir(row.FileId, row.FileName, null);
        }
      } else {
        _this.begin_previewfile(row, null);
      }
    },
    // begin_previewfile 方法后执行
    previewfile(row, ev){
      var _this = this;
      // 根据扩展名类型，取得在线浏览地址
      let filedownloadurl = `${window.bim_config.webserverurl}/api/v1/file/preview?FileId=${row.FileId}&Version=1&UserId=${this.$staticmethod.Get("UserId")}&Token=${this.$staticmethod.Get('Token')}`
      _this.func_previewbydownloadurl(row, filedownloadurl,row.FileExtension);
    },
    // 预览文件（文件库列表或矩阵数据中，不包括历史版本界面的预览）
    begin_previewfile(row, ev) {
      if (ev) {
        ev.stopPropagation();
      }
      var _this = this;
      _this.previewfile(row, ev);
    },

    // 根据下载地址来进行预览
    func_previewbydownloadurl(row, filedownloadurl,FileExtension) {
      var _this = this;
      // 根据扩展名获取在线浏览地址
      var url_iframe_all;

      // 判断如果是压缩文件的相关扩展名，直接下载：
      var zipExts = ['.zip', '.rar', '.7z', '.jar', '.tar'];
      var lastIndexOfDot = row.FileName.toLowerCase().lastIndexOf('.');
      if (lastIndexOfDot < 0) {
        // 不包含.，也直接下载
        window.location.href = filedownloadurl;
        return;
      }

      var theFileExt = row.FileName.substr(lastIndexOfDot);
      if (row.FileName.toLowerCase().indexOf(".rvt") > 0){
         this.$message({
           message: "rvt格式不支持打开预览",
           type: "warning"
        });
        return
      }
      if (zipExts.indexOf(theFileExt) >= 0) {
        this.$message({
           message: "压缩文件不支持打开预览",
           type: "warning"
        });
        return;
      }
      if (row.FileName.toLowerCase().indexOf(".dwg") > 0) {
        // 修改当前预览的关闭按钮类
        this.extdata._docviewtype = 'dwg'
        url_iframe_all = `${
          _this.$configjson.dwgurl
        }/Home/Index2?dwgurlcfg=${encodeURIComponent(filedownloadurl)}&name=${
          row.FileName
        }`;
      } else {
        // 修改当前预览的关闭按钮类
        this.extdata._docviewtype = 'office'

        url_iframe_all = _this.$staticmethod.getHuangNewcomputeViewUrl(filedownloadurl, row.FileName,FileExtension);
      }
      // 打开在线预览。
      this.extdata._show_idocview = true
      this.extdata._idocviewurl = url_iframe_all
    },
    // 转到项目文档
    turntodocmain(fid){
      // 获取面包屑
      var _this = this;
      //var _bimserverurl = _this.$configjson.b1imserverurl;
      var _bimcomposerId = _this.$staticmethod._Get("bimcomposerId");
      var _item = _this.extdata.tableData.filter(x => x.FileId == fid)[0];
      var _FolderID;
      var _FileID;
      if (_item.FileSize == '0') {
        _FolderID = fid;
        _FileID = '';
      } else {
        _FolderID = '';
        _FileID = fid;
      }

      _this.collapseoptions();
      _this.extdata.doclisttype = "doclib";
      // _this.enterroot(fid);
      _this.GetAllFolderAndFileByProjectID({
        ProjectID: _this.$staticmethod._Get("bimcomposerId"), //_this.$staticmethod._Get("bimcomposerId"),
        LikeName: "",
        NormalOrDrawings: "Normal",
        FolderID: 0,
        _if_dir_change_suc_openstacks: [],
        AutoSelectFileId: fid
      });
      return
    },
    enterdir(fileid, filename, ev, setopenstacks, AutoSelectFileId) {
      // 验证 fileid 文件夹对于当前人有无打开权限。（需要前置判断，这里做额外判断）
      if (ev) {
        ev.stopPropagation();
      }
      var _this = this;
      _this.v_Id = fileid
      _this.doc_file_folder(fileid)
      var new_openstacks = [];
      if (!setopenstacks) {
        // 没有指定打开文件夹后的“打开栈”时，通过fileid及filename计算“打开栈”
        for (var i = 0; i < _this.extdata._openstacks.length; i++) {
          new_openstacks.push(_this.extdata._openstacks[i]);
        }
        new_openstacks.push({ FileId: fileid, FileName: filename });
      } else {
        // 设置指定的“打开栈”
        new_openstacks = setopenstacks;
      }
        _this.GetAllFolderAndFileByProjectID({
          ProjectID: _this.$staticmethod._Get("bimcomposerId"),
          LikeName: "",
          NormalOrDrawings: "Normal",
          FolderID: fileid,
          _if_dir_change_suc_openstacks: new_openstacks,
          AutoSelectFileId: AutoSelectFileId
        });
    },
    // 展开或折叠第一项
    collapseorexpand(doclisttype) {
      var _this = this;

      if (doclisttype != "doclib") {
        return;
      }
      // 设置当前的doclisttype
      _this.extdata.doclisttype = "doclib";
      // 设置展开或折叠
      _this.extdata._doc_showtree = !_this.extdata._doc_showtree;
    },
    // 某节点展开时，加载子节点的途径
    loadNodeChild(node, resolve) {
      // 设置必要的参数
      var _this = this;

      // 请求接口，获取当前正在展开的节点的子节点数据
      _this
        .$axios({
          method: "get",
          url: `${window.bim_config.webserverurl}/api/v1/folder/tree?Token=${this.$staticmethod.Get('Token')}&projectId=${_this.$staticmethod._Get("organizeId")}&parentId=${node.data.Id}&userId=${_this.$staticmethod.Get("UserId")}`
        })
        .then(x => {
          if (x.data.Ret == 1) {
            var _arrgot
            // 默认情况是加载当前正在展开的节点内的全部文件及文件夹，此处过滤出文件夹

              _arrgot = x.data.Data;
            // var _arrgot = x.data.Data;
            for (var i = 0; i < _arrgot.length; i++) {
              // 对于每一个即将拼接的新子文件夹，设置及 chilren 使其带有展开按钮图标
              _arrgot[i].Children = [];

              // 使该文件夹项带有文件夹图标
              _arrgot[i].classname = "";
               _arrgot[i].isLeaf = !_arrgot[i].HasChildren;

              // 设置其 chain 属性。
              // 记录了从根级节点到当前节点的路径（有序节点数组）
              _arrgot[i].chain = [];

              // 添加父节点的 chain
              if (node.data.chain && node.data.chain.length) {
                for (var j = 0; j < node.data.chain.length; j++) {
                  _arrgot[i].chain.push(node.data.chain[j]);
                }
              }

              // 添加自己节点的 chain item
              _arrgot[i].chain.push({
                Id: _arrgot[i].Id,
                FolderName: _arrgot[i].FolderName,
                isLeaf:!_arrgot[i].HasChildren
              });
            }
            // 将获取到并处理过的数据加载到正在展开的节点上
            resolve(_arrgot);
          } else {
            console.warn(x);
          }
        })
        .catch(x => {
          console.warn(x);
        });

    },

    // 加载树的第一级文件夹(in mounted)
    load_tree_firstlevelfolder(recycle,newdirid) {
      var _this = this;
      // 调用 ajax 获取第一层的文件夹

      var _LoadingIns = _this.$loading({
        text: "加载中",
        target: document.getElementById("id_datamain")
        ,background:"rgba(240, 242, 245, 1)"
      });
      _this
        .$axios({
          method: "get",
          url: `${window.bim_config.webserverurl}/api/v1/folder/tree?Token=${this.$staticmethod.Get('Token')}&projectId=${_this.$staticmethod._Get("organizeId")}&parentId=0&userId=${_this.$staticmethod.Get("UserId")}`
        })
        .then(x => {
          if (x.data.Ret == 1){
            if(_this.v_Id == undefined){
              _this.v_Id = x.data.Data[0].Id
            }
            _this.firstLevelFolderData = x.data.Data[0]
            _this.$axios
            .get(`${window.bim_config.webserverurl}/api/v1/folder/tree?Token=${this.$staticmethod.Get('Token')}&projectId=${_this.$staticmethod._Get("organizeId")}&parentId=${x.data.Data[0].Id}&userId=${_this.$staticmethod.Get("UserId")}`)
            .then(y => {
              if(y.data.Ret == 1){
                var _arrgot = y.data.Data
                for (let i = 0; i < _arrgot.length; i++) {
                  _arrgot[i].isLeaf = !_arrgot[i].HasChildren;
                }
                _this.treedata.items = _arrgot;
                _this.extdata._doc_showtree = true;

                // 赋值数据
                _this.tableDatapatch(y.data.Data);
                if (_this.extdata._openstacks.length > 0) {
                  _this.open_current_history(
                    _this.extdata._openstacks.length - 1,
                    undefined,
                    newdirid
                  );
                  _this.doc_file_folder(this.v_Id)
                } else {
                  // 未打开任何文件夹下，处理根级目录
                  // _this.resetpath(undefined, newdirid);
                  // 赋值面包屑
                  _this.extdata._openstacks = [];
                }
                if(!recycle){
                  _this.doc_file_folder(this.v_Id)
                }
                _LoadingIns.close();
              }
            }).catch(x => {
              console.warn(x);
              _LoadingIns.close();
            });
          }else{
            _this.$message({
              message: x.data.Msg,
              type: "warning"
            });
            _LoadingIns.close();
          }
        })
        .catch(x => {
          console.warn(x);
          // _LoadingIns.close();
        });

    },

    //查询当前文件夹下信息
    doc_file_folder(folderId){
      let _this = this
      _this.$axios({
        method: "get",
        url: `${window.bim_config.webserverurl}/api/v1/folder/file-folder?Token=${this.$staticmethod.Get('Token')}&projectId=${_this.$staticmethod._Get("organizeId")}&folderId=${folderId}&userId=${_this.$staticmethod.Get("UserId")}`
      }).then(x => {
        if(x.data.Ret == 1){
          _this.extdata.tableData = x.data.Data;
        }else{
          _this.$message({
            message: x.data.Msg,
            type: 'warning'
          })
        }
      }).catch(x => {
        console.log(x);
      });
    },

    // 单击节点 回调
    node_click(itemi, node, comp) {
      this.sharedoclisttype = false
      this.breadList = []; //初始化
      console.log(this.$refs.tree)
      this.getTreeNode(this.$refs.tree.getNode(itemi.Id));
      var _this = this;
      _this.extdata._selectedobjs = [];
      _this.extdata.doclisttype = "doclib";
      _this.v_Id = itemi.Id
      this.keyword = ''
      _this.doc_file_folder(itemi.Id)
    },
    getTreeNode(node){
      var _this = this;
      if (node&&node.label) {
        _this.breadList.unshift({ FileId: node.key, FileName: node.label });
        _this.getTreeNode(node.parent);
        _this.GetAllFolderAndFileByProjectID({
          ProjectID: _this.$staticmethod._Get("bimcomposerId"),
          LikeName: "",
          NormalOrDrawings: "Normal",
          FolderID: node.key,
          _if_dir_change_suc_openstacks: _this.breadList,
        });
      }
    },
    GetAllFolderAndFileByProjectID(json) {
      // 请求文档服务接口，获取数据。
      var _this = this;


      // 只要请求数据，就将已选中的数组置空
      _this.extdata._selectedobjs = [];
      if(json.FolderID){
          _this.extdata._openstacks = json._if_dir_change_suc_openstacks;
          this.v_Id = json.FolderID
      }else{
      // 显示loading
      let LoadingIns = _this.$loading({
        text: "加载中",
        target: document.getElementById("id_datamain")
        ,background:"rgba(240, 242, 245, 1)"
      });

      // 发起请求
      var fileserverurl;

      var UserId=this.$staticmethod.Get('UserId');
      var OrganizeId=this.$staticmethod._Get('organizeId');
      _this.$axios
        .get(`${window.bim_config.webserverurl}/api/v1/folder/tree?Token=${this.$staticmethod.Get('Token')}&projectId=${_this.$staticmethod._Get("organizeId")}&parentId=${json.FolderID}&userId=${_this.$staticmethod.Get("UserId")}`)
        .then(x => {
          if (x.data.Ret == 1 && (x.data.Data.length || x.data.Data.length == 0)) {
            this.v_Id = x.data.Data[0].Id

            // 赋值面包屑
            _this.extdata._openstacks = json._if_dir_change_suc_openstacks;

            // 赋值数据
            _this.tableDatapatch(x.data);
            _this.doc_file_folder(this.v_Id)

            LoadingIns.close();
            setTimeout(() => {
                this.R_lastTime = true;
            },500);
          }
        })
        .catch(x => {
          LoadingIns.close();
        })};
    },
    //左侧树结构递归查找数据
    function_recursive(data,target_id) {
      let target_data = {}
      if (!Array.isArray(data) || !data.length) {
        return target_data
      }
      // 递归过程
      let get_target_data = item => {
        if (item.Id == target_id) {
        target_data = item
        } else {
          if (item.Children && item.Children.length) {
            for (let i = 0; i < item.Children.length; i++) {
              if (Object.keys(target_data).length) break // 如果已经找到了目标数据，记得break
              get_target_data(item.Children[i])
            }
          }
        }
      }

      // for循环传入的data数据并开始递归
      for (let i = 0; i < data.length; i++) {
        if (Object.keys(target_data).length) break // 同上
        get_target_data(data[i])
      }
      return target_data
    },

    // 展开节点 回调
    node_expand(itemi, node, comp) {
      itemi.classname = "icon-interface-folder";
    },

    // 收起节点 回调
    node_collapse(itemi, node, comp) {
      itemi.classname = "icon-interface-unfolder";
    },
    tableDatapatch(arr){
      var _this = this;
      for(var i = 0; i < arr.length; i++) {
        arr[i].relprop = {
          ModelID:''
        };
      }
    },
    // 单击的不是复选框，而是行时，切换当前行的选中状态，同时确保其它行没有被选中。
    row_click(row, column, ev) {
      var _this = this;
      _this.list_item_click(row.FileId, ev);
      ev.stopPropagation();
    },
    // 列表视图，选中一个文件，同时保证其它的未选中
    list_item_click(FileId, ev) {
      ev.stopPropagation();
      var _this = this;
      // //隐藏上下文菜单
      // 如果按下了 ctrlkey
      if (ev.ctrlKey) {
        //TO1DO 同时也按了 shiftkey
        if (ev.shiftKey) {
          // 拷贝原有的所有选中的数据
          var _originSelects = _this.$staticmethod.DeepCopy(
            _this.extdata._selectedobjs
          );

          // 声明要与 _originSelected 取并集的数组
          var _tomergetoorigin;

          // 只按 shiftkey 的情况
          var preindex = _this.extdata.tableData.findIndex(
            x => x.FileId == _this.extdata._lastclickedFileId
          );
          var nowindex = _this.extdata.tableData.findIndex(
            x => x.FileId == FileId
          );
          if (preindex == nowindex) {
            //_this.extdata._selectedobjs = [_this.extdata.tableData[preindex]];
            _tomergetoorigin = [_this.extdata.tableData[preindex]];
          } else {
            var _less = preindex < nowindex ? preindex : nowindex;
            var _greator = preindex > nowindex ? preindex : nowindex;
            var _tosetselected = [];
            for (var i = _less; i <= _greator; i++) {
              _tosetselected.push(_this.extdata.tableData[i]);
            }
            _tomergetoorigin = _tosetselected;
          }
          // //只按 shiftkey 的情况

          // 把已选中的数据，与 “只按 shiftkey”的情况取并集
          var _all_notdistinct = _originSelects.concat(_tomergetoorigin);

          // 将 _all_notdistinct 排重
          var _distinct = _this.$staticmethod.Unique(
            _all_notdistinct,
            x => x.FileId
          );

          // 赋值给 extdata
          _this.extdata._selectedobjs = _distinct;
        } else {
          // 只按了 ctrlKey
          var _maybehas = _this.extdata._selectedobjs.filter(
            x => x.FileId == FileId
          );
          if (_maybehas.length > 0) {
            // 移除出去
            _this.extdata._selectedobjs = _this.extdata._selectedobjs.filter(
              x => x.FileId != FileId
            );
          } else {
            // 添加进来
            var _has = _this.extdata.tableData.filter(x => x.FileId == FileId);
            for (var i = 0; i < _has.length; i++) {
              _this.extdata._selectedobjs.push(_has[i]);
            }
          }
          // 记录最后一次点击的项的 FileId
          // 仅有 ctrl 键被按下时也要记录
          _this.extdata._lastclickedFileId = FileId;

          // //只按了 ctrlKey
        }
      } else if (ev.shiftKey) {
        // 只按了 shiftkey
        console.log("只按了shiftkey " + FileId);

        var preindex = _this.extdata.tableData.findIndex(
          x => x.FileId == _this.extdata._lastclickedFileId
        );
        var nowindex = _this.extdata.tableData.findIndex(
          x => x.FileId == FileId
        );

        console.log(`preindex = ${preindex}, nowindex = ${nowindex}`);

        // 保证选中 [preindex, nowindex] 范围内的数据
        if (preindex == nowindex) {
          // 仅选中索引为 preindex的数据
          _this.extdata._selectedobjs = [_this.extdata.tableData[preindex]];
        } else {
          var _less = preindex < nowindex ? preindex : nowindex;
          var _greator = preindex > nowindex ? preindex : nowindex;
          // 设置 [_less, _greator]这个索引区间范围内的为将要选中的
          var _tosetselected = [];
          for (var i = _less; i <= _greator; i++) {
            _tosetselected.push(_this.extdata.tableData[i]);
          }
          _this.extdata._selectedobjs = _tosetselected;
        }
      } else {
        // 无 ctrlKey 无 shiftKey
        _this.extdata._selectedobjs = [];
        _this.extdata._selectedobjs = _this.extdata.tableData.filter(
          x => x.FileId == FileId
        );
        // 记录最后一次点击的项的 FileId
        // 仅有 ctrl 键被按下时也要记录
        _this.extdata._lastclickedFileId = FileId;
      }
      return;
    },
  },
  beforeDestroy () {
    // const contentWindow = document.getElementById('scene-iframe').contentWindow
    // contentWindow.scene.mv.events.pickFinished.off('default',this.elementPickFinished)
    // contentWindow.removeEventListener('onExtendElementMenuClick', this.ElementMenuClick)
  },
  destroyed(){
    window.removeEventListener('message', this.receiveMessageFromIndex)
  },
}
</script>
<style lang="scss" scoped>
.model-iframe-content {
  display: flex;
  position: relative;



  ._css-doc-preview {
  z-index: 70000;
  position: fixed;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  background-color: rgba(0, 0, 0, 0.45);
  opacity: 1;
  justify-content: space-around;
}
._css-doc-preview-iframe {
  height: 80%;
  width: 80%;
  border-width: 0;
  background-color: #fff;
}
._css-doc-preview-iframe._css-previewfull {
  height: 100%;
  width: 100%;
}
._css-docpreview-newtab {
  font-size: 20px;
  flex: none;
  width: 30px;
  height: 30px;
  position: fixed;
  background-repeat: no-repeat;
  cursor: pointer;
  color: rgba(0, 0, 0, 0.4);
  border: 1px solid transparent;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.8);
  right: calc(10% + 55px);
  top: calc(10% - 15px);
  line-height: 30px;
}

._css-docpreview-fullscreen {
    font-size: 20px;
  flex: none;
  width: 30px;
  height: 30px;
  position: fixed;
  background-repeat: no-repeat;
  cursor: pointer;
  color: rgba(0, 0, 0, 0.4);
  border: 1px solid transparent;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.8);
  right: calc(10% + 20px);
  top: calc(10% - 15px);
  line-height: 30px;
}

._css-doc-preview-closebtn-,
._css-doc-preview-closebtn-office {
  font-size: 20px;
  flex: none;
  width: 30px;
  height: 30px;
  position: fixed;
  background-repeat: no-repeat;
  cursor: pointer;
  color: rgba(0, 0, 0, 0.4);
  border: 1px solid transparent;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.8);
  right: calc(10% - 15px);
  top: calc(10% - 15px);
  line-height: 30px;
}

._css-doc-preview-closebtn-dwg {
  text-align: center;
  font-size: 20px;
  flex: none;
  width: 30px;
  height: 30px;
  position: fixed;
  background-image: url(/Content/images/ProjectManage/Project/close_normal.png);
  background-repeat: no-repeat;
  cursor: pointer;
  color: rgba(0, 0, 0, 0.4);
  border: 1px solid transparent;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.8);
  right: calc(10% - 15px);
  top: calc(10% - 15px);
  line-height: 30px;
}
._css-canfull._css-isfulling {
  top:0;
  right: 0px;
}
._css-docpreview-newtab._css-isfulling {
  right: 68px;
}
._css-docpreview-fullscreen._css-isfulling {
  right: 34px;
}
._css-doc-preview-beforeiframe {
  position: fixed;
  width: 30px;
  height: 40px;
  top: 0;
  right: 35px;
  background-color: transparent;
  display: flex;
  align-items: center;
  font-family: "微软雅黑";
}
._css-doc-preview-beforeiframe-01 {
  flex: none;
  width: 20px;
}
._css-doc-preview-beforeiframe-02 {
  flex: none;
  font-size: 20px;
  font-weight: normal;
  font-stretch: normal;
  letter-spacing: 0px;
  color: #ffffff;
}
._css-doc-preview-beforeiframe-03 {
  flex: 1;
  height: 100%;
}
._css-doc-preview-beforeiframe-04 {
  flex: none;
  width: 25px;
  height: 100%;
}




  .calibrate-box {
    width: 100%;
    height: 64px;
    position: absolute;
    bottom: 0;
    display: flex;
    color: #98A3B3;
    .flex-box {
      display: flex;
    }
    .input-item {
      width: 100px;
    }
    .mg-l8 {
      margin-left: 8px;
    }
    .mg-t4 {
      margin-top: 4px;
    }
    .line {
      min-width: 1px;
      background: #475059;
      margin: 0 8px;
      height: 40px;
    }
    .input-box {
      position: relative;
      height: 20px;
      width: 100px;
      background: #061326;
      border-radius: 2px;
      border: 1px solid #475059;
      box-sizing: border-box;
      .text-input {
        border: none;
        outline: none;
        background: transparent;
        padding-left: 48px;
        color: #DDDDDD;
        width: 100%;
        box-sizing: border-box;
        position: absolute;
        top: 0;
        left: 0;
        bottom: 0;
      }
      .prefix {
        position: absolute;
        background: #475059;
        border-radius: 1px 0px 0px 1px;
        color: #98A3B3;
        width: 39px;
        height: 18px;
        top: 0;
        left: 0;
        text-align: center;
        line-height: 18px;
      }
    }
    .left-box {
      justify-content: flex-end;
      .text-input {
        background: rgba(255, 255, 255, 0.2)!important;
      }

    }
    .right-box {
      justify-content: space-between;
      .content {
        display: flex;
      }
      .btns {
        display: flex;
        align-items: center;
        .btn {
          width: 32px;
          height: 32px;
          background: #2680FE;
          border-radius: 50%;
          background-size: 14px 14px;
          background-repeat: no-repeat;
          background-position: center;
          cursor: pointer;
          &.submit {
            margin-left: 8px;
            background-image: url('../../../assets/images/save.png');
          }
          &.reset {
            background-color: rgba(255, 255, 255, 0.2);
            background-image: url('../../../assets/images/close.png');
          }
        }
      }
    }
    .left-box, .right-box {
      padding: 10px 32px;
      box-sizing: border-box;
      background: #242630;
      display: flex;
      width: 50%;
    }

  }
  .center-box {
    width: 48px;
    height: 48px;
    background: #2680FE;
    border-radius: 50%;
    position: absolute;
    left: 50%;
    bottom: 8px;
    z-index: 1;
    transform: translateX(-50%);
    padding: 8px;
    box-sizing: border-box;
    cursor: pointer;
    img {
      width: 100%;
      height: 100%;
    }
  }
}
</style>
<style lang="scss">

.model-iframe-content{
  width: 100%;
  height: 100%;
  .twoDimensionCamera {
        width: 30px;
        height: 30px;
        position: relative;
        transform-origin: bottom center;
    }

    .twoDimensionCamera .operationArea {
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        position: relative;
        z-index: 1;
    }

    .twoDimensionCamera .operationArea .rotate {
        flex-grow: 1;
        cursor: ew-resize;
    }

    .twoDimensionCamera .operationArea .translate {
        flex-grow: 1;
        cursor: move;
        pointer-events: auto;
    }

    .twoDimensionCamera img {
        position: absolute;
        left: 0;
        top: 0
    }
  ._css-keyword-inputbtn {
    position: relative;
    .icon-interface-search {
      position: absolute;
      left: 14px
    }
  }
  .search-input {
    height: 32px;
    width: 100%;
    border: 1px solid #B8B8B8;
    background: #fff;
    border-radius: 2px;
    padding-left: 40px;
    outline: none;
  }
  .mask {
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.4);
    z-index: 1;
  }
  .doc-dialog {
    position: fixed;
    left: 50%;
    top: 50%;
    background: #fff;
    width: 672px;
    height: 798px;
    border-radius: 2px;
    transform: translate(-50%, -50%);
    overflow: hidden;
    display: flex;
    flex-direction: column;
    z-index: 2;
    .header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 16px;
      background: #F8F8F8;
      font-weight: bolder;
      .text {
        color: #132B4D;
        font-size: 18px;
      }
      .el-icon-close {
        cursor: pointer;
        font-size: 20px;
      }
    }

    .content {
      flex: 1;
      display: flex;
      padding: 24px 16px;
      box-sizing: border-box;
      .left {
        background: #F8F8F8;
        width: 240px;
        border-radius: 2px;
        overflow-y: scroll;
        .title {
          color: #132B4D;
          font-size: 16px;
          font-weight: bolder;
          padding: 12px 16px;
          border-bottom: 1px solid #E8E8E8;
          margin-bottom: 16px;
        }
      }
      .right {
        flex: 1;
        margin-left: 24px;
      }
    }
    .footer {
      margin-top: 20px;
      padding-right: 16px;
      padding-bottom: 24px;
    }
  }

  ._css-docpreview-newtab {
    font-size: 20px;
    flex: none;
    width: 30px;
    height: 30px;
    position: fixed;
    background-repeat: no-repeat;
    cursor: pointer;
    color: rgba(0, 0, 0, 0.4);
    border: 1px solid transparent;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.8);
    right: calc(10% + 55px);
    top: calc(10% - 15px);
    line-height: 30px;
    text-align: center;
  }
  ._css-doc-preview-iframe._css-previewfull {
    height: 100%;
    width: 100%;
  }
  ._css-docpreview-fullscreen {
    font-size: 20px;
    flex: none;
    width: 30px;
    height: 30px;
    position: fixed;
    background-repeat: no-repeat;
    cursor: pointer;
    color: rgba(0, 0, 0, 0.4);
    border: 1px solid transparent;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.8);
    right: calc(10% + 20px);
    top: calc(10% - 15px);
    line-height: 30px;
    text-align: center;
  }
  ._css-canfull._css-isfulling {
    top:0;
    right: 0px;
  }
  ._css-docpreview-newtab._css-isfulling {
    right: 68px;
  }
  ._css-docpreview-fullscreen._css-isfulling {
    right: 34px;
  }
  ._css-table-body,._css-table-top {
    margin-top: 12px;
  }
  .el-table__header-wrapper {
    background: #F4F4F4;
    font-weight: bolder;
  }
  .element-doc-list {
    position: fixed;
    left: 50px;
    top: 150px;
    background: #fff;
    width: 260px;
    height: 400px;
    display: flex;
    flex-direction: column;
    z-index: 2;
    .element-doc-list-title {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 8px 12px;
      font-size: 16px;
      color: #132B4D;
      font-weight: bolder;
      background: #F8F8F8;
      .el-icon-close {
        cursor: pointer;
      }

    }
    .list-content {
      flex: 1;
      overflow-y: scroll;
      .no-data {
        text-align: center;
        padding-top: 24px;
      }
      .list-item {
        padding: 12px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        color: #132B4D;
        font-size: 14px;
        cursor: pointer;
        .handle-btn {
          display: none;
          position: absolute;
          right: 0;
        }
        .text {
          display: inline-block;
          width: 224px;
          overflow-x: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
        .text-box {
          display: flex;
          align-items: center;
          &:hover {
            color: #1890ff;
          }
        }
        &:hover {
          background: #F4F4F4;
          .handle-btn {
            display: flex;
          }
          .text {
            width: 190px;
          }
        }
        i {
          width: 16px;
          height:16px;
          margin-right: 2px;
        }
      }
    }
    .doc-footer {
      padding: 0 12px 12px;
      box-sizing: border-box;
      .btn {
        width: 100%;
        background: #1890FF;
        color: #fff;
        padding: 4px 0;
        text-align: center;
        cursor: pointer;
        &:hover {
          opacity: .8;
        }
      }
    }
  }
  .footer {
    display: flex;
    justify-content: flex-end;
    .handle-btn {
      padding: 8px 20px;
      font-size: 12px;
      cursor: pointer;
      opacity: .8;
      &.cancle {
        color: #081A33;
        font-weight: bolder;
      }
      &.confirm {
        background: #1890FF;
        color: #fff;
        margin-left: 8px;
      }
    }
  }
}
.content-frame{
  width: 100%;
  height: 100%;
  // height: calc(100% - 56px);
}

</style>
