<template>
	<div class="_css-menu">
    <div class="_css-tabcontrolarea-menu bgfff">
			<div class="_css-menu-text">项目菜单设置</div>
		</div>
    <div class="_css-site-list">
      <site-level-menu-list v-if="showMenu" :info_companyname="FullName" :OrganizeId="organizeId"></site-level-menu-list>
    </div>
  </div>
</template>
<script>
import siteLevelMenuList from '../../Admin/Index/siteLevelMenuList.vue';
export default {
	components: { siteLevelMenuList },
	name: "projectMenuManage",
	data() {
		return {
      organizeId: '',
      showMenu: false,
      FullName: ''
		};
	},
	mounted() {
    this.organizeId = this.$staticmethod._Get("organizeId");
    this.getProjectData();
	},
	methods: {
    getProjectData(){
      let _this= this, _Token = _this.$staticmethod.Get("Token");
      _this
        .$axios({
          method: "get",
          url: `${
            window.bim_config.webserverurl
          }/api/User/Project/GetProject?token=${_Token}&projectid=${
            _this.organizeId
          }`
        })
        .then(x => {
          //debugger;
          if ((x.status == 200) & (x.data.Ret > 0)) { 
            _this.FullName = x.data.Data.ProjectName; 
            _this.showMenu = true;
          }else{
            this.$message.error(x.data.Msg);
            _this.showMenu = true;
            return
          }
        })
        .catch(x => {
          this.$message.error('服务器异常，请稍后再试');
        });
    },
  },
};
</script>
<style lang="scss" scoped>
._css-tabcontrolarea-menu {
  background: #fff;;
	height: 54px;
	display: flex;
	align-items: center;
	flex: none;
	border-bottom: 1px solid rgba(0, 0, 0, 0.15);
	._css-menu-text {
		color: rgba(0, 0, 0, 0.9);
		margin-left: 16px;
		font-size: 16px;
    font-weight: 500;
	}
}
._css-menu{
  height: 100%;
}
._css-site-list,.project-menu-list{ 
  height: calc(100% - 80px);
}
</style>
