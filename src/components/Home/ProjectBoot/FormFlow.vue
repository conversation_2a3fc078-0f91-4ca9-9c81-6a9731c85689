<template>
  <div class="work-flow">
    <div class="btn-initiating-header">{{ headerTitle }}</div>
    <!-- <div class="btn-initiating">
      <div class="click-initiating" @click="InitiatingProcess">
        <i class="icon-suggested-plus"></i>
        &nbsp;&nbsp;创建
      </div>
      <div class="initiating-tab">
        <el-menu
          style="background: transparent;"
          :default-active="checkTabIndex"
          class="el-menu-demo"
          mode="horizontal"
          @select="handleSelectCheckTab"
        >
          <el-menu-item index="1">查看台账</el-menu-item>
          <el-menu-item index="2">业务追踪</el-menu-item> 
        </el-menu>
      </div>
    </div> -->
    
    <div class="dialog-content" v-if="showFormSrc">
      <div class="close-iframe"><i class="icon-suggested-close" @click.stop="closeInitProcess"></i></div>
      <iframe class="dialog-iframe" 
        ref="ccIframeList" 
        id="ccIframeList" 
        name="ccflowInitiatingProcess" 
        sandbox="allow-forms allow-same-origin allow-scripts allow-popups allow-modals allow-downloads" 
        :src="SrcInitiatingProcess" frameborder="0" width="80%"></iframe>
    </div>
    <div class="flow-flex">
      <div class="click-initiating" v-if="loadBool" :class="canInitiat?'': 'not-click'" @click="InitiatingProcess">
        <i class="icon-suggested-plus"></i>
        &nbsp;&nbsp;创建
      </div>
      <div class="click-radio" v-if="loadBool && currentisProjectManager == '0'" >
        <el-radio-group size="mini" v-model="iframeRadio" @input="inputChange">
          <el-radio-button label="我发起的"></el-radio-button>
          <el-radio-button label="我审批的"></el-radio-button>
        </el-radio-group>   
      </div>
      <iframe sandbox="allow-modals allow-forms allow-popups allow-scripts allow-same-origin allow-downloads" :src="workBookSrc" @load="workSrcLoad" frameborder="0" width="100%" height="100%"></iframe>
    </div>
    <!-- <div class="flow-flex" v-if="checkTabIndex=='2'">
      <el-menu
        :default-active="activeIndex"
        class="el-menu-demo"
        mode="horizontal"
        @select="handleSelectBusiness"
      >
        <el-menu-item index="1">我发起的</el-menu-item>
        <el-menu-item index="2">待办</el-menu-item>
        <el-menu-item index="3">已完成</el-menu-item>
        <el-menu-item index="4">在途</el-menu-item>
        <el-menu-item index="5">查询</el-menu-item>
        <el-menu-item index="6">抄送列表</el-menu-item>
      </el-menu>
      <div class="work-conter">
        <iframe :src="startlistSrc" frameborder="0" width="100%" height="100%"></iframe>
      </div>
    </div> -->
    
    
  </div>
</template>
<script>
import { mapGetters } from 'vuex' 

export default {
  name: "FormFlow",
  data() {
    return {
      headerTitle: '', 
      cctoken: '',
      flowNo: '', // 表单编号，地址栏参数直接获取
      activeIndex: "1",
      checkTabIndex: '1',
      todolistSrc:"",
			completetSrc: '',
			runingSrc: '',
      startlistSrc: '',
      copyToListSrc: '',
			searchZongHeSrc: '',
      SrcInitiatingProcess: '', // 发起流程地址
      showFormSrc: false, // 发起流程显示
      workBookSrc: '', // 台账的src
      canInitiat: false, // 是否有创建权限
      loadBool: false,
      iframeRadio: '我发起的',
      currentisProjectManager: '0', // 是否是机构管理员
    };
  },
  watch: {
    $route(to, from) {
      this.refresh();
    }, 
    getMenuListChange(){
    }
  },
  computed: {
    ...mapGetters([
      'getMenuListChange'
    ]),
  },
	created(){
    this.refresh();
	},
  mounted(){
    let _this = this;
    _this.headerTitle = _this.$staticmethod._Get("menuText") || '';
    _this.currentisProjectManager = _this.$staticmethod._Get("currentisProjectManager");
    console.log(_this.currentisProjectManager ,'====', typeof(_this.currentisProjectManager))
    window.addEventListener("message", function (data) {
      // 处理消息 data 数据
      console.log(data,'测试cc--probim')
      if(data.data.act == 'cc_close_form'){
        _this.closeInitProcess();
      }
    });
  },
  methods: {
    refresh(){
      this.headerTitle = this.$staticmethod._Get("menuText") || '';
      this.flowNo = this.$route.params.flowNo; 
      this.activeIndex = this.$route.params.status || '1'
      this.checkTabIndex = '1';
      this.getToken();
      this.getInitiatAuth();
    },
    // 获取当前用户是否有创建权限
    getInitiatAuth(){
      let _url = `${window.bim_config.CCFlowUrl}/api/v1/flow/checkstartflow?organizeId=${this.$staticmethod._Get("organizeId")}&flowNo=${this.flowNo}&userNo=${this.$staticmethod.Get("Account")}`
      this.$axios
        .get(_url)
        .then(res=>{
          if(res.status == 200 && res.data.data){
            this.canInitiat = true;
          }else{
            this.canInitiat = false;
          }
        })
        .catch(err=>{
          this.canInitiat = false;
        })
    },
    getToken() {
      let _this = this;
      _this.workBookSrc = '';
      let _OrganizeId = _this.$staticmethod._Get("organizeId");
      let _Token = _this.$staticmethod.Get("Token");
      let _url = `${window.bim_config.webserverurl}/api/User/User/CCFlowLogin`;
      let _para = {
        Token: _Token,
        organizeId: _OrganizeId,
      };
      _this
        .$axios({
          method: "post",
          url: _url,
          data: _para,
        })
        .then((x) => {
          if (x.data.Ret > 0) {
            _this.cctoken = x.data.Data;
            // currentisProjectManager=true是管理员
            if(_this.currentisProjectManager == '1'){
              _this.workBookSrc=`${window.bim_config.CCFlowUrl}/WF/RptDfine/Search.htm?SearchType=MyDept&FK_Flow=${_this.flowNo}&Token=${x.data.Data}&organizeId=${_OrganizeId}`
            }else{
              // 不是管理员，显示我发起的
              _this.workBookSrc=`${window.bim_config.CCFlowUrl}/WF/RptDfine/Search.htm?SearchType=My&FK_Flow=${_this.flowNo}&Token=${x.data.Data}&organizeId=${_OrganizeId}`
              // 我审批的
              // _this.workBookSrc=`${window.bim_config.CCFlowUrl}/WF/RptDfine/Search.htm?SearchType=MyJoin&FK_Flow=${_this.flowNo}&Token=${x.data.Data}&organizeId=${_OrganizeId}`
            }
           
            // this.handleSelectBusiness('1')
          } else {
            _this.$message.error(x.data.Data);
          }
        })
        .catch((x) => {
          console.error(x);
        });
    },
    inputChange(val){
      if(val == '我发起的'){
        this.workBookSrc=`${window.bim_config.CCFlowUrl}/WF/RptDfine/Search.htm?SearchType=My&FK_Flow=${this.flowNo}&Token=${this.cctoken}&organizeId=${this.$staticmethod._Get("organizeId")}`
      }else{
        this.workBookSrc=`${window.bim_config.CCFlowUrl}/WF/RptDfine/Search.htm?SearchType=MyJoin&FK_Flow=${this.flowNo}&Token=${this.cctoken}&organizeId=${this.$staticmethod._Get("organizeId")}`
      }
    },
    workSrcLoad(){
      this.workBookSrc.length > 0 ?  this.loadBool= true : this.loadBool= false
    },
    // handleSelectBusiness(key) {
    //   this.activeIndex = key;
    //   this.startlistSrc = this.srcFunInit(key);
    // },
    handleSelectCheckTab(key){
      this.checkTabIndex = key;
    },
    // srcFunInit(index){
    //   let _src = ''
    //   let org = this.$staticmethod._Get("organizeId");

    //   switch(index){
    //     case '1':
    //       _src = `${window.bim_config.CCFlowUrl}/WF/Comm/Search.htm?EnsName=BP.WF.Data.MyStartFlows&Token=${this.cctoken}&organizeId=${org}&FK_Flow=${this.flowNo}`
    //     break;
    //     case '2':
    //       _src = `${window.bim_config.CCFlowUrl}/WF/Todolist.htm?Token=${this.cctoken}&organizeId=${org}&FK_Flow=${this.flowNo}`
    //       break;
    //     case '3':
    //       _src = `${window.bim_config.CCFlowUrl}/WF/Complete.htm?Token=${this.cctoken}&organizeId=${org}&FK_Flow=${this.flowNo}`
    //       break;
    //     case '4':
    //       _src = `${window.bim_config.CCFlowUrl}/WF/Runing.htm?Token=${this.cctoken}&organizeId=${org}&FK_Flow=${this.flowNo}`
    //       break;
    //     case '5':
    //       _src = `${window.bim_config.CCFlowUrl}/WF/SearchDataGrid.htm?Token=${this.cctoken}&organizeId=${org}&FK_Flow=${this.flowNo}`
    //       break;
    //     case '6':
    //       _src = `${window.bim_config.CCFlowUrl}/WF/CC.htm?Token=${this.cctoken}&organizeId=${org}&FK_Flow=${this.flowNo}`
    //       break;
    //   }
    //   console.log(index,'==index',_src)
    //   return _src;
    // },
    InitiatingProcess(){
      if(!this.canInitiat) return
      let _url = `${window.bim_config.CCFlowUrl}/WF/Comm/Handler.ashx?DoType=HttpHandler&DoMethod=MyFlow_Init&HttpHandlerName=BP.WF.HttpHandler.WF_MyFlow&t=0.1878593346871571`;
      let _data = {
        WorkID: '',
        FK_Flow: this.flowNo,
        FK_Node: '',
        Token: this.cctoken,
      }
       this
        .$axios({
          method: "post",
          url: _url,
          data: this.$qs.stringify(_data),
        })
        .then((x) => {
          if (x.status == 200) {
            let _urltarget = x.data.replace("url@", '/')

            // const  win = window.open(`${window.bim_config.CCFlowUrl}/WF${_urltarget}`)
            // var win = window.open(`${window.bim_config.CCFlowUrl}/WF${_urltarget}&organizeId=${this.$staticmethod._Get("organizeId")}`,'', 'width=' + window.screen.availWidth + ',height=' + window.screen.availHeight + ',scrollbars=yes,resizable=yes,toolbar=false,location=false,center=yes,center: yes;');
            // win.focus();
            // const loop = setInterval(()=>{
            //   if(win && win.closed){
            //     console.log('refresh-load')
            //     this.refresh();
            //     clearInterval(loop)
            //   }
            // },500)
            
            // window.open(`${window.bim_config.CCFlowUrl}/WF${_urltarget}`);
            this.SrcInitiatingProcess = `${window.bim_config.CCFlowUrl}/WF${_urltarget}`;
            this.showFormSrc = true;
          }
        })
        .catch((x) => {
          console.log(x);
        });

    },
    closeInitProcess(){
      // 页面要刷新  窗口要关闭
      this.refresh();
      this.showFormSrc = false;
    }
  },
  beforeDestroy() {
		window.removeEventListener('message')
	},
};
</script>
<style lang="less" scoped>
.work-flow {
  text-align: left;
	display: flex;
	flex-direction: column;
	height: 100%;
}
.work-conter {
	height: 100%;
	flex: 1;
}
.flow-flex{
  height: calc(100% - 110px);
  flex: 1;
  position: relative;
}
.litter-num{
  position: absolute;
  right: 5px;
  top: -5px;
  color: #1890ff;
  font-size: 10px;
  font-weight: 500;
}
.btn-initiating{ 
  height: 54px;
  display: flex;
  align-items: center;
  padding: 0 24px;
  background: #f0f2f5;
  justify-content: space-between;
} 
.click-initiating{
  padding: 0 12px;
  height: 28px;
  line-height: 28px;
  cursor: pointer;
  background: #1890ff;
  color: #fff;
  border-radius: 4px;
  text-align: center;
  align-items: center;
  display: flex;
  position: absolute;
  top: 10px;
  left: 16px;
}
.click-radio{
  position: absolute;
  top: 12px;
  right: 134px;
}
.click-initiating.not-click{
  opacity: 0.5;
  cursor: not-allowed;
}
.work-flow /deep/ .el-menu--horizontal>.el-menu-item{
  height: 44px;
  line-height: 44px;
}
.dialog-content{
	position: fixed;
	top: 0;
	left: 0;
	bottom: 0;
	right: 0;
	width: 100%;
	height: 100%;
	z-index: 1002;
	background: rgba(0, 0, 0, 0.4);
}
.dialog-iframe{
  margin: 50px 0 0 10% ;
  height: calc(90% - 50px);
  border-radius: 8px;
}
.close-iframe{
  position: absolute;
  right: 10%;
  top: 50px;
  cursor: pointer;
  width: 32px;
  height: 32px;
  text-align: center;
  line-height: 31px;
  border-radius: 50%;
  vertical-align: middle;
  font-size: 24px;
}
.click-radio /deep/ .el-radio-button__orig-radio:checked+.el-radio-button__inner{
  color: #fff;
  background-color: #1890ff;
  border-color: #1890ff;
}
.click-radio /deep/ .el-radio-button--mini .el-radio-button__inner{
  padding: 0px 4px;
  height: 28px;
  line-height: 28px;
}
.click-radio /deep/ .el-radio-button__inner{
  border: 1px solid #d9d9d9;
  color: #8C8C8C;
}

</style>