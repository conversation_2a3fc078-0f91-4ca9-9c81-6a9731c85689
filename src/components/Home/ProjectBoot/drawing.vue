<template>
  <div class="drawing-dialog-wrapper" v-if="isShow">
    <div class="header">
      <div class="title">
          选择模型
      </div>
      <div class="handle-box">
          
          <div @click.stop="close" class="close">
              <i class="el-icon-close"></i>
          </div>
          
      </div>
    </div>

    <div class="label-content"
      :class="{h100: loading}"
      v-loading="loading"
      element-loading-spinner="el-icon-loading"
      element-loading-background="rgba(0, 0, 0, 0)">

        <div class="left-list">
          <p>
          项目阶段
          </p> 
          <el-tree
            class="el-tree-cus"
            highlight-current
            node-key="BusinessCode"
            ref="elTree"
            empty-text="暂无子级"
            :default-expand-all="false"
            :expand-on-click-node="true"
            :auto-expand-parent="true"
            :default-expanded-keys="treeExpandedKeys"
            :data="treeData"
            :props="elTreeProps"
            @node-click="stageClick">
              <div class="el-tree-node-cus" slot-scope="{ node }">
                <el-tooltip effect="dark" :content="node.label" placement="top" :enterable="false">
                  <div class="label">{{ node.label }}</div>
                </el-tooltip>
              </div>
          </el-tree>
      </div>
        <div class="left">
          <div class="left-handle-box">
            <!-- <el-select
              v-model="selectStageID"
              size="medium"
              placeholder="请选择"
              @change="changeSelect">
              <el-option
                v-for="item in projectStage"
                :key="item.BusinessCode"
                :label="item.Description"
                :value="item.BusinessCode"
                >
              </el-option>
            </el-select> -->

            <el-input v-model="searchValue" placeholder="请输入模型名称" size="medium" @input="changeSearchVal" style="width: 200px">
              <i slot="prefix" class="el-input__icon el-icon-search"></i>
            </el-input>

          </div>
          <div class="item-list" v-if="filterCurrentDatas.length">
            <div class="label-item"
                  v-for="item in filterCurrentDatas" :key="item.featureID"
                  :class="{'active':currentModelId===item.featureID}"
                  @click="opendrawing(item.featureID)">
                <div class="thumb">
                    <img v-if="item.thumbnail=='' || item.thumbnail=='Project'" :src="defaultImg" alt="">
                    <img v-else :src="'data:image/png;base64,'+item.thumbnail" alt="">
                </div>
                <p class="title">
                    {{ item.featureName }}
                </p>
            </div>
          </div>
          <div class="no-data" v-else>数据为空</div>
        </div>

        <div class="right">
          <div class="right-handle-box">
            <span class="title">模型视图</span>
            <div class="btn" @click="confirm()">确定</div>
          </div>

          <div class="no-data" v-if="formatViews.length === 0">{{currentModelId ? '视图数据为空' : '请选择左侧模型'}}</div>
          <template v-else>
            <div class="item-label" v-for="item in formatViews" :key="item.label" @click="item.listState=!item.listState">
                <template v-if="item.children.length">
                  <span>
                    {{ item.label }}
                    <i class="el-icon" :class="item.listState?'el-icon-caret-bottom':'el-icon-caret-right'"></i>
                  </span>
                  <template v-if="item.listState">
                    <div class="children-label" :class="{'active':currentViewId === v.id}" v-for="v in item.children" :key="v.id" @click.stop="selectViewId(v)">{{ v.name }}</div>
                  </template>
                </template>
            </div>
          </template>
        </div>
    </div>
  </div>
</template>

<script>
export default {
  name: '',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    featureID: {
      type: String
    }
  },

  data () {
    return {
      searchValue: '',
      labelDatas: [],
      treeData: [],
      elTreeProps: {
        children: 'Children',
        label: 'MenuName'
      },
      treeKey:"",
      treeExpandedKeys:[],  // 默认展开节点,当前选中的节点
      projectID: '',
      loading: false,
      VaultID: '',
      projectStage: [],
      selectStageID: '',
      formatViews: [],
      currentModelId: '',
      currentViewId: '',
      viewType: ''
    }
  },
  computed: {
    filterCurrentDatas() {
        let datas = []
        let keyWord = this.searchValue.toLowerCase()
        this.labelDatas.forEach(item=>{
            let modelName = item.featureName.toLowerCase()
            if (modelName.search(keyWord) != -1) {
                datas.push(item)
            }
        })
        return datas
    },
    isShow: {
      get () {
        return this.visible
      },
      set (v) {
        this.$emit('update:visible', v)
      }
    }
  },
  created () {
    this.VaultID = this.$staticmethod._Get("organizeId");
    // this.currentModelId = this.featureID
    // this.getNewModelInfo(this.currentModelId)
  },
  mounted () {
    // this.getAllFeatures()
    this.getModelPhaseList()
  },

  methods: {
    changeSearchVal () {
      this.formatViews = []
      this.currentModelId = ''
      this.currentViewId = ''
    },
    stageClick (data) {
      this.formatViews = []
      this.currentModelId = ''
      this.currentViewId = ''
      if (data === 'All') {
        this.getAllFeatures()
      } else {
        this.getModelList(data)
      }
    },
    // 获取项目阶段
		getModelPhaseList() {
      let _token = this.$staticmethod.Get("Token");
      const VaultID = this.$staticmethod._Get("organizeId");

      this.$axios
				.get(`${this.$urlPool.GetUserMenuTree}?token=${this.$staticmethod.Get("Token")}&organizeId=${this.VaultID}&parentId=0`)
				.then(res=>{
					if(res.data.Ret == 1) {
            let modelTree = res.data.Data.find(index=>index.MenuCode == 'MODEL');
            let newArr = modelTree.Children.filter(item => item.BusinessCode !== 'allmodel_phase');
            this.treeData = newArr
            this.stageClick(newArr[0])
            this.highlightTree(newArr[0].BusinessCode)
					}else{
						this.$message.error(res.data.Msg);
					}
				})
				.catch(err=>{})

			// this.$axios
			// 	.get(
			// 		`${this.$urlPool.GetModelMenu}?organizeId=${VaultID}&token=${_token}`
			// 	)
			// 	.then((res) => {
			// 		if (res.data.Ret == 1) {
			// 			let _data = res.data.Data;
      //       _data.unshift({
      //         Description: '全部模型',
      //         BusinessCode: 'All'
      //       })
			// 			this.projectStage = _data;
      //       this.selectStageID = 'All'
			// 		} else {
			// 			this.$staticmethod.debug(x);
			// 		}
			// 	})
			// 	.catch((err) => {});
		},

    highlightTree(UrlPhase) {
      if(UrlPhase && UrlPhase !== 'allmodel_phases') {
        const treeKey = UrlPhase
        this.$nextTick(() => {
          this.$refs.elTree.setCurrentKey(treeKey)
        })
      }
    },

    // 获取当前阶段下模型list
		getModelList(selectStageID) {
			this.loading = true
			
			this.$axios
				.get(
          `${this.$ip('newModelHttpUrl')}/Vault/GetFeaturesByPhase?VaultID=${this.VaultID}&Phase=${selectStageID.BusinessCode}`
				)
				.then((res) => {
					if (res.status === 200) {
						this.labelDatas = res.data
					}
					this.loading = false
				})
				.catch((err) => {
					this.loading = false
				});
		},

    getAllFeatures() {

      this.loading = true;
      this.$axios
        .get(`${this.$ip('newModelHttpUrl')}/Vault/getAllFeatures?VaultID=${this.VaultID}&token=${this.$staticmethod.Get("Token")}`)
        .then(res => {
          if (res.status === 200) {
            res.data.forEach(item=>{
                item.type = 'model'
                item.isDragData = true
                item.maxVersion = item.currentVersion
            })
            this.loading = false
            this.labelDatas = res.data
          } else {
            _this.$message.error('获取列表失败')
          }
        })
        .catch(err => {
          console.log(err)
        })
    },

    // 获取模型视图
		getNewModelInfo(openModelID) {
			let _this = this;
			_this.$axios
				.get(
					`${this.$ip("newModelHttpUrl")}/Model/GetAllViews?VaultID=${
						_this.VaultID
					}&ModelID=${openModelID}&Thumbnail=false&Token=${this.$staticmethod.Get('Token')}`
				)
				.then((res) => {
					if (res.status === 200) {
            let _3dArr = [];
            let GridArr = [];
            let _2dArr = [];
            res.data.filter(v => v.category !=='Elevation').forEach((item) => {
              if (item.viewType === "3D") {
                _3dArr.push(item);
              } else if (item.viewType === "2D") {
                if (item.category === "Grid") {
                  GridArr.push(item);
                } else {
                  _2dArr.push(item);
                }
              }
            });


            const formatViews = [
              {
                label: '三维视图',//三维视图
                icon: "3D",
                children: _3dArr,
                listState: false,
                type: "3D",
              },
              {
                label: '二维视图',//二维视图
                icon: "Plan",
                children: _2dArr,
                listState: false,
                type: "2D",
              },
              {
                label: '轴网',//轴网
                icon: "Grid",
                children: GridArr,
                listState: false,
                type: "Grid",
              },
            ];
            _this.$set(this, "formatViews", formatViews);
					}
				})
				.catch((err) => {
          this.formatViews = []
        });
		},

    selectViewId (data) {
      const {id, viewType} = data
      this.currentViewId = id
      this.viewType = viewType
    },
    close () {
      this.$emit('closeDialog')
    },

    confirm () {
      if (this.currentViewId === '') {
        this.$message.error('请选择视图')
        return
      }
      this.$emit('openDrawing', {
        modelId: this.currentModelId,
        viewId: this.currentViewId,
        viewType: this.viewType
      })
      this.close()
    },

    opendrawing (id) {
      this.currentModelId = id
      this.getNewModelInfo(id)
    }
  }
}
</script>


<style lang='scss' scoped>
.drawing-dialog-wrapper {
  width: 950px;
  height: 600px;
  position: fixed;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  margin: auto;
  z-index: 5;
  background: #242630;
  border-radius: 4px;
  .no-data {
    text-align: center;
    padding-top: 20px;
  }
  .header {
    background: #13141a;
    padding: 14px 12px;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-radius: 4px;
    color: #fff;
    .title {
      font-size: 14px;
    }
    .handle-box {
      display: flex;
      align-items: center;
      .close {
        margin-left: 12px;
        font-size: 20px;
        cursor: pointer;
      }
    }
  }
  .label-content {
    display: flex;
    height: 542px;
    .left-list {
          overflow: auto;
          width: 150px;
          border-right: 1px solid #475059;

          li {
            width: 200px;
            height: 40px;
            line-height: 40px;
            padding: 0 12px 0 28px;
            text-align: left;
            display: flex;
            justify-content: space-between;
            color: #fff;
            border-radius: 4px;

            p {
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
              width: 130px;
              display: inline-block;
            }

            &:hover, &.active {
              background: rgba(24, 144, 255, 0.1);
              color: #1890FF;
              cursor: pointer;

              span {
                color: #1890FF;
              }
            }

            span {
              color: rgba(0, 0, 0, 0.25);
            }
          }

          p {
            display: flex;
            align-items: center;
            font-size: 16px;
            text-align: left;
            margin: 0;
            margin-bottom: 15px;
            color: #fff;
            padding: 8px 0 0 8px;

            i {
              margin-right: 8px;
            }
          }
        }
    .left {
      width: 500px;
      overflow-y: scroll;
      padding: 16px 10px 0;
      box-sizing: border-box;
      .left-handle-box {
        display: flex;
        justify-content: space-between;
      }
    }
    .right {
      flex: 1;
      border-left: 1px solid #475059;
      overflow-y: scroll;
      color: #fff;
      .right-handle-box {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 24px;
        box-sizing: border-box;
        .title {
          font-size: 14px;
        }
        .btn {
          border: 1px solid #1890FF;
          border-radius: 2px;
          cursor: pointer;
          font-size: 12px;
          height: 24px;
          width: 44px;
          text-align: center;
          line-height: 24px;
        }
      }
      .item-label {
        span{
          padding: 10px 24px;
          display: flex;
          justify-content: space-between;
          width: 100%;
          box-sizing: border-box;
          cursor: pointer;
          &:hover {
            background: rgba(255,255,255,0.1);
          }
          
        }
        
        .children-label {
          padding: 10px 44px;
          box-sizing: border-box;
          cursor: pointer;
          white-space: nowrap;
          text-overflow: ellipsis;
          overflow: hidden;
          &:hover {
            background: rgba(255,255,255,0.1);
          }
          &.active {
            background: #2680FE; 
          }
        }
      }
    }
    .item-list {
      display: flex;
      flex-wrap: wrap;
    }
    .label-item {
        margin: 0;
        padding: 5px 8px;
        margin-top: 16px;
        cursor: pointer;
        border-radius: 4px;
        &.active {
          .thumb {
            border-color: #2680FE;
          }
        }

        .thumb {
            width: 138px;
            height: 98px;
            flex-shrink: 0;
            border: 2px solid transparent;
            border-radius: 2px;
            img {
              width: 100%;
              height: 100%;
            }
        }

        .title {
          width: 140px;
          white-space: nowrap;
          text-overflow: ellipsis;
          overflow: hidden;
          margin-top: 10px;
          text-align: center;
          color: #fff;
        }
    }
  }
  
}
</style>
<style lang="scss">
.drawing-dialog-wrapper {
  .el-input__inner {
    background: #242630;
    color: #fff;
    border: 1px solid #475059;
  }
  .el-tree {
    background: transparent;
    color:#fff;
  }
  .el-tree-node__content:hover {
    background: rgba($color: #fff, $alpha: .1);
    color: #fff;
  }
  .el-tree-node__content {
    background: transparent!important;
  }
  .el-tree--highlight-current .el-tree-node.is-current>.el-tree-node__content {
    background-color: rgba($color: #fff, $alpha: .1)!important;
  }
}
</style>
