<template>
  <div class="bottomDiv">
    <div id="id_minimsg_divctn" data-debug="message_vue_line3" class="Message_Mini">
      <div class="_css-minimsg-divctnl">
        <div v-for="(v, idx) in dataSourceLocal.MessageModuleInfos" :key="idx" class="_css-minimsg-module"
          :class="{ '_css-sel': selectModule == v.Module }" @click.stop="conditionSearch(v.Module, selectType)">
          <div class="_css-ifselborder"></div>
          <div class="_css-minimsg-text">{{ v.Module }}</div>
          <div v-if="v.UnReadQuantity" :title="v.UnReadQuantity" class="_css-minimsg-ifnum">
            {{ v.UnReadQuantity <= 99 ? v.UnReadQuantity : "..." }} </div>
          </div>
          <div class="_css-minimsg-viewall">
            <el-tooltip popper-class="css-no-triangle" effect="dark" content="打开消息列表" placement="bottom">
              <div @click.stop="OpenMessageList" class="_css-minimsg-viewall-in icon-interface-history"></div>
            </el-tooltip>
          </div><!--暂时隐藏-->
        </div>
        <div class="_css-minimsg-divctnr">
          <!-- 此区域未使用 -->
          <ul class="title" style="display: none"></ul>
          <!-- //此区域未使用 -->
          <div v-show="selectModule != '站内信'" class="btns _css-msg-typebtns">
            <el-tooltip popper-class="css-no-triangle" effect="dark" content="全部设为已读" placement="bottom">
              <div @click.stop="setallread()" class="_css-setreadall icon-suggested-check_circle"></div>
            </el-tooltip>
          </div>
          <div id="id_msglistdiv" v-loading="islistloading" element-loading-text="加载中"
            class="list css-miniscroll css-littlescroll">
            <ul class="_css-list-item" v-for="item in SearchData" :key="item.mu_guid">
              <!-- 站内信或其它消息的标题内容部分，你加个class不香吗？ -->
              <div class="list_ulli_firstchild">
                <i class="isUnRead" style="display: none"></i>
                <span @click.stop="onMessageClick(item)" style="cursor: pointer" class="t" :title="item.Content">
                  <template v-if="item.Module === '流程'">
                    {{ item.Content }}
                    <span class="flowLink" @click.stop="openNewWindow(item)">点击处理</span>
                  </template>
                  <template v-else>{{ item.Content }}</template>
                </span>
                <!-- 弹出的受邀请机构的消息中的确认按钮 -->
                <div @click="joinToCompany(item, $event)" v-if="item.LogAndMsgType == 50" class="_css-join-btn" style="">
                  确认加入
                </div>
                <!-- //弹出的受邀请机构的消息中的确认按钮 -->
              </div>
              <!-- //站内信或其它消息的标题内容部分，你加个class不香吗？ -->

              <!-- 站内信或其它消息的日期部分 -->
              <li class="list_ulli">
                <font class="CreateDate">
                  {{ getItemDateStr(item) }}
                </font>
              </li>
              <!-- //站内信或其它消息的日期部分 -->

              <div class="_css-setreaditem">
                <el-tooltip popper-class="css-no-triangle" effect="dark" :content="'设为已读'" placement="bottom">
                  <div @click.stop="setItemsRead([item])" class="_css-setreaditem-in icon-suggested-check_circle"></div>
                </el-tooltip>
              </div>
            </ul>
            <div class="_css-emptyctn" v-if="!SearchData || SearchData.length == 0">
              <div class="_css-emptyctn-in">没有新消息</div>
            </div>
          </div>
        </div>
      </div>
    </div>
</template>

<script>
const RegForTailStr = /[\s\.。]*$/i
export default {
  name: "Message2",
  data() {
    return {
      islistloading: false,
      SearchData: [],
      selectModule: "问题",
      selectType: "-1",
      dicdata: {
        Issue_Create: 4,
        Issue_Modify: 5,
        Issue_Delete: 6,
        Issue_Comment_Create: 7,
        Issue_Comment_Delete: 8,
        Issue_Doc_Create: 9,
        Issue_Doc_Delete: 10,

        Doc_NewFile: 21,
        Doc_RemoveFile: 22,
        Doc_ModifyFile: 23,
        Doc_MoveFile: 24,
        Doc_NewDir: 27,

        Web_Notify: 50,

        Flow_Submit: 47,
        Flow_Reject: 48,
        Flow_Examine: 49,

        // 这里别加，没用！

        Issue_Comment_Create_At: 1007,
      },
      dataSourceLocal: {
        List: [],
        MessageModuleInfos: [],
        MessageTypeInfos: [],
      },
    };
  },
  computed: {
    allModuleDataMap() {
      const result = {};
      if (this.dataSourceLocal && this.dataSourceLocal.List) {
        this.dataSourceLocal.List.forEach((item) => {
          const module = item.Module;
          if (!result[module]) {
            result[module] = [item];
          } else {
            result[module].push(item);
          }
        });
      }
      return result;
    },
  },
  created() {
    const _this = this;
    _this.dicdata = _this.$staticmethod.getMsgTypesObj();
    _this.setSelectModule();
    _this.conditionSearch(_this.selectModule, _this.selectType);
  },
  mounted() {
    this.$emit("OnShow");
  },
  methods: {
    getCCToken() {
      const _this = this;
      const _OrganizeId = _this.$staticmethod._Get("organizeId");
      const _Token = _this.$staticmethod.Get("Token");
      const _url = `${window.bim_config.webserverurl}/api/User/User/CCFlowLogin`;
      const _para = {
        Token: _Token,
        organizeId: _OrganizeId,
      };
      return _this
        .$axios({
          method: "post",
          url: _url,
          data: _para,
        })
        .then((x) => {
          if (x.data.Ret > 0) {
            return x.data.Data;
          } else {
            _this.$message.error(x.data.Data);
          }
        })
        .catch(() => {
        });
    },
    async openNewWindow(info) {
      this.$staticmethod._Set("organizeId", info.ProjectId);
      const ccToken = await this.getCCToken()
      if (ccToken && info.linkUrl) {
        const urlParts = info.linkUrl.split(/token=/i)
        if (urlParts.length > 1) {
          const parts = urlParts[1].split("&")
          const tokenStr = parts[0]
          const tailIdx = tokenStr.indexOf("_")
          let tokenTail = ""
          if(tailIdx !== -1) {
            tokenTail = tokenStr.substring(tailIdx, tokenStr.length)
          }
          const finalTokenStr = ccToken + tokenTail
          parts[0] = `Token=${finalTokenStr}`
          const finalUrl = urlParts[0] + parts.join("&")
          window.open(finalUrl, "_blank")
        }
      } else {
        this.$message({message:"未获取到跳转地址",type:"warning"})
      }
    },
    // 点击消息的文字
    // -------------
    async onMessageClick(item) {
      // 用于比较是否需要更换项目
      const organizeId = this.$staticmethod._Get("organizeId");
      // 根据item包含的消息，组合出跳转页面和展开树所需的信息
      // 根据所属的模块进行分支判断
      let urlToJump;
      sessionStorage.setItem('organizeId', item.ProjectId)
      const _OrganizeId = item.ProjectId
      const _Token = localStorage.getItem("Token");
      const _UserId = this.$staticmethod.Get("UserId")
      switch (this.selectModule) {
        case "站内信": {
          break;
        }
        case "多方协同": {
          sessionStorage.setItem("UrlChangedBy", `Msg-${this.selectModule}`);
          urlToJump = `${window.location.origin}${window.bim_config.hasRouterFile}/#/Home/ProjectBoot/Issue/${_OrganizeId}/${_Token}`;
          window.location.href = urlToJump;
          if (organizeId !== _OrganizeId) {
            window.location.reload();
          }
          break;
        }
        case "进度": {
          const url = `${window.bim_config.webserverurl}/api/Schedual/Schedual/GetSingleProgressNew?objectId=${item.ObjectId}&Token=${_Token}`;
          const res = await this.$axios.get(url);
          if (res && res.data && res.data.Ret > 0) {
            // 跳转前做个标记:UrlChangeBy值的结构可约定为：Msg开头代表是点击消息引起的路由变更,横杠-后面接的是消息所属的Module(中文)
            sessionStorage.setItem("UrlChangedBy", `Msg-${this.selectModule}`);
            sessionStorage.setItem("MsgRelatedData", JSON.stringify(res.data.Data)); // 消息关联的数据:针对进度来说就是能够根据这个值找到树的展开层级
            urlToJump = `${window.location.origin}${window.bim_config.hasRouterFile}/#/Home/ProjectBoot/ProgressReporting/${_OrganizeId}/${_Token}`;
            window.location.href = urlToJump;
            if (organizeId !== _OrganizeId) {
              window.location.reload();
            }
            break;
          } else {
            sessionStorage.setItem("UrlChangedBy", "");
            sessionStorage.setItem("MsgRelatedData", "");
            this.$message({
              type: "warn",
              message: "未找到消息关联的数据"
            })
            break;
          }
        }
        case "流程": {
          break;
        }
        case "质量安全": {
          break;
        }
        case "验收": {
          sessionStorage.setItem("UrlChangedBy", `Msg-${this.selectModule}`);
          sessionStorage.setItem("MsgRelatedData", item.ObjectId); // 消息关联的数据:针对质量验收来说就是能够根据这个值找到树的展开层级

          urlToJump = `${window.location.origin}${window.bim_config.hasRouterFile}/#/Home/ProjectBoot/qualityAcceptance/${_OrganizeId}/${_Token}`;
          window.location.href = urlToJump;
          if (organizeId !== _OrganizeId) {
            window.location.reload();
          }
          break;
        }
        case "档案": {
          const url = `${window.bim_config.webserverurl}/api/Archives/Archives/Info?id=${item.ObjectId}&Token=${_Token}`;
          const res = await this.$axios.get(url);
          if (res && res.data && res.data.Ret > 0) {
            const info = await this.$axios.get(`${window.bim_config.webserverurl}/api/User/User/GetUserInfo?token=${_Token}&userId=${_UserId}`);
            let projectId = '';
            if (info.data.Ret === 1) {
              if (info.data.Data.HasDataSummaryProject) {
                // 存在汇总项目
                projectId = info.data.Data.SummaryProjectId;
              } else {
                let haveProject = false;
                info.data.Data.UserProjectInfos.map(item => {
                  if (item.ProjectId === item.ProjectId) {
                    haveProject = true;
                  }
                })
                if (haveProject) {
                  // 存在对应的BIMe项目
                  projectId = item.ProjectId;
                } else {
                  // 登录人不在汇总项目,也不再BIMe项目
                  this.$message.error('您无权限处理!');
                  return;
                }
              }
              sessionStorage.setItem('organizeId', projectId);
              // 跳转前做个标记:UrlChangeBy值的结构可约定为：Msg开头代表是点击消息引起的路由变更,横杠-后面接的是消息所属的Module(中文)
              sessionStorage.setItem("UrlChangedBy", `Msg-${this.selectModule}`);
              sessionStorage.setItem("MsgRelatedData", JSON.stringify(res.data.Data)); // 消息关联的数据:针对进度来说就是能够根据这个值找到树的展开层级
              urlToJump = `${window.location.origin}${window.bim_config.hasRouterFile}/#/Home/ProjectBoot/${info.data.Data.HasDataSummaryProject ? 'ProjectArchivesSummary' : 'ProjectArchives'}/${projectId}/${_Token}`;
              window.location.href = urlToJump;
              if (organizeId !== projectId) {
                window.location.reload();
              }
            }
          } else {
            sessionStorage.setItem("UrlChangedBy", "");
            sessionStorage.setItem("MsgRelatedData", "");
            this.$message({
              type: "warn",
              message: "未找到消息关联的数据"
            })
          }
          break;
        }
      }

    },

    getItemDateStr(item) {
      return item.CreateTime;
    },

    // 执行加入机构
    // -----------
    do_JoinToCompany(mu_guid, orgid, orgm) {
      var _this = this;
      var _url = `${window.bim_config.webserverurl}/api/Message/JPush/DoJoinCompany`;
      _this
        .$axios({
          method: "post",
          url: _url,
          data: _this.$qs.stringify({
            mu_guid: mu_guid,
            OrganizeId: orgid,
            Token: this.$staticmethod.Get("Token"),
          }),
        })
        .then((x) => {
          if (x.status == 200) {
            if (x.data.Ret > 0) {
              // 提示用户已加入机构，并修改logo和名称（如果显示）
              // -------------------------------------------
              _this.$message.success(
                `您已加入机构${x.data.Data.FullName}`
              );

              // 给上面发通知，刷新logo和名称
              // --------------------------
              if (
                window.location.href.indexOf("Home/Boot/") >= 0
              ) {
                //window.location.href = `${window.bim_config.hasRouterFile}/#/Home/Boot/${_this.$staticmethod.Get("Token")}`;
                window.location.reload();
              } else {
                // 通知外面把图标更新一下
                // --------------------
                // _this.$emit("do_refresh_from_beusing");

                // 把小界面上的数字刷一下
                // ------------------
                // _this.conditionSearch(
                //     _this.selectModule,
                //     _this.selectType
                // );
              }
            } else {
              _this.$message.error(x.data.Msg);
            }
          } else {
            console.error(x);
          }
        })
        .catch((x) => {
          console.error(x);
        });
    },

    // 加入机构
    // --------
    joinToCompany(item, ev) {
      // 弹出操作确认
      // -----------
      var _this = this;
      //             item.mu_guid
      // "65b3ecd6-2b49-4ec5-9d3b-26a3a31f952c"
      // item.orgid
      // "d3cb14ea-9db0-48fc-b53c-6388cb3f6777"
      var _mu_guid = item.mu_guid;
      var _orgid = item.mme_ProjectID;
      var _orgm = item.orgm;
      _this
        .$confirm(`确认加入机构？`, "操作确认", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        })
        .then((x) => {
          _this.do_JoinToCompany(_mu_guid, _orgid, _orgm);
        })
        .catch((x) => { });
    },

    // 更新内部的消息个数的数字
    // ---------------------
    updateMsgCntNumber(
      NotifyCnt,
      IssueCnt,
      DocCnt,
      FlowCnt,
      willloadmsgagain
    ) {
      var _this = this;
      _this.NotifyUnReadCnt = NotifyCnt;
      _this.IssueUnReadCnt = IssueCnt;
      _this.DocUnReadCnt = DocCnt;
      _this.FlowUnReadCnt = FlowCnt;

      // 顺带再把消息加载了
      if (willloadmsgagain) {
        _this.conditionSearch(_this.selectModule, _this.selectType);
      }
    },

    // 项目内部页面消息列表单个设为已读
    // 并通知外面，要更新数字和红点状态了
    // <Boot 及 <ProjectBoot 对此组件有引用
    // -----------------------------------
    setItemsRead(items) {
      const _this = this;
      _this.islistloading = true;
      // 设为已读
      _this
        .$axios({
          url: `${window.bim_config.webserverurl
            }/api/User/Message/Read?Token=${_this.$staticmethod.Get(
              "Token"
            )}`,
          method: "post",
          data: items.map((item) => item.MessageId),
        })
        .then((x) => {
          if (x.status == 200) {
            if (x.data.Ret > 0) {
              _this.$message.success("操作成功");
              // _this.$emit("do_refresh_from_beusing");
              _this.islistloading = false;
              _this.conditionSearch(
                _this.selectModule,
                _this.selectType
              );
            } else {
              console.error(x.data.Msg);
              _this.islistloading = false;
            }
          } else {
            console.error(x);
            _this.islistloading = false;
          }
        })
        .catch((x) => {
          console.error(x);
          _this.islistloading = false;
          // _this.$emit("do_refresh_from_beusing");
        });
    },

    do_setallread() {
      var _this = this;
      console.log(_this.selectModule);
      var typearr = [];
      if (_this.selectModule == "问题追踪") {
        typearr = [
          _this.dicdata.Issue_Create,
          _this.dicdata.Issue_Modify,
          _this.dicdata.Issue_Delete,
          _this.dicdata.Issue_Comment_Create,
          _this.dicdata.Issue_Comment_Delete,
          _this.dicdata.Issue_Doc_Create,
          _this.dicdata.Issue_Doc_Delete,
        ];
      } else if (_this.selectModule == "项目文档") {
        typearr = [
          _this.dicdata.Doc_NewFile,
          _this.dicdata.Doc_RemoveFile,
          _this.dicdata.Doc_ModifyFile,
          _this.dicdata.Doc_MoveFile,
          _this.dicdata.Doc_NewDir,
        ];
      } else if (_this.selectModule == "项目流程") {
        typearr = [
          _this.dicdata.Flow_Examine,
          _this.dicdata.Flow_Reject,
          _this.dicdata.Flow_Submit,
        ];
      } else {
        typearr = [];
      }
      var arrstr = typearr.join(",");
      console.log(arrstr);
      console.log(_this.$staticmethod.Get("Token"));

      // 调用接口，修改指定Token的某些类型消息为已读
      _this
        .$axios({
          url: `${window.bim_config.webserverurl}/api/Message/JPush/SetMsgReadByTypeAndToken`,
          method: "post",
          data: _this.$qs.stringify({
            Types: arrstr,
            Token: _this.$staticmethod.Get("Token"),
          }),
        })
        .then((x) => {
          if (x.status == 200) {
            if (x.data.Ret > 0) {
              _this.$message.success("操作成功");
              // _this.$emit("do_refresh_from_beusing");
              _this.LoadMsgUnRead();
            } else {
              console.error(x);
            }
          } else {
            console.error(x);
          }
        })
        .catch((x) => {
          console.error(x);
        });
    },

    // 全部设为已读
    setallread() {
      const _this = this;
      const notreadlen =
        _this.allModuleDataMap[_this.selectModule].length;
      if (notreadlen == 0) {
        _this.$message.warning("当前不存在未读消息");
        return;
      }

      _this
        .$confirm("确定全部置为已读？", "操作确认", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        })
        .then((x) => {
          this.setItemsRead(
            _this.allModuleDataMap[_this.selectModule]
          );
        })
        .catch((x) => { });
    },

    OpenMessageList() {
      this.$emit("OpenMessageList");
    },

    // 获取未读消息
    getMessage(args) {
      const _this = this;
      if (!_this.$staticmethod.isObject(args)) {
        args = { Module: "站内信", Type: -1 }; // 目前测试来看好像只要Module有值就行
      }
      const _token = _this.$staticmethod.Get("Token");
      const queryString = _this.$qs.stringify(args);
	    if(!_token){return}

      const url = `${window.bim_config.webserverurl}/api/User/Message/List?${queryString}&Token=${_token}`;
      return _this.$axios
        .get(url)
        .then((x) => {
          if (x.status == 200) {
            if (x.data.Ret > 0) {
              const res = x.data.Data;
              if(res.List.length > 0) {
                const toRemove = ['质量安全', '质量管理', '安全管理', '安全', '质量', '问题', '档案管理'];
                const filterList = res.List.filter(item => !toRemove.includes(item.Module));
                console.log('filterList', JSON.stringify(filterList))
                let bool
                bool = filterList.length > 0;
                this.$emit('updateDotStatus', bool)
              }
              res.List.forEach(item => {
                if (item.Module === "流程") {
                  const content = item.Content.toLocaleLowerCase()
                  let idxHttp = content.indexOf("http://")
                  if(idxHttp === -1) {
                    idxHttp = content.indexOf("https://")
                  }
                  if(idxHttp !== -1) {
                    item.linkUrl = item.Content.substring(idxHttp).replace(RegForTailStr,"")
                    item.Content = item.Content.substring(0,idxHttp)
                  }
                }
              })
              return {
                List: res.List,
                MessageModuleInfos: res.MessageModuleInfos,
                MessageTypeInfos: res.MessageTypeInfos,
              };
            } else {
              // _this.$message.error(x.data.Msg);
			        console.error(x.data.Msg)
            }
          } else {
            console.error(x);
          }
        })
        .catch((x) => {
          console.error(x);
        });
    },
    // 更新窗口消息:Boot.vue中handleUpdateMsg函数中有调用
    refreshMessage(data) {
      if (data.Module) {
        if (data.Module !== this.selectModule) {
          this.selectModule = data.Module; // 当前查看的Module不是推送的消息所属的Module则切换为消息所属的Module
        }
      }
      this.conditionSearch(this.selectModule, this.selectType);
    },
    // 条件检索
    async conditionSearch(module, type) {
      console.log("Message2.vue : conditionSearch", module, type);

      // 设置右上角消息操作类型的按钮的选中样式
      // ————及左侧的表示消息模块的按钮样式
      // -----------------------------------
      const _this = this;
      _this.selectModule = module;
      _this.selectType = type;

      _this.islistloading = true;

      // 请求数据
      const msgInfo = await _this.getMessage({
        Module: module,
        Type: type,
        HasRead: false,
      });

      _this.islistloading = false;

      if (msgInfo) {
        this.dataSourceLocal = msgInfo;
      } else {
        this.dataSourceLocal = {
          List: [],
          MessageModuleInfos: [],
          MessageTypeInfos: [],
        };
      }
      this.$nextTick(() => {
        if (this.allModuleDataMap[module]) {
          this.SearchData = this.allModuleDataMap[module].filter(
            (item) => item.HasRead === false
          );
        } else {
          this.SearchData = [];
        }
      })
    },

    // MessageContent(msgobj) {
    //   var _this = this;
    //   if (msgobj.Op_RealName == "") {
    //     msgobj.Op_RealName = null;
    //   }
    //   console.log(msgobj.LogAndMsgType);
    //   //debugger;
    //   var outJson = {};
    //   if (msgobj.LogAndMsgType == _this.dicdata.Issue_Create) {
    //     // 弹出消息
    //     outJson = {
    //       title: "【问题】",
    //       message: msgobj.ExtDataContent,
    //     };
    //   } else if (msgobj.LogAndMsgType == _this.dicdata.Issue_Delete) {
    //     outJson = {
    //       title: "【问题】",
    //       message: msgobj.ExtData,
    //     };
    //   } else if (msgobj.LogAndMsgType == _this.dicdata.Issue_Modify) {
    //     outJson = {
    //       title: "【问题】",
    //       message: msgobj.BI_Title
    //         ? `【${msgobj.Op_RealName}】修改了问题【${msgobj.BI_Title}】`
    //         : msgobj.ExtData,
    //     };
    //   } else if (
    //     msgobj.LogAndMsgType == _this.dicdata.Issue_Comment_Create
    //   ) {
    //     outJson = {
    //       title: "【问题】",
    //       message: msgobj.ExtDataContent,
    //     };
    //   } else if (
    //     msgobj.LogAndMsgType == _this.dicdata.Issue_Comment_Delete
    //   ) {
    //     outJson = {
    //       title: "【问题】",
    //       message: `【${msgobj.Op_RealName}】移除了问题【${msgobj.BI_Title}】中的评论`,
    //     };
    //   } else if (msgobj.LogAndMsgType == _this.dicdata.Issue_Doc_Create) {
    //     outJson = {
    //       title: "【问题】",
    //       message: `【${msgobj.Op_RealName}】向问题【${msgobj.BI_Title}】中添加了关联文档`,
    //     };
    //     //************ */
    //   } else if (msgobj.LogAndMsgType == _this.dicdata.Issue_Doc_Delete) {
    //     outJson = {
    //       title: "【问题】",
    //       message: `【${msgobj.Op_RealName}】移除了问题【${msgobj.BI_Title}】中的关联文档`,
    //     };
    //     //*************** */
    //   } else if (msgobj.LogAndMsgType == _this.dicdata.Doc_NewFile) {
    //     outJson = {
    //       title: "【文档】",
    //       message: msgobj.Op_RealName
    //         ? `【${msgobj.Op_RealName}】上传了文档【${msgobj.mm_objname}】`
    //         : msgobj.ExtData,
    //     };
    //   } else if (msgobj.LogAndMsgType == _this.dicdata.Doc_RemoveFile) {
    //     outJson = {
    //       title: "【文档】",
    //       message: msgobj.Op_RealName
    //         ? `【${msgobj.Op_RealName}】删除了文档【${msgobj.mm_objname}】`
    //         : msgobj.ExtData,
    //     };
    //   } else if (msgobj.LogAndMsgType == _this.dicdata.Doc_ModifyFile) {
    //     outJson = {
    //       title: "【文档】",
    //       message: msgobj.Op_RealName
    //         ? `【${msgobj.Op_RealName}】修改了文档【${msgobj.mm_objname}】`
    //         : msgobj.ExtData,
    //     };
    //   } else if (msgobj.LogAndMsgType == _this.dicdata.Doc_MoveFile) {
    //     outJson = {
    //       title: "【文档】",
    //       message: msgobj.Op_RealName
    //         ? `【${msgobj.Op_RealName}】移动了文档【${msgobj.mm_objname}】`
    //         : msgobj.ExtData,
    //     };
    //   } else if (msgobj.LogAndMsgType == _this.dicdata.Doc_NewDir) {
    //     outJson = {
    //       title: "【文档】",
    //       message: msgobj.Op_RealName
    //         ? `【${msgobj.Op_RealName}】新增了文件夹【${msgobj.mm_objname}】`
    //         : msgobj.ExtData,
    //     };
    //   } else if (
    //     msgobj.LogAndMsgType == _this.dicdata.Issue_Comment_Create_At
    //   ) {
    //     outJson = {
    //       title: "【问题】",
    //       dangerouslyUseHTMLString: true,
    //       message: `【提到了你】${msgobj.ExtDataContent}`,
    //     };
    //   } else if (msgobj.LogAndMsgType == _this.dicdata.Web_Notify) {
    //     outJson = {
    //       title: "【站内信】",
    //       message: msgobj.orgm
    //         ? `【${msgobj.Op_RealName}】邀请你加入机构【${msgobj.orgm}】`
    //         : msgobj.ExtData,
    //     };

    //     // 流程相关
    //   } else if (
    //     msgobj.LogAndMsgType == _this.dicdata.Flow_Submit ||
    //     msgobj.LogAndMsgType == _this.dicdata.Flow_Examine ||
    //     msgobj.LogAndMsgType == _this.dicdata.Flow_Reject
    //   ) {
    //     outJson = {
    //       title: "【流程】",
    //       message: `${msgobj.ExtDataContent}`,
    //     };
    //   } else {
    //     //console.warn('有消息类型未处理', msgobj);
    //     //debugger;
    //   }

    //   if (outJson && outJson.message) {
    //     return outJson.message.replace("【】", "");
    //   } else {
    //     return "";
    //   }
    // },

    // 设置selectModule（从sessionStorage中读取）
    setSelectModule() {
      const moduleFromSignalR =
        this.$staticmethod._Get("moduleFromSignalR");
      if (moduleFromSignalR) {
        this.selectModule = moduleFromSignalR;
      } else {
        this.selectModule = '站内信'
      }
    },
  },
};
</script>

<style scoped>
._css-join-btn {
  margin-right: 0;
  width: auto;
  padding: 2px 6px 2px 6px;
  line-height: 20px;
  position: absolute;
  border: 1px solid transparent;
  background-color: #1890ff;
  color: #fff;
  border-radius: 4px;
  text-align: center;
  box-sizing: border-box;
  cursor: pointer;
  right: 52px;
  bottom: 5px;
  opacity: 0.8;
}

._css-join-btn:hover {
  opacity: 1;
}

._css-list-item {
  position: relative;
}

.flowLink {
  color: rgba(24, 144, 255, 1);
  cursor: pointer;
}

._css-setreadall {
  color: rgba(0, 0, 0, 0.4);
  position: absolute;
  right: 12px;
  cursor: pointer;
  width: 24px;
  height: 24px;
  font-size: 17px;
  line-height: 24px;
}

._css-setreadall:hover {
  background-color: rgba(24, 144, 255, 0.1);
  color: rgba(24, 144, 255, 1);
}

._css-setreaditem {
  position: absolute;
  right: 12px;
  cursor: pointer;
  width: 24px;
  /* height: 100%; */
  font-size: 17px;
  /* line-height: 24px; */
  /* top: 0; */
  bottom: 6px;
  right: 12px;
  display: flex;
  align-items: center;
}

._css-setreaditem-in {
  color: rgba(0, 0, 0, 0.4);
  right: 12px;
  cursor: pointer;
  width: 24px;
  height: 24px;
  font-size: 17px;
  /* line-height: 24px; */
  top: 0;
  right: 12px;
  line-height: 24px;
}

._css-setreaditem-in:hover {
  background-color: rgba(24, 144, 255, 0.1);
  color: rgba(24, 144, 255, 1);
}

._css-minimsg-ifnum {
  width: 14px;
  height: 14px;
  border-radius: 2px;
  font-size: 12px;
  line-height: 14px;
  color: rgba(255, 255, 255, 1);
  background-color: rgba(245, 34, 45, 1);
}

._css-minimsg-text {
  width: 68px;
  height: 22px;
  line-height: 22px;
  color: rgba(0, 0, 0, 0.65);
  font-size: 14px;
}

._css-minimsg-module {
  height: 40px;
  margin-top: 16px;
  display: flex;
  align-items: center;
  cursor: pointer;
}

._css-minimsg-module:hover,
._css-minimsg-module._css-sel {
  background-color: rgba(0, 0, 0, 0.04);
  font-weight: 500;
}

._css-minimsg-module._css-sel ._css-minimsg-text {
  font-weight: bold;
}

._css-minimsg-module ._css-ifselborder {
  width: 4px;
  height: 100%;
  background-color: transparent;
}

._css-minimsg-module._css-sel ._css-ifselborder {
  width: 4px;
  height: 100%;
  background-color: rgba(24, 144, 255, 1);
}

._css-minimsg-viewall {
  position: absolute;
  width: 100%;
  bottom: 16px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: space-around;
}

._css-minimsg-viewall-in {
  width: 28px;
  height: 28px;
  color: rgba(0, 0, 0, 0.4);
  font-size: 20px;
  cursor: pointer;
  line-height: 28px;
}

._css-minimsg-viewall-in:hover {
  color: #1890ff;
  background-color: rgba(24, 144, 255, 0.1);
}

._css-msg-typebtns {
  display: flex;
  align-items: center;
  position: relative;
}

._css-emptyctn {
  height: 100%;
  box-sizing: border-box;
  display: flex;
  align-items: center;
}

._css-emptyctn-in {
  text-align: center;
  width: 100%;
  color: rgba(0, 0, 0, 0.35);
}

ul,
li {
  padding: 0px;
  margin: 0px;
  list-style-type: none;
}

.bottomDiv {
  width: 50px;
  height: 453px;
  position: absolute;
  display: block;
  /* z-index: 999; */
  background-color: transparent;
  top: 12px;
  right: 120px;
  overflow: visible;
  /* font-family:PingFangSC; */
  cursor: normal;
}

._css-minimsg-divctnl {
  width: 90px;
  display: flex;
  flex-direction: column;
  position: relative;
  background-color: rgba(0, 0, 0, 0.04);
}

.Message_Mini {
  width: 500px;
  height: 425px;
  background-color: #fff;
  box-shadow: 0px 1px 6px #dfdfdf;
  border-radius: 2px;
  overflow: hidden;
  position: fixed;
  right: 24px;
  top: 48px;
  z-index: 1000;
  display: flex;
}

.Message_Mini .title {
  width: 100%;
  height: 40px;
}

.Message_Mini .title li {
  width: 110px;
  float: left;
  height: 40px;
  line-height: 40px;
  font-size: 14px;
  color: rgba(0, 0, 0, 0.65);
  position: relative;
  cursor: pointer;
}

.Message_Mini .title li i {
  display: block;
  position: absolute;
  right: 10px;
  top: 13px;
  width: 14px;
  height: 14px;
  font-style: normal;
  background-color: red;
  color: #fff;
  border-radius: 2px;
  text-indent: 0px;
  line-height: 14px;
  font-size: 12px;
  text-align: center;
}

.Message_Mini .title li.sel {
  border-bottom: 2px solid rgba(24, 144, 255, 1);
  height: 38px;
}

.Message_Mini .btns {
  width: 100%;
  height: 54px;
  font-size: 0px;
}

.Message_Mini .btns button {
  width: 60px;
  height: 24px;
  font-size: 12px;
  color: #999;
  /* margin:11px 16px 0px 0px; */

  border: none;
  background-color: #f5f5f5;
  border-radius: 20px;
  cursor: pointer;
  outline: none;

  margin-left: 8px;
}

._css-minimsg-divctnr {
  display: flex;
  flex-direction: column;
  height: 100%;
  width: calc(100% - 70px);
  padding-left: 16px;
  padding-right: 16px;
  box-sizing: border-box;
}

.Message_Mini .btns button:first-child {
  margin-left: 0px;
}

.Message_Mini .btns button.sel {
  background-color: rgba(24, 144, 255, 1);
  color: #fff;
}

.Message_Mini .fuc {
  width: 100%;
  height: 59px;
}

.Message_Mini .list {
  width: calc(100%);
  /* height:calc(100% - 40px - 54px - 59px); */
  overflow-x: hidden;
  overflow-y: scroll;
  flex: 1;
  margin-bottom: 9px;
}

.Message_Mini .list ul {
  /* width:100%;
    height:64px;
    background-color:rgba(0, 0, 0, 0.02);
    margin-top:8px;
    padding-top:12px;
    box-sizing: border-box; */

  width: 100%;
  background-color: rgba(0, 0, 0, 0.02);
  margin-top: 8px;
  padding-top: 12px;
  padding-bottom: 2px;
}

.Message_Mini .list ul:hover {
  background-color: rgba(24, 144, 255, 0.1);
}

.Message_Mini .list ul li .t {
  width: calc(100% - 16px - 40px);
  height: 100%;
  float: left;
  display: block;
  color: rgba(0, 0, 0, 0.85);
  font-weight: bold;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.Message_Mini .list ul li .isUnRead {
  width: 7px;
  height: 7px;
  display: block;
  float: left;
  background-color: rgba(24, 144, 255, 1);
  border-radius: 50px;
  margin: 6px 6px 0px 0px;
}

.Message_Mini .list ul li .CreateDate {
  color: #bfbfbf;
  text-indent: 12px;
  display: block;
  width: 100%;
  height: 100%;
}

/* .Message_Mini .list ul li:first-child{

} */

.list_ulli {
  width: calc(100% - 32px);
  height: 20px;
  margin-bottom: 6px;
  text-align: left;
  padding: 0px 16px 0px 16px;
  line-height: 20px;
  font-size: 12px;
}

/* 站内信或其它消息的标题内容部分 */
.list_ulli_firstchild {
  width: 100%;
  /* height:20px; */
  margin-bottom: 6px;
  font-size: 12px;
  padding: 0 16px 0 16px;
  text-align: left;
  box-sizing: border-box;
}

.Message_Mini .fuc .OpenMessageList {
  width: 298px;
  height: 32px;
  border: none;
  outline: none;
  background: rgba(0, 0, 0, 0.04);
  color: rgba(0, 0, 0, 0.65);
  cursor: pointer;
  line-height: 30px;
  padding: 0px;
  margin-top: 20px;
  font-size: 14px;
}
</style>
