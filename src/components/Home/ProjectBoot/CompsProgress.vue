<template>
    <div class="el-progress-wp" @click.stop="closeAll()">
        <!-- <header v-if="headerHide"></header> -->
        <!-- <header>
            <span>{{ headerTitleText}}</span>
        </header> -->
        <div class="btn-initiating-header">{{ headerTitleText }}</div>

        <main>
            <progressList
                :defaultExpandedKeys="defaultExpandedKeys"
                ref="list"
                @nodeClick="treeClick"
                @listSelectData="removeClick"
                :treeData="progressListData"
                @loadNode="loadNode"
                :r_hasAuthEdit="r_hasAuthEdit"
            ></progressList>
            <div class="gantt-wp">
                <div class="header">
                    <div class="btn-wp"  style="pointer-events:all;">
                        <p class="_css-btnimport _css-change-addnew"
                        :class="{'noClick':!isModel}" 
                        @click="editModelProgress('编辑')">编辑进度数据</p>

                        <p class="_css-btnimport _css-change-addnew" 
                            @click="editModelProgress('模拟')"
                            :class="{'noClick':!isModel}"> 进度方案模拟
                        </p>
                    </div>
                </div>
                <iframe
                    id="addOpenPlanView_little"
                    v-if="listSelectData"
                    :src="`${getBimConfig().integrated_planurl}/demo/Project.html?id=${listSelectData.ID}&bcheckcolumn=1&pagetype=readonly&time&Token=${this.$staticmethod.Get('Token')}`"
                    frameborder="0"
                ></iframe>
                <div class="img-wp" v-else>
                     
                    <img src="../../../../static/images/progress-no-data.svg" alt="">
                </div>
                
            </div>
        </main> 
        <transition name="el-zoom-in-bottom">
            <CompsFullScreenDialog
                :title="listSelectData?listSelectData.name:''"
                @closeCallBack="progressClose"
                v-if="progressPlayShow"
            >
                <div slot="btn">
                    <div class="btn-wp">
                        <div>
                            <div 
                            class="_css-white-btn _css-modelplay-cfgbtn "
                            :class="{'_css-reverse':playSetIndex == true}"
                            @click="mockConfigClick"><i class="icon-interface-set_se"></i>
                                {{getPlayCfgBtnText()}}
                            </div>
                            <div
                                v-if="playsign == 'end'"
                                class="playmodel"
                                @click="mockprogressclick"
                                :class="{'ban-click':bStartDis}"
                            >
                                <i class="icon-interface-play"></i>
                                开始模拟
                            </div>
                            <div
                                v-if="playsign == 'pause'"
                                class="playmodel"
                                @click="mockprogressclick"
                                :class="{'ban-click':false && loadingModelEnd == true}"
                            >
                                <i class="icon-interface-play"></i>
                                继续模拟
                            </div>
                            <div
                                v-if="playsign == 'start'"
                                class="pausemodel"
                                @click="pausemodelClick"
                                :class="{'ban-click':false && loadingModelEnd == true}"
                            >
                                <i class="icon-interface-time-out"></i>
                                暂停模拟
                            </div>
                            <div
                                v-if="playsign != 'end'"
                                class="stopmodel"
                                @click="stopPlayModel()"
                                :class="{'ban-click':false && loadingModelEnd == true}"
                            >
                                <i class="icon-interface-stop"></i>
                                停止模拟
                            </div>
                    
                        </div>
                    </div>
                </div>
                <section slot="main" class="full-screen-main">
                    
                    <div class="gantt">
                        <iframe
                           id="addOpenPlanView"
                            style="z-index:5;position: relative;"
                            :src="`${getBimConfig().integrated_planurl}/demo/Project.html?id=${listSelectData.ID}&bcheckcolumn=1&pagetype=readonly&Token=${this.$staticmethod.Get('Token')}`"
                            frameborder="0"
                            ref="palyGanttView"
                        ></iframe>
                    </div>
                    <CompsDragWp style="z-index:9;width:798px;display:flex;">
                        <modelPlay 
                        ref="ref_indexmodelPlay"
                        slot="main"
                        @playStart="_playStateSign('start')"
                        @playPause="_playStateSign('pause')"
                        @inputChange="_inputChange()"
                        @playEnd="_playStateSign('end')"
                        @progressPlayLoad=progressPlayLoad
                        :moreprojectID='moreprojectID'
                        :moreModelIds='moreModelIds'
                        :modelInputEdit="modelInputEdit"
                        :showplaycomps="playSetIndex"
                        :modelid=editList[0].ID
                        @noDataFun="noDataFun"
                        :planid=listSelectData.ID></modelPlay>
                    </CompsDragWp>
                    
                </section>
            </CompsFullScreenDialog>
        </transition>
    </div>
</template>

<script>

import progressList from "@/components/CompsProgress/progressList";
import CompsFullScreenDialog from "@/components/CompsDialog/CompsFullScreenDialog";
import CompsDragWp from "@/components/CompsDialog/CompsdragWp";
import modelPlay from "@/components/CompsProgress/modelPlay";

export default {
    components: { progressList, CompsFullScreenDialog, CompsDragWp, modelPlay },
    props: {},
    data() {
        return {
            headerTitleText: '',
            playsign: 'end', // 当前的模型模拟状态
            bStartDis: false, // 刚点击完开始模拟时，立即dis掉，当 playSign 修改为 start 时，立即取消dis.
            loadingModelEnd: true,

            progressPlayShow: false,
            editProgressShow: false,
            projectID: "",
            addInputData: "",
            progressListData: [],
            defaultExpandedKeys:[],
            addDialogConfig: {
                show: false,
                progreeName: ""
            },
            listSelectData: null, //进度计划列表选中数据
            listSelectNode: null,
            selectModelID: "",
            isEdit: false,
            isModel: false,
            editList: [],
            addDialogBtnName: "打开模型",
            addDialogBtnDisabled: false,
            addOpenModel: false,
            addIframePlanID: "",
            addSelectElementID: [],
            ganttSelectElementID: [],
            bop_modelphaseArr:[],
            bop_modelIdArr:[],
            playSetIndex: false,
            modelInputEdit: false,
            moreprojectID: '',
            moreModelIds: [],
            headerHide: false, // 大屏显示使用该组件，header不展示
            hasALLAuth:'', //先获取全部功能模块
            r_hasAuthEdit:'' //是否拥有编辑权限
        }
    },
    watch: {
        addOpenModel: function(val) {
            if (val) {
                this.addOpenModelViewLoad()
            }
        }
    },
    computed: {},
    methods: {
        editModelProgress(str){
            if(str === '编辑'){
                if(this.r_hasAuthEdit == '0'){
                    this.$message.warning("没有相关权限")
                    return
                }
                if(!this.isModel) return
                this.$refs.list.detailProgressUI(1)
            }else{
                if (this.isEdit) {
                    this.$refs.list.detailProgressUI(0)
                }
            }
        },
        // editGanttClick(nub) {
        //     if (nub && this.isEdit) {
        //         this.$refs.list.detailProgressUI(0)
        //     }
        // },
        closeAll(){
            this.$refs.list.closeAll();
        },
        _initTreeData(){
            this.$axios.get(`${this.$MgrBaseUrl.planGetList}?organizeId=${this.projectID}&Token=${this.$staticmethod.Get('Token')}`).then(res=>{
                this.progressListData = res.data.Data
                this.progressListData.forEach(item => {
                    item.name = item.NAME_
                    item.leaf = true
                })
                res.data.Data.forEach(item=>{
                    if(this.bop_modelphaseArr.indexOf(item.bop_modelphase) < 0){
                        this.bop_modelphaseArr.push(item.bop_modelphase)
                    }
                    if(this.bop_modelIdArr.indexOf(item.bop_modelId) < 0){
                        this.bop_modelIdArr.push(item.bop_modelId)
                    }
                })
                console.log(this.progressListData,'=progressListData')
                
            })
        },
        
        _inputChange(){
            let _this = this
            _this.modelInputEdit = false
        },
        // 
        _playStateSign(sign) {
            var _this = this;
            _this.playsign = sign;
        },
        mockprogressclick(){
            let _this = this;

            if (_this.bStartDis) {
                return;
            }
            _this.bStartDis = true;
            _this.playSetIndex  = true
            _this.modelInputEdit = true
            // 如果模型页面已经打开了，判断是否正在模拟
            
            _this.playModelControlShowAndPlay();
        },

        // 模拟设置按钮文本
        getPlayCfgBtnText(){
            var _this = this;
            if (_this.playSetIndex == true) {
                return '关闭设置';
            } else {
                return '模拟设置';
            } 
        },
        
        /* 模拟设置 */
        mockConfigClick() {
            let _this = this
            // 如果模型页面已经打开了，判断是否正在模拟
            if(_this.bStartDis){
                _this.modelInputEdit = false
            }
            _this.playSetIndex = !_this.playSetIndex
        },
        // 确保控制界面已经显示
        playModelControlShowAndPlay() {

            // 如果此时控制界面没有显示，立即显示
            var _this = this;
            if (!_this.showplaycomps) {
                _this.showplaycomps = true;
            }

            // 
            // 开始模拟测试
            _this.$refs.ref_indexmodelPlay.playMovie();
            _this.bStartDis = false;

        },
        // 暂停模拟
        pausemodelClick() {
            var _this = this;
            setTimeout(()=>{
                _this.$refs.ref_indexmodelPlay.stopMovie(true);
            }, 10);
        },
        // 停止模拟
        stopPlayModel(){

            // 如果模型模拟播放控制台打开了，关掉
            var _this = this;
            if (_this.showplaycomps) {
                _this.showplaycomps = false;
            }   
            _this.modelInputEdit = false

            // 之前没有停止操作
            setTimeout(()=>{
                _this.$refs.ref_indexmodelPlay.forceStop(false, true);
            }, 10);

        },

        setSliderDate(date) {
            var _this = this;
            if (_this.$refs.ref_indexmodelPlay) {
                _this.$refs.ref_indexmodelPlay.triggerSliderDate(date);
            }
        },
        progressPlayLoad(el){
            let _this = this
            let iframe = el
            let iframeWindow = iframe.contentWindow
            // iframeWindow.B1IMe.event.BIMeEvent.modelLoadedEvent.subscribe(()=>{
            //     //console.log(1)
            // })
            // iframeWindow.BI1Me.event.BIMeEvent.finishRender.subscribe(()=>{
            //     //console.log(2)
            // })
           
        },
        // formatTooltip(val){
        //     if(this.modelPlayConfig.startDate == null){
        //         return val
        //     }
        //     let date = new Date(this.modelPlayConfig.startDate)
        //     date.setDate(date.getDate() + val)
        //     return this.$formatData.dateInit(date)
        // },
        // getElement(listElements){
        //     let arr = []
        //     listElements.forEach(item=>{
        //         item.elementids.forEach(ids=>{
        //             arr.push(`${item.modelid}^${ids}`)
        //         })
        //     })
        //     return arr
        // },
        // playInterval(data){
        //     var _this = this;
        //     let playModelView = _this.$refs.playModelView.contentWindow
        //     let lastResetElementColor = []
        //     _this.modelPlayConfig.timer = setInterval(()=>{
        //         if( _this.modelPlayConfig.inputVal == _this.modelPlayConfig.max){
        //             clearInterval(_this.modelPlayConfig.timer)
        //             _this.modelPlayConfig.inputVal = 0
        //             _this.modelPlayConfig.timer = null
        //             _this.modelPlayConfig.playState = 0
        //             _this.modelPlayConfig.isolate=[]
        //             _this.modelPlayConfig.currentDate = _this.modelPlayConfig.startDate;

        //             //playModelView.BI1Me.control.BIMeIsolate.removeAllIsolateElement()
        //             console.error('不支持 removeAllIsolateElement');

        //             //playModelView.BIM1e.control.BIMeUtility.setElementColor(lastResetElementColor);
        //              _this.$staticmethod.bimhelper_getColorUtility(playModelView).setElementColor(lastResetElementColor);
                    
        //             _this.$message({
        //                 message: '播放结束',
        //                 type: 'success'
        //             });
        //             return false
        //         }
        //         _this.modelPlayConfig.message = []
        //         data.list.forEach(listdata => {
        //             if(listdata.isSetColor == undefined) listdata.isSetColor = false
        //             let listElements = JSON.parse(listdata.pte_elementids)
        //             let arr = _this.getElement(listElements)
        //             let flag =false
        //             //获取时间段内构件信息
        //             if(_this.$formatData.isDateBetween(_this.modelPlayConfig.currentDate,listdata.task_Start,listdata.task_Finish)){
        //                 console.log('属于时间段')
        //                 _this.modelPlayConfig.isolate.length == 0?_this.modelPlayConfig.isolate = [...arr] : _this.modelPlayConfig.isolate = _this.modelPlayConfig.isolate.concat(arr) 
        //                 flag = true
        //                 _this.modelPlayConfig.message.push(listdata.task_Name)
        //             }
                    
        //             //结束时间 颜色重置
        //             let endDate = new Date(listdata.task_Finish)
        //             let taskEndTime = endDate.getTime()
        //             let currentTime = new Date(_this.modelPlayConfig.currentDate).getTime()
                    
        //             if(taskEndTime <= currentTime && listdata.isSetColor){
        //                 // console.log(arr)

        //                 //playModelView.B1IMe.control.BIMeUtility.resetEl1ementColor(arr)
        //                 _this.$staticmethod.bimhelper_getColorUtility(playModelView).resetElementColor(arr);
                        
        //                 listdata.isSetColor = false
        //                 flag = false
        //             }
                    
        //             if(flag && !listdata.isSetColor) {

        //                 // 将这些构件着为红色
        //                 // model.BIM365API.Controller.setElementColor(['c6b4dbce-8fef-40b5-8e09-cc394b0343f5^466264'], 255, 0, 0, 1);
        //                 //playModelView.BI1Me.control.BIMeUtility.setElementColor(arr,255,0,0,1);
        //                 _this.$staticmethod.bimhelper_getColorUtility(playModelView).setElementColor(arr,255,0,0,1);

        //                 lastResetElementColor = arr


        //                 // 隔离：isolateElementByElementId
        //                 // 旧：.BI1Me.control.BIMeIsolate.isolateElementByElementId
        //                 // 新：.model.BIM365API.Controller.isolateElementByElementId
        //                 //playModelView.BI1Me.control.BIMeIsolate.isolateElementByElementId(_this.modelPlayConfig.isolate);
        //                 _this.$staticmethod.bimhelper_getIsolateUtility(playModelView).isolateElementByElementId(_this.modelPlayConfig.isolate);

        //                 listdata.isSetColor = true;
        //             }
                    

        //         })
        //         _this.modelPlayConfig.drawColor = lastResetElementColor
        //         _this.modelPlayConfig.currentDate.setDate(_this.modelPlayConfig.currentDate.getDate()+1)
        //         _this.modelPlayConfig.inputVal += 1
                
        //     },1000)
            
            
        // },
        /**
         * 播放操作
         */
        // playMovie() {
        //     let _this = this
        //     this.modelPlayConfig.isDisabled = true
        //     this.modelPlayConfig.playState = 1
        //     if(_this.modelPlayConfig.resData != null){
        //         // console.log('resData 来自data')
        //         _this.playInterval(_this.modelPlayConfig.resData)
        //         return 
        //     }
        //     this.modelPlayConfig.mask = true
        //     this.$axios.get(`${window.bim_config.webserverurl}/api/Plus/PlusTask/GetRelationElements?planId=${this.listSelectData.ID}`).then(res=>{
        //         this.modelPlayConfig.mask = false
        //         if(res.data.Ret == 1){
        //             let data = res.data.Data
        //             _this.modelPlayConfig.resData = res.data.Data
                    
        //             if(data.list.length == 0){
        //                 _this.$message.error('无关联数据')
        //                 return false
        //             }
        //             _this.modelPlayConfig.startDate = new Date(data.start)
        //             _this.modelPlayConfig.endDate = new Date(data.end)
        //             _this.modelPlayConfig.endDate.setSeconds( _this.modelPlayConfig.endDate.getSeconds()+1)
        //             _this.modelPlayConfig.max = _this.$formatData.DateDiff(_this.modelPlayConfig.startDate,_this.modelPlayConfig.endDate)
                    
        //             _this.modelPlayConfig.currentDate = new Date(data.start)
        //             _this.playInterval(data)
        //         }else{
        //             _this.$message.error('获取数据出错,请从新点击播放')
        //             this.modelPlayConfig.playState = 0
        //         }
                
        //     })
        // },
        // drawChange(inputVal) {
        //     if(this.modelPlayConfig.isDisabled) return 
        //     let _this = this
        //     _this.modelPlayConfig.message = []
        //     _this.modelPlayConfig.isolate = []
        //     let playModelView = this.$refs.playModelView.contentWindow
        //     if(_this.modelPlayConfig.drawColor.length != 0){

        //         // 新 model.BIM365API.Con1troller.resetEl1ementColor
        //         // 旧 BIMe.control.BIMeUtility.resetElem1entColor
        //         _this.$staticmethod.bimhelper_getColorUtility(playModelView).resetElementColor(_this.modelPlayConfig.drawColor);

        //         //playModelView.BI1Me.control.BIMeUtility.resetEleme1ntColor(_this.modelPlayConfig.drawColor)
        //         _this.modelPlayConfig.drawColor = []
        //     }
            
        //     this.modelPlayConfig.currentDate = new Date(this.modelPlayConfig.startDate)
        //     this.modelPlayConfig.currentDate.setDate(this.modelPlayConfig.startDate.getDate() + inputVal)
        //     this.modelPlayConfig.resData.list.forEach(listdata=>{
        //         listdata.isSetColor = false
        //         let listElements = JSON.parse(listdata.pte_elementids)
        //         let arr = _this.getElement(listElements)
        //         //获取时间段内构件信息
        //         if(_this.$formatData.isDateBetween(_this.modelPlayConfig.currentDate,listdata.task_Start,listdata.task_Finish)){
        //             _this.modelPlayConfig.drawColor.push(arr)
        //             _this.modelPlayConfig.message.push(listdata.task_Name)
        //             listdata.isSetColor = true
        //         }
        //         if(_this.$formatData.isDateBetween(listdata.task_Start,_this.modelPlayConfig.startDate,_this.modelPlayConfig.currentDate)){
        //             _this.modelPlayConfig.isolate.length == 0?_this.modelPlayConfig.isolate = arr : _this.modelPlayConfig.isolate = _this.modelPlayConfig.isolate.concat(arr) 
        //         }
        //         if(_this.$formatData.isDateBetween(listdata.task_Finish,_this.modelPlayConfig.startDate,_this.modelPlayConfig.currentDate)){
        //             _this.modelPlayConfig.isolate.length == 0?_this.modelPlayConfig.isolate = arr : _this.modelPlayConfig.isolate = _this.modelPlayConfig.isolate.concat(arr) 
        //         }

        //     })
        //     _this.modelPlayConfig.isolate = Array.from(new Set(_this.modelPlayConfig.isolate))
        //     _this.modelPlayConfig.drawColor = Array.from(new Set(_this.modelPlayConfig.drawColor.join(',').split(',')))
            
        //     //playModelView.B1IMe.control.BIMeUtility.setElementColor(_this.modelPlayConfig.drawColor,0,0,255,.5)
        //     _this.$staticmethod.bimhelper_getColorUtility(playModelView).setElementColor(_this.modelPlayConfig.drawColor,0,0,255,.5);
            
        //     //playModelView.B1IMe.control.BIMeIsolate.isolateElementByElementId(_this.modelPlayConfig.isolate);
        //     _this.$staticmethod.bimhelper_getIsolateUtility(playModelView).isolateElementByElementId(_this.modelPlayConfig.isolate);

        // },
        stopMovie() {
            this.modelPlayConfig.playState = 0
            this.modelPlayConfig.isDisabled = false
            clearInterval(this.modelPlayConfig.timer)
        },
        fullScreen() {
            var element = this.$refs.playModelWp
            if (element.requestFullscreen) {
                element.requestFullscreen()
            } else if (element.msRequestFullscreen) {
                element.msRequestFullscreen()
            } else if (element.mozRequestFullScreen) {
                element.mozRequestFullScreen()
            } else if (element.webkitRequestFullscreen) {
                element.webkitRequestFullscreen()
            }
        },
        addDialogClose() {
            this.editProgressShow = false
            this.addOpenModel = false
            this.addDialogBtnName = "打开模型"
            //   this.addDialogBtnDisabled = false
        },
        editTreeClick(obj) {
            this.addIframePlanID = obj.data.bop_planId
        },
        
        addDialogBtnClick() {
            if (this.addDialogBtnName == "打开模型") {
                this.addOpenModel = true
                //   this.addDialogBtnDisabled = true
                this.addDialogBtnName = "关联"
            } else {
                let iframe = this.$refs.addOpenPlanView.contentWindow
                iframe.postMessage({ act: "reqToGtt_getSelecteds" }, "*")
            }
        },
        submitAddProgress() {
            let _this = this
            if (this.addInputData == "") {
                this.$message({
                    message: "输入名称",
                    type: "warning"
                })
                return false
            }
            for (let i = 0; i < this.editList[0].children.length; i++) {
                if (this.editList[0].children[i].name == _this.addInputData) {
                    _this.$message({
                        message: "名称重复，重新输入",
                        type: "warning"
                    })
                    this.$refs.inputName.select()
                    return false
                }
            }

            let postData = {
                Token: this.$staticmethod.Get("Token"),
                PlanName: this.addInputData,
                OrganizeId: this.projectID,
                ModelId: this.selectModelID
            }
            this.$axios
                .post(
                    `${window.bim_config.webserverurl}/api/Plus/PlusProject/NewComm_Add`,
                    this.$qs.stringify(postData)
                )
                .then(res => {
                    res.data.Data.ID = res.data.Data.bop_planId
                    res.data.Data.leaf = true
                    res.data.Data.name = _this.addInputData
                      this.editList[0].children.push(res.data.Data)
                    _this.$refs.list.$refs.tree.append(
                        res.data.Data,
                        _this.listSelectNode.parent
                    )
                    _this.$refs.addList.$refs["tree1"].append(
                        res.data.Data,
                        _this.listSelectNode.parent.data.ID
                    )
                   
                    _this.addDialogConfig.show = false
                    //新增完默认选中
                    this.listSelectData = res.data.Data
                    this.$nextTick(() => {
                        this.$refs.addList.$refs["tree1"].setCurrentKey(
                            res.data.Data.bop_planId
                        )
                    })
                })
        },
        addDialogShow() {
            this.addDialogConfig.show = true
        },
        getBimConfig() {
            var _this = this
            return window.bim_config
        },
        treeClick(obj) {
            this.listSelectData = null;
            if (obj.node.level == 1) {
                this.isEdit = true
                this.isModel = true
                this.listSelectData = obj.data
                this.listSelectNode = obj.node
                this.editList = []
                this.selectModelID = obj.node.parent.data.ID
                this.editList.push(obj.node.parent.data)
                this.editList[0].children = []
                obj.node.parent.childNodes.forEach(item => {
                    item.data.ID = item.data.bop_planId
                    this.editList[0].children.push(item.data)
                })
                // this.$nextTick(() => {
                //     this.$refs.addList.$refs["tree1"].setCurrentKey(
                //         obj.data.bop_planId
                //     )
                //     this.addIframePlanID = obj.data.bop_planId
                // })
                // this.listSelectData = true;
                // document.getElementById('addOpenPlanView_little').contentWindow.location.reload(true);
                if(document.getElementById("addOpenPlanView_little")){
                    this.reloadGan();
                }
            }
            
        },
        removeClick(val){
          this.listSelectData = val;
        },
        reloadGan(){
            let iframe_gtt = document.getElementById("addOpenPlanView_little");         
            let gtt_src = iframe_gtt.getAttribute("src");         
            iframe_gtt.setAttribute("src", gtt_src);
        },
        demonstration() {},
        progressClose() {
            // this.modelPlayConfig.playState = 0
            // this.modelPlayConfig.inputVal = 0
            // this.modelPlayConfig.timer = null
            // this.modelPlayConfig.resData = null
            // this.modelPlayConfig.isolate = []
            // this.modelPlayConfig.messageShow = false
            this.progressPlayShow = false
        },
        loadNode(obj) {
            this.projectID = this.$staticmethod._Get("organizeId")
            if (obj.node.level== 0){
                fetch(
                    `${this.$MgrBaseUrl.planGetList}?organizeId=${this.projectID}`
                )
                .then(res => {
                    return res.json()
                })
                .then(res => {
                    res.Data.forEach((item,index) => {
                        item.name = item.NAME_
                        item.leaf = true,
                        item.indexNum = index
                    }) 
                    // this.defaultExpandedKeys = [res.Data[0].bop_planId]
                    this.listSelectData = res.Data[0]
                    if(res.Data.length > 0){
                        this.listSelectData.ID = res.Data[0].bop_planId;
                    }
                    this.progressListData = res.Data
                    obj.resolve(res.Data)
                })
            } 
            
        },
        addOpenModelViewLoad() {
            var _this = this
            this.$nextTick(() => {
                this.$refs.addOpenModelView.onload = function() {
                    var iframeWindow = _this.$refs.addOpenModelView.contentWindow;
                    _this.$staticmethod.bimhelper_finishrender(iframeWindow, () => {
                        // iframeWindow.B1IMe.view.BIMeSelection.isShowMultipleSelection(
                        //     false
                        // )
                        console.warn('isShowMultipleSelection 暂不支持');

                        _this.$staticmethod.bimhelper_onSelect(iframeWindow, elementId => {
                            _this.addSelectElementID = elementId
                        });
                    });

                    // iframeWindow.BIM1e.event.BIMeEvent.finishRender.subscribe(
                    //     () => {
                    //         // iframeWindow.B1IMe.view.BIMeSelection.isShowMultipleSelection(
                    //         //     false
                    //         // )
                    //         console.warn('isShowMultipleSelection 暂不支持');

                    //         _this.$staticmethod.bimhelper_onSelect(iframeWindow, elementId => {
                    //             _this.addSelectElementID = elementId
                    //         });
                    //     }
                    // )
                }
            })
        },
        // 打开模型，多模型展示
        getMoreModel() {
            let _this = this;
            let _url = `${window.bim_config.webserverurl}/api/Plus/PlusTask/GetMaterialEleInfoByPlanId`;
            

            let _moreParam = {
                PlanId: _this.listSelectData.ID,
                Token: this.$staticmethod.Get('Token'),
                // TaskIds: _this.gannCurrentSelectData.join(',')
            }
            _this.$axios({
                url: _url,
                method: 'post',
                data: _this.$qs.stringify(_moreParam)
            }).then(x => {
            if(x.data.Ret == 1){
                _this.moreprojectID = x.data.Data
                
                if(x.data.Data.ModelIds.length <1){
                _this.$message.error('当前构件下暂无关联模型')
                return
                } 
                _this.moreModelIds = x.data.Data.ModelIds
                _this.addOpenModel = true;

            }else{
                _this.$message.error('服务器异常，请稍后再试')
            }

            // console.log(data)

            }).catch(x => {
            
            })
        },
        // 原来的关联模型改为关联构件之后，当返回无关联数据时候，改为不打开模型
        noDataFun(){
            let _this = this;
            _this.progressPlayShow = false;
            _this.bStartDis = false; 
            
            _this.addOpenModel = false;
            _this.playsign = 'end'
        },
      

    },
    created() {},
    mounted() {
        this.headerTitleText = this.$staticmethod._Get("menuText") || '';
        this.projectID = this.$staticmethod._Get("organizeId")
        // this._initTreeData()
        if (window.performance.navigation.type == 1) {
            console.log("页面被刷新")
        }else{
            // console.log("首次被加载")
            this.$staticmethod.Set("editGanttShow", "");
            this.$staticmethod.Set("currentGanttDataInfo", "");
            this.$staticmethod.Set("gttEdit", "");
        }
        // console.log(this.$route.query.title,'路由地址')
        if(this.$route.query.title == '进度方案'){
            this.headerHide = false
        }else{
            this.headerHide = true
        }

        // 权限获取
        this.hasALLAuth = this.$staticmethod.hasSomeAuth('JDFA_Edit')
        this.hasALLAuth ? this.r_hasAuthEdit = '1' : this.r_hasAuthEdit = '0';
        console.log(this.hasALLAuth,'=hasALLAuth',this.r_hasAuthEdit)
       

        this.$emit("onmounted", "CompsProgress");
        this.projectID = this.$staticmethod._Get("organizeId")
        //debugger;
        window.addEventListener("message", msg => {
            let _this = this;
            //debugger;
            if (msg.data.act == 'resFromGtt_setSliderDate') {
                //debugger;
                _this.setSliderDate(msg.data.Date);
            }
            if (msg.data.act == "resFromGtt_getSelecteds") {
                let arr = []
                msg.data.datas.forEach(item => {
                    arr.push(item.UID)
                })
                _this.ganttSelectElementID = arr
                if (
                    _this.addSelectElementID.length > 0 &&
                    _this.ganttSelectElementID.length > 0
                ) {
                    let postData = {
                        planId: this.addIframePlanID,
                        taskIds: this.ganttSelectElementID.join(","),
                        elementIds: []
                    }
                    _this.addSelectElementID.forEach(el => {
                        let elementarr = el.split("^")
                        if (postData.elementIds.length > 0) {
                            postData.elementIds.forEach(id => {
                                if (id.modelid == elementarr[0]) {
                                    id.elementids.push(elementarr[1])
                                }
                            })
                        } else {
                            postData.elementIds.push({
                                modelid: elementarr[0],
                                elementids: [elementarr[1]]
                            })
                        }
                    })
                    postData.elementIds = JSON.stringify(postData.elementIds)
                    _this.$axios
                        .post(
                            `${window.bim_config.webserverurl}/api/Plus/PlusTask/RelationElements?Token=${_this.$staticmethod.Get('Token')}`,
                            _this.$qs.stringify(postData)
                        )
                        .then(res => {
                            _this.$nextTick(()=>{
                                if (res.data.Ret == 1 && res.data.Msg == "OK") {
                                    _this.$message({
                                        message: "关联成功",
                                        type: "success"
                                    })
                                    let iframe = _this.$refs.addOpenPlanView.contentWindow
                                    iframe.postMessage(
                                        { act: "reqToGtt_setSelecteds", datas: [] },
                                        "*"
                                    )
                                }
                            })
                            
                        })
                } else {
                    this.$message.error("请选择相应数据")
                }
            }
        })
    },
    destroyed(){
        this.$staticmethod.Set("editGanttShow", "");
        this.$staticmethod.Set("currentGanttDataInfo", "");
        this.$staticmethod.Set("gttEdit", "");
    }
    
}
</script>

<style lang="stylus" scoped rel="stylesheet/stylus">
.el-progress-wp .btn-wp{
    width: auto !important;
    background-color: transparent !important;
}
.el-progress-wp .btn-wp>div div{
  padding: 0 8px;
  height: 32px;
  display: flex;
  flex-direction: row;
  align-items: center;
  border-radius: 4px;
  border: 1px solid #1890ff;

  margin-left: 16px;

  cursor: pointer;
}

.el-progress-wp .btn-wp>div div.playmodel{
  background: #1DA48C;
  color: #fff;
  border-color: #1DA48C;
  opacity:.7;
}

.el-progress-wp .btn-wp>div div.stopmodel{
  background: #F5222D;
  color: #fff;
  border-color: #F5222D;
  opacity:.7;
}

.el-progress-wp .btn-wp>div div.pausemodel{
  background: #FAAD14;
  color: #fff;
  border-color: #FAAD14;
  opacity:.7;
}

.el-progress-wp .btn-wp>div div.playmodel:hover{
    opacity:1;
}

.el-progress-wp .btn-wp>div div.stopmodel:hover{
    opacity:1;
}

.el-progress-wp .btn-wp>div div.pausemodel:hover{
    opacity:1;
}










.el-progress-wp .btn-wp>div div.ban-click{
  background: rgba(0, 0, 0, .25);
  color:#fff;
  cursor: not-allowed;
  border: 1px solid transparent;
}
.el-progress-wp {
    .noClick {
        opacity: 0.8;
        cursor: not-allowed !important;
    }

    .wzw-wp {
        display: inline-block;
        background: red;
        width: 20px;
        height: 20px;
    }

    display: flex;
    height: 100%;
    flex-direction: column;

    header {
        height: 54px;
        line-height: 54px;
        width: 100%;
        flex-shrink: 0;
        background:#fff;
        border-bottom: 1px solid rgba(0,0,0,.15);
        text-align:left;
        span{
            font-size: 16px;
            font-weight: 500;
            margin-left: 16px;
            color: rgba(0,0,0,.85);
        }

    }

    .full-screen-main {
        display: flex;
        flex-direction: row
        position: relative;

        .left {
            width: 350px;
        }

        .model-wp {
            width: 100%
            position: relative;

            iframe {
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
            }
            .play-message{
                position absolute
                bottom 88px
                left 24px
                border-radius 4px
                width 160px
                max-height 350px
                background rgba(0,0,0,.65)
                color #fff
                padding 16px
                overflow auto
                text-align left

            }
            .video-wp {
                height: 80px;
                position: absolute;
                bottom: 0;
                left: 0;
                width: 100%;
                padding: 0 24px 22px;
                background: rgba(240, 242, 245, 1);
                box-sizing: border-box;
                display: flex;
                flex-direction: column;

                .player-btn {
                    flex: 1;
                    display: flex;
                    flex-direction: row;
                    justify-content: space-between;

                    i {
                        cursor: pointer;
                    }
                }
            }
        }

        .gantt {
            flex: 1;

            iframe {
                width: 100%;
                height: 100%;
            }
        }

        .model-relation {
            flex: 1;
        }
    }

    main {
        flex: 1;
        display: flex;
        flex-direction: row;

        .gantt-wp {
            background:#f0f2f5;
            padding-right: 56px;
            flex: 1;
            display: flex;
            flex-direction: column;
            justify-content: end;
            .img-wp{
                flex 1
                position relative
                img{
                    width: 200px;
                    position: absolute;
                    top: 30%;
                    left: 0;
                    right: 0;
                    margin: auto;
                }
            }
            .header {
                height: 64px;
                line-height: 64px;
                display: flex;
                flex-direction: row;
                justify-content: end;

                .progress-name {
                    font-size: 20px;
                    font-weight: 500;
                    color: rgba(0, 0, 0, 0.85);
                }

                .btn-wp {
                    display: flex;
                    flex-direction: row;
                    justify-content: space-between;
                    align-items: center;

                    div {
                        width: 2px;
                        height: 24px;
                        background: rgba(0, 0, 0, 0.09);
                        margin: 0 30px;
                    }

                    i {
                        cursor: pointer;
                        background-size:100%
                    }
                }
            }

            iframe {
                width: 100%;
                height: 100%;
                border: none;
                margin: 0;
                padding: 0;
            }
        }
    }
}
</style>
<style scoped >
._css-white-btn {
  color:#1890FF;
}

._css-white-btn:hover{
  background-color: rgba(24, 144, 255, 0.1);
}

._css-white-btn._css-reverse {
  background-color: rgba(24, 144, 255, 1);
  color:#fff;
  opacity: 0.7;
}

._css-white-btn._css-reverse:hover {
  opacity: 1;
}
._css-btnimport {
    font-size: 12px;
    color: #1890FF;
    border: 1px solid #1890FF;
    border-radius: 4px;
    padding: 4px 6px 4px 6px;
    margin-right: 12px;
    cursor: pointer;
    line-height: 24px;
}
._css-btnimport:hover {
    color:#fff;
    background-color: #1890FF;
}
.el-progress-wp /deep/ .el-tree-node:focus>.el-tree-node__content, 
.el-progress-wp  /deep/ .el-tree-node__content:hover, 
.progress-list  /deep/ .el-tree-node:focus>.el-tree-node__content, 
.progress-list  /deep/ .el-tree-node__content:hover{
  background: rgba(245, 245, 245, 1) !important;
}

</style>