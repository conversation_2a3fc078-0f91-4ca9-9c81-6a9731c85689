<template>
  <section class="_css-doc-all css-h100" @click="hideallbtns()">
    <section class="_css-doc-top css-w100 css-bsb css-bst css-usn">
      <!-- 面包屑 -->
      <el-row class="css-crumbs-content">
        <!-- 面包屑左侧图标 -->
        <div class="css-bsb css-b css-icon18 css-ml24 icon-interface-database css-fs16"></div>
        <!-- //面包屑左侧图标 -->
        <el-breadcrumb separator="/" class="css-ml4">
          <el-breadcrumb-item>
            <a>项目列表</a>
          </el-breadcrumb-item>
          <el-breadcrumb-item>项目设置</el-breadcrumb-item>
        </el-breadcrumb>
      </el-row>
      <!-- //面包屑 -->
    </section>
  </section>
</template>
<script>
export default {
  name: "Main",
  data() {
    return {
      extdata: {
      }
    };
  },
  mounted() {
  },
  computed: {
  },
  methods: {
  }
};
</script>
<style scoped>
</style>
