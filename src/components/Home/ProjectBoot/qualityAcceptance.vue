<template>
	<div class="_css-materials-all" ref="cssMaterialsAll">
		<div class="btn-initiating-header">{{ headerTitleText }}</div>
		<div class="_css-bottomarea">
			<div class="left-content" ref="lefttree" v-if="proTreeIn"> 
				<div class="_css-materialtypelist-head">
					<span class="_css-materhead-text">数据结构</span>
				</div>
				<el-tree
					:data="treeData"
					node-key="Id"
					ref="tree"
					class="_css-customstyle"
					:highlight-current="true"
					:auto-expand-parent="true"
					default-expand-all
					:props="defaultProps"
					:expand-on-click-node="false" 
					:default-checked-keys="defaultCheckedKeys"
					@current-change="treefunc_current_change"
				>
					<span class="css-fc _css-treenode-content" slot-scope="{ node, data }">
                          <i
                            class="css-mr4 css-fs16 css-fs18 css-fc css-jcsa " :class="data.ChildrenCount > 0  ? 'icon-new-tree-last' : 'icon-new-tree'" ></i>
                          	<span :title="node.label" class="css-ml4 _css-treenodelabel">
							<el-tooltip popper-class="css-no-triangle" v-if="data.DivisionName.length > 12" effect="dark" :content="data.DivisionName" placement="top">
								<div class="_css-treenodellabelname overflow-point">
										{{ data.DivisionName }}
								</div>
							</el-tooltip>
                            <div class="_css-treenodellabelname" v-else>{{data.DivisionName}}</div>
                          </span> 
                    </span>
				</el-tree>
			</div>
			<div class="_css-choose-switch-parent">
				<div class="_css-choose-switch" @click.stop="chooseCloseOrOpen">
					<i
						class="pro-in-out"
						:class="tableClose ? 'pro-in-out' : 'p-out'"
					></i>
				</div>
			</div>
			<div class="content-right" :class="proTreeIn ? '' : 'width100'">
				<div class="_css-materialtypetab-head">
					<span class="_css-click-close-or-open">
						<i
							:class="
								tableClose
									? 'icon-interface-toleft'
									: 'icon-interface-toright'
							"
						></i>
					</span>
					<div>{{ selectTreeData.DivisionLevel  | filterTypeToText}}</div>
					<div class="right-input">
						<el-input
						v-model="inputVal"
						placeholder="请输入分类编码或分类名称"
						@input="searchName"
						></el-input>
						<i class="icon-interface-search"></i>
					</div>
				</div>
				<div class="_css-materialtypetab-body" >
					<el-table
						v-loading="elTableLoading"
						element-loading-text="数据加载中..."
						element-loading-spinner="el-icon-loading"
						element-loading-background="rgba(0, 0, 0, 0)"
						ref="multipleTable"
						@current-change="handleCurrentChange"
						highlight-current-row
						:border="true"
						:stripe="false"
						use-virtual
						height="70%"
						:data="tableData"
						style="width: 100%"
						:default-sort="{ prop: 'date', order: 'descending' }"
						class="_css-table-ele css-scroll _css-customstyle _css-qualitystyle"
						:row-class-name="tableRowClassName" 
						:header-cell-style="{ 'background-color': 'transparent'}"
						:row-height="rowHeight"
					> 
						<el-table-column type="index"  class-name="center-text" align="center" label="序号" width="50"></el-table-column> 
						<el-table-column property="DivisionCode" label="分类编码" width="120"></el-table-column>
						<el-table-column property="DivisionName" label="分类名称" min-width="200">
							<template slot-scope="scope">
								<el-tooltip class="item" v-if="scope.row.DivisionName.length > 12" popper-class="notice-tooltip-width"  effect="dark" :content="scope.row.DivisionName" placement="top-start">
									<div class="overflow-point">{{ scope.row.DivisionName }}</div>
								</el-tooltip>
								<div v-else>{{ scope.row.DivisionName }}</div>
							</template>
						</el-table-column>
						<el-table-column property="DivisionLevelText" label="分类级别" width="130"></el-table-column>
						<el-table-column property="DivisionImportanceText" label="重要性" width="130">
						</el-table-column>
						<el-table-column
							:resizable="false"
							class="_css-col-relmodel"
							label="验评开始时间"
							prop="EvaluationStart"
							width="120"
						>
							<template slot-scope="scope">
								<div class="table-text" v-if="scope.row.EvaluationStart">
									{{ scope.row.EvaluationStart | flttimeshorter }}
								</div>
								<div class="table-text" v-else>-</div>
							</template>
						</el-table-column>
						<el-table-column
							:resizable="false"
							class="_css-col-relmodel"
							label="验评结束时间"
							prop="EvaluationEnd"
							width="120"
						>
							<template slot-scope="scope">
								<div class="table-text" v-if="scope.row.EvaluationEnd">
									{{ scope.row.EvaluationEnd | flttimeshorter }}
								</div>
								<div class="table-text" v-else>-</div>
							</template>
						</el-table-column>
						<el-table-column
							:resizable="false"
							class="_css-col-relmodel"
							label="验评结果"
							prop="EvaluationResult"
							width="90"
						>
							<template slot-scope="scope">
								<div class="table-text">
									{{ scope.row.EvaluationResult | filterEvaluationResult }}
								</div>
							</template>
						</el-table-column>
						<el-table-column
							:resizable="false"
							class="_css-col-relmodel"
							label="验评状态"
							prop="AuditStatus"
							width="90"
						>
							<template slot-scope="scope">
								<div
									class="table-text"
									v-if="
										scope.row.AuditStatus != 2 && scope.row.AuditStatus != 3
									"
								>
									{{ scope.row.AuditStatus | filterStatus }}
								</div>
								<div
									class="table-text css-cp"
								  :class="getClassObject(scope.row.AuditStatus)"
									v-if="
										scope.row.AuditStatus == 2 || scope.row.AuditStatus == 3
									"
									@click="
										clickviewAuditDescription(
											scope.row.AuditStatus,
											scope.row.AuditDescription
										)
									"
								>
									{{ scope.row.AuditStatus | filterStatus }}
								</div>
							</template>
						</el-table-column>
						<el-table-column
							:resizable="false"
							class="_css-col-relmodel"
							label="验评详情"
							prop="bhas_relexam"
							width="90"
						>
							<template slot-scope="scope">
								<div
									class="css-cp _css-btnimport"
									@click="handelClickOpenDocument(scope.row.Id, 'detail')"
									v-if="
										scope.row.EvaluationResult && scope.row.EvaluationResult > 0
									"
								>
									查看
								</div>
							</template>
						</el-table-column>
						<el-table-column
							:resizable="true"
							class="_css-col-relmodel _css-celllongcolumn"
							label="操作"
							prop="bhas_relexam"
							width="130"
						> 
							<template slot-scope="scope">
								<!-- 施工方
								提交可点击    AuditStatus=0 待提交 
								编辑可点击    AuditStatus=0 待提交 =3 驳回
									被驳回后只能是在次点击编辑在去提交
								审批  AuditStatus=1   待审核
								驳回  AuditStatus=1   待审核 -->

								<div
									v-if="has_ZLYS_Submit"
									class="_css-costitem _css-btnsctn"
								>
									<div
										@click="submitAudit(scope.row, 0)"
										class="_css-btnimport _css-innerbtn"
										:class="scope.row.AuditStatus == 0 ? '' : 'not-allow'"
									>
										<div>提交</div>
									</div>
									<div
										@click="submitFillIn(scope.row)"
										class="_css-btnimport _css-innerbtn"
										:class="
											scope.row.AuditStatus == 0 || scope.row.AuditStatus == 3
												? ''
												: 'not-allow'
										"
									>
										<div>编辑</div>
									</div>
								</div>
								<div
									v-if="!has_ZLYS_Submit && has_ZLYS_Audit"
									class="_css-costitem _css-btnsctn"
								>
									<div
										@click="TurnDownClick(scope.row, 1)"
										:class="scope.row.AuditStatus == 1 ? '' : 'not-allow'"
										class="_css-btnimport _css-innerbtn"
									>
										<div>审批</div>
									</div>
									<div
										@click="TurnDownClick(scope.row, 2)"
										:class="scope.row.AuditStatus == 1 ? '' : 'not-allow'"
										class="_css-btnimport _css-innerbtn"
									>
										<div>驳回</div>
									</div>
								</div>
							</template>
						</el-table-column>
					</el-table>
					<el-pagination
						@size-change="handleSizeChange"
						@current-change="handleCurrentPaginationChange"
						:current-page="pageNum"
						:page-sizes="[20, 50, 100, 200]"
						:page-size="pageSize"
						layout="total, sizes, prev, pager, next, jumper"
						:total="paginationPageLength"
					>
					</el-pagination>
				</div>
			</div> 
		</div> 
		<zdialog-function
			:init_title="'填报单元验收'"
			:init_zindex="1003"
			:init_innerWidth="450"
			:init_width="450"
			init_closebtniconfontclass="icon-suggested-close"
			@onclose="status_showedit = false"
			v-if="status_showedit"
		>
			<div
				slot="mainslot"
				class="_css-addingnameinput-ctn"
				@mousedown="_stopPropagation($event)"
			>
				<div class="_css-line _css-line-name">
					<div class="_css-title _css-title-flowname">单元名称：</div>
					<div class="_css-fieldvalue _css-fieldvaluename _css-selectednode">
						{{ m_edit_name }}
					</div>
				</div>
				<div class="_css-line _css-line-name">
					<div class="_css-title _css-title-flowname">验评开始日期：</div>
					<div class="_css-fieldvalue _css-fieldvaluename">
						<el-date-picker
							v-model="m_editStartT"
							type="date"
							placeholder="验评开始日期"
						>
						</el-date-picker>
					</div>
				</div>
				<div class="_css-line _css-line-name">
					<div class="_css-title _css-title-flowname">验评结束日期：</div>
					<div class="_css-fieldvalue _css-fieldvaluename">
						<el-date-picker
							v-model="m_editEndT"
							type="date"
							placeholder="验评结束日期"
						>
						</el-date-picker>
					</div>
				</div>
				<div class="_css-line _css-line-name">
					<div class="_css-title _css-title-flowname">质量验评详情：</div>
					<div class="_css-fieldvalue _css-fieldvaluename">
						<div
							class="shelter-input"
							v-if="qualityFileAttachments.length > 0"
						>
							已上传文件（点击可重新上传）
						</div>
						<input
							:class="qualityFileAttachments.length > 0 ? 'opacity0' : ''"
							id="id_files"
							class="_css-upload-file-inputfile"
							type="file"
							accept=""
							@change="getFileChange()"
						/>
					</div>
				</div>
				<div
					class="_css-line _css-line-name"
					v-if="qualityFileAttachments.length > 0"
				>
					<div class="_css-title _css-title-flowname"></div>
					<div class="_css-fieldvalue file-attach">
						<div
							class="css-icon20"
							:class="
								$staticmethod.getIconClassByExtname(
									qualityFileAttachments[0].AttachmentName,
									qualityFileAttachments[0].AttachmentSize
								)
							"
						></div>
						{{ qualityFileAttachments[0].AttachmentName }}
					</div>
				</div>
				<div class="_css-line _css-line-name">
					<div class="_css-title _css-title-flowname">单元验评结果：</div>
					<div class="_css-fieldvalue _css-fieldvaluename">
						<el-select v-model="qualityvalue" placeholder="请选择验评结果">
							<el-option
								v-for="item in qualityoptions"
								:key="item.value"
								:label="item.label"
								:value="item.value"
							>
							</el-option>
						</el-select>
					</div>
				</div>
			</div>

			<div slot="buttonslot" class="_css-flowAddBtnCtn">
				<zbutton-function
					:init_text="'保存'"
					:init_fontsize="14"
					:debugmode="true"
					:init_height="'40px'"
					:init_width="'120px'"
					@onclick="func_saveedit"
				>
				</zbutton-function>
				<zbutton-function
					:init_text="'取消'"
					:init_color="'rgba(24, 144, 255)'"
					:init_bgcolor="'#fff'"
					:init_fontsize="14"
					:debugmode="true"
					:init_height="'40px'"
					:init_width="'120px'"
					@onclick="func_canceledit"
				>
				</zbutton-function>
			</div>
		</zdialog-function>
		<zdialog-function
				:init_title="viewAuditType == 0 ? '进度任务审核' : '进度任务驳回'"
				:init_zindex="1003"
				:init_innerWidth="450"
				:init_width="450"
				:init_height="500"
				init_closebtniconfontclass="icon-suggested-close"
				:init_usecustomtitlearea="false"
				@onclose="setTurnDownShow = false"
				v-if="setTurnDownShow"
			>
			
			<div slot="mainslot" class="_css-addingnameinput-ctn _css-turn-down"
			@mousedown="_stopPropagation($event)"
			>
				<el-input
					@mousedown="_stopPropagation($event)"
					type="textarea"
					:rows="2"
					:placeholder="viewAuditType == 0 ? '请输入审批意见' : '请输入驳回原因'"
					v-model="turnDowntextarea">
				</el-input>
			</div>
	
			<div slot="buttonslot" class="_css-flowAddBtnCtn" >
				<zbutton-function
					:init_text="viewAuditType == 0 ? '审批' : '驳回'"
					:init_fontsize="14"
					:debugmode="true"
					:init_height="'40px'"
					:init_width="'120px'"
					@onclick="turnDown"
					>
				</zbutton-function>
				<zbutton-function
					:init_text="'取消'"
					:init_color="'rgba(24, 144, 255)'"
					:init_bgcolor="'#fff'"
					:init_fontsize="14"
					:debugmode="true"
					:init_height="'40px'"
					:init_width="'120px'"
					@onclick="setTurnDownShow=false;turnDowntextarea=''"
					>
				</zbutton-function>
			</div>
			
			</zdialog-function>
			<zdialog-function
				:init_title="viewAuditType == 0 ? '审批意见' : '驳回原因'"
				:init_zindex="1003"
				:init_innerWidth="450"
				:init_width="450"
				:init_height="500"
				init_closebtniconfontclass="icon-suggested-close"
				:init_usecustomtitlearea="false"
				@onclose="viewAuditDescriptionShow = false"
				v-if="viewAuditDescriptionShow"
				>
				
				<div slot="mainslot" class="_css-addingnameinput-ctn _css-turn-down css-tal" :class="viewAuditType == 1 ? 'colorf00' :''"
				@mousedown="_stopPropagation($event)"
				>
				{{ viewAuditDescriptionText}}
				</div>
			</zdialog-function>
	</div>
</template>
<script>
export default {
	components: {
	},
	name: "qualityDivision",
	data() {
		return {
			projectName: '',
			headerTitleText: "", // header显示文字
			organizeId: "",
			tableClose:false,
			proTreeIn: true,
			treeData: [],
			defaultCheckedKeys: [], // 默认选中的节点
			defaultProps: {
				children: "Children",
				label: "DivisionName",
			},
			elTableLoading:false,
			tableData: [],
			pageNum: 1, // 第几页
			pageSize: 20, // 每页多少条
			paginationPageLength: 0, // 总条数
			rowHeight: 40, 
			
			has_ZLYS_Submit: false,
			has_ZLYS_Audit: false,
			selectTreeData: {},
			searchTableName: '',
			inputVal: '', 

			status_showedit:false,
			m_edit_name: '',
			m_editStartT: '',
			m_editEndT: '',
			qualityFileAttachments: [],
			Attachments: [],
			qualityvalue: "",
			qualityoptions: [
				// 1 合格 2 优良 3 不合格
				{ label: "合格", value: 1 },
				{ label: "优良", value: 2 },
				{ label: "不合格", value: 3 },
			],

			viewAuditType: 0,  // 审批 0 驳回 1 
			setTurnDownShow: false, // 驳回弹窗
			turnDowntextarea: '', // 驳回原因
			viewAuditDescriptionShow: false,
			viewAuditDescriptionText: '', // 驳回原因
			ValuationId: "", // 填报的ID
			tableWidth: 100 // 初始宽度


		};
	},
	props: {},
	watch: {},
	created() {
		this.$Bus.$on("UpdateAuth", this.updateAuth);
		this.organizeId = this.$staticmethod._Get("organizeId");
		this.headerTitleText = this.$staticmethod._Get("menuText") || "";
		this.updateAuth()
		this.getProjectInformation()
	},
	mounted() {
        this.handleRoueChange()
	},
	filters: {
		flttimeshorter(inputtime) {
			return inputtime.substr(0, 10) || '-';
		}, 
		filterTypeToText(number){
			let text = ''
			switch(number) { 
				case 0: 
					text = '合同工程'; 
					break; 
				case 1: 
					text = '单位工程'; 
					break; 
				case 2: 
					text = '子单位工程'; 
					break; 
			  	case 3: 
					text = '分部工程'; 
					break; 
				case 4: 
					text = '子分部工程'; 
					break; 
				case 5: 
					text = '分项工程'; 
					break; 
				case 6: 
					text = '单元工程'; 
					break; 
				case 7: 
					text = '检验批'; 
					break; 
			}
			return text
		},
		filterEvaluationResult(val) {
			if (val == 1) {
				return "合格";
			} else if (val == 2) {
				return "优良";
			} else if (val == 3) {
				return "不合格";
			} else {
				return "-";
			}
		},
		filterStatus(number) {
			// 0 待提交 1 审核中 2 审核通过 3 已驳回、审核不通过
			let status = "";
			switch (number) {
				case 0:
					status = "待提交";
					break;
				case 1:
					status = "待审核";
					break;
				case 2:
					status = "已审核";
					break;
				case 3:
					status = "已驳回";
					break;
				default:
					status = "审核不通过";
			}
			return status;
		},
	},
	computed: {
	},
	methods: {
		// 点击消息进入本页面的处理逻辑
        handleRoueChange() {
            const urlChangedBy = this.$staticmethod._Get("UrlChangedBy")
            if(urlChangedBy) {
                const segments = urlChangedBy.split("-")
                if(segments && segments.length) {
                    const flag = segments[0]
                    if(flag && flag === 'Msg') {
                        console.log('UnFoldMenuAndSelect-ZLYS')

                        this.$Bus.$emit('UnFoldMenuAndSelect','ZLYS') // ProjectBoot.vue中注册该事件:更新左侧菜单展开节点并选择菜单
                        const msgRelatedData = sessionStorage.getItem("MsgRelatedData") // 消息关联的数据
                        const refProgressPlan = this.$refs.tree
						if(refProgressPlan && msgRelatedData) {
							setTimeout(() => {
								this.GetDivisionInfo(msgRelatedData,'msg')
							}, 500);
						}	
        
                        this.$staticmethod._Set("MsgRelatedData","")
                        this.$staticmethod._Set("UrlChangedBy","")  // 处理完成重置
						

    					// this.headerTitleText = '质量验收'        
						setTimeout(() => {this.headerTitleText = this.$staticmethod._Get("menuText") || '';},300);

                    }
                }
            }
        },
		// 更新权限
        updateAuth() {
          try {
            // 权限：提交=提交+编辑、审核
            this.has_ZLYS_Submit = this.$staticmethod.hasSomeAuth("ZLYS_Submit");  
			this.has_ZLYS_Audit = this.$staticmethod.hasSomeAuth("ZLYS_Audit"); 
          } catch(e) {
          }
        },
		// 获取详情
        async getProjectInformation(){
            let params = {
                projectid: this.organizeId,
            }
            const res = await this.$api.GetProject(params);
            this.projectName = res.Data.ProjectName;
			this.getTreeData(1);
        },
		_stopPropagation(ev) {
			ev && ev.stopPropagation && ev.stopPropagation();
		},
		treefunc_current_change(data){
			this.inputVal = this.searchTableName = ''
			this.selectTreeData = data
			this.GetDivisionPaged()
		},
		// 获取树结构
		async getTreeData(type) {
			const checkedKeys = this.$refs.tree.getCurrentKey();
			// console.log(checkedKeys,'=====checkedKeys')
			let data = {
				organizeId: this.organizeId,
			};
			const res = await this.$api.GetDivisionTree(data);
			this.treeData = [{
				Id: '0',
				DivisionName: this.projectName,
				DivisionCode: '0',
				ChildrenCount: 1,
				DivisionLevel: -1,
				Children: res.Data
			}]
			if(res.Ret == 1 && res.Data){
				if(type == 1){
					this.selectTreeData = res.Data[0];
					this.GetDivisionPaged()
				}
				this.$nextTick(() => {
					this.$refs.tree.setCurrentKey(checkedKeys);
				});
			}
		}, 
		// 获取某个详情
		async GetDivisionInfo(id,type) {
			if(id == 0) return
			let data = {
				id: id
			};
			let doc = {};
			const res = await this.$api.GetDivisionInfo(data);
			if(res.Ret == 1){
				let _data = res.Data;
				if(type == 1){
					this.tableData = [res.Data];
					this.paginationPageLength = 1
				}else if(type=='detail'){
					if (!_data.Attachments || _data.Attachments.length == 0) {
						this.$message.warning("当前工程没有上传文件");
					}
					doc = {
						FileId: _data.Attachments[0].Id,
						FileName: _data.Attachments[0].AttachmentName,
						FileExtension: _data.Attachments[0].AttachmentExtension,
					};
					var url = `${
						window.bim_config.webserverurl
					}/api/v1/attach/preview?id=${
						doc.FileId
					}&Token=${this.$staticmethod.Get("Token")}`;
					// 根据扩展名获取在线浏览地址
					var url_iframe_all;
					if (doc.FileName.toLowerCase().indexOf(".dwg") > 0) {
						
						this.$emit("set_projectboot_extdata", "_docviewtype", "dwg");

						url_iframe_all = `${
							this.$configjson.dwgurl
						}/Home/Index2?dwgurlcfg=${encodeURIComponent(url)}&name=${
							doc.FileName
						}`;
					} else { 
						this.$emit(
							"set_projectboot_extdata",
							"_docviewtype",
							"office"
						); 
						url_iframe_all = this.$staticmethod.getHuangNewcomputeViewUrl(
							url,
							doc.FileName,
							doc.FileExtension
						);
					} 
					this.$emit("set_projectboot_extdata", "_show_idocview", true);
					this.$emit(
						"set_projectboot_extdata",
						"_idocviewurl",
						url_iframe_all
					);
				}else if (type == 'submit'){
					this.m_edit_name = _data.DivisionName;
					this.m_editStartT = _data.EvaluationStart;
					this.m_editEndT = _data.EvaluationEnd;
					this.qualityvalue = _data.EvaluationResult;
					this.qualityFileAttachments = _data.Attachments || [];
					this.status_showedit = true;
				}else if (type == 'msg'){
					this.selectTreeData = res.Data
					this.GetDivisionPaged()
				}
			}
		},
		// 获取表格
		async GetDivisionPaged() {
			let data = {
				OrganizeId: this.organizeId,
				ParentId: this.selectTreeData.Id,
				PageNum: this.pageNum,
				PageSize: this.pageSize,
				KeyWord: this.searchTableName,
			};
			const res = await this.$api.GetDivisionPaged(data);
			if(res.Ret == 1){
				// 如果当前表格返回没有子集，表格显示当前数据
				if(res.Data.Total > 0){
					this.tableData = res.Data.Data;
					this.paginationPageLength = res.Data.Total
				}else{
					this.GetDivisionInfo(this.selectTreeData.Id,1)
				}
			}
		},
		tableRowClassName({ row, rowIndex }) {
			return "css-tdunder-test1";
		},
		handleCurrentChange(currentRow) {
		},  
		chooseCloseOrOpen(){
			this.proTreeIn = !this.proTreeIn;
		},
		handleSizeChange(val) {
			this.pageSize = val;
			this.GetDivisionPaged();
		},
		handleCurrentPaginationChange(val) {
			this.pageNum = val;
			this.GetDivisionPaged();
		}, 
		searchName(val) {
			this.searchTableName = val;
			this.GetDivisionPaged();
		},
		func_saveedit() {
			// 先调用上传附件的接口，在调用上传接口
			let filedom = document.getElementById("id_files");
			let files = filedom.files;
			if (this.m_editStartT == "" || !this.m_editStartT ) {
				this.$message.error("请选择验评开始日期");
				return;
			}
			if (this.m_editEndT == "" || !this.m_editEndT ) {
				this.$message.error("请选择验评结束日期");
				return;
			}
			if (this.qualityvalue == "" || !this.qualityvalue) {
				this.$message.error("请选择验评结果");
				return;
			}
			if (files.length == 0) {
				this.handleSuccess();
			} else {
				for (var i = 0; i < files.length; i++) {
					this.handleUploadFlie(files[i]);
				}
			} 
		},
		// 上传文件
		handleUploadFlie(file) {
			let _this = this;
			let ProjectID = _this.$staticmethod._Get("organizeId");
			let File = file;
			let userid = _this.$staticmethod.Get("UserId");
			// 提交给服务器
			let fd = new FormData();
			let FileKey = this.$md5(new Date().getTime() + "" + File.name);
			fd.append("ProjectID", ProjectID);
			fd.append("BusinessId", this.ValuationId);
			fd.append("AttachmentType", "3");
			fd.append("FileName", File.name);
			fd.append("FileKey", FileKey);
			fd.append("FileSize", File.size);
			fd.append("ChunkNumber", 1);
			fd.append("Index", 1);
			fd.append("UserId", userid);
			fd.append("File", File);
			let config = {
				headers: {
					"Content-Type": "multipart/form-data",
				},
			};
			_this.$axios
				.post(
					`${
						window.bim_config.webserverurl
					}/api/v1/attach/upload?Token=${this.$staticmethod.Get("Token")}`,
					fd,
					config
				)
				.then((x) => {
					if (x.data.Ret == 1 && x.data) {
						let _data = x.data.Data.Data;
						_this.Attachments.push(_data.AttachmentId);
						_this.handleSuccess();
					}
				})
				.catch((x) => {
					_this.$message.error(
						`服务器处理发生错误，可能是文件类型不符合白名单规则，请检查文件`
					);
				});
		},
		getFileChange() {
			let file = document.getElementById("id_files");
			let fileName = file.files[0];
			if (fileName) {
				this.qualityFileAttachments = [
					{
						AttachmentName: fileName.name,
						AttachmentSize: fileName.size,
					},
				];
			} else {
				this.qualityFileAttachments = [];
			}
		},
		async handleSuccess() {
			this.m_editStartT = new Date(this.m_editStartT);
			this.m_editEndT = new Date(this.m_editEndT);

			let m_editStartT = this.$formatData
				.formatDateCheck(this.m_editStartT)
				.substr(0, 10);
			let m_editEndT = this.$formatData
				.formatDateCheck(this.m_editEndT)
				.substr(0, 10);
			 
			let data = {
				Id: this.ValuationId,
				EvaluationStart: m_editStartT,
				EvaluationEnd: m_editEndT,
				Attachments: this.Attachments,
				ValuationResult: this.qualityvalue,
			}
			const res = await this.$api.postDivisionEvaluation(data);
			if(res.Ret == 1){
				this.func_canceledit();
				this.GetDivisionPaged();
				this.$message.success("操作完成");
			}
		 
		},
		func_canceledit() {
			this.status_showedit = false;
		},
		turnDown() {
			if (this.turnDowntextarea.length == 0) {
				this.$message.warning("请输入驳回原因");
				return;
			}
			this.submitAudit(this.turnDownType.row, this.turnDownType.type);
		},
		// 提交审批驳回操作
		async submitAudit(row, type) {
			// 0 提交审批 1 审批通过 2 审批拒绝
			let params;
			if (type == 0) {
				params = {
					Id: row.Id,
					AuditType: type,
				};
			} else {
				params = { 
					Id: row.Id,
					AuditType: type,
					AuditDescription: this.turnDowntextarea,
				};
			}
			const res = await this.$api.postDivisionAudit(params);
			if(res.Ret == 1){
				this.$message.success(res.Msg);
				this.GetDivisionPaged();
				this.$message.success("操作完成");
			}
			if (type != 0) {
				this.setTurnDownShow = false;
				this.turnDowntextarea = "";
			} 
		},
		getClassObject(type) {
			return {
				color0f0: type === 2,
				colorf00: type === 3
			};
		},
		clickviewAuditDescription(val, viewAuditDescription) {
			val == 2 ? (this.viewAuditType = 0) : (this.viewAuditType = 1);
			this.viewAuditDescriptionText = viewAuditDescription;
			this.viewAuditDescriptionShow = true;
		},
		TurnDownClick(row, type) {
			type == 1 ? (this.viewAuditType = 0) : (this.viewAuditType = 1);
			this.turnDownType = {
				row: row,
				type: type,
			};
			if (type == 1) {
				this.turnDowntextarea = "同意";
			} else {
				this.turnDowntextarea = "";
			}
			this.setTurnDownShow = true;
		},
		// 文件预览
		handelClickOpenDocument(id, type) {
			this.GetDivisionInfo(id,type)
		},
		submitFillIn(row) {
			this.ValuationId = row.Id;
			this.m_edit_name = row.DivisionName;
			if (row.EvaluationResult && row.EvaluationResult > 0) {
				this.handelClickOpenDocument(row.Id, "submit");
			} else {
				this.status_showedit = true;
				this.m_editStartT = "";
				this.m_editEndT = "";
				this.qualityvalue = "";
				this.qualityFileAttachments = [];
			}
		},
	},
	beforeDestroy() {
      this.$Bus.$off('UpdateAuth',this.updateAuth)
	  this.$staticmethod._Set("MsgRelatedData","")
      this.$staticmethod._Set("UrlChangedBy","") 
    },
};
</script>
<style lang="scss" scoped>
@import url("../../../assets/css/MaterialsMgrStyle.css");

.pro-in-out {
	display: inline-block;
	width: 16px;
	height: 120px;
	background-image: url(../../../assets/images/p-in.png);
	background-size: 100%;
	background-repeat: no-repeat;
}
.pro-in-out.p-out {
	background-image: url(../../../assets/images/p-out.png);
	background-repeat: no-repeat;
	background-size: 100%;
	left: 0;
}
._css-bottomarea{
	position: relative;
	display: flex;
	.left-content{
		background: #fff;
		min-width: 270px;
		max-width: 370px;
	}
	.content-right{
		margin: 0 12px;
		// padding: 0 12px;
		flex: 1;
		width: calc(100% - 364px);
	}
	.content-right.width100{
		width: calc(100% - 64px);
	}
	._css-materialtypetab-body{
		position: relative;
		height: calc(100% - 64px);
	}
}
.first-dialog{
	position: absolute;
	background-color: #fff;
	width: 120px;
	box-shadow: 0 0 8px 0 rgba(0, 0, 0, .15);
    border-radius: 4px;
	z-index: 5;
}
._css-line {
	padding: 0 24px;
	box-sizing: border-box;
	margin: 8px 0 0 0;
	display: flex;
	align-items: center;
}
._css-fieldvaluename {
	flex: 1;
	height: 36px;
	display: flex;
	align-items: center;
	border: 1px solid rgba(0, 0, 0, 0.1);
	border-radius: 4px;
	box-sizing: border-box;
}
._css-fieldvaluename /deep/ .el-input__inner {
	line-height: 34px;
	height: 34px;
}
._css-fieldvaluename /deep/ .el-date-editor.el-input,
._css-fieldvaluename /deep/ .el-date-editor.el-input__inner {
	width: 100%;
}
._css-title-flowname {
	width: 25%;
	text-align: left;
}
._css-flowAddBtnCtn {
	display: flex;
	flex-direction: row-reverse;
	height: 64px;
	align-items: center;
	box-sizing: border-box;
	padding-right: 8px;
}
._css-materialtypetab-body /deep/ ._css-qualitystyle.el-table th {
	font-size: 14px !important;
}
.overflow-point{
	width: 200px;
}
._css-customstyle{
	height: 90% !important;
	overflow: auto;
}
.right-input{
	width:230px;
	border: 1px solid #D8D8D8;
	position: absolute;
	top: 16px;
    right: 8px;
}
.right-input i {
    position: absolute;
    top: 6px;
    right: 8px;
    color: #999999;
}
.right-input /deep/ .el-input__inner{
    line-height: 32px;
}
._css-materialtypetab-body /deep/ ._css-qualitystyle.el-table thead th .cell , ._css-materialtypetab-body /deep/ ._css-qualitystyle .center-text .cell{
	justify-content: center !important;
	padding: 0;
}
._css-btnsctn {
    display: flex;
    align-items: center;
    width:100%;
    justify-content: space-around;
	margin: 6px 0;
	background: #fff;
}
._css-btnimport {
	font-size: 12px;
	color: #1890ff;
	border: 1px solid #1890ff;
	border-radius: 4px;
	padding: 4px 6px 4px 6px;
	margin-right: 12px;
	cursor: pointer;
}
._css-btnimport:hover {
	color: #fff;
	background-color: #1890ff;
}
.table-text {
	padding-left: 12px;
	font-size: 14px;
	color: rgba(0, 0, 0, 0.85);
}
.file-attach {
	display: flex;
	align-content: center;
}
.shelter-input {
	position: absolute;
}
.opacity0 {
	opacity: 0;
}
.colorf00{
    color: #f00;
}
.color0f0{
	color: #0f0;
}
._css-turn-down{
    padding: 20px;
}
.not-allow {
	cursor: not-allowed;
	pointer-events: none;
	color: #606266;
	border: 1px solid #606266;
}
</style>
