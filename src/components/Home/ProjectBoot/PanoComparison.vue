<template>
  <div class="pano-comparsion">
    <div class="btn-initiating-header">{{ headerTitle }}</div>
    <div class="com-content">
      <div class="_css-top-search">
        <div class="_css-select">
          <i class="icon-interface-filter"></i>
          <el-select
            v-model="selectValue"
            filterable
            clearable
            placeholder="输入标签筛选"
            @change="selectChangeFun"
          >
            <el-option
              v-for="item in selectOption"
              :key="item.LabelId"
              :label="item.LabelName"
              :value="item.LabelId"
            >
            </el-option>
          </el-select>
        </div>
        <div class="_css-select search-input">
          <i class="icon-interface-search"></i>
          <el-input
            v-model="inputVal"
            placeholder="请输入全景图名称"
            @input="searchName"
          ></el-input>
        </div>
      </div>
      <div class="_css-pano-body">
        <div
          v-for="(item, index) in panoDataList"
          :key="index"
          class="_css-pano-list"
        >
          <div class="list-content">
            <div class="title" :style="computeTitleBg(item.BackGroundColor || 'rgba(230, 126, 126, 1)')">
              {{ item.LabelName || '未分组'}}
            </div>
            <div class="all-list-child">
              <div
                @click.stop="openurl(itemchild)"
                v-for="itemchild in item.Panoramas"
                :key="itemchild.PbGuid"
                class="_css-pano-i"
              >
                <div v-show="hasGis(itemchild)" class="_css-pano-gisflag"></div>
                <div
                  class="_css-pano-iimg"
                  :style="computeItemImage(itemchild)"
                ></div>
                <div class="_css-pano-itext">
                  {{ itemchild.PbName }} {{ getStatusText(itemchild.PqFlag) }}
                </div>

                <div class="_css-pano-istatus">
                  <div class="_css-pano-istatustext">
                    采集时间：{{ func_formatsuckstring(itemchild.PbUpdatetime) }}
                  </div>
                  <div
                    @click.stop="showmenu(itemchild,false)"
                    class="_css-pano-idelbtn"
                  ></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="iframe-content" v-if="iframeContentShow">
      <div class="css-title-pano">
        <div class="css-title-left">{{ iframeCheckedName.PbName }}</div>

        <div class="css-title-menu">
          <div
            class="css-title-right"
            :class="clickpanoindex == 0 ? 'css-title-right-checked' : ''"
            @click="splitScreenFun"
          >
            <div class="css-image icon-pano-splitScreen"></div>
            <div class="css-menu">分屏</div>
          </div>
          <div
            class="css-title-right"
            :class="clickpanoindex == 1 ? 'css-title-right-checked' : ''"
            @click="panoComparison"
          >
            <div class="css-image icon-pano-comparison"></div>
            <div class="css-menu">全景图对比</div>
          </div>
          <div
            class="css-title-right"
            :class="clickpanoindex == 2 ? 'css-title-right-checked' : ''"
            @click="modelComparison"
          >
            <div class="css-image icon-pano-model-comparison"></div>
            <div class="css-menu">图模对比</div>
          </div>
          <div
            class="css-title-right"
            :class="clickpanoindex == 3 ? 'css-title-right-checked' : ''"
            @click="AsQuestion"
          >
            <div class="css-image icon-pano-question"></div>
            <div class="css-menu">发起问题</div>
          </div>
          <div class="css-title-right" @click="closeviewer($event)">
            <div class="css-image icon-pano-closepano"></div>
            <div class="css-menu">退出全景图</div>
          </div>
        </div>
      </div>
      <div class="_css-iframe-open-pano" ref="ref_panoifri">
        <iframe
          id="id_panoifri"
          class="_css-pano-ifri"
					ref="ref_left_Iframe"
					@load="iframeLoadEnd"
          :class="selectOtheriframeShow ? '_css-pano-iframe-small' : ''"
          :src="iframeSrcShowingPano"
        />
				<iframe
					v-if="splitScreenIframeSrcShow && clickpanoindex === 1 && !modelSplitShow"
					@load="iframeSplitScreenLoadEnd"
					ref="ref_splitIframe"
          id="id_splitScreenIframe"
          class="_css-pano-iframe-small"
          :src="iframeSrcsplitScreenIframe"
        />
        <modelNewIframeLoading
          v-if="splitScreenIframeSrcShow && clickpanoindex === 2 && modelSplitShow"
          id="iframeModelNew"
          ref="iframeModelNew"
          class="_css-pano-iframe-small"
          :VaultID="valutID"
          :featureID="modelNewID"
          modelVersion=""
        ></modelNewIframeLoading>
        <!-- dialog-shade 右边iframe的遮罩层-->
        <div class="dialog-shade" v-if="splitScreenIframeSrcShow && lockShow"></div>
				<div v-if="splitScreenIframeSrcShow" @click="splitScreenIframeSrcClose" class="icon-pano-close-model"></div>
				<div v-if="splitScreenIframeSrcShow" 
          @click="lockCloseOrOpen" 
          :class="lockShow? 'icon-pano-lock-close': 'icon-pano-lock-open'"
          class="icon-pano-close-model icon-pano-lock"></div>

      </div>

    </div>
    <!-- 为了截图  保存的图片 -->
    <div class="canvas-content" v-show="showCrossToImage && clickpanoindex == 3">
      <div class="canvas-left" :style="imgBgStyle(img_panoifriSrc)"></div>
      <div class="canvas-left canvas-right" :style="imgBgStyle(img_splitScreenIframeSrc)"></div>
    </div>
		<panoDetailsList
			v-if="detailsListShow"
      :posStyle="iframeContentShow"
			:m_selectedobj="m_selectedobj"
			:panoList="panoList"
			:panoSplitScreen="formSplitScreenShow"
			@changeList="changeList"
			@backLevel="backLevel"
			@close="detailsListShow=false"
		></panoDetailsList>

		<panoAllListChange
			v-if="splitScreenClickShow"
			:selectOption="selectOption"
			@ClickCheckedListByPano="ClickCheckedListByPano"
			@close="splitScreenClickClose"
		></panoAllListChange>
    <CompsCreateIssue 
			v-if="showCreateIssue" 
      @set_projectboot_extdata="opendocview"
			@oncancel="cancelCreateIssue" 
			@onok="createIssueok"
      :outertop="300" 
      :hasBaseUrl="toCreateIssuebaseUrl"
			:hasImg="true"
			:z-index="1003">
		</CompsCreateIssue>
    
  </div>
</template>
<script>
import panoDetailsList from "@/components/CompsPano/panoDetailsList";
import panoAllListChange from "@/components/CompsPano/panoAllListChange";
import CompsCreateIssue from "@/components/CompsIssue/CompsCreateIssue";
import html2canvas from "html2canvas";
import { mapGetters } from 'vuex' 
import modelNewIframeLoading from "@/components/Home/ProjectBoot/modelNewIframeLoading"

export default {
	name: 'PanoComparison',
  data() {
    return {
      headerTitle: '',
      selectOption: [],
      selectValue: "", // 标签搜索选择
      inputVal: "", // 搜索全景图名称
      panoDataList: [],
      iframeContentShow: false,
      clickpanoindex: 5,
      iframeSrcShowingPano: null,
      selectOtheriframeShow: false,
			searchPbName: '',  // 搜索input的值
			searchlabelId: '',  // 选择标签类型的值
			m_selectedobj: {},
			panoList:[],
			detailsListShow: false,  // 全景图分类List显示隐藏
			splitScreenClickShow: false, // 点击分屏展示
			formSplitScreenShow: false,  // true 是在全景图列表进来的，false不展示返回上一级
			iframeCheckedName: '', // 选择进入的全景图名字
			splitScreenIframeSrcShow: false, // 分屏展示
      valutID: '', // 新模型
      modelNewID: '', // 新模型
      modelSplitShow: false, // 图模对比时候为true
			showCreateIssue: false,  // 发起问题展示
      iframeFromClickList: false, // 判断打开方式是全景图list还是直接点击进入
      clickpano_req_changeScene:{}, // 选择全景图集的全景图，全景图的属性
			allPanoList: [],  // 当前场景下所有的全景图
			findModelIdByscenename: {},  // 当前全景图所绑定的属性，包括模型的值
      img_panoifriSrc: "",  //截图的src，左边iframe
      img_splitScreenIframeSrc: "",  // 截图的src ，右边iframe
      lockShow: false,  // 锁开关
      showCrossToImage: false, // 截图时候的div
      toCreateIssuebaseUrl: '',  // 点击发起问题传值给组件的
      isModelOrPano: "", // 初始化为"",记录是全景图还是模型，如果是全景图=isPanoIframe,如果是模型=isModelIframe
      lastLogIndex: -1, // 记录点击发起问题前的index,
      modelView: false,  // 判断模型的视角转动
      panoNewHVF: {}, // 记录每次全景图的hvf==关于模型
      panoOldHVF: {}, // 记录上锁时候全景图的hvf==关于模型
      modelOldHVF: {}, // 记录上锁时候模型的hvf==关于模型
      newHVFLeftPano: {}, // 记录左边全景图新的hvf
      oldHVFLeftPano: {}, // 记录左边全景图旧的hvf
      newHVFRightPano: {}, // 记录左边全景图新的hvf
      oldHVFRightPano: {}, // 记录左边全景图旧的hvf
    };
  },
  watch: {
    getModelLoad(data){
      if(data.state == "end"){
        this.iframeSplitScreenLoadEnd()
      }
    }
  },
  computed: {
    ...mapGetters([
      'getModelLoad'
    ]),
  },
	components: {
    panoDetailsList,
    panoAllListChange,
		CompsCreateIssue,
    modelNewIframeLoading
  },
  mounted() {
		let _this = this;
    this.headerTitle = this.$staticmethod._Get("menuText") || '';
		this.getTagOption();
    this.getList();
		window.addEventListener("message", function (data) {
      _this.processMessageData(data);
    });
  },
  methods: {
		processMessageData(data){
			let _this = this;
			if (data.data.act == "pano_req_relModelPoint"){
        // // 关联视点获取当前的全景图，只有在图模联动状态下执行
        // if (data.data.msg &&  data.data.msg.psbuid && _this.clickpanoindex == 2) {
        //   _this.getScenesByPbGuid(_this.iframeCheckedName,data.data.msg.psbuid)
        // }
        if(_this.clickpanoindex === 2) {
          _this.getScenesByPbGuid(_this.iframeCheckedName,_this.iframeCheckedName.PbGuid)
        }
      }else if(data.data.act == "pano_req_twoPanoViewchanged"){
        // 两个iframe同时展示的时候，才会执行,
        let _gethvf = data.data.msg.hvf;
        _this.panoNewHVF = data.data.msg.hvf;
        console.log(data.data.msg.ifElement,'===id--name')
        if(data.data.msg.ifElement == "id_panoifri"){
          _this.newHVFLeftPano = data.data.msg.hvf;
        }else if(data.data.msg.ifElement == "id_splitScreenIframe"){
          _this.newHVFRightPano = data.data.msg.hvf;
        }
        if(_this.clickpanoindex == 0 && _this.lockShow && _this.splitScreenIframeSrcShow && data.data.msg.typeNum !="id_splitScreenIframe"){
          let p_h =  _this.oldHVFRightPano.h + (_this.newHVFLeftPano.h - _this.oldHVFLeftPano.h);
          let p_v = _this.oldHVFRightPano.v + (_this.newHVFLeftPano.v - _this.oldHVFLeftPano.v);
          let p_f = _gethvf.f;
          let change_hvf = { h: p_h,v: p_v,f: p_f}
          const wintwo = document.getElementById('id_splitScreenIframe').contentWindow;
            const _data2 = {
              act: 'pano_req_twoPanoViewchanged', 
              msg: {
                hvf: change_hvf,
                typeNum: 'id_splitScreenIframe',
              }
            };
            wintwo.postMessage(_data2, "*");
          _this.panoOldHVF = data.data.msg.hvf; 
        }else if(_this.clickpanoindex == 1 && _this.lockShow && _this.splitScreenIframeSrcShow && data.data.msg.typeNum !="id_splitScreenIframe"){
            // console.log(data.data.msg.typeNum)
            // const winone = document.getElementById('id_panoifri').contentWindow;
            // const _data1 = {
            //   act: 'pano_req_twoPanoViewchanged', 
            //   msg: {
            //     hvf: _gethvf,
            //     typeNum: 1
            //   }
            // };
            // winone.postMessage(_data1, "*");
            
            const wintwo = document.getElementById('id_splitScreenIframe').contentWindow;
            const _data2 = {
              act: 'pano_req_twoPanoViewchanged', 
              msg: {
                hvf: _gethvf,
                typeNum: 'id_splitScreenIframe',
              }
            };
            wintwo.postMessage(_data2, "*");
        }else if(_this.clickpanoindex == 2 && _this.splitScreenIframeSrcShow && _this.lockShow  && data.data.msg.ifElement == "id_panoifri"){
          // 当是图模联动的时候执行
          // 这里得到的是当前全景图的视角，hvf，模型是直接设置的，全景图是postMessage
          let iframeWindow = window;
          
          if(data.data.msg.typeNum != 0){
         
            // _h为全景图旋转前后的差值   _v为全景图旋转前后的差值 
            let _h =  _this.panoOldHVF.h - _this.panoNewHVF.h;
            let _v = _this.panoOldHVF.v - _this.panoNewHVF.v;
            
            let azimuthAngle = Math.PI *  (_h) / 180 ;
            let polarAngle = Math.PI * (_v ) / 180 ;
            let _fov = _gethvf.f
            
            iframeWindow.scene.mv.controller.distance = 0.000001;
            let v3 = new scene.mv._THREE.Vector3();
            iframeWindow.scene.mv.controller.getPosition(v3);
            iframeWindow.scene.mv.controller.setOrbitPoint(v3.x, v3.y, v3.z);
          
            iframeWindow.scene.mv.controller.rotate(azimuthAngle, polarAngle);  // 传入的是弧度
            iframeWindow.scene.mv.THREE_Camera.fov = _fov; // 这里是角度
            iframeWindow.scene.mv.THREE_Camera.updateProjectionMatrix();
            iframeWindow.scene.render(true);
            // let pos = iframeWindow.model.resource.activeCamera.position;
            // iframeWindow.model.orbitControl.controls.setOrbitPoint(pos.x,pos.y,pos.z+0.0001);
            // iframeWindow.model.orbitControl.controls.rotate(azimuthAngle, polarAngle)//传入的是弧度
            // iframeWindow.model.resource.activeCamera.fov = _fov;//这里是角度
            // iframeWindow.model.resource.activeCamera.updateProjectionMatrix();
            // iframeWindow.model.resource.isProgressiveRendering=true;
          }
          _this.panoOldHVF = data.data.msg.hvf;   // 记录上一次的hvf
        }
      }else if(data.data.act == "pano_req_getBaseUrl"){
        // id_panoifri为左边的全景图，id_splitScreenIframe为右边的全景图，这里是获取全景图的截图
        // console.log(data.data.msg)
        if(data.data.msg.baseID == "id_panoifri"){
          // 获取到返回的 给设置背景图片
          _this.img_panoifriSrc = data.data.msg.baseUrl;
        }else if(data.data.msg.baseID == "id_splitScreenIframe"){
          _this.img_splitScreenIframeSrc = data.data.msg.baseUrl;
        }
 
        _this.$nextTick(()=>{
          _this.showCrossToImage = true;
          setTimeout(()=>{
            _this.crossHtmltoImage();
          },100)

        })
      }
		},
	
		// 下来筛选表现
    selectChangeFun(val) {
			if(val== null || val== undefined)  val = "" 
			this.searchlabelId = val;
			this.getList();
    },
		// input筛选全景图名称
    searchName(val) {
			this.searchPbName = val;
			this.getList();
    },
    // 点击全景图列表打开全景图
    openurl(item, objparms) {
      var _this = this;
      // console.log(item);
			objparms ? _this.iframeFromClickList = true : _this.iframeFromClickList = false;
      // 先判断是否还在转换中
      // ------------------
      if (item.PqFlag != 2) {
        _this.$message.warning(
          "该全景图仍处于后台转换状态，请稍后刷新页面再试"
        );
        return;
      }

			var viewurl = _this.$staticmethod.getPanoUrl(
			  item.PbUrl,
			  _this.$staticmethod._Get("organizeId"),
			  ""
			);
			_this.iframeCheckedName = item;

			if (!_this.auth_modifyauth) {
				viewurl += "&isDis=1";
			}

			_this.iframeSrcShowingPano = viewurl + '&idElement=id_panoifri';
			_this.iframeContentShow = true;
			_this.clickpanoindex = 5;
			_this.detailsListShow = false;
			_this.selectOtheriframeShow = false;
			  
    },
		// 左边全景图的
		iframeLoadEnd(){
      let _this = this;
      if(_this.iframeFromClickList){
        setTimeout(()=>{
          const win = document.getElementById('id_panoifri').contentWindow;
          // 添加事件调用视角转换
          if (win) {
            const data = {
                act: 'pano_req_changeScene', data: 'scene_' + _this.clickpano_req_changeScene.PsScenename
            };
            win.postMessage(data, "*");
          }
        },1000)
      }
    },  
    // 是否已发布到GIS
    hasGis(item) {
      return item.PbGisinfo && item.PbGisinfo.indexOf("{") >= 0;
    },
    // 某一个标题的背景颜色
    computeTitleBg(item) {
      let _b = {};
      _b["background"] = item;
      return _b;
    },
    // 某一项显示的背景图
    computeItemImage(item) {
      let _s = {};
      _s["background-image"] = `url('${window.bim_config.webserverurl}/Panorama${item.PbUrl}/cover.png')`;//  'url(\'' + item.PbUrl + 'cover.png'+ '\')';
      
			return _s;
    },
		// 时间过滤函数
    func_formatsuckstring(uptime) {
      if (!uptime) {
        return "<无>";
      }
      var _this = this;
      var d = new Date(uptime);
      var year = d.getFullYear();
      var month = d.getMonth() + 1;
      var date = d.getDate();
      return `${year}年${month}月${date}日`;
    },
		// 显示全景图集
    showmenu(item,formSplitScreen){
			console.log(item)
			
      let _this = this;
			formSplitScreen && formSplitScreen == '1' ? _this.formSplitScreenShow = true : _this.formSplitScreenShow = false;
			_this.m_selectedobj = item;
      if (item.PqFlag != 2) {
        _this.$message.warning('该全景图仍处于后台转换状态，请稍后刷新页面再试');
        return;
      }
      
      _this.getScenesByPbGuid(item,'')
    },
		
		getScenesByPbGuid(item,str){
			let _this = this;
			let projectID = _this.$staticmethod._Get("bimcomposerId");
			_this.$axios
        .get(
          // `http://172.17.1.96:6311/api/Panorama/PanoramaFile/GetScenesByPbGuid?pbguid=${item.PbGuid}&Token=${this.$staticmethod.Get('Token')}`
          `${window.bim_config.webserverurl}/api/Panorama/PanoramaFile/GetScenesByPbGuid?pbguid=${item.PbGuid}&Token=${this.$staticmethod.Get('Token')}`
        )
        .then((x) => {
          if (x.data && x.data.Data && x.data.Data.length) {
						if(str.length > 0){
							_this.allPanoList =  x.data.Data;
							let findModelId = _this.findPanoIndex(x.data.Data,str);
							_this.findModelIdByscenename = findModelId;
							if(findModelId.ModelId == null || findModelId.ModelId.length == 0) {
								_this.$message.error('当前场景没有关联模型视点，请关联后使用该功能');
                _this.clickpanoindex = -1;
								_this.selectOtheriframeShow = false;
								_this.splitScreenIframeSrcShow = false;
								return
							}else{
                _this.valutID = _this.$staticmethod._Get("organizeId");
                _this.modelNewID = findModelId.ModelId;
                
								// _this.iframeSrcsplitScreenIframe =	`${window.bim_config.bimviewerurl}?projectId=${projectID}&model=${findModelId.ModelId}`
								console.log(findModelId.ModelId,'===模型地址')
								_this.selectOtheriframeShow = true;
								_this.splitScreenIframeSrcShow = true;
                if(this.clickpanoindex == 2){
                  _this.modelSplitShow = true;
                }
                _this.isModelOrPano = "isModelIframe";
                this.$nextTick(() => {
                  this.$Bus.$emit("BimeScenesFetched",findModelId);
                })
							}
						}else{
							_this.panoList = x.data.Data;
							_this.detailsListShow = true;
						}
          }else{
						if(str.length > 0){
							console.log('图模联动请求=',x.data.Msg)
						}else{
							_this.panoList = [];
							_this.$message.error(x.data.Msg);
						}
					}
        })
        .catch((x) => {
          console.log(x)
          // _this.$message.error("获取场景信息失败");
        });
		},
		findPanoIndex(list,str){
			// let index = list.findIndex(x => x.PsScenename == str)
			let index = list.findIndex(x => x.PbGuid == str)
			return list[index]
		},
    // 分屏
    splitScreenFun() {
			this.closeViewOther();
      this.clickpanoindex = 0;
			this.splitScreenClickShow = true;
			this.selectOtheriframeShow = false;
			this.splitScreenIframeSrcShow = false;
      this.lockShow = false;
      this.isModelOrPano = "";
    },
    // 全景图对比
    panoComparison() {
			this.closeViewOther();
      this.clickpanoindex = 1;
			this.selectOtheriframeShow = false;
			this.splitScreenIframeSrcShow = false;
      this.isModelOrPano = "";
      this.lockShow = false;

			this.showmenu(this.iframeCheckedName,false);
    },
    // 图模对比
    modelComparison() {
      console.log(this.modelNewID,'=====Mox ')
			this.splitScreenIframeSrcShow = false;
      this.isModelOrPano = "";
      this.lockShow = false;

			this.closeViewOther();
      this.clickpanoindex = 2;
      window.postMessage({act: 'pano_req_relModelPoint'});
      
			// // 先message  获取当前的name，根据name获取当前的属性，
			// const win = document.getElementById('id_panoifri').contentWindow;
      // // 添加事件调用视角转换
      // if (win) {
      //   const data = {
      //       act: 'pano_req_relModelPoint', data: 'setpoint'
      //   };
      //   win.postMessage(data, "*");
      // }
    },
		// 分屏的iframe加载完成
		iframeSplitScreenLoadEnd(){
			let _this = this;
			// 判断是否是图模对比，如果是，需要执行加载到对应视点
			if(_this.clickpanoindex == 2){

        // 设置模型初始化视角 _view是在全景图管理中绑定的视角
        // window.saveSceneJson2Store(_view);
        let iframeWindow = document.getElementById('scene-iframe').contentWindow
        iframeWindow.renderSceneMainMenu({name: 'widgetSource', attr: 'isShow', value: false})
        // iframeWindow.saveSceneJson2Store(_this.findModelIdByscenename.ViewId);
        let jsonCar = JSON.parse(_this.findModelIdByscenename.ViewId)
        iframeWindow.scene.resetCamera(JSON.parse(jsonCar))
        iframeWindow.toggleSceneManageEditMode(false);
        
        // iframeWindow.model.BIM365API.Events.finishRender.on('default',()=>{
				// 	let _view = JSON.parse(_this.findModelIdByscenename.ViewId)
				// 	// 设置模型初始化视角 _view是在全景图管理中绑定的视角

        //   // 旧引擎
        //   const {pos,target,focalOffset} =_view;
        //   iframeWindow.model.orbitControl.controls.setFocalOffset(focalOffset.x,focalOffset.y,focalOffset.z,false);
        //   iframeWindow.model.orbitControl.controls.setLookAt(pos.x,pos.y,pos.z,target.x,target.y,target.z,true);
        //   // rotate
        // })
        
			}else if(_this.clickpanoindex == 0 || _this.clickpanoindex == 1){
        const win = document.getElementById('id_splitScreenIframe').contentWindow;
				setTimeout(()=>{
          // 添加事件调用视角转换
          if (win) {
            const data = {
                act: 'pano_req_changeScene', data: 'scene_' + _this.clickpano_req_changeScene.PsScenename
            };
            win.postMessage(data, "*");
          }
        },1000)
        // console.log(win.getcurrenthvf(),'====全景图的视角');
			}
			
		},
    // 发起问题
    AsQuestion() {
			let _this = this;
      _this.lastLogIndex = _this.clickpanoindex;
      _this.lockShow = false;
      if(!_this.splitScreenIframeSrcShow){
				_this.$message.error('该状态下不支持发起问题')
				return;
			}
			
			_this.closeViewOther();
      _this.clickpanoindex = 3;
      _this.img_panoifriSrc = "";
      _this.img_splitScreenIframeSrc = "";
      
      const winone = document.getElementById('id_panoifri').contentWindow;
      const _data1 = {
        act: 'pano_req_getBaseUrl', 
        msg: {
          baseID: 'id_panoifri'
        }
      };
      winone.postMessage(_data1, "*");

      if(_this.isModelOrPano == "isPanoIframe"){
        const wintwo = document.getElementById('id_splitScreenIframe').contentWindow;
        const _data2 = {
          act: 'pano_req_getBaseUrl', 
          msg: {
            baseID: 'id_splitScreenIframe'
          }
        };
        wintwo.postMessage(_data2, "*");
      }else{
        window.scene.snapThumbnail().then(image_base64=>{
          _this.img_splitScreenIframeSrc = image_base64;
        })
      }

    },
    // 退出
    closeviewer() {
			this.splitScreenClickShow = false;
			this.detailsListShow = false;
			this.splitScreenIframeSrcShow = false;
      this.iframeContentShow = false;
      this.showCrossToImage = false;
      this.lockShow = false;
      this.modelSplitShow = false;
    },
		// 初始化获取全景图列表
    getList() {
      let _this = this;
      let _organizeId = _this.$staticmethod._Get("organizeId");

      // 显示loading
      // -----------
      let _LoadingIns = _this.$loading({
        text: "加载中",
        target: document.getElementsByClassName("_css-pano-bottom")[0],
      });

      setTimeout(() => {
        _this.$axios
					.get(
          	`${window.bim_config.webserverurl}/api/Panorama/PanoramaFile/GetListByLabelGroup?organizeId=${_organizeId}&labelId=${_this.searchlabelId}&pbName=${_this.searchPbName}&Token=${this.$staticmethod.Get('Token')}`,
        	)
          .then((x) => {
            _LoadingIns.close();
						if (x.status == 200 && x.data.Ret > 0) {
							_this.panoDataList = x.data.Data;
						}else{
							_this.$message.error(x.data.Msg);
						}
          })
          .catch((x) => {
            _LoadingIns.close();
            debugger;
						_this.$message.error('请求服务器异常，请稍后再试');
          });
      }, 0);
    },
		// 初始化获取标签类型
		getTagOption(){
			let _this = this;
			let _organizeId = this.$staticmethod._Get('organizeId');
      this.$axios
        .get(
          `${window.bim_config.webserverurl}/api/Panorama/Label/GetList?OrganizeId=${_organizeId}&Token=${this.$staticmethod.Get('Token')}`
        )
        .then((res) => {
					if (res.status == 200 && res.data.Ret > 0) {
						_this.selectOption = res.data.Data;
					}else{
						_this.$message.error(res.data.Msg);
					}
          
        });
    },
		// 二级全景图列表 点击选择打开的全景图
		changeList(item,selectedobj){
      // 打开全景图
			let _this = this;
			_this.clickpano_req_changeScene = item
			_this.detailsListShow = false;
			if(_this.formSplitScreenShow){
				// 分屏选择的打开右侧分屏的全景图
				_this.selectOtheriframeShow = true;

				let screenurl = _this.$staticmethod.getPanoUrl(
					_this.m_selectedobj.PbUrl,
					_this.$staticmethod._Get("organizeId"),
					""
				);
				_this.iframeSrcsplitScreenIframe = screenurl + '&idElement=id_splitScreenIframe'
				_this.splitScreenIframeSrcShow = true;
        _this.isModelOrPano = "isPanoIframe";
			}else if (_this.clickpanoindex == 1){
				// 打开全景图对比
				let screenurl = _this.$staticmethod.getPanoUrl(
					_this.m_selectedobj.PbUrl,
					_this.$staticmethod._Get("organizeId"),
					""
				);
				_this.selectOtheriframeShow = true;
				_this.iframeSrcsplitScreenIframe = screenurl + '&idElement=id_splitScreenIframe'
				_this.splitScreenIframeSrcShow = true;
        _this.isModelOrPano = "isPanoIframe";
			}else{
				// 点击打开的全景图
				_this.openurl(selectedobj,true)
			}
      // 然后切换
    },
		// 二级全景图列表 点击返回上一级
		backLevel(){
			this.clickpanoindex = 0;
			this.splitScreenClickShow = true;
			this.detailsListShow = false;
		},
		// 全景图全部筛选组件选择二级全景图列表
		ClickCheckedListByPano(item){
			this.splitScreenClickShow = false;
			this.formSplitScreenShow = true;
			this.detailsListShow = true;
			this.showmenu(item,'1')
		},
		// 关闭全景图全部筛选组件
		splitScreenClickClose(){
			this.formSplitScreenShow = false;
			this.splitScreenClickShow = false;
		},
		// 关闭分屏模型展示
		splitScreenIframeSrcClose(){
			this.selectOtheriframeShow = false;
			this.splitScreenIframeSrcShow = false;
      this.modelSplitShow = false;
			this.clickpanoindex = -1;
      this.lockShow = false;
		},
    // 发起问题，打开在线预览
    // --------------------
    opendocview(paratype, para){
      var _this = this;
      _this.$emit("setProjectbootExtdata", paratype, para);
    },
		cancelCreateIssue(){
			this.showCreateIssue = false;
      this.clickpanoindex = this.lastLogIndex;
		},
		// 创建问题：确定按钮
    createIssueok(obj) {
      // debugger
      // console.log(obj)
      var _this = this;

      if (!obj.title || obj.title == "" || obj.title.trim() == '') {
        _this.$message.error("请输入标题");
        return;
      }
      if (obj.deadlinetimeval == "") {
        _this.$message.error("请选择截止时间");
        return;
      }

      // 参数：截止时间
      var timestr = _this.timeToString(obj.deadlinetimeval);
      // 参数：问题标题
      var title = obj.title;
      // 参数：参与人
      var joinerstr = "";
      for (var i = 0; i < obj.addingjoiners.length; i++) {
        joinerstr += `${i == 0 ? "" : ","}${obj.addingjoiners[i].UserId}`;
      }
      // 参数：附件FileId
      var fileidstr = "";
      for (var i = 0; i < obj.addingFiles.length; i++) {
        fileidstr += `${i == 0 ? "" : ","}${obj.addingFiles[i].FileId}`;
      }
      // debugger
      // 调用问题接口，添加问题，在回调中刷新问题（依据当前过滤器进行刷新）。


      _this
        .$axios({
          method: "post",
          url: `${this.$issueBaseUrl.AddIssue}`,
          data: {
            Token: _this.$staticmethod.Get("Token"),
            RealName: _this.$staticmethod.Get("RealName"),
            Title: title,
            JoinerIds: joinerstr,
            FileIds: fileidstr,
            EndDateStr: timestr,
            organizeId: _this.$staticmethod._Get("organizeId"),
            IssueTypeId: obj.IssueTypeId || '',
            ModelID:obj.ModelID,
            ImageUrl:obj.ImageUrl,
            ViewPointID:obj.ViewPointID,
            addingPic:  obj.addingPic,
            ImageIds: obj.ImageIds
          }
        })
        .then(x => {
					if (x.status == 200 && x.data.Ret > 0) {
						_this.$message.success('发起问题成功！');
						_this.showCreateIssue = false;
            _this.clickpanoindex = _this.lastLogIndex;;
					} else {
						_this.$message.error(x.data.Msg);
            return;
					}
        })
        .catch(x => {
					_this.$message.error('请求异常，请稍后再试')
        });
    },
		timeToString(date) {
			let year = date.getFullYear();
			let month = date.getMonth() + 1;
			let dat = date.getDate();
			let h = date.getHours();
			let m = date.getMinutes();
			let s = date.getSeconds();
			let tostr = `${year}-${month < 10 ? "0" + month : month}-${
					dat < 10 ? "0" + dat : dat
			} ${h < 10 ? "0" + h : h}:${m < 10 ? "0" + m : m}:${
					s < 10 ? "0" + s : s
			}`;
			return tostr;
		},
		// 在全景图切换时候，关闭别的弹窗
		closeViewOther(){
			let _this = this;
			_this.detailsListShow = false;
			_this.splitScreenClickShow = false;
			_this.showCreateIssue = false;
      _this.clickpanoindex = -1;
		},
    imgBgStyle(imgsrc){
      let _s = {}
      _s["background-image"] = `url('${imgsrc}')`;
      return _s;
    },
    // 截图
    crossHtmltoImage() {
      let _this = this;
      if(_this.img_panoifriSrc.length < 1 || _this.img_splitScreenIframeSrc.length < 1) return
      
      const canvas = document.createElement("canvas");
      const canvasBox = document.querySelector(".canvas-content");
      const width = canvasBox.clientWidth;
      const height = canvasBox.clientHeight;
      // 宽高 * 2 并放大 2 倍 是为了防止图片模糊
      // canvas.width = width * 2
      // canvas.height = height * 2
      canvas.width = width;
      canvas.height = height;
      canvas.style.width = width + "px";
      canvas.style.height = height + "px";
      const context = canvas.getContext("2d");
      // context.scale(2, 2)
      context.scale(0.5, 0.5);
      const options = {
        backgroundColor: null,
        canvas: canvas,
        useCORS: true,
      };
      
      html2canvas(canvasBox, options).then((canvas) => {
        const dataURL = canvas.toDataURL("image/png"); // 图片格式转成base64
        // console.error(dataURL,'===dataURL===')
        // this.downloadImage(dataURL);
        _this.toCreateIssuebaseUrl = dataURL;
        _this.showCreateIssue = true;
      });
    },
    downloadImage(url) {
      const a = document.createElement("a");
      a.href = url;
      a.download = "图片命名";
      a.click();
    },
    lockCloseOrOpen(){
      let _this = this;
      if(_this.clickpanoindex == 0){
        _this.lockShow = !_this.lockShow;
        _this.oldHVFLeftPano = _this.newHVFLeftPano;
        _this.oldHVFRightPano = _this.newHVFRightPano;
      }else if(_this.clickpanoindex == 1){
        _this.lockShow = !_this.lockShow;
      }else if(_this.clickpanoindex == 2){
        _this.panoOldHVF = _this.panoNewHVF;
        _this.lockShow = !_this.lockShow;
        
        // let iframeWindow = document.getElementById('id_splitScreenIframe').contentWindow;
        // let _h = iframeWindow.model.orbitControl.controls.azimuthAngle;
        // let _v = iframeWindow.model.orbitControl.controls.polarAngle;
        // let _f = iframeWindow.model.orbitControl.controls.camera.fov;
        // _this.modelOldHVF = {h:_h, v: _v, f: _f}
        // // 在执行全景图联动前，执行这个代码
        // let position = iframeWindow.model.resource.activeCamera.position;
        // iframeWindow.model.controls.orbit.controls.setOrbitPoint(position.x,position.y-0.1,position.z)
      }else{
        _this.lockShow = false;
      }
    },
    getStatusText(num){
      let _status = "";
      switch(num){
        case 0:
          _status="(待转换)";
          break;
        case 1:
          _status="(转换中)";
          break;
        case 2:
          _status="";
          break;
        case 3:
          _status="(转换失败)";
          break;
        default:
          _status="(待转换)";
          break;
      }
      return _status;
    }
  },
  destroyed () {
    window.removeEventListener('message', this.processMessageData, false)
  },
};
</script>
<style lang="stylus" scoped>
.pano-comparsion {
  width: 100%;
  height: 100%;
  position: relative;
  .com-content{
    margin: 16px 20px;
    background: #fff;
    border-radius: 2px;
    height: calc(100% - 50px - 32px);
  }
  ._css-top-search {
    display: flex;
    justify-content: end;
    padding: 20px 0 16px 0;

    ._css-select {
      position: relative;
      width: 230px;
      height: 32px;
      border-radius: 2px;
      border: 1px solid #D9D9D9;
      margin-right: 24px;

      i {
        position: absolute;
        top: 8px;
        right: 7px;
        color: #999999;
      }
      /deep/ .el-icon-arrow-up:before{
        content: ''
      }
      /deep/ .el-select, /deep/ .el-input{
        width: 90%;
      }
      /deep/ .el-input__inner{
        line-height: 32px;
      }
    }
 
  }

  ._css-pano-body {
    height: calc(100% - 70px);
    display: flex;
    overflow-x: auto;
    margin-left: 16px;

    ._css-pano-list {
      width: 290px;
      border-radius: 8px;
      margin-right: 24px;
      height: calc(100% - 20px);

      .list-content {
        height: 100%;

        .title {
          line-height: 38px;
          font-size: 16px;
          font-family: PingFangSC-Medium, PingFang SC;
          font-weight: 500;
          color: #FFFFFF;
          border-radius: 8px 8px 0px 0px;
        }

        ._css-pano-i:hover {
          border: 1px solid #007AFF;
        }

        ._css-pano-i:hover ._css-pano-idelbtn {
          display: block;
        }

        .all-list-child {
          max-height: calc(100% - 40px);
          overflow-x: hidden;
          overflow-y: auto;
          background: #FFFFFF;
          border-radius: 0 0 8px 8px;
          border: 1px solid #e8e8e8;
        }

        ._css-pano-i {
          width: 242px;
          height: 240px;
          border-radius: 4px;
          border: 1px solid #E8E8E8;
          margin: 24px 24px 0;
          position: relative;
          cursor: pointer;

          ._css-pano-gisflag {
            position: absolute;
            right: 8px;
            top: 8px;
            width: 26px;
            height: 26px;
            border-radius: 8px 8px 0px 0px;
            background-image: url('../../../assets/svgs_loadbyurl/model_hasGIS.svg');
            background-size: contain;
            background-repeat: no-repeat;
          }

          ._css-pano-iimg {
            width: 242px;
            height: 168px;
            background-repeat: no-repeat;
            background-size: cover;
            background-position: center center;
          }

          ._css-pano-itext {
            font-size: 16px;
            height: 38px;
            font-weight: 500;
            overflow-x: hidden;
            text-overflow: ellipsis;
            width: 100%;
            white-space: nowrap;
            box-sizing: border-box;
            padding: 0 8px 0 8px;
            line-height: 38px;
            color: #222222;
          }

          ._css-pano-istatus {
            width: 100%;
            height: 38px;
            line-height: 26px;
            font-size: 12px;
            position: relative;
          }

          ._css-pano-istatustext {
            color: #666666;
            text-align: center;
          }

          ._css-pano-idelbtn {
            position: absolute;
            right: 12px;
            top: calc(50% - 15px);
            display: none;
            color: #999;
            width: 16px;
            height: 16px;
            margin: 0 auto 6px;
            background-image: url('../../../assets/images/icon-pano-timeline.png');
            background-size: contain;
            background-repeat: no-repeat;
            cursor: pointer;
          }

          ._css-pano-idelbtn:hover {
            background-image: url('../../../assets/images/icon-pano-timeline-hover.png');
          }
        }

        ._css-pano-i:last-child {
          margin: 24px;
        }
      }
    }
  }
}

.iframe-content {
  position: fixed;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  z-index: 1000;
  background-color: #fff;

  ._css-pano-ifri {
    border: none;
    width: 100%;
    height: 100%;
  }

  ._css-pano-iframe-small {
    border: none;
    height: 100%;
    width: 50% !important;
  }

  ._css-iframe-open-pano {
    // margin-top: 72px;
    width: 100%;
    height: 100%;
    // height: calc(100% - 72px);
		display: flex;
  }
  .dialog-shade{
    position: absolute;
    top: 0;
    left: 50%;
    margin-top: 72px;
    background: transparent;
    width: 50%;
    height: calc(100% - 72px);
    color: #fff;
    z-index: 1001;
  }
  .css-title-pano {
    position: absolute;
    top: 0;
    left: 0;
    background: linear-gradient(180deg, rgba(7, 28, 72, 0.5) 0%, rgba(7, 28, 72, 0.03) 100%);;
    // background: #0092fa;
    width: 100%;
    height: 72px;
    color: #fff;
  }

  .css-title-left {
    position: absolute;
    left: 24px;
    top: 24px;
  }

  .css-title-menu {
    position: absolute;
    top: 0px;
    right: 50px;
    display: flex;
  }

  .css-title-right {
    margin-right: 10px;
    cursor: pointer;
    background: transparent;
    padding: 12px;
  }

  .css-title-right-checked {
    background: rgba(255, 255, 255, 0.15);
    padding: 12px;
  }

  .css-menu {
    font-size: 12px;
  }

  .css-image {
    width: 24px;
    height: 24px;
    margin: 0 auto 6px;
    background-image: url('../../../assets/images/icon-pano-splitScreen.png');
    background-size: contain;
    background-repeat: no-repeat;
  }

  .icon-pano-question {
    background-image: url('../../../assets/images/icon-pano-question.png');
  }

  .icon-pano-closepano {
    background-image: url('../../../assets/images/icon-pano-closepano.png');
  }

  .icon-pano-comparison {
    background-image: url('../../../assets/images/icon-pano-comparison.png');
  }

  .icon-pano-model-comparison {
    background-image: url('../../../assets/images/icon-pano-model-comparison.png');
  }
}
.icon-pano-close-model{
	width: 40px;
	height: 40px;
	position:absolute;
	top: 87px;
	right: 15px;
	margin: 0 auto 6px;
	background-image: url('../../../assets/images/icon-pano-close-model.png');
	background-size: contain;
	background-repeat: no-repeat;
	z-index: 1002;
	cursor: pointer;
}
.icon-pano-lock{
  position:absolute;
	top: 48%;
	left: 20px;
}
.icon-pano-lock-close{
  background-image: url('../../../assets/images/icon-pano-lock-close.png');
	background-size: contain;
	background-repeat: no-repeat;
}
.icon-pano-lock-open{
  background-image: url('../../../assets/images/icon-pano-lock-open.png');
	background-size: contain;
	background-repeat: no-repeat;
}
.canvas-content{
  position: fixed;
  width: 800px;
  height: 400px;
  top: 20%;
  left: 0;
  z-index: 1;
  background-color: #fff;
  .canvas-left{
    display: inline-block;
    width: 49%;
    height: 100%;
    z-index: 1001;
    // background: #f00;
    background-repeat: no-repeat;
    background-size: contain;
    background-position: center center;
  }
  .canvas-right{
    z-index: 1001;
    // background: #ff0;
    background-repeat: no-repeat;
    background-size: contain;
    background-position: center center;
  }
}

</style>