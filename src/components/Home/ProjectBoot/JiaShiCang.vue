<template>
  <div class="jiashicang-content">
    <iframe :src="iframeUrl" frameborder="0" width="100%" height="100%" class="iframeCss" ref="iframeRef"></iframe>
  </div>
</template>
<script>
export default {
  name: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>',
  data(){
    return {
      iframeUrl:''
    }
  },
  mounted(){ 
    let _token = this.$staticmethod.Get("Token");
    this.iframeUrl=`http://************:5001/dist/#/projectHome?token=${_token}`
    this.$refs.iframeRef.contentWindow.postMessage({
        token:_token
    }, "*"); 
    window.open(`http://************:5001/dist/#/projectHome?token=${_token}`,'_blank')
  }
}
</script>
<style scoped>
.jiashicang-content{
  width: 100%;
  height: 100%;
}
.iframeCss{
  margin-top: 58px;
}
</style>