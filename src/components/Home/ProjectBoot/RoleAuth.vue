<template>
	<section class="_css-doc-all css-h100" @click="clearcontextbtns">
		<!-- 面包屑部分 -->
		<section
			class="_css-doc-top _css-roleauth-top css-w100 css-bsb css-bst css-usn"
		>
			<CompsEloButton
				:width="120"
				:height="40"
				:fontSize="14"
				color="#fff"
				text="新建角色"
				:extraclassobj="{ '_css-roleauth-newrole': true }"
				@onclick="btnnewrole_click"
			></CompsEloButton>
		</section>
		<!-- 面包屑部分 -->

		<!-- 主体部分 -->
		<section class="_css-body">
			<!-- 主体内部部分 -->
			<div class="_css-body-inner">
				<!-- 显示角色列表，显示角色成员列表，用于控制显示和隐藏（左藏入，右藏入） -->
				<div class="_css-body-inner-roleandmembers">
					<!-- 显示角色，维护角色的左侧 -->
					<div
						class="_css-left"
						:class="{ islefthide: showroletype != 'roles' }"
					>
						<!-- 显示角色列表的区域 -->
						<div class="_css-left-rolelist">
							<!-- 如果当前位置显示所有角色，则： -->
							<template>
								<!-- 每一个角色条目 -->
								<div
									:data-debug33="item.RoleId"
									class="_css-left-rolei"
									v-for="item in roles"
									:key="item.RoleId"
									:class="{
										clicked: item.RoleId == currentshowrole,
										'_css-left-roleiaddingnewrole':
											editing_roleid == item.RoleId,
									}"
									@click="begin_showroleauth(item.RoleId, $event)"
								>
									<div class="_css-left-role-icon icon-interface-team"></div>
									<div
										:title="item.FullName"
										class="_css-left-role-content"
										:class="
											item.EnabledMark == 1 ? '_css-rolenormal' : '_css-roledis'
										"
									>
										<!-- 当前正在编辑这个角色，则显示编辑框，否则只显示角色名称 -->
										<template v-if="item.RoleId == editing_roleid">
											<el-tooltip
												class="item"
												effect="dark"
												content="Enter确认；Esc取消"
												placement="bottom"
											>
												<input
													@click="_stopPropagation($event)"
													@keyup.stop="rolenameeditkeyup($event)"
													v-onfocus
													type="text"
													class="_css-addingnewrolename"
													v-model="item.FullName"
													placeholder="请输入角色名称"
												/>
											</el-tooltip>
										</template>
										<template v-else>{{ item.FullName }}</template>
										<!-- //当前正在编辑这个角色，则显示编辑框，否则只显示角色名称 -->
									</div>

									<el-tooltip
										popper-class="css-no-triangle"
										effect="dark"
										content="编辑角色"
										placement="left"
									>
										<div
											class="_css-left-role-rpart"
											@click="
												showrolecontextmenu(
													item.RoleId,
													item.EnabledMark,
													$event,
													item
												)
											"
										>
											<div
												v-show="item.RoleId != editing_roleid"
												class="_css-left-role-rpartbtn icon-interface-list"
											></div>
										</div>
									</el-tooltip>
								</div>
								<!-- //每一个角色条目 -->

								<!-- 正在新增的角色条目 -->
								<div
									v-if="isaddingnewrole"
									class="_css-left-rolei _css-left-roleiaddingnewrole"
								>
									<template>
										<div class="_css-left-role-icon icon-interface-team"></div>
										<div
											class="_css-left-role-content _css-rolenormal"
											@click="_stopPropagation($event)"
										>
											<!-- 内部用于显示角色名称部分 -->
											<el-tooltip
												class="item"
												effect="dark"
												content="Enter确认；Esc取消"
												placement="bottom"
											>
												<input
													@keyup.stop="rolenameeditkeyup($event)"
													v-onfocus
													type="text"
													class="_css-addingnewrolename"
													v-model="addingnewrolename"
													placeholder="请输入角色名称"
												/>
											</el-tooltip>
											<!-- //内部用于显示角色名称部分 -->
										</div>
										<!-- 如果是新增角色，则rpart里没有维护按钮 -->
										<div class="_css-left-role-rpart"></div>
										<!-- //如果是新增角色，则rpart里没有维护按钮 -->
									</template>
								</div>

								<!-- //正在新增的角色条目 -->
							</template>
							<!-- //如果当前位置显示所有角色，则： -->
						</div>
						<!-- //显示角色列表的区域 -->

						<div
							class="_css-contextmenus"
							v-if="CompsEloContextMenu_visible"
							:style="{
								left: CompsEloContextMenu_p_x + 'px',
								top: CompsEloContextMenu_p_y + 'px',
							}"
						>
							<CompsEloContextMenu
								@onitemclick="CompsEloContextMenu_click"
								:obj="CompsEloContextMenu_data"
							></CompsEloContextMenu>
						</div>
					</div>
					<!-- //显示角色，维护角色的左侧 -->

					<!-- 显示角色成员 -->
					<div
						class="_css-left__member"
						:class="{ isrighthide: showroletype != 'members' }"
					>
						<!-- 显示角色列表的区域 -->
						<div class="_css-left-rolelist">
							<!-- 否则显示某角色及其下成员 -->
							<template>
								<!-- 当前角色显示区域 -->
								<div
									class=""
									:class="{
										'_css-roledetailheader_true': true,
										'_css-left-roleiaddingnewrole': false,
									}"
								>
									<div
										class="_css-left-rolecur-icon icon-arrow-left_outline"
										@click.stop="backtoroleslist"
									></div>
									<div
										class="_css-left-role-content _css-rolenormal"
										@click="_stopPropagation($event)"
									>
										<template>{{ CompsEloContextMenu_rolename }}</template>
									</div>
									<el-tooltip
										v-show="true || iseditingmembers"
										popper-class="css-no-triangle"
										effect="dark"
										content="添加成员到角色"
										placement="bottom"
									>
										<div
											class="_css-left-role-rpart"
											@click.stop="roleuser_add()"
										>
											<div
												v-show="true || iseditingmembers"
												class="_css-left-user-rpartbtn icon-interface-user_fill basic-btn-normal-color basic-btn-normal-size"
											></div>
										</div>
									</el-tooltip>
								</div>
								<!-- //当前角色显示区域 -->
								<div class="_css-left-members css-bsb">
									<!-- 该角色下某一名成员 -->
									<div
										class="_css-left-memberi"
										:data-debug152="item.UserId"
										v-for="item in roleusers"
										:key="item.UserId"
									>
										<div class="_css-left-role-icon icon-interface-user"></div>
										<div
											class="_css-left-role-content _css-rolenormal"
											@click="_stopPropagation($event)"
										>
											<template>{{ item.RealName }}</template>
										</div>
										<el-tooltip
											v-show="true || iseditingmembers"
											popper-class="css-no-triangle"
											effect="dark"
											content="操作当前成员"
											placement="left"
										>
											<div
												class="_css-left-role-rpart"
												@click.stop="
													showusercontent(item.UserId, item.RealName, item.IsOrganizeManager, $event,)
												"
											>
												<div
													v-show="true || iseditingmembers"
													class="_css-left-user-rpartbtn icon-interface-list"
												></div>
											</div>
										</el-tooltip>
									</div>
									<!-- //该角色下某一名成员 -->
								</div>
							</template>
							<!-- //否则显示某角色及其下成员 -->
						</div>
						<!-- //显示角色列表的区域 -->
						<!-- 显示按钮的区域 -->
						<div class="_css-left-rolebtn2">
							<CompsEloButton
								v-show="false"
								v-if="!iseditingmembers"
								@onclick="btnrolememberedit_click"
								:text="'编辑角色成员'"
								:color="'#fff'"
								:fontSize="12"
								:width="148"
								:height="32"
							></CompsEloButton>

							<div v-else class="_css-editingrolemembersbtns">
								<CompsEloButton
									@onclick="btnrolemembercancel_click"
									:text="'取消'"
									:color="'#000'"
									:bgcolor="'#fff'"
									:border="'1px solid rgba(0,0,0,0.15)'"
									:fontSize="12"
									:width="70"
									:height="32"
								></CompsEloButton>
								<CompsEloButton
									@onclick="btnrolemembersave_click"
									:disabled="rmRoleIds.length == 0 && addRoleIds.length == 0"
									:text="'保存'"
									:color="'#fff'"
									:fontSize="12"
									:width="70"
									:height="32"
								></CompsEloButton>
							</div>
						</div>
						<!-- //显示按钮的区域 -->

						<div
							class="_css-contextmenus"
							v-if="CompsEloContextMenu_visible"
							:style="{
								left: CompsEloContextMenu_p_x + 'px',
								top: CompsEloContextMenu_p_y + 'px',
							}"
						>
							<CompsEloContextMenu
								@onitemclick="CompsEloContextMenu_click"
								:obj="CompsEloContextMenu_data"
							></CompsEloContextMenu>
						</div>
					</div>
					<!-- //显示角色成员 -->
				</div>
				<!-- //显示角色列表，显示角色成员列表，用于控制显示和隐藏（左藏入，右藏入） -->

				<!-- 显示角色权限的右侧，如果当前选择了一个角色的话。 -->
				<div class="_css-right" v-if="currentshowrole != ''">
					<!-- 顶部 -->
					<div class="_css-rt">
						<CompsTableControl
							v-bind:selecteditemid="roleauthselecteditemid"
							v-bind:tablist="roleauthtab"
							@onchange="authtabcontrol_onchange"
							@onbtnclick="authtabbtnclick"
						></CompsTableControl>
						<CompsTopTitleBtns
							v-bind:obj="CompsTopTitleBtns_data"
							@onok="CompsTopTitleBtns_ok"
							@oncancel="CompsTopTitleBtns_cancel"
						></CompsTopTitleBtns>
					</div>
					<!-- //顶部 -->
					<!-- 底部 -->
					<div class="_css-rb" data-debug="line 76">
						<!-- 底部有效 -->
						<div
							class="_css-rb-valid css-fixedleftgbcolor css-fixtreeicon"
							id="id_rb_valid"
							v-if="currentshowauthtype == 'docauth'"
						>
							<!-- 表格体 -->
							<el-table
								ref="docauthtable"
								key="docauthtable"
								:header-cell-class-name="header_cell_class_name"
								:cell-class-name="cell_class_name"
								:border="true"
								row-key="FolderId"
								lazy
								:load="table_load"
								:data="tableData"
								height="500"
								class="_css-table _css-customstyle"
								style="width: 100%"
							>
								<el-table-column
									fixed="left"
									:resizable="true"
									label="项目文档"
									width="196"
								>
									<template slot="header" slot-scope="scope">
										<span
											:data-test1="scope"
											class="_css-roleauthfolderheadcontainer"
										>
											<span
												class="icon-interface-document_fill _css-roleauthfolderhead"
											></span>
											<span class="_css-roleauthfolderheadspan">项目文档</span>
										</span>
									</template>
									<template slot-scope="scope">
										<span
											class="icon-interface-folder _css-roleauthfolder"
										></span>
										<span
											:title="scope.row.FolderName"
											class="_css-roleauthfname"
											:data-debugfolderid="scope.row.FolderId"
											>{{ scope.row.FolderName }}</span
										>
									</template>
								</el-table-column>

								<el-table-column
									:resizable="false"
									align="center"
									label="查看"
									min-width="46"
								>
									<template slot="header" slot-scope="scope">
										<span :data-test1="scope" class="_css-authcol-head"
											>查看</span
										>
									</template>
									<template slot-scope="scope">
										<div class="css-w100 css-fc css-jcsa">
											<div
												v-if="DocAuth.editable == true"
												class="css-cb css-icon14 css-cp"
												@click.stop="switchdocitemsomeauth(scope.row, 'Search')"
												:class="{
													'mulcolor-interface-checkbox-selected':
														scope.row.Auth.Search == true,
												}"
											></div>
											<div
												v-else
												:class="{
													'icon-checkbox-Selected-Disabled-dis-blue':
														scope.row.Auth.Search == true,
												}"
											></div>
										</div>
									</template>
								</el-table-column>

								<el-table-column
									:resizable="false"
									align="center"
									label="上传/新建"
									min-width="46"
								>
									<template slot="header" slot-scope="scope">
										<span :data-test1="scope" class="_css-authcol-head"
											>上传/新建</span
										>
									</template>
									<template slot-scope="scope">
										<div class="css-w100 css-fc css-jcsa">
											<div
												v-if="DocAuth.editable == true"
												class="css-cb css-icon14 css-cp"
												@click.stop="switchdocitemsomeauth(scope.row, 'Upload')"
												:class="{
													'mulcolor-interface-checkbox-selected':
														scope.row.Auth.Upload == true,
												}"
											></div>
											<div
												v-else
												:class="{
													'icon-checkbox-Selected-Disabled-dis-blue':
														scope.row.Auth.Upload == true,
												}"
											></div>
										</div>
									</template>
								</el-table-column>

								<el-table-column
									:resizable="false"
									align="center"
									label="打开"
									min-width="46"
								>
									<template slot="header" slot-scope="scope">
										<span :data-test1="scope" class="_css-authcol-head"
											>打开</span
										>
									</template>
									<template slot-scope="scope">
										<div class="css-w100 css-fc css-jcsa">
											<!-- 角色文档权限编辑模式下，“打开”权限的复选框 -->
											<div
												v-if="DocAuth.editable == true"
												class="css-cb css-icon14 css-cp"
												@click.stop="switchdocitemsomeauth(scope.row, 'Open')"
												:class="{
													'mulcolor-interface-checkbox-selected':
														scope.row.Auth.Open == true,
												}"
											></div>
											<!-- //角色文档权限编辑模式下，“打开”权限的复选框 -->
											<div
												v-else
												:class="{
													'icon-checkbox-Selected-Disabled-dis-blue':
														scope.row.Auth.Open == true,
												}"
											></div>
										</div>
									</template>
								</el-table-column>

								<el-table-column
									:resizable="false"
									align="center"
									label="下载"
									min-width="46"
								>
									<template slot="header" slot-scope="scope">
										<span :data-test1="scope" class="_css-authcol-head"
											>下载</span
										>
									</template>
									<template slot-scope="scope">
										<div class="css-w100 css-fc css-jcsa">
											<div
												v-if="DocAuth.editable == true"
												class="css-cb css-icon14 css-cp"
												@click.stop="
													switchdocitemsomeauth(scope.row, 'DownLoad')
												"
												:class="{
													'mulcolor-interface-checkbox-selected':
														scope.row.Auth.DownLoad == true,
												}"
											></div>
											<div
												v-else
												:class="{
													'icon-checkbox-Selected-Disabled-dis-blue':
														scope.row.Auth.DownLoad == true,
												}"
											></div>
										</div>
									</template>
								</el-table-column>

								<el-table-column
									:resizable="false"
									align="center"
									label="分享"
									min-width="46"
								>
									<template slot="header" slot-scope="scope">
										<span :data-test1="scope" class="_css-authcol-head"
											>分享</span
										>
									</template>
									<template slot-scope="scope">
										<div class="css-w100 css-fc css-jcsa">
											<div
												v-if="DocAuth.editable == true"
												class="css-cb css-icon14 css-cp"
												@click.stop="switchdocitemsomeauth(scope.row, 'Share')"
												:class="{
													'mulcolor-interface-checkbox-selected':
														scope.row.Auth.Share == true,
												}"
											></div>
											<div
												v-else
												:class="{
													'icon-checkbox-Selected-Disabled-dis-blue':
														scope.row.Auth.Share == true,
												}"
											></div>
										</div>
									</template>
								</el-table-column>

								<el-table-column
									:resizable="false"
									align="center"
									label="订阅"
									min-width="46"
								>
									<template slot="header" slot-scope="scope">
										<span :data-test1="scope" class="_css-authcol-head"
											>订阅</span
										>
									</template>
									<template slot-scope="scope">
										<div class="css-w100 css-fc css-jcsa">
											<div
												v-if="DocAuth.editable == true"
												class="css-cb css-icon14 css-cp"
												@click.stop="
													switchdocitemsomeauth(scope.row, 'Suscribe')
												"
												:class="{
													'mulcolor-interface-checkbox-selected':
														scope.row.Auth.Suscribe == true,
												}"
											></div>
											<div
												v-else
												:class="{
													'icon-checkbox-Selected-Disabled-dis-blue':
														scope.row.Auth.Suscribe == true,
												}"
											></div>
										</div>
									</template>
								</el-table-column>
								<el-table-column
									:resizable="false"
									align="center"
									label="历史"
									min-width="46"
								>
									<template slot="header" slot-scope="scope">
										<span :data-test1="scope" class="_css-authcol-head"
											>查看历史</span
										>
									</template>
									<template slot-scope="scope">
										<div class="css-w100 css-fc css-jcsa">
											<div
												v-if="DocAuth.editable == true"
												class="css-cb css-icon14 css-cp"
												@click.stop="
													switchdocitemsomeauth(scope.row, 'History')
												"
												:class="{
													'mulcolor-interface-checkbox-selected':
														scope.row.Auth.History == true,
												}"
											></div>
											<div
												v-else
												:class="{
													'icon-checkbox-Selected-Disabled-dis-blue':
														scope.row.Auth.History == true,
												}"
											></div>
										</div>
									</template>
								</el-table-column>

								<el-table-column
									:resizable="false"
									align="center"
									label="修改"
									min-width="46"
								>
									<template slot="header" slot-scope="scope">
										<span :data-test1="scope" class="_css-authcol-head"
											>修改</span
										>
									</template>
									<template slot-scope="scope">
										<div class="css-w100 css-fc css-jcsa">
											<div
												v-if="DocAuth.editable == true"
												class="css-cb css-icon14 css-cp"
												@click.stop="switchdocitemsomeauth(scope.row, 'Update')"
												:class="{
													'mulcolor-interface-checkbox-selected':
														scope.row.Auth.Update == true,
												}"
											></div>
											<div
												v-else
												:class="{
													'icon-checkbox-Selected-Disabled-dis-blue':
														scope.row.Auth.Update == true,
												}"
											></div>
										</div>
									</template>
								</el-table-column>

								<el-table-column
									:resizable="false"
									align="center"
									label="删除"
									min-width="46"
								>
									<template slot="header" slot-scope="scope">
										<span :data-test1="scope" class="_css-authcol-head"
											>删除</span
										>
									</template>
									<template slot-scope="scope">
										<div class="css-w100 css-fc css-jcsa">
											<div
												v-if="DocAuth.editable == true"
												class="css-cb css-icon14 css-cp"
												@click.stop="switchdocitemsomeauth(scope.row, 'Delete')"
												:class="{
													'mulcolor-interface-checkbox-selected':
														scope.row.Auth.Delete,
												}"
											></div>
											<div
												v-else
												:class="{
													'icon-checkbox-Selected-Disabled-dis-blue':
														scope.row.Auth.Delete == true,
												}"
											></div>
										</div>
									</template>
								</el-table-column>
							</el-table>
							<!-- //表格体 -->
						</div>

						<!-- 功能权限展示体 -->
						<div
							v-else-if="currentshowauthtype == 'funcauth'"
							class="_css-rb-valid css-bsb css-b css-oya"
							id="id_rb_valid"
						>
							<!-- margin-top:8的多个权限module项 -->
							<!-- <CompsBaseModuleItem
                v-for="item in funcauths"
                :key="item.Bm_ModuleId"
                v-bind:obj="item"
                v-bind:editable="CompsBaseModuleItems_data.editable"
                class="_css-each-moduleauthitem"
                :class="item.checkstate"
              ></CompsBaseModuleItem> -->
							<CompsBaseFuncauths
								:treeData="funcauths"
								:editable="!CompsBaseModuleItems_data.editable"
							></CompsBaseFuncauths>
							<!-- //margin-top:8的多个权限module项 -->
						</div>
						<!-- //功能权限展示体 -->

						<!-- 模型权限展示体 -->
						<div
							v-else-if="currentshowauthtype == 'phaseauth'"
							class="_css-rb-valid css-fixedleftgbcolor css-fixtreeicon"
							id="id_rb_valid"
						>
							<!-- 表格体 -->
							<el-table
								v-if="modelTableData.length"
								ref="phaseauth"
								key="phaseauth"
								:header-cell-class-name="header_cell_class_name"
								:cell-class-name="cell_class_name"
								:border="true"
								:row-key="getRowKey"
								:data="modelTableData"
								:tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
								height="500"
								class="_css-table _css-customstyle"
								style="width: 100%"
							>
								<el-table-column
									fixed="left"
									:resizable="true"
									label="模型权限"
									width="196"
								>
									<template slot="header" slot-scope="scope">
										<span
											:data-test1="scope"
											class="_css-roleauthfolderheadcontainer"
										>
											<span class="_css-roleauthfolderheadspan">模型权限</span>
										</span>
									</template>
									<template slot-scope="scope">
										<span
											:title="scope.row.MenuName"
											class="_css-roleauthfname"
											>{{ scope.row.MenuName }}
										</span>
									</template>
								</el-table-column>

								<el-table-column
									:resizable="false"
									align="center"
									label="全选"
									min-width="46"
								>
									<template slot="header" slot-scope="scope">
										<span :data-test1="scope" class="_css-authcol-head"
											>全选</span
										>
									</template>
									<template slot-scope="scope">
										<div class="css-w100 css-fc css-jcsa" v-if="modelAuth.editable && scope.row.Buttons && scope.row.Buttons.length > 0">
											<el-checkbox
												class="btn-switch"
												@change="checkboxChange(scope.row, $event)"
												:disabled="!modelAuth.editable"
												:value="scope.row.Buttons.every(item => item.HasAuth === true)"
											></el-checkbox>
										</div>
										<div class="css-w100 css-fc css-jcsa" v-if="!modelAuth.editable && scope.row.Buttons && scope.row.Buttons.length > 0">
											<div
												v-if="scope.row.Buttons.every(item => item.HasAuth === true)"
												class=
													'icon-checkbox-Selected-Disabled-dis-blue'
											></div>
										</div>

									</template>
								</el-table-column>

								<el-table-column
									:resizable="false"
									align="center"
									label="查看"
									min-width="46"
								>
									<template slot="header" slot-scope="scope">
										<span :data-test1="scope" class="_css-authcol-head"
											>查看</span
										>
									</template>
									<template slot-scope="scope">
										<div class="css-w100 css-fc css-jcsa" v-if="modelAuth.editable">
											<el-checkbox
												class="btn-switch"
												@change="checkboxChangeHandle(scope.row, 'isHide', $event)"
												:disabled="!modelAuth.editable"
												v-model="scope.row.HasAuth"
											></el-checkbox>
										</div>
										<div class="css-w100 css-fc css-jcsa" v-if="!modelAuth.editable">
											<div
												v-if="scope.row.HasAuth"
												class=
													'icon-checkbox-Selected-Disabled-dis-blue'
											></div>
										</div>
									</template>
								</el-table-column>

								<el-table-column
									:resizable="false"
									align="center"
									label="上传"
									min-width="46"
								>
									<template slot="header" slot-scope="scope">
										<span :data-test1="scope" class="_css-authcol-head"
											>上传</span
										>
									</template>
									<template slot-scope="scope">
										<div class="css-w100 css-fc css-jcsa" v-if="modelAuth.editable && scope.row.Buttons && scope.row.Buttons.length > 0">
											<el-checkbox
												class="btn-switch"
												@change="checkboxChangeHandle(scope.row, 0, $event)"
												:disabled="!modelAuth.editable"
												v-model="scope.row.Buttons[0].HasAuth"
											></el-checkbox>
										</div>
										<div class="css-w100 css-fc css-jcsa" v-if="!modelAuth.editable && scope.row.Buttons && scope.row.Buttons.length > 0">
											<div
												v-if="scope.row.Buttons[0].HasAuth"
												class=
													'icon-checkbox-Selected-Disabled-dis-blue'
											></div>
										</div>
									</template>
								</el-table-column>

								<el-table-column
									:resizable="false"
									align="center"
									label="编辑"
									min-width="46"
								>
									<template slot="header" slot-scope="scope">
										<span :data-test1="scope" class="_css-authcol-head"
											>编辑</span
										>
									</template>
									<template slot-scope="scope">
										<div class="css-w100 css-fc css-jcsa" v-if="modelAuth.editable && scope.row.Buttons && scope.row.Buttons.length > 0">
											<el-checkbox
												class="btn-switch"
												@change="checkboxChangeHandle(scope.row, 1, $event)"
												:disabled="!modelAuth.editable"
												v-model="scope.row.Buttons[1].HasAuth"
											></el-checkbox>
										</div>
										<div class="css-w100 css-fc css-jcsa" v-if="!modelAuth.editable && scope.row.Buttons && scope.row.Buttons.length > 0">
											<div
												v-if="scope.row.Buttons[1].HasAuth"
												class=
													'icon-checkbox-Selected-Disabled-dis-blue'
											></div>
										</div>
									</template>
								</el-table-column>

								<el-table-column
									:resizable="false"
									align="center"
									label="删除"
									min-width="46"
								>
									<template slot="header" slot-scope="scope">
										<span :data-test1="scope" class="_css-authcol-head"
											>删除</span
										>
									</template>
									<template slot-scope="scope">
										<div class="css-w100 css-fc css-jcsa" v-if="modelAuth.editable && scope.row.Buttons && scope.row.Buttons.length > 0">
											<el-checkbox
												class="btn-switch"
												@change="checkboxChangeHandle(scope.row, 2, $event)"
												:disabled="!modelAuth.editable"
												v-model="scope.row.Buttons[2].HasAuth"
											></el-checkbox>
										</div>
										<div class="css-w100 css-fc css-jcsa" v-if="!modelAuth.editable && scope.row.Buttons && scope.row.Buttons.length > 0">
											<div
												v-if="scope.row.Buttons[2].HasAuth"
												class=
													'icon-checkbox-Selected-Disabled-dis-blue'
											></div>
										</div>
									</template>
								</el-table-column>

								<el-table-column
									:resizable="false"
									align="center"
									label="分享"
									min-width="46"
								>
									<template slot="header" slot-scope="scope">
										<span :data-test1="scope" class="_css-authcol-head"
											>分享</span
										>
									</template>
									<template slot-scope="scope">
										<div class="css-w100 css-fc css-jcsa" v-if="modelAuth.editable && scope.row.Buttons && scope.row.Buttons.length > 0">
											<el-checkbox
												class="btn-switch"
												@change="checkboxChangeHandle(scope.row, 3, $event)"
												:disabled="!modelAuth.editable"
												v-model="scope.row.Buttons[3].HasAuth"
											></el-checkbox>
										</div>
										<div class="css-w100 css-fc css-jcsa" v-if="!modelAuth.editable && scope.row.Buttons && scope.row.Buttons.length > 0">
											<div
												v-if="scope.row.Buttons[3].HasAuth"
												class=
													'icon-checkbox-Selected-Disabled-dis-blue'
											></div>
										</div>
									</template>
								</el-table-column>
							</el-table>
							<!-- //表格体 -->
						</div>
						<!-- //模型权限展示体 -->
						<!-- 档案权限展示体 -->
						<div
							v-else-if="currentshowauthtype == 'archivesauth'"
							class="_css-rb-valid css-fixedleftgbcolor css-fixtreeicon"
							id="id_rb_valid"
						>
							<!-- 表格体 -->
							<el-table
								v-if="archivesTableData.length"
								ref="archivesauth"
								key="archivesauth"
								:header-cell-class-name="header_cell_class_name"
								:cell-class-name="cell_class_name"
								:border="true"
								row-key="Id"
								:data="archivesTableData"
								:tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
								height="500"
								class="_css-table _css-customstyle"
								style="width: 100%"
							>
								<el-table-column
									fixed="left"
									:resizable="true"
									label="档案权限"
									width="196"
								>
									<template slot="header" slot-scope="scope">
										<span
											:data-test1="scope"
											class="_css-roleauthfolderheadcontainer"
										>
											<span class="_css-roleauthfolderheadspan">档案权限</span>
										</span>
									</template>
									<template slot-scope="scope">
										<span
											:title="scope.row.CategoryName"
											class="_css-roleauthfname"
											>{{ scope.row.CategoryName }}
										</span>
									</template>
								</el-table-column>

								<el-table-column
									:resizable="false"
									align="center"
									label="全选"
									min-width="46"
								>
									<template slot="header" slot-scope="scope">
										<span :data-test1="scope" class="_css-authcol-head"
											>全选</span
										>
									</template>
									<template slot-scope="scope">
										<div class="css-w100 css-fc css-jcsa" v-if="archivesAuth.editable">
											<el-checkbox
												class="btn-switch"
												@change="archivesCheckboxChange(scope.row, $event)"
												:value="Object.values(scope.row.ArchivesAuth).every(value => value === true)"
											></el-checkbox>
										</div>
										<div class="css-w100 css-fc css-jcsa" v-else>
											<div
												v-if="Object.values(scope.row.ArchivesAuth).every(value => value === true)"
												class='icon-checkbox-Selected-Disabled-dis-blue'>
											</div>
										</div>

									</template>
								</el-table-column>

								<el-table-column
									:resizable="false"
									align="center"
									label="编辑分类"
									min-width="46"
								>
									<template slot="header" slot-scope="scope">
										<span :data-test1="scope" class="_css-authcol-head"
											>编辑分类</span
										>
									</template>
									<template slot-scope="scope">
										<div class="css-w100 css-fc css-jcsa" v-if="archivesAuth.editable">
											<el-checkbox
												class="btn-switch"
												@change="archivesCheckboxChangeHandle(scope.row, 'Update', $event)"
												v-model="scope.row.ArchivesAuth.Update"
											></el-checkbox>
										</div>
										<div class="css-w100 css-fc css-jcsa" v-else>
											<div
												v-if="scope.row.ArchivesAuth.Update"
												class='icon-checkbox-Selected-Disabled-dis-blue'>
											</div>
										</div>
									</template>
								</el-table-column>

								<el-table-column
									:resizable="false"
									align="center"
									label="分类可见"
									min-width="46"
								>
									<template slot="header" slot-scope="scope">
										<span :data-test1="scope" class="_css-authcol-head"
											>分类可见</span
										>
									</template>
									<template slot-scope="scope">
										<div class="css-w100 css-fc css-jcsa" v-if="archivesAuth.editable">
											<el-checkbox
												class="btn-switch"
												@change="archivesCheckboxChangeHandle(scope.row, 'Visable', $event)"
												v-model="scope.row.ArchivesAuth.Visable"
											></el-checkbox>
										</div>
										<div class="css-w100 css-fc css-jcsa" v-else>
											<div
												v-if="scope.row.ArchivesAuth.Visable"
												class='icon-checkbox-Selected-Disabled-dis-blue'>
											</div>
										</div>
									</template>
								</el-table-column>

								<el-table-column
									:resizable="false"
									align="center"
									label="新建档案"
									min-width="46"
								>
									<template slot="header" slot-scope="scope">
										<span :data-test1="scope" class="_css-authcol-head"
											>新建档案</span
										>
									</template>
									<template slot-scope="scope">
										<div class="css-w100 css-fc css-jcsa" v-if="archivesAuth.editable">
											<el-checkbox
												class="btn-switch"
												@change="archivesCheckboxChangeHandle(scope.row, 'Create', $event)"
												v-model="scope.row.ArchivesAuth.Create"
											></el-checkbox>
										</div>
										<div class="css-w100 css-fc css-jcsa" v-else>
											<div
												v-if="scope.row.ArchivesAuth.Create"
												class='icon-checkbox-Selected-Disabled-dis-blue'>
											</div>
										</div>
									</template>
								</el-table-column>

								<el-table-column
									:resizable="false"
									align="center"
									label="预览文件"
									min-width="46"
								>
									<template slot="header" slot-scope="scope">
										<span :data-test1="scope" class="_css-authcol-head"
											>预览文件</span
										>
									</template>
									<template slot-scope="scope">
										<div class="css-w100 css-fc css-jcsa" v-if="archivesAuth.editable">
											<el-checkbox
												class="btn-switch"
												@change="archivesCheckboxChangeHandle(scope.row, 'Privew', $event)"
												v-model="scope.row.ArchivesAuth.Privew"
											></el-checkbox>
										</div>
										<div class="css-w100 css-fc css-jcsa" v-else>
											<div
												v-if="scope.row.ArchivesAuth.Privew"
												class='icon-checkbox-Selected-Disabled-dis-blue'>
											</div>
										</div>
									</template>
								</el-table-column>

								<el-table-column
									:resizable="false"
									align="center"
									label="下载文件"
									min-width="46"
								>
									<template slot="header" slot-scope="scope">
										<span :data-test1="scope" class="_css-authcol-head"
											>下载文件</span
										>
									</template>
									<template slot-scope="scope">
										<div class="css-w100 css-fc css-jcsa" v-if="archivesAuth.editable">
											<el-checkbox
												class="btn-switch"
												@change="archivesCheckboxChangeHandle(scope.row, 'Download', $event)"
												v-model="scope.row.ArchivesAuth.Download"
											></el-checkbox>
										</div>
										<div class="css-w100 css-fc css-jcsa" v-else>
											<div
												v-if="scope.row.ArchivesAuth.Download"
												class='icon-checkbox-Selected-Disabled-dis-blue'>
											</div>
										</div>
									</template>
								</el-table-column>

								<el-table-column
									:resizable="false"
									align="center"
									label="删除档案"
									min-width="46"
								>
									<template slot="header" slot-scope="scope">
										<span :data-test1="scope" class="_css-authcol-head"
											>删除档案</span
										>
									</template>
									<template slot-scope="scope">
										<div class="css-w100 css-fc css-jcsa" v-if="archivesAuth.editable">
											<el-checkbox
												class="btn-switch"
												@change="archivesCheckboxChangeHandle(scope.row, 'Delete', $event)"
												v-model="scope.row.ArchivesAuth.Delete"
											></el-checkbox>
										</div>
										<div class="css-w100 css-fc css-jcsa" v-else>
											<div
												v-if="scope.row.ArchivesAuth.Delete"
												class='icon-checkbox-Selected-Disabled-dis-blue'>
											</div>
										</div>
									</template>
								</el-table-column>

								<el-table-column
									:resizable="false"
									align="center"
									label="档案导出"
									min-width="46"
								>
									<template slot="header" slot-scope="scope">
										<span :data-test1="scope" class="_css-authcol-head"
											>档案导出</span
										>
									</template>
									<template slot-scope="scope">
										<div class="css-w100 css-fc css-jcsa" v-if="archivesAuth.editable">
											<el-checkbox
												class="btn-switch"
												@change="archivesCheckboxChangeHandle(scope.row, 'Export', $event)"
												v-model="scope.row.ArchivesAuth.Export"
											></el-checkbox>
										</div>
										<div class="css-w100 css-fc css-jcsa" v-else>
											<div
												v-if="scope.row.ArchivesAuth.Export"
												class='icon-checkbox-Selected-Disabled-dis-blue'>
											</div>
										</div>
									</template>
								</el-table-column>
							</el-table>
							<!-- //表格体 -->
						</div>
						<!-- 档案权限展示体 -->
					</div>
					<!-- //底部 -->
				</div>
				<!-- //显示角色权限的右侧，如果当前选择了一个角色的话。 -->
				<!-- 未选择角色，提示用户选择左侧的角色 -->
				<div class="_css-right" v-else>
					<div class="_css-noneroleselectedtip">
						<div class="_css-tipcenter">
							<div class="_css-tipicon illustration_empty_document"></div>
							<div class="_css-tipmiddle">无角色权限</div>
							<div class="_css-tipbottom">请选择左侧角色查看权限</div>
						</div>
					</div>
				</div>
				<!-- //未选择角色，提示用户选择左侧的角色 -->
			</div>
			<!-- //主体内部部分 -->
		</section>
		<!-- //主体部分 -->

		<CompsUsersSearch
			@oncancel="userSearchCancel"
			@onok="userSearchOk"
			:roleId="CompsEloContextMenu_roleid"
			v-if="false && isaddingmember"
			:innerWidth="-1"
			:hovercolor="'rgba(0,0,0,0.15)'"
		></CompsUsersSearch>

		<CompsSimpleSearchList
			v-if="isaddingmember"
			@oncancel="isaddingmember = false"
			:title="getAddingToRoleTitle()"
			:projectId="$staticmethod._Get('organizeId')"
			showType="addToRole"
			:zIndex="2"
			@onok="addToRole_OK"
			@onok_multiple="addToRole_OK_Multiple"
			:willnotshowUsers="roleusers"
			:init_ismultiple="true"
		></CompsSimpleSearchList>

		<!-- 带有两级的上下文菜单 -->
		<div
			class="_css-roleuser-operates"
			v-if="extdata._showing_usercontext"
			v-clickOutClose="hideusercontext"
			:style="style_roleuser_operates()"
		>
			<!-- 移动成员 -->
			<div
				v-if="isnotequal(extdata._operating_userId)"
				class="_css-roleuser-oi"
				@mouseenter="useroper_move()"
				@mouseleave="useroper_hide()"
			>
				<div class="_css-roleuser-oiicon icon-suggested-close-fill"></div>
				<div class="_css-roleuser-oitext">移动到</div>
				<div class="_css-roleuser-oirighticon icon-arrow-right_outline"></div>
			</div>
			<div v-else class="_css-roleuser-oi _css-dis">
				<div class="_css-roleuser-oiicon icon-suggested-close-fill"></div>
				<div class="_css-roleuser-oitext">移动到</div>
				<div class="_css-roleuser-oirighticon icon-arrow-right_outline"></div>
			</div>
			<!-- //移动成员 -->

			<div
				class="_css-roleuser-oi"
				@mouseenter="useroper_copy()"
				@mouseleave="useroper_hide()"
			>
				<div class="_css-roleuser-oiicon icon-interface-Merge"></div>
				<div class="_css-roleuser-oitext">复制到</div>
				<div class="_css-roleuser-oirighticon icon-arrow-right_outline"></div>
			</div>

			<!-- 删除成员 -->
			<div
				class="_css-roleuser-oi"
				v-if="isnotequal(extdata._operating_userId)"
				@click.stop="rm_operaing_user()"
			>
				<div class="_css-roleuser-oiicon icon-interface-model-delete"></div>
				<div class="_css-roleuser-oitext">删除成员</div>
			</div>
			<div class="_css-roleuser-oi _css-dis" v-else>
				<div class="_css-roleuser-oiicon icon-interface-model-delete"></div>
				<div class="_css-roleuser-oitext">删除成员</div>
			</div>
			<!-- //删除成员 -->
		</div>
		<div
			class="_css-roleuser-operates2"
			@mouseenter="useroper_show()"
			v-if="extdata._showing_userroletargets"
			:style="style_roleuser_targets()"
		>
			<div
				class="_css-roleuser-oi"
				@click.stop="moveorcopyToTarget(role)"
				v-for="role in extdata.targetRoles"
				:key="role.RoleId"
				:title="role.FullName"
			>
				<div class="_css-roleuser-oitext2">{{ role.FullName }}</div>
			</div>
		</div>
		<!-- //带有两级的上下文菜单 -->
	</section>
</template>
<script>
// 国产化改版权限这块后端的逻辑是项目管理员不受权限控制，默认是有所有权限、之前8888的逻辑是项目管理员的菜单也受权限控制
import CompsTableControl from "@/components/CompsAuth/CompsTableControl";
import CompsBaseModuleItem from "@/components/CompsAuth/CompsBaseModuleItem";
import CompsBaseFuncauths from "@/components/CompsAuth/CompsBaseFuncauths";
import CompsTopTitleBtns from "@/components/CompsAuth/CompsTopTitleBtns";
import CompsEloButton from "@/components/CompsElOverride/CompsEloButton";
import CompsEloContextMenu from "@/components/CompsElOverride/CompsEloContextMenu";
import CompsUsersSearch from "@/components/CompsAuth/CompsUsersSearch";
import CompsSimpleSearchList from "@/components/CompsCommon/CompsSimpleSearchList";

/*
events:
    onmounted('roleauth')
*/
export default {
	name: "Main",
	components: {
		CompsTableControl,
		CompsBaseModuleItem,
		CompsBaseFuncauths,
		CompsTopTitleBtns,
		CompsEloButton,
		CompsEloContextMenu,
		CompsUsersSearch,
		CompsSimpleSearchList,
	},
	directives: {
		onfocus: {
			inserted: function (el) {
				el.focus();
				el.select();
			},
		},
	},
	data() {
		return {
			bIsProjectComMgr: false, // 当前操作人是当前项目所属机构的机构管理员

			// 上传/新建，打开，下载，分享，订阅，查看历史，修改，删除
			docauthsort: [
				"Search",
				"Upload",
				"Open",
				"DownLoad",
				"Share",
				"Suscribe",
				"History",
				"Update",
				"Delete",
			],
			extdata: {
				targetRoles: [
					{
						RoleId: "1",
						FullName: "角色1",
					},
					{
						RoleId: "2",
						FullName: "角色2",
					},
					{
						RoleId: "3",
						FullName: "角色3",
					},
					{
						RoleId: "4",
						FullName: "角色4",
					},
					{
						RoleId: "5",
						FullName: "角色5",
					},
				],
				_operating_type: "", // move copy
				_operating_userId: "",
				_operating_userRealName: "",
				_showing_usercontext: false,
				_contextbtns_p_x: 0, // 移动成员、复制成员
				_contextbtns_p_y: 0,
				_showing_userroletargets: false,
				_rolelist_x: 0, // 目标角色列表
				_rolelist_y: 0,
				IsOrganizeManager: false,
			},
			CompsEloContextMenu_roleid: "", // 上下文菜单操的角色ID
			CompsEloContextMenu_rolename: "", // 上下文菜单操的在角色名称
			CompsEloContextMenu_visible: false,
			CompsEloContextMenu_p_x: 0,
			CompsEloContextMenu_p_y: 0,
			CompsEloContextMenu_fulldata: {
				items: [
					{ id: "openrole", text: "查看角色成员" },
					{ id: "editrolename", text: "编辑角色名称" },
					{ id: "disablerole", text: "禁用角色" },
					{ id: "deleterole", text: "删除角色" },
				],
			}, // 上下文菜单数据条目（全部）
			CompsEloContextMenu_admindata: {
				items: [{ id: "openrole", text: "查看角色成员" }],
			},
			// 上下文菜单数据条目（仅管理员）

			CompsEloContextMenu_Publicdata: {
				items: [
					{ id: "disablerole", text: "禁用角色" },
					{ id: "openrole", text: "查看角色成员" },
				],
			},

			CompsEloContextMenu_data: {
				items: [
					{ id: "openrole", text: "查看角色成员" },
					{ id: "editrolename", text: "编辑角色名称" },
					{ id: "disablerole", text: "禁用角色" },
					{ id: "deleterole", text: "删除角色" },
				],
			}, // 上下文菜单数据条目
			isaddingnewrole: false, // 是否正在新增角色
			addingnewrolename: "", // 正在新增的角色名称
			docauths: [], // 进行过修改操作的所有当前角色的文件夹权限数据，每一个元素的格式为{RoleId:'', FolderId:'', AuthName:'', AuthValue:''}
			DocAuth: {
				editable: false,
			},
			modelAuth: {
				editable: false,
			},
			archivesAuth: {
				editable: false,
			},
			CompsBaseModuleItems_data: {
				// 所有的权限module项的统一配置
				editable: false, // 所有的权限module项是否处于正在编辑状态
				cachedata: [],
			},
			CompsTopTitleBtns_data: {
				title: "",
				visiable: false, // 顶部对话框按钮是否显示
			},
			funcauths: [], // 功能权限数据

			phaseauths: [], // 模型权限数据

			roleauthselecteditemid: "funcauth",
			roleauthtab: [
				{
					id: "funcauth",
					text: "功能权限",
					extrabtns: [
						{ btnid: "funcauthedit", classname: "", btntext: "编辑" },
					],
				},
				{
					id: "docauth",
					text: "文档权限",
					extrabtns: [{ btnid: "docauthedit", classname: "", btntext: "编辑" }],
				},
				{
					id: "phaseauth",
					text: "模型权限",
					extrabtns: [
						{ btnid: "phaseauthedit", classname: "", btntext: "编辑" },
					],
				},
				{
					id: "archivesauth",
					text: "档案权限",
					extrabtns: [{ btnid: "archivesauthedit", classname: "", btntext: "编辑" }],
				},
			], // 标签页上的内容：功能权限、文档权限
			tableData: [], // 所有根文件夹权限数据
			tableData_cache: [], // 所有根文件夹权限数据——缓存
			foldertableData: [], // 某一文件夹的下文件夹权限数据
			foldertableData_cache: [], // 某一文件夹下的文件夹权限数据——缓存
			editing_roleid: "", // 正在编辑名称的角色id
			roles: [], // 角色数据
			showroletype: "roles", // 左侧角色列表状态。roles, members

			// 当前正在显示什么权限，是功能权限还是文档权限还是模型权限
			// 新增：phaseauth
			currentshowauthtype: "funcauth", // funcauth, docauth 当前显示什么权限，默认显示功能权限

			currentshowrole: "", // 当前显示的哪个角色，默认为空字符串
			roleusers: [], // 当前显示的某个角色下的所有人员
			roleusers_cache: [], // roleusers 备份
			iseditingmembers: false, // 当前正在维护某个角色下的成员。
			rmRoleIds: [], // 已经移除，可能即将生效的所有成员ID
			addRoleIds: [], // 已经添加，可能即将生产的所有成员ID
			isaddingmember: false, // 正在添加人员，即正在显示人员备选对话框
			saveClickItem: {}, // 保存点击角色的信息
			modelTableData: [],
			archivesTableData: []
		};
	},
	mounted() {
		var _this = this;
		window.rolevue = _this;
		_this.$emit("onmounted", "roleauth");
		_this.getRoles();

		// zzh: 刚开始不加载任何权限数据界面
		// 如果当前显示的tab页为“文档权限”，则调用 loadr1ootpriviges
		//_this.loadroo1tpriviges();
		//debugger;
		// 在projectBoot.vue中对是否为项目管理员存入了缓存，在这里直接取值
		this.$staticmethod._Get("currentisProjectManager") == "1"
			? (_this.bIsProjectComMgr = true)
			: (_this.bIsProjectComMgr = false);
	},
	computed: {},
	methods: {
		// 模型权限全选按钮
		checkboxChange (data, e) {
			if (e) {
				data.HasAuth = true
				data.Buttons.forEach(v => {
					v.HasAuth = true
				})
			} else {
				data.HasAuth = false
				data.Buttons.forEach(v => {
					v.HasAuth = false
				})
			}
			this.setHasAuth(data.children, -1, e)
		},

		setHasAuth (node, index, e) {
			node.forEach(v => {
				if (index === -1) {
					v.HasAuth = e
					v.Buttons.forEach(item => {
						item.HasAuth = e
					})
				} else if (index === 'isHide') {
					v.HasAuth = e
				} else {
					v.Buttons[index].HasAuth = e
				}
				if (v.children) {
					this.setHasAuth(v.children, index, e)
				}
			})
		},


		checkboxChangeHandle (data, index, e) {
			this.setHasAuth(data.children, index, e)
		},

		getRowKey (row) {
			return `${row.MenuName}-${row.MenuLevel}`
		},
		_stopPropagation(ev) {
			ev && ev.stopPropagation && ev.stopPropagation();
		},

		isnotequal(userid) {
			var _this = this;
			_this.$staticmethod.debugshowlog(userid);
			_this.$staticmethod.debugshowlog(_this.$staticmethod.Get("UserId"));
			// 项目管理员中不能移除和删除自己
			if (
				userid == _this.$staticmethod.Get("UserId") &&
				_this.CompsEloContextMenu_rolename == "项目管理员"
			) {
				return false;
			}


			// IsOrganizeManager = true 机构管理员、项目管理员、不可以操作
			if(this.extdata.IsOrganizeManager){
				return false;
			}

			// 项目管理员中，自己不是当前项目的所属机构的机构管理员 return false.
			if (_this.CompsEloContextMenu_rolename == "项目管理员") {
				if (_this.bIsProjectComMgr) {
					// 机构管理员可以操作
					return true;
				} else {
					// 非机构管理员不可以操作
					return false;
				}
			} else {
				// 可以操作非项目管理员角色。
				return true;
			}
		},

		CopyUserToRoleAndRefresh(role) {
			var _this = this;
			_this
				.$axios({
					method: "post",
					url: `${_this.$urlPool.RoleAddUser}?token=${this.$staticmethod.Get(
						"Token"
					)}`,
					data: {
						OrganizeId: _this.$staticmethod._Get("organizeId"),
						RoleId: role.RoleId,
						UserId: _this.extdata._operating_userId,
					},
				})
				.then((x) => {
					if (x.status == 200) {
						if (x.data.Ret > 0) {
							// 关闭
							_this.isaddingmember = false;
							_this.$message.success("复制成功！");
							// 刷新
							_this.getRoleUsers();
						} else if (x.data.Ret == -20001) {
							_this.$message.error(x.data.Msg);
						} else {
							console.error(x.data);
						}
					} else {
						_this.$message.error(`服务器错误${x.status}`);
					}
				})
				.catch((x) => {
					console.error(x);
				});
		},

		MoveUserToRoleAndRefresh(role) {
			console.log(role, "====");
			var _this = this;
			_this
				.$axios({
					method: "post",
					url: `${_this.$urlPool.RoleMoveUser}?token=${_this.$staticmethod.Get(
						"Token"
					)}`,
					data: {
						OrganizeId: _this.$staticmethod._Get("organizeId"),
						SourceRoleId: _this.CompsEloContextMenu_roleid,
						TargetRoleId: role.RoleId,
						UserId: this.extdata._operating_userId,

						// RoleId: role.RoleId,
						// RoleIdFrom: _this.CompsEloContextMenu_roleid,   // 角色ID
						// RmUserIds: _this.extdata._operating_userId,  //
						// AddUserIds: _this.extdata._operating_userId
					},
				})
				.then((x) => {
					if (x.status == 200) {
						if (x.data.Ret > 0) {
							// 关闭
							_this.isaddingmember = false;
							_this.$message.success("移动成功！");
							// 刷新
							_this.getRoleUsers();
						} else {
							console.error(x.data);
						}
					} else {
						_this.$message.error(`服务器错误${x.status}`);
					}
				})
				.catch((x) => {
					console.error(x);
				});
		},

		MoveOrCopyAndRefresh(role) {
			var _this = this;
			if (_this.extdata._operating_type == "copy") {
				_this.CopyUserToRoleAndRefresh(role);
			} else {
				_this.MoveUserToRoleAndRefresh(role);
			}
		},
		moveorcopyToTarget(role) {
			var _this = this;
			// 操作确认
			_this
				.$confirm(
					`确认${
						_this.extdata._operating_type == "copy" ? "复制" : "移动"
					}成员【${_this.extdata._operating_userRealName}】到角色【${
						role.FullName
					}】`,
					`${
						_this.extdata._operating_type == "copy" ? "复制" : "移动"
					}操作确认`,
					{
						confirmButtonText: "确认",
						cancelButtonText: "取消",
						type: "warning",
					}
				)
				.then((x) => {
					_this.MoveOrCopyAndRefresh(role);
				})
				.catch((x) => {});
		},

		RemoveAndRefresh(userId, roleId) {
			var _this = this;
			// 发送 ajax 请求，然后关闭对话框，刷新
			_this
				.$axios({
					method: "post",
					url: `${
						_this.$urlPool.RoleRemoveUser
					}?token=${_this.$staticmethod.Get("Token")}`,
					data: {
						RoleId: roleId,
						UserId: userId,
						OrganizeId: _this.$staticmethod._Get("organizeId"),
					},
				})
				.then((x) => {
					if (x.status == 200) {
						if (x.data.Ret > 0) {
							_this.isaddingmember = false;
							_this.$message.success("删除成功！");
							// 刷新
							_this.getRoleUsers();
						} else if (x.data.Ret == -20001) {
							_this.$message.error(x.data.Msg);
						} else {
							console.error(x.data);
						}
					} else {
						_this.$message.error(`服务器错误${x.status}`);
					}
				})
				.catch((x) => {
					console.error(x);
				});
		},

		rm_operaing_user() {
			// 操作提示
			var _this = this;
			_this
				.$confirm(
					`确认从该角色中移除成员【${_this.extdata._operating_userRealName}】？`,
					"操作确认",
					{
						confirmButtonText: "确认",
						cancelButtonText: "取消",
						type: "warning",
					}
				)
				.then((x) => {
					_this.RemoveAndRefresh(
						_this.extdata._operating_userId,
						_this.CompsEloContextMenu_roleid
					);
				})
				.catch((x) => {});
		},

		useroper_show() {
			var _this = this;
			_this.extdata._showing_userroletargets = true;
		},

		useroper_move() {
			var _this = this;
			_this.extdata._operating_type = "move";

			// 位置
			_this.extdata._rolelist_x = _this.extdata._contextbtns_p_x + 140;
			_this.extdata._rolelist_y = _this.extdata._contextbtns_p_y + 0;

			// 本身高度： 40 * n + 8
			// console.log(document.body.clientHeight);
			// console.log(_this.extdata._rolelist_y);

			if (
				_this.extdata._rolelist_y >
				document.body.clientHeight - 40 * _this.extdata.targetRoles.length + 8
			) {
				_this.extdata._rolelist_y =
					document.body.clientHeight -
					40 * _this.extdata.targetRoles.length +
					8;
			}

			// 显示
			_this.extdata._showing_userroletargets = true;
		},

		useroper_copy() {
			var _this = this;
			_this.extdata._operating_type = "copy";

			// 位置
			_this.extdata._rolelist_x = _this.extdata._contextbtns_p_x + 140;
			_this.extdata._rolelist_y = _this.extdata._contextbtns_p_y + 40;

			// 本身高度： 40 * n + 8
			// console.log(document.body.clientHeight);
			// console.log(_this.extdata._rolelist_y);

			if (
				_this.extdata._rolelist_y >
				document.body.clientHeight - 40 * _this.extdata.targetRoles.length - 8
			) {
				_this.extdata._rolelist_y =
					document.body.clientHeight -
					40 * _this.extdata.targetRoles.length -
					8;
			}

			// 显示
			_this.extdata._showing_userroletargets = true;
		},

		useroper_hide() {
			var _this = this;
			_this.extdata._showing_userroletargets = false;
		},

		hideusercontext() {
			var _this = this;
			_this.extdata._showing_usercontext = false;
			_this.extdata._showing_userroletargets = false;
		},

		// 显示成员的上下文菜单
		showusercontent(userId, realName, IsOrganizeManager, ev) {
			var _this = this;

			_this.extdata.IsOrganizeManager = IsOrganizeManager;
			_this.extdata._operating_userId = userId;
			_this.extdata._operating_userRealName = realName;
			_this.set_usercontext_pos(140, 128, ev);
			_this.extdata._showing_usercontext = true;
		},

		style_roleuser_targets() {
			var _this = this;
			var _s = {};
			_s["top"] = `${_this.extdata._rolelist_y}px`;
			_s["left"] = `${_this.extdata._rolelist_x}px`;
			return _s;
		},

		style_roleuser_operates() {
			var _this = this;
			var _s = {};
			_s["top"] = `${_this.extdata._contextbtns_p_y}px`;
			_s["left"] = `${_this.extdata._contextbtns_p_x}px`;
			return _s;
		},

		set_usercontext_pos(contextwidth, contextheight, ev) {
			var _this = this;
			_this.extdata._contextbtns_p_x =
				ev.clientX > document.body.clientWidth - contextwidth
					? document.body.clientWidth - contextwidth
					: ev.clientX;
			_this.extdata._contextbtns_p_y =
				ev.clientY > document.body.clientHeight - contextheight
					? document.body.clientHeight - contextheight
					: ev.clientY;
		},

		// 选择完多个成员后，点击确认
		addToRole_OK_Multiple(userIds) {
			var _this = this;
			if (!userIds || userIds.length <= 0) {
				_this.$message.error("请先选择成员");
				return;
			}

			// 逐个判重
			var i = 0;
			for (i = 0; i < userIds.length; i++) {
				var hasIndex = _this.roleusers.findIndex((x) => x.UserId == userIds[i]);
				if (hasIndex >= 0) {
					_this.$message.error(
						`成员${_this.roleusers[hasIndex].RealName}已存在，请勿重复添加`
					);
					return;
				}
			}

			// 操作确认
			_this
				.$confirm("确认添加这些成员？", "操作确认", {
					confirmButtonText: "确定",
					cancelButtonText: "取消",
					type: "warning",
				})
				.then((x) => {
					var _AddUserIds = userIds.join(",");

					// 发送 ajax 请求，然后关闭对话框，刷新，参考 addToRole_OK
					_this
						.$axios({
							method: "post",
							url: `${
								_this.$urlPool.RoleAddUser
							}?token=${this.$staticmethod.Get("Token")}`,
							data: {
								OrganizeId: _this.$staticmethod._Get("organizeId"),
								RoleId: _this.CompsEloContextMenu_roleid,
								UserId: _AddUserIds,
							},
						})
						.then((x) => {
							if (x.status == 200) {
								if (x.data.Ret > 0) {
									_this.isaddingmember = false;
									_this.$message.success("添加成功！");
									// 刷新
									_this.getRoleUsers();
								} else if (x.data.Ret == -20001) {
									_this.$message.error(x.data.Msg);
								} else {
									console.error(x.data);
								}
							} else {
								_this.$message.error(`服务器错误${x.status}`);
							}
						})
						.catch((x) => {
							console.error(x);
						});
				});
		},

		addToRole_OK(userId, userName) {
			// 弹出提示：确认添加该成员到角色中？
			//console.log(userId, userName);
			var _this = this;
			// 判空
			if (!userId) {
				_this.$message.error("请先选择成员");
				return;
			}

			// 判重
			var hasIndex = _this.roleusers.findIndex((x) => x.UserId == userId);
			if (hasIndex >= 0) {
				_this.$message.error("当前成员已存在，请勿重复添加");
				return;
			}

			_this
				.$confirm("确认添加该成员？", "操作确认", {
					confirmButtonText: "确定",
					cancelButtonText: "取消",
					type: "warning",
				})
				.then((x) => {
					// 发送 ajax 请求，然后关闭对话框，刷新
					_this
						.$axios({
							method: "post",
							url: `${_this.$configjson.webserverurl}/api/User/User/SaveRoleMembersChange`,
							data: _this.$qs.stringify({
								Token: _this.$staticmethod.Get("Token"),
								RoleId: _this.CompsEloContextMenu_roleid,
								RmUserIds: "",
								AddUserIds: userId,
								OrganizeId: _this.$staticmethod._Get("organizeId"),
							}),
						})
						.then((x) => {
							if (x.status == 200) {
								if (x.data.Ret > 0) {
									_this.isaddingmember = false;
									_this.$message.success("添加成功！");
									// 刷新
									_this.getRoleUsers();
								} else if (x.data.Ret == -20001) {
									_this.$message.error(x.data.Msg);
								} else {
									console.error(x.data);
								}
							} else {
								_this.$message.error(`服务器错误${x.status}`);
							}
						})
						.catch((x) => {
							console.error(x);
						});
				})
				.catch((x) => {});
		},

		getAddingToRoleTitle() {
			var _this = this;
			return `添加成员到【${_this.CompsEloContextMenu_rolename}】`;
		},

		// 获取角色下的人员
		getRoleUsers() {
			var _this = this;
			let _Token = _this.$staticmethod.Get("Token");
			let ProjectId = _this.$staticmethod._Get("organizeId");
			let _OrganizeId = _this.$staticmethod._Get("_OrganizeId");
			let Url = `${this.$urlPool.GetUserPaged}?PageNum=1&PageSize=3000&KeyWord=&OrganizeId=${ProjectId}&searchType=0&RoleId=${_this.CompsEloContextMenu_roleid}&Token=${_Token}`;

			// 根据当前指定的角色，获取所有人员，
			_this
				.$axios({
					method: "get",
					url: Url,
				})
				.then((x) => {
					// 之后设置给 roleusers， 设置 showroletype 及 CompsEloContextMenu_visible
					if (x.status == 200 && x.data.Ret > 0) {
						_this.roleusers = x.data.Data.list;
						_this.showroletype = "members";
						_this.rmRoleIds = [];
						_this.addRoleIds = [];
						_this.CompsEloContextMenu_visible = false;
					} else if (_this.$configjson.debugmode == "1") {
						console.warn(x);
					}
				})
				.catch((x) => {
					if (_this.$configjson.debugmode == "1") {
						console.warn(x);
					}
				});
		},
		userSearchOk(list) {
			var _this = this;
			_this.isaddingmember = false;

			// 确保 list 均存在于 _this.roleusers 中
			// 确保 list 均存在于 _this.addRoleIds 中
			if (!list || list.length == 0) {
				return;
			}

			for (var i = 0; i < list.length; i++) {
				var ifhas = _this.roleusers.filter((x) => x.UserId == list[i].UserId);
				if (ifhas.length == 0) {
					//_this.roleusers.push(list[i]);
					_this.roleusers.unshift(list[i]);
				}

				if (_this.addRoleIds.indexOf(list[i].UserId) < 0) {
					_this.addRoleIds.push(list[i].UserId);
				}
			}
		},

		// 关闭已弹出的添加角色成员的成员列表窗口。
		userSearchCancel() {
			var _this = this;
			_this.isaddingmember = false;
		},

		roleuser_add() {
			var _this = this;
			_this.isaddingmember = true;
			_this.hideusercontext();
		},

		roleuser_remove(userId) {
			var _this = this;
			if (_this.rmRoleIds.indexOf(userId) < 0) {
				_this.rmRoleIds.push(userId);
			}
			_this.roleusers = _this.roleusers.filter((x) => x.UserId != userId);
		},

		btnrolemembersave_click() {
			var _this = this;

			// 弹出确认保存对话框
			_this
				.$confirm("确定保存角色成员改动？", "角色成员", {
					confirmButtonText: "确定",
					cancelButtonText: "取消",
					type: "warning",
				})
				.then((x) => {
					// 发请求，保存角色成员改动。
					var rmRoleIdsStr = "";
					for (var i = 0; i < _this.rmRoleIds.length; i++) {
						rmRoleIdsStr += _this.rmRoleIds[i];
						if (i != _this.rmRoleIds.length - 1) {
							rmRoleIdsStr += ",";
						}
					}

					var addRoleIdsStr = "";
					for (var i = 0; i < _this.addRoleIds.length; i++) {
						addRoleIdsStr += _this.addRoleIds[i];
						if (i != _this.addRoleIds.length - 1) {
							addRoleIdsStr += ",";
						}
					}

					//
					_this
						.$axios({
							method: "post",
							url: `${_this.$configjson.webserverurl}/api/User/User/SaveRoleMembersChange`,
							data: _this.$qs.stringify({
								Token: _this.$staticmethod.Get("Token"),
								RoleId: _this.CompsEloContextMenu_roleid,
								RmUserIds: rmRoleIdsStr,
								AddUserIds: addRoleIdsStr,
								OrganizeId: _this.$staticmethod._Get("organizeId"),
							}),
						})
						.then((x) => {
							if (x.status == 200 && x.data.Ret > 0) {
								_this.$message({
									message: "保存成功",
									type: "success",
								});
								_this.iseditingmembers = false;
							} else if (x.status == 200 && x.data.Ret == -20001) {
								_this.$message.error(x.data.Msg);
							} else if (_this.$configjson.debugmode == "1") {
								console.warn(x);
							}
							// done
						})
						.catch((x) => {
							if (_this.$configjson.debugmode == "1") {
								console.warn(x);
							}
							//done
						});
				})
				.catch((x) => {
					//done
				});
		},

		btnrolemembercancel_click() {
			var _this = this;
			// 恢复 roleusers
			_this.roleusers = _this.$staticmethod.DeepCopy(_this.roleusers_cache);
			_this.iseditingmembers = false;
		},

		btnrolememberedit_click() {
			var _this = this;
			// 清空：已删除角色 id 列表
			_this.rmRoleIds = [];
			_this.addRoleIds = [];
			// 备份 roleusers
			_this.roleusers_cache = _this.$staticmethod.DeepCopy(_this.roleusers);
			_this.iseditingmembers = true;
		},

		backtoroleslist() {
			var _this = this;
			_this.showroletype = "roles";
			_this.hideusercontext();
		},

		CompsEloContextMenu_click(itemid) {
			var _this = this;
			if (itemid == "deleterole") {
				// 弹出操作确认。
				_this
					.$confirm("确定删除该角色？", "操作确认", {
						confirmButtonText: "确认",
						cancelButtonText: "取消",
						type: "warning",
					})
					.then((x) => {
						_this
							.$axios({
								method: "post",
								url: `${_this.$urlPool.RoleDelete}?roleId=${
									_this.CompsEloContextMenu_roleid
								}&token=${_this.$staticmethod.Get("Token")}`,
							})
							.then((x) => {
								if (x.status == "200" && x.data.Ret == 1) {
									_this.getRoles();
									_this.$message({
										message: "删除成功",
										type: "success",
									});
								} else if (_this.$configjson.debugmode == "1") {
									console.warn(x);
								}
								_this.CompsEloContextMenu_visible = false;
							})
							.catch((x) => {
								if (_this.$configjson.debugmode == "1") {
									console.warn(x);
								}
								_this.CompsEloContextMenu_visible = false;
							});
					})
					.catch((x) => {
						_this.CompsEloContextMenu_visible = false;
					});
			} else if (itemid == "openrole") {
				var roleitem = _this.roles.filter(
					(x) => x.RoleId == _this.CompsEloContextMenu_roleid
				)[0];
				_this.CompsEloContextMenu_rolename = roleitem.FullName;

				// 根据当前指定的角色，获取所有人员，
				_this.getRoleUsers();
			} else if (itemid == "editrolename") {
				// 指定当前角色为编辑状态
				_this.editing_roleid = _this.CompsEloContextMenu_roleid;
				var rolecacheitem = _this.roles.filter(
					(x) => x.RoleId == _this.CompsEloContextMenu_roleid
				)[0];
				_this.editing_rolefullname_cache = rolecacheitem.FullName;
				_this.CompsEloContextMenu_visible = false;
			} else if (itemid == "disablerole") {
				console.log(_this.CompsEloContextMenu_roleEnabledMark, "===");
				let _url = "",
					_distext = "";
				if (_this.CompsEloContextMenu_roleEnabledMark == 0) {
					(_url = `${_this.$urlPool.RoleEnable}?token=${_this.$staticmethod.Get(
						"Token"
					)}&roleId=${_this.CompsEloContextMenu_roleid}`),
						(_distext = "启用成功");
				} else {
					(_url = `${
						_this.$urlPool.RoleDisable
					}?token=${_this.$staticmethod.Get("Token")}&roleId=${
						_this.CompsEloContextMenu_roleid
					}`),
						(_distext = "停用成功");
				}
				_this
					.$axios({
						method: "post",
						url: _url,
					})
					.then((x) => {
						// 操作数据dom
						_this.getRoles();
						_this.$message({
							message: _distext,
							type: "success",
						});
						_this.CompsEloContextMenu_visible = false;
					})
					.catch((x) => {
						if (_this.$configjson.debugmode == "1") {
							console.warn(x);
						}
						_this.CompsEloContextMenu_visible = false;
					});
			} else {
				// 禁用角色？ 其它。：
				_this.CompsEloContextMenu_visible = false;
			}
		},

		clearcontextbtns() {
			var _this = this;
			_this.CompsEloContextMenu_visible = false;
			_this.extdata._showing_usercontext = false;
			_this.extdata._showing_userroletargets = false;

			_this.func_cancelRoleRenaming();
		},

		set_context_pos(contextwidth, contextheight, ev) {
			var _this = this;
			_this.CompsEloContextMenu_p_x =
				ev.clientX > document.body.clientWidth - contextwidth
					? document.body.clientWidth - contextwidth
					: ev.clientX;
			_this.CompsEloContextMenu_p_y =
				ev.clientY > document.body.clientHeight - contextheight
					? document.body.clientHeight - contextheight
					: ev.clientY;
		},

		showrolecontextmenu(roleId, currentEnabledMark, ev, item) {
			this.saveClickItem = item;
			if (ev) {
				ev.stopPropagation();
			}
			var _this = this;
			var btnname;
			var thebtn;
			// 如果 是项目管理员，则仅展示 查看人员 item.IsProjectManager
			if (item.IsProjectManager == true) {
				_this.CompsEloContextMenu_roleid = roleId;
				_this.CompsEloContextMenu_roleEnabledMark = currentEnabledMark;
				_this.CompsEloContextMenu_data = _this.CompsEloContextMenu_admindata;
				_this.CompsEloContextMenu_visible = true;
				_this.set_context_pos(110, 136, ev);
			} else {
				_this.CompsEloContextMenu_roleid = roleId;
				_this.CompsEloContextMenu_roleEnabledMark = currentEnabledMark;

				// 设置按钮名称。 currentEnabledMark == 1 “禁用角色” ：“启用角色”

				if (currentEnabledMark == 1) {
					// 改为 禁用角色
					btnname = "禁用角色";
				} else {
					// 改为 启用角色
					btnname = "启用角色";
				}
				_this.CompsEloContextMenu_data = _this.CompsEloContextMenu_fulldata;

				// 如果是Public，则不让改名称
				var index = _this.roles.findIndex(
					(x) => x.RoleId == _this.CompsEloContextMenu_roleid
				);
				if (index >= 0) {
					var rolename = _this.roles[index].FullName;
					if (rolename == "Public") {
						_this.CompsEloContextMenu_data =
							_this.CompsEloContextMenu_Publicdata;
					}
				}
				// //如果是Public，则不让改名称

				thebtn = _this.CompsEloContextMenu_data.items.filter(
					(x) => x.id == "disablerole"
				)[0];
				thebtn.text = btnname;
				_this.CompsEloContextMenu_visible = true;
				_this.set_context_pos(110, 136, ev);
			}
		},

		rolenameadd() {
			var _this = this;
			var _Name = _this.addingnewrolename;
			var _Token = _this.$staticmethod.Get("Token");
			var _organizeId = _this.$staticmethod._Get("organizeId");

			// _Name 名称验证
			if (_Name.trim() == "项目管理员") {
				_this.$message.error("角色名称不能为项目管理员，请重新输入");
				return;
			}

			// 前端名称重复验证
			var index = _this.extdata.targetRoles.findIndex(
				(x) => x.FullName == _Name.trim()
			);
			if (index >= 0) {
				_this.$message.error("角色名称重复，请重新输入");
				return;
			}
			// 新增角色信息接口
			_this
				.$axios({
					method: "post",
					url: `${_this.$urlPool.RoleAdd}?token=${_Token}`,
					data: {
						Name: _Name.trim(),
						OrganizeId: _organizeId,
					},
				})
				.then((x) => {
					if (x.status == 200 && x.data.Data) {
						// 离开编辑模式
						_this.isaddingnewrole = false;
						_this.addingnewrolename = "";

						// 修改 _this.roles
						_this.getRoles();

						_this.$message({
							message: "添加成功",
							type: "success",
						});
					} else if (_this.$configjson.debugmode == "1") {
						console.warn(x);
					}
				})
				.catch((x) => {
					if (_this.$configjson.debugmode == "1") {
						console.warn(x);
					}
				});
		},

		rolenameeditkeyup(ev) {
			var _this = this;

			// 判断当前是在编辑角色还是新增角色
			if (_this.editing_roleid == "") {
				if (ev.keyCode == "13") {
					// 依据 addingnewrolename 进行接口访问，新增此条角色数据。默认启用
					_this.rolenameadd();
				} else if (ev.keyCode == "27") {
					_this.addingnewrolename = "";
					_this.isaddingnewrole = false;
				}
			} else {
				if (ev.keyCode == "13") {
					var roleitem = _this.roles.filter(
						(x) => x.RoleId == _this.editing_roleid
					)[0];
					// 编辑角色名称
					_this
						.$axios({
							method: "post",
							url: `${
								_this.$urlPool.RoleUpdate
							}?token=${_this.$staticmethod.Get("Token")}`,
							data: {
								Name: roleitem.FullName,
								Id: _this.editing_roleid,
							},
						})
						.then((x) => {
							_this.$message({
								message: "修改成功",
								type: "success",
							});
							_this.editing_roleid = "";
						})
						.catch((x) => {
							if (_this.$configjson.debugmode == "1") {
								console.warn(x);
							}

							// 恢复原有角色名称
							roleitem.FullName = _this.editing_rolefullname_cache;

							// 重置“正在编辑的角色ID”为""
							_this.editing_roleid = "";
						});
				} else if (ev.keyCode == "27") {
					_this.func_cancelRoleRenaming();
				}
			}
		},

		// 取消角色的编辑
		func_cancelRoleRenaming() {
			var _this = this;
			var roleitem = _this.roles.filter(
				(x) => x.RoleId == _this.editing_roleid
			)[0];
			if (!roleitem) {
				return;
			}
			roleitem.FullName = _this.editing_rolefullname_cache;

			// 重置“正在编辑的角色ID”为""
			_this.editing_roleid = "";
		},

		// 点击新增角色按钮
		btnnewrole_click() {
			var _this = this;
			_this.isaddingnewrole = true;
			_this.editing_roleid = "";
		},

		// 写入 docauths（用于保存时post）
		SaveDocAuths(folderId, roleId, prop, propval) {
			var _this = this;
			var ifhas = _this.docauths.filter(
				(x) =>
					x.FolderId == folderId
			);
			var tosetmodel;
			if (ifhas.length == 0) {
				tosetmodel = propval
				tosetmodel.FolderId = folderId
				_this.docauths.push(tosetmodel);
			} else {
				tosetmodel = ifhas[0];
			}
			tosetmodel.AuthValue = propval[prop];
		},

		// 根据当前文件夹id，向下追溯所有子文件夹，
		//    直到某一文件夹被显式设置了权限，或找到全部的子文件夹（如果当前文件夹未展开，则视为最深文件夹。）
		searchallchildren(reffolderobjarr, prop, folderid) {
			var _this = this;
			if (!folderid || !_this.foldertableData[folderid]) {
				return;
			}
			console.log(reffolderobjarr);
			var currentfolderidchildren = _this.foldertableData[folderid];
			for (var i = 0; i < currentfolderidchildren.length; i++) {
				reffolderobjarr.push(currentfolderidchildren[i]);
				// arguments.callee(reffolderobjarr, prop, currentfolderidchildren[i].FolderId);
				_this.searchallchildren(
					reffolderobjarr,
					prop,
					currentfolderidchildren[i].FolderId
				);
			}
		},

		GetDirectlyParentAuth(authname) {
			var _this = this;
			var index = _this.docauthsort.indexOf(authname);
			//debugger;
			// console.log(`index = ${index}, _this.docauthsort is`, _this.docauthsort);
			if (index >= 0 && index < _this.docauthsort.length - 1) {
				return _this.docauthsort[index + 1];
			} else {
				return undefined;
			}
		},

		GetDirectlyChildrenAuth(authname) {
			var _this = this;
			console.log(`GetDirectlyChildrenAuth, authname = ${authname}`);
			var index = _this.docauthsort.indexOf(authname);
			console.log(`index = ${index}, _this.docauthsort is`, _this.docauthsort);
			if (index >= 1 && index < _this.docauthsort.length) {
				return [_this.docauthsort[index - 1]];
			} else {
				return [];
			}
		},

		switchdocitemsomeauth(row, prop, willhasAuth) {

			var _this = this;
			// 先初始化将被牵连的数组为空
			var outtosetpropfolderarr = [];
			row.Auth[prop] = !row.Auth[prop];
			// 通过递归调用方法获取所有将被牵连的文件夹
      		_this.searchallchildren(outtosetpropfolderarr, prop, row.FolderId);
			//  debugger
			// let TheEdit = row.Auth;
			// TheEdit.FolderId = row.FolderId;
			// let _index = _this.docauths.findIndex((x) => x.FolderId === row.FolderId);
			// if (_index == -1) {
			// 	_this.docauths.push(TheEdit);
			// } else {
			// 	_this.docauths.splice(_index, 1);
			// 	_this.docauths.push(TheEdit);
			// }
			//  判断当前是否是勾选
			if(row.Auth[prop]){
				row.Auth[prop] = true
				// console.log(row.Auth[prop],'==选择')
				// 递归找到子集
				for (var i = 0; i < outtosetpropfolderarr.length; i++) {
					outtosetpropfolderarr[i].Auth[prop] = true;
					outtosetpropfolderarr[i].Auth.FolderId = outtosetpropfolderarr[i].FolderId
					let ele = outtosetpropfolderarr[i]
					_this.SaveDocAuths(
						ele.FolderId,
						_this.currentshowrole,
						prop,
						ele.Auth
					);
				}
				_this.SaveDocAuths(
					row.FolderId,
					_this.currentshowrole,
					prop,
					row.Auth
				);
			}else{
				row.Auth[prop] = false
				// console.log(row.Auth[prop],'==取消选择')
				for (var i = 0; i < outtosetpropfolderarr.length; i++) {
					outtosetpropfolderarr[i].Auth[prop] = false;
					outtosetpropfolderarr[i].Auth.FolderId = outtosetpropfolderarr[i].FolderId
					let ele = outtosetpropfolderarr[i]
					_this.SaveDocAuths(
						ele.FolderId,
						_this.currentshowrole,
						prop,
						ele.Auth
					);
				}
				_this.SaveDocAuths(
					row.FolderId,
					_this.currentshowrole,
					prop,
					row.Auth
				);
			}
		},

		// 点击某一种权限的编辑按钮时
		authtabbtnclick(btnid) {
			var _this = this;
			// 点击的是“功能权限”的编辑按钮
			if (btnid == "funcauthedit") {
				_this.CompsTopTitleBtns_data.title = "功能权限";

				// 设置功能权限可编辑了 rolevue.CompsBaseModuleItems_data.editable = true;
				_this.CompsBaseModuleItems_data.editable = true;

				// 缓存当前功能权限数据
				_this.CompsBaseModuleItems_data.cachedata =
					_this.$staticmethod.DeepCopy(_this.funcauths);
			} else if (btnid == "docauthedit") {
				// 点击的是“文档权限”的编辑按钮
				_this.CompsTopTitleBtns_data.title = "文档权限";

				// 设置文档权限可编辑了
				_this.DocAuth.editable = true;

				// 缓存当前文档权限数据，包括tableData及子文件夹的相关数据
				// _this.tableData_cache = _this.$staticmethod.DeepCopy(_this.tableData);
				// _this.foldertableData_cache = _this.$staticmethod.DeepCopyDic(
				//   _this.foldertableData
				// );
			} else if (btnid == "phaseauthedit") {
				// 点击的是“模型权限”的编辑按钮
				_this.CompsTopTitleBtns_data.title = "模型权限";

				// 设置模型权限可编辑了
				_this.modelAuth.editable = true;

				// 缓存当前模型权限数据
				// _this.CompsBaseModuleItems_data.cachedata =
					// _this.$staticmethod.DeepCopy(_this.phaseauths);
			} else if (btnid == 'archivesauthedit') {
				_this.CompsTopTitleBtns_data.title = "档案权限";
				_this.archivesAuth.editable = true;
			}
			_this.CompsTopTitleBtns_data.visiable = true;
		},

		CompsTopTitleBtns_ok() {
			var _this = this;
			_this.CompsTopTitleBtns_data.visiable = false;

			// 额外保存处理
			if (_this.roleauthselecteditemid == "funcauth") {
				// _this.currentshowrole
				var roleid = _this.currentshowrole;
				let allfuncauthsetting = _this.funcauths;
        // 后端要的是所有的选中的菜单，并且还是一维数组，Buttons中的HasAuth，只取true，在此转换；后端神逻辑非要这么处理，说没时间改，改的费劲***，真恶心.....
        let trueAuth = _this.flatMenu(allfuncauthsetting);  // 按照Children，扁平化一维数组
				let trueFunSetArr = trueAuth
        // for(let i = 0 ; i < trueFunSetArr.length; i++){
        //   if(trueFunSetArr[i].Buttons.length > 0){
        //     trueFunSetArr[i].Buttons = trueFunSetArr[i].Buttons.filter(item => item.HasAuth === true)
        //   }
        // }
        console.log(trueFunSetArr,'======')

        // 先过滤所有的一级菜单
				console.log("-00000");
				this.setAuth(trueFunSetArr,roleid);
			} else if (_this.roleauthselecteditemid == "docauth") {
				_this.docauths;
				// debugger;

				// 判断如果没有改动过，直接跳出
				if (_this.docauths.length == 0) {
					// 设置当前文档权限为非编辑状态
					_this.DocAuth.editable = false;
				} else {
					// 发送 post 请求给 bimserver
					_this
						.$axios({
							method: "post",
							headers: {
								"Content-Type": "application/json",
							},
							url: `${window.bim_config.webserverurl}/api/v1/folder/auth?Token=${this.$staticmethod.Get('Token')}`,
							data: JSON.stringify({
								ProjectID: _this.$staticmethod._Get("organizeId"),
								RoleId: _this.currentshowrole,
								RecursiveAuth: true,
								Auths: _this.docauths,
							}),
						})
						.then((x) => {
							// 设置当前文档权限为非编辑状态
							if (x.data.Ret == 1) {
								_this.$message({
									message: x.data.Msg,
									type: "success",
								});
							} else {
								_this.$message({
									message: x.data.Msg,
									type: "warning",
								});
							}
							_this.loadrootpriviges();
							_this.DocAuth.editable = false;
							_this.docauths = [];
							_this.foldertableData = [];
						})
						.catch((x) => {
							if (_this.$configjson.debugmode == "1") {
								console.warn(x);
							}

							// 设置当前文档权限为非编辑状态
							_this.DocAuth.editable = false;
							_this.docauths = [];
						});
				}
			} else if (_this.roleauthselecteditemid == "phaseauth") {
				var _RoleId = _this.currentshowrole;

				let modelTableData = _this.modelTableData;
				// 后端要的是所有的选中的菜单，并且还是一维数组，Buttons中的HasAuth，只取true，在此转换；后端神逻辑非要这么处理，说没时间改，改的费劲***，真恶心.....
				let trueAuth = _this.flatModelMenu(modelTableData);  // 按照Children，扁平化一维数组
						let trueFunSetArr = trueAuth
				// for(let i = 0 ; i < trueFunSetArr.length; i++){
				// 	if(trueFunSetArr[i].Buttons.length > 0){
				// 		trueFunSetArr[i].Buttons = trueFunSetArr[i].Buttons.filter(item => item.HasAuth === true)
				// 	}
				// }
				console.log(trueFunSetArr,'======')
			_this
				.$axios({
					method: "post",
					url: `${_this.$urlPool.SetModelRoleAuth}?token=${_this.$staticmethod.Get(
						"Token"
					)}`,
					data: {
						Auth: trueFunSetArr,
						RoleId: _RoleId,
					},
				})
				.then((x) => {
					_this.$message({
						message: "保存成功",
						type: "success",
					});

					// 设置当前功能权限为非编辑状态
					_this.modelAuth.editable = false;
					_this.loadphasepriviges();
				})
				.catch((x) => {
					if (_this.$configjson.debugmode == "1") {
						console.warn(x);
					}
					// 设置当前功能权限为非编辑状态
					_this.modelAuth.editable = false;
				});
			} else if (_this.roleauthselecteditemid == "archivesauth") {
				let auths = _this.flatArchivesMenu(_this.archivesTableData)
				_this.$axios({
					method: "post",
					headers: {
						"Content-Type": "application/json",
					},
					url: `${window.bim_config.webserverurl}/api/Archives/Category/Auth?Token=${this.$staticmethod.Get('Token')}`,
					data: JSON.stringify({
						OrganizeId: _this.$staticmethod._Get("organizeId"),
						RoleId: _this.currentshowrole,
						Auths: auths,
					}),
				})
				.then((x) => {
					if (x.data.Ret == 1) {
						_this.$message({
							message: x.data.Msg,
							type: "success",
						});
					} else {
						_this.$message({
							message: x.data.Msg,
							type: "warning",
						});
					}
					_this.archivesAuth.editable = false;
					_this.loadarchivespriviges();
				})
				.catch((x) => {
					if (_this.$configjson.debugmode == "1") {
						console.warn(x);
					}
				});
			}
		},
    flatMenu(arr, result=[]) {
      for (let i = 0; i < arr.length; i++) {
        const { Children, ...rest } = arr[i];
        result.push(rest);
        if (Children.length > 0) {
          this.flatMenu(Children, result);
        }

      }
      return result;
    },

	flatModelMenu(arr, result=[]) {
      for (let i = 0; i < arr.length; i++) {
        const { children, ...rest } = arr[i];
        result.push(rest);
        if (children.length > 0) {
          this.flatModelMenu(children, result);
        }

      }
      return result;
    },
	flatArchivesMenu(arr, result=[]) {
      for (let i = 0; i < arr.length; i++) {
        const { children, ArchivesAuth, Id } = arr[i];
        result.push({ CaegoryId: Id, ...ArchivesAuth});
        if (children.length > 0) {
          this.flatArchivesMenu(children, result);
        }
      }
      return result;
    },
		setAuth(allfuncauthsetting, roleid) {
			let _this = this;
			_this
				.$axios({
					method: "post",
					url: `${_this.$urlPool.SetRoleAuth}?token=${_this.$staticmethod.Get(
						"Token"
					)}`,
					data: {
						Auth: allfuncauthsetting,
						RoleId: roleid,
					},
				})
				.then((x) => {
					_this.$message({
						message: "保存成功",
						type: "success",
					});

					// 设置当前功能权限为非编辑状态
					_this.CompsBaseModuleItems_data.editable = false;
				})
				.catch((x) => {
					if (_this.$configjson.debugmode == "1") {
						console.warn(x);
					}

					// 设置当前功能权限为非编辑状态
					_this.CompsBaseModuleItems_data.editable = false;
				});
		},

		CompsTopTitleBtns_cancel() {
			var _this = this;
			_this.CompsTopTitleBtns_data.visiable = false;

			// 点取消后，判断当前是正在编辑功能权限还是文档权限
			if (_this.currentshowauthtype == "funcauth") {
				// 恢复原来的 funcauth // _this.funcauths ...
				_this.funcauths = _this.$staticmethod.DeepCopy(
					_this.CompsBaseModuleItems_data.cachedata
				);

				// 设置当前为非编辑状态
				_this.CompsBaseModuleItems_data.editable = false;
			} else if (_this.currentshowauthtype == "docauth") {
				// 恢复原来的 docauth
				_this.tableData = _this.$staticmethod.DeepCopy(_this.tableData_cache);
				_this.foldertableData = _this.$staticmethod.DeepCopyDic(
					_this.foldertableData_cache
				);

				// 设置当前为非编辑状态
				_this.DocAuth.editable = false;
				_this.docauths = [];
				_this.foldertableData = [];
				_this.loadrootpriviges();
			} else if (_this.currentshowauthtype == "phaseauth") {
				// 恢复原来的 phaseauth
				// _this.phaseauths = _this.$staticmethod.DeepCopy(
				// 	_this.CompsBaseModuleItems_data.cachedata
				// );
				_this.loadphasepriviges();


				// 设置当前为非编辑状态
				_this.modelAuth.editable = false;
			} else if (_this.currentshowauthtype == "archivesauth") {
				_this.archivesAuth.editable = false;
				_this.loadarchivespriviges();
			}
		},

		// tab 控件切换事件回调
		authtabcontrol_onchange(item) {
			var _this = this;
			_this.roleauthselecteditemid = item.id;
			_this.currentshowauthtype = item.id;

			if (_this.currentshowauthtype == "funcauth") {
				// 加载功能权限
				_this.loadfuncpriviges();
			} else if (_this.currentshowauthtype == "docauth") {
				// 加载文档权限
				_this.loadrootpriviges();
			} else if (_this.currentshowauthtype == "phaseauth") {
				// 加载模型权限
				_this.loadphasepriviges();
			} else if (_this.currentshowauthtype == "archivesauth") {
				// 加载档案权限
				_this.loadarchivespriviges();
			}
		},

		// 点击角色，显示其权限
		begin_showroleauth(roleId, ev) {
			var _this = this;

			// 隐藏顶部
			// 判断当前是否正处于编辑模式。
			if (_this.CompsBaseModuleItems_data.editable == true) {
				// 当前处于功能权限编辑模式
				_this
					.$confirm(`当前正在编辑功能权限，是否放弃现有编辑？`, "提示", {
						confirmButtonText: "是",
						cancelButtonText: "否",
						type: "warning",
					})
					.then((x) => {
						_this.showroleauth(roleId);
					})
					.catch((x) => {});
			} else if (_this.DocAuth.editable == true) {
				// 当前处于功能权限编辑模式
				_this
					.$confirm(`当前正在文档功能权限，是否放弃现有编辑？`, "提示", {
						confirmButtonText: "是",
						cancelButtonText: "否",
						type: "warning",
					})
					.then((x) => {
						_this.showroleauth(roleId);
					})
					.catch((x) => {});
			}else if (_this.archivesAuth.editable == true) {
				// 当前处于档案权限编辑模式
				_this
					.$confirm(`当前正在编辑档案权限，是否放弃现有编辑？`, "提示", {
						confirmButtonText: "是",
						cancelButtonText: "否",
						type: "warning",
					})
					.then((x) => {
						_this.showroleauth(roleId);
					})
					.catch((x) => {});
			} else {
				// 未处于任何编辑模式
				_this.showroleauth(roleId);
			}
		},

		// 点击某一个角色，且当前不处理编辑状态，显示对应权限
		showroleauth(roleId) {
			var _this = this;
			_this.currentshowrole = roleId;
			_this.CompsTopTitleBtns_cancel();

			// 判断当前是加载功能角色还是文档角色
			if (_this.currentshowauthtype == "funcauth") {
				// 加载功能权限
				_this.loadfuncpriviges();
			} else if (_this.currentshowauthtype == "docauth") {
				// 加载文档权限
				_this.loadrootpriviges();
			}
			else if (_this.currentshowauthtype == "phaseauth") {
				// 加载模型权限
				_this.loadphasepriviges();
			} else if (_this.currentshowauthtype == "archivesauth") {
				// 加载档案权限
				// 因上方CompsTopTitleBtns_cancel 有加载loadarchivespriviges 此处无需再次加载
                // _this.loadarchivespriviges();
			}
		},

		// 加载全部非删除角色（不一定是有效角色）
		getRoles() {
			var _this = this;
			var _organizeId = _this.$staticmethod._Get("organizeId");
			var _Tlk = _this.$staticmethod.Get("Token");
			var _webserverurl = _this.$configjson.webserverurl;
			_this
				.$axios({
					method: "get",
					url: `${_webserverurl}/api/User/Role/GetRoles?organizeId=${_organizeId}&token=${_Tlk}`,
				})
				.then((x) => {
					if (x.status == 200 && x.data.Ret > 0) {
						var _roles = x.data.Data;
						_this.roles = _roles;
						_this.extdata.targetRoles = _roles;

						// 不自动选择某一角色
						// var _roleEnableds = _this.roles.filter(x => x.EnabledMark == 1);
						// if (autoselectfirstvalid && _roleEnableds.length > 0) {
						//     _this.currentshowrole = _roleEnableds[0].RoleId;
						// }
					} else if (_this.$configjson.debugmode == "1") {
						console.warn(x);
					}
				})
				.catch((x) => {
					if (_this.$configjson.debugmode == "1") {
						console.warn(x);
					}
				});
		},

		getJsonAuth(fromdbauthjson, authname, treeauth) {
			var _this = this;
			var comauth = {};
			// isextend 表示子文件夹权限，表示是否继承 treeauth 的权限
			comauth.isextand =
				fromdbauthjson == null ? true : fromdbauthjson[authname] == null;
			comauth[authname] =
				comauth.isextand == true
					? treeauth[authname][authname]
					: fromdbauthjson[authname];
			return comauth;
		},

		getSomeAuth(fromdbauth, authname, treeauth) {
			var _this = this;
			var comauth = {};
			comauth.isextand = fromdbauth[authname] == null;
			comauth[authname] =
				fromdbauth[authname] == null
					? treeauth[authname][authname]
					: fromdbauth[authname];
			return comauth;
		},

		getRootJsonAuth(fromdbauthjson, authname) {
			var _this = this;
			var comauth = { isextand: false };
			comauth[authname] = fromdbauthjson ? fromdbauthjson[authname] : null;
			return comauth;
		},

		getRootAuth(fromdbauth, authname) {
			var _this = this;
			var comauth = { isextand: false };
			comauth[authname] = fromdbauth[authname];
			return comauth;
		},

		updateTreeStructure (tree) {
			for (const node of tree) {
				// 更换属性名
				node.children = node.Children;
				delete node.Children;

				// 添加hasChildren字段
				node.hasChildren = node.children && node.children.length > 0;

				// 递归处理子节点
				if (node.children && node.children.length > 0) {
					this.updateTreeStructure(node.children);
				}
			}
		},
		setArchivesAuth(children, authkey, val) {
			children.forEach(item => {
				if (authkey === 'selectall') {
					Object.keys(item.ArchivesAuth).forEach(key => {
						item.ArchivesAuth[key] = val;
					});
				} else {
					item.ArchivesAuth[authkey] = val;
				}
				if (item.children) {
					this.setArchivesAuth(item.children, authkey, val);
				}
			})
		},
		archivesCheckboxChangeHandle(data, authkey, val) {
			this.setArchivesAuth(data.children, authkey, val);
		},
		archivesCheckboxChange(data, val) {
			if (val) {
				Object.keys(data.ArchivesAuth).forEach(key => {
					data.ArchivesAuth[key] = true;
				});
			} else {
				Object.keys(data.ArchivesAuth).forEach(key => {
					data.ArchivesAuth[key] = false;
				});
			}
            this.setArchivesAuth(data.children, 'selectall', val);
		},
		// 加载当前角色的档案权限
		loadarchivespriviges() {
			// 获取根级文件夹
			var _this = this;
			var _LoadingIns = _this.$loading({
				text: "加载中",
				target: document.getElementById("id_rb_valid"),
			});
			var _roleID = _this.currentshowrole;
			_this.$axios({
				method: "get",
				url: `${
					window.bim_config.webserverurl
				}/api/Archives/Category/RoleTree?organizeId=${_this.$staticmethod._Get(
					"organizeId"
				)}&parentId=0&roleId=${_roleID}&Token=${this.$staticmethod.Get('Token')}`,
			}).then((x) => {
				if (x.data.Ret == 1) {
					var _onlydir = x.data.Data;
					this.updateTreeStructure(_onlydir)
					_this.archivesTableData = _onlydir;
					_LoadingIns.close();
				} else {
					_this.$message({
						message: x.data.Msg,
						type: "warning",
					});
					_LoadingIns.close();
				}
			}).catch((x) => {
				console.warn(x);
				_LoadingIns.close();
			});
		},

		// 加载当前角色的模型阶段权限
		loadphasepriviges() {
			// 调用接口获取权限数据
			var _this = this;

			var _LoadingIns = _this.$loading({
				text: "加载中",
				target: document.getElementById("id_rb_valid"),
			});
			_this.phaseauths = []
			var _roleID = _this.currentshowrole;
			var _organizeId = _this.$staticmethod._Get("organizeId");
			var _Tlk = _this.$staticmethod.Get("Token");
			var _this = this;
			var _url = `${_this.$urlPool.GetModelMenuTreeAll}?Token=${_Tlk}&roleId=${_roleID}`
			this.$axios({
					url: _url,
					method: "get",
				})
				.then((x) => {
					if (x.status == 200) {
						_LoadingIns.close();
						if (x.data.Ret > 0) {
							this.updateTreeStructure(x.data.Data)
							if (x.data.Data && x.data.Data[0].children) {
								_this.modelTableData = x.data.Data[0].children;
							}
						} else {
							_this.$message.error(x.data.Msg);
						}
					}
				})
				.catch((x) => {
					_LoadingIns.close();
				});
		},

		// 加载当前角色的功能权限数据
		loadfuncpriviges() {
			var _this = this;
			var _Tlk = _this.$staticmethod.Get("Token");
			var _roleID = _this.currentshowrole;
			_this
				.$axios({
					method: "get",
					url: `${_this.$urlPool.RoleTree}?token=${_Tlk}&roleId=${_roleID}`,
				})
				.then((x) => {
					if (x.status == 200 && x.data.Ret > 0 && x.data.Data) {
						if (window.bim_config.disabled_gis) {
							x.data.Data = x.data.Data.filter((y) => y.Bm_EnCode != "GIS");
						}

						_this.funcauths = x.data.Data;
            for (const funcauth of _this.funcauths) {
              if (funcauth.MenuName === '场景管理') {
                funcauth.Children = []
              }
            }
					} else if (_this.$configjson.debugmode == "1") {
						console.warn(x);
					}
				})
				.catch((x) => {
					if (_this.$configjson.debugmode == "1") {
						console.warn(x);
					}
				});
		},

		// 加载根级目录权限数据
		loadrootpriviges() {
			// 获取根级文件夹
			var _this = this;
			var _LoadingIns = _this.$loading({
				text: "加载中",
				target: document.getElementById("id_rb_valid"),
			});
			var _bimcomposerId = _this.$staticmethod._Get("bimcomposerId");
			var _bimserverurl = _this.$staticmethod.getDocServer();
			var _roleID = _this.currentshowrole; //"297486da-0459-4498-8e11-114c927e8e92"
			_this
				.$axios({
					method: "get",
					url: `${
						window.bim_config.webserverurl
					}/api/v1/folder/role-tree?projectId=${_this.$staticmethod._Get(
						"organizeId"
					)}&parentId=0&roleId=${_roleID}&Token=${this.$staticmethod.Get('Token')}`,
				})
				.then((x) => {
					if (x.data.Ret == 1) {
						// _this.v_Id = x.data.Data[0].Id
						// _this.firstLevelFolderData = x.data.Data[0]
						var _arr = [];
						var _onlydir = x.data.Data;
						for (var i = 0; i < _onlydir.length; i++) {
							// 判断如果 _onlydir[i].FolderId 与 bimcomposerId 相同，则 FolderName 为 根目录
							var rootele = {
								level: 1,
								FolderId: _onlydir[i].Id,
								FolderName: _onlydir[i].FolderName,
								Auth: _onlydir[i].Auth,
								hasChildren: _onlydir[i].HasChildren,
							};
							_arr.push(rootele);
						}
						_this.tableData = _arr;
						_LoadingIns.close();
					} else {
						_this.$message({
							message: x.data.Msg,
							type: "warning",
						});
						_LoadingIns.close();
					}
				})
				.catch((x) => {
					console.warn(x);
					_LoadingIns.close();
				});
		},

		header_cell_class_name({ row, column, rowIndex, columnIndex }) {
			var _this = this;
			if (columnIndex == 0) {
				return "css-bgtreecoli";
			} else {
				return "";
			}
		},
		cell_class_name({ row, column, rowIndex, columnIndex }) {
			var _this = this;
			if (columnIndex == 0) {
				return "css-bgtreecol";
			} else {
				return "";
			}
		},
		table_load(tree, treenode, resolve) {
			var _this = this;
			var _LoadingIns = _this.$loading({
				text: "加载中",
				target: document.getElementById("id_rb_valid"),
			});
			var _folderid = tree.FolderId;
			var _roleID = _this.currentshowrole; //"297486da-0459-4498-8e11-114c927e8e92"

			// 通过 _folderid 获取子文件夹
			_this
				.$axios({
					method: "get",
					url: `${
						window.bim_config.webserverurl
					}/api/v1/folder/role-tree?projectId=${_this.$staticmethod._Get(
						"organizeId"
					)}&parentId=${_folderid}&roleId=${_roleID}&Token=${this.$staticmethod.Get('Token')}`,
				})
				.then((x) => {
					if (x.data.Ret == 1 && x.data.Data) {
						var _onlydir = x.data.Data;
						var _arr = [];
						for (var i = 0; i < _onlydir.length; i++) {
							_arr.push({
								level: tree.level + 1,
								FolderId: _onlydir[i].Id,
								FolderName: _onlydir[i].FolderName,
								Auth: _onlydir[i].Auth,
								hasChildren: _onlydir[i].HasChildren,
							});
						}
						if (_arr.length == 0) {
							//   _this.$message({
							//       message:'当前文件夹没有子文件夹',
							//       type: 'warning'
							//   });
							tree.hasChildren = false;
						}

						_this.foldertableData[_folderid] = _arr;

						// 更新子文件夹权限缓存，为取消功能做准备
						_this.foldertableData_cache = _this.$staticmethod.DeepCopyDic(
							_this.foldertableData
						);

						// _this.$refs.docauthtable.doLayout();
						resolve(_arr);
					} else if (_this.$configjson.debugmode == "1") {
						console.warn(x);
					}
					_LoadingIns.close();
				})
				.catch((x) => {
					if (_this.$configjson.debugmode == "1") {
						console.warn(x);
					}
					_LoadingIns.close();
				});
		},
	},
};
</script>
<style scoped>
._css-dis {
	cursor: not-allowed !important;
	opacity: 0.3;
}
._css-roleauth-newrole {
	margin-left: 24px;
}
._css-roleauth-top {
	display: flex;
	align-items: center;
}
._css-roleuser-oiicon {
	width: 16px;
	height: 16px;
	margin-left: 24px;
	line-height: 16px;
}
._css-roleuser-oitext {
	flex: 1;
	font-size: 14px;
	color: rgba(0, 0, 0, 0.85);
	margin-left: 8px;
	height: 22px;
	line-height: 22px;
	text-align: left;
}
._css-roleuser-oitext2 {
	flex: 1;
	font-size: 14px;
	color: rgba(0, 0, 0, 0.85);
	margin-left: 20px;
	height: 22px;
	line-height: 22px;
	text-align: left;
	overflow-x: hidden;
	white-space: nowrap;
	text-overflow: ellipsis;
}
._css-roleuser-oi {
	height: 40px;
	display: flex;
	align-items: center;
	cursor: pointer;
}
._css-roleuser-oirighticon {
	width: 12px;
	height: 12px;
	font-size: 12px;
	color: rgba(0, 0, 0, 0.85);
	margin-right: 16px;
}
._css-roleuser-oi:hover {
	background-color: rgba(0, 0, 0, 0.04);
}
._css-roleuser-operates {
	background: rgba(255, 255, 255, 1);
	box-shadow: 0px 1px 3px 0px rgba(0, 21, 41, 0.12);
	border-radius: 2px;
	width: 140px;
	min-height: 128px;
	box-sizing: border-box;
	padding-top: 4px;
	padding-bottom: 4px;
	position: fixed;
	z-index: 3;
	user-select: none;
}
._css-roleuser-operates2 {
	background: rgba(255, 255, 255, 1);
	box-shadow: 0px 1px 3px 0px rgba(0, 21, 41, 0.12);
	border-radius: 2px;
	width: 120px;
	min-height: 128px;
	box-sizing: border-box;
	padding-top: 4px;
	padding-bottom: 4px;
	position: fixed;
	z-index: 3;
	user-select: none;
}
._css-editingrolemembersbtns {
	display: flex;
	width: 148px;
	justify-content: space-between;
}
._css-body-inner-roleandmembers {
	overflow-x: hidden;
	width: 196px;
	position: relative;
}
._css-contextmenus {
	position: fixed;
	z-index: 4;
}
._css-left-roleiaddingnewrole {
	color: rgba(0, 0, 0, 0.25);
	box-sizing: border-box;
	border: 1px dotted rgba(0, 0, 0, 0.15);
}
._css-addingnewrolename {
	border: none;
	outline: none;
	background-color: transparent;
	width: 100%;
}
._css-roleauthfolderheadcontainer {
	display: flex;
	align-items: center;
}
._css-roleauthfolderhead {
	color: rgba(86, 167, 255);
	margin-left: 4px;
}
._css-roleauthfolderheadspan {
	margin-left: 4px;
	font-size: 12px;
	color: #606266;
}
._css-roleauthfolder {
	color: rgba(86, 167, 255);
}
._css-roleauthfname {
	overflow-x: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
	font-size: 12px;
	padding-left: 4px;
}
::-webkit-scrollbar {
	/*滚动条整体样式*/
	width: 5px; /*高宽分别对应横竖滚动条的尺寸*/
	height: 4px;
}
::-webkit-scrollbar-thumb {
	/*滚动条里面小方块*/
	border-radius: 5px;
	-webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
	background: rgba(0, 0, 0, 0.2);
}
::-webkit-scrollbar-track {
	/*滚动条里面轨道*/
	-webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
	border-radius: 0;
	background: rgba(0, 0, 0, 0.2);
}
._css-each-moduleauthitem {
	margin-top: 8px;
}
._css-left-rolecurrent {
	height: 50px;
	background-color: rgba(0, 0, 0, 0.02);
}
._css-left-members {
	height: calc(100% - 50px);
	overflow-y: auto;
	padding-bottom: 8px;
}
._css-left-memberi {
	margin-top: 8px;
	height: 50px;
	display: flex;
	align-items: center;
}
._css-rolenormal {
	color: rgba(0, 0, 0, 0.85);
}
._css-roledis {
	color: rgba(0, 0, 0, 0.45);
	text-decoration: line-through;
}
._css-left-rolelist {
	height: calc(100% - 0px);
	padding-bottom: 10px;
	box-sizing: border-box;
	overflow-y: auto;
	width: 100%;
}
._css-left-rolei {
	height: 50px;
	width: 100%;
	display: flex;
	align-items: center;
}
._css-roledetailheader_true {
	height: 50px;
	width: 100%;
	display: flex;
	align-items: center;
	background-color: rgba(86, 98, 112, 0.1);
	border-bottom: 1px solid rgba(0, 0, 0, 0.09);
	box-sizing: border-box;
}
._css-left-role-icon {
	width: 16px;
	height: 16px;
	margin-left: 24px;
}
._css-left-rolecur-icon {
	width: 40px;
	height: 16px;
	cursor: pointer;
	height: 100%;
	display: flex;
	align-items: center;
	flex-direction: row-reverse;
}
._css-left-role-content {
	margin-left: 8px;
	text-align: left;
	width: 112px;
	font-size: 14px;
	overflow-x: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
	cursor: default;
}
._css-left-role-rpart {
	flex: 1;
	display: flex;
	flex-direction: row-reverse;
	align-items: center;
	height: 100%;
}
._css-left-role-rpartbtn {
	font-size: 16px;
	margin-right: 16px;
	cursor: pointer;
}
._css-left-user-rpartbtn {
	font-size: 16px;
	margin-right: 16px;
	cursor: pointer;
}
._css-left-rolei:hover,
._css-left-rolei.clicked {
	background-color: rgba(0, 0, 0, 0.04);
}
._css-left-rolebtn {
	height: 0px;
	width: 100%;
	display: flex;
	justify-content: space-around;
	align-items: center;
}
._css-left-rolebtn2 {
	height: 0px;
	width: 100%;
	display: flex;
	justify-content: space-around;
	align-items: center;
}
/* 面包屑部分行高 */
._css-doc-top {
	height: 64px;
}
/* //面包屑部分行高 */
/* 主体部分 */
._css-body {
	height: calc(100% - 64px);
	background-color: rgba(240, 242, 245, 1);
	padding: 16px;
	box-sizing: border-box;
}
/* //主体部分 */
/* 主体内部部分 */
._css-body-inner {
	width: 100%;
	height: 100%;
	background-color: #fff;
	display: flex;
	overflow-x: hidden;
}
._css-left {
	width: 196px;
	height: 100%;
	transition: left 100ms;
	left: 0;
	box-shadow: 0px 1px 1px 0px rgba(0, 21, 41, 0.12);
	border-radius: 2px 0px 0px 2px;
	position: absolute;
}
._css-left__member {
	width: 196px;
	height: 100%;
	transition: right 100ms;
	right: 0;
	box-shadow: 0px 1px 1px 0px rgba(0, 21, 41, 0.12);
	border-radius: 2px 0px 0px 2px;
	position: absolute;
}
._css-left__member.isrighthide {
	transition: right 100ms;
	right: -196px;
}
._css-left.islefthide {
	transition: left 100ms;
	left: -196px;
}
._css-right {
	width: calc(100% - 196px);
	box-sizing: border-box;
	padding-left: 24px;
	background: rgba(240, 242, 245, 1);
	display: flex;
	flex-direction: column;
}
._css-rt {
	height: 40px;
	background-color: rgba(255, 255, 255, 1);
	position: relative;
}
._css-rb {
	height: calc(100% - 40px);
	box-sizing: border-box;
}
._css-rb-valid {
	background-color: rgba(255, 255, 255, 1);
	width: 100%;
	height: 100%;
	padding-bottom: 8px;
	box-sizing: border-box;
}
/* //主体内部部分 */
._css-authcol-head {
	font-size: 12px;
	color: rgba(0, 0, 0, 0.65);
}
._css-table {
	height: 100% !important;
}
._css-noneroleselectedtip {
	width: 100%;
	height: 100%;
	background-color: #fff;
	display: flex;
	justify-content: space-around;
	align-items: center;
}
._css-tipcenter {
	width: 270px;
	display: flex;
	flex-direction: column;
	align-items: center;
}
._css-tipicon {
	height: 100px;
	width: 114px;
}
._css-tipmiddle {
	margin-top: 25px;
	height: 22px;
	font-size: 14px;
	color: rgba(0, 0, 0, 0.65);
	text-align: center;
}
._css-tipbottom {
	margin-top: 27px;
	height: 20px;
	font-size: 12px;
	color: rgba(0, 0, 0, 0.25);
	text-align: center;
}
</style>
