<template>
  <div class="scene-iframe-content" style="width: 100%;
          height: 100%;
          background-color:#f0f2f5;
          position: fixed;
          left: 0;
          top: 0;
          z-index: 1001;">
    <div class="detail-header" :class="editTrue ? 'header-edit': ''" v-if="sceneload">
      <div class="title-name">
        <p>{{sceneItemData.SceneName}}</p>
      </div>
      <ul class="" v-if="!isShare">
        <!-- 分享有权限，需要添加，问题追踪的需求 -->
        <li @click="btncreateissue_click" :dragstylemx="true"><i class="icon-interface-problem issue" title="问题追踪"></i></li>
        <li v-if="!fromLongyun" @click="openShare"><i class="share icon-scene-share" title="模型分享"></i></li>
        <li v-if="!fromLongyun && editTrue" @click="saveSubmitDetail" class="close save"><i class="share icon-save-scene"></i>保存</li>
        <li v-if="!fromLongyun" @click="closeDetail" class="close"><i class="share icon-scene-close"></i>关闭</li>
      </ul>
    </div>

    <iframe id="scene-iframe"  :class="editTrue ? 'content-frame-edit': ''" class="content-frame" src="" frameborder="0"></iframe>

    <zdialog-function
      :init_title="share_title"
      :init_zindex="1003"
      :init_innerWidth="400"
      :init_width="400"
      init_closebtniconfontclass="icon-suggested-close"
      :init_usecustomtitlearea="false"
      @onclose="dialogVisible = false"
      v-if="dialogVisible"
    >
      <div slot="mainslot" class="link-body">
        <div class="link-top" v-if="showCancel">
          <div class="top-title">
            <div class="title">点击复制该链接</div>
            <div class="cancelBtn" @click="canceShare" v-if="showCancel">取消分享</div>
          </div>
          <div @click="copy"><input class="share-link" id="txtWXUrl" readonly  v-model="shareUrl" type="text" /></div>
          <div class="share-txt">{{shareForm === '2'? '加密（密码: '+ shareCode +'）' : '公开'}}  有效期{{limitTime === '1'? '永久有效': limitTime === '2'?'一周': '一天'}}</div>
        </div>
        <div class="link-mid" v-if="btnStatus !== 2">
          <div class="share-title">分享形式</div>
          <div class="share-radio">
            <el-radio v-model="shareForm" label="1">公开</el-radio>
            <el-radio v-model="shareForm" label="2" @change="getSecretLink">加密</el-radio>
          </div>
        </div>
        <div class="link-btm" v-if="btnStatus !== 2">
          <div class="share-title">有效时间</div>
          <div class="share-radio">
            <el-radio v-model="limitTime" label="1">永久有效</el-radio>
            <el-radio v-model="limitTime" label="2">一周</el-radio>
            <el-radio v-model="limitTime" label="3">一天</el-radio>
          </div>
        </div>
      </div>
      <div slot="buttonslot" class="_css-addBtnCtn">
        <zbutton-function
          v-if="btnStatus === 1 || btnStatus === 3"
          :init_text="btnStatus === 1? '创建链接': '确定'"
          :init_fontsize="14"
          :debugmode="true"
          :init_height="'40px'"
          :init_width="'80px'"
          @onclick="createShare"
        >
        </zbutton-function>
        <zbutton-function
          v-if="btnStatus === 2"
          :init_text="'复制链接'"
          :init_fontsize="14"
          :debugmode="true"
          :init_height="'40px'"
          :init_width="'80px'"
          @onclick="copy"
        >
        </zbutton-function>
        <zbutton-function
          :init_text="'取消'"
          :init_color="'rgba(24, 144, 255)'"
          :init_bgcolor="'#fff'"
          :init_fontsize="14"
          :debugmode="true"
          :init_height="'40px'"
          :init_width="'80px'"
          @onclick="cancelShareDialog"
        >
        </zbutton-function>
      </div>
    </zdialog-function>
    <zdialog-function
      :init_title="'访问密码'"
      :init_zindex="1003"
      :init_innerWidth="400"
      :init_width="400"
      init_closebtniconfontclass="icon-suggested-close"
      :init_usecustomtitlearea="false"
      @onclose="psdVisible = false"
      v-if="psdVisible"
    >
      <div slot="mainslot" class="link-body">
        <el-form ref="form" :model="psdform">
            <el-form-item label="密码">
              <el-input v-model="psdform.psd" @keyup.enter.native="handlePsdConfirm"></el-input>
            </el-form-item>
          </el-form>
      </div>
      <div slot="buttonslot" class="_css-addBtnCtn">
        <zbutton-function
          :init_text=" '确定'"
          :init_fontsize="14"
          :debugmode="true"
          :init_height="'40px'"
          :init_width="'80px'"
          @onclick="handlePsdConfirm"
        >
        </zbutton-function>
      </div>
    </zdialog-function>
    <CompsCreateIssue v-if="showcreate" @oncancel="cancelcreate" @onok="createok" :hasImg="true" :outertop="300" @imgsrc="_imgsrc" @addMarkUp="addMarkUp" :zIndex="dialogzIndex" needMarkup :stayName="stayName" v-show="isShowCreateIssue"></CompsCreateIssue>
    <MarkupMenu v-if="showMarkupMenu" @closeMarkUpMenu="closeMarkUpMenu" @saveData="saveData" />
  </div>
</template>
<script>
import { EventBus } from "@static/event.js";
import CompsCreateIssue from "@/components/CompsIssue/CompsCreateIssue";
import MarkupMenu from "@/components/CompsIssue/markUpMenu";
export default {
  name: 'sceneManagementDetail',
  components:{
    CompsCreateIssue,
    MarkupMenu
  },
  props: {
    // project 项目ID
    ProjectIDProps:{
      type:String,
      required: false,
      default: ''
    },
    // userID  登录用户UserId
    userIDProps:{
      type:String,
      required: false,
      default: ''
    },
    // sceneID
    SceneIdProps:{
      type:String,
      required: false,
      default: ''
    },
    // 场景JSON
    sceneDataJson: {
      type:String,
      required: false,
      default: ''
    },
    // 选中的构件,收到的是字符串，使用JSON.parse()
    sceneSelectElements: {
      type:String,
      required: false,
      default: ''
    },
    sceneEdit: {
      type: Boolean,
      required: false,
      default: false
    },
    fromPage: {
      type: String,
      default: ''
    },
    markupName: {
      type: String,
      default: ''
    }

  },
  data(){
    return{
      isShowCreateIssue: false,
      sceneload: false,
      editTrue: false, // 默认false，true是编辑状态、判断场景是编辑状态还是预览状态
      iframeWindow: null, // iframe
      sceneItemData: {}, // 当前场景所有值
      projectId: '',
      SceneId: '',
      userID: '',
      dialogVisible: false,
      share_title: '',
      shareUrl: '',
      showCancel: false, // 取消分享
      btnStatus: 1, // 1 创建链接  2 复制链接  3 确定
      shareCode: '1234', // 密码
      shareForm: '1', // 1 公开 2 加密
      limitTime: '1', // 1 永久， 2 一周 3 一天
      psdform: { // 分享场景密码弹窗
        psd: ''
      },
      psdVisible: false,
      showcreate: false, // 问题追踪
      imageUrl: '', // 问题追踪img
      formUrlOrProps: '', // 页面进入方式是组件还是url  分享进入也是url
      fromLongyun: false,
      showMarkupMenu: false,
      dialogzIndex: 2,
      stayName: '',
      reducedErr: false,
    }

  },
  computed: {
    isShare () {
      const shareUrlToken = this.getQueryString('shareUrlToken')
      if (shareUrlToken) {
        return true
      } else {
        return false
      }
    }
  },
  created(){
    // 龙云大屏直接调用该页面、发起问题需要用到项目ID和机构ID、在此保存
    if(this.$route.query.fromname && this.$route.query.fromname == 'longyun'){
      this.fromLongyun = true
      this.$staticmethod._Set("organizeId", this.$route.query.ProjectID);
      this.$staticmethod._Set("_OrganizeId", this.$route.query.organizeId);
      this.$staticmethod.Set("Token", this.$route.query.token);
    }
    // 判断是地址栏还是组件
    if(this.$route.query.ProjectID){
      this.projectId = this.$route.query.ProjectID;
      this.SceneId = this.$route.query.SceneId;
      this.userID = this.$route.query.userID;
      // console.log(this.$route.query.sceneEdit,'====地址参数')
      if(this.$route.query.sceneEdit == '0'){
        // 编辑状态
        this.editTrue = true;
      }else{
        this.editTrue = false;
      }
      this.formUrlOrProps = 'url'
    }else{
      this.projectId = this.ProjectIDProps;
      this.SceneId = this.SceneIdProps;
      this.userID = this.userIDProps;
      this.formUrlOrProps = 'props'
    }
    // 分享进度
    if (!this.isShare) {
      this.$nextTick(() => {
        let _params = {
          projectId: this.projectId,
          SceneId: this.SceneId,
          userID: this.userID
        }
        this.getDetailItem(_params)
      })
    } else {
      this.projectId = '';
      this.SceneId = '';
      // 请求接口
      this.formUrlOrProps = 'url';
      this.getShareMessage('nopsd');
    }
  },
  mounted(){
    window.addEventListener('message', this.listeningMessage, false)
    this.$Bus.$on('getUserSessionError',this.returnGoUrl)
  },
  methods: {
    // 监听是否加载完毕
    listeningMessage(event){
      /*
        在这里添加个判断，formUrlOrProps == url和props区分，
        问题追踪打开的场景在这请求接口获取保存的数据
      */
      if(event.data == 'featureLoad' && this.formUrlOrProps == 'url'){
        this.loadScene(this.sceneItemData.SceneEventDataJson);
      }else if(event.data == 'featureLoad' && this.formUrlOrProps == 'props'){
        this.loadScene(this.sceneDataJson);
      }else if (event.data === 'fromJSONError') {
        this.reducedErr = true
      }
    },
    // 加载完毕后对场景的处理
    loadScene(sceneEventDataJson){
      this.iframeWindow = document.getElementById('scene-iframe').contentWindow
      this.changeScenewidgetSource(this.editTrue)
      // 还原场景 及 左侧场景管理树
      this.iframeWindow.saveSceneJson2Store(sceneEventDataJson)
      window.scene = this.iframeWindow.scene
      this.sceneload = true;
      // 移除postmessage事件监听
      window.removeEventListener('message', this.listeningMessage)
      if(this.formUrlOrProps == 'props' && this.isShare){
        let elements = JSON.parse(this.sceneSelectElements);
        if(elements.length > 0){
          setTimeout(()=>{
            for(let j = 0; j < elements.length; j++ ){
              let element = this.iframeWindow.scene.findObject(elements[j])
              element.selected = true
              this.iframeWindow.scene.render();
            }
          },500)
        }

      }
      // 如果来源于问题追踪并且有批注，则还原批注
      if (this.fromPage === 'issue') {
        const _this = this
        setTimeout(() => {
          let data = _this.iframeWindow.scene.viewpoints
          if (data.length) {
            let markupDatas = window.scene.viewpoints.filter(v => v.type === 'markup').filter(item => {
              // var reg = RegExp(/批注-/);
              const reg = RegExp(`批注-${(''+_this.markupName).slice(0,8)||''}`,'i');
              return reg.test(item.name)
            })
            if (markupDatas.length) {
              _this.iframeWindow.scene.restoreMarkup(markupDatas[0]);
              _this.iframeWindow.scene.render()
              _this.iframeWindow.setSpecifySceneManageMenuHide(["viewpoint","animation","trigger","pathAnimation","screening"])
            }
          }
        }, 500);
      }
    },
    saveSubmitDetail(){
      // 如果还原失败不能保存
      if (this.reducedErr) {
        this.$message.error('数据还原失败,无法保存!')
        return
      }
      this.editSceneOK('submit') // 报错场景
    },
    // 关闭当前dialog
    closeDetail(){
      if (this.reducedErr) {
        this.returnGoUrl()
        return
      }
      if(this.editTrue){
        this.$confirm('直接退出将不对当前操作进行保存', '确定保存并退出吗?', {
          distinguishCancelAndClose: true,
          confirmButtonText: '保存',
          cancelButtonText: '直接退出'
        }).then(async _ => {
          if (this.reducedErr) {
            this.$message.error('数据还原失败,无法保存!')
            this.$emit('closeDetail')
            return
          }
          this.editSceneOK('closeSubmit'); // 保存场景
        }).catch((action) => {
          if (action === 'cancel') {
            this.returnGoUrl()
          }
        })
      }else{
        this.returnGoUrl()
      }

    },
    returnGoUrl(){
      setTimeout(()=>{
        if(this.formUrlOrProps == 'url'){
          // debugger
            let r = Math.random()
            window.location.href = `${window.bim_config.hasRouterFile}/#/Home/ProjectBoot/sceneManagement/${this.$staticmethod._Get("organizeId")}/${this.$staticmethod.Get("Token")}?r=${r}`
          // this.$router.push({path:`/Home/ProjectBoot/sceneManagement/${this.$staticmethod._Get("organizeId")}/${this.$staticmethod.Get("Token")}`})
        }else{
          this.$emit('closeDetail')
        }
      },1000)

    },
    getHttpUrl(){
      return window.bim_config.newModelApi;
    },

    // 添加批注
    addMarkUp (data) {
      this.showMarkupMenu = true
      this.isShowCreateIssue = false
      this.dialogzIndex = -1

    },

    // 关闭批注
    closeMarkUpMenu () {
      this.showMarkupMenu = false
      this.isShowCreateIssue = true
      this.dialogzIndex = 2
    },
    // 保存批注
    async saveData () {
      let viewPoints = await window.scene.snapMarkup(410).catch(err => {
          console.log(err)
      })
      let name = '批注' + new Date().getTime()
      this.stayName = name
      viewPoints.name = name

      //保存数据至场景（如果不save，scene.viewpoints会为空）
      viewPoints.save(window.scene);
      this.closeMarkUpMenu()
    },
    // 问题追踪
    btncreateissue_click(){
      this.showcreate = true;
      this.isShowCreateIssue = true
    },
    // 点击分享
    openShare(){
      this.dialogVisible = true;
      this.share_title = `分享：${this.sceneItemData.SceneName}`
      this.shareUrl = window.location.href;
      console.log(this.shareUrl, window.location.href,'====')
      this.getOldShareDetail();
    },
    // 改变scene的加号状态
    changeScenewidgetSource(boolean){
      this.iframeWindow.toggleSceneManageEditMode(boolean);
    },
    // 提交编辑的场景
    async editSceneOK(type){
      let _dataJson = this.iframeWindow.getSceneJSON()  // 旧的方法获取场景，scene.toJSON();现在新获取iframe中的场景json，需要通过getSceneJSON()该方法来获取
      let allModel = [];  // 当前场景中所包含的模型ID
      let sceneAndmodelid = []
      this.iframeWindow.scene.features.forEach(item=>{
          if (item.type == 'model') {
              allModel.push(item.modelID)
              sceneAndmodelid.push(
                {
                  modelid: item.modelID,
                  sceneModelid: item.id,
                }
              )
          }
      })
      console.log(sceneAndmodelid,'==sceneAndmodelid')
      let _data = {
        ProjectId: this.projectId,
        SceneJsonDatas: [
          {
            SceneId: this.sceneItemData.SceneId,
            SceneDataJson: '[]',  // 场景数据
            SceneEventDataJson: _dataJson,   // 场景树数据
            SceneEntityDataJson: '[]',  // 场景实体数据
          }
        ],
        SceneIncludeModelID: allModel.join(','), // 当前场景中所有的模型ID
        SceneModelId: JSON.stringify(sceneAndmodelid)
      }
      const resupdate = await this.$api.postsceneJsonDataupdate(this.userID,_data)
      if(resupdate.Ret == 1){
        this.$message.success(resupdate.Msg);
        console.log(this.formUrlOrProps,'=====this.formUrlOrProps=====',this.editTrue ,'====this.editTrue ====_')
        if(type == 'closeSubmit'){
          // this.editTrue = false;
          // this.changeScenewidgetSource(false);
          // if(this.formUrlOrProps == 'url'){
          //   window.location.href = `${window.bim_config.hasRouterFile}/#/Home/ProjectBoot/sceneManagement/${this.$staticmethod._Get("organizeId")}/${this.$staticmethod.Get("Token")}`

          //   // this.$router.push({path:`/Home/ProjectBoot/sceneManagement/${this.$staticmethod._Get("organizeId")}/${this.$staticmethod.Get("Token")}`})
          // }else{
          //   this.$emit('closeDetail')
          // }
          // setTimeout(()=>{
            this.returnGoUrl()
          // },3000)
        }
      }else{
        // if(this.formUrlOrProps == 'url'){
        //     window.location.href = `${window.bim_config.hasRouterFile}/#/Home/ProjectBoot/sceneManagement/${this.$staticmethod._Get("organizeId")}/${this.$staticmethod.Get("Token")}`

        //   // this.$router.push({path:`/Home/ProjectBoot/sceneManagement/${this.$staticmethod._Get("organizeId")}/${this.$staticmethod.Get("Token")}`})
        // }else{
        //   this.$emit('closeDetail')
        // }
        this.returnGoUrl()
      }

    },
    // 获取场景详情
    async getDetailItem(param){
      if (this.SceneIdProps) {
        let _this = this;
        _this.$axios
          .get(
            `${this.$ip("newModelHttpUrl")}/Vault/GetFeature?VaultID=${
              _this.$staticmethod._Get("organizeId")
            }&FeatureID=${_this.SceneIdProps}`
          )
          .then((res) => {
            // 判断是不是场景，如果请求模型详情拿不到数据，则判断为场景
            if (res.status === 200) {
              this.sceneItemData.SceneName = res.data.featureName;
            } else {
              this.getSceneDetail(param)
            }
          })
          .catch((err) => {
          });
      } else {
        await this.getSceneDetail(param)
      }

      const iframe = document.getElementById('scene-iframe')
      const ifrSrc = this.getHttpUrl();
      iframe.src = ifrSrc + `?&projectId=${this.projectId}&lang=cn&edit=${this.editTrue}`

    },

    async getSceneDetail (param) {
      let _data = {
        projectID: param.projectId,
        SceneId: param.SceneId,
        userId: param.userID,
      }
      const res = await this.$api.getscenedetail(_data)
      this.sceneItemData =  res.Data;
    },
    getOldShareDetail(){

      let _this = this;
      _this.$axios
        .get(
          `${window.bim_config.webserverurl}/api/v1/scene/share/detail?ProjectId=${_this.projectId}&SceneId=${_this.SceneId}&userId=${_this.userID}&Token=${this.$staticmethod.Get('Token')}`
        )
        .then((x) => {
          if (x.data.Ret == 1) {
            this.btnStatus = 3
            const result = x.data.Data
            let  _url= window.location.href.split("?")[0]
            _url= _url.replace('/ProjectBoot','')
            this.shareUrl = _url + `?shareUrlToken=${result.Token}`
            this.shareForm = JSON.stringify(result.SceneSharePattern.SceneShareWay)
            this.limitTime = JSON.stringify(result.SceneSharePattern.SceneShareToDate)
            this.shareCode = result.VisitPassword
            this.showCancel = true
          } else {
            this.showCancel = false
            this.btnStatus = 1
          }
        })
        .catch((x) => {
          console.log(x)
        });
    },
    // 复制链接
    copy(){
      var txt=document.getElementById('txtWXUrl');
      txt.select();
      document.execCommand("Copy");
      this.$message({type:"success",message:"复制成功"});
    },
    // 创建分享
    async createShare(){
      // 调用创建分享接口
      // 创建分享/api/v1/scene/CreateShare
      let _this = this;
      let _data = {
        ProjectId: _this.projectId,
        SceneId: _this.SceneId,
        VisitPassword: this.shareForm === '2' ? this.shareCode : '',
        SceneSharePattern: {
          SceneShareWay: JSON.parse(this.shareForm),
          SceneShareToDate: JSON.parse(this.limitTime)
        }
      }
      const res = await this.$api.postsceneCreateShare(this.userID,_data)
      if(res.Ret == 1){
        let  _url= window.location.href.split("?")[0]
        _url= _url.replace('/ProjectBoot','')
        _this.shareUrl = _url + `?shareUrlToken=${res.Data}`
        this.showCancel = true
        if (this.btnStatus === 1) {
          this.btnStatus = 2
        } else if (this.btnStatus === 3) {
          this.copy()
        }
      }
    },
    // 取消分享
    async canceShare(){
      let _this = this;
      let _data = {
        ProjectId: _this.projectId,
        SceneId: _this.SceneId,
      }
      const res = await this.$api.postsceneCancelShare(this.userID,_data)

      if(res.Ret == 1){
        this.$message.warning('已取消分享链接')
        this.showCancel = false
        this.btnStatus = 1
      }

    },
    cancelShareDialog(){
      this.dialogVisible = false;
      this.share_title = '';
    },
    // 需要密码的链接
    getSecretLink () {
      if (this.shareForm === '2') {
        let code = ''
        const codeLength = 4
        // 设置随机字符
        const random = [0, 1, 2, 3, 4, 5, 6, 7, 8, 9]
        for (let i = 0; i < codeLength; i++) {
          const index = Math.floor(Math.random() * 9)
          code += random[index]
        }
        this.shareCode = code
      }
    },
    // 含有密码的分享
    handlePsdConfirm(){
      this.getShareMessage('psd');
    },
    getShareMessage(val){
      let _token = this.$route.query.shareUrlToken;
      let _this = this;
      if(val == 'psd'){
        // sharetoken参数  在5000端口是sharetoken  在5001端口是token
        _this.$axios
          .get(
            `${window.bim_config.webserverurl}/api/v1/scene/share/data?sharetoken=${_token}`,
            {
              headers: {'VisitPassword':this.psdform.psd}
            }
          )
          .then((x) => {
             this.requestData(x);
          })
          .catch((x) => {
            console.log(x)
          });
      }else{
        _this.$axios
          .get(
            `${window.bim_config.webserverurl}/api/v1/scene/share/data?sharetoken=${_token}`,
          )
          .then((x) => {
             this.requestData(x)
          })
          .catch((x) => {
            console.log(x)
          });
      }

    },
    requestData(x){
      let _data = x.data.Data;
      if (x.data.Ret == 1) {
        this.psdVisible = false;
        this.sceneItemData = _data.Data;
        this.projectId = _data.ProjectId;
        this.SceneId = _data.SceneId;
        const iframe = document.getElementById('scene-iframe')
        const ifrSrc = this.getHttpUrl();
        iframe.src = ifrSrc + `?&projectId=${this.projectId}&lang=cn`
      } else if(x.data.Ret == 401) {
        this.psdVisible = true;
      }
    },

    getQueryString (name) {
      var result = window.location.href.match(new RegExp('[\?\&]' + name + '=([^\&]+)', 'i'))
      if (result == null || result.length < 1) {
        return ''
      }
      return result[1]
    },
    // 问题追踪==取消
    cancelcreate(){
      this.showcreate = false;
      if (this.stayName) {
        // 删除还未保存的批注
        let eq = window.scene.viewpoints.findIndex(vp => vp.name == this.stayName)
            window.scene.viewpoints.splice(eq, 1)
            this.stayName = ''
      }
    },
    // 问题追踪==确定
    createok(obj) {
      let _this = this;
      //debugger;
      if (!obj.title || obj.title == "" || obj.title.trim() == '') {
        _this.$message.error("请输入标题");
        EventBus.$emit("R_InitiateProblem",true);
        return;
      }
      if (obj.deadlinetimeval == "") {
        _this.$message.error("请选择截止时间");
        EventBus.$emit("R_InitiateProblem",true);
        return;
      }

      // 参数：截止时间
      var timestr = _this.timeToString(obj.deadlinetimeval);
      // 参数：问题标题
      var title = obj.title;
      // 参数：参与人
      var joinerstr = "";
      if (obj.addingjoiners && obj.addingjoiners.length) {
          for (var i = 0; i < obj.addingjoiners.length; i++) {
              joinerstr += `${i == 0 ? "" : ","}${obj.addingjoiners[i].UserId}`;
          }
      }
      // 参数：附件FileId
      var fileidstr = "";
      if (obj.addingFiles && obj.addingFiles.length) {
          for (var i = 0; i < obj.addingFiles.length; i++) {
              fileidstr += `${i == 0 ? "" : ","}${obj.addingFiles[i].FileId}`;
          }
      }
      // 新的模型引擎，要保存当前选中构件，获取getSceneJSON()保存当前场景
      let elements = this.iframeWindow.scene.selectedObjects;
      let selectedObjects =  JSON.stringify(Array.from(elements.keys()))
      _this.showcreate = false;

      if (this.stayName && window.scene.viewpoints.length) {
        let eq = window.scene.viewpoints.findIndex(vp => vp.name == this.stayName)
        let viewPoints = window.scene.viewpoints
        viewPoints[eq].name = '批注-' + title.slice(0, 8) + '' + new Date().getTime()

         // 这里追加一段逻辑：只保留索引相等的那个viewpoint
         window.scene.viewpoints = window.scene.viewpoints.filter((item,idx)=>{ return idx === eq })
      }

      this.$nextTick(()=>{
        let _scenejson = this.iframeWindow.getSceneJSON()  // 旧的方法获取场景，scene.toJSON();现在新获取iframe中的场景json，需要通过getSceneJSON()该方法来获取
        _this
        .$axios({
          method: "post",
          url: `${this.$issueBaseUrl.AddIssue}`,
          data: {
              Token: _this.$staticmethod.Get("Token"),
              RealName: _this.$staticmethod.Get("RealName"),
              Title: title,
              JoinerIds: joinerstr,
              FileIds: fileidstr,
              EndDateStr: timestr,
              organizeId: _this.$staticmethod._Get("organizeId"),
              ModelID: _this.SceneId, // 新的引擎没有modelid，使用的是SceneId
              IssueTypeId: obj.IssueTypeId || '',
              ImageUrl:obj.ImageUrl,//_this.ImageUrl,
              ViewPointID: selectedObjects,  // 新引擎没有这个视点，selectedObjects是选中的构件
              ImageIds: obj.ImageIds,
              Isoverride:_scenejson  // 新引擎没有视点，直接存的是getSceneJSON()
          }
        })
        .then(x => {
            if (x.data.Ret < 0) {
              _this.$message.error(x.data.Msg)
            } else {
              // 刷新
              _this.showcreate = false;
              this.stayName = ''
              _this.$message.success(x.data.Msg);

              // 追加一段逻辑，发起问题成功后，删除所有的批注
              window.scene.viewpoints = window.scene.viewpoints.filter(item => item.type !== 'markup')
              if(_this.iframeWindow) {
                _this.iframeWindow.deepUpdateScene('markup')
              }
            }
        })
        .catch(x => {
          // 刷新
          _this.showcreate = false;
        });
      })

    },
    // 问题追踪图片
    _imgsrc(){
      this.imageUrl = val;
    },
    timeToString(date) {
      let year = date.getFullYear();
      let month = date.getMonth() + 1;
      let dat = date.getDate();
      let h = date.getHours();
      let m = date.getMinutes();
      let s = date.getSeconds();
      let tostr = `${year}-${month < 10 ? "0" + month : month}-${
          dat < 10 ? "0" + dat : dat
      } ${h < 10 ? "0" + h : h}:${m < 10 ? "0" + m : m}:${
          s < 10 ? "0" + s : s
      }`;
      return tostr;
    },

  },
  destroyed(){
    window.removeEventListener('message', this.listeningMessage)
    this.$Bus.$off('getUserSessionError',this.returnGoUrl)

  },
}
</script>
<style lang="scss" scoped>
.scene-iframe-content{
  width: 100%;
  height: 100%;
}
.content-frame{
  width: 100%;
  height: calc(100% - 37px);
}
.content-frame-edit{
  height: 100%;
}
.detail-header.header-edit{
  position: absolute;
  top:0;
  left: 200px;
  width: calc(100% - 200px);
  padding-left: 0px;
  .title-name{
    flex: 1;
  }
  p{
    text-align: center;
    position: absolute;
    left: 0;
    right: 200px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    // margin-left: -200px;
  }
}
.detail-header{
  height: 37px;
  line-height: 37px;
  border-bottom: 1px solid rgba(0,0,0,.15);
  position: relative;
  display: flex;
  background: #061326;
  justify-content: space-between;
  color: #fff;
  padding-left: 20px;
  ul{
    display: flex;
    position: absolute;
    right: 0;
    li{
      margin: 0 8px;
      cursor: pointer;
      i{
        font-size: 24px;
        vertical-align: middle;
      }
    }
  }
  .issue{
    color: #f3ac00;
  }
}

.scene-edit{
  position: absolute;
  bottom: 40px;
  right: 40px;
}
.edit-icon{
  cursor: pointer;
  display: inline-block;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  text-align: center;
  background: rgba(0,0,0,.8);
  i{
    text-align: center;
    vertical-align: middle;
    color: #fff;
    font-size: 24px;
    line-height: 40px;
  }
}
.link-body{
  margin: 10px 20px;
  text-align: left;
  .link-top, .link-mid, .link-btm{
    width: 100%;
    margin-bottom: 24px;
  }

  .el-radio{
    line-height: 26px;
  }
  .top-title{
    display:flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    .title{
      font-size: 14px;
    }
  }
  .share-title{
    font-size: 14px;
  }
  .share-link{
    width: 100%;
    height: 32px;
    line-height: 24px;
    border: 1px solid #ccc;
    border-radius: 6px;
    font-size: 14px;
    padding: 4px;
    margin: 4px 0;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    cursor: pointer;
  }
  .share-txt{
    font-size: 12px;
  }

  .share-radio{
    color: #fff;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    padding-top: 8px;
    /deep/ .el-radio{
      margin-bottom: 8px;
    }

  }
  .cancelBtn{
    width: 70px;
    height: 24px;
    line-height: 24px;
    text-align: center;
    font-size: 12px;
    border: 1px solid #98A3B3;
    border-radius: 4px;
    background: transparent;
    cursor: pointer;
    &:hover{
      border: 1px solid #1890ff;
      color: #1890ff;
    }
  }
}
._css-addBtnCtn {
  display: flex;
  flex-direction: row-reverse;
  height: 64px;
  align-items: center;
  box-sizing: border-box;
  padding-right: 8px;
}
</style>
<style scoped>
.link-body /deep/ .el-input__inner{
  border:1px solid rgba(0,0,0,.09) !important;
  border-width: 1px !important;
  border-radius: 4px;
}
.share{
  display: inline-block;
  width: 20px;
  height: 20px;
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center center;
}
.icon-scene-share{
  background-image: url('../../../assets/images/scene-share.png');
}
.icon-save-scene{
  width: 15px;
  margin-right: 5px;
  height: 15px;
  background-image: url('../../../assets/images/save-scene.png');
}
.icon-scene-close{
  width: 12px;
  height: 12px;
  margin-right: 5px;
  background-image: url('../../../assets/images/scene-close.png');
}
.detail-header ul li.close.save{
  background: #2680fe;
}
.detail-header ul li.close{
  width: 60px;
  background: #ff6640;
  font-size: 12px;
  height: 24px;
  line-height: 24px;
  margin: auto 8px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
}

</style>
