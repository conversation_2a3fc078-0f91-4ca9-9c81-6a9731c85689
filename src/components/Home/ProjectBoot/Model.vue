<template>
  <div class="model-new-content" @click="closeAll($event)">
    <!--模型管理头部-->
    <div class="model-manage-header">
      <div class="label">模型管理</div>
    </div>

    <!--模型管理主要内容-->
    <div class="model-manage-main" :class="{ collapsed: isTreeCollapsed }">
      <div
        v-if="menuBusinessCode !== 'allmodel_phase'"
        class="model-manage-col1"
      >
        <div class="col1-title">
          <i class="icon-newface-model"></i>
          <div class="label">模型管理</div>
          <i class="el-icon-plus import" @click="isShowingImportTemplate = true"></i>
        </div>
        <el-tree
          class="el-tree-cus"
          node-key="BusinessCode"
          ref="elTree"
          empty-text="暂无子级"
          :highlight-current="true"
          :default-expand-all="false"
          :expand-on-click-node="true"
          :auto-expand-parent="true"
          :default-expanded-keys="treeExpandedKeys"
          :data="treeData"
          :props="elTreeProps"
          @node-click="onElTreeNodeClick"
        >
          <div class="el-tree-node-cus" slot-scope="{ node, data }">
            <el-tooltip
              effect="dark"
              :content="node.label"
              placement="top"
              :enterable="false"
            >
              <div class="label">{{ node.label }}</div>
            </el-tooltip>
            <div class="extra" @click.stop="onExtraBtnClick(node, $event)">
              <div class="extra-btn icon-interface-list"></div>
              <div class="extra-label">{{ getPhaseModelCount(data) }}</div>
            </div>
          </div>
        </el-tree>
        <div
          class="collapse-bar"
          @click.stop="isTreeCollapsed = !isTreeCollapsed"
        ></div>
      </div>
      <div class="model-manage-col2">
        <div class="up-model-btn">
          <button
            v-if="UrlPhase != 'allmodel_phases' && hasSomeAuth('Model_Upload')"
            @click.stop="isUploadListDialogVisible = true"
          >
            上传模型
          </button>
          <span class="txtSearch">
            <i class="icon-interface-search"></i>
            <input type="text" v-model="searchName" placeholder="搜索模型" />
          </span>
        </div>

        <input
          type="file"
          style="display: none"
          id="updateModelMv"
          accept=".mv"
          @change="updateVault"
        />
        <input
          type="file"
          style="display: none"
          id="idupFeatureThumbnail"
          accept="image/*"
          @change="uploadFeatureThumbnailImg"
        />
        <div
          ref="tableContainer"
          class="model-list"
          :style="this.onClickModelPhase === 'allmodel_phase' ? 'width:100%': 'width:calc(100% - 60px)'"
          v-loading="isLoadingFeaturesList"
          :class="{ isLoadingFeaturesList }"
        >
          <!-- 子级模型表格 -->
          <div class="model-list-children">
            <el-table
              v-if="childrenFeaturesConvertingMatched.length || childrenFeaturesFinishedMatched.length"
              :data="sortedData"
              style="width: 100%"
              @row-click="handleRowClick"
              row-class-name="table-row-cursor"
              :height="tableHeight"
              :default-sort="{ prop: 'createTime', order: 'descending' }"
              @sort-change="handleSortChange"
            >
              <el-table-column label="缩略图" min-width="80" align="center">
                <template slot-scope="scope">
                  <div class="thumbnail-container">
                    <img
                      v-if="scope.row.TaskQueueState"
                      class="img-conver"
                      :class="scope.row.TaskQueueState == '1' ? 'imgloading' : ''"
                      :src="getItemState(scope.row, 'img')"
                      alt=""
                    />
                    <img
                      v-else
                      class="img-model-list"
                      :src="getItemImg(scope.row)"
                      alt=""
                    />
                  </div>
                </template>
              </el-table-column>

              <el-table-column label="模型名称" min-width="200"  prop="featureName" sortable>
                <template slot-scope="scope">
                  <el-tooltip effect="dark" :content="scope.row.featureName || scope.row.ModelName" placement="top">
                    <span class="model-name-text">{{ scope.row.featureName || scope.row.ModelName }}</span>
                  </el-tooltip>
                </template>
              </el-table-column>

              <el-table-column label="更新时间" min-width="100" align="center" prop="createTime" sortable>
                <template slot-scope="scope">
                  <span v-if="scope.row.TaskQueueState" class="status-text">
                    {{ getItemState(scope.row, "stateText") }}
                  </span>
                  <span v-else class="update-time-text">
                    {{ new Date(scope.row.createTime).toLocaleString("chinese", { hour12: false }) }}
                  </span>
                </template>
              </el-table-column>

              <el-table-column label="关联文档" min-width="100" align="center">
                <template slot-scope="scope">
                  <span class="link-count">{{ scope.row.docsCount }}</span>
                </template>
              </el-table-column>

              <el-table-column label="操作" min-width="80" align="center">
                <template slot-scope="scope">
                  <div class="operation-buttons">
                    <div style="width: 100%;height: 100%;display: flex;align-items: center;justify-content: center" v-if="scope.row.TaskQueueState && (scope.row.TaskQueueState == '3' || scope.row.TaskQueueState == '4' || scope.row.TaskQueueState == '5')">
                      <i
                        class="icon-interface-model-delete setting-btn"
                        @click.stop="handelClickConvertingList(scope.row)"
                      ></i>
                    </div>
                    <div style="width: 100%;height: 100%;display: flex;align-items: center;justify-content: center" v-else @click.stop="handelClickUnitList($event, scope.row)">
                      <i
                        class="icon-interface-set_se setting-btn"
                        :title="'菜单'"
                      ></i>
                    </div>
                    <ul
                      :style="getMenuStyle()"
                      class="set-list"
                      :class="{
                    'set-list-show':
                      checkedModel && checkedModelID == scope.row.featureID
                  }"
                      v-if="UrlPhase == 'allmodel_phases'"
                    >
                      <li @click.stop="getModelDetail(scope.row)">
                        <i class="icon-interface-model_list"></i>模型详情
                      </li>
                    </ul>
                    <ul
                      class="set-list"
                      :style="getMenuStyle()"
                      :class="{
                    'set-list-show':
                      checkedModel && checkedModelID == scope.row.featureID
                  }"
                      v-if="UrlPhase != 'allmodel_phases'"
                    >
                      <li @click.stop="getModelDetail(scope.row)">
                        <i class="icon-interface-model_list"></i>
                        模型详情
                      </li>
                      <li
                        @click.stop="openShare(scope.row, 'Model_Share')"
                        :class="hasSomeAuth('Model_Share') ? '' : 'not-edit'"
                      >
                        <i class="icon-interface-share-fill"></i>
                        分享
                      </li>
                      <li @click.stop="compareModel(scope.row)">
                        <i class="icon-duibi font-small"></i>
                        对比
                      </li>
                      <li
                        @mouseover="mouseovereditmodel($event)"
                        @mouseout="mouseouteditmodel($event)"
                      >
                        <i class="icon-interface-edit"></i>
                        <p>编辑模型</p>
                        <i class="icon-arrow-right_outline imore"></i>
                        <ul
                          v-show="itemMenuName == 'edit'"
                          class="itemMenu edit"
                          :style="getItemMenuStyle()"
                        >
                          <li
                            @click.stop="updateFeatureName(scope.row, 'Model_Edit')"
                            :class="hasSomeAuth('Model_Edit') ? '' : 'not-edit'"
                          >
                            <i class="icon-interface-edit"></i>
                            重命名
                          </li>
                          <li
                            @click.stop="updatePhase(scope.row, 'Model_Edit')"
                            :class="hasSomeAuth('Model_Edit') ? '' : 'not-edit'"
                          >
                            <i class="icon-interface-share-fill"></i>
                            移动到
                          </li>
                          <li
                            @click.stop="
                          updateFeatureThumbnail(scope.row.featureID, 'Model_Edit')
                        "
                            :class="hasSomeAuth('Model_Edit') ? '' : 'not-edit'"
                          >
                            <i class="icon-interface-edit"></i>
                            更新缩略图
                          </li>
                        </ul>
                      </li>
                      <li
                        @click.stop="updateMvFile(scope.row, 'Model_Upload')"
                        :class="hasSomeAuth('Model_Upload') ? '' : 'not-edit'"
                      >
                        <i class="icon-interface-cloud-download_could"></i>
                        更新模型
                      </li>
                      <li
                        @click.stop="deleteModel(scope.row, 'Model_Delete')"
                        :class="hasSomeAuth('Model_Delete') ? '' : 'not-edit'"
                      >
                        <i class="icon-interface-model-delete"></i>
                        删除
                      </li>
                    </ul>
                  </div>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
        <re-name-form
          v-if="status_reset_name"
          :modelName="m_modelNewName"
          :modelId="resetNameID"
          @CloseReName="handelClickResetNameClose"
          @changeModelName="handelClickResetNameSave"
          class="RN"
        ></re-name-form>

        <div class="model-detail-iframe" v-if="modelDetailShow">
          <model-new-detail
            :modelDetail="true"
            :VaultID="VaultID"
            :featureID="modelId"
            :modelTitle="modelTitle"
            :versionNO="SelModelObj.currentVersion"
            :extension="SelModelObj.extension"
            @closeModelDetail="closeModelDetail"
            @shareModel="shareModelHandle"
          >
          </model-new-detail>
        </div>

        <newModelInfo
          class="mi"
          v-if="showModelInfo"
          :VaultID="VaultID"
          :featureItem="modelDetailItem"
          :VersionNO="modelDetailItem.currentVersion"
          @CloseModelInfo="showModelInfo = false"
          @openVersion="handelClickModelUrl"
        ></newModelInfo>

        <zdialog-function
          :init_title="share_title"
          :init_zindex="10000"
          :init_innerWidth="400"
          :init_width="400"
          init_closebtniconfontclass="icon-suggested-close"
          :init_usecustomtitlearea="false"
          @onclose="dialogVisible = false"
          v-if="dialogVisible"
        >
          <div slot="mainslot" class="link-body">
            <div class="link-top" v-if="showCancel">
              <div class="top-title">
                <div class="title">点击复制该链接</div>
                <div class="cancelBtn" @click="canceShare" v-if="showCancel">
                  取消分享
                </div>
              </div>
              <div @click="copy">
                <input
                  class="share-link"
                  id="txtWXUrl"
                  readonly
                  v-model="shareUrl"
                  type="text"
                />
              </div>
              <div class="share-txt">
                {{
                  shareForm === "2" ? "加密（密码: " + shareCode + "）" : "公开"
                }}
                有效期{{
                  limitTime === "-1"
                    ? "永久有效"
                    : limitTime === "7"
                    ? "一周"
                    : "一天"
                }}
              </div>
            </div>
            <div class="link-mid" v-if="btnStatus !== 2">
              <div class="share-title">分享形式</div>
              <div class="share-radio">
                <el-radio v-model="shareForm" label="1">公开</el-radio>
                <el-radio v-model="shareForm" label="2" @change="getSecretLink"
                  >加密</el-radio
                >
              </div>
            </div>
            <div class="link-btm" v-if="btnStatus !== 2">
              <div class="share-title">有效时间</div>
              <div class="share-radio">
                <el-radio v-model="limitTime" label="1">一天</el-radio>
                <el-radio v-model="limitTime" label="7">一周</el-radio>
                <el-radio v-model="limitTime" label="-1">永久有效</el-radio>
              </div>
            </div>
          </div>
          <div slot="buttonslot" class="_css-addBtnCtn">
            <zbutton-function
              v-if="btnStatus === 1 || btnStatus === 3"
              :init_text="btnStatus === 1 ? '创建链接' : '确定'"
              :init_fontsize="14"
              :debugmode="true"
              :init_height="'40px'"
              :init_width="'80px'"
              @onclick="createShare"
            >
            </zbutton-function>
            <zbutton-function
              v-if="btnStatus === 2"
              :init_text="'复制链接'"
              :init_fontsize="14"
              :debugmode="true"
              :init_height="'40px'"
              :init_width="'80px'"
              @onclick="copy"
            >
            </zbutton-function>
            <zbutton-function
              :init_text="'取消'"
              :init_color="'rgba(24, 144, 255)'"
              :init_bgcolor="'#fff'"
              :init_fontsize="14"
              :debugmode="true"
              :init_height="'40px'"
              :init_width="'80px'"
              @onclick="cancelShareDialog"
            >
            </zbutton-function>
          </div>
        </zdialog-function>
        <move-model-phase
          v-if="showModelPhase"
          :modelName="SelModelObj.featureName"
          :modelId="SelModelObj.featureID"
          :modelPhase="UrlPhase"
          @CloseMovePhase="showModelPhase = false"
          @reloadModellist="onMoveFinished"
          class="MP"
        ></move-model-phase>
        <modelVersionList
          v-if="compareListshow"
          :list="versionList"
          @getVersionCompare="getVersionCompare"
          @close="compareListshow = false"
        >
        </modelVersionList>
        <div class="model-detail-iframe" v-if="isCompareModel">
          <modelCompare
            v-if="isCompareModel"
            :compareArr="compareArr"
            :modelDetail="modelCompareItem"
            @close="compareClose"
          >
          </modelCompare>
        </div>
      </div>
    </div>

    <!--弹窗：添加子级、重命名本级、删除本级-->
    <div
      v-if="phaseInEditInfo.target && modalInfoForPhaseActions.visible"
      class="_css-pjsection-btnlist"
      :style="styleForPhaseActions"
    >
      <div
        class="_css-pjsection-btn _css-pjsection-btn-add"
        @click.stop="showAddPhaseModal"
      >
        <div class="_css-pjsection-btnicon icon-interface-addnew"></div>
        <div class="_css-pjsection-btntext">添加</div>
      </div>
      <div
        class="_css-pjsection-btn _css-pjsection-btn-rename"
        @click.stop="showRenamePhaseModal"
      >
        <div class="_css-pjsection-btnicon icon-interface-edit-model"></div>
        <div class="_css-pjsection-btntext">重命名</div>
      </div>
      <div
        class="_css-pjsection-btn _css-pjsection-btn-delete"
        @click.stop="onDelPhaseClick"
      >
        <div class="_css-pjsection-btnicon icon-interface-model-delete"></div>
        <div class="_css-pjsection-btntext">删除</div>
      </div>
    </div>
    <CompsSingleField
      v-if="modalInfoForPhaseActions.isPhaseModalVisible"
      placeholder="模型管理文件"
      inputicon="icon-none"
      :zIndex="1000"
      :title="
        modalInfoForPhaseActions.type == 'add'
          ? '新建模型管理文件'
          : `重命名模型管理文件`
      "
      :inittext="phaseNameForModal"
      @onok="onPhaseModalOk"
      @oncancel="onPhaseModalCancel"
      @oninput="onPhaseModalInput"
    ></CompsSingleField>

    <!--文件上传队列弹窗-->
    <div
      v-show="isUploadQueueDialogVisible"
      class="fileListToUpload"
      :class="{ collapsed: isTreeCollapsed }"
    >
      <!-- <div class="title">
        <div class="label">模型上传队列({{ fileListToUpload.length }})</div>
        <div class="iconClose" @click.stop="isUploadQueueDialogVisible = false">
          <i class="el-icon-close"></i>
        </div>
      </div>
      <div class="content">
        <div class="header">
          <div class="item name">文件名称</div>
          <div class="item size">大小</div>
          <div class="item status">进度</div>
        </div>
        <el-scrollbar class="elScrollbarCus">
          <div class="files">
            <div
              v-for="(file, idx) in fileListToUpload"
              :key="idx"
              class="fileItem"
            >
              <el-tooltip effect="dark" :content="file.name" placement="left">
                <div class="item name">{{ file.name }}</div>
              </el-tooltip>
              <div class="item size">{{ file.size }}</div>
              <div class="item status">{{ file.status }}</div>
            </div>
          </div>
        </el-scrollbar>
      </div> -->

      <div class="css-bsb css-h100">
        <div class="css-h48  css-h100 css-common-tablehead">
          <el-row class="css-h100">
            <el-col :span="12" class="css-h100">
              <div class="css-fc css-h100">
                <div class="css-ml10 css-b css-bsb css-h22 css-fc">
                  {{
                    fileListToUpload.length
                      ? `${fileListToUpload.length}个文件正在`
                      : "暂无文件"
                  }}上传
                </div>
              </div>
            </el-col>
            <el-col :span="12" class="css-h100">
              <div class="css-fc_r css-h100">
                <div
                  class="css-b css-icon20 css-mr24 icon-suggested-close css-cp"
                  @click.stop="isUploadQueueDialogVisible = false"
                ></div>
                <!-- <div
                  class="css-b css-icon20 css-mr16 icon-suggested-minus css-cp"
                  @click="isUploadQueueDialogVisible = false"
                ></div> -->
              </div>
            </el-col>
          </el-row>
        </div>
        <div :class="isTreeCollapsed ? '_css-docqueue-close' : '_css-h-48'">
          <el-table
            :border="true"
            :stripe="true"
            :data="fileListToUpload"
            :default-sort="{ prop: 'name', order: 'descending' }"
            :key="tableKey"
            max-height="400"
            class="_css-table-ele _css-customstyle"
          >
            <el-table-column
              fixed="left"
              :resizable="false"
              class="_css-col-filename"
              prop="name"
              label="文件名称"
              min-width="50"
              sortable
            >
              <template slot-scope="scope">
                <i
                  :class="
                    'css-icon20 css-fs18 css-fc css-jcsa ' +
                      $staticmethod.getIconClassByExtname(
                        scope.row.name,
                        scope.row.size,
                      )
                  "
                ></i>
                <span
                  :title="scope.row.name"
                  class="css-ml10 css-ellipsis css-w100"
                  >{{ scope.row.name }}</span
                >
              </template>
            </el-table-column>

            <el-table-column
              :formatter="FileSize_formatter"
              :resizable="false"
              class="_css-col-filesize"
              prop="size"
              label="大小"
              min-width="20"
              sortable
            ></el-table-column>

            <el-table-column
              :resizable="false"
              class="_css-col-updatetime"
              prop="status"
              label="进度"
              min-width="30"
              sortable
            >
              <template slot-scope="scope">
                <div class="css-h100 css-w100 css-bsb css-fc">
                  <div v-if="scope.row.isZeroBytes">文件大小为空</div>
                  <div
                    v-else
                    class="_css-progress-font-running"
                    :class="{ isError: scope.row.isError }"
                  >
                    {{ scope.row.status }}
                  </div>
                </div>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </div>

    <!--选取文件、上传文件的弹窗-->
    <div v-if="isUploadListDialogVisible" class="uploadDialogWrapper">
      <div class="uploadContent">
        <div class="header">
          <div class="title">添加模型</div>
          <div class="rest">
            <i
              v-if="enableModelConverting"
              class="action el-icon-question"
              @click.stop="isHelpUploadModelVisible = true"
            ></i>
            <i
              class="action el-icon-close"
              @click.stop="isUploadListDialogVisible = false"
            ></i>
          </div>
        </div>
        <el-upload
          multiple
          class="upload"
          :accept="uploadAcceptStr"
          action=""
          ref="upload"
          :auto-upload="false"
          :http-request="uploadFile"
          :before-upload="uploadFileBefore"
          :on-progress="uploadFileProgress"
          :on-error="uploadFileError"
          :on-success="uploadFileSuccess"
        >
          <div slot="trigger" class="trigger">
            <i class="el-icon-upload"></i>
            <div class="el-upload__text">
              点击选择
            </div>
          </div>
        </el-upload>
        <div class="footer">
          <!-- <el-button class="btn cancel" @click.stop="isUploadListDialogVisible = false;">关闭</el-button> -->
          <el-button
            class="btn ok"
            type="primary"
            @click.stop="doUploadBusiness"
            >确定</el-button
          >
        </div>
      </div>
    </div>

    <!--帮助上传模型弹窗-->
    <div v-if="isHelpUploadModelVisible" class="uploadHelpWrapper">
      <div class="content">
        <div class="header">
          <i
            class="icon el-icon-close"
            @click.stop="isHelpUploadModelVisible = false"
          ></i>
        </div>
        <iframe
          class="iframeHelp"
          src="/static/HelpUploadModel/doc.html"
        ></iframe>
      </div>
    </div>

    <!--选择并裁剪图片组件-->
    <div class="wrapperForClipImage" v-if="isImgClipVisible">
      <CompsImageClippingUpload2
        ref="refClipImage"
        :width="700"
        :height="440"
        :cropAspectRatio="4/3"
        @onCrop="onImgCropDone"
        @close="isImgClipVisible=false"
        >
      </CompsImageClippingUpload2>
  </div>
    <!-- 导入模板 -->
    <CompsStepTip4
      :zIndex="1000"
      v-if="isShowingImportTemplate === true"
      width="504"
      :bc_guid="leftMenuIdActivated"
      @oncancel="isShowingImportTemplate = false"
      @onok="importFileSuccess"
      title="导入数据"
    ></CompsStepTip4>
  </div>
</template>
<script>
// newModelInfo=模型详情；ModelNewDetail=新模型加载；
import newModelInfo from "@/components/Home/ProjectBoot/Model/NewModelInfo";
import ModelNewDetail from "./modelNewDetail.vue";
import modelVersionList from "@/components/CompsModel/modelVersionList"; // 对比
import modelCompare from "@/components/CompsModel/modelCompare"; // 模型对比
import reNameForm from "@/components/Home/ProjectBoot/Model/ReNameForm"; //重命名
import MoveModelPhase from "@/components/Home/ProjectBoot/Model/MoveModelPhase"; //移动到阶段
import CompsSingleField from "@/components/CompsCommon/CompsSingleField"; // 添加模型文件夹
import CompsImageClippingUpload2 from "@/components/CompsCommon/CompsImageClippingUpload2"; // 上传并裁剪图片组件
import CompsStepTip4 from "@/components/CompsCommon/CompsStepTip4";
const regForOnlyEmptyStr = /^\s*$/i;
const regForEmptyStr = /\s*/gi;
const FileUploadStatusMap = {
  toUpload: "待上传",
  error: "上传失败",
  done: "上传完成"
};
export default {
  name: "ModelNew",
  data() {
    return {
      sortProp: 'createTime',
      sortOrder: 'descending',
      docsList: [],
      isShowingImportTemplate: false,
      tableHeight: 0,
      onClickModelPhase:'',
      addLoading: false,
      childrenFeaturesConverting: [], // 后代正在转换的模型列表
      childrenFeaturesFinished: [], // 后代所有模型列表
      VaultID: "", // 模型
      modelId: "", //  模型id
      modelTitle: "", // 模型title
      isLoad: 0,
      editThumbnailfeatureID: "", // 记录编辑的ID
      updateFileID: "", // 记录更新的ID
      status_reset_name: false, // 重命名弹框
      m_modelNewName: "", // 重命名input
      checkedModel: false, //
      checkedModelID: "",
      modelDetailShow: false,
      showModelInfo: false, // 模型详情
      VersionNO: "", // 模型版本号
      modelDetailItem: "",
      UrlPhase: "",
      shareModelInfo: {}, // 分享的这个对象
      SelModelObj: {}, // 选择的模型对象
      dialogVisible: false, // 分享弹窗
      share_title: "分享模型",
      showCancel: false, // 取消分享
      btnStatus: 1, // 1 创建链接  2 复制链接  3 确定
      shareCode: "1234", // 密码
      shareForm: "1", // 1 公开 2 加密
      limitTime: "1", // -1 永久， 7 一周  1一天
      shareID: "",
      userID: "",
      showModelPhase: false,
      searchName: "", // 搜索
      userPhaseAuthData: [], // 当前人员的模型阶段操作权限
      itemMenuName: "",
      mouseoverPos: {},
      unitPos: {},
      compareListshow: false,
      versionList: [],
      isCompareModel: false,
      compareArr: [],
      modelCompareItem: {},

      elTreeProps: {
        children: "Children",
        label: "MenuName"
      }, // el-tree的默认属性
      modalInfoForPhaseActions: {
        top: 0, //包含添加、重命名、删除按钮的弹窗的上边距
        left: 0, //包含添加、重命名、删除按钮的弹窗的做边距
        visible: false, // 包含添加、重命名、删除按钮的弹窗是否可见
        type: "", // 有效值：add rename del
        isPhaseModalVisible: false, // 按钮对应的阶段修改弹窗是否可见
        phaseNameFromModal: "" // 弹窗内部用户正在编辑的名字
      }, // 添加子级、重命名本级、删除本级的相关信息
      phaseInEditInfo: {
        target: null //正在操作的树节点
      }, // 当前正在编辑的阶段信息
      menuSystemButton: [], // 按钮权限
      modelManageTreeKey: "", // 右侧el-tree的key
      isTreeCollapsed: false, // 树是否折叠
      selfFeaturesFinished: [], // 自身层级已完成转换的Feature
      selfFeaturesConverting: [], // 自身层级正在转换的Feature
      isLoadingFeaturesList: false, // 是否正在加载Feature列表
      isLoadingConvertingList: false, // 是否正在加载转换中的Feature列表
      leftMenuIdActivated: "", // 左侧选中的菜单Id

      timerForModelConverting: null, // 轮询某阶段和所有后代是否有转换中模型的定时器
      fileListToUpload: [], // 文件上传列表
      isUploadQueueDialogVisible: false, // 文件上传队列弹窗是否可见
      userMenuList: [], // this.$urlPool.GetUserMenuTree接口的结果
      isUploadListDialogVisible: false, // el-upload组件是否可见
      tableKey: Math.random(),
      isHelpUploadModelVisible: false,
      enableModelConverting: false, // 是否允许上传需要转换的模型(关系到是否启用轮询逻辑)
      isImgClipVisible: false // 图片裁剪组件是否可见
    };
  },
  props: {
    phaseModelCountMap: {
      type: Object,
      default() {
        return {};
      }
    }
  },
  components: {
    newModelInfo,
    ModelNewDetail,
    modelVersionList,
    modelCompare,
    MoveModelPhase,
    reNameForm,
    CompsSingleField,
    CompsImageClippingUpload2,
    CompsStepTip4
  },
  created() {
    this.enableModelConverting = window.bim_config.EnableModelCoverting;
    // 获取权限
    this.VaultID = this.$staticmethod._Get("organizeId");
    this.userID = this.$staticmethod.Get("UserId");

    this.tokenValue = this.$staticmethod.Get("Token");
    this.UrlPhase = this.$route.params.Phase;
    this.leftMenuIdActivated = sessionStorage.getItem("leftMenuIdActivated");
    sessionStorage.setItem("modelManageTreeKey", this.UrlPhase);
    this.setUserMenuTree();

    this.setMenuBtn();
    this.leftMenuIdActivated = sessionStorage.getItem("leftMenuIdActivated");
    this.$Bus.$on("LeftMenuIdActivatedChanged", this.onLeftMenuChange);
  },
  mounted() {
    this.setTableHeight();
    window.addEventListener('resize', this.setTableHeight);
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.setTableHeight);
    clearTimeout(this.timerForModelConverting);
    this.timerForModelConverting = null;
    sessionStorage.setItem("modelManageTreeKey", "");
    this.$Bus.$off("LeftMenuIdActivatedChanged", this.onLeftMenuChange);
  },
  watch: {
    leftMenuActivated:{
      handler(cv,ov) {
      // console.log("watch-leftMenuActivated", cv, ov);
        if (cv) {
          if (!ov) {
            // console.log("watch-leftMenuActivated第一次");
            this.UrlPhase = this.$route.params.Phase;
            clearTimeout(this.timerForModelConverting);
            this.timerForModelConverting = null;
            this.resetRelatedConvertingList();
            this.closeAllDialog();
            this.fileListToUpload = [];
            setTimeout(() => {
              let targetPhase = this.$route.params.Phase
              targetPhase = targetPhase === 'allmodel_phases' ? 'allmodel_phase' : targetPhase
              this.extendHighlightModelManageTree(targetPhase,true)
            }, 20);
          }
        }
      },
      immediately: true
    },
    // 监听模型数据变化，自动获取关联文档数量
    childrenFeaturesFinished: {
      handler() {
        this.$nextTick(() => {
          this.getModelRelatedDocs();
        });
      },
      deep: true
    },
    selfFeaturesFinished: {
      handler() {
        this.$nextTick(() => {
          this.getModelRelatedDocs();
        });
      },
      deep: true
    }
  },
  computed: {
    sortedData(){
      return [...this.childrenFeaturesConvertingMatched, ...this.childrenFeaturesFinishedMatched].sort((a, b) => {
        if (this.sortOrder === 'ascending') {
          return a[this.sortProp] > b[this.sortProp] ? 1 : -1;
        } else if (this.sortOrder === 'descending') {
          return a[this.sortProp] < b[this.sortProp] ? 1 : -1;
        }
        return 0;
      })
    },
    modelManageMenu() {
      return this.userMenuList.find(
        item => item.MenuCode === "MODEL" && item.MenuLevel === 1
      );
    },
    leftMenuActivated() {
      if (!this.modelManageMenu || !this.leftMenuIdActivated) return;
      return this.modelManageMenu.Children.find(
        item => item.Id === this.leftMenuIdActivated
      );
    },
    treeData() {
      return this.leftMenuActivated ? [this.leftMenuActivated] : null;
    },
    leftMenuActivatedName() {
      return this.leftMenuActivated && this.leftMenuActivated.MenuName
        ? this.leftMenuActivated.MenuName
        : "";
    },
    menuBusinessCode() {
      return this.leftMenuActivated && this.leftMenuActivated.BusinessCode
        ? this.leftMenuActivated.BusinessCode
        : "";
    },
    styleForPhaseActions() {
      return {
        top: this.modalInfoForPhaseActions.top + "px",
        left: this.modalInfoForPhaseActions.left + "px"
      };
    },
    phaseNameForModal() {
      const target = this.phaseInEditInfo.target;
      if (target) {
        if (this.modalInfoForPhaseActions.type === "add") {
          return (
            target.data.MenuName +
            "-" +
            ((target.data.Children || []).length + 1)
          );
        } else {
          return target.data.MenuName;
        }
      } else {
        return "";
      }
    },
    parentId() {
      const target = this.phaseInEditInfo.target;
      if (target) {
        return target.data.Id;
      } else {
        return 0;
      }
    },
    treeExpandedKeys() {
      return [this.modelManageTreeKey];
    },
    isDescendantHasFeature() {
      return (
        !!this.childrenFeaturesConverting.length ||
        !!this.childrenFeaturesFinished.length
      );
    },
    selfFeaturesFinishedMatched() {
      const keyWord = this.searchName.trim();
      if (keyWord) {
        return this.selfFeaturesFinished.filter(value => {
          const { featureName, ModelName } = value;
          const nameToMatch = featureName || ModelName;
          return nameToMatch.includes(keyWord);
        });
      } else {
        return this.selfFeaturesFinished;
      }
    },
    selfFeaturesConvertingMatched() {
      const keyWord = this.searchName.trim();
      if (keyWord) {
        return this.selfFeaturesConverting.filter(value => {
          const { featureName, ModelName } = value;
          const nameToMatch = featureName || ModelName;
          return nameToMatch.includes(keyWord);
        });
      } else {
        return this.selfFeaturesConverting;
      }
    },
    childrenFeaturesFinishedMatched() {
      const keyWord = this.searchName.trim();
      if (keyWord) {
        return this.childrenFeaturesFinished.filter(value => {
          const { featureName, ModelName } = value;
          const nameToMatch = featureName || ModelName;
          return nameToMatch.includes(keyWord);
        });
      } else {
        console.log('childrenFeaturesFinished', this.childrenFeaturesFinished );
        console.log('selfFeaturesFinished', this.selfFeaturesFinished );
        return [...this.childrenFeaturesFinished, ...this.selfFeaturesFinished];
      }
    },
    childrenFeaturesConvertingMatched() {
      const keyWord = this.searchName.trim();
      if (keyWord) {
        return this.childrenFeaturesConverting.filter(value => {
          const { featureName, ModelName } = value;
          const nameToMatch = featureName || ModelName;
          return nameToMatch.includes(keyWord);
        });
      } else {
        return [...this.childrenFeaturesConverting, ...this.selfFeaturesConverting];
      }
    },
    uploadAcceptStr() {
      return this.enableModelConverting ? ".mv,.dwg,.rvt,.rfa,.dgn,.nwd,.nwc,.iam,.ipt,.ifc,.3dxml,.rvm,.skp,.zip" : ".mv"
    }
  },
  methods: {
    /**
     * 处理排序
     */
    handleSortChange({ prop, order }) {
      this.sortProp = prop;
      this.sortOrder = order;
    },
    /**
     * 导入成功
     */
    importFileSuccess(){
      this.isShowingImportTemplate = false
      this.setUserMenuTree();
    },
    /**
     * 设置表格高度
     */
    setTableHeight() {
      this.$nextTick(() => {
        this.tableHeight = window.innerHeight -
          this.$refs.tableContainer.getBoundingClientRect().top - 20;
      });
    },
    // 处理表格行点击事件
    handleRowClick(row, column, event) {
      this.handelClickModelUrl(row);
    },
    filterFileInUploadProgress() {
      this.fileListToUpload = this.fileListToUpload.filter(item => {
        const currentStatus = item.status;
        return (
          currentStatus !== FileUploadStatusMap.toUpload &&
          currentStatus !== FileUploadStatusMap.error &&
          currentStatus !== FileUploadStatusMap.done
        );
      });
    },
    // 文件大小格式化
    FileSize_formatter(row, column) {
      if (row.isZeroBytes) {
        return "0B";
      }
      let size = row.size;
      if (size == 0) {
        // 文件夹不显示大小
        return "-";
      } else {
        if (row.SizeUnit) {
          const SizeUnit = row.SizeUnit === "Byte" ? "B" : row.SizeUnit;
          // 显示文件的大小，需要转换为字符串
          return row.size + SizeUnit;
        } else {
          return this.formatFileSize(size);
        }
      }
    },
    /**
     * 文件大小转换
     * @param fileSize
     * @returns {string}
     */
    formatFileSize(fileSize) {
      let temp = 0;
      if (fileSize < 1024) {
        return fileSize + "B";
      } else if (fileSize < 1024 * 1024) {
        temp = fileSize / 1024;
        temp = temp.toFixed(2);
        return temp + "KB";
      } else if (fileSize < 1024 * 1024 * 1024) {
        temp = fileSize / (1024 * 1024);
        temp = temp.toFixed(2);
        return temp + "MB";
      } else {
        temp = fileSize / (1024 * 1024 * 1024);
        temp = temp.toFixed(2);
        return temp + "GB";
      }
    },
    doUploadBusiness() {
      this.filterFileInUploadProgress();
      this.$refs.upload.submit();
      if (this.fileListToUpload.length) {
        this.isUploadQueueDialogVisible = true;
      }
      this.isUploadListDialogVisible = false;
    },
    uploadFileBefore(file) {
      // console.log("uploadFileBefore", file);
      this.appendToUpLoadList(file);
      this.tableKey = Math.random();
    },
    uploadFileProgress(event, file) {
      // console.log("uploadFileProgress", event, file);
      if (file) {
        const { uid } = file;
        const targetFile = this.fileListToUpload.find(item => item.uid === uid);
        if (targetFile) {
          // targetFile.status =
          //   Math.floor((event.loaded / event.total) * 100) + "%";
          targetFile.status = "上传中"
        }
      }
    },
    uploadFileError(err, file) {
      // console.log("uploadFileError", err, file);
      if (file) {
        const { uid } = file;
        const targetFile = this.fileListToUpload.find(item => item.uid === uid);
        if (targetFile) {
          targetFile.isError = true;
          targetFile.status = FileUploadStatusMap.error;
        }
      }
    },
    uploadFileSuccess(response, file) {
      // console.log("uploadFileSuccess", response, file);
      if (file) {
        const { uid } = file;
        const targetFile = this.fileListToUpload.find(item => item.uid === uid);
        if (targetFile) {
          targetFile.status = FileUploadStatusMap.done;
          if (
            this.fileListToUpload.every(
              item => item.status === FileUploadStatusMap.done
            )
          ) {
            setTimeout(() => {
              this.fileListToUpload = [];
              this.isUploadQueueDialogVisible = false;
            }, 3000);
          } else {
            this.fileListToUpload = this.fileListToUpload.filter(
              item => item.status !== FileUploadStatusMap.done
            );
          }
        }
      }
    },
    uploadFile(info) {
      // console.log("uploadFile", info);
      const fileName = info.file.name;
      const lastDotIdx = info.file.name.lastIndexOf(".");
      let fileExtension = "";
      if (lastDotIdx !== -1) {
        fileExtension = fileName.substring(lastDotIdx);
      }
      if (fileExtension) {
        if (fileExtension.toLocaleLowerCase().includes(".mv")) {
          this.uploadMvFile(info);
        } else {
          this.uploadOtherFile(info);
        }
      }
    },
    /**
     * 获取模型关联文件数量
     * @returns {Promise<void>}
     */
    async getModelRelatedDocs() {
      const allModelList = [...this.childrenFeaturesFinished, ...this.selfFeaturesFinished,];
      if (allModelList.length === 0) return;

      const modelList = {
        ModelIds: allModelList.map(item => item.featureID)
      }
      const res = await this.$axios
        .post(
          `${this.$urlPool.GetModeRelatedDocList}?token=${
            this.tokenValue}`, modelList)
        .catch(() => {});
      if (res && res.data && res.data.Ret === 1) {
        // 更新原始数据源
        const updateModelDocs = (modelArray) => {
          modelArray.forEach(item => {
            const relatedDoc = res.data.Data.find(doc => doc.ModelId === item.featureID);
            if (relatedDoc) {
              this.$set(item, 'docsCount', relatedDoc.Count);
            } else {
              this.$set(item, 'docsCount', 0);
            }
          });
        };

        updateModelDocs(this.childrenFeaturesFinished);
        updateModelDocs(this.selfFeaturesFinished);
      }
    },
    // 设置userMenuList
    async setUserMenuTree() {
      const res = await this.$axios
        .get(
          `${this.$urlPool.GetUserMenuTree}?token=${
            this.tokenValue
          }&organizeId=${this.VaultID}&parentId=0`
        )
        .catch(() => {});
      if (res && res.data && res.data.Ret === 1) {
        this.userMenuList = res.data.Data || [];
      } else {
        this.userMenuList = [];
      }
    },
    // 响应左侧菜单点击事件
    async onLeftMenuChange(menuId) {
      // todo 1、获取最新的菜单数据
      await this.setUserMenuTree()
      this.$nextTick(() => {
        if(!this.leftMenuActivated) return
        // 2、筛选出模型管理的菜单A modelManageMenu

        this.leftMenuIdActivated = menuId;
        // 3、根据menuId从A中筛选出树的根节点 leftMenuActivated

        this.extendHighlightModelManageTree(this.leftMenuActivated.BusinessCode,true)
        // 4、执行点击节点的逻辑(高亮节点、改变url、加载该阶段下模型列表)
      })
    },
    getPhaseModelCount(item) {
      return this.phaseModelCountMap[item.BusinessCode] || 0;
    },
    setMenuBtn() {
      this.menuSystemButton = [];
      this.$axios
        .get(
          `${this.$urlPool.GetSystemButton}?menuCode=MODEL&token=${
            this.tokenValue
          }`
        )
        .then(res => {
          if (res.data.Ret == 1) {
            this.menuSystemButton = res.data.Data.map(item => {
              return {
                ButtonCode: item.ButtonCode,
                ButtonName: item.ButtonName,
                ButtonIcon: item.ButtonIcon,
                Description: item.ButtonName,
                ButtonSort: item.ButtonSort
              };
            });
          }
        })
        .catch(() => {});
    },
    closeAllDialog() {
      this.isCompareModel = this.compareListshow = this.addLoading = this.status_reset_name = this.modelDetailShow = this.showModelInfo = this.showModelPhase = this.checkedModel = false;
      this.modalInfoForPhaseActions.visible = false;
      this.isLoadingFeaturesList = this.isLoadingConvertingList = false;
      this.isUploadListDialogVisible = this.isUploadQueueDialogVisible = false;
    },
    updatePhase(item, editcode) {
      if (!this.hasSomeAuth(editcode)) return;
      this.showModelPhase = true;
      this.SelModelObj = item;
      this.closeAll();
    },
    // 追加上传列表
    appendToUpLoadList(file) {
      if (!file) return;
      this.fileListToUpload.push({
        name: file.name,
        size: file.size,
        isZeroBytes: file.size <= 0,
        status: FileUploadStatusMap.toUpload,
        isError: false,
        uid: file.uid
      });
    },
    // .dwg或者.rvt  需要转换文件上传
    uploadOtherFile(fileInfo) {
      if(!this.enableModelConverting) {
        this.$message({
          message: "暂不支持需要转换的模型文件",
          type: "warning"
        })
        return;
      }
      const { file, onError, onProgress, onSuccess } = fileInfo;
      const form = new FormData();
      const filekey = this.$md5(new Date().getTime() + "" + file.name);
      form.append("ProjectId", this.VaultID);
      form.append("ModelPhaseValue", this.UrlPhase);
      form.append("FileName", file.name);
      form.append("FileKey", filekey);
      form.append("FileSize", file.size);
      form.append("ChunkNumber", 1);
      form.append("Index", 1);
      form.append("File", file);
      form.append("UserId", this.$staticmethod.Get("UserId"));
      const uploadurl = `${
        window.bim_config.webserverurl
      }/api/v1/model/upload-file?Token=${this.$staticmethod.Get("Token")}`;
      const config = {
        headers: { "Content-Type": "multipart/form-data" },
        onUploadProgress: evt => onProgress(evt, file)
      };

      this.$axios
        .post(uploadurl, form, config)
        .then(res => {
          if (res.data.Ret == 1) {
            this.$message.success(res.data.Msg); // 弹窗提示接口返回的信息
            onSuccess(res, file);
            if (this.timerForModelConverting === null) {
              // console.log("没定时器");
              this.runNextLoop(this.UrlPhase, true); // 上传了文件则开启轮询
            }
            // else {
            //   console.log("有定时器");
            // }
          } else {
            this.$message.error(res.data.Msg);
            onError(res, file);
          }
        })
        .catch(err => onError(err, file));
    },
    // mv文件的上传
    uploadMvFile(fileInfo) {
      const { file, onError, onProgress, onSuccess } = fileInfo;
      const form = new FormData();
      form.append("file", file);
      form.append("VaultID", this.VaultID);
      form.append("Phase", this.UrlPhase);
      const uploadurl = `${this.$ip("newModelHttpUrl")}/Vault/UploadModel`;
      const config = {
        headers: { "Content-Type": "multipart/form-data" },
        onUploadProgress: evt => onProgress(evt, file)
      };
      this.$axios
        .post(uploadurl, form, config)
        .then(res => {
          if (res.status === 200) {
            this.getAllListFeatures("refresh");
            this.$message.success("上传成功");
            onSuccess(res, file);
          } else {
            this.$message.error("上传模型失败");
            onError(res, file);
          }
        })
        .catch(err => onError(err, file));
    },
    // 模型移动成功
    onMoveFinished() {
      this.getAllListFeatures("refresh");
    },
    /**
     * 重置自身层级、后代层级转换中的模型列表
     */
    resetRelatedConvertingList() {
      this.childrenFeaturesConverting = [];
      this.selfFeaturesConverting = [];
    },
    // 按照phase获取正在转换的列表：本级以及所有后代正在转换的Feature
    getAllConversionModel(targetPhase, reqLenOnce = 3) {
      if (!this.enableModelConverting || this.isLoadingConvertingList) return;
      this.isLoadingConvertingList = true;
      const menuItemMatched = this.$staticmethod.walkThroughTreesByDepthFirst(
        [this.leftMenuActivated],
        node => {
          return node.BusinessCode === targetPhase;
        },
        "Children",
        true
      ); // 找到BusinessCode等于targetPhase的树节点

      if (menuItemMatched) {
        const allPhases = [];
        this.$staticmethod.walkThroughTreesByDepthFirst(
          [menuItemMatched],
          node => {
            allPhases.push(node.BusinessCode);
          },
          "Children",
          false
        ); // 收集包括他自身和所有后代的phase：自身层级的phase在第1个
        const vaultId = this.VaultID;
        const webserverurl = window.bim_config.webserverurl;
        const token = this.$staticmethod.Get("Token");
        const allReqPaths = allPhases.map(item => {
          return [
            `${webserverurl}/api/v1/model/converting?projectId=${vaultId}&modelPhaseValue=${item}&Token=${token}` // 转换中的模型路径
          ];
        });
        // console.log(allPhases,allReqPaths)
        const reqLen = allReqPaths.length;
        if (reqLen) {
          const allReqPathsGrouped = [];
          for (let i = 0; i < reqLen; i += reqLenOnce) {
            allReqPathsGrouped.push(allReqPaths.slice(i, i + reqLenOnce));
          } // 将请求路径按照每reqLenOnce个分割

          // 请求一个路径组并将结果作为一个Promise的返回值
          const requestPathGroup = pathGroup => {
            return Promise.all(
              pathGroup.map(path => {
                return this.$axios
                  .get(path)
                  .then(res => {
                    // 转换中接口的响应
                    if (res && res.data && res.data.Ret > 0) {
                      return res.data.Data || [];
                    } else {
                      return [];
                    }
                  })
                  .catch(() => []);
              })
            ).then(res => {
              // console.log('中间结果-pathGroup',res)
              return res;
            });
          };
          const results = [];
          const resultPromise = allReqPathsGrouped.reduce(
            (acc, reqPathsGroup) => {
              return acc.then(() => {
                return Promise.all(
                  reqPathsGroup.map(pathGroup => {
                    return requestPathGroup(pathGroup);
                  })
                ).then(res => {
                  results.push(...res);
                  return res;
                });
              });
            },
            Promise.resolve()
          );

          const prevSelfFeaturesConverting = this.selfFeaturesConverting || [];
          const prevChildrenFeaturesConverting =
            this.childrenFeaturesConverting || [];
          resultPromise
            .then(() => {
              if (results.length) {
                let [list1, list2] = results.reduce(
                  (acc, next, idx) => {
                    if (idx !== 0) {
                      acc[1].push(...next[0]); // 累加后代的转换中的模型
                    } else {
                      acc[0].push(...next[0]); // 自身层级转换中的模型
                    }
                    return acc;
                  },
                  [[], []]
                ); // 将结果合并为1个值
                this.selfFeaturesConverting = list1;
                this.childrenFeaturesConverting = list2;
              } else {
                this.resetRelatedConvertingList();
              }
              // console.log('拿到的最终结果',results)
            })
            .catch(err => {
              this.resetRelatedConvertingList();
              console.log("err", err);
            })
            .finally(() => {
              this.isLoadingConvertingList = false;
              // 对比prevSelfFeaturesConverting与this.selfFeaturesConverting
              // 对比prevChildrenFeaturesConverting与this.childrenFeaturesConverting
              const currentSelfFeaturesConvertingLen = this
                .selfFeaturesConverting.length;

              const currentChildrenFeaturesConvertingLen = this
                .childrenFeaturesConverting.length;

              const prevSelfFeaturesConvertingLen =
                prevSelfFeaturesConverting.length;

              const prevChildrenFeaturesConvertingLen =
                prevChildrenFeaturesConverting.length;
              if (
                currentSelfFeaturesConvertingLen ||
                currentChildrenFeaturesConvertingLen
              ) {
                if (
                  prevSelfFeaturesConvertingLen &&
                  currentSelfFeaturesConvertingLen !==
                    prevSelfFeaturesConvertingLen
                ) {
                  // 当前自身阶段与之前自身阶段值的长度不同：刷新
                  this.getAllListFeatures("refresh");
                } else if (
                  prevChildrenFeaturesConvertingLen &&
                  currentChildrenFeaturesConvertingLen !==
                    prevChildrenFeaturesConvertingLen
                ) {
                  // 当前后代阶段与之前后代阶段值的长度不同：刷新
                  this.getAllListFeatures("refresh");
                } else {
                  this.runNextLoop(targetPhase,false);
                }
              } else {
                // // 当前自身阶段、后代阶段都没值
                // clearTimeout(this.timerForModelConverting);
                // this.timerForModelConverting = null;
                // if (
                //   prevSelfFeaturesConvertingLen ||
                //   prevChildrenFeaturesConvertingLen
                // ) {
                //   // 当前自身阶段或后代阶段有值：刷新
                //   this.getAllListFeatures("refresh");
                // }
                this.runNextLoop(targetPhase,false);
              }
            });
        } else {
          this.resetRelatedConvertingList();
          this.isLoadingConvertingList = false;
        }
      } else {
        this.resetRelatedConvertingList();
        this.isLoadingConvertingList = false;
      }
    },
    runNextLoop(targetPhase, immediately = false) {
      this.timerForModelConverting = setTimeout(
        () => {
          this.getAllConversionModel(targetPhase);
        },
        immediately ? 0 : window.bim_config.detectInterval || 1000
      );
    },
    // 获取模型列表
    getAllListFeatures(refresh) {
      let _this = this;
      if (_this.UrlPhase == "allmodel_phases") {
        if (this.isLoadingFeaturesList) return;
        this.isLoadingFeaturesList = true;
        this.resetRelatedList();
        _this.$axios
          .get(
            `${this.$ip("newModelHttpUrl")}/Vault/getAllFeatures?VaultID=${
              _this.VaultID
            }`
          )
          .then(res => {
            if (res.status === 200) {
              _this.childrenFeaturesFinished = res.data;
              if (refresh == "refresh") {
                _this.$emit("modelcntrefresh");
              }
            } else {
              _this.$message.error("获取列表失败");
            }
            this.isLoadingFeaturesList = false;
          })
          .catch(err => {
            console.log(err);
            this.isLoadingFeaturesList = false;
          });
      } else {
        if (refresh == "refresh") {
          _this.$emit("modelcntrefresh");
        }
        this.getAllFeaturesByPhase(_this.UrlPhase);
      }
    },

    // 获取某个层级以及后代层级的模型：每个层级转换完成的和转换中的
    getAllFeaturesByPhase(targetPhase, reqLenOnce = 3) {
      if (!this.leftMenuActivated || this.isLoadingFeaturesList) return;
      this.isLoadingFeaturesList = true;
      this.extendHighlightModelManageTree(targetPhase);
      const menuItemMatched = this.$staticmethod.walkThroughTreesByDepthFirst(
        [this.leftMenuActivated],
        node => {
          return node.BusinessCode === targetPhase;
        },
        "Children",
        true
      );
      if (menuItemMatched) {
        const allPhases = [];
        this.$staticmethod.walkThroughTreesByDepthFirst(
          [menuItemMatched],
          node => {
            allPhases.push(node.BusinessCode);
          },
          "Children",
          false
        );
        const vaultId = this.VaultID;
        const hostPart = this.$ip("newModelHttpUrl");
        const webserverurl = window.bim_config.webserverurl;
        const token = this.$staticmethod.Get("Token");
        let allReqPaths;
        if(this.enableModelConverting) {
          if (this.timerForModelConverting === null) {
            this.runNextLoop(targetPhase, true);
          } else {
            this.runNextLoop(targetPhase, false);
          }

          allReqPaths = allPhases.map(item => {
            return [
              `${hostPart}/Vault/GetFeaturesByPhase?VaultID=${vaultId}&Phase=${item}`, // 转换完成的模型路径
              `${webserverurl}/api/v1/model/converting?projectId=${vaultId}&modelPhaseValue=${item}&Token=${token}` // 转换中的模型路径
            ];
          });
        } else {
          allReqPaths = allPhases.map(item => {
            return [
              `${hostPart}/Vault/GetFeaturesByPhase?VaultID=${vaultId}&Phase=${item}`, // 转换完成的模型路径
            ];
          });
        }
        // console.log(allPhases,allReqPaths)
        const reqLen = allReqPaths.length;
        if (reqLen) {
          const allReqPathsGrouped = [];
          for (let i = 0; i < reqLen; i += reqLenOnce) {
            allReqPathsGrouped.push(allReqPaths.slice(i, i + reqLenOnce));
          } // 将请求路径按照每reqLenOnce个分割

          // 请求一个路径组并将结果作为一个Promise的返回值
          const requestPathGroup = pathGroup => {
            return Promise.all(
              pathGroup.map((path, idx) => {
                return this.$axios
                  .get(path)
                  .then(res => {
                    if (idx === 0) {
                      // 转换完接口的响应
                      if (res && res.status === 200) {
                        return res.data || [];
                      } else {
                        return [];
                      }
                    } else if (idx === 1) {
                      // 转换中接口的响应
                      if (res && res.data && res.data.Ret > 0) {
                        return res.data.Data || [];
                      } else {
                        return [];
                      }
                    }
                  })
                  .catch(() => []);
              })
            ).then(res => {
              // console.log('中间结果-pathGroup',res)
              return res;
            });
          };
          const results = [];
          const resultPromise = allReqPathsGrouped.reduce(
            (acc, reqPathsGroup) => {
              return acc.then(() => {
                return Promise.all(
                  reqPathsGroup.map(pathGroup => {
                    return requestPathGroup(pathGroup);
                  })
                ).then(res => {
                  results.push(...res);
                  return res;
                });
              });
            },
            Promise.resolve()
          );

          resultPromise
            .then(() => {
              if (results.length) {
                let list1 = [], list2 = [];
                if(this.EnableModelCoverting) {
                  [list1, list2] = results.reduce(
                    (acc, next, idx) => {
                      if (idx !== 0) {
                        // 跳过他自己
                        acc[0].push(...next[0]);
                        acc[1].push(...next[1]);
                      }
                      return acc;
                    },
                    [[], []]
                  ); // 将结果合并为1个值
                } else {
                  [list1] = results.reduce(
                    (acc, next, idx) => {
                      if (idx !== 0) {
                        // 跳过他自己
                        acc[0].push(...next[0]);
                      }
                      return acc;
                    },
                    [[]]
                  ); // 将结果合并为1个值
                }
                this.childrenFeaturesFinished = list1;
                this.childrenFeaturesConverting = list2;

                this.selfFeaturesFinished = results[0][0];
                this.selfFeaturesConverting = this.enableModelConverting ? results[0][1] : [];
                if (
                  results[0][0].length + list1.length !==
                  this.phaseModelCountMap[targetPhase]
                ) {
                  // 当前阶段已完成转换（自己加所有后代的）的模型数量与传递进来的阶段数量不等
                  // 主要是为了解决，在A阶段上传了模型，还没等转换完成就切换到了其他阶段，之后又切换回了A阶段
                  // 切换到其他阶段之后，轮询的阶段就变了，已经无法再侦测到A阶段的模型转换过程
                  // 这里比较当前阶段转换完成的数量，如果不相等则刷新
                  this.getAllListFeatures("refresh");
                }

                // if (this.selfFeaturesConverting.length + list2.length > 0) {
                //   // 当前阶段正在转换（自己的加所有后代的）的模型数量大于0则开启轮询当前阶段
                //   if (this.timerForModelConverting === null) {
                //     this.runNextLoop(targetPhase, true);
                //   } else {
                //     this.runNextLoop(targetPhase, false);
                //   }
                // }
              } else {
                this.resetRelatedList();
              }
              // console.log('拿到的最终结果',results)
            })
            .catch(err => {
              this.resetRelatedList();
              console.log("err", err);
            })
            .finally(() => {
              this.isLoadingFeaturesList = false;
            });
        } else {
          this.resetRelatedList();
          this.isLoadingFeaturesList = false;
        }
      } else {
        this.resetRelatedList();
        this.isLoadingFeaturesList = false;
      }
    },
    resetRelatedList() {
      this.childrenFeaturesFinished = this.childrenFeaturesConverting = this.selfFeaturesConverting = this.selfFeaturesFinished = [];
    },
    getItemImg(item) {
      if (item.thumbnail === "") {
        return require("../../../assets/images/default.jpg");
      } else {
        return `data:image/png;base64,${item.thumbnail}`;
      }
    },
    getItemState(item, str) {
      // 0  等待转换 1  转换中  2 转换完成 3  转换失败 4 跳过  5 已停止
      let obj = {
        img: "",
        imgText: "",
        stateText: "",
        imgColor: ""
      };
      const { TaskQueueState } = item;
      switch (TaskQueueState) {
        case "0":
          obj.img = require("../../../assets/images/modelLoad.png");
          obj.imgText = item.TaskState;
          obj.stateText = item.Message;
          obj.imgColor = "c666666";
          break;
        case "1":
          obj.img = require("../../../assets/images/modelloading.png");
          obj.imgText = item.TaskState;
          obj.stateText = item.Message;
          obj.imgColor = "c407FFE";
          break;
        case "2":
          obj.img = require("../../../assets/images/modelLoad.png");
          obj.imgText = item.TaskState;
          obj.stateText = item.Message;
          obj.imgColor = "c666666";
          break;
        case "3":
          obj.img = require("../../../assets/images/modelerr.png");
          obj.imgText = item.TaskState;
          obj.stateText = item.Message;
          obj.imgColor = "cFF6640";
          break;
        case "4":
          obj.img = require("../../../assets/images/modelerr.png");
          obj.imgText = item.TaskState;
          obj.stateText = item.Message;
          obj.imgColor = "cFF6640";
          break;
        case "5":
          obj.img = require("../../../assets/images/modelerr.png");
          obj.imgText = item.TaskState;
          obj.stateText = item.Message;
          obj.imgColor = "cFF6640";
          break;
        default:
          obj.img = require("../../../assets/images/modelerr.png");
          obj.imgText = item.TaskState;
          obj.stateText = item.Message;
          obj.imgColor = "cFF6640";
          break;
      }
      return obj[str];
    },
    /**
     * 在全部模型阶段打开的模型中判断此模型的阶段权限
     * @returns {boolean}
     */
    hasAllShareAuth() {
      // 递归检查权限的辅助函数
      const checkAuthInChildren = (children) => {
        for (const item of children) {
          if (item.BusinessCode === this.onClickModelPhase){
            // 检查当前节点的按钮权限
            if (item.Buttons && item.Buttons.length > 0) {
              const hasShareAuth = item.Buttons.some(btn => btn.ButtonCode === 'Model_Share');
              if (hasShareAuth) {
                return true;
              }
            }
          }
          // 递归检查子节点
          if (item.Children && item.Children.length > 0) {
            const hasAuth = checkAuthInChildren(item.Children);
            if (hasAuth) {
              return true;
            }
          }
        }
        return false;
      };

      // 从根节点开始递归检查
      if (this.modelManageMenu && this.modelManageMenu.Children) {
        return checkAuthInChildren(this.modelManageMenu.Children);
      }

      return false;
    },
    handelClickModelUrl(item, version = '') {
      const menuText = this.$staticmethod._Get("menuText") || '';
      if (menuText === '全部模型'){
        this.SelModelObj = item;
          // 判断模型所在阶段的权限
        this.onClickModelPhase = item.phase
        this.hasAllShareAuth()
      }
      if (item.TaskQueueState) {
        if (item.TaskQueueState != "2") {
          this.$message({
            message: "模型还未转换完成",
            type: "warning"
          });
        }
        return;
      }
      this.SelModelObj = item;
      this.modelId = item.featureID;
      this.modelTitle = item.featureName;
      this.SelModelObj.currentVersion = version + ''; // 点击打开的版本
      this.modelDetailShow = true;
    },
    // 获取模型详情
    getModelDetail(item) {
      let _this = this;
      _this.modelDetailItem = item;
      _this.showModelInfo = true;
      this.closeAll();
    },
    // 编辑模型：mouseover
    mouseovereditmodel(ev) {
      this.mouseoverPos.clientX = ev.clientX;
      this.mouseoverPos.clientY = ev.clientY;
      this.itemMenuName = "edit";
    },
    // 编辑模型：mouseout
    mouseouteditmodel(ev) {
      this.itemMenuName = "-1";
    },
    getMenuStyle() {
      let _s = {};
      let bool = false;

      // 检查是否接近屏幕底部
      this.unitPos.clientY > document.body.clientHeight - 300
        ? (bool = true)
        : (bool = false);

      if (bool == true) {
        _s["bottom"] = (document.body.clientHeight - this.unitPos.clientY + 10) + "px";
        _s["top"] = "auto";
      } else {
        _s["top"] = this.unitPos.clientY + "px";
        _s["bottom"] = "auto";
      }

      // 检查是否接近屏幕右边
      if (this.unitPos.clientX > document.body.clientWidth - 160) {
        _s["right"] = (document.body.clientWidth - this.unitPos.clientX + 10) + "px";
        _s["left"] = "auto";
      } else {
        _s["left"] = this.unitPos.clientX + "px";
        _s["right"] = "auto";
      }

      return _s;
    },
    getItemMenuStyle() {

    },
    // 模型重命名
    updateFeatureName(item, editcode) {
      if (!this.hasSomeAuth(editcode)) return;
      let _this = this;
      _this.resetNameID = item.featureID;
      _this.m_modelNewName = item.featureName;
      _this.status_reset_name = true;
      this.closeAll();
    },
    // 关闭重命名弹窗
    handelClickResetNameClose() {
      this.status_reset_name = false;
    },
    handelClickResetNameSave(name) {
      let _this = this;
      let _name = encodeURIComponent(name);

      _this.$axios
        .get(
          `${this.$ip("newModelHttpUrl")}/Vault/UpdateFeatureName?VaultID=${
            _this.VaultID
          }&FeatureID=${_this.resetNameID}&Name=${_name}`
        )
        .then(res => {
          if (res.status === 200) {
            _this.getAllListFeatures();
            _this.$message.success("修改成功");
          } else {
            _this.$message.error("获取列表失败");
          }
          _this.status_reset_name = false;
        })
        .catch(err => {
          console.log(err);
        });
    },
    // 修改缩略图
    updateFeatureThumbnail(featureID, editcode) {
      if (!this.hasSomeAuth(editcode)) return;
      this.editThumbnailfeatureID = featureID;
      let dom = document.getElementById("idupFeatureThumbnail");
      dom.value = "";
      dom.click();
      this.closeAll();
    },
    // 修改缩略图请求
    uploadFeatureThumbnailImg() {
      let files = document.getElementById("idupFeatureThumbnail").files[0];

      // 显示裁剪图片组件并传递图片
      this.isImgClipVisible = true
      this.$nextTick(() => {
        this.$refs.refClipImage.setImage(files)
      })
    },
    // 响应图片裁剪完成事件
    onImgCropDone(imgB64) {
      // console.log(imgB64);
      if(!imgB64) {
        this.$message({
            type: 'error',
            message: '未获得到图片数据'
          });
          return
      }
      const form = new FormData();
      form.append("vaultID", this.VaultID);
      form.append("featureID", this.editThumbnailfeatureID);
      form.append("objectID", "");
      form.append("versionNO", "");
      form.append("entity", imgB64.split(",")[1]);
      this.$axios
        .post(
          `${this.$ip("newModelHttpUrl")}/Vault/UpdateFeatureThumbnail`,
          form
        )
        .then(res => {
          this.$message({ message: "更新成功", type: "success" });
          this.isImgClipVisible = false
          //上传完成后获取一次列表
          this.getAllListFeatures();
        })
        .catch(error => {});
    },
    // deleteModel删除模型
    deleteModel(item, editcode) {
      this.closeAll();
      let _this = this;
      if (!this.hasSomeAuth(editcode)) return;
      _this
        .$confirm("确定删除当前模型", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
        .then(() => {
          _this.$axios
            .get(
              `${this.$ip("newModelHttpUrl")}/Vault/DeleteModel?VaultID=${
                this.VaultID
              }&ModelID=${item.featureID}`
            )
            .then(res => {
              if (res.status === 200) {
                this.$message.success("删除成功");
                this.DeleteMaterialsConnModel(item.featureID); // 删除工程结构中的关联关系
                this.delModelShare(item.featureID);
                _this.getAllListFeatures("refresh");
              } else {
                _this.$message.error("获取列表失败");
              }
            })
            .catch(err => {
              console.log(err);
            });
        })
        .catch(err => {
          console.log(err);
        });
    },
    // 删除模型分享
    async delModelShare(id) {
      const res = await this.$api.postcancelbymodelid(id);
      if (res.Ret == 1) {
      }
    },
    // 删除关联工程结构的对应数据
    async DeleteMaterialsConnModel(id) {
      const res = await this.$api.DeleteMaterialsConnModel({id: id});
      if (res.Ret == 1) {
      }
    },
    // 更新模型
    updateMvFile(item, editcode) {
      console.log("====", this.hasSomeAuth(editcode));
      if (!this.hasSomeAuth(editcode)) return;
      this.updateFileID = item.featureID;
      let dom = document.getElementById("updateModelMv");
      dom.value = "";
      dom.click();
      this.closeAll();
    },
    // 更新模型
    updateVault(item) {
      let _this = this;
      // this.addLoading = true;
      const dom = document.getElementById("updateModelMv");
      const form = new FormData();
      const fileObj = dom.files;
      form.append("file", fileObj[0]);
      form.append("ModelID", _this.updateFileID);
      form.append("VaultID", _this.VaultID);

      let uploadurl = `${this.$ip("newModelHttpUrl")}/Vault/UpdateModel`;
      let config = {
        headers: { "Content-Type": "multipart/form-data" }
      };
      _this.$axios
        .post(uploadurl, form, config)
        .then(res => {
          this.addLoading = false;
          if (res.status === 200) {
            this.$message.success("更新成功");
            this.getAllListFeatures();
          } else {
            this.$message.error("更新模型失败");
          }
        })
        .catch(err => {
          this.addLoading = false;
          console.log(err);
        });
    },
    handelClickUnitList(ev, item) {
      // 获取点击元素相对于视口的位置
      const rect = ev.target.getBoundingClientRect();
      // 使用元素的实际位置，而不是事件的clientX/clientY
      this.unitPos.clientX = rect.left + rect.width / 2;
      this.unitPos.clientY = rect.top + rect.height / 2;
      this.checkedModelID = item.featureID;
      this.checkedModel = true;
    },
    // 点击删除转换异常的模型
    handelClickConvertingList(item) {
      this.$confirm("确定删除当前转换异常的模型", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          this.deleteConvertingList(item);
        })
        .catch(err => {
          console.log(err);
        });
    },
    async deleteConvertingList(item) {
      let tarr = [];
      tarr.push(item.TaskId);
      const _data = {
        VaultId: item.VaultId,
        TaskIds: tarr
      };
      const res = await this.$api.deleteCovert(_data);
      if (res.Ret == 1) {
        this.getAllListFeatures("refresh");
        this.$message.success(res.Msg);
      }
    },
    closeAll(ev) {
      this.checkedModel = false;
      this.checkedModelID = "";
      if (ev) {
        ev.stopPropagation();
      }
      this.modalInfoForPhaseActions.visible = false;
    },
    closeModelDetail() {
      this.modelDetailShow = false;
    },
    getOldShareDetail() {
      let _this = this;
      _this.$axios
        .post(
          `${
            window.bim_config.webserverurl
          }/api/v1/model/share/info?projectId=${
            _this.VaultID
          }&Token=${this.$staticmethod.Get("Token")}&modelId=${
            this.shareModelInfo.featureID
          }`
        )
        .then(x => {
          if (x.data.Ret == 1) {
            this.btnStatus = 3;
            const result = x.data.Data;
            let _url = window.location.href.split("#")[0];
            this.shareUrl =
              _url + `#/modelNewShareLoad/${_this.VaultID}/${result.Id}`;
            this.shareForm = result.HasPassword ? "2" : "1";
            this.limitTime = result.ShardDays + "";
            this.shareCode = result.Password;
            this.showCancel = true;
            this.shareID = result.Id;
          } else {
            this.showCancel = false;
            this.btnStatus = 1;
          }
        })
        .catch(x => {
          console.log(x);
        });
    },
    shareModelHandle() {
      const menuText = this.$staticmethod._Get("menuText") || '';
      if (menuText === '全部模型'){
        // 判断模型所在阶段的权限
        this.openAllShare()
      }else {
        this.openShare(this.SelModelObj, "Model_Share");
      }
    },
    /**
     * 在全部模型阶段打开分享页面
     */
    openAllShare() {
      this.dialogVisible = true;
      this.share_title = `分享：${this.SelModelObj.featureName}`;
      this.shareModelInfo = this.SelModelObj;
      this.getOldShareDetail();
      this.closeAll();
    },
    // 点击分享
    openShare(item, editcode) {
      if (!this.hasSomeAuth(editcode)) return;
      this.dialogVisible = true;
      this.share_title = `分享：${item.featureName}`;
      // this.shareUrl = window.location.href;
      this.shareModelInfo = item;
      this.getOldShareDetail();
      this.closeAll();
    },
    compareModel(item) {
      // 对比、
      this.closeAll();
      let _this = this;
      _this.modelCompareItem = {
        featureID: item.featureID,
        featureName: item.featureName,
        currentVersion: item.currentVersion
      };
      _this.$axios
        .get(
          `${_this.$ip("newModelHttpUrl")}/Vault/GetAllVersions?VaultID=${
            _this.VaultID
          }&FeatureID=${item.featureID}`
        )
        .then(res => {
          let option = res.data.reverse();
          option = option.map(item => {
            return {
              img: this.getItemImg(item),
              label: `版本V${item.versionNO}  ${this.formDate(
                item.createTime
              )}`,
              value: item.versionNO,
              isSelect: false
            };
          });
          _this.versionList = option;
          _this.compareListshow = true;
        })
        .catch(error => {});
    },
    formDate(val) {
      if (val.length >= "2019-09-16 11:14".length) {
        return val.substr(0, "2019-09-16 11:14".length).replace("T", " ");
      }
    },
    getVersionCompare(arr) {
      if (arr.length === 2) {
        this.compareArr = arr;
        this.compareListshow = false;
        this.isCompareModel = true;
      }
    },
    compareClose() {
      this.isCompareModel = false;
      this.compareArr = [];
    },
    // 复制链接
    copy() {
      let txt = document.getElementById("txtWXUrl");
      txt.select();
      document.execCommand("Copy");
      this.$message({ type: "success", message: "复制成功" });
    },
    // 创建分享
    async createShare() {
      // 调用创建分享接口
      let _this = this;
      let _data = {
        ProjectId: _this.VaultID,
        ModelId: _this.shareModelInfo.featureID,
        ModelName: _this.shareModelInfo.featureName,
        ShardDays: JSON.parse(this.limitTime),
        HasPassword: this.shareForm === "2" ? true : false,
        Password: this.shareForm === "2" ? this.shareCode : "",
        CreatorUserId: this.userID
      };
      const res = await this.$api.shareCreate(_data);
      if (res.Ret == 1) {
        let _url = window.location.href.split("#")[0];
        this.shareUrl =
          _url + `#/modelNewShareLoad/${_this.VaultID}/${res.Data}`;
        this.showCancel = true;
        this.shareID = res.Data;
        if (this.btnStatus === 1) {
          this.btnStatus = 2;
        } else if (this.btnStatus === 3) {
          this.copy();
        }
        this.$message.success(res.Msg);
      }
    },
    // 取消分享
    canceShare() {
      let _this = this;
      let _data = {
        id: this.shareID
      };
      _this.$axios
        .post(
          `${window.bim_config.webserverurl}/api/v1/model/share/cancel?id=${
            this.shareID
          }&Token=${this.$staticmethod.Get("Token")}`,
          _data
        )
        .then(x => {
          if (x.data.Ret == 1) {
            this.$message.warning("已取消分享链接");
            this.showCancel = false;
            this.btnStatus = 1;
          } else {
            console.log(x);
          }
        })
        .catch(x => {
          console.log(x);
        });
    },
    cancelShareDialog() {
      this.dialogVisible = false;
      this.share_title = "";
    },
    // 需要密码的链接
    getSecretLink() {
      if (this.shareForm === "2") {
        let code = "";
        const codeLength = 4;
        // 设置随机字符
        const random = [0, 1, 2, 3, 4, 5, 6, 7, 8, 9];
        for (let i = 0; i < codeLength; i++) {
          const index = Math.floor(Math.random() * 9);
          code += random[index];
        }
        this.shareCode = code;
      }
    },
    // 获取权限
    hasSomeAuth(code) {
      let has_Auth = false;
      if (!this.UrlPhase) {
        return false;
      }
      let allBtnAuth = JSON.parse(this.$staticmethod._Get("menuListHasAuth"));

      let index = allBtnAuth.findIndex(x => x.ButtonCode == code);
      index != -1 ? (has_Auth = true) : (has_Auth = false);
      return has_Auth;
    },

    // 处理树节点点击事件
    onElTreeNodeClick(data, node) {
      console.log("onElTreeNodeClick", data, node);
      this.UrlPhase = data.BusinessCode;
      clearTimeout(this.timerForModelConverting);
      this.timerForModelConverting = null;
      this.resetRelatedConvertingList();
      this.closeAllDialog();
      this.fileListToUpload = [];
      sessionStorage.setItem("menuListHasAuth", JSON.stringify(data.Buttons));
      this.phaseInEditInfo.target = node;
      this.modalInfoForPhaseActions.visible = false;
      this.setMenuTreeKey();
      // console.log(window.bim_config.hasRouterFile ,'====', data.RoutePath)
      const _urltarget = (window.bim_config.hasRouterFile + data.RoutePath)
        .replace("@Token", this.tokenValue)
        .replace("@OrganizeId", this.VaultID);
      window.location.href = _urltarget;
      this.getAllListFeatures();
      // this.$router.push({name: 'Model',params: {
      //   organizeId: this.VaultID,
      //   Token: this.tokenValue,
      //   Phase: data.BusinessCode
      // }})
    },
    // 处理树节点右侧按钮点击事件
    onExtraBtnClick(node, evt) {
      // console.log('onExtraBtnClick',node,evt);
      this.phaseInEditInfo.target = node;
      this.modalInfoForPhaseActions.visible = true;
      this.setMenuTreeKey();
      let top, left;
      if (evt.target) {
        const boundingRect = evt.target.getBoundingClientRect();
        top = boundingRect.top + boundingRect.height;
        left = boundingRect.left + boundingRect.width;
      } else {
        top = evt.clientY;
        left = evt.clientX;
      }
      this.modalInfoForPhaseActions.top = top;
      this.modalInfoForPhaseActions.left = left;
    },
    // 显示添加阶段的弹窗
    showAddPhaseModal() {
      // this.modalInfoForPhaseActions.visible = false // 关闭包含添加、重命名、删除按钮的弹窗
      this.modalInfoForPhaseActions.type = "add";
      this.modalInfoForPhaseActions.phaseNameFromModal = this.phaseNameForModal;
      this.modalInfoForPhaseActions.isPhaseModalVisible = true;
    },
    // 显示重命名阶段的弹窗
    showRenamePhaseModal() {
      // this.modalInfoForPhaseActions.visible = false // 关闭包含添加、重命名、删除按钮的弹窗
      this.modalInfoForPhaseActions.type = "reanme";
      this.modalInfoForPhaseActions.phaseNameFromModal = this.phaseNameForModal;
      this.modalInfoForPhaseActions.isPhaseModalVisible = true;
    },

    // 删除模型阶段菜单
    getNewDeleteModelPhase(indexId) {
      this.$axios
        .post(
          `${this.$urlPool.DeleteMenu}?id=${indexId}&token=${this.tokenValue}`
        )
        .then(res => {
          if (res.data.Ret == 1) {
            this.setMenuTreeKey(true);
            this.$message.success(res.data.Msg);
            this.setUserMenuTree().then(() => {
              this.extendHighlightModelManageTree(
                this.modelManageTreeKey,
                true
              );
            });
          } else {
            this.$message.error(res.data.Msg);
          }
        })
        .catch(res => {});
    },

    // 响应删除阶段点击事件
    onDelPhaseClick() {
      // 判断是否有模型处于此阶段中：这里实际上也需要根据getAllFeaturesByPhase来计算当前阶段是否有模型
      const phaseModelCount = this.phaseModelCountMap[
        this.phaseInEditInfo.target.data.BusinessCode
      ]; // 复用了来自ProjectBoot.vue中的m_phasecnt，也可以基于getAllFeaturesByPhase逻辑改造一下
      if (phaseModelCount > 0) {
        this.$message.error("该模型管理文件下有模型，无法删除");
      } else {
        this.$confirm("确认删除该模型管理文件？", "操作确认", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            this.modalInfoForPhaseActions.visible = false;
            this.setMenuTreeKey(true);
            this.getNewDeleteModelPhase(this.phaseInEditInfo.target.data.Id);
          })
          .catch(() => {});
      }
      // var _this = this;
      // this.modalInfoForPhaseActions.isPhaseModalVisible = false
      // // 先判断是否已经有模型处于此阶段中
      // let vaultID = _this.$staticmethod._Get("organizeId");
      // let _phase = this.phaseInEditInfo.target.data.BusinessCode
      // _this.$axios
      //     .get(`${this.$ip('newModelHttpUrl')}/Vault/GetFeaturesByPhase?VaultID=${vaultID}&Phase=${_phase}`)
      //     .then(res => {
      //       if (res.status === 200) {
      //         let models = res.data;
      //         if (models.length > 0) {
      //           _this.$message.error("该模型管理文件下有模型，无法删除");
      //           this.modalInfoForPhaseActions.visible = false
      //         } else {
      //           _this
      //             .$confirm("确认删除该模型管理文件？", "操作确认", {
      //               confirmButtonText: "确定",
      //               cancelButtonText: "取消",
      //               type: "warning"
      //             })
      //             .then(()=> {
      //               this.modalInfoForPhaseActions.visible = false
      //               this.setMenuTreeKey(true)
      //               this.getNewDeleteModelPhase(this.phaseInEditInfo.target.data.Id);
      //             })
      //             .catch(() => {});
      //         }
      //       }
      //     })
      //     .catch(err => {
      //     })
    },

    // 保存新增的字典项或修改的字典项
    onPhaseModalOk() {
      let newPhaseName = this.modalInfoForPhaseActions.phaseNameFromModal;

      // 调用接口保存项目阶段
      if (!newPhaseName || regForOnlyEmptyStr.test(newPhaseName)) {
        this.$message.error("模型管理文件名称不能为空");
        return;
      }

      // 添加验证：名称不能为全部模型！
      newPhaseName = newPhaseName.replace(regForEmptyStr, "");
      if (newPhaseName === "全部模型") {
        this.$message.error("模型管理文件名称不能为“全部模型”");
        return;
      }
      this.modalInfoForPhaseActions.phaseNameFromModal = newPhaseName;

      // 判断是新增或编辑
      if (this.modalInfoForPhaseActions.type == "add") {
        const newval = "v" + this.convertDateToStr(new Date());
        const _data = {
          MenuName: this.modalInfoForPhaseActions.phaseNameFromModal,
          OrganizeId: this.$staticmethod._Get("organizeId"),
          Description: this.modalInfoForPhaseActions.phaseNameFromModal,
          ParentId: this.parentId,
          RoutePath: `/#/Home/ProjectBoot/Model/@OrganizeId/@Token/${newval}`,
          ComponentPath: "",
          NextMenuId: "",
          MenuType: 4, //  模型菜单
          MenuCode: "MODEL",
          MenuIcon: "",
          BusinessCode: newval,
          RequiresAuth: true,
          Buttons: this.menuSystemButton
        };
        this.getNewAddModelPhase(_data);
      } else {
        // console.log('编辑',this.leftMenuActivated,this.phaseInEditInfo.target)
        this.getNewEditModelPhase();
      }
    },
    // isFromDelete标记调用是否来自于删除操作
    getIdFromPhaseInEdit(isFromDelete = false) {
      const target = this.phaseInEditInfo.target;
      if (target) {
        if (isFromDelete) {
          return target.parent.data.BusinessCode; // 删除了某个层级则自动展开到被删除层级的上一级
        } else {
          return target.data.BusinessCode;
        }
      }
    },
    // isFromDelete标记调用是否来自于删除操作
    setMenuTreeKey(isFromDelete = false) {
      const modelManageTreeKey = this.getIdFromPhaseInEdit(isFromDelete);
      if (modelManageTreeKey) {
        this.modelManageTreeKey = modelManageTreeKey;
        sessionStorage.setItem("modelManageTreeKey", modelManageTreeKey); //右侧el-tree高亮节点/编辑节点的key
      }
    },
    onPhaseModalCancel() {
      this.modalInfoForPhaseActions.visible = false; // 关闭包含添加、重命名、删除按钮的弹窗
      this.modalInfoForPhaseActions.isPhaseModalVisible = false; // 关闭添加、重命名弹窗
    },
    onPhaseModalInput(data) {
      this.modalInfoForPhaseActions.phaseNameFromModal = data;
    },
    convertDateToStr(dt) {
      var year = dt.getFullYear();
      var month = dt.getMonth() + 1;
      var date = dt.getDate();
      var hour = dt.getHours();
      var minute = dt.getMinutes();
      var second = dt.getSeconds();
      var ms = dt.getMilliseconds();
      return (
        year +
        "" +
        month +
        "" +
        date +
        "" +
        hour +
        "" +
        minute +
        "" +
        second +
        "" +
        ms
      );
    },
    // 增加模型阶段，实际是增加菜单
    getNewAddModelPhase(params) {
      let _this = this;
      _this.$axios
        .post(`${_this.$urlPool.CreateMenu}?token=${_this.tokenValue}`, params)
        .then(res => {
          if (res.data.Ret == 1) {
            this.modalInfoForPhaseActions.isPhaseModalVisible = false;
            this.modalInfoForPhaseActions.visible = false;
            _this.$message.success("添加模型文件夹成功");
            sessionStorage.setItem("modelManageTreeKey", params.BusinessCode); // 展开到新添加的节点
            this.setUserMenuTree().then(() => {
              this.extendHighlightModelManageTree(params.BusinessCode, true);
            });
          } else {
            if (res.data.Msg == "当前菜单已存在") {
              this.$message.error("当前模型文件夹已存在");
            } else {
              this.$message.error(res.data.Msg);
            }
          }
        })
        .catch(err => {});
    },
    // 编辑模型阶段，实际是编辑菜单
    getNewEditModelPhase() {
      let _this = this;
      const params = JSON.parse(
        JSON.stringify(this.phaseInEditInfo.target.data)
      );
      params.MenuName = params.Description = this.modalInfoForPhaseActions.phaseNameFromModal;
      this.$axios
        .post(`${this.$urlPool.ModifyMenu}?token=${this.tokenValue}`, params)
        .then(res => {
          if (res.data.Ret == 1) {
            this.modalInfoForPhaseActions.isPhaseModalVisible = false;
            this.modalInfoForPhaseActions.visible = false;
            _this.$message.success("修改成功");
            sessionStorage.setItem("modelManageTreeKey", params.BusinessCode); // 展开到被重命名的节点
            this.setUserMenuTree().then(() => {
              this.extendHighlightModelManageTree(params.BusinessCode, true);
            });
            // 这里需要判断是否修改的根节点
            if (this.menuBusinessCode === params.BusinessCode) {
              _this.$emit("modelcntrefresh");
            }
            // 创建成功后清空form表单
          } else {
            if (res.data.Msg == "当前菜单已存在") {
              this.$message.error("当前模型文件夹已存在");
            } else {
              this.$message.error(res.data.Msg);
            }
          }
        })
        .catch(err => {});
    },
    /**
     * 展开并高亮模型管理树
     * @param modelManageTreeKey 模型管理树当前高亮节点的key(BusinessCode)
     * @param {Boolean} changeUrl 是否改变地址栏url,默认为false
     */
    extendHighlightModelManageTree(modelManageTreeKey, changeUrl = false) {
      if(modelManageTreeKey === 'allmodel_phase') {
        this.UrlPhase = 'allmodel_phases'
        this.getAllListFeatures()
      } else {
        this.UrlPhase = modelManageTreeKey
        setTimeout(() => {
          if (!this.$refs.elTree) return;
          if (modelManageTreeKey) {
            this.modelManageTreeKey = modelManageTreeKey;
            setTimeout(() => {
              this.$refs.elTree.setCurrentKey(modelManageTreeKey);
              if (changeUrl) {
                const data = this.$refs.elTree.getCurrentNode();
                this.onElTreeNodeClick(data, this.$refs.elTree.store.currentNode);
              }
            }, 20);
          }
        }, 20);
      }
    }
  },
  destroyed() {
    clearTimeout(this.modelRefreshTimer);
  }
};
</script>
<style lang="scss" scoped>
.wrapperForClipImage {
  position: fixed;
  z-index: 100;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: rgba(0,0,0,0.5);
}
.MP {
  width: 500px;
  height: 350px;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 999;
}

.link-body {
  margin: 10px 20px;
  text-align: left;
  .link-top,
  .link-mid,
  .link-btm {
    width: 100%;
    margin-bottom: 24px;
  }

  .el-radio {
    line-height: 26px;
  }
  .top-title {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    .title {
      font-size: 14px;
    }
  }
  .share-title {
    font-size: 14px;
  }
  .share-link {
    width: 100%;
    height: 32px;
    line-height: 24px;
    border: 1px solid #ccc;
    border-radius: 6px;
    font-size: 14px;
    padding: 4px;
    margin: 4px 0;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    cursor: pointer;
  }
  .share-txt {
    font-size: 12px;
  }

  .share-radio {
    color: #fff;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    padding-top: 8px;
    /deep/ .el-radio {
      margin-bottom: 8px;
    }
  }
  .cancelBtn {
    width: 70px;
    height: 24px;
    line-height: 24px;
    text-align: center;
    font-size: 12px;
    border: 1px solid #98a3b3;
    border-radius: 4px;
    background: transparent;
    cursor: pointer;
    &:hover {
      border: 1px solid #1890ff;
      color: #1890ff;
    }
  }
}
._css-addBtnCtn {
  display: flex;
  flex-direction: row-reverse;
  height: 64px;
  align-items: center;
  box-sizing: border-box;
  padding-right: 8px;
}
.model-new-content {
  text-align: left;
  height: calc(100% - 20px);
  overflow: auto;
  position: relative;
  display: flex;
  flex-direction: column;
  padding-bottom: 20px;
  // overflow: hidden;

  .model-manage-header {
    position: sticky;
    top: 0;
    z-index: 10;
    flex: none;
    height: 54px;
    display: flex;
    align-items: center;
    background-color: #fff;
    border-bottom: 1px solid rgba(0, 0, 0, 0.15);
    font-size: 16px;
    .label {
      padding-left: 16px;
      font-weight: 500;
    }
  }
  .model-manage-main {
    position: relative;
    flex: 1 1 auto;
    width: 100%;
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    align-items: stretch;
    overflow: hidden;
    * {
      box-sizing: border-box;
    }
    .model-manage-col1 {
      position: relative;
      z-index: 10;
      flex: 1 1 200px;
      min-width: 200px;
      max-width: 200px;
      height: calc(100% - 7px);
      background-color: #fff;
      transition: transform 0.1s ease-in-out;
      .col1-title {
        position: relative;
        display: flex;
        margin-bottom: 8px;
        padding-top: 12px;
        padding-left: 16px;
        justify-content: flex-start;
        align-items: center;
        .label {
          margin-left: 12px;
          margin-bottom: -3px;
        }
        .import{
          position: absolute;
          font-weight: bold;
          right: 10px;
          margin-left: auto;
          font-size: 16px;
          color: #1890ff;
          cursor: pointer;
        }
      }
      .el-tree-cus {
        position: relative;
        padding-left: 16px;
        max-height: calc(100% - 30px);
        overflow-y: auto;
        overflow-x: auto; // 添加横向滚动
        background-color: #fff;
        &.disabled {
          pointer-events: none;
          cursor: not-allowed;
          &::after {
            position: absolute;
            z-index: 20;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            content: "";
            background-color: rgba(0, 0, 0, 0.5);
            cursor: not-allowed;
          }
        }
        // 美化滚动条样式
        &::-webkit-scrollbar {
          width: 6px;
          height: 6px; // 横向滚动条高度
        }

        &::-webkit-scrollbar-thumb {
          background: #c0c4cc;
          border-radius: 3px;
        }

        &::-webkit-scrollbar-track {
          background: #f6f6f6;
        }
        /deep/ .el-tree {
          min-width: max-content; // 确保树容器有足够宽度
        }
        /deep/ .el-tree-node {
          min-width: max-content; // 确保节点内容不被截断
          white-space: nowrap; // 防止文本换行
          .el-tree-node__content {
            display: flex;
            justify-content: flex-start;
            align-items: center;
            padding-top: 8px;
            padding-bottom: 8px;
            min-width: max-content; // 确保内容容器有足够宽度
            &:hover {
              .el-tree-node-cus .extra {
                .extra-btn {
                  display: block;
                }
                .extra-label {
                  margin-right: 5px;
                  display: none;
                }
              }
            }
            .el-tree-node-cus {
              position: relative;
              display: flex;
              min-width: max-content; // 改为min-width确保内容不被截断
              justify-content: flex-start;
              align-items: center;
              .label {
                flex: 0 0 auto; // 改为不压缩
                white-space: nowrap; // 防止文本换行
                overflow: visible; // 允许内容显示
                text-overflow: unset; // 不截断文本
              }
              .extra {
                position: relative; // 改为相对定位
                z-index: 10;
                display: flex;
                justify-content: flex-end;
                align-items: center;
                width: 32px;
                height: 100%;
                margin-left: 8px; // 添加左边距
                flex-shrink: 0; // 防止被压缩
                .extra-btn {
                  display: none;
                }
                .extra-label {
                  display: block;
                }
              }
            }
          }
        }
      }
      .collapse-bar {
        position: absolute;
        top: 50%;
        right: 0;
        width: 16px;
        height: 120px;
        background-image: url(../../../assets/images/p-in.png);
        background-size: 100%;
        background-repeat: no-repeat;
        z-index: 2;
        transform: translate3d(100%, -50%, 0);
        cursor: pointer;
      }
    }
    .model-manage-col2 {
      position: relative;
      flex: 1 1 auto;
      display: flex;
      flex-direction: column;
      justify-content: flex-start;
      align-items: stretch;
    }
    &.collapsed {
      .model-manage-col1 {
        position: absolute;
        transform: translate3d(-100%, 0, 0);
        .collapse-bar {
          background-image: url(../../../assets/images/p-out.png);
        }
      }
    }
  }
  .uploadDialogWrapper {
    position: fixed;
    top: 0;
    left: 0;
    z-index: 9000;
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: rgba(0, 0, 0, 0.2);
    * {
      box-sizing: border-box;
    }
    .uploadContent {
      position: relative;
      width: 400px;
      padding-bottom: 44px;
      font-size: 16px;
      background-color: #fff;
      .header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 14px;
        color: #000;
        background-color: #f8f8f8;
        .title {
          font-weight: 500;
        }
        .rest {
          display: flex;
          justify-content: center;
          align-items: center;
          .action {
            margin-right: 16px;
            font-size: 18px;
            cursor: pointer;
            &:last-of-type {
              margin-right: 0;
            }
          }
        }
      }
      .upload {
        width: 100%;
        padding: 20px;
        /deep/ .el-upload {
          width: 100%;
          .trigger {
            padding: 8px 0;
            border: 1px dashed #1890ff;
            border-radius: 6px;
            .el-icon-upload {
              font-size: 20px;
            }
            .el-upload__text {
              color: #1890ff;
            }
          }
          // .el-upload-dragger {
          //   border: 1px dashed #1890ff;
          //   width: 100%;
          // }
        }
        /deep/ .el-upload-list {
          max-height: 300px;
          overflow-y: auto;
          &::-webkit-scrollbar {
            /*滚动条整体样式*/
            width: 4px;
            /*高宽分别对应横竖滚动条的尺寸*/
            height: 4px;
          }
          &::-webkit-scrollbar-thumb {
            /*滚动条里面小方块*/
            border-radius: 5px;
            -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
            background: rgba(0, 0, 0, 0.2);
          }

          &::-webkit-scrollbar-track {
            /*滚动条里面轨道*/
            -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
            border-radius: 0;
            background: rgba(0, 0, 0, 0.1);
          }
        }
      }
      .footer {
        position: absolute;
        bottom: 0;
        left: 0;
        z-index: 10;
        width: 100%;
        padding: 0 20px 20px 0;
        display: flex;
        justify-content: flex-end;
        align-items: center;
        .btn {
          padding: 5px 16px;
          border-radius: 3px;
          &:not(:last-of-type) {
            margin-right: 16px;
          }
        }
      }
    }
  }
  .fileListToUpload {
    $minWidth: 560px;
    position: absolute;
    bottom: 20px;
    left: 224px;
    z-index: 900;
    min-width: $minWidth;
    width: 45%;
    padding-bottom: 10px;
    background-color: #fff;
    transition: left 0.1s ease-in-out;
    font-size: 14px;
    // box-shadow: 0 0 4px 4px #ccc;
    &.collapsed {
      left: 24px;
    }
    * {
      box-sizing: border-box;
    }
    .title {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 8px;
      .label {
        color: #000;
      }
      .iconClose {
        display: flex;
        justify-content: center;
        align-items: center;
        padding: 4px;
        cursor: pointer;
      }
    }
    .content {
      .header {
        width: 100%;
        height: 32px;
        padding: 8px;
        display: flex;
        border-bottom: 1px solid #ccc;
        justify-content: space-evenly;
        align-items: center;
        background-color: #fff;
        .item {
          text-align: center;
          &.name {
            flex: 1 1 auto;
          }
          &.size {
            flex: 0 0 auto;
            width: 150px;
          }
          &.status {
            flex: 0 0 auto;
            width: 90px;
          }
        }
      }
      .elScrollbarCus {
        /deep/ .el-scrollbar__bar.is-horizontal {
          display: none;
        }
        .files {
          max-height: 260px;
          .fileItem {
            display: flex;
            width: 100%;
            padding: 8px;
            justify-content: space-evenly;
            border-bottom: 1px solid #ccc;
            align-items: center;
            &:hover {
              background-color: #f0f2f5;
            }

            .item {
              text-align: center;
              &.name {
                flex: 1 1 auto;
                text-overflow: ellipsis;
                overflow: hidden;
                word-break: break-all;
                white-space: nowrap;
              }
              &.size {
                flex: 0 0 auto;
                width: 150px;
              }
              &.status {
                flex: 0 0 auto;
                width: 90px;
              }
            }
          }
        }
      }
    }
    ._css-progress-font-running {
      color: #1890ff;
    }

    .isError {
      color: #f00;
    }
  }
  .uploadHelpWrapper {
    position: fixed;
    top: 0;
    left: 0;
    z-index: 9999;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.4);
    * {
      box-sizing: border-box;
    }
    display: flex;
    justify-content: center;
    align-items: center;
    .content {
      width: 50%;
      max-width: 800px;
      height: 70%;
      .header {
        display: flex;
        justify-content: flex-end;
        align-items: center;
        width: 100%;
        padding: 8px;
        background-color: #fff;
        border-bottom: 1px solid #999;
        .icon {
          font-size: 24px;
          cursor: pointer;
        }
      }
      .iframeHelp {
        width: 100%;
        height: 100%;
        padding: 12px;
        border: 0;
        background: #fff;
      }
    }
  }

  ._css-pjsection-btnlist {
    position: fixed;
    z-index: 20;
    width: 120px;
    background: rgba(255, 255, 255, 1);
    box-shadow: 0px 1px 3px 0px rgba(0, 21, 41, 0.12);
    border-radius: 2px;
    box-sizing: border-box;
    padding-top: 4px;
    padding-bottom: 4px;
    transition: all 0.3s ease;
  }

  ._css-pjsection-btnicon {
    width: 16px;
    height: 16px;
    font-size: 16px;
    margin-left: 24px;
    margin: 14px 18px 14px 14px;
  }
  ._css-pjsection-btn {
    height: 40px;
    display: flex;
    align-items: center;
    // justify-content: space-around;
    color: rgba(0, 0, 0, 0.85);
    cursor: pointer;
    &:hover {
      background-color: rgba(0, 0, 0, 0.04);
    }
  }
}
.not-click {
  button {
    cursor: not-allowed !important;
    opacity: 0.5;
  }
}
.up-model-btn {
  margin: 8px 0;
  height: 54px;
  padding: 0 24px;
  text-align: left;
  line-height: 54px;
  position: relative;
  button {
    width: 120px;
    height: 40px;
    border-radius: 3px;
    background-color: #1890ff;
    border: none;
    color: #fff;
    outline: none;
    position: absolute;
    padding: 0;
    left: 24px;
    top: 8px;
    cursor: pointer;
  }
  button.not-edit {
    cursor: not-allowed;
    opacity: 0.5;
  }
}
.model-list-self {
  position: relative;
  margin-bottom: 20px;
  width: 100%;
  &.isDescendantHasFeature::after {
    position: absolute;
    bottom: 0;
    left: 24px;
    right: 24px;
    height: 1px;
    background-color: #fff;
    content: "";
  }
}
.model-list {
  margin-left: 24px;
  margin-right: 24px;
  flex: 1 1 auto;
  overflow-y: auto;

  // 表格样式
  .el-table {
    border: 1px solid #e6e6e6;
    border-radius: 4px;

    &::before {
      display: none;
    }

    &::after {
      display: none;
    }
    // 添加表格内部边框
    /deep/ .el-table__header th,
    /deep/ .el-table__body td {
      text-align: center;
      vertical-align: middle;
      border-right: 1px solid #e6e6e6;
      border-bottom: 1px solid #e6e6e6;

      &:last-child {
        border-right: none;
      }
    }
    // 模型名称列（假设是第二列）左对齐
    /deep/ .el-table__header th:nth-child(2) .cell,
    /deep/ .el-table__body td:nth-child(2) .cell {
      justify-content: flex-start;
    }
    /deep/ .el-table__header-wrapper{
      height: 40px !important;
    }
    /deep/ .el-table__header {
      background-color: #F5F5F5;
      height: 40px !important;
      line-height: 40px !important;

      th {
        height: 40px !important;
        line-height: 40px !important;
        background-color: #F5F5F5;
        color:  #132B4D;
        font-weight: 500;
        font-size: 14px;
        padding: 0 !important;
        border-bottom: 1px solid #e6e6e6;
        vertical-align: middle;

        .cell {
          display: flex;
          align-items: center;
          justify-content: center;
          height: 40px;
          line-height: normal;
        }
      }
    }

    /deep/ .el-table__body {
      tr {
        &:hover {
          background-color: #f5f7fa !important;
        }

        &.table-row-cursor {
          cursor: pointer;
        }

        td {
          border-bottom: 1px solid #f0f0f0;

          .cell {
            height: 84px !important;
            display: flex;
            align-items: center;
            justify-content: center;
          }
        }
      }
    }
  }

  // 缩略图容器
  .thumbnail-container {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 100%;
    min-height: 70px; // 确保有足够的高度

    .img-model-list {
      width: 100px;
      height: 68px;
      border-radius: 4px;
      object-fit: cover;
      border: 1px solid #e6e6e6;
      display: block; // 确保图片作为块级元素
    }

    .img-conver {
      width: 40px;
      height: 40px;
      border-radius: 4px;
      display: block; // 确保图片作为块级元素

      &.imgloading {
        animation: rotating 2s linear infinite;
      }
    }
  }

  // 模型名称
  .model-name-text {
    width: 100%;
    height: 100%;
    cursor: pointer;
    font-size: 14px;
    color: #132B4D;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    display: flex;
    align-items: center;
  }

  // 更新时间
  .update-time-text {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 100%;
    font-size: 14px;
    color: #132B4D;
  }

  // 状态文本
  .status-text {
    font-size: 12px;
    color: #666;
  }

  // 关联数量
  .link-count {
    font-size: 14px;
    color: #132B4D;
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 100%;
  }

  // 操作按钮
  .operation-buttons {
    position: relative;
    cursor: pointer;
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 100%;

    .set-list {
      position: fixed;
      right: 45px;
      width: 140px;
      background-color: #fff;
      border: 1px solid #e6e6e6;
      border-radius: 4px;
      box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
      z-index: 1000;
      display: none;

      &.set-list-show {
        display: block;
      }

      li {
        height: 40px;
        cursor: pointer;
        font-size: 14px;
        color: black;
        display: flex;
        align-items: center;
        padding: 0 10px;

        &:hover {
          background-color: #f5f7fa;
        }

        &.not-edit {
          cursor: not-allowed;
          opacity: 0.5;
        }

        i {
          margin-right: 8px;
          font-size: 14px;
        }

        .imore {
          margin-left: auto;
          margin-right: 0;
        }

        .itemMenu {
          position: absolute;
          right: 100%;
          top: 0;
          width: 140px;
          background-color: #fff;
          border: 1px solid #e6e6e6;
          border-radius: 4px;
          box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);

          &.edit {
            right: 100%;
            top: 40px;
          }

          li {
            padding: 8px 12px;

            &:hover {
              background-color: #f5f7fa;
            }
          }
        }
      }
    }
  }

  // 状态图标和文本的样式
  .css-conver {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;

    .img-text {
      font-size: 12px;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      margin-top: 8px;

      &.c666666 {
        color: #666666;
      }

      &.c407FFE {
        color: #407ffe;
      }

      &.cFF6640 {
        color: #ff6640;
      }
    }
  }

  &.isLoadingFeaturesList {
    overflow-y: hidden;
  }
}
.txtSearch {
  display: inline-block;
  position: relative;
  margin-top: 12px;
  width: 400px;
  height: 32px;
  background-color: #e6e8eb;
  border: none;
  border-radius: 3px;
  text-indent: 26px;
  font-size: 13px;
  outline: none;
  left: 40%;
}
.txtSearch i {
  position: absolute;
  top: 8px;
  left: 10px;
  width: 18px;
  height: 18px;
  text-align: left;
}
.txtSearch i::before {
  float: left;
  width: auto;
  height: auto;
  text-indent: 0px;
}
.txtSearch input {
  width: calc(100% - 36px);
  margin-left: 34px;
  float: left;
  height: calc(100% - 2px);
  border: none;
  outline: none;
  background-color: #e6e8eb;
}
.txtSearch input::-webkit-input-placeholder {
  font-size: 13px;
  font-family: PingFangSC-Regular;
  line-height: 32px;
}
.model-detail-iframe {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: #fff;
  width: 100%;
  height: 100%;
  z-index: 1009;
}
.mi {
  width: 869px;
  height: 420px;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 97;
}
.RN {
  width: 368px;
  height: auto;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 999;
  padding: 0px 16px 0px 16px;
}
</style>
<style>
.jingruizhang-probim-vue.css-zdialog-titlelabel {
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}
body {
  overflow: hidden;
}
</style>
