<template>
  <div class="attributeView-wrapper" v-if="isHidden">
    <header>
          <div class="title">
              属性
          </div>
          <div class="handle-box">
            <div @click.stop="close">
              <i class="el-icon-close"></i>
            </div>
          </div>
      </header>
      <div class="attribute-content">
        <div class="tab-wrapper">
            <div class="tab-item" :class="{'active' : active === 0}" @click="changeTab(0)">属性</div>
            <div class="tab-item" :class="{'active' : active === 1}" @click="changeTab(1)">扩展</div>
            <div class="tab-item" :class="{'active' : active === 2}" @click="changeTab(2)">文档</div>
        </div>
        <div class="attribute-container">

          <div class="center">
            <!--<div @click="cybtest">asdasd</div>-->
            <div class="content"
                :class="{'flex':active === 1, 'pd0':active === 2}">
                <div v-show="active === 0">
                    <div v-show="detailsData.length === 0" class="text-center no-select">请选择构件查看属性</div>
                    <el-table
                        header-row-class-name="attribute-first-row"
                        v-if="detailsData.length>0"
                        empty-text="请选择构件查看属性"
                        border
                        :span-method="arraySpanMethod"
                        :row-class-name="arraySpanClass"
                        :data="detailsData" style="width: 100%">
                        <el-table-column style="text-align: center" prop="key" label="名称"/>
                        <el-table-column style="text-align: center" prop="val" label="属性"/>
                    </el-table>
                </div>
                <div style="display: flex;flex-direction: column;flex:1" v-show="active === 1">
                    <div style="overflow-y:scroll;flex:1">
                        <el-table
                            header-row-class-name="attribute-first-row"
                            empty-text="暂无扩展属性"
                            border
                            :data="propertyList" style="width: 100%">
                            <el-table-column label="名称">
                                <template slot-scope="scope">
                                    {{ scope.row.PropertyName }}
                                </template>
                            </el-table-column>
                            <el-table-column label="值">
                                <template slot-scope="scope">
                                    <div class="value">
                                        <span>{{ scope.row.PropertyValue }} {{ scope.row.PropertyUnit }}</span>
                                        <el-dropdown placement="bottom">
                                            <span class="el-dropdown-link">
                                                <i class="el-icon-more"></i>
                                                <!-- <CommonSVG :size="20" icon-class="Set-up"> </CommonSVG> -->
                                            </span>
                                            <el-dropdown-menu slot="dropdown">
                                                <el-dropdown-item @click.native.stop="handleitem(scope.row, 'modify')">
                                                    <!-- <CommonSVG
                                                    color="#DCDCDC"
                                                    size="16"
                                                    icon-class="bianji"
                                                    ></CommonSVG> -->
                                                    <i class="el-icon-edit"></i>
                                                    <span class="text">编辑</span>
                                                </el-dropdown-item>
                                                <el-dropdown-item @click.native.stop="handleitem(scope.row, 'delete')">
                                                    <!-- <CommonSVG
                                                    color="#DCDCDC"
                                                    size="16"
                                                    icon-class="delete"
                                                    ></CommonSVG> -->
                                                    <i class="el-icon-delete"></i>
                                                    <span class="text">删除</span>
                                                </el-dropdown-item>
                                            </el-dropdown-menu>
                                        </el-dropdown>
                                    </div>
                                </template>
                            </el-table-column>
                        </el-table>
                    </div>
                    <div class="add-btn" :class="{'disabled':!componentId}" @click="addItem()">新建扩展属性</div>
                </div>

                <div class="document-list" v-show="active === 2">
                  <div class="list-content">
                    <div class="no-data" v-if="elementFileData.length === 0">
                      暂无文档
                    </div>
                    <template v-else>
                      <div class="list-item" v-for="(item, index) in elementFileData" :key="index">
                        <div class="text-box" @click="previewfile(item)" >
                          <i
                            :class="'css-icon20 css-fs18 css-fc css-jcsa ' + $staticmethod.getIconClassByExtname(item.FileName, item.FileSize)"
                          ></i>
                          <span class="text">{{ item.FileName }}</span>
                        </div>
                        <div class="handle-btn">
                          <div
                            @click.stop="removeFile(item.RelationId)"
                            class="_css-fileitem-contentbtn css-icon20 css-cp icon-interface-delete"
                          ></div>
                          <div
                            @click.stop="context_download(item.FileId)"
                            style="margin-left:4px"
                            class="_css-fileitem-contentbtn css-icon20 css-cp icon-interface-download-fill"
                          ></div>
                        </div>
                      </div>
                    </template>
                  </div>
                  <div class="doc-footer">
                    <div class="add-btn" :class="{'disabled':!componentId}" @click="addFile()">关联文档</div>
                  </div>
                </div>
            </div>
          </div>
        </div>
      </div>

      <div
        v-if="addDialog"
        class="add-dialog-wrapper">
        <header>
            <div class="title">
                新增自定义属性
            </div>
            <div class="handle-box">
              <div @click.stop="closeAddDialog">
                <i class="el-icon-close"></i>
              </div>
            </div>
        </header>
        <div class="add-property-wrapper">

            <div class="data-item">
                <el-input size="small" type="text" title=''
                    v-model="addForm.PropertyName" placeholder="请输入属性名称">
                    <div slot="prefix" class="prefix-text">
                        属性名称
                    </div>
                </el-input>
            </div>
            <div class="data-item">
                <el-input size="small" type="text" title=''
                    v-model="addForm.PropertyValue" placeholder="请输入属性值">
                    <div slot="prefix" class="prefix-text">
                        属性值
                    </div>
                </el-input>
            </div>
            <div class="data-item">
                <el-input size="small" type="text" title=''
                    v-model="addForm.PropertyUnit" placeholder="请输入单位">
                    <div slot="prefix" class="prefix-text">
                        单位
                    </div>
                </el-input>
            </div>
            <div class="btns">
                <div class="btn" @click="closeAddDialog()">取消</div>
                <div class="btn" @click="confirm()">确定</div>
            </div>
        </div>
      </div>
  </div>
</template>

<script>

  export default {
      name: "Attribute",
      components: {
          // CommonSVG: () => import("../Common/CommonSVG"),

      },
      props: {
        visible: {
          type: Boolean,
          default: false
        }
      },
      data(){
          return {
            detailsData: [],
            propertyList: [],
            attrHeight: 500,
            attrTop: 170,
            active: 0,
            addDialog: false,
            componentId: '',
            modelId: '',
            elementFileData: [],
            isUpdate: false,
            addForm: {
                PropertyName: '',
                PropertyValue: '',
                PropertyUnit: ''
            }

          }
      },
      computed: {
        isHidden: {
          get () {
            return this.visible
          },
          set (v) {
            this.$emit('update:visible', v)
          }
        }
      },

      created() {
          // this.setDialogSize()
          // window.addEventListener('resize',this.setDialogSize)
          const contentWindow = document.getElementById('scene-iframe').contentWindow
          //开启监听，从可视域分析中查看构件属性
          contentWindow.sceneExtendEventListener.$bus.on('viewshedShowAttribute',this.getSelectedObjects)

      },

      mounted () {
          this.getAttributeOnmounted()
          // this.getPropertyList()
          console.log('attribute mounted!!!')
          //注册画布点击事件，用来获取当前选择的构件
          //如果后续提供 监听构件被选择的方法，这里需要修改
          // window.scene.mapbox.mv.dom.addEventListener("click",this.getSelectedObjects);
          const contentWindow = document.getElementById('scene-iframe').contentWindow

          contentWindow.scene.mv.events.pickFinished.on('default',this.getSelectedObjects)

      },
      beforeDestroy () {
        const contentWindow = document.getElementById('scene-iframe').contentWindow
          // window.scene.mapbox.mv.dom.removeEventListener("click",this.getSelectedObjects);
          contentWindow.scene.mv.events.pickFinished.off('default',this.getSelectedObjects)
          contentWindow.sceneExtendEventListener.$bus.off('viewshedShowAttribute',this.getSelectedObjects)
      },
      methods: {

        close () {
          this.$emit('cancel')
        },
        addFile () {
          if (!this.componentId) return
          this.$emit('addFile')
        },
        previewfile (data) {
          this.$emit('previewfile', data)
        },
        removeFile (id) {
          this.$emit('removeFile', id)
        },
        context_download (id) {
          this.$emit('context_download', id)
        },

          addItem () {
              if (!this.componentId) return
              this.addDialog = true
          },
          setDialogSize () {
              let bh = document.body.clientHeight
              let th = document.querySelector('.desktop-view').clientHeight

              if (th == 0) {
                  this.attrHeight = bh - 40 - 30
                  this.attrTop = 50
              } else {
                  this.attrHeight = bh - th - 30
                  this.attrTop = th + 10
              }

          },

          //首次打开属性框时
          getAttributeOnmounted() {
              //获取选中的构件集合
              const contentWindow = document.getElementById('scene-iframe').contentWindow

              let elements = contentWindow.scene.getSelection().filter(item=>item.type=='element')
              console.log(elements,'当前选择的构件')
              if (elements.length > 0) {
                  //取最后一条选中的构件
                  let laseEle = elements[elements.length-1];
                  this.modelId = laseEle.model.modelID
                  //将最后一条选中构件对象传递过去
                  this.getSelectedObjects(laseEle.id)
              }
          },

          closeDialog () {
              console.log('关闭')
              this.$store.commit('toggleActiveDialog','attribute')
              this.$store.commit('toggleMenuActive','attribute')
          },

          closeAddDialog () {
              this.addDialog = false
              this.isUpdate = false
              this.addForm = {
                  PropertyName: '',
                  PropertyValue: '',
                  PropertyUnit: ''
              }
          },

          async confirm () {
              let res = null
              if (this.addForm.PropertyName == '') {
                  this.$message.error('请填写属性名称')
                  return
              }
              if (this.addForm.PropertyValue == '') {
                  this.$message.error('请填写属性值')
                  return
              }
              if (this.isUpdate) {
                this.$axios
                .post(`${window.bim_config.webserverurl}/api/Component/Property/Update`,
                {
                        Id: this.addForm.Id,
                        PropertyName: this.addForm.PropertyName,
                        PropertyValue: this.addForm.PropertyValue,
                        PropertyUnit: this.addForm.PropertyUnit,
                        Token: this.$staticmethod.Get('Token')
                    })
                .then(res => {
                  if (res.data && res.data.Ret == 1) {
                    this.$message.success('修改成功')
                    this.getPropertyList()
                    this.closeAddDialog()
                } else {
                    this.$message.error(res.data.Msg)
                }
                }).catch(err=>{
                  console.log(err)
                })
              } else {
                  this.$axios
                  .post(`${window.bim_config.webserverurl}/api/Component/Property/Add`,
                  {
                        ComponentId: this.componentId.split('^')[1],
                        ModelId: this.modelId,
                        PropertyName: this.addForm.PropertyName,
                        PropertyValue: this.addForm.PropertyValue,
                        PropertyUnit: this.addForm.PropertyUnit,
                        Token: this.$staticmethod.Get('Token')
                    })
                  .then(res => {
                    if (res.data && res.data.Ret == 1) {
                      this.$message.success('新增成功')
                      this.getPropertyList()
                      this.closeAddDialog()
                  } else {
                      this.$message.error(res.data.Msg)
                  }
                  }).catch(err=>{
                    console.log(err)
                  })

              }
          },

          getPropertyList () {
              this.$axios
              .get(`${window.bim_config.webserverurl}/api/Component/Property/List?componentId=${this.componentId.split('^')[1]}&modelId=${this.modelId}&Token=${this.$staticmethod.Get('Token')}`)
              .then(res => {
                if (res.data.Ret === 1) {
                  this.propertyList = res.data.Data
                }
              }).catch(err=>{
                console.log(err)
              })

          },

          getDocumentList () {
            const elementId = this.componentId.split('^')[1]
            // this.v_Id = undefined
            // this.extdata._openstacks = []

            let _this = this
            _this.$axios({
              method: "get",
              url: `${window.bim_config.webserverurl}/api/v1/file/elementrelation?elementId=${elementId}&Token=${this.$staticmethod.Get('Token')}`
            }).then(x => {
              if(x.data.Ret == 1){
                x.data.Data.map(v => {
                  v.FileExtension = '.' + v.FileName.split('.')[1]
                  return v
                })
                _this.elementFileData = x.data.Data;
                // if (this.elementFileData.length) {
                //   this.elementFileListHidden = true
                // } else {
                //   this.docListHidden = true
                //   this.$nextTick(() => {
                //     this.load_tree_firstlevelfolder()
                //   })
                // }
              }else{
                _this.elementFileData= []

                _this.$message({
                  message: x.data.Msg,
                  type: 'warning'
                })
              }
            }).catch(x => {
              console.log(x);
            });
          },

          cybtest () {
              // console.log(this.$scene,'--子组件')
          },


          //获取选择构件的属性
          getSelectedObjects (id) {
              // console.log(id)
              this.componentId = id

              if (id != undefined) {
              const contentWindow = document.getElementById('scene-iframe').contentWindow
                  let elements = contentWindow.scene.findObject(id)
                  elements.getProperty().then(prop => {
                       // console.log(prop,'构件属性')
                       this.setSelectedObjectsProperty(prop)

                   })
                   this.getPropertyList()
                   this.getDocumentList()
              }
          },

          changeTab (tab) {
              this.active = tab
          },

          handleitem (data, type) {
              if (type === 'modify') {
                  this.addDialog = true
                  this.addForm = JSON.parse(JSON.stringify(data))
                  this.isUpdate = true
              } else {
                  this.$axios
                  .post(`${window.bim_config.webserverurl}/api/Component/Property/Remove`,
                  {
                    Id: data.Id,
                    Token: this.$staticmethod.Get('Token')
                  })
                  .then(res => {
                    if (res.data && res.data.Ret == 1) {
                      if (res.data.Ret == 1) {
                          this.$message.success('删除成功！')
                          this.getPropertyList()
                      } else {
                          this.$message.error(res.data.Msg)
                      }
                  } else {
                      this.$message.error(res.data.Msg)
                  }
                  }).catch(err=>{
                    console.log(err)
                  })

              }
          },


          setSelectedObjectsProperty (elementInfo) {
              this.detailsData = []
              let list = ['自定义属性', '限制条件', '结构', '阶段化', '构造', '图形', '标识数据', '材质和装饰', '分析属性', '尺寸标注']
              let elementKey = new Set(list)
              let storage = new Set()

              //将所有的分组摘出来
              elementInfo.forEach(element => {
                  storage.add(element.group)
                  elementKey.add(element.group)
              })

              //将当前构件不存在的属性分类移除掉
              elementKey.forEach(key=>{
                  if (!storage.has(key)) {
                      elementKey.delete(key)
                  }
              })

              elementKey.forEach(el => {
                  //每个分组创建一个对象 children里放的就是同一个分组的属性
                  this.detailsData.push({
                      key: el,
                      val:'',
                      children: []
                  })
                  elementInfo.forEach(d => {
                      //将属于同一个分组的数据 紧跟其后 push到数组中
                      if (d.group === el) {
                          this.detailsData.push({
                              id: d.id,
                              key: d.name,
                              val: d.value,
                              group: d.group
                          })
                      }
                  })
              })

              // console.log(this.detailsData,'处理完的构件属性')
          },

          //合并行
          arraySpanMethod (data) {
              if(!data.row.id){
                  return [1,2]
              }
          },
          //返回class
          arraySpanClass (data) {
              if(!data.row.id){
                  return 'table-title'
              }
          }
      },

      beforeDestroy() {
          // window.removeEventListener('resize',this.setDialogSize)
      },
  }
</script>

<style lang="scss">
.attributeView-wrapper {
  width: 360px;
  height: calc(100vh - 100px);
  position: fixed;
  right: 10px;
  top: 70px;
  background: #242630;
  display: flex;
  flex-direction: column;
  box-shadow: 0 13px 24px -17px rgba(11,41,62,0.8);
  .el-table,.el-table__expanded-cell {
    color: #FFFFFF;
    background-color: transparent !important;
  }

  .el-table__header tr,
  .el-table th, .el-table tr{
    background-color:transparent;
    color: #fff;
  }
  .el-table__header-wrapper {
    background: #525b62;
  }
  .el-table td.el-table__cell,
  .el-table th.el-table__cell.is-leaf {
    border-bottom: 1px solid #475059;
  }
  .el-table th.el-table__cell {
    color: #FFFFFF;
    background-color: #525B62;
  }
  .el-table--border::after, .el-table--group::after, .el-table::before {
    background-color: rgba(255,255,255,0);
  }
  .el-table--border .el-table__cell,
  .el-table__body-wrapper .el-table--border.is-scrolling-left~.el-table__fixed {
    border-right: 1px solid #475059;
  }
  .el-table--border td, .el-table--border th, .el-table__body-wrapper .el-table--border.is-scrolling-left~.el-table__fixed {
    border-right: 1px solid #475059;
  }
  .el-table--enable-row-hover .el-table__body tr:hover>td.el-table__cell {
    background-color: #525B62;
  }
  .el-table--enable-row-hover .el-table__body tr:hover>td {
    background-color: #525B62;
  }

  .el-table--border, .el-table--group {
    border: 1px solid #475059;
  }
  .el-table td, .el-table th.is-leaf {
    border-bottom: 1px solid #475059;
  }

  .datas-table-column {
    //background: rgba(255, 255, 255, 0.1);
    background-color: #363F46;
    color: #FFFFFF;

    .custom-column {
      width: 100%;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      /*color: #FFFFFF;*/
      /*display: inline-block;*/
    }
    td.el-table__cell {
      border-bottom: 8px solid rgba(31, 41, 51, 0.95);

      .el-radio {
        .el-radio__label {
          display: none;
        }
      }
    }

    span.el-checkbox__inner {
      border: 1px solid #FFFFFF;
    }
  }
  header {
    color: #FFFFFF;
    background-color: #13141a;
    height: 40px;
    line-height: 40px;
    padding: 0 9px;
    display: flex;
    /*border-bottom: 1px solid #E9ECED;*/
    box-shadow: inset 0px -1px 0px 0px #475059;
    justify-content: space-between;
    align-items: center;
    /*border-radius: 16px 16px 0 0;*/

    div {
        display: flex
    }
    .title{
        /*font-size: 16px;*/
    }

    i {
        cursor: pointer;
        font-size: 16px;
    }
    i.el-icon-close{
        font-size: 20px;
    }
  }
  .center {
      flex: 1;
      // overflow: auto;
      color: #FFFFFF;
      display: flex;
      flex-direction: column;
      /*border-radius: 0 0 16px 16px;*/
      .no-select {
        text-align: center;
      }
  }
}
  .attribute-container {
      flex: 1;
      overflow-y: scroll;
      display: flex;
      flex-direction: column;
      &::-webkit-scrollbar {
        display: block;
      }
      &::-webkit-scrollbar-thumb {
        background: #98a3b3;
      }
      &::-webkit-scrollbar-thumb:hover {
        background: #98a3b3;
      }
      .table-title {
        .cell {
            display: flex;
            justify-content: center;
            background: rgba(255, 255, 255, 0.08);
        }
      }
      .content {
          padding: 16px 11px 16px 16px;
          min-height: 100%;
          box-sizing: border-box;
          &.flex {
              display: flex;
              flex-direction: column;
          }
          &.pd0 {
            padding: 0;
          }
      }
      .value {
          display: flex;
          align-items: center;
          justify-content: space-between;
          width: 100%;
          span {
              margin-right: 4px;
          }
          .el-dropdown-link {
              cursor: pointer;
              .el-icon-more {
                  font-size: 20px;
                  margin-left: 4px;
                  color: rgba($color: #fff, $alpha: .8);
              }
          }
          .text {
              margin-left: 4px;
          }
      }
      .el-dropdown-menu {
          padding: 0;
      }
  }
  .attribute-content {
    display: flex;
    height: 100%;
    overflow: hidden;
  }
  .tab-wrapper {
    overflow: auto;
    height: 100%;
    box-shadow: inset -1px 0px 0px 0px #475059;
    background: #13141a;
    min-width: 40px;
    width: 40px;
    &::-webkit-scrollbar {
      display: none;
    }
    .tab-item {
      color: #ddd;
      font-size: 14px;
      font-weight: 400;
      background: #13141a;
      padding: 12px;
      box-sizing: border-box;
      cursor: pointer;
      box-shadow: inset -1px 0px 0px 0px #475059;
      &:hover {
        background-color: #242630;
        color: #fff;
        box-shadow: 4px 0px 8px 0px #13141a;
      }
      &.active {
        background-color: #2680fe;
        color: #fff;
        box-shadow: 4px 0px 8px 0px #13141a;
      }
    }
  }


  .attribute-first-row > th {
      text-align: center !important;
  }
  .add-property-wrapper {
    .el-input {
      background: rgba(255,255,255,0.2);
      border-radius: 4px;
      overflow: hidden;
    }
      .el-input__prefix {
          display: flex;
          align-items: center;
          background: rgba(255, 255, 255, .1);
          width: 80px;
          padding-left: 12px;
          left: 0;
          border-radius: 4px;
          box-sizing: border-box;

      }
      .el-input--prefix .el-input__inner {
          padding-left: 92px;
          color: #fff;
      }
  }
</style>
<style scoped lang="scss">
.add-property-wrapper {
  padding: 24px 40px;
  box-sizing: border-box;
  .data-item {
      margin-bottom: 16px;
  }
  .btns {
      display: flex;
      justify-content: flex-end;
      .btn {
          margin-left: 16px;
          padding: 8px 20px;
          box-sizing: border-box;
          border-radius: 4px;
          background: rgba(255,255,255,0.2);
          border-radius: 4px;
          border: 1px solid #98A3B3;
          color: #fff;
          font-size: 12px;
          cursor: pointer;
      }
  }
}

.document-list {
  display: flex;
  height: 100%;
  flex-direction: column;
  .doc-footer {
    padding: 0 16px 16px;
    box-sizing: border-box;
  }
  .list-content {
      flex: 1;
      // overflow-y: scroll;
      .no-data {
        text-align: center;
        padding-top: 24px;
      }
      .list-item {
        padding: 12px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        color: #fff;
        font-size: 14px;
        cursor: pointer;
        .handle-btn {
          margin-right: 15px;
          display: none;
          position: absolute;
          right: 0;
        }
        .text {
          display: inline-block;
          width: 280px;
          overflow-x: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
        .text-box {
          display: flex;
          align-items: center;
          &:hover {
            color: #1890ff;
          }
        }
        &:hover {
          background: rgba($color: #fff, $alpha: .2);
          .handle-btn {
            display: flex;
          }
          .text {
            width: 250px;
          }
        }
        i {
          width: 16px;
          height:16px;
          margin-right: 2px;
        }
      }
    }
  }
.add-btn {
  text-align: center;
  padding: 8px 0;
  border-radius: 2px;
  border: 1px solid #2680fe;
  font-size: 14px;
  color: #fff;
  cursor: pointer;
  background: rgba($color: #fff, $alpha: .2);
  margin-top: 24px;
  &.disabled {
    opacity: .6;
    cursor: no-drop;
  }
}
.add-dialog-wrapper {
  width: 400px;
  height: 280px;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  position: fixed;
  background: #242630;
}
</style>
