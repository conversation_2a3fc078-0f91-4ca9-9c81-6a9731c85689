<template>
  <div class="model-iframe-content">
    <div class="detail-header" v-if="modelDetail">
      <div class="header-title-css model-word">
        <span class="font-Doc" @click="openModelDocumentsItem">
          <i class="icon-interface-folder"></i>
            &nbsp;关联文档 ({{ modelAndDocumentList.length }})
        </span>
        <span class="txtSearch">
          <i class="icon-interface-search ml4" @click.stop="searchModelList($event, 1)"></i>
          <input
            class="search-input-text"
            type="text"
            v-model="searchModelInfo"
            placeholder="输入构件ID或名称"
            @keyup="searchModelList($event)"/>
          <!-- <div class="input-dropdown" @click="searchExpansionList">
            <i class="search-up el-icon-caret-bottom"></i>
          </div> -->
          <div class="expan-list" v-if="expansionListShow">
            <div class="expan-header">高级筛选</div>
            <div class="expan-content">
              <ul>
                <li>
                  <label>类别</label>
                  <el-select
                  v-model="expanVal"
                  :popper-append-to-body="false"
                  popper-class="select-popper"
                  @change="expanChange"
                  placeholder="请选择">
                    <el-option
                      v-for="(item,index) in expanOptions"
                      :key="index"
                      :label="item.GroupName"
                      :value="item.GroupName">
                    </el-option>
                  </el-select>
                </li>
                <li>
                  <label>特性</label>
                  <el-select
                  v-model="expanChildVal"
                  :popper-append-to-body="false"
                  popper-class="select-popper"
                  placeholder="请选择">
                    <el-option
                      v-for="(item,index) in expanOptionsChild"
                      :key="index"
                      :label="item"
                      :value="item">
                    </el-option>
                  </el-select>
                </li>
                <li>
                  <label>条件</label>
                  <el-select
                  v-model="expanCondition"
                  :popper-append-to-body="false"
                  popper-class="select-popper"
                  placeholder="请选择">
                    <el-option
                      v-for="(item,index) in expanConditionOptions"
                      :key="index"
                      :label="item"
                      :value="item">
                    </el-option>
                  </el-select>
                </li>
                <li>
                  <label>条件值</label>
                  <el-input v-model="conditionInputValue" placeholder="请输入条件值"></el-input>
                </li>
              </ul>
              <div class="sure-btn" type="primary" @click="searchConFun">确定</div>
            </div>
          </div>
          <!-- <model-search-list></model-search-list> -->
        </span>
      </div>
      <div class="header-title-css text-title" :class="modelDetail ? 'margin120' : 'text-center'"><span>{{modelTitle}}</span></div>
      <div class="header-title-css flex-box">
        <div class="tools-item drawing-box" :class="{'active':isModelDrawing}" @click="openDrawDialog()">
          <img class="drawing-img" :src="drawSvg" />
            联动对比
        </div>
        <div class="tools-item">
          <img :src="toolsSkybox"  class="create"
                @click="isShowSkybox = true" title="设置环境">
        </div>
        <div class="tools-item">
          <img :src="toolsDefaultView"  class="create"
                @click="setCamera" title="设置默认视角">
        </div>
        <div class="tools-item">
          <img :src="toolsSaveToScene"  class="create"
                @click="isShowSaveToScene= true" title="保存场景">
        </div>
        <div class="tools-item">
          <i class="icon-interface-problem issue" title="问题追踪" @click="btncreateissue_click"></i>
        </div>

        <div class="tools-item">
          <i class="share icon-scene-share share-btn" @click="shareModel()"  v-if="modelDetail"></i>
        </div>
        <div class="tools-item">
          <div @click="closeModelDetail" class="close"><i class="share icon-scene-close"></i>关闭</div>
        </div>
      </div>
    </div>
    <div class="detail-header only-title" v-else>
      {{modelTitle}}
      <div class="tools-item">
          <div @click="closeModelDetail" class="close"><i class="share icon-scene-close"></i>关闭</div>
        </div>
    </div>
    <modelNewIframeLoading
      v-if="modelShow"
      class="model-iframe"
      :VaultID=VaultID
      :featureID=featureID
      :extension="extension"
      :modelVersion="versionNOProps"
      :isModelDrawing="isModelDrawing"
      @closeDrawingDialog="isModelDrawing=null"
    ></modelNewIframeLoading>
    <ViewDoc class="VD position"
      v-if="showViewDoc"
      :openUrl="OpenDocViewUrl"
      @closeViewDoc="showViewDoc=false;OpenDocViewUrl='';">
    </ViewDoc>

    <modelAssociatDocument
      class="model-document position"
      v-if="modelAndDocShow"
      @openDoc="openDoc"
      :docList="modelAndDocumentList"
      @closeDoc="modelAndDocShow=false"></modelAssociatDocument>
    <!--天空盒列表-->
    <div class="skybox-dialog" v-if="isShowSkybox" v-drag>
      <div class="icloud-dialog__header" >
        <span class="icloud-dialog__title">天空盒</span>
        <!-- <i class="share icon-scene-close" ></i> -->
         <div @click="isShowSkybox = false" class="close"><i class="share icon-scene-close "></i></div>
      </div>
      <div class="dialog__body">
        <ul class="skybox-ul">
          <li class="skybox-li" v-for="(item, index) in skyboxData" :key="index" @click="switchSky(item)">
            <img class="skybox-li-img" :src="item.url" :alt="item.name">
            <span>{{item.name}}</span>
          </li>
        </ul>
      </div>
    </div>
    <!--模型保存到场景-->
    <!-- <icloud-dialog title='保存场景' width="400px"  :visible.sync="isShowSaveToScene" @close="closeSceneDialog" >
      <el-form ref="saveToSceneForm" :model="saveToSceneForm" :rules="saveToSceneRules" label-position="top">
        <el-form-item label="场景名称" prop="name">
          <el-input v-model="saveToSceneForm.name" placeholder="请输入场景名称"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="icloud-dialog-footer">
        <wx-button @click="closeSceneDialog">取消</wx-button>
        <wx-button type="primary" @click="saveToScene">确定</wx-button>
      </div>
    </icloud-dialog> -->
    <zdialog-function
      :init_title="'保存场景'"
      :init_zindex="1002"
      :init_innerWidth="400"
      :init_width="270"
      init_closebtniconfontclass="icon-suggested-close"
      v-if="isShowSaveToScene"
      @onclose="closeSceneDialog()"
      >
        <div class="_css-qrcodearea" slot="mainslot" @mousedown="_stopPropagation($event)">
           <el-form ref="saveToSceneForm" :model="saveToSceneForm" :rules="saveToSceneRules" label-position="top">
            <el-form-item label="父级场景：" prop="parentSceneName">
              <div @click="openTreeDialog()">
                <el-input
                  v-model="saveToSceneForm.parentSceneName"
                  placeholder="选择父级场景"
                  readonly>
                  <i slot="suffix" class="el-icon-caret-bottom" style="cursor: pointer;"></i>
                </el-input>
              </div>
            </el-form-item>
            <el-form-item label="场景名称：" prop="name">
              <el-input v-model="saveToSceneForm.name" placeholder="输入场景名称"></el-input>
            </el-form-item>
          </el-form>
        </div>

        <div slot="buttonslot" class="css-common-zdialogbtnctn" >
            <zbutton-function
                :init_text="'取消'"
                :init_fontsize="14"
                :debugmode="true"
                :init_height="undefined"
                :init_width="'76px'"
                :init_bgcolor="'#fff'"
                :init_color="'#1890FF'"
                @onclick="closeSceneDialog()"
                >
            </zbutton-function>
            <zbutton-function
                :init_text="'确定'"
                :init_fontsize="14"
                :debugmode="true"
                :init_height="undefined"
                :init_width="'76px'"
                @onclick="saveToScene()"
                >
            </zbutton-function>
        </div>
      </zdialog-function>

    <!-- 场景层级选择弹窗 -->
    <ModelAddSceneDialog
      class="dialog-function"
      :init_title="'场景层级'"
      :init_zindex="1003"
      :init_innerWidth="500"
      :init_width="400"
      init_closebtniconfontclass="icon-suggested-close"
      v-if="showParentSceneDialog"
      @onclose="closeParentSceneDialog()"
      >
        <div class="_css-qrcodearea" slot="mainslot" @mousedown="_stopPropagation($event)">
            <el-tree
              class="el-tree-cus"
              node-key="BusinessCode"
              ref="elTree"
              empty-text="暂无子级"
              :highlight-current="true"
              :default-expand-all="true"
              :auto-expand-parent="true"
              :expand-on-click-node="false"
              :data="sceneTreeData"
              :props="elTreeProps"
              @node-click="onElTreeNodeClick"
            >
              <div class="el-tree-node-cus" slot-scope="{ node, data }">
                  <div class="label">{{ node.label }}</div>
                  <div class="extra">
                    <div class="extra-btn el-icon-check"></div>
                  </div>
              </div>
            </el-tree>
          </div>

        <div slot="buttonslot" class="css-common-zdialogbtnctn" >
            <zbutton-function
                :init_text="'取消'"
                :init_fontsize="14"
                :debugmode="true"
                :init_height="undefined"
                :init_width="'76px'"
                :init_bgcolor="'#fff'"
                :init_color="'#1890FF'"
                @onclick="closeParentSceneDialog()"
                >
            </zbutton-function>
            <zbutton-function
                :init_text="'确定'"
                :init_fontsize="14"
                :debugmode="true"
                :init_height="undefined"
                :init_width="'76px'"
                @onclick="confirmParentScene()"
                >
            </zbutton-function>
        </div>
      </ModelAddSceneDialog>
    <CompsCreateIssue v-if="showcreate" @oncancel="cancelcreate" @onok="createok" :hasImg="true" :outertop="300" @imgsrc="_imgsrc" @addMarkUp="addMarkUp" :zIndex="dialogzIndex" needMarkup :stayName="stayName"></CompsCreateIssue>
    <MarkupMenu v-if="showMarkupMenu" @closeMarkUpMenu="closeMarkUpMenu" @saveData="saveData" />
  </div>
</template>
<script>
import { EventBus } from "@static/event.js";
import CompsCreateIssue from "@/components/CompsIssue/CompsCreateIssue";
import MarkupMenu from "@/components/CompsIssue/markUpMenu";
import modelNewIframeLoading from './modelNewIframeLoading'
import ViewDoc from '@/components/Home/ProjectBoot/Document/ViewDoc'
import modelAssociatDocument from '@/components/Home/ProjectBoot/Model/modelAssociatDocument'
import modelSearchList from '@/components/Home/ProjectBoot/Model/modelSearchList'
import ModelAddSceneDialog from "../../CompsModel/ModelAddSceneDialog.vue";
export default {
  name: 'modelNewDetail',
  components:{
    ModelAddSceneDialog,
    modelNewIframeLoading,
    modelAssociatDocument,
    modelSearchList,
    ViewDoc,
    CompsCreateIssue,
    MarkupMenu
  },
  props: {
    modelDetail: {
      type: Boolean,
      default: false,
    },
    versionNO: {
      type: [String,Number],
      required:false,
      default: ''
    },
    modelTitle:{
      type: String,
      required:false,
      default: ''
    },
    featureID:{
      type: String,
      default: ''
    },
    featureName: {
      type: String,
      default: ''
    },
    VaultID: {
      type: String,
      default: ''
    },
    extension: {
      type: String,
      default: ''
    }
  },
  data(){
    return{
      elTreeProps: {
        children: "Children",
        label: "MenuName"
      },
      showcreate: false,
      modelAndDocumentList: [],
      modelAndDocShow: false,
      OpenDocViewUrl: '',
      showViewDoc: false,
      searchModelInfo: '',
      expansionListShow: false,
      expansionList: [],
      expanVal: '',
      expanOptions: [],
      expanChildVal: '',
      expanOptionsChild:[],
      expanCondition:[],
      expanConditionOptions: ['等于','不等于','大于','小于','包含','不包含'],
      conditionInputValue: '',
      selectorSelectEle: [], // 记录之前高亮的构件
      modelShow: false,
      toolsSkybox: require('@/assets/images/open-skybox-btn.png'),
      toolsDefaultView: require('@/assets/images/defalut-view-btn.png'),
      toolsSaveToScene: require('@/assets/images/save-to-scene-btn.png'),
      isShowSkybox: false,  // 天空盒默认值
      skyboxData: [
        {
          name: '早晨',
          type: 'morning',
          url: require('@/assets/images/skybox-morning.png')
        },
        {
          name: '中午',
          type: 'noon',
          url: require('@/assets/images/skybox-noon.png')
        },
        {
          name: '傍晚',
          type: 'evening',
          url: require('@/assets/images/skybox-evening.png')
        },
        {
          name: '夜间',
          type: 'night',
          url: require('@/assets/images/skybox-night.png')
        }
      ],
      isShowSaveToScene: false, // 保存场景默认
      skybox: null,
      saveToSceneForm: {
        name: '',
        parentSceneName: '',
        parentSceneId: ''
      },
      saveToSceneRules: {
        name: [{ required: true, message: '场景名称不能为空', trigger: 'change' }],
        parentSceneName: [{ required: true, message: '父级场景不能为空', trigger: 'change' }],
      },
      showParentSceneDialog: false,
      selectedParentScene: {
        name:'',
        id:''
      },
      sceneTreeData: [],
      isModelDrawing: null,
      drawSvg: require('../../../assets/images/drawing.png'),
      showMarkupMenu: false,
      dialogzIndex: 2,
      stayName: '', // 待保存的批注名称
      versionNOProps: '',
    }
  },
  created(){

  },
  mounted(){
    this.getModelDocumentNum()
    this.versionNOProps = this.versionNO + ''
    this.modelShow = true
  },
  methods: {
    onElTreeNodeClick(data, node) {
      console.log('data', data, 'node', node);
      this.selectedParentScene.name = data.MenuName;
      this.selectedParentScene.id = data.Id;
    },
    /**
     * 获取用户菜单树
     * @returns {Promise<void>}
     */
    async getUserMenuTree() {
      const res = await this.$axios
        .get(
          `${this.$urlPool.GetUserMenuTree}?token=${
            this.$staticmethod.Get('Token')
          }&organizeId=${this.$staticmethod._Get("organizeId")}&parentId=0`
        )
        .catch(() => {});
      if (res && res.data && res.data.Ret === 1) {
        const userMenuTree = res.data.Data;

        // 找到MenuCode是CJGL_1的对象
        const cjglItem = userMenuTree.find(item => item.MenuCode === 'CJGL_1');

        // 如果找到了该对象且有Children数组，则筛选掉MenuCode为CJGL的对象
        if (cjglItem && cjglItem.Children && Array.isArray(cjglItem.Children)) {
         this.sceneTreeData = cjglItem.Children.filter(child => child.MenuCode !== 'CJGL');
          console.log('sceneTreeData', this.sceneTreeData);
        }
      }
    },
    openTreeDialog(){
      this.getUserMenuTree()
      this.showParentSceneDialog = true
    },
    openDrawDialog () {
      this.isModelDrawing = new Date().getTime()
    },
    // 获取当前模型下关联的文档
    getModelDocumentNum(){
      let _userid=this.$staticmethod.Get("UserId")
      this.$axios
        .get(`${window.bim_config.webserverurl}/api/v1/file/modelfiles?modelId=${this.featureID}&userId=${_userid}&fileName=&Token=${this.$staticmethod.Get('Token')}`)
        .then(x=>{
          if(x.data.Data.length > 0 && x.data.Ret == 1){
            this.modelAndDocumentList = x.data.Data
          }else{
            this.modelAndDocumentList = [];
          }
        })
        .catch(err=>{})
    },
    // 打开关联文档详情
    openModelDocumentsItem(){
      this.modelAndDocShow = true;
    },
    openDoc(FileId, FileName, FileExtension) {
			let _this = this;
      let filedownloadurl = `${window.bim_config.webserverurl}/api/v1/file/preview?FileId=${FileId}&Version=1&UserId=${this.$staticmethod.Get("UserId")}&Token=${this.$staticmethod.Get('Token')}`
			// 根据扩展名获取在线浏览地址
      let url_iframe_all;
      // 判断如果是压缩文件的相关扩展名，直接下载：
      // -------------------------------------
      let zipExts = ['.zip', '.rar', '.7z', '.jar', '.tar'];
      let lastIndexOfDot = FileName.toLowerCase().lastIndexOf('.');
      if (lastIndexOfDot < 0) {
        window.location.href = filedownloadurl;
        return;
      }

      let theFileExt = FileName.substr(lastIndexOfDot);
      if (zipExts.indexOf(theFileExt) >= 0) {
        this.$message({
           message: "压缩文件不支持打开预览",
           type: "warning"
        });
        return;
      }
      if (FileName.toLowerCase().indexOf(".dwg") > 0) {
        // 修改当前预览的关闭按钮类
        _this.$emit("set_projectboot_extdata", "_docviewtype", "dwg");
        url_iframe_all = `${
          _this.$configjson.dwgurl
        }/Home/Index2?dwgurlcfg=${encodeURIComponent(filedownloadurl)}&name=${
          FileName
        }`;
      } else {
        // 修改当前预览的关闭按钮类
        _this.$emit("set_projectboot_extdata", "_docviewtype", "office");
        url_iframe_all = _this.$staticmethod.getHuangNewcomputeViewUrl(filedownloadurl, FileName,FileExtension);
      }
			_this.OpenDocViewUrl = url_iframe_all;
      _this.showViewDoc = true
    },
    closeModelDetail(){
      this.$emit('closeModelDetail')
    },
    shareModel () {
      const menuText = this.$staticmethod._Get("menuText") || '';
      if (menuText === '全部模型') {
        if(!this.$parent.hasAllShareAuth()) {
          this.$message({
            type: 'warning',
            message: '暂无分享权限',
            customClass: 'messageIndex'

          });
          return
        }
        this.$emit('shareModel')
      }else {
        if(!this.$parent.hasSomeAuth('Model_Share')) {
          this.$message({
            type: 'warning',
            message: '暂无分享权限',
            customClass: 'messageIndex'

          });
          return
        }
        this.$emit('shareModel')
      }

    },
    // 搜索
    searchModelList(e, jd){
      if (e.keyCode == 13 || jd == 1) {
        this.KeywordSearchModel();
      }
    },
    KeywordSearchModel(){
      // 根据模型名称还是ID
      /*
      GetElementByName 根据构件名称查找
      GetElementByID  根据构件ID查找
      GetElementByCategoryID  根据构件类别ID查找
      GetElementByTypeID  根据类型ID
      GetElementByFamilyeID 根据族ID
      SearchElementByProperty  根据属性值查找构件
      GetPropertyNames   获取一个模型下全部构件的属性组及属性名称  ==类别，特性
      */
      let numReg = /^[0-9]*$/
      let numRe = new RegExp(numReg)
      if(numRe.test(this.searchModelInfo)){
        // 为数字，根据构件ID
        this.searchByID();
      }else{
        // 非数字，根据构件名称
        this.searchByName();
      }
    },
    // 根据构件ID搜索
    searchByID(){
      this.$axios
        .get(`${this.$ip('newModelHttpUrl')}/Model/GetElementByID?VaultID=${this.VaultID}&ModelID=${this.featureID}&VersionNO=${this.versionNOProps}&ElementIDs=${this.searchModelInfo}`)
        .then(res=>{
          console.log(res)
          if(res.data.length > 0){
            // 这是根据ID查找的所有构件
            let eleData = res.data;
            let eles = eleData.map(key => {return `${this.featureID}^${key.elementID}`});
            this.selectorEleByElementId(eles);
          }else{
            this.$message.warning('搜索结果为空，请重新输入条件')
          }
        })
        .catch(err=>{})
    },
    // 根据Name搜索
    searchByName(){
      let _search = this.searchModelInfo;
      _search = _search.replaceAll('+','\\+')
      let name = encodeURIComponent(_search);
      this.$axios
        .get(`${this.$ip('newModelHttpUrl')}/Model/GetElementByName?VaultID=${this.VaultID}&ModelID=${this.featureID}&VersionNO=${this.versionNOProps}&ElementNames=${name}&RecordCount=0&Offset=1`)
        .then(res=>{
          console.log(res)
          if(res.data.length > 0){
            // 这是根据ID查找的所有构件
            let eleData = res.data
            let eles = eleData.map(key => {return `${this.featureID}^${key.elementID}`});
            this.selectorEleByElementId(eles);
          }else{
            this.$message.warning('搜索结果为空，请重新输入条件')
          }
        })
        .catch(err=>{

        })
    },
    // 获取高级搜索里面的相关数据
    searchExpansionList(){
      this.$axios
        .get(`${this.$ip('newModelHttpUrl')}/Model/GetPropertyNames?VaultID=${this.VaultID}&ModelID=${this.featureID}&VersionNO=${this.versionNOProps}`)
        .then(res=>{
          if(res.data.length > 0){
            this.expansionList = res.data;
            this.expanOptions = res.data;
            // 这是根据ID查找的所有构件
            // let eleData = res.data

          }else{
            this.$message.warning('搜索结果为空，请重新输入条件')
          }
        })
        .catch(err=>{})
      this.$nextTick(()=>{
        this.expansionListShow = true;
      })
    },
    // 选择类别change
    expanChange(val){
      let index = this.expanOptions.find((v)=>{return v.GroupName == val})
      this.expanOptionsChild = index.PropertyNames;
    },
    // 点击确定搜索相关构件
    searchConFun(){
      let data = {
        "vaultID": this.VaultID,
        "featureID": this.featureID,
        "objectID": "",
        "versionNOProps": this.versionNOProps + '',
        "entity":  this.filterCondition(this.expanCondition)
        // "entity":  "Name ='族名称' AND Value='基本墙' |AND| Name='结构' AND Value='否'"

      }
      this.$axios
        .post(`${this.$ip('newModelHttpUrl')}/Model/SearchElementByProperty`,data)
        .then(res=>{
          console.log(res,'SearchElementByProperty')
          // let eles = eleData.map(key => {return `${this.featureID}^${key.elementID}`});
          // this.selectorEleByElementId(eles);
          if(res.data.length > 0){
            // 这是根据ID查找的所有构件
            let eleData = res.data
            let eles = eleData.map(key => {return `${this.featureID}^${key.elementID}`});
            this.selectorEleByElementId(eles);
          }else{
            this.$message.warning('搜索结果为空，请重新输入条件')
          }
        })
        .catch(err=>{})

    },
    // 高亮构件
    selectorEleByElementId(eles){
      for(let i = 0; i < this.selectorSelectEle.length; i++ ){
        let element =window.model3.objects.get(this.selectorSelectEle[i])
        element.selected = false;
      }
      for(let i = 0; i < eles.length; i++ ){
        let element =window.model3.objects.get(eles[i])
        element.selected = true
      }
      window.scene.render();

      this.$nextTick(()=>{
        // 记录高亮的构件，再次进入的时候取消高亮
        this.selectorSelectEle = eles;
      })
    },
    // 高级搜索时不同条件下的参数值
    filterCondition(params){
      let con = ''
      switch(params){
        case '等于':
          con = `Name='${this.expanChildVal}' AND Value='${this.conditionInputValue}'`
          break;
        case '不等于':
          con = `Name='${this.expanChildVal}' AND Value<>'${this.conditionInputValue}'`
          break;
        case '大于':
          con = `Name='${this.expanChildVal}' AND CAST(Value as DOUBLE) > '${this.conditionInputValue}'`
          break;
        case '小于':
          con = `Name='${this.expanChildVal}' AND CAST(Value as DOUBLE) <'${this.conditionInputValue}'`
          break;
        case '包含':
          con = `Name='${this.expanChildVal}' AND  Value like '%${this.conditionInputValue}%'`
          break;
        case '不包含':
          con = `Name='${this.expanChildVal}' AND Value not like '%${this.conditionInputValue}%'`
          break;
      }
      return con;
    },
    /**
     * 切换天空盒
     */
    switchSky (item) {
      const sk1 = window.scene.addFeature('skybox')
      // 设置环境：morning-早晨、noon-中午、evening-傍晚、night-夜间
      console.log('skybox', item.type)
      window.model3.config.skybox = item.type
      window.model3.updateConfig()
      sk1.setTime(item.type)
      window.scene.render()
    },
    /**
     * 设置相机视角
     */
    setCamera () {
      window.model3.config.defaultViewpoint = window.scene.getCameraInfo()
      window.model3.updateConfig()
      this.$message.success('默认视角设置成功!')
    },
    /**
     * 关闭保存场景弹窗
     */
    closeSceneDialog () {
      this.$refs.saveToSceneForm.resetFields()
      this.isShowSaveToScene = false
      this.selectedParentScene = this.$options.data().selectedParentScene
    },
    /**
     * 关闭父级场景选择弹窗
     */
    closeParentSceneDialog() {
      this.showParentSceneDialog = false
      this.sceneSearchKeyword = ''
    },
    /**
     * 确认选择父级场景
     */
    confirmParentScene() {
      if (this.selectedParentScene) {
        this.saveToSceneForm.parentSceneName = this.selectedParentScene.name
        this.saveToSceneForm.parentSceneId = this.selectedParentScene.id
      }
      console.log('saveToSceneForm', this.saveToSceneForm)
      this.closeParentSceneDialog()
    },
    /**
     * 保存模型到场景
     */
    async saveToScene () {
      if (!this.saveToSceneForm.parentSceneName){
        this.$message.error("请选择父级场景");
        return
      }
      if (!this.saveToSceneForm.name){
        this.$message.error("请输入场景名称");
        return
      }
      let userID = this.$staticmethod.Get("UserId");

      let _data = {
          Comment: "",
          CreateTime: "",
          CreateUserName: "",
          ProjectId: this.$staticmethod._Get("organizeId"),
          SceneDataJson:"[]",
          SceneEntityDataJson: "[]",
          SceneEventDataJson:  window.scene.toJSON(),
          SceneId: "",
          SceneLogo: "",
          SceneName: this.saveToSceneForm.name,
          MenuId: this.saveToSceneForm.parentSceneId
      }
      const res = await this.$api.postsceneadd(userID,_data)

      if(res.Ret == 1){
        this.$message.success(res.Msg);
      }
      this.closeSceneDialog()
    },
    _stopPropagation(ev){
			ev && ev.stopPropagation && ev.stopPropagation();
    },
    // 添加批注
    addMarkUp (data) {
      this.showMarkupMenu = true
      this.dialogzIndex = -1

    },

    // 关闭批注
    closeMarkUpMenu () {
      this.showMarkupMenu = false
      this.dialogzIndex = 2
    },
    // 保存批注
    async saveData () {
      let viewPoints = await window.scene.snapMarkup(410).catch(err => {
          console.log(err)
      })
      let name = '批注' + new Date().getTime()
      this.stayName = name
      viewPoints.name = name

      //保存数据至场景（如果不save，scene.viewpoints会为空）
      viewPoints.save(window.scene);
      this.closeMarkUpMenu()
    },
    // 问题追踪
    btncreateissue_click(){
      this.showcreate = true;
    },
    // 问题追踪==取消
    cancelcreate(){
      this.showcreate = false;
      if (this.stayName) {
        // 删除还未保存的批注
        let eq = window.scene.viewpoints.findIndex(vp => vp.name == this.stayName)
            window.scene.viewpoints.splice(eq, 1)
            this.stayName = ''
      }

    },
    // 问题追踪==确定
    createok(obj) {
      let _this = this;
      //debugger;
      if (!obj.title || obj.title == "" || obj.title.trim() == '') {
        _this.$message.error("请输入标题");
        EventBus.$emit("R_InitiateProblem",true);
        return;
      }
      if (obj.deadlinetimeval == "") {
        _this.$message.error("请选择截止时间");
        EventBus.$emit("R_InitiateProblem",true);
        return;
      }

      // 参数：截止时间
      var timestr = _this.timeToString(obj.deadlinetimeval);
      // 参数：问题标题
      var title = obj.title;
      // 参数：参与人
      var joinerstr = "";
      if (obj.addingjoiners && obj.addingjoiners.length) {
          for (var i = 0; i < obj.addingjoiners.length; i++) {
              joinerstr += `${i == 0 ? "" : ","}${obj.addingjoiners[i].UserId}`;
          }
      }
      // 参数：附件FileId
      var fileidstr = "";
      if (obj.addingFiles && obj.addingFiles.length) {
          for (var i = 0; i < obj.addingFiles.length; i++) {
              fileidstr += `${i == 0 ? "" : ","}${obj.addingFiles[i].FileId}`;
          }
      }
      // 新的模型引擎，要保存当前选中构件，获取scene.toJSON()保存当前场景
      let elements = window.scene.selectedObjects;
      let selectedObjects =  JSON.stringify(Array.from(elements.keys()))
      _this.showcreate = false;

      if (this.stayName && window.scene.viewpoints.length) {
        let eq = window.scene.viewpoints.findIndex(vp => vp.name == this.stayName)
        let viewPoints = window.scene.viewpoints
        viewPoints[eq].name = '批注-' + title.slice(0, 8) + '' + new Date().getTime()

        // 这里追加一段逻辑：只保留索引相等的那个viewpoint
        window.scene.viewpoints = window.scene.viewpoints.filter((item,idx)=>{ return idx === eq })
      }

      this.$nextTick(()=>{
        // let _scenejson = window.scene.toJSON()

        const sceneIframe = document.getElementById("scene-iframe")
        const sceneIframeW = sceneIframe && sceneIframe.contentWindow
        const _scenejson = (sceneIframeW && sceneIframeW.getSceneJSON()) || ''

        _this
        .$axios({
          method: "post",
          url: `${this.$issueBaseUrl.AddIssue}`,
          data: {
              Token: _this.$staticmethod.Get("Token"),
              RealName: _this.$staticmethod.Get("RealName"),
              Title: title,
              JoinerIds: joinerstr,
              FileIds: fileidstr,
              EndDateStr: timestr,
              organizeId: _this.$staticmethod._Get("organizeId"),
              ModelID: _this.featureID, // 新的引擎没有modelid，使用的是SceneId
              IssueTypeId: obj.IssueTypeId || '',
              ImageUrl:obj.ImageUrl,//_this.ImageUrl,
              ViewPointID: selectedObjects,  // 新引擎没有这个视点，selectedObjects是选中的构件
              ImageIds: obj.ImageIds,
              Isoverride:_scenejson  // 新引擎没有视点，直接存的是scene.toJSON()
          }
        })
        .then(x => {
            if (x.data.Ret < 0) {
              _this.$message.error(x.data.Msg)
            } else {
              // 刷新
              _this.showcreate = false;
              this.stayName = ''
              _this.$message.success(x.data.Msg);

              // 追加一段逻辑，发起问题成功后，删除所有的批注
              window.scene.viewpoints = window.scene.viewpoints.filter(item => item.type !== 'markup')
              if(sceneIframeW) {
                sceneIframeW.deepUpdateScene('markup')
              }
            }
        })
        .catch(x => {
          // 刷新
          _this.showcreate = false;
        });
      })

    },
    // 问题追踪图片
    _imgsrc(){
      this.imageUrl = val;
    },
    timeToString(date) {
      let year = date.getFullYear();
      let month = date.getMonth() + 1;
      let dat = date.getDate();
      let h = date.getHours();
      let m = date.getMinutes();
      let s = date.getSeconds();
      let tostr = `${year}-${month < 10 ? "0" + month : month}-${
          dat < 10 ? "0" + dat : dat
      } ${h < 10 ? "0" + h : h}:${m < 10 ? "0" + m : m}:${
          s < 10 ? "0" + s : s
      }`;
      return tostr;
    },

  }
}
</script>
<style lang="scss" scoped>
.ml4 {
  margin-left: 4px;
}
.margin120{
  text-align: center;
  flex: 1;
}
.flex-box {
  display: flex;
  align-items: center;
  .share-btn {
    margin-right: 8px;
    cursor: pointer;
  }
  .issue {
    margin-right: 8px;
    color: #f3ac00;
    cursor: pointer;

  }
  .tools-item{
    display: flex;
    width: 100%;
    height: 100%;
    justify-content: center;
    align-items: center;
    &:hover{
      background-color: rgba(255, 255, 255, 0.15);
    }
    &.drawing-box {
      min-width: 100px;
      display: flex;
      align-items: center;
      cursor: pointer;
      img {
        margin-right: 8px;
      }
      &.active {
        background-color: rgba(255, 255, 255, 0.15);
      }
    }
    .create {
      margin-left: 10px;
      margin-right: 10px;
      width: 100px;
      height: 48px;
      cursor: pointer;
    }
  }
}
.model-iframe-content{
  width: 100%;
  height: 100%;
  .model-iframe{
    height: calc(100% - 49px);
  }
}
.content-frame{
  width: 100%;
  height: 100%;
}

.detail-header{
  .header-title-css{
    flex: 1;
  }
  display: flex;
  justify-content: space-between;
  width: calc(100% - 30px);
  height: 48px;
  line-height: 48px;
  border-bottom: 1px solid rgba(0,0,0,.15);
  padding-left: 20px;
  padding-right: 10px;
  position: relative;
  background: #061326;
  color: #fff;
  .text-title{
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
  }
  .model-word{
    width: 460px;
    cursor: pointer;
    i{
      vertical-align: middle;
    }
    .txtSearch{
      margin:0;
      display: inline-block;
      width: 200px;
      height: 32px;
      border: 1px solid #e6e8eb;
      border-radius: 4px;
      line-height: 32px;
      position: relative;
      top: 0px;
      left: 20px;
      .search-input-text{
        background: transparent;
        color: #fff;
      }

      .input-dropdown{
        position: absolute;
        top: 0;
        right: 0;
        width: 32px;
        height: 32px;
        line-height: 32px;
        i{
          text-align: center;
          color: #8c898d;
        }
        i:hover{
          color: #1990ff;
        }
      }
      .expan-list{
        position: absolute;
        top: 10px;
        background: #fff;
        padding-bottom: 10px;
        top: 34px;
        border-radius: 4px;
        .expan-header{
          text-align: center;
          font-weight: 600;
          border-bottom: 1px solid #dcdfe6;
        }
        ul{
          padding: 0 10px;
          /deep/ .el-input__inner{
            line-height: 32px;
            border-radius: 4px;
            border: 1px solid #dcdfe6;
          }
          li{
            margin-top: 10px;
          }
        }
        .sure-btn{
          text-align: center;
          width: 90%;
          height: 32px;
          line-height: 32px;
          border-radius: 4px;
          color: #fff;
          background: #1990ff;
          margin: 18px 5% 8px;
        }
      }
      input{
        width: calc(100% - 46px);
        border: none;
        outline: none;
      }
    }

  }
  .font-Doc:hover {
    color: #1890ff;
  }
  .icon{
    line-height: 56px;
    cursor: pointer;
  }
}
.VD{
  width:80%;
  height:80%;
  z-index:999;
}
.position{
  position: absolute;
  top:50%;
  left:50%;
  transform:translate(-50%,-50%);
}
.model-document{
  width:400px;
  height:500px;
  z-index:998;
}
.messageIndex{
  z-index: 1100 !important;
}
.detail-header /deep/ input::input-placeholder{
	color: rgba(255,255,255,0.8);
}
.detail-header /deep/ input::-webkit-input-placeholder{	//兼容WebKit browsers（Chrome的内核）
	color: rgba(255,255,255,0.8);
}
.detail-header /deep/ input::-moz-placeholder{			//Mozilla Firefox 4 to 18
	color: rgba(255,255,255,0.8);
}
.detail-header /deep/ input::-moz-placeholder{			//Mozilla Firefox 19+
	color: rgba(255,255,255,0.8);
}
.detail-header /deep/ input::-ms-input-placeholder{		//Internet Explorer 10+
	color: rgba(255,255,255,0.8);
}

.share{
  display: inline-block;
  width: 20px;
  height: 20px;
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center center;
}
.icon-scene-share{
  background-image: url('../../../assets/images/scene-share.png');
}
.icon-scene-close{
  width: 12px;
  height: 12px;
  margin-right: 5px;
  background-image: url('../../../assets/images/scene-close.png');
}
.flex-box .close,.detail-header.only-title .close{
  width: 60px;
  background: #ff6640;
  font-size: 12px;
  height: 24px;
  line-height: 24px;
  margin: auto 8px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}
.detail-header.only-title .close{
  margin-top: 10px;
}
.skybox-dialog {
  background: rgba(36, 38, 48, 1);
  z-index: 100;
  left: 40px;
  top: 80px;
  position: fixed;
  width: 376px;
  height: 174px;
  display: flex;
  flex-direction: column;
  box-shadow: 0px 13px 61px 0px rgba(169, 169, 169, 0.37);

  .icloud-dialog__header {
    background: rgba(6, 19, 38, 1);
    cursor: move;
    height: 40px;
    border-radius: 4px 4px 0px 0px;
    width: calc(100% - 48px);
    border-bottom: 1px solid rgba(6, 19, 38, 1);
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0px 24px;

    .icloud-dialog__title {
      display: inline-block;
      line-height: 40px;
      font-size: 16px;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      color: white;
    }
    .close{
      cursor: pointer;
    }
  }

  .dialog__body{
    padding: 16px;
    .skybox-ul{
      display: flex;
      .skybox-li{
        display: flex;
        flex-direction: column;
        align-items: center;
        margin-right: 16px;
        height: 72px;
        width: 72px;
        .skybox-li-img{
          border: 4px solid hsla(0,0%,100%,.1);
          border-radius: 4px;
          cursor: pointer;
          width: 76px;
          height: 76px;
        }
        span{
          margin-top: 6px;
          font-size: 12px;
          color: rgba(255, 255, 255, 1);
        }
      }
    }
  }
}
._css-qrcodearea{
  text-align: left;
  padding: 0 24px;
}
._css-qrcodearea /deep/ .el-input__inner{
  border-width: 1px;
}
.dialog-function{
  ._css-qrcodearea{
    height: 500px;
    text-align: left;
    padding: 12px 24px;
    .el-tree-cus {
      position: relative;
      padding-left: 16px;
      max-height: 500px;
      overflow-y: auto;
      overflow-x: auto; // 添加横向滚动
      background-color: #F5F7FA;
      &.disabled {
        pointer-events: none;
        cursor: not-allowed;
        &::after {
          position: absolute;
          z-index: 20;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          content: "";
          background-color: rgba(0, 0, 0, 0.5);
          cursor: not-allowed;
        }
      }
      // 美化滚动条样式
      &::-webkit-scrollbar {
        width: 6px;
        height: 6px; // 横向滚动条高度
      }

      &::-webkit-scrollbar-thumb {
        background: #c0c4cc;
        border-radius: 3px;
      }

      &::-webkit-scrollbar-track {
        background: #f6f6f6;
      }
      /deep/ .el-tree-node {
        min-width: fit-content; // 确保节点内容不被截断
        white-space: nowrap; // 防止文本换行
        .el-tree-node__content {
          display: flex;
          justify-content: flex-start;
          align-items: center;
          padding-top: 8px;
          padding-bottom: 8px;
          &.is-current {
            background-color: #F5F7FA !important;
            .el-tree-node-cus .extra .extra-btn {
              display: block !important;
            }
          }
          .el-tree-node-cus {
            position: relative;
            display: flex;
            width: calc(100% - 36px);
            justify-content: flex-start;
            align-items: center;
            .label {
              flex: 1 1 auto;
              max-width: calc(100% - 32px);
              text-overflow: ellipsis;
              overflow: hidden;
              word-break: break-all;
              white-space: nowrap;
            }
            .extra {
              position: absolute;
              z-index: 10;
              top: 0;
              right: 0;
              display: flex;
              justify-content: flex-end;
              align-items: center;
              width: 32px;
              height: 100%;
              text-overflow: ellipsis;
              overflow: hidden;
              word-break: break-all;
              white-space: nowrap;
              .extra-btn {
                color: #0B79F0;
                display: none;
                font-weight: 600;
                font-size: 18px;
              }
              .extra-label {
                display: block;
              }
            }
          }
        }
      }
      // 额外添加更高权重的选择器确保样式生效
      /deep/ .el-tree-node.is-current > .el-tree-node__content,
      /deep/ .el-tree-node__content.is-current {
        .el-tree-node-cus .extra .extra-btn {
          display: block !important;
        }
      }
    }
  }

}
</style>
