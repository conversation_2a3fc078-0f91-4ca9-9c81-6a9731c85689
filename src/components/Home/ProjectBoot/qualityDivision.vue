<template>
	<div class="_css-materials-all" ref="cssMaterialsAll">
		<div class="btn-initiating-header">{{ headerTitleText }}</div>
		<div class="_css-bottomarea" @click.stop="closeAll">
			<div class="left-content" ref="lefttree" v-if="proTreeIn">
				<div class="_css-materialtypelist-head">
					<span class="_css-materhead-text">数据结构</span>
				</div>
				<el-tree
					:data="treeData"
					node-key="Id"
					ref="tree"
					class="_css-customstyle"
					:highlight-current="true"
					:auto-expand-parent="true"
					default-expand-all
					:props="defaultProps"
					:expand-on-click-node="false"
					:default-checked-keys="defaultCheckedKeys"
					@current-change="treefunc_current_change"
				>
					<span class="css-fc _css-treenode-content" slot-scope="{ node, data }">
                          <i
                            class="css-mr4 css-fs16 css-fs18 css-fc css-jcsa " :class="data.ChildrenCount > 0  ? 'icon-new-tree-last' : 'icon-new-tree'" ></i>
                          	<span :title="node.label" class="css-ml4 _css-treenodelabel">
							<el-tooltip popper-class="css-no-triangle" v-if="data.DivisionName.length > 12" effect="dark" :content="data.DivisionName" placement="top">
								<div class="_css-treenodellabelname overflow-point">
									{{ data.DivisionName }}
								</div>
							</el-tooltip>
                            <div class="_css-treenodellabelname" v-else>{{data.DivisionName}}</div>
                          </span>
						<div @click.stop="func_tree_showmenu($event, data)" class="_css-treenode-menubtn icon-interface-list" ></div>
                    </span>
				</el-tree>
			</div>
			<div class="_css-choose-switch-parent">
				<div class="_css-choose-switch" @click.stop="chooseCloseOrOpen">
					<i
						class="pro-in-out"
						:class="tableClose ? 'pro-in-out' : 'p-out'"
					></i>
				</div>
			</div>
			<div class="content-right" :class="proTreeIn ? '222' : 'width100'">
				<div class="_css-materialtypetab-head">
					<span class="_css-click-close-or-open">
						<i
							:class="
								tableClose
									? 'icon-interface-toleft'
									: 'icon-interface-toright'
							"
						></i>
					</span>
					<div>{{ selectTreeData.DivisionName }}</div>
					<div class="right-input">
						<el-input
						v-model="inputVal"
						placeholder="请输入分类编码或分类名称"
						@input="searchName"
						></el-input>
						<i class="icon-interface-search"></i>
					</div>
				</div>
				<div class="_css-materialtypetab-body">
					<div class="table-top-bar" v-if="multipleSelection.length > 0">
						<ul class="menu-item menu-left" style="height: 100%">
							<li>
							<el-checkbox
								:indeterminate="tableTopBarState.isIndeterminate"
								v-model="tableTopBarState.checkAll"
								@change="handleTableTopBarCheckAllChange">
							</el-checkbox>
							</li>

							<li  @click="deleteSelectQualityItems">
							 <span class="icon icon-interface-delete"></span>删除
							</li>

							<li><span class="desc">已选择{{ multipleSelection.length }}项</span></li>
						</ul>
					</div>
					<el-table
						v-loading="elTableLoading"
						element-loading-text="数据加载中..."
						element-loading-spinner="el-icon-loading"
						element-loading-background="rgba(0, 0, 0, 0)"
						ref="multipleTable"
						highlight-current-row
						:border="true"
						:stripe="false"
						use-virtual
						height="70%"
						:data="tableData"
						style="width: 100%"
						:default-sort="{ prop: 'date', order: 'descending' }"
						class="_css-table-ele css-scroll _css-customstyle _css-qualitystyle"
						:row-class-name="tableRowClassName"
						@selection-change="handleSelectionChange"
						:header-cell-style="{ 'background-color': 'transparent'}"
						:row-height="rowHeight"
					>
						<el-table-column type="index"  class-name="center-text" align="center" label="序号" width="50"></el-table-column>
						<el-table-column type="selection"  class-name="center-text" align="center" width="50"></el-table-column>
						<el-table-column property="DivisionCode" label="分类编码" width="120"></el-table-column>
						<el-table-column property="DivisionName" label="分类名称">
							<template slot-scope="scope">
								<el-tooltip class="item" v-if="scope.row.DivisionName.length > 12" popper-class="notice-tooltip-width"  effect="dark" :content="scope.row.DivisionName" placement="top-start">
									<div class="overflow-point">{{ scope.row.DivisionName }}</div>
								</el-tooltip>
								<div v-else>{{ scope.row.DivisionName }}</div>
							</template>
						</el-table-column>
						<el-table-column property="DivisionLevelText" label="分类级别" width="200"></el-table-column>
						<el-table-column property="DivisionImportanceText" label="重要性" width="200">
						</el-table-column>
						<el-table-column property="ModifyTime" label="更新时间" width="120">
							<template slot-scope="scope">
								<div class="table-text">
									{{ scope.row.ModifyTime | flttimeshorter }}
								</div>
							</template>
						</el-table-column>
					</el-table>
					<el-pagination
						@size-change="handleSizeChange"
						@current-change="handleCurrentPaginationChange"
						:current-page="pageNum"
						:page-sizes="[20, 50, 100, 200]"
						:page-size="pageSize"
						layout="total, sizes, prev, pager, next, jumper"
						:total="paginationPageLength"
					>
					</el-pagination>
				</div>
			</div>
			<div class="first-dialog" :style="firstDialogStyle()" v-if="firstDialogShow">
				<div class="_css-flow-contextmenu-in" v-if="has_ZZLHF_Edit" @click.stop="DivisionCreate">
					<div class="_css-flow-contextmenu-inicon icon-interface-addnew"></div>
					<div class="_css-flow-contextmenu-intext">新增</div>
				</div>
				<div class="_css-flow-contextmenu-in" v-if="has_ZZLHF_Edit" @click.stop="DivisionImport">
					<div class="_css-flow-contextmenu-inicon icon-interface-cloud-upload"></div>
					<div class="_css-flow-contextmenu-intext">导入</div>
				</div>
				<div class="_css-flow-contextmenu-in" v-if="has_ZZLHF_Edit" @click.stop="DivisionExport">
					<div class="_css-flow-contextmenu-inicon icon-interface-download-fill"></div>
					<div class="_css-flow-contextmenu-intext">导出</div>
				</div>
			</div>
			<div class="first-dialog" :style="treeAddDialogStyle()" v-if="treeAddDialogShow">
				<div class="_css-flow-contextmenu-in" v-if="has_ZZLHF_Edit" @click.stop="DivisionCreateChildren">
					<div class="_css-flow-contextmenu-inicon icon-interface-addnew"></div>
					<div class="_css-flow-contextmenu-intext">新增</div>
				</div>
				<div class="_css-flow-contextmenu-in" v-if="has_ZZLHF_Edit" @click.stop="DivisionEdit">
					<div class="_css-flow-contextmenu-inicon icon-interface-edit"></div>
					<div class="_css-flow-contextmenu-intext">编辑</div>
				</div>
				<div class="_css-flow-contextmenu-in" v-if="has_ZLHF_Delete" @click.stop="DivisionDel">
					<div class="_css-flow-contextmenu-inicon icon-interface-model-delete"></div>
					<div class="_css-flow-contextmenu-intext">删除</div>
				</div>
				<div class="_css-flow-contextmenu-in" v-if="has_ZZLHF_Edit" @click.stop="DivisionExport">
					<div class="_css-flow-contextmenu-inicon icon-interface-download-fill"></div>
					<div class="_css-flow-contextmenu-intext">导出</div>
				</div>
			</div>
		</div>
		<zdialog-function
			:init_title="typeTitle ? '新增分类': '编辑分类'"
			:init_zindex="1003"
			:init_innerWidth="450"
			:init_width="450"
			init_closebtniconfontclass="icon-suggested-close"
			@onclose="status_showedit = false"
			v-if="status_showedit"
		>
			<div
				slot="mainslot"
				class="_css-addingnameinput-ctn"
				@mousedown="_stopPropagation($event)"
			>
				<div class="_css-line _css-line-name">
					<div class="_css-title _css-title-flowname">分类级别：</div>
					<div class="_css-fieldvalue _css-fieldvaluename _css-selectednode">
						<el-select v-model="editDialog.DivisionLevel" placeholder="请选择" :disabled="!typeTitle">
							<el-option
								v-for="item in DivisionLevelOption"
								:key="item.value"
								:label="item.label"
								:value="item.value"
							>
							</el-option>
						</el-select>
					</div>
				</div>
				<div class="_css-line _css-line-name">
					<div class="_css-title _css-title-flowname">分类名称：</div>
					<div class="_css-fieldvalue _css-fieldvaluename _css-selectednode">
						<el-input
							@mousedown="_stopPropagation($event)"
							placeholder="请输入"
							v-model="editDialog.DivisionName">
						</el-input>
					</div>
				</div>
				<div class="_css-line _css-line-name">
					<div class="_css-title _css-title-flowname">分类编码：</div>
					<div class="_css-fieldvalue _css-fieldvaluename _css-selectednode">
						<el-input
							@mousedown="_stopPropagation($event)"
							placeholder="请输入"
							v-model="editDialog.DivisionCode">
						</el-input>
					</div>
				</div>
				<div class="_css-line _css-line-name">
					<div class="_css-title _css-title-flowname">重要性：</div>
					<div class="_css-fieldvalue _css-fieldvaluename">
						<el-select v-model="editDialog.DivisionImportance" placeholder="请选择">
							<el-option
								v-for="item in DivisionImportanceOptions"
								:key="item.value"
								:label="item.label"
								:value="item.value"
							>
							</el-option>
						</el-select>
					</div>
				</div>
			</div>

			<div slot="buttonslot" class="_css-flowAddBtnCtn">
				<zbutton-function
					:init_text="'保存'"
					:init_fontsize="14"
					:debugmode="true"
					:init_height="'40px'"
					:init_width="'120px'"
					@onclick="func_saveedit"
				>
				</zbutton-function>
				<zbutton-function
					:init_text="'取消'"
					:init_color="'rgba(24, 144, 255)'"
					:init_bgcolor="'#fff'"
					:init_fontsize="14"
					:debugmode="true"
					:init_height="'40px'"
					:init_width="'120px'"
					@onclick="func_canceledit"
				>
				</zbutton-function>
			</div>
		</zdialog-function>
		<CompsStepTip3
			:zIndex="1000"
			v-if="ShowingImportTemplate == true"
			width="504"
			:mubanimgclass="mubanimgclass"
			:bc_guid="''"
			@oncancel="ShowingImportTemplate = false"
			@onok="_onimportok"
			title="导入"
		></CompsStepTip3>
	</div>
</template>
<script>
import CompsStepTip3 from "@/components/CompsCommon/CompsStepTip3";
export default {
	components: {
		CompsStepTip3
	},
	name: "qualityDivision",
	data() {
		return {
      parentId:'',
			projectName: '',
			headerTitleText: "", // header显示文字
			organizeId: "",
			tableClose:false,
			proTreeIn: true,
			treeData: [],
			defaultCheckedKeys: [], // 默认选中的节点
			defaultProps: {
				children: "Children",
				label: "DivisionName",
			},
			elTableLoading:false,
			tableData: [],
			pageNum: 1, // 第几页
			pageSize: 20, // 每页多少条
			paginationPageLength: 0, // 总条数
			rowHeight: 40,
			first_contextpos2: {
				top: 0,
				left: 0,
			},
			firstDialogShow: false,
			has_ZLHF_Delete: false,
			has_ZZLHF_Edit: false,
			typeTitle: true,
			editDialog: {
				DivisionLevel: 0,
				DivisionName: '',
				DivisionCode:  '',
				DivisionImportance: 0,
				ParentId: '0',
			},
			DivisionImportanceOptions:[
				{
					value: 0,
					label: '一般'
				},
				{
					value: 1,
					label: '重要'
				}
			],
			DivisionLevelOption: [
				// {
				// 	value: 0,
				// 	label: '合同工程'
				// },
				// {
				// 	value: 1,
				// 	label: '单位工程'
				// },
				// {
				// 	value: 2,
				// 	label: '子单位工程'
				// },
				// {
				// 	value: 3,
				// 	label: '分部工程'
				// },
				// {
				// 	value: 4,
				// 	label: '子分部工程'
				// },
				// {
				// 	value: 5,
				// 	label: '分项工程'
				// },
				// {
				// 	value: 6,
				// 	label: '单元工程'
				// },
				// {
				// 	value: 7,
				// 	label: '检验批'
				// },
			],
			status_showedit: false,
			treeAddDialog: false,
			treeAddDialogPos: {
				top: 0,
				left: 0,
			},
			treeAddDialogShow: false,
			selectTreeData: {},
			ShowingImportTemplate: false,
			mubanimgclass: 'divisionBg',
			searchTableName: '',
			inputVal: '',
			tableTopBarState: {//表格数据选中后 出现的菜单相关数据
				isIndeterminate: false,//表格表头全选框状态
				checkAll: false,
			},
        	multipleSelection: [],//已选择的数据
			delType: '',

		};
	},
	props: {},
	watch: {},
	created() {
		this.organizeId = this.$staticmethod._Get("organizeId");
		this.headerTitleText = this.$staticmethod._Get("menuText") || "";
		this.has_ZLHF_Delete = this.$staticmethod.hasSomeAuth("ZLHF_Delete");
		this.has_ZZLHF_Edit = this.$staticmethod.hasSomeAuth("ZLHF_Edit");
		this.getProjectInformation()
	},
	mounted() {
	},
	filters: {
		flttimeshorter(inputtime) {
			return inputtime.substr(0, 10) || '-';
		},
	},
	computed: {},
	methods: {
    // 递归查找父级对象的函数
     findParentObject(array, targetId, parentId = null, idKey = 'Id', childrenKey = 'Children') {
       for (const item of array) {
        // 如果找到目标对象
         if (item[idKey] === targetId) {
      // 返回父级对象的 id
      return parentId;
    }
    // 如果当前对象有子节点,递归查找
    if (item[childrenKey] && item[childrenKey].length > 0) {
      const result = this.findParentObject(item[childrenKey], targetId, item[idKey], idKey, childrenKey);
      if (result !== null) {
        return result;
      }
    }
  }
  return null;
},
		// 获取详情
        async getProjectInformation(){
            let params = {
                projectid: this.organizeId,
            }
            const res = await this.$api.GetProject(params);
            this.projectName = res.Data.ProjectName;
			this.getTreeData(1);
        },
		_stopPropagation(ev) {
			ev && ev.stopPropagation && ev.stopPropagation();
		},
		treefunc_current_change(data){
			this.closeAll()
			this.inputVal = this.searchTableName = ''
			this.selectTreeData = data
			this.GetDivisionPaged()
		},
		// 获取树结构
		async getTreeData(type) {
			const checkedKeys = this.$refs.tree.getCurrentKey();
			console.log(checkedKeys,'=====checkedKeys')
			let data = {
				organizeId: this.organizeId,
			};
			const res = await this.$api.GetDivisionTree(data);
			this.treeData = [{
				Id: '0',
				DivisionName: this.projectName,
				DivisionCode: '0',
				ChildrenCount: 1,
				DivisionLevel: -1,
				Children: res.Data
			}]
			if(res.Ret == 1 && res.Data){
				if(type == 1){
					this.selectTreeData = this.treeData[0];
          await this.GetDivisionPaged()
        }else if (type == 2){
          await this.GetDivisionPaged(true)
          this.$nextTick(() => {
            this.$refs.tree.setCurrentKey(this.parentId);
          });
        }
			}else {
        this.tableData = []
      }
		},

		// 新增
		async postDivisionCreate() {
			if(!this.editDialog.DivisionName){
				this.$message.warning('请输入分类名称')
				return
			}
			if(!this.editDialog.DivisionCode){
				this.$message.warning('请输入分类编码')
				return
			}
			let data = {
				OrganizeId: this.organizeId,
				ParentId: this.editDialog.ParentId,
				DivisionName: this.editDialog.DivisionName,
				DivisionCode: this.editDialog.DivisionCode,
				DivisionLevel: this.editDialog.DivisionLevel,
				DivisionImportance: this.editDialog.DivisionImportance,
			};
			const res = await this.$api.postDivisionCreate(data);
			if(res.Ret == 1){
				this.$message.success('新增成功')
				this.getTreeData();
				this.GetDivisionPaged()
				this.closeAll()
				this.func_canceledit()
			}
		},
		// 编辑
		async postDivisionModify() {
			if(!this.editDialog.DivisionName){
				this.$message.warning('请输入分类名称')
				return
			}
			if(!this.editDialog.DivisionCode){
				this.$message.warning('请输入分类编码')
				return
			}
			let data = {
				Id: this.selectTreeData.Id,
				DivisionName: this.editDialog.DivisionName,
				DivisionCode: this.editDialog.DivisionCode,
				Importance: this.editDialog.DivisionImportance
			};
			const res = await this.$api.postDivisionModify(data);
			if(res.Ret == 1){
				this.$message.success('编辑成功')
				this.selectTreeData.DivisionName = this.editDialog.DivisionName
				this.getTreeData();
				this.GetDivisionPaged()
				this.closeAll()
				this.func_canceledit()
			}
		},
		// 获取某个详情
		async GetDivisionInfo(id) {
			if(id == 0) return
			// if( this.delType == 'selectDel'){
			// 	this.tableData = [];
			// 	this.paginationPageLength = 0
			// 	this.selectTreeData.DivisionName = ''
			// 	return
			// }
			let data = {
				id: id
			};
			const res = await this.$api.GetDivisionInfo(data);
			if(res.Ret == 1){
				this.tableData = [res.Data];
				this.paginationPageLength = 1
			}else{
				this.tableData = [];
				this.paginationPageLength = 0
				this.selectTreeData.DivisionName = ''
			}
		},
		// 获取表格
		async GetDivisionPaged(isDelete = false) {
       let ParentId =''
       if (isDelete) {
         ParentId = this.parentId
       }else {
         ParentId = this.selectTreeData.Id
       }
			let data = {
				OrganizeId: this.organizeId,
				ParentId,
				PageNum: this.pageNum,
				PageSize: this.pageSize,
				KeyWord: this.searchTableName,
			};
			const res = await this.$api.GetDivisionPaged(data);
			if(res.Ret == 1){
				// 如果当前表格返回没有子集，表格显示当前数据
				if(res.Data.Total > 0){
					this.tableData = res.Data.Data;
					this.paginationPageLength = res.Data.Total
				}else{
					await this.GetDivisionInfo(ParentId)
				}
			}
		},
		tableRowClassName({ row, rowIndex }) {
			return "css-tdunder-test1";
		},

		handleSelectionChange(val){
			this.multipleSelection = val;
			//设置第二表头的全选框状态
			let checkedCount = val.length;
			this.tableTopBarState.checkAll = checkedCount === this.tableData.length;
			this.tableTopBarState.isIndeterminate = checkedCount > 0 && checkedCount < this.tableData.length;
		},
		showAddFirstLeave(ev){
			this.closeAll()
			this.first_contextpos2.left = ev.clientX - 190;
			this.first_contextpos2.top = ev.clientY - 30;
			this.firstDialogShow = true
			this.typeTitle = true
		},
		firstDialogStyle(){
			let _s = {};
			_s["left"] = this.first_contextpos2.left + 'px';
			_s["top"] = this.first_contextpos2.top + 'px';
			return _s;
		},
		treeAddDialogStyle(){
			let _s = {};
			_s["left"] = this.treeAddDialogPos.left + 'px';
			_s["top"] = this.treeAddDialogPos.top + 'px';
			return _s;
		},
		closeAll(){
			this.firstDialogShow = this.treeAddDialogShow = false
      this.ShowingImportTemplate = false
		},
		chooseCloseOrOpen(){
			this.proTreeIn = !this.proTreeIn;
		},
		DivisionCreate(){
			this.DivisionLevelOption = [
				{
					value: 0,
					label: '合同工程'
				}
			]
			this.editDialog.DivisionName = ''
			this.editDialog.DivisionCode = ''
			this.editDialog.DivisionLevel = 0;
			this.editDialog.ParentId = '0';
			this.editDialog.DivisionImportance = 0
			this.status_showedit = true
			this.firstDialogShow = false;
		},
		// 导入
		DivisionImport(){
			this.ShowingImportTemplate = true;
		},
		// 导出
		DivisionExport(){
			let src = `${window.bim_config.webserverurl}/api/Material/Division/Export?token=${this.$staticmethod.Get('Token')}&OrganizeId=${this.organizeId}&ParentId=${this.selectTreeData.Id}`
			window.location.href = src
		},
		func_saveedit(){
			if(this.typeTitle){
				this.postDivisionCreate()
			}else{
				this.postDivisionModify()
			}
		},
		func_canceledit(){
			this.status_showedit = false;
			this.editDialog.DivisionName = ''
			this.editDialog.DivisionCode = ''
			this.closeAll()
		},
		func_tree_showmenu(ev,data){
			if(data.Id == '0'){
				if(!this.has_ZZLHF_Edit){
					this.$message.error('没有操作权限')
					return
				}
				this.first_contextpos2.left = ev.clientX - 190;
				this.first_contextpos2.top = ev.clientY - 30;
				this.firstDialogShow = true
				this.typeTitle = true
			}else{
				this.closeAll()
				this.treeAddDialogPos.top = ev.clientY - 30;
				this.treeAddDialogPos.left = ev.clientX - 190;

				if (this.treeAddDialogPos.top > document.body.clientHeight - 240) {
					this.treeAddDialogPos.top = document.body.clientHeight - 240;
				}

				this.treeAddDialogShow = true
				this.selectTreeData = data;
			}
		},
		DivisionCreateChildren(){
			this.typeTitle = true
			this.editDialog.ParentId = this.selectTreeData.Id;
			this.editDialog.DivisionImportance = 0
			this.editDialog.DivisionName = ''
			this.editDialog.DivisionCode = ''

			if(this.selectTreeData.DivisionLevel == -1){
				this.DivisionLevelOption = [
					{
						value: 0,
						label: '合同工程'
					}
				]
				this.editDialog.DivisionLevel = 0;
			}else if(this.selectTreeData.DivisionLevel == 0){
				this.DivisionLevelOption = [
					{
						value: 1,
						label: '单位工程'
					}
				]
				this.editDialog.DivisionLevel = 1;
			}else if(this.selectTreeData.DivisionLevel == 1){
				this.DivisionLevelOption = [
					{
						value: 2,
						label: '子单位工程'
					},
					{
						value: 3,
						label: '分部工程'
					},
				]
				this.editDialog.DivisionLevel = 2;
			}else if(this.selectTreeData.DivisionLevel == 2){
				this.DivisionLevelOption = [
					{
						value: 3,
						label: '分部工程'
					},
				]
				this.editDialog.DivisionLevel = 3;
			}else if(this.selectTreeData.DivisionLevel == 3){
				this.DivisionLevelOption = [
					{
						value: 4,
						label: '子分部工程'
					},
					{
						value: 5,
						label: '分项工程'
					},
					{
						value: 6,
						label: '单元工程'
					},
				]
				this.editDialog.DivisionLevel = 4;
			}else if(this.selectTreeData.DivisionLevel == 4){
				this.DivisionLevelOption = [
					{
						value: 5,
						label: '分项工程'
					},
					{
						value: 6,
						label: '单元工程'
					},
				]
				this.editDialog.DivisionLevel = 5;
			}else if(this.selectTreeData.DivisionLevel == 5 || this.selectTreeData.DivisionLevel == 6){
				this.DivisionLevelOption = [
					{
						value: 7,
						label: '检验批'
					},
				]
				this.editDialog.DivisionLevel = 7;
			}else{
				this.$message.error('当前层级无法创建子项数据')
				return
			}
			this.status_showedit = true
			this.treeAddDialogShow = false;
		},
		DivisionEdit(){
			this.typeTitle = false,
			this.editDialog = {
				DivisionLevel: this.selectTreeData.DivisionLevel,
				DivisionName:  this.selectTreeData.DivisionName,
				DivisionCode:  this.selectTreeData.DivisionCode,
				DivisionImportance:  this.selectTreeData.DivisionImportance,
				ParentId: this.selectTreeData.ParentId,
			}
			if(this.selectTreeData.DivisionLevel == 0){
				this.DivisionLevelOption = [
					{
						value: 0,
						label: '合同工程'
					}
				]
				this.editDialog.DivisionLevel = 0;
			}else if(this.selectTreeData.DivisionLevel == 1){
				this.DivisionLevelOption = [
					{
						value: 1,
						label: '单位工程'
					}
				]
				this.editDialog.DivisionLevel = 1;
			}else if(this.selectTreeData.DivisionLevel == 2){
				this.DivisionLevelOption = [
					{
						value: 2,
						label: '子单位工程'
					}
				]
				this.editDialog.DivisionLevel = 2;
			}else if(this.selectTreeData.DivisionLevel == 3){
				this.DivisionLevelOption = [

					{
						value: 3,
						label: '分部工程'
					},
				]
				this.editDialog.DivisionLevel = 3;
			}else if(this.selectTreeData.DivisionLevel == 4){
				this.DivisionLevelOption = [
					{
						value: 4,
						label: '子分部工程'
					},
				]
				this.editDialog.DivisionLevel = 4;
			}else if(this.selectTreeData.DivisionLevel == 5){
				this.DivisionLevelOption = [
					{
						value: 5,
						label: '分项工程'
					},

				]
				this.editDialog.DivisionLevel = 5;

			}else if(this.selectTreeData.DivisionLevel == 6){
				this.DivisionLevelOption = [

					{
						value: 6,
						label: '单元工程'
					},
				]
				this.editDialog.DivisionLevel = 6;
			}else if(this.selectTreeData.DivisionLevel == 7){
				this.DivisionLevelOption = [
					{
						value: 7,
						label: '检验批'
					},
				]
				this.editDialog.DivisionLevel = 7;
			}
			this.status_showedit = true
			this.treeAddDialogShow = false
		},
		DivisionDel(){
			this
				.$confirm("确定删除选定的数据？", "操作确认", {
					confirmButtonText: "确认",
					cancelButtonText: "取消",
					type: "warning"
				})
			.then(x => {
				this.delType = 'treeDel'
				this.delFun([this.selectTreeData.Id]);
			})
			.catch(x => {});
		},
		async delFun(delData){
			if(!this.has_ZLHF_Delete){
				this.$message.warning('没有操作权限')
				return
			}
			const res = await this.$api.postDivisionDelete(delData)
			if(res.Ret == 1){
        this.$message.success('删除成功')
        this.parentId = this.findParentObject(this.treeData,this.selectTreeData.Id)
        this.pageNum = 1
				await this.getTreeData(2)
				this.closeAll()
			}

		},
		handleSizeChange(val) {
			this.pageSize = val;
			this.GetDivisionPaged();
		},
		handleCurrentPaginationChange(val) {
			this.pageNum = val;
			this.GetDivisionPaged();
		},
		_onimportok(){
      this.$message.success('导入成功')
			this.closeAll();
			this.getTreeData();
		},
		searchName(val) {
			this.searchTableName = val;
			this.GetDivisionPaged();
		},

		deleteSelectQualityItems(){
			this
				.$confirm("确定删除选定的数据？", "操作确认", {
					confirmButtonText: "确认",
					cancelButtonText: "取消",
					type: "warning"
				})
			.then(x => {
				let id = this.multipleSelection.map((obj) => {
					return obj.Id;
				});
				this.delType = 'selectDel'

				this.delFun(id);
			})
			.catch(x => {});
		},

		handleTableTopBarCheckAllChange(val){
			this.multipleSelection = val ? this.tableData : [];
			this.tableTopBarState.isIndeterminate = false;
			this.$refs.multipleTable.toggleAllSelection();
		},
		deleteAndAdjustPage(data, pageSize, currentPage) {
			if (data === 0) {
				return 1;
			} else {
				var totalPage = Math.ceil(data / pageSize); // 计算总页数
				if (currentPage > totalPage) {
					return totalPage; // 如果当前页超出了总页数，返回最后一页
				} else {
					return currentPage; // 否则返回当前页
				}
			}
		}
	},
};
</script>
<style lang="scss" scoped>
@import url("../../../assets/css/MaterialsMgrStyle.css");

.pro-in-out {
	display: inline-block;
	width: 16px;
	height: 120px;
	background-image: url(../../../assets/images/p-in.png);
	background-size: 100%;
	background-repeat: no-repeat;
}
.pro-in-out.p-out {
	background-image: url(../../../assets/images/p-out.png);
	background-repeat: no-repeat;
	background-size: 100%;
	left: 0;
}
._css-bottomarea{
	position: relative;
	display: flex;
	.left-content{
		background: #fff;
		min-width: 270px;
		max-width: 370px;
	}
	.content-right{
		margin: 0 12px;
		// padding: 0 12px;
		flex: 1;
		width: calc(100% - 364px);
	}
	.content-right.width100{
		width: calc(100% - 64px);
	}
	._css-materialtypetab-body{
		position: relative;
		height: calc(100% - 64px);
	}
}
.first-dialog{
	position: absolute;
	background-color: #fff;
	width: 96px;
	box-shadow: 0px 2px 4px 0px rgba(0,38,77,0.15);
	border-radius: 2px;
	z-index: 5;
}
._css-line {
	padding: 0 24px;
	box-sizing: border-box;
	margin: 8px 0 0 0;
	display: flex;
	align-items: center;
}
._css-fieldvaluename {
	flex: 1;
	height: 36px;
	display: flex;
	align-items: center;
	border: 1px solid rgba(0, 0, 0, 0.1);
	border-radius: 4px;
	box-sizing: border-box;
}
._css-fieldvaluename /deep/ .el-input__inner {
	line-height: 34px;
	height: 34px;
}
._css-fieldvaluename /deep/ .el-date-editor.el-input,
._css-fieldvaluename /deep/ .el-date-editor.el-input__inner {
	width: 100%;
}
._css-title-flowname {
	width: 20%;
	text-align: left;
}
._css-flowAddBtnCtn {
	display: flex;
	flex-direction: row-reverse;
	height: 64px;
	align-items: center;
	box-sizing: border-box;
	padding-right: 8px;
}
._css-materialtypetab-body /deep/ ._css-qualitystyle.el-table th {
	font-size: 14px !important;
}
.overflow-point{
	width: 200px;
}
._css-customstyle{
	height: 90% !important;
	overflow: auto;
}
.right-input{
	width:230px;
	border: 1px solid #D8D8D8;
	position: absolute;
	top: 16px;
    right: 8px;
}
.right-input i {
    position: absolute;
    top: 6px;
    right: 8px;
    color: #999999;
}
.right-input /deep/ .el-input__inner{
    line-height: 32px;
}
._css-materialtypetab-body /deep/ ._css-qualitystyle.el-table thead th .cell , ._css-materialtypetab-body /deep/ ._css-qualitystyle .center-text .cell{
	justify-content: center !important;
	padding: 0;
}
.table-top-bar {
    position: absolute;
    top: 0;
    left: 0;
    height: 43px;
    width: 100%;
    background-color: #FFFFFF;
    z-index: 10;
    text-align: left;
    padding-left: 67px;
    box-sizing: border-box;
}
.table-top-bar .menu-item {
    display: flex;
    align-items: center;
    font-weight: 500;
    color: rgba(0,0,0,0.45);
}
.table-top-bar .menu-item li {
    cursor: pointer;
}

.table-top-bar .menu-item.menu-left li {
    margin-right: 25px;
}

.table-top-bar .menu-item.menu-right li {
    margin-left: 25px;
}

.table-top-bar .menu-item li .icon {
    vertical-align: text-bottom;
	margin-right: 5px;
}

.table-top-bar .menu-item li .desc {
    color: rgba(0,0,0,0.25);
}

.table-top-bar .menu-item li:hover {
    opacity: 0.8;
}

</style>
