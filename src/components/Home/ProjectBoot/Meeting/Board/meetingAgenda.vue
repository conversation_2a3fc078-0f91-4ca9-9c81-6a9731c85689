<template>
  <div class="agenda-wrap">
    <div class="title-wrap">
      <span class="title">决议概述</span>

      <el-button @click="submitAgenda()" class="btn save-btn" v-if="!hideSubmit">提交</el-button>
    </div>
    <div class="title2 mg-t8 pd-l24">
      概述内容
    </div>
    <div class="textarea-box mg-t8 pd-l24">
      <textarea class="input-textarea" v-model="content"></textarea>
    </div>
    <!-- <div class="title2 mg-t16 pd-l24">
      关联文档
    </div>
    <div class="mg-t8 desc pd-l24">
      暂无关联文档
    </div> -->
    <div class="flex-box spance-between align-center list-title">
      <div class="title">
        议程列表
      </div>
      <div class="flex-box">
        <div class="icon add-icon" @click="addItem" v-if="!hideSubmit"></div>
        <!-- <div class="icon reduce-icon" @click="deleteAll"></div> -->
      </div>
    </div>

    <ul class="table-wrap">
      <li class="table-header table-item">
        <!-- <div class="checkbox-box">
          <el-checkbox :value="(dataList.length === checkedList.length)" @change="checkAllData"></el-checkbox>
        </div> -->
        <div class="table-td">序号</div>
        <div class="table-td">标题</div>
        <div class="table-td">执行人</div>
        <div class="table-td">监察人</div>
        <div class="table-td">开始时间</div>
        <div class="table-td">结束时间</div>
        <div class="table-td handle-box"></div>
      </li>
      <li class="table-item" v-for="(item, index) in dataList" :key="item.listId">
        <!-- <div class="checkbox-box">
          <el-checkbox :value="isChecked(item)" @change="checkRow(item, index)"></el-checkbox>
        </div> -->
        <div class="table-td">
          <div v-if="(currentData.listId === item.listId)">
            <input type="text" v-model="item.Sort" class="title-input" @blur="item.Sort=Number(item.Sort)" />
          </div>
          <div v-else>
            {{item.Sort}}
          </div>
        </div>
        <div class="table-td">
          <div v-if="(currentData.listId === item.listId)">
            <input type="text" v-model="item.Title" class="title-input" />
          </div>
          <div v-else>
            {{item.Title}}
          </div>
        </div>
        <div class="table-td">
          <div v-if="(currentData.listId === item.listId)">
            <bim-input
              :dataList="memberData"
              :multiple="false"
              optionValue="UserId"
              optionLabel="RealName"
              :value="item.Executor"
              @updateData="updateCharge"
            />
          </div>
          <div v-else>
            {{(memberObj[item.Executor] || {}).RealName || '/'}}
          </div>
        </div>
        <div class="table-td">
          <div v-if="(currentData.listId === item.listId)">
            <bim-input
              :dataList="memberData"
              optionValue="UserId"
              optionLabel="RealName"
              :value="item.Examiner"
              @updateData="updateMember"
            />
          </div>
          <div v-else>
            {{forMateData(item.Examiner)}}
          </div>
        </div>
        <div class="table-td">
          <div v-if="(currentData.listId === item.listId)">
            <el-date-picker
              v-model="item.StartTime"
              type="date"
              size="small"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
              @change="changeTime"
              placeholder="选择日期">
            </el-date-picker>
          </div>
          <div v-else>
            {{item.StartTime}}
          </div>
        </div>
        <div class="table-td">
          <div v-if="(currentData.listId === item.listId)">
            <el-date-picker
              v-model="item.EndTime"
              type="date"
              size="small"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
              @change="changeTime1"
              placeholder="选择日期">
            </el-date-picker>
          </div>
          <div v-else>
            {{item.EndTime}}
          </div>
        </div>
        <div class="table-td flex-box handle-box">
          <template v-if="!hideSubmit">
            <template v-if="(currentData.listId !== item.listId)">
              <span class="modify" @click="modify(item, index)">
                修改
              </span>
              <span class="modify" @click="deleteRow(item, index)">
                删除
              </span>
            </template>
            <template v-else>
              <span class="modify" @click="save(item, index)">
                保存
              </span>
              <span class="modify" @click="cancle(item, index)">
                取消
              </span>
            </template>
          </template>
        </div>
      </li>
    </ul>
  </div>
</template>

<script>
import BimInput from '../component/bim-input'
export default {
  name: '',
  props: {
    memberData: {
      type: Array,
      default: () => {
        return []
      }
    },
    mettingId: String,
    memberObj: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },

  data () {
    return {
      checked: false,
      value1: new Date(),
      dataList: [
      ],
      checkedList: [],
      currentData: {},
      oldRowData: {},
      // memberObj: {},
      modyfyCharge: '',
      modyfyMember: [],
      content: '',
      modifyTime: '',
      modifyTime1: '',
      hideSubmit: false
    }
  },
  components: {
    BimInput
  },
  created () {
    // this.memberData.forEach(v => {
    //   this.memberObj[v.UserId] = v
    // })
    this.getListData()
  },
  mounted () {
    
  },

  methods: {
    submitAgenda () {
      this.dataList.map(v => {
        if (typeof v.Examiner != 'string') {
          v.Examiner = v.Examiner.join(',')
        }
        return v
      })
      this.$axios.post(window.bim_config.webserverurl+this.$urlPool.MeetingResulotion, {
        MeetingId: this.mettingId,
        Content: this.content,
        Detail: this.dataList,
        Submit: true,
        Token: this.$staticmethod.Get("Token")
      }).then(res => {
        if (res.data.Ret == 1) {
          this.$message.success('提交成功')
          this.content = ''
          this.getListData()
        } else {
          this.$message.error(res.data.Msg)
          this.content = ''
        }
      })
    },
    getListData () {
      this.$axios.get(window.bim_config.webserverurl+this.$urlPool.MeetingResulotion, {
        params: {
          MeetingId: this.mettingId,
          Token: this.$staticmethod.Get("Token")
        }
      }).then(res => {
        if (res.data.Ret == 1) {
          this.dataList = res.data.Data.Detail
          this.content = res.data.Data.Content
          if (res.data.Data.Detail.length != 0 || res.data.Data.Content) {
            this.hideSubmit = true
          }
          this.dataList.map((v, i) => {
            v.listId = i
            if (typeof v.Examiner == 'string') {
              v.Examiner = v.Examiner.split(',')
            }
            return v
          })
        }
      })
    },
    addItem () {
      this.dataList.push({
        "Title": "",
        "Executor": "",
        "Examiner": "",
        "StartTime": "",
        "EndTime": "",
        "Sort": '',
        'listId': this.dataList.length
      })
      this.currentData = {
        "Title": "",
        "Executor": "",
        "Examiner": "",
        "StartTime": "",
        "EndTime": "",
        "Sort": '',
        'listId': this.dataList.length - 1
      }
    },
    deleteAll () {
      console.log(this.checkedList)
    },
    forMateData (data) {
      let arr = []
      data.forEach(v => {
        if (this.memberObj[v]) {
          arr.push(this.memberObj[v].RealName)
        }
      })
      return arr.join('、')
    },
    checkAllData () {
      if (this.dataList.length === this.checkedList.length) {
        this.checkedList = []
      } else {
        this.checkedList = []
        this.dataList.forEach(v => {
          this.checkedList.push(v.id)
        })
      }
    },
    checkRow (data, index) {
      if (this.checkedList.indexOf(data.id) > -1) {
        this.checkedList.splice(this.checkedList.indexOf(data.id), 1)
      } else {
        this.checkedList.push(data.id)
      }
    },
    isChecked (data) {
      return this.checkedList.includes(data.id)
    },
    checkAll () {
      console.log(321)
    },
    updateMember (data) {
      this.modyfyMember = data
    },
    updateCharge (data) {
      this.modyfyCharge = data
    },
    changeTime (time) {
      this.modifyTime = time
    },
    changeTime1 (time) {
      this.modifyTime1 = time
    },
    modify (data) {
      this.currentData = data
      this.modyfyCharge = data.Executor
      this.modyfyMember = data.Examiner
      this.modifyTime = data.StartTime
      this.modifyTime1 = data.EndTime
      this.oldRowData = data
    },
    save (data, index) {
      this.dataList[index].Executor = this.modyfyCharge
      this.dataList[index].Examiner = this.modyfyMember
      this.dataList[index].StartTime = this.modifyTime
      this.dataList[index].EndTime = this.modifyTime1
      this.currentData = {}
    },
    deleteRow (data, index) {
      this.dataList.splice(index, 1)
    },
    cancle (data, index) {
      if (data.id === '') {
        this.dataList.splice(index, 1)
      } else {
        this.currentData = {}
        this.modyfyCharge = ''
        this.modyfyMember = []
        this.modifyTime = ''
        this.modifyTime1 = ''
        this.dataList[index] = this.oldRowData
      }
      
    }
  }
}
</script>
<style lang="less">
.agenda-wrap {
  .el-date-editor.el-input {
    width: 100%!important;
  }
}
</style>
<style lang='scss' scoped>
.agenda-wrap {
  text-align: left;
  .title-wrap {
    display: flex;
    justify-content: space-between;
    padding: 24px;
    .title {
      padding: 0;
      color: #222;
      font-size: 16px;
      font-weight: 500;
    }
    .btn {
      border-color: #007AFF;
      font-size: 14px;
      padding: 10px 24px;
      border-radius: 4px;
      background: #007AFF;
      color: #fff!important;
    }
  }
  .title {
    padding: 24px;
    color: #222;
    font-size: 16px;
    font-weight: 500;
  }
  .mg-t8 {
    margin-top: 8px;
  }
  .mg-t16 {
    margin-top: 16px;
  }
  .pd-l24 {
    padding-left: 24px;
  }
  .title2 {
    color: #222;
    font-size: 14px;
  }
  .desc {
    color: #999999;
    font-size: 12px;
  }
  .input-textarea {
    width: 400px;
    height: 80px;
  }
  .flex-box {
    display: flex;
    &.spance-between {
      justify-content: space-between;
    }
    &.align-center {
      align-items: center;
    }
  }
  .list-title {
    margin-top: 40px;
    margin-bottom: 20px;
    box-sizing: border-box;
    padding-right: 24px;
    .icon {
      width: 20px;
      height: 20px;
      margin-left: 8px;
      &.add-icon {
        background: url('../../../../../assets/images/add-icon.png');
        background-size: 100% 100%;
      }
      &.reduce-icon {
        background: url('../../../../../assets/images/reduce-icon.png');
        background-size: 100% 100%;
      }
    }
  }


  .table-wrap {
    box-sizing: border-box;
    .title-input {
      width: 100%;
      height: 30px;
    }
    .table-header {
      background: #F5F5F5;
      .table-td {
        color: #333;
        font-size: 14px;
        font-weight: 500;
      }
    }
    .table-item {
      display: flex;
      justify-content: space-between;
      padding: 20px 0 20px 20px;
      border-bottom: 1px solid rgba($color: #000000, $alpha: .1);
      .checkbox-box {
        display: flex;
        justify-content: center;
        margin-right: 20px;
      }
      .table-td {
        padding-right: 24px;
        box-sizing: border-box;
        flex: 1;
        min-width: 150px;
        font-size: 12px;
        &:nth-child(2) {
          flex: 2;
        }
        &.handle-box {
          min-width: 100px;
          align-items: center;
          padding-left: 16px;
          box-sizing: border-box;
        }
        .modify {
          color: #007AFF;
          font-size: 12px;
          margin-right: 8px;
          cursor: pointer;
        }
      }
    }
  }
}
</style>
