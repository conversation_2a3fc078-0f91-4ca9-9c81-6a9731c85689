<template>
  <div>
    <div class="meeting-sigin-wrap" v-if="meetingStatus != 2 && meetingStatus != 3">
      <div class="title">
        基本信息
      </div>
      <div class="main">
        <div class="flex-box form-item">
          <div class="item">
            <div class="label">
              允许签到时间
            </div>
            <div class="time-box mg-t8">
              <span>会议开始前</span>
              <div class="time-input">
                <el-input
                  placeholder="请输入内容"
                  size="small"
                  @blur="form.SignInMinutes=Number(form.SignInMinutes)"
                  v-model="form.SignInMinutes">
                  <i
                    class="input-icon"
                    slot="suffix">
                    分钟
                  </i>
                </el-input>
              </div>
              <span>可以签到</span>
            </div>
          </div>
          <div class="item mg-l60">
            <div class="label">
              启用会议签退
            </div>
            <div
              class="mg-t8">
              <el-switch
                v-model="form.SignOut">
              </el-switch>
            </div>
            
          </div>
        </div>

        <div class="flex-box form-item mg-b40">
          <div class="item">
            <div class="label">
              允许签退时间
            </div>
            <div class="time-box mg-t8">
              <span>会议签到</span>
              <div class="time-input">
                <el-input
                  placeholder="请输入内容"
                  size="small"
                  @blur="form.SignInOut=Number(form.SignInOut)"
                  v-model="form.SignInOut">
                  <i
                    class="input-icon"
                    slot="suffix">
                    分钟
                  </i>
                </el-input>
              </div>
              <span>后才能签退</span>
            </div>

            <div class="time-box mg-t8">
              <span>会议结束</span>
              <div class="time-input">
                <el-input
                  placeholder="请输入内容"
                  size="small"
                  @blur="form.OverSignOut=Number(form.OverSignOut)"
                  v-model="form.OverSignOut">
                  <i
                    class="input-icon"
                    slot="suffix">
                    分钟
                  </i>
                </el-input>
              </div>
              <span>后不允许签退</span>
            </div>

          </div>
        </div>

        <div class="title">
          屏幕签到设置
        </div>

        <div class="flex-box form-item">
          <div class="item">
            <div class="label">背景颜色</div>
            <!-- <input type="text" class="input-box" v-model="form.BackgroundColor" disabled /> -->
            <!-- 颜色块区域 -->
            <div class="_css-color-list">
              <div
                @click="selectColor('BackgroundColor', cor)"
                v-for="cor in colorarr"
                :key="cor"
                class="_css-color-item"
                :style="getColorItemStyle(cor)"
              >
                <div
                  class="_css-color-item-in"
                  :class="{'icon-checkbox-Selected-Disabled-dis': form.BackgroundColor == cor}"
                ></div>
              </div>
            </div>
          </div>
          <div class="item mg-l60">
            <div class="label">显示角色</div>
            <el-switch
              class="mg-t8"
              v-model="form.RoleDisplay">
            </el-switch>
          </div>
        </div>
        <div class="flex-box form-item">
          <div class="item">
            <div class="label">字体颜色</div>
            <!-- <input type="text" class="input-box" v-model="form.FontColor" /> -->
            <!-- 颜色块区域 -->
            <div class="_css-color-list">
              <div
                @click="selectColor('FontColor', cor)"
                v-for="cor in colorarr"
                :key="cor"
                class="_css-color-item"
                :style="getColorItemStyle(cor)"
              >
                <div
                  class="_css-color-item-in"
                  :class="{'icon-checkbox-Selected-Disabled-dis': form.FontColor == cor}"
                ></div>
              </div>
            </div>
          </div>
          <div class="item mg-l60">
            <div class="label">显示签到统计</div>
            <el-switch
              class="mg-t8"
              v-model="form.ShowSignIn">
            </el-switch>
          </div>
        </div>
        <div class="flex-box form-item">
          <div class="item">
            <div class="label">字体大小</div>
            <input type="text" class="input-box" v-model="form.FontSize" @blur="form.FontSize=Number(form.FontSize)" />
          </div>
          <div class="item mg-l60">
            <div class="label">显示签退统计</div>
            <el-switch
              class="mg-t8"
              v-model="form.ShowSignOut">
            </el-switch>
          </div>
        </div>

      </div>
    </div>
    <div class="meeting-qrcode" v-if="meetingStatus == 2 || meetingStatus == 3">
      <div class="title">
        会议二维码
      </div>
      <div class="flex-box align-center mg-t16">
        <div class="qrcode-box" ref="qrCodeUrl" @click="openDialog"></div>
        <ul class="sign-info">
        <li class="item">
          使用移动端APP【扫一扫】功能，进行会议
        </li>
        <li class="item">
          签到情况：应签到 <span>{{ (signInfo.MeetingInfo || {}).ShouldArriveNumber }}</span> 人，已签到 <span>{{ signInfo.SignedPeopleNumber }}</span>人， 未签到 <span>{{ signInfo.NotSingedPeopleNumber }}</span> 人
        </li>
        <li class="item">
          签退情况：应签到 <span>{{ (signInfo.MeetingInfo || {}).ShouldArriveNumber }}</span> 人，已签到 <span>{{ signInfo.SignOutPeopleNumber }}</span>人， 未签到 <span>{{ signInfo.NotSignOutPeopleNumber }}</span> 人
        </li>
        </ul>
      </div>
      <div class="title mg-t16">
        签到/签退记录
      </div>
      <div class="table-wrap mg-t16">
        <el-table
          :data="tableData"
          style="width: 100%">
          <el-table-column
            label="签到人"
            >
            <template slot-scope="scope">{{ scope.row.UserName }}</template>
          </el-table-column>
          <el-table-column
            prop="RoleName"
            label="角色部门"
            >
          </el-table-column>
          <el-table-column
            prop="address"
            label="参会"
            >
            <template slot-scope="scope">{{ scope.row.SignInStatus ? '是' : '否' }}</template>
          </el-table-column>
          <el-table-column
            label="签退状态"
            >
            <template slot-scope="scope">{{ scope.row.SignOutStatus ? '是' : '否' }}</template>
          </el-table-column>
          <el-table-column
            label="签到时间"
            >
            <template slot-scope="scope">{{ scope.row.SignInTime || '--' }}</template>
          </el-table-column>
          <el-table-column
            label="签退时间"
            >
            <template slot-scope="scope">{{ scope.row.SignOutTime || '--' }}</template>
          </el-table-column>
          <!-- <el-table-column
            label="参会说明"
            >
            <template slot-scope="scope">{{ scope.row.date }}</template>
          </el-table-column> -->
        </el-table>
      </div>
    </div>
    <div v-show="isVisible" class="mark" @click="openDialog"></div>
    <div v-show="isVisible" class="dialog-wrap" @click="openDialog">
      <div id="qrcode" class="dialog-img" ref="qrCodeUrl1"></div>
    </div>
  </div>
</template>

<script>
import QRCode from 'qrcodejs2'
export default {
  name: '',
  props: {
    formData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },

  data () {
    return {
      isVisible: false,
      value1: true,
      radio: '',
      tableData: [],
      form: {},
      signInfo: {},
      meetingStatus: '',
      colorarr: [
        "rgba(28, 50, 75, 1)",
        "rgba(86, 98, 112, 1)",
        "rgba(127, 179, 210, 1)",
        "rgba(151, 107, 61, 1)",
        "rgba(184, 158, 123, 1)",
        "rgba(29, 164, 140, 1)",
        "rgba(115, 113, 157, 1)"
      ], // 备选颜色
    }
  },
  watch: {
    form: {
      handler (newVal) {
        this.$emit('changeData', newVal)
      },
      deep: true
    }
  },
  created () {
    this.meetingStatus = this.$route.params.status

  },
  mounted () {
    this.form = this.formData
    const meetingId = this.$route.params.id
    if (meetingId && this.$refs.qrCodeUrl) {
      var qrcode = new QRCode(this.$refs.qrCodeUrl, {
        text: meetingId, // 需要转换为二维码的内容
        width: 120,
        height: 120,
        colorDark: '#000000',
        colorLight: '#ffffff',
        correctLevel: QRCode.CorrectLevel.H
      })
      this.getSiginInfo(meetingId)
    }
  },

  methods: {
    openDialog () {
      this.isVisible = !this.isVisible
      const meetingId = this.$route.params.id
      const ele = document.getElementById('qrcode')
      ele.innerHTML = ''
      if (this.isVisible) {
        var qrcode = new QRCode(this.$refs.qrCodeUrl1, {
        text: meetingId, // 需要转换为二维码的内容
        width: 500,
        height: 500,
        colorDark: '#000000',
        colorLight: '#ffffff',
        correctLevel: QRCode.CorrectLevel.H
      })
      }
    },
    getSiginInfo (id) {
      this.$axios.get(window.bim_config.webserverurl+this.$urlPool.getSigninfo, {
        params: {
          meetingId: id,
          Token: this.$staticmethod.Get("Token")
        }
      }).then(res => {
        if(res.data.Ret == 1){
          this.tableData = res.data.Data.MeetingPersonInfo
        }else{
          this.tableData = []
        }
      })
    },
    selectColor (key, color) {
      this.form[key] = color
    },
    getColorItemStyle(cor) {
      var _this = this;
      var _s = {};
      _s["background-color"] = cor;
      return _s;
    }
  }
}
</script>

<style lang='scss' scoped>
.dialog-wrap {
  position: fixed;
  left: 50%;
  top: 50%;
  margin-left: -250px;
  margin-top: -250px;
  width: 500px;
  height: 500px;
  cursor: pointer;
  .dialog-img {
    width: 100%;
    height: 100%
  }
  z-index: 2;
}
.mark {
  position: fixed;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  background: rgba($color: #000000, $alpha: .4);
  z-index: 1;
}
.meeting-sigin-wrap, .meeting-qrcode {
  text-align: left;
  padding: 24px;
  
  ._css-color-item {
    margin-right: 12px;
  }
  ._css-color-item-in {
  width: 24px;
  height: 24px;
  line-height: 24px;
  font-size: 22px;
  color: #fff;
}
._css-color-list {
  display: flex;
  align-items: center;
  justify-content: space-between;
  // padding-left: 12px;
  // padding-right: 12px;
  margin-top: 8px;
  // margin-bottom: 12px;
}

  .flex-box {
    display: flex;
    &.align-center {
      align-items: center;
    }
  }
  
  .form-item {
    margin-top: 16px;
    .label {
      color: #222;
      font-size: 14px;
    }
  }
  .mg-b40 {
    margin-bottom: 40px;
  }
  .mg-l60 {
    margin-left: 60px;
  }
  .mg-t8 {
    margin-top: 8px;
  }
  .mg-t16 {
    margin-top: 16px;
  }
  .title {
    color: #222;
    font-size: 16px;
    font-weight: 500;
  }
  .time-box {
    display: flex;
    align-items: center;
    .time-input {
      width: 120px;
      margin-left: 20px;
    }
  }
  .input-box {
    width: 400px;
    height: 32px;
    margin-top: 8px;
    border-radius: 2px;
    border: 1px solid #d8d8d8;
    outline: none;
    padding-left: 8px;
    box-sizing: border-box;
  }
}
.meeting-qrcode {
  .qrcode-box {
    width: 120px;
    height: 120px;
    margin-right: 20px;
    background: #ccc;
    cursor: pointer;
  }
  .sign-info {
    .item {
      margin-bottom: 12px;
      columns: #222;
      font-size: 14px;
      span {
        color: #007AFF;
      }
    }
    
  }
}
</style>
<style lang="less">
.meeting-sigin-wrap {
  .time-input {
    .el-input__inner {
      border-width: 1px;
    }
    .input-icon {
      line-height: 32px;
    }
  }
  
}
</style>
