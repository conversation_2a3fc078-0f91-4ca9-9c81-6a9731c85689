<template>
  <div class="agenda-wrap">
    <div class="flex-box spance-between align-center list-wrap">
      <div class="title">
        议程列表
      </div>
      <div class="flex-box">
        <div class="icon add-icon" @click="addItem"></div>
        <!-- <div class="icon reduce-icon" @click="deleteAll"></div> -->
      </div>
    </div>
    <ul class="table-wrap">
      <li class="table-header table-item">
        <!-- <div class="checkbox-box">
          <el-checkbox :value="(dataList.length === checkedList.length)" @change="checkAllData"></el-checkbox>
        </div> -->
        <div class="table-td">议题</div>
        <div class="table-td">负责人</div>
        <div class="table-td">相关干系人</div>
        <div class="table-td">议题开始时间</div>
        <div class="table-td">议题结束时间</div>
        <div class="table-td handle-box"></div>
      </li>
      <li class="table-item" v-for="(item, index) in dataList" :key="item.Sort">
        <!-- <div class="checkbox-box">
          <el-checkbox :value="isChecked(item)" @change="checkRow(item, index)"></el-checkbox>
        </div> -->
        <div class="table-td">
          <div v-if="(currentData.Sort === item.Sort)">
            <input type="text" v-model="item.AgendaTheme" class="title-input" />
          </div>
          <div v-else>
            {{item.AgendaTheme}}
          </div>
        </div>
        <div class="table-td">
          <div v-if="(currentData.Sort === item.Sort)">
            <bim-input
              :dataList="memberData"
              :multiple="false"
              optionValue="UserId"
              optionLabel="RealName"
              :value="item.LeaderUserId"
              @updateData="updateCharge"
            />
          </div>
          <div v-else>
            {{(memberObj[item.LeaderUserId] || {}).RealName || '/'}}
          </div>
          <div style="display: none">{{ memberData }}</div>
        </div>
        <div class="table-td">
          <div v-if="(currentData.Sort === item.Sort)">
            <bim-input
              :dataList="memberData"
              optionValue="UserId"
              optionLabel="RealName"
              :value="item.RelevantUserName"
              @updateData="updateMember"
            />
          </div>
          <div v-else>
            {{forMateData(item.RelevantUserName)}}
          </div>
        </div>
        <div class="table-td">
          <div v-if="(currentData.Sort === item.Sort)">
            <el-time-picker
              v-model="item.StartTime"
              format="HH:mm:ss"
              value-format="HH:mm:ss"
              size="small"
              @change="changeTime"
              placeholder="选择时间">
            </el-time-picker>
          </div>
          <div v-else>
            {{item.StartTime}}
          </div>
        </div>
        <div class="table-td">
          <div v-if="(currentData.Sort === item.Sort)">
            <el-time-picker
              v-model="item.EndTime"
              format="HH:mm:ss"
              value-format="HH:mm:ss"
              size="small"
              @change="changeTime1"
              placeholder="选择时间">
            </el-time-picker>
          </div>
          <div v-else>
            {{item.StartTime}}
          </div>
        </div>
        <div class="table-td flex-box handle-box">
          <template v-if="(currentData.Sort !== item.Sort)">
            <span class="modify" @click="modify(item, index)">
              修改
            </span>
            <span class="modify" @click="deleteRow(item, index)">
              删除
            </span>
          </template>
          <template v-else>
            <span class="modify" @click="save(item, index)">
              保存
            </span>
            <span class="modify" @click="cancle(item, index)">
              取消
            </span>
          </template>
        </div>
      </li>
    </ul>
  </div>
</template>

<script>
import BimInput from '../component/bim-input'
export default {
  name: '',
  props: {
    memberData: {
      type: Array,
      default: () => {
        return []
      }
    },
    memberObj: {
      type: Object,
      default: () => {
        return {}
      }
    },
    formData: {
      type: Array,
      default: () => {
        return []
      }
    }
  },

  data () {
    return {
      checked: false,
      dataList: [],
      checkedList: [],
      currentData: {},
      oldRowData: {},
      modyfyCharge: '',
      modyfyMember: [],
      modifyAgendaTheme: '',
      modifyTime: '',
      modifyTime1: '',
      isAdd: 0
    }
  },
  components: {
    BimInput
  },
  created () {
    // this.memberData.forEach(v => {
    //   this.memberObj[v.UserId] = v
    // })
  },
  mounted () {
    this.dataList = this.formData
  },

  methods: {
    addItem () {
      if (this.isAdd == 0) {
        this.dataList.push({
          "CreateTime": new Date(),
          "AgendaTheme": "",
          "Sort": this.dataList.length,
          "LeaderUserId": "",
          "RelevantUserName": [],
          "StartTime": "",
          "EndTime": ""
        })
        this.isAdd = 1
        this.currentData = {
          "CreateTime": new Date(),
          "AgendaTheme": "",
          "Sort": this.dataList.length - 1,
          "LeaderUserId": "",
          "RelevantUserName": [],
          "StartTime": "",
          "EndTime": ""
        }
      }
      
    },
    deleteAll () {
      console.log(this.checkedList)
    },
    forMateData (data) {
      let arr = []
      if (typeof data === 'string') {
        data = data.split(',')
      }
      data.forEach(v => {
        if (this.memberObj[v]) {
          arr.push(this.memberObj[v].RealName)
        }
      })
      return arr.join('、')
    },
    checkAllData () {
      if (this.dataList.length === this.checkedList.length) {
        this.checkedList = []
      } else {
        this.checkedList = []
        this.dataList.forEach(v => {
          this.checkedList.push(v.id)
        })
      }
    },
    checkRow (data, index) {
      if (this.checkedList.indexOf(data.id) > -1) {
        this.checkedList.splice(this.checkedList.indexOf(data.id), 1)
      } else {
        this.checkedList.push(data.id)
      }
    },
    isChecked (data) {
      return this.checkedList.includes(data.id)
    },
    checkAll () {
      console.log(321)
    },
    updateMember (data) {
      this.modyfyMember = data
    },
    updateCharge (data) {
      this.modyfyCharge = data
    },
    changeTime (time) {
      this.modifyTime = time
    },
    changeTime1 (time) {
      this.modifyTime1 = time
    },
    modify (data) {
      this.currentData = data
      this.modyfyCharge = data.LeaderUserId
      this.modyfyMember = data.RelevantUserName
      this.modifyTime = data.StartTime
      this.modifyTime1 = data.EndTime
      this.oldRowData = data
    },
    save (data, index) {
      if (!data.AgendaTheme || !this.modyfyCharge || !this.modyfyMember || !this.modifyTime || !this.modifyTime1) {
        this.$message.error('请填写完整信息')
        return
      }
      this.dataList[index].LeaderUserId = this.modyfyCharge
      this.dataList[index].RelevantUserName = this.modyfyMember
      this.dataList[index].StartTime = this.modifyTime
      this.dataList[index].EndTime = this.modifyTime1
      this.currentData = {}
      this.changeData()
      this.isAdd = 0
    },
    deleteRow (data, index) {
      this.dataList.splice(index, 1)
      this.changeData()
    },
    cancle (data, index) {
      if (this.isAdd === 1 || data.Sort === '') {
        this.dataList.splice(index, 1)
        this.isAdd = 0
      } else {
        this.currentData = {}
        this.modyfyCharge = ''
        this.modyfyMember = []
        this.modifyTime = ''
        this.dataList[index] = this.oldRowData
        this.isAdd = 0
      }
      this.changeData()
      
    },
    changeData () {
      this.$emit('changeData', this.dataList)
    }
  }
}
</script>

<style lang='scss' scoped>
.agenda-wrap {
  text-align: left;
  .title-input {
    width: 100%;
    height: 30px;
  }
  .list-wrap {
    padding-right: 24px;
  }
  .title {
    padding: 24px;
    color: #222;
    font-size: 16px;
    font-weight: 500;
  }
  .icon {
    width: 20px;
    height: 20px;
    margin-left: 8px;
    &.add-icon {
      background: url('../../../../../assets/images/add-icon.png');
      background-size: 100% 100%;
    }
    &.reduce-icon {
      background: url('../../../../../assets/images/reduce-icon.png');
      background-size: 100% 100%;
    }
  }
  .flex-box {
    display: flex;
    &.spance-between {
      justify-content: space-between;
    }
    &.align-center {
      align-items: center;
    }
  }
  .table-wrap {
    box-sizing: border-box;
    .table-header {
      background: #F5F5F5;
      .table-td {
        color: #333;
        font-size: 14px;
        font-weight: 500;
      }
    }
    .table-item {
      display: flex;
      justify-content: space-between;
      padding: 20px 0 20px 20px;
      border-bottom: 1px solid rgba($color: #000000, $alpha: .1);
      .checkbox-box {
        display: flex;
        justify-content: center;
        margin-right: 20px;
      }
      .table-td {
        padding-right: 24px;
        box-sizing: border-box;
        flex: 1;
        min-width: 220px;
        font-size: 12px;
        &.handle-box {
          min-width: 150px;
          align-items: center;
          padding-left: 16px;
          box-sizing: border-box;
        }
        .modify {
          color: #007AFF;
          font-size: 12px;
          margin-right: 8px;
          cursor: pointer;
        }
      }
    }
  }
}
</style>
