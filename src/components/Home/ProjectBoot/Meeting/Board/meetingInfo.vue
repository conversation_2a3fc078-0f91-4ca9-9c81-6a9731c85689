<template>
  <div class="meeting-info">
    <div class="title">
      基本信息
    </div>
    <div class="form-item">
      <div class="input-item">
        <div class="label">* 会议名称</div>
        <div class="input-box">
          <input type="text" class="input-text" v-model="form.MeetingName" @input="changeData" />
        </div>
      </div>
      <div class="input-item mg-l60">
        <div class="label">发起人</div>
        <div class="input-box">
          <bim-input
            :dataList="memberData"
            optionValue="UserId"
            optionLabel="RealName"
            :multiple="false"
            :value="form.SponsorUserId"
            @updateData="updateCharge('SponsorUserId', $event)"
          />
        </div>
      </div>
    </div>

    <div class="form-item mg-b40">
      <div class="input-item">
        <div class="label">联系人</div>
        <div class="input-box">
          <bim-input
            :dataList="memberData"
            optionValue="UserId"
            optionLabel="RealName"
            :multiple="false"
            :value="form.ContactUserId"
            @updateData="updateCharge('ContactUserId', $event)"
          />
        </div>
      </div>
      <div class="input-item mg-l60">
        <div class="label">会议内容</div>
        <div class="textarea-box">
          <textarea class="input-textarea" v-model="form.MeetingCotent" @input="changeData"></textarea>
        </div>
      </div>
    </div>

    <div class="title">
      时间地点
    </div>

    <div class="form-item">
      <div class="input-item">
        <div class="label">开始日期与时间</div>
        <div class="input-box">
          <el-date-picker
            v-model="form.StartDate"
            size="small"
            type="date"
            format="yyyy-MM-dd"
            @change="changeData"
            value-format="yyyy-MM-dd"
            placeholder="选择日期">
          </el-date-picker>
          <el-time-picker
            v-model="form.StartTime"
            size="small"
            format="HH:mm:ss"
            value-format="HH:mm:ss"
            class="mg-t8"
            @change="changeData"
            placeholder="选择时间">
          </el-time-picker>
        </div>
      </div>
      <div class="input-item mg-l60">
        <div class="label">结束日期与时间</div>
        <div class="input-box">
          <el-date-picker
            v-model="form.EndDate"
            size="small"
            type="date"
            format="yyyy-MM-dd"
            @change="changeData"
            value-format="yyyy-MM-dd"
            placeholder="选择日期">
          </el-date-picker>
          <el-time-picker
            v-model="form.EndTime"
            size="small"
            format="HH:mm:ss"
            value-format="HH:mm:ss"
            class="mg-t8"
            @change="changeData"
            placeholder="选择时间">
          </el-time-picker>
        </div>
      </div>
    </div>

    <div class="form-item">
      <div class="input-item">
        <div class="label">会议地点*</div>
        <div class="input-box">
          <bim-input
            :dataList="roomList"
            optionValue="Id"
            optionLabel="RoomName"
            :multiple="false"
            isMeetingRoom
            :disabled="!isDisabled"
            :value="form.RoomId"
            @updateData="updateCharge('RoomId', $event)"/>
        </div>
        <div class="error-tip" v-if="!isDisabled">请先选择会议开始结束时间</div>
      </div>
      <div class="input-item mg-l60">
        <div class="label">自定义会议地点</div>
        <div class="textarea-box">
          <input type="text" class="input-text" v-model="form.CustomAddress" @input="changeData" />
        </div>
      </div>
    </div>

    <div class="form-item mg-b40">
      <div class="input-item">
        <div class="label">提醒方式</div>
        <div class="input-box">
          <el-radio :value="form.RemindMethod" :label="2" disabled>邮箱提醒</el-radio>
        </div>
      </div>
      <div class="input-item mg-l60">
        <div class="label">立即提醒</div>
        <div class="textarea-box">
          <el-switch
            v-model="IsPrompt"
            @change="changeIsPrompt">
          </el-switch>
        </div>
        <div class="time-box" v-if="!IsPrompt">
          <span>开始前</span>
          <div class="time-input">
            <el-input
            placeholder="请输入内容"
            size="small"
            @change="changeData"
            v-model="form.ReminderTime">
            <i
              class="input-icon"
              slot="suffix">
              小时
            </i>
          </el-input>
          </div>
          <div class="time-input">
            <el-input
            placeholder="请输入内容"
            size="small"
            @change="changeData"
            v-model="form.RemindrMinutes">
            <i
              class="input-icon"
              slot="suffix">
              分钟
            </i>
          </el-input>
          </div>
        </div>
      </div>
    </div>

    <div class="title">
      参会成员
    </div>

    <div class="form-item">
      <div class="input-item">
        <div class="label">参会人员</div>
        <div class="input-box">
          <bim-input
            :dataList="memberData"
            optionValue="UserId"
            optionLabel="RealName"
            :value="form.MeetingPerson"
            @updateData="updateCharge('MeetingPerson', $event)"
          />
        </div>
      </div>
      <div class="input-item mg-l60">
        <div class="label">其他人员</div>
        <div class="textarea-box">
          <input type="text" class="input-text" v-model="form.OtherParticipants" @input="changeData" @blur="form.OtherParticipants = Number(form.OtherParticipants)" />
        </div>
      </div>
    </div>

    <div class="form-item mg-b40">
      <div class="input-item">
        <div class="label">应到人数</div>
        <div class="textarea-box">
          <input type="text" class="input-text" v-model="form.ShouldArriveNumber" @input="changeData" @blur="form.ShouldArriveNumber = Number(form.ShouldArriveNumber)" />
        </div>
      </div>
    </div>

    <div class="title">
      其他相关
    </div>

    <div class="form-item">
      <div class="input-item">
        <div class="label">其他信息</div>
        <div class="textarea-box">
          <textarea class="input-textarea" v-model="form.Remark" @input="changeData"></textarea>
        </div>
      </div>
      <div class="input-item mg-l60">
        <div class="label">相关附件</div>
        <div class="textarea-box">
          <CompsEloButton
            :width="76"
            :height="32"
            text="上传文件"
            color="#1890ff"
            bgcolor="#fff"
            border="1px solid #1890ff"
            :fontSize="12"
            @onclick="upload_importingfile"
          ></CompsEloButton>
        </div>
        <div>
          {{((form.Attached || [])[0] || {}).AttchedName}}
        </div>
      </div>
    </div>
    <!-- 下方的导入用户 -->
    <input
      type="file"
      style="display:none"
      id="id_ImportMaterials_File"
      @change="importFileChange($event)"
    >
  </div>
</template>

<script>
import BimInput from '../component/bim-input.vue'
import CompsEloButton from "@/components/CompsElOverride/CompsEloButton";
export default {
  name: '',
  props: {
    memberData: {
      type: Array,
      default: () => {
        return []
      }
    },
    formData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },

  data () {
    return {
      IsPrompt: true,
      form: {
        "MeetingName": "",
        "SponsorUserId": "",
        "ContactUserId": "",
        "MeetingCotent": "",
        "RemindMethod": 2,
        "IsPrompt": true,
        "ReminderTime": 0,
        "RemindrMinutes": 0,
        "OtherParticipants": 0,
        "MeetingPerson": [],
        "ShouldArriveNumber": 0,
        "Remark": "",
        "RoomId": "",
        "CustomAddress": "",
        "Attached": [ // 上传附件
          
        ]
      },
      roomList: []
    }
  },
  components: {
    BimInput,
    CompsEloButton
  },
  mounted () {
    this.form = this.formData
  },
  computed: {
    isDisabled () {
      return this.form.StartTime && this.form.EndTime && this.form.StartDate && this.form.EndDate
    }
  },
  watch: {
    isDisabled (val) {
      if (val) {
        this.getRoomList()
      }
    }
  },

  methods: {
    getRoomList () {
      let organizeId = this.$staticmethod._Get('organizeId')
      let UserId = this.$staticmethod.Get("UserId")
      this.$axios.get(window.bim_config.webserverurl+this.$urlPool.getRoomList, {
        params: {
          projectId: organizeId,
          startTime: this.form.StartDate + ` ${this.form.StartTime}`,
          endTime: this.form.EndDate + ` ${this.form.EndTime}`,
          Token: this.$staticmethod.Get("Token")
        }
      }).then(res => {
        this.roomList = res.data.Data
      })
    },
    changeIsPrompt (bol) {
      this.form.IsPrompt = bol
      this.changeData()
    },
    updateCharge (key, data) {
      this.form[key] = data
      this.changeData()
    },
    changeData () {
      this.$emit('changeData', this.form)
    },
    upload_importingfile() {
      var _this = this;
      var dominputfile = document.getElementById("id_ImportMaterials_File");
      dominputfile.value = "";
      dominputfile.click();
    },
    // 选择Excel文件后执行
    importFileChange(e) {
      // 准备 formData
      var _this = this;
      var dominputfile = document.getElementById("id_ImportMaterials_File");
      if (dominputfile.files.length == 1) {
        // 拿到文件对象
        var file = dominputfile.files[0];   
        let organizeId = this.$staticmethod._Get('organizeId')     
        let UserId = this.$staticmethod.Get("UserId")
        var config = {
          headers: {
            "Content-Type": "multipart/form-data"
          }
        };
        var fd = new FormData();
        fd.append("ProjectId", organizeId);
        fd.append("UserId", UserId);
        fd.append("FileKey", file.name);
        fd.append("File", file);
        fd.append("Token", this.$staticmethod.Get("Token"));
        this.$axios.post(window.bim_config.webserverurl+this.$urlPool.uploadFile, fd, config).then(res => {
          if(res.data.Ret == 1){
            this.form.Attached = [{
              "CreateTime": new Date(),
              "AttchedName": file.name,
              "Sort": 0,
              "Address": res.data.Data.FileAddress
            }]
            this.changeData()
          }else{
            this.$message({
              message: res.data.Msg,
              type: 'error'
            });
          }
        })
        
      }
    }
  }
}
</script>

<style lang='scss' scoped>
.meeting-info {
  padding: 24px;
  text-align: left;
  .error-tip {
    color: red;
  }
  .title {
    color: #222;
    font-size: 16px;
    font-weight: 500;
  }
  .textarea-box {
    width: 400px;
    margin-top: 8px;
  }
  input, textarea {
    width: 100%;
    height: 100%;
    outline: none;
    padding-left: 16px;
    box-sizing: border-box;
  }
  .input-text {
    height: 36px;
  }
  .input-textarea {
    height: 80px;
  }
  .mg-l60 {
    margin-left: 60px;
  }
  .mg-b40 {
    margin-bottom: 40px;
  }
  .mg-t8 {
    margin-top: 8px;
  }
  .form-item {
    display: flex;
    margin-top: 16px;
    .label {
      color: #222;
      font-size: 14px;
    }
    .input-box {
      width: 400px;
      margin-top: 8px;
    }
    .time-box {
      display: flex;
      align-items: center;
    }
  }
  .time-input {
    width: 150px;
    margin-left: 20px;
  }
}
.input-icon {
  line-height: 32px;
}
</style>
<style lang="less">
.meeting-info {
  .time-input {
    .el-input__inner {
      border-width: 1px;
    }
  }
  
}
</style>
