<template>
  <div class="create-meeting-wrap">
    <div class="head">
      {{ meetingStatus ? '查看会议' : '新建会议'}}
    </div>
    <div class="main">
      <div class="handle-wrap">
        <ul class="tab-wrap">
          <li :class="['tab-item', {'active': activeType === 0}]" @click="changeType(0)">会议信息</li>
          <li :class="['tab-item', {'active': activeType === 1}]" @click="changeType(1)">会议议程</li>
          <li :class="['tab-item', {'active': activeType === 2}]" @click="changeType(2)">会议服务</li>
          <!-- <li :class="['tab-item', {'active': activeType === 3}]" @click="changeType(3)">会议签到</li> -->
          <li v-if="(meetingStatus == 3)" :class="['tab-item', {'active': activeType === 4}]" @click="changeType(4)">会议决议</li>
        </ul>
        <div class="button-wrap">
          <template v-if="meetingStatus != 2 && meetingStatus != 3" >
            <el-button @click="submit(1)" class="btn save-btn">提交</el-button>
            <el-button type="primary" @click="submit(0)" class="btn cancle-btn" v-if="meetingStatus != 1">草稿</el-button>
          </template>
          <div class="close-btn" @click="goBack"></div>
        </div>
      </div>
      <div class="create-body" v-if="requestFlag">
        <div class="remark" v-if="(meetingStatus == 2 || meetingStatus == 3) && activeType !== 4 && activeType !== 3"></div>
        <MeetingInfo
          v-show="(activeType === 0)"
          :memberData="memberData"
          :memberObj="memberObj"
          :formData="meetingInfo"
          @changeData="changeData('meetingInfo', $event)"/>
        <MeetingResolution
          v-show="(activeType === 1)"
          :formData="Agenda"
          :memberData="memberData"
          :memberObj="memberObj"
          @changeData="changeData('Agenda', $event)"/>
        <MeetingService
          v-show="(activeType === 2)"
          :formData="Service"
          :memberData="memberData"
          :memberObj="memberObj"
          @changeData="changeData('Service', $event)"/>
        <MeetingSignIn
          v-show="(activeType === 3)"
          :formData="MeetingSignSetting"
          :memberData="memberData"
          :memberObj="memberObj"
          @changeData="changeData('MeetingSignSetting', $event)"/>
        <MeetingAgenda
          v-if="(activeType === 4)"
          :mettingId="mettingId"
          :memberObj="memberObj"
          :memberData="memberData"/>
      </div>
      
    </div>
  </div>
</template>

<script>
import MeetingInfo from './meetingInfo.vue'
import MeetingResolution from './meetingResolution.vue'
import MeetingSignIn from './meetingSignIn.vue'
import MeetingService from './meetingService.vue'
import MeetingAgenda from './meetingAgenda.vue'
import dayjs from 'dayjs'
export default {
  name: '',

  data () {
    return {
      activeType: 0,
      meetingStatus: '',
      memberData: [],
      memberObj: {},
      organizeId: '',
      Token: '',
      currentData: dayjs().format('YYYY-MM-DD'),
      roomList: [],
      mettingId: '',
      meetingInfo: {
        "MeetingName": "",
        "SponsorUserId": "",
        "ContactUserId": "",
        "MeetingCotent": "",
        "RemindMethod": 2,
        "IsPrompt": true,
        "ReminderTime": 0,
        "RemindrMinutes": 0,
        "OtherParticipants": 0,
        "MeetingPerson": [],
        "ShouldArriveNumber": 0,
        "Remark": "",
        "RoomId": "",
        "CustomAddress": "",
        "Attached": [],
        "StartDate": "",
        "StartTime": "",
        "EndDate": "",
        "EndTime": ""
      },
      "Agenda": [ // 会议议程
        // {
        //   "CreateTime": "",
        //   "AgendaTheme": "",
        //   "Sort": '',
        //   "LeaderUserId": "",
        //   "RelevantUserName": [],
        //   "StartTime": "",
        //   "EndTime": ""
        // }
      ],
      "Service": [ // 会议服务
        // {
        //   "CreateTime": "",
        //   "Title": "",
        //   "Content": "",
        //   "Sort": '',
        //   "LeaderUserId": ""
        // }
      ],
      "MeetingSignSetting": { // 会议签到
        "SignInMinutes": 0,
        "SignOut": true,
        "SignInOut": 0,
        "OverSignOut": 0,
        "BackgroundColor": "rgba(28, 50, 75, 1)",
        "FontColor": "rgba(127, 179, 210, 1)",
        "ShowSignIn": true,
        "ShowSignOut": true,
        "FontSize": 12,
        "RoleDisplay": true
      },
      formData: {
        "Id": "",
        "ProjectId": "",
        
        "Submit": true
      },
      requestFlag: false
    }
  },
  components: {
    MeetingInfo,
    MeetingResolution,
    MeetingSignIn,
    MeetingService,
    MeetingAgenda
  },
  created () {
    this.mettingId = this.$route.params.id
    this.meetingStatus = this.$route.params.status
    this.organizeId = this.$staticmethod._Get("organizeId");
    this.Token = this.$staticmethod.Get("Token");
    this.getMemberList()
    
  },
  mounted () {
    if (this.mettingId) {
      this.getMeetingInfo()
    } else {
      this.requestFlag = true
    }
  },

  methods: {
    goBack () {
      this.$router.go(-1)
    },
    getMeetingInfo () {
      let meetingInfo = new Promise((resolve) => {
        this.$axios.get(window.bim_config.webserverurl+this.$urlPool.getMeetingInfo, {
          params: {
            id: this.mettingId,
            Token: this.$staticmethod.Get("Token")
          }
        }).then(res => {
          resolve(res.data.Data)
        })
      })

      let meetingAgenda = new Promise((resolve) => {
        this.$axios.get(window.bim_config.webserverurl+this.$urlPool.getMeetingAgenda, {
          params: {
            meetingId: this.mettingId,
            Token: this.$staticmethod.Get("Token")
          }
        }).then(res => {
          
          resolve(res.data.Data)
        })
      })
      
      let mettingServices = new Promise(resolve => {
        this.$axios.get(window.bim_config.webserverurl+this.$urlPool.getMeetingServices, {
          params: {
            meetingId: this.mettingId,
            Token: this.$staticmethod.Get("Token")
          }
        }).then(res => {
          if(res.data.Ret == 1){
            resolve(res.data.Data)
          }else{
             resolve([])
          }
        })
      })

      let meetingSign = new Promise(resolve => {
        this.$axios.get(window.bim_config.webserverurl+this.$urlPool.getMeetingSign, {
          params: {
            meetingId: this.mettingId,
            Token: this.$staticmethod.Get("Token")
          }
        }).then(res => {
          
          resolve(res.data.Data)
        })
      })

      let getPersonList = new Promise(resolve => {
        this.$axios.get(window.bim_config.webserverurl+this.$urlPool.getPersonList, {
          params: {
            meetingId: this.mettingId,
            Token: this.$staticmethod.Get("Token")
          }
        }).then(res => {
          
          resolve(res.data.Data)
        })
      })

      getPersonList
      let requestList = [meetingInfo, meetingAgenda, mettingServices, meetingSign, getPersonList]

      Promise.all(requestList).then(res => {
        res[0].Attached = !res[0].Attached ? [] : res[0].Attached

        this.formData = {
          "Id": (res[0] ||{}).Id,
          "ProjectId": (res[0] ||{}).ProjectId,
        }
        this.meetingInfo = (res[0] ||{})
        res[1].map(v => {
          v.RelevantUserName = v.RelevantUserName.split(',')
        })
        this.Agenda = res[1]
        this.Service = res[2]
        this.MeetingSignSetting = res[3]
        let presonList = []
        res[4].forEach(v => {
          presonList.push(v.UserId)
        })
        this.meetingInfo.MeetingPerson = presonList
        this.requestFlag = true
      })
      

      
      
    },
    changeType (type) {
      this.activeType = type
    },
    changeData (key, data) {
      this[key] = data
    },

    getMemberList () {
      let url = `${this.$urlPool.GetUserPaged}?PageNum=1&PageSize=3000&KeyWord=&OrganizeId=${this.organizeId}&searchType=0&RoleId=&Token=${this.Token}`
      
      this.$axios.get(url).then(res => {
        this.memberData = res.data.Data.list
        this.memberData.forEach(v => {
          this.memberObj[v.UserId] = v
        })
      })
    },
    
    submit (isSubmit) {
      
      let organizeId = this.$staticmethod._Get('organizeId')
      let UserId = this.$staticmethod.Get("UserId")
      var _Token = this.$staticmethod.Get("Token");
      this.formData = Object.assign(this.formData, this.meetingInfo)
      const Agenda = this.Agenda.map(v => {
        if (typeof v.RelevantUserName != 'string') {
          v.RelevantUserName = v.RelevantUserName.join(',')
        }
        return v
      }).filter(v => {
        return v.AgendaTheme && v.CreateTime && v.EndTime && v.LeaderUserId && v.RelevantUserName && v.StartTime
      })
      const Service = this.Service.filter (v => {
        return v.Title && v.LeaderUserId && v.Content
      })
      this.formData.Agenda = Agenda
      this.formData.Service = Service
      this.formData.MeetingSignSetting = this.MeetingSignSetting
      if (this.formData.Attached.length == 0) {
        this.formData.Attached = null
      }
      if (this.formData.MeetingName === '') {
        this.$message.error('请填写会议名称')
        return
      }
      if (this.formData.ContactUserId === '') {
        this.$message.error('请选择联系人')
        return
      }
      if (this.formData.SponsorUserId === '') {
        this.$message.error('请选择会议发起人')
        return
      }
      if (this.formData.EndDate === '' || this.formData.EndTime === '' || this.formData.StartDate === '' || this.formData.EndDate === '') {
        this.$message.error('请选择会议开始结束时间！')
        return
      }
      if (this.formData.RoomId === '' && this.formData.CustomAddress === '') {
        this.$message.error('请选择会议室')
        return
      }

      if (this.formData.MeetingSignSetting.BackgroundColor === '') {
        this.$message.error('请选择签到屏幕背景颜色')
        return
      }

      if (this.formData.MeetingSignSetting.FontColor === '') {
        this.$message.error('请选择签到屏幕字体颜色')
        return
      }

      let personList = []
      this.formData.MeetingPerson.forEach((v, i) => {
        personList.push({
          sort: i,
          UserId: v
        })
      })
      this.formData.MeetingPerson = personList
      this.formData.OtherParticipants = Number(this.formData.OtherParticipants)

      this.$axios.post(window.bim_config.webserverurl+this.$urlPool.saveMeeting + `?userId=${UserId}&Token=${this.$staticmethod.Get('Token')}`, {
        ...this.formData,
        Submit: isSubmit ? true : false,
        ProjectId: organizeId
      }).then(res => {
        if (res.data.Ret == 1) {
          this.$message.success('创建成功')
          window.location.href = `${window.bim_config.hasRouterFile}/#/Home/ProjectBoot/Meeting/${organizeId}/${_Token}`;
        } else {
          this.$message.error(res.data.Msg)
        }
      })
    }
  }
}
</script>

<style lang='scss' scoped>
.create-meeting-wrap {
  .create-body {
    overflow-y: scroll;
    height: calc(100vh - 54px - 16px - 16px - 34px);
    position: relative;
    .remark {
      position: absolute;
      left: 0;
      right: 0;
      top: 0;
      bottom: 0;
      background: rgba($color: #fff, $alpha: 0.3);
      z-index: 1;
    }
  }
  .head {
    height: 54px;
    background: #fff;
    font-size: 16px;
    line-height: 54px;
    font-weight: 500;
    color: rgba($color: #000000, $alpha: .9);
    padding-left: 24px;
    text-align: left;
  }
  .main {
    margin-left: 24px;
    margin-top: 16px;
    padding-top: 16px;
    height: 100%;
    width: 100%;
    background: #fff;
    .handle-wrap {
      border-bottom: 1px solid #E8E8E8;
      padding: 0 24px;
      display: flex;
      justify-content: space-between;
      .tab-wrap {
        display: flex;
        .tab-item {
          border-top-left-radius: 4px;
          border-top-right-radius: 4px;
          padding: 6px 20px;
          color: #fff;
          font-size: 14px;
          margin-right: 4px;
          background: rgba($color: #000000, $alpha: .25);
          cursor: pointer;
          &.active {
            background: #007AFF;
          }
        }
      }
    }
  }
  .button-wrap {
    display: flex;
    align-items: center;
    .btn {
      border-color: #007AFF;
      font-size: 14px;
      padding: 10px 24px;
      border-radius: 4px;
    }
    .cancle-btn {
      color: #007AFF;
      background: #fff;
      margin-left: 8px;
    }
    .save-btn {
      background: #007AFF;
      color: #fff!important;
      margin-left: 16px;
    }
    .close-btn {
      width: 24px;
      height: 24px;
      background: url('../../../../../assets/images/metting-close.png');
      background-size: 100% 100%;
      margin-left: 40px;
      margin-right: 24px;
      cursor: pointer;
    }
  }
  
}
</style>
<style lang="less">
.create-meeting-wrap {
  .el-date-editor {
    border: 1px solid #949090;
  }
}
</style>
