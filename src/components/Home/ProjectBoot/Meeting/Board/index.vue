<template>
  <div class="meeting-board-wrap">
    <div class="head">
      {{ headerTitleText }}
    </div>
    <div class="main">
      <div class="left">
        <div class="meeting-title">会议状态</div>
        <ul class="filter-list">
          <li
          @click="changeTypeFn('')"
          :class="['item', {'active' : activeType === ''}]">
            <img src="../../../../../assets/images/all.png" class="icon-img" alt="">
            全部
          </li>
          <li
          @click="changeTypeFn(2)"
          :class="['item', {'active' : activeType === 2}]">
            <div class="icon ing"></div>
            进行中
          </li>
          <li
          @click="changeTypeFn(1)"
          :class="['item', {'active' : activeType === 1}]">
            <div class="icon will-start"></div>
            待开始
          </li>
          <li
          @click="changeTypeFn(3)"
          :class="['item', {'active' : activeType === 3}]">
            <div class="icon end"></div>
            已结束
          </li>
          <li
          @click="changeTypeFn(0)"
          :class="['item', {'active' : activeType === 0}]">
            <div class="icon draft"></div>
            草稿
          </li>
        </ul>
        <div class="meeting-title">我的会议</div>
        <ul class="filter-list my-meeting">
          <li class="item" v-for="item in myMeetingList" :key="item.id">
            <div class="meeting-name">{{item.MeetingName}}</div>
            <div class="meeting-room mg-t8">{{item.RoomName}}</div>
            <div class="meeting-time mg-t8">{{item.StartDateTime}} — {{item.EndDateTime}}</div>
          </li>
        </ul>
      </div>
      <div class="right">

        <t-calendar
        :firstDayOfWeek="7"
        :onControllerChange="onControllerChange"
        :controllerConfig="controllerConfig">
          <template slot="cellAppend" slot-scope="data">
            <div v-if="getShow(data)" class="cell-append-demo-outer">
              <t-tag v-for="v in (weekObj[data.formattedDate] || {}).MeetingData" :key="v.day" variant="light" size="small" class="activeTag" :class="classData[v.MeetingStatus]" style="width: 100%" @click="getData(v, 1)">
                {{ v.StartTime.split(':')[0]+":"+v.StartTime.split(':')[1] }}-{{v.MeetingName}}{{ v.MeetingStatus == 0 ? "(草稿)" : '' }}
              </t-tag>
            </div>
            <div v-if="getweekDate(data)" class="bg-content" @click="getData(data, 2)"></div>
          </template>
          <div slot="head" class="calendar-header">
            <t-button theme="primary" @click="createMeeting">新建会议</t-button>
            <!-- <t-button variant="outline" theme="primary" class="mg-l16">会议室使用情况</t-button> -->
          </div>
        </t-calendar>
      </div>
    </div>

  </div>
</template>

<script>
// 会议看板模块
import dayjs from 'dayjs'
export default {
  name: '',

  data () {
    return {
      headerTitleText: '',
      controllerConfig: {
        mode: {
          visible: false
        },
        weekend: {
          bisible: false
        }
      },
      activeType: '',
      weekDataArr: [],
      weekObj: {},
      classData: ['draft', 'willStart', 'ing', 'end'],
      weekData: [],
      myMeetingList: [],
      currentData: dayjs().format('YYYY-MM-DD')
    }
  },
  created () {
    const status = this.$route.params.status
    this.activeType = status ? Number(status) : ''
  },
  mounted () {
    this.headerTitleText = this.$staticmethod._Get("menuText") || '';
    this.getRequestData()
  },

  methods: {
    changeTypeFn (type) {
      this.activeType = type
      this.getRequestData()
    },
    createMeeting () {
      var _organizeId = this.$staticmethod._Get("organizeId");
      var _Token = this.$staticmethod.Get("Token");
      window.location.href = `${window.bim_config.hasRouterFile}/#/Home/ProjectBoot/createMeeting/${_organizeId}/${_Token}`;
    },
    getData (data) {
      console.log(data, 141243)
      var _organizeId = this.$staticmethod._Get("organizeId");
      var _Token = this.$staticmethod.Get("Token");
      window.location.href = `${window.bim_config.hasRouterFile}/#/Home/ProjectBoot/createMeeting/${_organizeId}/${_Token}/${data.MeetingId}/${data.MeetingStatus}`;
    },
    onControllerChange (data) {
      this.currentData = data.formattedFilterDate
      this.getRequestData()
    },
    getShow (data) {
      return this.weekObj[data.formattedDate]
    },
    getweekDate (data) {
      const list = this.weekData
      const weekDataArr = []
      list.forEach(v => {
        weekDataArr.push(v.Day)
      })
      return weekDataArr.includes(data.formattedDate)
    },
    getRequestData () {
      let organizeId = this.$staticmethod._Get('organizeId')
      let UserId = this.$staticmethod.Get("UserId")
      let month = this.currentData.split('-')
      let endDay = new Date(month[0], month[1], 0).getDate();
      let start = [month[0], month[1],  '01'].join('-')
      let end = [month[0],  month[1],  endDay].join('-')
      this.$axios.get(window.bim_config.webserverurl+this.$urlPool.getCalendarList, {
        params: {
          ProjectId: organizeId,
          StartTime: start + ' 00:00:00',
          EndTime: end + ' 23:59:59',
          UserId: UserId,
          MeetingStatus: this.activeType,
          Token: this.$staticmethod.Get("Token")
        }
      }).then(res => {
        if(res.data.Ret == 1){
          this.weekData = res.data.Data.MeetingList
          this.myMeetingList = res.data.Data.MyMeeting
          this.weekObj = {}
          this.weekData.forEach(v => {
            this.weekObj[v.Day] = v
          })
        }
      })
    }
  }
}
</script>

<style lang='scss' scoped>
.meeting-board-wrap {
  min-width: 1660px;
  .mg-l16 {
    margin-left: 16px;
  }
  .head {
    height: 54px;
    background: #fff;
    font-size: 16px;
    line-height: 54px;
    font-weight: 500;
    color: rgba($color: #000000, $alpha: .9);
    padding-left: 16px;
    text-align: left;
  }
  .main {
    display: flex;
    .left {
      width: 270px;
      height: calc(100vh - 54px);
      background: #fff;
      display: flex;
      flex-direction: column;
      .meeting-title {
        padding-left: 16px;
        padding-top: 22px;
        padding-bottom: 22px;
        font-size: 14px;
        color: rgba($color: #000000, $alpha: .9);
        box-shadow: 0px 1px 0px 0px rgba(0,0,0,0.05);
        font-weight: 500;
        background: rgba(0,122,255,0.05);
        text-align: left;
      }
      .filter-list {
        .item {
          padding-left: 24px;
          padding-top: 22px;
          padding-bottom: 22px;
          font-size: 14px;
          color: rgba($color: #000000, $alpha: .9);
          box-shadow: 0px 1px 0px 0px rgba(0,0,0,0.05);
          display: flex;
          justify-content: flex-start;
          cursor: pointer;
          &:hover {
            background: rgba($color: #000, $alpha: .05);
          }
          &.active {
            background: rgba($color: #000, $alpha: .05);
          }
          .icon-img {
            width: 18px;
            height: 18px;
            margin-right: 18px;
          }
          .icon {
            width: 18px;
            height: 18px;
            margin-right: 8px;
            border: 1px solid transparent;
            border-radius: 2px;
            &.ing {
              background: rgba($color: #FFC30D, $alpha: .2);
              border-color: #FFC30D;
            }
            &.will-start {
              background: rgba($color: #007AFF, $alpha: .2);
              border-color: #007AFF;
            }
            &.end {
              background: rgba($color: #999999, $alpha: .2);
              border-color: #999999;
            }
            &.draft {
              background: rgba($color: #89F8FF, $alpha: .2);
              border-color: #89F8FF;
            }
          }
        }
      }
      .my-meeting {
        flex: 1;
        overflow-y: scroll;
        &::-webkit-scrollbar {
          display: none;
        }
        .mg-t8 {
          margin-top: 8px;
        }
        .item {
          flex-direction: column;
          align-items: flex-start;
        }
        .meeting-time,.meeting-room {
          font-size: 12px;
          color: #666666;
          font-weight: 400;
        }
      }
    }
    .right {
      margin-left: 24px;
      // flex: 1;
      width: calc(100vw - 260px - 270px - 24px);
      .bg-content {
        position: absolute;
        left: 0;
        right: 0;
        top: 0;
        bottom: 0;
        opacity: .5;
        &.end {
          background: rgba($color: #000000, $alpha: .1);
        }
        &.ing {
          background: rgba($color: #FFC30D, $alpha: .3);
        }
        &.willStart {
          background: rgba($color: #007AFF, $alpha: .2);
        }
      }
      .activeTag {
        z-index: 1;
        position: relative;
        &.end {
          background: rgba($color: #000000, $alpha: .1);
        }
        &.ing {
          background: rgba($color: #FFC30D, $alpha: .3);
        }
        &.willStart {
          background: rgba($color: #007AFF, $alpha: .2);
        }
        &.draft {
          background: rgba($color: #89F8FF, $alpha: .2);
        }
      }

      .calendar-header {
        display: flex;
        justify-content: flex-start;
      }
    }
  }
}
</style>
<style lang="less">
.meeting-board-wrap {
  .t-calendar--full {
    border: 0;
    border-radius: 0;
    height: 100%;
  }
  .t-button--variant-base.t-button--theme-primary:not(.t-is-disabled):not(.t-button--ghost) {
    background: #007AFF;
    border-color: #007AFF;
  }
}
</style>
