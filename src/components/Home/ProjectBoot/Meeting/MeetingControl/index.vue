<template>
  <div class="meeting-control-wrap">
    <div class="head">
      {{ headerTitleText }}
    </div>
    <div class="main">
      <div class="handle-wrap">
        <ul class="tab-wrap">
          <li :class="['tab-item', {'active': activeType === 0}]" @click="filterList(0)">全部</li>
          <li :class="['tab-item', {'active': activeType === 1}]" @click="filterList(1)">今天</li>
          <li :class="['tab-item', {'active': activeType === 2}]" @click="filterList(2)">本月</li>
          <li :class="['tab-item', {'active': activeType === 3}]" @click="filterList(3)">本年</li>
        </ul>
        <div class="button-wrap">
          <button class="create-btn" @click="ExportSceneData">导出</button>
        </div>
      </div>
      <div class="title-wrap">
        <span></span>
        <div class="index-wrap">
          <input type="text" v-model="searchVal" placeholder="输入会议名称" @keyup="search">
          <img src="../../../../../assets/images/metting-search.png" class="search-icon" alt="" @click="search">
        </div>
      </div>
      <div class="table-wrap">
        <el-table
          :data="tableData"
          style="width: 100%"
          @selection-change="handleSelectionChange">
          <!-- <el-table-column
            type="selection"
            width="55">
          </el-table-column> -->
          <el-table-column
            label="会议名称"
            >
            <template slot-scope="scope">{{ scope.row.MeetingName }}</template>
          </el-table-column>
          <el-table-column
            label="会议室地点"
            >
            <template slot-scope="scope">{{ scope.row.RoomName || scope.row.CustomerAddress }}</template>
          </el-table-column>
          <el-table-column
            prop="SponsorUserName"
            label="发起人"
            >
          </el-table-column>
          <el-table-column
            prop="CreateTime"
            label="创建时间"
            >
            <template slot-scope="scope">{{ scope.row.CreateTime.split(' ')[0] }} {{ scope.row.CreateTime.split(' ')[1].split(':')[0] }}:{{ scope.row.CreateTime.split(' ')[1].split(':')[1] }}</template>
          </el-table-column>
          <el-table-column
            label="状态"
            >
            <template slot-scope="scope">{{ scope.row.MeetingStatusDesc }}</template>
          </el-table-column>
          <el-table-column
            label="开始时间"
            >
            <template slot-scope="scope">{{ scope.row.StartTime.split(' ')[0] }} {{ scope.row.StartTime.split(' ')[1].split(':')[0] }}:{{ scope.row.StartTime.split(' ')[1].split(':')[1] }}</template>
          </el-table-column>
          <el-table-column
            label="结束时间"
            >
            <template slot-scope="scope">{{ scope.row.EndTime.split(' ')[0] }} {{ scope.row.EndTime.split(' ')[1].split(':')[0] }}:{{ scope.row.EndTime.split(' ')[1].split(':')[1] }}</template>
          </el-table-column>
          <el-table-column
            label="操作"
            >
            <template slot-scope="scope">
              <span v-if="scope.row.MeetingStatus == 0 || scope.row.MeetingStatus == 1" class="btn" @click="deleteMeeting(scope.row)">删除</span>
            </template>
          </el-table-column>
        </el-table>
        <div class="page-wrap">
          <el-pagination
            background
            layout="prev, pager, next"
            :total="total"
            @current-change="handleCurrentChange">
          </el-pagination>
        </div>
      </div>
      
    </div>
  </div>
</template>

<script>
// 会议控制台
export default {
  name: '',

  data () {
    return {
      headerTitleText: '',
      searchVal: '',
      activeType: 0,
      tableData: [],
      multipleSelection: [],
      total: 100,
      pageData: {
        PageNum: 1,
        PageSize: 10
      }
    }
  },
  created () {
    this.getListData()
    this.headerTitleText = this.$staticmethod._Get("menuText") || '';
  },

  methods: {
    handleCurrentChange (page) {
      this.pageData = Object.assign(this.pageData, {PageNum: page})
      this.getListData()
    },
    search () {
      this.getListData()
    },
    handleSelectionChange(val) {
      console.log(val, 13412421)
      this.multipleSelection = val;
    },
    getListData () {
      let organizeId = this.$staticmethod._Get('organizeId')
      this.$axios.get(window.bim_config.webserverurl+this.$urlPool.getMeetingList, {
        params: {
          ...this.pageData,
          ProjectId: organizeId,
          TimeType: this.activeType,
          MeetingName: this.searchVal,
          Token:this.$staticmethod.Get('Token')
        }
      }).then(res => {
        if(res.data.Ret ==1){
          this.total = res.data.Data.Total
          this.tableData = res.data.Data.Data
        }
      })
    },
    deleteMeeting (data) {
      this.$confirm("确定删除当前会议", {
        confirmButtonText:'确定',
        cancelButtonText:'取消',
        type:'warning'
      }).then(() => {
        this.$axios.post(window.bim_config.webserverurl+this.$urlPool.deleteMeeting + `?meetingId=${data.Id}&Token=${this.$staticmethod.Get('Token')}`).then(res => {
          if (res.data.Ret == 1) {
            this.$message.success(res.data.Msg)
            this.getListData()
          } else {
            this.$message.error(res.data.Msg)
            this.getListData()
          }
          
        })
      }).catch((err) => {
        console.log(err)
      })
    },
    filterList (type) {
      this.activeType = type
      this.getListData()
    },
     //导出
    ExportSceneData() {
      window.location.href = `${window.bim_config.webserverurl}${this.$urlPool.exportExcel}?ProjectId=${this.$staticmethod._Get("organizeId")}&Token=${this.$staticmethod.Get('Token')}`
    },
    deleteRoom () {
      console.log(this.multipleSelection, 141234)
    },
    on_Modelthumbnail_change() {
    }
  }
}
</script>

<style lang='scss' scoped>
.meeting-control-wrap {
  overflow-y: scroll;
  text-align: left;
  .page-wrap {
    margin-top: 12px;
    display: flex;
    justify-content: flex-end;
    padding-right: 24px;
  }
  .head {
    height: 54px;
    background: #fff;
    font-size: 16px;
    line-height: 54px;
    font-weight: 500;
    color: rgba($color: #000000, $alpha: .9);
    padding-left: 16px;
    text-align: left;
  }
  .main {
    margin-left: 16px;
    margin-top: 16px;
    padding-top: 16px;
    height: 100%;
    width: 100%;
    background: #fff;
    .handle-wrap {
      border-bottom: 1px solid #E8E8E8;
      padding: 0 24px;
      display: flex;
      justify-content: space-between;
      .tab-wrap {
        display: flex;
        .tab-item {
          border-top-left-radius: 4px;
          border-top-right-radius: 4px;
          padding: 6px 20px;
          color: #fff;
          font-size: 14px;
          margin-right: 4px;
          background: rgba($color: #000000, $alpha: .25);
          cursor: pointer;
          &.active {
            background: #007AFF;
          }
        }
      }
    }
    .title-wrap {
      display: flex;
      justify-content: space-between;
      padding: 24px 24px 20px;
      span {
        color: #000;
        opacity: .9;
        font-size: 16px;
        font-weight: 500;
      }
      .index-wrap {
        position: relative;
        width: 240px;
        height: 32px;
        border: 1px solid #D8D8D8;
        input {
          border: 0;
          outline: 0;
          font-size: 12px;
          width: 100%;
          height: 100%;
          padding: 0 0 0 12px;
        }
        .search-icon {
          position: absolute;
          top: 6px;
          right: 12px;
          width: 20px;
          height: 20px;
          cursor: pointer;
        }
      }
    }
    .btn {
      color: #007AFF;
      cursor: pointer;
      margin-right: 4px;
    }
  }
  .table-wrap {
      height: calc(100vh - 54px - 78px - 50px);
      overflow-y: scroll;
  }
  .button-wrap {
    display: flex;
    margin-top: -7px;
    .create-btn {
      width: 53px;
      height: 34px;
      background: #007AFF;
      color: #fff;
      border: none;
      margin-right: 16px;
      font-size: 14px;
      border-radius: 4px;
      cursor: pointer;
    }
    .delete-btn {
      width: 53px;
      height: 34px;
      background: transparent;
      color: #007AFF;
      border: 1px solid #007AFF;
      font-size: 14px;
      border-radius: 4px;
      cursor: pointer;
    }
  }
}
</style>
<style lang="less">
.meeting-control-wrap {
  .el-table td {
    border-color: rgba(0,0,0,.1);
  }
  .el-table thead {
    background: #F5F5F5;
    color: #333333;
    font-size: 14px;
    font-weight: 500;
  }
    
}
</style>