<template>
  <div class="meeting-setting-wrap">
    <div class="head">
      {{ headerTitleText }}
    </div>
    <div class="main">
      <div class="handle-wrap">
        <ul class="tab-wrap">
          <li :class="['tab-item', {'active': activeType === 0}]" @click="filterList(0)">全部</li>
          <li :class="['tab-item', {'active': activeType === 2}]" @click="filterList(2)">正常</li>
          <li :class="['tab-item', {'active': activeType === 1}]" @click="filterList(1)">冻结</li>
        </ul>
        <div class="button-wrap">
          <button class="create-btn" @click="openDialog">新建</button>
          <!-- <button class="delete-btn" @click="deleteRoom">删除</button> -->
        </div>
      </div>
      <div class="title-wrap">
        <span>会议列表</span>
        <div class="index-wrap">
          <input type="text" v-model="searchVal" placeholder="输入会议室名称"  @keyup="search($event)">
          <img src="../../../../../assets/images/metting-search.png" class="search-icon" alt="" @click="search">
        </div>
      </div>
      <div class="table-wrap">
        <el-table
          :data="tableDataList"
          style="width: 100%"
          @selection-change="handleSelectionChange">
          <!-- <el-table-column
            type="selection"
            width="55">
          </el-table-column> -->
          <el-table-column
            label="会议室名称"
            >
            <template slot-scope="scope">{{ scope.row.RoomName }}</template>
          </el-table-column>
          <el-table-column
            label="会议室属性"
            >
            <template slot-scope="scope">{{ RoomTypeArr[scope.row.RoomType] }}</template>
          </el-table-column>
          <el-table-column
            label="负责人"
            >
            <template slot-scope="scope">{{ (memberDataObj[scope.row.LeaderUserId] || {}).RealName || '/' }}</template>
          </el-table-column>
          <el-table-column
            label="会议室描述"
            >
            <template slot-scope="scope">{{ scope.row.Description }}</template>
          </el-table-column>
          <el-table-column
            label="所属项目"
            >
            <template slot-scope="">/</template>
          </el-table-column>
          <el-table-column
            label="会议室所在地"
            >
            <template slot-scope="scope">{{ scope.row.RoomAddress }}</template>
          </el-table-column>
          <el-table-column
            label="会议状态"
            >
            <template slot-scope="scope">{{ scope.row.RoomStatus === 2 ? '正常' : '冻结' }}</template>
          </el-table-column>
          <el-table-column
            label="编辑"
            >
            <template slot-scope="scope">
                <span class="btn-text" @click="modify(scope.row)">编辑</span>
                <span class="btn-text" @click="deleteData(scope.row.Id)">删除</span>
                <span class="btn-text" @click="freeze(scope.row.Id, scope.row.RoomStatus)">{{scope.row.RoomStatus === 2 ? '冻结' : '启用'}}</span>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <el-dialog  :visible.sync="dialogFormVisible" width="500px">
        <div slot="title" class="dialog-header">
          新建会议室
        </div>
        <div class="dialog-body">
          <el-form :model="form">
            <el-form-item prop="RoomName">
              <div class="form-item">
                <div class="label">会议室名称 * :</div>
                <el-input v-model="form.RoomName" size="mini" autocomplete="off" placeholder="输入会议室名称" />
              </div>
            </el-form-item>
            <el-form-item>
              <div class="form-item">
                <div class="label">会议室属性</div>
                <el-select v-model="form.RoomType" size="mini" placeholder="实体会议室/虚拟会议室/组合会议室">
                  <el-option label="实体会议室" :value="0"></el-option>
                  <el-option label="虚拟会议室" :value="1"></el-option>
                  <el-option label="组合会议室" :value="2"></el-option>
                </el-select>
              </div>
            </el-form-item>
            <el-form-item>
              <div class="form-item">
                <div class="label">负责人</div>
                <el-select v-model="form.LeaderUserId" size="mini" placeholder="选择负责人">
                  <el-option
                    v-for="item in memberData"
                    :key="item.UserId"
                    :label="item.RealName"
                    :value="item.UserId">
                  </el-option>
                </el-select>
              </div>
            </el-form-item>
            <el-form-item>
              <div class="form-item">
                <div class="label">状态</div>
                <el-select v-model="form.status" size="mini" placeholder="正常/冻结">
                  <el-option label="冻结" :value="1"></el-option>
                  <el-option label="正常" :value="2"></el-option>
                </el-select>
              </div>
            </el-form-item>
            <el-form-item>
              <div class="form-item">
                <div class="label">会议室描述</div>
                <el-input
                  type="textarea"
                  placeholder="多行输入"
                  v-model="form.Description"
                  show-word-limit
                >
                </el-input>
              </div>
            </el-form-item>
            <el-form-item>
              <div class="form-item">
                <div class="label">会议室地点</div>
                <el-input
                  type="textarea"
                  placeholder="输入会议室地点"
                  v-model="form.RoomAddress"
                  show-word-limit
                >
                </el-input>
              </div>
            </el-form-item>
            <el-form-item>
              <div class="form-item">
                <div class="label">会议室缩略图</div>
                <input type="file"
                  id="id_add_fileimage"
                  accept="image/*" 
                  @change="EngineeringImageEdit($event)">
              </div>
            </el-form-item>
          
          </el-form>
          
        </div>
        <div slot="footer" class="dialog-footer">
          <el-button @click="dialogFormVisible = false" class="btn cancle-btn">取消</el-button>
          <el-button type="primary" @click="createRroom" class="btn save-btn">保存</el-button>
        </div>
      </el-dialog>
      
    </div>
  </div>
</template>

<script>
// 会议室管理模块
export default {
  name: '',

  data () {
    return {
      headerTitleText: '',
      searchVal: '',
      activeType: 0,
      dialogFormVisible: false,
       tableData: [],
        multipleSelection: [],
        RoomTypeArr: ['实体会议室', '虚拟会议室', '组合会议室'],
        form: {
          "Id": '',
          "ProjectId": "",
          "RoomName": "",
          "RoomAddress": "",
          "Thumbnail": "",
          "RoomType": '',
          "Description": "",
          "LeaderUserId": ""
        },
        memberData: [],
        memberDataObj: {},
        tableDataList: []
    }
  },
  created () {
    this.organizeId = this.$staticmethod._Get("organizeId");
    this.Token = this.$staticmethod.Get("Token");
    this.headerTitleText = this.$staticmethod._Get("menuText") || '';
    this.getMemberList()
  },

  methods: {
    getMemberList () {
      let url = `${this.$urlPool.GetUserPaged}?PageNum=1&PageSize=3000&KeyWord=&OrganizeId=${this.organizeId}&searchType=0&RoleId=&Token=${this.Token}`

      this.$axios.get(url).then(res => {
        if(res.data.Ret == 1){
          this.memberData = res.data.Data.list
          this.memberData.forEach(v => {
            this.memberDataObj[v.UserId] = v
          })
          this.getRoomList()
        }
      })
    },

    getRoomList () {
      let organizeId = this.$staticmethod._Get('organizeId')
      let UserId = this.$staticmethod.Get("UserId")
      this.$axios.get(window.bim_config.webserverurl+this.$urlPool.getRoomList, {
        params: {
          projectId: organizeId,
          status: this.activeType ? this.activeType : '',
          Token:this.$staticmethod.Get('Token')
        }
      }).then(res => {
        if(res.data.Ret == 1){
          this.tableData = res.data.Data
          this.tableDataList = res.data.Data
        }
      })
    },

    search () {
      if (!this.searchVal) {
        this.tableDataList = this.tableData
        return
      }
      const list = this.tableData.filter(v => {
        return v.RoomName.indexOf(this.searchVal) > -1
      })
      this.tableDataList = list
    },
    handleSelectionChange(val) {
      console.log(val, 13412421)
      this.multipleSelection = val;
    },
    filterList (type) {
      this.activeType = type
      this.getRoomList()
    },
    deleteData (id) {
      this.$confirm("确定删除当前会议", {
        confirmButtonText:'确定',
        cancelButtonText:'取消',
        type:'warning'
      }).then(() => {
        let UserId = this.$staticmethod.Get("UserId")
        this.$axios.post(window.bim_config.webserverurl+this.$urlPool.deleteRoom +  `?userId=${UserId}&id=${id}&Token=${this.$staticmethod.Get('Token')}`
        ).then(res => {
          if (res.data.Ret == 1) {
            this.$message({
              message: res.data.Msg,
            });
            this.getRoomList()
          } else {
            this.$message({
              message: res.data.Msg,
              type: 'warning'
            });
          }
        })
      }).catch((err) => {
        console.log(err)
      })

      
    },
    freeze (id, status) {
      let UserId = this.$staticmethod.Get("UserId")
      const url = status === 2 ? this.$urlPool.prohibitedRoom : this.$urlPool.enableRoom
      this.$axios.post(window.bim_config.webserverurl+url +  `?userId=${UserId}&id=${id}&Token=${this.$staticmethod.Get('Token')}`
      ).then(res => {
        if (res.data.Ret == 1) {
          this.$message({
            message: res.data.Msg,
            type: 'success'
          });
          this.getRoomList()
        } else {
          this.$message({
            message: res.data.Msg,
            type: 'warning'
          });
        }
      })
    },
    modify (data) {
      this.dialogFormVisible = true
      this.form = data
      this.form.status = data.RoomStatus
      console.log(data, 1414)
    },
    createRroom () {
      let UserId = this.$staticmethod.Get("UserId")
      this.$axios.post(window.bim_config.webserverurl+this.$urlPool.createRoom +  `?userId=${UserId}&Token=${this.$staticmethod.Get('Token')}`, 
      {
        ...this.form,
        ProjectId: this.organizeId
      }
      ).then(res => {
        if (res.data.Ret == 1) {
          this.$message({
            message: res.data.Msg,
            type: 'success'
          });
          this.dialogFormVisible = false
          this.form = {
            "Id": '',
            "ProjectId": "",
            "RoomName": "",
            "RoomAddress": "",
            "Thumbnail": "",
            "RoomType": '',
            "Description": "",
            "LeaderUserId": ""
          }
          this.getRoomList()
        } else {
          this.$message({
            message: res.data.Msg,
            type: 'warning'
          });
        }
      })
    },
    openDialog () {
      this.dialogFormVisible = true
    },
    deleteRoom () {
      console.log(this.multipleSelection, 141234)
    },
    EngineeringImageEdit(event) {
      console.log(event, 14124)
      let _this = this;
      let file = event.target.files[0];
      let fr = new FileReader();
      fr.readAsDataURL(file);
      // fr.onloadend = function(e) {
      //   _this.EngineeringImage = e.target.result;
      // };
      // 添加图片后，直接拿到图片，访问 UploadImage 接口，获取 bf_guid, bf_md5 和 bf_path
      var dominputfile = document.getElementById("id_add_fileimage");
      if (dominputfile.files.length == 0) {
        return;
      }
      var File = dominputfile.files[0];
      var _Token = _this.$staticmethod.Get("Token");
      var fd = new FormData();
      fd.append("FormFile", File); 
      fd.append("IssueId", '');
      var config = {
        headers: {
            "Content-Type": "multipart/form-data"
        }
      };
      _this.$axios
      .post(
        `${this.$issueBaseUrl.UploadImage}?token=${ _this.$staticmethod.Get("Token")}`,
        fd,
        config
      )
      .then(res =>{
        this.form.Thumbnail = `${window.bim_config.webserverurl}/${res.data.Data.bf_path}`
      })
    }
  }
}
</script>

<style lang='scss' scoped>
.meeting-setting-wrap {
  overflow-y: scroll;
  text-align: left;
  .head {
    height: 54px;
    background: #fff;
    font-size: 16px;
    line-height: 54px;
    font-weight: 500;
    color: rgba($color: #000000, $alpha: .9);
    padding-left: 16px;
    text-align: left;
  }
  .main {
    margin-left: 16px;
    margin-top: 16px;
    padding-top: 16px;
    height: 100%;
    width: 100%;
    background: #fff;
    .handle-wrap {
      border-bottom: 1px solid #E8E8E8;
      padding: 0 24px;
      display: flex;
      justify-content: space-between;
      .tab-wrap {
        display: flex;
        .tab-item {
          border-top-left-radius: 4px;
          border-top-right-radius: 4px;
          padding: 6px 20px;
          color: #fff;
          font-size: 14px;
          margin-right: 4px;
          background: rgba($color: #000000, $alpha: .25);
          cursor: pointer;
          &.active {
            background: #007AFF;
          }
        }
      }
    }
    .title-wrap {
      display: flex;
      justify-content: space-between;
      padding: 24px 24px 20px;
      span {
        color: #000;
        opacity: .9;
        font-size: 16px;
        font-weight: 500;
      }
      .index-wrap {
        position: relative;
        width: 240px;
        height: 32px;
        border: 1px solid #D8D8D8;
        input {
          border: 0;
          outline: 0;
          font-size: 12px;
          width: 100%;
          height: 28px;
          padding-left: 12px;
        }
        .search-icon {
          position: absolute;
          top: 6px;
          right: 12px;
          width: 20px;
          height: 20px;
          cursor: pointer;
        }
      }
    }
    .btn-text {
      color: #007AFF;
      cursor: pointer;
      margin-right: 4px;
    }
  }
  .table-wrap {
      height: calc(100vh - 54px - 78px - 50px);
      overflow-y: scroll;
  }
  .button-wrap {
    display: flex;
    margin-top: -7px;
    .create-btn {
      width: 53px;
      height: 34px;
      background: #007AFF;
      color: #fff;
      border: none;
      margin-right: 16px;
      font-size: 14px;
      border-radius: 4px;
      cursor: pointer;
    }
    .delete-btn {
      width: 53px;
      height: 34px;
      background: transparent;
      color: #007AFF;
      border: 1px solid #007AFF;
      font-size: 14px;
      border-radius: 4px;
      cursor: pointer;
    }
  }
  .dialog-header {
    color: #222;
    font-size: 16px;
    padding: 19px 0 0 24px;
    font-weight: 500;
  }
  .dialog-body {
    padding: 24px;
    box-sizing: border-box;
    .form-item {
      display: flex;
      align-items: center;
      .label {
        min-width: 83px;
        margin-right: 48px;
        text-align: left;
        font-size: 12px;
        color: #222;
      }
    }
    
  }
  .btn {
    border-color: #007AFF;
    font-size: 14px;
    padding: 10px 24px;
    border-radius: 4px;
  }
  .cancle-btn {
    color: #007AFF;
    background: #fff;
    margin-right: 8px;
  }
  .save-btn {
    background: #007AFF;
    color: #fff!important;
  }
}
</style>
<style lang="less">
.meeting-setting-wrap {
  .el-table td {
    border-color: rgba(0,0,0,.1);
  }
  .el-table thead {
    background: #F5F5F5;
    color: #333333;
    font-size: 14px;
    font-weight: 500;
  }
  .el-dialog__header {
    border-bottom: 1px solid rgba(0,0,0,.1);
    padding: 0;
  }
  .el-dialog__body {
    padding: 0;
  }
  .el-form-item__content {
    line-height: 28px;
  }
  .el-input {
    height: 28px;
    border: 1px solid #E5E5E5;
  }
  
    
}
.dialog-body {
  i.el-select__caret {

    width: 16px;
    /*很关键：将默认的select选择框样式清除*/  
    appearance:none;
    -moz-appearance:none;
    -webkit-appearance:none;
    /*为下拉小箭头留出一点位置，避免被文字覆盖*/  
    padding-right: 0;
    /*自定义图片*/  
    background: url("../../../../../assets/images/select-arrow.png") no-repeat scroll right center transparent;
    /*自定义图片的大小*/  
    background-size: 16px 16px;
    -webkit-transform: rotate(0)!important;
    transform: rotate(0)!important;
  }
  /*将小箭头的样式去去掉*/  
  .el-icon-arrow-up:before {
    content: '';
  }
  .el-select .el-input .el-select__caret.is-reverse {
    -webkit-transform: rotate(180deg)!important;
    transform: rotate(180deg)!important;
  }
  .el-select {
    flex: 1;
  }
}
</style>