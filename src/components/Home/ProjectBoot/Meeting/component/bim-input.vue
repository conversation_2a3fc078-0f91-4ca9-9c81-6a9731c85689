<template>
  <div class="bim-input-wrap">
    <el-select
      v-model="defaultVal"
      :multiple="multiple"
      :filterable="filter"
      :size="size"
      :placeholder="placeholder"
      :disabled="disabled"
      @change="change">
    <el-option
      v-for="item in dataList"
      :key="item[optionValue]"
      :label="item[optionLabel]"
      :value="item[optionValue]"
      :disabled="isMeetingRoom && !item.Available">
    </el-option>
  </el-select>

  </div>
</template>

<script>
export default {
  name: 'bimInput',
  props: {
    filter: {
      type: Boolean,
      default: true
    },
    multiple: {
      type: Boolean,
      default: true
    },
    placeholder: {
      type: String,
      default: '请选择'
    },
    size: {
      type: String,
      default: 'small'
    },
    dataList: {
      type: Array,
      default: () => {
        return [
        ]
      }
    },
    value: {
      type: [String, Number, Array],
      default: ''
    },
    optionLabel: {
      type: String,
      default: ''
    },
    optionValue: {
      type: String,
      default: ''
    },
    isMeetingRoom: Boolean,
    disabled: Boolean
  },

  data () {
    return {
      memberObj: {},
      defaultVal: ''
    }
  },
  mounted () {
    this.dataList.forEach(v => {
      this.memberObj[v.userId] = v
    })
    this.defaultVal = this.value
  },
  watch: {
    value (val) {
      this.defaultVal = val
    }
  },

  methods: {
    change (data) {
      this.$emit('updateData', data)
    }
  }
}
</script>

<style lang='less'>
.bim-input-wrap {
  .el-select {
    width: 100%
  }
  .el-input {
    // height: 28px;
    width: 100%;
    border: 1px solid #949090;
    border-radius: 2px;
  }
  i.el-select__caret {

    width: 20px;
    /*很关键：将默认的select选择框样式清除*/  
    appearance:none;
    -moz-appearance:none;
    -webkit-appearance:none;
    /*为下拉小箭头留出一点位置，避免被文字覆盖*/  
    padding-right: 0;
    /*自定义图片*/  
    background: url("../../../../../assets/images/metting-search.png") no-repeat scroll right center transparent;
    /*自定义图片的大小*/  
    background-size: 20px 20px;
    -webkit-transform: rotate(0)!important;
    transform: rotate(0)!important;
  }
  /*将小箭头的样式去去掉*/  
  .el-icon-arrow-up:before {
    content: '';
  }
  .el-select .el-input .el-select__caret.is-reverse {
    -webkit-transform: rotate(0)!important;
    transform: rotate(0)!important;
  }
}
</style>
