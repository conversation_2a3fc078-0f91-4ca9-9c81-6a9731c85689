<template>
  <div class="all-work-flow">
    <el-menu
      :default-active="activeIndex"
      class="el-menu-demo"
      mode="horizontal"
      @select="handleSelectBusiness"
    >
      <el-menu-item index="1">进行中</el-menu-item>
      <el-menu-item index="2">已完成</el-menu-item>
    </el-menu>
    <div class="work-conter">
      <iframe :src="startlistSrc" frameborder="0" width="100%" height="100%"></iframe>
    </div>
  </div>
</template>
<script>
export default {
  name: "AllWorkFlowTask",
  data() {
    return {
      activeIndex: '1',
      startlistSrc: '',
      cctoken: ''
    };
  },
  watch: {
  },
  computed: {},
	created(){},
  mounted(){
    this.getToken();
  },
  methods: {
    getToken() {
      let _this = this;
      _this.startlistSrc = '';
      let _OrganizeId = _this.$staticmethod._Get("organizeId");
      let _Token = _this.$staticmethod.Get("Token");
      let _url = `${window.bim_config.webserverurl}/api/User/User/CCFlowLogin`;
      let _para = {
        Token: _Token,
        organizeId: _OrganizeId,
      };
      _this
        .$axios({
          method: "post",
          url: _url,
          data: _para,
        })
        .then((x) => {
          if (x.data.Ret > 0) {
            _this.cctoken = x.data.Data;
            this.handleSelectBusiness('1')
          } else {
            _this.$message.error(x.data.Data);
          }
        })
        .catch((x) => {
          console.error(x);
        });
    },
    handleSelectBusiness(key) {
      this.activeIndex = key;
      this.startlistSrc = this.srcFunInit(key);
    },
    srcFunInit(index){
      let _src = ''
      let org = this.$staticmethod._Get("organizeId");

      switch(index){
        case '1':
          _src = `${window.bim_config.CCFlowUrl}/WF/Runing.htm?Token=${this.cctoken}&organizeId=${org}`
        break;
        case '2':
          _src = `${window.bim_config.CCFlowUrl}/WF/Complete.htm?Token=${this.cctoken}&organizeId=${org}`
          break;
        //在途  进行中 /WF/Runing.htm?Token=&organizeId="
      }
      // console.log(index,'==index',_src)
      return _src;
    },
  },
};
</script>
<style lang="less" scoped>
.all-work-flow{
  text-align: left;
	display: flex;
	flex-direction: column;
	height: 100%;
  .work-conter{
    flex: 1;
  }
}
</style>