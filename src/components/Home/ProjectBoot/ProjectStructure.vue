<template>
  <div class="structureContainer">
    <div class="headerWrapper">
      <div class="label">项目结构</div>
    </div>
    <div class="titleWrapper">
      <div class="left">
        <img src="../../../assets/images/structure-header.png" alt="">
        <span>项目结构</span>
      </div>
      <div class="right">
        <div class="btn" @click="isShowingImportTemplate = true">
          <img src="../../../assets/images/structure-import.png" alt="">
          <span>导入</span>
        </div>
        <div class="btn" @click="exportData">
          <img src="../../../assets/images/structure-export.png" alt="">
          <span>导出</span>
        </div>
      </div>
    </div>
    <div class="tableWrapper css-fixtreeicon">
      <el-table
        ref="treeTable"
        :data="tableData"
        @row-click="handleRowClick"
        :row-class-name="getRowClassName"
        :row-key="rowKey"
        :tree-props="{children: 'children', hasChildren: 'hasChildren',label: 'StructureName'}"
      >
        <!-- 名称列 -->
        <el-table-column label="目录名称" min-width="200" prop="StructureName">
          <template slot-scope="scope">
            <el-tooltip effect="dark" :content="scope.row.StructureName" placement="top">
              <span class="name-text">{{ scope.row.StructureName }}</span>
            </el-tooltip>
          </template>
        </el-table-column>
        <!-- 状态列 -->
        <el-table-column label="关联状态" min-width="200" prop="status">
          <template slot-scope="scope">
            <img src="../../../assets/images/structure-related.png" alt="" v-if="scope.row.ModelSceneId" style="width: 14px; height: 14px;">
            <span v-else> -- </span>
          </template>
        </el-table-column>
        <!-- 操作列 -->
        <el-table-column label="操作" min-width="200" >
          <template slot-scope="scope">
            <div class="operation-buttons">
              <el-popover
                ref="editPopover"
                placement="bottom-start"
                width="90"
                trigger="hover"
                popper-class="edit-menu-popover"
                @show="currentEditRow = scope.row"
                :hide-after="0"
                :close-delay="0"
              >
                <div class="edit-menu">
                  <div class="menu-item" @click="handleMenuClick('addChild', scope.row)">
                    <img class="menu-icon" src="../../../assets/images/pop-add.png" alt="">
                    <span>添加</span>
                  </div>
                  <div class="menu-item" @click="handleMenuClick('rename', scope.row)">
                    <img class="menu-icon" src="../../../assets/images/pop-rename.png" alt="">
                    <span>重命名</span>
                  </div>
                  <div class="menu-item" @click="handleMenuClick('upgrade', scope.row)">
                    <img class="menu-icon" src="../../../assets/images/pop-up-level.png" alt="">
                    <span>升级</span>
                  </div>
                  <div class="menu-item" @click="handleMenuClick('downgrade', scope.row)">
                    <img class="menu-icon" src="../../../assets/images/pop-down-level.png" alt="">
                    <span>降级</span>
                  </div>
                  <div class="menu-item" @click="handleMenuClick('moveUp', scope.row)">
                    <img class="menu-icon" src="../../../assets/images/pop-up.png" alt="">
                    <span>上移</span>
                  </div>
                  <div class="menu-item" @click="handleMenuClick('moveDown', scope.row)">
                    <img class="menu-icon" src="../../../assets/images/pop-down.png" alt="">
                    <span>下移</span>
                  </div>
                </div>
                <div class="operation-btn" slot="reference" @click.stop>
                  <img src="../../../assets/images/structure-edit.png" alt="">
                  <span>编辑</span>
                </div>
              </el-popover>
              <el-popover
                ref="relatePopover"
                placement="bottom-start"
                width="90"
                trigger="hover"
                popper-class="edit-menu-popover"
                @show="currentRelateRow = scope.row"
                :hide-after="0"
                :close-delay="0"
              >
                <div class="edit-menu">
                  <div class="menu-item" @click="handleMenuClick('relateToScene', scope.row)">
                    <img class="menu-icon" src="../../../assets/images/relate-to-scene.png" alt="">
                    <span>场景</span>
                  </div>
                  <div class="menu-item" @click="handleMenuClick('relateToModel', scope.row)">
                    <img class="menu-icon" src="../../../assets/images/relate-to-model.png" alt="">
                    <span>模型</span>
                  </div>
                </div>
                <div class="operation-btn" slot="reference" @click.stop>
                  <img src="../../../assets/images/structure-relate.png" alt="">
                  <span>关联</span>
                </div>
              </el-popover>
              <div class="operation-btn" @click.stop="handleDelete(scope.row)">
                <img src="../../../assets/images/structure-delete.png" alt="">
                <span>删除</span>
              </div>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <!-- 添加子级对话框 -->
    <zdialog-function
      init_title='新建子级'
      :init_zindex="1003"
      :init_innerWidth="350"
      :init_width="350"
      init_closebtniconfontclass="icon-suggested-close"
      @onclose="cancel"
      v-if="isAddShow"
    >
      <div
        slot="mainslot"
        class="_css-addingnameinput-ctn"
        @mousedown="_stopPropagation($event)"
      >
        <div class="_css-line _css-line-name">
          <div class="_css-title _css-title-flowname">子级名称：</div>
          <div class="_css-fieldvalue _css-fieldvaluename _css-selectednode">
            <el-input
              @mousedown="_stopPropagation($event)"
              placeholder="请输入子级名称"
              v-model="selectedRow.name"
            >
            </el-input>
          </div>
        </div>
        <div class="_css-line _css-line-name">
          <div class="_css-title _css-title-flowname">当前父级：</div>
          <div class="_css-fieldvalue _css-fieldvaluename _css-selectednode">
            <el-input
              disabled
              @mousedown="_stopPropagation($event)"
              placeholder="父级未选择"
              v-model="selectedRow.StructureName"
            >
            </el-input>
          </div>
        </div>
      </div>

      <div slot="buttonslot" class="_css-flowAddBtnCtn">
        <zbutton-function
          :init_text="'保存'"
          :init_fontsize="14"
          :debugmode="true"
          :init_height="'40px'"
          :init_width="'120px'"
          @onclick="addNewItem"
        >
        </zbutton-function>
        <zbutton-function
          :init_text="'取消'"
          :init_color="'rgba(24, 144, 255)'"
          :init_bgcolor="'#fff'"
          :init_fontsize="14"
          :debugmode="true"
          :init_height="'40px'"
          :init_width="'120px'"
          @onclick="cancel"
        >
        </zbutton-function>
      </div>
    </zdialog-function>
    <!-- 重命名对话框 -->
    <zdialog-function
      init_title='重命名'
      :init_zindex="1003"
      :init_innerWidth="350"
      :init_width="350"
      init_closebtniconfontclass="icon-suggested-close"
      @onclose="cancel"
      v-if="isRenameShow"
    >
      <div
        slot="mainslot"
        class="_css-addingnameinput-ctn"
        @mousedown="_stopPropagation($event)"
      >
        <div class="_css-line _css-line-name">
          <div class="_css-title _css-title-flowname">名称：</div>
          <div class="_css-fieldvalue _css-fieldvaluename _css-selectednode">
            <el-input
              @mousedown="_stopPropagation($event)"
              placeholder="请输入名称"
              v-model="selectedRow.StructureName"
            >
            </el-input>
          </div>
        </div>
      </div>

      <div slot="buttonslot" class="_css-flowAddBtnCtn">
        <zbutton-function
          :init_text="'保存'"
          :init_fontsize="14"
          :debugmode="true"
          :init_height="'40px'"
          :init_width="'120px'"
          @onclick="handleRename"
        >
        </zbutton-function>
        <zbutton-function
          :init_text="'取消'"
          :init_color="'rgba(24, 144, 255)'"
          :init_bgcolor="'#fff'"
          :init_fontsize="14"
          :debugmode="true"
          :init_height="'40px'"
          :init_width="'120px'"
          @onclick="cancel"
        >
        </zbutton-function>
      </div>
    </zdialog-function>
    <!-- 导入模板 -->
    <CompsStepTip6
      :zIndex="1000"
      v-if="isShowingImportTemplate === true"
      width="504"
      :bc_guid="selectedRowId ? selectedRowId : tableData[0].Id"
      @oncancel="isShowingImportTemplate = false"
      @onok="importFileSuccess"
      title="导入数据"
    ></CompsStepTip6>
    <!--关联模型组件-->
    <CompsSelectModel
      v-if="isShowRelateModel"
      @getModelInfo="relateModel($event,0)"
      @close="isShowRelateModel = false"
    ></CompsSelectModel>
    <!--关联场景组件-->
    <CompsSelectScene
      v-if="isShowRelateScene"
      @confirmRelatedScene="relateModel($event,1)"
      @close="isShowRelateScene = false"
    ></CompsSelectScene>

  </div>
</template>

<script>
import CompsStepTip6 from "../../CompsCommon/CompsStepTip6.vue";
import CompsSelectModel from "@/components/CompsMaterial/CompsSelectModel"
import CompsSelectScene from "@/components/CompsMaterial/CompsSelectScene"


export default {
  name: 'ProjectStructure',
  components: {
    CompsStepTip6,
    CompsSelectModel,
    CompsSelectScene
  },
  data() {
    return {
      isShowRelateScene: false,
      isShowRelateModel: false,
      isRenameShow : false,
      isShowingImportTemplate: false,
      addForm:{
        name:'',
        parentName:'',
      },
      renameForm:{
        name:'',
      },
      isAddShow: false,
      viewDialogVisible: false,
      currentEditRow: null,
      currentRelateRow: null,
      selectedRowId: '',
      selectedRow: {},
      rowKey: 'Id',
      tableData: [],
      expandedKeys: [] // 展开的节点key数组
    }
  },
  methods: {
    /**
     * 关联模型操作
     * @param obj
     * @param ConnectType
     */
    relateModel(obj,ConnectType){
      console.log('obj',obj)
      let data= {
        Id: this.selectedRow.Id,
        ConnectId: obj.modelid,
        Token:this.$staticmethod.Get('Token'),
        ConnectType,
        Extension:obj.extension
      }
      this.$axios.post(this.$MgrBaseUrl.ConnectStructure,data).then(res=>{
        if (res.data.Ret === 1) {
          this.$message.success('关联成功');
          this.isShowRelateModel = false;
          this.isShowRelateScene = false;
          this.getTreeList(0);
        } else {
          this.$message.error(res.data.Msg);
        }
      }).catch(error => {
        this.$message.error(error.message);
      });
    },
    /**
     * 更新项目结构
     */
    handleRename(){
      let data= {
        Id: this.selectedRow.Id,
        Token:this.$staticmethod.Get('Token'),
        StructureName: this.selectedRow.StructureName,
      }
      this.$axios.post(this.$MgrBaseUrl.UpdateProjectStructure,data).then(res=>{
        if (res.data.Ret === 1) {
          this.$message.success('重命名成功');
          this.isRenameShow = false;
          this.getTreeList(0);
        } else {
          this.$message.error(res.data.Msg);
        }
      }).catch(error => {
        this.$message.error(error.message);
      });
    },
    /**
     * 导出项目结构数据
     */
    exportData(){
      this.$axios({
        url: `${this.$MgrBaseUrl.ExportStructureTree}?Token=${this.$staticmethod.Get('Token')}&organizeId=${this.$staticmethod._Get("organizeId")}&parentId=${this.selectedRow.Id ? this.selectedRow.Id:''}`,
        method: 'get',
        responseType: 'blob'
      }).then(response => {
        const blob = new Blob([response.data], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
        const link = document.createElement('a');
        link.href = URL.createObjectURL(blob);
        link.download = '项目结构导出.xlsx';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
      }).catch(error => {
        this.$message.error(error.message);
      });
    },
    /**
     * 导入文件成功回调
     */
    importFileSuccess(){
      this.isShowingImportTemplate = false;
      this.getTreeList(0)
    },
    /**
     * 取消添加操作
     * 重置表单数据
     */
    cancel(){
      this.addForm = this.$options.data().addForm
      this.isAddShow = false
      this.renameForm.name = this.$options.data().renameForm
      this.isRenameShow = false
    },
    _stopPropagation(ev) {
      ev && ev.stopPropagation && ev.stopPropagation();
    },
    // 获取行类名
    getRowClassName({row, rowIndex}) {
      let className = 'table-row-cursor'
      if (row.Id === this.selectedRowId) {
        className += ' selected-row'
      }
      return className
    },
    // 获取状态类型
    getStatusType(status) {
      const statusMap = {
        'active': 'success',
        'inactive': 'info',
        'pending': 'warning',
        'error': 'danger'
      }
      return statusMap[status] || 'info'
    },

    // 处理行点击事件
    handleRowClick(row, column, event) {
      console.log('行点击:', row)
      this.selectedRowId = row.Id // 设置选中行ID
      this.selectedRow = row
    },

    // 处理排序变化
    handleSortChange(sortInfo) {
      console.log('排序变化:', sortInfo)
    },

    // 处理展开变化
    handleExpandChange(row, expanded) {
      console.log('展开变化:', row.StructureName, expanded)
      const key = row.Id

      if (expanded && !this.expandedKeys.includes(key)) {
        this.expandedKeys.push(key)
      } else if (!expanded && this.expandedKeys.includes(key)) {
        const index = this.expandedKeys.indexOf(key)
        this.expandedKeys.splice(index, 1)
      }
    },

    /**
     * 递归转换 Children 字段为 children
     * @param {Array} data - 需要转换的数据数组
     */
    convertChildrenFields(data) {
      if (!Array.isArray(data)) return data;

      return data.map(item => {
        const newItem = { ...item };

        // 如果存在 Children 字段，则转换为 children
        if (newItem.Children && newItem.Children.length > 0) {
          newItem.children = this.convertChildrenFields(newItem.Children);
          newItem.hasChildren = true; // 标记该节点有子节点
        } else {
          newItem.hasChildren = false; // 标记该节点没有子节点
        }
        // 删除原始的 Children 字段
        delete newItem.Children;
        return newItem;
      });
    },
/**
 * 根据 key 在树形数据中查找对应的行
 * @param {Array} data - 树形数据数组
 * @param {string|number} key - 要查找的 key 值
 * @returns {Object|null} 找到的行数据或 null
 */
findRowByKey(data, key) {
  if (!Array.isArray(data)) return null;

  for (let item of data) {
    // 如果当前项的 Id 匹配，返回该项
    if (item.Id === key) {
      return item;
    }

    // 如果有子节点，递归查找
    if (item.children && item.children.length > 0) {
      const found = this.findRowByKey(item.children, key);
      if (found) {
        return found;
      }
    }
  }

  return null;
},
    /**
     * 获取树形结构数据
     * @param {number} parentId - 父级ID
     */
    async getTreeList(parentId) {
      await this.$axios({
        url: `${this.$MgrBaseUrl.GetStructureTree}?Token=${this.$staticmethod.Get('Token')}&parentId=${parentId}&organizeId=${this.$staticmethod._Get("organizeId")}`,
        method: 'get',
      }).then(x => {
        if (x.data.Data) {
          // 处理树形数据结构，递归转换 Children 为 children
          this.tableData = this.convertChildrenFields(x.data.Data)
          console.log('tableData', this.tableData)
          // 确保数据更新后再处理展开状态
          this.$nextTick(() => {
            const els = this.$el.getElementsByClassName('el-table__expand-icon')
            for (let i = 0; i < els.length / 2; i++) {
              els[i].click()
            }
          })
        }
      }).catch(x => {
        this.$message.error(x);
      });
    },
    /**
     * 查看关联操作
     * @param row
     */
    handleRelate(row) {
      this.$message.success(`关联: ${row.name}`)
      console.log('关联:', row)
      this.selectedItem = row
      this.viewDialogVisible = true
    },

    // 编辑操作 - 已被弹出菜单替代
    handleEdit(row) {
      console.log('编辑:', row)
      this.$message.success(`编辑: ${row.name}`)
    },

    // 统一处理菜单点击事件
    handleMenuClick(action, row) {
      this.selectedRow = JSON.parse(JSON.stringify(row))
      console.log('selectedRow', this.selectedRow)
      // 关闭弹出框
      this.closeEditPopover()

      // 执行对应的操作
      switch (action) {
        case 'addChild':
          this.handleAddChild(row)
          break
        case 'rename':
          this.isRenameShow = true
          break
        case 'upgrade':
          // this.handleUpgrade(row)
          this.gradeItem(0)
          break
        case 'downgrade':
          // this.handleDowngrade(row)
          this.gradeItem(1)
          break
        case 'moveUp':
          this.moveItem(0)
          // this.handleMoveUp(row)
          break
        case 'moveDown':
          this.moveItem(1)
          // this.handleMoveDown(row)
          break
        case 'relateToScene':
          this.isShowRelateScene = true
          // this.handleRelateToScene(row)
          break
        case 'relateToModel':
          this.isShowRelateModel = true
          // this.handleRelateToModel(row)
          break
      }
    },

    // 关闭编辑弹出框
    closeEditPopover() {
      // 查找所有的 popover 实例并关闭
      this.$nextTick(() => {
        const popovers = this.$children.filter(child => child.$options.name === 'ElPopover')
        popovers.forEach(popover => {
          if (popover.showPopper) {
            popover.doClose()
          }
        })
      })
    },

    // 添加子项
    handleAddChild(row) {
      console.log('row',row)
      this.isAddShow = true
/*      this.$prompt('请输入新子项名称', '添加子项', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputPattern: /\S+/,
        inputErrorMessage: '名称不能为空'
      }).then(({ value }) => {
        const newId = this.getMaxId(this.tableData) + 1
        const newChild = {
          id: newId,
          name: value,
          description: `${row.name}的子项`,
          status: 'pending',
          createTime: Date.now(),
          level: row.level + 1
        }

        if (!row.children) {
          this.$set(row, 'children', [])
        }
        row.children.push(newChild)
        this.$message.success(`已添加子项: ${value}`)
      }).catch(() => {
        this.$message.info('已取消添加')
      })*/
    },
    // 升级（提升层级）
    handleUpgrade(row) {
      if (row.level === 0) {
        this.$message.warning('顶级项目无法升级')
        return
      }

      const parent = this.findParent(this.tableData, row.id)
      if (parent && parent.parent) {
        // 从当前父级移除
        const index = parent.parent.children.findIndex(item => item.id === row.id)
        if (index > -1) {
          parent.parent.children.splice(index, 1)

          // 添加到祖父级
          const grandParent = this.findParent(this.tableData, parent.parent.id)
          if (grandParent && grandParent.parent) {
            if (!grandParent.parent.children) {
              this.$set(grandParent.parent, 'children', [])
            }
            row.level = grandParent.parent.level + 1
            grandParent.parent.children.push(row)
          } else {
            // 提升到顶级
            row.level = 0
            this.tableData.push(row)
          }

          this.$message.success(`已升级: ${row.name}`)
        }
      }
    },

    // 降级（降低层级）
    handleDowngrade(row) {
      const siblings = this.getSiblings(this.tableData, row.id)
      const currentIndex = siblings.findIndex(item => item.id === row.id)

      if (currentIndex > 0) {
        const prevSibling = siblings[currentIndex - 1]

        // 从当前位置移除
        siblings.splice(currentIndex, 1)

        // 添加到前一个兄弟节点的子级
        if (!prevSibling.children) {
          this.$set(prevSibling, 'children', [])
        }
        row.level = prevSibling.level + 1
        prevSibling.children.push(row)

        this.$message.success(`已降级: ${row.name}`)
      } else {
        this.$message.warning('无法降级，没有可作为父级的前置节点')
      }
    },

    // 上移
    handleMoveUp(row) {
      const siblings = this.getSiblings(this.tableData, row.id)
      const currentIndex = siblings.findIndex(item => item.id === row.id)

      if (currentIndex > 0) {
        // 交换位置
        const temp = siblings[currentIndex]
        siblings[currentIndex] = siblings[currentIndex - 1]
        siblings[currentIndex - 1] = temp

        this.$message.success(`已上移: ${row.name}`)
      } else {
        this.$message.warning('已经是第一个，无法上移')
      }
    },

    // 下移
    handleMoveDown(row) {
      const siblings = this.getSiblings(this.tableData, row.id)
      const currentIndex = siblings.findIndex(item => item.id === row.id)

      if (currentIndex < siblings.length - 1) {
        // 交换位置
        const temp = siblings[currentIndex]
        siblings[currentIndex] = siblings[currentIndex + 1]
        siblings[currentIndex + 1] = temp

        this.$message.success(`已下移: ${row.name}`)
      } else {
        this.$message.warning('已经是最后一个，无法下移')
      }
    },

    // 查找父节点
    findParent(data, id, parent = null) {
      for (let item of data) {
        if (item.id === id) {
          return { item, parent }
        }
        if (item.children && item.children.length > 0) {
          const result = this.findParent(item.children, id, item)
          if (result) {
            return result
          }
        }
      }
      return null
    },

    // 获取兄弟节点数组
    getSiblings(data, id, parent = null) {
      for (let item of data) {
        if (item.id === id) {
          return parent ? parent.children : data
        }
        if (item.children && item.children.length > 0) {
          const result = this.getSiblings(item.children, id, item)
          if (result) {
            return result
          }
        }
      }
      return []
    },

    // 删除操作
    handleDelete(row) {
      console.log('删除:', row)
      this.$confirm(`确定要删除 "${row.StructureName}" 吗？`, '确认删除', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.deleteItem(row.Id)
        // this.removeItemFromTree(this.tableData, row.id)
        // this.$message.success('删除成功')
      }).catch(() => {
        // this.$message.info('已取消删除')
      })
    },

    // 从树形数据中移除项目
    removeItemFromTree(data, id) {
      for (let i = 0; i < data.length; i++) {
        if (data[i].id === id) {
          data.splice(i, 1)
          return true
        }
        if (data[i].children && data[i].children.length > 0) {
          if (this.removeItemFromTree(data[i].children, id)) {
            return true
          }
        }
      }
      return false
    },

    // 展开全部
    expandAll() {
      this.expandedKeys = this.getAllKeys(this.treeTableData)
      this.$message.success('已展开全部节点')
    },

    // 收起全部
    collapseAll() {
      this.expandedKeys = []
      this.$message.success('已收起全部节点')
    },

    // 获取所有节点的key
    getAllKeys(data) {
      let keys = []
      data.forEach(item => {
        if (item.children && item.children.length > 0) {
          keys.push(item.id)
          keys = keys.concat(this.getAllKeys(item.children))
        }
      })
      return keys
    },
    /**
     * 删除项目
     */
    deleteItem(Id) {
      console.log('selectedRow',this.selectedRow)
      let data= {
        Id,
        Token:this.$staticmethod.Get('Token'),
      }
      this.$axios.post(this.$MgrBaseUrl.DeleteProjectStructure,data).then(res=>{
        if (res.data.Ret === 1) {
          this.$message.success('删除成功');
          this.getTreeList(0);
        } else {
          this.$message.error(res.data.Msg);
        }
      }).catch(error => {
        this.$message.error(error.message);
      });
    },
    /**
     * 移动项目到其他位置
     */
    gradeItem(MoveType) {
      console.log('selectedRow',this.selectedRow)
      let data= {
        Id: this.selectedRow.Id,
        Token:this.$staticmethod.Get('Token'),
        MoveType
      }
      this.$axios.post(this.$MgrBaseUrl.UpDownGradeStructure,data).then(res=>{
        if (res.data.Ret === 1) {
          this.$message.success('修改成功');
          this.getTreeList(0);
        } else {
          this.$message.error(res.data.Msg);
        }
      }).catch(error => {
        this.$message.error(error.message);
      });
    },
    /**
     * 移动项目到其他位置
     */
    moveItem(MoveType) {
      console.log('selectedRow',this.selectedRow)
      let data= {
        Id: this.selectedRow.Id,
        Token:this.$staticmethod.Get('Token'),
        MoveType
      }
      this.$axios.post(this.$MgrBaseUrl.MoveStructure,data).then(res=>{
        if (res.data.Ret === 1) {
          this.$message.success('修改成功');
          this.getTreeList(0);
        } else {
          this.$message.error(res.data.Msg);
        }
      }).catch(error => {
        this.$message.error(error.message);
      });
    },
    // 添加新项
    addNewItem() {
      let data= {
        StructureName: this.selectedRow.name,
        ParentId: this.selectedRow.Id,
        Token:this.$staticmethod.Get('Token'),
        OrganizeId: this.$staticmethod._Get("organizeId"),
      }
      this.$axios.post(this.$MgrBaseUrl.CreateProjectStructure,data).then(res=>{
        if (res.data.Ret === 1) {
          this.$message.success('新增成功');
          this.isAddShow = false;
          this.getTreeList(0);
        } else {
          this.$message.error(res.data.Msg);
        }
      }).catch(error => {
        this.$message.error(error.message);
      });
    },
    /**
     * 导出树形结构数据
     */
    exportTree() {
      let data= {
        Id: this.selectedRowId,
        Token:this.$staticmethod.Get('Token'),
      }
      this.$axios.post(this.$MgrBaseUrl.UpdateProjectStructure,data).then(res=>{
        if (res.data.Ret === 1) {
          // 导出树结构
          window.open(this.$MgrBaseUrl.DownloadImportTemplate,'_blank') ;
        } else {
          this.$message.error(res.data.Msg);
        }
      }).catch(error => {
        this.$message.error(error.message);
      });
    },

    // 获取最大ID
    getMaxId(data) {
      let maxId = 0
      data.forEach(item => {
        if (item.id > maxId) {
          maxId = item.id
        }
        if (item.children && item.children.length > 0) {
          const childMaxId = this.getMaxId(item.children)
          if (childMaxId > maxId) {
            maxId = childMaxId
          }
        }
      })
      return maxId
    },

    // 格式化时间
    formatTime(time) {
      if (!time) return '--'
      return new Date(time).toLocaleString('chinese', { hour12: false })
    },

    // 获取状态文本
    getStatusText(status) {
      const statusMap = {
        'active': '激活',
        'inactive': '未激活',
        'pending': '待处理',
        'error': '错误'
      }
      return statusMap[status] || '未知'
    },

     /**
      * 展开所有节点
      */
     expandAllNodes() {
       // 获取所有有子节点的行的key
       const expandKeys = []
       const getAllExpandKeys = (data) => {
         data.forEach(item => {
           if (item.children && item.children.length > 0) {
             expandKeys.push(item.Id)
             getAllExpandKeys(item.children)
           }
         })
       }
       getAllExpandKeys(this.tableData)

       // 设置展开的keys
       this.expandedKeys = expandKeys
       console.log('自动展开的节点keys:', expandKeys)
     }
  },
  mounted() {
    this.getTreeList(0)
  }
}
</script>
<style lang="scss" scoped>
.structureContainer {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  .headerWrapper{
    position: sticky;
    top: 0;
    z-index: 10;
    flex: none;
    height: 54px;
    display: flex;
    align-items: center;
    background-color: #fff;
    border-bottom: 1px solid rgba(0, 0, 0, 0.15);
    font-size: 16px;
    .label {
      padding-left: 16px;
      font-weight: 500;
    }
  }
  .titleWrapper{
    margin-top: 12px;
    margin-left: 16px;
    margin-right: 16px;
    .left{
      margin-top: 5px;
      display: flex;
      align-items: center;
      justify-content: center;
      span{
        font-family: PingFangSC, PingFang SC;
        font-weight: 500;
        font-size: 16px;
        color: #222222;
      }
      img{
        width: 18px;
        height: 18px;
        margin-right: 8px;
      }
    }
    .right{
      display: flex;
      align-items: center;
      justify-content: flex-end;
      height: 100%;
      padding-right: 16px;
      .btn{
        margin-left: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 82px;
        height: 32px;
        background: #FFFFFF;
        border-radius: 4px;
        border: 1px solid #007AFF;
        cursor: pointer;
        transition: background-color 0.3s ease;

        &:hover {
          background-color: #E6E6E6;
        }

        img {
          width: 16px;
          height: 16px;
        }
        span{
          margin-left: 8px;
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          font-size: 14px;
          color: #007AFF;
          line-height: 20px;
          text-align: left;
          font-style: normal;
        }
      }
    }
  }
  .tableWrapper{
    height: calc(100% - 140px);
    margin-top: 21px;
    margin-left: 16px;
    margin-right: 16px;
    // 表格样式
    /deep/ .el-table {
      overflow: auto;
      height: 100%;
      width: 100%;
      border: 1px solid #e6e6e6;
      border-radius: 4px;

      &::before {
        display: none;
      }

      &::after {
        display: none;
      }

      // 添加表格内部边框
      .el-table__header th,
      .el-table__body td {
        text-align: center;
        vertical-align: middle;
        border-right: 1px solid #e6e6e6;
        border-bottom: 1px solid #e6e6e6;

        &:last-child {
          border-right: none;
        }
      }

      // 名称和描述列左对齐
      .el-table__header th:nth-child(1) .cell,
      .el-table__body td:nth-child(1) .cell,
      .el-table__header th:nth-child(1) .cell,
      .el-table__body td:nth-child(1) .cell {
        justify-content: flex-start;
      }

      // 表头样式
      .el-table__header-wrapper {
        height: 40px !important;
      }

      .el-table__header {
        background-color: #F5F5F5;
        height: 40px !important;
        line-height: 40px !important;

        th {
          height: 40px !important;
          line-height: 40px !important;
          background-color: #F5F5F5;
          color: #132B4D;
          font-weight: 500;
          font-size: 14px;
          padding: 0 !important;
          border-bottom: 1px solid #e6e6e6;
          vertical-align: middle;

          .cell {
            display: flex;
            align-items: center;
            justify-content: center;
            height: 40px;
            line-height: normal;
          }
        }
      }

      // 表体样式
      .el-table__body {
        tr {
          &:hover {
            background-color: #f5f7fa !important;
          }

          &.table-row-cursor {
            cursor: pointer;
          }

          // 选中行样式
          &.selected-row {
            background-color: #e6f7ff !important;

            td {
              background-color: #e6f7ff !important;
            }

            &:hover {
              background-color: #bae7ff !important;

              td {
                background-color: #bae7ff !important;
              }
            }
          }

          td {
            border-bottom: 1px solid #f0f0f0;

            .cell {
              line-height: normal;
              height: 40px!important;
              display: flex;
              align-items: center;
              justify-content: center;
            }
          }
        }
      }
    }

    // 覆盖全局树形图标样式，使用圆形加号减号
    &.css-fixtreeicon {
      // 隐藏原始图标
      /deep/ .el-table__expand-icon .el-icon {
        &::before {
          display: none !important;
        }
      }

      // 收缩状态 - 显示自定义图片
      /deep/ .el-table__expand-icon:not(.el-table__expand-icon--expanded)::after {
        content: '' !important;
        display: inline-block !important;
        width: 12px !important;
        height: 12px !important;
        background-image: url('../../../../src/assets/images/expand-str.png') !important;
        background-size: contain !important;
        background-repeat: no-repeat !important;
        background-position: center !important;
        transition: all 0.3s ease !important;
        position: absolute !important;
        top: 50% !important;
        left: 50% !important;
        transform: translate(-50%, -50%) !important;
      }

      // 展开状态 - 显示圆形减号
      /deep/ .el-table__expand-icon--expanded::after {
        content: '' !important;
        display: inline-block !important;
        width: 12px !important;
        height: 12px !important;
        background-image: url('../../../../src/assets/images/close-str.png') !important;
        background-size: contain !important;
        background-repeat: no-repeat !important;
        background-position: center !important;
        transition: all 0.3s ease !important;
        position: absolute !important;
        top: 50% !important;
        left: 50% !important;
        transform: translate(-50%, -50%) !important;
      }

      // 悬停效果
      /deep/ .el-table__expand-icon:hover::after {
        background-image: url('../../../../src/assets/images/expand-str.png') !important;
        opacity: 0.7 !important;
      }

      /deep/ .el-table__expand-icon--expanded:hover::after {
        background-image: url('../../../../src/assets/images/close-str.png') !important;
      }

      .el-table__expand-icon {
        width: 20px !important;

        > .el-icon {
          left: 0 !important;
          top: 0 !important;
          margin-left: 0 !important;
          margin-top: 0 !important;
        }
      }
    }

    // 文本样式
    .name-text {
      cursor: pointer;
      font-size: 14px;
      color: #132B4D;
      font-weight: 500;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    // 操作按钮样式
    .operation-buttons {
      display: flex;
      justify-content: center;
      align-items: center;
      .operation-btn {
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 20px;
        img{
          width: 16px;
          height: 16px;
          margin-right: 4px;
        }
        span{
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          font-size: 12px;
          color: #132B4D;
          line-height: 16px;
          text-align: left;
          font-style: normal;
        }
        &:hover {
          color: #66b1ff;
        }
      }
    }
  }
}
</style>
<style lang="scss">
.edit-menu-popover {
  min-width: 90px!important;
  padding: 8px 0 !important;
  border-radius: 6px !important;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1) !important;
  border: 1px solid #e4e7ed !important;

  .edit-menu {
    .menu-item {
      display: flex;
      align-items: center;
      padding: 8px 16px;
      cursor: pointer;
      transition: background-color 0.3s ease;
      font-size: 14px;
      color: #606266;

      &:hover {
        background-color: #f5f7fa;
        color: #409eff;
      }

      .menu-icon {
        margin-right: 4px;
        width: 14px;
        height: 14px;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      span {
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        font-size: 12px;
        color: #132B4D;
        line-height: 16px;
        text-align: left;
        font-style: normal;
        &:hover {
          color: #007AFF;
        }
      }
    }
  }
}
/* //插件覆盖（class名以el开头） */
</style>
