<template>
	<div class="_css-materials-share-all" @click.stop="close()">
		<div class="_css-tabcontrolarea-sign bgfff">
			<div class="_css-sign-text">签章管理</div>
		</div>
		<div class="_css-bottomarea">
			<div class="sign-left bgfff">
				<div class="roles-list">
					<ul>
						<li
							@click.stop="handelRolesList(role)"
							v-for="role in rolesList"
							:key="role.RoleId"
              class="role-list-css"
              :class="roleRoleIdActive.RoleId  == role.RoleId ? 'role-active' : ''"
						>
							<i class="_css-left-role-icon icon-interface-team"></i>
							<span>{{ role.FullName }}</span>
              <!-- <i 
                class="signature-css" 
                @click.stop="handelIconClick(role.SignAddress,'0')"
                :class="role.SignAddress.length == 0 ? 'icon-signature' : 'icon-signature-check'"></i> -->
              <div
                class="_css-left-role-rpart"
                @click.stop="showrolecontextmenu(role,$event)"
              >
                <div
                  class="signature-css"
                  :class="role.SignAddress && role.SignAddress.length == 0 ? 'icon-signature' : 'icon-signature-check'">
                </div>
                <div class="sign-edit" v-if="roleEditShow" :style="getStyleMenu()">
                  <p v-if="clickRoleItem.SignAddress && clickRoleItem.SignAddress.length>0" @click.stop="handelIconClick(clickRoleItem.SignAddress,'0')">
                    <i class="signature-css" :class=" clickRoleItem.SignAddress && clickRoleItem.SignAddress.length == 0 ? 'icon-signature' : 'icon-signature-check'"></i>
                    <span>查看签章</span>
                  </p>
                  <p @click.stop="uploadSignatureInput(clickRoleItem,'0')">
                    <i class="signature-css" :class="clickRoleItem.SignAddress && clickRoleItem.SignAddress.length == 0 ? 'icon-interface-cloud-upload' : 'icon-interface-cloud-download_could'"></i>
                    <span>{{ clickRoleItem.SignAddress && clickRoleItem.SignAddress.length > 0 ? '更新' : '上传' }}</span>
                  </p>
                  <p @click.stop="deleteSign(clickRoleItem,'0')" v-if="clickRoleItem.SignAddress && clickRoleItem.SignAddress.length > 0 ">
                    <i class="signature-css icon-interface-model-delete"></i>
                    <span>删除</span>
                  </p>
                </div>
              </div>
						</li>
					</ul>
				</div>
			</div>
			<div class="sign-right bgfff">
				<el-table :data="roleUserList"  :key="Math.random()" style="width: 100%" class="sign-table">
					<el-table-column prop="RealName" label="角色成员" width="180">
					</el-table-column>
					<el-table-column prop="SignAddress" label="签名" width="180">
            <template  slot-scope="scope">
              <i 
                class="signature-css no-cursor" 
                :class="scope.row.SignAddress && scope.row.SignAddress.length == 0 ? 'icon-signature' : 'icon-signature-check'"></i>
            </template>
					</el-table-column>
					<el-table-column prop="" label="缩略图">
            <template  slot-scope="scope">
              <div class="" @click.stop="handelIconClick(scope.row.SignAddress,'0')">
                <img width="auto" height="100px" v-if="scope.row.SignAddress && scope.row.SignAddress.length != 0" :src="handelIconClick(scope.row.SignAddress,'1')" alt="">
              </div>
            </template>
          </el-table-column>
					<el-table-column prop="" label="操作">
            <template  slot-scope="scope">
              <div class="sign-btn" @click.stop="uploadSignatureInput(scope.row,'1')">{{ scope.row.SignAddress && scope.row.SignAddress.length > 0 ? '更新' : '上传' }}</div>
              <div class="sign-btn" v-if="scope.row.SignAddress && scope.row.SignAddress.length > 0" @click.stop="deleteSign(scope.row,'1')">删除</div>
            </template>
          </el-table-column>
				</el-table>
			</div>
		</div>
    <input style="display:none" id="id_file_Signature" type="file"  @change="uploadSignature" accept=".png" />
    <div class="big-img" v-if="showBigImg.show">
      <i class="big-icon-close icon-suggested-close" @click.stop="close()"></i>
      <img :src="handelIconClick(showBigImg.src,'1')" alt="">
    </div>
	</div>
</template>
<script>
export default {
	name: "projectSignature",
	data() {
		return {
			rolesList: [], // 角色
			roleUserList: [], // 角色成员
      roleRoleIdActive: {},
      roleEditShow: false,
      signatureItem: {},
      menuStyle: {
        left: 0,
        top:0
      },
      roleImg: false, // 角色的电子签章图片
      clickRoleItem: {}, // 点击的角色
      showBigImg: {
        show: false,
        src: ''
      }, // 点击查看大图
		};
	},
	mounted() {
		this.getRoles();
	},
	methods: {
    close(){
      this.roleEditShow = false;
      this.showBigImg.show = false;
    },
		getRoles() {
			let _this = this;
			let _organizeId = _this.$staticmethod._Get("organizeId");
			let _token = _this.$staticmethod.Get("Token");
			_this
				.$axios({
					method: "get",
					url: `${window.bim_config.webserverurl}/api/User/Role/GetRoles?organizeId=${_organizeId}&token=${_token}`,
				})
				.then((x) => {
					if (x.status == 200 && x.data.Ret > 0) {
						_this.rolesList = x.data.Data;
					} else {
            _this.rolesList = []
						console.warn(x);
					}
				})
				.catch((err) => {});
		},
		// 点击获取当前角色下成员
		handelRolesList(role) {
      // console.log(role,'=role')
      this.showBigImg.show = false;
      this.roleRoleIdActive = role;
      let _Token = this.$staticmethod.Get("Token"); 
      let organizeId = this.$staticmethod._Get("organizeId");
      let Url = `${this.$urlPool.GetUserPaged}?PageNum=1&PageSize=3000&KeyWord=&OrganizeId=${organizeId}&searchType=0&RoleId=${role.RoleId}&Token=${_Token}`

			this.$axios
				.get(Url)
				.then((x) => {
					if (x.status == 200 && x.data.Ret > 0) {
						this.roleUserList = x.data.Data.list;
					} else {
						this.roleUserList = [];
					}
				})
				.catch((err) => {});
		},
    handelIconClick(item,type){
      if(item.length == 0) return
      if(type && type=='0'){
        this.showBigImg.src = item
        this.showBigImg.show = true;
      }
      let imgUrl = `${window.bim_config.webserverurl}${item}?r=${Math.random() * 100000 + 1}`
      return imgUrl;
    },
    showrolecontextmenu(item,ev){
      this.clickRoleItem = item;
      this.menuStyle.left = ev.clientX + 13+ 'px';
      this.menuStyle.top = ev.clientY - 10 + 'px';
      this.roleEditShow = true;
    },
    getStyleMenu(){
      let _s={};
      _s['left'] = this.menuStyle.left;
      _s['top'] = this.menuStyle.top;
      return _s;
    },
    uploadSignatureInput(item,type){
      this.roleEditShow = false;
      if(type == '0'){
        this.signatureItem = {
          OrganizeId: item.OrganizeId,
          roleId: item.RoleId,
          Type: type,
          UserId: ''
        }
      }else{
        this.signatureItem = {
          OrganizeId: this.roleRoleIdActive.OrganizeId,
          roleId: this.roleRoleIdActive.RoleId,
          Type: type,
          UserId: item.UserId
        }
      }
      let dom = document.getElementById('id_file_Signature');
      dom.value = '';
      dom.click();
    },
    // 电子签章
    uploadSignature(){
      let _this = this;
      const dom = document.getElementById('id_file_Signature');
      const form = new FormData()
      const fileObj = dom.files;
      form.append('File', fileObj[0])
      form.append('OrganizeId', this.signatureItem.OrganizeId)
      form.append('Type', this.signatureItem.Type)
      form.append('RoleId', this.signatureItem.roleId)
      form.append('UserId', this.signatureItem.UserId)
      form.append('Token', this.$staticmethod.Get("Token"))
      
      let uploadurl = `${window.bim_config.webserverurl}/api/User/User/UploadElectronicSignature`
      let config = {
        headers:  { 'Content-Type': 'multipart/form-data' }
      }
      _this.$axios
        .post(
          uploadurl,
          form,
          config,
        )
        .then(res=>{
          if (res.data.Ret == 1) {
            _this.$message.success(res.data.Msg);
            _this.handelRolesList(this.roleRoleIdActive);
            if(this.signatureItem.Type == '0'){
              this.roleEditShow = false
              this.getRoles();
            }

          } else {
            _this.$message.error(res.data.Msg)
          }
        })
        .catch(err => {
          console.log(err)
        })
    },
    // 删除电子签章
    deleteSign(item,type){
      let str = type==0 ? '签章' : '签名'
      this.$confirm(`确定删除当前${str}`, {
        confirmButtonText:'确定',
        cancelButtonText:'取消',
        type:'warning'
      }).then(() => {
        this.delRoleConfirm(item,type);
      }).catch((err) => {
        console.log(err)
      })
      
      // 
    },
    delRoleConfirm(item,type){
      let _this = this;
      let signatureItem = {}
      if(type == '0'){
        signatureItem = {
          OrganizeId:this.$staticmethod._Get("organizeId"),
          RoleId: item.RoleId,
          UserId: ''
        }
      }else{
        signatureItem = {
          OrganizeId:this.$staticmethod._Get("organizeId"),
          RoleId: this.roleRoleIdActive.RoleId,
          UserId: item.UserId
        }
      }
      const form = new FormData()
      form.append('OrganizeId',signatureItem.OrganizeId)
      form.append('RoleId',signatureItem.RoleId)
      form.append('Token', this.$staticmethod.Get("Token"))
      form.append('UserId',signatureItem.UserId)
      
      let delurl =  `${window.bim_config.webserverurl}/api/User/User/RemoveElectronicSignature`
      let config = {
        headers:  { 'Content-Type': 'multipart/form-data' }
      }
      _this.$axios
        .post(
          delurl,
          form,
          config,
        )
        .then(res=>{
          if (res.data.Ret == 1) {
            _this.$message.success(res.data.Msg);
            _this.handelRolesList(this.roleRoleIdActive);
            if(type == '0'){
              this.roleEditShow= false
              _this.getRoles();
            }
          } else {
            _this.$message.error(res.data.Msg)
          }
        })
        .catch(err => {
          console.log(err)
        })
    },
    delRoleConfirm1(item,type){
      let signatureItem = {}
      if(type == '0'){
        signatureItem = {
          OrganizeId:this.$staticmethod._Get("organizeId"),
          RoleId: item.RoleId,
          UserId: ''
        }
      }else{
        signatureItem = {
          OrganizeId:this.$staticmethod._Get("organizeId"),
          RoleId: this.roleRoleIdActive.RoleId,
          UserId: item.UserId
        }
      }
      this.$axios({
          method: "post",
          url: `${window.bim_config.webserverurl}api/User/User/RemoveElectronicSignature?Token=${this.$staticmethod.Get('Token')}`,
          data: signatureItem
        })
        .then(res=>{
          if (res.data.Ret == 1) {
            this.$message.success(res.data.Msg);
          } else {
            this.$message.error(res.data.Msg)
          }
        })
        .catch(err => {
          console.log(err)
        })
    }
	},
};
</script>
<style lang="scss" scoped>
._css-materials-share-all {
	height: 100%;
	display: flex;
	flex-direction: column;
}
._css-tabcontrolarea-sign {
	height: 54px;
	display: flex;
	align-items: center;
	flex: none;
	border-bottom: 1px solid rgba(0, 0, 0, 0.15);
	._css-sign-text {
		color: rgba(0, 0, 0, 0.9);
		margin-left: 16px;
		font-size: 16px;
    font-weight: 500;
	}
}
._css-bottomarea {
	flex: 1;
	display: flex;
	height: calc(100% - 54px);
	margin: 24px;
	.sign-left {
		width: 200px;
		height: 100%;
		border-radius: 2px 0 0 2px;
		margin-right: 20px;
		.roles-list {
			li {
				text-align: left;
				line-height: 50px;
				display: flex;
				align-items: center;
				span {
					display: inline-block;
					margin-left: 8px;
					text-align: left;
					width: 112px;
					font-size: 14px;
					overflow-x: hidden;
					text-overflow: ellipsis;
					white-space: nowrap;
					cursor: pointer;
				}
			}
      li:hover, .role-active{
        background: rgba(0,0,0,.04);
      }
		}
	}
	._css-left-role-icon {
		width: 16px;
		height: 16px;
		margin-left: 24px;
	}
	.sign-right {
		flex: 1;
	}
}
.bgfff {
	background-color: #fff;
}
.signature-css{
  margin-right: 5px;
  width: 16px;
  height: 16px;
  cursor: pointer;
}
.no-cursor{
  cursor: auto;
}
.icon-signature{
  background-image: url('../../../assets/images/icon-signature.png');
  background-repeat: no-repeat;
  background-size: 100%;
}
.icon-signature-check{
  background-image: url('../../../assets/images/icon-signature-check.png');
  background-repeat: no-repeat;
  background-size: 100%;
}
._css-left-role-rpart{
  margin: 0 5px 0 5px;
  cursor: pointer;
}
.sign-edit{
  z-index: 2;
  position: absolute; 
  background: #fff;
  padding:10px 0;
  border-radius: 4px;
  box-shadow: 0px 1px 2px 0px rgba(45, 56, 66, .05);
  p{
    padding-left: 20px;
    display: flex;
    align-items: center;
    line-height: 40px;
  }
  p:first-child{
    border-bottom: 1px solid rgba(86, 98, 112, 0.1);
  }
  p:hover{
    color: #1890FF;
    background: rgba(86, 98, 112, 0.1);
  }
}
/deep/ .el-table td{
  border-bottom-color:rgba(0,0,0, 0.08) !important;
  border-right-color:rgba(0,0,0, 0.08) !important;
}
/deep/ .el-table .cell{
  justify-content: center;
}
/deep/ .el-table th{
  text-align: center;
}
.sign-btn{
  margin-right: 15px;
}
.sign-btn:hover{
  cursor: pointer;
  color: #1890ff;
}
.click-css{
  cursor: pointer;
}
._css-left-role-rpartbtn{
  color: #606266;
}
.big-img{
  position: fixed;
  top: 20%;
  left: 50%;
  margin-left: -15%;
  max-width: 700px;
  height: 400px;
  background: #fff;
  box-shadow: 0 13px 24px -17px rgba(11, 41, 62, .8);
  border-radius: 4px;
  z-index: 1;
  .big-icon-close{
    position: absolute;
    right: 10px;
    top: 10px;
    color: #000;
    font-size: 16px;
    cursor: pointer;
  }
  img{
    height: 400px;
    width: auto;
  }
}
</style>
