<template>
    <div class="_css-costmgr-all">

        <!-- 顶部留出空白，下方左树右表 -->
        <!-- ------------------------ -->

        <!-- 顶部的空白区域，可能会加按钮和搜索框，待定 -->
        <div class="_css-costmgr-top">
            <span v-if="!fromFullScreen">{{ headerTitleText }}</span>
        </div>
        <!-- //顶部的空白区域，可能会加按钮和搜索框，待定 -->

        <!-- 下方的左树右表区域，抽出组件 -->
        <div class="_css-costmgr-body">

            <!-- 引入自定义业务组件 -->
            <CompsCostMgr></CompsCostMgr>
            <!-- //引入自定义业务组件 -->

        </div>
        <!-- //下方的左树右表区域，抽出组件 -->

        <!-- ------------------------ -->
        <!-- //顶部留出空白，下方左树右表 -->


    </div>
</template>
<script>
import CompsCostMgr from "@/components/CompsCostMgr/CompsCostMgr";
export default {
    components:{
        CompsCostMgr
    },
    data(){
        return {
            fromFullScreen:false,
            headerTitleText: '',
        };
    },
   
    mounted(){

        // 菜单自动选中
        // -----------
        var _this = this;
        this.headerTitleText = this.$staticmethod._Get("menuText") || '';

        _this.$emit("onmounted", "costmgr");
        let query = this.$route.query;
        if (query.sign && query.sign == 'fullScreen') {
            this.fromFullScreen = true;
        } 
    },
    methods:{
    }
}
</script>
<style scoped>
._css-costmgr-body {
    height: calc(100% - 54px);
    box-sizing: border-box;
    padding: 16px;
}
._css-costmgr-top {
    padding-left: 16px;
    color: rgba(0,0,0,.9);
    height: 54px; 
    display: flex;
    font-size: 16px;
    font-weight: 500;
    align-items: center;
    background-color: #fff;
    border-bottom: 1px solid rgba(0,0,0,.15);
}
._css-costmgr-all {
    height:100%;
}
</style>