<template>
  <div class="msr-admin-wp">
    <comps-screen-dialog @closeCallBack="closeCallBack" title="标签维护">
      <div slot="main" style="background: rgba(247, 247, 247, 1)">
        <div class="mgradmin-wp">
          <header>
            <span>标签维护</span>
            <div @click="addMaterial">
              <i class="icon-interface-addnew"></i>
              添加新标签
            </div>
          </header>
          <main data-debug="mgradmin">
            <ul class="_css_bugs_and_bb_noWork css-miniscroll">
              <li v-for="item in list" :key="item.LabelId">
                <div :style="{ background: item.BackGroundColor }" style="color: #fff">
                  {{ item.LabelName }}
                </div>
                <div
                  class="edit"
                  :style="{ background: item.BackGroundColor }"
                  style="color: #fff"
                  @click="materielListClick(item)"
                >
                  <i class="icon-interface-edit_se"></i>
                </div>
              </li>
            </ul>
          </main>
        </div>
      </div>
    </comps-screen-dialog>
    <div
      class="maker"
      @click="dialogConfig.show = false"
      v-show="dialogConfig.show"
    >
      <div class="center" @click.stop>
        <header>
          <span>{{ dialogConfig.type == 0 ? "添加新标签" : "修改标签" }}</span>
          <i
            class="icon-suggested-close"
            @click="dialogConfig.show = false"
          ></i>
        </header>
        <main>
          <p>标签名称</p>
          <el-input
            v-model="dialogConfig.input"
            placeholder="请输入标签名称"
          ></el-input>
          <div class="color-list">
            <div
              v-for="item in colorList"
              :key="item"
              :style="{ background: item }"
              @click="colorListClick(item)"
            >
              <i
                class="icon-suggested-check"
                v-show="dialogConfig.selectColor == item"
              ></i>
            </div>
          </div>
          <div class="btn-wp">
            <div
              :style="{
                visibility: dialogConfig.type == 0 ? 'hidden' : 'visible',
              }"
              @click="predel()"
            >
              删除标签
            </div>
            <div class="btns-right">
              <div @click="cancelediting">取消</div>
              <div @click="submit">确定</div>
            </div>
          </div>
        </main>
      </div>
    </div>
  </div>
</template>
<script>
import compsScreenDialog from "../../CompsDialog/CompsFullScreenDialog";
import modifyDialog from "../../CompsCommon/CompsModifyDicItem";
export default {
  data() {
    return {
      Token: "",
      ProjectID: "",
      list: [],
      dialogConfig: {
        show: false,
        input: "",
        id: "",
        selectColor: "",
        type: 0, //0新增
      },
      colorList: [
        "rgba(230, 126, 126, 1)",
        "rgba(192, 113, 86, 1)",
        "rgba(242, 174, 36, 1)",
        "rgba(87, 130, 217, 1)",
        "rgba(0, 170, 255, 1)",
        "rgba(3, 173, 173, 1)",
        "rgba(35, 173, 104, 1)",
      ],
    };
  },
  components: { compsScreenDialog, modifyDialog },
  created() {},
  methods: {
    cancelediting() {
      var _this = this;
      _this.dialogConfig.show = false;
    },

    closeCallBack() {
      this.$router.push({ name: "Pano", params: { Token: this.Token } });
    },
    colorListClick(item) {
      this.dialogConfig.selectColor = item;
    },
    materielListClick(item) {
      console.log(item);
      this.dialogConfig.selectColor = item.BackGroundColor;
      this.dialogConfig.input = item.LabelName;
      this.dialogConfig.type = 1;
      this.dialogConfig.id = item.LabelId;
      this.dialogConfig.show = true;
    },
    addMaterial() {
      this.dialogConfig.selectColor = "rgba(230, 126, 126, 1)";
      this.dialogConfig.input = "";
      this.dialogConfig.type = 0;
      this.dialogConfig.show = true;
    },
    submit() {
      if (this.dialogConfig.input.length > 0) {
					let _labelId = "";let _msg = "";
					this.dialogConfig.type == 0? _labelId = "" :  _labelId = this.dialogConfig.id;
					this.dialogConfig.type == 0? _msg = "添加成功" :  _msg = "修改成功";
          let data = {
						"labelId": _labelId,
						"organizeId": this.ProjectID,
						"backGroundColor": this.dialogConfig.selectColor,
						"fontColor": 'rgba(255, 255, 255, 1)',
						"labelName": this.dialogConfig.input,
						"labelSort": 0
          };
          this.$axios
            .post(
              `${window.bim_config.webserverurl}/api/Panorama/Label/Save?Token=${this.$staticmethod.Get('Token')}`,
              data
            )
            .then((res) => {
              if (res.data.Ret != -1) {
                this.$message({
                  message: _msg,
                  type: "success",
                });
                this.getListData();
                this.dialogConfig.show = false;
              } else {
                this.$message.error(res.data.Msg);
              }
            })
						.catch(()=>{
							this.$message.error("服务器异常，请稍后再试");
						});
      } else {
        this.$message.error("请输入标签名称");
      }
    },
    predel() {
      var _this = this;
      _this
        .$confirm("确认删除该状态？", "操作确认", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        })
        .then((x) => {
          _this.del();
        })
        .catch((x) => {});
    },
    del() {
      let data = {
        labelId: this.dialogConfig.id,
      };
      this.$axios
        .delete(
          `${window.bim_config.webserverurl}/api/Panorama/Label/DelLabel?Token=${this.$staticmethod.Get('Token')}`,
					{	
						params: data
    			}
        )
        .then((res) => {
          if (res.data.Ret == 1) {
            this.$message({
              message: "删除成功",
              type: "success",
            });
            this.getListData();
            this.dialogConfig.show = false;
          }else {
						this.$message.error(res.data.Msg);
					}
        })
				.catch(()=>{
					this.$message.error('服务器异常，请稍后再试');
				});
    },
    getListData() {
			this.$axios
        .get(
          `${window.bim_config.webserverurl}/api/Panorama/Label/GetList?OrganizeId=${this.ProjectID}&Token=${this.$staticmethod.Get('Token')}`
        )
        .then((res) => {
					if (res.data.Ret == 1) {
            this.list = res.data.Data;
          }else {
						this.$message.error(res.data.Msg);
					}
          
        });
    },
  },
  mounted() {
    this.ProjectID = this.$staticmethod._Get("organizeId");
    this.Token = this.$staticmethod.Get("Token");
    // 设置左侧菜单自动选中
    var _this = this;
    _this.$emit("onmounted", "Pano");
    this.getListData();
  },
};
</script>
<style scoped>
._css_bugs_and_bb_noWork {
  overflow-y: auto;
}
</style>
<style lang="stylus" scoped rel="stylesheet/stylus">
.maker {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1001;

  .center {
    width: 410px;
    height: 274px;
    background: #ffffff;
    border-radius: 4px;
    box-shadow: 0px 13px 24px -17px rgba(11, 41, 62, 0.8);
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    display: flex;
    flex-direction: column;

    header {
      height: 64px;
      line-height: 64px;
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      align-items: center;
      padding: 0 24px;

      span {
        font-size: 20px;
        color: rgba(0, 0, 0, 0.85);
      }

      i {
        color: #bfbfbf;
        cursor: pointer;
      }
    }

    main {
      padding: 0 24px;
      flex: 1;

      .btn-wp {
        display: flex;
        flex-direction: row;
        justify-content: space-between;

        &>div {
          color: red;
          line-height: 40px;
          cursor: pointer;

          &.btns-right {
            display: flex;
            flex-direction: row;

            div {
              color: #ffffff;
              width: 76px;
              height: 40px;
              line-height: 40px;
              background: #1890FF;
              border-radius: 2px;
              margin-left: 16px;
              opacity: 0.8;

              &:hover {
                opacity: 1;
              }

              &:first-child {
                color: rgba(0, 0, 0, 0.85);
                background: #fff;
              }
            }
          }
        }
      }

      p {
        text-align: left;
        margin-top: 10px;
        font-size: 12px;
        color: rgba(0, 0, 0, 0.65);
      }

      .color-list {
        height: 80px;
        display: flex;
        flex-direction: row;
        align-items: center;

        div {
          width: 40px;
          height: 40px;
          border-radius: 4px;
          cursor: pointer;
          margin-left: 12px;
          display: flex;
          align-items: center;
          justify-content: center;
          color: #fff;

          &:first-child {
            margin-left: 5px;
          }
        }
      }
    }
  }
}

.mgradmin-wp {
  width: 800px;
  display: flex;
  height: calc(100% - 64px);
  flex-direction: column;
  margin: auto;

  header {
    margin-top: 24px;
    height: 68px;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    border-bottom: 1px solid rgba(0, 0, 0, 0.09);
    background: #fff;

    &>span {
      font-size: 16px;
      color: rgba(0, 0, 0, 0.85);
    }

    div {
      color: #1890FF;
      cursor: pointer;
      font-size: 14px;
    }
  }

  main {
    height: calc(100% - 90px);
    box-shadow: 0px 1px 3px 0px rgba(0, 21, 41, 0.12);
    background: #fff;
    padding: 24px;

    li {
      padding-left: 76px;
      padding-right: 24px;
      height: 76px;
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      align-items: center;
      border-bottom: 1px solid rgba(0, 0, 0, 0.09);

      div {
        width: 155px;
        height: 40px;
        line-height: 40px;
        text-align: center;
        // border 1px solid #ccc
        border-radius: 4px;

        &.edit {
          width: 28px;
          height: 28px;
          line-height: 34px;
          background: rgba(24, 144, 255, 0.1);
          cursor: pointer;
          display: none;
        }
      }

      &:hover {
        background: rgba(0, 0, 0, 0.04);

        .edit {
          display: block;
        }
      }
    }
  }
}
</style>