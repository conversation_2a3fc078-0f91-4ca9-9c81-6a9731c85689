<template>
  <div class="scene-iframe-content"> 
    <iframe id="scene-iframe" class="content-frame" src="" frameborder="0"></iframe>
  </div>
</template>
<script>

export default {
  name: 'scenIframeLoading',
  components:{
    
  },
  props: {
    projectID:{
      type: String, 
      default: ''
    }, 
  }, 
  data(){
    return{

    }
  },
  created(){
     
  },
  mounted(){
    let _this = this;
    const iframe = document.getElementById('scene-iframe')
    const ifrSrc = this.getHttpUrl();
    iframe.src = ifrSrc + `?&projectId=${_this.projectID}&lang=cn&edit=false`
  },
  methods: {
    getHttpUrl(){
      return window.bim_config.newModelApi
    },
  }
}
</script>
<style lang="scss" scoped>
.scene-iframe-content{
  width: 100%;
  height: 100%;
  .scene-iframe{
    height: calc(100% - 56px);
  }
}
.content-frame{
  width: 100%;
  height: 100%;
}

.detail-header{
  height: 56px;
  line-height: 56px;
  border-bottom: 1px solid rgba(0,0,0,.15);
  padding-left: 20px;
  position: relative;
  display: flex;
  // i{  
  //   position: absolute;
  //   right: 10px;
  //   top: 20px;
  //   cursor: pointer;
  // }
}
.content-frame{
  height: calc(100% - 56px);
}
</style>