<template>
    <div class="wrapper" :class="{'isMobile':isMobile}">
        <div class="text-wp">{{percentage}}%</div>
        <div v-if="!progressTextShow" class="load-wp">{{loadText}}</div>
        <div v-if="progressTextShow" class="load-wp">{{progressText}}</div>
        <div class="progress-bar" :style="{width:`${percentage}%`}"></div>
    </div>
</template>

<script>
export default {
    name: 'modelLoadingProgress',
    components: {},
    props: {
        percentage:{
            type: Number,
            default: 30
        },
        isMobile:{
            type:Boolean,
            default:false
        },
        loadingTxt:<PERSON>olean,
        progressTextShow: Boolean,
        progressText: String,

    },
    data() {
        return {
            loadText:'加载渲染配置...'
        };
    },
    watch: {
        percentage:function(val){
            if(!this.loadingTxt){
                if(val == 1){
                    this.loadText = '获取工程结构...'
                }else if(val >1 && val <= 5){
                    this.loadText = '获取填报日期...'
                }else if(val >5 && val <= 50){
                    this.loadText = '获取填报数据...'
                }else if(val >50 && val <= 80){
                    this.loadText = '获取关联构件...'
                }else if(val > 80 && val <= 95){
                    this.loadText = '成本数据匹配中...'
                }else{
                    this.loadText = '成本数据匹配完成，可以进行模拟...'
                }
            }else{
                if(val == 1){
                    this.loadText = '加载材质信息...'
                }else if(val >1 && val <= 5){
                    this.loadText = '加载初始化视图...'
                }else if(val >5 && val <= 50){
                    this.loadText = '加载当前视图下构件信息...'
                }else if(val >50 && val <= 80){
                    this.loadText = '加载几何体信息...'
                }else if(val > 80 && val <= 95){
                    this.loadText = '加载视点...'
                }else{
                    this.loadText = '构建场景并渲染...'
                }
            }
        }
    },
    computed: {},
    methods: {},
    created() {},
    mounted() {}
};
</script>
<style lang="stylus" scoped rel="stylesheet/stylus">
.wrapper {
    position: absolute;
    left 0
    right 0
    margin auto
    bottom 35px
    width 250px
    height 100px
    z-index: 9999;
    background #f0f2f5
    box-shadow 0px 13px 24px -17px rgba(11,41,62,0.8)
    border-radius 4px
    overflow hidden
    .text-wp{
        width 150px
        height 38px
        line-height 38px
        color #1890FF
        font-size 20px
        position absolute
        top 20px
        left 0
        right 0
        text-align center
        margin  auto
    }
    .load-wp{
        width 146px
        height 32px
        position absolute
        top 55px
        left 0
        right 0
        margin auto
        font-size 15px
        color rgba(0,0,0,.65)
        text-align center
        overflow hidden
        text-overflow ellipsis
        white-space nowrap

    }
    .progress-bar{
        height 12px
        position absolute
        left 0
        bottom 0
        background #1890FF

    }
}
.isMobile{
    width 800px
    height 300px
    bottom 70px
    .text-wp{
        width 300px
        height 120px
        line-height 120px
        font-size 80px
        top 40px
    }
    .load-wp{
        width 590px
        height 70px
        position absolute
        top 155px
        font-size 40px

    }
}
</style>