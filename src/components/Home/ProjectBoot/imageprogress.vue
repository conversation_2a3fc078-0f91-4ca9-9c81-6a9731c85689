<template>
    <div class="_css-imgpro-all">

    <!-- :init_width="840"
        :init_innerWidth="840" -->

        <!-- 修改进度填报对话框 -->
        <zdialog-function
        :init_title="'修改进度填报'"
        :init_zindex="1003"
        :init_innerWidth="450"
        :init_width="450"
        init_closebtniconfontclass="icon-suggested-close"
        :init_usecustomtitlearea="false"
        @onclose="status_showedit = false"
        v-if="status_showedit"
        >
        
        <div slot="mainslot" class="_css-addingnameinput-ctn"
        @mousedown="_stopPropagation($event)"
        >
             <!-- 所选分类 -->
            <div class="_css-line _css-line-name">
                <div class="_css-title _css-title-flowname ">
                    工程结构：
                </div>
                <div class="_css-fieldvalue _css-fieldvaluename _css-selectednode">
                    {{m_edit_selectednode_name}}
                </div>
            </div>
            <!-- //所选分类 -->

            <!-- 计划日期 -->
            <div class="_css-line _css-line-name">
                <div class="_css-title _css-title-flowname">
                    计划日期：
                </div>
                <div class="_css-fieldvalue _css-fieldvaluename">
                   <el-date-picker
                        v-model="m_edit_DataTime"
                        type="daterange"
                        :readonly="editDataTime_readonly"
                        range-separator="至"
                        start-placeholder="开始日期"
                        end-placeholder="结束日期">
                    </el-date-picker>
                </div>
            </div> 
            <!-- //计划日期 -->
            
            <!-- 填报日期 -->
            <div class="_css-line _css-line-name">
                <div class="_css-title _css-title-flowname">
                    填报日期：
                </div>
                <div class="_css-fieldvalue _css-fieldvaluename">
                    <el-date-picker
                    v-model="m_editingdatetime"
                    type="date"
                    @change="getEditAddingTimeChange"
                    placeholder="选择填报日期">
                    </el-date-picker>
                </div>
            </div> 
            <!-- //填报日期 -->
            <!-- 请输入百分比 -->
            <div class="_css-line _css-line-name" >
                <div class="_css-title _css-title-flowname">
                    计划完成比例：
                </div>
                <div class="_css-fieldvalue _css-fieldvaluename _css-percentarea _css-plan-percent">
                     
                    <el-input v-model="m_editpercentStr" readonly placeholder="请输入内容"></el-input>
                    <!-- <el-input-number
                    size="small"
                     v-model="m_planpercent" controls-position="right" readonly='readonly' >
                    </el-input-number> -->
                </div>
            </div>

            <!-- 请输入百分比 -->
            <div class="_css-line _css-line-name">
                <div class="_css-title _css-title-flowname">
                    实际完成比例：
                </div>
                <div class="_css-fieldvalue _css-fieldvaluename _css-percentarea">
                    <el-input-number
                        size="small"
                        v-model="m_editingpercent" 
                        :precision="1"
                        controls-position="right" 
                        @change="evt_editingpercentchange" 
                        :min="1" 
                        :max="100">
                    </el-input-number>

                    <div class="_css-percentshow">%</div>
                </div>
            </div>
            <!-- //请输入百分比 -->
            <!-- 状态 -->
            <div class="_css-line _css-line-name">
                <div class="_css-title _css-title-flowname">
                    状态：                   
                </div>
                <div class="_css-fieldvalue _css-fieldvaluename _css-percentarea">
                   <!-- <el-input v-model="m_planState" readonly placeholder="请输入内容"></el-input> -->
                   <div class="_css-percentshow">{{m_editState}}</div>
                </div>
            </div>
            <!-- //状态 -->
            
        </div>

        <div slot="buttonslot" class="_css-flowAddBtnCtn" >
            <zbutton-function
                :init_text="'保存'"
                :init_fontsize="14"
                :debugmode="true"
                :init_height="'40px'"
                :init_width="'120px'"
                @onclick="func_saveedit"
                >
            </zbutton-function>
            <zbutton-function
                :init_text="'取消'"
                :init_color="'rgba(24, 144, 255)'"
                :init_bgcolor="'#fff'"
                :init_fontsize="14"
                :debugmode="true"
                :init_height="'40px'"
                :init_width="'120px'"
                @onclick="func_canceledititem"
                >
            </zbutton-function>
        </div>
        
        </zdialog-function>
        <!-- //修改进度填报对话框 -->

        <!-- 新进度填报对话框 -->
        <zdialog-function
        :init_title="'进度填报'"
        :init_zindex="1003"
        :init_innerWidth="450"
        :init_width="450"
        init_closebtniconfontclass="icon-suggested-close"
        :init_usecustomtitlearea="false"
        @onclose="status_showadd = false"
        v-if="status_showadd"
        >
        
        <div slot="mainslot" class="_css-addingnameinput-ctn"
        @mousedown="_stopPropagation($event)"
        >
            
            <!-- 所选分类 -->
            <div class="_css-line _css-line-name">
                <div class="_css-title _css-title-flowname ">
                    工程结构：
                </div>
                <div class="_css-fieldvalue _css-fieldvaluename _css-selectednode">
                    {{m_addingselectednode.bmc_name}}
                </div>
            </div>
            <!-- //所选分类 -->

            <!-- 计划日期 -->
            <div class="_css-line _css-line-name">
                <div class="_css-title _css-title-flowname">
                    计划日期：
                </div>
                <div class="_css-fieldvalue _css-fieldvaluename">
                   <el-date-picker
                        v-model="m_planDataTime"
                        type="daterange"
                        :readonly="planDataTime_readonly"
                        range-separator="至"
                        @change="getPlanTimeChange"
                        start-placeholder="开始日期"
                        end-placeholder="结束日期">
                    </el-date-picker>
                </div>
            </div> 
            <!-- //计划日期 -->
            
            <!-- 填报日期 -->
            <div class="_css-line _css-line-name">
                <div class="_css-title _css-title-flowname">
                    填报日期：
                </div>
                <div class="_css-fieldvalue _css-fieldvaluename">
                    <el-date-picker
                    v-model="m_addingdatetime"
                    type="date"
                    @change="getAddingTimeChange"
                    placeholder="选择填报日期">
                    </el-date-picker>
                    
                </div>
            </div>
            <!-- //填报日期 -->
            <!-- 请输入百分比 -->
            <div class="_css-line _css-line-name" >
                <div class="_css-title _css-title-flowname">
                    计划完成比例：
                </div>
                <div class="_css-fieldvalue _css-fieldvaluename _css-percentarea _css-plan-percent">
                     
                    <el-input v-model="m_planpercentStr" readonly placeholder="请输入内容"></el-input>
                    <!-- <el-input-number
                    size="small"
                     v-model="m_planpercent" controls-position="right" readonly='readonly' >
                    </el-input-number> -->
                </div>
            </div>

            <!-- 请输入百分比 -->
            <div class="_css-line _css-line-name">
                <div class="_css-title _css-title-flowname">
                    实际完成比例：
                </div>
                <div class="_css-fieldvalue _css-fieldvaluename _css-percentarea">
                    <el-input-number
                    size="small"
                     v-model="m_addingpercent" 
                    :precision="1"                     
                     controls-position="right"
                      @change="evt_percentchange" 
                      :min="0" 
                      :max="100">
                    </el-input-number>

                    <div class="_css-percentshow">%</div>
                </div>
            </div>
            <!-- //请输入百分比 -->
            <!-- 状态 -->
            <div class="_css-line _css-line-name">
                <div class="_css-title _css-title-flowname">
                    状态：                   
                </div>
                <div class="_css-fieldvalue _css-fieldvaluename _css-percentarea">
                   <!-- <el-input v-model="m_planState" readonly placeholder="请输入内容"></el-input> -->
                   <div class="_css-percentshow">{{m_planState}}</div>
                </div>
            </div>
            <!-- //状态 -->
        </div>

        <div slot="buttonslot" class="_css-flowAddBtnCtn" >
            <zbutton-function
                :init_text="'确定'"
                :init_fontsize="14"
                :debugmode="true"
                :init_height="'40px'"
                :init_width="'120px'"
                @onclick="func_save"
                >
            </zbutton-function>
            <zbutton-function
                :init_text="'取消'"
                :init_color="'rgba(24, 144, 255)'"
                :init_bgcolor="'#fff'"
                :init_fontsize="14"
                :debugmode="true"
                :init_height="'40px'"
                :init_width="'120px'"
                @onclick="func_cancelnewitem"
                >
            </zbutton-function>
        </div>
        
        </zdialog-function>
        <!-- //新进度填报对话框 -->
        <!-- 设置百分比容差值 -->
        <zdialog-function
        :init_title="'设置容差值'"
        :init_zindex="1003"
        :init_innerWidth="350"
        :init_width="350"
        init_closebtniconfontclass="icon-suggested-close"
        :init_usecustomtitlearea="false"
        @onclose="set_Percentage_Dialog = false"
        v-if="set_Percentage_Dialog"
        >
        
        <div slot="mainslot" class="_css-addingnameinput-ctn"
        @mousedown="_stopPropagation($event)"
        > 
            <!-- 请输入百分比 -->
            <div class="_css-line _css-line-name">
                <div class="_css-title _css-title-flowname">
                    容差值：
                </div>
                <div class="_css-fieldvalue _css-fieldvaluename _css-percentarea">
                    <div class="_css-percentshow">±</div>
                    <el-input-number
                    size="small"
                     v-model="setToleranceValuesInput" controls-position="right" @change="evt_setToleranceValueschange" :min="0" :max="100">
                    </el-input-number>

                    <div class="_css-percentshow">%</div>
                </div>
            </div>
            <!-- //请输入百分比 --> 
        </div>

        <div slot="buttonslot" class="_css-flowAddBtnCtn" >
            <zbutton-function
                :init_text="'保存'"
                :init_fontsize="14"
                :debugmode="true"
                :init_height="'40px'"
                :init_width="'120px'"
                @onclick="func_setToleranceValues"
                >
            </zbutton-function>
            <zbutton-function
                :init_text="'取消'"
                :init_color="'rgba(24, 144, 255)'"
                :init_bgcolor="'#fff'"
                :init_fontsize="14"
                :debugmode="true"
                :init_height="'40px'"
                :init_width="'120px'"
                @onclick="set_Percentage_Dialog=false"
                >
            </zbutton-function>
        </div>
        
        </zdialog-function>
        <!-- 设置百分比容差值 -->
        <div class="css-page-head" v-if="headerHide"></div>
        <div class="_css-imgpro-body">
            <!-- left tree and right table -->
            <div class="css-body-tree _css-tree-left">

                <!-- tree head -->
                <div class="_css-materialtypelist-head">
                    <div class="_css-materhead-text">工程结构
                        <span @click="setPercentageDialog" class="icon-interface-set_se basic-btn-normal-size basic-btn-normal-color"></span>
                    </div>
                        <div  
                        class="_css-btnimport _css-change-addnew" @click.stop="clickTreeOpenOrClose">
                        
                            <div >{{treeOpenOrCloseText}}</div>
                        </div>
                </div>
                <!-- //tree head -->

                <template>
                    
                <div 
                class="_css-treearea">
                    <el-tree :data="m_bmclist" :props="treem_props" lazy
                        ref="ref_bmc"
                        class="_css-customstyle"
                        :expand-on-click-node="false"
                        @node-collapse="node_collapse"
                        @node-expand="node_expand"
                        :load="treefunc_loadChild"
                        node-key="bmc_guid"
                        :highlight-current="true"
                        :auto-expand-parent="true"
                        :default-expanded-keys="treem_default_expanded_keys" 
                        :default-expand-all='false'
                        @current-change="treefunc_current_change"
                    >
                        <span class="css-fc _css-treenode-content" slot-scope="{ node, data }">
                            
                            <i class="css-icon20 css-fs18 css-fc css-jcsa " :class="data.classname" ></i>
                            <span :title="node.label" class="css-ml4 _css-treenodelabel">
                                <div class="_css-treenodellabelname" >{{data.bmc_name}}</div>
                                <div class="_css-tre-percent" 
                                :class="{'redRoom': data.percentState == '/滞后', 
                                'greenRoom': data.percentState == '/超前', 
                                'normalRoom': data.percentState }">
                                    {{data.PercentOrValue}}
                                    <span v-if='data.PercentOrValue>=0 && data.PercentOrValue != ""'>%</span>
                                    {{data.percentState}}
                                </div>
                            </span>
                            
                        </span>
                    </el-tree>
                </div>
                </template>
            </div>

            <!-- table area -->
            <div class="css-body-table _css-imgprog-table">

                <!-- table top -->
                <div class="_css-imgprog-tabletop">
                    
                    <div class="_css-btngroup">
                        <span>工程结构：{{clickMaterialName}}</span>
                        <div v-if="change_btnimport">
                          <div 
                          v-if="m_hasimageeditauth"
                          @click="func_toaddnewitem($event)"
                          class="_css-btnimport _css-change-addnew">
                              <div >进度填报</div>
                          </div>
                          <div v-else class="_css-btnimport _css-change-addnew css-dis" style="cursor: not-allowed;">
                              <div >进度填报</div>
                          </div>
                        </div>
                    </div>
                    <div class="_css-button-detail" @click="func_model">
                        <!-- <i :style="{backgroundImage: 'url(../../../../static/images/interface-show.svg)', width: '18px', height: '18px'}"></i> -->
                        <div  
                        class="_css-btnimport _css-change-addnew">
                        
                            <div >形象进度模拟</div>
                        </div>
                    </div>
                </div>
                <!-- //table top -->

                <!-- table self -->
                <div class="_css-imgprog-tableself">
                    <el-table
                        ref="ref_table"
                        :data="m_tabdata"
                        style="width: 100%;margin-bottom: 20px;"

                        border
                        class="_css-table-ele _css-table-ele2 css-scroll _css-customstyle css-fixedleftgbcolor-white css-table-cellborder"
                        height="500"
                   
                        :default-sort="{prop: 'date', order: 'descending'}"
                        :row-class-name="tableRowClassName"
                        :highlight-current-row="true"
                        :header-cell-style="{'background-color':'transparent'}"
                            @sort-change="evt_closesome"
                    >

                        <el-table-column
                        :resizable="true"
                        
                          prop="bmc_name"
                        border
                        fixed="left"
                        min-width="150"
                        label="工程结构"
                        class="_css-celllongcolumn"
                        
                        >
                            <template slot-scope="scope">
                                <div 
                                class="_css-costitem" >{{scope.row.bmc_name}}</div>
                            </template>
                        </el-table-column>
                        <el-table-column
                        :resizable="true"
                        prop="RealName"
                        width="130"
                        label="填报人"
                        class="_css-celllongcolumn"
                        >
                        <template slot-scope="scope">
                            <div
                            class="_css-costitem" >{{scope.row.RealName}}</div>
                        </template>
                        </el-table-column>
                        <el-table-column
                            :resizable="true"
                            prop="bip_percent"
                            width="80"
                            label="状态"
                            class="_css-celllongcolumn"
                            >
                            <template slot-scope="scope">
                                <div
                                class="_css-costitem" 
                                :class="{'redRoom': data_state(scope.row.bip_planPercent,scope.row.bip_percent,scope.row.bip_planStartDT,scope.row.bip_planEndDT,scope.row.bip_datetime) == '滞后', 
                                'greenRoom': data_state(scope.row.bip_planPercent,scope.row.bip_percent,scope.row.bip_planStartDT,scope.row.bip_planEndDT,scope.row.bip_datetime) == '超前', 
                                'normalRoom': data_state(scope.row.bip_planPercent,scope.row.bip_percent,scope.row.bip_planStartDT,scope.row.bip_planEndDT,scope.row.bip_datetime)}">
                                {{data_state(scope.row.bip_planPercent,scope.row.bip_percent,scope.row.bip_planStartDT,scope.row.bip_planEndDT,scope.row.bip_datetime)}}
                                </div>
                            </template>
                        </el-table-column>

                        <el-table-column
                            :resizable="true"
                              prop="bip_planPercent"
                            width="120"
                            label="计划完成比例"
                            class="_css-celllongcolumn"
                            >
                            <template slot-scope="scope">
                                <div
                                class="_css-costitem" >{{scope.row.bip_planPercent}}%</div>
                            </template>
                        </el-table-column>

                        <el-table-column
                            :resizable="true"
                              prop="bip_percent"
                            width="120"
                            label="实际完成比例"
                            class="_css-celllongcolumn"
                            >
                            <template slot-scope="scope">
                                <div
                                class="_css-costitem" >{{scope.row.bip_percent}}%</div>
                            </template>
                        </el-table-column>
                        
                        

                        
                        <el-table-column
                        :resizable="true"
                        :fixed="false"
                          prop="bip_datetime"
                        sortable
                        border
                        min-width="120"
                        label="填报时间"
                        class="_css-celllongcolumn"
                        
                        >
                            <template slot-scope="scope">
                                <div 
                                class="_css-costitem" >{{scope.row.bip_datetime | flt_datetimefromserver}}</div>
                            </template>
                        </el-table-column>

                        <el-table-column
                        :resizable="true"
                        :fixed="false"
                        prop="bip_planStartDT"
                        border
                        min-width="120"
                        label="计划开始时间"
                        class="_css-celllongcolumn"
                        
                        >
                            <template slot-scope="scope">
                                <div 
                                class="_css-costitem" >{{scope.row.bip_planStartDT | flt_datetimefromserver}}</div>
                            </template>
                        </el-table-column>
                        <el-table-column
                        :resizable="true"
                        :fixed="false"
                        prop="bip_planEndDT"
                        border
                        min-width="120"
                        label="计划结束时间"
                        class="_css-celllongcolumn"
                        
                        >
                            <template slot-scope="scope">
                                <div 
                                class="_css-costitem" >{{scope.row.bip_planEndDT | flt_datetimefromserver}}</div>
                            </template>
                        </el-table-column>
                        <el-table-column
                        :resizable="true"
                        :fixed="false"
                        prop="bip_planDay"
                        border
                        width="100"
                        label="计划天数"
                        class="_css-celllongcolumn"
                        
                        >
                            <template slot-scope="scope">
                                <div 
                                class="_css-costitem" >{{scope.row.bip_planStartDT | planDiffDay(scope.row.bip_planEndDT)}}</div>
                            </template>
                        </el-table-column>

                        <el-table-column
                        min-width="150"
                        label="操作"
                        fixed="right"
                        class="_css-celllongcolumn"
                        :resizable="true"
                        >
                            <template slot-scope="scope">
                                <div v-if="m_hasimageeditauth"
                                class="_css-costitem _css-btnsctn" >
                                    <div @click="itemfunc_edit(scope,scope.row)"
                                    class="_css-btnimport _css-innerbtn">
                                        <div >编辑</div>
                                    </div>
                                    <div @click="itemfunc_delete(scope.row)"
                                    class="_css-btnimport _css-innerbtn">
                                        <div >删除</div>
                                    </div>
                                </div>

                                <div v-else
                                class="_css-costitem _css-btnsctn" >
                                    <div
                                    class="_css-btnimport _css-innerbtn css-dis">
                                        <div >编辑</div>
                                    </div>
                                    <div
                                    class="_css-btnimport _css-innerbtn css-dis">
                                        <div >删除</div>
                                    </div>
                                </div>
                            </template>
                        </el-table-column>

                    </el-table>
                </div>
                <!-- //table self -->

            </div>
            <!-- //table area -->
            <!-- //left tree and right table -->
        </div>
        <CompsSelectModel
            v-if="CompsProgressModel_data.visible"
            :data="CompsProgressModel_data"
            @getModelInfo="progressModelOK"
            @close="CompsProgressModel_data.visible = false"
        ></CompsSelectModel>
        <imageprogressModelChange
            v-if="imageModelVisible"
            :modelIframeSrc='modelIframeSrc'
            :bmtypeId='clickMaterialTypeID'
            :modelid="modelid"
            :ToleranceValues="setToleranceValues"
            :allModelID="allModelID"
            @close="imageModelVisible = false"
        ></imageprogressModelChange>
    </div>
</template>
<script>
import CompsSelectModel from "@/components/CompsMaterial/CompsSelectModel"
import imageprogressModelChange from "./imageprogressModelChange"

export default {
    data() {
        return {

            extdata: {
                funcauthdata: undefined
            },

            // 有权限进度的编辑权限
            // ------------------
            m_hasimageeditauth: false,

            // 正在添加的填报时间
            // 正在添加的百分比
            // 当前所选中的节点数据
            // ---------------
            m_addingdatetime: undefined,
            m_addingpercent: null,
            m_planDataTime: [],  // 计划日期
            planDataTime_readonly: false,
            startPlanTime: '',
            endPlanTime: '',
            m_planpercent: 0,  // 计划完成比例
            m_planpercentStr: '',
            m_planState: '正常',
            m_addingselectednode: undefined,
            m_editingdatetime: undefined,
            m_editingbip_guid:'',

            // 编辑进度
            m_edit_selectednode_name: '',//编辑的名字
            m_edit_DataTime: [],  // 编辑的计划时间
            startEditTime: '',
            endEditTime: '',
            editDataTime_readonly: true,  
            m_editpercentStr: '',   // 编辑的计划完成比例
            m_editpercent: 0,   // 编辑计划完成比例
            m_editState: '',     //编辑 的状态
            m_editingpercent:1,    // 编辑的实际完成比例

            // 显示进度填报对话框
            // -----------------
            status_showadd: false,
            status_showedit: false,

            // tree area
            // 树控件的 props 属性对象
            // ----------------------
            treem_props:{
                children: "children",
                label: "bmc_name",
                isLeaf: "isLeaf"
            },

            // 默认展开节点
            // 当前选中的节点
            // -----------
            treem_default_expanded_keys:[],

            // 构件分类数据
            // -----------
            m_bmclist:[],

            // //tree area

            // table area

            m_tabdata: [],

            // //table area

            CompsProgressModel_data: {
                visible: false,
                dataSource: [],
                projectID: '',
                fileMsg: [],
                CreateUserName: '',
            },
            imageModelVisible: false,
            modelIframeSrc:'',
            modelid: '',// 当前选中模型的modelid
            allModelID: [], // 多个模型的modelid 
            clickMaterialName: '全部分类',
            change_btnimport: false,
            treenode: null,
            treeresolve: null,
            treeOpenOrClose: false,  // 点击展开折叠tree
            treeOpenOrCloseText: '点击展开',
            set_Percentage_Dialog: false,  // 显示设置dialog
            setToleranceValues: 0,  // 设置容差值vlaue,实际提交和使用的值
            setToleranceValuesInput: 0,  // 该值与setToleranceValues相同，属于一个暂存值储存setToleranceValues，change暂存值
            clickMaterialTypeID: '-1000',  // 点击tree记录的bmc_guid
            headerHide: false, // 大屏显示使用该组件，header不展示
        };
    },
    components: {
        CompsSelectModel,
        imageprogressModelChange
    },
    filters: {
        flt_datetimefromserver(str) {
            var _this = this;
            var str1 = str;
            if (str) {
                // str1 = str.replace("T", " ");
                str1 = str.substr(0, 10)
            }
            return str1;
        }, 
        planDiffDay(start,end){
            let _this = this;
            let _start = start.substr(0, 10);
            let _end = end.substr(0, 10);
            var aDate, oDate1, oDate2, iDays;
            oDate1 = new Date(_start).getTime();
            oDate2 = new Date(_end).getTime();
            iDays = parseInt((oDate1 - oDate2) / 1000 / 60 / 60 / 24) //把相差的毫秒数转换为天数  
            return  Math.abs(iDays) + 1;
        }
    },
    mounted() {
        var _this = this;
        if(this.$route.query.title == '形象进度'){
            this.headerHide = false
        }else{
            this.headerHide = true
        }
        window.imgprogvue = this;
        _this.$emit('onmounted', 'imageprogress');
        _this.getToleranceValues();

        // 加载树形数据
        // -----------
        _this.loadtypes();

        // load default datas
        // -------------------
        _this.func_getdatas('-1000');

        // 权限获取
        // -------
        _this.testhasfuncbtnauth('imageprogress', 'lr-edit', (v)=>{
            _this.m_hasimageeditauth = v;
            // console.log(_this.m_hasimageeditauth);
        });

    },
    methods: {
        // 设置实际完成比例与计划完成比例，计算状态容差值
        setPercentageDialog(){
            this.set_Percentage_Dialog = true;
            this.getToleranceValues();
        },
        // 改变容差值
        evt_setToleranceValueschange(ev){

        },
        // 确定提交容差值
        func_setToleranceValues(){
             
            let _this = this;
            // 点击提交容差值，需要重新加载页面
            let _Token = _this.$staticmethod.Get("Token");
            let _OrganizeId = _this.$staticmethod._Get("organizeId");
            let _url = `${window.bim_config.webserverurl}/api/ImageProgress/BIP/SaveProjectDiffValue`; 
            _this.setToleranceValues = _this.setToleranceValuesInput
            _this.$axios({
                method: 'post',
                data: _this.$qs.stringify({
                    Token: _Token, 
                    organizeId: _OrganizeId,
                    diffvalue: _this.setToleranceValues
                }),
                url: _url
            }).then(x => {
                if (x.data.Ret > 0) {
                    _this.getToleranceValues();
                    _this.func_gettypelistAll();
                    _this.set_Percentage_Dialog = false;
                } else {
                    _this.$message.error(x.data.Msg);
                }
            }).catch(x => {
                console.error(x);
            });
        },
        // 获取容差值
        getToleranceValues(){
            let _this = this;
            let _OrganizeId = _this.$staticmethod._Get("organizeId");
            let _url = `${window.bim_config.webserverurl}/api/ImageProgress/BIP/GetProjectDiffValue?organizId=${_OrganizeId}&Token=${this.$staticmethod.Get('Token')}`; 
            _this.$axios.get(_url).then(x => {
                    if (x.data.Ret > 0) {
                        _this.setToleranceValues = x.data.Data.boee_imagediffvalue;
                        _this.setToleranceValuesInput = x.data.Data.boee_imagediffvalue;
                         
                    } else {
                        _this.$message.error(x.data.Msg);
                    }
                }).catch(x => {
                    console.error(x);
                });
        },
        // 点击设置树结构展开折叠
        clickTreeOpenOrClose(){
            let _this = this;
            _this.treeOpenOrClose = !_this.treeOpenOrClose;
            _this.func_getdatas('-1000');

            if(_this.treeOpenOrClose){
                _this.func_expandroot();
                _this.treeOpenOrCloseText = '点击折叠';
            }else{
                _this.treeOpenOrCloseText = '点击展开';
                _this.treem_default_expanded_keys = [];
                _this.func_gettypelistAll();
            }
        },
        data_state(plan,pre,start,end,pre_end){
            let _this = this;
            let str_State = ''; 
           let _start = start.substr(0, 10);
           let _end = end.substr(0, 10);
           let _pre_end = pre_end.substr(0, 10);

            let diffdate = _this.isDuringDate(_pre_end,_start,_end);  // 判断实际时间是否在计划时间范围

            if(diffdate){
                /*if(plan - pre > 0){    // 原来容差值为0时候的逻辑
                    str_State = '滞后';
                }else if(plan - pre < 0){
                    str_State = '超前';
                }else{
                    str_State = '正常';
                }*/
                str_State = _this.toCalculatePercentState(plan,pre);
            }else{
                if(_this.DateDiff(_pre_end,_start) < 1){
                    str_State = '超前';
                }else{
                    str_State = '滞后';
                }
            }
           return str_State;

        },

        // 保存编辑
        // -------
        func_saveedit() { 

            var _this = this;
            var _Token = _this.$staticmethod.Get("Token");
            var _bip_percent = _this.m_editingpercent;
            // var _bip_datetime = _this.$staticmethod.dtToString(_this.m_editingdatetime);
            var _bip_datetime = _this.$formatData.formatDateCheck(_this.m_editingdatetime).substr(0,10);
            var _bmc_guid = _this.clickMaterialTypeID;
            var _url = `${window.bim_config.webserverurl}/api/ImageProgress/BIP/Add`;
            
            _this.$axios({
                method: 'post',
                data: _this.$qs.stringify({
                    bip_guid: _this.m_editingbip_guid,
                    Token: _Token,
                    bip_percent: _bip_percent,
                    bip_datetime: _bip_datetime,
                    bmc_guid:_bmc_guid,
                    bip_refvalue: '0',
                    bip_planStartDT: _this.startEditTime,
                    bip_planEndDT: _this.endEditTime,
                    bip_planPercent: _this.m_editpercent.toString()
                }),
                url: _url
            }).then(x => {
                if (x.data.Ret > 0) {

                    // 提示，关闭对话框及刷新
                    // --------------------
                    _this.$message.success('编辑成功');

                    _this.status_showedit = false;

                    // 刷新
                    // ----
                    _this.func_getdatas(_bmc_guid); 
                    _this.refreshNodeBy(_bmc_guid);
                    
                   

                } else {
                    _this.$message.error(x.data.Msg);
                }
            }).catch(x => {
                console.error(x);
            });
            
 
        },

        func_canceledititem() {
            var _this = this;
            _this.status_showedit = false;
        },
        // 局部刷新
        refreshNodeBy(id){
            let node = this.$refs.ref_bmc.getNode(id); // 通过节点id找到对应树节点对象
            this.treem_default_expanded_keys = []
            node.parent.loaded = false;
            node.parent.expand();
            node.loaded = false;
            node.expand(); // 主动调用展开节点方法，重新查询该节点下的所有子节点
            // this.func_gettypelistAll();  // 整体刷新树结构
            // this.treefunc_loadChild(this.treenode,this.treeresolve)
            

        },

        // 弹出编辑对话框
        // -------------
        itemfunc_edit(scope,row) {
            // console.log(row,'=======click==scope')
            var _this = this; 
            
            var _this = this;
            // var node = _this.$refs.ref_bmc.getCurrentNode();
            // if (!node) {
            //     _this.$message.error('请先选择叶子节点');
            //     return;
            // }

            // // !node.DirectChildrenCount 表示是全部节点
            // // ---------------------------------------
            // if (node.DirectChildrenCount == undefined || node.DirectChildrenCount > 0) {
            //     _this.$message.error('必须选择叶子节点');
            //     return;
            // }
            // 添加前及编辑前注意初始化
            // ----------------------
            
            let startT = _this.$options.filters["flt_datetimefromserver"](row.bip_planStartDT);
            let endT = _this.$options.filters["flt_datetimefromserver"](row.bip_planEndDT);

            _this.m_edit_selectednode_name = row.bmc_name;   //编辑的名字
            _this.m_edit_DataTime = [startT,endT];  // 编辑的计划时间
            _this.startEditTime = startT;
            _this.endEditTime = endT; 
            _this.m_editpercentStr = row.bip_planPercent + '%';
            _this.m_editpercent = row.bip_planPercent;
            _this.m_editingpercent = row.bip_percent; // 编辑的实际完成比例
            _this.m_editingdatetime = new Date(row.bip_datetime);
            _this.m_editingbip_guid = row.bip_guid;
            _this.status_showedit = true;
            /*
            // 原来容差为0时候的判断逻辑
            if(row.bip_planPercent == row.bip_percent){
                _this.m_editState =  '正常'; 
            }else if( row.bip_planPercent < row.bip_percent ){
                _this.m_editState =  '超前'; 
            }else{
                _this.m_editState =  '滞后'; 
            }
            */
        //    debugger
            _this.m_editState = _this.data_state(row.bip_planPercent,row.bip_percent,row.bip_planStartDT,row.bip_planEndDT,row.bip_datetime);
            // _this.m_editState = _this.toCalculatePercentState(row.bip_planPercent,row.bip_percent)


        },

        // 移除填报数据
        // -----------
        do_deleteitem(bip_guid) {
            // console.log(bip_guid);

            // 先调用接口，再刷新数据
            // --------------------
            var _this = this;
            var _Token = _this.$staticmethod.Get("Token");
            var _bip_guid = bip_guid;
            var _url = `${window.bim_config.webserverurl}/api/ImageProgress/BIP/RemoveItem`;
            _this.$axios({
                url: _url,
                method: 'post',
                data: _this.$qs.stringify({
                    Token: _Token,
                    bip_guid: _bip_guid
                })
            }).then(x => {
                if (x.data.Ret > 0) {

                    // 刷新
                    // ----
                    _this.$message.success('操作成功');
                    _this.func_refresh();
                    _this.refreshNodeBy(_this.clickMaterialTypeID);

                } else {
                    _this.$message.error(x.data.Msg);
                }
            }).catch(x => {
                console.error(x);
            });
        },

        // 触发移除进度填报数据
        // -------------------
        itemfunc_delete(row) {
            // console.log(row);
            var _this = this;
            _this.$confirm('确认移除该填报数据？', '操作确认').then(x => {
                _this.do_deleteitem(row.bip_guid);

            }).catch(x => {

            });
        },

        setfuncauthdata(callback) {
            var _this = this;
            // /api/User/Role/GetUserOrgFuncAuth?organizeId=48617e7b-07f2-4748-9199-238af8f2bfc6&Token=322D1C8F
            var _OrganizeId = _this.$staticmethod._Get("organizeId");
            var _Token = _this.$staticmethod.Get("Token");
            _this.$axios
                .get(
                `${window.bim_config.webserverurl}/api/User/Role/GetUserOrgFuncAuth?organizeId=${_OrganizeId}&Token=${_Token}`
                )
                .then(x => {
                if (x.status == 200) {
                    if (x.data.Ret > 0) {
                        if (x.data.Data) {
                            _this.extdata.funcauthdata = x.data.Data;
                            _this.$staticmethod.Set("funcauthdata", JSON.stringify(x.data.Data));
                            if (callback) {
                            //debugger;
                            callback();
                            }
                        }
                    }else{
                        _this.$message.error(x.data.Msg);
                    }
                }
                })
                .catch(x => {});
        },

        // 各个功能模块页面的通用权限判断函数1
        // 需要有 extdata.funcauthdata
        // 需要有 authdata
        // -------------------------------
        testhasfuncbtnauth(bmencode, bmbencode, callback) {
            var _this = this;
            //debugger;
            if (!_this.extdata.funcauthdata) {
                _this.setfuncauthdata(()=>{
                _this.testhasfuncbtnauth(bmencode, bmbencode, callback);
                });
                return;
            }
            var bmIndex = _this.extdata.funcauthdata.findIndex(
                x => x.Bm_EnCode == bmencode
            );
            if (bmIndex < 0) {
                return false; // 没有找到指定bm项
            }
            var bm = _this.extdata.funcauthdata[bmIndex];
            if (bm.checkstate == "0") {
                return false; // 权限设置中，所有角色的bm设置均没有打开。
            }
            if (bm.Bmbs.length == 0) {
                return false; // 功能模块下没有按钮
            }
            var hasAuth = bm.Bmbs.findIndex(
                x => x.Roles.length > 0 && x.checkstate == "1" && x.Bmb_EnCode == bmbencode
            ); // 这个功能模块下有有角色的，且为有权限的
            //callback && callback(hasAuth >= 0);
            //return hasAuth >= 0;

            if (callback) {
                callback(hasAuth >= 0);
            } else {
                return hasAuth >= 0;
            }
        },

        // adding area

        // 保存新条目
        // ---------
        func_save() {
            var _this = this;
            var _Token = _this.$staticmethod.Get("Token");
            var _bip_percent = _this.m_addingpercent;
            // var _bip_datetime = _this.$staticmethod.dtToString(_this.m_addingdatetime);
            var _bip_datetime = _this.$formatData.formatDateCheck(_this.m_addingdatetime).substr(0,10);
            var _bmc_guid = _this.m_addingselectednode.bmc_guid;
            var _url = `${window.bim_config.webserverurl}/api/ImageProgress/BIP/Add`;
            if(_this.startPlanTime == '' || _this.endPlanTime == '') {
                _this.$message.error('计划时间不能为空');
                return
            }
            _this.$axios({
                method: 'post',
                data: _this.$qs.stringify({
                    bip_guid: '',
                    Token: _Token,
                    bip_percent: _bip_percent,
                    bip_datetime: _bip_datetime,
                    bmc_guid:_bmc_guid,
                    bip_refvalue: '0',
                    bip_planStartDT: _this.startPlanTime,
                    bip_planEndDT: _this.endPlanTime,
                    bip_planPercent: _this.m_planpercent.toString()
                }),
                url: _url
            }).then(x => {
                if (x.data.Ret > 0) {

                    // 提示，关闭对话框及刷新
                    // --------------------
                    _this.$message.success('添加成功');

                    _this.status_showadd = false;

                    // 刷新
                    // ----
                    _this.func_getdatas(_bmc_guid);
                    _this.refreshNodeBy(_this.clickMaterialTypeID);

                } else {
                    _this.$message.error(x.data.Msg);
                }
            }).catch(x => {
                console.error(x);
            });

        },

        func_cancelnewitem() {
            var _this = this;
            _this.status_showadd = false;
        },

        func_initadding() {
            var _this = this;
            _this.m_addingdatetime = undefined;
            // _this.m_addingpercent = 0;
        },

        // 弹出进度填报的对话框，如果选择了正确的构件叶子节点分类
        // -------------------------------------------------
        func_toaddnewitem(ev) {
            var _this = this;
            var node = _this.$refs.ref_bmc.getCurrentNode();
            if (!node) {
                _this.$message.error('请先选择叶子节点');
                return;
            }

            // !node.DirectChildrenCount 表示是全部节点
            // ---------------------------------------
            if (node.DirectChildrenCount == undefined || node.DirectChildrenCount > 0) {
                _this.$message.error('必须选择叶子节点');
                return;
            }
            _this.m_addingselectednode = node; 

            // 初始化填写的值
            // -------------
            _this.func_initadding();

            // 显示对话框
            // ---------
            // 获取计划开始时间，计划结束时间，实际时间
            _this.m_addingdatetime = new Date();
            _this.m_addingpercent = '';

            if(_this.startPlanTime){
                // 两者的时间差
                let edit_time = _this.$formatData.formatDateCheck(_this.m_addingdatetime).substr(0,10);

                let TimeDiff = this.$formatData.DateDiff(this.startPlanTime,this.endPlanTime) + 1;
                let TimeDiffPer = this.$formatData.DateDiff(this.startPlanTime,edit_time);
                
                
                let diffdate = this.isDuringDate(edit_time,this.startPlanTime,this.endPlanTime);
                let diffPer = (100 / TimeDiff);
                let tdiffper = TimeDiffPer * diffPer;
            
                if(diffdate){
                    let diffLastPer = (diffPer*1 + tdiffper*1 ).toFixed(1);
                    if(diffLastPer > 100){
                        diffLastPer = 100
                    }else{
                        diffLastPer = (diffPer*1 + tdiffper*1 ).toFixed(1);
                    }
                    // 计算计划完成比例
                    _this.m_planpercentStr = diffLastPer + '%';
                    _this.m_planpercent = diffLastPer;
                }else{
                    if(this.DateDiff(this.m_addingdatetime,this.startPlanTime) < 1){
                        _this.m_planpercentStr = '0%';
                        _this.m_planpercent = 0;
                        this.m_planState = '超前';
                    }else{
                        _this.m_planpercentStr = '100%';
                        _this.m_planpercent = 100
                        this.m_planState = '滞后';
                    }
                }
            }else{
                _this.m_planpercentStr = '';
                _this.m_planpercent = 0;
                _this.m_planState = '正常';
                _this.status_showadd = true;
                _this.m_addingdatetime = new Date();
            } 
            _this.status_showadd = true;
        },
        isDuringDate (_adding,beginDateStr, endDateStr) {
            var curDate = new Date(_adding),
                beginDate = new Date(beginDateStr),
                endDate = new Date(endDateStr);
            if (curDate >= beginDate && curDate <= endDate) {
                return true;
            }
            return false;
        },
        // 进度填报的实际完成比例
        evt_percentchange(ev) { 
            let _this = this;
            //console.log(ev)
            this.m_addingpercent = ev;
            let _adding = _this.$formatData.formatDateCheck(_this.m_addingdatetime).substr(0,10);

            let diffdate = _this.isDuringDate(_adding,_this.startPlanTime,_this.endPlanTime);// 判断实际填报时间是否在计划时间范围
            
            if(diffdate){
                let state = (_this.m_addingpercent - _this.m_planpercent).toFixed(0);
                /*
                // 原来容差为0时候的判断逻辑
                if(state>0){
                    _this.m_planState = '超前';
                }else if(state<0){
                    _this.m_planState = '滞后';
                }else{
                    _this.m_planState = '正常';
                }
                */
                _this.m_planState = _this.toCalculatePercentState(_this.m_planpercent,_this.m_addingpercent)
            }else{
                if(_this.DateDiff(_this.m_addingdatetime,_this.startPlanTime) < 1){
                    _this.m_planState = '超前';
                }else{
                    _this.m_planState = '滞后';
                }
            }
            
        },
        // 编辑信息中的实际完成比例事件
        evt_editingpercentchange(ev) {
            // m_editingpercent
            let _this = this;
            
            _this.m_editingpercent = ev;
            let _adding = _this.$formatData.formatDateCheck(_this.m_editingdatetime).substr(0,10);

            let diffdate = _this.isDuringDate(_adding,_this.startEditTime,_this.endEditTime);

            if(diffdate){
                // let state = (_this.m_editingpercent - _this.m_editpercent).toFixed(0);
                /*  // 原来容差为0时候的判断逻辑
                if(state>0){
                    _this.m_editState = '超前';
                }else if(state<0){
                    _this.m_editState = '滞后';
                }else{
                    _this.m_editState = '正常';
                }
                */
                _this.m_editState = _this.toCalculatePercentState(_this.m_editpercent,_this.m_editingpercent)
            }else{
                if(_this.DateDiff(_this.m_editingdatetime,_this.startEditTime) < 1){
                    _this.m_editState = '超前';
                }else{
                    _this.m_editState = '滞后';
                }
            }
        },

        _stopPropagation(ev) {
            var _this = this;
            ev && ev.stopPropagation && ev.stopPropagation();
        },

        // //adding area

        // data area

        // get records by selecting bmc_guid
        // ---------------------------------
        func_getdatas(bmc_guid) {
            var _this = this;
            var _organizeId = _this.$staticmethod._Get("organizeId");
            var _bmc_guid = bmc_guid;
            var _url = `${window.bim_config.webserverurl}/api/ImageProgress/BIP/GetList?organizeId=${_organizeId}&bmc_guid=${_bmc_guid}&Token=${this.$staticmethod.Get('Token')}`;
            _this.$axios.get(_url).then(x => {
                if (x.data.Ret > 0) {
                    if (x.data.Data && x.data.Data.List) {
                        _this.m_tabdata = _this.$staticmethod.DeepCopy(x.data.Data.List);
                        if(_this.m_tabdata.length>0){
                            let startT = _this.$options.filters["flt_datetimefromserver"](
                                    _this.m_tabdata[0].bip_planStartDT
                                    );
                            let endT = _this.$options.filters["flt_datetimefromserver"](
                                        _this.m_tabdata[0].bip_planEndDT
                                        );
                            _this.m_planDataTime = [startT,endT];
                            _this.planDataTime_readonly = true;
                            _this.startPlanTime = startT;
                            _this.endPlanTime = endT;
                        }else{
                            _this.m_planDataTime = [];
                            _this.planDataTime_readonly = false
                            _this.startPlanTime = '';
                            _this.endPlanTime = '';
                        }
                    }
                } else {
                    _this.$message.error(x.data.Msg);
                    console.error(x.data.Msg);
                }
            }).catch(x => {
                console.error(x);
            });
        },

        // //data area

        // table area

        // 根据是否选中了某一行，返回类字符串
        // -------------------------------
        tableRowClassName({ row, rowIndex }) {
            return 'css-tdunder';
        },

        evt_closesome() {

        },

        // //table area

        // tree area

        // 在渲染树的根节点并nexttick后，再请求接口获取所有分类的id，并设置到 m_defaultexpands，然后再展开“全部”节点
        // --------------------------------------------------------------------------------------------------
        func_expandroot() {

            // 获取当前项目的所有流程模板分类，并赋予 m_defaultexpands
            // ----------------------------------------------------
            var _this = this;
            var _organizeId = _this.$staticmethod._Get("organizeId");
            var _url = `${window.bim_config.webserverurl}/api/Material/MaterialCategory/GetAllCategories?organizeId=${_organizeId}&Token=${this.$staticmethod.Get('Token')}`;
            _this.$axios.get(_url).then(x => {
                if (x.data.Ret > 0) {
                    if (x.data.Data && x.data.Data.List) {
                        _this.treem_default_expanded_keys = _this.$staticmethod.DeepCopy(x.data.Data.List); 
                        var node = _this.$refs.ref_bmc.getNode({bmc_guid:'-1000'});
                        if (node) {
                            node.expand();
                        }
                    }
                } else {
                    _this.$message.error(x.data.Msg);
                }
            }).catch(x => {
                console.error(x);
            });
        },

        // 加载所有构件分类
        // ---------------
        loadtypes() {

            // 先加载一个叫 “全部构件” 的节点
            // ----------------------------
            var _this = this;

            //_this.func_gettypelist();
            _this.func_gettypelistAll();
            return;
        },

        // 加载节点：“全部构件”
        // ------------------
        func_gettypelistAll() {

        // 请求接口，加载第一级的分类
        // ------------------------
        var _this = this;

        // 调用接口，获取分类数据
        // --------------------
        var _this = this;
        var _organizeId = _this.$staticmethod._Get("organizeId");
        var _url = `${window.bim_config.webserverurl}/api/Material/MaterialCategory/GetCategories?organizeId=${_organizeId}&baseCode=&Token=${_this.$staticmethod.Get('Token')}`;
        // var _url = `${window.bim_config.webserverurl}/api/Material/Mtr/GetBmcsImageProcessPercents_ByPCode?organizeId=${_organizeId}&PCode=`;
        _this.$axios.get(_url).then(x => {
            if(x.data.Ret > 0){
                
                // 处理 isLeaf、children、classname 等属性
                // --------------------------------------
                var retdata = _this.$staticmethod.DeepCopy(x.data.Data.list);
                
                // 标记是否有第一级分类
                // ------------------
                var hasfirstwftc = retdata.length > 0;
                
                // 预设“全部构件”树节点
                // -------------------
                var _all = [
                    {
                        classname: "icon-interface-component_classification",

                        // 这里应设为多少？
                        // --------------
                        isLeaf: !hasfirstwftc,

                        // 全部的 code 为空字符串
                        // -1000 表示空节点，置空时使用，这里设为-1000
                        // 其它值
                        // -----
                        bmc_code: '',
                        bmc_guid: '-1000',
                        bmc_name: '全部分类',
                        ChildrenItemCount: x.data.Data.totalcount,
                        percentOrValue: '测试'
                    }
                ];
                _this.m_bmclist = _all;
                _this.treem_default_expanded_keys = ['-1000'];

                // 自动展开这个“全部”节点
                // --------------------
                //_this.func_expandroot();
                /*
                // 初始化加载列表自动展开tree
                _this.$nextTick(()=>{
                    _this.func_expandroot();
                });
                */
            }else{
                _this.$message.error(x.data.Msg);
            }
        }).catch(x => {
            console.error(x);
        });
        
        },
        // 加载子节点
        // ---------
        treefunc_loadChild(node, resolve) {
            this.treenode = node;
            this.treeresolve = resolve;
            // console.log(node,'======node')
            // 页面初次加载会自动调用此处，且 node.id 为0
            // 如果 node.id 为 0，直接 return
            // ------------------------------
            if (node.id == 0) {
                return;
            }
            if(node.parent){
                node.parent.expand();
            }
            // !node.DirectChildrenCount 表示是全部节点
            // ---------------------------------------
            //debugger;

            // 拿着 code 请求子一级数据
            // ----------------------
            var _this = this;
            var _organizeId = _this.$staticmethod._Get("organizeId");
            var _pcode = node.data.bmc_code;
            // 收起又显示树的情况
            // ----------------
            if (node.data.length != undefined && node.data.length >= 0) {
                _this.func_gettypelistAll();
                return; 
            }
            var _urlPer = `${window.bim_config.webserverurl}/api/Material/Mtr/GetBmcsImageProcessPercents_ByPCode?organizeId=${_organizeId}&PCode=${_pcode}&Token=${_this.$staticmethod.Get('Token')}`;

            var _url = `${window.bim_config.webserverurl}/api/Material/MaterialCategory/GetCategories?organizeId=${_organizeId}&baseCode=${_pcode}&Token=${_this.$staticmethod.Get('Token')}`;
            _this.$axios.get(_url).then(x => {
            if (x.data && x.data.Ret > 0) {
                // 深拷贝、补充 classname、设置 isLeaf
                // ---------------------------------
                var retdata = _this.$staticmethod.DeepCopy(x.data.Data.list);
                for (var i = 0; i < retdata.length; i++) {
                    retdata[i].classname = 'icon-interface-component_classification';
                    retdata[i].isLeaf = retdata[i].DirectChildrenCount == 0;
                    if (retdata[i].isLeaf) {
                        retdata[i].classname = 'icon-interface-associated-component _css-1890ff';
                    }
                    retdata[i].PercentOrValue ='';
                }

                /* 
                当 IsLeaf 不为ture 时，只显示 PercentOrValue
                当 IsLeaf 为 true 时，LastData 为 null ,什么都显示。
                当 IsLeaf 为 true 时，LastData 不为null，显示 PercentOrValue 并且显示  bip_percent   bip_planPercent 对比。
                */
                _this.$axios.get(_urlPer).then(x => {
                    if (x.data && x.data.Ret > 0) {

                        // 深拷贝、补充 classname、设置 isLeaf
                        // ---------------------------------
                        
                        let per_retdata = x.data.Data
                        
                        for (let i = 0; i < per_retdata.length; i++) {
                            
                            for(let j=0 ;j < retdata.length; j++) {
                                if(retdata[j].bmc_name == per_retdata[i].bmc_name){
                                    if(per_retdata[i].IsLeaf && per_retdata[i].LastData == null){ 
                                        retdata[j].PercentOrValue = '';
                                        retdata[j].percentState = '';
                                    }else if(per_retdata[i].IsLeaf && per_retdata[i].LastData != null && per_retdata[i].LastData_planEndDT !=null){
                                        // 原来容差为0时候的判断逻辑
                                        // if(per_retdata[i].LastData.bip_planPercent - per_retdata[i].LastData.bip_percent > 0){
                                        //     retdata[j].percentState = '/滞后';
                                        // }else if(per_retdata[i].LastData.bip_planPercent - per_retdata[i].LastData.bip_percent < 0){
                                        //     retdata[j].percentState = "/超前";
                                        // }else{
                                        //     retdata[j].percentState = '/正常';
                                        // }
                                        
                                        // 左边tree百分比和状态
                                        retdata[j].percentState = '/' + _this.data_state(per_retdata[i].LastData.bip_planPercent,per_retdata[i].LastData.bip_percent,per_retdata[i].LastData_planStartDT,per_retdata[i].LastData_planEndDT,per_retdata[i].LastData_realDT);                                        
                                        retdata[j].PercentOrValue = per_retdata[i].PercentOrValue;
                                    }else{ 
                                        retdata[j].PercentOrValue = per_retdata[i].PercentOrValue + '%';
                                        retdata[j].percentState = '';
                                    }
                                }
                            }

                        }
                        resolve(retdata);
                }
                }).catch(x => {
                    console.error(x);
                });

            }else{
                _this.$message.error(x.data.Msg);
            }
            }).catch(x => {
            console.error(x);
            });
        },

        // 刷新
        // ----
        func_refresh() {
            var _this = this;
            _this.func_getdatas(_this.clickMaterialTypeID || '-1000');
        },

        // 当前节点选中变化 
        // ---------------
        treefunc_current_change(data, node) {

            // 不需要是叶子节点
            // ---------------
            var _this = this;
            _this.m_currentbmc = data;
            // console.log(data,'======data')
            if (data.DirectChildrenCount == undefined || data.DirectChildrenCount > 0) {
                _this.change_btnimport = false;
            }else{
                _this.change_btnimport = true;
            }
            // selectCateObj 为 CompsMaterialRelation 使用，暂未赋值
            // ----------------------------------------------------
            // console.log('selectCateObj 暂未赋值');
    
            // clickMaterialTypeID 导出模板时应用到的 bc_guid
            // ---------------------------------------------
            _this.clickMaterialTypeID = data.bmc_guid;
            _this.clickMaterialName = data.bmc_name;

            // 加载这个分类下的数据
            // ------------------
            _this.func_getdatas(_this.clickMaterialTypeID);

        },

        // 收起节点 回调
        // 展开节点 回调
        // ------------
        node_collapse(itemi, node, comp) {

            // 移除展开过的节点
            // ---------------
            var _this = this;
            if (_this.treem_default_expanded_keys.indexOf(node.key) >= 0) {
                _this.treem_default_expanded_keys = _this.treem_default_expanded_keys.filter(x => x != node.key);
            }

            itemi.classname = "icon-interface-component_classification";
        },
        node_expand(itemi, node, comp) {
            
            // 记录展开过的节点
            // 记录到 treem_default_expanded_keys 中
            // ------------------------------------
            var _this = this;
            if (_this.treem_default_expanded_keys.indexOf(node.key) < 0) {
                _this.treem_default_expanded_keys.push(node.key);
            }

            itemi.classname = "icon-interface-component_classification";
        },
        // 点击进去当前详情
        func_model() {
            var _this = this;
            var node = _this.$refs.ref_bmc.getCurrentNode();
            // if (!node) {
            //     _this.$message.error('请先选择叶子节点');
            //     return;
            // }

            // // !node.DirectChildrenCount 表示是全部节点
            // // ---------------------------------------
            // if (node.DirectChildrenCount == undefined || node.DirectChildrenCount > 0) {
            //     _this.$message.error('必须选择叶子节点');
            //     return;
            // }
            _this.func_getModelElementId(_this.clickMaterialTypeID);
            // this.CompsProgressModel_data.projectID = this.$staticmethod._Get("bimcomposerId");
            // this.CompsProgressModel_data.CreateUserName = this.$staticmethod.Get("Account");
            // this.CompsProgressModel_data.visible = true;
        },
        // 点击播放获取所关联的模型
        func_getModelElementId(bmc_guid) {
            var _this = this;
            let  _projectId = _this.$staticmethod._Get("bimcomposerId");
            var _organizeId = _this.$staticmethod._Get("organizeId");
            var _bmc_guid = bmc_guid;
            var _url = `${window.bim_config.webserverurl}/api/Material/Mtr/GetRelationDatasByOrgIdAndBMTypeId?organizeId=${_organizeId}&bmtypeId=${_bmc_guid}&Token=${this.$staticmethod.Get('Token')}`;
            _this.allModelID = [];
            _this.$axios.get(_url).then(x => {
                if (x.data.Ret > 0) {
                    if (x.data.Data) {
                        let _list = x.data.Data;
                        _list.forEach((el) => {
                            _this.allModelID.push(el.modelid);
                        })
                        if(_this.allModelID.length<1){
                            _this.$message.error('当前形象进度下没有关联的模型');
                            _this.imageModelVisible = false;
                            return
                        } 
                        // console.log(_this.allModelID)

                        let str = '';
                        for(var i=0;i<_this.allModelID.length-1;i++){
                            str+=_this.allModelID[i]+"|";
                        }
                        let _model = str + _this.allModelID[_this.allModelID.length-1]
                        // _model是将所有的modelid用 | 拼接，支持多模型加载
                        // console.log(str + _this.allModelID[_this.allModelID.length-1])
                        _this.modelIframeSrc = `${window.bim_config.bimviewerurl}?projectId=${_projectId}&model=${_model}&ver=`
                        _this.imageModelVisible = true;
                    } 
                } else {
                    _this.allModelID = [];
                    _this.$message.error(x.data.Msg);
                    console.error(x.data.Msg);
                }
            }).catch(x => {
                console.error(x);
            });
        },
        progressModelOK(obj){
            this.modelid = obj.modelid
            // _this.$staticmethod._Get("bimcomposerId")
            // this.modelIframeSrc = `${this.$configjson.bimviewerurl}?projectId=${this.CompsProgressModel_data.projectID}&model=${obj.modelid}&ver=`
            this.$nextTick(()=>{
                this.CompsProgressModel_data.visible = false
                this.imageModelVisible = true
            }) 
        }, 
        // 修改计划时间
        getPlanTimeChange(ev) {
            let _this = this;
            // 计划时间的开始时间和结束时间
            let start = new Date(this.$staticmethod.dtToString(ev[0]));
            let end = new Date(this.$staticmethod.dtToString(ev[1]));
            this.startPlanTime = _this.$formatData.formatDateCheck(start).substr(0,10);
            this.endPlanTime = _this.$formatData.formatDateCheck(end).substr(0,10);
        },
        // getEditTimeChange(ev){
        //     console.log(ev,'编辑时间变化')
        // },

        DateDiff(sDate1, sDate2) {
            var aDate, oDate1, oDate2, iDays;
            oDate1 = new Date(sDate1).getTime();
            oDate2 = new Date(sDate2).getTime();
            iDays = parseInt((oDate1 - oDate2) / 1000 / 60 / 60 / 24) //把相差的毫秒数转换为天数   
            return  iDays;
        },
        getEditAddingTimeChange(ev) {
            let _this = this;
            _this.m_editingdatetime = ev;
            let _adding = _this.$formatData.formatDateCheck(_this.m_editingdatetime).substr(0,10);
            // 两者的时间差
            let TimeDiff = _this.$formatData.DateDiff(_this.startEditTime,_this.endEditTime) + 1;
            let TimeDiffPer = _this.$formatData.DateDiff(_this.startEditTime,_adding);
             
            let diffdate = _this.isDuringDate(_adding,_this.startEditTime,_this.endEditTime);
            // console.log(_adding,_this.startEditTime,_this.endEditTime,'=======形象进度',_adding==_this.endEditTime)
            let diffPer = (100 / TimeDiff);
            let tdiffper = TimeDiffPer * diffPer;
            
            if(diffdate){
                // 计算计划完成比例
                if(_adding==_this.endEditTime){
                    _this.m_editpercentStr = '100%';
                    _this.m_editpercent = 100;
                }else{
                    let diffLastPer = (diffPer*1 + tdiffper*1 ).toFixed(1);
                    if(diffLastPer > 100){
                        diffLastPer = 100
                    }else{
                        diffLastPer = (diffPer*1 + tdiffper*1 ).toFixed(1);
                    }
                    _this.m_editpercentStr = diffLastPer + '%';
                    _this.m_editpercent = diffLastPer;
                }
                
                _this.m_editState = _this.toCalculatePercentState(_this.m_editpercent,_this.m_editingpercent)

            }else{
                if(_this.DateDiff(_this.m_editingdatetime,_this.startEditTime) < 1){
                    _this.m_editpercentStr = '0%'
                    _this.m_editpercent = 0;
                    _this.m_editState = '超前';
                }else{
                    _this.m_editpercentStr = '100%'
                    _this.m_editpercent = 100;
                    _this.m_editState = '滞后';
                }
            }
 
        },
        getAddingTimeChange(ev){
            let _this = this;
            _this.m_addingdatetime = ev;


            let _adding =  _this.$formatData.formatDateCheck(_this.m_addingdatetime).substr(0,10);
            // 两者的时间差
            let TimeDiff = _this.$formatData.DateDiff(_this.startPlanTime,_this.endPlanTime) + 1;
            let TimeDiffPer = _this.$formatData.DateDiff(_this.startPlanTime,_adding);
             
             
            let diffdate = _this.isDuringDate(_adding,_this.startPlanTime,_this.endPlanTime);
            // console.log('新增填报时间change',ev,_adding,_this.startPlanTime,_this.endPlanTime,diffdate)
            let diffPer = (100 / TimeDiff);
            let tdiffper = TimeDiffPer * diffPer;
            
            if(diffdate){
                if(_adding == _this.endPlanTime){
                    _this.m_planpercentStr = '100%';
                    _this.m_planpercent = 100;
                }else{
                    // 计算计划完成比例
                    let diffLastPer = (diffPer*1 + tdiffper*1 ).toFixed(1);
                    if(diffLastPer > 100){
                        diffLastPer = 100
                    }else{
                        diffLastPer = (diffPer*1 + tdiffper*1 ).toFixed(1);
                    }
                    _this.m_planpercentStr = diffLastPer + '%';
                    _this.m_planpercent = diffLastPer;
                }
                // console.log( '计划==实际') 
                _this.m_planState = _this.toCalculatePercentState(_this.m_planpercent,_this.m_addingpercent)
            }else{
                if(_this.DateDiff(_this.m_addingdatetime,_this.startPlanTime) < 1){
                    _this.m_planpercentStr = '0%';
                    _this.m_planpercent = 0;
                    _this.m_planState = '超前';
                }else{
                    _this.m_planpercentStr = '100%';
                    _this.m_planpercent = 100;
                    _this.m_planState = '滞后';
                }
            }

        },
        /*
            setToleranceValues 容差值
            planPercent  计划完成比例
            actualPercent  实际完成比例
        */
        toCalculatePercentState(planPercent,actualPercent){
            let _this = this;
            let setToleranceValues = _this.setToleranceValues;
            // 当实际完成时间在计划时间范围内使用该方法
            // 当实际比例在planPercent ± setToleranceValues内，属于正常,小于属于滞后，大于属于超前
            let percentState = '';
            if(actualPercent >= planPercent - setToleranceValues  && actualPercent <= planPercent+setToleranceValues){
                percentState = '正常';
            }else if(actualPercent > planPercent - setToleranceValues){
                percentState = '超前';                
            }else{
                percentState = '滞后';
            }
            return percentState;
        },
        // //tree area
    }
}
</script>
<style scoped>

/* new dialog area */
._css-tree-left{
    width: 350px;
}
._css-selectednode {
    box-sizing: 24px;
    box-sizing: border-box;
    padding: 0 12px 0 12px;
    line-height: 24px;
}

._css-percentarea {
    justify-content: space-around;
}

._css-fieldvaluename-in {
    width: 100%;
    outline: none;
    border: none;
    height: 32px;
    line-height: 32px;
}

._css-flowAddBtnCtn {
    display: flex;
    flex-direction: row-reverse;
    height: 64px;
    align-items: center;
    box-sizing: border-box;
    padding-right: 8px;
}
._css-line {
    padding:0 24px;
    box-sizing: border-box;
    margin: 8px 0 0 0;
    display: flex;
    align-items: center;
}
._css-fieldvaluename {
    flex: 1;
    height: 36px;
    display: flex;
    align-items: center;
    border: 1px solid rgba(0,0,0,0.1);
    border-radius: 4px;
    box-sizing: border-box;
    /* padding: 4px 8px 4px 18px; */
}

/* //new dialog area */

/* table area */

._css-btnimport._css-innerbtn {
    height: 25px;
    line-height: 16px;
}

._css-btnimport._css-innerbtn.css-dis {
    cursor:not-allowed;
}

._css-numitem {
    text-align: right;
    width:100%;
}

._css-btnimport {
    font-size: 12px;
    color: #1890FF;
    border: 1px solid #1890FF;
    border-radius: 4px;
    padding: 4px 6px 4px 6px;
    margin-right: 12px;
    cursor: pointer;
}
._css-btnimport:hover {
    color:#fff;
    background-color: #1890FF;
}
._css-button-detail{
    color: #1890FF;
    margin-right: 10px;
}
._css-button-detail:hover{
    cursor: pointer;
}
._css-button-detail i {
    font-size: 20px;
    display: inline-block;
}
._css-btngroup {
    display: flex;
    align-items: center;
    margin-left:12px;
}

._css-imgprog-tabletop {
    height: 40px;
    background-color: #fff;
    display: flex;
    align-items: center;
    border-bottom:1px solid rgba(0, 0, 0, 0.05);
    box-sizing: border-box;
    justify-content: space-between;
}

._css-imgprog-tableself {
    position: absolute;
    width: 99%;
    height: calc(100% - 80px);
    background-color: #fff;
}

._css-table-ele {
    background-color: #fff;
}

._css-imgprog-table {
    position: relative;
    padding: 24px 10px 24px 10px;
    width: calc(100% - 350px);
    box-sizing: border-box;
}

._css-costitem {
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow-x: hidden;
    font-size: 14px;
    /* font-weight: 100; */
    font-weight: normal;

}

._css-btnsctn {
    display: flex;
    align-items: center;
    width:100%;
    justify-content: space-around;
}

._css-customstyle {
  height: calc(100% - 0px) !important;
}

/* //table area */

/* tree area */

/* tree head */
._css-materialtypelist-head {
  height: 64px;
  display: flex;
  align-items: center;
  padding-left: 8px;
}

._css-materhead-icon {
  height: 20px;
  width: 20px;
}
._css-materhead-text {
  height: 22px;
  line-height: 22px;
  flex: 1;
  text-align: left;
  margin-left: 8px;
  font-weight: 500;
  color: rgba(0, 0, 0, 1);
}
._css-materhead-text span{
    vertical-align: middle;
}
/* //tree head */

._css-treenode-content {
    /* width:100%; */
    width: calc(100% - 26px);
    text-align: left;
}

._css-treenodelabel {
    font-size: 13px;
    flex: 1;
    display: flex;
    height: 32px;
    align-items: center;
    width: calc(100% - 48px);
}

._css-treenodellabelname {
  flex:1;
  overflow: hidden;
    width: calc(100% - 24px);
    overflow-x: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

._css-1890ff {
  color:#1890FF;
}

._css-treearea {
    height: calc(99.99% - 64px - 24px);
    overflow-y: auto;
    margin-bottom: 24px;
}


/* //tree area */

._css-imgpro-all {
    height:100%;
    background: #f0f2f5;
}
._css-imgpro-body {
    display: flex;
    height: calc(100% - 54px);
}
._css-addingnameinput-ctn /deep/ .el-date-editor--daterange{
  width: 100%;
}
._css-title-flowname{
    width: 25%;
    text-align: left;
}
._css-plan-percent /deep/ .el-input__inner{
    padding:0 15px;
}

.redRoom{
    color:red;
}
.greenRoom{
    color: green;
}
.normalRoom{
    color:'#606266'
}
._css-tre-percent{
    padding: 0 10px;
}
._css-change-addnew{
    margin-left: 20px;
}
._css-imgprog-tableself /deep/ .el-table__header,._css-imgprog-tableself /deep/ .el-table__fixed-body-wrapper{
    background:#fff;
}
</style>
