<template>
  <div class="_css-materials-share-all">
    <div class="_css-tabcontrolarea-share">
      <div class="_css-tabcontrolitem-share">模型分享管理</div>
    </div>
    <div class="_css-bottomarea">
      <!-- <div class="_css-choose-switch-parent">
        <div class="_css-choose-switch">
          <i class="_css-interface-back icon-arrow-right_outline"></i>
        </div>
      </div> -->
      <div class="_css-content-right">
        <div class="-css-bottom-title">
          <div class="-css-bottom-title-left">全部分享</div>
          <div class="-css-bottom-title-right">
            <div class="-css-buttom-share" @click.stop="shareTableRemoveAll">移 除</div>
          </div>
        </div>
        <div class="_css-content-table">
          <el-table
            v-loading="loading"
            element-loading-text="加载中"
            ref="multipleTable"
            :data="tableData"
            tooltip-effect="dark"
            style="width: 100%"
            class="_css-share-table"
            height="500"
            @selection-change="handleSelectionChange"
          >
            <el-table-column type="selection" width="50"></el-table-column>
            <el-table-column :resizable="false" show-overflow-tooltip label="模型名称">
              <template slot-scope="scope">
                <div class="_css-share-url">{{ scope.row.shareModelName }}</div>
              </template>
            </el-table-column>
            <el-table-column prop="shareUserName" label="分享人" width="100">
              <template slot-scope="scope">
                <div class="_css-share-url">{{scope.row.shareRealName}}</div>
              </template>
            </el-table-column>
            <el-table-column prop="shareModelPublic" label="是否加密" width="100">
              <template slot-scope="scope">
                <div v-if="scope.row.shareModelPublic">是</div>
                <div v-else>否</div>
              </template>
            </el-table-column>
            <el-table-column prop="shareCreateDate" label="有效期" width="150">
              <template slot-scope="scope">
                <div class="_css-share-url">{{ shareDaytime(scope.row) }}</div>
              </template>
            </el-table-column>

            <el-table-column
              :resizable="false"
              show-overflow-tooltip
              prop="shareBrowseUrl"
              label="浏览链接"
            >
              <template slot-scope="scope">
                <div class="_css-share-url">{{scope.row.shareBrowseUrl}}</div>
              </template>
            </el-table-column>
            <el-table-column label="二维码" :resizable="false" min-width="16" width="100">
              <template slot="header">
                <div class="_css-table-title">
                  <span class="_css-dataitemcode">二维码</span>
                </div>
              </template>
              <template slot-scope="scope">
                <div class="_css-qrcode icon-interface-erweima">
                  <div class="_css-hover-qrcode">
                    <p>查看模型</p>
                    <!-- <img :src="getQrCodeSrc(scope.row.shareModelid)" /> -->
                    <img :src="getQrCodeSrc(scope.row.shareBrowseqrCodeUrl)" />
                    
                  </div>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="操作" prop="shareModelid" width="280">
              <template slot-scope="scope">
                <div class="_css-operation-table">
                  <div
                    class="-css-buttom-share"
                    @click.stop="shareTableEdit(scope.row)"
                  >编 辑</div>
                  <div
                    class="-css-buttom-share"
                    @click.stop="shareTableRemove(scope.row.shareId)"
                  >移 除</div>
                  <div
                    class="-css-buttom-share"
                    @click.stop="shareTableCopy(scope.row.shareBrowseUrl)"
                  >复制链接</div>
                </div>
              </template>
            </el-table-column>
          </el-table>
          <el-pagination
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page.sync="pageNum"
            :page-size="pageSize"
            layout="prev, pager, next, jumper"
            :total="Total">
          </el-pagination>
        </div>
      </div>
    </div>
    <div class="_css-share-edit">
      <ComModelShareEdit
        v-if="editModelShare"
        :shareEditObj="tableShareEditObj"
        @handelClickShareEditSure="shareEditClickSure"
        @closeall="closeall()"
      ></ComModelShareEdit>
    </div>
  </div>
</template>
<script>
import ComModelShareEdit from "@/components/CompsCommon/ComModelShareEdit";

export default {
  components: {
    ComModelShareEdit
  },
  data() {
    return {
      tableData: [], // 表格数据
      multipleSelection: [],
      editModelShare: false, //
      loading: true,
      tableShareEditObj: {},
      removeShowDialog: false,
      Total: 0,  // 分页总数
      pageNum: 1,  // 当前页
      pageSize: 20, // 当前页条数
    };
  },
  mounted() {
    // 参数获取测试
    let _this = this;
   
    _this.requestTableData();
    // 当直接输入路径打开页面时，自动选中左侧对应的菜单项
    _this.$emit("onmounted", "ModelSharedMgr");
  },
  methods: {
    // 请求接口返回表格数据
    async requestTableData(){
      this.loading = true;
      let params = {
        pageNum: this.pageNum,
        pageSize: this.pageSize,
        organizeId: this.$staticmethod._Get("organizeId")
      }
      const res = await this.$api.getsharepaged(params)
      this.loading = false;
      
      this.tableData = this.shareConvertToDt(res.Data.Data);
      this.Total = res.Data.Total;
    },
    handleSizeChange(val) {
      this.pageSize = val
      console.log(`每页 ${val} 条`);
      this.requestTableData()
    },
    handleCurrentChange(val) {
      this.pageNum = val
      console.log(`当前页: ${val}`);
      this.requestTableData()
    },
    shareConvertToDt(items) {
      let _arr = [];
      for (let i = 0; i < items.length; i++) {
        let tobj = {};
        let url = `${window.location.origin}${window.bim_config.hasRouterFile}/#/modelNewShareLoad/${items[i].ProjectId}/${items[i].Id}`;
        let qrCodeUrl = `${window.bim_config.newModelApi}?vaultID=${items[i].ProjectId}&modelID=${items[i].Id}`;
        tobj.shareModelName = items[i].ModelName;
        tobj.shareModelPublic = items[i].HasPassword;
        tobj.shareCreateDate = items[i].CreateTime;
        tobj.shareBrowseUrl = url;
        tobj.shareBrowseqrCodeUrl = qrCodeUrl;
        tobj.shareModelid = items[i].ModelId;
        tobj.shareId = items[i].Id;
        tobj.ShardDays = items[i].ShardDays
        tobj.Password = items[i].Password

        tobj.shareRealName = items[i].CreatorUserName;

        _arr.push(tobj);
      }
      return _arr;
    },
    // 借助服务器返回二维码图片
    getQrCodeSrc(shareModelid) {
      let encodedUrl = encodeURIComponent(shareModelid);
      let codeImgUrl = `${this.$urlPool.QRCode}?encodedUrl=${encodedUrl}&token=${this.$staticmethod.Get("Token")}`;
      return codeImgUrl;
    },
    toggleSelection(rows) {
      if (rows) {
        rows.forEach(row => {
          this.$refs.multipleTable.toggleRowSelection(row);
        });
      } else {
        this.$refs.multipleTable.clearSelection();
      }
    },
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    // 点击表格编辑
    shareTableEdit(shareobj) {
      let _this = this;
      _this.tableShareEditObj = shareobj;
      _this.editModelShare = true;
    },
    openRemoveShowDialog() {
      let _this = this;
      _this
        .$confirm("此操作将永久删除该文件, 是否继续?", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
        .then(() => {
          _this.sureRemoveTableMes();
        })
        .catch(() => {
          _this.$message({
            type: "info",
            message: "已取消删除"
          });
        });
    },
    // 点击表格移除
    shareTableRemove(shareId) {
      let _this = this;
      _this
        .$confirm("确定删除选定的数据?", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
        .then(() => {
          _this.sureRemoveTableMes(shareId);
        })
        .catch(() => {
          console.log("取消操作");
        });
    },
    // 点击复制链接
    shareTableCopy(shareLink) {
      let _this = this;
      let oInput = document.createElement("input");
      oInput.value = shareLink;
      document.body.appendChild(oInput);
      oInput.select(); // 选择对象
      document.execCommand("Copy"); // 执行浏览器复制命令
      oInput.className = "oInput";
      oInput.style.display = "none";
      this.$message.success("复制成功");
    },
    async sureRemoveTableMes(shareId) {
      const resdata = await this.$api.postsharecancel(shareId)
      if (resdata.Ret == 1) {
        this.requestTableData();
        this.$message.success(resdata.Msg);
        this.loading = false;
      } 
    },
    // 点击批量删除
    shareTableRemoveAll() {
      let _this = this;
      if (!_this.multipleSelection.length > 0) {
        _this.$message.error("请选择需要移除的模型");
        return;
      }
      let removeMultipleSelection = [];
      _this.multipleSelection.forEach((item, index) => {
        removeMultipleSelection.push(item.shareId);
      });
      _this
        .$confirm("确定删除选定的数据?", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
        .then(() => {
          _this.sureRemoveTablemultipleChoice(removeMultipleSelection);
        })
        .catch(() => {
          console.log("取消操作");
        });
    },
    // 批量取消分享
    async sureRemoveTablemultipleChoice(id){
      const resData = await this.$api.postsharecancelbatch(id)
      if(resData.Ret == 1){
        this.requestTableData();
        this.closeall()
        this.$message.success(resData.Msg);
      }
    },
    // 编辑分享
    async shareEditClickSure(dataArg) {
      const resData = await this.$api.postsharemodify(dataArg)
      if(resData.Ret == 1){
        this.requestTableData();
        this.closeall()
        this.$message.success(resData.Msg);
      }
    },
    closeall() {
      this.editModelShare = false;
    },
    // 有效期过滤
    shareDaytime(item){
      let dateTime = ''
      if(item.ShardDays === 7 || item.ShardDays === 1){
        dateTime = this.dateToString(item.shareCreateDate,item.ShardDays) + `（${item.ShardDays}天）`; 
      }else if(item.ShardDays === -1){
        dateTime = "永久";
      } 
      return dateTime;
    },
    dateToString(time,num){
      let createTime = new Date(time);
      // 在日期上增加一天
      createTime.setDate(createTime.getDate() + num);
      // 将结果格式化为所需的格式
      let nextDay = createTime.toISOString().split('T')[0];
      return nextDay
    }
  }
};
</script>
<style scoped>
._css-materials-share-all {
  height: 100%;
  display: flex;
  flex-direction: column;
  line-height: 1;
}
._css-tabcontrolarea-share {
  height: 54px;
  display: flex;
  align-items: center;
  flex: none;
  background-color: #fff;
  border-bottom: 1px solid rgba(0, 0, 0, 0.15);
}
._css-tabcontrolitem-share {
  color: rgba(0, 0, 0, 0.9);
  margin-left: 16px;
  font-size: 16px;
  font-weight: 500;
}
._css-bottomarea {
  flex: 1;
  display: flex;
  height: calc(100% - 54px);
  padding-right: 24px;
}
._css-content-right {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding-left: 16px;
}
.-css-bottom-title {
  height: 50px;
  line-height: 50px;
  display: flex;
  align-items: center;
  font-size: 16px;
}
.-css-bottom-title-left {
  height: 22px;
  line-height: 22px;
  flex: 1;
  text-align: left;
  font-weight: 500;
}
.-css-bottom-title-right {
  text-align: right;
}
.-css-buttom-share {
  width: 78px;
  line-height: 26px;
  border-radius: 4px;
  border: 1px solid #1890ff;
  color: #1890ff;
  text-align: center;
  cursor: pointer;
  font-size: 14px;
}
._css-choose-switch-parent {
  height: 100%;
  display: flex;
  align-items: center;
  cursor: pointer;
}
._css-choose-switch {
  width: 16px;
  height: 32px;
  background-color: rgba(0, 0, 0, 0.09);
  border-radius: 0 4px 4px 0;
}
._css-choose-switch ._css-interface-back {
  color: rgba(0, 0, 0, 0.4);
  text-align: center;
  line-height: 32px;
}
/* ._css-choose-switch:hover { 
  background-color: #1890ff;
} */
._css-choose-switch:hover ._css-interface-back {
  color: #fff;
}
._css-share-table {
  height: 95% !important;
}
._css-content-table {
  width: calc(100% - 1px);
  height: calc(100% - 64px);
  background: #fff;
  position: relative;
  /* overflow: auto */
}
._css-operation-table {
  display: flex;
  padding: 2px 3px;
  /* background: rgba(0, 0, 0, 0.09); */
}
._css-operation-table .-css-buttom-share {
  margin: 3px;
}
._css-content-table /deep/ .el-table__body-wrapper .el-table__row {
  height: 40px !important;
  border-bottom: 1px solid rgba(0, 0, 0, 0.09);
  line-height: 40px !important;
}
._css-content-table /deep/ .el-table__header-wrapper th {
  height: 44px !important;
  text-align: center;
  font-size: 14px;
  color: rgba(0, 0, 0, 0.85);
  font-weight: 800;
  border-bottom: 1px solid rgba(0, 0, 0, 0.09);
  border-right: 1px solid rgba(0, 0, 0, 0.09);
  line-height: 44px !important;
}
._css-content-table /deep/ .el-table th.is-leaf:nth-child(8) {
  border-right: none !important;
}
._css-content-table /deep/ .el-table__header-wrapper .cell,
._css-content-table /deep/ .el-table__header-wrapper th div,
._css-content-table /deep/ .el-table--border td:first-child .cell,
._css-content-table /deep/ .el-table--border th:first-child .cell {
  padding: 0;
}
._css-content-table /deep/ .el-table .cell {
  line-height: initial;
}
._css-content-table /deep/ .el-table__body-wrapper tr td:first-child .cell {
  padding-left: 18px !important;
}
._css-content-table /deep/ .el-table td {
  border-bottom-color: rgba(0, 0, 0, 0.08) !important;
  border-right: 1px solid rgba(0, 0, 0, 0.08) !important;
}
._css-content-table /deep/ .el-table td:last-child {
  border-right-color: rgba(0, 0, 0, 0) !important;
}
._css-content-table /deep/ .el-table--border::after,
._css-content-table /deep/ .el-table--group::after,
._css-content-table /deep/ .el-table::before {
  background: transparent;
}

._css-qrcode {
  height: 20px;
  width: 20px;
}
._css-qrcode ._css-hover-qrcode {
  display: none;
}
._css-qrcode:hover ._css-hover-qrcode {
  display: block;
  position: absolute;
  left: 30px;
  top: 0;
  z-index: 1999;
  background-color: #fff;
  border: 1px solid #ccc;
  text-align: center;
  padding: 0 8px 8px 8px;
}
._css-hover-qrcode img {
  width: 150px;
  height: 150px;
}
._css-hover-qrcode p {
  margin: 10px 0;
  line-height: 30px;
  font-size: 14px;
}
._css-share-url {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  font-size: 14px;
  font-weight: 500;
  color: rgba(0, 0, 0, 0.85);
}
</style>