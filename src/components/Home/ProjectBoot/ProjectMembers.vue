<template>
  <div class="_css-projmembers-all">
    <div class="_css-projmembers-top">
      <div class="_css-pmt-title">项目成员({{ProjectUsersLength}})</div>
    </div>
    <div id="id_projmembers_body" class="_css-projmembers-body">
      <div class="_css-pm-thead">
        <!-- 输入框和查询按钮 -->
        <div>
          <CompsUsersInput
            @oninput="_oninput"
            :placeholder="'请输入姓名关键字'"
            :width="'100%'"
            :maxWidth="'200px'"
            iconclass="icon-interface-search"
          ></CompsUsersInput>
        </div>
        <div @click="search($event)" class="_css-pm-btnsearch">搜索</div>
        <!-- //输入框和查询按钮 -->
        <div 
        @click="removeSelMem($event)"
        class="_css-pm-rbtns">
           <div  class="_css-pm-btn "
           :class="{'_css-dis': getbtndeldis()}"
           >移除成员</div>
        </div>
      </div>
      <div class="_css-pm-tbody">
        <!-- element 表格 -->
        <el-table :height="500" class="_css-table-ele" :data="tableData" style="width: 100%">
          <el-table-column :resizable="false" width="40">
            <template slot="header" slot-scope="scope">
              <span @click="switchall($event)" class="css-cb css-icon12 css-cp css-blk"
               :class="{'mulcolor-interface-checkbox-selected':selectedAll()}"
              ></span>
            </template>
            <template slot-scope="scope">
              <span class="css-cb css-icon12 css-cp "
              @click="switchsingle(scope.row.UserId, $event)"
              :class="{'mulcolor-interface-checkbox-selected':selectedContains(scope.row.UserId)}"
              ></span>
            </template>
          </el-table-column>
          <el-table-column header-align="center" prop="RealName" label="姓名" min-width="50">
            <template slot="header">
              <span class="_css-tcolh">姓名</span>
            </template>
            <template slot-scope="scope">
              <span class="_css-ellipsis">{{scope.row.RealName}}</span>
            </template>
          </el-table-column>
          <el-table-column header-align="center" prop="Account" label="账号" min-width="50">
            <template slot="header">
              <span class="_css-tcolh">账号</span>
            </template>
            <template slot-scope="scope">
              <span class="_css-ellipsis">{{scope.row.Account}}</span>
            </template>
          </el-table-column>
          <el-table-column header-align="center" prop="Email" label="邮箱" min-width="100">
            <template slot="header">
              <span class="_css-tcolh">邮箱</span>
            </template>
            <template slot-scope="scope">
              <span class="_css-ellipsis">{{scope.row.Email}}</span>
            </template>
          </el-table-column>
              
          <el-table-column 
            header-align="center" 
            prop="OrganizeName" 
            label="所属机构" 
            
            min-width="100">
            <template slot="header">
              <span class="_css-tcolh">所属机构</span>
            </template>
            <template slot-scope="scope">
              <div class="_css-change-organize"> 
                <span class="_css-ellipsis _css-none-organize">
                  {{scope.row.OrganizeName}}
                </span>
              </div>
            </template> 
          </el-table-column>
          <el-table-column header-align="center" prop="RoleName" label="角色" min-width="150">
            <template slot="header">
              <span class="_css-tcolh">角色</span>
            </template>
            <template slot-scope="scope">
              <span class="_css-ellipsis">{{scope.row.RoleName }}</span>
            </template>
          </el-table-column>
        </el-table>
        <!-- //element 表格 -->
      </div>
    </div>
  </div>
</template>
<script>
import CompsUsersInput from "@/components/CompsAuth/CompsUsersInput";
export default {
  name: "Main",
  components: {
    CompsUsersInput
  },
  data() {
    return {
      extdata: {},
      inputtingtext: "", // 搜索文本框中的内容。getData 函数依赖此对象
      selectedIds: [],  // 选中的数据
      tableData: [],
      clickOrganizeButton: false,
      searchOrganizeName: '',
      organizeList: [],  // 公司机构名称数组
      clickTableUserId: '',
      nowIndex: -1,
      butFlagId: '',
      liststyle: {
        left: 0,
      },
      ProjectUsersLength:0
    };
  },
  mounted() {
    var _this = this;
    window.rolevue = _this;
    _this.$emit("onmounted", "projectmembers");
    _this.getData();
  },
  filters: {
    
  },
  methods: {
    // 点击编辑标签
    changeOrganizeName(event,userId) {
      let _this = this;
      let clientname = _this.$refs.editOrganizeName.getBoundingClientRect()
      _this.nowIndex=-1
      _this.searchOrganizeName = ''
      _this.liststyle.left = clientname.x - 340 || 0
      _this.getChangeOrganize();
      _this.clickTableUserId = userId
      
    },
    // input 输入
    searchOrganizeNameChange() {
      let _this = this
      setTimeout(() => {
        _this.getChangeOrganize()
      }, 300);
    },
    // 获取机构列表
    getChangeOrganize() {
      let _this = this
      _this.$axios
        .get(   
          `${window.bim_config.webserverurl}/api/User/User/GetAllTags?keyWord=${this.searchOrganizeName}&Token=${this.$staticmethod.Get('Token')}`
        ).then(res => {
          if (res.data.Ret > 0) {
            let dataArr = res.data.Data
            let listArr = []
            dataArr.forEach(element => {
              listArr.push({
                lable: element.FullName,
                value: element.OrganizeId
              })
            });
            _this.clickOrganizeButton = true;
            _this.organizeList = listArr
            
          } else {
            _this.$message.error(res.data.Msg);
          }

        }).catch(err=>{
          console.log(err)
        })
    },
    // 选择列表机构
    ChooseListFun(event,value,index) {
      let _this = this;
      _this.nowIndex = index
      _this.butFlagId = value
    },
    // 点击取消
    listChangeCancel() {
      let _this = this
      _this.clickOrganizeButton = false
    },
    // 点击确定提交
    changeOrganizeSubmit(value) {
      let _this = this
      let _Token = _this.$staticmethod.Get("Token");
      _this.$axios({
        method: 'post',
        url: `${window.bim_config.webserverurl}/api/User/User/ModifyUserTag`,
        data: {
          "UserId": _this.clickTableUserId,
          "butFlagId":  _this.butFlagId,
          "Token": _Token,
          "OrganizeId": _this.$staticmethod._Get("organizeId")
        }
      }).then(res=>{
        if (res.data.Ret > 0) {
          
          _this.getData();
          _this.clickOrganizeButton = false
        } else {
          _this.$message.error(res.data.Msg);
        }
      }).catch(err=>{

      })
    },
    getOrganizeButtonStyle() {
      let _this = this;
      let _s = {};
      _s["left"] = _this.liststyle.left + 0 + "px";
      return _s;
    },
    handleHelpTooptip(h, { column }) {
      alert(0)
        return (
            h('span', [
                h('el-tooltip', {
                    props: {
                        effect: 'dark',
                        content: 'q321321312',
                        placement: 'top'
                    }
                }, [
                    h('span', 'q321321312', {})
                ])
            ])
        );
    },
    do_removeSelMem(){
      var _this = this;
      var _Token = _this.$staticmethod.Get("Token");
      var _Url = `${window.bim_config.webserverurl}/api/User/User/RemoveUsersFromProject?token=${_Token}`;
      var _UserIds = '';
      for (var i = 0; i < _this.selectedIds.length; i++) {
        _UserIds += `${_this.selectedIds[i]}${i == _this.selectedIds.length - 1?"":","}`;
      }
      _this.$axios({
        method:'post',
        url: _Url,
        data: {
          "UserIds": _UserIds,
          "OrganizeId": _this.$staticmethod._Get("organizeId")
        }
      }).then(x => {
        if (x.data.Ret > 0) {
          _this.$message.success('操作成功');
          _this.getData();
        } else {
          _this.$message.error(x.data.Msg);
        }
      }).catch(x => {
        console.error(x);
      });

    },

    removeSelMem(){
      var _this = this;
      if (_this.selectedIds.length <= 0) {
        _this.$staticmethod.debugshowlog('未选中任何数据');
        return;
      }
      // 操作提示
      _this.$confirm("确定从项目中移除选中的成员？", "操作确认", {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(x => {
        _this.do_removeSelMem();
      }).catch(x => {
        _this.$staticmethod.debugshowlog('用户取消了操作');
      });
    },
    getbtndeldis(){
      var _this = this;
      return _this.selectedIds.length <= 0;
    },
    selectedAll(){
      var _this = this;
      console.log(_this.selectedIds.length,'=_this.selectedIds.length',_this.selectedIds.length == _this.tableData.length && _this.tableData.length > 0)
      return _this.selectedIds.length == _this.tableData.length && _this.tableData.length > 0;
    },
    switchsingle(userId) {
      var _this = this;
      if (_this.selectedIds.indexOf(userId) >= 0) {
        _this.selectedIds = _this.selectedIds.filter(x => x != userId);
      } else {
        _this.selectedIds.push(userId);
      }
    },

    selectedContains(userId) {
      var _this = this;
      return _this.selectedIds.indexOf(userId) >= 0;
    },

    switchall(ev) {
      var _this = this;
      if (_this.tableData.length == 0) {

      } else {
        // 如果有未选中的，则设置全部选中，否则显示全部不选中
        if (_this.tableData.length > _this.selectedIds.length) {
          // 设置全部选中
          _this.selectedIds = _this.tableData.map(x => x.UserId);
        } else {
          // 设置全部不选中
          _this.selectedIds = [];
        }
      }
    },

    // 根据输入的关键字，获取数据
    getData() {
      // 显示loading
      var _this = this;
      var _LoadingIns = _this.$loading({
        text: "操作中",
        target: document.getElementById("id_projmembers_body")
      }); 
      _this.selectedIds = [];
 
      var _Token = _this.$staticmethod.Get("Token");
      var ProjectId = _this.$staticmethod._Get("organizeId");
      var _OrganizeId = _this.$staticmethod._Get("_OrganizeId");
      var _LikeName = _this.inputtingtext;
      let Url = `${this.$urlPool.GetUserPaged}?PageNum=1&PageSize=3000&KeyWord=${_LikeName}&OrganizeId=${ProjectId}&searchType=0&RoleId=&Token=${_Token}`
      // 请求数据 // 隐藏loading
      _this.$axios
        .get(Url)
        .then(x => {
          if (x.data.Ret > 0) {
            _this.tableData = x.data.Data.list;
            _this.ProjectUsersLength =  x.data.Data.total
          } else {
            _this.$message.error(x.data.Msg);
          }
          _LoadingIns.close();
        })
        .catch(x => {
          console.error(x);
          _LoadingIns.close();
        });
    },
    search(ev) {
      var _this = this;
      _this.getData();
    },
    _oninput(str, para2, ev) {
      var _this = this;
      _this.inputtingtext = str;
      if (ev.keyCode == 13) {
        _this.getData();
      }
    }
  }
};
</script>
<style scoped>
._css-none-organize{
  min-width:70px;
  line-height: 26px;
}
._css-click-organize{
  position: absolute;
  top: 190px;
  width: 300px;
  max-height:600px;
  padding: 20px 10px 10px 10px;
  background: #fff;
  border: 1px solid rgba(0, 0, 0, 0.08);
  border-radius: 6px;
}
._css-list-organize{
  border-top: 1px solid rgba(0, 0, 0, 0.08);
}
._css-list-organize ul{
  text-align: left;
  margin-top:10px;
  padding-bottom: 10px;
  overflow-y: auto;
  max-height: 300px;
}
::-webkit-scrollbar {
  width:4px;
  background-color: #ededed;
}

/* 滚动槽 */
::-webkit-scrollbar-track {
  border-radius:10px;
  }

/* 滚动条滑块 */
::-webkit-scrollbar-thumb {
  border-radius:10px;
  background:#cccccc;
}
._css-list-organize ul li{
  line-height: 34px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  padding: 0 10px;
}
._css-list-submit{
  border-top: 1px solid rgba(0, 0, 0, 0.08);
  width: 100%;
  height: 44px;
  display: flex;
  justify-content:space-between;
}
._css-list-submit p._css-button-cancel{
  border: 1px solid #1890FF;
  background:#fff;
  color: #1890FF;
}
._css-list-submit p {
  border: 1px solid #1890FF;
  width: 120px;
  height: 36px;
  background: #1890FF;
  color: #fff;
  text-align: center;
  line-height: 36px;
  border-radius: 4px;
  cursor: pointer;
  margin: 8px auto;
}
._css-list-organize ul li._css-haveList:hover{
  cursor:pointer;
  background: rgba(0, 0, 0, 0.08);
}
._css-list-organize-click{
  color: #1890FF
}
._css-change-organize{
  width:100%;
  display:flex;
  justify-content:space-between;
  padding: 12px 0;
  line-height: initial;
}
._css-input-search{
  padding-left:10px;
}
._css-change-organize-name p{
  width: 76px;
  height: 24px;
  text-align: center;
  line-height: 24px;
  border-radius: 4px;
  border: 1px solid rgba(24, 144, 255, 1);
  color: rgba(24, 144, 255, 1);
  cursor: pointer;
}
._css-dis {
  cursor:not-allowed !important;
  opacity: 0.3;
}
._css-pm-rbtns{
  flex:1;
  height: 100%;
  box-sizing: border-box;
  padding-right: 24px;
  display: flex;
  align-items: center;
  flex-direction: row-reverse;
}
._css-ellipsis {
  text-overflow: ellipsis;
  overflow-x: hidden;
  white-space: nowrap;
}
._css-tcolh {
  font-size: 14px;
  color: rgba(0, 0, 0, 0.85);
  font-weight: 800;
}
._css-pm-btnsearch {
  width: 66px;
  height: 24px;
  line-height: 24px;
  border-radius: 4px;
  border: 1px solid rgba(24, 144, 255, 1);
  color: rgba(24, 144, 255, 1);
  cursor: pointer;
  margin-left: 24px;
}

._css-pm-btnsearch:hover {
  background-color: rgba(24, 144, 255, 1);
  color: #fff;
}

._css-pm-btn {
  width: 66px;
  height: 24px;
  line-height: 24px;
  border-radius: 4px;
  border: 1px solid rgba(24, 144, 255, 1);
  color: rgba(24, 144, 255, 1);
  cursor: pointer;
  margin-left: 24px;
}

._css-pm-btn:hover {
  background-color: rgba(24, 144, 255, 1);
  color: #fff;
}

.el-table {
  height: 100% !important;
}
._css-projmembers-all {
  height: 100%;
  box-sizing: border-box;
}
._css-projmembers-top {
  height: 54px;
  background-color: #fff;
  text-align: left;
  display: flex;
  align-items: center;
  font-size: 16px;
  font-weight: 500;
  color: rgba(0, 0, 0, 0.9);
}
._css-pmt-title {
  margin-left: 16px;
  line-height: 24px;
}
._css-projmembers-body {
  height: calc(100% - 54px);
}
._css-pm-thead {
  height: 64px;
  display: flex;
  align-items: center;
  padding-left: 24px;
  box-sizing: border-box;
}
._css-pm-tbody {
  height: calc(100% - 64px);
  box-sizing: border-box;
  padding: 0 24px 24px 24px;
}
* /deep/ .el-table__body-wrapper td {
  border-bottom: 1px solid rgba(0, 0, 0, 0.08) !important;
  border-right: 1px solid rgba(0, 0, 0, 0.08) !important;
  background-color: #fff;
}

* /deep/ .el-table__header-wrapper th {
  height: 44px !important;
  border-right: 1px solid rgba(0, 0, 0, 0.08);
  border-bottom: 1px solid rgba(0, 0, 0, 0.08);
  line-height: 44px !important;
  background-color: #fff;
}

* /deep/ .el-table {
  border: 1px solid rgba(0, 0, 0, 0.08) !important;
  background-color: #fff;
}
* /deep/ .el-tooltip__popper[x-placement^=top] .popper__arrow::after{
  background: rgba(0, 0, 0, 0.65) !important;
}
</style>