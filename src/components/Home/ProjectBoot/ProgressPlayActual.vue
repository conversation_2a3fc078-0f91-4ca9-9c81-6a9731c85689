<template>
    <div :class="[closeHover==false?'Vusual_cssmodel_progress':'_css-model-progress']"
    :style="{'width': (closeHover==false? getContainsLeft('iframe'):'100%')}">
        <div class="_css-model-close icon-suggested-close" @click="closeDialog" v-if="closeHover"></div>
        <div :class="[closeHover==false?'Vusual_csstimeline':'_css-time-line']"
            :style="{'width': (closeHover==false? getContainsLeft('timeline'):'')}">
           <div v-show="!isProgress">
                <div class="_css-play-time-text" v-if="playsign != 'end' && changeTime " :style="{'top': (closeHover==false? 6+'px':20+'px')}">
                    模拟时间：{{changeTime | flt_datetimefromList}}
                </div>
                
                <div class="_css-time-slider" v-if="!timeResNull">
                    <div class="_css-timeline" >
                        <el-slider
                            v-if="!tableTaskShow"
                            v-model="timeSlidervalue" 
                            :format-tooltip="timestepToolTip"
                            :max="sliderMax"
                            @change="sliderChange"
                            :marks="marks">
                        </el-slider>
                    </div>
                    <div class="_css-time-detail">
                        <div 
                        v-if="closeHover && playsign == 'end'"
                        :class="{'_css-reverse':openSetTimeBtn == true}"
                        class="_css-white-btn _css-modelplay-cfgbtn " @click="mockConfigClick">
                            <i class="icon-interface-set_se"></i>
                            {{getPlayCfgBtnText()}}
                        </div>
                        <!-- <div class="_css-playtime-input" v-if="openSetTimeBtn" :class="{'_css-disabled':playsign != 'end'}">
                            <el-input 
                                class="_css-playtime"  
                                maxlength="3"  
                                :disabled="playsign != 'end'"
                                onkeyup="this.value=this.value.replace(/[^\d.]/g,'');"
                                v-model="playTime" 
                                placeholder=""
                            ></el-input>秒
                        </div> -->
                        <div
                            v-if="playsign == 'end'"
                            class="playmodel"
                            @click="mockprogressclick"
                            :class="{'ban-click':bStartDis}">
                            <i class="icon-interface-play"></i>
                            开始模拟
                        </div>
                        <div
                            v-if="playsign == 'pause'"
                            class="playmodel"
                            @click="mockprogressclick"
                            :class="{'ban-click':false && loadingModelEnd == true}"><i class="icon-interface-play"></i>
                            继续模拟
                        </div>
                        <div
                            v-if="playsign == 'start'"
                            class="pausemodel"
                            @click="pausemodelClick"
                            :class="{'ban-click':false && loadingModelEnd == true}"><i class="icon-interface-time-out"></i>
                            暂停模拟
                        </div>
                        <div
                            v-if="playsign != 'end'"
                            class="stopmodel"
                            @click="stopPlayModel()"
                            :class="{'ban-click':false && loadingModelEnd == true}"><i class="icon-interface-stop"></i>
                            停止模拟
                        </div>
                            <!-- 计划模拟 -->
                        <el-select 
                        v-if="closeHover===false"
                        v-model="defaultModel_a" 
                        class="choosemodel" 
                        @change="changeModel">
                          <el-option
                            v-for="item in progressOptions"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value">
                          </el-option>
                        </el-select>
                    </div>
                </div>
                <div class="_css-null" v-if="timeResNull" >当前项目不存在进度关联数据</div>
           </div>
            
        </div>
        
        <div :class="[closeHover==false?'Vusual_model_iframe':'model-iframe']"
            :style="{'width': (closeHover==false? getContainsLeft('iframe'):'100%')}">
            <div class="_css-legend" :class="tableCollapseShow ? '_css-legend-big' : ''">
                <ul>
                    <li v-for="(item,index) in legendColor" :key="index" style="color:rgba(255,255,255,.8)">
                        <span class="_css-num">{{item.num}}</span>
                        <span class="_css-color icon-newface-pro-jd" :style="{color:item.color}"></span>
                    </li>
                </ul>
            </div> 
           <div class="progress-model" >
                <progressSceneLoading 
                    v-if="selectSceneIDChange"
                    :SceneId="selectSceneID" 
                    openfail="openScenefail"
                    @sceneLoadEnd="sceneLoadEnd"></progressSceneLoading>
           </div> 
            <div class="table-task shrink" :class="tableCollapseShow ? 'expand' : 'collapse'">
                <div class="collapse-table" @click="tableCollapseShow = !tableCollapseShow">
                    <i class="c1684FC" :class="tableCollapseShow ? 'icon-newface-pro-open' : 'icon-newface-pro-close'"></i>
                </div>
                <div class="collapse-table-list">
                    <ProgressPlayingListTable 
                        :allPlayingTableList="allPlayingTableList"
                    ></ProgressPlayingListTable>
                </div>
            </div>
        </div>
        
        <div class='_css-masking-edit-component' v-if="openSetTimeBtn">
          <progressActualPlaySet 
            :SetSceneObj="allPro_PlayObj"
            :beginTime="getPlayingData_.StartTime"
            :endTime="getPlayingData_.EndTime"
            @ok="modelPlaySetParams"
            @close="closeModelPlaySetDialog"
        ></progressActualPlaySet>
          
        </div>
        
    </div>
</template>
<script>  

import ProgressPlayingListTable from './ProgressPlayingListTable'
import progressSceneLoading from '@/components/CompsProgress/progressSceneLoading'
import progressActualPlaySet from '@/components/CompsProgress/progressActualPlaySet'
import { mapGetters } from 'vuex' 
// import modelNewIframeLoading from '@/components/Home/ProjectBoot/modelNewIframeLoading'

export default {
    data() {
        return { 
            openSetTimeBtn: false, // 判断模拟设置按钮状态是打开还是关闭
            dateStep: 1, // 每次播放一天为一个单位前进 
            timeSlidervalue:  0,
            sliderMax : null,
            marks: {},
            dateMin: null,   // 记录开始时间，最小时间
            dateMax: null,   // 记录结束时间，最大时间
            deteDiffDay: null,   // 记录开始时间和结束时间时间差
            diffStep: 1,  
            changeTime: null,
            timeResNull: false, 
            isProgress: true, 
            legendColor: [
                {color: 'rgba(6, 240, 68, 1)', num: '超前'},
                {color: 'rgba(22, 132, 252, 1)', num: '正常'}, 
                {color: 'rgba(247, 29, 8, 1)', num: '滞后'},
            ],
            elementArr: [], // 着色的构件和颜色

            playsign: 'end', // 当前的模型模拟状态 
            bStartDis: false, // 刚点击完开始模拟时，立即dis掉，当 playSign 修改为 start 时，立即取消dis.
            loadingModelEnd: true,
            playTimeHandler: 0,  
            setValue: 0,  // 模拟时候记录时间值
            timeAutoData: 1000, // 设置模拟播放时间
            playTime: null,   // 展示播放秒数
            playingBtn: true,
            sliderMinTime: '',  // 播放的最小时间
            sliderMaxTime: '',  // 播放的最大时间
            sliderTimeDiff: 0,
            revalue: 0,
            getModelData_: null,  // 接口返回的当前进度数据
            getPlayingData_: null,  // 开始播放使用的，接口返回的所有填报数据
            allPro_PlayObj: {},  // 该值为allPro_PlayObj的存值

            // 当前进度参数
            tableTaskShow: false, // 表格显示隐藏
            pullAheadList: [],
            normalList: [],
            hysteresisList: [],
            playingModelElement: [],  // 模拟播放时候着色的构件，
            modelLoading: true,

            actualTime: null, // 实时进度时间
            progressOptions:[{
              value: '1',
              label: '计划模拟'
            }, {
              value: '2',
              label: '实际模拟'
            }],
            defaultModel_a:this.defaultModel,
            VaultID: '',
            featureID: '',
            selectSceneIDChange:true,
            tableCollapseShow:true,  
            allPlayingTableList: [], // 模拟展示的表格内容
            setColorNormalColor: [],
            setColorAdvancedColor: [],
            setColorLagColor: [],
            
        };
    },
    props: {
        modelIframeSrc: {
            type: Object,
            required: false
        }, 
        allPlayObj: {
            type: Object,
            required: false
        }, 
        getModelData: {
            type: Object,
            required: false
        },
        getPlayingData: {
            type: Object,
            required: false
        },
        closeHover:{
          default:true,
          type: Boolean,
        },
        defaultModel: {
            type: String,
        },
        projectID: {
            type: String,
        },
        // 当前加载场景的场景id
        selectSceneID:{
            type:String
        }, 
        ProgressId: {
            type: String,
        },
    },
    filters: {
        flt_datetimefromserver(str) {
            var str1 = str;
            if (str) {
                str1 = str.replace("T", " ");
            }
            return str1;
        },
        flt_datetimefromList(str) { 
            var str1 = str;
            if (str) {
                str1 = str.substr(0,4) + '年' + str.substr(5,2) + '月' + str.substr(8,2) + '日'
            }
            return str1;
        }, 
    },
    components: {
        ProgressPlayingListTable,
        progressSceneLoading,
        progressActualPlaySet,
        // modelNewIframeLoading
    },
    watch: { 
        getModelLoad(data){
            // 使用vuex==设置模型加载状态
            if(data.state == "end"){

                this.modelloadingfun();
            }
        },
        selectSceneID(){
            // 场景直接隐藏在显示
            this.selectSceneIDChange = false;
            setTimeout(()=>{
                this.selectSceneIDChange = true;
            },10)
        },
        allPlayObj: {
            handler(val) { 
                this.allPro_PlayObj = val
                this.setColorAttribute()
            },
            deep: true

        }
    },
    computed: {
        ...mapGetters([
            'getModelLoad'
        ]),
    },
    created(){
        this.getPlayingData_ = this.getPlayingData;
        this.allPro_PlayObj =  this.allPlayObj
        let val = this.allPro_PlayObj

        this.allPro_PlayObj.NostartOpacity = val.NostartOpacity && val.NostartOpacity.length > 0 ? val.NostartOpacity * 1 : 30;
        this.allPro_PlayObj.NormalColor = val.NormalColor && val.NormalColor.length > 0 ? val.NormalColor : 'rgba(22, 132, 252, 0.5)';
        this.allPro_PlayObj.AdvancedColor = val.AdvancedColor && val.AdvancedColor.length > 0 ? val.AdvancedColor : 'rgba(6, 240, 68, 0.5)';
        this.allPro_PlayObj.LagColor = val.LagColor && val.LagColor.length > 0 ? val.LagColor : 'rgba(247, 29, 8, 0.5)';
        this.allPro_PlayObj.PlayTime = val.PlayTime && val.PlayTime.length > 0 ? val.PlayTime * 1 : this.getDaysBetween(this.getPlayingData_.StartTime,this.getPlayingData_.EndTime)
        this.playTime = this.allPro_PlayObj.PlayTime;
        this.setColorAttribute()
    },
    
    mounted() {
        let _this = this;
        this.projectID ? this.VaultID = this.projectID : this.VaultID = this.$staticmethod._Get("organizeId")
        
      
        _this.getModelData_ = _this.getModelData;
        
        // _this.getModelDataFun();  
        // _this.getPlayingDataFun(); 
        
    },
    methods: { 
        sceneLoadEnd(){
            setTimeout(() => {
                this.newModelGetAllElement()
                this.modelloadingfun();
            }, 1000);
        },
        // 新模型=着色
        newModelsetElementColor(eles,r,g,b,a){
            window.scene.execute('color',{objectIDs:eles,color:`rgb(${r},${g},${b})`,opacity:`${a}`})
        },
        // 新模型=删除着色
        newModelresetElement(r_eles,num){
            for(let i = 0; i < r_eles.length; i++ ){
                let element = window.scene.findObject(r_eles[i]); 
                element.resetColor();
            }
            window.scene.render();
        }, 
        //可视化面板切换进度模拟
        changeModel(value){
            this.$emit('changeModel',value)
        },
        
        // 获取所有构件
        newModelGetAllElement(){

            let _changemoreModelIds = this.modelIframeSrc.modelID.split('|')
            let elementIds = []
            for(let i = 0; i < _changemoreModelIds.length; i++ ){
                let allElements = window.scene.findFeature(_changemoreModelIds[i]).objects;
                elementIds = Array.from(allElements.keys())
                window.scene.execute('color',{objectIDs:elementIds,color:'rgb(0,0,0)',opacity:this.allPro_PlayObj.NostartOpacity * 0.01})
            }
            window.scene.render();
        },
        modelloadingfun(){ 
       
            this.modelLoading = false;
            this.isProgress = false;
            this.getPlayingDataFun()
            // // 获取当天、如果填报的数据小于当天，就选择填报数据的最后一天，如果大于当天，就选择当天
            // let _min = this.$options.filters["flt_datetimefromserver"](this.getPlayingData_.StartTime).substr(0, 10);
            // let _max = this.$options.filters["flt_datetimefromserver"](this.getPlayingData_.EndTime).substr(0, 10);
            // let nowDate = new Date()
            // let nowTime = this.$formatData.DateDiff(nowDate,_max);
            // console.log(nowTime,'[[[nowTime',)

            // this.sliderChange();  //  调用：初始化时候要显示当前天的数据
        },
        getPlayCfgBtnText(){
            var _this = this;
            if (_this.openSetTimeBtn == true) {
            return '关闭设置';
            } else {
            return '模拟设置';
            } 
        },
        mockConfigClick(){
            if(this.playsign == 'end'){
                this.openSetTimeBtn = !this.openSetTimeBtn
            }
        },
        // 初始化进度，点击开始播放时候使用的数据
        getPlayingDataFun(){
            let _this = this;
            let getPlayingData = _this.getPlayingData_;
            let _min = _this.$options.filters["flt_datetimefromserver"](getPlayingData.StartTime).substr(0, 10);
            let _max = _this.$options.filters["flt_datetimefromserver"](getPlayingData.EndTime).substr(0, 10);

            let nowTimeSlider = 0
            let _maxtime =  new Date(_max).getTime();  // 最大时间
            let _nowtime =  new Date().getTime();
            let diffDay = _this.$formatData.DateDiff(_min,_max);
            
            if(_nowtime >= _maxtime ){
                //   当前时间大于等于最大时间，那就取值最大时间
                nowTimeSlider = diffDay
            }else{
                //   当前时间不大于最大时间，那就取值当前时间
                nowTimeSlider = this.$formatData.DateDiff(_min,_nowtime);
            }
            // console.log(_min,_max,diffDay,'===diffDay')
            _this.dateMin = _min;
            _this.dateMax = _max;
            _this.deteDiffDay = diffDay;
            _this.diffStep = diffDay/10;
            _this.sliderMax = diffDay;
            _this.marks = {
                0: _min.toString(),
                [diffDay]: _max.toString()
            } 
            _this.actualTime = _max.toString()
            if(_min == null && _max == null){
                _this.timeResNull = true;
            }

            this.sliderChange(nowTimeSlider);
           
        },
        // 处理当前进度的数据
        getModelDataFun() {
            let _this = this;
            let getModelData = _this.getModelData_;
            let _min = _this.$options.filters["flt_datetimefromserver"](getModelData.StartTime).substr(0, 10);
            let _max = _this.$options.filters["flt_datetimefromserver"](getModelData.EndTime).substr(0, 10);
            let diffDay = _this.$formatData.DateDiff(_min,_max);
            
            _this.dateMin = _min;
            _this.dateMax = _max;
            _this.deteDiffDay = diffDay;
            _this.diffStep = diffDay/10;
            _this.sliderMax = diffDay;
            _this.marks = {
                0: _min.toString(),
                [diffDay]: _max.toString()
            } 
            if(_min == null && _max == null){
                _this.timeResNull = true;
            } 
        }, 

        getInFactSeconds(totalFrameCnt, inputSeconds) {
            
            let _this = this
            var inFactSeconds; // 实际总秒数
            var leastDelay = 0.3 * totalFrameCnt;
            var mostDelay = 1 * totalFrameCnt;
            if (inputSeconds && inputSeconds < leastDelay ) {
                _this.$message.warning('已为您自动计算接近输入数值的秒数')
                inFactSeconds = leastDelay ;
            } else if (inputSeconds && inputSeconds > mostDelay ) {
                _this.$message.warning('已为您自动计算接近输入数值的秒数')
                inFactSeconds = mostDelay
            } else {
                inputSeconds? inFactSeconds = inputSeconds : inFactSeconds = mostDelay 
            }
            return inFactSeconds;
        },
        // 删除模型着色
		removeIsolateElement(){
            let _this = this;
            if(this.playingModelElement.length == 0) return
            _this.newModelresetElement(this.playingModelElement,0)
            _this.playingModelElement = []; 
		},
       
        DateDiff(sDate1, sDate2) {
            var aDate, oDate1, oDate2, iDays;
            oDate1 = new Date(sDate1).getTime();
            oDate2 = new Date(sDate2).getTime();
            iDays = parseInt((oDate1 - oDate2) / 1000 / 60 / 60 / 24) //把相差的毫秒数转换为天数   
            return  iDays;
        }, 
        closeDialog() {
            let _this = this;
            _this.playingBtn = true; 
            _this.timeSlidervalue = 0;
            _this.setValue = 0;
            clearInterval(_this.playTimeHandler);
            _this.playsign = 'end';
            this.$emit('close')
        },
        // 时间轴
        timestepToolTip(value){ 
            let date = new Date(this.dateMin);
            date.setDate(date.getDate() + value);
            var datestrTool = this.$formatData.dateInit(date);
            return datestrTool;
        },

        // 时间轴change，每一帧都会走这里
        sliderChange(value) { 

            if(value > this.sliderMax){
                return;
            } 
            this.newModelGetAllElement();
            this.setValue = value;
            this.timeSlidervalue = value; 
            
            // 计算开始时间和结束时间的时间差，得到总的播放时间
            this.getListTimePoint(value);
        },
        getDateStr(dt) {
            let year = dt.getFullYear();
            let month = dt.getMonth() + 1;
            let dat = dt.getDate();
            month = month.toString().padStart(2, "0");
            dat = dat.toString().padStart(2, "0");
            let finalStr = `${year}-${month}-${dat}`;
            return finalStr;
        }, 
        // 根据时间获取所有模型ID着色
        getListTimePoint(inputVal){
            let _this = this; 
            this.lastPlayInputVal = inputVal
            let _n = new Date(_this.getPlayingData_.StartTime)
            let currentDate = new Date(_n);
            currentDate.setDate(_n.getDate() + inputVal);
            this.changeTime = this.getDateStr(currentDate);
            
            // 这里获取的是当前时间下的所有数据，直接取_this.getPlayingData_.Details[inputVal].List
            // console.log(this.allPro_PlayObj.AdvancedColor,'======time');
            // console.time()
            console.log(this.changeTime,_this.getPlayingData_.Details[inputVal].List)
            let list = _this.getPlayingData_.Details[inputVal].List;
           
            let filteredNot100 = list.filter(item => !(item.Progress_actualratio === "100.00" && item.Progress_unittime !==  _this.changeTime));


            // 判断Progress_planratio=100 删除着色、
            let list100 = list.filter(item => item.Progress_actualratio == "100.00");
            let listNot100 = list.filter(item => item.Progress_actualratio != "100.00");
           

            let normalArray = filteredNot100.filter(item => item.Progress_state === "正常" && item.PteElementIds.length > 0);
            let lagArray = filteredNot100.filter(item => item.Progress_state === "滞后" && item.PteElementIds.length > 0);
            let advancedArray = filteredNot100.filter(item => item.Progress_state === "超前" && item.PteElementIds.length > 0);

            
            let normalArrayElement = normalArray.flatMap(item => item.elementids);
            let lagArrayElement = lagArray.flatMap(item => item.elementids);
            let advancedArrayElement = advancedArray.flatMap(item => item.elementids);

            // console.log(this.setColorNormalColor,this.setColorLagColor,this.setColorAdvancedColor,'====setColorAdvancedColor')
            this.newModelsetElementColor(normalArrayElement,this.setColorNormalColor[0],this.setColorNormalColor[1],this.setColorNormalColor[2],this.setColorNormalColor[3])
            this.newModelsetElementColor(lagArrayElement,this.setColorLagColor[0],this.setColorLagColor[1],this.setColorLagColor[2],this.setColorLagColor[3])
            this.newModelsetElementColor(advancedArrayElement,this.setColorAdvancedColor[0],this.setColorAdvancedColor[1],this.setColorAdvancedColor[2],this.setColorAdvancedColor[3])
            // 判断已完成的构件 如果完成进度是100%、是原色
            this.allPlayingTableList = [
                {
                    color: this.allPro_PlayObj.AdvancedColor,
                    name:'超前',
                    data: advancedArray,
                },
                {
                    color: this.allPro_PlayObj.NormalColor,
                    name:'正常',
                    data: normalArray,
                },
                {
                    color: this.allPro_PlayObj.LagColor,
                    name:'滞后',
                    data: lagArray,
                }
            ]
            this.playingModelElement = [...normalArrayElement,...lagArrayElement,...advancedArrayElement]

            // console.timeEnd()
            console.log('======timeend');
            let resetColorelement = list100.flatMap(item => item.elementids);

            this.newModelresetElement(resetColorelement,1)
 
 
        },
        getElement(listElements) {
            let arr = [];
            listElements.forEach(item => {
                item.elementids.forEach(ids => {
                arr.push(`${item.modelid}^${ids}`);
                });
            });
            return arr;
        },
        // 开始模拟，继续模拟
        mockprogressclick() {
            let _this = this;
            if(this.playsign == 'end'){
                _this.sliderChange(0);
                _this.timestepToolTip(0);
            }
            this.newModelGetAllElement()

            _this.tableTaskShow = false;  // 点击开始模拟，隐藏当前进度的table
             
            let totalFrameCnt = _this.sliderMax;
            let playTimerAuto = _this.getInFactSeconds(totalFrameCnt, this.playTime) * 1000;
            let timeauto = parseInt(playTimerAuto/totalFrameCnt);
            _this.playTime = parseInt(_this.getInFactSeconds(totalFrameCnt, this.playTime)); 
            _this.marks = {
                0: _this.dateMin.toString(),
                [_this.deteDiffDay]: _this.dateMax.toString()
            }
            if(_this.timeSlidervalue == _this.sliderMax){
                _this.timeSlidervalue = 0;
                _this.setValue = 0;
                
                _this.sliderChange(0);
                _this.timestepToolTip(0);
            }
            
            _this.playTimeHandler = setTimeout(()=>{
                _this.frameExecuteUnit(timeauto,_this.setValue);
            }, timeauto);
            _this.playingBtn = false;
            _this.playsign = 'start';
            this.tableCollapseShow = false
        },
        frameExecuteUnit(timeauto,val){
            let _this = this;
            _this.revalue = val;
            if(val > _this.sliderMax){  
                _this.timeSlidervalue = _this.sliderMax;
                clearInterval(_this.playTimeHandler);
                _this.playsign = 'end';
                _this.setValue = _this.sliderMax;
                _this.sliderChange(_this.sliderMax);
                _this.timestepToolTip(_this.sliderMax);
                return
            }
            
            
            _this.playTimeHandler = setTimeout(()=>{
                _this.timeSlidervalue += _this.dateStep;
                _this.sliderChange( _this.timeSlidervalue);
                _this.timestepToolTip( _this.timeSlidervalue);
                _this.frameExecuteUnit(timeauto, _this.timeSlidervalue);
            }, timeauto);
        },
        getContainsLeft(c) {
          if(c=="timeline"){
            return (document.body.offsetWidth - 418-(document.body.clientWidth/100*5))+'px'
          }else if(c="iframe"){
            return (document.body.offsetWidth - 418)+'px'
          }
        },
        // 暂停模拟
        pausemodelClick() {
            let _this = this;
            _this.setValue = _this.revalue;
            _this.playingBtn = true;
            _this.playsign = 'pause';
            clearInterval(_this.playTimeHandler);
        },
        // 停止模拟
        stopPlayModel() {
            let _this = this;
            this.tableCollapseShow = true

            _this.openSetTimeBtn = false;
            _this.removeIsolateElement();
            _this.tableTaskShow = false;  // 点击开始模拟，隐藏当前进度的table
            _this.playingBtn = true;
            _this.marks = {
                0: _this.dateMin.toString(),
                [_this.deteDiffDay]: _this.dateMax.toString()
            }

            // 停止播放
            _this.playingModelElement = [];
            _this.sliderChange(0);
            _this.timestepToolTip(0);
            _this.timeSlidervalue = 0;
            _this.setValue = 0;
            clearInterval(_this.playTimeHandler);
            _this.playsign = 'end';
        }, 
        // 关闭实时进度
        closeTable(){
            this.tableTaskShow=false;
            this.stopPlayModel();
        },
        modelPlaySetParams(obj){
            // this.$emit('setPlay',obj)
            this.saveModelPlaySetAttribute(obj)
            this.closeModelPlaySetDialog()
        },
        closeModelPlaySetDialog(){
            this.openSetTimeBtn = false;
        },
        rgbaString(str) {
            // 将字符串转换为数组
            const arr = JSON.parse(str);
            // 使用解构赋值得到数组中的各个元素
            const [r, g, b, a] = arr;
            // 构建rgba字符串
            return `rgba(${r},${g},${b},${a})`;
        },
        // 设置模拟设置的值
        async saveModelPlaySetAttribute(obj){
            let params = {
                projectId: this.$staticmethod._Get("organizeId"),
                progressId: this.ProgressId,
                IntialColor: '',
                ...obj
            }
            const res = await this.$api.postAddProgressFillingSettingsAsync(params)
            if(res.Ret == 1){
                this.allPro_PlayObj = {
                    NostartOpacity: params.NostartOpacity && params.NostartOpacity !== '0' ? params.NostartOpacity * 1 : 30,
                    NormalColor: params.NormalColor && params.NormalColor.length > 0 ? params.NormalColor : 'rgba(22, 132, 252, 0.5)',
                    AdvancedColor: params.AdvancedColor && params.AdvancedColor.length > 0 ? params.AdvancedColor : 'rgba(6, 240, 68, 0.5)',
                    LagColor: params.LagColor && params.LagColor.length > 0 ? params.LagColor : 'rgba(247, 29, 8, 0.5)',
                    PlayTime: params.PlayTime && params.PlayTime.length > 0 ? params.PlayTime * 1 : this.deteDiffDay,
                    SceneId: params.SceneId,
                    SceneModelId: params.SceneModelId,
                }
                this.playTime = params.PlayTime * 1
                this.newModelGetAllElement()
                this.$nextTick(()=>{
                    this.setColorAttribute()
                    this.sliderChange(this.timeSlidervalue)
                })
            }
        },
        stringToArray(color){
            const match = color.match(/[\d.]+/g);
            const r = match[0]*1;
            const g = match[1]*1;
            const b = match[2]*1;
            const a = match[3]*1;
            let arr = [r,g,b,a]
            return arr;
        },
        setColorAttribute(){
            this.legendColor = [
                {color:  this.allPro_PlayObj.AdvancedColor, num: '超前'},
                {color:  this.allPro_PlayObj.NormalColor, num: '正常'}, 
                {color:  this.allPro_PlayObj.LagColor, num: '滞后'},
            ]
            this.setColorNormalColor = this.stringToArray(this.allPro_PlayObj.NormalColor)
            this.setColorAdvancedColor = this.stringToArray(this.allPro_PlayObj.AdvancedColor)
            this.setColorLagColor = this.stringToArray(this.allPro_PlayObj.LagColor)
        },
        getDaysBetween(sTime,eTime){
            let startDate = Date.parse(sTime);
            let endDate = Date.parse(eTime);
            let days=(endDate - startDate)/(1*24*60*60*1000);
            return  days;
        },
        


    },
    beforeDestroy() {
        clearInterval(this.playTimeHandler);
    },
    destroyed() {
        clearInterval(this.playTimeHandler);
    }
}
</script>
<style scoped>
.choosemodel{
  background: #d5c740;
  border-color: #d5c740;
  width: 105px;
  border: none !important;
  color: #fff;
}
.choosemodel /deep/ .el-input__inner {
  color: #fff;
}
.choosemodel /deep/ .el-select__caret{
  color: #fff;
}
._css-model-progress{
    width: 100%;
    height: 100%;
    position: fixed;
    z-index: 1000;
    top:0;
    left: 0;
    /* background: #fff; */
    background: #f7f7f7;
}
.Vusual_cssmodel_progress{
    width: 100%;
    height: 100%;
    position: fixed;
    z-index: 1000;
    top:100px;
    left: 418px;
    background: #f7f7f7;
}

._css-model-close{
    position: fixed;
    right: 10px;
    top: 10px;
    z-index:1000;
}
._css-model-close:hover{
    cursor: pointer;
}
._css-time-line{
    pointer-events: all;
    height: 50px;
    margin: 45px 2% 15px 3%;
    z-index: 1003;
}
.Vusual_csstimeline{
    pointer-events: all;
    height: 50px;
    margin: 30px 2% 15px 4%;
    z-index: 1003;
}
.model-iframe{
    position: fixed;
    top: 110px;
    left: 0;
    z-index: 1001;
    width: 100%;
    height: calc(100% - 110px);
    background: #b3c1cd;
}
.Vusual_model_iframe{
    position: fixed;
    top: 190px;
    left: 418px;
    z-index: 1001;
    width: 100%;
    height: calc(100% - 110px);
    background: #b3c1cd;
}
._css-time-slider{
    display: flex;
}
._css-time-slider /deep/ .el-slider__marks-text{
    white-space: nowrap;
}
._css-null{
    margin: 0 5%;
    text-align: left;
}
._css-time-detail{
    display: flex;
    text-align: right;
    line-height: 40px;
    font-size: 14px;
    font-weight: 500;
    margin-left: 35px;
    
}
._css-time-detail:hover{
    cursor: pointer;
}
._css-time-detail div{
    padding: 0 8px;
    height: 32px;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
    -ms-flex-direction: row;
    flex-direction: row;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    border-radius: 4px;
    border: 1px solid #1890ff; 
    margin-left: 16px;
    cursor: pointer;
}

._css-time-detail div.pausemodel:hover{
    opacity:1;
}
._css-time-detail div.pausemodel{
  background: #FAAD14;
  color: #fff;
  border-color: #FAAD14;
   opacity:.7;
}
._css-time-detail div.stopmodel{
  background: #F5222D;
  color: #fff;
  border-color: #F5222D;
   opacity:.7;
}
._css-time-detail div.stopmodel:hover{
    opacity:1;
}
._css-time-detail div.playmodel{
  background: #1DA48C;
  color: #fff;
  border-color: #1DA48C;
   opacity:.7;
}
._css-time-detail div.playmodel:hover{
    opacity:1;
}
._css-time-detail div.nowmodel{
    background: #d5c740;
    border-color: #d5c740;
    color: #fff;
}
._css-time-detail div.notallowed{
    background: #8a8a8a;
}
._css-time-detail div.nowDismodel{
    background: rgb(224, 240, 6,0.5);
}
._css-timeline{
    flex:1;
}
.content-list p{
    line-height: 30px;
}
.content-list{
    height: 200px;
    overflow-y: auto;
}
.content-list /deep/ .el-divider--horizontal{
    margin: 10px 0;    
}
._css-legend{
    position: fixed;
    bottom: 20px;
    right: 440px;
    z-index: 1002;
    text-align: left;
}
._css-legend-big{
    right: 30px;
}
._css-legend li,._css-legend-big li{
    line-height: 26px;
}
/* ._css-color{
    display: inline-block;
    width: 20px;
    height: 8px;
    border-radius: 1px;
    margin-right: 5px;
} */
div.ban-click{
  background: rgba(0, 0, 0, .25);
  color:#fff;
  cursor: not-allowed;
  border: 1px solid transparent;
}
._css-play-time-text{
    position: absolute;
    left: 3%;
    top: 20px;
    width: 215px;
    height: 34px;
    background: rgba(0,0,0,0.65);
    color: #fff;
    line-height: 34px;
    border-radius: 6px;
    font-size: 15px;
}
._css-play-time-text._css-actual-time{
    top: 45px;
}
._css-white-btn._css-reverse {
  background-color: rgba(24, 144, 255, 1);
  color:#fff;
  opacity: 0.7;
}

._css-white-btn._css-reverse:hover {
  opacity: 1;
  background-color: rgba(24, 144, 255, 0.9)
}
._css-white-btn {
  color:#1890FF;
}
._css-white-btn:hover{
  background-color: rgba(24, 144, 255, 0.1);
}
._css-playtime-input /deep/ .el-input.is-disabled .el-input__inner{
    background:transparent
}
._css-time-slider ._css-playtime-input{
    border-color: transparent;
    padding: 0;
    margin: 0;
}
._css-time-slider ._css-playtime-input /deep/ .el-input__inner{
  padding: 0;
}
._css-time-slider ._css-playtime {
    width: 30px;
    border-color: #a4a3a3;
    margin-right: 8px;
}
 
.progress-model{
  width: 100%; 
  height: 100%;
  background: #b3c1cd;
}

.el-select-dropdown__item{
  text-align: center;
}
.table-task{
    position: absolute;
    top: 0;
    right: 0;
    width: 416px;
    height:100%;
    z-index:1002;
    background:#fff;
}
.collapse-table{
    cursor: pointer;
    position: absolute;
    left: -10px;
    top: 50%;
    width: 10px;
    border-radius: 4px 0 0 4px;
    height: 100px;
    margin-top: -10px;
    background-color: #fff;
}
.collapse-table:before{
    content: "";
    width: 8px;
    height: 50px;
    display: inline-block;
    top: 50%;
    position: absolute;
    margin-top: -26px;
    background-color: #fff;
    left: -8px;
    border-radius: 4px 0 0 4px;
}
.collapse-table i{
    position: absolute;
    top: 50%;
    margin-top: -8px;
    margin-left: -10px;
}
.tableCollapse{
    transform: translate3d(100%,0,0);
    transition: transform 0.6s;
}
.shrink {
  transition: transform 0.6s;
  transform: translate3d(0, 0, 0);
}

.shrink.expand {
  transform: translate3d(100%, 0, 0);
}

.shrink.collapse {
  transform: translate3d(0, 0, 0);
}
.collapse-table-list{
    overflow-y: auto;
    height: 100%;
}
._css-masking-edit-component{
  position:fixed;
  top:0;
  left:0;
  bottom:0;
  right:0;
  width:100%;
  height:100%;
  background:rgba(0,0,0,.4);
  z-index:1002;
}
</style>