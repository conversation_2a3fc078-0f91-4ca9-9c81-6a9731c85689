<template>
    <div class="_css-clp-outer">
      <CompsComponentLibrary :injectclass="'libparent'"
        @onitemclick="evt_itemclick"
      >
      </CompsComponentLibrary>
    </div>
</template>
<script>
import CompsComponentLibrary from "@/components/CompsBoot/CompsComponentLibrary"
export default {
    components:{
        CompsComponentLibrary
    },
    mounted(){
        var _this = this;
        _this.$emit("onmounted", "componentLibParent");
    },
    methods:{

        // 构件库附件文件点击事件
        // --------------------
        evt_itemclick(item){

            // 判断类型，决定预览方式
            // --------------------
            var _this = this;
            var extnamewithdot = item.originname.substr(item.originname.lastIndexOf('.')).toLowerCase();

            // 所有 office 类型
            // ---------------
            var allofficeext = [
                '.docx'
                ,'.doc'
                ,'.pptx'
                ,'.ppt'
                ,'.xlsx'
                ,'.xls'
                ,'.png'
                ,'.jpg'
                ,'.gif'
                ,'.bmp'
                ,'.jpeg'
            ];

            // dwg
            // ---
            //debugger;
            var downloadurl = window.bim_config.webserverurl + item.bcf_path;
            var url_iframe_all;
            if ('.dwg' == extnamewithdot) {
                _this.$emit("set_projectboot_extdata", "_docviewtype", "dwg");
                url_iframe_all = `${
                _this.$configjson.dwgurl
                }/Home/Index2?dwgurlcfg=${encodeURIComponent(downloadurl)}&name=${
                item.originname
                }`;
            } else if (allofficeext.indexOf(extnamewithdot) >= 0) {
                _this.$emit("set_projectboot_extdata", "_docviewtype", "office");
                url_iframe_all = 
                _this.$staticmethod.computeViewUrl(downloadurl, item.originname);
            } else {
                _this.$message.warning('暂不支持预览该类型文件');
                return;
            }

            // set value to open preview frame
            // -------------------------------
            _this.$emit("set_projectboot_extdata", "_idocviewurl", 'about:blank');
            _this.$emit("set_projectboot_extdata", "_idocviewurl", url_iframe_all);
            _this.$emit("set_projectboot_extdata", "_show_idocview", true);
        },
    },
    data(){
        return {

        };
    }
}
</script>
<style scoped>
._css-clp-outer {
    height:calc(100% - 54px);
}

</style>
<style>
.component-library.libparent {
    height:100% !important;    
}
.component-library.libparent .library-right-details {
    
  
    /* height: calc(100% - 40px);
    margin-top: 40px; */

    height:100%;
    position: relative;
    padding-top:0px;
    box-sizing: border-box;
    
}

._detail_content.libparent {
  overflow-y: auto;
}

.top-functional-area.libparent {
    /* position:absolute;
    width:100%;
    top:0;
    left:0; */
}

.title.button-hover.libparent {
    height:40px;
    line-height: 40px;
}
div._css-extend-itemctn .libparent {
    width: 200px;
}
._css-extend-itemctn.libparent {
    display: flex;
    flex-wrap: wrap;
}
</style>