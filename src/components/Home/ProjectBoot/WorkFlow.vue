<template>
  <div class="_css-wf-mytasks">
		<!-- <workStart v-if="m_isall"></workStart>
		<myWorkFlow v-if="!m_isall"></myWorkFlow> -->
	</div>
</template>
<script>
// import workStart from '../../CompsNewWorkFlow/workStart'
// import myWorkFlow from '../../CompsNewWorkFlow/myWorkFlow'
export default {
  data() {
    return {
      m_isall: false,
    };
  },
	// components:{
	// 	workStart,
	// 	myWorkFlow
	// },
  watch: {
    $route(to, from) {
      let _this = this;
      if (to.params.IsAll == "1") {  // 流程列表
        // console.log("onmounted", "workflowlist"); 
        _this.m_isall = true;
				window.location.href = `${window.bim_config.hasRouterFile}/#/Home/start`
      } else {  // 我的流程
        // console.log("onmounted", "workflow");
        _this.m_isall = false;
				window.location.href = `${window.bim_config.hasRouterFile}/#/Home/todolist`
      }
    },
  },

  mounted() {
    let _this = this;
    if (_this.$route.params.IsAll) {
      console.log("onmounted", "workflowlist");
      _this.m_isall = true;
			window.location.href = `${window.bim_config.hasRouterFile}/#/Home/start`
			
    } else {
      console.log("onmounted", "workflow");
			window.location.href = `${window.bim_config.hasRouterFile}/#/Home/todolist`
      _this.m_isall = false;
    }
    // this.getToken()
  },
  methods: {
    /*
        getToken() {
            let _this = this;
            let _OrganizeId = _this.$staticmethod._Get("organizeId");
            let _username = _this.$staticmethod.Get("username");
            let _Token = _this.$staticmethod.Get("Token"); 
            let _url = `${window.bim_config.webserverurl}/api/User/User/CCFlowLogin`;
            let _para = {
                Token: _Token,
                organizeId: _OrganizeId
            };
            _this
                .$axios({
                    method: "post",
                    url: _url,
                    data: _para
                })
                .then(x => {
                    if (x.data.Ret > 0) {  // userno   uesrname   // OrgNo 机构ID
                        window.open(`${window.bim_config.CCFlowUrl}/Portal/Standard/Default.htm?Token=${x.data.Data}&UserNo=${_username}&OrgNo=${_OrganizeId}`, "_blank")
                    } else {
                        _this.$message.error(x.data.Data);
                    }
                    console.log(x)
                })
                .catch(x => {
                    console.error(x);
                });
        }, 
        */
  },
};
</script>
<style scoped>
._css-wf-mytasks {
  text-align: left;
}
._css-wf-mytasks /deep/ .el-button:not(.el-picker-panel__link-btn) {
  padding: 12px 20px;
  /* margin: 12px 20px; */
  border-radius: 4px;
}
</style>