<template>
  <div class="_css-main-content" :class="{ 'main-max-width': isLargeScreen, 'main-min-width': isSmallScreen }" >
    <div class="login-user">
      欢迎 <font>{{currentUser.RealName}}</font>
      <!-- 设置项目到期时间，谢鹏把这里的入口删除了 -->
      <!-- <span class="end-time" :class="{'_css-notvisible':cfg_ifhidedeadline()}">
        项目到期时间：<font style="color:#999;" v-show="!showEndTimeEditor">{{TransEndTime()}}</font>
        <i class="icon-interface-edit-model" v-if="showEndTimeIcon&&!showEndTimeEditor" @click="showEditEndTime"></i>
        <el-date-picker class="txtEndTime" v-if="showEndTimeEditor" ref="txtEndTime"
          v-model="ShowProject_EndTime"
          @change="editProjectEndTime"
          type="date"
          placeholder="请选择时间">
        </el-date-picker>
        <button class="cancel" v-if="showEndTimeEditor" @click="showEndTimeEditor=false">取消</button>
      </span> -->
    </div>
    <div class="index-wrap">
      <div class="project-content left-wrap">
        <div class="wrap-content content-left bg-white margin-bottom">
          <MainProject
            :currentisProjectManager="currentisProjectManager"
            :isScreenParams="isLargeScreen"
            @time="getShowProject_EndTime"
          ></MainProject>
          
        </div>
        <div class="wrap-content content-left content-bottom">
          <main-invest
            :currentisProjectManager="currentisProjectManager"
            :isScreenParams="isLargeScreen"
          ></main-invest>
        </div>
      </div>
      <div class="project-content right-wrap">
        <div class="wrap-content bg-white margin-bottom">
          <main-notice 
            :isScreenParams="isLargeScreen"
            :currentisProjectManager="currentisProjectManager">
          </main-notice>
        </div>
        <div class="wrap-content content-bottom bg-white">
          <MainParticipatingUnit 
            :currentisProjectManager="currentisProjectManager" 
            :isScreenParams="isLargeScreen"
          ></MainParticipatingUnit>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import MainProject from '../../CompsMainProject/MainProject'; // 基本信息
import MainNotice from '../../CompsMainProject/MainNotice'; // 项目公告
import MainParticipatingUnit from '../../CompsMainProject/MainParticipatingUnit'; // 参建单位
import MainInvest from '../../CompsMainProject/MainInvest'; // 投资模块
export default {
  name: 'Main',
  data() {
    return {
      token: '',
      organizeId: '',
      isLargeScreen: window.innerWidth > 1512,
      isSmallScreen: window.innerWidth <= 1512,
      currentUser:{},   // 用户信息
      showEndTimeEditor:false,
      showEndTimeIcon:true,
      Project_EndTime:-1,
      ShowProject_EndTime:"",
      currentisProjectManager: false, // 当前操作人是否为当前项目的项目管理员
    };
  },
  components:{
    MainProject,
    MainNotice,
    MainParticipatingUnit,
    MainInvest,
  },
  created() {
    this.setCurrent();  // 获取登录人信息 
    window.addEventListener('resize', this.handleResize);
  },
  destroyed() {
    window.removeEventListener('resize', this.handleResize);
  },
  mounted(){
    this.token = this.$staticmethod.Get("Token");
    this.organizeId = this.$staticmethod._Get("organizeId");
    window.mainvue_zjr = this;
    this.$emit('onmounted', 'main');
    this.loadprojectadmin();
  },
  methods: {
    // 屏幕尺寸
    handleResize() {
      this.isLargeScreen = window.innerWidth > 1512;
      this.isSmallScreen = window.innerWidth <= 1512;
    },
     // 获取登录人信息 
    setCurrent(){
      if(this.$staticmethod.Get("IsSystem")=='1'){
        this.showEndTimeIcon=true;
        this.currentUser.RealName='超级管理员';
        this.currentUser.Account='System';
        this.currentUser.UserId='System';
      }
      else{
        this.showEndTimeIcon=false;
        this.currentUser.RealName=window.localStorage.RealName;
        this.currentUser.Account=window.localStorage.Account;
        this.currentUser.UserId=window.localStorage.UserId;
      }
    },
    getShowProject_EndTime(time){
      if(time){
        this.Project_EndTime = time.substr(0,10);
      }
    },
    // currentisProjectManager 赋值，即当前操作人是否为当前项目的项目管理员
    loadprojectadmin() {
      let _this = this;
      if(!this.token || !this.organizeId) return
      _this
        .$axios({
          method: "get",
          url: `${_this.$configjson.webserverurl}/api/User/Project/TestTokenIsProjectManager?Token=${this.token}&OrganizeId=${this.organizeId}`
        })
        .then(x => {
          _this.currentisProjectManager = false;
          if (
            x.status == 200 &&
            x.data.Ret > 0 &&
            x.data.Data &&
            x.data.Data.IsProjectMgr == true
          ) {
            _this.currentisProjectManager = x.data.Data.IsProjectMgr;
          }else if(x.data.Ret == -9999){
            this.$message.error(x.data.Msg);
            return
          }
        })
        .catch(x => {
        });
    },
    // 是否隐藏到期时间
    cfg_ifhidedeadline(){
      if (window.bim_config.custom_hideProjectDeadline) {
        return true;
      } else {
        return false;
      }
    },
    // 转换到期时间
    TransEndTime(){
      if(this.Project_EndTime && this.Project_EndTime.length > 0)
      {
        return this.Project_EndTime.slice(0,10);
      }else{
        return '--/--/--';
      }
    }, 
    // 编辑有效时间
    editProjectEndTime(e){
      if(e==null||e==undefined){
        this.$message({type:'error',message:'请设置有效的时间'});
        return;
      }
      this.$confirm("是否设置该时间为项目到期时间？","确认设置",{confirmButtonText:'确定',cancelButtonText:'取消'}).then(res=>{
        //debugger;
        let endDate=this.$formatData.formatDateCheck(e).substr(0,10)
        
        let loading=this.$loading({
          lock:true,
          text:'正在执行...',
        });
        let data= {
            ProjectId: this.organizeId,
            EndTime: endDate,
            Token:this.token
        }
        this.$axios.post(this.$urlPool.SetOrganzieEndTime,data).then(res=>{
          this.$message({type:'success',message:'保存成功'});
          this.Project_EndTime=endDate;
          this.showEndTimeEditor=false;
          loading.close();
        });
      }).catch(res=>{
        this.ShowProject_EndTime='';
        this.showEndTimeEditor=false;
      });
    },
    showEditEndTime(){
      this.showEndTimeEditor=!this.showEndTimeEditor;
    },
  }
};
</script>

<style lang="scss" scoped>
._css-main-content{
  line-height: 1;
  display: flex;
  flex-direction: column;
  text-align: left;
  height: 100%;
  .login-user{
    width: calc(100% - 16px);
    text-align: left;
    font-size:16px;
    font-family: 黑体;
    font-weight: 400; 

    line-height: 50px;
    height: 50px;
    background-color: #fff;
    padding-left: 16px;
    border-bottom: 1px solid rgba(0,0,0,.15);
  }
  .login-user font{
    color:#4d9fff;position: relative;
  }
  .end-time{
    font-size: 13px;
    padding-left: 100px;
    color: #999;
    position: relative;
  }
  .module-content{
    background: #FFFFFF;
    border-radius: 8px;
  }
  .index-wrap{
    flex: 1;
    // flex-direction: column;
    overflow: auto;
    padding: 16px 24px;
    display: flex;
    justify-content: space-between;
    .project-content{
      display: flex;
      flex-direction: column;
      flex: 1;
    }
  
    .wrap-content{
      border-radius: 8px;
    }
    .content-bottom{
      flex: 1;
    }
    
  }
}
._css-main-content /deep/ .project-dialog{
  display: flex;
  .ltitle-label{
    width: 130px;
  }
  .dialog-content{
    flex: 1;
  }
  .el-date-editor.el-input, .el-date-editor.el-input__inner{
    width: 100%;
  }
  .el-input--prefix .el-input__inner{
    padding-left: 16px;
  }
  .el-input__prefix{
    left: 90%;
  }
  .el-icon-date:before{
    content: '';
    width: 16px;
    height: 16px;
    background-image: url('../../../assets/images/m-date-editor.png');
    background-size: 100%;
    margin-right: 4px;
  }
}
._css-main-content /deep/ ._css-zdialog{
  margin: 20px 20px 0;
  .el-input__inner{
    border-width: 1px;
    line-height: 28px;
    height: 28px;
  }
  .el-form-item{
    margin-bottom:16px ;
  }
  .el-form-item__content{
    line-height: 28px;
    display: flex;
  }
  .flex-total{
    display: flex !important;
    flex: 1;
    .el-select{
      background: rgba(229, 229, 229, 1);
      .el-icon-arrow-up:before{
        content: "\e790";
        // display: inline-block;
        // width: 0px;
        // height: 0px;
        // border-bottom: 8px solid rgba(136, 136, 136, 1);
        // border-left: 8px solid transparent;
        // border-right: 8px solid transparent;
        // border-top: 8px solid transparent;
      }
    }
  }
}
._css-main-content /deep/ ._css-dialog-btn {
    display: flex;
    flex-direction: row-reverse;
    height: 64px;
    align-items: center;
    box-sizing: border-box;
    padding-right: 8px;
}
._css-main-content /deep/ 
.item-top-bar {
	display: flex;
	justify-content: space-between;
	line-height: 24px;
	font-size: 18px;
	font-family: PingFangSC, PingFang SC;
	font-weight: 500;
	color: #222222;
	.title:before {
		content: " ";
		display: inline-block;
		width: 4px;
		height: 16px;
		margin: 0 10px -2px 0;
		background: rgba(24, 144, 255, 1);
	}
  .modify-btn {
    color: #1890ff;
    border: 1px solid #1890ff;
    border-radius: 4px;
    padding: 4px 12px;
    box-sizing: border-box;
    font-size: 12px;
    cursor: pointer;
  }
}
.main-max-width /deep/{
  .left-wrap{
    min-width: 1090px;
  }
  .content-left {
    // width: 1090px;
    // min-width: 1010px;
    margin-right: 20px;
  }
  .margin-bottom{
    margin-bottom:20px;
  }
  .project-content.right-wrap{
    min-width: 440px;
  }

}

.main-min-width /deep/{
  .left-wrap{
    min-width: 710px;
  }
  .content-left {
    // width: 710px;
    // min-width: 710px;
    margin-right: 16px;
  }
  .margin-bottom{
    margin-bottom: 14px;
  }
  .project-content.right-wrap{
    min-width: 350px;
  }
}
</style>