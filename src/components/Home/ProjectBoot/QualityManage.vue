<template>
  <div :class="[{zIndex: parentZindex},'quality-manage']">
    <div class="top-bar">
      <span v-for="(item,index) in iconMenu"
            @click="toggleTemplate(index)"
            :class="[{active: toggleTemplateState == index},'tab-title']" :key="index">{{ item.title }}</span>

      <!--<span @click="toggleTemplate" :class="[{active: toggleTemplateState},'icon icon-interface-list-fill']"></span>-->
      <!--<span @click="toggleTemplate" :class="[{active: !toggleTemplateState},'icon icon-interface-model-statistics']"></span>-->
      <!--<span @click="toggleTemplate" :class="[{active: !toggleTemplateState},'icon icon-interface-louceng']"></span>-->
      <div class="quality-search-container" v-if="toggleTemplateState==0">
        <i class="icon icon-interface-search"></i>
        <input
          v-model="searchInfo"
          type="text"
          class="quality-search"
          placeholder="搜索（标题）">
      </div>

      <!--<div class="invite-colleagues">-->
        <!--<i class="icon icon-interface-authorization"></i>-->
        <!--邀请同事进项目-->
      <!--</div>-->
    </div>

    <div class="container">
      <CompsQualityList
        :searchCheckDetails="searchInfo" 
        @getTaskType="getQualityTaskType"
        :typelist="'1'"
        @getQualityList="getQualityList"
        @updateComponentsKey="updateComponentsKey"
        :key="updateKeyData"
        v-if="toggleTemplateState==0">
      </CompsQualityList>
      <CompsQualityStatistics 
        :qualityTaskType="qualityTaskType"
        :typelist="'1'"
        :qualityList="qualityListData"
        v-if="toggleTemplateState == 1">
      </CompsQualityStatistics>
      <!--<CompsQualityManageSet @onclose="toggleSetting" v-if="toggleTemplateState==2"></CompsQualityManageSet>-->
    </div>
  </div>
</template>

<script>
  import CompsQualityList from '@/components/CompsQuality/CompsQualityList'
  import CompsQualityStatistics from '@/components/CompsQuality/CompsQualityStatistics'
  // import CompsQualityManageSet from '@/components/CompsQuality/CompsQualityManageSet'

  export default {
    components: {
      CompsQualityList,//质量管理列表
      CompsQualityStatistics,//统计图
      // CompsQualityManageSet,//组织结构树
    },

    data() {
      return {
        showSetting: 0,
        parentZindex: false,
        toggleTemplateState: 0,//切换板块状态 0为列表 1为统计
        iconMenu: [
          {id: 0, title: '任务列表'},
          {id: 1, title: '统计报表'},
        ],
        updateKeyData: 10000,
        searchInfo: '',//搜索（检查详情）
        qualityListData:[],//列表数据
        qualityTaskType:[],//数据类别
      };
    },

    created() {
      let sign = this.$route.query;
      if (sign.Setting) {
        this.toggleTemplate(2);
      }
    },
    mounted() {
      var _this = this; 
      sessionStorage.setItem('typelist', '1')
      
      // 高亮菜单项
      _this.$emit('onmounted', 'QualityManage');
      window.debugvue = _this;
    },

    methods: {
      getQualityList(data) {
        // console.log(data)
        //现场管理列表数据
        this.qualityListData = data;
      },

      //获取数据类别
      getQualityTaskType(data) {
        // console.log(data)
        this.qualityTaskType = data;
      },

      //更新组件key值
      updateComponentsKey() {
        this.updateKeyData = new Date().getTime();
      },

      //打开/关闭 工程结构树页面
      toggleSetting(url) {
        window.location.href = url.split('?')[0];
        this.toggleTemplateState = 0;
        this.parentZindex = 0;
        if (this.$route.query.Setting) {
          document.querySelector('.clicked.iconlevel-2').previousElementSibling.click();
        }
      },

      //切换板块
      toggleTemplate(index) {
        this.toggleTemplateState = index;
        this.parentZindex = index;
      },
    },
    

    // watch: {
    //   '$route.query': function (n,o) {
    //     if (n.Setting) {
    //       this.toggleTemplate(2)
    //     }
    //   }
    // }
  }
</script>
<style scoped>
  .quality-manage {
    position: relative;
    height: 100%;
  }

  .quality-manage.zIndex {
    z-index: 1001;
  }

  .quality-manage > .top-bar {
    position: relative;
    z-index: 1;
    height: 54px;
    display: flex;
    align-items: center;
    padding: 0 24px;
    background-color: #FFFFFF;
    box-shadow:0 1px 3px 0 rgba(0,21,41,0.12);
  }

  .quality-manage > .top-bar > .tab-title {
    font-size: 16px;
    padding: 3px;
    cursor: pointer;
    margin-right: 24px;
    color:rgba(166,174,182,1);
  }

  .quality-manage > .top-bar > .tab-title.active,
  .quality-manage > .top-bar > .tab-title:hover {
    font-weight: 500;
    color: rgba(40,58,79,1);
  }

  .quality-manage > .top-bar > .icon:last-of-type {
    visibility: hidden;
  }

  .quality-manage > .top-bar .quality-search-container {
    position: absolute;
    margin: 0 auto;
    left: 0;
    right: 0;
    width:270px;
    height:40px;
  }

  .quality-manage > .top-bar .quality-search-container .icon {
    position: absolute;
    left: 5px;
    top: 11px;
  }

  .quality-manage > .top-bar .quality-search-container .quality-search {
    width: 100%;
    height: 100%;
    border: none;
    outline: none;
    border-radius:4px;
    background-color: #F7F7F7;
    padding: 9px 16px 9px 30px;
    box-sizing: border-box;
    font-weight: 500;
  }

  .quality-manage > .top-bar .invite-colleagues {
    cursor: pointer;
    position: absolute;
    right: 230px;
    font-weight: 500;
    color: rgba(0,122,255,1);
  }

  .quality-manage .container {
    padding: 24px;
    height: calc(100% - 112px);
    overflow: auto;
  }
</style>
