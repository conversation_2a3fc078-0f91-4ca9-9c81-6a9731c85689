<template>
  <div class="_css-materials-all" ref="cssMaterialsAll" @click="closeall(true)">
    <!-- 自动关联进度条开始 -->
    <div class="_css-auto-element-relation" v-if="LoadingAutoElement">
      <model-loading-progress
          v-if="autoLoadingSuccess"
          class="_css-table-loading"
          :progressTextShow="true"
          :progressText="loadingText"
          :percentage="loadingPercentage">
      </model-loading-progress>
    </div>
    <!-- 自动关联进度条结束 -->
    <main>
        <slot name="btn"></slot>
    </main>
    <!-- 下拉子项，新增、导入及导出 -->
    <input
      type="file"
      style="display:none"
      id="id_importbmc_File"
      accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
      @change="ev_importbmcChange()"
    />
    <!-- 上下文菜单 -->
      <div
      v-show="m_contextpos2.isshowing"
      class="_css-flow-contextmenu"
      :style="style_getContextStyle2()"
      >
      <div @click="func_showbmcediting($event)"
      class="_css-flow-contextmenu-i">
          <div
          class="_css-flow-contextmenu-in">
          <div class="_css-flow-contextmenu-inicon icon-interface-addnew"></div>
          <div class="_css-flow-contextmenu-intext">新增</div>
          </div>
      </div>
      <div
      @click="func_showbmcimporting($event)"
        class="_css-flow-contextmenu-i">
          <div
          class="_css-flow-contextmenu-in">
          <div class="_css-flow-contextmenu-inicon icon-interface-cloud-upload"></div>
          <div class="_css-flow-contextmenu-intext">导入全部</div>
          </div>
      </div>

      <div @click="func_exportall($event)"
        class="_css-flow-contextmenu-i">
          <div
          class="_css-flow-contextmenu-in">
          <div class="_css-flow-contextmenu-inicon icon-interface-download-fill"></div>
          <div class="_css-flow-contextmenu-intext">导出全部</div>
          </div>
      </div>

      </div>
      <!-- //上下文菜单 -->
    <div
    v-show="m_contextpos.isshowing"
    class="_css-flow-contextmenu"
    :style="style_getContextStyle()"
    >
    <div
    @click="func_renamebmc($event)"
    class="_css-flow-contextmenu-i">
        <div
        class="_css-flow-contextmenu-in">
        <div class="_css-flow-contextmenu-inicon icon-interface-edit_se"></div>
        <div class="_css-flow-contextmenu-intext">重命名</div>
        </div>
    </div>
    <div
    @click="func_delbmc($event)"
      class="_css-flow-contextmenu-i">
        <div
        class="_css-flow-contextmenu-in">
        <div class="_css-flow-contextmenu-inicon icon-interface-model-delete"></div>
        <div class="_css-flow-contextmenu-intext">删除</div>
        </div>
    </div>
    </div>
    <!-- //上下文菜单 -->

    <!-- 新增或编辑分类对话框 -->
    <zdialog-function
    v-if="status_bmcadd"
    :init_title="m_bmcedittitle"
    :init_zindex="1000"
    init_closebtniconfontclass="icon-suggested-close"
    @onclose="func_closebmcedit()"
    >
        <!-- 输入区域 -->
        <div slot="mainslot">
          <div class="_css-line css-common-line">
          <div class="_css-title _css-title-flowname">
            请输入名称：
            </div>
            <div class="_css-fieldvalue css-common-fieldvaluename">
              <input v-model="m_bmceditingname"
              @mousedown="_stopPropagation($event)"
               type="text" class="css-common-fieldvaluename-in">
            </div>
          </div>
        </div><!-- //输入区域 -->

        <div slot="buttonslot" class="css-common-zdialogbtnctn" >
           <zbutton-function
              :init_text="'取消'"
              :init_fontsize="14"
              :debugmode="true"
              :init_height="undefined"
              :init_width="'76px'"
              :init_bgcolor="'#fff'"
              :init_color="'#1890FF'"
              @onclick="func_closebmcedit()"
              >
          </zbutton-function>
            <zbutton-function
              :init_text="'保存'"
              :init_fontsize="14"
              :debugmode="true"
              :init_height="undefined"
              :init_width="'76px'"
              @onclick="func_savebmcadd()"
              >
          </zbutton-function>
        </div>
    </zdialog-function>
    <!-- //新增或编辑分类对话框 -->

    <!-- 上方的tab control -->
    <div class="_css-tabcontrolarea">
      <div
        class="_css-tabcontrolitem _css-tabcontrolalltype"
        :class="{'_css-clicked':true || extdata.maintype == 'all'}"
      >{{ headerTitleText }}</div>
    </div>
    <!-- //上方的tab control -->

    <div class="_css-bottomarea">
      <!-- 左列表，右表格 -->
      <!-- <transition name="fade"> -->
        <div
        @click="func_clearselect($event)"
        class="_css-bottomlist" v-show="tableClose">
          <!-- v-if="tableClose" -->
          <div class="_css-materialtypelist-head">
            <!-- 图标，文本和添加按钮 -->
            <!-- <div class="_css-materhead-icon icon-interface-material-management"></div> -->

            <!-- 所有构件分类上面的标题 -->
            <div class="_css-materhead-text">工程单元</div>
            <!-- //所有构件分类上面的标题 -->


            <!-- 新构件分类需要lr-admin权限 -->
            <!-- <div @click="func_showbmcediting($event)"
              v-if="showEdit? true: false"
              class="_css-materhead-addbtn icon-interface-addnew"
            ></div> -->
             <div @click="func_showwbsmgr($event)"
              v-if="showEdit? true: false"
              class="_css-materhead-addbtn icon-interface-more"
            ></div>

            <!-- //新构件分类需要lr-admin权限 -->

            <!-- //图标，文本和添加按钮 -->
          </div>

          <div
          @click.stop="_stopPropagation($event)"
          v-if="extdata.bShowNewType" class="_css-addMaterialtypelist" :style="getAddListstyle()">

            <div class="_css-typeitem-title">
              新增分类
              <span class="_css-typeitem-close icon-interface-guanbimoxing" @click.stop="closeall()"></span>
            </div>
            <div class="_css-typeitem-add-input">
              构件类别
              <input
                class="_css-newtypename-text"
                type="text"
                v-model="extdata.newtypename"
                id="id_newtypename"
                placeholder="请输入构件类别名称"
              />
            </div>
            <div class="_css-typeitem-add-sure">
              <p @click.stop="clickAddListSure()">确定</p>
            </div>
          </div>

          <div
            class="_css-materialtypelist css-miniscroll"
            :class="{'_css-withadding': extdata.bShowNewType}"
          >

            <!-- 原来的循环数据（所有构件分类）切回 true ，查看原来页面，改为 false，使用新版的树 -->
            <template v-if="false">
              <div

              :data-bcguid="materialtype.materialtypeid"
                v-for="materialtype in extdata.materialtypes"
                :key="materialtype.materialtypeid"
                class="_css-materialtypeitem"
                :class="{'_css-clicked': materialtype.materialtypeid == extdata.selectedtypeid}"
                @click.stop="selecttype(materialtype.materialtypeid,materialtype.materialtypename,materialtype)"
              >
                <div
                  class="_css-newmaterialtypename"
                  v-if="extdata.renamingtypeid == materialtype.materialtypeid"
                >
                  <el-tooltip
                    popper-class="css-no-triangle"
                    class="item"
                    effect="dark"
                    content="回车确认；ESC取消"
                    placement="right"
                  >
                    <input
                      @blur="renametypenameblur()"
                      @keyup="renametypenamekeyup($event)"
                      :id="'itemid_' + materialtype.materialtypeid"
                      class="_css-newtypename-text"
                      type="text"
                      v-model="extdata.renamingtypename"
                      placeholder="请输入构件类别名称"
                    />
                  </el-tooltip>
                </div>
                <template v-else>
                  <div class="_css-materialitemicon icon-interface-sanweidiejia"></div>
                  <div class="_css-materialitemname">
                    <span :class="{'_css-material-overflow':materialtype.materialtypename.length>9}">{{materialtype.materialtypename}}</span>
                    <span class="_css-material-number">&nbsp;•&nbsp;{{materialtype.MaterialCnt}}</span>
                  </div>


                  <div
                    :class="hasEditAuth ? '' : '_css-dis'"
                    class="_css-materialitemnum"
                    @click.stop="showtypeitemmenu($event, materialtype)"
                  >
                    <i class="icon-interface-more"></i>
                  </div>
                </template>
              </div>
            </template>
            <template v-else >

              <!-- 可滚动的树形区域，包含树头 -->
              <div
              class="_css-treearea">

                  <!-- 树控件 -->
                  <!-- 1. data 属性指向数组，数组的元素属性名称暂任意指定 -->
                  <!-- 2. 如果是懒加载树，需要指定 lazy，及 props 属性，props 属性值如下：
                      treem_props:{
                          children: "children",
                          label: "wftc_name",
                          isLeaf: "isLeaf"
                      },
                  -->
                  <!-- 3. 指定 expand-on-click-node 为 false，表示只能点击小箭头来收展。 -->
                  <!-- 4. 指定 回调函数 node-collapse 及 node-expand，用来设置 classname，以展示不同的图标 -->
                  <!-- 5. 指定加载子节点函数：load -->
                  <!-- 6. 自动展开父节点 -->
                  <!-- getCurrentNode -->

                  <el-tree :data="m_bmclist" :props="treem_props" lazy
                      ref="ref_bmc"
                      class="_css-customstyle"
                      :expand-on-click-node="false"
                      @node-collapse="node_collapse"
                      @node-expand="node_expand"
                      :load="treefunc_loadChild"
                      :default-expand-all='false'

                      node-key="bmc_guid"
                      :highlight-current="true"
                      :auto-expand-parent="true"
                      :default-checked-keys="treem_default_checked_keys"
                      :default-expanded-keys="treem_default_expanded_keys"
                      @current-change="treefunc_current_change"
                  >
                      <span class="css-fc _css-treenode-content" slot-scope="{ node, data }">
                          <i
                            class="css-mr4 css-fs16 css-fc css-jcsa " :class="data.classname" ></i>
                          <span :title="node.label" class="css-ml4 _css-treenodelabel">

                            <!-- 分类名称 -->
                            <div class="_css-treenodellabelname" >{{data.bmc_name}}</div>
                            <!-- //分类名称 -->

                            <!-- 其下数量 -->
                            <div class="_css-treenodellabelnum">{{data.ChildrenItemCount}}</div>
                            <!-- //其下数量 -->

                          </span>

                            <!-- hover 时显示的按钮 -->
                            <div v-if="data.bmc_guid != '-1000' && showEdit? true: false"
                            @click="func_showmenu($event, data)"
                            class="_css-treenode-menubtn icon-interface-list" ></div>
                            <!-- //hover 时显示的按钮 -->

                      </span>
                  </el-tree>
                  <!-- //树控件 -->

              </div>

            </template>
            <!-- //原来的循环数据（所有构件分类）切回 true ，查看原来页面，改为 false，使用新版的树 -->

          </div>

        </div>
      <!-- </transition> -->
      <div class="_css-choose-switch-parent" v-if="associationModelChild">
        <div class="_css-choose-switch" @click.stop="chooseCloseOrOpen">
          <!-- <i
            class="_css-interface-back"
            :class="tableClose?'icon-interface-back':'icon-arrow-right_outline'"
          ></i> -->
          <i
            class="pro-in-out"
            :class="tableClose?'pro-in-out':'p-out'"
          ></i>

        </div>
      </div>
      <div class="_css-bottomtable" :style="getStyleBottomtable()">
        <div class="_css-materialtypetab-head"  v-if="associationModelChild">
          <span class="_css-click-close-or-open">
            <!-- <i @click.stop="chooseCloseOrOpen" :class="tableClose?'icon-arrow-indent':'icon-arrow-outdent'"></i> -->
            <i :class="leftRightHide?'icon-interface-toleft':'icon-interface-toright'"></i>
          </span>
          <div>{{clickMaterialTypeName}}</div>
          <div class="_css-materialthead-title"></div>
          <div class="_css-header-right-buttom" v-if="associationModelChild && showEdit? true: false">

            <div
              v-if="!hasEditAuth"
              @click="importtemplate($event)"
              class="_css-materialthead-filter _css-commonbtn _css-buttom-two-word"
              :class="{'_css-commonbtn-clicked':extdata.filter_css.display}"
            >
              导入
            </div>
            <div
              v-if="hasEditAuth"
              @click="!importtemplate($event)"
              class="_css-materialthead-filter _css-commonbtn _css-buttom-two-word"
              :class="{'_css-commonbtn-clicked':extdata.filter_css.display }"
            >
              导入
            </div>

            <div
            v-if="R_import == true"
              @click.stop="exportselected()"
              class="_css-materialthead-filter _css-commonbtn  _css-buttom-two-word"
              :class="{'_css-commonbtn-clicked':extdata.filter_css.display, '_css-down-disabled':extdata.selectedData.length<=0}"
            >
              导出
            </div>
            <div
            v-else
              class="_css-materialthead-filter _css-commonbtn  _css-buttom-two-word _css-dis"
              :class="{'_css-commonbtn-clicked':extdata.filter_css.display, '_css-down-disabled':extdata.selectedData.length<=0}"
            >
              导出
            </div>

            <div
            v-if="true"
              @click.stop="showorhidefilter($event)"
              class="_css-materialthead-filter _css-commonbtn _css-buttom-two-word"
              :class="{'_css-commonbtn-clicked':extdata.filter_css.display}"
            >
              筛选
            </div>
            <div
            v-else
              class="_css-materialthead-filter _css-commonbtn _css-buttom-two-word _css-dis"
              :class="{'_css-commonbtn-clicked':extdata.filter_css.display}"
            >
              筛选
            </div>

            <div
              :class="hasEditAuth ? '' : '_css-dis'"
              class="_css-materialthead-filter _css-commonbtn _css-buttom-down-word _css-btnmaterialrelmodel-btn"
                @click.stop="showorhiderelmethod()"
            >
              <div class="_css-btnmaterialrelmodel-text" >关联模型</div>
              <i class="icon-arrow-down"></i>

              <div class="_css-btnmaterialrelmodel-btndropctn"
              v-if="bIfShowRelationDropItems==true"
              >
                <div
                @click.stop="showComsMaterialRelation()"
                class="_css-btnmaterialrelmodel-btndropi">手动关联</div>
                <div
                @click.stop="showAutoRelation()"
                class="_css-btnmaterialrelmodel-btndropi">自动关联</div>
              </div>

            </div>

            <div
              v-if="hasEditAuth"
              @click.stop="newmaterial_open()"
              class="_css-materialthead-filter _css-commonbtn _css-buttom-four-word"
              :class="{'_css-commonbtn-clicked':extdata.bIsShowingNewMaterial}"
            >
              添加单元
            </div>
            <div
              v-if="!hasEditAuth"
              @click.stop="newmaterial_open()"
              class="_css-materialthead-filter _css-commonbtn _css-buttom-four-word _css-dis"
              :class="{'_css-commonbtn-clicked':extdata.bIsShowingNewMaterial}"
            >
              添加单元
            </div>

          </div>
          <div class="_css-righthide-checkbuttom" v-if="!associationModelChild">
            <div class="_css-righthide-more" @click.stop="showMoreButtom" ref="showMoreButtomRef">
              <i class="icon-interface-more"></i>
              更多
            </div>

          </div>
          <div
            v-if="changeShowButtomMore"
            :style="getShowButtomstyle()"
            class="_css-materialitem-menu"
          >
            <div class="_css-typeitem-title">
              单元菜单
              <span class="_css-typeitem-close icon-interface-guanbimoxing" @click.stop="closeall()"></span>
            </div>
            <div @click="importtemplate($event)" class="_css-material-menu-i _css-last">
              <div class="_css-material-menu-in">
                <div class="_css-menu-in-icon icon-interface-cloud-upload"></div>
                <div class="_css-menu-in-text">导入单元</div>
              </div>
            </div>
            <div @click.stop="exportdatasofcate()" class="_css-material-menu-i _css-last">
              <div class="_css-material-menu-in">
                <div class="_css-menu-in-icon icon-interface-download-fill"></div>
                <div class="_css-menu-in-text">导出单元</div>
              </div>
            </div>
            <div @click.stop="showorhidefilter($event)" class="_css-material-menu-i _css-last">
              <div class="_css-material-menu-in">
                <div class="_css-menu-in-icon icon-interface-filter"></div>
                <div class="_css-menu-in-text">筛选</div>
              </div>
            </div>
            <div @click.stop="showComsMaterialRelation()" class="_css-material-menu-i _css-last">
              <div class="_css-material-menu-in">
                <div class="_css-menu-in-icon icon-interface-associated-component"></div>
                <div class="_css-menu-in-text">手动关联模型</div>
              </div>
            </div>
            <div @click.stop="showAutoRelation()" class="_css-material-menu-i _css-last">
              <div class="_css-material-menu-in">
                <div class="_css-menu-in-icon icon-interface-delete-fill"></div>
                <div class="_css-menu-in-text">自动关联模型</div>
              </div>
            </div>
            <div @click.stop="newmaterial_open()" class="_css-material-menu-i _css-last">
              <div class="_css-material-menu-in">
                <div class="_css-menu-in-icon icon-interface-addnew"></div>
                <div class="_css-menu-in-text">添加单元</div>
              </div>
            </div>
          </div>
        </div>
        <div class="_css-materialtypetab-body"  v-if="associationModelChild">
          <div v-if="extdata.selectedData.length > 0" class="_css-materialtab-selectedcontrol">
            <div
            v-if="R_import==true"
              @click.stop="exportselected"
              class="_css-seloperi _css-seloperbtn  _css-seloper-export"
            >
              <div class="_css-seloperi-icon icon-interface-download-fill"></div>
              <div class="_css-seloperi-text">导出</div>
            </div>
            <div
            v-else
              class="_css-seloperi _css-seloperbtn  _css-seloper-export _css-dis"
            >
              <div class="_css-seloperi-icon icon-interface-download-fill"></div>
              <div class="_css-seloperi-text">导出</div>
            </div>

            <div v-if="showEdit? true: false">
              <div
              :class="hasDelAuth ? '' : '_css-dis'"
              @click.stop="Mul_del" class="_css-seloperi _css-seloperbtn _css-seloper-delete">
                <div class="_css-seloperi-icon icon-interface-model-delete"></div>
                <div class="_css-seloperi-text">删除</div>
              </div>

            </div>

            <div class="_css-seloperi _css-seloper-cnt">
              <div class="_css-seloperi-label">已选择{{extdata.selectedData.length}}项</div>
            </div>
          </div>
          <!-- el-table===start -->
          <el-table
            v-loading="elTableLoading"
            element-loading-text="数据加载中..."
            element-loading-spinner="el-icon-loading"
            element-loading-background="rgba(0, 0, 0, 0)"
            @sort-change="sort_change"
            ref="doctable"
            @row-dblclick="on_row_dblclick"
            @current-change="handleCurrentChange"
            @cell-mouse-enter="modelMouseEnter"
            highlight-current-row
            :border="true"
            :stripe="false"
            use-virtual
            height="70%"
            :data="extdata.tableData"
            style="width: 100%"
            :default-sort="{prop: 'date', order: 'descending'}"
            class="_css-table-ele css-scroll _css-customstyle"
            :row-class-name="tableRowClassName"
            :header-cell-style="{'background-color':'transparent'}"
            :row-height="rowHeight"
          >
            <el-table-column :resizable="false" width="50">
              <template slot="header" slot-scope="scope">
                <span
                  class="css-cb css-icon12 css-cp css-blk"
                  style="margin-left:16px;"
                  :data-use1="typeof scope"
                  :class="{'mulcolor-interface-checkbox-selected':extdata.tableData.length == extdata.selectedData.length && extdata.selectedData.length != 0}"
                  @click.stop="exchangecheckhead()"
                  @dblclick="_stopPropagation($event)"
                ></span>
              </template>
              <template slot-scope="scope">
                  <span
                    @click.stop="row_cb_click(scope.row, $event)"
                    style="margin-left:16px;"
                    class="css-cb css-icon12 css-cp"
                    :class="{'mulcolor-interface-checkbox-selected':selectedContainsMaterialId(scope.row.materialid)}"
                  ></span>
              </template>
            </el-table-column>

            <el-table-column :resizable="false" prop="materialcode" label="编码" min-width="30">
              <template slot="header">
                <!-- <div class="_css-table-title _css-table-title-hover"> -->
                <div class="_css-table-title _css-table-title-hover" @click.stop="handleTableSort('bm_materialcode')">
                  <span class="_css-dataitemcode">编码</span>
                  <i
                    ref="materialcode"
                    class="_css-table-header-icon"
                    :class="extdata.tableSortType.bm_materialcode?'icon-arrow-arrowdown':'icon-arrow-arrowup'"
                  ></i>
                </div>
              </template>
              <template slot-scope="scope">
                <span
                  @click.stop="materialname_click(scope.row.materialid)"
                  class="_css-dataitemname css-cp css-hoverunder basic-font-color-emphasize"
                >{{scope.row.materialcode}}</span>
              </template>
            </el-table-column>

            <el-table-column
              :resizable="true"
              class="_css-col-filename"
              prop="materialname"
              label="单元名称"
              min-width="30"
            >
              <template slot="header" >
                <div class="_css-table-title _css-table-title-hover">
                <!-- <div class="_css-table-title _css-table-title-hover" @click.stop="handleTableSort('bm_materialname')"> -->
                  <span class="_css-dataitemcode">单元名称</span>
                  <!-- <i
                    ref="materialname"
                    class="_css-table-header-icon"
                    :class="extdata.tableSortType.bm_materialname?'icon-arrow-arrowdown':'icon-arrow-arrowup'"
                  ></i> -->
                </div>
              </template>
              <template slot-scope="scope">
                <template>
                  <span
                    @click.stop="materialname_click(scope.row.materialid)"
                    class="_css-dataitemname css-cp css-hoverunder css-ellipsis basic-font-color-emphasize"
                  >{{scope.row.materialname}}</span>
                  <div class="_css-trbtn-container">
                    <div
                      @click.stop="showmaterialmenu($event, scope.row)"
                      class="_css-trbtn icon-interface-more"
                    ></div>
                  </div>
                </template>
              </template>
            </el-table-column>
            <el-table-column
              :resizable="false"
              class="_css-col-relmodel"
              label="关联模型"
              min-width="30"
            >
              <template slot="header">
                <!-- 注释掉的为关联模型的排序 暂时注释掉 后期有需求可能会放开-->
                <!-- <div class="_css-table-title _css-table-title-hover" @click.stop="handleTableSort('bhaselerel')">
                  <span class="_css-dataitemcode">关联模型</span>
                  <i
                    ref="materialBulid"
                    class="_css-table-header-icon"
                    :class="extdata.tableSortType.bhaselerel?'icon-arrow-arrowdown':'icon-arrow-arrowup'"
                  ></i>
                </div> -->
                <div class="_css-table-title _css-table-title-hover" >
                  <span class="_css-dataitemcode">关联模型</span>
                  <i
                    ref="materialBulid"
                    class="_css-table-header-icon"
                  ></i>
                </div>
              </template>
              <template slot-scope="scope">
                <div class="width100" style="display:flex;align-items: center;justify-content: center">
                  <div
                    @mouseenter="getAssociationModelDetail(scope.row,$event)"
                    class="_css-relele  _css-guanlianmoxing-hover css-mr12"
                    :class="{'icon-interface-guanlianmoxing': scope.row.bhaselerel}"
                  ></div>
                </div>

              </template>
            </el-table-column>
            <el-table-column
              :resizable="false"
              class="_css-col-relmodel"
              label="现场数据"
              min-width="40"
              v-if="associationModelChild==true"
            >
              <template slot="header">
                <div class="_css-table-title _css-table-title-hover">
                  <span class="_css-dataitemcode">现场数据</span>
                </div>
              </template>
              <template slot-scope="scope">
                <div class="width100" style="display:flex;align-items: center;justify-content: center">
                  {{scope.row.bhas_relexam | filterHasBeenAssociated}}
                </div>
              </template>
            </el-table-column>
            <el-table-column
              :resizable="false"
              class="_css-col-relmodel"
              label="项目流程"
              min-width="40"
              v-if="associationModelChild==true"
            >
              <template slot="header" >
                <div class="_css-table-title _css-table-title-hover">
                  <span class="_css-dataitemcode">项目流程</span>
                </div>
              </template>
              <template slot-scope="scope">{{scope.row.bhas_relflow | filterHasBeenAssociated}}</template>
            </el-table-column>
            <el-table-column
              :resizable="false"
              class="_css-col-relmodel"
              label="二维码"
              min-width="30"
              v-if="associationModelChild==true"
            >
              <template slot="header" >
                <div class="_css-table-title">
                  <span class="_css-dataitemcode">二维码</span>
                <!-- <i></i> -->
                </div>
              </template>
              <template slot-scope="scope">
                <div class="width100" style="display:flex;align-items: center;justify-content: center">
                  <div class="_css-qrcode icon-interface-erweima" @mouseenter="hoverQrcodeEnter(scope.row.materialid,$event)" @mouseleave="hoverQrcode=false">

                  </div>
                </div>

              </template>
            </el-table-column>

            <el-table-column
              :resizable="false"
              class="_css-col-relmodel"
              label="更新日期"
              min-width="30"
              v-if="associationModelChild==true"
            >
              <template slot="header">
                <div class="_css-table-title _css-table-title-hover" @click.stop="handleTableSort('bm_updatetime')">
                  <span class="_css-dataitemcode">更新日期</span>
                  <i
                    ref="materialTime"
                    class="_css-table-header-icon"
                    :class="extdata.tableSortType.bm_updatetime?'icon-arrow-arrowdown':'icon-arrow-arrowup'"
                  ></i>
                </div>
              </template>
              <template slot-scope="scope">
                <div class="width100" style="display:flex;align-items: center;justify-content: center">
                  {{scope.row.bm_updatetime | flttimeshorter}}
                </div>
              </template>
            </el-table-column>
          </el-table>
          <!-- el-table===end -->
        </div>
        <div class="pagination-css"  v-if="associationModelChild">
          <el-pagination
            @size-change="handleSizeChange"
            @current-change="handleCurrentPaginationChange"
            :current-page="pageNum"
            :page-sizes="[20,50, 100, 200]"
            :page-size="pageSize"
            layout="total, sizes, prev, pager, next, jumper"
            :total="paginationPageLength">
          </el-pagination>
        </div>
        <!-- 点击表格中关联模型后 展开树结构 ==== 开始-->
        <div v-if="!associationModelChild" class="_css-mgr-tree">
          <!-- <div class="_css-materialtypetab-head">
            结构单元
          </div> -->
          <MaterialsMgrTree :openModelID="openModelID" :tree_table_id="tree_table_id" @treefunc_checked="treefunc_checked"></MaterialsMgrTree>
        </div>

        <!-- 点击表格中关联模型后 展开树结构 ==== 结束-->
      </div>
      <!-- v-if="!associationModelChild"  -->
      <div class="_css-right-model-iframe" v-if="!associationModelChild" >
          <modelNewDetail
            :VaultID=VaultID
            :featureID=openModelID
            :modelTitle='newModelTitle'
            @closeModelDetail="onmodelview_close"
            >
          </modelNewDetail>

      </div>
      <!-- //左列表，右表格 -->
    </div>

    <div @click.stop="closeinnerdrops()" class="_css-filterdrop" :style="getfilterdropstyle()">
      <!-- 搜索框 -->
      <div class="_css-filter-top">
        <div class="_css-filter-title">编码</div>
        <div class="_css-filtersearch">
          <input
            v-model="extdata.text_filter_mat_name"
            placeholder="请输入编码"
            type="text"
            class="_css-filtersearchinput"
          />

          <div
            @click.stop="extdata.text_filter_mat_name = '';"
            v-if="extdata.text_filter_mat_name"
            class="_css-search-clear icon-suggested-close_circle"
          ></div>
        </div>
        <div class="_css-filter-line"></div>
        <!-- 搜索框 -->

        <!-- 单元名称 -->
        <div class="_css-filter-title">单元名称</div>
        <div class="_css-filtersearch">
          <input
            v-model="extdata.filter_mat_name_component"
            placeholder="请输入单元名称"
            type="text"
            class="_css-filtersearchinput"
          />

          <div
            @click.stop="extdata.filter_mat_name_component = '';"
            v-if="extdata.filter_mat_name_component"
            class="_css-search-clear icon-suggested-close_circle"
          ></div>
        </div>
        <div class="_css-filter-line"></div>
        <!-- 单元名称 -->

        <!-- 单元状态 -->
        <div class="_css-materialstatus">
          <!-- 左112 -->
          <div class="_css-filter-title">状态</div>
          <!-- //左112 -->
          <div class="_css-statusval" @click.stop="togglestatusoptions">
            {{getSelectedStatusStr()}}
            <div class="_css-matertialstatusoptions">
              <div class="_css-mat-status-options css-miniscroll">
                <div
                  @click.stop="togglestatusselect(status.statusid)"
                  class="_css-mat-status-option"
                  v-for="status in extdata.allmaterialstatus"
                  :key="status.statusid"
                >
                  <div class="_css-mat-status-optionicon">
                    <div
                      class="_css-mat-status-optionicon-in"
                      :title="status.statusname"
                      :style="getstatusstyle(status.statusmaincolor, extdata.selectedfilterstatus_editing.indexOf(status.statusid) >= 0)"
                    >{{status.statusname}}</div>
                  </div>
                </div>
              </div>

              <!-- <div @click.stop="savefilter_statusselected()" class="_css-mat-status-btn">确定</div> -->
            </div>
          </div>
        </div>
        <div class="_css-filter-line _css-filter-line-margin"></div>

        <!-- //单元状态 -->

        <!-- h144 mt14 关联构件过滤 -->
        <div class="_css-relelearea">
          <div class="_css-filter-title">关联构件</div>
          <div class="_css-filter-releleoptions">
            <el-checkbox-group v-model="extdata.filterCheckList">
              <!-- <p class="_css-filter-checkedList">
                <el-checkbox label="已关联" value="0"></el-checkbox>
              </p>
              <p class="_css-filter-checkedList">
                <el-checkbox label="未关联" value="1"></el-checkbox>
              </p>-->
              <p
                class="_css-filter-checkedList"
                v-for="(item,index) in extdata.filterCheckItemList"
                :key="index"
              >
                <el-checkbox :label="item.value" value="0">{{item.name}}</el-checkbox>
              </p>
            </el-checkbox-group>
          </div>

        </div>
        <div class="_css-filter-line"></div>

        <!-- //h144 mt14 关联构件过滤 -->

        <!-- 更新时间 -->
        <div class="_css-fliter-time">
          <div class="_css-filter-title">更新时间</div>
          <div class="_css-date-picker _css-start-time">
            <el-date-picker
              v-model="extdata.filterStartTime"
              :editable="false"
              type="datetime"
              :picker-options="pickerStartOptions"
              placeholder="开始日期"
            ></el-date-picker>
          </div>
          <div class="_css-date-picker _css-end-time">
            <el-date-picker
              v-model="extdata.filterEndTime"
              type="datetime"
              :picker-options="pickerEndOptions"
              :editable="false"
              placeholder="结束日期"
            ></el-date-picker>
          </div>
        </div>
      </div>
      <!-- 更新时间 -->

      <!-- h40, mt24 -->
      <!-- <div @click.stop="reset_filters" class="_css-filter-resetbtn">重置筛选</div> -->
      <div class="_css-dialog-select-button">
        <span @click.stop="reset_filters2">清除筛选项</span>
        <span @click.stop="handleFilters">确认</span>
      </div>
      <!-- //h40, mt24 -->
    </div>

    <!-- 排序的弹出下拉 -->
    <div
      v-if="extdata.sort_css.display"
      :style="getsortoptionsstyle()"
      class="_css-sortoptions-menu"
    >
      <div @click.stop="changefiltersort()" class="_css-sortoption-item">默认</div>
      <div @click.stop="changefiltersort('updatetime_desc')" class="_css-sortoption-item">更新时间（最新）</div>
      <div @click.stop="changefiltersort('updatetime_asc')" class="_css-sortoption-item">更新时间（最早）</div>
      <div @click.stop="changefiltersort('materialname_asc')" class="_css-sortoption-item">名称（正序）</div>
      <div @click.stop="changefiltersort('materialname_desc')" class="_css-sortoption-item">名称（倒序）</div>
    </div>
    <!-- //排序的弹出下拉 -->
    <!-- 滑过展示二维码 -->
    <div class="_css-hover-qrcode" v-if="hoverQrcode" :style="hoverQrcodeStyle()">
      <p>构件详情</p>
      <img :src="getCodeSrc" />
    </div>
    <!-- 构件分类的弹出菜单 -->
    <div
      v-if="extdata.typemenu_css.display == true"
      :style="gettypemenustyle()"
      class="_css-typeitem-menu"
    >
      <div class="_css-typeitem-title">
        分类菜单
        <span class="_css-typeitem-close icon-interface-guanbimoxing" @click.stop="closeall()"></span>
      </div>
      <div @mouseenter="me_popupfieldmgr($event)" @mouseleave="me_popupfieldmgrMouseLeave($event)" class="_css-typei _css-typei-fieldmgr">
        <div class="_css-typei-icon icon-interface-document_fill"></div>
        <div class="_css-typei-text">分类字段管理</div>
        <div class="_css-typei-expand icon-arrow-right_outline"></div>
          <!-- 字段管理中的字段列表 -->
          <div
            id="id_fieldmgr_list"
            @click="_stopPropagation($event)"
            v-show="extdata.bIsShowingFieldMgr == true"
            class="_css-field-list"
            :style="getflstyle()"
          >
            <div class="_css-fieldlist-head">
              <input
                id="id_newfieldnameinput"
                ref="id_newfieldnameinput"
                type="text"
                class="_css-flhead-text"
                placeholder="请输入新字段名称"
                v-model="extdata.fieldnewname"
              />
              <div @click.stop="btn_addfield_click()" class="_css-flhead-btn icon-suggested-plus_square"></div>

              <!-- 复制和粘贴功能 -->
              <el-tooltip
                popper-class="css-no-triangle"
                class="item"
                effect="dark"
                content="复制全部字段"
                placement="top"
              >
                <div
                @click="func_copyallfields()"
                class="_css-flhead-btn icon-interface-copy _css-flhead-btn-copy"></div>
              </el-tooltip>

              <el-tooltip
                popper-class="css-no-triangle"
                class="item"
                effect="dark"
                content="粘贴全部字段"
                placement="top"
              >
                <div
                @click="func_pasteallfields"
                class="_css-flhead-btn icon-interface-paste _css-flhead-btn-paste"></div>
              </el-tooltip>
              <!-- //复制和粘贴功能 -->

            </div>
            <div
              class="_css-fieldlist-body css-miniscroll"
              :class="{'_css-flex-jcsa': extdata.fields.length == 0}"
            >
              <!-- 如果当前类别已添加过字段，则显示template中的内容，否则显示一张图片及为空文字说明 -->
              <div class="_css-cur-fields-emptycontent" v-if="extdata.fields.length == 0">
                <div class="_css-cur-fields-emptyimg mulcolor-interface-morenxianmu"></div>
                <div class="_css-cur-fields-emptylabel">暂无自定义字段</div>
              </div>
              <template v-else>
                <div v-for="field in extdata.fields" :key="field.fieldid" class="_css-flbody-li">
                  <div class="_css-fli-label">{{field.fieldname}}</div>
                  <div @click.stop="showfieldnameedit(field)" class="_css-fli-btn icon-interface-edit"></div>
                </div>
              </template>
              <!-- //如果当前类别已添加过字段，则显示template中的内容，否则显示一张图片及为空文字说明 -->
            </div>
          </div>
          <!-- //字段管理中的字段列表 -->
      </div>
      <div class="_css-typei-spliter"></div>

      <div class="_css-typei _css-typei-rename" @click.stop="exportdatasofcate()">
        <div class="_css-typei-icon icon-interface-download-fill"></div>
        <div class="_css-typei-text">全部导出</div>
      </div>
      <div class="_css-typei-spliter"></div>
      <div class="_css-typei _css-typei-rename" @click.stop="renamematerialtype()">
        <div class="_css-typei-icon icon-interface-edit"></div>
        <div class="_css-typei-text">重命名分类</div>
      </div>
      <div
        @click.stop="deletematerialtype()"
        class="_css-typei _css-typei-delete"
        :class="{'_css-dis': extdata.editingmaterialtype && extdata.editingmaterialtype.MaterialCnt > 0}"
      >
        <div class="_css-typei-icon icon-interface-model-delete"></div>
        <div class="_css-typei-text">删除分类</div>
      </div>

    </div>
    <!-- //构件分类的弹出菜单 -->

    <!-- 右键 -->
    <div
      v-if="extdata.materialitemmenu.isshowing"
      :style="getmaterialmenustyle()"
      class="_css-materialitem-menu"
    >
      <div class="_css-typeitem-title">
        单元菜单
        <span class="_css-typeitem-close icon-interface-guanbimoxing" @click.stop="closeall()"></span>
      </div>
      <div
      v-if="true"
      @click.stop="materialname_click(clickTableDetailsID)" class="_css-material-menu-i _css-last">
        <div class="_css-material-menu-in">
          <div class="_css-menu-in-icon icon-interface-component-search"></div>
          <div class="_css-menu-in-text">查看单元</div>
        </div>
      </div>
      <div
      v-else class="_css-material-menu-i _css-last">
        <div class="_css-material-menu-in _css-dis">
          <div class="_css-menu-in-icon icon-interface-component-search"></div>
          <div class="_css-menu-in-text">查看单元</div>
        </div>
      </div>

      <div v-if="showEdit? true: false">
        <div
        :class="hasDelAuth ? '' : '_css-dis'"
        @click.stop="delsingle()" class="_css-material-menu-i _css-last">
          <div class="_css-material-menu-in">
            <div class="_css-menu-in-icon icon-interface-model-delete"></div>
            <div class="_css-menu-in-text">删除单元</div>
          </div>
        </div>
      </div>

    </div>
    <!-- //右键 -->

    <!-- 右键，呼出，状态 -->
    <div
      v-if="extdata.materialitemmenu_popup.isshowing"
      :style="getmaterialstatuspopup()"
      class="_css-material-status-popup"
      @click.stop=""
    >
      <div class="_css-mat-status-options _css-mat-options-previous css-miniscroll">
        <div class="_css-typeitem-title">
          <span class="_css-typeitem-previous icon-interface-back" @click.stop="extdata.materialitemmenu_popup.isshowing=false"></span>
          修改状态
          <span class="_css-typeitem-close icon-interface-guanbimoxing" @click.stop="closeall()"></span>
        </div>
        <div class="_css-typeitem-options">
          <div
            @click.stop="changematerialstatus(status.statusid)"
            class="_css-mat-status-option"
            v-for="status in extdata.allmaterialstatus"
            :key="status.statusid"
          >
            <div class="_css-mat-status-optionicon _css-mat-alter-state">
              <div
                class="_css-mat-status-optionicon-in"
                :title="status.statusname"
                :style="getstatusstyle(status.statusmaincolor, extdata.changeStatusSureId==status.statusid)"
              >{{status.statusname}}</div>
            </div>
          </div>
        </div>
        <div class="_css-typeitem-add-sure _css-option-add-sure">
          <p @click="changeStatusSure">确定</p>
        </div>
      </div>
    </div>
    <!-- //右键，呼出，状态 -->
    <!-- 构件管理关联 -->
    <CompsMaterialRelation
    ref="refmaterialrel"
      :selectCateObj=selectCateObj
      :stateDetailList="extdata.allmaterialstatus"
      :allMaterialTypes="extdata.materialtypes"
      :inData="extdata.tableData"
      :SelectedObj="MaterialRel_SelectedCateObj"
      :SelectedMaterialId="relationSelectedMId"
      @oncancel="materialrelation_cancel"
      @ontypeselected="_ontypeselected"
      @onmaterialsel="_onmaterialsel"
      @oncomplete="_oncompleteType"
      v-if="isComsMaterialRelationShow"
    ></CompsMaterialRelation>
    <!-- //构件管理关联 -->

    <!-- 新建构件 -->
    <CompsNewMaterial
      v-if="extdata.bIsShowingNewMaterial"
      @onadded="_onadded"
      :selectedtypeid="extdata.selectedtypeid"
      @oncancel="newmaterial_cancel"
    ></CompsNewMaterial>
    <CompsMaterialInfo
      v-if="extdata.isShowInfo"
      :bm_guid="extdata.selectedbmid"
      :allstatus="extdata.allmaterialstatus"
      @oncancel="materialinfo_cancel"
      :zIndex="2001"
      @viewidoc="_viewidoc"
      @cancelRelevanceReloadTble="cancelRelevanceReloadTble"
      @onrefresh="func_refresh"
    ></CompsMaterialInfo>
    <!-- //新建构件 -->



    <!-- 编辑字段小窗口 -->
    <div
      @click="_stopPropagation($event)"
      v-if="extdata.bIsShowingFieldEdit"
      class="_css-edit-field-hover"
    >
      <div class="_css-edit-field">
        <div class="_css-ef-title">
          <div class="_css-eftitle-back icon-arrow-left_outline _css-eftitle-btn"></div>
          <div class="_css-eftitle-label">编辑字段</div>
          <div
            @click.stop="closefieldediting()"
            class="_css-eftitle-close icon-suggested-close _css-eftitle-btn"
          ></div>
        </div>
        <div class="_css-ef-text">
          <input
            class="_css-ef-text-input"
            type="text"
            placeholder="请输入字段名称"
            v-model="extdata.editingfieldname"
          />
        </div>
        <div class="_css-ef-btns">
          <div @click.stop="removecategoryfield()" class="_css-efbtn-del _css-efbtn">删除</div>
          <div @click.stop="savecategoryfield()" class="_css-efbtn-ok _css-efbtn">完成</div>
        </div>
      </div>
    </div>
    <div class="model-list-table-hover" @mouseleave="modelListTableShow=false" v-if="modelListTableShow && getModelListForTable.length > 0" :style="style_getmodelList()">
      <div
        class="list css-ellipsis"
        v-for="(jsonitem,index) in getModelListForTable"
        :key="index"
        @click.stop="openAssociationModelComponents(getModelListForTableData,jsonitem.featureID)"
      >
        {{ jsonitem.featureName }}
      </div>
    </div>
    <!-- //编辑字段小窗口 -->

    <!-- 从分类导出模板 -->
    <CompsStepTip2
      :zIndex="1000"
      v-if="extdata.bIsShowingImportTemplate == true"
      width="504"
      :bc_guid="clickMaterialTypeID"
      @oncancel="extdata.bIsShowingImportTemplate = false"
      @onok="_onimportok"
      title="导入构件"
    ></CompsStepTip2>
    <!-- //从分类导出模板 -->

    <CompsAutoRelation
    @autoRelmaskclick="autoRelMaskClick"
    @onsubmit="autoRelSubmit"
    v-if="showautoRelation == true"></CompsAutoRelation>
  </div>
</template>
<script>
import CompsNewMaterial from "@/components/CompsMaterial/CompsNewMaterial";
import CompsMaterialInfo from "@/components/CompsMaterial/CompsMaterialInfo";
import CompsMaterialRelation from "@/components/CompsMaterial/CompsMaterialRelation";
import CompsLinkDialog from "@/components/CompsCommon/CompsLinkDialog";
import CompsStepTip2 from "@/components/CompsCommon/CompsStepTip2";
import CompsAutoRelation from '@/components/CompsMaterial/CompsAutoRelation'
import modelLoadingProgress from '@/components/Home/ProjectBoot/modelLoadingProgress'  // 加载中进度条
import MaterialsMgrTree from './MaterialsMgrTree'  // 加载中进度条
import { mapGetters } from 'vuex'
import modelNewDetail from '@/components/Home/ProjectBoot/modelNewDetail'

export default {
  components: {
    CompsNewMaterial,
    CompsMaterialInfo,
    CompsMaterialRelation,
    CompsStepTip2,
    CompsAutoRelation,
    CompsLinkDialog,
    modelLoadingProgress,
    MaterialsMgrTree,
    modelNewDetail,
    // MaterialsMgrAdmin,
  },
  data() {
    return {
      headerTitleText: '', // header显示文字
      hasEditAuth: false, // 编辑权限
      hasDelAuth: false, // 删除权限
      status_bmcadd: false,// 构件分类数据
      m_bmclist:[],
      m_bmcedittitle:'',// 新增分类，或“重命名”
      m_bmceditingname:'', // 新增或重命名分类时正在进行编辑的名称
      m_currentbmc: undefined,// 当前选中项
      // 上下文菜单显示问题
      // 当前正在操作的流程模板分类数据
      m_contextpos: {
          top: 0,
          left: 0,
          isshowing: false
      },
      // 构件分类导入导出的
      m_contextpos2: {
          top: 0,
          left: 0,
          isshowing: false
      },
      // 树控件的 props 属性对象
      treem_props:{
          children: "children",
          label: "bmc_name",
          isLeaf: "isLeaf"
      },
      treem_default_checked_keys: [], // 当前选中节点
      // 默认展开节点
      // 当前选中的节点
      treem_default_expanded_keys:[],
      getCodeSrc: '',
      hoverQrcode: false,  // 鼠标滑过展示二维码
      hoverQR:{
        field_x: 0,field_y:0
      },
      bIfShowRelationDropItems: false, // 是否显示关联方式按钮。
      showautoRelation: false,         // 是否显示自动关联对话框。
      selectCateObj:null,
      checkedAll: false,
      checkedSelectList: [], // 选中的项
      clickCheckedSelectList: [], // 当前选中的项
      clickCheckedSelectListOptions: [], // 所有的选项
      selectisIndeterminate: false,
      selectAsc: {
        dialogShow: false,
        searchName: "请输入搜索内容",
        searchList: [],
        handelClickChecked: true
      },

      restaurants: [],
      state: "",
      timeout: null,
      fieldconditions: [], // 选择传给后台的参数
      selectTableDataList: [], // 储存首次加载表格data
      tableClose: true, // 控制点击左边菜单栏收起
      leftRightHide: true, // 控制右边icon
      clickMaterialTypeName: "",
      R_bmc_name:"", //记录点击tree的名称
      R_import:true,//判断是否是单元管理
      clickMaterialTypeID: "",
      clickTableDetailsID: "",
      clickTreebmc_code: "",  //记录点击tree的bmc_code,格式：001-001
      isComsMaterialRelationShow: false, //显示构件管理与模型关联组件
      relationSelectedMId: "", // 关联页面默认选中的构件
      hoverSeen: false, // 鼠标滑过二维码显示隐藏,
      changeShowButtomMore: false, // 点击更多显示选择按钮
      extdata: {
        isshowing_linkDoalog: false,
        funcauthdata: undefined,
        currentRow: null,  // 点击表格该行高亮
        modelHoverName: "", // 鼠标滑过真是模型的名称
        bIsShowingMulStatusOptions: false, // 是否正在显示批量的状态下拉框
        selectedfilterstatus_editing: [], // 正在编辑的，选中的所有的状态
        selectedfilterstatus: [], // 选中的所有的状态（可能包含空字符串的数组）
        selectedfilterbhaselerel: undefined, // 选中的关联构件的情况（可选值："0", "1", undefined）
        selectedfiltersorttype: 'materialcode_asc', // 选中的排序方式(可选值undefined, "updatetime_desc", "updatetime_asc", "materialname_desc", "materialname_asc")
        bIsShowingImportTemplate: false,
        selectedmaterial: {}, // 已右键呼出菜单的构件数据
        bIsShowingFieldEdit: false,
        editingfieldid: "",
        editingfieldname: "",
        fields: [], // 当前（某）分类弹出的所有字段
        fieldnewname: "", // 新字段的名称
        field_x: 0,
        field_y: 0,
        bIsShowingFieldMgr: false,
        renamingtypename: "", // 正在进行重命名填写的名称
        renamingtypeid: "", // 正在进行重命名的分类ID
        editingmaterialtype: null, // 正在进行编辑的类型
        bIsShowingNewMaterial: false, // 是否显示新建构件对话框
        isShowInfo: false,
        newtypename: "",
        materialitemmenu: {
          left: 0,
          top: 0,
          isshowing: false
        },
        showMoreButtomStyle: {
          left: 0,
          top: 0,
        },
        materialitemmenu_popup: {
          isshowing: false
        },
        maintype: "all", // all, near_use, near_update
        bShowNewType: false, // 是否显示新类别的输入框
        // 表格上方过滤呼出菜单
        filter_css: {
          left: 200,
          top: 200,
          display: false
        },
        // 左边菜单栏新增分类
        addList_css: {
          left: 410,
          top: 86,
          display: false
        },
        // 表格上方排序方式呼出菜单
        sort_css: {
          left: 200,
          top: 200,
          display: false
        },

        // 构件分类呼出菜单
        typemenu_css: {
          left: 200,
          top: 200,
          display: false
        },
        text_filter_mat_name: "", // 单元名称过滤
        filter_mat_name_component: "", // 筛选-单元名称
        isshow_filter_mat_status: false, // 显示构件状态下拉
        selectedtypeid: undefined, // 选中的构件类型ID
        selectedbmid: "", // 选中的构件ID
        materialtypes: [],
        allmaterialstatus: [
          // {
          //   statusid: "",
          //   statusname: "未开始",
          //   statusmaincolor: "rgba(250, 173, 20, 1)"
          // }
        ],
        selectedData: [],
        tableData: [],
        filterStartTime: "",
        filterEndTime: "",
        filterCheckList: [],
        filterCheckItemList: [
          {
            name: "已关联",
            value: "1"
          },
          {
            name: "未关联",
            value: "0"
          }
        ],
        tableSortField: "bm_materialname",
        tableSortType: {},
        changeStatusSureId: '',
        statusSureIding: '',
        modeldetailstyle:{
          flex: 1,
          width:'calc(100% - 347px)',
          paddingLeft: '7px'
        }
      },
      cacheModelID: '', //是否打开模型窗口
      materialRelationModel: [],
      SelModelObj: {}, //选择的当前模型对象
      BIM_Session: "", //模型参数返回的Session
      associationModelChild: true,
      fromQuality: false,//是否是从现场管理打开该页面
      rowHeight:40,
      uTableHeight:800,
      oldSetmaterialtype: '',// 记录当前点击的和上次点击的是否为同一个
      elTableLoading: false,
      loadingNumber: 0,  // 自动关联进度条数据
      successLoadingNumber: 0,// 自动关联进度条成功条数
      failLoadingNumber: 0,// 自动关联进度条失败条数
      tableLengthNumber: 0,   // 自动关联进度条总数
      LoadingAutoElement: false,  // 自动关联进度条
      loadingLineWidth: 0,// 自动关联进度条宽度百分比
      loadingPercentage: 0, // 进度条初始化值
      loadingText: '构件数据获取中...',
      autoLoadingSuccess: false,
      autoIndex: 0,
      relations: [],
      tree_table_id: '',  // 保存点击的table中构件的materialid
      importMaterTreeData: false, // 导入全部弹框展示
      resetCheckedTree: [], // 选中的树节点
      addOrEdit : 0, //判断点击的是新增还是修改
      pickerStartOptions: {
        disabledDate:(time)=> {
          if(this.extdata.filterEndTime){
              return time.getTime() >this.extdata.filterEndTime;
          }
        },
      },
      pickerEndOptions: {
        disabledDate:(time)=> {
          let curDate = (new Date()).getTime();
          let day = 30 * 24 * 3600 * 1000;
          let dateRegion = curDate - day;
          if(this.extdata.filterStartTime){
              return  time.getTime() < this.extdata.filterStartTime;
          }
        },
      },
      VaultID: '',  // 新引擎使用的
      openModelID: '', // 记录打开模型的modelid
      newModelTitle: '', // 打开模型的title
      modelLoadingOK: false, // 记录模型加载完毕
      pageNum: 1, // 第几页
      pageSize: 20,  // 每页多少条
      paginationPageLength: 0, // 总条数
      autoRelType: 0, // 记录自动关联时候传值来的关联类型
      getModelListForTableData: null, // 记录当前鼠标滑过的表格数据
      getModelListForTable: [], // 鼠标滑过表格的关联模型、展示当前模型名称
      modelListTableShow: false,
      m_modellist: {
        top: 0,
        left: 0,
      },
    };
  },
  props: {
    showEdit: {
      type:Boolean,
      default:true
    }
  },
  watch: {
    getModelLoad(data){
      if(data.state == "end"){
        this.modelLoadingOK = true;
        if(!this.isComsMaterialRelationShow){
          this.newModelGetAllElement();
        }
      }
    }
  },

  created() {
    // 加载所有构件状态
    // this.loadmaterialstatus();    // 暂时注释、先不删除
    this.VaultID = this.$staticmethod._Get("organizeId");
    this.headerTitleText = this.$staticmethod._Get("menuText") || '';
  },

  mounted() {
    // 通过 onmounted:MaterialsMgr 保证页面在刷新后，或者直接打开本页面，左侧功能菜单的选中项为 MaterialsMgr
    var _this = this;
    _this.$emit("onmounted", "MaterialsMgr");
    this.hasEditAuth = this.$staticmethod.hasSomeAuth('GCJG_Edit')
    this.hasDelAuth =  this.$staticmethod.hasSomeAuth('GCJG_Delete')

    // 绑定 materialvue，辅助调试
    window.materialvue = _this;
    _this.loadtypes();
  },
  filters: {
    flttimeshorter(inputtime) {
      var _this = this;
      if (!inputtime || inputtime.trim().length == 0) {
        return inputtime;
      }
      if (inputtime.length >= "2019-09-16T11:14".length) {
        // return inputtime.substr(0, "2019-09-16T11:14".length).replace("T", " ");
        return inputtime.substr(0, 10);
      }
    },
    filterHasBeenAssociated(val){
      if(val == 1){
        return '已关联'
      }else{
        return '-'
      }
    }
  },
  computed: {
    ...mapGetters([
        'getModelLoad'
    ]),
    custom_materialmgr:{
      get() {
        return window.bim_config.custom_materialmgr == true;
      }
    },
    MaterialRel_SelectedCateObj: {
      get() {
        var _this = this;
        if (_this.extdata.selectedtypeid) {
          var _index = _this.extdata.materialtypes.findIndex(
            x => x.materialtypeid == _this.extdata.selectedtypeid
          );
          if (_index >= 0) {
            return _this.extdata.materialtypes[_index];
          } else {
            return {};
          }
        } else {
          return {};
        }
      }
    }
  },
  methods: {
    // 在渲染树的根节点并nexttick后，再请求接口获取所有分类的id，并设置到 m_defaultexpands，然后再展开“全部”节点
    func_expandroot() {
        // 获取当前项目的所有流程模板分类，并赋予 m_defaultexpands
        var _this = this;
        var _organizeId = _this.$staticmethod._Get("organizeId");
        var _url = `${window.bim_config.webserverurl}/api/Material/MaterialCategory/GetAllCategories?organizeId=${_organizeId}&Token=${this.$staticmethod.Get('Token')}`;
        _this.$axios.get(_url).then(x => {
            if (x.data.Ret > 0) {
                if (x.data.Data && x.data.Data.List) {
                    _this.treem_default_expanded_keys = _this.$staticmethod.DeepCopy(x.data.Data.List);
                    var node = _this.$refs.ref_bmc.getNode({bmc_guid:'-1000'});
                    if (node) {
                        node.expand();
                    }
                }
            } else {
                _this.$message.error(x.data.Msg);
            }
        }).catch(x => {
            console.error(x);
        });
    },

    func_pasteallfields() {
      // 读取已拷贝的字段
      var _this = this;

      var fieldsstr = _this.$staticmethod._Get("bmc_fields");
      if (!fieldsstr) {
        _this.$message.warning('当前没有复制任何数据');
        return;
      }
      var fieldarr;
      try {
        fieldarr = JSON.parse(fieldsstr);
      } catch (e) {

      }
      if (!fieldarr || !fieldarr.length) {
        _this.$message.warning('当前没有复制任何数据');
        return;
      }

      // 保存到db前先提示
      var strtip = fieldarr.map(x => x.fieldname).join(',');
      _this.$confirm(`确定将 【${strtip}】 复制到这个分类？(复制后现有字段将丢失)`, "操作确认").then(x => {
        var objtowrite = {"fields": fieldarr};
        _this.func_overridebmcfields(objtowrite);
      }).catch(()=>{

      });

    },

    // 调用接口，改写分类字段
    func_overridebmcfields(objtowrite) {
      var _this = this;
      _this
        .$axios({
          url: `${window.bim_config.webserverurl}/api/Material/MaterialCategory/OverrideCategoryFields`,
          method: "post",
          data: _this.$qs.stringify({
            Token: _this.$staticmethod.Get("Token"),
            fieldsjson: JSON.stringify(objtowrite),
            bmc_guid: _this.extdata.editingmaterialtype.bmc_guid
          })
        })
        .then(x => {
          if (x.data.Ret > 0) {
            _this.extdata.fields = _this.$staticmethod.DeepCopy(objtowrite.fields);
            _this.$message.success('操作完成');
          } else {
            _this.$message.error(x.data.Msg);
          }
        }).catch(x => {
          console.error(x);
        });
    },

    func_copyallfields() {
      var _this = this;
      if (!_this.extdata.fields || !_this.extdata.fields.length) {
        return;
      }
      var fieldstr = JSON.stringify(_this.extdata.fields);
      _this.$staticmethod._Set("bmc_fields", fieldstr);
      _this.$message.success('已复制');
    },

    do_delbmc() {
        var _this = this;
        var _para = {
            Token: _this.$staticmethod.Get("Token"),
            bmc_guid: _this.m_currentbmc.bmc_guid
        };
        var _url = `${this.$MgrBaseUrl.RemoveCategory}`;
        _this.m_showleftloding = true;
        _this.$axios({
            url: _url,
            data: _para,
            method: 'post'
        }).then(x => {
            _this.m_showleftloding = false;
            if (x.data.Ret > 0) {
                if (x.data.Data && x.data.Data.Error) {
                    _this.$message.error(x.data.Data.ErrMsg);
                } else {
                    //_this.func_getTemplateCategories(_this.treem_default_expanded_keys);

                    // 这里应该先加载“单元管理”
                    _this.func_gettypelistAll();
                }
            } else {
                _this.$message.error(x.data.Msg);
            }
        }).catch(x => {
            _this.m_showleftloding = false;
            // debugger;
        });
    },

    func_delbmc() {
      if(!this.hasDelAuth) {
        this.$message.warning("没有编辑权限")
        return
      }
        // 操作确认
        var _this = this;
        _this.$confirm("确认删除该分类?", "操作确认", {
            confirmButtonText: '确认',
            cancelButtonText: '取消',
            type: 'warning'
        }).then(x => {
            _this.do_delbmc();
        }).catch(x => {

        });
    },

    // 弹出重命名的窗口
    func_renamebmc(ev) {
      if(!this.hasEditAuth) {
        this.$message.warning("没有编辑权限")
        return
      }
      var _this = this;
      ev && ev.stopPropagation && ev.stopPropagation();

      //1. 设置弹出窗口的标题
      //2. 重置弹出窗口中的输入内容
      //3. 重置“当前选中项”
      //4. 显示出来
      _this.m_bmcedittitle = '重命名';
      _this.m_bmceditingname = _this.m_currentbmc.bmc_name;
      //_this.m_currentbmc = undefined;
      _this.status_bmcadd = true;
    },

    // 流程模板分类的上下文菜单样式对象
    style_getContextStyle(){
        var _this = this;
        var _s = {};
        _s["left"] = _this.m_contextpos.left + 'px';
        _s["top"] = _this.m_contextpos.top + 'px';
        return _s;
    },

      style_getContextStyle2(){
        var _this = this;
        var _s = {};
        _s["left"] = _this.m_contextpos2.left + 'px';
        _s["top"] = _this.m_contextpos2.top + 'px';
        return _s;
    },

    _stopPropagation2(ev) {
      ev && ev.stopPropagation && ev.stopPropagation();
    },

    // 显示流程模板分类的上下文菜单
    func_showmenu(ev, item) {

        // 先关闭全部
        var _this = this;
        _this.closeall();
        _this.clickMaterialTypeID = item.bmc_guid;

        // 获取所点击元素（按钮）的top及left
        var rect = ev.target.getBoundingClientRect();

        // 阻止冒泡
        _this._stopPropagation2(ev);
        // 如果是叶子节点，则呼出之前的菜单
        var itemIsLeaf = item.isLeaf;
        if (itemIsLeaf) {
          // 设置“正在编辑的构件分类项”
          _this.extdata.editingmaterialtype = item;

          // 取位置
          _this.extdata.typemenu_css.left = rect.x - 40;
          _this.extdata.typemenu_css.top = rect.y + 32;

          _this.extdata.typemenu_css.display = true;
          return;
        }

        // 设置上下文菜单的left及top
        // 后续再根据是否超出屏幕再调整
        _this.m_contextpos.top = rect.y + 12;
        _this.m_contextpos.left = rect.x + 12;

        // 记录上下文菜单的宽及高度
        var context_h = 96;
        var context_w = 160;

        // 根据当前屏幕高度及菜单本身高度，修改contextpos.top的值
        if (_this.m_contextpos.left > document.body.clientWidth - context_w) {
            _this.m_contextpos.left = document.body.clientWidth - context_w;
        }
        if (_this.m_contextpos.top > document.body.clientHeight - context_h) {
            _this.m_contextpos.top = document.body.clientHeight - context_h;
        }
        // 显示流程模板分类的上下文菜单
        _this.m_contextpos.isshowing = true;

        // 设置当前数据是哪条，以显示内容，及提交数据guid
        _this.m_currentbmc = item;
    },

    // 保存当前正在编辑的分类名称（新增或修改）
    func_savebmcadd(){
      // 准备参数
      var _this = this;
      // 确定 bmc_code
      var _curNode = _this.$refs.ref_bmc.getCurrentNode();
      var _bmc_code;
      if (!_curNode) {
        _bmc_code = '';
      } else {
        _bmc_code = _curNode.bmc_code || '';
      }
      // 确定 bmc_guid
      // 确定或许已存在的 bmc_elementids
      var _bmc_guid = '';
      var _bmc_elementids = '';
      if (_this.m_currentbmc) {
        _this.m_currentbmc.bmc_guid && (_bmc_guid = _this.m_currentbmc.bmc_guid);
        _this.m_currentbmc.bmc_elementids && (_bmc_elementids = _this.m_currentbmc.bmc_elementids);
      }

      var _url = `${this.$MgrBaseUrl.EditCategory}`;
      var _para = {
        Token: _this.$staticmethod.Get("Token"),
        bmc_guid: _bmc_guid,
        organizeId: _this.$staticmethod._Get("organizeId"),
        bmc_name: _this.m_bmceditingname,
        bmc_code: _bmc_code,
        bmc_elementids: _bmc_elementids
      };

      // 发请求，添加数据
      _this.$axios({
        url: _url,
        method: 'post',
        data: _para
      }).then(x => {
        if (x.data.Ret > 0) {
          _this.status_bmcadd = false;
          // 刷新数据
          // debugger
          if (_this.treem_default_expanded_keys.indexOf(_bmc_guid) < 0) {
            _this.treem_default_expanded_keys.push(_bmc_guid);
          }
          if(_this.addOrEdit == 1){ //新增
            _this.$message.success('添加成功');
            _this.addOrEdit == 0
            _this.func_gettypelistAll();

            let node = this.$refs.ref_bmc.getNode(this.clickMaterialTypeID); // 通过节点id找到对应树节点对象
            if(node){
              node.loaded = false;
              node.expand(); // 主动调用展开节点方法，重新查询该节点下的所有子节点
            }

          }else if(_this.addOrEdit == 2){ //修改
            _this.$message.success('修改成功');
            _this.refreshNodeBy();
            _this.addOrEdit == 0
          }

          // _this.func_gettypelistAll();
        } else {
          _this.$message.error(x.data.Msg);
        }
      }).catch(x => {
        console.error(x);
      });
    },

    func_closebmcedit(){
      var _this = this;
      _this.status_bmcadd = false;
    },
    // 清除选中的节点
    func_clearselect(ev) {
      var _this = this;
      _this.treefunc_clearcurrent();
    },

    // 当前节点选中变化
    treefunc_current_change(data, node) {
      // console.log(data, node)
        // 不需要是叶子节点
        var _this = this;
        _this.m_currentbmc = data;
        _this.clickTreebmc_code = data.bmc_code; // 保存当前点击的tree的bmc_code(格式：001-001)
        // selectCateObj 为 CompsMaterialRelation 使用，暂未赋值
        console.log('selectCateObj 暂未赋值',data);

        // clickMaterialTypeID 导出模板时应用到的 bc_guid
        _this.clickMaterialTypeID = data.bmc_guid;

        // 设置构件数据表格左上角显示的名称
        _this.clickMaterialTypeName = data.bmc_name;

        // extdata.selectedtypeid 为自动关联使用
        _this.extdata.selectedtypeid = data.bmc_guid;

        // 清空选中的数据
        _this.extdata.selectedData = [];

        // closeall
        _this.closeall();

        // 加载这个分类下的数据
        _this.loadmaterialitems2(data.bmc_guid);
        _this.R_bmc_name = data.bmc_name
    },

    // 收起节点 回调
    // 展开节点 回调
    node_collapse(itemi, node, comp) {
        // 移除展开过的节点
          var _this = this;
        if (_this.treem_default_expanded_keys.indexOf(node.key) >= 0) {
            _this.treem_default_expanded_keys = _this.treem_default_expanded_keys.filter(x => x != node.key);
        }
        itemi.classname = "icon-new-tree-last";
    },
    node_expand(itemi, node, comp) {
        // 记录展开过的节点
        // 记录到 treem_default_expanded_keys 中
        var _this = this;
        _this.closeall();
        _this.clickMaterialTypeID = itemi.bmc_guid;
        if (_this.treem_default_expanded_keys.indexOf(node.key) < 0) {
            _this.treem_default_expanded_keys.push(node.key);
        }
        itemi.classname = "icon-new-tree-last";
    },

    // 加载子节点
    treefunc_loadChild(node, resolve) {
        // 页面初次加载会自动调用此处，且 node.id 为0
        // 如果 node.id 为 0，直接 return
        if (node.id == 0) {
            return;
        }

        // 拿着 code 请求子一级数据
        var _this = this;
        var _organizeId = _this.$staticmethod._Get("organizeId");
        var _pcode = node.data.bmc_code;

        // 收起又显示树的情况
        if (node.data.length != undefined && node.data.length >= 0) {
          _this.func_gettypelistAll();
          return;
        }
        if(node.data.bmc_guid == this.treem_default_checked_keys[0]){
          this.treefunc_current_change(node.data)
        }

        var _url = `${this.$MgrBaseUrl.GetCategories}?organizeId=${_organizeId}&baseCode=${_pcode}&Token=${_this.$staticmethod.Get('Token')}`;
        _this.$axios.get(_url).then(x => {
          if (x.data && x.data.Ret > 0) {
            // this.treefunc_current_change(x.data.Data.list[0])
            // 深拷贝、补充 classname、设置 isLeaf
            var retdata = _this.$staticmethod.DeepCopy(x.data.Data.list);
            for (var i = 0; i < retdata.length; i++) {
              retdata[i].classname = 'icon-new-tree-last';
              retdata[i].isLeaf = retdata[i].DirectChildrenCount == 0;
              this.treem_default_expanded_keys.push(retdata[i].bmc_guid)
              if (retdata[i].isLeaf) {
                retdata[i].classname = 'icon-new-tree _css-1890ff';
              }
            }

            resolve(retdata);
          }
        }).catch(x => {
          console.error(x);
        });
    },

    _viewidoc(willshow, urlall, doctype){
      var _this = this;
      _this.$emit("set_projectboot_extdata", "_docviewtype", doctype);
      _this.$emit("set_projectboot_extdata", "_show_idocview", willshow);
      _this.$emit("set_projectboot_extdata", "_idocviewurl", urlall);
    },

    // 请求自动关联接口。
    // modellist： 模型id和名称数组。
    // elepropval：构件列表的属性字段，为 bm_materialcode 或
    // elename： 模型构件的属性名称。\
    // ========原来一条一条关联，后台关联方法======

   recursivePushAndLastPostAPI(_t_Data,Properties_data,_modelid,elepropval, elename) {
      // _t_Data  表格数据，
      // Properties_data，  /Model/GetAllElementsWithSingleProperty接口获取的properties[0]属性
      // _modelid,   模型id
      // elepropval,   选择的构件列表中的属性名称 bm_materialcode=编码，bm_materialname=任务项
      //  elename  输入的模型中构件属性名称   WBS编码
      let propnameSelected = '';
      if(elepropval == 'bm_materialcode'){
        propnameSelected = 'materialcode'
      }else{
        propnameSelected = 'materialname'
      }
      let propnameInputted = elename;
      // 当前 _this.autoIndex
      // 为当前遍历的表格中的数据，从xue接口中返回的所有数据中，找到匹配的。
      let _this = this;
      let el = _t_Data[_this.autoIndex];
      let _Percent = 0
      let _matches = Properties_data.filter(x => x.name == propnameInputted && x.value == el[propnameSelected]);
      if (_matches && _matches.length) {
        for (var i = 0; i < _matches.length; i++) {
          _this.relations.push({bmguid:el.materialid, elementid:_matches[i].elementID})
          _Percent = ((_this.relations.length / _t_Data.length) * 100).toFixed(0) * 1;
        }
      }

      // let _Percent = ((_this.relations.length / _t_Data.length) * 100).toFixed(0) * 1;
        if(_Percent > 0 && _Percent < 5) {
          _this.loadingPercentage = 5;
          _this.loadingText = '构件数据获取中...';
        }else if(_Percent >= 5 && _Percent < 30){
          _this.loadingPercentage = _Percent;
            _this.loadingText = '模型数据获取中...';
        }else if(_Percent >= 30 && _Percent < 50){
          _this.loadingPercentage = _Percent;
            _this.loadingText = '模型属性获取中...';
        }else if(_Percent >= 50 && _Percent < 98){
          _this.loadingPercentage = _Percent;
          _this.loadingText = '数据匹配中...';
        }else if(_Percent >= 98 && _Percent < 99){
          _this.loadingPercentage = _Percent;
          _this.loadingText = '提交数据中...';
        }


      if (_this.autoIndex == _t_Data.length - 1) {
        // 调用接口
        let _autoSunmitData = {
            "Token": _this.$staticmethod.Get("Token"),
            "organizeId": _this.$staticmethod._Get("organizeId"),
            "modelId": _modelid,
            "PairArr": _this.relations,
            "OperateType": this.autoRelType
          }
        // console.log(_this.relations,'=======_this.relations',_this.relations.length)
        // console.log('=======_autoSunmitData',_autoSunmitData)
        // 成功的个数relations.length,失败的个数_t_Data.length - relations.length
        _this.$nextTick( x => {
          _this.getSubmitAutoRelation(_autoSunmitData);
        })
      } else {
        // 更新进度条
        setTimeout(function(){
          _this.autoIndex += 1;
          _this.recursivePushAndLastPostAPI(_t_Data,Properties_data,_modelid,elepropval, elename);
        }, 0);
      }
    },
   do_autoRelSubmit(_t_Data,modellist, elepropval, elename) {
      console.log(modellist, elepropval, elename)
      var _this = this;
      _this.showautoRelation = false;
      _this.loadingPercentage = 5;
      _this.loadingText = '构件数据获取中...';
      _this.relations = [];
      let _modelid = modellist[0].modelid;
      let vaultID = _this.$staticmethod._Get("organizeId");
      let _url = `${this.$ip('newModelHttpUrl')}/Model/GetAllElementsWithSingleProperty?VaultID=${vaultID}&ModelID=${_modelid}&VersionNO=&PropertyName=${elename}`
      _this.$axios.get(_url).then(x => {
        if (x.data && x.data.length > 0) {
          let _data = x.data;

          let Properties_data = [];  // 接口返回含有Properties的值
          let relations = [];

          for(let j = 0; j < _data.length; j++){
            if(_data[j].properties.length>0){
              Properties_data.push(_data[j].properties[0])
            }
          }

          // 重置 autoIndex
          _this.autoIndex = 0;

          // 递归调用 push
          if (_t_Data.length > 0) {
            setTimeout(function(){
                _this.recursivePushAndLastPostAPI(_t_Data,Properties_data,_modelid,elepropval, elename);
            }, 0);
          }
        } else {
          _this.$message.error('暂无匹配数据');
          _this.LoadingAutoElement = false;

        }
      }).catch(x => {
        _this.$message.error('暂无匹配数据');
        _this.LoadingAutoElement = false;
      });

    },
    getSubmitAutoRelation(autoSunmitData){
      let _this = this;
      _this.loadingPercentage = 99;
      _this.loadingText = '数据匹配中...';
      _this.$axios({
        url:  `${window.bim_config.webserverurl}/api/Material/Mtr/InsertSubmitAutoRelation`,
        method: 'post',
        data: autoSunmitData,
      }).then( x => {
        if(x.status == 200 && x.data.Ret == 1){
          _this.loadingText = '提交数据中...';
          _this.loadingPercentage = 100;
          _this.$message.success('关联成功');
          _this.LoadingAutoElement = false;
          _this.cancelRelevanceReloadTble();
        }else{
          _this.$message.error(x.data.Msg);
          _this.LoadingAutoElement = false;
        }
      }).catch( error => {
        _this.$message.error('暂无数据');
        _this.LoadingAutoElement = false;
      })
    },
    // 自动关联的对话框传出事件
    autoRelSubmit(choseModelList, elementpropertyval, elementName,type) {
      var _this = this;
      if (choseModelList.length == 0) {
        _this.$message.error('请选择模型');
        return;
      }
      if (elementpropertyval == '') {
        _this.$message.error('请指定构件列表属性');
        return;
      }
      if (elementName == '') {
        _this.$message.error('请输入模型构件属性名称');
        return;
      }
      this.autoRelType = type; // 0 新增   1 覆盖
      // 操作确认
      _this.$confirm('即将自动关联当前分类下构件数据，确认操作？', '操作确认', {
        confirmButtonText: '确定',
        cancelButtonText:'取消',
        type: 'warning'
      }).then(x => {
        _this.loadingNumber = 0;
        _this.successLoadingNumber = 0;
        _this.failLoadingNumber = 0;
        _this.LoadingAutoElement = true;
        _this.autoLoadingSuccess = true;
        // console.log(choseModelList, elementpropertyval, elementName);
        // 因为是自动关联，先调用接口要先获取所有数据，在进行关联
        let _data=`PageNum=1&PageSize=9999999&bc_guid_materialtype=${_this.extdata.selectedtypeid}&bm_materialname=&bm_materialcode=&statusIdlistjson=&ifhasrelation=&updatetimestart=&updatetimeend=&SortField=sort&SortType=asc&organizeId=${_this.$staticmethod._Get("organizeId")}&token=${this.$staticmethod.Get("Token")}`
        _this
          .$axios({
            url: `${this.$MgrBaseUrl.GetMaterialList_Condition2}?${_data}`,
            method: "get",
          })
          .then(x => {

            if (x.status == 200) {
              let _t_Data = _this.convertToDt(x.data.Data.Data);
              _this.do_autoRelSubmit(_t_Data,choseModelList, elementpropertyval, elementName);
            } else {
              console.error(x);
            }
          })
          .catch(x => {
            console.error(x);
          });
      }).catch(x => {
      });
    },
    autoRelMaskClick(){
      var _this = this;
      _this.showautoRelation = false;
    },
    //
    showAutoRelation() {
      var _this = this;
      if (!_this.m_currentbmc) {
        _this.$message.error('请先选中分类叶子节点');
        return;
      }

      var _bmc_guid = _this.m_currentbmc.bmc_guid;
      _this.func_testIfIsLeafAutoRelation(_bmc_guid, ()=>{
        _this.closeall();
        _this.showautoRelation = true;
      });

    },
    // 判断某节点是否为叶子节点，如果是，调用回调
    func_testIfIsLeafAutoRelation(bmc_guid, callback) {
      var _this = this;
      var _organizeId = _this.$staticmethod._Get("organizeId");
      var _bmc_guid = bmc_guid;
      var _url = `${this.$MgrBaseUrl.TestCategoryIsLeaf}?organizeId=${_organizeId}&bmc_guid=${_bmc_guid}&Token=${_this.$staticmethod.Get('Token')}`;
      _this.$axios.get(_url).then(x => {
        if (x.data.Ret > 0) {
          if (callback) {
                callback();
              }

        } else {
          _this.$message.error(x.data.Msg);
        }
      }).catch(x => {
        console.error(x);
        // debugger;
      });
    },


    showorhiderelmethod(){
      var _this = this;
      _this.bIfShowRelationDropItems = !_this.bIfShowRelationDropItems
    },
    // 选择文件后：先验证数据是否有重复，再让用户选择是否覆盖
    ev_importbmcChange() {
      var _this = this;

      // config
      var config = {
        headers: {
          "Content-Type": "application/json"
        }
      };

      // url
      var _url = `${window.bim_config.webserverurl}/api/Material/MaterialCategory/TestImortingCodeRepeat?token=${this.$staticmethod.Get("Token")}`;

      // fd 参数准备
      var fd = new FormData();
      var dom = document.getElementById('id_importbmc_File');
      var files = dom.files;
      if (files.length == 0) {
        _this.$message.error('未选择文件');
        return;
      }
      fd.append("File", files[0]);
      fd.append("OrganizeId", _this.$staticmethod._Get("organizeId"));
      _this.$axios.post(_url, fd, config).then(x => {
        if (x.data.Ret > 0) {
          if (x.data.Data) {
            // 询问用户
            _this.$confirm("已存在部分导入的数据，是否覆盖？", "操作确认", {
              confirmButtonText: '覆盖',
              cancelButtonText: '跳过重复',
              type: 'warning'
            }).then(x => {
              _this.do_importbmc(_this.$staticmethod._Get("organizeId"), fd, true);
            }).catch(x => {
              _this.do_importbmc(_this.$staticmethod._Get("organizeId"), fd, false);
            })
          } else {
            // 直接调用导入 function
            _this.do_importbmc(_this.$staticmethod._Get("organizeId"), fd, false);
          }
        } else {
          _this.$message.error(x.data.Msg);
        }
      }).catch(x => {
        console.error(x);
      });


    },
    do_importbmc(organizeId, fd, willOverride) {
      var _this = this;
      var _Token = _this.$staticmethod.Get("Token");
      fd.append("Token", _Token);
      fd.append("WillOverride", willOverride);

      var config = {
        headers: {
          "Content-Type": "application/json"
        }
      };

      var _url = `${window.bim_config.webserverurl}/api/Material/MaterialCategory/ImportCategories`;

      _this.$axios.post(_url, fd, config).then(x => {
        if (x.data.Ret > 0) {
          // 提示，刷新
          _this.$message.success(x.data.Msg);
          _this.loadtypes();
        } else {
          _this.$message.error(x.data.Msg);
        }
      }).catch(x => {
        console.error(x);
      });
    },

    // 点击导入全部
    func_showbmcimporting() {
      if(!this.hasEditAuth) {
        this.$message.warning("没有编辑权限")
        return
      }
      var _this = this;
      var dom = document.getElementById('id_importbmc_File');
      dom.value = '';
      dom.click();
    },

    // 导出全部
    func_exportall(ev) {
      var _this = this;
      var _organizeId = _this.$staticmethod._Get("organizeId");
      var url = `${window.bim_config.webserverurl}/api/Material/MaterialCategory/ExportCategories?organizeId=${_organizeId}&token=${this.$staticmethod.Get("Token")}`;
      window.location.href = url;
    },

    // 显示菜单
    func_showwbsmgr(ev) {
      // 关闭全部
      var _this = this;
      _this.closeall();

      // 获取所点击元素（按钮）的top及left
      var rect = ev.target.getBoundingClientRect();

      // 阻止冒泡
      _this._stopPropagation2(ev);

      // 设置上下文菜单的left及top
      // 后续再根据是否超出屏幕再调整
      _this.m_contextpos2.top = rect.y + 12;
      _this.m_contextpos2.left = rect.x + 12;

      // 记录上下文菜单的宽及高度
      var context_h = 96;
      var context_w = 160;

      // 根据当前屏幕高度及菜单本身高度，修改contextpos.top的值
      if (_this.m_contextpos2.left > document.body.clientWidth - context_w) {
          _this.m_contextpos2.left = document.body.clientWidth - context_w;
      }
      if (_this.m_contextpos2.top > document.body.clientHeight - context_h) {
          _this.m_contextpos2.top = document.body.clientHeight - context_h;
      }
      _this.m_contextpos2.isshowing = true;
    },
    // 刷新页面或点击类型
    loadmaterialitems2(typeid, willnotshowloading) {
      // console.log(typeid,willnotshowloading)
      var _this = this;
      if(_this.oldSetmaterialtype !== typeid){
        _this.oldSetmaterialtype =  typeid;
        _this.extdata.selectedtypeid = typeid;
        _this.reset_filters(true);
        _this.handleFilters(willnotshowloading);
      }
    },
    // 点击筛选
    handleFilters(willnotshowloading) {
      //debugger;
      let _this = this;
      _this.extdata.tableData=[];
      _this.elTableLoading = false;
      //console.log('显示loading');
      if (!willnotshowloading ) {
        _this.elTableLoading = true;
      }

      let startTime = _this.extdata.filterStartTime
        ? _this.formatDateTime(_this.extdata.filterStartTime)
        : "";
      let endTime = _this.extdata.filterEndTime
        ? _this.formatDateTime(_this.extdata.filterEndTime)
        : "";

        // 处理 _this.extdata.selectedfilterstatus_editing筛选中为空值的数组转换为null
        let filterStateArr = _this.extdata.selectedfilterstatus_editing.map(x => x == "" ? null : x);

      // 获取编码-单元名称-状态-关联构件-开始时间-结束时间
      let axiosSortType = _this.extdata.tableSortTypeClick ? "desc" : "asc";
      let __statusIdlistjson = JSON.stringify(
              filterStateArr
            )
      this.extdata.selectedtypeid = this.extdata.selectedtypeid || '-1000'
      let _ifhasrelation = ''
      if(_this.extdata.filterCheckList.length == 1){
        _ifhasrelation = _this.extdata.filterCheckList[0]
      }else{
        _ifhasrelation = ''
      }
      let _data=`PageNum=${this.pageNum}&PageSize=${this.pageSize}&bc_guid_materialtype=${_this.extdata.selectedtypeid}&bm_materialname=${_this.extdata.filter_mat_name_component}&bm_materialcode=${_this.extdata.text_filter_mat_name}&statusIdlistjson=${__statusIdlistjson}&ifhasrelation=${_ifhasrelation}&updatetimestart=${startTime}&updatetimeend=${endTime}&SortField=sort&SortType=${axiosSortType}&organizeId=${_this.$staticmethod._Get("organizeId")}&token=${this.$staticmethod.Get("Token")}`
      _this
        .$axios({
          url: `${this.$MgrBaseUrl.GetMaterialList_Condition2}?${_data}`,
          method: "get",
        })
        .then(x => {
             if (!willnotshowloading) {
                _this.elTableLoading = false;
             }
          if (x.status == 200) {
            // console.log(x);
            _this.extdata.tableData = _this.convertToDt(x.data.Data.Data);
            // console.log(_this.extdata.tableData)
            _this.extdata.filter_css.display = false;
            _this.paginationPageLength = x.data.Data.Total
          } else {
            console.error(x);
          }
        })
        .catch(x => {
          console.error(x);
        });
    },
    // 点击表头排序
    handleTableSort(type) {
      let _this = this;
      _this.extdata.tableSortType[type] = !_this.extdata.tableSortType[type];
      _this.extdata.tableSortTypeClick = !_this.extdata.tableSortType[type]
      _this.extdata.tableSortField = type;
      _this.handleFilters();
    },
    createStateFilter(queryString) {
      return state => {
        return (
          state.value.toLowerCase().indexOf(queryString.toLowerCase()) === 0
        );
      };
    },
    // 点击表格选择收起左边菜单栏
    chooseCloseOrOpen() {
      this.tableClose = !this.tableClose;
      this.leftRightHide = this.tableClose
    },
    // table表头添加icon
    renderHeaderIcon(h, { column }) {
      return h("span", [
        h("span", column.label),
        h("i", {
          class: "icon-interface-filter",
          style: "margin-left:5px;vertical-align: middle;"
        })
      ]);
    },
    handleCurrentChange(currentRow, oldCurrentRow) {
      console.log(currentRow)
      this.extdata.currentRow=currentRow
    },
    // 鼠标滑过模型，展示模型名称
    modelMouseEnter(row,column,cell) {
      if(column.label !== "关联模型"){
        this.modelListTableShow = false;
      }
    },

    // 借助服务器返回二维码图片
    getQrCodeSrc(materialid) {
      let encodeDwgurl = encodeURIComponent(window.bim_config.dwgurl);
      let Url = `${window.location.origin}${window.bim_config.hasRouterFile}/#/MaterialsMgrMobile/${materialid}/${encodeDwgurl}/${this.$staticmethod.Get("Token")}`;
      let encodedUrl = encodeURIComponent(Url);
      let codeImgUrl = `${this.$urlPool.QRCode}?encodedUrl=${encodedUrl}&token=${this.$staticmethod.Get("Token")}`;
      return codeImgUrl;
    },
    // 模型打开--点击更多
    showMoreButtom() {
      let _this = this;
      _this.changeShowButtomMore = true
      let clientmodeldetail = _this.$refs.showMoreButtomRef.getBoundingClientRect()
      _this.extdata.showMoreButtomStyle.left = clientmodeldetail.x - 120 || 0
      _this.extdata.showMoreButtomStyle.top = '122'
    },

    delsingle() {
      var _this = this;
      _this
        .$confirm("确定删除该条数据？", "操作确认", {
          confirmButtonText: "确认",
          cancelButtonText: "取消",
          type: "warning"
        })
        .then(x => {
          _this.do_delsingle();
        })
        .catch(x => {});
    },

    do_savemulstatus(statusid) {
      var _this = this;
      // 判断是否选中了数据，如果选中了，把选中的数据额外再引用到 seldatas 变量中
      var _this = this;
      var seldatas = _this.extdata.selectedData;
      if (seldatas.length == 0) {
        _this.$message.error("请选择数据再操作");
        return;
      }
      var _Token = _this.$staticmethod.Get("Token");
      var _Ids = "";
      for (var i = 0; i < seldatas.length; i++) {
        _Ids += `${seldatas[i].materialid}${
          i == seldatas.length - 1 ? "" : ","
        }`;
      }

      _this
        .$axios({
          url: `${window.bim_config.webserverurl}/api/Material/Mtr/ModifyMaterialItemsStatus`,
          method: "post",
          data: _this.$qs.stringify({
            Token: _Token,
            StatusId: statusid,
            Ids: _Ids
          })
        })
        .then(x => {
          if (x.status == 200) {
            if (x.data.Ret > 0) {
              _this.$message.success("修改成功");
              _this.loadmaterialitems_bycurrentfilters();
            } else {
              _this.$message.error(x.data.Msg);
            }
          } else {
            console.error(x);
          }
        })
        .catch(x => {
          console.error(x);
        });
    },

    savemulstatus(statusid) {
      var _this = this;
      if (statusid == 'tocorrect') {
        _this
        .$confirm("您将修改的新状态为\"待修正\"，修改后无法再次修改状态，是否确认？", "操作确认", {
          confirmButtonText: "确认",
          cancelButtonText: "取消",
          type: "warning"
        })
        .then(x => {
          _this.do_savemulstatus(statusid);
          // _this.handleFilters()
          _this.closeall();
        })
        .catch(x => {});
      } else {
        _this
        .$confirm("确定将所选状态应用到所选数据？", "操作确认", {
          confirmButtonText: "确认",
          cancelButtonText: "取消",
          type: "warning"
        })
        .then(x => {
          _this.do_savemulstatus(statusid);
          _this.closeall();
        })
        .catch(x => {});
      }

    },

    showmulstatusoptions() {
      var _this = this;
      if (_this.extdata.bIsShowingMulStatusOptions == true) {
        _this.extdata.bIsShowingMulStatusOptions = false;
      } else {
        _this.extdata.bIsShowingMulStatusOptions = true;
      }
    },
    cancelRelevanceReloadTble(){
      // 刷新当前表格的数据
      this.handleFilters(this.clickMaterialTypeID);
    },
    changefiltersort(para) {
      var _this = this;
      if (para) {
        _this.extdata.selectedfiltersorttype = para;
      } else {
        _this.extdata.selectedfiltersorttype = undefined;
      }
      _this.closeall();
      _this.loadmaterialitems_bycurrentfilters();
    },
    getSelectedStatusStr() {
      var _this = this;
      var retstr = "";
      for (var i = 0; i < _this.extdata.selectedfilterstatus.length; i++) {
        var thisid = _this.extdata.selectedfilterstatus[i];
        var thisindex = _this.extdata.allmaterialstatus.findIndex(
          x => x.statusid == thisid
        );
        if (thisindex >= 0) {
          retstr += `${_this.extdata.allmaterialstatus[thisindex].statusname},`;
        }
      }
      // 去除最后一个英文逗号
      if (retstr.length > 0) {
        retstr = retstr.substr(0, retstr.length - 1);
      }
      return retstr;
    },

    savefilter_statusselected() {
      var _this = this;
      _this.extdata.selectedfilterstatus = _this.$staticmethod.DeepCopy(
        _this.extdata.selectedfilterstatus_editing
      );
      _this.loadmaterialitems_bycurrentfilters();

      // 关闭小弹层
      _this.extdata.isshow_filter_mat_status = false;
    },
    togglestatusoptions() {
      var _this = this;
      if (_this.extdata.isshow_filter_mat_status == true) {
        _this.extdata.isshow_filter_mat_status = false;
      } else {
        // 显示前将 selectedfilterstatus 赋予 selectedfilterstatus_editing
        _this.extdata.selectedfilterstatus_editing = _this.$staticmethod.DeepCopy(
          _this.extdata.selectedfilterstatus
        );
        _this.extdata.isshow_filter_mat_status = true;
      }
    },
    togglestatusselect(id) {
      var _this = this;
      if (_this.extdata.selectedfilterstatus_editing.indexOf(id) >= 0) {
        _this.extdata.selectedfilterstatus_editing = _this.extdata.selectedfilterstatus_editing.filter(
          x => x != id
        );
      } else {
        _this.extdata.selectedfilterstatus_editing.push(id);
      }
    },

    reset_filters2(){
      var _this = this;
      _this.reset_filters(true);
    },
    reset_filters(willnotreload) {
      var _this = this;
      _this.extdata.text_filter_mat_name = "";
      _this.extdata.filter_mat_name_component = "";
      _this.extdata.selectedfiltersorttype = undefined;
      _this.extdata.selectedfilterbhaselerel = undefined;
      _this.extdata.selectedfilterstatus = [];
      _this.extdata.selectedfilterstatus_editing = [];
      _this.extdata.filterCheckList = [];
      _this.extdata.filterStartTime = "";
      _this.extdata.filterEndTime = "";
      if (willnotreload == true) {

      } else {
        _this.loadmaterialitems_bycurrentfilters();
      }

    },
    change_bhaselerel(selrelval) {
      var _this = this;
      _this.extdata.selectedfilterbhaselerel = selrelval;
      _this.loadmaterialitems_bycurrentfilters();
    },

    // 根据当前的过滤状态，加载数据
    loadmaterialitems_bycurrentfilters() {
      // 所选择的构件分类
      var _this = this;
      var _selectedtypeid = _this.extdata.selectedtypeid;

      // 所输入的关键字
      var keyWorld = _this.extdata.text_filter_mat_name;

      // 所选择的排序方式
      var sortstr = _this.extdata.selectedfiltersorttype;

      // 所选择的关联构件的情况（可选值："0", "1", "-1"）
      var elerelstr = _this.extdata.selectedfilterbhaselerel;

      // 所选择的所有状态
      var statusstr = "";
      for (var i = 0; i < _this.extdata.selectedfilterstatus.length; i++) {
        statusstr += `${_this.extdata.selectedfilterstatus[i]}${
          i == _this.extdata.selectedfilterstatus.length - 1 ? "" : ","
        }`;
      }
      _this.loadmaterialitems(
        _selectedtypeid,
        sortstr,
        elerelstr,
        statusstr,
        keyWorld
      );
      if(_this.oldSetmaterialtype == -1000){
        _this.handleFilters()
      }
    },

    //sort_change
    sort_change(column_order_prop) {
      // console.log(column_order_prop)
      var _this = this;
      if (column_order_prop && column_order_prop.order == '"descending"') {
        _this.loadmaterialitems(
          _this.extdata.selectedtypeid,
          "updatetime_desc"
        );
      } else if (column_order_prop && column_order_prop.order == "ascending") {
        _this.loadmaterialitems(_this.extdata.selectedtypeid, "updatetime_asc");
      } else {
        _this.loadmaterialitems(_this.extdata.selectedtypeid, "");
      }
    },

    // 选中数据后，在表格表头点“导出”
    // 或是表格上方
    exportselected() {

      console.log('表格表头、表格上方按钮触发导出');
      var _this = this;
      var _bc_guid = _this.extdata.selectedtypeid;
      var _bm_guids = "";
      if (_this.extdata.selectedData.length == 0) {
        _this.$message.error("请先选中数据");
        return;
      }
      for (var i = 0; i < _this.extdata.selectedData.length; i++) {
        _bm_guids += `${_this.extdata.selectedData[i].materialid}${
          i == _this.extdata.selectedData.length - 1 ? "" : ","
        }`;
      }
      this.downloadCategoryTemplate_post(_bc_guid,_bm_guids)
    },
    downloadCategoryTemplate_post(bc_guid,bm_guids){
      let _this = this
      _this
        .$axios({
          url: `${this.$MgrBaseUrl.DownloadCategoryTemplate_post}`,
          method: "post",
          withCredentials: true,
          // headers:{
          //     Cookie:'.AspNetCore.Session=CfDJ8Mla5vWFYOZEr0wDhj%********************************%2F1yGomYMtCuOZyLdeZTeTfGtqZewrhOGY9bsnObzc%2FPa51nJt8xlzVMqq%2F4A6NEg%2BqtxUdUdPS5EvQ6JIbM%2FNL8OaH4GXM93WFSvbf55CzLEtLOs6U6GZ31MpQg; ProBIMe.Cookie=CfDJ8Mla5vWFYOZEr0wDhj%2FthD2XHC%2Bsnk4%2FNyrCYmeYMjhiIAmgJ8c3iHvNzRc0zgn%2FO%2B4xzQa%2FRHx%2FsTWSCpgJsWR049KK1Uee1RsHFtmwYK6viFwgVmT%2BQce%2FKx1C6HaTlep%2BAcRpC7UwE54X8ivmwXmvciQExjgv4Lrr%2FpiigeYD',
          //     // Cookie:'.AspNetCore.Session=CfDJ8Mla5vWFYOZEr0wDhj%2FthD34CmzUzqI446I2R%2BJH%2B4Hmxw03e9RXfFcGY86Z72Z18uc82zfErt0QQ3eCpKDNrVgOc1q87prin5vGF5gyIAZWLLZS%2Boo%2BmvLY%2BK4Xm0WxiJyq4sF8Bu%2FU6%2Bfmo1xBB9NCVFxojGos0MeyYJz2ayPB; ProBIMe.Cookie=CfDJ8Mla5vWFYOZEr0wDhj%2FthD1BTXY08LPZvTE8ACqgpF1U8hgtve9Hf6p5QoHJWyiS9MDlkBfKKNush0On1TBHqiaRsREkiXz0fAGL%2F2ikeeFo6dpxxMngy5pWk8UT2nMMqPYWuf619z9%2BUkkzebvtkyMQIIMfmSPlPa%2BuIVwCanc0',
          //     // Cookie: 'ProBIMe.Cookie=CfDJ8Mla5vWFYOZEr0wDhj%2FthD2pACijDDvdZojO%2B5qlvp%2BC%2BxVBYS7is0iAqUiAzCdZkgMkk3rxtLxhFdSfnSWTB7B48o79Pf6fsu%2BP5PQ%2F4t%2FgtBTkzClPhycWpNwG%2BAWfx3bpn392Z2xTHcLpR7GJ2wGS18NKPZ7GVzVwapOr%2B8xg'
          // },
          data: {
            Token: this.$staticmethod.Get("Token"),
            bc_guid: bc_guid,
            bm_guids: bm_guids,
          }
        })
        .then(x => {
            if (x.data.Ret > 0) {
              const appGuid = x.data.Data;
              let organizeId = _this.$staticmethod._Get("organizeId");
              let encodeBimserverurl =  encodeURIComponent(
                window.bim_config.newModelHttpUrl
              );
              let encodeIdocviewurl = encodeURIComponent(
                window.bim_config.webserverurl
              );
              let encodeDwgurl = encodeURIComponent(window.bim_config.dwgurl);
              let originurl = encodeURIComponent(window.location.origin); // 末尾无‘/’
              let down_url = `${window.bim_config.webserverurl}/api/Material/Mtr/DownloadCategoryTemplate_Get?Token=${this.$staticmethod.Get("Token")}&AppGuid=${appGuid}&OrganizeId=${organizeId}&EncodeBimserverurl=${encodeBimserverurl}&EncodeIdocviewurl=${encodeIdocviewurl}&EncodeDwgurl=${encodeDwgurl}&EncodeOrigin=${originurl}&Bc_guid=${bc_guid}`;
              // debugger
              setTimeout(x => {
                console.log(`${window.bim_config.webserverurl}/api/Material/Mtr/DownloadCategoryTemplate_Get?Token=${this.$staticmethod.Get("Token")}&AppGuid=${appGuid}&OrganizeId=${organizeId}&EncodeBimserverurl=${encodeBimserverurl}&EncodeIdocviewurl=${encodeIdocviewurl}&EncodeDwgurl=${encodeDwgurl}&EncodeOrigin=${originurl}&Bc_guid=${bc_guid}`);
                // debugger
                window.location.href = down_url
                // `${window.bim_config.webserverurl}/api/Material/Mtr/DownloadCategoryTemplate_Get?Token=${this.$staticmethod.Get("Token")}&AppGuid=${appGuid}&OrganizeId=${organizeId}&EncodeBimserverurl=${encodeBimserverurl}&EncodeIdocviewurl=${encodeIdocviewurl}&EncodeDwgurl=${encodeDwgurl}&EncodeOrigin=${originurl}&Bc_guid=${bc_guid}`;
              }, 1000);

            } else {
              _this.$message.error(x.data.Msg)
              console.error(x);
            }
        })
        .catch(x => {
          console.error(x);
        });
    },
    getFileNameFromResponseHeaders(response) {
      const contentDisposition = response.headers['content-disposition'];
      const fileNameMatch = contentDisposition.match(/filename="(.+)"/);
      if (fileNameMatch && fileNameMatch.length === 2) {
        return fileNameMatch[1];
      } else {
        return 'download'; // 默认文件名
      }
    },
    do_delsingle() {
      var _this = this;
      var _Token = _this.$staticmethod.Get("Token");
      let _ids = []
      _ids.push(_this.extdata.selectedmaterial.materialid)

      _this
        .$axios({
          url: `${this.$MgrBaseUrl.RemoveMaterialItems}`,
          method: "post",
          data: {
            Token: _Token,
            Ids: [_this.extdata.selectedmaterial.materialid]
          }
        })
        .then(x => {
          if (x.status == 200) {
            if (x.data.Ret > 0) {
              _this.$message.success("操作成功");
              _this.extdata.selectedData = [];

              _this.refresh();
              _this.loadmaterialitems2(
                _this.clickMaterialTypeID
              );
              _this.loadtypes();
              _this.cancelRelevanceReloadTble();

            } else {
              _this.$message.error(x.data.Msg);
            }
          } else {
            console.error(x);
          }
          _this.closeall();
        })
        .catch(x => {
          console.error(x);
        });
    },

    //
    do_Mul_del() {
      // 判断是否选中了数据，如果选中了，把选中的数据额外再引用到 seldatas 变量中
      var _this = this;
      var seldatas = _this.extdata.selectedData;
      // console.log(seldatas,'====4567890')
      if (seldatas.length == 0) {
        _this.$message.error("请选择数据再删除");
        return;
      }
      var _Token = _this.$staticmethod.Get("Token");
      var _Ids = [];
      for (var i = 0; i < seldatas.length; i++) {
        _Ids.push(seldatas[i].materialid)
      }

      _this
        .$axios({
          url: `${this.$MgrBaseUrl.RemoveMaterialItems}`,
          method: "post",
          data: {
            Token: _Token,
            Ids: _Ids
          }
        })
        .then(x => {
          if (x.status == 200) {
            if (x.data.Ret > 0) {
              _this.$message.success("操作成功");
              _this.extdata.selectedData = [];

               _this.refresh();

              _this.loadmaterialitems2(
                _this.clickMaterialTypeID
              );
              _this.loadtypes();
              _this.cancelRelevanceReloadTble();

            } else {
              _this.$message.error(x.data.Msg);
            }
          } else {
            console.error(x);
          }
        })
        .catch(x => {
          console.error(x);
        });
    },
    Mul_del() {
      var _this = this;
      _this
        .$confirm("确定删除选定的数据？", "操作确认", {
          confirmButtonText: "确认",
          cancelButtonText: "取消",
          type: "warning"
        })
        .then(x => {
          _this.do_Mul_del();
        })
        .catch(x => {});
    },
    _oncompleteType(materialid, eles,modelID,type){
      // type=0  新增关联构件   type=1 覆盖关联构件
      if(type == 0){
        this._oncompleteAdd(materialid, eles,modelID)
      }else{
        this._oncomplete(materialid, eles,modelID)
      }
    },
    // 新增关联构件
    _oncompleteAdd(materialid, eles,modelID){
      var _this = this;
      if (!materialid) {
        _this.$message.error('请先选择左侧构件数据');
        return;
      }
      // 发起请求，进行关联
      _this
        .$axios({
          url: `${this.$MgrBaseUrl.InsertElementsRelate}`,
          method: "post",
          data: {
            Token: _this.$staticmethod.Get("Token"),
            bme_materialid: materialid,
            bme_elementids: JSON.stringify(eles),
            OrganizeId: _this.$staticmethod._Get("organizeId"),
          }
        })
        .then(x => {
          if (x.status == 200) {
            if (x.data.Ret > 0) {
              _this.$message.success("关联成功");

              // 刷新 inData,也就是重新加载这一条数据。
              //_this.$message.error('需要刷新数据，以及当前表格重新渲染，从而显示是否带有模型的图标');

              _this.loadmaterialitems2(_this.extdata.selectedtypeid);

              // 关联后刷新关联页面的列表数据
              //debugger;
              _this.$refs.refmaterialrel.reload();

            } else {
              _this.$message.error(x.data.Msg);
            }
          } else {
            console.error(x);
          }
        })
        .catch(x => {
          console.error(x);
        });
    },
    // 覆盖关联构件
    _oncomplete(materialid, eles,modelID) {
      var _this = this;

      if (!materialid) {
        _this.$message.error('请先选择左侧构件数据');
        return;
      }

      // 发起请求，进行关联
      _this
        .$axios({
          url: `${this.$MgrBaseUrl.ElementsRelate}`,
          method: "post",
          data: {
            Token: _this.$staticmethod.Get("Token"),
            bme_materialid: materialid,
            bme_elementids: JSON.stringify(eles),
            OrganizeId: _this.$staticmethod._Get("organizeId"),
          }
        })
        .then(x => {
          if (x.status == 200) {
            if (x.data.Ret > 0) {
              _this.$message.success("关联成功");

              // 刷新 inData,也就是重新加载这一条数据。
              //_this.$message.error('需要刷新数据，以及当前表格重新渲染，从而显示是否带有模型的图标');

              _this.loadmaterialitems2(_this.extdata.selectedtypeid);

              // 关联后刷新关联页面的列表数据
              //debugger;
              _this.$refs.refmaterialrel.reload();

            } else {
              _this.$message.error(x.data.Msg);
            }
          } else {
            console.error(x);
          }
        })
        .catch(x => {
          console.error(x);
        });
    },

    _onmaterialsel(materialid) {
      var _this = this;
      _this.relationSelectedMId = materialid;
    },
    _ontypeselected(type) {
      var _this = this;
      _this.loadmaterialitems2(type.materialtypeid);
      _this.extdata.selectedtypeid = type.materialtypeid;
    },
    materialrelation_cancel() {
      var _this = this;
      _this.isComsMaterialRelationShow = false;
      _this.cancelRelevanceReloadTble();
    },
    _onimportok() {
      var _this = this;
      // 提示
      _this.$message.success("导入成功");
      // 关闭导入对话框
      _this.extdata.bIsShowingImportTemplate = false;
      // 刷新
      _this.loadmaterialitems2(
        _this.clickMaterialTypeID
      );
      _this.loadtypes();
      _this.cancelRelevanceReloadTble();
    },
    // 某个叶子节点的导出动作
    exportdatasofcate() {
      console.log('某个叶子节点的导出');
      this.closeall();
      this.downloadCategoryTemplate_post(this.clickMaterialTypeID,'')
    },
    // 判断某节点是否为叶子节点，如果是，调用回调
    func_testIfIsLeaf(bmc_guid, callback) {
      var _this = this;
      var _organizeId = _this.$staticmethod._Get("organizeId");
      var _bmc_guid = bmc_guid;
      var _url = `${this.$MgrBaseUrl.TestCategoryIsLeaf}?organizeId=${_organizeId}&bmc_guid=${_bmc_guid}&Token=${_this.$staticmethod.Get('Token')}`;
      _this.$axios.get(_url).then(x => {
        if (x.data.Ret > 0) {
          if (x.data.Data && x.data.Data.Error) {
            _this.$message.error(x.data.Data.ErrMsg);
          } else {
            if (!x.data.Data.IsLeaf) {
              _this.$message.error('当前选中的节点不是叶子节点');
            } else {
              if (callback) {
                callback();
              }
            }
          }
        } else {
          _this.$message.error(x.data.Msg);
        }
      }).catch(x => {
        console.error(x);
        // debugger;
      });
    },

    // 点击导入模板按钮
    importtemplate(ev) {
      // 传入呼出菜单对应的分类ID，并触发下载模板动作
      // 判断当前的 bmc_guid
      var _this = this;

      // 如果没有选中节点，提示选中节点
      if (!_this.m_currentbmc) {
        _this.$message.error('请先选中分类叶子节点');
        return;
      }

      var _bmc_guid = _this.m_currentbmc.bmc_guid;
      _this.func_testIfIsLeaf(_bmc_guid, ()=>{
        _this.extdata.bIsShowingImportTemplate = true;
      });

    },

    // 修改构件状态
    changematerialstatus(statusid) {
      // 提交给服务器修改构件状态
      var _this = this;
      // console.log(statusid)
      _this.extdata.changeStatusSureId = statusid
    },
    // 修改构件状态
    changeStatusSure() {
      let _this = this;
      _this.extdata.statusSureIding = _this.extdata.changeStatusSureId
      _this
        .$axios({
          url: `${window.bim_config.webserverurl}/api/Material/Mtr/ModifyMaterialItem`,
          method: "post",
          data: _this.$qs.stringify({
            Token: _this.$staticmethod.Get("Token"),
            bm_guid: _this.extdata.selectedmaterial.materialid,
            FieldName: "bc_guid_materialstatus",
            FieldVal: _this.extdata.statusSureIding
          })
        })
        .then(x => {
          if ((x.status = 200)) {
            if (x.data.Ret > 0) {
              //debugger;
              _this.$message.success("修改成功");
              _this.closeall();
              _this.loadmaterialitems2(_this.extdata.selectedtypeid);
            } else {
              _this.$message.error(x.data.Msg);
            }
          } else {
            console.error(x);
          }
        })
        .catch(x => {
          console.error(x);
        });
    },
    // 点击保存（字段）
    savecategoryfield() {
      // 发起请求
      var _this = this;

      // 字段名称为空判断
      if (_this.extdata.editingfieldname.trim() == '') {
        _this.$message.error('字段名称不能为空');
        return;
      }

      var _LoadingIns = _this.$loading({
        text: "处理中",
        target: document.getElementById("id_fieldmgr_list")
      });
      _this
        .$axios({
          url: `${this.$MgrBaseUrl.EditCategoryField}`,
          method: "post",
          data: {
            Token: _this.$staticmethod.Get("Token"),
            fieldid: _this.extdata.editingfieldid,
            bc_guid: _this.extdata.editingmaterialtype.bmc_guid,
            fieldname: _this.extdata.editingfieldname.trim()
          }
        })
        .then(x => {
          if (x.status == 200) {
            if (x.data.Ret > 0) {
              // 提示
              _this.$message.success("修改成功");
              // 关闭删除对话框
              _this.extdata.bIsShowingFieldEdit = false;
              // 从返回的数据中，设置fields
              _this.extdata.fields = x.data.Data.fields;
              // 清空已输入的内容
              _this.extdata.fieldnewname = "";
              // 设置焦点
              _this.$nextTick(x => {
                // document.getElementById("id_newfieldnameinput").focus();
                _this.$refs.id_newfieldnameinput.focus();
             });
            } else {
              _this.$message.error(x.data.Msg);
            }
          } else {
            console.error(x);
          }
          _LoadingIns.close();
        })
        .catch(x => {
          _LoadingIns.close();
          console.error(x);
        });
    },

    do_removecategoryfield() {
      // 调用接口，删除字段，成功后返回所有字段
      var _this = this;
      var _LoadingIns = _this.$loading({
        text: "处理中",
        target: document.getElementById("id_fieldmgr_list")
      });
      _this
        .$axios({
          url: `${this.$MgrBaseUrl.RemoveCategoryField}`,
          method: "post",
          data: {
            Token: _this.$staticmethod.Get("Token"),
            fieldid: _this.extdata.editingfieldid,
            bc_guid: _this.extdata.editingmaterialtype.bmc_guid
          }
        })
        .then(x => {
          if (x.status == 200) {
            if (x.data.Ret > 0) {
              // 提示
              _this.$message.success("操作成功");
              // 关闭删除对话框
              _this.extdata.bIsShowingFieldEdit = false;
              // 从返回的数据中，设置fields
              _this.extdata.fields = x.data.Data.fields;
              // 清空已输入的内容
              _this.extdata.fieldnewname = "";
              // 设置焦点
              _this.$nextTick(x => {
                // document.getElementById("id_newfieldnameinput").focus();
                _this.$refs.id_newfieldnameinput.focus();
              });
            } else {
              _this.$message.error(x.data.Msg);
            }
          } else {
            console.error(x);
          }
          _LoadingIns.close();
        })
        .catch(x => {
          _LoadingIns.close();
          console.error(x);
        });
    },

    // 删除当前正在编辑的字段
    removecategoryfield() {
      var _this = this;
      // 提示用户，是否确认删除该字段
      _this
        .$confirm("确认删除该字段", "操作确认", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
        .then(x => {
          _this.do_removecategoryfield();
        })
        .catch(x => {
          // 不做任何操作
        });
    },

    closefieldediting() {
      var _this = this;
      _this.extdata.bIsShowingFieldEdit = false;
    },

    showfieldnameedit(field) {
      var _this = this;
      _this.extdata.editingfieldname = field.fieldname;
      _this.extdata.editingfieldid = field.fieldid;
      _this.extdata.bIsShowingFieldEdit = true;
    },

    /**
     * 显示魔性关联
     */
    showComsMaterialRelation() {
      var _this = this;
      if (!_this.m_currentbmc) {
        _this.$message.error('请先选中分类叶子节点');
        return;
      }

      var _bmc_guid = _this.m_currentbmc.bmc_guid;
      _this.func_testIfIsLeaf(_bmc_guid, ()=>{
        _this.closeall();

        // 提供内部页面“选中的分类”
        _this.selectCateObj = _this.m_currentbmc;

        // 显示手动关联界面
        _this.isComsMaterialRelationShow = true;
        _this.bIfShowRelationDropItems = false;
      });
    },
    // 添加字段
    btn_addfield_click() {
      var _this = this;
      var length = _this.extdata.fields.length;

      // 字段名称为空判断
      if (_this.extdata.fieldnewname.trim() == '') {
        _this.$message.error('字段名称不能为空');
        return;
      }

      var _LoadingIns = _this.$loading({
        text: "加载中",
        target: document.getElementById("id_fieldmgr_list")
      });

      // 调用接口，添加字段
      _this
        .$axios({
          url: `${this.$MgrBaseUrl.EditCategoryField}`,
          method: "post",
          data: {
            Token: _this.$staticmethod.Get("Token"),
            fieldid: "",
            bc_guid: _this.extdata.editingmaterialtype.bmc_guid,
            fieldname: _this.extdata.fieldnewname.trim()
          }
        })
        .then(x => {
          if (x.status == 200) {
            if (x.data.Ret > 0) {
              // 从返回的数据中，设置fields
              _this.extdata.fields = x.data.Data.fields;

              // 清空已输入的内容
              _this.extdata.fieldnewname = "";

              // 设置焦点
              _this.$nextTick(x => {
                _this.$refs.id_newfieldnameinput.focus();
              });
            } else {
              _this.$message.error(x.data.Msg);
              _this.$nextTick(x => {
                _this.$refs.id_newfieldnameinput.focus();
              });
              // 设置焦点
            }
          } else {
            console.error(x);
          }
          _LoadingIns.close();
        })
        .catch(x => {
          _LoadingIns.close();
          console.error(x);
        });
    },

    _stopPropagation(ev) {
      if (ev) {
        ev.stopPropagation();
      }
    },
    //“字段管理菜单项hover结束时的触发”
    me_popupfieldmgrMouseLeave(ev) {
      this.extdata.bIsShowingFieldMgr = false
      this.$nextTick(x => {
        // document.getElementById("id_newfieldnameinput").focus();
        this.$refs.id_newfieldnameinput.blur();
      });
    },
    // “字段管理菜单项hover时的触发”
    me_popupfieldmgr(ev) {
      // 得到所mouseenter的元素
      ev.stopPropagation
      var _this = this;
      var bcrect = ev.target.getBoundingClientRect();

      // 得到元素本身的高度
      // var ele_fieldmgr_list = document.getElementById("id_fieldmgr_list");
      // debugger;
      var height = 345;

      // 将元素位置的坐标赋予extdata
      _this.extdata.field_x = bcrect.x + 216 + 4;

      // 判断如果纵坐标值 + 本身的高度 > 页面高度，则纵坐标值需要修正
      _this.extdata.field_y = 0;
      if (bcrect.y + height > document.body.clientHeight) {
        _this.extdata.field_y = document.body.clientHeight - height;
      } else {
        // console.log('535');
        _this.extdata.field_y = bcrect.y;
      }

      // 显示字段管理popup。
      _this.extdata.bIsShowingFieldMgr = true;
      _this.extdata.fieldnewname = ""; // 清空原来输入的新字段名称

      // id_newfieldnameinput 设置焦点
      _this.$nextTick(x => {
        _this.$refs.id_newfieldnameinput.focus();
      });

      // 加载当前分类的字段有哪些
      // var _LoadingIns = _this.$loading({
      //   text: "加载中",
      //   target: document.getElementById("id_fieldmgr_list")
      // });

      // 准备要请求的参数
      var _toquerybcguid = _this.extdata.editingmaterialtype.bmc_guid;

      // 发起请求
      _this.$axios
        .get(
          `${this.$MgrBaseUrl.GetMaterialCategoryExtJson}?bmc_guid=${_toquerybcguid}&Token=${_this.$staticmethod.Get('Token')}`
        )
        .then(x => {
          if (x.status == 200) {
            if (x.data.Ret > 0) {
              if (x.data.Data.fields) {
                _this.extdata.fields = x.data.Data.fields;
              } else {
                _this.extdata.fields = [];
              }
            } else {
              // _this.$message.error(x.data.Msg);
            }
          } else {
            console.error(x);
          }
        })
        .catch(x => {
          console.error(x);
        });
    },

    getflstyle() {
      var _this = this;
      var _s = {};
      _s["left"] = _this.extdata.field_x + "px";
      _s["top"] = _this.extdata.field_y + "px";
      return _s;
    },

    convertToDt(items) {
      var _this = this;
      var _arr = [];
      for (var i = 0; i < items.length; i++) {
        var tobj = {};
        tobj.materialid = items[i].bm_guid;
        tobj.materialname = items[i].bm_materialname;
        tobj.materialcode = items[i].bm_materialcode;
        tobj.statusid = items[i].bc_guid_materialstatus;
        tobj.bhaselerel = items[i].bhaselerel;
        tobj.bm_updatetime = items[i].bm_updatetime;
        tobj.relelejson = items[i].relelejson.split(',');
        tobj.bhas_relexam = items[i].bhas_relexam;
        tobj.bhas_relflow = items[i].bhas_relflow;

        _arr.push(tobj);
      }
      return _arr;
    },
    refreshNodeBy(){
        let node = this.$refs.ref_bmc.getNode(this.clickMaterialTypeID); // 通过节点id找到对应树节点对象
        // this.treem_default_expanded_keys = []
        node.parent.loaded = false;
        node.parent.expand();
        node.loaded = false;
        node.expand(); // 主动调用展开节点方法，重新查询该节点下的所有子节点

        // this.func_gettypelistAll();  // 整体刷新树结构
        // this.treefunc_loadChild(this.treenode,this.treeresolve)
    },
    // _onadded
    _onadded(goonadd) {
      var _this = this;
      // console.log(goonadd);

      // 弹出添加成功的提示
      _this.$message.success("添加成功");

      // goonadd
      if (!goonadd) {
        _this.extdata.bIsShowingNewMaterial = false;
      }

      // _this.refresh();

      // 刷新分类
      // var oldnode = _this.$refs.ref_bmc.getCurrentNode();

      // _this.refreshNodeBy();

      this.func_gettypelistAll('refresh')
      // _this.handleFilters(_this.clickMaterialTypeID);
    },

    // 进行删除操作
    do_deletematerialtype() {
      var _this = this;
      _this
        .$axios({
          method: "post",
          url: `${this.$MgrBaseUrl.RemoveCategory}`,
          data: {
            Token: _this.$staticmethod.Get("Token"),
            bmc_guid: _this.extdata.editingmaterialtype.bmc_guid
          }
        })
        .then(x => {
          if (x.data.Ret > 0) {
            _this.$message.success(x.data.Msg);
            _this.closeall();
            _this.refreshNodeBy();
          } else {
            _this.$message.error(x.data.Msg);
          }
        })
        .catch(x => {
          console.error(x);
        });
    },

    // 点击重命名
    renamematerialtype() {
      var _this = this;

      // 隐藏这个弹出菜单
      _this.closeall();

      //1. 设置弹出窗口的标题
      //2. 重置弹出窗口中的输入内容
      //3. 重置“当前选中项”
      //4. 显示出来
      _this.m_bmcedittitle = '重命名';
      _this.m_bmceditingname = _this.extdata.editingmaterialtype.bmc_name;
      _this.m_currentbmc = _this.extdata.editingmaterialtype;
      _this.addOrEdit = 2
      _this.status_bmcadd = true;
    },

    // 删除构件分类
    deletematerialtype() {
      // 下面的数据不能删除
      var _this = this;
      if (
        _this.extdata.editingmaterialtype &&
        _this.extdata.editingmaterialtype.MaterialCnt > 0
      ) {
        return;
      }

      if(!this.hasDelAuth){
        this.$message.error('当前用户暂无删除权限')
        return
      }
      // 确认删除提示
      _this
        .$confirm(`删除后将影响与工程单元关联的业务数据，是否确定删除该分类`, "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
        .then(x => {
          // 进行删除操作
          _this.do_deletematerialtype();
          _this.coseall()

        })
        .catch(x => {});
    },

    trymodifyname() {
      // 调用接口，接口中判断是否已重复，并新增
      var _this = this;
      _this
        .$axios({
          method: "post",
          url: `${window.bim_config.webserverurl}/api/Material/Mtr/RenameCategory`,
          data: _this.$qs.stringify({
            Token: _this.$staticmethod.Get("Token"),
            materialtypeid: _this.extdata.renamingtypeid,
            bc_categoryname: _this.extdata.renamingtypename,
            bc_categorytype: "materialtype",
            bc_organizeId: _this.$staticmethod._Get("organizeId")
          })
        })
        .then(x => {
          if (x.status != 200) {
            console.error(x);
          } else if (x.data.Ret > 0) {
            if (x.data.Data && x.data.Data.Exists) {
              _this.$message.error("已存在相同的名称！");
            } else {
              _this.extdata.renamingtypeid = "";
              _this.$message.success("修改成功！");
              _this.loadtypes();
            }
          } else {
            _this.$message.error(x.data.Msg);
          }
        })
        .catch(x => {
          console.error(x);
        });
    },

    // 输入新构件类别名称后按回车后的处理
    tryaddnewtype() {
      // 调用接口，接口中判断是否已重复，并新增
      var _this = this;

      _this.extdata.newtypename = _this.extdata.newtypename.trim();

      _this
        .$axios({
          method: "post",
          url: `${window.bim_config.webserverurl}/api/Material/Mtr/TryAddNewType`,
          data: _this.$qs.stringify({
            Token: _this.$staticmethod.Get("Token"),
            bc_categoryname: _this.extdata.newtypename,
            bc_categorytype: "materialtype",
            bc_organizeId: _this.$staticmethod._Get("organizeId")
          })
        })
        .then(x => {
          if (x.status != 200) {
            console.error(x);
          } else if (x.data.Ret > 0) {
            if (x.data.Data && x.data.Data.Exists) {
              _this.$message.error("已存在相同的名称！");
            } else {
              _this.extdata.bShowNewType = false;
              _this.$message.success("添加成功！");
              _this.loadtypes();
            }
          } else {
            _this.$message.error(x.data.Msg);
          }
        })
        .catch(x => {
          console.error(x);
        });
    },

    // 新构件类别名称输入框的 keyup 事件
    newtypenamekeyup(ev) {
      var _this = this;
      var KEYCODE_ESC = 27;
      var KEYCODE_ENTER = 13;

      if (ev.keyCode == KEYCODE_ESC) {
        // 取消新构件类别输入
        _this.newtypenameblur();
      } else if (ev.keyCode == KEYCODE_ENTER) {
        // 保存新构件类别输入
        // 内部需要验证是否重复。
        //console.log(_this.extdata.newtypename);
        _this.tryaddnewtype();
      }
    },
    // 点击确定新增分类
    clickAddListSure() {
      var _this = this;
      if(_this.extdata.newtypename){
        _this.tryaddnewtype();
      }else{
        this.$message({
          message: '分类名称不能为空',
          type: 'warning'
        });
      }
    },
    // 刷新数据
    refresh() {
      var _this = this;
      if (!_this.extdata.selectedtypeid) {
      } else {
        _this.loadmaterialitems2(_this.extdata.selectedtypeid);
      }
//       let node = this.$refs.ref_bmc.getNode(this.clickMaterialTypeID); // 通过节点id找到对应树节点对象
//       this.treem_default_expanded_keys = []
//       node.parent.loaded = false;
//       node.parent.expand();
//       node.loaded = false;
//       node.expand(); // 主动调用展开节点方法，重新查询该节点下的所有子节点
    },

    // 加载某分类下的构件数据
    loadmaterialitems(typeid, sortmethodstr, bhaselerel, statusids, keyWorld) {
      // console.log(1111111111);
      var _this = this;
      var _url = `${this.$MgrBaseUrl.GetMaterialList}?TypeId=${typeid}&token=${this.$staticmethod.Get("Token")}`;

      // 可选的排序参数
      if (sortmethodstr) {
        _url += `&Sortmethod=${sortmethodstr}`;
      }

      // 可选的bhaselerel
      if (bhaselerel == "0" || bhaselerel == "1") {
        _url += `&Bhaselerel=${bhaselerel}`;
      }

      // 可选的 statusids
      if (statusids) {
        _url += `&Statusids=${statusids}`;
      }

      // keyWorld
      if (keyWorld) {
        _url += `&KeyWorld=${keyWorld}`;
      }
      _this.$axios
        .get(_url)
        .then(x => {
          if (x.status == 200) {
            if (x.data.Ret > 0) {
              //debugger;
              _this.selectTableDataList = _this.convertToDt(x.data.Data.List);
              _this.extdata.tableData = _this.convertToDt(x.data.Data.List);
              let allupdatetime = x.data.Data.allupdatetime;
              for (let i in allupdatetime) {
                allupdatetime[i] = this.$options.filters["flttimeshorter"](
                  allupdatetime[i]
                );
              }

              // 设置 relationDefaultS1electedMId
              if (_this.extdata.tableData.length > 0) {
                _this.relationSelectedMId =
                  _this.extdata.tableData[0].materialid;
              }

              // 刷新个数
              var thecateindex = _this.extdata.materialtypes.findIndex(
                x => x.materialtypeid == typeid
              );
              if (typeid && thecateindex >= 0) {
                _this.extdata.materialtypes[thecateindex].MaterialCnt =
                  _this.extdata.tableData.length;
              }
            } else {
              _this.$message.error(x.data.Msg);
            }
          } else {
            console.error(x);
          }
        })
        .catch(x => {
          console.error(x);
        });
    },

    // 清空选中
    treefunc_clearcurrent() {
        var _this = this;
        try {
            _this.m_currentbmc = undefined;
            _this.$refs.ref_bmc.setCurrentNode({bmc_guid:'-1'});
        } catch (e){

        }
    },

    // 加载节点：“单元管理”
    func_gettypelistAll(reload) {
      // 请求接口，加载第一级的分类
      var _this = this;
      // 调用接口，获取分类数据
      // console.log('刷新111');
      var _this = this;
      var _organizeId = _this.$staticmethod._Get("organizeId");
      var _url = `${this.$MgrBaseUrl.GetCategories}?organizeId=${_organizeId}&baseCode=&Token=${_this.$staticmethod.Get('Token')}`;
      _this.$axios.get(_url).then(x => {

        // 处理 isLeaf、children、classname 等属性
        var retdata = _this.$staticmethod.DeepCopy(x.data.Data.list);

        // 标记是否有第一级分类
        var hasfirstwftc = retdata.length > 0;

        // 预设“单元管理”树节点
        var _all = [
            {
                classname: "icon-new-tree-last",

                // 这里应设为多少？
                isLeaf: !hasfirstwftc,

                // 全部的 code 为空字符串
                // -1000 表示空节点，置空时使用，这里设为-1000
                // 其它值
                bmc_code: '',
                bmc_guid: '-1000',
                bmc_name: '单元管理',
                ChildrenItemCount: x.data.Data.totalcount
            }
        ];
        this.treem_default_expanded_keys = ['-1000']
        // 这以前也不知道是干啥的，注释掉
        // if(x.data.Data.list.length > 0){
        //   this.treem_default_checked_keys =  [x.data.Data.list[0].bmc_guid]
        // }
        // this.treefunc_current_change(_all)
        this.handleFilters();
        _this.m_bmclist = _all;
        this.fromQuality = true;
        setTimeout(()=>{
          this.$refs.ref_bmc.setCurrentNode({bmc_guid:this.extdata.selectedtypeid});
        },1000)
      }).catch(x => {
      console.error(x);
      });
    },

    // 加载所有构件分类-新方法
    func_gettypelist(node) {
        // 调用接口，获取分类数据
        var _this = this;
        var _organizeId = _this.$staticmethod._Get("organizeId");
        var _url = `${this.$MgrBaseUrl.GetCategories}?organizeId=${_organizeId}&baseCode=&Token=${_this.$staticmethod.Get('Token')}`;
        _this.$axios.get(_url).then(x => {

          // 处理 isLeaf、children、classname 等属性
          var retdata = _this.$staticmethod.DeepCopy(x.data.Data.list);

          // 图标属性补充
          for (var i = 0; i < retdata.length; i++) {
            retdata[i].classname = 'icon-new-tree-last';
            retdata[i].isLeaf = retdata[i].DirectChildrenCount == 0;
          }
          _this.m_bmclist = retdata;

          // 设置自动选中
          if (node && _this.$refs.ref_bmc) {
            setTimeout(()=> {
              _this.$refs.ref_bmc.setCurrentNode(node);
            }, 1000);
          }

        }).catch(x => {
          console.error(x);
        });
    },

    // 加载所有构件分类
    loadtypes() {
      // 先加载一个叫 “单元管理” 的节点
      var _this = this;
      _this.func_gettypelistAll();
      return;
    },

    // 构件详细信息-取消
    materialinfo_cancel() {
      var _this = this;
      _this.extdata.isShowInfo = false;
      // _this.loadmaterialstatus()
    },

    // 刷新当前
    func_refresh() {
      var _this = this;
      console.log('mgr 页面接收到了 onrefresh');
      _this.loadmaterialitems2(_this.extdata.selectedtypeid, true);
    },

    // 新建构件-取消
    newmaterial_cancel() {
      var _this = this;
      _this.extdata.bIsShowingNewMaterial = false;
    },

    // 新建构件-显示
    newmaterial_open() {
      var _this = this;
      _this.closeall();
      if (!_this.extdata.selectedtypeid) {
        _this.$message.error("请先选择分类");
        return;
      }

      // 判断是否为叶子节点
      if (!_this.m_currentbmc) {
        _this.$message.error('请先选中分类叶子节点');
        return;
      }
      var _bmc_guid = _this.m_currentbmc.bmc_guid;
      _this.func_testIfIsLeaf(_bmc_guid, ()=>{
        _this.extdata.bIsShowingNewMaterial = true;
      });
    },

    renametypenamekeyup(ev) {
      var _this = this;
      var KEYCODE_ESC = 27;
      var KEYCODE_ENTER = 13;
      if (ev.keyCode == KEYCODE_ESC) {
        _this.renametypenameblur();
      } else if (ev.keyCode == KEYCODE_ENTER) {
        _this.trymodifyname();
      }
    },

    renametypenameblur() {
      var _this = this;
      _this.extdata.renamingtypeid = "";
    },

    newtypenameblur() {
      var _this = this;
      _this.extdata.bShowNewType = false;
    },

    // 显示新增分类窗口
    func_showbmcediting(ev) {
      if(!this.hasEditAuth) {
        this.$message.warning("没有编辑权限")
        return
      }
      // 弹出窗口
      var _this = this;
      _this.closeall();
      ev && ev.stopPropagation && ev.stopPropagation();

      //1. 设置弹出窗口的标题
      //2. 重置弹出窗口中的输入内容
      //3. 重置“当前选中项”
      //4. 显示出来
      _this.m_bmcedittitle = '新增分类';
      _this.m_bmceditingname = '';
      _this.m_currentbmc = undefined;
      _this.status_bmcadd = true;
      _this.addOrEdit = 1

    },

    addmaterialtype() {
      var _this = this;
      _this.closeall();
      if (_this.extdata.bShowNewType == true) {
        _this.extdata.bShowNewType = false;
      } else {
        _this.extdata.newtypename = "";
        _this.extdata.bShowNewType = true;
        _this.$nextTick(x => {
          document.getElementById("id_newtypename").focus();
        });
      }
    },

    getMaterialTypeName(statusid) {
      var _this = this;
      var index = _this.extdata.allmaterialstatus.findIndex(
        x => x.statusid == statusid
      );
      var name = "";
      if (index >= 0) {
        name = _this.extdata.allmaterialstatus[index].statusname;
      } else {
        name = '无';
      }
      return name;
    },

    getMaterialTypeStyle(statusid) {
      var _this = this;
      var _s = {};
      var index = _this.extdata.allmaterialstatus.findIndex(
        x => x.statusid == statusid
      );
      if (index >= 0) {
        var materialstatus = _this.extdata.allmaterialstatus[index];
        _s["overflow"] = 'hidden';
        _s["textOverflow"] = 'ellipsis';
        _s["whiteSpace"] = 'nowrap';
        _s["color"] = materialstatus.statusmaincolor;
        _s["background-color"] = materialstatus.statusmaincolor.replace(
          "1)",
          "0.1)"
        );
        _s["border"] = `1px solid ${materialstatus.statusmaincolor.replace(
          "1)",
          "1)"
        )}`;
      } else {
        _s["color"] = 'rgba(0, 0, 0, 0.45)';
        _s["border"] = '1px solid rgba(0, 0, 0, 0.15)';
        _s["background-color"] = '#fff';
        _s["overflow"] = 'hidden';
        _s["textOverflow"] = 'ellipsis';
        _s["whiteSpace"] = 'nowrap';
      }
      return _s;
    },

    getstatusstyle(colorstr, ischecking) {
      var _this = this;
      var _s = {};
      _s["background-color"] = colorstr.replace("1)", "0.1)");
      _s["color"] = colorstr;
      _s["border"] = `1px solid ${colorstr.replace("1)", "0.1)")}`;
      _s["overflow"] = 'hidden';
      _s["textOverflow"] = 'ellipsis';
      _s["whiteSpace"] = 'nowrap';
      if (ischecking) {
        _s["background-color"] = colorstr;
        _s["color"] = "#fff";
        _s["border"] = `1px solid ${colorstr}`;
      }
      return _s;
    },

    exchangecheckhead() {
      var _this = this;
      if(_this.R_bmc_name == "单元管理"){
        _this.R_import = false
      }else{
        _this.R_import = true
      }
      if (_this.extdata.tableData.length > _this.extdata.selectedData.length) {
        _this.extdata.selectedData = _this.$staticmethod.DeepCopy(
          _this.extdata.tableData
        );
      } else {
        _this.extdata.selectedData = [];
      }
    },

    on_row_dblclick(row, el, mousee) {
      var _this = this;
      if (
        mousee.path.length > 0 &&
        mousee.path[0].className &&
        mousee.path[0].className.indexOf("css-cb") >= 0
      ) {
      } else {
        _this.showdetail(row.materialid);
      }
    },

    materialname_click(bmid) {
      this.showdetail(bmid);
    },

    showdetail(id) {
      var _this = this;
      _this.closeall();
      _this.extdata.selectedbmid = id;
      _this.extdata.isShowInfo = true;
    },

    row_cb_click(row, ev) {
      // console.log(row,ev)
      var _this = this;
      ev.stopPropagation();
      if(_this.R_bmc_name == "单元管理"){
        _this.R_import = false
      }else{
        _this.R_import = true
      }

      // 判断 row.materialid 是否已存在于 selectedData 中
      var index = _this.extdata.selectedData.findIndex(
        x => x.materialid == row.materialid
      );
      if (index >= 0) {
        _this.extdata.selectedData = _this.extdata.selectedData.filter(
          x => x.materialid != row.materialid
        );
      } else {
        _this.extdata.selectedData.push(row);
      }
    },

    //
    selectedContainsMaterialId(materialid) {
      var _this = this;
      var index = _this.extdata.selectedData.findIndex(
        x => x.materialid == materialid
      );
      return index >= 0;
    },

    // 显示或隐藏某一种构件类型的菜单
    showtypeitemmenu(ev, materialtype) {
      var _this = this;

      // 缓存现在的状态，是显示着还是隐藏着
      var cachedisplay = _this.extdata.typemenu_css.display;

      // 将所有菜单隐藏
      _this.closeall();

      // 根据ev得到位置
      var bcrect = ev.target.getBoundingClientRect();
      _this.extdata.typemenu_css.left = bcrect.x - 40;
      _this.extdata.typemenu_css.top = bcrect.y + 32;

      // 显示或隐藏构件类型的上下文菜单
      if (cachedisplay) {
        _this.extdata.editingmaterialtype = null;
        _this.extdata.typemenu_css.display = false;
      } else {
        _this.extdata.editingmaterialtype = materialtype;
        _this.extdata.typemenu_css.display = true;
      }
    },

    // 选中某一种构件类别ID
    selecttype(typeid, typename,obj) {
      var _this = this;
      // debugger;
      this.selectCateObj = obj
      _this.clickMaterialTypeName = typename;
      _this.closeall();
      _this.extdata.selectedtypeid = typeid;
      _this.clickMaterialTypeID = typeid;
      _this.refresh();

      // 清空选中的数据
      _this.extdata.selectedData = [];

    },
    getAddListstyle() {
      var _this = this;
      var _s = {};
      _s["left"] = _this.extdata.addList_css.left + 0 + "px";
      _s["top"] = _this.extdata.addList_css.top + 0 + "px";
      return _s;
    },
    getsortoptionsstyle() {
      var _this = this;
      var _s = {};
      _s["left"] = _this.extdata.sort_css.left + 0 + "px";
      _s["top"] = _this.extdata.sort_css.top + 0 + "px";
      return _s;
    },

    gettypemenustyle() {
      var _this = this;
      var _s = {};
      _s["left"] = _this.extdata.typemenu_css.left + 0 + "px";
      _s["top"] =
        _this.getContainsTop(280, _this.extdata.typemenu_css.top + 0) + "px";
      return _s;
    },

    showmodifymenus() {
      var _this = this;
      _this.extdata.materialitemmenu_popup.isshowing = true;
      _this.extdata.changeStatusSureId = _this.extdata.statusSureIding
    },

    // 基于 document.body.clientHeight 得到不会超出屏幕的 top 值
    getContainsTop(eleheight, oldtop) {
      if (oldtop > document.body.clientHeight - eleheight) {
        return document.body.clientHeight - eleheight;
      } else {
        return oldtop;
      }
    },
    // //基于 document.body.clientHeight 得到不会超出屏幕的 top 值

    getmaterialstatuspopup() {
      var _this = this;
      var _s = {};
      var eleheight = 348;
      if (
        _this.extdata.materialitemmenu.top >
        document.body.clientHeight - eleheight
      ) {
        _s["top"] = document.body.clientHeight - eleheight + "px";
      } else {
        _s["top"] = _this.extdata.materialitemmenu.top + "px";
      }
      _s["left"] = _this.extdata.materialitemmenu.left + 0 + "px";
      return _s;
    },
    showmaterialmenu(ev, material) {
      var _this = this;
      _this.clickTableDetailsID = material.materialid
      // 缓存 materialitemmenu.isshowing
      _this.extdata.selectedmaterial = material;
      // console.log(material); // statusid
      var materialitemmenu_isshowing = _this.extdata.materialitemmenu.isshowing;

      // 先隐藏所有
      _this.closeall();

      var bcrect = ev.target.getBoundingClientRect();
      _this.extdata.materialitemmenu.left = bcrect.x - 60;
      _this.extdata.materialitemmenu.top = bcrect.y + 32;

      // 确保在屏幕内
      var eleheight = 146;
      if (
        _this.extdata.materialitemmenu.top >
        document.body.clientHeight - eleheight
      ) {
        _this.extdata.materialitemmenu.top =
          document.body.clientHeight - eleheight;
      }
      // //确保在屏幕内

      _this.extdata.materialitemmenu_popup.isshowing = false;
      if (materialitemmenu_isshowing) {
        _this.extdata.materialitemmenu.isshowing = false;
      } else {
        _this.extdata.materialitemmenu.isshowing = true;
      }
    },
    getShowButtomstyle() {
      var _this = this;
      var _m = {};
      if(_this.extdata.showMoreButtomStyle.top > document.body.clientHeight - 200){
        _this.extdata.showMoreButtomStyle.top = document.body.clientHeight - 200
      }
      _m["top"] = _this.extdata.showMoreButtomStyle.top + "px";
      _m["left"] = _this.extdata.showMoreButtomStyle.left + "px";
      return _m;
    },
    getmaterialmenustyle() {
      var _this = this;
      var _s = {};
      if(_this.extdata.materialitemmenu.top > document.body.clientHeight - 200){
        _this.extdata.materialitemmenu.top = document.body.clientHeight - 200
      }
      _s["top"] = _this.extdata.materialitemmenu.top + "px";
      _s["left"] = _this.extdata.materialitemmenu.left + "px";
      return _s;
    },
    closeinnerdrops() {
      var _this = this;
      _this.extdata.isshow_filter_mat_status = false;
    },

    closeall(recur) {
      var _this = this;
      // filter
      if (_this.extdata.filter_css.display) {
        _this.extdata.filter_css.display = false;
        if (recur) {
          _this.extdata.isshow_filter_mat_status = false;
        }
      }

      // sort
      if (_this.extdata.sort_css.display) {
        _this.extdata.sort_css.display = false;
      }
      _this.extdata.materialitemmenu.isshowing = false;
      _this.extdata.materialitemmenu_popup.isshowing = false;
      _this.extdata.typemenu_css.display = false;
      _this.extdata.bIsShowingFieldMgr = false;
      _this.extdata.bIsShowingMulStatusOptions = false;
      _this.selectAsc.dialogShow = false;
      _this.extdata.bShowNewType = false;
      _this.bIfShowRelationDropItems = false;
      _this.changeShowButtomMore = false
      // 取消已勾选的数据
      // _this.extdata.selectedData = [];

      // 右键菜单
      _this.m_contextpos.isshowing = false;
      _this.m_contextpos2.isshowing = false;
      // _this.showImportFromApiSelect = false;
    },
    // 表格上方显示或隐藏过滤小弹层
    showorhidefilter(ev) {
      var _this = this;
      // 缓存 filter_css.display
      var olddisplay = _this.extdata.filter_css.display;
      // 先隐藏所有
      _this.closeall();

      var bcrect = ev.target.getBoundingClientRect();
      _this.extdata.filter_css.left = bcrect.x - 328 || 0;
      _this.extdata.filter_css.top = bcrect.y - 10;

      if (olddisplay) {
        _this.extdata.filter_css.display = false;
      } else {
        _this.extdata.filter_css.display = true;
      }
    },
    // 表格上方显示或隐藏排序小弹层
    showorhidesort(ev) {
      var _this = this;
      var olddisplay = _this.extdata.sort_css.display;

      // 先隐藏所有
      _this.closeall();

      var bcrect = ev.target.getBoundingClientRect();
      _this.extdata.sort_css.left = bcrect.x - 70 || 0;
      _this.extdata.sort_css.top = bcrect.y + 32;

      if (olddisplay) {
        _this.extdata.sort_css.display = false;
      } else {
        _this.extdata.sort_css.display = true;
      }
    },
    getfilterdropstyle() {
      var _this = this;
      var _s = {};
      _s["left"] = _this.extdata.filter_css.left + "px";
      _s["top"] = _this.extdata.filter_css.top + "px";
      _s["display"] = _this.extdata.filter_css.display ? "block" : "none";
      return _s;
    },
    tableRowClassName({ row, rowIndex }) {
      var _this = this;
      return "css-tdunder-test1";
    },
    //控制右侧table样式
    getStyleBottomtable () {
      let _s = {};
      _s["flex"] = this.extdata.modeldetailstyle.flex
      _s["width"] = this.extdata.modeldetailstyle.width;
      _s["paddingLeft"] = this.extdata.modeldetailstyle.paddingLeft;
      return _s;
    },
    getwebserverurl() {
      return window.bim_config.bimviewerurl;
    },
    // 鼠标滑过，获取当前绑定的模型
    getAssociationModelDetail(tableData,event){
      let _this = this;
      this.getModelListForTableData = tableData
      let relelejson = tableData.relelejson
      if(tableData.bhaselerel !== 0 ){

        this.getModelListForTable = []
        _this.m_modellist.top = event.y - 12;
        _this.m_modellist.left = event.x + 14;
        for(let i=0;i<relelejson.length;i++){
          _this.$axios
            .get(`${this.$ip('newModelHttpUrl')}/Vault/GetFeature?VaultID=${_this.VaultID}&FeatureID=${relelejson[i]}`)
            .then(res => {
              if (res.status === 200) {
                this.getModelListForTable.push({
                  featureName: res.data.featureName,
                  featureID: relelejson[i]
                })
              }
            })
            .catch(err => {
            })
        }
        _this.modelListTableShow = true;
      }

    },
    style_getmodelList(){
        var _this = this;
        var _s = {};
        _s["left"] = _this.m_modellist.left + 'px';
        _s["top"] = _this.m_modellist.top + 'px';
        return _s;
    },
    //打开模型（关联的构件）
    openAssociationModelComponents(tableData,relelejson) {
      // debugger
      this.openModelID = relelejson;
      this.modelListTableShow = false
      this.tree_table_id = []
      this.tree_table_id.push(tableData.materialid)
      // debugger
      this.extdata.modeldetailstyle.flex = 'initial'
      this.extdata.modeldetailstyle.width = 500 + 'px'
      this.extdata.modeldetailstyle.paddingLeft = 24 + 'px'
      // this.associationModelChild = false
      this.tableClose = false
      this.leftRightHide = false
      let selectModelID = ''

      // 如果有关联构件bhaselerel==1，请求GetMaterialDetail获取关联的构件
      if (tableData.bhaselerel) {
        this.$axios
          .get(
            `${this.$MgrBaseUrl.GetMaterialDetail}?bm_guid=${tableData.materialid}&token=${this.$staticmethod.Get("Token")}`,
          )
          .then(res => {
            if (res.data.Ret == 1) {
              this.getNewModelInfo(relelejson);
              selectModelID = relelejson // res.data.Data.mris[0].modelid
              // 判断本次modelid是否与上次相同
              // debugger
              if(this.cacheModelID == selectModelID){
                // 新模型处理=先取消当前高亮的构件==开始
                let sel_elements = window.scene.selectedObjects;
                let sel_elementIds = ''
                if (sel_elements.size > 0) {
                    sel_elementIds = Array.from(sel_elements.keys())
                }
                for(let i = 0; i < sel_elementIds.length; i++ ){
                  let element_false = window.scene.findObject(sel_elementIds[i])
                  element_false.selected = false
                  window.scene.render();
                }
                // 新模型处理=先取消当前高亮的构件==结束
                let openmodelidIndex = res.data.Data.mris.findIndex(item=>{
                      return item.modelid == relelejson
                  })
                // 新模型处理=高亮的构件
                this.materialRelationModel = res.data.Data.mris[openmodelidIndex].elementids.map(
                  key => {
                    return `${res.data.Data.mris[openmodelidIndex].modelid}^${key}`;
                  }
                );

                // 新模型==聚焦高亮
                if(this.materialRelationModel.length > 0){
                  for(let _i = 0; _i < this.materialRelationModel.length; _i++ ){
                    let a = this.materialRelationModel[_i]//'09c78392-5b51-4a85-af4b-2cdada0d6fa2^573547'
                    let element = window.model3.objects.get(a)
                    element.selected = true
                  }
                  window.scene.render();
                  // this.newModelsetElementColor(this.materialRelationModel,30,255,0,0.5)
                }

              }else{
                this.openModelID = relelejson // res.data.Data.mris[0].modelid

                console.log('======4124不一样')
                this.getNewModelInfo(selectModelID);
                this.modelLoadingOK = false;
                clearInterval(timeer)
                // 这个地方是模型不一样、需要模型切换，
                //循环拼接模型id和构件所关联的构件id 格式为 “模型id^构件id”
                this.cacheModelID = relelejson // res.data.Data.mris[0].modelid
                this.associationModelChild = false;
                let openmodelidIndex = res.data.Data.mris.findIndex(item=>{
                      return item.modelid == relelejson
                  })// res.data.Data.mris[0] // 在res.data.Data.mris中查找是第几个
                this.materialRelationModel = res.data.Data.mris[openmodelidIndex].elementids.map(
                  key => {
                    return `${res.data.Data.mris[openmodelidIndex].modelid}^${key}`;
                  }
                );

                let timeer = null
                timeer = setInterval(() => {
                  if(this.modelLoadingOK){
                      /*
                      // 之前高亮的逻辑
                      for(let _i = 0; _i < this.materialRelationModel.length; _i++ ){
                        let a = this.materialRelationModel[_i]//'09c78392-5b51-4a85-af4b-2cdada0d6fa2^573547'
                        let element =  window.model3.objects.get(a)
                        element.selected = true
                      }
                      window.scene.render();
                      */
                    //  2023-12-20、谢鹏对当前逻辑更改==改为：给当前关联的构件着色
                    this.newModelsetElementColor(this.materialRelationModel,30,255,0,0.5)
                    clearInterval(timeer)
                  }
                }, 1000);

              }
              //  res.data.Data.mris[0].modelid cache
              // 缓存Modelid 作为下一次打开时的参考
            } else {
              this.$message({
                type: "error",
                message: "请求构件详情出错，请稍后再试。"
              });
              this.associationModelChild = false;
            }
          })
          .catch(res => {
            this.$message.warning("当前单元暂无关联构件");
            console.warn(`请求构件详情出错:${res}`);
            this.associationModelChild = false;
          });
      }else{
        this.$message.warning("当前单元暂无关联构件");
        this.associationModelChild = false;
      }
    },
    // 获取新模型详情
    getNewModelInfo(openModelID){
      // console.log(openModelID)
      if(openModelID == null || openModelID.length == 0) return
      let _this = this;
      _this.$axios
        .get(`${this.$ip('newModelHttpUrl')}/Vault/GetFeature?VaultID=${_this.VaultID}&FeatureID=${openModelID}`)
        .then(res => {
          if (res.status === 200) {
            _this.newModelTitle = res.data.featureName;
            _this.extdata.modelHoverName = res.data.featureName;
          }else{
            _this.newModelTitle = ""
            _this.extdata.modelHoverName = ''
          }
        })
        .catch(err => {
        })
    },
    // 获取所有构件
    newModelGetAllElement(){
      let allElements = window.scene.findFeature(this.openModelID).objects;
      let elementIds = Array.from(allElements.keys())
      window.scene.execute('color',{objectIDs:elementIds,color:'rgb(0,0,0)',opacity:0.3})
    },
    // 新模型=删除着色
    newModelresetElement(r_eles){
        for(let i = 0; i < r_eles.length; i++ ){
            let element =  window.model3.objects.get(r_eles[i])
            element.resetColor();
        }
        window.scene.render();
    },
    // 新模型=删除全部着色
    newModelresetAllElement(){
        window.scene.coloredObjects.forEach(obj=>{obj.reset()})
        window.scene.render();
    },
    // 新模型=着色
    newModelsetElementColor(eles,r,g,b,a){
        this.newModelresetElement(eles)
        window.scene.execute('color',{objectIDs:eles,color:`rgb(${r},${g},${b})`,opacity:`${a}`})
    },
    nottreefunc_checked(){
      this.newModelsetElementColor(this.resetCheckedTree,0,0,0,0.1)
    },
    treefunc_checked(checked_data){
      // console.log(checked_data,'这是选择后的高亮的构件',this.cacheModelID)
      if(checked_data.modelids){
        if(this.cacheModelID == checked_data.modelids[0]){
          let elementIds = checked_data.eleids
          for(let i = 0; i < elementIds.length; i++ ){
              let element =  window.model3.objects.get(elementIds[i])
              element.selected = false
          }
          window.scene.render();
          // 当前选中构件着色
          let allCheckRemoveColor = [...new Set(checked_data.eleids.concat(this.resetCheckedTree))]
          this.newModelsetElementColor(allCheckRemoveColor,0,0,0,0.1)

          this.resetCheckedTree = [];
          this.resetCheckedTree = checked_data.eleids;
          this.newModelsetElementColor(this.resetCheckedTree,30,255,0,0.5)
        }else{
          // console.log('======不一样的模型')
          this.$message.warning('当前分类下暂无关联构件或需切换模型、请点击构件名称切换模型')
        }
      }else{
        this.$message({
          type: "warning",
          message: "当前分类下暂无关联构件，请确保选择的为末级节点"
        });
      }
    },
    onmodelview_close() {
      var _this = this;
      _this.associationModelChild = true;
      _this.cacheModelID = '';
      this.extdata.modeldetailstyle.flex = 1
      this.extdata.modeldetailstyle.width = 'calc(100% - 347px)'
      this.extdata.modeldetailstyle.paddingLeft = 7 + 'px'
      this.tableClose = true
      this.leftRightHide = true
      // _this.showModelInfo = false;
      setTimeout(function() {
        _this.$staticmethod._Set("needpausegetmsg", 0);
      }, 1000);
    },
    formatDateTime(inputTime) {
      var date = new Date(inputTime);
      var y = date.getFullYear();
      var m = date.getMonth() + 1;
      m = m < 10 ? "0" + m : m;
      var d = date.getDate();
      d = d < 10 ? "0" + d : d;
      var h = date.getHours();
      h = h < 10 ? "0" + h : h;
      var minute = date.getMinutes();
      var second = date.getSeconds();
      minute = minute < 10 ? "0" + minute : minute;
      second = second < 10 ? "0" + second : second;
      return y + "-" + m + "-" + d + " " + h + ":" + minute + ":" + second;
    },
    hoverQrcodeEnter(srcid,ev){
      let _this = this;
      _this.getCodeSrc = _this.getQrCodeSrc(srcid);
      _this.hoverQR.field_x = ev.clientX +30;
      _this.hoverQR.field_y = ev.clientY;
      if(_this.hoverQR.field_y > document.body.clientHeight - 220){
        _this.hoverQR.field_y = document.body.clientHeight - 220
      }
      _this.hoverQrcode = true;
    },
    hoverQrcodeStyle(){
      let _this = this;
      let _h = {};
      _h["left"] = _this.hoverQR.field_x + "px";
      _h["top"] = _this.hoverQR.field_y + "px";
      return _h;
    },
    handleSizeChange(val) {
      this.pageSize = val
      this.handleFilters()
      console.log(`每页 ${val} 条`);
    },
    handleCurrentPaginationChange(val) {
      this.pageNum = val;
      this.handleFilters()
    },

    //从现场数据点击构件时  要跳转并定位到该构件
    setSomeTreeChecked(item,currentParentDatas) {
      let tm = setInterval(() => {
        if (this.fromQuality) {
          clearInterval(tm);
          let storages = [];
          //将目标构件的父级节点的bmc_guid取出，放到数组
          currentParentDatas.forEach((ele,index)=>{
            storages[index] = ele.bmc_guid;
          });
          //展开这些父节点
          this.treem_default_expanded_keys = storages;
          //展开根节点
          var node = this.$refs.ref_bmc.getNode({bmc_guid:'-1000'});
          if (node) {
              node.expand();
          }

          //父节点展开后
          setTimeout(()=>{
            try{
              //设置目标构件高亮选中
              this.$refs.ref_bmc.setCurrentNode({bmc_guid:item.bc_guid_materialtype});
              //获取被选中的构件的节点的node
              let _curNode = this.$refs.ref_bmc.getCurrentNode();
              //调用节点选中后的方法，展示右侧该构件节点的详情数据
              this.treefunc_current_change(_curNode);
            }catch(err) {}
          },1000)
        }
      }, 500);

    },
    destroyed() {
        window.onresize = null;
    },
  }
}
</script>
<style scoped>
@import url("../../../assets/css/MaterialsMgrStyle.css");

.pro-in-out{
  display: inline-block;
  width: 16px;
  height: 120px;
  background-image: url(../../../assets/images/p-in.png) ;
  background-size: 100%;
  background-repeat: no-repeat;
}
.pro-in-out.p-out{
  background-image: url(../../../assets/images/p-out.png) ;
  background-repeat: no-repeat;
  background-size: 100%;
  left: 0;
}
.width100{
  width: 100%;
  height: 100%;
}
.model-list-table-hover{
  position: fixed;
  min-width:200px;
  max-width:400px;
  box-shadow: 0 0 8px 0 rgba(0, 0, 0, .15);
  background: #fff;
  padding: 10px 0;
  border-radius: 2px;
}
.model-list-table-hover .list {
  line-height: 30px;
  padding: 0 15px;
}
.model-list-table-hover .list:hover{
  cursor: pointer;
  color: #1890ff;
  background: rgba(0,122,255,0.05);;
}

</style>
