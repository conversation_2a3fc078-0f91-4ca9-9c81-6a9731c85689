<template>
  <section class="_css-doc-all css-h100 document-summary-container" @click="hideallbtns()">
    <section class="_css-doc-top css-fc css-w100 css-bsb css-bst css-usn css-bc-white">
      <el-row class="_css-search-operators css-w100" type="flex">
        <el-col :span="8" style="display: flex;align-items: center;">
          <div>
            <span class="css-left-span">文档汇总</span>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="css-h100">
            <div class="_css-keyword-inputbtn css-fc css-usn">

              <CompsUsersInput
                @oninput="_ondocnameinput"
                :placeholder="'请输入文档名称'"
                :width="'100%'"
                :maxWidth="'400px'"
                iconclass="icon-interface-search"
              ></CompsUsersInput>
            </div>
          </div>
        </el-col>
        <el-col :span="8">
        </el-col>
      </el-row>
    </section>
    <section class="_css-doc-body css-w100 css-flex1 css-b css-p24"
             @contextmenu="blankareacontextmenu($event)"
    >
      <div class="css-h100 css-fc _css-doc-body-in">
        <div class="css-h100  css-flexn css-bsb css-usn _css-docleftfuncitems padding-10">
          <!-- 左边的菜单 -->
<!--            <div class="document-summary" @click="clickDocument">
              <img class="icon" src="../../../assets/images/DocumentSummary/document-summary-icon.png" alt="">
              <span>项目文档</span>
            </div>-->
            <div class="project-select-wrapper" @click="clickSelectProject">
              <img class="icon" src="../../../assets/images/DocumentSummary/document-project-icon.png" alt="">
              <el-tooltip class="tooltip" effect="dark" :content="this.projectName" placement="top-start">
                <span class="project-name">{{ this.projectName }}</span>
              </el-tooltip>
              <img class="project-select-icon" src="../../../assets/images/DocumentSummary/select-project-icon.png" alt="">
            </div>
            <el-tree
              :data="treedata.items"
              class="tree"
              :class="extdata._doc_showtree?'':'css-hide'"
              lazy
              :load="loadNodeChild"
              node-key="Id"
              :props="treedata.props1"
              :expand-on-click-node="false"
              @node-collapse="node_collapse"
              @node-expand="node_expand"
              @node-click="node_click"
              ref="tree"
              :defaultExpandedKeys="_openstacks_withprocessing"
            >
                <span class="css-fc" slot-scope="{ node, data }">
                  <i class="css-icon20 css-fs18 css-fc css-jcsa css-folder icon-interface-unfolder"></i>
                  <span :title="data.FolderName" class="css-ml4">{{ node.label }}</span>
                </span>
            </el-tree>
          <!-- //左边的菜单 -->
        </div>
        <div id="id_datamain"   class="css-h100  css-bsb css-prel summary-table" @click="func_clearselect_and_contextmenu"
             @contextmenu="blankareacontextmenu($event)"

        >
          <!-- 右边的表格 -->
          <div class="css-h100 css-prel css-z1">
            <!-- 共？个文件，选中的文件个数，操作按钮区域 -->
            <div class="_css-table-top css-p1024 css-usn">
              <div class="css-h100 css-fc">
                <!-- 面包屑 -->
                <div
                  class="_css-table-top-left css-h100 css-fc css-usn css-bsb css-miniscroll css-oxa css-flex1"
                >
                  <!-- 返回上一级及分隔符 -->
                  <div
                    v-if="extdata._openstacks.length > 0"
                    @click="open_parentfolder($event);"
                    class="css-breadcrumb-item css-cp"
                  >
                    <label class="css-cp _css-backpfolder">返回上一级</label>
                  </div>
                  <div v-if="extdata._openstacks.length > 0" class="css-breadcrumb-splitter">/</div>
                  <!-- //返回上一级及分隔符 -->
                  <!-- 显示“项目文档/搜索结果”及分隔符 -->
                  <div class="css-breadcrumb-item css-cp">
                    <label data-debug="line 181" @click="resetpath($event)" class="css-cp _css-basicfolder">
                      {{extdata._lastkeyword && extdata._lastkeyword != ''
                      ?('搜索结果【' + extdata._lastkeyword + '】')
                      :doclisttypename}}
                    </label>
                    <i
                      @click="clear_search($event)"
                      v-if="extdata._lastkeyword && extdata._lastkeyword != ''"
                      class="css-icon12 css-fs12 icon-suggested-close"
                    ></i>
                  </div>
                  <div v-if="extdata._openstacks.length > 0" class="css-breadcrumb-splitter">/</div>
                  <!-- //显示“项目文档/搜索结果”及分隔符 -->
                  <!-- 所有打开过的文件夹栈 -->
                  <template v-for="(item,index) in extdata._openstacks">
                    <div
                      class="css-breadcrumb-item css-cp"
                      :key="item.FileId"
                      @click="open_current_history(index, $event);"
                    >
                      <label class="css-cp"
                             :class="{'_css-parentsfolder':index != extdata._openstacks.length - 1
                        , '_css-currentfolder':index == extdata._openstacks.length - 1}"
                      >{{item.FileName}}</label>
                    </div>
                    <div
                      :key="'_' + item.FileId"
                      v-if="index != extdata._openstacks.length - 1"
                      class="css-breadcrumb-splitter"
                    >/</div>
                  </template>
                </div>
                <!-- //面包屑 -->
              </div>
            </div>
            <!-- //共？个文件，选中的文件个数，操作按钮区域 -->
            <!-- 表格体区域 -->
            <div
              @dragleave="mulfile_dragleave($event)"
              @drop="mulfile_drop($event)"
              @dragenter="mulfile_dragenter($event)"
              @dragover="mulfile_dragover($event)"
              class="_css-table-body css-p1024 css-bsb"
            >
              <!-- 列表显示 -->
              <div
                class="css-h100 css-w100 css-bsb css-usn"
                :class="extdata.showtype != 1?'css-none':''"
              >
                <!-- 高度为40的列表头 -->
                <div class="css-h40 css-w100 css-bsb css-fc css-bglgray">
                  <!-- 复选框 -->
                  <div class="css-h100 css-ml10 css-fc">
                    <div
                      @click="list_item_head_check($event)"
                      class="css-cb css-icon14 css-bsb css-cp"
                      :class="(extdata._selectedobjs.length == extdata.tableData.length) && extdata.tableData.length > 0?'mulcolor-interface-checkbox-selected':''"
                    ></div>
                  </div>
                </div>
                <!-- //高度为40的列表头 -->
                <!-- 高度为100-40的列表体 -->
                <div class="css-h-40 css-w100 css-bsb css-flexlist">
                  <!-- 单一个文件 -->
                  <div
                    :key="item.FileId"
                    v-for="(item) in extdata.tableData"
                    class="_css-doc-list-item css-bsb"
                    :title="item.FileId == '-1'?'':item.FileName"
                    @click="list_item_click(item.FileId, $event)"
                    @dblclick="list_item_dblclick(item, $event)"
                    @contextmenu="list_item_rclick(item, $event)"
                  >
                    <!-- 图标显示部分 -->
                    <div class="_css-doc-list-item-icon css-bsb css-fc css-jcsa css-prel">
                      <!-- 复选框 -->
                      <div
                        class="css-pabs css-cb css-icon12 css-t4 css-l4"
                        :class="selectedContains(item.FileId)?'mulcolor-interface-checkbox-selected':''"
                        @click="list_item_checkbox_click(item.FileId, $event)"
                        @dblclick="_stopPropagation($event)"
                      ></div>
                      <!-- //复选框 -->
                      <!-- 非缩略图情况，直接显示icon，60*60 -->
                      <div
                        class="css-icon60 css-bsb css-fs60 _css-icon60"
                        :class="$staticmethod.getIconClassByExtname(item.FileName, item.FileSize)"
                      ></div>
                      <!-- //非缩略图情况，直接显示icon，60*60 -->
                    </div>
                    <!-- //图标显示部分 -->
                    <!-- 文件名显示部分 -->
                    <div class="css-flex1 css-bsb css-w100 _css-doc-list-item-namectn css-prel">
                      <el-tooltip
                        v-if="item.FileId == '-1' || item.FileId == extdata._showcontextbtns_fileid_renaming"
                        :hide-after="3000"
                        :open-delay="0"
                        class="item"
                        effect="dark"
                        content="Enter确认；ESC取消"
                        placement="bottom"
                      >
                        <input
                          :id="'listview_' + item.FileId"
                          @dblclick="_stopPropagation($event)"
                          @blur="itemnameblur(item.FileId)"
                          @click="_stopPropagation($event)"
                          @keyup="dirnamekeyup(item.FileId, item.FileName, $event);"
                          v-onfocus
                          type="text"
                          class="css-ellipsis _css-newdirname-input css-pl12 css-pr12 css-tac css-fs12"
                          v-model="item.FileName"
                        >
                      </el-tooltip>
                      <div v-else class="_css-doc-list-item-name css-pabs css-fc css-jcsa css-bsb">
                        <div class="css-ellipsis">{{item.FileName}}</div>
                      </div>
                    </div>
                    <!-- //文件名显示部分 -->
                  </div>
                  <!-- //单一个文件 -->
                </div>
                <!-- //高度为100-40的列表体 -->
              </div>
              <!-- //列表显示 -->
              <div
                data-debugflag="line192"
                class="css-h100 css-w100 css-bsb css-usn css-prel"
                :class="extdata.showtype != 0?'css-none':''"
              >
                <el-table
                  ref="doctable"
                  :highlight-current-row="false"
                  @row-click="row_click"
                  @row-dblclick="on_row_dblclick"
                  :border="true"
                  :stripe="false"
                  :data="extdata.tableData"
                  :default-sort="{prop: 'date', order: 'descending'}"
                  height="500"
                  class="_css-table-ele css-scroll _css-customstyle"
                  :row-class-name="tableRowClassName"
                  :header-cell-style="{'background-color':'transparent'}"
                >
                  <el-table-column
                    :resizable="true"
                    class="_css-col-filename"
                    prop="FileName"
                    label="文件名称"
                    min-width="168"

                  >
                    <template slot-scope="scope" >
                      <i
                        :class="'css-icon20 css-fs18 css-fc css-jcsa ' + $staticmethod.getIconClassByExtname(scope.row.FileName, scope.row.FileSize, scope.row.FileExtension )"
                      ></i>
                      <template>
                        <span
                          class="css-cp css-hoverunder css-ml10 css-ellipsis basic-font-color-emphasize"
                          @click="row_filename_click(scope.row, $event)"
                          :title="scope.row.FileName"
                        >{{scope.row.FileName}}</span>

                        <el-tooltip
                          v-if="extdata._suscribed.indexOf(scope.row.FileId)>=0"
                          class="item"  popper-class="css-no-triangle" effect="dark" content="取消订阅" placement="top">
                          <div
                            @click="suscribeitem(scope.row, $event,
                        extdata._suscribed.indexOf(scope.row.FileId)>=0?'2':'1'
                        )"
                            class="_css-fileitem-contentbtn2 _css-alwaysfollowfname css-icon20 css-mr16 css-cp"
                            :class="{'icon-interface-star-se': extdata._suscribed.indexOf(scope.row.FileId)>=0}"

                          ></div>
                        </el-tooltip>

                        <!-- //如果 FileID为-1，则为正在新建的文件夹那一行，否则为普通行 -->
                        <!-- 文档条目 hover 时显示的操作按钮 -->
                        <div
                          class="_css-fileitem-contextbtns css-r0 css-h100 css-bsb css-flex1 css-fc_r"
                        >
                          <template v-if="extdata.doclisttype == 'doclib'">
                            <!-- 更多 -->
                            <el-tooltip class="item"  popper-class="css-no-triangle" effect="dark" content="更多" placement="left">
                              <div
                                @click="show_context_menu(scope.row, $event)"
                                @dblclick="_stopPropagation($event)"
                                class="_css-fileitem-contentbtn css-icon20 css-mr14 css-cp icon-interface-list"
                              ></div>
                            </el-tooltip>
                            <el-tooltip v-if="!scope.row.IsFolder && scope.row.Switchstate == '上传成功' && EnableModelCoverting" class="item"  popper-class="css-no-triangle" effect="dark" content="转换" placement="left">
                              <div
                                @click="conversionModel(scope.row, $event)"
                                @dblclick="_stopPropagation($event)"
                                class="_css-fileitem-contentbtn css-icon20 css-mr14 css-cp icon-zhuanhuan"
                              ></div>
                            </el-tooltip>
                          </template>
                        </div>
                        <!-- //文档条目 hover 时显示的操作按钮 -->
                      </template>
                    </template>
                  </el-table-column>
                  <el-table-column
                    :formatter="FileSize_formatter"
                    :resizable="true"
                    class="_css-col-filesize"
                    prop="FileSize"
                    label="文件大小"
                    min-width="48"

                  ></el-table-column>
                  <el-table-column
                    :formatter="ModifyData_formatter"
                    :resizable="true"
                    class="_css-col-updatetime"
                    prop="ModifyDate"
                    label="更新时间"
                    min-width="48"
                    sortable
                  ></el-table-column>
                </el-table>
              </div>

            </div>
            <!-- //表格体区域 -->
          </div>
          <!-- //右边的表格 -->
          <div class="project-container" v-if="isShowProject">
           <el-input v-model="searchProjectValue" placeholder="输入项目名称" prefix-icon="el-icon-search" @change="getProjectList(false)"></el-input>
            <ul>
              <li v-for="(item,index) in projectList" @click="clickProjectItem(index,item)" :key=index :class="item.selected ? 'selected': 'normal' ">
                <img class="icon" src="../../../assets/images/DocumentSummary/document-project-icon.png" alt="">
                <el-tooltip class="tooltip" effect="dark" :content="item.ProjectName" placement="top-start">
                  <span>{{ item.ProjectName }}</span>
                </el-tooltip>
                <img class="right-icon" src="../../../assets/images/DocumentSummary/project-select-icon.png" alt="" v-if="item.selected">
              </li>
            </ul>
            <div class="btn-wrapper">
              <span class="confirm" @click="confirmProjectSelect">确定</span>
              <span class="cancel" @click="cancelProjectSelect">取消</span>
            </div>
          </div>
        </div>
      </div>
    </section>
    <div
      v-if="extdata._showcontextbtns"
      class="css-pfix css-bsb css-z1  css-pt4 css-pb4 _css-contextMenuContainer"
      :style="{left:extdata._contextbtns_p_x+'px', top:extdata._contextbtns_p_y+'px'}"
    >
      <!-- 菜单中的某一项 -->
      <div
        :title="!item.disable?item.text:''"
        class=" css-fc _css-menuitemstyle"
        :class="{'css-dis':item.disable, '_css-hasbottom':item.bHasBottom}"
        v-for="item in contextmenubtns"
        :key="item.value"
        @click="_stopPropagation($event)"
      >
        <!-- 菜单中的某一项可高亮部分 -->
        <div
          @click.stop="contextitemclick(item.value, item.disable, $event)"
          class=" css-w100 css-tal css-bsb _css-hover-highlight css-b css-usn"
          :class="{'css-dis':item.disable, 'css-cp':!item.disable}"
        >

          <!-- 16 * 16 ml为24 的图标 -->
          <div class="_css-rightbtns-btnicon"
               :class="item.iconclass"
          ></div>
          <!-- //16 * 16 ml为24 的图标 -->

          <!-- 右键菜单中的按钮项，ml为8px -->
          <div class="_css-rightbtns-btntext" >{{item.text}}</div>
          <!-- //右键菜单中的按钮项，ml为8px -->

        </div>
        <!-- //菜单中的某一项可高亮部分 -->
      </div>
      <!-- //菜单中的某一项 -->
    </div>
  </section>
</template>

<script>

import { EventBus } from "@static/event.js";
import CompsFolderBrowser from "@/components/CompsDocument/CompsFolderBrowser";
import CompsSelectModel from "@/components/CompsMaterial/CompsSelectModel"
import CompsFileHistory from "@/components/CompsDocument/CompsFileHistory";
// import CompsCorrelationModel from "@/components/CompsDocument/CompsCorrelationModel";
import CompsUsersInput from "@/components/CompsAuth/CompsUsersInput";
import ModelNewDetail from '@/components/Home/ProjectBoot/modelNewDetail'
import CompsIframeDialog from '@/components/CompsCommon/CompsIframeDialog'
import Vue from "vue";
import {getProjectName} from "../../../utils/projectName";
export default {
  name: "DocumentSummary",
  components: {
    CompsFolderBrowser,
    CompsFileHistory,
    CompsUsersInput,
    // CompsCorrelationModel,
    CompsSelectModel,
    ModelNewDetail,
    CompsIframeDialog
  },

  directives: {
    onfocus: {
      inserted: function(el) {
        // 获取文件（文件夹）ID
        var _filefolderid = el.id.replace("tableview_", "").replace("listview_", "");
        // 获取必要参数
        var _this = debugvue;
        var _arr = _this.extdata.tableData.filter(x => x.FileId == _filefolderid);
        if (_arr.length > 0) {
          if (_arr[0].FileSize == '0') {
            // 文件夹重命名
            _this.extdata._editingext = '';
            el.select();
            el.focus();
          } else {
            // 文件重命名
            // 获取到最后一个“.”的位置。
            var _lastindexofdot = el.value.lastIndexOf(".");
            if (_lastindexofdot == -1) {
              el.select();
              el.focus();
              _this.extdata._editingext = '';
            } else {
              el.selectionStart = 0;
              el.selectionEnd = _lastindexofdot;
              el.focus();
              _this.extdata._editingext = el.value.substr(_lastindexofdot + 1);
            }
          }
        } else {
          // 一般不会进入此分支
          el.select();
          el.focus();
        }
      }
    }
  },

  data() {
    return {
      projectName:'',
      token:'',
      projectList:[],
      searchProjectValue :'',
      isShowProject: false,
      projectId: '', // 缓存获取projectId
      userId: '', // 缓存获取userId
      breadList:[],     //面包屑数组
      breadLabel:'' ,     //面包屑文字
      R_lastTime:true, //记录上一次点击
      R_RoleId:'',//获取角色权限
      // 最后一次选择打开的模型版本为 VersionNO
      lastOpenVersionNo: '',

      // 文档右键菜单的高度与宽度
      docrightmenu_w:160,
      docrightmenu_h:120,//435

      showModelView:false,
      BIM_Session:"",//模型参数返回的Session
      // ProjectId:"",
      SelModelObj:{},//选择的当前模型对象
      // //KC

      // 与当前多选的可用性相关
      currentMulAuth:{
        delete: false
      },

      // 与 CompsCorrelationModel 组件相关的 data
      CompsCorrelationModel_data: {
        visible: false,
        dataSource: [],
        projectID: '',
        fileMsg: [],
        CreateUserName: '',
      },

      // 与 CompsFolderBrowser 组件相关的 data
      CompsFolderBrowser_data: {
        visible: false, // 是否显示 CompsFolderBrowser 组件
        dataSource: [],
        title: "XXX",
        title_left:'移动【',
        title_right:'】文件夹到'
      },
      // //与 CompsFolderBrowser 组件相关的 data

      // 与 CompsFileHistory 组件相关的 data
      CompsFileHistory_data: {
        visible: false,
        dataSource: [],
        title_maxwidth: 300,
        title: "",
        title_left: '历史版本：',
        title_right: ''
      },
      // 与 CompsFileHistory 组件相关的 data

      callerdata_folderBrowser: {
        backgroundColorOpacity: "0",
        zIndex: "-1",
        callerdata_dialogHeader: {
          title: "移动【FileId1 文件或者文件夹】到"
        },
        if_folderBrowser: true,
        treedata: []
      },

      // 树形数据
      treedata: {
        props1: {
          children: "Children",
          label: "FolderName",
          isLeaf:"isLeaf"
        },
        // [Obsolete]
        allcollapsedids: [], // 所有已经展开了的节点（未使用）
        items: [
        ] // classname: 默认为未展开的文件夹图标
      },
      // 分享相关的
      sharingdata: {
        _sharing_emailcontent: "", // 分享文件邮件正文内容
        _sharing_inputkw: "", // 分享-角色成员-输入姓名关键字
        _sharing_showkwsuggest: false, // 分享-角色成员-输入姓名关键字-是否显示下拉提示
        _sharing_showkwsuggest_items: [], // 分享-角色成员-输入姓名关键字-下拉提示数据源
        _sharing_inputemail: "", // 分享输入邮件地址
        _sharing_showemailsuggest: false, // 显示邮箱地址建议
        _sharing_showemailsuggest_items: [
          //,{UserId: 'e', Email: '<EMAIL>', Account: 'a', RealName: 'aaa'}
        ] // 显示邮箱地址的建议内容列表
      },
      // 对话框缓存属性值。
      cachedata: {
        _sharing_deadline: -1, // 缓存的分享有效时间（天）
        _sharing_auths: ["Download"], // 缓存的分享权限 js array value coping
        _sharing_ispublic: true // 缓存的分享是否公开
      },
      extdata: {
        _showblankcontextbtns_p_x:0,  // “上传/新建”上下文菜单:left
        _showblankcontextbtns_p_y:0,  // “上传/新建”上下文菜单:top
        _showblankareacontext: false, // 是否显示“上传/新建”上下文菜单
        alreadycancelnameedit: false, // 已经通过esc过点击取消，取消了新文件夹名或文件新名称的编辑状态
        nonewauth: true, // 为 true 时当前所处文件夹无新建、拖动上传、移动后置权限。
        _editingext: '', // 正在修改文件（夹）的扩展名，如果是文件夹，则为空字符串
        _doc_showtree: false, // 是否显示目录树
        _sharing_pwd:'', // 文档分享的密码
        _sharing_toemails: [], // 文件（夹）分享的邮件发送目标（initFile...中要初始化，把自己加进来）
        _sharing_ispublic: true, // 分享的初始权限 是否是公开
        _sharing_auths: ["Download"], // 分享的初始权限
        _sharing_deadline: -1, // 分享的时效
        _sharing_url: "about:blank", // 分享的地址
        _sharingmain_emailing: false, // 分享的主窗体中，是否展开邮件
        _sharingshowtype: "sharingmain", // 'sharingmain', 'sharingauth', 'sharingmember
        _rolelist: [], // 角色数据
        _userselected: [], // 已经选择的人
        _userlist: [], // 成员数据
        _membersearchkeyvalue: "", // 角色成员搜索的关键字
        _showshare: false, // 是否显示分享对话框
        _contextbtns_p_x: 0, // 上下文菜单元素的left值
        _contextbtns_p_y: 0, // 上下文菜单元素的top值
        _showcontextbtns_fileid: undefined, // 显示的上下文菜单所影响的FileId
        _showcontextbtns_fileid_renaming: undefined, // 正在重命名的 FileId
        _showcontextbtns: false, // 是否正在显示上下文菜单
        _droppingdirandfiles: {}, // 正在拖放的多个文件夹及文件
        _lastclickedFileId: "", // 最后一次点击的 FileId
        _cachedirname_renameesc: "", // 缓存文件名名称，供 ESC 使用
        _cachedirname: "", // 缓存文件夹名称（当错误名称出现时，恢复到旧名称，一开始创建时，需要初始化此字段值
        _totalcount: 0, // 总文档数
        _suscribed: [], // 已经订阅了的 objectId
        _showfilebtns: false, // 是否显示文件上传相关按钮组
        _lastkeyword: "", // 最后一次搜索使用的关键字
        _selectedobjs: [], // 选中的所有数据（对象数组）
        _openstacks: [
          //{FileId:'a', FileName: 'a'}
        ], // 当前打开的所有文件夹（栈结构）
        //_waitforsecondc1lickfileid: undefined, // 等待第二次点击的FileId
        queueStateStr: "传输队列", // 传输队列， 上传完成， 正在上传（22/27）
        showQueue: false, // 是否显示上传队列窗口
        collapseQueue: false, // 是否折叠上传队列
        /* declare */ doclisttype: "doclib", // 'doclib':项目文档 'mysuscribe'：我的收藏 'myshared'：我的分享 'recycle'：回收站
        //btngrouptip: "选中了个文件，可以执行右侧操作",
        showtype: 0, // 0为表格， 1为列表
        tableData: [], // 加载到数据主区域的表格/列表数据
        uploadQueueData: [] // 上传队列数据
      },
      // 上下文菜单项----新
      contextmenubtns_origin: [
        { bHasBottom:true, iconclass:'icon-interface-folder', text: "打开", value: "Open", disable: 0 },
        { bHasBottom:false, iconclass:'icon-interface-download-fill', text: "下载", value: "DownLoad", disable: 0 },
        // { text: "授权", value: "auth", disable: 1 },
      ],
      contextmenubtns: [
        { bHasBottom:true, iconclass:'icon-interface-folder', text: "打开", value: "Open", disable: 0 },
        { bHasBottom:false, iconclass:'icon-interface-download-fill', text: "下载", value: "DownLoad", disable: 0 },
      ],
      blankareacontextmenubtns: [
        { text: "上传文件", value: "uploadfile", disable: 0 },
        { text: "上传文件夹", value: "uploadfolder", disable: 0 },
        { text: "新建文件夹", value: "newfolder", disable: 0 }
      ], // 空白区域上下文菜单项
      docmenuitems: [
        {
          text: "项目文档",
          doclisttype: "doclib",
          iconclass: "icon-interface-doc",
          istree: false,
          collapsable: true
        },
        {
          text: "树",
          doclisttype: "tree",
          iconclass: "icon-interface-star-nor",
          istree: true
        },
      ], // 项目文档，我的订阅，我的分享，回收站
      keyword: "",
      isCollapse: "上海",
      msg: "Document.vue",
      v_Id:undefined, //当前选中的文档Id
      firstLevelFolderData:[], //第一级的数据
      openModelItem: {}, // 点击打开关联模型id和name
      shareTableData: [], // 分享显示表格
      sharedoclisttype: false, // 记录点击的是分享
      conversionLabelId: '',  // 默认全景图标签
      conversionModelPhaseValue: '',  // 默认模型阶段
      conAllType: [], // 转换文件支持的格式
      panoUrlIframe: {}, // 打开全景图的url
      showPanoIframeView: false,
      stopDoubleClick: {
        bool:false,
        folderId: '',
      },  // 阻止多次请求
      EnableModelCoverting: false,
    };
  },
  filters: {
    filterShardDays(val){
      let _val = val == -1 ? _val = "永久" : _val = val + '天'
      return _val
    },
    filterHasPassword(val){
      return val ? '是' : '否'
    }
  },
  created(){
    if (this.$route.params.token){
      this.token = this.$route.params.token;
      Vue.prototype.$staticmethod.Set("Token", this.token);
      this.userId = this.$route.params.userId;
      this.getProjectList(true)
    }else {
      this.token = this.$staticmethod.Get("Token");
      this.projectId = this.$staticmethod._Get("organizeId");
      this.userId = this.$staticmethod.Get("UserId");
      this.load_tree_firstlevelfolder();
      this.projectName = getProjectName()
    }
  },
  mounted() {
    let _this = this;
    _this.$emit('onmounted', 'document');
    window.debugvue = _this;
    // _this.load_tree_firstlevelfolder();
    // _this.getModelPhaseList();
    this.EnableModelCoverting = window.bim_config.EnableModelCoverting

  },
  computed: {

    // 带处理的 _openstacks
    _openstacks_withprocessing:{
      get(){
        let _this = this;
        return _this.extdata._openstacks;
      }
    },

    // 当前已处于的文件夹ID，如果是根目录，则为""
    CompsFolderBrowser_data_alreadyinFolderId:{
      get(){
        let _this = this;
        if (_this.extdata._openstacks.length == 0) {
          return "";
        } else {
          return _this.extdata._openstacks[_this.extdata._openstacks.length - 1].FileId;
        }
      }
    },

    // 文档数据右上角“订阅”按钮的文本
    mul_suscribe_btnobj: {
      get() {
        let _this = this;

        // 根据选中的情况来决定显示“订阅”还是“取消订阅”
        // 判断选中的数据中，是否有不存在于 _suscribed 中的
        // 如果有，显示“订阅”否则显示“取消订阅”
        var _notinsuscribeds_selecteds = _this.extdata._selectedobjs.filter(
          x => _this.extdata._suscribed.indexOf(x.FileId) < 0
        );
        if (_notinsuscribeds_selecteds.length > 0) {
          return {
            name: "订阅",
            value: 1
          };
        } else {
          return {
            name: "取消订阅",
            value: 0
          };
        }
      }
    },

    //
    doclisttypename: {
      get() {
        // 根据 doclisttype 得到面包屑第一项的内容
        let _this = this;
        var docitem = _this.docmenuitems.filter(
          x => x.doclisttype == _this.extdata.doclisttype
        );
        if (docitem.length > 0) {
          return docitem[0].text;
        } else {
          return "项目文档";
        }
      }
    },

    // 显示分享正在应用的权限
    showauth_whensharing_deadline: {
      get() {
        let _this = this;
        switch (_this.extdata._sharing_deadline) {
          case -1:
            return "永久";
            break;
          case 1:
            return "1天";
            break;
          case 7:
            return "7天";
            break;
          default:
            return "永久";
        }
      }
    },

    // 显示分享正在应用的权限-操作权限
    showauth_whensharing_operate: {
      get() {
        let _this = this;
        var _strret = "";
        if (_this.hasUpload) {
          _strret += "上传";
          if (_this.hasDownload) {
            _strret += "/下载";
          }
        } else if (_this.hasDownload) {
          _strret += "下载";
        } else {
          _strret += "无权限";
        }
        return _strret;
      }
    },

    // get or set if my email has been including to 'toemails'
    sharingCopingToMe: {
      set(val) {
        let _this = this;
        var _myemail = _this.$staticmethod.Get("Email");
        if (val) {
          // 将设置为包含自己的邮件地址
          if (_this.extdata._sharing_toemails.indexOf(_myemail) < 0) {
            _this.extdata._sharing_toemails.push(_myemail);
          }
        } else {
          // 将移除自己的邮件地址
          if (_this.extdata._sharing_toemails.indexOf(_myemail) >= 0) {
            _this.extdata._sharing_toemails = _this.extdata._sharing_toemails.filter(
              x => x != _myemail
            );
          }
        }
      },
      get(val) {
        let _this = this;
        var _myemail = _this.$staticmethod.Get("Email");
        // 判断自己的邮件地址是否存在于 extdata._sharing_toemails 中
        var _has = _this.extdata._sharing_toemails.indexOf(_myemail) >= 0;
        return _has;
      }
    },

    // hasUpload
    hasUpload: {
      set(val) {
        let _this = this;
        if (val) {
          if (_this.extdata._sharing_auths.indexOf("Upload") < 0) {
            _this.extdata._sharing_auths.push("Upload");
          }
        } else {
          if (_this.extdata._sharing_auths.indexOf("Upload") >= 0) {
            _this.extdata._sharing_auths = _this.extdata._sharing_auths.filter(
              x => x != "Upload"
            );
          }
        }
      },
      get() {
        let _this = this;
        return _this.extdata._sharing_auths.indexOf("Upload") >= 0;
      }
    },

    // hadDownload
    hasDownload: {
      set(val) {
        let _this = this;
        if (val) {
          if (_this.extdata._sharing_auths.indexOf("Download") < 0) {
            _this.extdata._sharing_auths.push("Download");
          }
        } else {
          if (_this.extdata._sharing_auths.indexOf("Download") >= 0) {
            _this.extdata._sharing_auths = _this.extdata._sharing_auths.filter(
              x => x != "Download"
            );
          }
        }
      },
      get() {
        let _this = this;
        return _this.extdata._sharing_auths.indexOf("Download") >= 0;
      }
    },

    // 二维码地址拼接
    qrcode_sharing: {
      get() {
        let encodedUrl = encodeURIComponent(this.extdata._sharing_url);
        let codeImgUrl = `${this.$urlPool.QRCode}?encodedUrl=${encodedUrl}&token=${this.$staticmethod.Get("Token")}`;
        return codeImgUrl;
      }
    },

    // 读取内存数据中的已选中文件个数，拼接成文本
    btngrouptip: {
      get() {
        let _this = this;
        var selectionlen = _this.extdata._selectedobjs.length;
        return `选中了${selectionlen}个文件，可以执行右侧操作`;
      }
    }
  },
  methods: {
    /**
     * 选择项目确认
     */
    confirmProjectSelect(){
      // 切换ProjectId 获取文档数据
      this.isShowProject = false
      const project = this.projectList.find(item => item.selected)
      this.projectId = project.ProjectId
      this.projectName = project.ProjectName
      this.load_tree_firstlevelfolder();
      this.extdata._openstacks = []
      this.searchProjectValue = ''
      this.extdata._lastkeyword = ''
    },
    /**
     * 取消选择 关闭界面
     */
    cancelProjectSelect(){
      this.searchProjectValue = ''
      //
      this.isShowProject = false
    },
    /**
     * 点击项目列表
     * @param index
     * @param item
     */
    clickProjectItem(index,item){
      for (const projectListElement of this.projectList) {
        projectListElement.selected = false
      }
      this.projectList[index].selected = true
    },
    /**
     * 获取项目列表
     * @param isRouter
     */
    getProjectList(isRouter){
      let _OrganizeId = this.$staticmethod._Get("_OrganizeId"); // 机构ID
      this.$axios
        .get(`${window.bim_config.webserverurl}/api/User/Project/QueryProjectPaged?organizeId=${_OrganizeId}&token=${this.token}&pageNum=1&pageSize=3000&keyword=${this.searchProjectValue}&sort=`)
        .then(res=>{
          //  ProjectId
          this.projectList = res.data.Data.rows;
          for (const projectListElement of this.projectList) {
            projectListElement.selected = false
          }
          if (isRouter){
            this.projectList[0].selected = true
            this.projectId = this.projectList[0].ProjectId
            this.projectName = this.projectList[0].ProjectName
            this.load_tree_firstlevelfolder();

          }else {
            const index = this.projectList.findIndex((item) => item.ProjectId === this.projectId);
            if (index !== -1){
              this.projectList[index].selected = true
              this.projectName = this.projectList[index].ProjectName
            }
          }

        })
        .catch(err=>{})
    },
    /**
     * 打开选择项目
     */
    clickSelectProject(){
      this.getProjectList(false)
      this.isShowProject = true;
    },
    /**
     * 点击左侧项目文档按钮重置数据
     */
    clickDocument(){
      this.closeallpopup()
      this.GetAllFolderAndFileByProjectID({
        LikeName: "",
        NormalOrDrawings: "Normal",
        FolderID: 0,
        _if_dir_change_suc_openstacks: []
      });
    },
    copy() {
      let txt = document.getElementById("txtWXUrl");
      txt.select();
      document.execCommand("Copy");
      this.$message({ type: "success", message: "复制成功" });
    },
    // 开始批量下载
    patchdownload(){
      let _this = this;
      // console.log('TODO: 批量下载');
      var _selectedobjs = _this.extdata._selectedobjs;
      var _selFiles = _selectedobjs.filter(x => x.FileSize != '0').map(x => x.FileId).join(',');
      var _selDirs = _selectedobjs.filter(x => x.FileSize == '0').map(x => x.FileId).join(',');
      window.location.href = `${window.bim_config.webserverurl}/api/v1/file/download-pack?projectId=${_this.projectId}&fileIds=${_selFiles}&folderIds=${_selDirs}&Token=${this.token}`;
    },
    // 取消关联
    do_unrelmodel(docid){
      let _this = this;
      // console.log(_this.extdata._selectedobjs[0].FileId,docid)
      _this.$axios({
        url:`${window.bim_config.webserverurl}/api/v1/file/model/cancel?projectId=${_this.$staticmethod._Get("organizeId")}&Token=${_this.$staticmethod.Get('Token')}&userId=${_this.$staticmethod.Get("UserId")}`,
        method:'post',
        data: [docid],
      }).then(x => {
        if (x.data.Ret == 1) {
          _this.refreshdata(docid);
        }
        _this.$message.error(x.data.Msg)
      }).catch(x => {

      });

    },

    // 取消关联文档
    unrelmodel(){
      // 操作确认
      let _this = this;
      // 获得参数
      if (_this.extdata._selectedobjs.length != 1) {
        console.error('必须选中且只选中一条数据');
        return;
      }
      var fileobj = _this.extdata._selectedobjs[0];
      if (fileobj.ModelId.length == 0){
        _this.$message.error('该文档未关联模型')
        return;
      }
      _this.$confirm('是否确定取消该关联？', '操作确认',{
        confirmButtonText: '确定',
        cancelButtonText:'取消',
        type: 'warning'
      }).then(x => {
        _this.do_unrelmodel(fileobj.FileId);
      }).catch(x => {

      });
    },

    // 发送上传文件后的消息
    sendUploadFilesMsg(ajaxretarr){
      let _this = this;
      var filearr = [];
      for (let i = 0; i < ajaxretarr.length; i++) {
        if (ajaxretarr[i] && ajaxretarr[i].status == 200 && ajaxretarr[i].data) {
          // _FileIds += `${ajaxretarr[i].data.FileId},`;
          // _FileNames += `${ajaxretarr[i].data.FileName},`;
          filearr.push(ajaxretarr[i].data);
        }
      }
      var filearrstr = JSON.stringify(filearr);
      // debugger;
      _this.$axios({
        method:'post',
        url:`${window.bim_config.webserverurl}/api/Tool/SendMsg/SendMessage_MulFiles`,
        data:_this.$qs.stringify({
          ProjectID: _this.$staticmethod._Get("bimcomposerId"),
          organizeId: _this.$staticmethod._Get("organizeId"),
          Token: _this.$staticmethod.Get("Token"),
          fileArrStr: filearrstr
        })
      });
    },

    // 获取当前所在的文件夹ID（如果在根目录，返回bimcomposerId）
    getAtFolderId(){
      let _this = this;
      var organizeId = _this.$staticmethod._Get("organizeId");
      var _AtFolderId;
      if (_this.extdata._openstacks.length == 0) {
        _AtFolderId = organizeId;
      } else {
        _AtFolderId = _this.extdata._openstacks[_this.extdata._openstacks.length - 1].FileId;
      }
      return _AtFolderId;
    },

    //  打开模型
    preopenmodel(modelId,modelname,VersionNO){
      VersionNO = VersionNO || '';
      let _this = this;
      let _vaultID = _this.$staticmethod._Get("organizeId");
      this.openModelItem = {VaultID:_vaultID, modelID:modelId,modelName: modelname,modelVersion: VersionNO}
      this.showModelView = true;
    },
    correlationModel_ok2(obj){
      let _this = this;

      let data = {
        ProjectId: _this.$staticmethod._Get("organizeId"),
        UserId: this.$staticmethod.Get("UserId"),
        FileIds: [
          _this.extdata._selectedobjs[0].FileId
        ],
        ModelId: obj.modelid,
        ModelName: obj.modelname,
      }

      _this.$axios({
        method: 'post',
        data: data,
        url: `${window.bim_config.webserverurl}/api/v1/file/model/bind?Token=${_this.$staticmethod.Get('Token')}`
      }).then(x => {
        if (x.data.Ret == 1) {
          this.$message({
            message: '关联成功',
            type: 'success'
          });

          _this.CompsCorrelationModel_data.visible = false;
          _this.refreshdata(_this.extdata._selectedobjs[0].FileId);
        } else {
          _this.$message.error(x.data.Msg)
        }
      }).catch(x => {
        console.error(x);
      });
    },
    openModel_Version(Model, ViewID, versionNo) {
      let _this = this;
      this._hideModelInfo();
      _this.IsDialogCovery=false;
      this.lastOpenVersionNo = versionNo;
      if (this.showModelView == true) {
        this.$refs.ref_bim_viewer.changeModelUrlVersion(versionNo);
        return;
      }
    },

    tableDatapatch(arr){
      // 这个方法是用来干啥的
      // let _this = this;
      // for(var i = 0; i < arr.length; i++) {
      //   arr[i].relprop = {
      //     ModelID:''
      //   };
      // }
    },

    // 新建、上传右键菜单中某项的点击动作
    blankareaitemclick(ev, item){
      let _this = this;
      if (item.disable == true) {
        return;
      }

      if (item.value == 'uploadfile') {
        _this.open_file_uploader(ev);
      } else if (item.value == 'uploadfolder') {
        _this.open_folder_uploader(ev);
      } else if (item.value == 'newfolder'){
        _this.appendCreatingDir(ev);
      }
      _this.closeallpopup();
    },

    _preventDefault(ev){
      var _this =this;
      _this.closeallpopup();
      ev.preventDefault();
      ev.stopPropagation();
    },

    // 文档（矩阵及表格视图下）空白区域的右键操作
    blankareacontextmenu(ev){
      ev.preventDefault();

      // 全局隐藏全部的弹出菜单
      var _this= this;
      _this.closeallpopup();

      // 读取当前所在文件夹的上传/新建权限
      if (_this.extdata.doclisttype == 'doclib') {
        // 文档库时，显示与文档库相关的空白区域上下文菜单
        // 根据点击的位置，设置上下文菜单的位置
        _this.set_blankcontext_pos(100, 104 ,ev);
        // 设置所有权限数据为无权限
        for (var i = 0; i < _this.blankareacontextmenubtns.length; i++) {
          // disable
          if (_this.extdata.nonewauth == true) {
            _this.blankareacontextmenubtns[i].disable = true;
          } else {
            _this.blankareacontextmenubtns[i].disable = false;
          }
        }
        // 设置“新建、上传”上下文菜单为显示
        _this.extdata._showblankareacontext = true;
      }
    },

    // 新建文件夹或重命名项的文本框的 blur 事件处理
    itemnameblur(fileId){
      var _this =this;
      // console.log('_this.extdata.alreadycancelnameedit=' + _this.extdata.alreadycancelnameedit);
      if (_this.extdata.alreadycancelnameedit == true) {
        return;
      }

      // 判断要操作的是文件还是文件夹
      var _IsFolder = false;
      var TheFiles = _this.extdata.tableData.filter(x => x.FileId == fileId);
      if (TheFiles.length > 0 && TheFiles[0].FileSize == '0') {
        _IsFolder = true;
      }

      // oknamebtn_
      if (fileId == -1) {
        document.getElementById('oknamebtn_' + fileId).click();
        return;
      } else if (_IsFolder == false) {
        document.getElementById('oknamebtn_' + fileId).click();
        return;
      }
      // console.log(_this.extdata._showcontextbtns_fileid_renaming);
      _this.$confirm('是否放弃本次操作?', '操作确认', {
        confirmButtonText: '确定',
        cancelButtonText:'取消',
        type: 'warning'
      }).then(x => {
        //cancelnamebtn_
        document.getElementById('cancelnamebtn_' + fileId).click();
      }).catch(x => {
        if (debugvue.extdata.showtype == 0) {
          // 表格
          document.getElementById('tableview_' + fileId).focus();
          document.getElementById('tableview_' + fileId).select();
        } else {
          // 矩阵
          document.getElementById('listview_' + fileId).focus();
          document.getElementById('listview_' + fileId).select();
        }
      });
    },

    // 移除指定的队列数据
    remove_uploaditem(rowitem){
      let _this = this;
      _this.extdata.uploadQueueData.splice(_this.extdata.uploadQueueData.indexOf(rowitem), 1);
    },

    // 取消文件上传
    cancel_uploaditem(rowitem){
      let _this = this;
      // 弹出操作确认
      _this.$confirm("确定取消该文件上传？", "操作确认"
        , {
          confirmButtonText: '确认',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(x => {
        rowitem.source.cancel('cancel');
        rowitem.hasbeenCancelled = true;

        // 取消后直接移除
        _this.remove_uploaditem(rowitem);
        _this.refreshdata();

      }).catch(x => {
      });

    },

    _ondocnameinput(str, unknown, ev){
      // 通过 ev 判断是否按的是回车
      let _this = this;
      if (ev.keyCode == 13) {
        // 将 str 作为关键字进行文档搜索
        _this.keyword = str;
        _this.search_doc_bykeyword(undefined, undefined);
      }
    },
    //历史版本下载
    filehistory_onitemdownload(item){
      let _this = this;
      var _FileInfoVersionId = item.FileId;
      var _FileInfoVersion = item.Version;
      var filedownloadurl = `${window.bim_config.webserverurl}/api/v1/file/summary-download?fileId=${_FileInfoVersionId}&userId=${_this.$staticmethod.Get("UserId")}&version=${_FileInfoVersion}&Token=${this.$staticmethod.Get('Token')}`;
      window.location.href = filedownloadurl;
    },
    //历史版本预览
    filehistory_onitempreview(item){
      let _this = this;
      let filedownloadurl = `${window.bim_config.webserverurl}/api/v1/file/summary-preview?FileId=${item.FileId}&Version=${item.FileVersion}&UserId=${this.$staticmethod.Get("UserId")}&Token=${this.$staticmethod.Get('Token')}`
      _this.previewfile_usingdownloadurl(filedownloadurl, item.FileId);
    },

    // 转到项目文档
    turntodocmain(fid){
      // 获取面包屑
      let _this = this;
      let _item = _this.extdata.tableData.filter(x => x.FileId == fid)[0];
      let _FolderID, _FileID;
      if (_item.FileSize == '0') {
        _FolderID = fid;
        _FileID = '';
      } else {
        _FolderID = '';
        _FileID = fid;
      }

      _this.collapseoptions();
      _this.extdata.doclisttype = "doclib";
      // _this.enterroot(fid);
      _this.GetAllFolderAndFileByProjectID({
        LikeName: "",
        NormalOrDrawings: "Normal",
        FolderID: _FolderID,
        _if_dir_change_suc_openstacks: [],
        AutoSelectFileId: fid
      });
      return
    },

    // 全部下拉按钮的收起动作
    collapseoptions(){
      let _this = this;
      // 左上角按钮
      _this.extdata._showfilebtns = false;
    },

    // 点击全部清空（回收站）
    begin_remove_all(){
      let _this = this;
      if (_this.extdata.tableData.length == 0) {
        return;
      }
      // 操作确认：确认清空回收站？
      _this.$confirm("确认清空回收站？", "操作确认"
        , {
          confirmButtonText: '确认',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(x => {
        _this.extdata._selectedobjs = _this.$staticmethod.DeepCopy(_this.extdata.tableData);
        _this.remove_multiple(null);
        _this.collapseoptions();
      }).catch(x => {
      });
    },

    // 恢复选中的文件或全部文件
    recover(willall, specificItemId){
      let _this = this;
      if (willall == true){
        _this.extdata._selectedobjs = _this.$staticmethod.DeepCopy(_this.extdata.tableData);
      }
      if (specificItemId) {
        _this.extdata._selectedobjs = _this.extdata.tableData.filter(x => x.FileId == specificItemId);
      }
      // 调用恢复接口，恢复回收站中的数据
      let data = []
      for (var i = 0; i < _this.extdata._selectedobjs.length; i++) {
        let ele = _this.extdata._selectedobjs[i]
        let obj ={
          Id:ele.FileId,
          IsFolder:ele.IsFolder
        }
        data.push(obj)
      }
      _this.$axios({
        method: 'post',
        headers:{
          'Content-Type':'application/json'
        },
        url: `${window.bim_config.webserverurl}/api/v1/file/restore?userId=${_this.$staticmethod.Get("UserId")}&Token=${this.$staticmethod.Get('Token')}`,
        data: JSON.stringify(data)
      }).then(x => {
        _this.$message({
          message: x.data.Msg,
          type: x.data.Ret==1?"success":"warning"
        });
        _this.refreshdata();

      }).catch(x => {
        console.warn(x);
      });

    },
    begin_recover(willall, specificItemId){
      let _this = this;
      if (willall && _this.extdata.tableData.length == 0) {
        return;
      }
      _this.recover(willall, specificItemId);
    },

    // 表格、列表视图的文件（夹）进入方法（包括回收站不可进的逻辑）。
    item_enter(row){
      // 判断是否是处于回收站状态
      let _this = this;
      if (_this.extdata.doclisttype == 'recycle') {
        if (_this.$configjson.debugmode == '1') {
          console.log('当前为回收站，不进行文件（夹）进入动作');
        }
        return;
      }
      // 进行文件进入动作
      if (row.FileSize == '0' && row.IsFolder) {
        if (_this.extdata.doclisttype == 'mysuscribe') {
          _this.turntodocmain(row.FileId);
        } else {
          _this.enterdir(row.FileId, row.FileName, null);
        }
      } else {
        _this.begin_previewfile(row, null);
      }
    },

    set_context_pos(contextwidth, contextheight, ev){
      let _this = this;
      _this.extdata._contextbtns_p_x = ev.clientX > document.body.clientWidth - contextwidth?document.body.clientWidth - contextwidth:ev.clientX;
      _this.extdata._contextbtns_p_y = ev.clientY > document.body.clientHeight - contextheight?document.body.clientHeight - contextheight:ev.clientY;
    },

    set_blankcontext_pos(contextwidth, contextheight, ev){
      let _this = this;
      _this.extdata._showblankcontextbtns_p_x = ev.clientX > document.body.clientWidth - contextwidth?document.body.clientWidth - contextwidth:ev.clientX;
      _this.extdata._showblankcontextbtns_p_y = ev.clientY > document.body.clientHeight - contextheight?document.body.clientHeight - contextheight:ev.clientY;
    },

    // UpdateFileName 重命名文件唯一接口调用
    final_renamefile(finalname, fileid){
      // 显示 loading
      let _this = this;
      var _LoadingIns = _this.$loading({
        text: '处理中',
        target: document.getElementById("id_datamain")
        ,background:"rgba(240, 242, 245, 1)"
      });

      let IsFolder = _this.extdata._selectedobjs.filter(x => x.FileId == fileid)[0].IsFolder
      let postdata = JSON.stringify({
        Id: fileid,
        UserId: _this.$staticmethod.Get("UserId"),
        Name: finalname,
        IsFolder
      });

      // 调用接口

      _this.$axios({
        method: 'post',
        headers:{
          'Content-Type':'application/json'
        },
        url: `${window.bim_config.webserverurl}/api/v1/folder/update?Token=${_this.$staticmethod.Get('Token')}`,
        data: postdata
      }).then(x => {
        // 刷新
        _this.$message({
          message: x.data.Msg,
          type: x.data.Ret==1?"success":"warning"
        });
        _this.refreshdata(fileid);
        _LoadingIns.close();
      }).catch(x => {
        console.warn(x);
        _LoadingIns.close();
      });
    },

    // 用户已经通过了扩展名确认，进行下一步确认
    extincluing_renameItem(tomodifyname, tomodifyid){
      let _this = this;
      // console.log(tomodifyname, tomodifyid);

      // 同名判断，并提供新名
      var _hasrepeatornewname = _this.getSuggestRepeatingFileName(tomodifyname, tomodifyid);
      console.log('文件重命名失去焦点');
      if (_hasrepeatornewname) {
        _this.$confirm(`已存在重复的名称，要命名为 ${_hasrepeatornewname} 吗`, "提示",
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(x => {
          _this.final_renamefile(_hasrepeatornewname, tomodifyid);
        }).catch(x => {
          // 不做任何操作

          // 判断是哪种视图，直接select and focus
          if (_this.extdata.showtype == 0) {
            // 表格
            document.getElementById('tableview_' + tomodifyid).focus();
            document.getElementById('tableview_' + tomodifyid).select(); // tableview_-1
          } else {
            // 矩阵
            document.getElementById('listview_' + tomodifyid).focus();
            document.getElementById('listview_' + tomodifyid).select();
          }
          _this.extdata.alreadycancelnameedit = false;
        });
      } else {
        // 不重名，直接进行重命名
        _this.final_renamefile(tomodifyname, tomodifyid);
      }
    },

    // Obsolete
    func_clearselect_and_contextmenu() {
      let _this = this;
      _this.extdata._selectedobjs = [];
      _this.extdata._showcontextbtns = false;
      _this.collapseoptions();
      _this.closeallpopup();
    },

    context_hispatches() {
      // 获取文件ID
      // 获取文件版本（当前）
      let _this = this;
      var _Files = _this.extdata._selectedobjs;
      if (_Files.length == 0) {
        _this.$message({
          message: "请选中文件后再操作",
          type: "warning"
        });
        return;
      }
      var _FileId = _Files[0].FileId;

      var _CurrentVersionNo = _Files[0].FileVersion;

      // loading
      var _LoadingIns = _this.$loading({
        text: "加载中"
      });

      // 调用接口：获取某 FileId 的所有历史版本数据
      var _getVersion = _this
        .$axios({
          method: "get",
          url: `${window.bim_config.webserverurl}/api/v1/file/history?fileId=${_FileId}&userId=${_this.$staticmethod.Get("UserId")}&Token=${_this.$staticmethod.Get('Token')}`
        })
        .then(x => {
          if(x.data.Ret == 1){
            // 如果当前文件没有历史版本数据，则直接弹出提示
            if (x.data.Data.length == 0) {
              _LoadingIns.close();
              _this.$message({
                message: "当前文件没有历史版本",
                type: "warning"
              });
              return;
            }

            var _reversed = x.data.Data.sort((a, b) => b.Version - a.Version);
            var _DataSourceHistory = [];

            // 逆序
            for (var i = 0; i < _reversed.length; i++) {
              // 从 base_users 中找到指定 userId
              _DataSourceHistory.push({
                FileVersion: _reversed[i].Version,
                CreateDateStr: _reversed[i].CreateTime.replace(
                  "T",
                  " "
                ).replace(/-/g, "/"),
                IsCurrent: _CurrentVersionNo == _reversed[i].Version,
                RealName: _reversed[i].CreatorUserName,
                FileInfoVersionId: _reversed[i].Id,
                FileId: _reversed[i].FileId
              });
            }

            // all=>赋予 dataSource ，显示弹窗
            _this.CompsFileHistory_data.title = `${_Files[0].FileName}`;
            _this.CompsFileHistory_data.title_left = `历史版本：【`;
            _this.CompsFileHistory_data.title_right = `】`;
            _this.CompsFileHistory_data.dataSource = _DataSourceHistory;
            _this.CompsFileHistory_data.visible = true;
            _LoadingIns.close();
          }else{
            this.$message.error(x.data.Msg)
          }
        })
        .catch(x => {
          _LoadingIns.close();
        });
    },

    //打开模型数据列表
    showModelList() {
      //获取项目id
      this.CompsCorrelationModel_data.projectID = this.$staticmethod._Get("bimcomposerId");
      this.CompsCorrelationModel_data.fileMsg = this.extdata._selectedobjs;
      this.CompsCorrelationModel_data.CreateUserName = this.$staticmethod.Get("Account");
      this.CompsCorrelationModel_data.visible = true;
      console.log('打开模型数据列表');
    },

    //tableRowClassName
    tableRowClassName({ row, rowIndex }) {
      let _this = this;
      var _sel_hasThisRow =
        _this.extdata._selectedobjs.filter(x => x.FileId == row.FileId).length >
        0;
      return "css-tdunder " + (_sel_hasThisRow ? "css-tabrow-selected" : "");
    },

    // 点击队列表格的关闭按钮
    begin_clearuploadqueue() {
      // 点击队列表格的关闭按钮
      let _this = this;
      // 如果队列个数为0，直接隐藏
      if (_this.extdata.uploadQueueData.length == 0) {
        _this.showorhidequeue(false, false);
        return;
      }
      // 显示弹窗提示用户操作
      _this.showorhidequeue(false, true);
    },

    // 大小为0的文件的上传方法
    empty_file_upload(fileobj) {
      // 作特殊标记，以便让用户自己取消上传失败的文件
      let _this = this;
      fileobj.queueobj.iszerobytes = true;

      // 直接添加到数组中，不作任何处理。
      _this.extdata.uploadQueueData.push(fileobj.queueobj);
    },

    // susobj.value==1 订阅
    // susobj.value!=1 取消订阅
    mul_suscribe(susobj) {
      // 准备参数
      let _this = this;
      var _organizeId = _this.$staticmethod._Get("organizeId");
      var _ActionType = susobj.value;
      var UserId = _this.$staticmethod.Get("UserId");
      var _arr_SuscribePara = [];
      var _arr_selects = _this.extdata._selectedobjs;

      for (var i = 0; i < _arr_selects.length; i++) {
        let ele = _arr_selects[i]
        var _i_SuscribePara = {
          Id:ele.FileId,
          IsFolder: ele.IsFolder
        }
        _arr_SuscribePara.push(_i_SuscribePara)
      }
      var postData = JSON.stringify({
        Subscribes: _arr_SuscribePara,
        UserId: UserId,
        ProjectId: _organizeId,
        IsCancel: susobj.value==1?false:true,
      });
      var _LoadingIns = _this.$loading({
        text: "处理中",
        target: document.getElementById("id_datamain")
        ,background:"rgba(240, 242, 245, 1)"
      });
      _this
        .$axios({
          method: "post",
          headers:{
            'Content-Type':'application/json'
          },
          url: `${window.bim_config.webserverurl}/api/v1/folder/subscribe?Token=${_this.$staticmethod.Get('Token')}`,
          data: postData
        })
        .then(x => {
          _LoadingIns.close();

          if(x.data.Ret == 1){
            _this.$message({
              message: x.data.Msg,
              type: "success"
            });
            this.GetAllFolderAndFileByProjectID_Suscribe()
          }else{
            _this.$message({
              message: x.data.Msg,
              type: "warning"
            });
          }

          // 重新加载数据，并让已订阅的显示出来
          // _this.loadsuscribeditems();
          // //重新加载数据，并让已订阅的显示出来
        })
        .catch(res => {
          console.warn(res);
          _this.$message.error("服务器错误");
          _LoadingIns.close();
        });
    },
    // 文件上传、切片
    async singlefile_ajax_chunckupload(chunkList,queueobj){
      try{

        let _this = this;
        if(chunkList.length === 0){
          _this.showorhidequeue(true, false);
          _this.refreshdata();
          return
        }
        if(_this.extdata.uploadQueueData.indexOf(queueobj) == -1){
          _this.extdata.uploadQueueData.push(queueobj);
        }
        let pool = [];
        let max = 1;
        let finish = 0//完成的数量
        let failList = []//失败的列表
        for (let i = 0; i < chunkList.length; i++) {
          let item = chunkList[i];
          var fd = new FormData();
          fd.append("ProjectId", this.projectId);
          fd.append("FolderId",_this.v_Id);
          fd.append("FileName", item.OldFileName);//上传的原始文件名称(包括后缀）
          fd.append("UserId", _this.userId);
          fd.append("FileKey", item.FileKey); //前端生成的唯一文件GUID标识
          fd.append("ChunkNumber", chunkList.length); //切分的文件个数
          fd.append("File", item.Files,item.filename); //切分的文件
          fd.append("FileSize", item.Files.size); //切分的文件大小,即切分大小基数
          fd.append("Index", item.Index+1); //切割后的文件序号

          let task = _this.$axios.post(
            `${window.bim_config.webserverurl}/api/v1/file/upload?Token=${_this.$staticmethod.Get('Token')}`,
            fd
          )
          task.then(x=>{
            if(x.data.Ret == 1){

              let index = pool.findIndex(t => t ===  task)
              pool.splice(index)
              // let sendUploadFilesMsg = []
              // if(x.data.FileId){
              //   // sendUploadFilesMsg.push(x)
              //   //   _this.sendUploadFilesMsg(sendUploadFilesMsg);
              // }else{
              //   _this.$message.success('上传成功');
              // }
              if(item.Index+1 == chunkList.length){
                _this.$message.success('上传成功');
              }

            }else{
              _this.begin_clearuploadqueue();
              _this.$message.error(x.data.Msg);
              return
            }

          }).catch(x=>{
            _this.$message.error(x.data.Msg);
            if(item.count < 5){
              item.count++
              failList.push(item)
            }

          }).finally(()=>{

            if(item.count>=5){
              let speed = _this.extdata.uploadQueueData.filter(item => item.FileKey == queueobj.FileKey&& item.FileName == queueobj.FileName)
              speed[0].css_upload = "上传失败"
              return
            }
            finish++
            let speed = _this.extdata.uploadQueueData.filter(item => item.FileKey == queueobj.FileKey&& item.FileName == queueobj.FileName)
            speed[0].css_upload = Math.floor(((finish - failList.length) / chunkList.length) * 100) + '%'

            if(finish == chunkList.length ){
              _this.singlefile_ajax_chunckupload(failList,queueobj)
            }
          })
          pool.push(task)
          if(pool.length === max){
            await Promise.race(pool)
          }
        }
      }catch(error){

      }

    },
    // 展开或折叠第一项
    collapseorexpand(doclisttype) {
      let _this = this;

      if (doclisttype != "doclib") {
        return;
      }
      // 设置当前的doclisttype
      _this.extdata.doclisttype = "doclib";
      // 设置展开或折叠
      _this.extdata._doc_showtree = !_this.extdata._doc_showtree;
    },

    // 从回收站删除
    deletefromrecycle(row) {
      let _this = this;
      // 弹出提示，确认彻底删除操作
      _this
        .$confirm("确认彻底删除？", "操作确认", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
        .then(x => {
          _this.remove_multiple(null);
        })
        .catch(x => {
          console.log(
            "用户取消了删除文件或文件夹的操作，从数据按钮中"
          );
        });
    },

    // 通过按钮移动多个文件
    mul_move() {
      // 显示"文件夹对话框"组件
      let _this = this;

      // 设置“文件夹对话框”组件的数据源
      _this.CompsFolderBrowser_data.bimcomposerId = _this.$staticmethod._Get(
        "organizeId"
      );

      // 设置标题
      // 如果只有一个文件，设置标题为单个文件
      if (_this.extdata._selectedobjs.length == 1) {
        var _movingfile = _this.extdata._selectedobjs[0];
        _this.CompsFolderBrowser_data.title = _movingfile.FileName;
        _this.CompsFolderBrowser_data.title_left = `移动【`;
        _this.CompsFolderBrowser_data.title_right = `】文件${_movingfile.FileSize == "0" ? "夹" : ""}到`;
      } else {
        _this.CompsFolderBrowser_data.title = "";
        _this.CompsFolderBrowser_data.title_left = "移动";
        _this.CompsFolderBrowser_data.title_right = "多个文件（夹）到";
      }

      // 显示"文件夹对话框"组件
      // CompsFolderBrowser_data_alreadyinFolderId
      _this.CompsFolderBrowser_data.visible = true;
    },

    context_moveto() {
      // 显示"文件夹对话框"组件
      let _this = this;
      // 设置“文件夹对话框”组件的数据源
      _this.CompsFolderBrowser_data.bimcomposerId = _this.$staticmethod._Get(
        "organizeId"
      );
      //
      var _movingfileid;
      if (_this.extdata._selectedobjs.length > 0) {
        _movingfileid = _this.extdata._selectedobjs[0].FileId;
      } else {
        return;
      }
      // 设置目标文件夹树窗口的标题
      var _movingfiles = _this.extdata.tableData.filter(
        x => x.FileId == _movingfileid
      );
      if (_movingfiles.length > 0) {
        var _movingfile = _movingfiles[0];
        _this.CompsFolderBrowser_data.title = _movingfile.FileName;
        _this.CompsFolderBrowser_data.title_left = `移动【`;
        _this.CompsFolderBrowser_data.title_right = `】文件${_movingfile.FileSize == "0" ? "夹" : ""}到`;
      }

      // 显示"文件夹对话框"组件
      // CompsFolderBrowser_data_alreadyinFolderId
      _this.CompsFolderBrowser_data.visible = true;
    },

    folderBrowser_oncancel() {
      this.CompsFolderBrowser_data.visible = false;
    },
    filehistory_oncancel() {
      this.CompsFileHistory_data.visible = false;
    },
    // 文件夹树打开后，选择文件夹并点击确定
    folderBrowser_onok(nodekey, alreadyinFolderId) {
      let _this = this;
      if(nodekey == undefined){
        _this.$message({
          message: `请选择目标文件夹`,
          type: 'warning'
        });
        EventBus.$emit("R_InitiateProblem",true);
        return false;
      }
      // 移动窗口确定按钮，弹出确定操作提示
      _this.$confirm('确定移动到该文件夹？', '移动确认', {
        confirmButtonText:'确认',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(x => {

        // 判断nodekey与alreadyinFolderId是否相同
        if (nodekey == alreadyinFolderId || (alreadyinFolderId == '' && nodekey == 0)){
          _this.$message({
            message: `已处于指定的文件夹中`,
            type: 'warning'
          });
          _this.CompsFolderBrowser_data.visible = false;
          return false;
        }
        let _arr = [];
        let _arr_selects = _this.extdata._selectedobjs;
        for (var i = 0; i < _arr_selects.length; i++) {
          let ele = _arr_selects[i]
          let _i = {
            Id:ele.FileId,
            IsFolder: ele.IsFolder
          }
          _arr.push(_i)
        }
        var data = JSON.stringify({
          SourceInput: _arr,
          TargetFolderId: nodekey,
          ProjectId: _this.$staticmethod._Get("organizeId"),
        });
        const findSomekey = _arr.find(item => item.Id === nodekey);
        if (findSomekey) {
          _this.$message({
            message: `已处于指定的文件夹中`,
            type: 'warning'
          });
          return false;
        }
        var _LoadingIns = _this.$loading({
          text: "操作中"
        });
        _this.$axios({
          method: "post",
          headers:{
            'Content-Type':'application/json'
          },
          url: `${window.bim_config.webserverurl}/api/v1/folder/move?Token=${_this.token}`,
          data: data
        })
          .then(x => {
            // 刷新当前页面
            if(x.data.Ret == 1){
              this.$message({
                message: "操作成功",
                type: "success"
              });
              _this.refreshdata();
            }else{
              this.$message({
                message: x.data.Msg,
                type: "warning"
              });
            }
            _this.CompsFolderBrowser_data.visible = false;
            _LoadingIns.close();
          })
          .catch(x => {
            console.warn(x);
            this.$message({
              message: "操作失败",
              type: "warning"
            });
            _this.CompsFolderBrowser_data.visible = false;
            _LoadingIns.close();
          });
      }).catch(x => {

      });
    },

    // 通过 sessionId 得到分享地址
    getShareLinkBySessionId(sessionId) {
      let _this = this;
      var urlfull = `${
        window.location.origin
      }${window.bim_config.hasRouterFile}/#/LinkShare/DocShare/Index?SessionId=${sessionId}`;
      return urlfull;
    },

    // 通过 sessionId 得到可直接打开的分享地址
    getShareLinkBySessionId_open(sessionId) {
      var urlfull = `${window.bim_config.hasRouterFile}/#/LinkShare/DocShare/Index?SessionId=${sessionId}`;
      return urlfull;
    },

    // 我的分享，右上角，复制链接
    copysharelink_auto(row){
      let _this = this;
      document.getElementById("id_forcopy").value = _this.getShareLinkBySessionId(row.ShardId);
      document.getElementById("id_forcopy").focus();
      document.getElementById("id_forcopy").select();
      document.execCommand("Copy");
      _this.$message({
        message: "复制成功",
        type: "success"
      });
    },

    // 复制链接
    copysharelink(row) {
      let _this = this;
      document.getElementById(`id_${row.ShardId}`).focus();
      document.getElementById(`id_${row.ShardId}`).select();
      document.execCommand("Copy");
      _this.$message({
        message: "复制成功",
        type: "success"
      });
    },

    // 分享的文件，打开链接
    opensharelink(row) {
      let _this = this;
      var _shareopenurl = _this.getShareLinkBySessionId_open(row.ShardId);
      window.open(_shareopenurl,"_blank");
    },

    // 取消分享
    breaksharing(row) {
      let _this = this;
      // 操作确认
      _this
        .$confirm("确认取消分享？", "操作确认", {
          confirmButtonText: "确认",
          cancelButtonText: "返回",
          type: "warning"
        })
        .then(x => {
          // 取消文件的分享
          var para = {
            shardId: row.Id,
            // userId: _this.$staticmethod.Get("UserId"),
          };
          var _LoadingIns = _this.$loading({
            text: "执行中"
          });
          _this
            .$axios({
              method: "post",
              url: `${window.bim_config.webserverurl}/api/v1/share/detail/delete?shardId=${row.ShardId}&Token=${_this.$staticmethod.Get('Token')}&shardDetailId=${row.Id}&userId=${_this.$staticmethod.Get("UserId")}`,
            })
            .then(x => {
              if(x.data.Ret == 1){
                _this.$message.success(x.data.Msg);
                _this.GetAllFolderAndFileByProjectID_MyShared();
              }else{
                console.warn(x);
              }
              _LoadingIns.close();
            })
            .catch(x => {
              console.warn(x);
              _LoadingIns.close();
            });
        })
        .catch(x => {
          console.warn(x);
        });
    },
    // 某节点展开时，加载子节点的途径
    loadNodeChild(node, resolve) {
      if(!node.data.Id) return
      // 设置必要的参数

      let _this = this;
      // 请求接口，获取当前正在展开的节点的子节点数据
      _this
        .$axios({
          method: "get",
          url: `${window.bim_config.webserverurl}/api/v1/folder/summary-tree?projectId=${_this.projectId}&Token=${_this.token}&parentId=${node.data.Id}&userId=${_this.userId}`
        })
        .then(x => {
          if (x.data.Ret == 1 && x.data.Data.length > 0) {
            var _arrgot
            // 默认情况是加载当前正在展开的节点内的全部文件及文件夹，此处过滤出文件夹

            _arrgot = x.data.Data;
            // var _arrgot = x.data.Data;
            for (var i = 0; i < _arrgot.length; i++) {
              // 对于每一个即将拼接的新子文件夹，设置及 chilren 使其带有展开按钮图标
              _arrgot[i].Children = [];

              // 使该文件夹项带有文件夹图标
              _arrgot[i].classname = "";
              _arrgot[i].isLeaf = !_arrgot[i].HasChildren;

              // 设置其 chain 属性。
              // 记录了从根级节点到当前节点的路径（有序节点数组）
              _arrgot[i].chain = [];

              // 添加父节点的 chain
              if (node.data.chain && node.data.chain.length) {
                for (var j = 0; j < node.data.chain.length; j++) {
                  _arrgot[i].chain.push(node.data.chain[j]);
                }
              }

              // 添加自己节点的 chain item
              _arrgot[i].chain.push({
                Id: _arrgot[i].Id,
                FolderName: _arrgot[i].FolderName,
                isLeaf:!_arrgot[i].HasChildren
              });
            }
            // 将获取到并处理过的数据加载到正在展开的节点上
            resolve(_arrgot);
          } else {
            console.warn(x);
          }
        })
        .catch(x => {
          console.warn(x);
        });

    },

    // 加载树的第一级文件夹(in mounted)
    load_tree_firstlevelfolder(recycle,newdirid) {

      let _this = this;
      // 调用 ajax 获取第一层的文件夹

      var _LoadingIns = _this.$loading({
        text: "加载中",
        target: document.getElementById("id_datamain")
        ,background:"rgba(240, 242, 245, 1)"
      });
      _this
        .$axios({
          method: "get",
          url: `${window.bim_config.webserverurl}/api/v1/folder/summary-tree?projectId=${_this.projectId}&Token=${_this.token}&parentId=0&userId=${_this.userId}`
        })
        .then(x => {

          if (x.data.Ret == 1 && x.data.Data.length > 0){
            _LoadingIns.close();
              _this.v_Id = x.data.Data[0].Id
            _this.firstLevelFolderData = x.data.Data[0];

            let upload = x.data.Data[0].Auth.Upload
            // 初始化在这里设置上传文件权限
            _this.extdata.nonewauth = !upload;

            _this.$axios
              .get(`${window.bim_config.webserverurl}/api/v1/folder/summary-tree?projectId=${_this.projectId}&Token=${_this.token}&parentId=${x.data.Data[0].Id}&userId=${_this.userId}`)
              .then(y => {
                if(y.data.Ret == 1){
                  var _arrgot = y.data.Data
                  for (let i = 0; i < _arrgot.length; i++) {
                    _arrgot[i].isLeaf = !_arrgot[i].HasChildren;
                  }
                  _this.treedata.items = _arrgot;
                  _this.extdata._doc_showtree = true;

                  // 赋值数据
                  _this.tableDatapatch(y.data.Data);
                  if (_this.extdata._openstacks.length > 0) {
                    _this.open_current_history(
                      _this.extdata._openstacks.length - 1,
                      undefined,
                      newdirid
                    );
                    _this.doc_file_folder(this.v_Id)
                  } else {
                    // 未打开任何文件夹下，处理根级目录
                    // _this.resetpath(undefined, newdirid);
                    // 赋值面包屑
                    _this.extdata._openstacks = [];
                  }
                  if(!recycle){
                    _this.doc_file_folder(this.v_Id)
                  }
                  // _this.extdata.nonewauth = false;
                }else{
                  _this.doc_file_folder(this.v_Id)
                }
                _LoadingIns.close();
              }).catch(x => {
              console.warn(x);
              _LoadingIns.close();
            });
          }else{
            _this.$message({
              message: x.data.Msg,
              type: "warning"
            });
            _LoadingIns.close();
          }
        })
        .catch(x => {
          console.warn(x);
          // _LoadingIns.close();
        });

    },

    // 单击节点 回调
    node_click(itemi, node, comp) {
      console.log('item',itemi)
      console.log('this.$refs.tree',this.$refs.tree)
      this.sharedoclisttype = false
      this.breadList = []; //初始化
      this.getTreeNode(this.$refs.tree.getNode(itemi.Id));
      let _this = this;
      _this.collapseoptions();
      _this.extdata._selectedobjs = [];
      _this.extdata.doclisttype = "doclib";
      _this.v_Id = itemi.Id
      _this.doc_file_folder(itemi.Id)
    },
    getTreeNode(node){
      let _this = this;
      if (node&&node.label) {
        _this.breadList.unshift({ FileId: node.key, FileName: node.label });
        const uniqueArr = this.breadList.reduce((acc, current) => {
          const x = acc.find(item => item.FileId === current.FileId);
          if (!x) {
            return acc.concat([current]);
          } else {
            return acc;
          }
        }, []);

        _this.getTreeNode(node.parent);
        _this.GetAllFolderAndFileByProjectID({
          LikeName: "",
          NormalOrDrawings: "Normal",
          FolderID: node.key,
          _if_dir_change_suc_openstacks: uniqueArr,
        });
      }
    },
    //左侧树结构递归查找数据
    function_recursive(data,target_id) {
      let target_data = {}
      if (!Array.isArray(data) || !data.length) {
        return target_data
      }
      // 递归过程
      let get_target_data = item => {
        if (item.Id == target_id) {
          target_data = item
        } else {
          if (item.Children && item.Children.length) {
            for (let i = 0; i < item.Children.length; i++) {
              if (Object.keys(target_data).length) break // 如果已经找到了目标数据，记得break
              get_target_data(item.Children[i])
            }
          }
        }
      }

      // for循环传入的data数据并开始递归
      for (let i = 0; i < data.length; i++) {
        if (Object.keys(target_data).length) break // 同上
        get_target_data(data[i])
      }
      return target_data
    },

    // 展开节点 回调
    node_expand(itemi, node, comp) {
      itemi.classname = "icon-interface-folder";
    },

    // 收起节点 回调
    node_collapse(itemi, node, comp) {
      itemi.classname = "icon-interface-unfolder";
    },

    // 发送文档分享邮件
    func_sharing_sendmail() {
      let _this = this;

      // 当前邮箱判断
      var _email = _this.$staticmethod.Get("Email");
      if (!_email || _email.trim() == '') {
        _this.$message.error("当前用户没有绑定邮箱地址，请绑定后再次尝试");
        return false;
      }

      var _jsonparaorigin = {
        Token: _this.$staticmethod.Get("Token"),
        Receivers: _this.sharingdata._sharing_inputemail,
        SenderEmail: _this.$staticmethod.Get("Email"),
        SenderRealName: _this.$staticmethod.Get("RealName"),
        IncludeSenderEmail: _this.sharingCopingToMe ? 1 : 0,
        EmailTitle: "文档分享",
        SharedDocLink: _this.extdata._sharing_url,
        SharedDocPwd: _this.extdata._sharing_ispublic?'':_this.extdata._sharing_pwd,
        Telephone: "4006506225",
        Email: "<EMAIL>",
        PlatFormName: "BIMe协作平台",
        SharedContent: _this.sharingdata._sharing_emailcontent,
        SharedEndTime: _this.extdata._sharing_deadline
      };
      var _LoadingIns = _this.$loading({
        text: "发送中"
      });
      let postdata = _this.$qs.stringify(_jsonparaorigin);
      _this
        .$axios({
          method: "post",
          url: `${
            _this.$configjson.webserverurl
          }/api/User/Document/SendShareEmail`,
          data: postdata
        })
        .then(x => {
          if (x.status == 200 && x.data.Ret > 0) {
            _this.$message({
              message: "发送成功",
              type: "success"
            });
          } else {
            console.warn(x);
            _this.$message({
              message: "发送失败，请检查所有目标邮箱地址是否正确",
              type: "warning"
            });
          }
          _LoadingIns.close();
        })
        .catch(x => {
          console.warn(x);
          _LoadingIns.close();
        });
    },

    // 复制分享链接
    func_sharing_linkcopy() {
      let _this = this;
      _this.$refs.rf_sharingurlcreated.select();
      document.execCommand("Copy");
      _this.$message({
        message: "复制成功！",
        type: "success"
      });
    },

    // 分享文件（夹）角色成员-索引-项点击
    func_sharing_kwuseritemclick(user) {
      let _this = this;
      _this.func_sharing_addmemtosel([user], user.UserId);
      _this.sharingdata._sharing_showkwsuggest = false;
      _this.sharingdata._sharing_inputkw = "";
    },

    // suggestclose
    suggestclose() {
      let _this = this;
      _this.sharingdata._sharing_showemailsuggest = false;
      _this.sharingdata._sharing_showkwsuggest = false;
    },

    // 分享文件发邮件时，角色成员输入
    userkeyup(ev) {
      let _this = this;
      if (ev.keyCode == "8") {
        return;
      }

      // 参数
      var _keyWordStart = _this.extdata._membersearchkeyvalue;
      var _OrgId = _this.$staticmethod._Get("organizeId");
      var _Token = _this.$staticmethod.Get("Token");
      var _DeleteMark = "0";
      var _EnabledMark = "1";

      _this
        .$axios({
          method: "get",
          url: `${
            _this.$configjson.webserverurl
          }/api/User/Role/GetUsersByRealNameStart?keyWordStart=${_keyWordStart}&OrgId=${_OrgId}&Token=${_Token}&DeleteMark=${_DeleteMark}&EnabledMark=${_EnabledMark}`
        })
        .then(x => {
          if (x.status != 200 || x.data.Ret <= 0) {
            console.warn(x);
          } else {
            if (x.data.Data.length > 0) {
              _this.sharingdata._sharing_showkwsuggest = true;
              _this.sharingdata._sharing_showkwsuggest_items = x.data.Data;
            } else {
              _this.sharingdata._sharing_showkwsuggest = false;
            }
          }
        })
        .catch(x => {
          console.warn(x);
        });
    },

    // 分享文件发邮件，角色成员选择人员完毕
    func_sharing_setemails() {
      let _this = this;
      var _emails = [];
      for (var user of _this.extdata._userselected) {
        if (user && user.Email != "") {
          _emails.push(user.Email);
        }
      }
      _this.emailsugitem_click(_emails);
      _this.func_sharing_pageswitch("sharingmain");
    },

    // 分享文件（夹）输入邮件地址文本框动态加载提示框内数据项点击事件
    emailsugitem_click(emails) {
      let _this = this;

      // 先将已存在于邮件地址输入框中的值按分号分隔为数组
      var _lastindexofsplitter = _this.sharingdata._sharing_inputemail.lastIndexOf(
        ";"
      );
      _this.sharingdata._sharing_inputemail = _this.sharingdata._sharing_inputemail.substr(
        0,
        _lastindexofsplitter
      );
      var _emailarr = _this.sharingdata._sharing_inputemail
        .split(";")
        .filter(x => x && x.trim() != "");

      // 确保新值处于最后一个位置
      var _emailarr_excepttarget = _emailarr.filter(x => emails.indexOf(x) < 0);
      for (var em of emails) {
        _emailarr_excepttarget.push(em);
      }

      // 得到应该赋予的新邮件地址字符串
      var strvalue = "";
      for (var em of _emailarr_excepttarget) {
        strvalue += `${em};`;
      }
      _this.sharingdata._sharing_inputemail = strvalue;

      // 隐藏推荐下拉框
      _this.sharingdata._sharing_showemailsuggest = false;
      _this.$refs.rf_emailinputting.focus();
    },

    // 分享文件（夹）输入邮件地址文本框动态加载提示框内数据
    emailkeyup() {
      let _this = this;

      // 根据最后一个分号获取搜索的依据
      var _lastsplitterindex = _this.sharingdata._sharing_inputemail.lastIndexOf(
        ";"
      );
      var _tosearch = _this.sharingdata._sharing_inputemail.substr(
        _lastsplitterindex + 1
      );
      var _tlk = _this.$staticmethod.Get("Token");

      // 请求 webserverurl
      _this
        .$axios({
          method: "get",
          url: `${
            _this.$configjson.webserverurl
          }/api/User/User/GetUserEmails?keyWordStart=${_tosearch}&Token=${_tlk}`
        })
        .then(x => {
          if (x.status == 200 && x.data.Ret > 0) {
            // 推荐的邮件地址条数大于0，显示，否则隐藏
            _this.sharingdata._sharing_showemailsuggest =
              x.data.Data.length > 0;
            _this.sharingdata._sharing_showemailsuggest_items = x.data.Data;
          } else {
            console.warn(x);
          }
        })
        .catch(x => {
          console.warn(x);
        });
    },

    // 判断指定角色的人是否全部处于已选人数组中
    func_sharing_testallrolememselected(roleId) {
      // 得到指定 roleId 的 Users
      let _this = this;
      var _role = _this.extdata._rolelist.filter(x => x.RoleId == roleId);
      if (_role.length > 0) {
        var _users = _role[0].Users;
        if (_users.length == 0) {
          return false;
        }

        // 判断 _users 是否全部处于 _this.extdata._userselected 中
        // 假设此次勾选角色获取到的所有相关人员，都被包含到了 _userselected 中
        var _allin_userselected = true;
        for (var i = 0; i < _users.length; i++) {
          if (
            _this.extdata._userselected.findIndex(
              x => x.UserId == _users[i].UserId
            ) < 0
          ) {
            // 有人是不存在于 _userselected 中的
            _allin_userselected = false;
            break;
          }
        }
        return _allin_userselected;
      } else {
        return false;
      }
    },

    // 直接批量修改某项目角色下的全体人员是否是已选状态
    func_sharing_switchrole(roleId) {
      let _this = this;
      _this.func_sharing_loadmembyrole(roleId, function(list) {
        // 判断 list 是否全部存在于 _userselected 中 ，如果不是，则将全部 list 填充到 _userselected 中
        // ，否则将全部的人从 _userselected 中移除
        if (list.length < 0) {
          return;
        }

        // 假设此次勾选角色获取到的所有相关人员，都被包含到了 _userselected 中
        var _allin_userselected = true;
        for (var i = 0; i < list.length; i++) {
          if (
            _this.extdata._userselected.findIndex(
              x => x.UserId == list[i].UserId
            ) < 0
          ) {
            // 有人是不存在于 _userselected 中的
            _allin_userselected = false;
            break;
          }
        }

        if (_allin_userselected) {
          // 将这些人全部从 _userselected 中移除
          for (var i = 0; i < list.length; i++) {
            _this.func_sharing_removememsel(list[i].UserId);
          }
        } else {
          // 将这些人全部加入到 _userselected 中
          for (var i = 0; i < list.length; i++) {
            _this.func_sharing_addmemtosel(list, list[i].UserId);
          }
        }
      });
    },

    // 将指定集合中的指定byUserId的人员加入到 _userselected 中
    // 其中会自动判断是否已存在了
    func_sharing_addmemtosel(source, byUserId) {
      let _this = this;

      // 有这个人就不加
      var _ifhasindex = _this.extdata._userselected.findIndex(
        x => x.UserId == byUserId
      );
      if (_ifhasindex >= 0) {
        return;
      }

      var _topushs = source.filter(x => x.UserId == byUserId);
      if (_topushs.length > 0) {
        _this.extdata._userselected.push(_topushs[0]);
      }
    },

    // 把某人从已选列表中移除
    func_sharing_removememsel(userId) {
      let _this = this;
      _this.extdata._userselected = _this.extdata._userselected.filter(
        x => x.UserId != userId
      );
    },

    // 点击邮件备选接收者的一个人员
    // 点击复选框与点击（人员）整体区域效果相同
    func_sharing_emailuseritemclick(userId) {
      let _this = this;
      // 如果已选人员列表中不包括userId这个人，则将其包括进来，否则剔除出去
      var _has =
        _this.extdata._userselected.findIndex(x => x.UserId == userId) >= 0;
      if (_has) {
        // 将此人剔除出去
        _this.func_sharing_removememsel(userId);
      } else {
        // 将此人加进来
        var _topushs = _this.extdata._userlist.filter(x => x.UserId == userId);
        if (_topushs.length > 0) {
          _this.extdata._userselected.push(_topushs[0]);
        }
      }
    },

    // 根据 func_sharing_loadmembyrole 根据 RoleId 加载人员
    func_sharing_loadmembyrole(roleId, userlistcallback) {
      let _this = this;
      var _RoleId = roleId;
      var _TheRoles = _this.extdata._rolelist.filter(x => x.RoleId == roleId);
      if (_TheRoles.length > 0) {
        _this.extdata._userlist = _TheRoles[0].Users;
        if (userlistcallback) {
          userlistcallback(_this.extdata._userlist);
        }
      }
    },

    // 跳转到角色成员对话框
    func_sharing_pageswitch_tosharingmember() {
      let _this = this;
      // api paras
      var _OrgId = _this.$staticmethod._Get("organizeId");
      var _Token = _this.$staticmethod.Get("Token");
      var _DeleteMark = "0";
      var _EnabledMark = "1";
      // post
      var _LoadingIns = _this.$loading({
        text: "加载中"
      });
      _this
        .$axios({
          method: "get",
          url: `${
            _this.$configjson.webserverurl
          }/api/User/Role/GetUsersByOrgId?OrgId=${_OrgId}&Token=${_Token}&DeleteMark=${_DeleteMark}&EnabledMark=${_EnabledMark}`
        })
        .then(x => {
          if (x.status == 200 && x.data.Ret > 0) {
            // 加载完角色后，如果角色个数大于0，则自动加载第一角色中的人
            _this.extdata._rolelist = x.data.Data;
            _this.func_sharing_pageswitch("sharingmember");

            if (_this.extdata._rolelist.length > 0) {
              _this.extdata._userlist = _this.extdata._rolelist[0].Users;
            }
          } else {
            console.warn(x);
          }
          _LoadingIns.close();
        })
        .catch(x => {
          console.warn(x);
          _LoadingIns.close();
        });
    },

    func_sharing_switch_hasDownload() {
      let _this = this;
      if (_this.hasDownload) {
        _this.hasDownload = false;
      } else {
        _this.hasDownload = true;
      }
    },

    // 点击修改权限的确定时，需要将 extdata 的相关权限值写入 cachedata，并生成新链接和二维码
    // 与 switch 的地方有重复！
    func_sharing_authok() {
      // 直接用内存中的 extdata 中的字段生成新 url 及 二维码，不管 cache
      let _this = this;
      var _docids = _this.extdata._showcontextbtns_fileid;
      _this.context_share(_docids);
    },

    // 点击修改权限的取消时，需要将 cachedata 的值 写回 extdata
    func_sharing_authcancel() {
      // 恢复原有的权限！原有的权限，是在打开时，由内存中的值写入给 cachedata 的，这里是使用cache的唯一的地方
      let _this = this;
      _this.extdata._sharing_ispublic = _this.cachedata._sharing_ispublic;
      _this.extdata._sharing_deadline = _this.cachedata._sharing_deadline;
      _this.extdata._sharing_auths = _this.$staticmethod.DeepCopy(
        _this.cachedata._sharing_auths
      );

      _this.func_sharing_pageswitch("sharingmain");
    },

    func_sharing_setpublic(ispublic) {
      this.extdata._sharing_ispublic = ispublic;
    },
    func_sharing_deadline(num) {
      this.extdata._sharing_deadline = num;
    },
    // 弹出 sharingauth
    func_sharing_pageswitchto_sharingauth() {
      let _this = this;
      _this.cachedata._sharing_deadline = _this.extdata._sharing_deadline;
      _this.cachedata._sharing_ispublic = _this.extdata._sharing_ispublic;
      _this.cachedata._sharing_auths = _this.$staticmethod.DeepCopy(
        _this.extdata._sharing_auths
      );
      _this.func_sharing_pageswitch("sharingauth");
    },
    func_sharing_pageswitch(sharingwhat) {
      let _this = this;
      _this.extdata._sharingshowtype = sharingwhat;
    },
    func_sharing_close() {
      let _this = this;
      _this.extdata._showshare = false;
    },
    func_sharing_sendbyemail() {
      let _this = this;
      _this.extdata._sharingmain_emailing = true;
    },
    contextitemclick(val, isdis, ev) {
      //debugger;
      if (isdis) {
        return;
      }
      let _this = this;
      switch (val) {
        case "Open":
          _this.context_open();
          break;
        case "DownLoad":
        {
          // 判断如果有文件夹，或选中大于1个项，走批量下载
          var ifhasdir = _this.extdata._selectedobjs.filter(x => x.FileSize == '0').length > 0;
          if (_this.extdata._selectedobjs.length > 1 || ifhasdir) {
            // 走批量下载
            _this.$confirm("您选中了多个文件，或包含至少一个文件夹，是否进行打包下载？", "打包下载", {
              cancelButtonText:'取消',
              confirmButtonText:'确定',
              type: 'warning'
            }).then(x => {
              _this.patchdownload();
            }).catch(x => {

            });
          } else {
            _this.begin_context_download();
          }
        }
          break;
        case "Suscribe":
          // 订阅或取消订阅
          var _suspara = _this.mul_suscribe_btnobj;

          _this.mul_suscribe(_suspara);
          break;

        // 发起流程
        // case "createflow":
        //   _this.func_createflow();
        //   break;

        case "Share":
          _this.begin_context_share();
          break;
        case "moveto":
          _this.context_moveto();
          break;
        case "Delete":
          _this.begin_remove_multiple(ev);
          break;
        case "Update":
          var _renamingFileId;
          if (_this.extdata._selectedobjs.length > 0) {
            _renamingFileId = _this.extdata._selectedobjs[0].FileId;
          } else {
            return;
          }

          // 指定 _cachedirname
          var arr = _this.extdata.tableData.filter(
            x => x.FileId == _renamingFileId // _this.extdata._showcontextbtns_fileid
          );
          if (arr.length > 0) {
            var _setdirnameinit = arr[0].FileName;
            _this.extdata._cachedirname = _setdirnameinit;
            // 缓存重命名前的文件夹名
            _this.extdata._cachedirname_renameesc = _setdirnameinit;
          }

          // 修改“正在重命名的FileId”，为此次上下文的弹出相关FileId
          _this.extdata._showcontextbtns_fileid_renaming = _renamingFileId; //_this.extdata._showcontextbtns_fileid;
          _this.extdata.alreadycancelnameedit = false;

          // 收回上下文菜单
          _this.closeallpopup();
          break;
        case "History":
          _this.context_hispatches();
          break;
        case "correlationModel":
          _this.showModelList();
          break;
        case "uncorrelationModel":
          _this.unrelmodel();
          break;
        default:
          console.log(val);
          break;
      }
      _this.closeallpopup();
    },
    // 获取项目的默认阶段和全景图的默认标签ID，在调用转换接口
    getModelPhaseList() {
      this.$axios
        .get(
          `${window.bim_config.webserverurl}/api/User/User/GetUserMenuTree?organizeId=${this.$staticmethod._Get("organizeId")}&token=${this.$staticmethod.Get("Token")}&parentId=0`
        )
        .then((res) => {
          if (res.data.Ret == 1) {
            let _data = res.data.Data;
            let _ind = _data.find(i=>i.MenuCode=='MXGL')
            let _c_ind =  _ind.Children.find(i=>i.MenuName=='模型菜单')
            let _cc_ind = _c_ind.Children.find(i=>i.MenuName=='默认阶段')
            this.conversionModelPhaseValue = _cc_ind.BusinessCode;
            this.getKrpanoTagOption();
          } else {
            this.$staticmethod.debug(x);
          }
        })
        .catch((err) => {});
    },
    getKrpanoTagOption(){
      let _organizeId =  this.$staticmethod._Get('organizeId')
      this.$axios
        .get(
          `${window.bim_config.webserverurl}/api/Panorama/Label/GetList?OrganizeId=${_organizeId}&Token=${this.$staticmethod.Get('Token')}`
        )
        .then((res) => {
          if (res.data.Ret == 1) {
            let _data = res.data.Data;
            let _ind = _data.findIndex(i=>i.LabelName=='默认标签')
            this.conversionLabelId = _data[_ind].LabelId;
            this.getAllFilType()
          } else {
            this.$staticmethod.debug(x);
          }
        });
    },
    // 转换
    conversionModel(row){
      let _this = this;
      if(row.Switchstate == '转换中' || row.Switchstate == '转换成功') return
      _this.extdata._showcontextbtns_fileid = row.FileId;
      _this.extdata._selectedobjs = _this.extdata.tableData.filter(
        x => x.FileId == row.FileId
      );
      // console.log(_this.extdata._selectedobjs,'==选中')
      // 循环当前的选中FileExtension，判断不是当前这些格式，不能转换

      // 判断当前选中文件是否是支持转换文件格式
      if( this.conAllType.indexOf(_this.extdata._selectedobjs[0].FileExtension) == -1 ){
        this.$message.warning('当前选中文件无法转换，请重新选择')
      }
      var _LoadingIns = _this.$loading({
        text: "处理中"
      });
      let Fileid = row.FileId;
      let projectId = _this.$staticmethod._Get("organizeId"),
        UserId = _this.$staticmethod.Get("UserId");

      let _url = `${window.bim_config.webserverurl}/api/v1/file/byfileid?Fileid=${Fileid}&Token=${_this.$staticmethod.Get('Token')}&ProjectId=${projectId}&UserId=${UserId}&labelId=${this.conversionLabelId}&ModelPhaseValue=${this.conversionModelPhaseValue}&CfgItemCode=NEWWEB_PROBIMCN_FLOW`
      this.$axios
        .get(_url)
        .then(res=>{
          _LoadingIns.close();
          if(res.data.Ret == 1){
            this.$message.success(res.data.Msg)
            _this.refreshdata();
          }
        })
        .catch(err=>{
          _LoadingIns.close();
        })
    },
    // 获取所有支持转换的格式
    getAllFilType(){
      this.$axios
        .get(`${window.bim_config.webserverurl}/api/v1/file/byfiletype?Token=${this.$staticmethod.Get('Token')}`)
        .then(res=>{
          if(res.data.Ret == 1){
            this.conAllType = res.data;
          }else{
            this.conAllType = [];
          }
        })
        .catch(err=>{

        })
    },
    // 点击查询是否正在转换
    getFileVersionInfo(Fileid){
      this.$axios({
        method: 'get',
        url: `${window.bim_config.webserverurl}/api/v1/file/byfilemodelid?Fileid=${Fileid}&Token=${this.$staticmethod.Get('Token')}`
      })
        .then(x => {
          if (x.data.Ret === 1) {
            let _data = x.data.Data[0];
            if(_data.IfModel){
              if(_data.ModelID.length > 0){
                this.preopenmodel(row.ModelBase.ModelID,row.FileName,row.ModelBase.VersionNO)
                return;
              }
            }else{
              this.$message({
                message: '当前文件正在转换',
                type: 'warning'
              })
              return;
            }
          }else {
            this.$message.error(x.data.Msg)
            return;
          }
        })
        .catch(x => {
        })
    },
    // 禁用全部右键按钮
    contextmenubtns_alldis() {
      let _this = this;
      for (var i = 0; i < _this.contextmenubtns.length; i++) {
        _this.contextmenubtns[i].disable = 1;
      }
    },

    // 默认右键按钮可用性
    contextmenubtns_default(fileId) {
      let _this = this;

      let TheFiles  = this.function_recursive(_this.treedata.items,fileId)
      let TheAuth
      // 判断要操作的是文件还是文件夹
      let _IsFolderIndex = _this.extdata.tableData.filter(x => x.FileId == fileId)
      let _IsFolder = _IsFolderIndex[0].IsFolder;


      // _IsFolder ? _this.getAuthFun(fileId) :_this.getAuthFun()


      if(typeof(TheFiles.Auth)=="undefined"){
        if(_this.extdata._openstacks.length == 0){
          TheFiles = this.firstLevelFolderData.Auth
        }else{
          TheFiles  = this.function_recursive(_this.treedata.items,this.v_Id)
          TheAuth = TheFiles.Auth;
        }
      }else{
        TheAuth = TheFiles.Auth;
      }

      var hasRelationModel = _IsFolderIndex[0].ModelId.length;

      // 判断是文件还是文件夹，如果是文件夹，去除历史及关联以及取消关联，以及发起流程
      // 如果是文件的话，判断是否已关联过模型
      if (_IsFolder) {
        _this.contextmenubtns = _this.contextmenubtns
          .filter(x => x.value != 'History' && x.value != 'correlationModel' && x.value != 'uncorrelationModel'
            && x.value != 'createflow');
      } else {
        _this.contextmenubtns = _this.$staticmethod.DeepCopy(_this.contextmenubtns_origin);

        // 如果没有关联模型的话，将取消关联按钮移除
        if (hasRelationModel.length > 0){
          _this.contextmenubtns = _this.contextmenubtns.filter(x => x.value != 'uncorrelationModel');
        }
      }
      // //判断是文件还是文件夹，如果是文件夹，去除历史及关联以及取消关联
      if(TheAuth){
        for (let [key, value] of Object.entries(TheAuth)) {
          for (var i = 0; i < _this.contextmenubtns.length; i++) {

            // 先将所有按钮权限置为无权限
            _this.contextmenubtns[i].disable = 0;

            // 用于存储用来翻译权限
            var tempactionname = _this.contextmenubtns[i].value;
            if (tempactionname == 'moveto') {
              tempactionname = 'Delete';
            }

            if (tempactionname == 'correlationModel'){
              tempactionname = "Update";
            }

            if (tempactionname == 'uncorrelationModel'){
              tempactionname = "Update";
            }
            if (key == tempactionname && value == "true") {
              _this.contextmenubtns[i].disable = 1;
            }
          }
        }
      }
      _this.extdata._showcontextbtns = true;
      return
    },

    // 仅支持多项的右键按钮可用性
    contextmenubtns_multipleonly() {
      let _this = this;
      for (var i = 0; i < _this.contextmenubtns.length; i++) {
        _this.contextmenubtns[i].disable = 0;

        // 额外
        if (_this.contextmenubtns[i].value == "auth") {
          _this.contextmenubtns[i].disable = 1;
        }
        if (_this.contextmenubtns[i].value == "History") {
          _this.contextmenubtns[i].disable = 1;
        }
        // 不支持多选的
        // -----------
        var notSupportMuls = ["Open", "Download"];
        if (notSupportMuls.includes(_this.contextmenubtns[i].value)) {
          _this.contextmenubtns[i].disable = 1;
        }

        // delete and moveto auths
        if (_this.contextmenubtns[i].value == "Delete") {
          let TheAuth;
          if(_this.extdata._openstacks.length == 0){
            TheAuth = _this.firstLevelFolderData.Auth
          }else{
            let TheFiles  = this.function_recursive(_this.treedata.items,_this.v_Id)
            TheAuth = TheFiles.Auth;
          }
          _this.contextmenubtns[i].disable = TheAuth['Delete']==false?1:0;
        }
        if (_this.contextmenubtns[i].value == "moveto") {
          let TheAuth;
          if(_this.extdata._openstacks.length == 0){
            TheAuth = _this.firstLevelFolderData.Auth
          }else{
            let TheFiles  = this.function_recursive(_this.treedata.items,_this.v_Id)
            TheAuth = TheFiles.Auth;
          }
          _this.contextmenubtns[i].disable = TheAuth['Delete']==false?1:0;
        }

      }
      _this.extdata._showcontextbtns = true;
    },

    // 开始显示“更多”按钮
    begin_mul_showmore(ev) {
      let _this = this;

      // 判断选中的是一个还是多个，从而设置个别按钮的可用性
      if (_this.extdata._selectedobjs.length == 0) {
        // 所有按钮都不可用
        _this.contextmenubtns_alldis();
      } else if (_this.extdata._selectedobjs.length == 1) {
        _this.contextmenubtns_default(_this.extdata._selectedobjs[0].FileId);
      } else {
        _this.contextmenubtns_multipleonly();
      }
      // //判断选中的是一个还是多个，从而设置个别按钮的可用性

      // 修改 content 订阅按钮的文本
      var _suspara = _this.mul_suscribe_btnobj;
      var _susbtns = _this.contextmenubtns.filter(x => x.value == "Suscribe");
      if (_susbtns.length > 0) {
        _susbtns[0].text = _suspara.name;
      }

      // 更多
      _this.set_context_pos(_this.docrightmenu_w, _this.docrightmenu_h, ev);
    },

    show_context_menu(row, ev) {

      ev.stopPropagation();
      let _this = this;
      // _this.getAuthFun();

      _this.extdata._showcontextbtns_fileid = row.FileId;

      // 先设置可用性
      _this.contextmenubtns_default(row.FileId);

      // 再修改“订阅”按钮的文本
      // 得到“订阅”菜单项
      var _btns = _this.contextmenubtns.filter(x => x.value == "Suscribe");
      // 判断当前文件是否已经订阅，如果是，显示“取消订阅”，否则显示取消订阅
      if (_btns.length > 0) {
        if (_this.extdata._suscribed.indexOf(row.FileId) >= 0) {
          // 当前文件已经订阅了
          _btns[0].text = "取消订阅";
        } else {
          // 当前文件未订阅
          _btns[0].text = "订阅";
        }
      }

      _this.set_context_pos(_this.docrightmenu_w, _this.docrightmenu_h, ev);
      // //表格视图单数据，更多

      // 自动设当前数据为唯一选中项
      _this.extdata._selectedobjs = _this.extdata.tableData.filter(
        x => x.FileId == row.FileId
      );
    },

    // 判断文件（夹）名称是否正确
    testitemname(str) {
      if (!str || str.length == 0) {
        return false;
      } else if (str.indexOf("\\") >= 0 || str.indexOf("|") >= 0) {
        return false;
      } else if (str.indexOf("/") >= 0 || str.indexOf("?") >= 0) {
        return false;
      } else if (str.indexOf(":") >= 0 || str.indexOf("*") >= 0) {
        return false;
      } else if (
        str.indexOf('"') >= 0 ||
        str.indexOf("<") >= 0 ||
        str.indexOf(">") >= 0
      ) {
        return false;
      }
      return true;
    },

    // 符合文件夹不允许的按钮组合
    dirnameInvalidKeycode(ev) {
      if (ev.keyCode == "220") {
        // 不允许\及|
        return true;
      } else if (ev.keyCode == "191") {
        // 不允许/及?
        return true;
      } else if (ev.shiftKey && ev.keyCode == "186") {
        // 不允许:
        return true;
      } else if (ev.shiftKey && ev.keyCode == "56") {
        // 不允许*
        return true;
      } else if (ev.shiftKey && ev.keyCode == "222") {
        // 不允许"
        return true;
      } else if (ev.shiftKey && ev.keyCode == "188") {
        // 不允许<
        return true;
      } else if (ev.shiftKey && ev.keyCode == "190") {
        // 不允许>
        return true;
      }
      return false;
    },

    // 新建文件夹文本框按键
    // keyCode 参考：
    // ** 文件夹名不能包含的字符 **
    // \ 220
    // | 220
    // / 191
    // ? 191
    // : 186
    // * 106 ?
    // " 222
    // < 188
    // > 190
    // //** 文件夹名不能包含的字符 **
    // beginrename1Directory(scope.row.FileName, scope.row.FileId, $event)
    // 表格，或矩阵中的新建，或重命名文本框中的按键事件
    dirnamekeyup(fileid, filename, ev) {
      let _this = this;
      ev.stopPropagation();

      // 当输入新文件夹名时，需要恢复及重新赋予 _cachedirname，其过程需要指定索引
      // 新建文件夹时，索引为0，否则为...
      var _targetindex = 0;
      if (fileid != "-1") {
        _targetindex = _this.extdata.tableData.findIndex(
          x => x.FileId == fileid
        );
      }

      // 动态验证长度
      if (_this.extdata.tableData[_targetindex].FileName.length > 300) {
        _this.$message.error('文件或文件夹名称不得超过300个字符');
        _this.extdata.tableData[_targetindex].FileName =
          _this.extdata._cachedirname;
      }
      // //动态验证长度

      // 动态验证文件名，及处理回车和ESC
      if (_this.dirnameInvalidKeycode(ev)) {
        _this.$message.error('不允许包含以下特殊字符：\\|/?:*"<>');
        _this.extdata.tableData[_targetindex].FileName =
          _this.extdata._cachedirname;
        return;
      } else {
        _this.extdata._cachedirname =
          _this.extdata.tableData[_targetindex].FileName;
      }
      // //动态验证文件名，及处理回车和ESC

      // 根据 fileid 来决定回车键及 ESC 的动作

      if (fileid == "-1") {
        if (ev.keyCode == "13") {

          _this.begincreateDirectory(null);
          _this.extdata.alreadycancelnameedit = true;

        } else if (ev.keyCode == "27") {

          _this.cancelcreatingDir(null);
          _this.extdata.alreadycancelnameedit = true;
        }
      } else {
        if (ev.keyCode == "13") {

          _this.beginrenameItem(
            _this.extdata.tableData[_targetindex].FileName,
            fileid,
            null
          );
          _this.extdata.alreadycancelnameedit = true;

        } else if (ev.keyCode == "27") {

          // 需要在需要的地方设置为 false
          _this.extdata.alreadycancelnameedit = true;
          _this.cancelrenamedir(fileid, null);

        }
      }
      // //根据 fileid 来决定回车键及 ESC 的动作
    },

    // 我的订阅
    async GetAllFolderAndFileByProjectID_Suscribe(val) {
      let _this = this;
      try {
        _this.extdata._openstacks = [];
        let params = {
          projectId: this.projectId,
          userId: this.userId
        }
        const res = await this.$api.getfoldersubscribe(params)
        if(res.Ret == 1){
          _this.extdata.tableData = res.Data;
          _this.extdata._selectedobjs = [];
          _this.tableDatapatch(res.Data);
          if(val=='list'){
            let _toset_susval = [];
            for (var i = 0; i < res.Data.length; i++) {
              _toset_susval.push(res.Data[i].FileId);
            }
            //TO1DO: 加载已订阅的数据
            this.extdata._suscribed = _toset_susval; //res.Data;
          }
        }
        // your asynchronous code here
      } catch (error) {

      }

    },

    // 取消重命名文件夹，并恢复文件夹原来的名字
    cancelrenamedir(fileid, ev) {
      if (ev) {
        ev.stopPropagation();
      }
      let _this = this;
      var toresetarr = _this.extdata.tableData.filter(x => x.FileId == fileid);
      if (toresetarr.length > 0) {
        toresetarr[0].FileName = _this.extdata._cachedirname_renameesc;
      }
      _this.extdata._showcontextbtns_fileid = undefined;
      _this.extdata._showcontextbtns_fileid_renaming = undefined;

      _this.closeallpopup();
    },

    // 取消创建文件夹
    cancelcreatingDir(ev) {
      if (ev) {
        ev.stopPropagation();
      }
      let _this = this;
      _this.extdata.alreadycancelnameedit = true;
      var arr = _this.extdata.tableData.slice(1);
      _this.tableDatapatch(arr);
      _this.extdata.tableData = arr;
      // _this.getRelModelDataByMulFileId(); 不用。
    },

    // 新建文件夹后点击确定
    begincreateDirectory(ev) {
      if (ev) {
        ev.stopPropagation();
      }
      let _this = this;
      _this.extdata.alreadycancelnameedit = true;
      var dirname = _this.extdata.tableData[0].FileName;
      var dirname_suggest = dirname;

      // 文件夹名称为空或空白
      if (dirname.length == 0 || dirname.trim().length == 0) {
        _this.$message.error('文件夹名称不能为空');
        if (_this.extdata.showtype == 0) {
          // 表格
          document.getElementById('tableview_' + '-1').focus();
          document.getElementById('tableview_' + '-1').select(); // tableview_-1
        } else {
          // 矩阵
          document.getElementById('listview_' + '-1').focus();
          document.getElementById('listview_' + '-1').select();
        }
        _this.extdata.alreadycancelnameedit = false;
        return;
      }

      // 再一次验证输入的文件夹名称正确性
      if (!_this.testitemname(dirname)) {
        _this.$message.error("文件夹名称不正确！");
      } else if ((dirname_suggest = _this.getSuggestRepeatingName(dirname))) {
        // 判断有无重复名称，如果有，需要确认！
        console.log('新文件夹失去焦点-新');
        _this
          .$confirm(
            "已存在重复的名称，要命名为" + dirname_suggest + "吗？",
            "提示",
            {
              confirmButtonText: "确定",
              cancelButtonText: "取消",
              type: "warning"
            }
          )
          .then(() => {
            _this.createDirectory(dirname_suggest);
          })
          .catch(() => {
            //_this.cancelcreatingDir(null);
            // 根据当前是否为表格视图，来恢复文本框的焦点
            if (_this.extdata.showtype == 0) {
              // 表格
              document.getElementById('tableview_' + '-1').focus();
              document.getElementById('tableview_' + '-1').select(); // tableview_-1
            } else {
              // 矩阵
              document.getElementById('listview_' + '-1').focus();
              document.getElementById('listview_' + '-1').select();
            }
            _this.extdata.alreadycancelnameedit = false;
          });
        // //判断有无重复名称，如果有，需要确认！
      } else {
        _this.createDirectory(dirname);
      }
    },

    // 点击新建文件夹
    appendCreatingDir(ev) {
      let _this = this;
      ev.stopPropagation();

      // 隐藏新建文件夹按钮组
      _this.extdata._showfilebtns = false;
      _this.extdata.alreadycancelnameedit = false;

      // 判断如果有正在未创建的文件夹，则return
      var _creatingDir = _this.extdata.tableData.filter(x => x.FileId == -1);
      if (_creatingDir.length > 0) {
        return;
      }

      var _setdirnameinit = "新建文件夹";

      _this.extdata._cachedirname = _setdirnameinit;

      // 在 _this.extdata.tableData 前插入一项
      _this.extdata.tableData.unshift({
        CreateDate: "",
        CreateUserId: "",
        CreateUserName: "",
        DeleteMark: "",
        Description: "",
        DownloadCount: "",
        EnabledMark: "",
        FileExtensions: "",
        FileId: "-1", // 重要：-1表示新文件夹
        FileName: _setdirnameinit, // 需要做出判断
        FilePath: "",
        FileSize: "0",
        FileType: "Normal",
        FileVersion: "0",
        FolderID: "-1",
        IsShare: "",
        IsTop: "",
        ModifyDate: "",
        ModifyUserId: "",
        ModifyUserName: "",
        ProjectID: "",
        ShareCode: "",
        ShareLink: "",
        ShareTime: "",
        SortCode: ""
      });

      _this.$nextTick(() => {
        _this.$refs.doctable.bodyWrapper.scrollTop = 0;
      });
    },

    // 根据当前列表中的数据，得到一个推荐的新文件名
    // 无重名，返回 false
    // 有重名，返回不会重复的新文件名
    // testbyfileid，一定不为-1的文件id
    getSuggestRepeatingFileName(filename, testbyfileid) {
      let _this = this;
      var arrInThese = _this.extdata.tableData.filter(
        x => x.FileName == filename && x.FileId != testbyfileid
      );
      if (arrInThese.length == 0) {
        return false;
      }
      while(arrInThese.length > 0) {
        filename = _this.$staticmethod.ReNameRepeatFile(filename);
        arrInThese = _this.extdata.tableData.filter(
          x => x.FileName == filename && x.FileId != testbyfileid
        );
      }
      return filename;
    },

    // 根据当前列表中的数据，得到一个推荐的新文件夹名
    // 无重名，返回 false
    // 有重名，返回不会重复的新名字
    // testbyfileid: 默认为-1
    getSuggestRepeatingName(dirname, testbyfileid) {
      if (!testbyfileid) {
        testbyfileid = -1;
      }
      let _this = this;
      var arrInThese = _this.extdata.tableData.filter(
        x => x.FileName == dirname && x.FileId != testbyfileid
      );
      if (arrInThese.length == 0) {
        return false;
      }
      while (arrInThese.length > 0) {
        dirname = _this.$staticmethod.ReNameRepeat(dirname);
        arrInThese = _this.extdata.tableData.filter(
          x => x.FileName == dirname && x.FileId != testbyfileid
        );
      }
      return dirname;
    },

    // 开始重命名文件
    beginrenamefile(itemname, targetFileId){
      let _this = this;

      // 重命名文件，文件名+扩展名超长提示
      if (itemname && itemname.length > 300) {
        _this.$message({
          message:'文件或文件夹名称不得超过300个字符',
          type: 'warning'
        });
        return;
      }
      // //重命名文件，文件名+扩展名超长提示

      var _targetFile;
      var _targetFiles = _this.extdata.tableData.filter(x => x.FileId == targetFileId);
      if (_targetFiles.length > 0) {
        _targetFile = _targetFiles[0];
      } else {
        _targetFile = undefined;
      }

      // 判断文件名称是否为空
      if (!itemname || itemname.length == 0 || itemname.trim().length == 0) {
        _this.$message.error('文件名不能为空');
        _this.extdata.alreadycancelnameedit = false;
        if (_this.extdata.showtype == 0) {
          // 表格
          document.getElementById('tableview_' + targetFileId).focus();
          document.getElementById('tableview_' + targetFileId).select(); // tableview_-1
        } else {
          // 矩阵
          document.getElementById('listview_' + targetFileId).focus();
          document.getElementById('listview_' + targetFileId).select();
        }
        return;
      }
      // 判断扩展名是否被修改，如果被修改了，需要让用户确认是否使用新扩展名
      var _lastindexofdot = itemname.lastIndexOf(".");
      var _extname = itemname.substr(_lastindexofdot + 1);
      // //判断扩展名是否被修改，如果被修改了，需要让用户确认是否使用新扩展名

      if (_extname != _this.extdata._editingext) {
        _this
          .$confirm("如果改变文件扩展名，可能会导致文件不可用，确实要修改吗？", '文件重命名',
            {
              confirmButtonText: '是',
              cancelButtonText: '否',
              type: 'warning'
            })
          .then(x => {
            // 已通过了扩展名确认
            _this.extincluing_renameItem(itemname, targetFileId);
          })
          .catch(x => {
            if (_targetFile) {
              // 调用方法，放弃新扩展名
              _targetFile.FileName = _this.$staticmethod.giveupnewext(itemname, _this.extdata._editingext);
              // 已通过了扩展名确认
              _this.extincluing_renameItem(_targetFile.FileName, targetFileId);
            }
          });
      } else {
        // 已通过了扩展名确认
        _this.extincluing_renameItem(itemname, targetFileId);
      }
    },

    // 开始重命名文件夹
    beginrenameItem(dirname, targetFileId, ev) {
      let _this = this;
      // 判断如果是正在重命名文件，则需要调用函数： beginrenam1efile(filename, targetFileId);
      var _items = _this.extdata.tableData.filter(x => x.FileId == targetFileId);
      if (_items.length > 0 && _items[0].FileSize != '0') {
        _this.beginrenamefile(dirname, targetFileId);
        return;
      }
      // //判断如果是正在重命名文件，则需要调用函数： beginrenam1efile(filename, targetFileId);

      // 文件夹名称为空或空白
      if (dirname.length == 0 || dirname.trim().length == 0) {
        _this.$message.error('文件夹名称不能为空');
        _this.extdata.alreadycancelnameedit = true;
        if (_this.extdata.showtype == 0) {
          // 表格
          document.getElementById('tableview_' + targetFileId).focus();
          document.getElementById('tableview_' + targetFileId).select(); // tableview_-1
        } else {
          // 矩阵
          document.getElementById('listview_' + targetFileId).focus();
          document.getElementById('listview_' + targetFileId).select();
        }
        return;
      }

      var dirname_suggest = dirname;
      if (ev) {
        ev.stopPropagation();
      }

      // 再一次验证输入的文件夹名称正确性
      if (!_this.testitemname(dirname)) {
        _this.$message.error("文件夹名称不正确！");
      } else if (
        (dirname_suggest = _this.getSuggestRepeatingName(dirname, targetFileId))
      ) {
        // 判断有无重复名称，如果有，需要确认！
        console.log('新文件夹失去焦点-重命名');
        _this
          .$confirm(
            "已存在重复的名称，要命名为" + dirname_suggest + "吗？",
            "提示",
            {
              confirmButtonText: "确定",
              cancelButtonText: "取消",
              type: "warning"
            }
          )
          .then(() => {
            // 执行创建文件夹
            _this.renameDirectory(dirname_suggest, targetFileId);
          })
          .catch(() => {
          });
        // //判断有无重复名称，如果有，需要确认！
      } else {
        // 执行创建文件夹
        _this.renameDirectory(dirname, targetFileId);
      }
      // //再一次验证输入的文件夹名称正确性
    },

    // 重命名文件夹
    renameDirectory(dirname, targetFileId) {
      let _this = this;
      var ProjectID = _this.$staticmethod._Get("bimcomposerId");

      // 文件夹名称为空或空白
      if (dirname.length == 0 || dirname.trim().length == 0) {
        _this.$message.error('文件夹名称不能为空');
        _this.extdata.alreadycancelnameedit = true;
        if (_this.extdata.showtype == 0) {
          // 表格
          document.getElementById('tableview_' + targetFileId).focus();
          document.getElementById('tableview_' + targetFileId).select(); // tableview_-1
        } else {
          // 矩阵
          document.getElementById('listview_' + targetFileId).focus();
          document.getElementById('listview_' + targetFileId).select();
        }
        return;
      }

      var _LoadingIns = _this.$loading({
        text: "处理中"
      });
      let IsFolder = _this.extdata._selectedobjs.filter(x => x.FileId == targetFileId)[0].IsFolder
      let postdata = JSON.stringify({
        Id: targetFileId,
        UserId: _this.$staticmethod.Get("UserId"),
        Name: dirname,
        IsFolder
      });
      _this
        .$axios({
          method: "post",
          headers:{
            'Content-Type':'application/json'
          },
          url: `${window.bim_config.webserverurl}/api/v1/folder/update?Token=${_this.$staticmethod.Get('Token')}`,
          data: postdata
        })
        .then(x => {
          if(x.data.Ret == 1){
            // 刷新当前页面
            _LoadingIns.close();
            _this.$message({
              message: x.data.Msg,
              type: x.data.Ret==1?"success":"warning"
            });

            // 重置右键菜单的FileId及正在修改的FileId

            _this.refreshdata(targetFileId);
          }else{
            this.$message.error(x.data.Msg)
          }
        })
        .catch(x => {
          console.warn(x);
          _LoadingIns.close();
        });
    },

    // (暂无操作界面)
    // 发送请求，新建文件夹，不包含刷新页面
    createDirectory(dirname) {
      // 请求参数准备
      let _this = this;
      var ProjectID = _this.$staticmethod._Get("bimcomposerId");
      var ParentId = _this.getFolderId();
      var NormalOrDrawings = "Normal";

      // 保证当前文件夹内无重名文件夹
      var arrInThese = _this.extdata.tableData.filter(
        x => x.FileName == dirname && x.FileId != -1
      );
      while (arrInThese.length > 0) {
        dirname = _this.$staticmethod.ReNameRepeat(dirname);
        arrInThese = _this.extdata.tableData.filter(
          x => x.FileName == dirname && x.FileId != -1
        );
      }
      // //保证当前文件夹内无重名文件夹

      var CreateUserID = _this.$staticmethod.Get("UserId"); //localSt1orage.getItem("UserId");
      var CreateUserName = _this.$staticmethod.Get("Account"); //localSt1orage.getItem("Account");
      // 发请求
      let LoadingIns = _this.$loading({
        text: "创建中"
      });
      let postdata = JSON.stringify({
        ProjectId: _this.$staticmethod._Get("organizeId"),
        ParentId: _this.v_Id,
        FolderName: dirname,
        UserId: CreateUserID
      });
      _this
        .$axios({
          method: "post",
          headers:{
            'Content-Type':'application/json'
          },
          url: `${window.bim_config.webserverurl}/api/v1/folder/create?Token=${_this.$staticmethod.Get('Token')}`,
          data: postdata
        })
        .then(x => {
          if(x.data.Ret == 1){
            _this.$message({
              message: x.data.Msg,
              type: 'success'
            });
            _this.refreshdata();
            LoadingIns.close();

          }else{
            this.$message.err(x.data.Msg)
          }
        })
        .catch(x => {
          console.warn(x);
          LoadingIns.close();
        });
    },

    // 基于当前条件，重新请求数据
    refreshdata(newdirid) {
      let _this = this;

      // 重置操作的上下文属性值
      _this.extdata._showcontextbtns_fileid = undefined;
      _this.extdata._showcontextbtns_fileid_renaming = undefined;
      //_this.extdata._showcontext1btns = false;

      _this.closeallpopup();

      // 刷新数据，取决于当前是处于哪个 doclis1ttype
      switch (_this.extdata.doclisttype) {
        case "doclib":
          // 项目文档
          // 已存在文件夹的打开栈
          if (_this.extdata._openstacks.length > 0) {
            _this.open_current_history(
              _this.extdata._openstacks.length - 1,
              undefined,
              newdirid
            );
          } else {
            // 未打开任何文件夹下，处理根级目录
            _this.resetpath(undefined, newdirid);
          }

          // 刷新树
          _this.treedata.items = [];
          _this.load_tree_firstlevelfolder('',newdirid);
          break;
        // //项目文档

        case "mysuscribe":
          // 我的订阅
          _this.GetAllFolderAndFileByProjectID_Suscribe();
          // //我的订阅
          break;
        case "myshared":
          // 我的分享
          _this.GetAllFolderAndFileByProjectID_MyShared();
          // //我的分享
          break;
        case "allshared":
          // 全部分享
          _this.GetAllFolderAndFileByProjectID_MyShared();
          // //全部分享
          break;
        case "recycle":
          // 回收站
          _this.GetAllFolderAndFileByProjectID_Recycled();

          // 刷新树
          _this.treedata.items = [];
          _this.load_tree_firstlevelfolder("recycle");
          // //回收站
          break;
        default:
          break;
      }
    },

    // 上下文菜单的删除按钮功能-开始执行
    // iftargetfileid: 此方法原为上下文菜单单项删除使用，现在用于指定文件（夹）删除，会自动判断 doclisttype
    // context_delete这个方法并没有调用
    /*
    context_delete(iftargetfileid) {
      let _this = this;
      var _fileid;
      if (iftargetfileid) {
        _fileid = iftargetfileid;
      } else {
        //_fileid = _this.extdata._showcontextbtns_fileid;
        if (_this.extdata._selectedobjs.length > 0) {
          _fileid = _this.extdata._selectedobjs[0].FileId;
        } else {
          return;
        }
      }
      var _contextfiles = _this.extdata.tableData.filter(
        x => x.FileId == _fileid
      );
      var _bimcomposerId = _this.$staticmethod._Get("bimcomposerId");
      if (_contextfiles.length > 0) {
        // 得到指定的要删除的文件（/夹）
        var _contextfile = _contextfiles[0];
        var _apiurl;
        var _tostringifypara;

        // 获取 api 地址
        var _url_dir_delete;
        var _url_file_delete;
        if (_this.extdata.doclisttype == "recycle") {
          _url_file_delete = `${window.bim_config.webserverurl}/api/v1/file/clear?userId=${_this.$staticmethod.Get("UserId")}`
        } else {
          // 删除至回收站
          _url_dir_delete = `${window.bim_config.webserverurl}/api/v1/folder/delete?Token=${this.$staticmethod.Get('Token')}`
          _url_file_delete = `${window.bim_config.webserverurl}/api/v1/file/delete?Token=${this.$staticmethod.Get('Token')}`
        }
        // //获取 api 地址

        if (_contextfile.FileSize == "0") {
          // 要删除的目标为文件夹
          _apiurl = _url_dir_delete;
          _tostringifypara = {
            ProjectID: _bimcomposerId,
            FolderIDs: _contextfile.FileId
          };
        } else {
          // 要删除的目标为文件

          if (_this.extdata.doclisttype == "recycle") {

            // // 从回收站中删除
            _apiurl = _url_file_delete;
            _tostringifypara = {
              ProjectID: _bimcomposerId,
              FileIDs: _contextfile.FileId
            };

          } else {

            // // 删除至回收站
            _apiurl = `${window.bim_config.webserverurl}/api/User/Document/DeleteFileByID_Mult?Token=${this.$staticmethod.Get('Token')}`;
            var _AtFolderId = _this.getAtFolderId();
            // debugger;
            _tostringifypara = {
              Token: _this.$staticmethod.Get("Token"),
              ProjectID: bimcomposerId,
              OrganizeId: _this.$staticmethod._Get("organizeId"),
              ObjIDs: _contextfile.FileId,
              ObjNames: _contextfile.FileName,
              AtFolderId: _AtFolderId
            };
          }
        }

        // 发请求
        var _LoadingIns = _this.$loading({
          text: "操作中",
          target: document.getElementById("id_datamain")
          ,background:"rgba(240, 242, 245, 1)"
        });
        _this
          .$axios({
            method: "post",
            url: _apiurl,
            data: _this.$qs.stringify(_tostringifypara)
          })
          .then(x => {
            if(x.data.StatusCode == 200){
              _this.$message({
                message: "删除成功",
                type: "success",
              });
            }else{
              _this.$message({
                message: x.data.Msg,
                type: "warning",
              });
            }
            _this.refreshdata();
            _LoadingIns.close();
          })
          .catch(x => {
            console.warn(x);
            _LoadingIns.close();
          });
      } else {
        console.warn(`未找到 FileId 为${_fileid}的文件`);
      }
      _this.extdata._showcontextbtns_fileid = undefined;
      _this.extdata._showcontextbtns_fileid_renaming = undefined;
    },
    */

    // 初始化所有与文件（夹）相关的权限属性值
    initFileFolderAuth() {
      // 初始化为：公开，可且仅可下载，永久有效
      let _this = this;
      _this.extdata._sharing_ispublic = true;
      _this.extdata._sharing_auths = ["Download"];
      _this.extdata._sharing;
      _this.extdata._sharing_deadline = -1;

      // 同步 cache
      _this.cachedata._sharing_ispublic = _this.extdata._sharing_ispublic;
      _this.cachedata._sharing_deadline = _this.extdata._sharing_deadline;
      // 深拷贝数组
      _this.cachedata._sharing_auths = _this.$staticmethod.DeepCopy(
        _this.extdata._sharing_auths
      );

      // 初始化邮件包含自己
      _this.sharingCopingToMe = true;
    },

    // 判断 tableData 中指定 docid 的项是文件还是文件夹
    // true: 是文件夹
    // false: 不是文件夹
    // undefined: 指定的docid不存在
    isfolder(docid){
      let _this = this;
      var docitems = _this.extdata.tableData.filter(x =>
        x.FileId == docid);
      if (docitems.length == 0) {
        return undefined;
      }
      return docitems[0].FileSize == '0';
    },

    // 尝试对 operatingFileId 文件或文件夹进行分享操作时，需要判断是否有分享权限
    //   获取需要传入 /api/Document/Doc/GetDocRolesAuthByName 接口的
    //   FolderId 参数的值。
    // <bimcomposerId>: 当前操作的项为文件，且该文件处于根目录下，直接返回 bimcomposerId.
    // someFoledId: 需要传入的 FolderId
    getAuthTestingFolderId_share(operatingFileId){

      // 如果当前操作的是文件夹，直接将其返回
      let _this = this;
      if (_this.isfolder(operatingFileId)){
        return operatingFileId;
      }

      // 判断打开栈个数
      var openstackslength = _this.extdata._openstacks.length;
      if (openstackslength == 0) {
        //return undefined;
        return _this.$staticmethod._Get("bimcomposerId");
      }

      // 返回最后一个打开栈的 FileId
      return _this.extdata._openstacks[openstackslength - 1].FileId;
    },

    // 权限验证完成，请求接口生成分享链接，并显示分享设置界面
    // undefined: 默认返回
    showsharedialog(docids){
      let _this = this;
      _this.extdata._showcontextbtns_fileid = docids;
      var topush = _this.extdata.tableData.filter(x => x.FileId == docids)[0];
      // 请求服务器端，创建生成分享链接
      var _LoadingIns = _this.$loading({
        text: "处理中"
      });
      var _HasPwd = _this.extdata._sharing_ispublic ? 0 : 1; //判断是否加密
      var _EndTime = _this.extdata._sharing_deadline;
      //生成四位随机密码
      let Password = ""
      if(_HasPwd == 1){
        let arr = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
        for (let i = 0; i < 4; i++) {
          let num = parseInt(Math.random()*36)
          Password = Password+arr[num]
        }
        _this.extdata._sharing_pwd = Password;
      }

      let data = JSON.stringify({
        UserId: _this.$staticmethod.Get("UserId"),
        ProjectId: _this.$staticmethod._Get("organizeId"),
        HasPassword: _HasPwd==0?false:true,
        Password,
        HasDownload: this.hasDownload,
        ShardDays:_EndTime,
        Detail:[
          {
            ObjectId:topush.FileId,
            ShardContentType:topush.IsFolder == true?0:1,
          }
        ]
      });
      _this
        .$axios({
          method: "post",
          headers:{
            'Content-Type':'application/json'
          },
          url: `${window.bim_config.webserverurl}/api/v1/share/create?Token=${_this.$staticmethod.Get('Token')}`,
          data
        })
        .then(x => {
          if(x.data.Ret == 1){
            //T显示分享地址与密码
            _this.extdata._sharing_url = `${
              window.location.origin
            }${window.bim_config.hasRouterFile}/#/LinkShare/DocShare/Index?SessionId=${x.data.Data}&DocPassword=${Password}&DocProjectId=${_this.$staticmethod._Get("organizeId")}&userId=${this.$staticmethod.Get("UserId")}&token=${this.$staticmethod.Get('Token')}`;

            // 默认：公开，仅可下载，永久有效
            // 重置与分享相关的 extdata 属性
            _this.extdata._sharingshowtype = "sharingmain";
            _this.extdata._sharingmain_emailing = false;

            // 显示分享对话框
            _this.extdata._showshare = true;

          }else{
            this.$message.err(x.data.Msg)
          }
          _LoadingIns.close();
        })
        .catch(x => {
          console.warn(x);
          _LoadingIns.close();
        });
    },

    // 根据所设置的 extdata 的分享设置值生成分享链接
    context_share(docids) {

      let _this = this;
      _this.showsharedialog(docids);
    },

    // 上下文菜单的分享
    // parafileid: 传入则为指定的 fileid ，否则为当前上下文的 fileid
    begin_context_share(parafileid) {
      let _this = this;

      // 此时要初始化所有与文件（夹）分享权限属性为默认值
      _this.initFileFolderAuth();
      var _docids;
      if (parafileid) {
        _docids = parafileid;
      } else {
        if (_this.extdata._selectedobjs.length > 0) {
          _docids = _this.extdata._selectedobjs[0].FileId;
        } else {
          return;
        }
      }
      _this.context_share(_docids);
    },

    // begin_remove_multiple -> remove_multiple
    // 删除文件夹或文档
    remove_multiple(ev) {
      let _this = this;

      // 显示 loading
      let LoadingIns = _this.$loading({
        text: "操作中",
        target: document.getElementById("id_datamain")
        ,background:"rgba(240, 242, 245, 1)"
      });

      var _url_file_delete;
      if (_this.extdata.doclisttype == "recycle") {
        // 从回收站中删除
        _url_file_delete = `${window.bim_config.webserverurl}/api/v1/file/clear?userId=${_this.$staticmethod.Get("UserId")}&Token=${_this.$staticmethod.Get('Token')}`
      } else {
        // 删除至回收站
        _url_file_delete = `${window.bim_config.webserverurl}/api/v1/file/delete-batch?userId=${_this.$staticmethod.Get("UserId")}&Token=${_this.$staticmethod.Get('Token')}`
      }
      let data = []
      for (var i = 0; i < _this.extdata._selectedobjs.length; i++) {
        let ele = _this.extdata._selectedobjs[i]
        let obj ={
          Id:ele.FileId,
          IsFolder:ele.IsFolder
        }
        data.push(obj)
      }
      _this.$axios({
        method: "post",
        headers:{
          'Content-Type':'application/json'
        },
        url: _url_file_delete,
        data: JSON.stringify(data)
      }).then(x=>{
        if(x.data.Ret == 1){
          _this.$message({
            message: "删除成功",
            type: "success",
          });
        }else{
          _this.$message({
            message: x.data.Msg,
            type: "warning",
          });
        }
        _this.refreshdata();
        LoadingIns.close();
      }).catch(x => {
        LoadingIns.close();
      });
    },

    // 将多个项移动1到回收站，或从回收站中批量移除多个项
    // 将选中的多个文件及文件夹移动1到回收站，或从回收站中彻底删除（EnabledMark = 0)
    begin_remove_multiple(ev) {
      if (ev) {
        ev.stopPropagation();
      }
      let _this = this;

      // 如果选中的个数少于1个，则报错
      if (_this.extdata._selectedobjs.length <= 0) {
        _this.$message.error(`请选择文件后再进行操作`);
        return;
      }

      // 如果当前是在回收站数据列表中，需要有操作确认
      if (_this.extdata.doclisttype == "recycle") {

        var _strtip = _this.extdata._selectedobjs.length > 1?"这些内容":'';

        // 从回收站删除，需要加操作确认
        _this
          .$confirm("确认彻底删除" + _strtip + "？", "操作确认", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning"
          })
          .then(x => {
            // 真正的执行多项删除
            _this.remove_multiple(null);
          })
          .catch(x => {
            console.warn(x);
          });
      } else {
        var _strtip = _this.extdata._selectedobjs.length > 1?"这些内容":'';
        _this.$confirm('确认删除' + _strtip + '？', "操作确认", {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(x => {
          // 真正的执行多项删除
          _this.remove_multiple(null);
        }).catch(x => {
        });
      }
    },

    // 根据 DirPath 计算出可视化内容
    // 改为返回本身
    getPathStr(patharr) {
      return patharr;
    },
    // 加载已订阅的数据
    // 执行订阅操作
    exec_suscribeitem(row, ev, actiontype){
      if(this.extdata._selectedobjs.length == 0){
        this.extdata._selectedobjs.push(row)
      }
      let _this = this;
      let LoadingIns = _this.$loading({
        text: "操作中",
        target: document.getElementById("id_datamain")
        ,background:"rgba(240, 242, 245, 1)"
      });
      var _arr_SuscribePara = [];  // 当前选中的内容，处理成需要的格式
      let selectArr = this.extdata._selectedobjs;  // this.extdata._selectedobjs为当前选中的数组
      _arr_SuscribePara = selectArr.map(obj => {
        return {
          Id: obj.FileId,
          IsFolder: obj.IsFolder
        };
      });

      let postData = JSON.stringify({
        Subscribes: _arr_SuscribePara,
        UserId: _this.$staticmethod.Get("UserId"),
        ProjectId:  _this.$staticmethod._Get("organizeId"),
        IsCancel: actiontype=='2'?true:false,
      });
      _this
        .$axios({
          method: "post",
          headers:{
            'Content-Type':'application/json'
          },
          url: `${window.bim_config.webserverurl}/api/v1/folder/subscribe?Token=${_this.$staticmethod.Get('Token')}`,
          data: postData
        })
        .then(x => {
          LoadingIns.close();
          if(x.data.Ret == 1){
            _this.$message({
              message: x.data.Msg,
              type: "success"
            });

            // 重新加载数据，并让已订阅的显示出来
            _this.refreshdata();
            _this.GetAllFolderAndFileByProjectID_Suscribe('list');


          }else{
            this.$message.error(x.data.Msg)
          }
        })
        .catch(res => {
          console.warn(res);
          _this.$message.error("服务器错误");
          LoadingIns.close();
        });
    },

    // 添加订阅
    suscribeitem(row, ev, actiontype) {
      if (ev) {
        ev.stopPropagation();
      }
      let _this = this;

      // 尝试进行订阅时，需要判断权限
      var testfolderid = _this.getAuthTestingFolderId_share(row.FileId);
      if (actiontype == 1) {
        // 判断文件或文件夹的订阅权限

        if (undefined == testfolderid) {
          _this.exec_suscribeitem(row, ev, actiontype);
        } else {
          _this.testdocauth(testfolderid, 'Suscribe', (x)=>{
            _this.exec_suscribeitem(row, ev, actiontype);
          }, (x)=>{
            _this.$message({
              message: '当前文件夹无订阅权限',
              type: 'warning'
            });
          });
        }

      } else {
        _this.exec_suscribeitem(row, ev, actiontype);
      }

    },

    //
    open_file_uploader(ev) {
      ev.stopPropagation();
      document.getElementById("id_file_container").click();
    },

    //
    open_folder_uploader(ev) {
      if (ev) {
        ev.stopPropagation();
      }
      document.getElementById("id_folder_container").click();
    },

    // 上传文件夹，弹出的窗口，确定后的处理
    // 取得内部的文件，进行多文件上传
    foldercontrolchange(ev) {
      // 所选的文件夹内部的所有文件（不按层级）上传到当前路径下
      let _this = this;
      var _items = ev.target.files;

      if (_items.length > 0) {
        _this.ajax_uploadsfiles(_items);
      } else {
        console.warn("没有文件");
      }
      ev.target.value = "";
    },

    //
    filecontrolchange(ev) {
      let _this = this;
      if (ev.target.files.length > 0) {
        _this.ajax_uploadsfiles(ev.target.files);
      } else {
        console.warn("没有文件");
      }
      ev.target.value = "";
    },

    // 点击时关闭所有下拉菜单按钮
    hideallbtns() {
      let _this = this;
      // 上传/新建
      _this.extdata._showfilebtns = false;
      // 文件表格内的上下文菜单
      _this.closeallpopup();
      _this.extdata._showcontextbtns_fileid = undefined;
    },

    //
    uploadfilebtn(ev) {
      ev.stopPropagation();
      let _this = this;
      if (_this.extdata._showfilebtns) {
        _this.extdata._showfilebtns = false;
      } else {
        _this.extdata._showfilebtns = true;
      }
    },

    // 获取当前的 FolderID
    getFolderId() {
      let _this = this;
      var openingDirFileId = _this.$staticmethod._Get("bimcomposerId");
      if (_this.extdata._openstacks.length == 0) {
        openingDirFileId = _this.$staticmethod._Get("bimcomposerId");
      } else {
        openingDirFileId =
          _this.extdata._openstacks[_this.extdata._openstacks.length - 1]
            .FileId;
      }
      return openingDirFileId;
    },

    // 自动弹出上传队列小窗口，并开始异步上传
    ajax_uploadsfiles(files) {
      let _this = this;
      var openingDirFileId = _this.getFolderId();
      // //get current uploading dirpath

      // 确保弹出传输队列
      if (!_this.extdata.showQueue) {
        _this.extdata.showQueue = true;
      }
      // //确保弹出传输队列

      // 设置共同的上传目录地址头
      var _dirheadtarget = "项目文档";

      // 根据当前的 _openstacks 得到整体目录地址头
      for (var i = 0; i < _this.extdata._openstacks.length; i++) {
        _dirheadtarget += `/${_this.extdata._openstacks[i].FileName}`;
      }

      // 设置新一批上传的新起点索引号
      var newStartindex = _this.extdata.uploadQueueData.length;
      var fileobj = {};
      var _axios_all = [];
      for (var i = 0; i < files.length; i++) {
        let FileKey=this.$md5(new Date().getTime()+''+files[i].name);
        // 每个文件都会单独上传
        fileobj = {
          f: files[i]
        };
        fileobj.queueobj = {
          FileName: files[i].name,
          FileSize: files[i].size,
          DirPath: _dirheadtarget,
          FolderID: openingDirFileId,
          ProcessValue: 0,
          css_upload:"上传中",
          FileKey
        };
        let chunkList = [];
        let chunkSize = 1024*1024*30;
        let current = 0;
        let fileName = new Date().getTime() + "_" + files[i].name;
        for (let cur = 0; cur < files[i].size; cur+= chunkSize) {
          let Files = files[i].slice(current * chunkSize, (current + 1) * chunkSize);
          chunkList.push({
            FileKey:FileKey,
            OldFileName:files[i].name,
            Index:current++,
            Files,
            filename:current + "_" + fileName,
            count:0
          });
        }
        _this.singlefile_ajax_chunckupload(chunkList,fileobj.queueobj)
      }
    },

    // 文件拖拽相关方法
    mulfile_dragleave(ev) {
      let _this = this;
      ev.stopPropagation();
      ev.preventDefault();
    },

    // 测试获取所有文件名称及路径
    // 传入 ev.dataTransfer.items
    TestDroppingFiles(items) {
      // 初始化 _droppingdirandfiles.items
      let _this = this;
      _this.extdata._droppingdirandfiles.items = [];

      var entry;
      var reader;
      for (var i = 0; i < items.length; i++) {
        entry = items[i].webkitGetAsEntry();

        if (entry.isDirectory) {
          _this.extdata._droppingdirandfiles.items.push({
            fullpath: entry.fullPath,
            type: "dir",
            name: entry.name
          });
          reader = entry.createReader();

          reader.readEntries(function(res) {
            // 开始调用递归方法
            _this.RecursiveReadFiles(res);
          }, null);
        } else {
          var obj = {
            f: items[i].getAsFile(),
            fullpath: entry.fullPath,
            type: "file",
            name: entry.name
          };
          // console.log(obj);
          _this.extdata._droppingdirandfiles.items.push(obj);
        }
      }
    },

    AddItemIToAttr(fileEntry) {
      let _this = this;
      fileEntry.file(x => {
        var obj = {
          f: x,
          fullpath: fileEntry.fullPath,
          type: "file",
          name: fileEntry.name
        };
        _this.extdata._droppingdirandfiles.items.push(obj);
      });
    },

    RecursiveReadFiles(items) {
      let _this = this;
      var reader;
      for (var i = 0; i < items.length; i++) {
        if (items[i].isDirectory) {
          _this.extdata._droppingdirandfiles.items.push({
            fullpath: items[i].fullPath,
            type: "dir",
            name: items[i].name
          });
          reader = items[i].createReader();
          reader.readEntries(function(res) {
            _this.RecursiveReadFiles(res);
          }, null);
        } else {
          _this.AddItemIToAttr(items[i]);
        }
      }
    },

    mulfile_drop(ev) {
      let _this = this;
      // console.log('拖拽上传')
      ev.stopPropagation();
      ev.preventDefault();

      // 判断当前如果不是'项目文档'页签，直接跳出
      if (_this.extdata.doclisttype != "doclib") {
        if (_this.$configjson.debugmode == 1) {
          console.log("当前不是项目文档页签，不支持上传文件");
        }
        return;
      }

      // 如果当前无上传、新建权限，则提示并跳出
      if (_this.extdata.nonewauth == true) {
        _this.$message({
          message: '当前文件夹无新建或上传权限',
          type: 'warning'
        });
        return;
      }

      // 拖动着多个文件夹或文件的情况
      _this.TestDroppingFiles(ev.dataTransfer.items);
      setTimeout(function() {
        // 筛选出文件夹类型的数据
        var _folderfullpaths = _this.extdata._droppingdirandfiles.items.filter(
          x => x.type == "dir"
        );

        // 筛选出文件类型的数据
        /*
        [{"f":{},"fullpath":"/folder1_file.docx","type":"file","name":"folder1_file.docx"}
        ,{"f":{},"fullpath":"/folder1/13_NpmRunBuild关闭map.docx","type":"file","name":"13_NpmRunBuild关闭map.docx"}
        ,{"f":{},"fullpath":"/folder1/folder1_1/14_NpmRunBuild_css相关问题.docx","type":"file","name":"14_NpmRunBuild_css相关问题.docx"}]
        */
        var _filefullpath = _this.extdata._droppingdirandfiles.items.filter(
          x => x.f
        );

        // 根目录上传时：
        /*
        _dirheadtarget = 项目文档
        */
        // 内部目录上传时：
        /*
        _dirheadtarget = 项目文档/ddd12
        */
        // 现有的打开栈
        // 设置共同的上传目录地址头
        var _dirheadtarget = "项目文档";
        var _sharepredirpath = "";
        for (var i = 0; i < _this.extdata._openstacks.length; i++) {
          _dirheadtarget += `/${_this.extdata._openstacks[i].FileName}`;
          _sharepredirpath += `${_this.extdata._openstacks[i].FileName}/`;
        }

        // directoryname：每一条数据相对于此次拖动的对象的相对路径，也是 ajax 创建文件夹返回数据，找到每一个对应的 FolderId 的依据

        var _lastindexofs = -1;
        for (var i = 0; i < _filefullpath.length; i++) {
          _lastindexofs = _filefullpath[i].fullpath.lastIndexOf("/");
          //_filefullpath[i].directoryname = _filefullpath[i].fullpath.substr(0, _filefullpath[i].fullpath.lastIndexOf("/"));
          _filefullpath[i].directoryname =
            _sharepredirpath +
            _filefullpath[i].fullpath.substr(
              1,
              _filefullpath[i].fullpath.lastIndexOf("/") - 1
            );
        }

        // 获取此次拖动涉及到的应确保存在的业务文件夹，为后绪调用接口递归创建文件夹做准备
        /*
        _folderfullpaths[0].fullpath = "bb1/folder1"
        _folderfullpaths[0].fullpath = "bb1/folder1/folder1_1"
        _folderfullpaths[0].fullpath = "bb1/folder1/folder1_2"
        _folderfullpaths[0].fullpath = "bb1/folder1/folder1_3"
        */
        var _temp = _sharepredirpath;
        for (var i = 0; i < _folderfullpaths.length; i++) {
          if (_folderfullpaths[i].fullpath.indexOf("/") == 0) {
            _folderfullpaths[i].fullpath = _folderfullpaths[i].fullpath.substr(
              1
            );
          }

          // 先拼接前置字符串，再拼接 fullpath
          _temp = _sharepredirpath;
          _temp += _folderfullpaths[i].fullpath;
          _folderfullpaths[i].fullpath = _temp;
        }

        // 构造为数组，序列化成接口参数格式
        /*
        准备递归创建文件夹接口的参数
        _fullpatharrstr = ["folder1","folder1/folder1_1","folder1/folder1_2","folder1/folder1_3"]
        */
        var _fullpatharr = [];
        var _;
        for (var i = 0; i < _folderfullpaths.length; i++) {
          _ = _folderfullpaths[i].fullpath;
          if (_fullpatharr.indexOf(_) < 0) {
            _fullpatharr.push(_);
          }
        }
        var _fullpatharrstr = JSON.stringify(_fullpatharr);
        if (!_this.extdata.showQueue) {
          _this.extdata.showQueue = true;
        }

        var currentFolder = _this.getFolderId();
        var fileobj = {};

        // 所有文件上传完成后，调用 axios.all 的参数


        // 获取 doclib 的中文名称
        var _doclibitems = _this.docmenuitems.filter(
          _tdi => _tdi.doclisttype == "doclib"
        );
        var _rootname = "";
        if (_doclibitems.length > 0) {
          _rootname = `${_doclibitems[0].text}/`;
        }

        // 遍历上面得到的“筛选出文件类型的数据”，构造 fileobj 对象，并对于每一条数据，调用 singlefile_1ajax_upload 进行异步上传

        for (var i = 0; i < _filefullpath.length; i++) {

          // 构造对象 fileobj
          let FileKey=_this.$md5(new Date().getTime()+''+_filefullpath[i].f.name);
          fileobj = {
            f: _filefullpath[i].f,
            queueobj: {
              FileName: _filefullpath[i].f.name,
              FileSize: _filefullpath[i].f.size,
              DirPath: _rootname + _filefullpath[i].directoryname, //_dirheadtarget, //_rootname + '/' + _filefullpath[i].directoryname,
              // FolderID: openingDirFileId,
              ProcessValue: 0,
              css_upload:"上传中",
              FileKey
            }
          };
          var maygotdirid = []

          if (maygotdirid.length == 0) {
            // console.warn(`找不到文件 【${_filefullpath[i].name}】的文件夹`);
            // continue;
            fileobj.queueobj.FolderID = currentFolder;
          } else {
            fileobj.queueobj.FolderID = maygotdirid[0].DocId;
          }

          let chunkList = [];
          let chunkSize = 1024*1024*30;
          let current = 0;
          let fileName = new Date().getTime() + "_" + _filefullpath[i].f.name;
          for (let cur = 0; cur < _filefullpath[i].f.size; cur+= chunkSize) {
            let Files = _filefullpath[i].f.slice(current * chunkSize, (current + 1) * chunkSize);
            chunkList.push({
              FileKey:FileKey,
              OldFileName:_filefullpath[i].f.name,
              Index:current++,
              Files,
              filename:current + "_" + fileName,
              count:0
            });
          }
          _this.singlefile_ajax_chunckupload(chunkList,fileobj.queueobj)

        }

      }, 100);
    },
    mulfile_dragover(ev) {
      let _this = this;
      ev.stopPropagation();
      ev.preventDefault();
    },
    mulfile_dragenter(ev) {
      let _this = this;
      ev.stopPropagation();
      ev.preventDefault();
    },

    // 阻止冒泡
    _stopPropagation(ev) {
      ev.stopPropagation();
    },

    // 点击“项目文档”（或搜索结果的文本）
    resetpath(ev, newdirid) {
      if (ev) {
        ev.stopPropagation();
      }
      let _this = this;

      // 如果当前不处于“doclib”直接略过
      if (_this.extdata.doclisttype != 'doclib'){
        return;
      }
      _this.collapseoptions();
    },

    // 清除搜索结果
    clear_search(ev) {
      let _this = this;
      ev.stopPropagation();

      _this.collapseoptions();

      _this.keyword = "";
      var bimcomposerId = _this.$staticmethod._Get("bimcomposerId");
      _this.GetAllFolderAndFileByProjectID({
        ProjectID: bimcomposerId,
        LikeName: _this.keyword,
        NormalOrDrawings: "Normal",
        FolderID: undefined,
        _if_dir_change_suc_openstacks: []
      });
      _this.extdata._lastkeyword = "";
    },

    // 搜索按钮点击
    search_doc_bykeyword(ev, newdirid) {
      let _this = this;
      if (ev) {
        ev.stopPropagation();
      }
      _this.doc_file_search(_this.keyword)
      // 保存最后一次搜索使用的关键字
      _this.extdata._lastkeyword = _this.keyword;
    },

    // 列表视图图标双击
    list_item_dblclick(item, ev) {
      let _this = this;
      ev.stopPropagation();
      _this.item_enter(item);
    },

    // 列表视图，点击一个文件的c heckbox
    list_item_checkbox_click(FileId, ev) {
      ev.stopPropagation();
      let _this = this;
      //_this.$refs.doctable.toggleRowSele1ction(row);
      if (_this.selectedContains(FileId)) {
        _this.extdata._selectedobjs = _this.extdata._selectedobjs.filter(
          x => x.FileId != FileId
        );
      } else {
        var topush = _this.extdata.tableData.filter(x => x.FileId == FileId)[0];
        if (topush) {
          _this.extdata._selectedobjs.push(topush);
        }
      }
      _this.applySelectedItemsAuths();
    },
    // 列表视图，选中一个文件，同时保证其它的未选中
    list_item_click(FileId, ev) {
      ev.stopPropagation();
      let _this = this;

      _this.closeallpopup();
      _this.extdata._showcontextbtns_fileid = undefined;
      // //隐藏上下文菜单
      // 如果按下了 ctrlkey
      if (ev.ctrlKey) {
        //TO1DO 同时也按了 shiftkey
        if (ev.shiftKey) {
          // 拷贝原有的所有选中的数据
          var _originSelects = _this.$staticmethod.DeepCopy(
            _this.extdata._selectedobjs
          );

          // 声明要与 _originSelected 取并集的数组
          var _tomergetoorigin;

          // 只按 shiftkey 的情况
          var preindex = _this.extdata.tableData.findIndex(
            x => x.FileId == _this.extdata._lastclickedFileId
          );
          var nowindex = _this.extdata.tableData.findIndex(
            x => x.FileId == FileId
          );
          if (preindex == nowindex) {
            //_this.extdata._selectedobjs = [_this.extdata.tableData[preindex]];
            _tomergetoorigin = [_this.extdata.tableData[preindex]];
          } else {
            var _less = preindex < nowindex ? preindex : nowindex;
            var _greator = preindex > nowindex ? preindex : nowindex;
            var _tosetselected = [];
            for (var i = _less; i <= _greator; i++) {
              _tosetselected.push(_this.extdata.tableData[i]);
            }
            _tomergetoorigin = _tosetselected;
          }
          // //只按 shiftkey 的情况

          // 把已选中的数据，与 “只按 shiftkey”的情况取并集
          var _all_notdistinct = _originSelects.concat(_tomergetoorigin);

          // 将 _all_notdistinct 排重
          var _distinct = _this.$staticmethod.Unique(
            _all_notdistinct,
            x => x.FileId
          );

          // 赋值给 extdata
          _this.extdata._selectedobjs = _distinct;
        } else {
          // 只按了 ctrlKey
          var _maybehas = _this.extdata._selectedobjs.filter(
            x => x.FileId == FileId
          );
          if (_maybehas.length > 0) {
            // 移除出去
            _this.extdata._selectedobjs = _this.extdata._selectedobjs.filter(
              x => x.FileId != FileId
            );
          } else {
            // 添加进来
            var _has = _this.extdata.tableData.filter(x => x.FileId == FileId);
            for (var i = 0; i < _has.length; i++) {
              _this.extdata._selectedobjs.push(_has[i]);
            }
          }
          // 记录最后一次点击的项的 FileId
          // 仅有 ctrl 键被按下时也要记录
          _this.extdata._lastclickedFileId = FileId;

          // //只按了 ctrlKey
        }
      } else if (ev.shiftKey) {
        // 只按了 shiftkey
        console.log("只按了shiftkey " + FileId);

        var preindex = _this.extdata.tableData.findIndex(
          x => x.FileId == _this.extdata._lastclickedFileId
        );
        var nowindex = _this.extdata.tableData.findIndex(
          x => x.FileId == FileId
        );

        console.log(`preindex = ${preindex}, nowindex = ${nowindex}`);

        // 保证选中 [preindex, nowindex] 范围内的数据
        if (preindex == nowindex) {
          // 仅选中索引为 preindex的数据
          _this.extdata._selectedobjs = [_this.extdata.tableData[preindex]];
        } else {
          var _less = preindex < nowindex ? preindex : nowindex;
          var _greator = preindex > nowindex ? preindex : nowindex;
          // 设置 [_less, _greator]这个索引区间范围内的为将要选中的
          var _tosetselected = [];
          for (var i = _less; i <= _greator; i++) {
            _tosetselected.push(_this.extdata.tableData[i]);
          }
          _this.extdata._selectedobjs = _tosetselected;
        }
      } else {
        // 无 ctrlKey 无 shiftKey
        _this.extdata._selectedobjs = [];
        _this.extdata._selectedobjs = _this.extdata.tableData.filter(
          x => x.FileId == FileId
        );
        // 记录最后一次点击的项的 FileId
        // 仅有 ctrl 键被按下时也要记录
        _this.extdata._lastclickedFileId = FileId;
      }
      _this.applySelectedItemsAuths();
      return;
    },

    resolveCurrentMultipleAuth(datalist){
      let _this = this;
      var allRoleAuthNameAndValue = new Array();
      for (var i = 0; i < datalist.ContainerFolderAuths.length; i++) {
        for (var j = 0; j < datalist.ContainerFolderAuths[i].RoleDocAuths.length; j++) {
          if (!allRoleAuthNameAndValue[datalist.ContainerFolderAuths[i].RoleDocAuths[j].RoleDocAuthName]) {
            allRoleAuthNameAndValue[datalist.ContainerFolderAuths[i].RoleDocAuths[j].RoleDocAuthName] = datalist.ContainerFolderAuths[i].RoleDocAuths[j].RoleDocAuthValue;
          }
        }
      }

      // 多选时，对于 delete 权限，先判断文件的，如果如果没有权限，则直接判定没有 delete 权限
      if (allRoleAuthNameAndValue['delete'] == false) {
        _this.currentMulAuth['delete'] = false;
      } else {
        // 对于每一个文件夹都要判断是否有删除权限。如果有“没有”删除权限的，则直接设置 currentMulAuth 为 false
        var anyFolderNotAllowDelete_explicit = datalist.FolderSelfAuthDatas.filter(x => x.DeletePrivilege != null && x.DeletePrivilege != 1);
        if (anyFolderNotAllowDelete_explicit.length > 0) {
          _this.currentMulAuth['delete'] = false;
        } else {
          _this.currentMulAuth['delete'] = true;
        }
      }
    },
    applySelectedItemsAuths(){
      // 准备接口参数
      let _this = this;
      // 隐藏掉上下文菜单
      _this.closeallpopup();
      // _this.getAuthFun()



      // var _Token = _this.$staticmethod.Get("Token");
      // var _OrgId = _this.$staticmethod._Get("organizeId");
      // var _BIMComposerId = _this.$staticmethod._Get("bimcomposerId");
      // var _FolderId;// = _this.extdata._openstacks[_this.extdata._openstacks.length - 1].FileId;

      // 判断当前是否处于根目录
      // if (_this.extdata._openstacks.length > 0) {
      //   _FolderId = _this.extdata._openstacks[_this.extdata._openstacks.length - 1].FileId;
      // } else {
      //   _FolderId = _BIMComposerId;
      // }
      // var SelectedFolders = _this.extdata._selectedobjs.filter(x => x.FileSize == '0');
      // var _CFolderIds = "";
      // for (var i = 0; i < SelectedFolders.length; i++) {
      //   _CFolderIds += `${SelectedFolders[i].FileId}${i == SelectedFolders.length - 1?'':','}`;
      // }
      // 调用接口
      /*
      // 原来的权限请求======删除
      _this.$axios({
        method: 'get',
        url: `${_this.$configjson.webserverurl}/api/Document/Doc/GetDocCurrentFoldersAuth?Token=${_Token}&OrgId=${_OrgId}&FolderId=${_FolderId}&CFolderIds=${_CFolderIds}&BIMComposerId=${_BIMComposerId}`
      }).then(x => {
        if (x.status == 200 && x.data.Ret > 0 && x.data.Data.Ret > 0 && x.data.Data.Data) {
          // 依据 x.data.Data.Data 进行按钮可用性判断
          var ContainerFolderAuths_FolderSelfAuthDatas = {
          };
          if (x.data.Data.Data.ContainerFolderAuths) {
            ContainerFolderAuths_FolderSelfAuthDatas = x.data.Data.Data;
          } else {
            ContainerFolderAuths_FolderSelfAuthDatas = {
              ContainerFolderAuths: x.data.Data.Data,
              FolderSelfAuthDatas: []
            };
          }
          _this.resolveCurrentMultipleAuth(ContainerFolderAuths_FolderSelfAuthDatas);
        } else {
          _this.$staticmethod.debug(x);
        }
      }).catch(x => {
        _this.$staticmethod.debug(x);
      });
      */
    },

    list_item_head_check(ev) {
      ev.stopPropagation();
      let _this = this;
      if (_this.extdata.tableData.length == 0) {
        return;
      }
      if (_this.extdata._selectedobjs.length < _this.extdata.tableData.length) {
        _this.extdata._selectedobjs = _this.extdata.tableData;
      } else {
        _this.extdata._selectedobjs = [];
      }
      _this.applySelectedItemsAuths()
    },
    // 改变全部的选中状态
    head_check_toggle(ev) {
      ev.stopPropagation();
      let _this = this;
      _this.list_item_head_check(ev);
    },
    // 仅改变单行的选中状态
    row_check_toggle(row, ev) {
      ev.stopPropagation();
      let _this = this;
      _this.list_item_checkbox_click(row.FileId, ev);
    },
    // 判断已选中的数据（指Vue内存数据）是否包含某一FileId
    selectedContains(fileid, use_el_select1ion) {
      let _this = this;
      if (use_el_select1ion) {
        return (
          _this.$refs.doctable.selection.filter(x => x.FileId == fileid)
            .length > 0
        );
      } else {
        return (
          _this.extdata._selectedobjs.filter(x => x.FileId == fileid).length > 0
        );
      }
    },
    // 打开某一层级（从面包屑）
    open_current_history(index, ev, newdirid) {

      if (ev) {
        ev.stopPropagation();
      }
      let _this = this;
      // // 判断如果是最后一个，不执行动作
      if (index == _this.extdata._openstacks.length - 1) {
        return;
      }
      _this.collapseoptions();

      var newhisarr = [];
      for (var i = 0; i <= index; i++) {
        newhisarr.push(_this.extdata._openstacks[i]);
      }

      _this.extdata._openstacks = newhisarr;
      _this.v_Id = _this.extdata._openstacks[index].FileId
      _this.doc_file_folder(this.v_Id)
    },

    // 打开上一级文件夹
    open_parentfolder(ev) {
      let _this = this;
      ev.stopPropagation();
      _this.collapseoptions();
      // 当没有目录栈时，直接跳出
      if (_this.extdata._openstacks.length == 0) {
        return;
      }

      var bimcomposerId = _this.$staticmethod._Get("bimcomposerId");
      var newstacks = [];
      // 打开栈只剩一个的时候，即将回到最初始的位置，即：项目文档根目录
      if (_this.extdata._openstacks.length == 1) {
        // 判断最后一次搜索使用的关键字
        if (_this.extdata._lastkeyword && _this.extdata._lastkeyword != "") {
          // 进入到根目录，但带有搜索关键字，等同于直接搜索
          _this.keyword = _this.extdata._lastkeyword;
          _this.GetAllFolderAndFileByProjectID({
            ProjectID: bimcomposerId,
            LikeName: _this.keyword,
            NormalOrDrawings: "Normal",
            FolderID: undefined,
            _if_dir_change_suc_openstacks: []
          });
        } else {
          // 进入到根目录
          _this.GetAllFolderAndFileByProjectID({
            ProjectID: bimcomposerId,
            LikeName: "",
            NormalOrDrawings: "Normal",
            FolderID: 0,
            _if_dir_change_suc_openstacks: []
          });
        }
      } else {
        // 进入到上一级目录，通过 FileId，并将 extdata._openstacks 弹栈
        newstacks = [];
        for (var i = 0; i < _this.extdata._openstacks.length - 1; i++) {
          newstacks.push(_this.extdata._openstacks[i]);
        }
        _this.extdata._openstacks = newstacks;
        _this.v_Id = newstacks[newstacks.length - 1].FileId
        _this.doc_file_folder(this.v_Id)
      }
    },
    row_filename_click(obj, ev) {
      let _this = this;
      _this.on_row_dblclick(obj, null, ev);
      ev.stopPropagation();
    },

    // 列表视图右击
    list_item_rclick(row, ev) {
      let _this = this;
      _this.list_item_click(row.FileId, ev);
      _this.extdata._showcontextbtns_fileid = row.FileId;

      // 先设置可用性
      _this.contextmenubtns_default(row.FileId);

      // 再修改“订阅”按钮的文本
      // 得到“订阅”菜单项
      var _btns = _this.contextmenubtns.filter(x => x.value == "Suscribe");
      // 判断当前文件是否已经订阅，如果是，显示“取消订阅”，否则显示取消订阅
      if (_btns.length > 0) {
        if (_this.extdata._suscribed.indexOf(row.FileId) >= 0) {
          // 当前文件已经订阅了
          _btns[0].text = "取消订阅";
        } else {
          // 当前文件未订阅
          _btns[0].text = "订阅";
        }
      }

      _this.set_context_pos(_this.docrightmenu_w, _this.docrightmenu_h, ev);
      ev.stopPropagation();
      ev.preventDefault();
    },
    // 表格行右击
    row_contextmenu(row, column, ev) {
      let _this = this;
      _this.list_item_rclick(row, ev);
    },
    enterroot(AutoSelectFileId){
      let _this = this;
      _this.GetAllFolderAndFileByProjectID({
        LikeName: "",
        NormalOrDrawings: "Normal",
        FolderID: undefined,
        _if_dir_change_suc_openstacks: [],
        AutoSelectFileId: AutoSelectFileId
      });
    },
    // 之前的权限代码、删除
    /*
    testdocauth(folderId, actionname, successcallback, errorcallback) {
      // 获取当前人，当前项目的所有所属角色
      let _this = this;
      var _UserId = _this.$staticmethod.Get("UserId");
      var _organizeId = _this.$staticmethod._Get("organizeId");
      var _bimcomposerId = _this.$staticmethod._Get("bimcomposerId");
      var _FolderId = folderId;
      var _actionName = actionname;
      var _Token = _this.$staticmethod.Get("Token");

      // 调用接口，判断权限
      _this.$axios({
        method: 'get',
        url: `${_this.$configjson.webserverurl}/api/Document/Doc/GetDocRolesAuthByName?Token=${_Token}&OrgId=${_organizeId}&BIMComposerId=${_bimcomposerId}&FolderId=${_FolderId}&authName=${actionname}`
      }).then(x => {
        if (x.status == 200 && x.data.Data && x.data.Data.Ret > 0 && x.data.Data.Data) {
          var allRoleAuthNameAndValue = new Array();
          for (var i = 0; i < x.data.Data.Data.length; i++) {
            for (var j = 0; j < x.data.Data.Data[i].RoleDocAuths.length; j++) {
              if (!allRoleAuthNameAndValue[x.data.Data.Data[i].RoleDocAuths[j].RoleDocAuthName]) {
                allRoleAuthNameAndValue[x.data.Data.Data[i].RoleDocAuths[j].RoleDocAuthName] = x.data.Data.Data[i].RoleDocAuths[j].RoleDocAuthValue;
              }
            }
          }
          if (allRoleAuthNameAndValue[actionname] == true) {
            successcallback();
          } else {
            errorcallback();
          }
        }
      }).catch(x => {
        if (_this.$configjson.debugmode == "1") {
          console.warn(x);
        }
      });
    },
    */
    enterdir(fileid, filename, ev, setopenstacks, AutoSelectFileId) {
      // 验证 fileid 文件夹对于当前人有无打开权限。（需要前置判断，这里做额外判断）
      if (ev) {
        ev.stopPropagation();
      }
      let _this = this;
      _this.v_Id = fileid

      _this.doc_file_folder(fileid)
      var new_openstacks = [];
      if (!setopenstacks) {
        // 没有指定打开文件夹后的“打开栈”时，通过fileid及filename计算“打开栈”
        for (var i = 0; i < _this.extdata._openstacks.length; i++) {
          new_openstacks.push(_this.extdata._openstacks[i]);
        }
        new_openstacks.push({ FileId: fileid, FileName: filename });
      } else {
        // 设置指定的“打开栈”
        new_openstacks = setopenstacks;
      }
      const uniqueArr = new_openstacks.reduce((acc, current) => {
        const x = acc.find(item => item.FileId === current.FileId);
        if (!x) {
          return acc.concat([current]);
        } else {
          return acc;
        }
      }, []);
      _this.GetAllFolderAndFileByProjectID({
        LikeName: "",
        NormalOrDrawings: "Normal",
        FolderID: fileid,
        _if_dir_change_suc_openstacks: uniqueArr,
        AutoSelectFileId: AutoSelectFileId
      });
    },

    // 通过下载文件地址预览文档
    // CompsFileHistory_data.dataSource 中确保有 fileid 属性！
    previewfile_usingdownloadurl(filedownloadurl, fileid){
      // 找到当前文档
      let _this = this;
      var filethese = _this.extdata.tableData.filter(x => x.FileId == fileid);
      if (filethese.length == 0) {
        return;
      }
      let row = filethese[0];  // 当前选中行的数据
      // 根据扩展名类型，取得在线浏览地址
      // 根据扩展名获取在线浏览地址
      var url_iframe_all;

      if (row.FileExtension == ".rvt" || row.FileExtension == ".dwg") {
        if (row.ModelBase && row.ModelBase.IfModel) {
          if (row.ModelBase.ModelID.length > 0) {
            this.preopenmodel(row.ModelBase.ModelID,row.FileName,row.ModelBase.VersionNO)
            return
          } else {
            this.getFileVersionInfo(row.FileId)
          }

        } else {
          if(row.FileExtension == ".dwg"){
            // 修改当前预览的关闭按钮类
            _this.$emit("set_projectboot_extdata", "_docviewtype", "dwg");
            url_iframe_all = `${
              _this.$configjson.dwgurl
            }/Home/Index2?dwgurlcfg=${encodeURIComponent(filedownloadurl)}&name=${
              row.FileName
            }`;
            // return
          }else{
            this.$message({
              message: '无法预览未转换的文件',
              type: 'warning'
            })
            return;
          }
        }
      } else {
        if(row.ModelBase && row.ModelBase.IfPanorama){
          this.openPanoUrl(row.ModelBase.PanoramaUrl,row.FileName)
          return
        }else{
          // 修改当前预览的关闭按钮类
          _this.$emit("set_projectboot_extdata", "_docviewtype", "office");
          url_iframe_all = _this.$staticmethod.getHuangNewcomputeViewUrl(filedownloadurl, row.FileName,row.FileExtension);
        }
      }

      // 打开在线预览。
      _this.$emit("set_projectboot_extdata", "_show_idocview", true);
      _this.$emit("set_projectboot_extdata", "_idocviewurl", url_iframe_all);
    },
    // 根据下载地址来进行预览
    func_previewbydownloadurl(row, filedownloadurl,FileExtension) {
      let _this = this;
      // 根据扩展名获取在线浏览地址
      var url_iframe_all;

      // 判断如果是压缩文件的相关扩展名，直接下载：
      var zipExts = ['.zip', '.rar', '.7z', '.jar', '.tar'];
      var lastIndexOfDot = row.FileName.toLowerCase().lastIndexOf('.');
      if (lastIndexOfDot < 0) {
        // 不包含.，也直接下载
        window.location.href = filedownloadurl;
        return;
      }

      var theFileExt = row.FileName.substr(lastIndexOfDot);

      if (zipExts.indexOf(theFileExt) >= 0) {
        this.$message({
          message: "压缩文件不支持打开预览",
          type: "warning"
        });
        return;
      }
      if (row.FileExtension == ".rvt" || row.FileExtension == ".dwg") {
        if (row.ModelBase && row.ModelBase.IfModel) {
          if (row.ModelBase.ModelID.length > 0) {
            this.preopenmodel(row.ModelBase.ModelID,row.FileName,row.ModelBase.VersionNO)
            return
          } else {
            this.getFileVersionInfo(row.FileId)
          }

        } else {
          if(row.FileExtension == ".dwg"){
            // 修改当前预览的关闭按钮类
            _this.$emit("set_projectboot_extdata", "_docviewtype", "dwg");
            url_iframe_all = `${
              _this.$configjson.dwgurl
            }/Home/Index2?dwgurlcfg=${encodeURIComponent(filedownloadurl)}&name=${
              row.FileName
            }`;
            // return
          }else{
            this.$message({
              message: '无法预览未转换的文件',
              type: 'warning'
            })
            return;
          }
        }
      } else {
        if(row.ModelBase && row.ModelBase.IfPanorama){
          this.openPanoUrl(row.ModelBase.PanoramaUrl,row.FileName)
          return
        }else{
          // 修改当前预览的关闭按钮类
          _this.$emit("set_projectboot_extdata", "_docviewtype", "office");
          url_iframe_all = _this.$staticmethod.getHuangNewcomputeViewUrl(filedownloadurl, row.FileName,FileExtension);
        }
      }
      // 打开在线预览。
      _this.$emit("set_projectboot_extdata", "_show_idocview", true);
      _this.$emit("set_projectboot_extdata", "_idocviewurl", url_iframe_all);
    },
    openPanoUrl(PbUrl,PbName){
      var viewurl = this.$staticmethod.getPanoUrl(PbUrl, this.$staticmethod._Get('organizeId'), '');
      var randomnumber=Math.floor(Math.random()*100000)
      viewurl += "&timestamp=" + randomnumber;
      this.panoUrlIframe = {url:viewurl, name:PbName};
      this.showPanoIframeView = true;
      // console.log(this.panoUrlIframe,'===viewurl')
    },
    // begin_previewfile 方法后执行
    previewfile(row, ev){
      let _this = this;
      // 根据扩展名类型，取得在线浏览地址
      let filedownloadurl = `${window.bim_config.webserverurl}/api/v1/file/summary-preview?FileId=${row.FileId}&Version=1&UserId=${this.$staticmethod.Get("UserId")}&Token=${this.$staticmethod.Get('Token')}`
      _this.func_previewbydownloadurl(row, filedownloadurl,row.FileExtension);
    },
    // 预览文件（文件库列表或矩阵数据中，不包括历史版本界面的预览）
    begin_previewfile(row, ev) {
      if (ev) {
        ev.stopPropagation();
      }
      let _this = this;
      _this.previewfile(row, ev);
    },

    // 被触发的双击事件
    on_row_dblclick(row, column, ev) {
      let _this = this;
      ev.stopPropagation();
      _this.item_enter(row);
    },

    // 上下文菜单的 download 操作
    begin_context_download() {
      let _this = this;
      var fileid;
      if (_this.extdata._selectedobjs.length > 0) {
        fileid = _this.extdata._selectedobjs[0].FileId;
      } else {
        return;
      }
      _this.context_download(fileid);
    },
    // 权限验证后，执行下载
    exec_download(fileid){
      let _this = this;
      var filedownloadurl = `${window.bim_config.webserverurl}/api/v1/file/summary-download?fileId=${fileid}&userId=${_this.$staticmethod.Get("UserId")}&Token=${this.$staticmethod.Get('Token')}`;
      window.location.href = filedownloadurl;
    },
    // 下载单个文件
    context_download(fileid) {
      // 得到指定的 参数
      let _this = this;
      var _fileid = fileid;
      // 得到指定的文件
      var _contextfiles = _this.extdata.tableData.filter(
        x => x.FileId == _fileid
      );
      var _contextfile = undefined;
      // console.log(_contextfiles);
      if (_contextfiles.length > 0) {
        _contextfile = _contextfiles[0];
      }
      // 下载指定文件
      if (_contextfile) {
        // 判断当前操作下载的项是文件还是文件夹
        var docid = _contextfile.FileId;
        if (_this.isfolder(docid)){
          _this.$message({
            message: "暂不支持下载文件夹",
            type: "warning"
          });
        } else {
          _this.exec_download(docid);
        }
      }
    },
    // 上下文菜单的 open 操作
    context_open() {
      let _this = this;
      var _fileid;
      if (_this.extdata._selectedobjs.length > 0) {
        _fileid = _this.extdata._selectedobjs[0].FileId;
      } else {
        return;
      }
      var _contextfiles = _this.extdata.tableData.filter(
        x => x.FileId == _fileid
      );
      if (_contextfiles.length > 0) {
        _this.item_enter(_contextfiles[0]);
      } else {
        console.warn(`未找到 FileId 为${_fileid}的文件`);
      }
      _this.closeallpopup();
      _this.extdata._showcontextbtns_fileid = undefined;
      _this.extdata._showcontextbtns_fileid_renaming = undefined;
    },
    // 单击的不是复选框，而是行时，切换当前行的选中状态，同时确保其它行没有被选中。
    row_click(row, column, ev) {
      let _this = this;
      _this.list_item_click(row.FileId, ev);
      ev.stopPropagation();
    },
    // 点击分享的
    share_row_click(row, column, ev){
      let _this = this;
      this.$axios
        .get(`${window.bim_config.webserverurl}/api/v1/share/detail?shardId=${row.Id}&objectName=&Token=${_this.$staticmethod.Get('Token')}`)
        .then(x=>{
          if(x.data.Ret == 1){
            this.sharedoclisttype = false;
            // Id  当前数据的id    ObjectId是分享数据的id
            let _obj = x.data.Data
            for(let i = 0; i<_obj.length; i++ ){
              _obj[i].FileId = _obj[i].ObjectId;
              _obj[i].FileName = _obj[i].ObjectName;
              _obj[i].shd_SessionId = _obj[i].ObjectId
            }
            this.extdata.tableData = _obj;
          }else{
            this.extdata.tableData = []
          }
        })
    },
    // 取消分享
    shareDeleteParent(row){
      let _this = this;
      _this
        .$axios({
          method: "post",
          url: `${window.bim_config.webserverurl}/api/v1/share/delete?shardId=${row.Id}&userId=${_this.$staticmethod.Get("UserId")}&Token=${_this.$staticmethod.Get('Token')}`,
        })
        .then(x => {
          if(x.data.Ret == 1){
            _this.sharedoclisttype = true;
            _this.$message.success(x.data.Msg);
            _this.GetAllFolderAndFileByProjectID_MyShared();
          }else{
            console.warn(x);
          }
        })
        .catch(x => {
          console.warn(x);
        });
    },
    Switchstate_formatter(row){
      if(row.IsFolder){
        return '-'
      }else if(row.ModelBase && row.ModelBase != null ){
        if(row.ModelBase.IfModel){
          if(row.ModelBase.ModelID && row.ModelBase.ModelID.length > 0){
            return '转换成功'
          }else{
            return '转换中'
          }
        }
        if(row.ModelBase.IfPanorama){
          if(row.ModelBase.PanoramaUrl && row.ModelBase.PanoramaUrl.length > 0){
            return '转换成功'
          }else{
            return '转换中'
          }
        }
      }else{
        return '上传成功'
      }
    },
    // 文件大小格式化
    FileSize_formatter(row, column) {
      if (row.iszerobytes) {
        return "0B";
      }
      let size = row.FileSize;
      if (size == 0) {
        // 文件夹不显示大小
        return "-";
      } else {
        if (row.SizeUnit) {
          const SizeUnit = row.SizeUnit === 'Byte' ? 'B' : row.SizeUnit
          // 显示文件的大小，需要转换为字符串
          return row.FileSize + SizeUnit
        } else {
          return this.formatFileSize(size)
        }
      }
    },
    /**
     * 文件大小转换
     * @param fileSize
     * @returns {string}
     */
    formatFileSize (fileSize) {
      let temp = 0
      if (fileSize < 1024) {
        return fileSize + 'B'
      } else if (fileSize < (1024 * 1024)) {
        temp = fileSize / 1024
        temp = temp.toFixed(2)
        return temp + 'KB'
      } else if (fileSize < (1024 * 1024 * 1024)) {
        temp = fileSize / (1024 * 1024)
        temp = temp.toFixed(2)
        return temp + 'MB'
      } else {
        temp = fileSize / (1024 * 1024 * 1024)
        temp = temp.toFixed(2)
        return temp + 'GB'
      }
    },
    // 时间格式化
    ModifyData_formatter(row, column) {
      let datetime = row.CreateTime;
      if (datetime) {
        return datetime.toString().substr(0, 10);
      } else {
        return "";
      }
    },

    // 我的分享和全部分享都调用GetAllFolderAndFileByProjectID_MyShared方法
    GetAllFolderAndFileByProjectID_MyShared() {
      let _this = this;
      let _userid = ""
      if(_this.extdata.doclisttype == 'allshared'){
        _userid=""
      }else if(_this.extdata.doclisttype == 'myshared'){
        _userid=_this.$staticmethod.Get("UserId")
      }
      this.extdata.tableData = []
      this.shareTableData = []
      _this.extdata._openstacks = [];
      _this.extdata._selectedobjs = [];
      var _bimcomposerId = _this.$staticmethod._Get("bimcomposerId");
      var _tlk = _this.$staticmethod.Get("Token");
      var _LoadingIns = _this.$loading({
        text: "加载中",
        target: document.getElementById("id_datamain")
        ,background:"rgba(240, 242, 245, 1)"
      });
      _this
        .$axios({
          method: "get",
          url: `${window.bim_config.webserverurl}/api/v1/share/list?projectId=${_this.$staticmethod._Get("organizeId")}&userId=${_userid}&objectName=&Token=${_this.$staticmethod.Get('Token')}`
        })
        .then(x => {
          if (x.data.Ret == 1) {
            this.shareTableData = x.data.Data;
          } else {
            this.shareTableData = []
          }
          _LoadingIns.close();
        })
        .catch(x => {
          console.warn(x);
          _LoadingIns.close();
        });
    },
    // 加载回收站中的文档
    GetAllFolderAndFileByProjectID_Recycled() {
      let _this = this;
      var organizeId = _this.$staticmethod._Get("organizeId");
      var _serverurl = `${window.bim_config.webserverurl}/api/v1/folder/collection?projectId=${organizeId}&Token=${_this.$staticmethod.Get('Token')}`;
      var _LoadingIns = _this.$loading({
        text: "加载中",
        target: document.getElementById("id_datamain")
        ,background:"rgba(240, 242, 245, 1)"
      });
      _this
        .$axios({
          method: "get",
          url: _serverurl
        })
        .then(x => {
          if (x.data.Ret == 1) {
            // 转换为 extdata.tableData 可识别的格式
            _this.extdata._lastkeyword = "";
            _this.keyword = "";
            _this.tableDatapatch(x.data.Data);
            _this.extdata.tableData = x.data.Data;
            _this.extdata._selectedobjs = [];
            _this.extdata._openstacks = [];
          }
          _LoadingIns.close();
        })
        .catch(x => {
          console.warn(x);
          _LoadingIns.close();
        });
    },

    // 关闭所有弹出
    closeallpopup(){
      let _this = this;
      _this.extdata._showcontextbtns = false;
      _this.extdata._showblankareacontext = false;
    },

    // 特用于项目文档加载数据
    // 先清除所有选中的数据
    // 按项目、？ 加载文件及文件夹
    // json.ProjectID: BIMComposerID
    // json.LikeName: 关键字
    // json.NormalOrDrawings: 指定传 Normal
    // json.FolderID: 如果是文件夹内，传文件夹id，否则传 undefined
    // json._if_dir_change_suc_openstacks : 如果切换文件夹成功，则将 _openstacks 指定为该值，前提是 json.FolderID 成立
    // json.AutoSelectFileId: 加载后，自动选中的 FileId
    GetAllFolderAndFileByProjectID(json) {
      // 请求文档服务接口，获取数据。
      let _this = this;

      // 关闭所有弹出
      _this.closeallpopup();

      // 只要请求数据，就将已选中的数组置空
      _this.extdata._selectedobjs = [];
      if(json.FolderID){
        _this.extdata._openstacks = json._if_dir_change_suc_openstacks;
        this.v_Id = json.FolderID
      }else{
        // 显示loading
        let LoadingIns = _this.$loading({
          text: "加载中",
          target: document.getElementById("id_datamain")
          ,background:"rgba(240, 242, 245, 1)"
        });

        // 发起请求
        var fileserverurl;

        var UserId=this.$staticmethod.Get('UserId');
        var OrganizeId=this.$staticmethod._Get('organizeId');
        _this.$axios
          .get(`${window.bim_config.webserverurl}/api/v1/folder/summary-tree?projectId=${_this.projectId}&Token=${_this.token}&parentId=${json.FolderID}&userId=${_this.userId}`)
          .then(x => {
            if (x.data.Ret == 1 && x.data.Data.length > 0) {

              this.v_Id = x.data.Data[0].Id

              // 赋值面包屑
              _this.extdata._openstacks = json._if_dir_change_suc_openstacks;

              // 赋值数据
              _this.tableDatapatch(x.data);
              _this.doc_file_folder(this.v_Id)

              setTimeout(() => {
                this.R_lastTime = true;
              },500);
              // _this.extdata.nonewauth = false;
            }
            LoadingIns.close();
          })
          .catch(x => {
            LoadingIns.close();
          })};
    },
    // 查询当前文件夹下信息
    async doc_file_folder(folderId){
      this.stopDoubleClick.folderId = folderId;
      let params = {
        projectId: this.projectId,
        userId: this.userId,
        folderId: folderId,
      }
      const res = await this.$api.getSummaryfilefolder(params)
      if(res.Ret == 1){
        this.extdata.tableData = res.Data;
        // this.getAuthFun()
      }
    },
    // 获取权限=20231218黄兴林新写的接口,获取的是当前文件或者文件夹的权限、传值objectId是父级id,
    getAuthFun(fileId){
      // 这个地方要判断下是文件还是文件夹
      let _fileId = this.v_Id;
      if(fileId){
        _fileId = fileId
      }
      let _this = this;
      _this.$axios
        .get(`${window.bim_config.webserverurl}/api/v1/folder/folder-auth?Token=${_this.token}&objectId=${_fileId}&isFolder=true`)
        .then(x => {
          if (x.data.Ret == 1) {
            // 在这里取当前点击的菜单的权限
            // 在这里设置上传文件权限
            let obj = x.data.Data[0]
            // let upload = x.data.Data[0].Auth.Upload
            _this.extdata.nonewauth = !obj.Upload;
            _this.currentMulAuth['Delete'] = obj.Delete;
            // 移动和删除一个权限、关联模型和重命名一个权限
            for (let i = 0; i < this.contextmenubtns.length; i++) {
              const key = this.contextmenubtns[i].value;
              if (obj.hasOwnProperty(key)) {
                this.contextmenubtns[i].disable = !obj[key];
              }
              if (key == 'moveto') {
                this.contextmenubtns[i].disable = !obj['Delete'];
              }
              if (key == 'correlationModel'){
                this.contextmenubtns[i].disable = !obj['Update'];
              }
              if (key == 'uncorrelationModel'){
                this.contextmenubtns[i].disable = !obj['Update'];
              }
            }
          }
        })
        .catch(x => {

        });
    },
    //搜索文档
    doc_file_search(fileName){
      if(fileName.length != 0){
        let _this = this
        _this.$axios({
          method: "get",
          url: `${window.bim_config.webserverurl}/api/v1/file/search?projectId=${_this.projectId}&Token=${_this.token}&fileName=${fileName}&userId=${_this.userId}`
        }).then(x => {
          if(x.data.Ret == 1){
            _this.extdata.tableData = x.data.Data;
          }else{
            _this.extdata.tableData = []
            _this.$message({
              message: x.data.Msg,
              type: 'warning'
            })
          }
        }).catch(x => {
          console.log(x);
        });
      }else{
        this.load_tree_firstlevelfolder();
      }
    },
    showorhidequeue(willshow, willclear) {
      let _this = this;
      _this.extdata.showQueue = willshow;
      if (willclear) {
        // 清空上传队列数据
        _this.extdata.uploadQueueData = [];
      }
    },
    switchdocitem(ev, typeval) {
      let _this = this;
      ev.stopPropagation();
      if (typeval == '_css-doc-patcher1_') {
        return;
      }
      _this.extdata.doclisttype = typeval;
      if(typeval == 'myshared' || typeval == 'allshared'){
        _this.changeshowtype(0)
        _this.sharedoclisttype = true
      }else{
        _this.sharedoclisttype = false
      }
      // 切换时，要收起下拉按钮...
      _this.extdata._showfilebtns = false;
      _this.closeallpopup();

      // 判断不同情况加载不同数据
      switch (_this.extdata.doclisttype) {
        case "doclib":
          // 初始化加载项目文档一栏的数据
          _this.GetAllFolderAndFileByProjectID({
            LikeName: "",
            NormalOrDrawings: "Normal",
            FolderID: 0,
            _if_dir_change_suc_openstacks: []
          });
          break;
        case "mysuscribe":
          // 加载我的订阅数据
          _this.GetAllFolderAndFileByProjectID_Suscribe();
          break;
        case "myshared":
          // 加载：我的分享
          _this.GetAllFolderAndFileByProjectID_MyShared();
          break;
        case "allshared":
          // 加载：全部分享
          _this.GetAllFolderAndFileByProjectID_MyShared()
          break;
        case "recycle":
          // 刷新 _this.extdata._recycledIds 并且加载到界面上
          _this.GetAllFolderAndFileByProjectID_Recycled();
          break;
        default:
          break;
      }
    },

    changeshowtype(val) {
      this.extdata.showtype = val;
    },
  }
};
</script>
<style scoped>
@import url("../../../assets/css/document.css");
</style>
<style lang="scss" scoped>
.document-summary-container{
  width: 100%;
  background: #f0f2f5;
  .css-bc-white{
    background: white;
    .css-left-span{
      margin-left: 24px;
      font-weight: 500;
      font-size: 16px;
      color: rgba(0,0,0,0.9);
    }
  }
  .document-summary{
    cursor: pointer;
    width: 100%;
    height: 40px;
    display: flex;
    align-items: center;
    .icon{
      margin-left: 8px;
      width: 16px;
      height: 16px;
    }
    span{
      margin-left: 8px;
      font-weight: 500;
      font-size: 14px;
      color: #333333;
    }
  }
  .project-select-wrapper{
    cursor: pointer;
    background-color: #EBEDF0;
    width: 100%;
    height: 40px;
    display: flex;
    align-items: center;
    border-bottom: #E5E6EB 1px solid;
    .icon{
      margin-left: 8px;
      width: 16px;
      height: 16px;
    }
    .project-name{
      margin-left: 8px;
      height: 20px;
      font-weight: 500;
      font-size: 14px;
      color: #333333;
      /* 超出部分隐藏 */
      overflow: hidden;
      /* 不换行 */
      white-space: nowrap;
      /* 溢出用省略号代替 */
      text-overflow: ellipsis;
      /* 设置宽度，超出这个宽度就会被截断 */
      max-width: 200px;
    }
    .project-select-icon{
      margin-right: 10px;
      margin-left: auto;
      width: 16px;
      height: 16px;
    }
  }
  /deep/ .el-tree{
    overflow: auto;
    height: calc(100% - 40px);
    background-color: #F8F8F8;
    font-size: 12px;
    .el-tree-node__content{
      height: 36px;
      margin-left: 8px;
    }
  }
  .project-container{
    width: 300px;
    background-color: white;
    border-radius: 4px;
    position: absolute;
    top: 40px;
    left: 8px;
    z-index: 999;
    box-shadow: 2px 2px 2px 2px rgba(0, 0, 0, 0.2);
    .el-input{
      width: 280px;
      border-radius: 2px;
      border: 1px solid #D8D8D8;
      margin: 8px;
      height: 32px;
      /deep/ .el-input__inner{
        border: 0;
        height: 32px;
        line-height: 32px;
      }
    }
    ul{
      max-height: 510px;
      overflow: auto;
      li{
        margin: 0;
        cursor: pointer;
        height: 46px;
        display: flex;
        align-items: center;
        .icon{
          margin-left: 16px;
          width: 16px;
          height: 16px;
        }
        span{
          margin-left: 7px;
          font-weight: 400;
          font-size: 14px;
          /* 超出部分隐藏 */
          overflow: hidden;
          /* 不换行 */
          white-space: nowrap;
          /* 溢出用省略号代替 */
          text-overflow: ellipsis;
          /* 设置宽度，超出这个宽度就会被截断 */
          max-width: 230px;
        }
        &.normal{
          color: #222222;
          background-color: white;
        }
        &.selected{
          color: #007AFF;
          background: rgba(0,122,255,0.05);
        }
        .right-icon{
          margin-left: auto;
          margin-right: 16px;
          width: 20px;
          height: 20px;
        }
        &:hover{
          color: #007AFF;
          background: rgba(0,122,255,0.05);
        }
      }
    }
    .btn-wrapper{
      position: relative;
      margin-top: 16px;
      display: flex;
      height: 25px;
      padding-bottom: 10px;
      .confirm{
        position: absolute;
        right: 80px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        font-weight: 400;
        font-size: 12px;
        background-color:#1890FF ;
        color: white;
        width: 48px;
        height: 24px;
        border-radius: 4px;
        border: 1px solid #1890FF;
      }
      .cancel{
        position: absolute;
        right: 10px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        font-weight: 400;
        font-size: 12px;
        color: #1890FF;
        width: 48px;
        height: 24px;
        border-radius: 4px;
        border: 1px solid #1890FF;
      }
    }

  }
}
</style>
