<template>
    <div class="bottomDiv">
        <div id="id_minimsg_divctn" data-debug="message_vue_line3" class="Message_Mini">
            <div class="_css-minimsg-divctnl">
                   <div class="_css-minimsg-module "
                @click.stop="conditionSearch('站内信',selectFuc);"
                :class="{'_css-sel':selectType=='站内信'}"
                 >
                    <div class="_css-ifselborder"></div>
                    <div class="_css-minimsg-text">站内信</div>
                    <div 
                    :title="NotifyUnReadCnt"
                    v-if="NotifyUnReadCnt"
                    class="_css-minimsg-ifnum">{{NotifyUnReadCnt<=99?NotifyUnReadCnt:'...'}}</div>
                </div>
                <div class="_css-minimsg-module "
                @click.stop="conditionSearch('问题追踪',selectFuc);"
                :class="{'_css-sel':selectType=='问题追踪'}"
                 >
                    <div class="_css-ifselborder"></div>
                    <div class="_css-minimsg-text">问题</div>
                    <div 
                    :title="IssueUnReadCnt"
                    v-if="IssueUnReadCnt"
                    class="_css-minimsg-ifnum">{{IssueUnReadCnt<=99?IssueUnReadCnt:'...'}}</div>
                </div>
                <div class="_css-minimsg-module " 
                @click.stop="conditionSearch('项目文档',selectFuc);"
                :class="{'_css-sel':selectType=='项目文档'}"
                >
                    <div class="_css-ifselborder"></div>
                    <div class="_css-minimsg-text">文档</div>
                    <div
                    :title="DocUnReadCnt"
                    v-if="DocUnReadCnt"
                     class="_css-minimsg-ifnum">{{DocUnReadCnt<=99?DocUnReadCnt:'...'}}</div>
                </div>
                <div class="_css-minimsg-module " 
                @click.stop="conditionSearch('项目流程',selectFuc);"
                :class="{'_css-sel':selectType=='项目流程'}"
                >
                    <div class="_css-ifselborder"></div>
                    <div class="_css-minimsg-text">流程</div>
                    <div
                    v-if="FlowUnReadCnt"
                     class="_css-minimsg-ifnum">{{FlowUnReadCnt<=99?FlowUnReadCnt:'...'}}</div>
                </div>
                <div class="_css-minimsg-viewall" >
                    <el-tooltip
                        popper-class="css-no-triangle"
                        effect="dark"
                        content="打开消息列表"
                        placement="bottom"
                    >
                        <div 
                        @click.stop="OpenMessageList"
                        class="_css-minimsg-viewall-in icon-interface-history" ></div>
                    </el-tooltip>
                </div>
            </div>
            <div class="_css-minimsg-divctnr ">
                <!-- 此区域未使用 -->
                <ul class="title" style="display:none;" >
                </ul>
                <!-- //此区域未使用 -->
                <div
                v-show="selectType != '站内信'"
                 class="btns _css-msg-typebtns">
                    <button data-debug="line55" :class="selectFuc=='全部'?'sel':''" @click="conditionSearch(selectType,'全部');">全部</button>
                    <button :class="selectFuc=='新增'?'sel':''" @click="conditionSearch(selectType,'新增');">新增</button>
                    <button :class="selectFuc=='更新'?'sel':''" @click="conditionSearch(selectType,'更新');">更新</button>
                    <button :class="selectFuc=='删除'?'sel':''" @click="conditionSearch(selectType,'删除');">删除</button>
                    <el-tooltip
                        popper-class="css-no-triangle"
                        effect="dark"
                        content="全部设为已读"
                        placement="bottom"
                    >
                        <div
                        @click.stop="setallread()"
                         class="_css-setreadall icon-suggested-check_circle " ></div>
                    </el-tooltip>
                </div>
                <!-- element-loading-background="rgba(0, 0, 0, 0.5)" -->
                <div id="id_msglistdiv" 
                v-loading="islistloading" 
                element-loading-text="加载中"
                class="list css-miniscroll css-littlescroll">
                    <ul class="_css-list-item"                    
                    v-for="item in SearchData" :key="item.mu_guid">

                        <!-- 站内信或其它消息的标题内容部分，你加个class不香吗？ -->
                        <div class="list_ulli_firstchild" >
                            <i class="isUnRead" style="display:none;"></i>
                            <span @click="func_messageclick(item, $event)" style="cursor:pointer;"  class="t" :title="MessageContent(item)">{{MessageContent(item)}}</span>
                            <!-- 弹出的受邀请机构的消息中的确认按钮 -->
                            <div @click="joinToCompany(item, $event)" v-if="item.LogAndMsgType == 50" class="_css-join-btn" style="">确认加入</div>
                            <!-- //弹出的受邀请机构的消息中的确认按钮 -->
                        </div>
                        <!-- //站内信或其它消息的标题内容部分，你加个class不香吗？ -->

                        <!-- 站内信或其它消息的日期部分 -->
                        <li class="list_ulli" >
                            <font class="CreateDate">{{getItemDateStr(item)}}</font>
                        </li>
                        <!-- //站内信或其它消息的日期部分 -->

                        <div class="_css-setreaditem  " >
                            <el-tooltip
                                popper-class="css-no-triangle"
                                effect="dark"
                                :content="'设为已读'"
                                placement="bottom"
                            >
                                <div 
                                @click.stop="setitemread(item.mu_guid, item)"
                                class="_css-setreaditem-in icon-suggested-check_circle"></div>
                            </el-tooltip>
                        </div>
                    </ul>
                    <div
                    class="_css-emptyctn"
                    v-if="!SearchData || SearchData.length == 0"
                    >
                        <div
                        class="_css-emptyctn-in"
                        >没有新消息</div>
                    </div>
                </div>
                <!-- <div class="fuc">
                    <button class="OpenMessageList" @click="OpenMessageList">打开消息列表</button>
                </div> -->
            </div>
        </div>
    </div>
</template>
<script>
export default {
    name:'Message',
    components:{
        
    },
    props:{
        // 后加的通知数
        InitNotifyUnReadCnt:{
            type:Number,
            required:false
        },
        // 问题未读数
        InitIssueUnReadCnt:{
            type:Number,
            required:false
        },
        // 文档未读数
        InitDocUnReadCnt:{
            type:Number,
            required:false
        },
        // 流程未读数
        InitFlowUnReadCnt:{
            type:Number,
            required:false
        }
    },
    data(){
        return {
            // 用于存储 cancelToken
            msgm: {
                canceltoken: undefined,//_this.$axios.CancelToken
                canceltoken_source: undefined//_this.msgm.canceltoken.source();
            },

            NotifyUnReadCnt:0,
            IssueUnReadCnt:0,
            DocUnReadCnt:0,
            FlowUnReadCnt:0,

            islistloading: false,
            //last1Loading: undefined,
            UnReadDatas:[],
            SearchData:[],
            // tota1lNum:{
            //     // IssueNum:0,// 似乎没用
            //     // DocNum:0,// 似乎没用
            //     //ProcessNum:0,

            //     //IssueN1um_UnRead:0,// 似乎没用
            //     //DocNum_1UnRead:0,// 似乎没用
            //     //ProcessNum_UnRead:0
            // },
            selectType:"问题追踪",      // 影响消息模块的按钮选中样式
            selectFuc:"全部",           // 影响消息的操作类型按钮选中样式
            dicdata:{
               Issue_Create: 4,
               Issue_Modify: 5,
               Issue_Delete: 6,
               Issue_Comment_Create: 7,
               Issue_Comment_Delete: 8,
               Issue_Doc_Create: 9,
               Issue_Doc_Delete: 10,

               Doc_NewFile: 21,
               Doc_RemoveFile: 22,
               Doc_ModifyFile: 23,
               Doc_MoveFile: 24,
               Doc_NewDir: 27,

               Web_Notify: 50,

               Flow_Submit: 47,
               Flow_Reject: 48,
               Flow_Examine: 49,

               // 这里别加，没用！



               Issue_Comment_Create_At: 1007
            }
        };
    },
    methods:{

        // 点击消息的文字
        // -------------
        func_messageclick(item, ev) {

            // 取出条目的 objectid
            // ------------------
            var _this = this;
            var mmo_objectid = item.mmo_objectid || '';

            // 取出项目ID
            // ---------
            var mme_ProjectID = item.mme_ProjectID || '';

            // 拿到 Token
            // ----------
            var _Token = _this.$staticmethod.Get("Token");

            // 拿到 LogAndMsgType
            // ------------------
            var _LogAndMsgType = item.LogAndMsgType;

            // 点击得到要跳转的地址
            // ------------------
            var _url = `${window.bim_config.webserverurl}/api/User/User/GetMessageJumpUrl?strLogAndMsgType=${
                _LogAndMsgType}&objectId=${mmo_objectid}&organizeId=${mme_ProjectID}&Token=${_Token}`;

            _this.islistloading = true;

            _this.$axios.get(_url).then(x => {
                if (x.data.Ret > 0) {
                    if (x.data.Data != null) {

                        // 这里有一个跨项目，名称的问题
                        // --------------------------
                        var urltojump = `${window.location.origin}${window.bim_config.hasRouterFile}/#/Home/ProjectBoot/${x.data.Data}`;
                        
                        // 跳转前做个标记，表示会触发 refreshleft
                        // ------------------------------------
                        _this.$staticmethod._Set("WillRefreshLeft", 1);
                        
                        window.location.href = urltojump;

                    } else {
                        debugger;
                    }
                } else {
                    _this.$message.error(x.data.Msg);
                }
                _this.islistloading = false;
            }).catch(x => {
                _this.islistloading = false;
                console.error(x);
            });
        },

        getItemDateStr(item) {
            var _this = this;
            
            // if (item.BI_Title) {
            //     // 旧数据，从ExtData 中取时间
            //     return item.ExtData.split('【')[2].split('】')[0];
            // } else {
            //     debugger;
            // }

            

            return item.mm_createdatetimestr;
            
        },

        // 执行加入机构
        // -----------
        do_JoinToCompany(mu_guid, orgid, orgm) {
            var _this = this;
            var _url = `${window.bim_config.webserverurl}/api/Message/JPush/DoJoinCompany`;
            _this.$axios({
                method:'post',
                url:_url,
                data: _this.$qs.stringify({
                    mu_guid: mu_guid,
                    OrganizeId: orgid,
                    Token: this.$staticmethod.Get("Token")
                })
            }).then(x => {
                if (x.status == 200) {
                    if (x.data.Ret > 0) {

                        // 提示用户已加入机构，并修改logo和名称（如果显示）
                        // -------------------------------------------
                        _this.$message.success(`您已加入机构${x.data.Data.FullName}`);

                        // 给上面发通知，刷新logo和名称
                        // --------------------------
                        if (window.location.href.indexOf('Home/Boot/') >= 0) {
                            //window.location.href = `${window.bim_config.hasRouterFile}/#/Home/Boot/${_this.$staticmethod.Get("Token")}`;
                            window.location.reload();
                        } else {

                            // 通知外面把图标更新一下
                            // --------------------
                            _this.$emit("do_refresh_from_beusing");

                            // 把小界面上的数字刷一下
                            // ------------------
                            _this.conditionSearch(_this.selectType, _this.selectFuc);
                        }

                    } else {
                        _this.$message.error(x.data.Msg);
                    }
                } else {
                    console.error(x);
                }
            }).catch(x => {
                console.error(x);
            });
        },

        // 加入机构
        // --------
        joinToCompany(item, ev) {

            // 弹出操作确认
            // -----------
            var _this = this;
//             item.mu_guid
// "65b3ecd6-2b49-4ec5-9d3b-26a3a31f952c"
// item.orgid
// "d3cb14ea-9db0-48fc-b53c-6388cb3f6777"
            var _mu_guid = item.mu_guid;
            var _orgid = item.mme_ProjectID;
            var _orgm = item.orgm;
            _this.$confirm(`确认加入机构？`, '操作确认', {
                confirmButtonText:'确定',
                cancelButtonText:'取消',
                type:'warning'
            }).then(x => {
                _this.do_JoinToCompany(_mu_guid, _orgid, _orgm);
            }).catch(x => {

            });

        },

        // 更新内部的消息个数的数字
        // ---------------------
        updateMsgCntNumber(NotifyCnt, IssueCnt, DocCnt, FlowCnt, willloadmsgagain){
            var _this = this;
            _this.NotifyUnReadCnt = NotifyCnt;
            _this.IssueUnReadCnt = IssueCnt;
            _this.DocUnReadCnt = DocCnt;
            _this.FlowUnReadCnt = FlowCnt;

            // 顺带再把消息加载了
            if (willloadmsgagain) {
                _this.conditionSearch(_this.selectType, _this.selectFuc);
            }
            
        },

        // 项目内部页面消息列表单个设为已读
        // 并通知外面，要更新数字和红点状态了
        // <Boot 及 <ProjectBoot 对此组件有引用
        // -----------------------------------
        setitemread(mu_guid, item) {

            var _this = this;
            _this.islistloading = true;

            // 设为已读
            _this.$axios({
                url:`${window.bim_config.webserverurl}/api/Message/JPush/SetMsgRead`,
                method:'post',
                data:_this.$qs.stringify({
                    mu_guids: mu_guid,
                    Token: this.$staticmethod.Get("Token")
                })
            }).then(x => {
                if (x.status == 200) {
                    if (x.data.Ret > 0) {


                        //bugger;
                        // 刷新
                        _this.$message.success('操作成功');
                        //_this.LoadMessage(_this.selectType);
                        _this.$emit("do_refresh_from_beusing");
                        _this.islistloading = false;
                        _this.LoadMsgUnRead();
                    } else {
                        console.error(x.data.Msg);
                        _this.islistloading = false;
                    }
                } else {
                    console.error(x);
                    _this.islistloading = false;
                }
            }).catch(x => {
                console.error(x);
                _this.islistloading = false;
                _this.$emit("do_refresh_from_beusing");
            });
        },

        do_setallread(){
            var _this = this;
            console.log(_this.selectType);
            var typearr = [];
            if (_this.selectType == '问题追踪') {
                typearr = [
                    _this.dicdata.Issue_Create,
                    _this.dicdata.Issue_Modify,
                    _this.dicdata.Issue_Delete,
                    _this.dicdata.Issue_Comment_Create,
                    _this.dicdata.Issue_Comment_Delete,
                    _this.dicdata.Issue_Doc_Create,
                    _this.dicdata.Issue_Doc_Delete
                ];
            } else if (_this.selectType == '项目文档') {
                typearr = [
                    _this.dicdata.Doc_NewFile,
                    _this.dicdata.Doc_RemoveFile,
                    _this.dicdata.Doc_ModifyFile,
                    _this.dicdata.Doc_MoveFile,
                    _this.dicdata.Doc_NewDir
                ];
            } else if (_this.selectType == '项目流程'){
                typearr = [
                    _this.dicdata.Flow_Examine,
                    _this.dicdata.Flow_Reject,
                    _this.dicdata.Flow_Submit
                ];
            } else {
                typearr = [
                ];
            }
            var arrstr = typearr.join(',');
            console.log(arrstr);
            console.log(_this.$staticmethod.Get("Token"));

            // 调用接口，修改指定Token的某些类型消息为已读
            _this.$axios({
                url:`${window.bim_config.webserverurl}/api/Message/JPush/SetMsgReadByTypeAndToken`,
                method:'post',
                data: _this.$qs.stringify({
                    Types: arrstr,
                    Token: _this.$staticmethod.Get("Token")
                })
            }).then(x => {
                if (x.status == 200) {
                    if (x.data.Ret > 0) {
                        _this.$message.success('操作成功');
                        _this.$emit("do_refresh_from_beusing");
                        _this.LoadMsgUnRead();
                    } else {
                        console.error(x);
                    }
                } else {
                    console.error(x);    
                }
            }).catch(x => {
                console.error(x);
            });
        },

        // 全部设为已读
        setallread(){
            var _this = this;

            var notreadlen = _this.UnReadDatas.length;
            if (notreadlen == 0) {
                _this.$message.warning('当前不存在未读消息');
                return;
            }

            _this
            .$confirm("确定全部置为已读？", "操作确认", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning"
            })
            .then(x => {
                _this.do_setallread();
            })
            .catch(x => {});
        },

        OpenMessageList(){
            this.$emit("OpenMessageList");
        },
        LoadMsgUnRead(loadingIns){
            console.log('Message.vue : LoadMsgUnRead');
            var token=this.$staticmethod.Get('Token');
            var url = `${window.bim_config.webserverurl}/api/Message/JPush/CurMsg_WithNum?isRead=0&buttonSign=${this.selectType}&fucSign=${this.selectFuc}&Token=${token}`;
            
            // 开始进行指定 cancelToken 的请求
            // 先指定好 cancelToken 和 cancelTokenSource
            // ----------------------------------------
            var _this = this;

            // 如果之前已赋值，则先取消请求
            // -------------------------
            if (_this.msgm.canceltoken_source) {
                _this.msgm.canceltoken_source.cancel('cancel');
            }

            _this.msgm.canceltoken = _this.$axios.CancelToken;
            _this.msgm.canceltoken_source = _this.msgm.canceltoken.source();

            // 开始请求
            // -------
            this.SearchData = [];
            this.$axios.get(url,
                {
                    cancelToken: _this.msgm.canceltoken_source.token
                }
            ).then(res=>{
                var AllDatas=res.data.Data.List;

                // 过滤出未读的，分别赋值 UnReadDatas 及 SearchData
                // ---------------------------------
                this.UnReadDatas = AllDatas;//.filter(x => x.isRead == '0'/* && msgtypes_valid.indexOf(x.LogAndMsgType) >= 0*/);
                _this.SearchData = this.$staticmethod.DeepCopy(this.UnReadDatas);
                //debugger;
                //de1bugger;
                
                // 关闭 loading
                // ------------
                this.islistloading = false;

                // 更新数字
                // -------
                this.DocUnReadCnt = res.data.Data.CntObj.DocUnReadCnt;
                this.NotifyUnReadCnt = res.data.Data.CntObj.NotifyUnReadCnt;
                this.IssueUnReadCnt = res.data.Data.CntObj.IssueUnReadCnt;
                this.FlowUnReadCnt = res.data.Data.CntObj.FlowUnReadCnt;

            }).catch(x => {
                //this.SearchData
            });
        },
        conditionSearch(buttonSign,fucSign){

            console.log('Message.vue : conditionSearch');

            // 设置右上角消息操作类型的按钮的选中样式
            // ————及左侧的表示消息模块的按钮样式
            // -----------------------------------
            var _this = this;
            _this.selectFuc = fucSign;
            _this.selectType = buttonSign;

            // 如果之前有 loading，把它隐藏
            // --------------------------
            _this.islistloading = false;
            
            // 显示 loading（小区域）
            // --------------------
            _this.islistloading = true;

            // 请求数据
            // -------
            _this.LoadMsgUnRead();

        },
        MessageContent(msgobj){
            var _this = this;
            if (msgobj.Op_RealName == '') {
                msgobj.Op_RealName = null;
            }
            console.log(msgobj.LogAndMsgType);
            //debugger;
            var outJson={};
            if (msgobj.LogAndMsgType == _this.dicdata.Issue_Create) {
                // 弹出消息
                outJson={
                    title: '【问题】',
                    message: msgobj.ExtDataContent,
                };
            } else if (msgobj.LogAndMsgType == _this.dicdata.Issue_Delete){
                outJson={
                    title: '【问题】',
                   message: msgobj.ExtData,
                };
            } else if (msgobj.LogAndMsgType == _this.dicdata.Issue_Modify) {
                outJson={
                    title: '【问题】',
                    message: msgobj.BI_Title?`【${msgobj.Op_RealName}】修改了问题【${msgobj.BI_Title}】`:msgobj.ExtData,
                };
            } else if (msgobj.LogAndMsgType == _this.dicdata.Issue_Comment_Create) {
                outJson={
                    title: '【问题】',
                    message: msgobj.ExtDataContent,
                };
            } else if (msgobj.LogAndMsgType == _this.dicdata.Issue_Comment_Delete) {
                outJson={
                    title: '【问题】',
                    message: `【${msgobj.Op_RealName}】移除了问题【${msgobj.BI_Title}】中的评论`,
                };
            } else if (msgobj.LogAndMsgType == _this.dicdata.Issue_Doc_Create) {
                outJson={
                    title: '【问题】',
                    message: `【${msgobj.Op_RealName}】向问题【${msgobj.BI_Title}】中添加了关联文档`,
                };
                //************ */
            } else if (msgobj.LogAndMsgType == _this.dicdata.Issue_Doc_Delete) {
                outJson={
                    title: '【问题】',
                    message: `【${msgobj.Op_RealName}】移除了问题【${msgobj.BI_Title}】中的关联文档`,
                };
                //*************** */
            } else if (msgobj.LogAndMsgType == _this.dicdata.Doc_NewFile) {
                outJson={
                    title: '【文档】',
                    message: msgobj.Op_RealName?`【${msgobj.Op_RealName}】上传了文档【${msgobj.mm_objname}】`:msgobj.ExtData,
                };
            } else if (msgobj.LogAndMsgType == _this.dicdata.Doc_RemoveFile) {
                outJson={
                    title: '【文档】',
                    message: msgobj.Op_RealName?`【${msgobj.Op_RealName}】删除了文档【${msgobj.mm_objname}】`:msgobj.ExtData,
                };
            } else if (msgobj.LogAndMsgType == _this.dicdata.Doc_ModifyFile) {
                outJson={
                    title: '【文档】',
                    message: msgobj.Op_RealName?`【${msgobj.Op_RealName}】修改了文档【${msgobj.mm_objname}】`:msgobj.ExtData,
                };
            } else if (msgobj.LogAndMsgType == _this.dicdata.Doc_MoveFile) {
                outJson={
                    title: '【文档】',
                    message: msgobj.Op_RealName?`【${msgobj.Op_RealName}】移动了文档【${msgobj.mm_objname}】`:msgobj.ExtData,
                };
            } else if (msgobj.LogAndMsgType == _this.dicdata.Doc_NewDir) {
                outJson={
                    title: '【文档】',
                    message: msgobj.Op_RealName?`【${msgobj.Op_RealName}】新增了文件夹【${msgobj.mm_objname}】`:msgobj.ExtData,
                };
            } else if (msgobj.LogAndMsgType == _this.dicdata.Issue_Comment_Create_At) {
                outJson={
                    title: '【问题】',
                    dangerouslyUseHTMLString: true,
                    message: `【提到了你】${msgobj.ExtDataContent}`,
                };
            } else if (msgobj.LogAndMsgType == _this.dicdata.Web_Notify) {
                 outJson={
                    title: '【站内信】',
                    message: msgobj.orgm?`【${msgobj.Op_RealName}】邀请你加入机构【${msgobj.orgm}】`:msgobj.ExtData,
                };

            // 流程相关
            } else if (msgobj.LogAndMsgType == _this.dicdata.Flow_Submit
            || msgobj.LogAndMsgType == _this.dicdata.Flow_Examine
            || msgobj.LogAndMsgType == _this.dicdata.Flow_Reject
            ) {
                 outJson={
                    title: '【流程】',
                    message: `${msgobj.ExtDataContent}`,
                };

            } else {
                //console.warn('有消息类型未处理', msgobj);
                //debugger;
            }

            if (outJson && outJson.message) {
                return outJson.message.replace('【】','');
            } else {
                return '';
            }
            
        },
    },
    created(){

       
    },
    mounted(){

        // 默认加载问题追踪————全部
        // ----------------------
        var _this = this;
        _this.dicdata = _this.$staticmethod.getMsgTypesObj();
        //deb1gger;

        // 先赋值
        _this.NotifyUnReadCnt = _this.InitNotifyUnReadCnt;
        _this.IssueUnReadCnt = _this.InitIssueUnReadCnt;
        _this.DocUnReadCnt = _this.InitDocUnReadCnt;
        _this.FlowUnReadCnt = _this.InitFlowUnReadCnt;

        window.msgvue = _this;

        // 看哪一栏有消息，就显示那一栏的，都没有，则显示站内信
        // -----------------------------------------------
        if (_this.NotifyUnReadCnt > 0) {
            _this.conditionSearch('站内信', '全部');
        } else if (_this.IssueUnReadCnt > 0) {
            _this.conditionSearch('问题追踪', '全部');
        } else if (_this.DocUnReadCnt > 0) {
            _this.conditionSearch('项目文档', '全部');
        } else if (_this.FlowUnReadCnt > 0) {
            _this.conditionSearch('项目流程', '全部');
        } else {
            _this.conditionSearch('站内信', '全部');
        }

    }
}
</script>
<style scoped>

._css-join-btn{
    /* margin-right: 0;
    width: 32px;
    height: 20px;
    line-height: 20px;
    position: absolute;
    border: 1px solid transparent;
    background-color: #1890FF;
    color: #fff;
    border-radius: 4px;
    text-align: center;
    box-sizing: border-box;
    cursor: pointer;
    right: 52px;
    bottom: 8px;
    opacity: 0.8; */

    margin-right: 0;
    width: auto;
    padding: 2px 6px 2px 6px;
    line-height: 20px;
    position: absolute;
    border: 1px solid transparent;
    background-color: #1890FF;
    color: #fff;
    border-radius: 4px;
    text-align: center;
    box-sizing: border-box;
    cursor: pointer;
    right: 52px;
    bottom: 5px;
    opacity: 0.8;

}

._css-join-btn:hover{
    opacity: 1;
}

._css-list-item{
    position: relative;
}

._css-setreadall {
    color:rgba(0, 0, 0, 0.4);
    position:absolute;
    right: 12px;
    cursor:pointer;
    width:24px;
    height:24px;
    font-size: 17px;
    line-height: 24px;
}

._css-setreadall:hover {
    background-color: rgba(24, 144, 255, 0.1);
    color: rgba(24, 144, 255, 1);
}

._css-setreaditem {
    position: absolute;
    right: 12px;
    cursor: pointer;
    width: 24px;
    /* height: 100%; */
    font-size: 17px;
    /* line-height: 24px; */
    /* top: 0; */
    bottom: 6px;
    right: 12px;
    display: flex;
    align-items: center;
}

._css-setreaditem-in {
    color: rgba(0, 0, 0, 0.4);
    right: 12px;
    cursor: pointer;
    width: 24px;
    height:24px;
    font-size: 17px;
    /* line-height: 24px; */
    top: 0;
    right: 12px;
    line-height: 24px;
}

._css-setreaditem-in:hover {
    background-color: rgba(24, 144, 255, 0.1);
    color: rgba(24, 144, 255, 1);
}
._css-minimsg-ifnum{
    width:14px;
    height:14px;
    border-radius: 2px;
    font-size: 12px;
    line-height: 14px;
    color:rgba(255, 255, 255, 1);
    background-color: rgba(245, 34, 45, 1);
}

._css-minimsg-text{
    width:48px;
    height:22px;
    line-height: 22px;
    color:rgba(0,0,0,0.65);
    font-size: 14px;
}

._css-minimsg-module{
    height:40px;
    margin-top:16px;
    display: flex;
    align-items: center;
    cursor:pointer;
}

._css-minimsg-module:hover, ._css-minimsg-module._css-sel{
    background-color: rgba(0, 0, 0, 0.04);
    font-weight: 500;
}

._css-minimsg-module._css-sel ._css-minimsg-text{
    font-weight: bold;
}

._css-minimsg-module ._css-ifselborder{
    width:4px;
    height:100%;
    background-color: transparent;
}

._css-minimsg-module._css-sel ._css-ifselborder{
    width:4px;
    height:100%;
    background-color: rgba(24, 144, 255, 1);
}

._css-minimsg-viewall{
    position: absolute;
    width:100%;
    bottom:16px;
    height:28px;
    display: flex;
    align-items: center;
    justify-content: space-around;
}

._css-minimsg-viewall-in{
    width:28px;
    height:28px;
    color:rgba(0,0,0,0.4);
    font-size: 20px;
    cursor: pointer;
    line-height: 28px;
}

._css-minimsg-viewall-in:hover{
    color:#1890FF;
    background-color: rgba(24, 144, 255, 0.1);
}

._css-msg-typebtns {
    display: flex;
    align-items: center;
    position: relative;
}

._css-emptyctn{
    height: 100%;
    box-sizing: border-box;
    display: flex;
    align-items: center;
}

._css-emptyctn-in{
    text-align: center;
    width:100%;
    color:rgba(0,0,0,0.35);
}

ul,li{padding:0px;margin:0px;list-style-type: none;}
.bottomDiv{
    width:50px;
    height:453px;
    position: absolute;
    display: block;
    /* z-index: 999; */
    background-color:transparent;
    top:12px;
    right:120px;
    overflow:visible;
    /* font-family:PingFangSC; */
    cursor: normal;
}
._css-minimsg-divctnl {
    width: 70px;
    display: flex;
    flex-direction: column;
    position: relative;
    background-color: rgba(0, 0, 0, 0.04);
}
.Message_Mini{
    width: 500px;
    height: 425px;
    background-color: #fff;
    box-shadow: 0px 1px 6px #dfdfdf;
    border-radius: 2px;
    overflow: hidden;
    position: fixed;
    right: 24px;
    top: 48px;
    z-index: 1000;
    display: flex;
}
.Message_Mini .title{
    width:100%;height:40px;
}
.Message_Mini .title li{
    width:110px;float:left;height:40px;line-height:40px;font-size:14px;color:rgba(0,0,0,0.65);position: relative;cursor: pointer;
}
.Message_Mini .title li i{
    display:block;position:absolute;right:10px;top:13px;width:14px;height:14px;font-style:normal;background-color: red;color:#fff;border-radius: 2px;text-indent: 0px;line-height:14px;font-size: 12px;text-align: center;
}
.Message_Mini .title li.sel{
    border-bottom:2px solid rgba(24,144,255,1);height:38px;
}
.Message_Mini .btns{
    width:100%;height:54px;font-size:0px;
}
.Message_Mini .btns button{
    width:60px;
    height:24px;
    font-size:12px;
    color:#999;
    /* margin:11px 16px 0px 0px; */

    border:none;
    background-color:#f5f5f5;
    border-radius: 20px;
    cursor: pointer;
    outline: none;

    margin-left:8px;

}

._css-minimsg-divctnr {
    display: flex;
    flex-direction: column;
    height:100%;
    width:calc(100% - 70px);
    padding-left: 16px;
    padding-right: 16px;
    box-sizing: border-box;
}

.Message_Mini .btns button:first-child{
    margin-left: 0px;
}

.Message_Mini .btns button.sel{
    background-color:rgba(24, 144, 255, 1);color:#fff;
}
.Message_Mini .fuc{
    width:100%;height:59px;
}
.Message_Mini .list{
    width:calc(100%);
    /* height:calc(100% - 40px - 54px - 59px); */
    overflow-x:hidden;
    overflow-y:scroll;
    flex:1;
    margin-bottom: 9px;
}
.Message_Mini .list ul{
    /* width:100%;
    height:64px;
    background-color:rgba(0, 0, 0, 0.02);
    margin-top:8px;
    padding-top:12px;
    box-sizing: border-box; */

    width: 100%;
    background-color: rgba(0, 0, 0, 0.02);
    margin-top: 8px;
    padding-top: 12px;
    padding-bottom: 2px;
}
.Message_Mini .list ul:hover{
    background-color:rgba(24, 144, 255, 0.1);
}



.Message_Mini .list ul li .t{
    width: calc(100% - 16px - 40px);
    height:100%;
    float:left;
    display: block;
    color:rgba(0,0,0,0.85);
    font-weight:bold;
    overflow:hidden;
    text-overflow:ellipsis;
    white-space: nowrap;
}
.Message_Mini .list ul li .isUnRead{
    width:7px;height:7px;display: block;float:left;background-color: rgba(24, 144, 255, 1);border-radius:50px;margin:6px 6px 0px 0px;
}
.Message_Mini .list ul li .CreateDate{
    color:#bfbfbf;text-indent:12px;display: block;width:100%;height:100%;
}
/* .Message_Mini .list ul li:first-child{
    
} */

.list_ulli{
    width:calc(100% - 32px);
    height:20px;
    margin-bottom:6px;
    text-align: left;
    padding:0px 16px 0px 16px;
    line-height:20px;
    font-size:12px;
}

/* 站内信或其它消息的标题内容部分 */
.list_ulli_firstchild{
    width:100%;
    /* height:20px; */
    margin-bottom:6px;
    font-size: 12px;
    padding:0 16px 0 16px;
    text-align: left;
    box-sizing: border-box;
}

.Message_Mini .fuc .OpenMessageList{
    width:298px;height:32px;border:none;outline:none;background:rgba(0,0,0,0.04);color:rgba(0,0,0,0.65);cursor: pointer;line-height: 30px;padding: 0px;margin-top:20px;font-size:14px;
}
</style>