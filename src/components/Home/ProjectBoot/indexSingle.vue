<template>
  <div class="_css-model-page"  @click="hideModelMenue($event)">
   
    <div v-if="ModelList.length!=0&&ModelListTyep=='list'" class="Models">
      <div class="unit" v-for="item in ModelList" :key="item.ID">
     
      
        <img v-bind:src="GetModelImg(item.Thumbnail,item.IsMerge)" @click="openModel(item,'','')" />
        <span class="modelName" :title="item.Name">{{item.Name}}</span>
        <span class="updateDate">
          更新时间：{{ new Date(item.CreateTime).toLocaleString('chinese',{hour12:false}) }}
          <i class="icon-interface-set_se setting noHide" :title="'菜单'" @click="CurrentModelId=item.ID;CheckMenuPos($event);"></i>
          <i class="icon-interface-list-fill viewList noHide" :title="'视图列表'" @click="openViewList(item)"></i>

          <ul :id="'sul' + item.ID.replace('-', '_')" :data-debug="item.ID"  v-show="CurrentModelId==item.ID" @mouseover="coveryMenu=true;CurrentModelId=item.ID"  class="menu" :class="NeedChangePos?'bm':''"> <!--v-show="CurrentModelId==item.ID" -->
            <li class="line"><font @click="openModelInfo(item)" class="marginBottom noHide"><i class="icon-interface-model_list"></i>模型详情</font></li>
            <!-- <li class="line"><font @click="openModel(item,'')" class="marginBottom noHide"><i class="icon-interface-folder"></i>打开</font></li> -->
            <li class="line"><font @click="TargetDOCList(item)" class="marginTop marginBottom noHide"><i class="icon-interface-document"></i>查看关联文档</font></li>
            <li><font @click="updateModel(item.ID)" class="marginTop noHide"><i class="icon-interface-cloud-download_could"></i>更新模型</font></li>
         <li
             
             ><font class="marginTop noHide" @click="openProgress(item)"><i class="icon-interface-problem-status"></i>进度方案</font></li>
          </ul>
        </span>
      </div>
    </div>
 
    <div v-else>暂无数据</div>
  
  
    <div v-show="IsDialogCovery" class="Covery"></div>
  
   
    <!-- 进度 -->
      <CompsFullScreenDialog2
              class="el-edit-progress-wp"
              :title="'进度管理'"
              @closeCallBack="ganttDialogCallBack"
              v-if="editGanttShow"
          >
              <section slot="btn" class="top-btn">
               
              </section>
              <section slot="main" class="full-screen-main el-progress-wp">
                  <div class="btn-wp">
                    <div class="left-btns">  </div>
                    <div class="right-btns">
                   
                      <div v-if="!addOpenModel" @click.stop="openmodelview2"><i class="icon-interface-guanlianmoxing"></i>{{'打开模型'}}</div>
                      <div v-else @click.stop="closemodelview2"><i class="icon-interface-guanlianmoxing"></i>{{'关闭模型'}}</div>
                      <div @click="mockConfigClick"><i class="icon-interface-set_se"></i>
                          模拟设置
                      </div>
                      <div
                      v-if="playsign == 'end'"
                        class="playmodel"
                        @click="mockprogressclick"
                        :class="{'ban-click':bStartDis}"><i class="icon-interface-play"></i>
                          开始模拟
                      </div>
                         <div
                      v-if="playsign == 'pause'"
                        class="playmodel"
                        @click="mockprogressclick"
                        :class="{'ban-click':false && loadingModelEnd == true}"><i class="icon-interface-play"></i>
                          继续模拟
                      </div>
                      <div
                      v-if="playsign == 'start'"
                        class="pausemodel"
                        @click="pausemodelClick"
                        :class="{'ban-click':false && loadingModelEnd == true}"><i class="icon-interface-time-out"></i>
                          暂停模拟
                      </div>
                      <div
                        v-if="playsign != 'end'"
                        class="stopmodel"
                        @click="stopPlayModel()"
                        :class="{'ban-click':false && loadingModelEnd == true}"><i class="icon-interface-stop"></i>
                          停止模拟
                      </div>
                    </div>
                  </div>
                  <div class="iframes-wp">
                    <iframe
                        @load="ganttIframeLoad"
                        v-if="currentGanttDataInfo !== null"
                        :src="`${getBimConfig().integrated_planurl}/demo/Project.html?id=${getplanid()}&bcheckcolumn=1&pagetype=readonly&Token=${this.$staticmethod.Get('Token')}`"
                        frameborder="0"
                        ref="addOpenPlanView"
                        id="addOpenPlanView"
                        style="flex:1;"
                    ></iframe>

                    <CompsdragWp style="width:700px;" v-if="addOpenModel">
                        <modelPlay
                        ref="ref_modelPlay"
                          slot="main"
                          :showplaycomps='showplaycomps'
                          @progressPlayLoad="modeliframeload"
                          :modelid="modelid"
                          :bimcomposerId="bimcomposerId"
                          :planid="getplanid()"
                          :iscache=false
                          @playStart="_playStateSign('start')"
                          @playPause="_playStateSign('pause')"
                          @playEnd="_playStateSign('end')"
                          :playEnd="playEndFun"
                        ></modelPlay>

                    </CompsdragWp>
                  </div>

              </section>
          </CompsFullScreenDialog2>
    <!-- 进度 -->

    <!-- 选择类型和视图的弹框 -->
    <div
    v-if="showGISPuboptionDialog"
    @click.stop="showGISPuboptionDialog = false"
     class="_css-selectviewtype-dialog" >
      <div
      id="id_pubgis"
      :style="getGISPubOptionDialogInPos()"
      class="_css-selectviewtype-dialog-in" >

        <!-- title -->
        <div class="_css-gispubdialog-title">
          <div
          style="visibility:hidden;"
          class="icon-arrow-left_outline _css-gispubdialog-titleback">
          </div>
          <div class="_css-gispubdialog-text">
            {{somemodelcontext_isrepub?'重新发布到GIS':'发布到GIS'}}
          </div>
             <div class="_css-gispubdialog-titleother">
          </div>
        </div>
        <!-- //title -->

        <!-- labeltype-->
        <div class="_css-gispubdialogtype-label _css-gispubdialog-label" >
          类型
        </div>
        <div
        @click.stop="switch_GISPub_typesel()"
        class="_css-gispubdialogtype-ctrl _css-gispubdialog-ctrl" >
          <div :title="GetSelectedTypeText()" class="_css-gispubdialog-cbtext" >{{GetSelectedTypeText()}}</div>
          <div class="_css-gispubdialog-cbicon icon-arrow-down_outline" ></div>

          <div
          v-if="show_GISPub_Type"
          class="_css-gispubdialogtypes _css-gispubitems" >

              <div
              v-for="t in GISPub_AllType"
              :key="t.id"
              @click.stop="selectgispubtype(t.id)"
              class="_css-gispubitemi-ctn" >
                <div :title="t.name" class="_css-gispubitemi" >{{t.name}}</div>
              </div>
          </div>

        </div>
        <!-- //labeltype-->

        <!-- labelview -->
        <div class="_css-gispubdialogview-label _css-gispubdialog-label" >
          视图
        </div>
        <div
        @click.stop="switch_GISPub_viewsel()"
        class="_css-gispubdialogview-ctrl _css-gispubdialog-ctrl" >
          <div :title="GetSelectedViewText()" class="_css-gispubdialog-cbtext" >{{GetSelectedViewText()}}</div>
          <div class="_css-gispubdialog-cbicon icon-arrow-down_outline" ></div>

          <div
          v-if="show_GISPub_view"
           class="_css-gispubdialogtypes _css-gispubitems" >
              <div class="_css-gispubitemi-ctn"
              v-for="sv in GISPub_AllView"
              :key="sv.ID"
              @click.stop="selectgispubview(sv.ID)"
              >
                <div :title="sv.Name" class="_css-gispubitemi" >{{sv.Name}}</div>
              </div>
          </div>

        </div>
        <!-- //labelview -->

        <!-- policyview -->
        <div class="_css-gispubdialogview-label _css-gispubdialog-label" >
          发布策略
        </div>
        <div
        @click.stop="switch_GISPub_viewpolicy()"
        class="_css-gispubdialogview-ctrl _css-gispubdialog-ctrl"
        :class="{'_css-disabled': bEnabled_GISPub_Policy != true}"
        >
          <div :title="GetSelectedPolicyText()" class="_css-gispubdialog-cbtext" >{{GetSelectedPolicyText()}}</div>
          <div class="_css-gispubdialog-cbicon icon-arrow-down_outline" ></div>

          <div
          v-if="show_GISPub_Policy"
           class="_css-gispubdialogtypes _css-gispubitems" >
              <div class="_css-gispubitemi-ctn"
              v-for="gp in GISPub_Policy"
              :key="gp.ID"
              @click.stop="selectgispubpolicy(gp.ID)"
              >
                <div :title="gp.Name" class="_css-gispubitemi" >{{gp.Name}}</div>
              </div>
          </div>

        </div>
        <!-- //policyview -->

        <!-- labelbtn-->
        <div class="_css-gispubdialogbtnok-ctn" >
          <div
          @click.stop="setPlace2()"
           class="_css-gispubdialogbtnok" >
            确定
          </div>
        </div>
        <!-- //labelbtn-->

      </div>
    </div>
    <!-- //选择类型和视图的弹框 -->

    <!-- 进度管理，自动关联功能弹出对话框 -->

    <!-- //进度管理，自动关联功能弹出对话框 -->

  </div>
</template>
<script>
import reNameForm from '@/components/Home/ProjectBoot/Model/ReNameForm' //重命名
import MoveModelPhase from '@/components/Home/ProjectBoot/Model/MoveModelPhase' //移动到阶段
import BimViewer from '@/components/Home/ProjectBoot/Model/BIMViewer' //打开模型
//import ModelMerge from '@/components/Home/ProjectBoot/Model/Merge' //合并模型
import ModelMerge from '@/components/Home/ProjectBoot/Model/Merge_V2' //合并模型
import ModelShare from '@/components/Home/ProjectBoot/Model/ModelShare' //分享模型
import ModelViewList from '@/components/Home/ProjectBoot/Model/ViewList' //视图列表
import UpdateModelThumbnail from '@/components/Home/ProjectBoot/Model/UpdateModelThumbnail'//更改缩略图
import TargetDocList from '@/components/Home/ProjectBoot/Model/TargetDocList'//关联文档弹窗
import ModelInfo from '@/components/Home/ProjectBoot/Model/ModelInfo'
import ViewDoc from '@/components/Home/ProjectBoot/Document/ViewDoc'
import CompsFullScreenDialog2 from '@/components/CompsDialog/CompsFullScreenDialog2'
import progressList from "@/components/CompsProgress/progressList"
import CompsdragWp from '@/components/CompsDialog/CompsdragWp'
import modelPlay from '@/components/CompsProgress/modelPlay'
import CompsAutoRel from '@/components/CompsProgress/CompsAutoRel'
export default {
  components:{
    modelPlay,
    CompsdragWp,
    progressList,
    reNameForm,
    MoveModelPhase,
    BimViewer,
    ModelMerge,
    ModelShare,
    ModelViewList,
    UpdateModelThumbnail,
    TargetDocList,
    ModelInfo,//模型详情
    ViewDoc,
    CompsFullScreenDialog2,
    CompsAutoRel
  },
  data() {
    return {

      
//       1: "67170069-1711-4f4c-8ee0-a715325942a1"
// pl1anid: "c6999d4a-0f7c-42b0-a1f3-c9ed166ac62f"
// proje1ctid: "46d11566-6b7e-47a1-ba5d-12761ab9b55c"
// title: "进度线测试"

      // demo
      // ----
      title: '进度模拟',
      planid: '',
      modelid: '',
      bimcomposerId: '',



      // bimviewer 的额外的右键菜单
      // ------------------------
      arrExtraContextMenus: [],

      // bimviewer 加载后，是否需要立即高亮并 zoom 一些构件
      //    以及：哪些构件
      // -----------------------------------------------
      bSelectAndZoomAfterRender: '', 
      arrSelectAndZoom: [],

      bStartDis: false, // 刚点击完开始模拟时，立即dis掉，当 playSign 修改为 start 时，立即取消dis.
      playsign: 'end', // 当前的模型模拟状态
      bPlayAfterFinishRender: false, // 加载模型完成后，立即开始模拟

      currentModelInfo_ID:'',
      currentModelInfo_Name:'',
      bShowProgressAutoRel: false, // 是否显示进度管理的自动关联对话框
      GISPub_ModelCache: {},
      GISPub_TypeSelectedId: '-1',
      GISPub_ViewSelectedId: '-1',
      GISPub_PolicySelectedId: '-1',
      //'bim-normal','bim-b3dm','bim-i3dm','bim-i3s'
      GISPub_AllType:[{id:'-1', name:'请选择GIS类型'}
      ,{id:'bim-normal', name:'bim-normal'}
      ,{id:'bim-b3dm', name:'bim-b3dm'}
      // ,{id:'bim-i3dm', name:'bim-i3dm'}
      // ,{id:'bim-i3s', name:'bim-i3s'}
      ],
      GISPub_AllView:[{ID:'-1', Name:'请选择模型视图'}],
      GISPub_Policy:[{ID:'-1',Name:'请选择发布策略'}, {ID:'0',Name:'文件大小优先'}, {ID:'1',Name:'速度优先'}],
      show_GISPub_Type: false,
      show_GISPub_view: false,
      show_GISPub_Policy: false,

      bEnabled_GISPub_Policy: false, // 发布策略选项是否可用

      somemodelcontext_left: 0,
      somemodelcontext_top: 0,
      somemodelcontext_isrepub: false,
      showGISPuboptionDialog: false, // 显示选择类型及视图的gis对话框
      showEditMenuFromRight: false,
      extdata: {},
      ModelList: [],

      SelModelObj:{},//选择的当前模型对象
      ShareModel:{},
      GISModel:{},
      ViewListModel:{},
      ProjectId:"",

      SearchModelName:"",//搜索的模型名
      NeedChangePos:true,
      tranIndex:-1,//模型详情的Z-index
      AddorEditGIS:'Add',//编辑、新增GIS Sign
      itemMenuName:"",//移动到子菜单的名字

      showBtnMenu:false,//显示按钮菜单
      CurrentModelId:"",//显示模型菜单
      CurrentModelId_Cache:'', // 最后一次显示模型菜单的模型ID
      showReModelName:false,//显示重命名弹窗
      showModelPhase:false,//显示移动阶段弹窗
      showModelView:false,//显示模型视图（打开模型BIMViewer）
      showMerge:false,//显示合并弹窗
      showViewList:false,
      showViewDoc:false,//显示预览文件弹窗
      MergePhase:"",//当前阶段
      showModelShare:false,//模型分享
      LoadShareUrl:true,
      showGIS:false,//显示GIS选点页
      coveryMenu:false,//显示模型菜单
      showUpdateThumbnail:false,//显示更改模型缩略图组件
      showTargetDocList:false,//显示关联文档
      showModelInfo:false,//显示模型详情
      IsDialogCovery:false,//显示遮罩层

    


      BIM_Session:"",//模型参数返回的Session
      ModelIds:"",//所有模型Id
      AllProjectLayer:[],//所有项目GIS图层数据
      OpenDocViewUrl:'',


      isLoading_V2:true,
      StopLoading:1,//传递子页，让其暂时不加载

      FunctionBtnNum:8,//模型菜单功能按钮个数
      ModelIdsOfHasGIS:[],//已经拥有GIS数据的模型Id
      ModelIdsOfHasGIS_UnVaild:[],//所有已经禁用的GIS数据
      ModelListTyep:"list", //模型列表展现方式

      addIframePlanID:'',//taskid
      addDialogBtnName:'打开模型',//编辑模型text
      addOpenModel:false,//判断是否打开模型
      editGanttShow:false,
      addInputData: "",

 
      currentModelInfo:null,
      currentGanttDataInfo:null,
      ganttBtnsConfig:{
        btnName:'打开模型'
      },
      gannCurrentSelectData:[],
      modelCurrentSelectData:[],
      showplaycomps:false,//模拟按钮
      editModelIframeWindow:null,
      loadingModelEnd: true
    };
  },
  props: {
    Phase: {
      type: String,
      required: false
    },
  },
  methods:{

    getplanid(){
      return this.planid;
    },

    // 设置 bimviewer 中的菜单项
    // ------------------------
    modifyExtraContextMenus(){
        var _this = this;
        // --------------
        //console.log(_this.modelCurrentSelectData);

        if (_this.modelCurrentSelectData.length > 1) {
            _this.arrExtraContextMenus = [
                {
                  label:'选了多个构件',callback:function(){console.log(385, arguments)}
                }
            ];
            _this.$refs.modelPlay.setContextMenu(_this.arrExtraContextMenus);
        } else if (_this.modelCurrentSelectData.length > 0) {
            _this.arrExtraContextMenus = [
                {
                  label:'只选了一个构件',callback:function(){console.log(385, arguments)}
                }
            ];
            _this.$refs.modelPlay.setContextMenu(_this.arrExtraContextMenus);
        }
    },

    // 
    _playStateSign(sign) {
      var _this = this;
      _this.playsign = sign;
      //_this.$message.warning(sign);
    },

    // 暂停模拟
    // -------
    pausemodelClick() {
      var _this = this;
      setTimeout(()=>{
        _this.$refs.ref_modelPlay.stopMovie(true);
      }, 10);
    },

    // 停止模拟
    // -------
    stopPlayModel(){

      // 如果模型模拟播放控制台打开了，关掉
      // -------------------------------
      var _this = this;
      if (_this.showplaycomps) {
        _this.showplaycomps = false;
      }   

      // 之前没有停止操作
      // ---------------
      setTimeout(()=>{
        _this.$refs.ref_modelPlay.forceStop(false, true);
      }, 10);

    },

    setSliderDate(date) {
      var _this = this;
      if (_this.$refs.ref_modelPlay) {
        _this.$refs.ref_modelPlay.triggerSliderDate(date);
      }
    },

    //
    do_CompsAutoRel_onrel(modelarr, taskProperty, eleProperty){
    
    },

    //
    CompsAutoRel_onrel(modelarr, taskProperty, eleProperty){
    

    },

    //
    CompsAutoRel_oncancel(){
      var _this = this;
      _this.bShowProgressAutoRel = false;
    },

    //
    ProgressAutoRel(ev) {
      var _this = this;
      _this.currentModelInfo_ID = _this.currentModelInfo.ID;
      _this.currentModelInfo_Name = _this.currentModelInfo.Name;
      _this.bShowProgressAutoRel = true;
    },

    _hideModelInfo(){
      var _this = this;
      _this.showModelInfo=false;
      _this.IsDialogCovery=false;
    },

    selectgispubview(viewid) {
      var _this = this;
      _this.GISPub_ViewSelectedId = viewid;
      _this.closeall();
    },

    selectgispubtype(typeid){
      var _this = this;
      _this.GISPub_TypeSelectedId = typeid;
      _this.closeall();

      // 设置是否禁用发布策略
      _this.bEnabled_GISPub_Policy = typeid == 'bim-b3dm';
    },

    selectgispubpolicy(policyid) {
      var _this = this;
      _this.GISPub_PolicySelectedId = policyid;
      _this.closeall();
    },

    backtorightmenu(){
      var _this = this;
      _this.CurrentModelId = _this.CurrentModelId_Cache;
      _this.showGISPuboptionDialog = false;
    },

    closeall(){
      var _this = this;
      // _this.showGISPuboptionDialog = false;
      //_this.showEditMenuFromRight = false;
      _this.show_GISPub_Type = false;
      _this.show_GISPub_view = false;
      _this.show_GISPub_Policy = false;
    },

    switch_GISPub_typesel(){
      var _this = this;
      _this.show_GISPub_view = false;
      _this.show_GISPub_Policy = false;
      if (_this.show_GISPub_Type == true) {
        _this.show_GISPub_Type = false;
      } else {
        //_this.closeall();
        _this.show_GISPub_Type = true;
      }
    },

    switch_GISPub_viewsel(){
      var _this = this;
      _this.show_GISPub_Type = false;
      _this.show_GISPub_Policy = false;
      if (_this.show_GISPub_view == true) {
        _this.show_GISPub_view = false;
      } else {
        //_this.closeall();
        _this.show_GISPub_view = true;
      }
    },

    switch_GISPub_viewpolicy(){
      var _this = this;

      // 如果禁用了发布策略，则直接跳出
      if (_this.bEnabled_GISPub_Policy == false) {
        return;
      }

      _this.show_GISPub_view = false;
      _this.show_GISPub_Type = false;
      if (_this.show_GISPub_Policy == true) {
        _this.show_GISPub_Policy = false;
      } else {
        _this.show_GISPub_Policy = true;
      }
    },

    GetSelectedTypeText(){
      var _this = this;
      var index = _this.GISPub_AllType.findIndex(x => x.id == _this.GISPub_TypeSelectedId);
      if (index >= 0) {
        return _this.GISPub_AllType[index].name;
      } else {
        return '';
      }
    },

    GetSelectedViewText(){
      var _this = this;
      var index = _this.GISPub_AllView.findIndex(x => x.ID == _this.GISPub_ViewSelectedId);
      if (index >= 0) {
        return _this.GISPub_AllView[index].Name;
      } else {
        return '';
      }
    },

    GetSelectedPolicyText() {
      var _this = this;
      var index = _this.GISPub_Policy.findIndex(x => x.ID == _this.GISPub_PolicySelectedId);
      if (index >= 0) {
        return _this.GISPub_Policy[index].Name;
      } else {
        return '';
      }
    },

    getGISPubOptionDialogInPos(){
      var _this = this;
      var _s = {};
      _s["left"] = _this.somemodelcontext_left + 'px';
      _s["top"] = _this.somemodelcontext_top + 'px';
      return _s;
    },

    // 确保控制界面已经显示
    // ------------------
    playModelControlShowAndPlay() {

      // 如果此时控制界面没有显示，立即显示
      // -------------------------------
      var _this = this;
      if (!_this.showplaycomps) {
        _this.showplaycomps = true;
      }

      // 
      // 开始模拟测试
      _this.$refs.ref_modelPlay.playMovie();
      _this.bStartDis = false;

    },

    // 模拟设置
    // -------
    mockConfigClick() {
      let _this = this
      // 如果模型页面已经打开了，判断是否正在模拟
      // ------------------------------------
      if (_this.addOpenModel == true) { 
          _this.showplaycomps = !_this.showplaycomps
          // 显示控制界面
          // -----------
          // _this.playModelControlShow();

      } else {
          _this.bShowControlAfterFinishRender = true;
          _this.addOpenModel = true;
          _this.showplaycomps = true
      }

    },

    // 仅开始模拟
    // ---------
    mockprogressclick(){
      let _this = this;

      if (_this.bStartDis) {
        return;
      }
      _this.bStartDis = true;

      // 如果模型页面已经打开了，判断是否正在模拟
      // ------------------------------------
      if (_this.addOpenModel == true) {

        // 确保控制界面已经显示
        // ------------------
        _this.playModelControlShowAndPlay();

      } else {

        _this.bPlayAfterFinishRender = true;
         _this.addOpenModel = true;

      }

      // if(_this.ganttBtnsConfig.btnName == '关闭模型'){
      //   _this.showplaycomps = !_this.showplaycomps
      //   if(_this.showplaycomps){
      //     _this.editModelIframeWindow.BI1Me.control.BIMeIsolate.isolateElementByElementId('');
      //   }else{
      //     _this.playEndFun()
      //   }
      // }

    },
    tiggerGanttBtns(str){
      let ifa = this.$refs.addOpenPlanView
      let data = {act:'reqToGtt_triggerBtn',type:str}
      ifa.contentWindow.postMessage(data,'*')
    },
    // 进度模拟点击播放发出message
    playerStartPlaying() {
      // alert("点击播放==760")
      // let ifa = this.$refs.addOpenPlanView
      // let data = {act:'clickPlayerModel',type:'startPlaying'}
      // ifa.contentWindow.postMessage(data,'*')
    },
    // 进度模拟播放结束取消隔离
    playEndFun() {

      //this.editModelIframeWindow.BI1Me.control.BIMeIsolate.removeAllIsolateElement();
      this.$staticmethod.bimhelper_getIsolateUtility(this.editModelIframeWindow).removeAllIsolateElement();
      let ifa = this.$refs.addOpenPlanView
      let data = {act:'clickPlayerModel',type:'startPlaying'}
      ifa.contentWindow.postMessage(data,'*');

    },

    modeliframeload_onFinishRender() {
      var _this = this;
      // iframe.contentWindow.BI1Me.control.BIMeIsolate.isolateElementByElementId('');
      _this.loadingModelEnd = false;
  
      // 是否在模型加载完成后立即开始模拟
      // -----------------------------
      if (_this.bPlayAfterFinishRender == true) {
          
          // 立即模拟及恢复默认值
          // ------------------
          _this.bPlayAfterFinishRender = false;
          _this.playModelControlShowAndPlay();
      }

      // 是否在模型加载完成后立即高亮构件及 zoom 
      // ------------------------------------
      if (_this.bSelectAndZoomAfterRender == true) {
        
          // 高亮及 zoom，及恢复默认值
          // -----------------------
          //_this.editModelIframeWindow.BI1Me.control.BIMeZoom.1zoomElementByElementId(_this.arrSelectAndZoom[0]);
          _this.$staticmethod.bimhelper_getzoomer(_this.editModelIframeWindow).zoomElementByElementId(_this.arrSelectAndZoom);

          // 高亮
          //_this.editModelIframeWindow.BI1Me.control.BIMeSelector.selectorEleme1ntByElementId(_this.arrSelectAndZoom);
          //_this.$staticmethod.bimhelp1er_highlight(_this.editModelIframeWindow)(_this.arrSelectAndZoom);
          var highlightobj = _this.$staticmethod.bimhelper_highlight(_this.editModelIframeWindow);
          _this.$staticmethod.bimhelper_callhighlight(highlightobj, _this.arrSelectAndZoom);

          _this.arrSelectAndZoom = [];
          _this.bSelectAndZoomAfterRender = false;
      }
    },



    modeliframeload(el){
      let iframe = el
      let _this  = this
        console.log('onload')
        let iframeWindow = iframe.contentWindow
        this.editModelIframeWindow = iframe.contentWindow

        // iframeWindow.BI1Me.event.BIMeEvent.finishRender.subscribe(() => {
        // })
        _this.$staticmethod.bimhelper_finishrender(iframeWindow, ()=>{
          _this.modeliframeload_onFinishRender();
        });

        //iframeWindow.BI1Me.view.BIMeSelection.isShowMultipleSel1ection(false);
        console.error('暂不支持 isShowMultipleSelection');

        // iframeWindow.BI1Me.control.BIMeUtility.addRightMenu(
        //   // [
        //   //     // {
        //   //     //     label:'添加选中的构件到单个任务',callback:window.top.addRelation
        //   //     //   },{
        //   //     //     label:'从选中任务及构件关联',callback:window.top.cancelRelation
        //   //     //   },{
        //   //     //     label:'显示已关联任务',callback:window.top.showRelation
        //   //     //   }
        //   //       ]
        // _this.arrExtraContextMenus);

        _this.$staticmethod.bimhelper_addRightMenu(iframeWindow, _this.arrExtraContextMenus);

        _this.$staticmethod.bimhelper_onSelect(iframeWindow, (elementid)=>{
            _this.modelCurrentSelectData = elementid;
            _this.modifyExtraContextMenus();
          });

        // iframeWindow.BI1Me.control.BIMeSelector.selec1torCallBack(
        //   (elementid)=>{
        //     // debugger;
        //     _this.modelCurrentSelectData = elementid;
        //     _this.modifyExtraContextMenus();
        //   }
        // )

    },

    // 仅打开模型，但立即开始模拟、高亮聚焦等参数走默认值
    // -----------------------
    openmodelview2(){
      var _this = this;
      _this.addOpenModel = true;
    },

    do_closemodelview2() {
      var _this = this;

      // 关闭模型小窗口，播放控制界面
      // -------------------------
      _this.$refs.ref_modelPlay.forceStop(false, true);
      _this.showplaycomps = false;
      _this.addOpenModel = false;
    },

    closemodelview2(){
      var _this = this;

      // 判断当前的播放状态
      // ----------------
      if (_this.playsign != 'end') {

        // 给出操作提示
        _this.$confirm('当前正在模拟，是否立即关闭？', '操作确认', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type:'warning'
        }).then(x => {
          _this.do_closemodelview2();
        }).catch(x => {

        });

      } else {
        _this.do_closemodelview2();
      }

      
      
    },

    // 进度模拟左侧的“打开模型”按钮点击事件处理
    openmodelview(){
      if(this.ganttBtnsConfig.btnName == '打开模型'){
        this.addOpenModel = true
        this.ganttBtnsConfig.btnName = '关闭模型'
      }else{
        this.addOpenModel = false
        this.ganttBtnsConfig.btnName = '打开模型'
        this.loadingModelEnd = true
      }

    },
    ganttDialogCallBack(){
      // this.editGanttShow = false
      // this.addOpenModel = false
      // this.ganttBtnsConfig.btnName = '打开模型'
      // this.showplaycomps = false
    },
    ganttIframeLoad(){
      // let ifa = this.$refs.addOpenPlanView
      /**
       * var data = {
            act: 'reqToGtt_setShowTableView', msg: {}, datas: false
          };
       */
      // let data = {
      //   act: 'reqToGtt_setShowGanttView', msg: {}, datas: false
      // }
      // ifa.contentWindow.postMessage(data,'*')

    },
    getCurrentPlanListData(){
      this.currentGanttDataInfo = {}
      this.editGanttShow = true
    },
    progressClose(){
    
    },
    openProgress(item){
      this.currentModelInfo = item
    
    },
    progressMaskClick(){
    },
    getBimConfig() {
        return window.bim_config
    },


    //
    getItemMenuStyle(){
      var _this = this;
      var _s = {};
      if (_this.showEditMenuFromRight == true) {
        _s["right"] = '156px';
      } else {
        _s["left"] = '156px';
      }
      return _s;
    },

    // 编辑模型：mouseover
    mouseovereditmodel(ev){
      var _this = this;
      _this.itemMenuName = 'edit';
    },

    // 编辑模型：mouseout
    mouseouteditmodel(ev){
      var _this = this;
      _this.itemMenuName = '-1';
    },

    setTranIndex(val)
    {
      this.tranIndex=val;
    },
    CheckMenuPos(e){

      var _this = this;

      //检测菜单是否超出界面
      var posX=e.pageX;
      var posY=e.pageY;
      var screenX=window.screenX;
      var screenY=this.$staticmethod.getClientHeight();
      var menuHeight=40*this.FunctionBtnNum;//40*this.FunctionBtnNum*7/8;

      // 不加大括号的都TM人才
      if(parseFloat(posY)+parseFloat(menuHeight)-52>parseFloat(screenY)) {
        this.NeedChangePos=true;
      }
      else {
        this.NeedChangePos=false;
      }

      // 修改变量，记住点击的是否为最右侧的那一列模型。
      if (document.body.clientWidth - posX < 150) {
        // 最右侧的
        _this.showEditMenuFromRight = true;
      } else {
        // 非最右侧的
        _this.showEditMenuFromRight = false;
      }


    },
    loadModelList(searchName)
    {
     
    },
    GetCategory(CallBack) {
  
    },
    openMerge()
    {
     
    },
    openShare(Model)
    {
   
    },
    _openFile(fileId,fileName,fileExName)
    {
    
    },
    presetPlace(Model, isrepub){

     

    },
    setPlace(Model) {
    
    },


    // GISPub_ModelCache
    setPlace2() {
  
    },


    openViewList(Model)
    {
  
    },
    _onselectpoint(pointinfo)
    {
  
    },
    openModel(Model,ViewID,VersionNO)
    {

    
    },
    GetModelImg(Thumbnail,IsMerg)
    {
      if(Thumbnail==''){
        if(IsMerg)
          return require('../../../assets/svgs_loadbyurl/model-Merg.svg');
        else
          return require('../../../assets/svgs_loadbyurl/model-Default.svg');
      }
      else
        return 'data:image/png;base64,'+ Thumbnail;
    },
    changeMN(modelName)
    {
      this.SelModelObj.Name=modelName;
    },
    reName(Model)//模型重命名
    {
     
    },
    openModelInfo(item)
    {
 
    },
    updateModel(ModelId)//更新模型
    {
     
    },
    uploadModel()//上传模型
    {
    
    },
    updatePhase(item){
     
    },
    TargetDOCList(item)
    {
     
    },
    updateModelThumbnail_V2(Model)
    {
   
    },

    //修改缩略图
    updateModelThumbnail(Model){
    
    },
    delModel(ModelId)//删除模型
    {
     

    },
    hideModelMenue(e)
    {
      if(e.target.className.indexOf('noHide')==-1&&e.target.nodeName.toLowerCase()!='li')
        this.CurrentModelId='-1';
    },
    IsHaveRole(m)
    {
      return true;
    },
    SearchList(e,jd)
    {
      if(e.keyCode==13||jd==1){
        this.loadModelList(this.SearchModelName);
      }
    },
   
    // 刷新甘特图
    reloadGan() {
      let iframe_gtt = document.getElementById("addOpenPlanView");
      let gtt_src = iframe_gtt.getAttribute("src");
      iframe_gtt.setAttribute("src", gtt_src);
    },
    attachrelationAjax() {
      
    },

    // 覆盖关联：特别注意如果右侧模型关闭了，则现在的 modelCurrentSelectData 应为 []
    // ------------------------------------------------------------------------
    reRelationAjax(){
     
    },
    clearRelationAjax(){
     
    },

    // 处理以下内容：
    // 接收从甘特图组件发送来的消息并处理：显示某些任务的关联构件并高亮、聚焦
    // ----------------------------------------------------------------
    ganttShowRelation(){

      let _this = this;

      // 如果左侧并没有选择任务，直接提示并跳出
      // -----------------------------------
      if(_this.gannCurrentSelectData.length == 0){
        _this.$message.error('请先勾选至少一个任务');
        return false;
      }else{

        // 先请求与这些任务相关联的构件，再判断是否已经打开了模型
        // -------------------------------------------------
        let postdata = {
          taskIds: this.gannCurrentSelectData.join(','),
          Token: this.$staticmethod.Get("Token")
        }
        this.$axios.post(`${window.bim_config.webserverurl}/api/Plus/PlusTask/GetRelationElementsByTaskIds`,this.$qs.stringify(postdata)).then(res=>{
          
          let data = res.data.Data.list
          if(data.length>0){
            let elements = []
            data.forEach(item=>{
              let elementinfos = JSON.parse(item.pte_elementids)
              elementinfos.forEach(info=>{
                info.elementids.forEach(el=>{
                  elements.push(`${info.modelid}^${el}`)
                })
              })

            })

            //debugger;

            if(this.addOpenModel){

              // 显示某些任务的关联构件并高亮、聚焦
              // -------------------------------
              //this.editModelIframeWindow.BI1Me.control.BIMeZ1oom.zoomEleme1ntByElementId(elements[0]);
              this.$staticmethod.bimhelper_getzoomer(this.editModelIframeWindow).zoomElementByElementId(elements);

              //this.editModelIframeWindow.BI1Me.control.BIMeSelector.selectorEl1ementByElementId(elements);
              // var highlightm = this.$staticmethod.bimhel1per_highlight(this.editModelIframeWindow);
              // highlightm(elements);

              var highlightobj = this.$staticmethod.bimhelper_highlight(this.editModelIframeWindow);
              this.$staticmethod.bimhelper_callhighlight(highlightobj, elements);

            }else{

              // this.$message.error('请先打开模型');
              // return false

              // 如果此时没有打开模型，标记“打开模型后需要设置构件的高亮及聚焦”属性，再设置 addOpenModel 为 true
              // ----------------------------------------------------------------------------------------
              _this.bSelectAndZoomAfterRender = true;
              _this.arrSelectAndZoom = elements;
              _this.addOpenModel = true;

            }

          }
        });
        
      }
    }
  },
  mounted() {
    //gantt postmaessage
    let _this = this
    window.modelvue = this;




    window.addEventListener('message',(msg)=>{
      let arr = []
      if(msg.data.datas){
        msg.data.datas.forEach(item => {
            arr.push(item.UID)
        })
      }

      _this.gannCurrentSelectData = arr
      if(msg.data.act == "resFromGtt_OnAttachrelation"){//附加关联
        _this.attachrelationAjax()
      }
      if(msg.data.act == "resFromGtt_OnReRelation"){//新增关联==覆盖关联
        _this.reRelationAjax()
      }
      if(msg.data.act == "reqFromGtt_triggerBtn"){//编辑任务
        _this.tiggerGanttBtns('id_abtn_updatetask')
      }

      // 接收从甘特图组件发送来的消息并处理：显示某些任务的关联构件并高亮、聚焦
      // ----------------------------------------------------------------
      if(msg.data.act == 'resFromGtt_OnShowRelation'){
        _this.ganttShowRelation();
      }
      if(msg.data.act == "resFromGtt_OnClearRelation"){//取消关联
        _this.clearRelationAjax()
      }
      if (msg.data.act == "resFromGtt_getSelecteds"){
        if(msg.data.type == 'add'){
          _this.reRelationAjax()
        }else if(msg.data.type == 'show'){
          _this.ganttShowRelation()
        }else{
            _this.clearRelationAjax()
        }
      }
      if (msg.data.act == 'resFromGtt_setSliderDate') {
        _this.setSliderDate(msg.data.Date);
      }


    })


    window.addRelation = function(){
      // debugger
      let iframe = document.getElementById('addOpenPlanView').contentWindow
      iframe.postMessage({act:'reqToGtt_getSelecteds',type:'add'},'*')

    }
    window.cancelRelation = function(){
      let iframe = document.getElementById('addOpenPlanView').contentWindow
      iframe.postMessage({act:'reqToGtt_getSelecteds',type:'cancel'},'*')
    }
    window.showRelation = function(){
      let iframe = document.getElementById('addOpenPlanView').contentWindow
      iframe.postMessage({act:'reqToGtt_getSelecteds',type:'show'},'*')
    }

    // this.UrlPhase = this.$route.params.Phase;
    this.modelid = this.$route.params.modelid;
    this.planid = this.$route.params.planid;
    this.bimcomposerId = this.$route.params.bimcomposerId;

    this.getCurrentPlanListData();
  }
}
</script>
<style scoped>

#addOpenPlanView {
  height:calc(100% - 8px);
}

._css-auto-rel {
  opacity: 0.7 !important;
}

._css-auto-rel:hover {
    opacity: 1 !important;
}

._css-auto-rel-text{
      padding-left: 0 !important;
    padding-right: 0 !important;
    border: none !important;
    margin-left:8px !important;
    font-size: 14px !important;
    line-height: 14px !important;
    color:#1890FF !important;
}

._css-auto-rel-icon {
    padding-left: 0 !important;
    padding-right: 0 !important;
    border: none !important;
}

._css-gispubdialog-ctrl._css-disabled{
  cursor:not-allowed;
  opacity: 0.3;
}

._css-gispubitems{
  /* position:absolute;
  border-radius: 4px;
  height:40px;
  display: flex;
  align-items: center;
  justify-content: space-around;
  width:100%;
  top: calc(100% + 2px);
  max-height: 300px;
  overflow-y: auto;
  background-color: #fff;
  border: 1px solid rgba(0,0,0,0.09); */
  position: absolute;
    border-radius: 4px;
    /* height: 40px; */
    /* display: -webkit-box; */
    /* display: flex; */
    /* align-items: center; */
    justify-content: space-around;
    width: 100%;
    top: calc(100% + 2px);
    max-height: 120px;
    overflow-y: auto;
    background-color: #fff;
    border: 1px solid rgba(0,0,0,0.09);
    z-index: 2;
}

._css-gispubitemi-ctn{
  height: 40px;
  text-align: left;
  width: 100%;
  padding-left:16px;
  box-sizing:border-box;
  display: flex;
  align-items: center;
}

._css-gispubitemi{
  height: 22px;
  line-height: 22px;
  text-align: left;
  width:100%;
  overflow-x: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

._css-gispubitemi-ctn:hover{
  background-color: rgba(0,0,0,0.04);
}

._css-gispubdialog-cbtext{
  flex:1;
  height:22px;
  line-height: 22px;
  margin-left:16px;
  text-align: left;
  border:1px dotted transparent;
  overflow-x: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

._css-gispubdialog-cbicon{
  height: 16px;
  width:16px;
  margin-right: 16px;
  margin-left: 16px;
  border:1px solid transparent;
}

._css-gispubdialog-label{
  height:22px;
  margin-top:6px;
  width:163px;
  margin-left:16px;
  text-align: left;
  color:rgba(0, 0, 0, .85);
}

._css-gispubdialog-ctrl{
  height:36px;
  margin-top:5px;
  width:188px;
  border-radius:4px;
  border:1px solid rgba(0,0,0,0.09);
  margin-left:16px;
  cursor:pointer;
  display: flex;
  align-items: center;
  position: relative;
}

._css-gispubdialogbtnok{
  background-color: #1890FF;
  width:188px;
  height:40px;
  font-size:14px;
  line-height: 40px;
  color:#fff;
  cursor: pointer;
}

._css-gispubdialogbtnok-ctn{
  /* height:63px;
  width:100%;
  border-top:1px solid rgba(0, 0, 0, .09);
  display: flex;
  align-items: center;
  justify-content: space-around;
  margin-top:56px; */
  height: 63px;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-around;
  margin-top: 0px;
}

._css-gispubdialog-text{
  flex:1;
  height:24px;
  font-size:16px;
  font-weight:500;
  color:rgba(0,0,0,1);
  line-height:24px;
}

._css-gispubdialog-title{
  display: flex;
  align-items: center;
  height:44px;
}

._css-gispubdialog-titleback{
  width:20px;
  height:20px;
  font-size: 20px;
  margin-left:12px;
  color:rgba(0, 0, 0, .25);
}

._css-gispubdialog-titleother{
  width:20px;
  height:20px;
  font-size: 20px;
  margin-right:12px;
  color:rgba(0, 0, 0, .25);
}

._css-selectviewtype-dialog{
  position:fixed;
  top:0;
  left:0;
  width:100%;
  height:100%;
  background-color: rgba(0,0,0,0);
  z-index:100;
  display: flex;
  align-items: center;
  justify-content: space-around;
}

._css-selectviewtype-dialog-in{
  width:220px;
  height:332px;
  background-color:rgba(255,255,255,1);
  box-shadow:0px 2px 14px 0px rgba(26,26,26,0.1);
  border-radius:4px;
  position: absolute;
  /* left:222px;
  top:33px; */
}

.el-progress-wp{
  display: flex;
  flex-direction: column;
}
.el-progress-wp .btn-wp{
  height: 64px;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  padding: 0 24px;
}
.el-progress-wp .btn-wp i{
  margin-right: 8px;
}
.iframes-wp{
  flex: 1;
  display: flex;
  flex-direction: row;
}
.el-progress-wp .btn-wp>div{
  display: flex;
  flex-direction: row;
}
.el-progress-wp .btn-wp>div div{
  padding: 0 8px;
  height: 32px;
  display: flex;
  flex-direction: row;
  align-items: center;
  border-radius: 4px;
  border: 1px solid #1890ff;
  color: #1890ff;
  background: #f7f7f7;
  margin-left: 16px;
  opacity: .7;
  cursor: pointer;
}

.el-progress-wp .btn-wp>div div.playmodel{
  background: #1DA48C;
  color: #fff;
  border-color: #1DA48C;
}

.el-progress-wp .btn-wp>div div.stopmodel{
  background: #F5222D;
  color: #fff;
  border-color: #F5222D;
}

.el-progress-wp .btn-wp>div div.pausemodel{
  background: #FAAD14;
  color: #fff;
  border-color: #FAAD14;
}

.el-progress-wp .btn-wp>div div.ban-click{
  background: rgba(0, 0, 0, .25);
  color:#fff;
  cursor: not-allowed;
  border: 1px solid transparent;
}

.el-progress-wp .btn-wp>div div._css-btn-dis{
  opacity: 0.3;
  cursor: not-allowed;
}

.el-progress-wp .btn-wp>div div:not(._css-nothover):not(._css-btn-dis):hover{
  opacity: 1;
}
.el-progress-wp .btn-wp>div div:first-child{
  margin-left: 0;
}
.el-edit-progress-wp .top-btn{
  display: flex;
  flex-direction: row;
}
.el-edit-progress-wp .top-btn div{
  height: 40px;
  line-height: 40px;
  color: #fff;
  background: rgb(24, 144, 255);
  cursor: pointer;
  padding: 0 8px;
  border-radius: 4px;
  margin-left: 16px;
  opacity: .8;
}
.el-edit-progress-wp .top-btn div:hover{
  opacity: 1;
}
.full-screen-main {
  display: flex;
  position:fixed;
  height:100%;
  width:100%;
}
._css-model-page{
  width:100%;height:100%;
}
.btns {
  width: clac(100% - 48px);
  height: 64px;
  padding: 0px 24px 0px 24px;
  text-align: center;
  line-height: 64px;
  position:relative;
}
.btns .upload {
  width: 120px;
  height: 40px;
  border-radius: 3px;
  background-color: #1890ff;
  border: none;
  color: #fff;
  outline: none;
  position: absolute;
  padding: 0px;
  left:24px;
  top:16px;
}
.btns .upload .menu{
  left:0px;top:40px;width:100%;
}
.btns .txtSearch {
  display: inline-block;
  position: relative;
  margin-top: 16px;
  width: 400px;
  height: 32px;
  background-color: #e6e8eb;
  border: none;
  border-radius: 3px;
  text-indent: 26px;
  font-size: 13px;
  outline: none;
}
.btns .txtSearch i{position: absolute;top:8px;left:10px;width:18px;height:18px;text-align:left;}.btns .txtSearch i::before{float: left;width:auto;height:auto;text-indent:0px;}
.btns .txtSearch input{width: calc(100% - 36px);margin-left:34px;float:left;height:calc(100% - 2px);border:none;outline:none;background-color:#e6e8eb;}
.btns .txtSearch input::-webkit-input-placeholder {
  font-size: 13px;
  font-family: PingFangSC-Regular;
  line-height: 32px;
}
.btns .showType{width:auto;height:40px;float: left;margin: 0px;padding: 0px;overflow:hidden;position: absolute;left:170px;top:16px;}
.btns .showType li{list-style-type: none;float: left;width:50px;height:40px;line-height:0px;}
.btns .showType li i{width:100%;height:100%;display:inline-block;line-height:40px;background-color:#e6e8eb;color:#7e7f81;}
.btns .showType li i:hover{opacity:0.8;cursor: pointer;}
.btns .showType .sel{background-color:#1890ff;color:#fff;}
.GISWindow{
  width:100%;height:calc(100% - 54px);
  /* 我曹 高度调整了 这块没动 导致黑边 */
}
.Models {
  width: 100%;
  height: calc(100% - 64px);
  overflow-x:hidden;
  overflow-y:auto;
  align-content:flex-start;
  flex-wrap: wrap;
  display: flex;
}
.Models .unit {
  width: 245px;
  height: 260px;
  box-shadow: 0px 1px 1px 0px rgba(0, 21, 41, 0.12);
  text-align: center;
  background-color: #fff;
  font-size: 0px;
  float: left;
  position: relative;
  margin: 0px 0px 24px 24px;
}
.Models .unit:hover {
  box-shadow: 0px 13px 24px -17px rgba(11, 41, 62, 0.8);
  cursor: pointer;
}
.Models .unit:hover .updateDate .setting,.Models .unit:hover .updateDate .viewList{
  display: block;
}
.Models .unit:hover img{
  background-color:#fafafa;
}
.Models .HasGIS{
  width:24px;height:24px;display: block;position: absolute;top:8px;right:8px;color: #333;font-size: 12px;line-height: 20px;font-style: normal;border:1px solid #fff;border-radius: 20px;background-image: url('../../../assets/svgs_loadbyurl/model_hasGIS.svg');background-color:#fff;background-size:100%;
}
.HasGIS_yellow{
  width:24px;height:24px;display: block;position: absolute;top:8px;right:8px;color: #333;font-size: 12px;line-height: 20px;font-style: normal;border:1px solid #fff;border-radius: 20px;background-image: url('../../../assets/svgs_loadbyurl/model_hasGIS_yellow.svg');background-color:#fff;background-size:100%;
}
.Models .unit img {
  width: calc(100% - 12px);
  height: 178px;
  border: none;
  display: inline-block;
  margin-top:6px;
  border-radius:4px
}
.Models .unit span {
  width: calc(100% - 30px);
  height: 38px;
  display: inline-block;
  line-height: 38px;
  position: relative;
  padding: 0px 15px 0px 15px;
}
.Models .unit .modelName {
  font-size: 16px;
  font-weight: 400;
  color: #292929;
  overflow: hidden;text-overflow:ellipsis;white-space: nowrap;
}
.Models .unit .updateDate {
  font-size: 12px;
  font-weight: 400;
  color: #c4c4c4;
  position: relative;
}
.Models .unit .updateDate .setting {
  position: absolute;
  width: 20px;
  height: 20px;
  right: 10px;
  top: 50%;
  transform: translate(0, -50%);
  display: none;
  color:#aaa;
}
.Models .unit .updateDate .setting::before {
  float: left;
  width: 20px;
  height: 20px;
  line-height: 20px;
}
.Models .unit .updateDate .setting:hover,.Models .unit .updateDate .viewList:hover{
  color:#1890ff;
}
.Models .unit .updateDate .viewList{
    position: absolute;
    width: 20px;
    height: 20px;
    left: 10px;
    top: 50%;
    transform: translate(0, -50%);
    display: none;
    color:#999;
}
.Models .unit .updateDate .viewList::before{
  float: left;
  width: 20px;
  height: 20px;
  line-height: 20px;
}
.Models .unit .updateDate .more{
  position: relative;
}
.Models .unit .updateDate .more .imore{
  display:block;position: absolute;right:16px;top:14px;left:unset;font-size:12px;
}


.RN{
  width:368px;height:auto;position: absolute;top:50%;left:50%;transform:translate(-50%,-50%);z-index:999;padding:0px 16px 0px 16px;
}
.MP{
  width:500px;height:350px;position: absolute;top:50%;left:50%;transform:translate(-50%,-50%);z-index:999;
}
.svl{
  width:534px;height:592px;position: absolute;top:50%;left:50%;transform:translate(-50%,-50%);z-index:999;
}
.MMG{
  width:100%;
  height:100%;
  position: absolute;
  z-index:1001;
  top:0px;
  left:0px;
}
.shl{
  width:750px;height:auto;position: absolute;top:50%;left:50%;transform:translate(-50%,-50%);z-index:999;
}
.umt{
  width:510px;height:auto;position: absolute;top:50%;left:50%;transform:translate(-50%,-50%);z-index:999;
}
.TDL{
  width:410px;height:480px;position: absolute;top:50%;left:50%;transform:translate(-50%,-50%);z-index:999;
}
.VD{
  width:80%;height:80%;position: absolute;top:50%;left:50%;transform:translate(-50%,-50%);z-index:999;
}
.CGIS{
   width:auto;height:auto;position: absolute;top:50%;left:50%;transform:translate(-50%,-50%);z-index:999;
}
.menu {
  position:absolute;display: block;padding: 4px 0px 1px 0px;margin: 0px;width:160px;height: auto;border-radius: 4px;box-shadow:0px 1px 3px 0px rgba(0,21,41,0.12);top:-20px;left:85px;z-index:99;background-color:#fff;
}
.mi{
  width:869px;height:420px;position: absolute;top:50%;left:50%;transform:translate(-50%,-50%);z-index:97;
}
.menu.bm{
  top:-400px;left:85px;
}

.menu li{
  list-style-type: none;color:#333;text-align:left;width:100%;height:auto;font-size: 0px;
}
.menu .line{
  border-bottom:1px solid rgba(0,21,41,0.12);
}
.menu .marginTop{
  margin-top:4px;
}
.menu .marginBottom{
  margin-bottom:4px;
}
.menu li:last-child{
  border:none;
}
.menu li font{
  display: inline-block;width:calc(100% - 40px);height:40px;padding:0px 20px 0px 20px;line-height:40px;font-size:12px;text-indent:28px;position:relative;
}

.menu.btns li font{text-indent:0px;float:left;}
.menu li font:hover{background-color:#f5f5f5;}
.menu li i{
  position:absolute;top:9px;left:-5px;width:18px;height:18px;
}
.menu li i::before{width:18px;height:18px;line-height: 20px;float:left;}
.mv{
  position:absolute;top:0px;right:0px;z-index: 1001;background-color:#fff;
}
.Covery{
  width:100%;height:100%;background-color:rgba(0,0,0,0.4);position: absolute;top:0px;left: 0px;z-index: 1;
}

</style>
