<template>
<div class="Doc_ViewDoc">
    <div class="title">预览文件<i class="icon-suggested-close close" @click="$emit('closeViewDoc')"></i></div>
    <div class="con">
        <iframe :src="openUrl"></iframe>
    </div>
    <div class="btns">
        <button class="blue" @click="$emit('closeViewDoc')">关闭</button>
    </div>
</div>
</template>
<script>
export default {
    name:'Doc_ViewDoc',
    components:{

    },
    data(){
        return {
            
        };
    },
    props:{
        openUrl:{
            type:String,
            required:true,
        }
    }
}
</script>
<style scoped>
ul{padding: 0px;margin: 0px;}li{padding: 0px;margin: 0px;}
.Doc_ViewDoc{background-color: #fff;box-shadow:0px 13px 24px -17px rgba(11,41,62,0.8);border-radius:4px;font-family:PingFangSC-Medium;letter-spacing:0.5px;}
.Doc_ViewDoc .title{width:calc(100% - 32px);height:48px;font-size:16px;color:#333;line-height:48px;text-align:left;font-weight:bold; position: relative;overflow: hidden;padding:0px 16px 0px 16px;}
.Doc_ViewDoc .title .close{width:18px;height:18px;position: absolute;right:16px;top:15px;display:block;color:#aaa;}
.Doc_ViewDoc .title .close::before{float:left;width:18px;height:18px;display:block;text-align:left;text-indent:0px;cursor: pointer;}
.Doc_ViewDoc .con{height:calc(100% - 55px - 48px);overflow:hidden;line-height:50px;}

.Doc_ViewDoc .con iframe{width:100%;height:100%;border:none;}

.Doc_ViewDoc .btns{width:calc(100% - 48px);height:50px;line-height:50px;text-align:right;padding:0px 24px 0px 24px;}
.Doc_ViewDoc .btns button{width:auto;height: 30px;border:none;outline: none;border-radius: 3px;cursor: pointer;font-size:12px; letter-spacing:1px;padding:0px 25px 0px 25px;}
.Doc_ViewDoc .btns button:hover{opacity:0.8;}
.Doc_ViewDoc .btns .blue{background-color:#1890FF;color:#fff; }
.Doc_ViewDoc .btns .white{background-color:transparent;color:#666;}

::-webkit-scrollbar{width:20px; height:8px;}
::-webkit-scrollbar-thumb{border-radius: 12px;border:6px solid transparent;box-shadow: 8px 0px 0px #A5ADB7 inset;}
::-webkit-scrollbar-thumb:hover {box-shadow: 8px 0px 0px #4A4A4A inset;} 
</style>
