<template>
	<div class="dialog">
		<modelNewIframeLoading
			class="model-iframe"
			:VaultID="VaultID"
			:featureID="featureID"
		></modelNewIframeLoading>
	</div>
</template>
<script>
import modelNewIframeLoading from '@/components/Home/ProjectBoot/modelNewIframeLoading'
export default {
	name: "CompModelDetail",
	data() {
		return {
			VaultID: "", // 项目ID
			featureID: "",
		};
	},
	components: {
		modelNewIframeLoading
	},
	created() {
		this.VaultID = this.$route.params.organizeId;
		this.featureID = this.$route.params.featureID;
	},
	mounted() {},
	watch: {},
	methods: {},
};
</script>
<style lang="stylus" scoped rel="stylesheet/stylus">
.dialog{
	width: 100%;
	height: 100%;
}
</style>