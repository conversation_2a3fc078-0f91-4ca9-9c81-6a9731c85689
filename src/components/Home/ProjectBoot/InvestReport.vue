<template>
  <div class="invest-container">
    <div class="btn-initiating-header">{{ headerTitle }}</div>
    <div class="invest-content">
      <div class="pro-in-out" @click.stop="handleClickInOut" :class="proLeftIn ? 'p-out' : ''"></div>
      <div class="left" v-show="!proLeftIn">
        <div class="left-header">投资分类</div>
        <div class="left-menu">
          <el-menu
            :default-active="defaultActive"
            class="el-menu-vertical-demo css-menu-list"
            @select="handelSelectMenuList"
            @open="openList"
            @close="openList"
          >
            <div
              v-for="(item,index) in treeList" 
              :key='index' 
              :index="index + ''"
            >
              <el-submenu :index="index + ''" v-if="item.CategoryName != '移民安置'">
                <template slot="title" > 
                  <div class="title-add">
                    <span>{{item.CategoryName}}</span>
                    <span class="margin20" @click.stop="handelCreat('新增',item)"><i class="icon-suggested-plus"></i></span>
                  </div>
                </template>
                <div 
                  v-for="(itemC,indexC) in item.Children" :key='indexC'  
                  :index="index + '-' + indexC" 
                >
                  <el-menu-item  :index="index + '-' + indexC"  @click.native="handleSelectClick(itemC,'222')">
                    <div class="title-add">
                      <span>{{itemC.CategoryName}}</span>
                      <div class="item-edit">
                        <span @click.stop="handelCreat('编辑',itemC)"><i class="icon-interface-edit"></i></span>
                        <span @click.stop="handelDetele(itemC.Id)"><i class="icon-interface-model-delete"></i></span>
                      </div>
                    </div>
                  </el-menu-item>
                </div>
              </el-submenu>
              <el-menu-item :index="index + ''" @click.native="handleSelectClick(item,'111')" v-else>
                <span>{{item.CategoryName}}</span>
              </el-menu-item>
            </div>
          </el-menu>
        </div>
      </div>
      <div class="right _css-right-table" :class="proLeftIn ? 'width0' : ''">
        <!-- <div class="list-tab">
          当前分类：{{ rightTableindex.CategoryName }}
        </div> -->
        <div class="table-content">
          <investment ref="investmentList" v-if="rightTableindex.CategoryType == 0" :clickitem="rightTableindex"></investment>
          <resettlement v-if="rightTableindex.CategoryType == 1" :clickitem="rightTableindex"></resettlement>
          <investOther v-if="rightTableindex.CategoryType == 2" :clickitem="rightTableindex"></investOther>
        </div>
      </div>
      <zdialog-function
        :init_title="dialogTitle"
        :init_zindex="1003"
        :init_innerWidth="340"
        :init_width="340"
        :init_height="477"
        init_closebtniconfontclass="icon-suggested-close"
        :init_usecustomtitlearea="false"
        @onclose="resetDialog()"
        v-if="showDialog"
      >
        <div slot="mainslot" class="dialog-list" @mousedown="_stopPropagation($event)">
          <div class="_css-line _css-line-name">
              <div class="_css-title _css-title-flowname ">
                  分类名称：
              </div>
              <div class="_css-fieldvaluename">
                <el-input class="parent-readonly" v-model="dialogText" placeholder="请输入名称"></el-input>
              </div>
          </div>
        </div>
        <div slot="buttonslot" class="css-common-zdialogbtnctn" >
          <zbutton-function
              :init_text="'取消'"
              :init_fontsize="14"
              :debugmode="true"
              :init_height="undefined"
              :init_width="'76px'"
              :init_bgcolor="'#fff'"
              :init_color="'#1890FF'"
              @onclick="resetDialog()">
          </zbutton-function>
          <zbutton-function
            :init_text="'确定'"
            :init_fontsize="14"
            :debugmode="true"
            :init_height="undefined"
            :init_width="'76px'"
            @onclick="submitDialog()">
          </zbutton-function>
        </div>
      </zdialog-function>
    </div>
  </div>
</template>
<script>
import investment from '@/components/CompsInvest/InvestMent'
import resettlement from '@/components/CompsInvest/resettlement'
import investOther from '@/components/CompsInvest/InvestOther'
export default {
  name: 'InvestReport',
  components: {
    investment,
    investOther,
    resettlement
  },
  data() {
    return {
      token: '',
      organizeId: '',
      headerTitle: '',
      proLeftIn: false,
      treeList: [],
			defaultActive: '0',
      investmentList: [],
      rightTableindex: 0, // 右边表格title显示文字
      dialogEditID: '', // 编辑还是新增的ID
      dialogTitle: '', // 编辑还是新增
      showDialog: false,
      dialogText: '',  // 编辑的时候复制、新增是空
    };
  }, 
  watch: { 
  },
  
  created() {},
  mounted() {
    this.headerTitle = this.$staticmethod._Get("menuText") || '';
    this.token = this.$staticmethod.Get("Token")
    this.organizeId = this.$staticmethod._Get("organizeId");
    this.getTreeList()
  },
  methods: {
    handleClickInOut(){
      this.proLeftIn = !this.proLeftIn;
    },
    handelSelectMenuList(key){
      console.log(key,'===key')
      this.defaultActive = key;
      // this.rightTableindex = key[0];
    },
    openList(index){
      console.log(index,'=====index',typeof(index))
      // this.rightTableindex = index[0];
    },
    handleSelectClick(item,index){
      console.log(item,index)
      this.rightTableindex = item;
    },
    // 获取左边树结构
    getTreeList(){
      this.$axios
        .get(`${this.$IRUrl.categoryTree}?token=${this.token}&organizeId=${this.organizeId}`)
        .then(res=>{
          if(res.data.Ret == 1){
            this.treeList = res.data.Data;
            this.resetDialog()
          }else{
            this.$message.error(res.data.Msg)
          }
        })
        .catch(res=>{
          console.log(err,'===err')
        })
    },
    // 点击新增
    handelCreat(text,item){
      this.dialogTitle = text
      this.showDialog = true;
      this.dialogEditID = item.Id;
      text == '编辑' ? this.dialogText = item.CategoryName : this.dialogText = ''
    },
    // 关闭
    resetDialog(){
      this.dialogText = '';
      this.showDialog = false;
    },
    submitDialog(){
      if(this.dialogText == ''){
        this.$message.warning('请输入名称')
        return
      }
      // 判断是编辑还是删除
      this.dialogTitle == '新增' ? this.categoryCreate() : this.categoryModify()
    },
    categoryCreate(){
      let data = {
        "Token": this.token,
        "OrganizeId": this.organizeId,
        "CategoryName": this.dialogText,
        "ParentId": this.dialogEditID,
        "Sort": 0
      }
      this.$axios
        .post(`${this.$IRUrl.categoryCreate}`,data)
        .then(res=>{
          if(res.data.Ret == 1){
            this.$message.success('创建成功')
            this.getTreeList()
          }else{
            this.$message.error(res.data.Msg)
          }
        })
        .catch(res=>{
          this.$message.error('服务器请求异常')
        })
    },
    categoryModify(){
      let data = {
        "Token": this.token, 
        "CategoryName":this.dialogText, 
        "Id": this.dialogEditID, 
      }
      this.$axios
        .post(`${this.$IRUrl.categoryModify}`,data)
        .then(res=>{
          if(res.data.Ret == 1){
            this.$message.success('编辑成功')
            this.getTreeList();
            this.$refs.investmentList.getTableData();
          }else{
            this.$message.error(res.data.Msg)
          }
        })
        .catch(res=>{
          this.$message.error('服务器请求异常')
        })
    },
    handelDetele(id){
      this.$confirm("确定删除当前数据", {
        confirmButtonText:'确定',
        cancelButtonText:'取消',
        type:'warning'
      }).then(() => {
				let data = {
          "Token": this.token, 
          "Id": id, 
        }
        this.$axios
          .post(`${this.$IRUrl.categoryDelete}`,data)
          .then(res=>{
            if(res.data.Ret == 1){
              this.$message.success('删除成功')
              this.getTreeList()
            }else{
              this.$message.error(res.data.Msg)
            }
          })
          .catch(res=>{
            this.$message.error('服务器请求异常')
          })
      }).catch((err) => {
        console.log(err)
      })
      
    },
    _stopPropagation(ev){      
			ev && ev.stopPropagation && ev.stopPropagation();     
    },


  }
}
</script>
<style lang="scss" scoped>
.invest-container{
  width: 100%;
  height: 100%;
  text-align: left;
}
.invest-content{
  display: flex;
  width: 100%;
  height: calc(100% - 60px);
  position: relative;
}
.left{
  width: 270px;
  box-sizing: border-box;
  background-color: #fff;
  position: relative;
}
.right{
  height: 100%;
  background: #fff;
  flex: 1;
  position: relative;
  margin-left: 25px;
  width: calc(100% - 300px);
  box-sizing: border-box;
}
.pro-in-out{
  position: absolute;
  top: calc(50% - 120px);
  left:270px;
  width: 16px;
  height: 120px;
  background-image: url(../../../assets/images/p-in.png) ;
  background-size: 100%;
  background-repeat: no-repeat;
  z-index: 2;
  cursor: pointer;
}
.pro-in-out.p-out{
  background-image: url(../../../assets/images/p-out.png) ;
  background-repeat: no-repeat;
  background-size: 100%;
  z-index: 2;
  left: 0;
}
._css-right-table.width0{
    width: calc(100% - 30px);
}
.left-header{
  padding-left: 20px;
  line-height: 46px;
  font-weight: 500;
  color: #000;
}
.margin20{
  margin-right: 20px;
}
.title-add{
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.item-edit{
  display: none;
}
.title-add:hover{
  .item-edit{
    display: inline-block;
  }
}
.item-edit i{
  padding-left: 10px;
}
.item-edit i:hover{
  color: #409eff;
}
.left-menu /deep/ .el-menu-item, 
.left-menu /deep/ .el-submenu .el-menu-item, 
.left-menu /deep/ .el-submenu__title{
  height: 36px;
  line-height: 36px;
  color: #606266;
}
.right{
  overflow-y: auto;
  .list-tab{
    margin: 15px 20px;
  }
  .table-content{
    margin: 0 20px;
    /deep/ .btn{
      height: 26px;
      line-height: 26px;
      width: auto;
      padding: 0 10px;
      text-align: center;
      color: #1890ff;
      background: #fff;
      border-radius: 4px;
      border: 1px solid #1890ff;
      cursor: pointer;
      margin-right: 10px;
    }
    /deep/ .btn:hover{
      background: #1890ff;
      color: #fff;
      border: 1px solid #fff;
    }
    /deep/ .btn.not-click{
      cursor: not-allowed;
      pointer-events: none;
      color: #606266;
      border: 1px solid #606266;
    }
  }
}
@import url("../../../assets/css/invest.css");
</style>
