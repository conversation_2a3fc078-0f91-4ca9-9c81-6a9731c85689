<template>
  <div style="height:100%">
    <CompsProBimIssueList @setProjectbootExtdata="_setProjectbootExt"></CompsProBimIssueList>
  </div>
</template>

<script>
  import CompsProBimIssueList from '@/components/CompsIssue/CompsProBimIssueList' 

export default {

  components: {
    CompsProBimIssueList,//主平台 问题追踪列表 
  },

  data() {
    return { 
    };
  },

  created() {

  },

  mounted() { 
  },

  methods: {

    // 暂不简写成 _this.$emit("set_projectboot_extdata", para1, parar2);
    _setProjectbootExt(para1, parar2, para3){
      var _this = this;
      if (para1 == '_docviewtype') {
          _this.$emit("set_projectboot_extdata", "_docviewtype", parar2);
      } else if (para1 == '_show_idocview') {
        _this.$emit("set_projectboot_extdata", "_show_idocview", parar2);
      } else if (para1 == '_idocviewurl') {
         _this.$emit("set_projectboot_extdata", "_idocviewurl", parar2);
      }
    },
  },

  destroyed(){
    window.onresize = null;
  },

  watch:{

  },
  computed: {

  },

};

</script>
<style scoped>

</style>
