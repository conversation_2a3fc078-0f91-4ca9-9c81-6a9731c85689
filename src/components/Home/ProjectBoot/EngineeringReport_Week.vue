<template>
  <div>
    <div class="_css-tabcontrolarea">
      <div class="_css-tabcontrolitem">施工周报</div>
    </div>
    <div class="container" :style="getContainsHeight()">
      <div class="ER_body">
        <div class="Engineering_header">
          <div class="ERD_header_date">
            <el-date-picker
              v-model="ERD_date"
              type="date"
              placeholder="选择日期"
              :picker-options="{'firstDayOfWeek': 1}"
              :format="'yyyy年MM月 第' + ERD_week + '周'"
              value-format="yyyy-MM-dd"
              class="choose_date"
              style="float: left;width: 220px;">
            </el-date-picker>
            <div v-if="ERD_date">
              <div class="custom_config_btn css-hover-btn"  @click="ERD_ChoseWeek('next')">
                下一周
              </div>
              <div class="custom_config_btn css-hover-btn"  @click="ERD_ChoseWeek('')">
                上一周
              </div>
            </div>
          </div>
          <div class="custom_config_btn css-hover-btn" v-if="ER_edit === false"  @click="ER_cont_edit">
            编辑
          </div>
          <div  v-if="ER_edit === true">
            <div class="css-hoverable-container">
                <zbutton-function
                    :init_text="'取消'"
                    :init_bgcolor="'darkgray'"
                    :init_fontsize="14"
                    :debugmode="true"
                    :init_height="undefined"
                    :init_width="'76px'"
                    :style="'marginLeft:30px;float: right;'"
                    @onclick="ER_cont_edit_cancel"
                    >
                </zbutton-function>
            </div>
            <div class="css-hoverable-container">
                <zbutton-function
                    :init_text="'保存'"
                    :init_fontsize="14"
                    :debugmode="true"
                    :init_height="undefined"
                    :init_width="'76px'"
                    :style="'marginLeft:30px;float: right;'"
                    @onclick="ER_cont_edit_ok"
                    >
                </zbutton-function>
            </div>
          </div>
        </div>
        <el-container>
          <el-header>
            <span>施工进度</span>
            <div class="custom_config_header_btn css-hover-btn"  @click="custom_config_edit(21)" v-if="!ER_edit">
              自定义配置
            </div>
          </el-header>
          <ul class="ER_custom_item" v-for="(item,index) in ERW_progress" :key="index">
            <el-header>{{item.ERW_progressHeader}}：</el-header>
            <li v-for="(i,d) in item.ERW_progressSingle" :key="i.ER_cont">  
              <div class="ER_cont" :title="i.ER_cont">
                  {{i.ER_cont}}：
              </div>
              <el-input oninput="value=value.replace(/[^\d]/g,'')" v-model="item.ERW_progressSingle[d].ER_custom_input" :disabled="InputStatus" placeholder="请输入内容" class="ER_input"></el-input>
            </li>
          </ul>
        </el-container>
        <el-container>
          <el-header>
            <span>工程质量</span>
            <div class="custom_config_header_btn css-hover-btn"  @click="custom_config_edit(22)" v-if="!ER_edit">
              自定义配置
            </div>
          </el-header>
          <ul class="ER_custom_item ERW_quality">
            <li v-for="(item,index) in ERW_quality" :key="item.ER_cont">
              <div class="ER_cont" :title="item.ER_cont">
                  {{item.ER_cont}}：
              </div>
              <el-input v-model="ERW_quality[index].ER_custom_input" :disabled="InputStatus" placeholder="请输入内容" class="ER_input"></el-input>
            </li>
          </ul>
        </el-container>
        <el-container>
          <el-header>
            <span>安全文明</span>
            <div class="custom_config_header_btn css-hover-btn"  @click="custom_config_edit(23)" v-if="!ER_edit">
              自定义配置
            </div>
          </el-header>
          <ul class="ER_custom_item" v-for="(item,index) in ERW_Safty" :key="index">
            <el-header>{{item.ERW_SaftyHeader}}：</el-header>
            <li v-for="(i,d) in item.ERW_SaftySingle" :key="i.ER_cont">  
              <div class="ER_cont" :title="i.ER_cont">
                  {{i.ER_cont}}：
              </div>
              <el-input v-model="item.ERW_SaftySingle[d].ER_custom_input" :disabled="InputStatus" placeholder="请输入内容" class="ER_input"></el-input>
            </li>
          </ul>
        </el-container>
        <el-container>
          <el-header>
            <span>其他</span>
            <div class="custom_config_header_btn css-hover-btn"  @click="custom_config_edit(24)" v-if="!ER_edit">
              自定义配置
            </div>
          </el-header>
          <ul class="ER_custom_item ERW_quality">
            <li v-for="(item,index) in ERW_other" :key="item.ER_cont">
              <div class="ER_cont" :title="item.ER_cont">
                  {{item.ER_cont}}：
              </div>
              <el-input v-model="ERW_other[index].ER_custom_input" :disabled="InputStatus" placeholder="请输入内容" class="ER_input"></el-input>
            </li>
          </ul>
        </el-container>
      </div>
    </div>
    <CompsCockpitCustomEditWeek
      v-if="showCustomEdit"
      :CockpitCustomEditStatus = ERW_coustom_status
      :ERW_coustom_title = ERW_coustom_title
      :ChooseER_date = ERD_date
      :ER_data = NewER_data
      :organizeId = organizeId
      @CloseERCustomEdit="IsDialogCovery=false,showCustomEdit=false"
      @RefreshData="ERD_WatchDate(ERD_date)"
      >
    </CompsCockpitCustomEditWeek>
    <div v-show="IsDialogCovery" class="Covery"></div>
    <!-- 
        @Close='CloseCustomEdit' -->
  </div> 
</template>
<script>
import CompsCockpitCustomEditWeek from '@/components/CompsCockpitCustom/CompsCockpitCustomEditWeek'
export default {
    name:'EngineeringReport_Week',
    components:{
      CompsCockpitCustomEditWeek
    },
    data(){
      return {
        organizeId:'',
        ERD_date:'',//施工周报选择时间
        ERD_week:'',//计算为当月第几周
        ERW_coustom_status:null,//打开自定义配置的项，21为施工进度，22为工程质量，23为安全文明，24为其他
        ERW_coustom_title:'',//配置名称
        ER_edit:false, //编辑状态
        InputStatus:true, //禁用状态
        showCustomEdit:false, //自定义配置页
        IsDialogCovery:false,//显示遮罩层
        ERW_progress:[//施工进度
          {
            ERW_progressHeader:"工区一",
            ERW_progressSingle:[
              {
                ER_cont:"基础结构（百分比）",
                ER_custom_input:''
              },
              {
                ER_cont:"钢结构（百分比）",
                ER_custom_input:''
              },
              {
                ER_cont:"屋面、雨棚（百分比）",
                ER_custom_input:''
              },
              {
                ER_cont:"幕墙（百分比）",
                ER_custom_input:''
              },
              {
                ER_cont:"二次结构（百分比）",
                ER_custom_input:''
              },
              {
                ER_cont:"机电施工（百分比）",
                ER_custom_input:''
              },
              {
                ER_cont:"装饰工程（百分比）",
                ER_custom_input:''
              },
              {
                ER_cont:"市政工程（百分比）",
                ER_custom_input:''
              },
            ],
          },
          {
            ERW_progressHeader:"工区二",
            ERW_progressSingle:[
              {
                ER_cont:"基础结构（百分比）",
                ER_custom_input:''
              },
              {
                ER_cont:"钢结构（百分比）",
                ER_custom_input:''
              },
              {
                ER_cont:"屋面、雨棚（百分比）",
                ER_custom_input:''
              },
              {
                ER_cont:"幕墙（百分比）",
                ER_custom_input:''
              },
              {
                ER_cont:"二次结构（百分比）",
                ER_custom_input:''
              },
              {
                ER_cont:"机电施工（百分比）",
                ER_custom_input:''
              },
              {
                ER_cont:"装饰工程（百分比）",
                ER_custom_input:''
              },
              {
                ER_cont:"市政工程（百分比）",
                ER_custom_input:''
              },
            ],
          },
        ],
        ERW_quality:[//工程质量
          {
            ER_cont:"本周发现缺陷待解决问题",
            ER_custom_input:''
          },
          {
            ER_cont:"本周发现缺陷已解决问题",
            ER_custom_input:''
          },
          {
            ER_cont:"工程待解决问题总数",
            ER_custom_input:''
          },
        ],
        ERW_Safty:[//安全文明
          {
            ERW_SaftyHeader:"安全隐患",
            ERW_SaftySingle:[
              {
                ER_cont:"物体打击",
                ER_custom_input:''
              },
              {
                ER_cont:"火灾",
                ER_custom_input:''
              },
              {
                ER_cont:"中毒和窒息",
                ER_custom_input:''
              },
              {
                ER_cont:"坍塌",
                ER_custom_input:''
              },
              {
                ER_cont:"机械伤害",
                ER_custom_input:''
              },
              {
                ER_cont:"车辆伤害",
                ER_custom_input:''
              },
              {
                ER_cont:"起重伤害",
                ER_custom_input:''
              },
              {
                ER_cont:"火药爆炸",
                ER_custom_input:''
              },
              {
                ER_cont:"触电",
                ER_custom_input:''
              },
              {
                ER_cont:"灼伤",
                ER_custom_input:''
              },
              {
                ER_cont:"高空坠落",
                ER_custom_input:''
              },
              {
                ER_cont:"其他伤害",
                ER_custom_input:''
              },
            ],
          },
          {
            ERW_SaftyHeader:"绿色施工",
            ERW_SaftySingle:[
              {
                ER_cont:"场地苫盖",
                ER_custom_input:''
              },
            ],
          },
          {
            ERW_SaftyHeader:"疫情防控",
            ERW_SaftySingle:[
              {
                ER_cont:"本周核酸检测（人数）",
                ER_custom_input:''
              },
              {
                ER_cont:"本周核酸检测（合格）",
                ER_custom_input:''
              },
            ],
          },
        ],
        ERW_other:[//其他
          {
            ER_cont:"进度滞后原因",
            ER_custom_input:''
          },
          {
            ER_cont:"质量问题及整改措施",
            ER_custom_input:''
          },
        ],
        ER_data:undefined,
        NewER_data:undefined,
      }
    },
    methods:{
      ER_cont_edit(){
        this.ER_edit = true
        this.InputStatus = false
      },
      ER_cont_edit_cancel(){
        this.ER_edit = false
        this.InputStatus = true
      },
      custom_config_edit(num){
        if(!this.ERD_date){
            this.$message.warning('请选择日期')
            return
        }
        this.ERW_coustom_status = num
        if(num===21){
          this.ERW_coustom_title = '施工进度配置'
          this.NewER_data = this.ER_data?{
            ERW_progress:this.ER_data.ERW_progress,
          }:undefined
        }else if(num===22){
          this.ERW_coustom_title = '工程质量配置'
          this.NewER_data = this.ER_data?{
            ERW_quality:this.ER_data.ERW_quality,
          }:undefined
        }else if(num===23){
          this.ERW_coustom_title = '安全文明配置'
          this.NewER_data = this.ER_data?{
            ERW_Safty:this.ER_data.ERW_Safty,
          }:undefined
        }else if(num===24){
          this.ERW_coustom_title = '其他配置'
          this.NewER_data = this.ER_data?{
            ERW_other:this.ER_data.ERW_other,
          }:undefined
        }
        this.showCustomEdit = true
        this.IsDialogCovery = true
      },
      ER_cont_edit_ok(){
        let _this = this
        if(!_this.ERD_date){
            _this.$message.warning('请选择日期')
            return
        }
        // for (let i = 0; i < _this.ERW_progress.length; i++) {
        //   const ele = _this.ERW_progress[i].ERW_progressSingle;
        //   let ERW_progressFill=ele.every(item => item.ER_custom_input)
        //   if(!ERW_progressFill){
        //     _this.$message.warning('请填写完整的数据')
        //     return
        //   }
        // }
        // for (let i = 0; i < _this.ERW_Safty.length; i++) {
        //   const ele = _this.ERW_Safty[i].ERW_SaftySingle;
        //   let ERW_SaftyFill=ele.every(item => item.ER_custom_input)
        //   if(!ERW_SaftyFill){
        //     _this.$message.warning('请填写完整的数据')
        //     return
        //   }
        // }
        // let ERW_qualityFill=_this.ERW_quality.every(item => item.ER_custom_input)
        // let ERW_otherFill=_this.ERW_other.every(item => item.ER_custom_input)
        // if(!ERW_qualityFill||!ERW_otherFill){
        //   _this.$message.warning('请填写完整的数据')
        //   return
        // }
        let WeekJSON = JSON.stringify({
          ERW_progress:_this.ERW_progress,
          ERW_quality:_this.ERW_quality,
          ERW_Safty:_this.ERW_Safty,
          ERW_other:_this.ERW_other
        })
        _this.$axios({
          method: "post",
          url: `${window.bim_config.webserverurl}/api/VisualView/VisualView/EditWeekJSON?Token=${_this.$staticmethod.Get('Token')}`,
          data: _this.$qs.stringify({
              WeekJSON,
              Week_Time: _this.ERD_date,
              organizeId: _this.organizeId
          })
        })
        .then(() => {
          _this.$message.success('保存成功')
          _this.ER_cont_edit_cancel()
        })
        .catch(() => {
        });
      },
      ERD_ChoseWeek(c){
        let odata = null
        if(c==="next"){
          odata = new Date(
            new Date(this.ERD_date).getTime() + 7*24 * 60 * 60 * 1000
          ); //计算当前日期+7
        }else{
          odata = new Date(
            new Date(this.ERD_date).getTime() - 7*24 * 60 * 60 * 1000
          ); //计算当前日期-7
        }
        this.ERD_date = this.convertToDate(odata); //格式化日期并赋值
      },
      convertToDate(date) {
          var date = new Date(date);
          var y = date.getFullYear();
          var m = date.getMonth() + 1;
          var d = date.getDate();
          m = m < 10 ? "0" + m : m; //月小于10，加0
          d = d < 10 ? "0" + d : d; //day小于10，加0
          return y + "-" + m + "-" + d;
      },
      // 初始化日期
      GetNewWeek() {
        this.$axios({
          method: "post",
          url: `${window.bim_config.webserverurl}/api/VisualView/VisualView/GetNewWeek?Token=${this.$staticmethod.Get('Token')}`,
          data:this.$qs.stringify({
            organizeId:this.organizeId
          })
        })
        .then(res => {
          let data = res.data.Data
          if(data.Week_Time){
            let nowDay = data.Week_Time;
            this.ERD_week = this.getWeekInMonth(new Date(nowDay));
            this.ERD_date = nowDay;
          }else{
            var date = new Date();
            var y = date.getFullYear();
            var m = date.getMonth() + 1;
            var d = date.getDate();
            m = m < 10 ? "0" + m : m; //月小于10，加0
            d = d < 10 ? "0" + d : d; //day小于10，加0
            let nowDay = y + "-" + m + "-" + d;
            this.ERD_week = this.getWeekInMonth(new Date(nowDay));
            this.ERD_date = nowDay;
          }
        })
        .catch(() => {
        });
      },
      ERD_WatchDate(n) {
        if(n === null) return
        this.ERD_week = this.getWeekInMonth(new Date(n));
        this.$axios({
          method: "get",
          url: `${window.bim_config.webserverurl}/api/VisualView/VisualView/GetWeekJSON?Week_Time=${n}&organizeId=${this.organizeId}&Token=${this.$staticmethod.Get('Token')}`,
        })
        .then(res => {
          let data = res.data.Data
          if(Object.keys(data).length!=0){
            if(data.ERW_progress){
              this.ERW_progress = data.ERW_progress
            }else{
              for (let i = 0; i < this.ERW_progress.length; i++) {
                const ele = this.ERW_progress[i].ERW_progressSingle;
                ele.forEach(e => {
                  e.ER_custom_input = ''
                });
              }
            }
            data.ERW_quality?this.ERW_quality =data.ERW_quality:this.ERW_quality.forEach(e => {
              e.ER_custom_input = ''
            });
            if(data.ERW_Safty){
              this.ERW_Safty = data.ERW_Safty
            }else{
              for (let i = 0; i < this.ERW_Safty.length; i++) {
                const ele = this.ERW_Safty[i].ERW_progressSingle;
                ele.forEach(e => {
                  e.ER_custom_input = ''
                });
              }
            }
            data.ERW_other?this.ERW_other =data.ERW_other:this.ERW_other.forEach(e => {
              e.ER_custom_input = ''
            });
            this.ER_data = data
          }else{
            for (let i = 0; i < this.ERW_progress.length; i++) {
              const ele = this.ERW_progress[i].ERW_progressSingle;
              ele.forEach(e => {
                e.ER_custom_input = ''
                e.Week_Id = 0
              });
            }
            this.ERW_quality.forEach(e => {
              e.ER_custom_input = ''
              e.Week_Id = 0
            });
            for (let i = 0; i < this.ERW_Safty.length; i++) {
              const ele = this.ERW_Safty[i].ERW_SaftySingle;
              ele.forEach(e => {
                e.ER_custom_input = ''
                e.Week_Id = 0
              });
            }
            this.ERW_other.forEach(e => {
              e.ER_custom_input = ''
              e.Week_Id = 0
            });
            this.ER_data = undefined
          }
        })
        .catch(() => {
        });
      },
      // 根据日期判断是月的第几周
      getWeekInMonth(t) {
        if (t == undefined || t == '' || t == null) {
          t = new Date();
        } else {
          var _t = new Date();
          _t.setYear(t.getFullYear());
          _t.setMonth(t.getMonth());
          _t.setDate(t.getDate());

          var date = _t.getDate(); //给定的日期是几号

          _t.setDate(1);
          var d = _t.getDay(); //1. 得到当前的1号是星期几。
          var fisrtWeekend = d;
          if (d == 0) {
            fisrtWeekend = 1;
            //1号就是星期天
          } else {
            fisrtWeekend = 7 - d + 1; //第一周的周末是几号
          }
          if (date <= fisrtWeekend) {
            return 1;
          } else {
            return 1 + Math.ceil((date - fisrtWeekend) / 7);
          }
        }
      },
      getContainsHeight(){
        var _s = {};
        _s["height"] = (document.body.clientHeight-115)+ 'px';
        return _s;
      },
    },
    created(){
      this.organizeId = this.$staticmethod._Get("organizeId");
    },
    mounted() {
      this.GetNewWeek();
    },
    watch:{
      ERD_date(n){
        this.ERD_WatchDate(n)
      }
    }
}
</script>
<style scoped>
  .el-container {
    border-bottom: 1px solid rgba(0, 0, 0, 0.15);
  }
  .el-main{
    min-height: 100px;
  }
  .el-header{
    color: #111;
    font-weight: bold;
    background-color: #fff;
    height: 54px;
    display: flex;
    align-items: center;
    flex: none;
    line-height: 60px;
    padding-left: 30px;
  }
  .el-header .custom_config_header_btn{
    background: linear-gradient(224deg,rgba(0,145,255,1) 0%,rgba(0,122,255,1) 100%);
    border-radius: 4px;
    color: #FFFFFF;
    font-size: 14px;
    padding: 6px 10px;
    font-weight: normal;
    cursor: pointer;
    line-height: normal;
    margin-left: 70px;
  }
  .container {
    padding: 24px;
    overflow: auto;
  }
  ._css-tabcontrolarea {
    height: 54px;
    display: flex;
    align-items: center;
    flex: none;
    background-color: #fff;
    border-bottom: 1px solid rgba(0, 0, 0, 0.15);
  }
  ._css-tabcontrolitem {
    margin-left: 40px;
    width: 64px;
    height: 24px;
    line-height: 24px;
    font-size: 16px;
    font-weight: 400;
    color: rgba(0, 0, 0, 0.85);
    margin-left: 24px;
  }
  .ER_body{
    width: 100%;
    min-width: 950px;
    margin: 0 auto;
    background: #fff;
    border-radius: 2px;
    padding:25px 0 50px 0;
  }
  .Engineering_header{
    padding: 0px 10% 60px 70px;
    border-bottom: 1px solid #ccc;
  }
  .container .custom_config_btn{
    background: linear-gradient(224deg,rgba(0,145,255,1) 0%,rgba(0,122,255,1) 100%);
    border-radius: 4px;
    color: #FFFFFF;
    font-size: 14px;
    padding: 9px 16px;
    cursor: pointer;
    float: right;
    margin-left: 20px;
  }
  .ER_item{
    display: flex;
    align-items: center;
    font-weight: 500;
    color: rgba(0,0,0,0.45);
    padding: 10px 0;
  }
  .ER_cont{
    color:rgba(0,0,0,.85);
    width: 165px;
    text-align: right;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    cursor: pointer;
  }
  .el-button {
    padding: 6px 10px;
    border-radius: 2px;
  }
  .ERD_header_date{
    float: left;
    width: 420px;
    text-align: left;
  }
  .choose_date,.ER_input{
    border-radius: 4px;
    border: 1px solid #dcdfe6;
    background-color: #fff;
    cursor: pointer;
    width: 160px;
  }
  .ER_custom_item{
    padding: 0px 0px 20px 70px;
  }
  .ER_custom_item .el-header{
    color: #000;
    font-weight: 400;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-flex: 0;
    -ms-flex: none;
    flex: none;
    padding: 0;
  }
  .ER_custom_item li{
    display: flex;
    align-items: center;
    float: left;
    margin: 5px 50px 5px 0;
  }
  .ER_custom_item li .ER_input{
    border-radius: 4px;
    border: 1px solid #dcdfe6;
    background-color: #fff;
    cursor: pointer;
    width: 200px;
  }
  .ERW_quality li{
    display: flex;
    align-items: center;
    float: initial;
    margin: 10px;
  }
  .ERW_quality li .ER_cont{
    width: 250px;
  }
  
.Covery{
  width:100%;height:100%;background-color:rgba(0,0,0,0.4);position: absolute;top:0px;left: 0px;z-index: 11;
}
</style>