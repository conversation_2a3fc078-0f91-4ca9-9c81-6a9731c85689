<template>
    <div class="msr-admin-wp">
        <comps-screen-dialog 
        @closeCallBack=closeCallBack
        title="构件管理">
            <div slot="main" style="background:rgba(247,247,247,1);">
                <div class="mgradmin-wp">
                    <header>
                        <span>构件状态</span>
                        <div @click="addMaterial">
                            <i class="icon-interface-addnew"></i>
                            新建构件状态
                        </div>
                    </header>
                    <main data-debug="mgradmin">
                        <ul class="_css_bugs_and_bb_noWork css-miniscroll">
                            <li v-for="item in list" :key="item.ItemDetailId" >
                                <div :style="{'background':item.ItemCode,}" :title="item.ItemName" class="overflow_ellipsis">{{item.ItemName}}</div>
                                <div class="edit" :style="{'background':item.ItemCode,}" style="color:#fff" @click="materielListClick(item)">
                                    <i class="icon-interface-edit_se"></i>
                                </div>
                                
                            </li>
                        </ul>
                    </main>
                </div>
            </div>
        </comps-screen-dialog>
        <div class="maker" @click="dialogConfig.show=false" v-show="dialogConfig.show"> 
            <div class="center" @click.stop>
                <header>
                    <span>{{dialogConfig.type == 0 ?'新增':'修改'}}</span>
                    <i class="icon-suggested-close" @click="dialogConfig.show=false"></i>
                </header>
                <main>
                    <p>名称</p>
                    <el-input v-model="dialogConfig.input" placeholder="请输入内容"></el-input>
                    <div class="color-list">
                        <div v-for="item in colorList" :key="item" :style="{background:item}" @click="colorListClick(item)">
                            <i class="icon-suggested-check" v-show="dialogConfig.selectColor == item"></i>
                        </div>
                    </div>
                    <div class="btn-wp">
                        <div :style="{visibility:dialogConfig.type == 0 ? 'hidden':'visible'}" @click="predel()">删除状态</div>
                        <div class="btns-right">
                            <div @click="cancelediting" >取消</div>
                            <div @click="submit">确定</div>
                        </div>
                    </div>
                </main>
            </div>
        </div>
    </div>
    
</template>
<script>
import compsScreenDialog from '../../CompsDialog/CompsFullScreenDialog'
import modifyDialog from '../../CompsCommon/CompsModifyDicItem'
export default {
    data(){
        return {
            Token:'',
            ProjectID:'',
            list:[],
            dialogConfig:{
                show:false,
                input:'',
                id:'',
                value:'',
                selectColor:'rgba(28, 50, 75, 1)',
                type:0,//0新增
            },
            colorList: [
                "rgba(28, 50, 75, 1)",
                "rgba(86, 98, 112, 1)",
                "rgba(127, 179, 210, 1)",
                "rgba(151, 107, 61, 1)",
                "rgba(184, 158, 123, 1)",
                "rgba(29, 164, 140, 1)",
                "rgba(115, 113, 157, 1)"
            ],
        };
    },
    components:{compsScreenDialog,modifyDialog},
    created(){

    },
    methods:{

        cancelediting(){
            var _this = this;
            _this.dialogConfig.show = false;
        },

        closeCallBack(){
           this.$emit('close') 
            // this.$router.push({name:'MaterialsMgr',params:{Token:this.Token}})
        },
        colorListClick(item){
            this.dialogConfig.selectColor = item
        },
        materielListClick(item){
            console.log(item)
            this.dialogConfig.selectColor = item.ItemCode
            this.dialogConfig.input = item.ItemName
            this.dialogConfig.type = 1
            this.dialogConfig.id = item.ItemDetailId
            this.dialogConfig.value = item.ItemValue
            this.dialogConfig.show = true
        },
        addMaterial(){
            this.dialogConfig.selectColor = "rgba(28, 50, 75, 1)"
            this.dialogConfig.input = ''
            this.dialogConfig.type = 0
            this.dialogConfig.show = true
        },
        submit(){
            if(this.dialogConfig.input.length>0){

            
                if(this.dialogConfig.type == 0){
                    let data = {
                        Token:this.Token,
                        dictype_select_id:'materialstatus',
                        item_id:-1,
                        item_name:this.dialogConfig.input,
                        item_color_ItemCode:this.dialogConfig.selectColor,
                        companyId:this.ProjectID

                    }
                    this.$axios.post(`${window.bim_config.webserverurl}/api/User/Issue/ModifyDicItem`,this.$qs.stringify(data)).then(res=>{
                        //debugger;
                        if(res.data.Ret != -1){
                            this.$message({
                                message: '添加成功',
                                type: 'success'
                            });
                            this.getListData()
                            this.dialogConfig.show = false
                        } else {
                            this.$message.error(res.data.Msg);
                        }
                    })
                }else{
                    let data = {
                        Token:this.Token,
                        dictype_select_id:'materialstatus',
                        item_id:this.dialogConfig.id,
                        item_name:this.dialogConfig.input,
                        item_val:this.dialogConfig.value,
                        item_color_ItemCode:this.dialogConfig.selectColor,
                        companyId:this.ProjectID
                    }
                    this.$axios.post(`${window.bim_config.webserverurl}/api/User/Issue/ModifyDicItem`,this.$qs.stringify(data)).then(res=>{
                        //debugger;
                        if(res.data.Ret != -1){
                            this.$message({
                                message: '修改成功',
                                type: 'success'
                            });
                            this.getListData()
                            this.dialogConfig.show = false
                        } else {
                            this.$message.error(res.data.Msg);
                        }
                    })
                }
            }else{
                this.$message.error('请输入构件状态名称');
            }
        },
        predel(){
            var _this = this;
            _this.$confirm('确认删除该状态？', '操作确认', {
                confirmButtonText:'确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(x => {
                _this.del();
            }).catch(x => {

            });
        },
        del(){
            let data = {
                Token:this.Token,
                dictype_select_id:'materialstatus',
                item_id:this.dialogConfig.id
            }
            this.$axios.post(`${window.bim_config.webserverurl}/api/User/Issue/RemoveDicItem`,this.$qs.stringify(data)).then(res=>{
                if(res.data. Ret == 1){
                    this.$message({
                        message: '删除成功',
                        type: 'success'
                    });
                    this.getListData()
                    this.dialogConfig.show = false
                }
            })
        },
        getListData(){
            this.$axios.get(`${window.MgrBaseUrl.GetMaterialStatus}?organizeId=${this.ProjectID}&Token=${this.Token}`).then(res=>{
                this.list = res.data.Data
            })
        }
    },
    mounted(){
        this.ProjectID = this.$staticmethod._Get("organizeId");
        this.Token = this.$staticmethod.Get("Token")
        // 设置左侧菜单自动选中
        var _this = this;
        _this.$emit('onmounted', 'MaterialsMgrAdmin');
        this.getListData()
        
    },
}
</script>
<style scoped>
._css_bugs_and_bb_noWork{
    overflow-y: auto;
}
.overflow_ellipsis{
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  color:#fff;
  padding: 0 20px;
}
</style>
<style lang="stylus" scoped rel="stylesheet/stylus">
    .maker{
        position fixed
        top 0
        left 0
        right 0
        bottom 0
        background rgba(0,0,0,.5)
        z-index 1001
        .center{
            width 410px
            height 274px
            background #ffffff
            border-radius 4px
            box-shadow 0px 13px 24px -17px rgba(11,41,62,0.8)
            position absolute
            top 50%
            left 50%
            transform translate(-50%,-50%)
            display flex
            flex-direction column
            header{
                height 64px
                line-height 64px
                display flex
                flex-direction row
                justify-content space-between
                align-items center
                padding 0 24px
                span{
                    font-size 20px
                    color rgba(0,0,0,.85)
                }
                i{
                    color #bfbfbf;
                    cursor pointer
                }
            }
            main{
                padding 0 24px
                flex 1
                .btn-wp{
                    display flex
                    flex-direction row
                    justify-content space-between
                    &>div{
                        color red
                        line-height 40px
                        cursor pointer
                        &.btns-right{
                            display flex
                            flex-direction row
                            div{
                                color #ffffff
                                width 76px
                                height 40px
                                line-height 40px
                                background #1890FF
                                border-radius 2px
                                margin-left 16px
                                opacity .8
                                &:hover{
                                    opacity 1
                                }
                                
                                &:first-child{
                                    color rgba(0,0,0,.85)
                                    background #fff
                                }

                            }
                        }
                    }
                }
                p {
                    text-align left 
                    margin-top 10px
                    font-size 12px
                    color rgba(0,0,0,.65)
                }
                .color-list{
                    height 80px
                    display flex
                    flex-direction row
                    align-items center
                    div{
                        width 40px
                        height 40px
                        border-radius 4px
                        cursor pointer
                        margin-left 12px
                        display flex
                        align-items center
                        justify-content  center
                        color #fff
                        &:first-child{
                            margin-left 5px
                        }

                    }
                }
            }
        }
    }
    .mgradmin-wp{
        width 800px
        display flex
        
        height calc(100% - 64px)
        flex-direction column
        margin auto
        header{
            margin-top 24px
            height 68px
            display flex
            flex-direction row
            justify-content space-between
            border-bottom 1px solid rgba(0,0,0,.09)
            background #fff
            &>span{
                font-size 16px
                color rgba(0,0,0,.85)
            }
            div{
                color #1890FF
                cursor pointer
                font-size 14px
            }
        }
        main{
            height calc(100% - 90px)
            box-shadow: 0px 1px 3px 0px rgba(0, 21, 41, 0.12)
            background #fff
            padding 24px
            li{
                padding-left 76px
                padding-right 24px
                height 76px
                display flex
                flex-direction row
                justify-content space-between
                align-items center
                border-bottom 1px solid rgba(0,0,0,.09)
                div{
                    width 155px
                    height 40px
                    line-height 40px
                    text-align center
                    // border 1px solid #ccc
                    border-radius 4px
                    &.edit{
                        width 28px
                        height 28px
                        line-height 34px
                        background:rgba(24,144,255,0.1);
                        cursor pointer
                        display none
                    }
                }

                &:hover{
                    background rgba(0,0,0,.04)
                    .edit{
                        display block
                    }
                }
            }
        }
    }
</style>