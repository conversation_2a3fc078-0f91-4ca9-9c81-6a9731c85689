<template>
  <div class="_css-materials-all">
    <el-tree
      :data="treeData"
      :props="treem_props"
      show-checkbox
      class="_css-customstyle"
      ref="ref_bmc"
      node-key="id"
      :default-expanded-keys="treem_default_expanded_keys"
      :default-checked-keys="tree_checked_param"
      :expand-on-click-node="false"
      :highlight-current="true"
      
      current-node-key="bm_guid"
      @node-collapse="node_collapse"
      @node-expand="node_expand"
      
      @current-change="treefunc_current_change"
      @check="treefunc_check"
    >
      <span class="css-fc _css-treenode-content" slot-scope="{ node, data }">
        <i class="css-icon20 css-fs18 css-fc css-jcsa"></i>
        <span :title="node.label" class="css-ml4 _css-treenodelabel">
          <div class="_css-treenodellabelname">
            {{data.label}}
            <span v-if="data.data.bm_materialcode">({{data.data.bm_materialcode}})</span>
            <i v-if="data.data.bm_materialcode" 
              class="_css-tree-detail icon-interface-component-search"
              @click.stop="iconDetailClick(data.data)"></i>
          </div>

          <!-- <div class="_css-treenodellabelnum">{{data.children.length}}</div> -->
        </span>
        <!-- <span :title="node.label" class="css-ml4 _css-treenodelabel" v-else>
          <div class="_css-treenodellabelname"> {{data.data.bm_materialname}}({{data.data.bm_materialcode}})</div>
        </span> -->
      </span>
    </el-tree>
    <!-- <el-tree
      :data="m_bmclist"
      :props="treem_props"
      lazy
      show-checkbox
      ref="ref_bmc"
      class="_css-customstyle"
      :expand-on-click-node="false"
      @node-collapse="node_collapse"
      @node-expand="node_expand"
      @node-click="nodeClick"
      :load="treefunc_loadChild"
      :default-expand-all="false"
      node-key="bmc_guid"
      :highlight-current="true"
      :auto-expand-parent="true"
      current-node-key="bm_guid"
      :default-checked-keys="tree_checked_param"
      :default-expanded-keys="treem_default_expanded_keys"
      @current-change="treefunc_current_change"
      @check="treefunc_check"
    >
      <span class="css-fc _css-treenode-content" slot-scope="{ node, data }">
        <i class="css-icon20 css-fs18 css-fc css-jcsa" :class="data.classname"></i>
        <span :title="node.label" class="css-ml4 _css-treenodelabel" v-if="data.bmc_createTime">
          <div class="_css-treenodellabelname">{{data.bmc_name}}</div>

          <div class="_css-treenodellabelnum">{{data.ChildrenItemCount}}</div>
        </span>
        <span :title="node.label" class="css-ml4 _css-treenodelabel" v-else>
          <div class="_css-treenodellabelname"> {{data.bmc_name}}({{data.ChildrenItemCount}})</div>
        </span> 
      </span>
    </el-tree> -->
  </div>
</template>
<script>
export default {
  name:'MaterialsMgrTree',
  components: {},
  data() {
    return {
      treeData: [],
      m_bmclist: [], // tree结构的data
      //  treem_props树控件的 props 属性对象
      treem_props: {
        children: "children",
        label: "bmc_name",
        isLeaf: "isLeaf"
      },
      treem_default_expanded_keys:[],  // 默认展开节点,当前选中的节点
      tree_checked_param: [],  // 选中的节点
      checked_data: null,
      lastClickData: null,
    };
  },
  props: {
    tree_table_id:{
      type:Array,
      default:true
    },
    openModelID:{
      type:String,
      default:true
    },
  },
  watch: {},
  created() {},
  mounted() {
    this.tree_checked_param = Array.from(this.tree_table_id)
    this.treem_default_expanded_keys = Array.from(this.tree_table_id)
    // console.log(Array.from(this.tree_table_id),'=dffsa')
    // this.func_gettypelistAll();
    this.getTreeDataFun();
  },
  filters: {},
  computed: {},
  methods: {
    // 获取树结构的data
    getTreeDataFun(){
      let _this = this;
      let _organizeId = _this.$staticmethod._Get("organizeId");
      let _url = `${window.bim_config.webserverurl}/api/Material/MaterialCategory/GetLeftTreeData?organizeId=${_organizeId}&Token=${this.$staticmethod.Get('Token')}`
      _this.$axios
        .get(_url)
        .then(res=>{
          if(res.data && res.data.Ret > 0){
            // console.log(res.data)
            _this.treeData= res.data.Data.List;
            // resolve(retdata);
          }
        }).catch(err=>{

        })
    },

    // 收起节点 回调
    node_collapse(itemi, node, comp) {
      // console.log(itemi, node, comp)
      // 移除展开过的节点
      // ---------------
      let _this = this;
      if (_this.treem_default_expanded_keys.indexOf(node.id) >= 0) {
        _this.treem_default_expanded_keys = _this.treem_default_expanded_keys.filter(
          x => x != node.id
        );
      }
      itemi.classname = "icon-interface-component_classification";
    },
    // 双击展示当前详情
    iconDetailClick(data){
      this.$parent.materialname_click(data.bm_guid);
    },
    // 展开节点 回调
    node_expand(itemi, node, comp) {
      // 记录展开过的节点
      // 记录到 treem_default_expanded_keys 中
      // ------------------------------------
      var _this = this;
      if (_this.treem_default_expanded_keys.indexOf(node.id) < 0) {
        _this.treem_default_expanded_keys.push(node.id);
      }

      itemi.classname = "icon-interface-component_classification";
    },
    // 加载子节点
    // ---------该方法为原来懒加载的方法，现暂没使用
    treefunc_loadChild(node, resolve) {
      // 页面初次加载会自动调用此处，且 node.id 为0
      // 如果 node.id 为 0，直接 return
      // ------------------------------
      // console.log(node)
      if (node.id == 0) {
        return;
      }
      //debugger;

      // 拿着 code 请求子一级数据
      // ----------------------
      var _this = this;
      var _organizeId = _this.$staticmethod._Get("organizeId");
      var _pcode = node.data.bmc_code;

      // 收起又显示树的情况
      // ----------------
      if (node.data.length != undefined && node.data.length >= 0) {
        _this.func_gettypelistAll();
        return;
      }
      let axiosUrl = ''
      if(_pcode != ''){
        axiosUrl = 'GetCategories_WithData';
      }else{
        axiosUrl = 'GetCategories';
      }
      var _url = `${window.bim_config.webserverurl}/api/Material/MaterialCategory/${axiosUrl}?organizeId=${_organizeId}&baseCode=${_pcode}&Token=${this.$staticmethod.Get('Token')}`;
      _this.$axios
        .get(_url)
        .then(x => {
          if (x.data && x.data.Ret > 0) {
            // 深拷贝、补充 classname、设置 isLeaf
            // ---------------------------------
            // var retdata = _this.$staticmethod.DeepCopy(x.data.Data.list);
            // for (var i = 0; i < retdata.length; i++) {
            //   retdata[i].classname = "icon-interface-component_classification";
            //   retdata[i].isLeaf = retdata[i].DirectChildrenCount == 0;
            //   if (retdata[i].isLeaf) {
            //     retdata[i].classname =
            //       "icon-interface-associated-component _css-1890ff";
            //   }
            // }
            // console.log(retdata)
            let retdata;
            if(x.data.Data.list.length > 0){
              retdata = _this.$staticmethod.DeepCopy(x.data.Data.list);
              for (let i = 0; i < retdata.length; i++) {
                retdata[i].classname = "icon-interface-component_classification";
                if(retdata[i].DirectChildrenCount == 0 && retdata[i].ChildrenItemCount == 0){
                  retdata[i].isLeaf = retdata[i].DirectChildrenCount == 0;
                  // retdata[i].isLeaf == true
                  retdata[i].classname = "icon-interface-associated-component _css-1890ff";
                }
              }
            }else{
              retdata = _this.$staticmethod.DeepCopy(x.data.Data.datalist);
              for (let i = 0; i < retdata.length; i++) {
                // retdata[i].classname = "icon-interface-component_classification";
                // retdata[i].isLeaf = 0;
                // 最后一级的节点  为了和前面的父节点有一样的属性，在循环时候添加了
                retdata[i].materialid = retdata[i].bm_guid;
                retdata[i].materialname = retdata[i].bm_materialname;
                retdata[i].materialcode = retdata[i].bm_materialcode;
                retdata[i].statusid = retdata[i].bc_guid_materialstatus;
                retdata[i].bhaselerel = retdata[i].bhaselerel;
                retdata[i].bm_updatetime = retdata[i].bm_updatetime;

                retdata[i].DirectChildrenCount == 0;
                retdata[i].isLeaf = true;
                retdata[i].bmc_name = retdata[i].bm_materialname;
                retdata[i].ChildrenItemCount = retdata[i].bm_materialcode;
                retdata[i].bmc_guid = retdata[i].bm_guid;
                retdata[i].classname = "icon-interface-associated-component _css-1890ff";
              }
            }

            resolve(retdata);
          }
        })
        .catch(x => {
          console.error(x);
        });
    },
    // 加载节点：“单元管理”
    // ------------------该方法为原来懒加载的方法，现暂没使用
    func_gettypelistAll() {
      // 请求接口，加载第一级的分类
      // ------------------------
      var _this = this;

      // 调用接口，获取分类数据
      // --------------------
      var _this = this;
      var _organizeId = _this.$staticmethod._Get("organizeId");
      var _url = `${window.bim_config.webserverurl}/api/Material/MaterialCategory/GetCategories?organizeId=${_organizeId}&baseCode=&Token=${_this.$staticmethod.Get('Token')}`;
      _this.$axios
        .get(_url)
        .then(x => {
          // 处理 isLeaf、children、classname 等属性
          // --------------------------------------
          var retdata = _this.$staticmethod.DeepCopy(x.data.Data.list);

          // 标记是否有第一级分类
          // ------------------
          var hasfirstwftc = retdata.length > 0;

          // 预设“单元管理”树节点
          // -------------------
          var _all = [
            {
              classname: "icon-interface-component_classification",

              // 这里应设为多少？
              // --------------
              isLeaf: !hasfirstwftc,

              // 全部的 code 为空字符串
              // -1000 表示空节点，置空时使用，这里设为-1000
              // 其它值
              // -----
              bmc_code: "",
              bmc_guid: "-1000",
              bmc_createTime: '0',
              bmc_name: "单元管理",
              ChildrenItemCount: x.data.Data.totalcount
            }
          ];
          _this.m_bmclist = _all;
          this.treefunc_loadChild();
          // _this.treem_default_expanded_keys = ['-1000'];
          // 自动展开这个“全部”节点
          // --------------------
          //_this.func_expandroot();
          // 自动展开tree
          // _this.$nextTick(()=>{
          //     _this.func_expandroot();
          // });
 
        })
        .catch(x => {
          console.error(x);
        });
    },
    // 当前节点选中变化
    // ---------------
    treefunc_current_change(data, node) {
        if(this.lastClickData && this.lastClickData.bm_guid == data.data.bm_guid) return; // 手动判断阻止多次点击
        this.lastClickData = data;
        // 当前节点选中，对应构件高亮并聚焦
        data.bhaselerel = 1;
        data.materialid = data.data.bm_guid;
        data.bm_guid = data.data.bm_guid;
        // 节点点击时候  直接调用父组件的方法
        if(data.bm_guid){
          if(data.bhaselerel>0){
            this.$parent.openAssociationModelComponents(data,this.openModelID);
          }else{
            this.$message.warning("暂无关联模型");
          }
        }
    },
    // 刷新页面或点击类型
    // -----------------
    loadmaterialitems2(typeid, willnotshowloading) {
      // console.log(typeid)
      var _this = this;
      if(_this.oldSetmaterialtype !== typeid){
        _this.oldSetmaterialtype =  typeid;
        _this.extdata.selectedtypeid = typeid;
        // _this.reset_filters(true);
        // _this.handleFilters(willnotshowloading);
      }
    },
    // 复选框被选中
    treefunc_check(checkedNodes,checkedKeys){
      // 复选框选择时候 构件高亮，通过接口返回的值，在父组件进行高亮构件操作   使用的bm_guid，末级节点才有bm_guid
      
      // console.log(checkedKeys.checkedKeys,'选中2') 
      let checked_bm_guid = checkedKeys.checkedKeys.join(',')
      
      if(checkedKeys.checkedKeys.length > 0){
          
        // 选中的构件   请求接口   得到所有的模型，所有的构件 进行高亮
        let _this = this;
        _this.$axios({
          url:`${this.$MgrBaseUrl.GetMaterialsElements}?bm_guids=${checked_bm_guid}&token=${this.$staticmethod.Get("Token")}`,
          method:'get',
          // data: {
          //   bm_guids: checked_bm_guid,
          //   token:this.$staticmethod.Get("Token")
          // }
        }).then(x => {
          if(x.data.Data){
            this.checked_data = x.data.Data;
            this.$emit('treefunc_checked',this.checked_data)
          }else{
            _this.$message.error(x.data.Msg);
          }
        }).catch(x => {
          _this.$message.error(x);
        });
      }else{
        // 这里是没有选任何构件，要获取当前所有的着色构件，然后删除颜色
        this.$parent.nottreefunc_checked();
      }
    }, 
  }
};
</script>
<style scoped>
._css-materials-all{
  height: calc(100% - 64px - 14px);
  overflow-y: auto;
  overflow-x: hidden;
}
._css-materials-all
  ._css-customstyle
  .el-table__header-wrapper
  /deep/
  .el-table
  th
  > .cell {
  color: rgba(0, 0, 0, 0.6);
  /* color: #f00; */
}
._css-treenode-content {
    width:calc(100% - 24px);
    text-align: left;
}
._css-treenode-content ._css-treenodellabelnum {
    margin-right: 12px;
    max-width: 30px;
    overflow-x: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    color: rgba(0,0,0,0.3);
    display: block;
}
/* 
._css-treenode-content:hover ._css-treenodellabelnum {
    display: none;
} */
._css-treenode-content:hover ._css-treenode-menubtn {
    display: block;
}
._css-treenodelabel {
    font-size: 13px;
    flex: 1;
    display: flex;
    height: 32px;
    align-items: center;
    width: calc(100% - 48px);
}
._css-treenodellabelname {
  flex:1;
  width: calc(100% - 24px);
  overflow-x: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
._css-treenode-content ._css-treenodellabelnum {
    margin-right: 12px;
    max-width: 30px;
    overflow-x: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    color: rgba(0,0,0,0.3);
    display: block;
}
._css-tree-detail{
  float: right;
  margin-right: 30px;
}
._css-1890ff {
  color:#1890FF;
}
</style>