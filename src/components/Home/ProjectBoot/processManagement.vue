<template>
  <div class="_css-omall">
    <div class="header">流程管理</div>
    <div  class="_css-data-inner css-miniscroll css-iframe-workflow" id="id_formlist">
      <iframe v-if="workFlowUrl" width="100%" height="100%" :src="workFlowUrl" frameborder="0"  ></iframe>
    </div>
  </div>
</template>
<script>

export default {
  data() {
    return {
      workFlowUrl: '', // 流程表单
    };

  },
  created() {
  },
  mounted() {
    this.getWorkFlowToken('FlowTree')

  },
  methods: {
    getWorkFlowToken(type) {
      let _this = this;
      let _OrganizeId = _this.$staticmethod._Get("organizeId")
      let _username = _this.$staticmethod.Get("username");
      let _Token = _this.$staticmethod.Get("Token"); 
      let _url = `${window.bim_config.webserverurl}/api/User/User/CCFlowLogin`;
      let _para = {
          Token: _Token,
          organizeId: _OrganizeId
      };
      _this
          .$axios({
              method: "post",
              url: _url,
              data: _para
          })
          .then(x => {
              if (x.data.Ret > 0) { 
               _this.workFlowUrl= `${window.bim_config.CCFlowUrl}/WF/Portal/${type}.htm?Token=${x.data.Data}&OrgNo=${_OrganizeId}&UserNo=${_username}&typeProject=project`
              } else {
                _this.$message.error(x.data.Data);
              }
          })
          .catch(x => {
              console.error(x);
          });
    },
  }
};
</script>
<style scoped>
._css-omall {
  height: 100%;
  width: 100%;
}
.header{
  text-align: left;
  line-height: 50px;
  height: 50px;
  background-color: #fff;
  padding-left: 16px;
  font-size: 16px;
  font-weight: 500;
  color: rgba(0,0,0,.9);
  border-bottom: 1px solid rgba(0,0,0,.15);
}
.css-iframe-workflow{
  height: 100% !important;
  width: 100% !important;
  padding-top: 12px;
  box-sizing: border-box;
}
</style>

