<template>
    <div class="_css-daxing">
        <div data-v-9b9f33f8="" class="css-page-head"></div>
        <!-- <div class="upload-btn" @click="uploadShowFun('大兴机场日模板')">
            导入工程数据（日）
        </div>
        <div class="upload-btn" @click="uploadShowFun('大兴机场周模板')">
            导入工程数据（周）
        </div>
        <div class="upload-btn" @click="uploadShowFun('大兴机场月模板')">
            导入工程数据（月）
        </div> -->
        
      <el-row>
        <el-col :span="18" :offset="3">
          <div class="grid-content bg-purple">
            <div class="title">大兴新机场建设指挥部 — 数字建设驾驶舱施工数据入口</div>
            <div class="upload">
                <div class="upload-cont">
                  <h1>导入施工监管数据（按日）</h1>
                  <span>填报数据是对应“数据看板 — 工程进度”、“安全生产 — 进场情况”模块</span>
                </div>
                <div class="upload-btn el-icon-download" @click="uploadShowFun('大兴机场日模板')">
                    导入工程数据
                </div>
            </div>
            <div class="upload">
                <div class="upload-cont">
                  <h1>导入施工监管数据（按周）</h1>
                  <span>填报数据是对应“数据看板 — 工程进度&安全文明”、“形象进度 — 完成情况&进度滞后”模块</span>
                </div>
                <div class="upload-btn el-icon-download" @click="uploadShowFun('大兴机场周模板')">
                    导入工程数据
                </div>
            </div>
            <div class="upload">
                <div class="upload-cont">
                  <h1>导入施工监管数据（按月）</h1>
                  <span>填报数据是对应“数据看板 — 投资成本、工资质量、安全文明”、“形象进度 — 工程进度”模块</span>
                </div>
                <div class="upload-btn el-icon-download" @click="uploadShowFun('大兴机场月模板')">
                    导入工程数据
                </div>
            </div>
          </div>
        </el-col>
      </el-row>
        <div>
           <zdialog-function
                v-if="uploadShow"
                init_title="导入工程数据"
                :init_zindex="1001"
                :init_innerWidth="450"
                :init_width="450"
                init_closebtniconfontclass="icon-suggested-close"
                @onclose="uploadShowFun()">
                <!-- 输入区域 -->
                <div slot="mainslot">
                    <div class="_css-line css-common-line">
                        步骤一:&nbsp;&nbsp;
                        <p @click="downloadTemplate"
                            class="import-cost-link css-hover-btn">点击此处下载工程数据导入模板（必须）</p>
                    </div>
                    <div class="_css-line css-common-line">
                        步骤二:&nbsp;&nbsp;
                        <p class="import-cost-link css-hover-btn">
                            <label class="desc" for="uploadCostTemplate">点击此处上传录入完成的工程数据</label>
                            <input type="file" ref="uploadCostTemplate" id="uploadCostTemplate" accept=".xlsx,.xls" class="hidden" @change="getFile">
                        </p>
                    </div>
                </div><!-- //输入区域 -->
            </zdialog-function>
        </div>
    </div>
</template>
<script>
export default {
    name: 'DaXingAirPort',
    data(){
        return {
            uploadShow: false,
            clickdateType: '',  // 记录点击的是日、周、月
        }
    },
    methods:{
        uploadShowFun(dateType){
            this.clickdateType = dateType;
            this.uploadShow = !this.uploadShow
        },
        downloadTemplate(){
            let url = `${this.$configjson.webserverurl}/api/Daxing/Daxing/DownloadFile?file=${this.clickdateType}`
            window.open(url,'_blank');
        },
        getFile(e){
            if (e.target.files[0] != '' && e.target.files[0] != undefined) {
                let fd = new FormData();
                fd.append('Token',this.$staticmethod.Get("Token"))
                fd.append('file',e.target.files[0]);
                this.$axios.post(`${this.$configjson.webserverurl}/api/DaXing/DaXing/ImportExcel`,fd).then(res=>{
                    // console.log(res);
                    if (res.data.Ret == 1) {
                        this.$message.success(res.data.Msg);
                        this.uploadShow = false;
                    } else {
                        this.$message.error(res.data.Msg);
                    }
                });
            }
        }
    }
}
    
</script>
<style scoped>
.upload{
  margin: 40px auto 0;
  height: 100px;
  background: rgba(40, 153, 255, 0.05);
  border-radius: 10px;  
}
.upload-btn{
    width: 90px;
    line-height: 22px;
    border-radius: 4px;
    color: #fff;
    font-size: 12px;
    margin: 36px;
    padding: 5px 10px;
    background: linear-gradient(133deg, #007AFF 0%, #0091FF 100%);
    cursor: pointer;
    float: right;
}
.upload-cont{
  float: left;
  margin-left: 30px;
  margin-top: 20px;
  text-align: left;
}
.upload-cont h1{
  font-size: 16px;
  font-family: PingFangSC-Semibold, PingFang SC;
  font-weight: 600;
  color: #333330;
  line-height: 38px;
}
.upload-cont span{
  font-size: 12px;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #666666;
  line-height: 22px;
}
.import-cost-link{
    margin: 10px;
    text-decoration: underline;
    font-style: italic;
}
.import-cost-link .hidden {
    display: none;
}
 .el-row {
    margin-top: 50px;
    
  }
  .grid-content {
    min-width: 800px;
  }
  .grid-content .title{
    color: #007AFF;
    margin-bottom: 40px;
    font-size: 26px;
    text-align: center;
    font-weight: bold;
  }
</style>