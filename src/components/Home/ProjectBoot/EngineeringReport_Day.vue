<template>
  <div>
    <div class="_css-tabcontrolarea">
      <div class="_css-tabcontrolitem">施工日报</div>
    </div>
    <div class="container" :style="getContainsHeight()">
      <div class="ER_body">
        <div class="Engineering_header">
          <div class="ERD_header_date">
             <el-date-picker
              v-model="ERD_date"
              type="date"
              placeholder="选择日期"
              format="yyyy 年 MM 月 dd 日"
              value-format="yyyy-MM-dd"
              class="choose_date"
              style="float: left;width: 220px;">
            </el-date-picker>
            <div v-if="ERD_date">
              <div class="custom_config_btn css-hover-btn"  @click="ERD_ChoseDay('next')">
                下一天
              </div>
              <div class="custom_config_btn css-hover-btn"  @click="ERD_ChoseDay('')">
                上一天
              </div>
            </div>
          </div>
          <div class="custom_config_btn css-hover-btn" v-if="ER_edit === false"  @click="ER_cont_edit">
            编辑
          </div>
          <div class="custom_config_btn css-hover-btn"  @click="custom_config_edit" v-if="ER_edit === false">
            自定义配置
          </div>
          <div  v-if="ER_edit === true">
            <div class="css-hoverable-container">
                <zbutton-function
                    :init_text="'取消'"
                    :init_bgcolor="'darkgray'"
                    :init_fontsize="14"
                    :debugmode="true"
                    :init_height="undefined"
                    :init_width="'76px'"
                    :style="'marginLeft:30px;float: right;'"
                    @onclick="ER_cont_edit_cancel"
                    >
                </zbutton-function>
            </div>
            <div class="css-hoverable-container">
                <zbutton-function
                    :init_text="'保存'"
                    :init_fontsize="14"
                    :debugmode="true"
                    :init_height="undefined"
                    :init_width="'76px'"
                    :style="'marginLeft:30px;float: right;'"
                    @onclick="ER_cont_edit_ok"
                    >
                </zbutton-function>
            </div>
          </div>
        </div>
        <el-container>
          <el-header>进场情况</el-header>
          <ul class="ER_custom_item">
            <li v-for="(item,index) in Mobilization" :key="index">
              <div class="ER_cont" :title="item.ER_cont">
                  {{item.ER_cont}}：
              </div>
              <el-input v-model="Mobilization[index].ER_custom_input" :disabled="InputStatus" placeholder="请输入内容" class="ER_input"></el-input>
            </li>
          </ul>
        </el-container>
        <el-container>
          <el-header>安全文明</el-header>
          <ul class="ER_custom_item">
            <li v-for="(item,index) in Safety" :key="index">
              <div class="ER_cont" :title="item.ER_cont">
                  {{item.ER_cont}}：
              </div>
              <el-input v-model="Safety[index].ER_custom_input" :disabled="InputStatus" placeholder="请输入内容" class="ER_input"></el-input>
            </li>
          </ul>
        </el-container>
        <el-container>
          <el-header>现场情况</el-header>
          <ul class="ER_custom_item">
            <li v-for="(item,index) in Site" :key="index">
              <div class="ER_cont" :title="item.ER_cont">
                  {{item.ER_cont}}：
              </div>
              <el-input v-model="Site[index].ER_custom_input" :disabled="InputStatus" placeholder="请输入内容" class="ER_input"></el-input>
            </li>
          </ul>
        </el-container>
      </div>
    </div>
    <CompsCockpitCustomEdit
      v-if="showCustomEdit"
      :CockpitCustomEditStatus = 1
      :ChooseER_date = ERD_date
      :ER_data = ER_data
      :organizeId = organizeId
      @CloseERCustomEdit="IsDialogCovery=false,showCustomEdit=false"
      @RefreshData="ERD_WatchDate(ERD_date)"
      >
    </CompsCockpitCustomEdit>
    <div v-show="IsDialogCovery" class="Covery"></div>
    <!-- 
        @Close='CloseCustomEdit' -->
  </div> 
</template>
<script>
import CompsCockpitCustomEdit from '@/components/CompsCockpitCustom/CompsCockpitCustomEdit'
export default {
    name:'EngineeringReport_Day',
    components:{
      CompsCockpitCustomEdit
    },
    data(){
      return {
        organizeId:'',
        ERD_date:'',//施工日报选择时间
        ER_edit:false, //编辑状态
        InputStatus:true, //禁用状态
        showCustomEdit:false, //自定义配置页
        IsDialogCovery:false,//显示遮罩层
        Mobilization:[//进场情况
          {
            ER_cont:"施工人员计划（人次）",
            ER_custom_input:''
          },
          {
            ER_cont:"施工人员实际（人次）",
            ER_custom_input:''
          },
          {
            ER_cont:"机械台班计划（台）",
            ER_custom_input:''
          },
          {
            ER_cont:"机械台班实际（台）",
            ER_custom_input:''
          },
          {
            ER_cont:"材料进场计划（批次）",
            ER_custom_input:''
          },
          {
            ER_cont:"材料进场实际（批次）",
            ER_custom_input:''
          },
        ],
        Safety:[//安全文明
          {
            ER_cont:"通风消杀计划（人次）",
            ER_custom_input:''
          },
          {
            ER_cont:"通风消杀实际（人次）",
            ER_custom_input:''
          },
          {
            ER_cont:"今日体温检测（人数）",
            ER_custom_input:''
          },
          {
            ER_cont:"体温检测合格（人数）",
            ER_custom_input:''
          },
          {
            ER_cont:"降尘喷洒（次数）",
            ER_custom_input:''
          },
          {
            ER_cont:"车辆清洗（次数）",
            ER_custom_input:''
          },
        ],
        Site:[//现场情况
          {
            ER_cont:"现场负责人员",
            ER_custom_input:''
          },
          {
            ER_cont:"现场值班人员",
            ER_custom_input:''
          },
        ],
        ER_data:undefined
      }
    },
    methods:{
      ER_cont_edit(){
        this.ER_edit = true
        this.InputStatus = false
      },
      ER_cont_edit_cancel(){
        this.ER_edit = false
        this.InputStatus = true
      },
      custom_config_edit(){
        if(!this.ERD_date){
            this.$message.warning('请选择日期')
            return
        }
        this.showCustomEdit = true
        this.IsDialogCovery = true
      },
      ER_cont_edit_ok(){
        let _this = this
        if(!this.ERD_date){
            this.$message.warning('请选择日期')
            return
        }
        // let ERD_MobilizationFill=_this.Mobilization.every(item => item.ER_custom_input)
        // let ERD_SafetyFill=_this.Safety.every(item => item.ER_custom_input)
        // let ERD_SiteFill=_this.Site.every(item => item.ER_custom_input)
        // if(ERD_MobilizationFill === true&&ERD_SafetyFill === true&&ERD_SiteFill === true){
        //   let DailyJSON = JSON.stringify({
        //     ERD_Mobilization:_this.Mobilization,
        //     ERD_Safety:_this.Safety,
        //     ERD_Site:_this.Site
        //   })
        //   _this.$axios({
        //     method: "post",
        //     url: `${window.bim_config.webserverurl}/api/VisualView/VisualView/EditDailyJSON`,
        //     data:_this.$qs.stringify({
        //         DailyJSON,
        //         Daily_Time: _this.ERD_date,
        //         organizeId:_this.organizeId
        //     })
        //   })
        //   .then(() => {
        //     _this.$message.success('保存成功')
        //     _this.ER_cont_edit_cancel()
        //   })
        //   .catch(() => {
        //   });
        // }else{
        //   _this.$message.warning('请填写完整数据')
        // }
        let DailyJSON = JSON.stringify({
            ERD_Mobilization:_this.Mobilization,
            ERD_Safety:_this.Safety,
            ERD_Site:_this.Site
          })
          _this.$axios({
            method: "post",
            url: `${window.bim_config.webserverurl}/api/VisualView/VisualView/EditDailyJSON?Token=${_this.$staticmethod.Get('Token')}`,
            data:_this.$qs.stringify({
                DailyJSON,
                Daily_Time: _this.ERD_date,
                organizeId:_this.organizeId
            })
          })
          .then(() => {
            _this.$message.success('保存成功')
            _this.ER_cont_edit_cancel()
          })
          .catch(() => {
          });
      },
      ERD_ChoseDay(c){
        let odata = null
        if(c==="next"){
          odata = new Date(
            new Date(this.ERD_date).getTime() + 24 * 60 * 60 * 1000
          ); //计算当前日期 +1
        }else{
          odata = new Date(
            new Date(this.ERD_date).getTime() - 24 * 60 * 60 * 1000
          ); //计算当前日期 -1
        }
        this.ERD_date = this.convertToDate(odata); //格式化日期并赋值
        // console.log(this.ERD_date);
      },
      convertToDate(date) {
          var date = new Date(date);
          var y = date.getFullYear();
          var m = date.getMonth() + 1;
          var d = date.getDate();
          m = m < 10 ? "0" + m : m; //月小于10，加0
          d = d < 10 ? "0" + d : d; //day小于10，加0
          return y + "-" + m + "-" + d;
      },
      ERD_WatchDate(n){
        // console.log(n);
        if(n === null) return
        this.$axios({
          method: "get",
          url: `${window.bim_config.webserverurl}/api/VisualView/VisualView/GetDailyJSON?Daily_Time=${n}&organizeId=${this.organizeId}&Token=${this.$staticmethod.Get('Token')}`,
        })
        .then(res => {
          let data = res.data.Data
          if(Object.keys(data).length!=0){
            // console.log(data);
            this.Mobilization = data.ERD_Mobilization
            this.Safety = data.ERD_Safety
            this.Site = data.ERD_Site
            this.ER_data = data
          }else{
            this.Mobilization.forEach(e => {
              e.ER_custom_input = ''
              e.Daily_Id = 0
            });
            this.Safety.forEach(e => {
              e.ER_custom_input = ''
              e.Daily_Id = 0
            });
            this.Site.forEach(e => {
              e.ER_custom_input = ''
              e.Daily_Id = 0
            });
            this.ER_data = undefined
          }
        })
        .catch(() => {
        });
      },
      getContainsHeight(){
        var _s = {};
        _s["height"] = (document.body.clientHeight-115)+ 'px';
        return _s;
      },
      GetNewDaily(){
        this.$axios({
          method: "post",
          url: `${window.bim_config.webserverurl}/api/VisualView/VisualView/GetNewDaily?Token=${this.$staticmethod.Get('Token')}`,
          data:this.$qs.stringify({
            organizeId:this.organizeId
          })
        })
        .then(res => {
          let data = res.data.Data
          if(data.Daily_Time){
            this.ERD_date = data.Daily_Time;
          }else{
            var date = new Date();
            var y = date.getFullYear();
            var m = date.getMonth() + 1;
            var d = date.getDate();
            m = m < 10 ? "0" + m : m; //月小于10，加0
            d = d < 10 ? "0" + d : d; //day小于10，加0
            this.ERD_date = y + "-" + m + "-" + d;
          }
        })
        .catch(() => {
        });
      }
    },
    created(){
      this.organizeId = this.$staticmethod._Get("organizeId");
    },
    mounted(){
      this.GetNewDaily()
    },
    watch:{
      ERD_date(n){
        this.ERD_WatchDate(n)
      }
    }
}
</script>
<style scoped>
  .el-container {
    border-bottom: 1px solid rgba(0, 0, 0, 0.15);
  }
  .el-main{
    min-height: 100px;
  }
  .el-header{
    color: #111;
    font-weight: bold;
    background-color: #fff;
    text-align: left;
    line-height: 60px;
    padding-left: 30px;
  }
  .container {
    padding: 24px;
    overflow: auto;
  }
  ._css-tabcontrolarea {
    height: 54px;
    display: flex;
    align-items: center;
    flex: none;
    background-color: #fff;
    border-bottom: 1px solid rgba(0, 0, 0, 0.15);
  }
  ._css-tabcontrolitem {
    margin-left: 40px;
    width: 64px;
    height: 24px;
    line-height: 24px;
    font-size: 16px;
    font-weight: 400;
    color: rgba(0, 0, 0, 0.85);
    margin-left: 24px;
  }
  .ER_body{
    width: 100%;
    min-width: 950px;
    margin: 0 auto;
    background: #fff;
    border-radius: 2px;
    padding:25px 0 50px 0;
  }
  .Engineering_header{
    padding: 0px 10% 60px 70px;
    border-bottom: 1px solid #ccc;
  }
  .container .custom_config_btn{
    background: linear-gradient(224deg,rgba(0,145,255,1) 0%,rgba(0,122,255,1) 100%);
    border-radius: 4px;
    color: #FFFFFF;
    font-size: 14px;
    padding: 9px 16px;
    cursor: pointer;
    float: right;
    margin-left: 20px;
  }
  .ER_item{
    display: flex;
    align-items: center;
    font-weight: 500;
    color: rgba(0,0,0,0.45);
    padding: 10px 0;
  }
  .ER_cont{
    color:rgba(0,0,0,.85);
    width: 165px;
    text-align: right;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    cursor: pointer;
  }
  .el-button {
    padding: 6px 10px;
    border-radius: 2px;
  }
  .ERD_header_date{
    float: left;
    width: 420px;
    text-align: left;
  }
  .choose_date,.ER_input{
    border-radius: 4px;
    border: 1px solid #dcdfe6;
    background-color: #fff;
    cursor: pointer;
    width: 160px;
  }
  .ER_custom_item{
    padding: 0px 0px 20px 70px;
  }
  .ER_custom_item li{
    display: flex;
    align-items: center;
  }
  .ER_custom_item li .ER_cont{
    padding: 15px 0;
  }
  .ER_custom_item li .ER_input{
    width: 300px;
  }
  
.Covery{
  width:100%;height:100%;background-color:rgba(0,0,0,0.4);position: absolute;top:0px;left: 0px;z-index: 11;
}
</style>