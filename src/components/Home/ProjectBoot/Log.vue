<template>
	<div class="_project_log">
		<div class="btn-initiating-header">{{ headerTitleText }}</div>
        <div class="_css-search">
            <CompsUsersInput
                @oninput="_oninput"
                :placeholder="'请输入操作描述关键字'"
                :width="'100%'"
                :maxWidth="'200px'"
                iconclass="icon-interface-search"
            ></CompsUsersInput>
        <div @click="search($event)" class="_css-pm-btnsearch">搜索</div>
        </div>
		<div class="log-table">
			<el-table :data="logTableList" :key="Math.random()" height="95%" style="width: 100%;">
				<el-table-column prop="UserName" label="操作人" width="180">
				</el-table-column>
				<el-table-column prop="ModuleName" label="操作模块" width="180">
				</el-table-column>
				<el-table-column prop="Description" label="操作描述">
					<template slot-scope="scope">
                        <el-tooltip class="item" effect="dark" :content="scope.row.Description" placement="top-start">
                            <div class="overflow-point" >  
                                {{ scope.row.Description }}
                            </div>
                        </el-tooltip>
					</template>
				</el-table-column>
				<el-table-column prop="IpAddress" label="操作IP" width="180"> </el-table-column>
				<el-table-column prop="LogTime" label="操作时间" width="180">
				</el-table-column>
			</el-table>
			<div class="pagination-css">
				<el-pagination
					@size-change="handleSizeChange"
					@current-change="handleCurrentPaginationChange"
					:current-page="PageNum"
					:page-sizes="[20, 50, 100, 200]"
					:page-size="PageSize"
					layout="total, sizes, prev, pager, next, jumper"
					:total="paginationPageLength"
				>
				</el-pagination>
			</div>
		</div>
	</div>
</template>
<script>
import CompsUsersInput from "@/components/CompsAuth/CompsUsersInput";
export default {
	name: "ProjectLog",
    components: {
        CompsUsersInput
    },
	data() {
		return {
			headerTitleText: "",
			logTableList: [],
			PageNum: 1,
			PageSize: 20,
            paginationPageLength: 0,
            KeyWord: '',
		};
	},
	created() {
		this.headerTitleText = this.$staticmethod._Get("menuText") || "";
	},
	mounted() {
		this.getLogList();
	},
	methods: {
		async getLogList() {
			let data = {
				PageNum: this.PageNum,
				PageSize: this.PageSize,
				OrganizeId: this.$staticmethod._Get("organizeId"),
				KeyWord: this.KeyWord,
				// RequestMethod: '',
				// ModuleName:'',
				// StartTime: '',
				// EndTime: '',
			};
			let res = await this.$api.GetUserLogPaged(data);
			this.logTableList = res.Data.Data;
			this.paginationPageLength = res.Data.Total;
		},
		handleSizeChange(val) {
			this.PageSize = val;
			this.getLogList();
		},
		handleCurrentPaginationChange(val) {
			this.PageNum = val;
			this.getLogList();
		},
        search(){
            this.getLogList();
        },
        _oninput(str,para2,ev){
            this.KeyWord = str;
            if (ev.keyCode == 13) {
                this.getLogList();
            }
        }
		 
	},
};
</script> 

<style scoped>
._project_log{
    height: 100%;
    flex-direction: column;
}
.log-table{
    height: calc(100% - 140px);
    background:#fff;
    overflow-y: auto;
    margin: 0 20px ;
    padding: 5px;
}
._css-search {
  height: 64px;
  display: flex;
  align-items: center;
  padding-left: 24px;
  box-sizing: border-box;
}
._css-pm-btnsearch {
  width: 66px;
  height: 24px;
  line-height: 24px;
  border-radius: 4px;
  border: 1px solid rgba(24, 144, 255, 1);
  color: rgba(24, 144, 255, 1);
  cursor: pointer;
  margin-left: 24px;
}

._css-pm-btnsearch:hover {
  background-color: rgba(24, 144, 255, 1);
  color: #fff;
}

.log-table /deep/ .el-table__body-wrapper .el-table__row {
    height: 40px !important;
    border-bottom: 1px solid rgba(0, 0, 0, .09);
    line-height: 40px !important;
}

.log-table /deep/ .el-table__header-wrapper th {
    height: 44px !important;
    border-bottom: 1px solid rgba(0, 0, 0, .09);
    border-right: 1px solid rgba(0, 0, 0, .09);
    line-height: 44px !important;
}
.log-table /deep/ .el-table td {
    border-bottom-color: rgba(0, 0, 0, 0.08) !important;
    border-right: 1px solid rgba(0,0,0,.08)!important
}
.log-table /deep/ .el-table td:last-child {
    border-right-color: rgba(0, 0, 0, 0.00) !important;
}
</style>