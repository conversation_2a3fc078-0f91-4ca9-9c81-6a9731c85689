<template>
	<div class="_css-materials-all">
		<div class="btn-initiating-header">{{ headerTitleText }}</div>
		<div class="_css-bottomarea" @click.stop="closeAll">
			<div class="left-content" ref="lefttree" v-if="proTreeIn">
				<div class="_css-materialtypelist-head">
					<i class="icon-new-dangan"></i>
					<span class="_css-materhead-text">档案管理</span>
				</div>
				<el-tree
					:data="treeData"
					node-key="Id"
					ref="tree"
					class="_css-customstyle"
					:highlight-current="true"
					:auto-expand-parent="true"
					default-expand-all
					:props="defaultProps"
					:expand-on-click-node="false"
					:default-checked-keys="defaultCheckedKeys"
					@current-change="treefunc_current_change"
				>
					<span
						class="css-fc _css-treenode-content"
						slot-scope="{ node, data }"
					>
						<i
							class="css-mr4 css-fs16 css-fs18 css-fc css-jcsa"
							:class="
								data.HasChildren ? 'icon-new-tree-last' : 'icon-new-tree'
							"
						></i>
						<span :title="node.label" class="css-ml4 _css-treenodelabel">
							<el-tooltip
								popper-class="css-no-triangle"
								v-if="data.CategoryName.length > 12"
								effect="dark"
								:content="data.CategoryName"
								placement="top"
							>
								<div class="_css-treenodellabelname overflow-point">
									{{ data.CategoryName }}
								</div>
							</el-tooltip>
							<div class="_css-treenodellabelname" v-else>
								{{ data.CategoryName }}
							</div>
							<div
								@click.stop="func_tree_showmenu($event, data)"
								class="_css-treenode-menubtn icon-interface-list"
							></div>
						</span>
					</span>
				</el-tree>
			</div>
			<div class="_css-choose-switch-parent">
				<div class="_css-choose-switch" @click.stop="chooseCloseOrOpen">
					<i
						class="pro-in-out"
						:class="tableClose ? 'pro-in-out' : 'p-out'"
					></i>
				</div>
			</div>
			<div class="content-right" :class="proTreeIn ? '' : 'width100'">
				<div class="_css-materialtypetab-head">
					<div class="unify-btn" :class="treeClickData.ArchivesAuth.Create ? '' : 'unify-btn-disable'" v-if="!treeClickData.HasChildren && treeClickData.ParentId !='0'" @click.stop="GetCurrentArchivesCode">新增</div>
					<div class="right-input">
						<el-input
							v-model="inputVal"
							placeholder="请输入档案关键字"
							@input="searchName"
						></el-input>
						<i class="icon-interface-search"></i>
					</div>
				</div>
				<div class="_css-materialtypetab-body">
          <div class="table-top-bar" v-if="multipleSelection.length > 0">
						<ul class="menu-item menu-left" style="height: 100%">
							<li>
							<el-checkbox
								:indeterminate="tableTopBarState.isIndeterminate"
								v-model="tableTopBarState.checkAll"
								@change="handleTableTopBarCheckAllChange">
							</el-checkbox>
							</li>

							<li  @click="deleteSelectQualityItems">
							 <span class="icon icon-interface-delete"></span>删除
							</li>

							<li><span class="desc">已选择{{ multipleSelection.length }}项</span></li>
						</ul>
					</div>
				  <el-table
						element-loading-text="数据加载中..."
						element-loading-spinner="el-icon-loading"
						element-loading-background="rgba(0, 0, 0, 0)"
						ref="multipleTable"
						highlight-current-row
						:border="true"
						:stripe="false"
						use-virtual
						height="70%"
						:data="tableData"
						style="width: 100%"
						:default-sort="{ prop: 'date', order: 'descending' }"
						class="_css-table-ele css-scroll _css-customstyle _css-qualitystyle"
						:header-cell-style="{ 'background-color': 'transparent' }"
						@selection-change="handleSelectionChange"
						:row-height="rowHeight"
					>
						<el-table-column
							type="index"
							class-name="center-text"
							align="center"
							label="序号"
							width="50"
						></el-table-column>
						<el-table-column type="selection"  class-name="center-text" align="center" width="50"></el-table-column>

            <el-table-column property="ArchivesName" label="档案名称" minWidth="150">
							<template slot-scope="scope">
								<el-tooltip class="item" v-if="scope.row.ArchivesName.length > 24" popper-class="tooltip-model-hover"  effect="dark" :content="scope.row.ArchivesName" placement="top-start">
									<div class="overflow-point _css-itemname">{{ scope.row.ArchivesName }}</div>
								</el-tooltip>
								<div v-else class="_css-itemname">{{ scope.row.ArchivesName }}</div>
							</template>
						</el-table-column>
						<el-table-column property="ArchivalDepartment" label="归档部门" width="160">
              <template slot-scope="scope">
								<div class="normal-font normal-center">{{ scope.row.ArchivalDepartment || '-' }}</div>
							</template>
            </el-table-column>
						<el-table-column property="ArchivesNumber" label="档案份数" width="100">
              <template slot-scope="scope">
								<div class="normal-font normal-center">{{ scope.row.ArchivesNumber || '-' }}</div>
							</template>
            </el-table-column>
						<el-table-column property="CreateTime" label="申请时间" width="180">
              <template slot-scope="scope">
								<div class="normal-font normal-center">{{ scope.row.CreateTime || '-' }}</div>
							</template>
            </el-table-column>
            <el-table-column property="ArchivalDescription" label="档案说明" minWidth="150">
							<template slot-scope="scope">
								<el-tooltip class="item" v-if="scope.row.ArchivalDescription.length > 10" popper-class="tooltip-model-hover"  effect="dark" :content="scope.row.ArchivalDescription" placement="top-start">
									<div class="overflow-point normal-font">{{ scope.row.ArchivalDescription || '-' }}</div>
								</el-tooltip>
								<div v-else class="normal-font">{{ scope.row.ArchivalDescription || '-' }}</div>
							</template>
						</el-table-column>
						<el-table-column property="ArchivedUserId" label="归档人" width="140">
              <template slot-scope="scope">
								<div class="normal-font normal-center">{{ scope.row.ArchivedUserName || '-' }}</div>
							</template>
            </el-table-column>
						<el-table-column property="ArchivedTime" label="归档时间" width="180">
              <template slot-scope="scope">
								<div class="normal-font normal-center">{{ scope.row.ArchivedTime || '-' }}</div>
							</template>
            </el-table-column>
						<el-table-column
              property="Status"
              label="当前节点"
              width="120"

            >
              <template slot="header">
                <div class="_css-table-title _css-table-title-hover" @click.stop="handleHeaderClick($event)">
                  <span class="_css-dataitemcode">当前节点</span>
                  <i
                    ref="materialcode"
                    class="_css-table-header-icon-el"
                    :class="statusDialogShow?'el-icon-caret-bottom':'el-icon-caret-top'"
                  ></i>
                </div>
              </template>
              <template slot-scope="scope">
								<div class="normal-font normal-center" :class="scope.row.Status == 3 ? 'c0C800C' : ''">{{ scope.row.Status | filterStatus }}</div>
							</template>
            </el-table-column>
            <el-table-column
							:resizable="true"
							class="_css-col-relmodel _css-celllongcolumn"
							label="操作"
							prop="bhas_relexam"
							width="100"
						>
							<template slot-scope="scope">
								<div class="_css-costitem _css-btnsctn normal-center" >
									<div
                    v-if="scope.row.HasApprovalAuth"
										@click="submitAudit(scope.row)"
										class="_css-btnimport _css-innerbtn c007AFF css-cp"
										:class="scope.row.HasApprovalAuth ? '' : 'not-allow'"
									>
										<div>处理</div>
									</div>
									<div
                    v-if="!scope.row.HasApprovalAuth"
										@click="submitFillIn(scope.row)"
										class="_css-btnimport _css-innerbtn c007AFF css-cp"
										:class="
											!scope.row.HasApprovalAuth
												? ''
												: 'not-allow'
										"
									>
										<div>查看</div>
									</div>
								</div>
							</template>
						</el-table-column>

					</el-table>
					<el-pagination
						@size-change="handleSizeChange"
						@current-change="handleCurrentPaginationChange"
						:current-page="pageNum"
						:page-sizes="[20, 50, 100, 200]"
						:page-size="pageSize"
						layout="total, sizes, prev, pager, next, jumper"
						:total="paginationPageLength"
					>
					</el-pagination>
				</div>
			</div>
			<div
				class="first-dialog"
				:style="firstDialogStyle()"
				v-if="firstDialogShow"
			>
				<div class="_css-flow-contextmenu-in" :class="categoryUpdateAuth ? '' : '_css-dis'" @click.stop="archivesCreate('0')">
					<div class="_css-flow-contextmenu-inicon icon-interface-addnew"></div>
					<div class="_css-flow-contextmenu-intext">新增</div>
				</div>
				<div class="_css-flow-contextmenu-in" :class="categoryUpdateAuth ? '' : '_css-dis'" @click.stop="ShowingImportTemplate = true">
					<div
						class="_css-flow-contextmenu-inicon icon-interface-cloud-upload"
					></div>
					<div class="_css-flow-contextmenu-intext">导入</div>
				</div>
        <div class="_css-flow-contextmenu-in" :class="categoryExportAuth ? '' : '_css-dis'" @click.stop="archivesExport(true)">
					<div
						class="_css-flow-contextmenu-inicon icon-interface-download-fill"
					></div>
					<div class="_css-flow-contextmenu-intext">导出</div>
				</div>
			</div>
			<div
				class="first-dialog"
				:style="treeAddDialogStyle()"
				v-if="treeAddDialogShow"
			>
				<div class="_css-flow-contextmenu-in" :class="categoryUpdateAuth ? '' : '_css-dis'" @click.stop="archivesCreate()">
					<div class="_css-flow-contextmenu-inicon icon-interface-addnew"></div>
					<div class="_css-flow-contextmenu-intext">新增</div>
				</div>
				<div
					class="_css-flow-contextmenu-in"
					:class="categoryUpdateAuth ? '' : '_css-dis'"
					@click.stop="archivesCreate('edit')"
				>
					<div class="_css-flow-contextmenu-inicon icon-interface-edit"></div>
					<div class="_css-flow-contextmenu-intext">编辑</div>
				</div>
				<div class="_css-flow-contextmenu-in" :class="categoryUpdateAuth ? '' : '_css-dis'" @click.stop="archivesDel" v-if="!selectTreeData.HasChildren">
					<div
						class="_css-flow-contextmenu-inicon icon-interface-model-delete"
					></div>
					<div class="_css-flow-contextmenu-intext">删除</div>
				</div>
				<div class="_css-flow-contextmenu-in" :class="categoryExportAuth ? '' : '_css-dis'" @click.stop="archivesExport(false)">
					<div
						class="_css-flow-contextmenu-inicon icon-interface-download-fill"
					></div>
					<div class="_css-flow-contextmenu-intext">导出</div>
				</div>
			</div>
			<zdialog-function
				:init_title="typeAddOrEdit ? '新增分类' : '编辑分类'"
				:init_zindex="1003"
				:init_innerWidth="350"
				:init_width="350"
				init_closebtniconfontclass="icon-suggested-close"
				@onclose="status_showedit = false"
				v-if="status_showedit"
			>
				<div
					slot="mainslot"
					class="_css-addingnameinput-ctn"
					@mousedown="_stopPropagation($event)"
				>
					<div class="_css-line _css-line-name">
						<div class="_css-title _css-title-flowname">分类名称：</div>
						<div class="_css-fieldvalue _css-fieldvaluename _css-selectednode">
							<el-input
								@mousedown="_stopPropagation($event)"
								placeholder="请输入"
								v-model="editDialog.CategoryName"
							>
							</el-input>
						</div>
					</div>
					<div class="_css-line _css-line-name">
						<div class="_css-title _css-title-flowname">分类编码：</div>
						<div class="_css-fieldvalue _css-fieldvaluename _css-selectednode">
							<el-input
								@mousedown="_stopPropagation($event)"
								placeholder="请输入"
								v-model="editDialog.CategoryCode"
							>
							</el-input>
						</div>
					</div>
				</div>

				<div slot="buttonslot" class="_css-flowAddBtnCtn">
					<zbutton-function
						:init_text="'保存'"
						:init_fontsize="14"
						:debugmode="true"
						:init_height="'40px'"
						:init_width="'120px'"
						@onclick="func_saveedit"
					>
					</zbutton-function>
					<zbutton-function
						:init_text="'取消'"
						:init_color="'rgba(24, 144, 255)'"
						:init_bgcolor="'#fff'"
						:init_fontsize="14"
						:debugmode="true"
						:init_height="'40px'"
						:init_width="'120px'"
						@onclick="func_canceledit"
					>
					</zbutton-function>
				</div>
			</zdialog-function>
			<CompsStepTip3
				:zIndex="1000"
				v-if="ShowingImportTemplate == true"
				width="504"
				:mubanimgclass="mubanimgclass"
				:bc_guid="firstTreeDataId"
        :importUrl="'/api/Archives/Category/Import'"
        :downloadUrl="'/api/Archives/Category/DownloadTemplate'"
				@oncancel="ShowingImportTemplate = false"
				@onok="_onimportok"
				title="导入"
			></CompsStepTip3>
      <CompsArchivesDialog
        v-if="archivesDialogShow"
        :selectTreeData="treeClickData"
        :processingType="processingType"
        :archivesCode="archivesCode"
        :detailId="detailId"
        @close="archivesDialogClose"
				@previewFile="previewFile"
      ></CompsArchivesDialog>
      <div
				class="first-dialog status-dialog"
				:style="statusDialogStyle()"
				v-if="statusDialogShow"
			>
        <div class="check-box">
          <el-checkbox-group v-model="tableStatusCheck" @change="handletableStatusChange">
              <div class="group" v-for="tableitem in tableStatusArr" :key="tableitem.value">
                  <el-checkbox :label="tableitem.value" >
                      <span class="name-text">{{ tableitem.text }}</span>
                  </el-checkbox>
              </div>
          </el-checkbox-group>
        </div>
        <div class="set-btn">
          <div class="check-btn css-cp" @click.stop="resetTableStatusArr">重置</div>
          <div class="check-btn css-cp bg" @click.stop="searchTableStatusArr">筛选</div>
        </div>
			</div>
		</div>
	</div>
</template>
<script>
import CompsStepTip3 from "@/components/CompsCommon/CompsStepTip3";
import CompsArchivesDialog from "@/components/CompsArchives/CompsArchivesDialog";

export default {
	components: {
		CompsStepTip3,
    CompsArchivesDialog,
	},
	name: "ProjectArchives",
	data() {
		return {
			headerTitleText: "", // header显示文字
			organizeId: "",
			proTreeIn: true,
			tableClose: false,
			treeData: [],
      firstTreeDataId: '', // 记录项目名称的id
			defaultProps: {
				children: "Children",
				label: "CategoryName",
			},
			defaultCheckedKeys: [],
			searchTableName: "",
			inputVal: "",
			selectTreeData: {
        Id: '0',
				CategoryName: '全部',
				CategoryCode: '0',
        HasChildren: true,
      },
      // 树结构点击、在表格处使用
      treeClickData: {
        Id: '0',
				CategoryName: '全部',
				CategoryCode: '0',
        HasChildren: true,
      },
      tableData: [],
      tableStatusCheck: [0,1,2,3],
      tableStatusArr: [
        //  0  待提交 1  待审核 2  待归档  3  已归档
        {
          text: '待提交',
          value: 0,
        },
        {
          text: '待审核',
          value: 1,
        },
        {
          text: '待归档',
          value: 2,
        },
        {
          text: '已归档',
          value: 3,
        },
      ],
			rowHeight: 40,
			pageNum: 1, // 第几页
			pageSize: 20, // 每页多少条
			paginationPageLength: 0, // 总条数
      multipleSelection: [],//已选择的数据
      tableTopBarState: {//表格数据选中后 出现的菜单相关数据
				isIndeterminate: false,//表格表头全选框状态
				checkAll: false,
			},
			firstDialogShow: false,
			treeAddDialogShow: false,
			first_contextpos2: {
				top: 0,
				left: 0,
			},
			treeAddDialogPos: {
				top: 0,
				left: 0,
			},
      statusDialogShow: false,
      status_contextpos2: {
				top: 0,
				left: 0,
			},
			typeAddOrEdit: true,
			editDialog: {
				Id: "",
				CategoryName: "",
				CategoryCode: "",
			},
			status_showedit: false,
			ShowingImportTemplate: false,
			mubanimgclass: "archivesBg",
      processingType: '', // 当前处理的类型（新增0、被驳回待提交1、审批2、驳回3、归档4、退回查看5、归档查看6、详情查看7）
      archivesDialogShow: false,
      archivesCode: '', // 获取档案编码、传值给子组件
      detailId: '', // 当前选中的表格数据的ID 传值给子组件
      categoryUpdateAuth: false, // 编辑分类权限
      categoryExportAuth: false, // 导出权限
		};
	},
	watch: {},
	created() {
		this.organizeId = this.$staticmethod._Get("organizeId");
		this.headerTitleText = this.$staticmethod._Get("menuText") || "";
	},
	async mounted() {
		await this.GetCategoryTree();
		this.handleRoueChange();
	},
	filters: {
    filterStatus(number){
      let text = '-'
			switch(number) {
				case 0:
					text = '待提交';
					break;
				case 1:
					text = '待审核';
					break;
				case 2:
					text = '待归档';
					break;
			  	case 3:
					text = '已归档';
					break;
			}
			return text
    },
  },
	computed: {},
	methods: {
		previewFile(row){
			let FileExtension = row.AttachmentName.substring(row.AttachmentName.lastIndexOf("."));
			let doc = {
					FileId: row.AttachmentId,
					FileName: row.AttachmentName,
					FileExtension: FileExtension,
			};
      let url
      if (row.Type === 0){
        url = `${window.bim_config.webserverurl}/api/v1/file/preview?FileId=${doc.FileId}&Version=1&UserId=&Token=${this.$staticmethod.Get("Token")}`
      }else {
        url = `${window.bim_config.webserverurl}/api/v1/attach/preview?id=${doc.FileId}&Token=${this.$staticmethod.Get("Token")}`;
      }
			// 根据扩展名获取在线浏览地址
			var url_iframe_all;
			if (doc.FileName.toLowerCase().indexOf(".dwg") > 0) {
					this.$emit("set_projectboot_extdata", "_docviewtype", "dwg");
					url_iframe_all = `${this.$configjson.dwgurl}/Home/Index2?dwgurlcfg=${encodeURIComponent(url)}&name=${doc.FileName}`;
			} else {
					this.$emit("set_projectboot_extdata","_docviewtype","office");
					url_iframe_all = this.$staticmethod.getHuangNewcomputeViewUrl(
							url,
							doc.FileName,
							doc.FileExtension
					);
			}
			this.$emit("set_projectboot_extdata", "_show_idocview", true);
			this.$emit("set_projectboot_extdata","_idocviewurl",url_iframe_all);
		},
		handleRoueChange() {
			const urlChangedBy = this.$staticmethod._Get("UrlChangedBy")
			if(urlChangedBy === 'Msg-档案') {
				this.$Bus.$emit('UnFoldMenuAndSelect','XMDA') // ProjectBoot.vue中注册该事件:更新左侧菜单展开节点并选择菜单
				const msgRelatedData = JSON.parse(sessionStorage.getItem("MsgRelatedData")) // 消息关联的数据
				const refProgressPlan = this.$refs.tree
				if(refProgressPlan && msgRelatedData) {
					this.$nextTick(() => {
						this.$refs.tree.setCurrentKey(msgRelatedData.CategoryId);
						let category = this.$refs.tree.getNode(msgRelatedData.CategoryId).data;
						this.treefunc_current_change(category);
					})
				}
				this.$staticmethod._Set("MsgRelatedData","")
				this.$staticmethod._Set("UrlChangedBy","")  // 处理完成重置
			}
	  },
		_stopPropagation(ev) {
			ev && ev.stopPropagation && ev.stopPropagation();
		},
    // 获取树结构
		async GetCategoryTree(type) {
			const checkedKeys = this.$refs.tree.getCurrentKey();
			let params = {
				organizeId: this.organizeId,
				parentId: 0,
				verifyPermissions: true
			};
      let res = await this.$api.GetCategoryTree(params);
			if(res.Ret == 1 && res.Data){
        this.treeData = res.Data
        res.Data.length > 0 ? this.firstTreeDataId = res.Data[0].Id : this.firstTreeDataId = ''
				if(type == 1){
					this.selectTreeData = res.Data[0];
				}
        if(res.Data && res.Data.length > 0 && res.Data[0].Children.length > 0){
					this.treeClickData = res.Data[0].Children[0];
					const urlChangedBy = this.$staticmethod._Get("UrlChangedBy")
		      if(urlChangedBy !== 'Msg-档案') {
						this.getTableList()
					}
				}else{
					this.tableData = []
					this.paginationPageLength = 0
				}
				this.$nextTick(() => {
					this.$refs.tree.setCurrentKey(checkedKeys);
				});
			}

		},
    // 获取详情
		// async GetCategoryInfo(id) {
		// 	let params = {
		// 		id: id,
		// 	};
		// 	let res = await this.$api.GetCategoryInfo(params);
		// },

    // 新增、先获取编码
    async GetCurrentArchivesCode(){
      let params = {
				organizeId:  this.organizeId,
        categoryId:this.treeClickData.Id
			};
			let res = await this.$api.GetCurrentArchivesCode(params);
      if(res.Ret == 1){
        this.archivesCode = res.Data
        this.processingType = '0';
        this.archivesDialogShow = true;
      }
    },
    archivesExport(isExportAll){
      let categoryId = isExportAll ? this.firstTreeDataId : this.selectTreeData.Id;
      let isExportBool = isExportAll ? true : this.selectTreeData.HasChildren
      let d_url = `${window.bim_config.webserverurl}/api/Archives/Archives/Export?organizeId=${this.organizeId}&categoryId=${categoryId}&isExportAll=${isExportBool}&token=${this.$staticmethod.Get("Token")}&verifyPermissions=true`
      window.location.href = d_url
      this.closeAll();
    },
    // 新增树结构数据
		async postCategoryCreate() {
			let params = {
				OrganizeId: this.organizeId,
				CategoryName: this.editDialog.CategoryName,
				CategoryCode: this.editDialog.CategoryCode,
				ParentId: this.selectTreeData.Id || this.firstTreeDataId,
			};
			let res = await this.$api.postCategoryCreate(params);
      if(res.Ret == 1){
        this.$message.success(res.Msg)
        this.status_showedit = false;
        this.GetCategoryTree();
      }
		},
    // 删除树结构数据
		async postCategoryDelete(id) {
			let res = await this.$api.postCategoryDelete(id);
			res.Ret == 1
				? this.$message.success(res.Msg)
				: this.$message.error(res.Msg);
			this.GetCategoryTree();
			this.closeAll();
		},
    // 编辑树结构数据
		async postCategoryModify(id) {
			let params = {
				Id: id,
				CategoryName: this.editDialog.CategoryName,
				CategoryCode: this.editDialog.CategoryCode,
			};
			let res = await this.$api.postCategoryModify(params);
      if(res.Ret == 1){
        this.$message.success(res.Msg)
        this.status_showedit = false;
        this.GetCategoryTree();
      }
		},
		chooseCloseOrOpen() {
			this.proTreeIn = !this.proTreeIn;
		},
		treefunc_current_change(data) {
      this.closeAll();
      console.log(data,'---tree-data')
			this.inputVal = this.searchTableName = "";
			this.selectTreeData = data;
      this.treeClickData = data;
			this.getTableList();
		},
		searchName(val) {
			this.searchTableName = val;
			this.getTableList();
		},
		func_tree_showmenu(ev, data) {
      this.closeAll();
      this.categoryUpdateAuth = data.ArchivesAuth.Update;
      this.categoryExportAuth = data.ArchivesAuth.Export;
      if(data.ParentId == '0'){
        this.first_contextpos2.left = ev.clientX - 190;
        this.first_contextpos2.top = ev.clientY - 30;
        this.firstDialogShow = true;
      }else{
        this.treeAddDialogPos.top = ev.clientY - 30;
        this.treeAddDialogPos.left = ev.clientX - 190;
        if (this.treeAddDialogPos.top > document.body.clientHeight - 240) {
          this.treeAddDialogPos.top = document.body.clientHeight - 240;
        }
        this.treeAddDialogShow = true;
        this.selectTreeData = data;
      }
		},
		closeAll() {
			this.firstDialogShow = false;
			this.treeAddDialogShow = false;
		},
		firstDialogStyle() {
			let _s = {};
			_s["left"] = this.first_contextpos2.left + "px";
			_s["top"] = this.first_contextpos2.top + "px";
			return _s;
		},
    statusDialogStyle() {
			let _s = {};
			_s["left"] = this.status_contextpos2.left + "px";
			_s["top"] = this.status_contextpos2.top + "px";
			return _s;
		},
		treeAddDialogStyle() {
			let _s = {};
			_s["left"] = this.treeAddDialogPos.left + "px";
			_s["top"] = this.treeAddDialogPos.top + "px";
			return _s;
		},
		_onimportok() {
      this.ShowingImportTemplate = false
      this.GetCategoryTree()
    },

		archivesCreate(type) {
			if (type == "0") {
        this.selectTreeData.Id = this.firstTreeDataId
				this.editDialog = {
					Id: this.firstTreeDataId,
					CategoryName: "",
					CategoryCode: "",
				};
				this.typeAddOrEdit = true;
			} else if (type == "edit") {
				this.editDialog = {
					Id: this.selectTreeData.Id,
					CategoryName: this.selectTreeData.CategoryName,
					CategoryCode: this.selectTreeData.CategoryCode,
				};
				this.typeAddOrEdit = false;
			} else {
				this.editDialog = {
					Id: this.selectTreeData.Id,
					CategoryName: "",
					CategoryCode: "",
				};
				this.typeAddOrEdit = true;
			}
			this.status_showedit = true;
			this.closeAll();
		},
    archivesDel(){
			this.closeAll();
      this
				.$confirm("是否确定删除，删除后相关数据将被清空", "删除分类", {
					confirmButtonText: "确认",
					cancelButtonText: "取消",
					type: "warning"
				})
			.then(x => {
				this.postCategoryDelete(this.selectTreeData.Id);
			})
			.catch(x => {});
    },
    func_saveedit(){
      if(this.typeAddOrEdit){
        this.postCategoryCreate()
      }else{
        this.postCategoryModify(this.selectTreeData.Id)
      }
    },
		func_canceledit() {
			this.status_showedit = false;
			this.editDialog = {
				Id: "",
				CategoryName: "",
				CategoryCode: "",
			};
		},
    getTableList(){
      let _andStatus = this.$qs.stringify({ Status: this.tableStatusCheck }, { arrayFormat: 'repeat' })
      let url = `${window.bim_config.webserverurl}/api/Archives/Archives/Paged?OrganizeId=${this.organizeId}&PageNum=${this.pageNum}&PageSize=${this.pageSize}&CategoryId=${this.selectTreeData.Id}&IsSearchAll=${this.selectTreeData.HasChildren}&KeyWord=${this.searchTableName}&${_andStatus}&token=${this.$staticmethod.Get("Token")}`
      this.$axios
        .get(url)
        .then(x=>{
          if (x.status == 200) {
						if (x.data.Ret == 1 ) {
							this.tableData = x.data.Data.Data;
              this.paginationPageLength = x.data.Data.Total
						}else{
              this.tableData = []
              this.paginationPageLength = 0
            }
					}else{
            this.$message.error(x.data.Msg)
          }
        }).catch(err=>{

        })
    },
    // 筛选
    handletableStatusChange(value){
      // console.log(value,'======')
      this.tableStatusCheck = value
    },
    // 删除表格数据
    deleteSelectQualityItems(){
			if (!this.treeClickData.ArchivesAuth.Delete) {
				this.$message.error('您无权删除!');
				return;
			}
      // console.log(this.multipleSelection,'====')
      this
				.$confirm("是否确定删除，删除后相关数据将被清空", "操作确认", {
					confirmButtonText: "确认",
					cancelButtonText: "取消",
					type: "warning"
				})
			.then(x => {
				let id = this.multipleSelection.map((obj) => {
					return obj.Id;
				});
				this.delFun(id);
			})
			.catch(x => {});
    },
    async delFun(id){
      let res = await this.$api.postArchivesDeleteBulk(id);
      if(res.Ret == 1){
        this.$message.success(res.Msg)
				let datalength = this.paginationPageLength - id.length
				this.pageNum = this.deleteAndAdjustPage(datalength,this.pageSize,this.pageNum)
        this.getTableList()
      }
    },
    handleSizeChange(val) {
			this.pageSize = val;
			this.getTableList();
		},
		handleCurrentPaginationChange(val) {
			this.pageNum = val;
			this.getTableList();
		},
    handleTableTopBarCheckAllChange(val){
			this.multipleSelection = val ? this.tableData : [];
			this.tableTopBarState.isIndeterminate = false;
			this.$refs.multipleTable.toggleAllSelection();
		},
    handleSelectionChange(val){
			this.multipleSelection = val;
			//设置第二表头的全选框状态
			let checkedCount = val.length;
			this.tableTopBarState.checkAll = checkedCount === this.tableData.length;
			this.tableTopBarState.isIndeterminate = checkedCount > 0 && checkedCount < this.tableData.length;
		},
    handleHeaderClick(event){
      console.log(event)
        this.status_contextpos2.left = event.clientX - 240;
        this.status_contextpos2.top = event.clientY - 30;
        this.statusDialogShow = !this.statusDialogShow;

    },
    archivesDialogClose(){
      this.archivesDialogShow=false;
      this.getTableList()
    },
    // 点击处理
    submitAudit(row){
      // 处理   待审核   审核，点击展示详情，除了档案位置可以编辑，其他的都不可以编辑，
      this.detailId = row.Id;

      // （新增0、被驳回待提交1、审批2、驳回3、归档4、退回查看5、归档查看6、详情查看7）
			switch(row.Status) {
				case 0:
					// '待提交'
          this.processingType = '1';
					break;
				case 1:
					// '待审核'
          this.processingType = '2';
					break;
				case 2:
					// '待归档'
          this.processingType = '3';
					break;
			  	case 3:
					// '已归档'
          this.processingType = '4';
					break;
			}
      this.archivesDialogShow = true;
    },
    resetTableStatusArr(){
      this.tableStatusCheck = [0, 1, 2, 3]
    },
    searchTableStatusArr(){
      this.getTableList();
      this.statusDialogShow = false;
    },
    // 点击查看
    submitFillIn(row){
      // 查看，先请求详情接口，所有的都不能编辑
      this.detailId = row.Id;
      // （新增0、被驳回待提交1、审批2、驳回3、归档4、退回查看5、归档查看6、详情查看7）
      switch(row.Status) {
				case 0:
					// '待提交'
          this.processingType = '5';
					break;
				case 1:
					// '待审核'
          this.processingType = '6';
					break;
				case 2:
					// '待归档'
          this.processingType = '6';
					break;
			  case 3:
					// '已归档'
          this.processingType = '6';
					break;
        default:
          this.processingType = '7';
					break;
			}
      this.archivesDialogShow = true;
    },
		deleteAndAdjustPage(data, pageSize, currentPage) {
			if (data === 0) {
				return 1;
			} else {
				var totalPage = Math.ceil(data / pageSize); // 计算总页数
				if (currentPage > totalPage) {
					return totalPage; // 如果当前页超出了总页数，返回最后一页
				} else {
					return currentPage; // 否则返回当前页
				}
			}
		}
	},
	beforeDestroy() {},
};
</script>
<style lang="scss" scoped>
@import url("../../../assets/css/MaterialsMgrStyle.css");
.pro-in-out {
	display: inline-block;
	width: 16px;
	height: 120px;
	background-image: url(../../../assets/images/p-in.png);
	background-size: 100%;
	background-repeat: no-repeat;
}
.pro-in-out.p-out {
	background-image: url(../../../assets/images/p-out.png);
	background-repeat: no-repeat;
	background-size: 100%;
	left: 0;
}
._css-bottomarea {
	position: relative;
	display: flex;
	.left-content {
		background: #fff;
		min-width: 270px;
		max-width: 370px;
	}
	.content-right {
		margin: 0 12px;
		// padding: 0 12px;
		flex: 1;
		width: calc(100% - 364px);
	}
	.content-right.width100 {
		width: calc(100% - 64px);
	}
	._css-materialtypetab-body {
		position: relative;
		height: calc(100% - 64px);
	}
}
.right-input {
	width: 230px;
	border: 1px solid #d8d8d8;
	position: absolute;
	top: 16px;
	right: 8px;
}
.right-input i {
	position: absolute;
	top: 6px;
	right: 8px;
	color: #999999;
}
.right-input /deep/ .el-input__inner {
	line-height: 32px;
}
._css-customstyle {
	height: 90% !important;
	overflow: auto;
}
.first-dialog {
	position: absolute;
	background-color: #fff;
	width: 96px;
	box-shadow: 0px 2px 4px 0px rgba(0, 38, 77, 0.15);
	border-radius: 2px;
	z-index: 5;
}

/deep/ ._css-step-tableimagearea{
  height: 100px !important;
}
/deep/ ._css-stop-tableimage{
  width: 330px;
}
._css-materialtypetab-body /deep/ ._css-qualitystyle.el-table th {
	font-size: 14px !important;
}
._css-materialtypetab-body /deep/ ._css-qualitystyle.el-table thead th .cell , ._css-materialtypetab-body /deep/ ._css-qualitystyle .center-text .cell{
	justify-content: center !important;
	padding: 0;
}
.c0C800C{
  color: #0C800C !important;
}
._css-itemname{
  font-weight: 500;
  font-size: 12px;
  color: #222222;
}
.normal-font{
  font-weight: 400;
  font-size: 12px;
  color: #222222;
}
.normal-center{
  width: 100%;
  text-align: center;
}
.status-dialog{
  width: 140px;
  // height: 160px;
  background: #FFFFFF;
  box-shadow: 0px 0px 6px 0px #E5E6EB;
  border-radius: 2px;
  .check-box{
    text-align: left;
    margin: 8px 0;
  }
  .group{
    padding-left: 8px;
    line-height: 28px;
  }
  .group:hover{
    background: rgba(0,122,255,0.05);
  }
}
.set-btn{
  display: flex;
  margin-bottom: 8px;
  justify-content: center;
  .check-btn{
    width: 48px;
    height: 24px;
    line-height: 24px;
    border-radius: 2px;
    border: 1px solid #007AFF;
    background: #fff;
    color: #007AFF;;
  }
  .check-btn.bg{
    background: #007AFF;
    color: #fff;
    margin-left: 8px;
  }
}
._css-table-header-icon-el{
  font-size: 18px;
  color: #888888;
}
</style>
