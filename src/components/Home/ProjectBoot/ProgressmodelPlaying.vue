<template>
    <div class="_css-model-progress">
        <div class="_css-model-close icon-suggested-close" @click="closeDialog"></div>
        <div class="_css-time-line">
           <div v-show="!isProgress">
                <div class="_css-play-time-text" v-if="playsign != 'end' && changeTime ">
                    当前时间：{{changeTime | flt_datetimefromList}}
                </div>
                <div class="_css-time-slider" v-if="!timeResNull">
                    <div class="_css-timeline">
                        <el-slider
                            v-model="timeSlidervalue" 
                            :format-tooltip="timestepToolTip"
                            :max="sliderMax"
                            @change="sliderChange"
                            :marks="marks">
                        </el-slider>
                    </div>
                    <div class="_css-time-detail">
                        <div
                            v-if="playsign == 'end'"
                            class="nowmodel"
                            @click="nowTimeModelPlay">
                            当前进度
                        </div>
                        <div 
                        :class="{'_css-reverse':openSetTimeBtn == true}"
                        class="_css-white-btn _css-modelplay-cfgbtn " @click="mockConfigClick">
                            <i class="icon-interface-set_se"></i>
                            {{getPlayCfgBtnText()}}
                        </div>
                        <div class="_css-playtime-input" v-if="openSetTimeBtn" :class="{'_css-disabled':playsign != 'end'}">
                            <el-input 
                                class="_css-playtime"  
                                maxlength="3"  
                                :disabled="playsign != 'end'"
                                onkeyup="this.value=this.value.replace(/[^\d.]/g,'');"
                                v-model="playTime" 
                                placeholder=""
                            ></el-input>秒
                        </div>
                        <div
                            v-if="playsign == 'end'"
                            class="playmodel"
                            @click="mockprogressclick"
                            :class="{'ban-click':bStartDis}">
                            <i class="icon-interface-play"></i>
                            开始模拟
                        </div>
                        <div
                            v-if="playsign == 'pause'"
                            class="playmodel"
                            @click="mockprogressclick"
                            :class="{'ban-click':false && loadingModelEnd == true}"><i class="icon-interface-play"></i>
                            继续模拟
                        </div>
                        <div
                            v-if="playsign == 'start'"
                            class="pausemodel"
                            @click="pausemodelClick"
                            :class="{'ban-click':false && loadingModelEnd == true}"><i class="icon-interface-time-out"></i>
                            暂停模拟
                        </div>
                        <div
                            v-if="playsign != 'end'"
                            class="stopmodel"
                            @click="stopPlayModel()"
                            :class="{'ban-click':false && loadingModelEnd == true}"><i class="icon-interface-stop"></i>
                            停止模拟
                        </div>
                    </div>
                </div>
                <div class="_css-null" v-if="timeResNull" >当前项目不存在进度关联数据</div>
           </div>
            
        </div>
        
        <div class="model-iframe">
            <div class="_css-legend" v-show="tableTaskShow">
                <ul>
                    <li v-for="(item,index) in legendColor" :key="index" style="color:rgba(255,255,255,.8)">
                        <span class="_css-color" :style="{background:item.color}"></span>
                        <span class="_css-num">{{item.num}}</span>
                    </li>
                </ul>
            </div>
            <div class="_css-legend legend-play" v-show="!tableTaskShow">
                <ul>
                    <li v-for="(item,index) in legendColorPlay" :key="index" style="color:rgba(255,255,255,.8)">
                        <span class="_css-color" :style="{background:item.color}"></span>
                        <span class="_css-num">{{item.num}}</span>
                    </li>
                </ul>
            </div>

           <div :class="tableTaskShow ? '_css-small' : '_css-big'" >
                <iframe 
                width="100%" 
                height="100%" 
                ref="modelRefTime" 
                @load="imageProLoad" 
                :src="modelIframeSrc" 
                frameborder="0"></iframe> 
           </div>
            <transition name="el-zoom-in-bottom">
                <div class="table" style="height:400px;">
                    <ProgressModelTaskTable 
                        v-if="tableTaskShow"
                        :finished="pullAheadList"
                        :inProgress="normalList"
                        :notStarted="hysteresisList"
                        @close="tableTaskShow=false"
                    ></ProgressModelTaskTable>
                </div>
            </transition>
        </div>
        <div class="slider-list-content" v-if="listSetColor.length>0">
            <div class="list-progress" ref="listProgress">
                <div v-for="(item,index) in listSetColor" :key="index" >
                    <p>工程结构：{{item.NAME_}}</p>
                    <p>计划时间： {{item.START_ | flt_datetimefromList}}--{{item.FINISH_ | flt_datetimefromList}}</p>
                    <p>填报时间： {{item.Progress_unittime | flt_datetimefromList}}</p>
                    <p>实际完成比例： {{item.PERCENTCOMPLETE_}}%</p>
                    <p>状态：{{item.Progress_state}}</p>
                    <p>填报人： {{item.Progress_createuser}}</p>
                    <el-divider v-if="index<listSetColor.length-1"></el-divider>
                </div>
            </div>
        </div> 
    </div>
</template>
<script>  

import ProgressModelTaskTable from './ProgressModelTaskTable'

export default {
    data() {
        return { 
            openSetTimeBtn: false, // 判断模拟设置按钮状态是打开还是关闭
            shouldhighlight: [],  // 模拟 正在进行的构件  要取消隔离，取消着色
			finishResetModelColor: [], // 模拟完成的构件  要取消隔离，取消着色
            dateStep: 1, // 每次播放一天为一个单位前进 
            // =============
            timeSlidervalue:  0,
            sliderMax : null,
            marks: {},
            dateMin: null,   // 记录开始时间，最小时间
            dateMax: null,   // 记录结束时间，最大时间
            deteDiffDay: null,   // 记录开始时间和结束时间时间差
            diffStep: 1,  
            changeTime: null,
            timeResNull: false, 
            isProgress: true, 
            listSetColor: [],  // 模拟展示信息
            legendColor: [
                {color: 'rgba(171, 71, 188, 1)', num: '1%-25%'},
                {color: 'rgba(243,152,0, 1)', num: '26%-50%'}, 
                {color: 'rgba(83, 109, 254, 1)', num: '51%-75%'}, 
                {color: 'rgba(0, 172, 193, 1)', num: '76%-99%'},
                {color: 'rgba(67, 160, 71, 1)', num: '100%'},
            ],
            legendColorPlay:  [
                {color: 'rgba(0, 255, 0, 1)', num: '进行中'},
                {color: 'rgba(255, 0, 0, 1)', num: '已完成'},
            ],
            elementArr: [], // 着色的构件和颜色

            playsign: 'end', // 当前的模型模拟状态 
            bStartDis: false, // 刚点击完开始模拟时，立即dis掉，当 playSign 修改为 start 时，立即取消dis.
            loadingModelEnd: true,
            playTimeHandler: 0,  
            setValue: 0,  // 模拟时候记录时间值
            timeAutoData: 1000, // 设置模拟播放时间
            playTime: null,   // 展示播放秒数
            playingBtn: true,
            sliderMinTime: '',  // 播放的最小时间
            sliderMaxTime: '',  // 播放的最大时间
            sliderTimeDiff: 0,
            revalue: 0,
            getModelData_: null,  // 接口返回的属于数据
            allPro_ModelID: [],  // 该值为allPro_ModelID的存值，大屏项目时候该值为全部的allPro_ModelID，平台形象进度进入时候为传入的值
            iframeWindow: null,  // 模型对象

            // 当前进度参数
            tableTaskShow: false, // 表格显示隐藏
            pullAheadList: [],
            normalList: [],
            hysteresisList: [],
        };
    },
    props: {
        modelIframeSrc: {
            type: String,
            required: false
        }, 
        allModelID: {
            type: Array,
            required: false
        }, 
        getModelData: {
            type: Object,
            required: false
        }
    },
    filters: {
        flt_datetimefromserver(str) {
            var str1 = str;
            if (str) {
                str1 = str.replace("T", " ");
            }
            return str1;
        },
        flt_datetimefromList(str) { 
            var str1 = str;
            if (str) {
                str1 = str.substr(0,4) + '年' + str.substr(5,2) + '月' + str.substr(8,2) + '日'
            }
            return str1;
        }, 
    },
    components: {
        ProgressModelTaskTable
    },
    watch: {
        // 当变量 shouldhighlight 变化时，会针对 shouldhighlight 进行高亮
        // ------------------------------------------------------------
        'shouldhighlight': {
        handler(val) {
            let playModelView = this.iframeWindow;
            if(val.length == 0) return
            this.func_setElementColor(playModelView, val, 0, 255, 0, 0.5);
        },
        deep: true
        },
        'finishResetModelColor': {
        handler(val) {
            let playModelView = this.iframeWindow;
            if(val.length == 0) return
            this.func_setElementColor(playModelView, val, 255, 0, 0, 0.5);
        },
        deep: true
        }
    },
    
    mounted() {
        let _this = this;
        _this.allPro_ModelID = _this.allModelID; 
        _this.getModelData_ = _this.getModelData;
        _this.getModelDataFun();
    },
    methods: {
        getPlayCfgBtnText(){
            var _this = this;
            if (_this.openSetTimeBtn == true) {
            return '关闭设置';
            } else {
            return '模拟设置';
            } 
        },
        mockConfigClick(){
            this.openSetTimeBtn = !this.openSetTimeBtn
        },
        getModelDataFun() {
            let _this = this;
            let getModelData = _this.getModelData_;
            let _min = _this.$options.filters["flt_datetimefromserver"](getModelData.start).substr(0, 10);
            let _max = _this.$options.filters["flt_datetimefromserver"](getModelData.end).substr(0, 10);
            let diffDay = _this.$formatData.DateDiff(_min,_max);
            
            _this.dateMin = _min;
            _this.dateMax = _max;
            _this.deteDiffDay = diffDay;
            _this.diffStep = diffDay/10;
            _this.sliderMax = diffDay;
            _this.marks = {
                0: _min.toString(),
                [diffDay]: _max.toString()
            } 
            if(_min == null && _max == null){
                _this.timeResNull = true;
            } 
        }, 
        // 统一处理构件着色
        // ---------------
        func_setElementColor(thewin, eles, r, g, b, a) {
            // 测试此次着色的颜色值
            // ------------------
            var _this = this;
            var colorUtility = _this.$staticmethod.bimhelper_getColorUtility(thewin);
            colorUtility.setElementColor(eles, r, g, b, a,false,false);
        },
        getInFactSeconds(totalFrameCnt, inputSeconds) {
            
            let _this = this
            var inFactSeconds; // 实际总秒数
            var leastDelay = 0.3 * totalFrameCnt;
            var mostDelay = 1 * totalFrameCnt;
            if (inputSeconds && inputSeconds < leastDelay ) {
                _this.$message.warning('已为您自动计算接近输入数值的秒数')
                inFactSeconds = leastDelay ;
            } else if (inputSeconds && inputSeconds > mostDelay ) {
                _this.$message.warning('已为您自动计算接近输入数值的秒数')
                inFactSeconds = mostDelay
            } else {
                inputSeconds? inFactSeconds = inputSeconds : inFactSeconds = mostDelay 
            }
            return inFactSeconds;
        },
        // 删除模型着色
		removeIsolateElement(){
            let _this = this;

            let _allReset = _this.shouldhighlight.concat(_this.finishResetModelColor)
            _allReset =  [...new Set(_allReset)]
            if(_allReset.length == 0) return
            _this.iframeWindow.model.BIM365API.Controller.resetElementColor(_allReset);
            // 这俩记录的是正在模拟时已完成和正在进行的构件，resetColor后要清空，要不然二次resetElementColor构件就没有隔离状态
            _this.shouldhighlight = [];
            _this.finishResetModelColor = [];
		},
        removeNowProgress(){
            if(this.elementArr.length == 0) return
            this.iframeWindow.model.BIM365API.Controller.resetElementColor(this.elementArr);
            this.elementArr = [];
        },
        // 点击当前播放按钮
        nowTimeModelPlay(){
            let _this = this;
            _this.removeIsolateElement();
            _this.timeSlidervalue = 0;
            let nowTime_ = _this.getDateStr(new Date())
            let _nowDiff =  _this.$formatData.DateDiff(_this.dateMin,nowTime_)
            _this.pullAheadList = [];
            _this.normalList = [];
            _this.hysteresisList = [];
            // 判断当前日期是否在计划时间内
            if(!_this.$formatData.isDateBetween(nowTime_,_this.dateMin,_this.dateMax)) {
                _this.$message.error('当前日期不在计划范围')
                return
            }
             
            _this.marks = {
                0: _this.dateMin.toString(),
                [_nowDiff]: '当前时间：' + nowTime_,
                [_this.deteDiffDay]: _this.dateMax.toString()
            }
            // 对接口返回的数据进行处理，判断当前日期下，超前，滞后，未开始的进行分类，赋值
            _this.getNowTableData();

            _this.tableTaskShow = !_this.tableTaskShow;
            
        },
        getNowTableData(){
            let _this = this;
            let _arrList = []; 
            let nowElementColorArr = [];  // 保存当前进度下着色的构件，在点击播放时候，要删除模型颜色
            _this.getModelData_.List.forEach((list)=>{
                if(list.Elements.length > 0 && list.Task.Progress_state != null){
                    _arrList = list.Elements;
                    let listElements = JSON.parse(_arrList[0].bme_elementids);
                    let arr = _this.getElement(listElements);
                    nowElementColorArr.push.apply(nowElementColorArr,arr); 
                    _this.setColorBIM(arr,list.Task.PERCENTCOMPLETE_)
                    if(list.Task.Progress_state == "超前"){
                        _this.pullAheadList.push(list);
                    }else if(list.Task.Progress_state == "正常"){
                        _this.normalList.push(list);
                    }else if(list.Task.Progress_state == "滞后"){
                        _this.hysteresisList.push(list);
                    }
                }
            })
            _this.elementArr = nowElementColorArr;
        },
        getElementAddColor(listElements,color) {
            let arr = [];
            let _ele_=[];
            listElements.forEach(item => {
                item.elementids.forEach(ids => {
                    arr.push(`${item.modelid}^${ids}`);
                    _ele_.push({ele:arr,color:item.color})
                });
            }); 
            return arr;
        },
        setColorBIM(eles,num) {
            var _this = this;
            var colorUtility = _this.$staticmethod.bimhelper_getColorUtility(this.iframeWindow); 
            // 着色是根据不同的完成百分比，来确定当前构件的颜色，将构件循环着色
            let r,g,b,a; 
            if(num > 0 && num<=25){
                r = 171;
                g=71;
                b=188;
                a=1;
            }else if(num>25  && num<=50){
                r = 243;
                g = 152;
                b = 0,
                a = 1
            }else if(num>50  && num<=75){
                r = 83;
                g = 209;
                b = 254,
                a = 1
            }else if(num>75 && num < 100){
                r = 0;
                g = 172;
                b = 193,
                a = 1
            }else{
                r = 67;
                g = 160;
                b = 71,
                a = 1
            } 
            colorUtility.setElementColor(eles,r,g,b,a,false,false)
            
        },
        
        DateDiff(sDate1, sDate2) {
            var aDate, oDate1, oDate2, iDays;
            oDate1 = new Date(sDate1).getTime();
            oDate2 = new Date(sDate2).getTime();
            iDays = parseInt((oDate1 - oDate2) / 1000 / 60 / 60 / 24) //把相差的毫秒数转换为天数   
            return  iDays;
        }, 
        closeDialog() {
            let _this = this;
            this.listSetColor = []; 
            _this.playingBtn = true; 
            _this.timeSlidervalue = 0;
            _this.setValue = 0;
            clearInterval(_this.playTimeHandler);
            _this.playsign = 'end';
            this.$emit('close')
        },
        // 时间轴
        timestepToolTip(value){ 
             
            let date = new Date(this.dateMin);
            date.setDate(date.getDate() + value);
            var datestrTool = this.$formatData.dateInit(date);
            return datestrTool;
        },

        // 时间轴change，每一帧都会走这里
        sliderChange(value) { 
            if(value > this.sliderMax){
                return;
            } 
            this.removeIsolateElement();
            this.removeNowProgress();
            this.setValue = value;
            this.timeSlidervalue = value; 
            
            // 计算开始时间和结束时间的时间差，得到总的播放时间
            this.getListTimePoint(value);
        },
        getDateStr(dt) {
            let year = dt.getFullYear();
            let month = dt.getMonth() + 1;
            let dat = dt.getDate();
            month = month.toString().padStart(2, "0");
            dat = dat.toString().padStart(2, "0");
            let finalStr = `${year}-${month}-${dat}`;
            return finalStr;
        }, 
        // 根据时间获取所有模型ID着色
        getListTimePoint(inputVal){
            let _this = this; 
            let _n = new Date(_this.getModelData_.start)
            let currentDate = new Date(_n);

            currentDate.setDate(
                _n.getDate() + inputVal
            );

            _this.listSetColor = [];
            this.changeTime = this.getDateStr(currentDate);
            let _arrList = [];  // 下面循环中记录了elements
            let foringPushArr = [];  // 用来储存当前循环中所有进行中的高亮的构件,
            let noArr = [];    // 当前不在时间范围的构件

            _this.getModelData_.List.forEach((list)=>{
                _arrList = list.Elements;
                let currentenddate = new Date(_this.changeTime);
                if(_arrList.length > 0){
                    let listElements = JSON.parse(_arrList[0].bme_elementids);
                    let arr = _this.getElement(listElements);
                    if(_this.$formatData.checkTime(_this.changeTime,currentenddate,list.task_Start,list.task_FINISH)){
                        foringPushArr.push.apply(foringPushArr,arr);   // foringPushArr为所有正在进行的数组，arr是当前循环中所有的构件，两个数组合并，得到当前着色的构件
                        _this.shouldhighlight = foringPushArr;
                        _this.listSetColor.push(list.Task);
                        
                        _this.listSetColor = Array.from(new Set(_this.listSetColor)); 
                    }
                    else{
                        let dateDiff = _this.DateDiff(_this.changeTime,list.task_FINISH);
                        // 不在时间范围内 并且在当前播放时间之前的是已经完成的构件，给它着色为已完成的颜色（目前是红色）
                        if(dateDiff >= 0){
                            noArr.push.apply(noArr,arr);
                            _this.finishResetModelColor = noArr;
                        }
                    } 
                }
            }) 
 
        }, 
        getElement(listElements) {
            let arr = [];
            listElements.forEach(item => {
                item.elementids.forEach(ids => {
                arr.push(`${item.modelid}^${ids}`);
                });
            });
            return arr;
        },
        // iframe 加载完毕后
        imageProLoad() {
            
            let _this  = this;
            _this.iframeWindow = _this.$refs.modelRefTime.contentWindow;
            
            _this.iframeWindow.model.BIM365API.Events.finishRender.on('default',()=>{
                _this.isProgress = false;
                _this.iframeWindow.model.BIM365API.Controller.isolateElementByElementId()
                _this.sliderChange(0);  //  调用：初始化时候要显示第一天的数据
            })
        },
        // 开始模拟，继续模拟
        mockprogressclick() {
            let _this = this;
            _this.tableTaskShow = false;  // 点击开始模拟，隐藏当前进度的table
             
            let totalFrameCnt = _this.sliderMax;
            let playTimerAuto = _this.getInFactSeconds(totalFrameCnt, this.playTime) * 1000;
            let timeauto = parseInt(playTimerAuto/totalFrameCnt);
            _this.playTime = parseInt(_this.getInFactSeconds(totalFrameCnt, this.playTime)); 
            _this.marks = {
                0: _this.dateMin.toString(),
                [_this.deteDiffDay]: _this.dateMax.toString()
            }
            if(_this.timeSlidervalue == _this.sliderMax){
                _this.timeSlidervalue = 0;
                _this.setValue = 0;
                
                _this.sliderChange(0);
                _this.timestepToolTip(0);
            }
            
            _this.playTimeHandler = setTimeout(()=>{
                _this.frameExecuteUnit(timeauto,_this.setValue);
            }, timeauto);
            _this.playingBtn = false;
            _this.playsign = 'start'; 
        },
        frameExecuteUnit(timeauto,val){
            let _this = this;
            _this.revalue = val;
            if(val > _this.sliderMax){  
                _this.timeSlidervalue = _this.sliderMax;
                clearInterval(_this.playTimeHandler);
                _this.playsign = 'end';
                _this.setValue = _this.sliderMax;
                _this.sliderChange(_this.sliderMax);
                _this.timestepToolTip(_this.sliderMax);
                return
            }
            
            
            _this.playTimeHandler = setTimeout(()=>{
                _this.timeSlidervalue += _this.dateStep;
                _this.sliderChange( _this.timeSlidervalue);
                _this.timestepToolTip( _this.timeSlidervalue);
                _this.frameExecuteUnit(timeauto, _this.timeSlidervalue);
            }, timeauto);
        },
        // 暂停模拟
        pausemodelClick() {
            let _this = this;
            _this.setValue = _this.revalue;
            _this.playingBtn = true;
            _this.playsign = 'pause';
            clearInterval(_this.playTimeHandler);
        },
        // 停止模拟
        stopPlayModel() {
            let _this = this;
            _this.removeIsolateElement();
            _this.tableTaskShow = false;  // 点击开始模拟，隐藏当前进度的table
            _this.playingBtn = true;
            _this.marks = {
                0: _this.dateMin.toString(),
                [_this.deteDiffDay]: _this.dateMax.toString()
            }

            // 停止播放
            _this.shouldhighlight = [];
            _this.finishResetModelColor = [];
            _this.sliderChange(0);
            _this.timestepToolTip(0);
            _this.timeSlidervalue = 0;
            _this.setValue = 0;
            clearInterval(_this.playTimeHandler);
            _this.playsign = 'end';
        }, 

    },
    beforeDestroy() {
        
        clearInterval(this.playTimeHandler);
    },
    destroyed() {
        clearInterval(this.playTimeHandler);
    }


}
</script>
<style scoped>
._css-model-progress{
    width: 100%;
    height: 100%;
    position: fixed;
    z-index: 1000;
    top:0;
    left: 0;
    /* background: #fff; */
    background: #f7f7f7;
}
._css-model-progress-big{
    position: absolute;
    z-index: 1000;
    top:20px;
    left: 20px;
    right: 20px;
    bottom: 20px;
    pointer-events: none;
}
._css-model-close{
    position: fixed;
    right: 10px;
    top: 10px;
    z-index:1000;
}
._css-model-close:hover{
    cursor: pointer;
}
._css-time-line{
    pointer-events: all;
    height: 50px;
    padding: 45px 2% 15px 3%;
}
.model-iframe{
    width: 100%;
    height: calc(100% - 100px);
}

._css-time-slider{
    /* margin: 60px 2% 0 3%; */
    display: flex;
}

._css-time-slider /deep/ .el-slider__marks-text{
    white-space: nowrap;
}
._css-null{
    margin: 0 5%;
    text-align: left;
}
._css-time-detail{
    /* width: 150px; */
    display: flex;
    text-align: right;
    line-height: 40px;
    font-size: 14px;
    font-weight: 500;
    margin-left: 35px;
    
}
._css-time-detail:hover{
    cursor: pointer;
}
._css-time-detail div{
    padding: 0 8px;
    height: 32px;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
    -ms-flex-direction: row;
    flex-direction: row;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    border-radius: 4px;
    border: 1px solid #1890ff;
    /* color: #1890ff; */
    /* background: #f7f7f7; */
    margin-left: 16px;
    /* opacity: .7; */
    cursor: pointer;
}

._css-time-detail div.pausemodel:hover{
    opacity:1;
}
._css-time-detail div.pausemodel{
  background: #FAAD14;
  color: #fff;
  border-color: #FAAD14;
   opacity:.7;
}
._css-time-detail div.stopmodel{
  background: #F5222D;
  color: #fff;
  border-color: #F5222D;
   opacity:.7;
}
._css-time-detail div.stopmodel:hover{
    opacity:1;
}
._css-time-detail div.playmodel{
  background: #1DA48C;
  color: #fff;
  border-color: #1DA48C;
   opacity:.7;
}
._css-time-detail div.playmodel:hover{
    opacity:1;
}
._css-time-detail div.nowmodel{
    background: #d5c740;
    border-color: #d5c740;
    color: #fff;
}
._css-time-detail div.nowDismodel{
    background: rgb(224, 240, 6,0.5);
}
._css-timeline{
    flex:1;
}
.content-list p{
    line-height: 30px;
}
.content-list{
    height: 200px;
    overflow-y: auto;
}
.content-list /deep/ .el-divider--horizontal{
    margin: 10px 0;    
}
._css-legend{
    position: fixed;
    bottom: 420px;
    right: 2%;
    text-align: left;
}
._css-legend.legend-play{
    bottom: 10px;
}
 
._css-color{
    display: inline-block;
    width: 20px;
    height: 8px;
    border-radius: 1px;
    margin-right: 5px;
}

.slider-list-content{
    position: fixed;
    top: 35%;
    left: 2%;
    text-align: left; 
    min-width:300px;
    max-height: 500px;
    max-width: 600px;
    background: rgba(0, 0, 0, 0.5);
    border-radius: 10px;
    color: #fff;
    padding: 12px;
}
.slider-list-content-big{
    position: absolute;
    top: 35%;
    left: 3%;
    text-align: left; 
    min-width:300px;
    max-height: 500px;
    max-width: 600px;
    background: rgba(0, 0, 0, 0.5);
    border-radius: 10px;
    color: #fff;
    padding: 12px;
    pointer-events: all;
}
.list-progress{
    max-height: 500px;
    overflow: auto;
}
.slider-list-content p,.slider-list-content-big p {
    line-height: 24px;
}
.model-loading{
   width: 250px;
   height: 100px;
   overflow: hidden;
}
div.ban-click{
  background: rgba(0, 0, 0, .25);
  color:#fff;
  cursor: not-allowed;
  border: 1px solid transparent;
}
._css-play-time-text{
    position: absolute;
    left: 3%;
    top: 20px;
    width: 215px;
    height: 34px;
    background: rgba(0,0,0,0.65);
    color: #fff;
    line-height: 34px;
    border-radius: 6px;
    font-size: 15px;
}
._css-white-btn._css-reverse {
  background-color: rgba(24, 144, 255, 1);
  color:#fff;
  opacity: 0.7;
}

._css-white-btn._css-reverse:hover {
  opacity: 1;
  background-color: rgba(24, 144, 255, 0.9)
}
._css-white-btn {
  color:#1890FF;
}
._css-white-btn:hover{
  background-color: rgba(24, 144, 255, 0.1);
}
._css-playtime-input /deep/ .el-input.is-disabled .el-input__inner{
    background:transparent
}
._css-time-slider ._css-playtime-input{
    border-color: transparent;
    padding: 0;
    margin: 0;
}
._css-time-slider ._css-playtime {
    width: 30px;
    border-color: #a4a3a3;
    margin-right: 8px;
}

._css-small{
    width: 100%;
    height: calc(100% - 400px)
}
._css-big{
    width: 100%;
    height: 100%
}
</style>