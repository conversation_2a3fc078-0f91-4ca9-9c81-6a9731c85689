<template>
	<div class="dialog">
		<!-- <div class="_css-dialog-wp-title">
			<span>选择模型</span>
			<i
				class="_css-dialog-wp-title-close icon-interface-guanbimoxing"
				@click.stop="closerSelectModel"
			></i>
		</div> -->
		
		<div class="dialog-center">
			<div class="left-list">
				<p class="list-name">项目阶段</p>
				<!-- <ul>
					<li
						v-for="stage in projectStage"
						:key="stage.BusinessCode"
						:class="{ active: selectStageID == stage.BusinessCode }"
						@click.stop="stageClick(stage.BusinessCode)"
					>
						{{ stage.MenuName }}
					</li>
				</ul> -->
				<el-tree
                  class="el-tree-cus"
                  highlight-current
                  node-key="BusinessCode"
                  ref="elTree"
                  empty-text="暂无子级"
                  :default-expand-all="false"
                  :expand-on-click-node="true"
                  :auto-expand-parent="true"
                  :default-expanded-keys="treeExpandedKeys"
                  :data="treeData"
                  :props="elTreeProps"
                  @node-click="stageClick">
                    <div class="el-tree-node-cus" slot-scope="{ node }">
                      <el-tooltip effect="dark" :content="node.label" placement="top" :enterable="false">
                        <div class="label">{{ node.label }}</div>
                      </el-tooltip>
                    </div>
                </el-tree>
			</div>
			<div class="right-list">
				<ul>
					<li
						v-for="model in currentModelList"
						:key="model.featureID"
						:class="{ active: includesBool(model.featureID)}"
						@click.stop="modelListClick(model)"
					>
						<img class="img-model-list" :src="getItemImg(model)" alt="">

						<div>
							<p>{{ model.featureName }}</p>
							<span>更新时间：{{ model.createTime.split("T")[0] }}</span>
						</div>
						<i
							class="icon-checkbox-Selected-Disabled-dis-blue"
							v-show="includesBool(model.featureID)"
						></i>
					</li>
				</ul>
				 
			</div>
		</div>
		<!-- <div class="btn-wp">
			<div @click.stop="openModel">确定</div>
			<div class="_css-notdef _css-marginr0" @click.stop="_closerelationmodel">
				取消
			</div>
		</div> -->
	</div>
</template>
<script>
export default {
	name: "CompModelSelect",
	data() {
		return {
			VaultID: "", // 项目ID
			token: "",
			projectStage: [],
			selectModelID: "",
			selectStageID: "",
      		selectModelName: "",
			currentModelList: [],
			SelectId: [],  // 已选列表
			treeData: [],
			elTreeProps: {
				children: 'Children',
				label: 'MenuName'
			},
			treeKey:"",
			treeExpandedKeys:[],  // 默认展开节点,当前选中的节点

		};
	},
	created() {
		this.VaultID = this.$route.params.organizeId;
		this.token = this.$route.params.Token || this.$staticmethod.Get("Token");
	},
	mounted() {
		let _this = this
		this.getMenuTree()
		// _this.getModelPhaseList();
		window.addEventListener("message", function (data) {
			if(data.data.act == 'cc_selected'){
				console.log(data,'=cc')
				_this.Btn_OK();
			}
		});
	},
	watch: {},
	methods: {
		getMenuTree(){
			const loading = this.$loading({
				lock: true,
				text: 'Loading',
				spinner: 'el-icon-loading',
				background: 'rgba(255, 255, 255, 0.7)'
			});
			this.$axios
				.get(`${this.$urlPool.GetUserMenuTree}?token=${this.token}&organizeId=${this.VaultID}&parentId=0`)
				.then(res=>{
					loading.close();
					if(res.data.Ret == 1) {
						let modelTree = res.data.Data.find(index=>index.MenuCode == 'MODEL');
						let newArr = modelTree.Children.filter(item => item.BusinessCode !== 'allmodel_phase');
						this.treeData = newArr
						this.stageClick(newArr[0])
						this.highlightTree(newArr[0].BusinessCode)
					}else{
						this.$message.error(res.data.Msg);
					}
				})
				.catch(err=>{})
		},
		highlightTree(UrlPhase) {
			if(UrlPhase && UrlPhase !== 'allmodel_phases') {
				const treeKey = UrlPhase
				this.$nextTick(() => {
					this.$refs.elTree.setCurrentKey(treeKey)
				})
			}
		},
		Btn_OK(){
			let _data = {
				act: "selected_to_cc",
				data: this.SelectId
			};
			console.log(_data,'=to-cc')

			window.parent.postMessage(_data, "*");
		},
		// 获取项目阶段
		getModelPhaseList() {
            let _token = this.$staticmethod.Get("Token");
			this.$axios
				.get(
					`${this.$urlPool.GetModelMenu}?organizeId=${this.VaultID}&token=${_token}`
				)
				.then((res) => {
					if (res.data.Ret == 1) {
						let _data = res.data.Data;
						this.projectStage = _data;
						this.selectStageID = this.projectStage[0].BusinessCode;
						this.getModelList(this.selectStageID);
					} else {
						this.$staticmethod.debug(x);
					}
				})
				.catch((err) => {});
		},
		// 获取当前阶段下模型list
		getModelList(selectStageID) {
			const loading = this.$loading({
				lock: true,
				text: 'Loading',
				spinner: 'el-icon-loading',
				background: 'rgba(255, 255, 255, 0.7)'
			});
			
			this.$axios
				.get(
					`${this.$ip("newModelHttpUrl")}/Vault/GetFeaturesByPhase?VaultID=${
						this.VaultID
					}&Phase=${selectStageID}`
				)
				.then((res) => {
					if (res.status === 200) {
						this.currentModelList = res.data;
						this.selectModelID = res.data[0].featureID;
					}
					loading.close();
				})
				.catch((err) => {
					loading.close();
				});
		},
		// 关闭当前对话框
		closerSelectModel() {},
		stageClick(phase) {
			let _this = this;
			this.SelectId = []
			// this.selectStageID = phase;
			_this.$axios
				.get(
					`${this.$ip("newModelHttpUrl")}/Vault/GetFeaturesByPhase?VaultID=${
						_this.VaultID
					}&Phase=${phase.BusinessCode}`
				)
				.then((res) => {
					if (res.status === 200) {
						this.currentModelList = res.data;
					}
				})
				.catch((err) => {});
		},
		includesBool(featureID){
			let bool = false;
			this.SelectId.findIndex(v=>v.No == featureID) >= 0 ? bool = true : bool=false
			return bool;
		},
		modelListClick(model) {
			if (this.SelectId.findIndex(i=>i.No == model.featureID) == -1 ) {
				this.SelectId.push({
					No: model.featureID,
					Name: model.featureName,
				}) // 判断已选列表中是否存在该id，不是则追加进去
			} else {
				let index = this.SelectId.findIndex(v=>v.No == model.featureID);//indexOf(model.featureID) // 求出当前id的所在位置
				this.SelectId.splice(index, 1) // 否则则删除
			}
		},
		openModel() {
			// 选中的模型
			console.log(this.SelectId,'=====oooo');
		},
		getItemImg (item) {
			if (item.thumbnail === '') {
				return require('../../../assets/images/default.jpg')
			} else {
				return `data:image/png;base64,${item.thumbnail}`
			}
		},
	
	},
};
</script>
<style lang="stylus" scoped rel="stylesheet/stylus">
.dialog .model-iframe{
	height: 100% !important;
}
.dialog {
	width: 100%;
	height: 100%;
	background: #ffffff;
	box-shadow: 0px 13px 24px -17px rgba(11, 41, 62, 0.8);
	border-radius: 4px;
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	// margin: auto;
	display: flex;
	flex-direction: column;

	.dialog-center {
		flex: 1;
		padding: 0 24px 0;
		display: flex;
		position: relative;

		.list-name {
			padding-top: 10px;
		}

		.left-list {
			max-height: 455px;
			overflow: auto;
			width: 300px;

			li {
				width: 200px;
				height: 40px;
				line-height: 40px;
				padding: 0 12px 0 28px;
				text-align: left;
				display: flex;
				justify-content: space-between;
				color: rgba(0, 0, 0, 0.65);
				border-radius: 4px;

				p {
					overflow: hidden;
					text-overflow: ellipsis;
					white-space: nowrap;
					width: 130px;
					display: inline-block;
				}

				&:hover, &.active {
					background: rgba(24, 144, 255, 0.1);
					color: #1890FF;
					cursor: pointer;

					span {
						color: #1890FF;
					}
				}

				span {
					color: rgba(0, 0, 0, 0.25);
				}
			}

			p {
				display: flex;
				align-items: center;
				font-size: 16px;
				text-align: left;
				margin: 0;
				margin-bottom: 15px;

				i {
					margin-right: 8px;
				}
			}
		}

		.right-list {
			flex: 1;
			max-height: 455px;
			border-radius: 2px;
			overflow: auto;
			margin-top: 20px;
			li {
				padding: 12px;
				display: flex;
				flex-direction: row;
				border-radius: 2px;
				position: relative;

				&:hover, &.active {
					background: rgba(0, 0, 0, 0.04);
					cursor: pointer;
				}

				i {
					position: absolute;
					top: 0;
					bottom: 0;
					right: 12px;
					margin: auto;
					line-height: 114px;
				}

				img {
					width: 120px;
					height: 90px;
					border-radius: 4px;
				}

				div {
					flex: 1;
					margin-left: 8px;
					display: flex;
					flex-direction: column;
					justify-content: space-between;

					p {
						font-size: 14px;
						color: rgba(0, 0, 0, 0.65);
						width: 190px;
						overflow: hidden;
						text-overflow: ellipsis;
						display: -webkit-box;
						-webkit-line-clamp: 2;
						-webkit-box-orient: vertical;
						text-align: left;
						font-weight: 400;
					}

					span {
						font-size: 12px;
						font-weight: 400;
						color: rgba(0, 0, 0, 0.25);
						height: 20px;
						line-height: 20px;
						text-align: left;
						display: inline-block;
					}
				}
			}
		}
	}

	.btn-wp {
		height: 64px;
		border-top: 1px solid rgba(0, 0, 0, 0.09);

		div {
			height: 40px;
			text-align: center;
			line-height: 40px;
			font-size: 14px;
			cursor: pointer;
			background: #1890FF;
			color: #fff;
			float: right;
			margin: 12px 24px;
			width: 120px;
			border-radius: 4px;

			&:hover {
				background: #49B5FF;
			}

			&._css-notdef {
				background: rgba(0, 0, 0, 0.25);

				&:hover {
					background: rgba(0, 0, 0, 0.35);
				}
			}
		}
	}
}
.el-tree-cus{
    /deep/ .el-tree-node {
          .el-tree-node__content {
            display: flex;
            justify-content: flex-start;
            align-items: center;
            padding-top: 8px;
            padding-bottom: 8px;
          }
    }
  }
</style>