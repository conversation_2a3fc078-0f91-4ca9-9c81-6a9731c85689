<template>
    <div class="_css-imgpro-all" v-loading="axiosLoading" element-loading-text="模拟数据加载中...">
 
        <!-- 修改进度填报对话框 -->
        <zdialog-function
        :init_title="'修改进度填报'"
        :init_zindex="1003"
        :init_innerWidth="450"
        :init_width="450"
        init_closebtniconfontclass="icon-suggested-close"
        :init_usecustomtitlearea="false"
        @onclose="status_showedit = false"
        v-if="status_showedit"
        >
            <div slot="mainslot" class="_css-addingnameinput-ctn"
            @mousedown="_stopPropagation($event)"
            >
                <div class="_css-line _css-line-name">
                    <div class="_css-title _css-title-flowname ">
                        工程任务：
                    </div>
                    <div v-if="m_edit_selectednode_name.length <= 19" class="_css-fieldvalue _css-fieldvaluename _css-selectednode overflow-point">
                        {{m_edit_selectednode_name}}
                    </div>
                    <el-tooltip v-if="m_edit_selectednode_name.length > 19" class="item" effect="dark" :content="m_edit_selectednode_name" placement="top-start">
                        <div class="_css-fieldvalue _css-fieldvaluename _css-selectednode overflow-point">
                            {{m_edit_selectednode_name}}
                        </div>
                    </el-tooltip>
                </div>
                <div class="_css-line _css-line-name">
                    <div class="_css-title _css-title-flowname">
                        计划日期：
                    </div>
                    <div class="_css-fieldvalue _css-fieldvaluename">
                    <el-date-picker
                            v-model="m_edit_DataTime"
                            type="daterange"
                            readonly
                            range-separator="至"
                            start-placeholder="开始日期"
                            end-placeholder="结束日期">
                        </el-date-picker>
                    </div>
                </div>
                <div class="_css-line _css-line-name">
                    <div class="_css-title _css-title-flowname">
                        填报日期：
                    </div>
                    <div class="_css-fieldvalue _css-fieldvaluename">
                        <el-date-picker
                        v-model="m_editingdatetime"
                        type="date"
                        @change="getEditAddingTimeChange"
                        placeholder="选择填报日期">
                        </el-date-picker>
                    </div>
                </div> 
                <div class="_css-line _css-line-name">
                    <div class="_css-title _css-title-flowname">
                        实际开始时间：
                    </div>
                    <div class="_css-fieldvalue _css-fieldvaluename">
                        <el-date-picker
                        v-model="m_editingActualStartT"
                        type="date"
                        readonly
                        disabled
                        placeholder="选择实际开始时间">
                        </el-date-picker>
                    </div>
                </div> 
                <div class="_css-line _css-line-name">
                    <div class="_css-title _css-title-flowname">
                        实际结束时间：
                    </div>
                    <div class="_css-fieldvalue _css-fieldvaluename">
                        <el-date-picker
                        v-model="m_editingActualEndT"
                        type="date"
                        disabled
                        readonly
                        placeholder="选择实际结束时间">
                        </el-date-picker>
                    </div>
                </div>
                <div class="_css-line _css-line-name display-none" >
                    <div class="_css-title _css-title-flowname">
                        计划完成比例：
                    </div>
                    <div class="_css-fieldvalue _css-fieldvaluename _css-percentarea _css-plan-percent">
                        <el-input v-model="m_editpercentStr" readonly placeholder="请输入内容"></el-input>
                    </div>
                </div>
                <div class="_css-line _css-line-name display-none">
                    <div class="_css-title _css-title-flowname">
                        实际完成比例：
                    </div>
                    <div class="_css-fieldvalue _css-fieldvaluename _css-percentarea">
                        <el-input-number
                            size="small"
                            v-model="m_editingpercent" 
                            :precision="2"
                            controls-position="right"
                            :min="1" 
                            :max="100">
                        </el-input-number>
    
                        <div class="_css-percentshow">%</div>
                    </div>
                </div>
                <div class="_css-line _css-line-name">
                    <div class="_css-title _css-title-flowname">
                        计划完成进度：
                    </div>
                    <div class="_css-fieldvalue _css-fieldvaluename _css-percentarea">
                        <div class="unit-number">
                            <el-input
                                type="number"
                                v-model="m_edit_planvalue" 
                                controls-position="right"
                                readonly
                            >
                            </el-input>
                            <div class="_css-percentshow">{{ m_add_unitValue }}</div>
                        </div>
                    </div>
                </div>
                <div class="_css-line _css-line-name">
                    <div class="_css-title _css-title-flowname">
                        实际完成进度：
                    </div>
                    <div class="_css-fieldvalue _css-fieldvaluename _css-percentarea" :class="{'bg-f5f7fa':m_editingActualEndT && m_editingActualEndT.length > 0}">
                        <el-input-number
                        :disabled='m_editingActualEndT && m_editingActualEndT.length > 0'
                        size="small"
                        v-model="m_edit_actualvalue" 
                        :precision="2"                     
                        controls-position="right"
                        @change="evt_editingpercentchange" 
                        >
                        </el-input-number>
                        <div class="_css-percentshow">{{ m_add_unitValue }}</div>
                    </div>
                </div>
                <div class="_css-line _css-line-name">
                    <div class="_css-title _css-title-flowname">
                        状态：                   
                    </div>
                    <div class="_css-fieldvalue _css-fieldvaluename _css-percentarea">
                    <div class="_css-percentshow">{{m_editState}}</div>
                    </div>
                </div>
            </div>
    
            <div slot="buttonslot" class="_css-flowAddBtnCtn" >
                <zbutton-function
                    :init_text="'保存'"
                    :init_fontsize="14"
                    :debugmode="true"
                    :init_height="'40px'"
                    :init_width="'120px'"
                    @onclick="func_saveedit"
                    >
                </zbutton-function>
                <zbutton-function
                    :init_text="'取消'"
                    :init_color="'rgba(24, 144, 255)'"
                    :init_bgcolor="'#fff'"
                    :init_fontsize="14"
                    :debugmode="true"
                    :init_height="'40px'"
                    :init_width="'120px'"
                    @onclick="func_canceledititem"
                    >
                </zbutton-function>
            </div>
            
        </zdialog-function>
        <!-- //修改进度填报对话框 -->
 
        <!-- 新进度填报对话框 -->
        <zdialog-function
        :init_title="'进度填报'"
        :init_zindex="1003"
        :init_innerWidth="450"
        :init_width="450"
        init_closebtniconfontclass="icon-suggested-close"
        :init_usecustomtitlearea="false"
        @onclose="status_showadd = false"
        v-if="status_showadd"
        >
            <div slot="mainslot" class="_css-addingnameinput-ctn"
            @mousedown="_stopPropagation($event)"
            >
                <div class="_css-line _css-line-name">
                    <div class="_css-title _css-title-flowname ">
                        进度展示方式：
                    </div>
                    <div class="_css-fieldvalue _css-fieldvaluename _css-selectednode">
                        <el-select 
                            v-model="proDisplayModeValue" 
                            @change="proDisplayModeChange" 
                            :disabled="unitSetBool"
                            placeholder="请选择展示方式">
                            <el-option
                            v-for="item in proDisplayModeoptions"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value">
                            </el-option>
                        </el-select>
                    </div>
                </div>
                <div class="_css-line _css-line-name" v-if="m_add_unitText !== '百分比'">
                    <div class="_css-title _css-title-flowname ">
                        任务{{m_add_unitText == '百分比' ? '总量' : m_add_unitText}}：
                    </div>
                    <div class="_css-fieldvalue _css-fieldvaluename _css-selectednode">
                        <div class="unit-number" v-if="m_add_unitText=='百分比' || m_add_unitText=='总量'">
                            <el-input
                                type="number"
                                placeholder="请输入百分比"
                                :readonly="unitSetBool"
                                size="small"
                                v-model="m_add_setunitValuePer" 
                            >
                            </el-input>
                            <div class="_css-percentshow">{{ m_add_unitValue }}</div>
                        </div>
                        <div class="unit-number" v-if="m_add_unitText=='里程'">
                            <el-input
                                type="number"
                                placeholder="请输入里程"
                                :readonly="unitSetBool"
                                size="small"
                                v-model="m_add_setunitValueLC" 
                                @change="m_add_setunitValueLCChange"
                            >
                            </el-input>
                            <div class="_css-percentshow">{{ m_add_unitValue }}</div>
                        </div>
                        <div class="unit-number" v-if="m_add_unitText=='高程'">
                            底部高程：
                            <el-input
                                type="number"
                                placeholder="请输入底部高程"
                                :readonly="unitSetBool"
                                size="small"
                                v-model="m_add_setunitValueDi" 
                                @change="m_add_setunitValueDiChange"
                            >
                            </el-input>
                            顶部高程：
                            <el-input
                                type="number"
                                placeholder="请输入顶部高程"
                                size="small"
                                :readonly="unitSetBool"
                                v-model="m_add_setunitValueDing" 
                                @change="m_add_setunitValueDingChange"
                            >
                            </el-input>
                        </div>
                    </div>
                </div>
                <div class="_css-line _css-line-name">
                    <div class="_css-title _css-title-flowname ">
                        工程任务：
                    </div>
                    <div v-if="m_addingselectednode.Name.length <= 19" class="_css-fieldvalue _css-fieldvaluename _css-selectednode overflow-point">
                        {{m_addingselectednode.Name}}
                    </div>
                    <el-tooltip v-if="m_addingselectednode.Name.length > 19" class="item" effect="dark" :content="m_addingselectednode.Name" placement="top-start">
                        <div class="_css-fieldvalue _css-fieldvaluename _css-selectednode overflow-point">
                            {{m_addingselectednode.Name}}
                        </div>
                    </el-tooltip>
                </div>
                <div class="_css-line _css-line-name">
                    <div class="_css-title _css-title-flowname">
                        计划日期：
                    </div>
                    <div class="_css-fieldvalue _css-fieldvaluename">
                    <el-date-picker
                            v-model="m_planDataTime"
                            type="daterange"
                            readonly
                            range-separator="至"
                            @change="getPlanTimeChange"
                            start-placeholder="开始日期"
                            end-placeholder="结束日期">
                        </el-date-picker>
                    </div>
                </div>
                <div class="_css-line _css-line-name">
                    <div class="_css-title _css-title-flowname">
                        填报日期：
                    </div>
                    <div class="_css-fieldvalue _css-fieldvaluename">
                        <el-date-picker
                        v-model="m_addingdatetime"
                        type="date"
                        @change="getAddingTimeChange"
                        placeholder="选择填报日期">
                        </el-date-picker> 
                    </div>
                </div>
                <div class="_css-line _css-line-name">
                    <div class="_css-title _css-title-flowname">
                    实际开始时间：
                    </div>
                    <div class="_css-fieldvalue _css-fieldvaluename">
                        <el-date-picker
                        v-model="m_addingActualStartT"
                        type="date"
                        :readonly="addingActualStartTReadonly"
                        :disabled="addingActualStartTReadonly"
                        @change="getAddingActualStartTimeChange"
                        placeholder="选择实际开始日期">
                        </el-date-picker> 
                    </div>
                </div>
                <div class="_css-line _css-line-name">
                    <div class="_css-title _css-title-flowname">
                    实际结束时间：
                    </div>
                    <div class="_css-fieldvalue _css-fieldvaluename">
                        <el-date-picker
                        v-model="m_addingActualEndT"
                        type="date"
                        @change="getAddingActualEndTimeChange"
                        :disabled="addingActualEndTReadonly"
                        :readonly="addingActualEndTReadonly"
                        placeholder="选择实际结束日期">
                        </el-date-picker> 
                    </div>
                </div>
                <!-- 计划完成比例：实际完成比例   隐藏 需要计算 -->
                <div class="_css-line _css-line-name display-none" >
                    <div class="_css-title _css-title-flowname">
                        计划完成比例：
                    </div>
                    <div class="_css-fieldvalue _css-fieldvaluename _css-percentarea _css-plan-percent">
                        <el-input v-model="m_planpercentStr" readonly placeholder="请输入内容"></el-input>
                    </div>
                </div> 
                <div class="_css-line _css-line-name display-none">
                    <div class="_css-title _css-title-flowname">
                        实际完成比例：
                    </div>
                    <div class="_css-fieldvalue _css-fieldvaluename _css-percentarea">
                        <el-input-number
                            size="small"
                            v-model="m_addingpercent" 
                            :precision="2"                     
                            controls-position="right"
                            @change="evt_percentchangePerent"
                            :min="0" 
                            :max="100">
                        </el-input-number>
                        <div class="_css-percentshow">%</div>
                    </div>
                </div> 
                <div class="_css-line _css-line-name">
                    <div class="_css-title _css-title-flowname">
                        计划完成进度：
                    </div>
                    <div class="_css-fieldvalue _css-fieldvaluename _css-percentarea">
                        <div class="unit-number">
                            <el-input
                                type="number"
                                v-model="m_adding_planvalue" 
                                controls-position="right"
                                readonly
                            >
                            </el-input>
                            <div class="_css-percentshow">{{ m_add_unitValue }}</div>
                        </div>
                    </div>
                </div>
                <div class="_css-line _css-line-name">
                    <div class="_css-title _css-title-flowname">
                        实际完成进度：
                    </div>
                    <div class="_css-fieldvalue _css-fieldvaluename _css-percentarea">
                        <el-input-number
                        size="small"
                        v-model="m_adding_actualvalue" 
                        :precision="2"                     
                        controls-position="right"
                        @change="evt_percentchange" 
                        >
                        </el-input-number>
                        <div class="_css-percentshow">{{ m_add_unitValue }}</div>
                    </div>
                </div>
                <div class="_css-line _css-line-name">
                    <div class="_css-title _css-title-flowname">
                        状态：                   
                    </div>
                    <div class="_css-fieldvalue _css-fieldvaluename _css-percentarea">
                    <div class="_css-percentshow">{{m_planState}}</div>
                    </div>
                </div>
            </div>
    
            <div slot="buttonslot" class="_css-flowAddBtnCtn" >
                <zbutton-function
                    :init_text="'确定'"
                    :init_fontsize="14"
                    :debugmode="true"
                    :init_height="'40px'"
                    :init_width="'120px'"
                    @onclick="func_save"
                    >
                </zbutton-function>
                <zbutton-function
                    :init_text="'取消'"
                    :init_color="'rgba(24, 144, 255)'"
                    :init_bgcolor="'#fff'"
                    :init_fontsize="14"
                    :debugmode="true"
                    :init_height="'40px'"
                    :init_width="'120px'"
                    @onclick="func_cancelnewitem"
                    >
                </zbutton-function>
            </div>
        
        </zdialog-function>
        <!-- //新进度填报对话框 -->
        
        <div class="btn-initiating-header">{{ headerTitleText }}</div>
        <div class="_css-imgpro-body">
            <div class="pro-in-out" @click.stop="handleClickInOut" :class="proLeftIn ? 'p-out' : ''"></div>
            <div class="css-body-tree _css-tree-left"  v-show="!proLeftIn">
                <!-- tree head -->
                <div class="_css-materialtypelist-head">
                    <div class="_css-materhead-text">
                        进度方案
                        <!-- <i class="icon-interface-set_se cursor-pointer" v-if="bIsProjectComMgr" @click="handelClickSetTaskSort"></i> -->
                    </div>
                    <div  
                    class="_css-btnimport _css-change-addnew" @click.stop="clickTreeOpenOrClose">
                    
                        <div >{{treeOpenOrCloseText}}</div>
                    </div>
                </div>
                <!-- //tree head -->
                <template>

                <div 
                class="_css-treearea">
                    <el-tree
                        :data="treeData"
                        node-key="UID"
                        class="_css-customstyle"
                        auto-expand-parent
                        ref="tree"
                        :props="defaultProps"
                        :expand-on-click-node="false"
                        :current-node-key="checked"
                        @node-click="nodeClick"
                        @node-expand="nodeExpand"
                        @node-collapse="nodeCollapse"
                        :default-expanded-keys="expandedList"
                        :default-expand-all="defaultExpandAll"
                    >
                    </el-tree>
                </div>
                </template>
            </div>
 
            <!-- table area -->
            <div class="css-body-table _css-imgprog-table"  :class="proLeftIn ? 'width0' : ''">
                <!-- table top -->
                <div class="_css-imgprog-tabletop">
                    <div class="_css-btngroup">
                        <span>工程任务：{{clickMaterialName}}</span>
                        <div v-if="change_btnimport">
                            <div 
                                v-if="m_hasReportSubmit && m_tableIsCanAdd"
                                @click="func_toaddnewitem()"
                                class="_css-btnimport _css-change-addnew">
                              <div >进度填报</div>
                            </div>
                            <div 
                                v-else
                                class="_css-btnimport _css-change-addnew css-dis _css-not-allowed">
                            <div >进度填报</div>
                          </div>
                        </div>
                    </div>
                    <div class="_css-button-detail" @click="func_model">
                        <div class="_css-btnimport _css-change-addnew">
                            <div >实际进度模拟</div>
                        </div>
                    </div>
                </div>
                <!-- //table top -->
 
                <!-- table self -->
                <div class="_css-imgprog-tableself">
                    <u-table
                        ref="ref_table"
                        :data="m_tabdata"
                        style="width: 100%;margin-bottom: 20px;"
                        v-loading="elTableLoading"
                        element-loading-text="数据加载中..."
                        element-loading-spinner="el-icon-loading"
                        element-loading-background="rgba(0, 0, 0, 0.3)"
                        border
                        class="_css-table-ele _css-table-ele2 css-scroll _css-customstyle css-fixedleftgbcolor-white css-table-cellborder"
                        height="500"
                        :key="tableKey + Math.floor(Math.random() * 1000) + 1"
                        :default-sort="{prop: 'date', order: 'descending'}"
                        :row-class-name="tableRowClassName"
                        :highlight-current-row="true"
                        :header-cell-style="{'background-color':'transparent'}"
                    >
 
                        <u-table-column
                        :resizable="true"
                        prop="Progress_Name"
                        border
                        fixed="left"
                        min-width="150"
                        label="工程任务"
                        class="_css-celllongcolumn"
                        >
                            <template slot-scope="scope">
                                <div 
                                class="_css-costitem" >{{scope.row.Progress_Name}}</div>
                            </template>
                        </u-table-column>
                        <template v-if="BackLevel == 1 || BackLevel == 2">
 
                            <u-table-column
                            :resizable="true"
                            prop="Progress_createuser"
                            width="80"
                            label="填报人"
                            class="_css-celllongcolumn"
                            >
                            <template slot-scope="scope">
                                <div
                                class="_css-costitem" >{{scope.row.Progress_createuser}}</div>
                            </template>
                            </u-table-column>
                        </template>
                        <u-table-column
                            :resizable="true"
                            prop="Progress_state"
                            width="60"
                            label="状态"
                            class="_css-celllongcolumn"
                            >
                            <template slot-scope="scope">
                                <div
                                class="_css-costitem" 
                                :class="{'redRoom': scope.row.Progress_state == '滞后', 
                                'greenRoom': scope.row.Progress_state == '超前', 
                                'normalRoom': scope.row.Progress_state}">
                                {{scope.row.Progress_state}}
                                </div>
                            </template>
                        </u-table-column>
 
                        <u-table-column
                            :resizable="true"
                              prop="Progress_planvalue"
                            width="120"
                            label="计划完成进度"
                            class="_css-celllongcolumn"
                            >
                            <template slot-scope="scope">
                                <div
                                class="_css-costitem" >{{scope.row.Progress_planvalue}}</div>
                            </template>
                        </u-table-column>
 
                        <u-table-column
                            :resizable="true"
                              prop="Progress_actualvalue"
                            width="110"
                            label="实际完成进度"
                            class="_css-celllongcolumn"
                            >
                            <template slot-scope="scope">
                                <div
                                class="_css-costitem" >{{scope.row.Progress_actualvalue}}</div>
                            </template>
                        </u-table-column>
                        <template v-if="BackLevel == 1 || BackLevel == 2">
                            <u-table-column
                            :resizable="true"
                            :fixed="false"
                            prop="Progress_unittime"
                            sortable
                            border
                            min-width="110"
                            label="填报时间"
                            class="_css-celllongcolumn"
                            >
                                <template slot-scope="scope">
                                    <div 
                                    class="_css-costitem" >{{scope.row.Progress_unittime | flt_datetimefromserver}}</div>
                                </template>
                            </u-table-column>
                        </template>
 
                        <u-table-column
                        :resizable="true"
                        :fixed="false"
                        prop="bip_planStartDT"
                        border
                        min-width="120"
                        label="计划开始时间"
                        class="_css-celllongcolumn"
                        >
                            <template slot-scope="scope">
                                <div 
                                class="_css-costitem" >{{scope.row.Progress_planstarttime | flt_datetimefromserver}}</div>
                            </template>
                        </u-table-column>
                        <u-table-column
                        :resizable="true"
                        :fixed="false"
                        prop="Progress_plannendtime"
                        border
                        min-width="120"
                        label="计划结束时间"
                        class="_css-celllongcolumn"
                        >
                            <template slot-scope="scope">
                                <div 
                                class="_css-costitem" >{{scope.row.Progress_plannendtime | flt_datetimefromserver}}</div>
                            </template>
                        </u-table-column>
                        <u-table-column
                        :resizable="true"
                        :fixed="false"
                        prop="Progress_actualstarttime"
                        border
                        min-width="120"
                        label="实际开始时间"
                        class="_css-celllongcolumn"
                        >
                            <template slot-scope="scope">
                                <div 
                                class="_css-costitem" >{{scope.row.Progress_actualstarttime | flt_datetimefromserver}}</div>
                            </template>
                        </u-table-column>
                        <u-table-column
                        :resizable="true"
                        :fixed="false"
                        prop="Progress_actualendtime"
                        border
                        min-width="120"
                        label="实际结束时间"
                        class="_css-celllongcolumn"
                        >
                            <template slot-scope="scope">
                                <div 
                                class="_css-costitem" >{{scope.row.Progress_actualendtime | flt_datetimefromserver}}</div>
                            </template>
                        </u-table-column>
                        <template v-if="BackLevel == 1 || BackLevel == 2">
                            <u-table-column
                            :resizable="true"
                            :fixed="false"
                            prop="AuditStatus"
                            border
                            width="90"
                            label="审核状态"
                            class="_css-celllongcolumn"
                            >
                                <template slot-scope="scope">
                                    <div 
                                    class="_css-costitem" v-if="scope.row.AuditStatus != 2 && scope.row.AuditStatus != 3">{{scope.row.AuditStatus | filterStatus}}</div>
                                    <div 
								  :class="getClassObject(scope.row.AuditStatus)"
                                    class="_css-costitem css-cp"  v-if="scope.row.AuditStatus == 2 || scope.row.AuditStatus == 3" @click="clickviewAuditDescription(scope.row.AuditStatus,scope.row.AuditDescription)">{{scope.row.AuditStatus | filterStatus}}</div>
                                </template>
                            </u-table-column>
                        </template>
                        <template v-if="BackLevel == 1 || BackLevel == 2">
                            <u-table-column
                            v-if="BackLevel == 1 || BackLevel == 2"
                            min-width="180"
                            label="操作"
                            fixed="right"
                            class="_css-celllongcolumn"
                            :resizable="true"
                            >
                                <template slot-scope="scope">
                                    <!-- 施工方
                                    提交可点击    AuditStatus=0 待提交 
                                    编辑可点击    AuditStatus=0 待提交 =3 驳回
                                    删除可点击    AuditStatus=0 待提交   // 有提交审核权限、是管理员、就能删除、不管是提交还是驳回还是
                                    监理方   被驳回后只能是在次点击编辑在去提交
                                    审批  AuditStatus=1   待审核
                                    驳回  AuditStatus=1   待审核 -->
 
                                    <div 
                                        v-if="m_hasReportSubmit"
                                        class="_css-costitem _css-btnsctn" >
                                        <div @click="submitAudit(scope.row,0)"
                                        class="_css-btnimport _css-innerbtn" 
                                        :class="scope.row.AuditStatus == 0 ? '' : 'not-allow'"
                                        >
                                            <div >提交</div>
                                        </div>
                                        <div @click="itemfunc_edit(scope.row)"
                                            class="_css-btnimport _css-innerbtn"
                                            :class="scope.row.AuditStatus == 0 || scope.row.AuditStatus == 3? '' : 'not-allow'"
                                        >
                                            <div >编辑</div>
                                        </div>
                                        <!-- bIsProjectComMgr 是项目管理员  任何时候都能删除 -->
                                        <div @click="itemfunc_delete(scope.row)"
                                        v-if="bIsProjectComMgr"
                                        class="_css-btnimport _css-innerbtn"
                                        >
                                            <div >删除</div>
                                        </div>
                                        <!-- bIsProjectComMgr=false 不是项目管理员 -->
                                        <div @click="itemfunc_delete(scope.row)"
                                        v-if="!bIsProjectComMgr"
                                        class="_css-btnimport _css-innerbtn"
                                        :class="scope.row.AuditStatus == 0 || scope.row.AuditStatus == 3 ? '' : 'not-allow'"
                                        >
                                            <div >删除</div>
                                        </div>
                                    </div>
                                    <div 
                                        v-if="!m_hasReportSubmit && m_hasReportJDTB_Audit"
                                        class="_css-costitem _css-btnsctn" >
                                        <div @click="TurnDownClick(scope.row,1)"
                                        :class="scope.row.AuditStatus == 1 ? '' : 'not-allow'"
                                        class="_css-btnimport _css-innerbtn">
                                            <div >审批</div>
                                        </div>
                                        <div @click="TurnDownClick(scope.row,2)"
                                        :class="scope.row.AuditStatus == 1 ? '' : 'not-allow'"
                                        class="_css-btnimport _css-innerbtn">
                                            <div >驳回</div>
                                        </div>
                                    </div>
                                
                                </template>
                            </u-table-column>
                        </template>
 
                    </u-table>
                </div>
            </div>
        </div>
        <zdialog-function
        :init_title="'设置'"
        :init_zindex="1003"
        :init_innerWidth="450"
        :init_width="450"
        :init_height="500"
        init_closebtniconfontclass="icon-suggested-close"
        :init_usecustomtitlearea="false"
        @onclose="setTaskSortDialogShow = false"
        v-if="setTaskSortDialogShow"
        >
        
        <div slot="mainslot" class="_css-addingnameinput-ctn"
        @mousedown="_stopPropagation($event)"
        >
            <div class="addingnameinput">
                <el-tree
                    style="height:500px !important;overflow:scroll"
                    :data="setTreeData"
                    node-key="UID"
                    class="_css-customstyle"
                    auto-expand-parent
                    ref="setTree"
                    default-expand-all
                    :props="defaultProps"
                    :expand-on-click-node="false"
                    @node-click="setSortnodeClick"
                    @node-expand="setSornodeExpand"
                    @node-collapse="setSornodeCollapse"
                    :default-expanded-keys="setSorexpandedList"
                    :default-checked-keys="setDefaultCheckedKeys"
                    show-checkbox
                >
                    <div class="custom-tree-node" slot-scope="{ node }">
                        <span>{{ node.label }}</span>
                        
                    </div>
                </el-tree> 
            </div>
        </div>
 
        <div slot="buttonslot" class="_css-flowAddBtnCtn" >
            <zbutton-function
                :init_text="'保存'"
                :init_fontsize="14"
                :debugmode="true"
                :init_height="'40px'"
                :init_width="'120px'"
                @onclick="setTaskSortCancelSave"
                >
            </zbutton-function>
            <zbutton-function
                :init_text="'取消'"
                :init_color="'rgba(24, 144, 255)'"
                :init_bgcolor="'#fff'"
                :init_fontsize="14"
                :debugmode="true"
                :init_height="'40px'"
                :init_width="'120px'"
                @onclick="setTaskSortCancel"
                >
            </zbutton-function>
        </div>
        
        </zdialog-function>
 
        <zdialog-function
        :init_title="viewAuditType == 0 ? '进度任务审核' : '进度任务驳回'"
        :init_zindex="1003"
        :init_innerWidth="450"
        :init_width="450"
        :init_height="500"
        init_closebtniconfontclass="icon-suggested-close"
        :init_usecustomtitlearea="false"
        @onclose="setTurnDownShow = false"
        v-if="setTurnDownShow"
        >
        
        <div slot="mainslot" class="_css-addingnameinput-ctn _css-turn-down"
        @mousedown="_stopPropagation($event)"
        >
            <el-input
                @mousedown="_stopPropagation($event)"
                type="textarea"
                :rows="2"
                :placeholder="viewAuditType == 0 ? '请输入审批意见' : '请输入驳回原因'"
                v-model="turnDowntextarea">
            </el-input>
        </div>
 
        <div slot="buttonslot" class="_css-flowAddBtnCtn" >
            <zbutton-function
                :init_text="viewAuditType == 0 ? '审批' : '驳回'"
                :init_fontsize="14"
                :debugmode="true"
                :init_height="'40px'"
                :init_width="'120px'"
                @onclick="turnDown"
                >
            </zbutton-function>
            <zbutton-function
                :init_text="'取消'"
                :init_color="'rgba(24, 144, 255)'"
                :init_bgcolor="'#fff'"
                :init_fontsize="14"
                :debugmode="true"
                :init_height="'40px'"
                :init_width="'120px'"
                @onclick="setTurnDownShow=false;turnDowntextarea=''"
                >
            </zbutton-function>
        </div>
        
        </zdialog-function>
       <zdialog-function
        :init_title="viewAuditType == 0 ? '审批意见' : '驳回原因'"
        :init_zindex="1003"
        :init_innerWidth="450"
        :init_width="450"
        :init_height="500"
        init_closebtniconfontclass="icon-suggested-close"
        :init_usecustomtitlearea="false"
        @onclose="viewAuditDescriptionShow = false"
        v-if="viewAuditDescriptionShow"
        >
        
        <div slot="mainslot" class="_css-addingnameinput-ctn _css-turn-down css-tal" :class="viewAuditType == 1 ? 'colorf00' :''"
        @mousedown="_stopPropagation($event)"
        >
           {{ viewAuditDescriptionText}}
        </div>
        </zdialog-function>
 
        <ProgressPlayActual
            v-if="imageModelVisible"
            :ProgressId="click_projectUID"
            :modelIframeSrc='modelIframeSrc'
            :getModelData="getModelData"
            :allPlayObj="allPlayObj"
            :getPlayingData="getPlayingData"
            :selectSceneID="selectOpenSceneObj.SceneId"
            @close="imageModelVisible=false"
        ></ProgressPlayActual>
    </div>
</template>
<script>
import ProgressPlayActual from "./ProgressPlayActual"
 
export default {
    data() {
        return {
            bIsProjectComMgr: false, // 是否是项目管理员
            headerTitleText: '',
            proLeftIn: false, // 左边折叠
 
            treeData: null,  // 树结构的值
            defaultProps: {
                children: "children",
                label: "Name",
            },
            defaultExpandAll: false,  // 树结构展开
            expandedList: [],
            checked: '', // 记录当前选中的节点UID
 
            // 正在添加的填报时间
            // 正在添加的百分比
            // 当前所选中的节点数据
            m_addingdatetime: undefined, // 填报时间
            m_addingActualStartT: null,
            m_addingActualEndT: null,
            addingActualStartTReadonly: false,
            addingActualEndTReadonly: false,
            m_addingpercent: null,
            
            m_add_unitValue: '%', // 当前填报的单位、默认是%
            m_add_unitText: '总量', // 当前填报的文字、默认是总量
            proDisplayModeValue: '百分比/%',
            proDisplayModeoptions:[
                {
                    value: '百分比/%',
                    label: '百分比%',
                },
                {
                    value: '里程/m',
                    label: '里程m'
                },
                {
                    value: '里程/km',
                    label: '里程km'
                },
                {
                    value: '高程/m',
                    label: '高程m'
                },
            ],
            m_add_setunitValuePer: 100,  // 设置单位百分比数值
            m_add_setunitValueLC: 0,  // 设置单位里程数值
            m_add_setunitValueDi: 0,  // 设置单位高程底部高程数值
            m_add_setunitValueDing: 0,  // 设置单位高程顶部高程数值
            unitSetBool: false, // 是否设置进度单位，来确定是否能编辑 
            m_planDataTime: [],  // 计划日期
            planDataTime_readonly: false,
            startPlanTime: '', // 计划开始时间
            endPlanTime: '', // 计划结束时间
            m_planpercent: 0,  // 计划完成比例 
            m_adding_planvalue: 0,// 计划完成进度
            m_adding_actualvalue: 0,// 实际完成进度  
            m_planpercentStr: '',
            m_planState: '正常',
            m_addingselectednode: undefined,
            m_editingdatetime: undefined,
            m_editingActualStartT: null,
            m_editingActualEndT: null,
            m_editingbip_guid:'',
            m_editTimeDiffAdd: 0,
            m_edit_parentid: '',
            TimeDiffAdd: '', // 计划天数
 
            // 编辑进度
            m_edit_selectednode_name: '',//编辑的名字
            m_edit_DataTime: [],  // 编辑的计划时间
            startEditTime: '',
            endEditTime: '',
            editDataTime_readonly: true,  
            m_editpercentStr: '',   // 编辑的计划完成比例
            m_editpercent: 0,   // 编辑计划完成比例
            m_editState: '',     //编辑 的状态
            m_editingpercent:1,    // 编辑的实际完成比例
            m_edit_planvalue: 0,  // 编辑的计划完成进度
            m_edit_actualvalue: 0, // 编辑的实际完成进度
            edit_Progress_ProjectID: '', // 编辑时候请求接口需要的参数
            edit_Progress_ID: '', // 编辑时候请求接口需要的参数
            Progress_treeID: null, // 编辑时候请求接口需要的参数
            imageModelVisible: false,
            // 显示进度填报对话框
            status_showadd: false,
            status_showedit: false,
            m_tabdata: [], 
            m_tableIsCanAdd:true,
            BackLevel:0,
            tableKey:'',
            elTableLoading: false,
            modelIframeSrc:'',
            modelid: '',// 当前选中模型的modelid
            allPlayObj: {}, // 当前模拟相关设置值
            clickMaterialName: '',
            change_btnimport: false,
            treeOpenOrClose: false,  // 点击展开折叠tree
            treeOpenOrCloseText: '点击展开',
            getModelData: null,  // 实际进度模拟请求接口返回的数据
            axiosLoading: false, // 有些项目请求接口时间过长，添加loading提示
            getPlayingData: [], // 这个值为模拟时候的数据，是根据填报数据来的
 
            extdata: {
                funcauthdata: undefined
            },
            // 有权限进度的编辑权限
            // m_hasReportEdit: true,  // 无权限为false  有权限为true
            m_hasReportSubmit: true, // 提交+编辑权限、如果是管理员俩权限都有、默认提交权限
            m_hasReportJDTB_Audit: false, // 审核权限、并且m_hasReportSubmit != true
            setTaskSortDialogShow: false,// 设置大屏显示数据
            setTreeData: [],
            setSorexpandedList: [],
            setDefaultCheckedKeys: [],
            setSortchecked: [], // 所有被勾选选中的数据
            turnDowntextarea: '', // 驳回原因
            setTurnDownShow: false, // 驳回弹窗
            turnDownType: {}, 
            viewAuditDescriptionShow: false,
            viewAuditDescriptionText: '', // 驳回原因
            viewAuditType: 0,  // 审批 0 驳回 1 
 
            getTreeDataPromise: '', // 请求进度方案树数据的Promise
            selectOpenSceneObj: {}, // 选中打开场景的场景相关值
            click_projectUID: '',

        };
    },
    components: {
        ProgressPlayActual,
    },
    filters: {
        flt_datetimefromserver(str) {
            let _this = this;
            var str1 = str;
            if (str) {
                str1 = str.substr(0, 10)
            }
            return str1;
        }, 
        planDiffDay(start,end){
            let _this = this;
            let _start = start.substr(0, 10);
            let _end = end.substr(0, 10);
            var aDate, oDate1, oDate2, iDays;
            oDate1 = new Date(_start).getTime();
            oDate2 = new Date(_end).getTime();
            iDays = parseInt((oDate1 - oDate2) / 1000 / 60 / 60 / 24) //把相差的毫秒数转换为天数  
            return  Math.abs(iDays) + 1;
        },
        filterStatus(number){
            // 0 待提交 1 审核中 2 审核通过 3 已驳回、审核不通过 
            let status = '';
            switch(number) { 
                case 0: 
                    status = '待提交'; 
                    break; 
                case 1: 
                    status = '待审核'; 
                    break; 
                case 2: 
                    status = '已审核'; 
                    break; 
                case 3: 
                    status = '已驳回'; 
                    break; 
                    default: status = '审核不通过'; 
            }
            return status
        }
    }, 
    created() {
      this.$Bus.$on('UpdateAuth',this.updateAuth)
    },
    mounted() {
        this.headerTitleText = this.$staticmethod._Get("menuText") || '';
 
        let _this = this;
 
        _this.getTreeData();
        this.$staticmethod._Get("currentisProjectManager") == "1"
            ? (_this.bIsProjectComMgr = true)
            : (_this.bIsProjectComMgr = false);
        // // 权限：提交=提交+编辑、审核
        // this.m_hasReportSubmit = this.$staticmethod.hasSomeAuth('JDTB_SubmitAudit')  // 提交+编辑权限
        // this.m_hasReportJDTB_Audit = this.$staticmethod.hasSomeAuth('JDTB_Audit')  // 审核权限
        this.updateAuth()
        this.handleRoueChange()
        setTimeout(() => {this.headerTitleText = this.$staticmethod._Get("menuText") || '';},300);
    },
    
    beforeDestroy() {
      this.$Bus.$off('UpdateAuth',this.updateAuth)
    },
    methods: {
        // 点击消息进入本页面的处理逻辑
        handleRoueChange() {
            const urlChangedBy = this.$staticmethod._Get("UrlChangedBy")
            if(urlChangedBy) {
                const segments = urlChangedBy.split("-")
                if(segments && segments.length) {
                    const flag = segments[0]
                    if(flag && flag === 'Msg') {
                        console.log('UnFoldMenuAndSelect-JDTB')
                        this.$Bus.$emit('UnFoldMenuAndSelect','JDTB') // ProjectBoot.vue中注册该事件:更新左侧菜单展开节点并选择菜单
                        const msgRelatedData = JSON.parse(sessionStorage.getItem("MsgRelatedData")) // 消息关联的数据
                        const refProgressPlan = this.$refs.tree
                        if(refProgressPlan && msgRelatedData) {
                            this.getTreeDataPromise.then(() => {
                                if(this.treeData && this.treeData.length) {
                                    const treeId = msgRelatedData.Progress_treeID 
                                    const treeItem = this.$staticmethod.walkThroughTreesByDepthFirst(
                                        this.treeData,
                                        (node) => {
                                            if(node.UID === treeId) {
                                                return true
                                            }
                                        },
                                        "children",
                                        true
                                    )
                                    if(treeItem) {
                                        this.$nextTick(() => {
                                            this.nodeClick(treeItem)
                                            this.expandedList = [treeId]
                                        })
                                    }
                                }
                            })
                        }
                        this.$staticmethod._Set("MsgRelatedData","")
                        this.$staticmethod._Set("UrlChangedBy","")  // 处理完成重置
                    }
                }
            }
        },
        // 更新权限
        updateAuth() {
            //   console.log('updateAuth');
            // 或者通过保存在store中的leftMenuActivated的Buttons去判断
          try {
            // 权限：提交=提交+编辑、审核
            this.m_hasReportSubmit = this.$staticmethod.hasSomeAuth('JDTB_SubmitAudit')  // 提交+编辑权限
            this.m_hasReportJDTB_Audit = this.$staticmethod.hasSomeAuth('JDTB_Audit')  // 审核权限
          } catch(e) {
          }
        },
        getTreeData(){
            let _this = this;
            let _OrganizeId = this.$staticmethod._Get("organizeId")  
            let _url = `${this.$MgrBaseUrl.planGetTree}?organizeId=${_OrganizeId}&Token=${this.$staticmethod.Get('Token')}`;
            _this.getTreeDataPromise = _this.$axios
                .get(_url)
                .then((x) => {
                    if (x.data.Ret > 0) {
                        this.treeData = x.data.Data;
 
                        // x.data.Data原来的树结构、先只取UID, Name, IsStatistics, children四个属性
                        const processData = (data) => {
                            const { UID, Name, IsStatistics, children = [] } = data;
                            const newData = { UID, Name, IsStatistics };
                            
                            if (children.length > 0) {
                                newData.children = children.map(child => processData(child));
                            } else {
                                newData.children = [];
                            }
 
                            return newData;
                        };
                        let processedData= x.data.Data.map(item => processData(item));
                        // 在对取值到的processedData设置index
                        const addIndexes = (arr, index = '') => {
                            return arr.map((item, i) => {
                                const newIndex = index !== '' ? `${index}-${i + 1}` : `${i + 1}`;
                                item.index = newIndex;
                                if (item.children && item.children.length > 0) {
                                    item.children = addIndexes(item.children, newIndex);
                                }
                                return item;
                            });
                        };
                        
                        this.setTreeData = addIndexes(processedData);
                        this.setDefaultCheckedKeys = this.findUIDs(this.setTreeData )
        
                        // this.setTreeData = this.keepProperties(x.data.Data)
                        // console.log(this.setTreeData)
                        this.$nextTick(()=>{
                            // 在tree数据更新后，需要将更新的tree结构数据，同时更新在选中的节点上，所以在这重新setCurrentKey，然后赋值给m_addingselectednode
                            _this.$refs['tree'].setCurrentKey(_this.checked); 
                            _this.m_addingselectednode = _this.$refs.tree.getCurrentNode();
 
                        })
                        // console.log(this.$refs['tree'].getCheckedNodes(this.checked),'====')
                    } else {
                        _this.$message.error(x.data.Msg);
                    }
                })
                .catch((x) => {
                    console.error(x);
                });
        },
        findUIDs(arr) {
            let uids = [];
            // 遍历每个对象
            arr.forEach((item) => {
                // 检查 IsStatistics 是否为 true 或 1
                if (item.IsStatistics === true || item.IsStatistics === 1) {
                uids.push(item.UID);
                }
                
                // 递归查找子对象的 UID
                if (item.children && item.children.length > 0) {
                uids.push(...this.findUIDs(item.children));
                }
            });
 
            return uids;
        },
        
        getTableData(){
            let _this = this;
            _this.elTableLoading = true;
            let _uid = _this.m_addingselectednode.UID;
            let _projectUID = _this.m_addingselectednode.ProjectUID ? _this.m_addingselectednode.ProjectUID : _this.m_addingselectednode.UID;
            this.click_projectUID = _projectUID
            let _url = `${this.$MgrBaseUrl.GetProject}?uid=${_uid}&projectid=${_projectUID}&Token=${_this.$staticmethod.Get("Token")}`;
            _this.$axios
                .get(_url)
                .then((x) => {
                    if (x.data.Ret > 0) {
                        _this.tableKey = new Date()+'' 
                        _this.m_tabdata = x.data.Data.baseProgressNewOutputs;
                        _this.m_tableIsCanAdd = x.data.Data.IsCanAdd
                        _this.BackLevel = x.data.Data.BackLevel; // 当前的阶段的节点、1是最末级节点，2是倒数第二级节点（末级的父级）
                        _this.status_showadd = false;
                        _this.elTableLoading = false;
                    } else {
                        _this.$message.error(x.data.Msg);
                    }
                })
                .catch((x) => {
                    console.error(x);
                });
        },
        nodeClick(data){
            let _this = this;
            data.children ? _this.change_btnimport = false : _this.change_btnimport = true;
            _this.clickMaterialName = data.Name;
            _this.m_addingselectednode = data;
            _this.checked = data.UID;
            _this.$nextTick(()=>{
                let arr =[]; arr.push(data)
                this.$refs['tree'].setCurrentKey(_this.checked); 
                _this.$refs.tree.setCurrentNode(data)
                _this.$refs.tree.setCheckedNodes(arr)
                
            })
            _this.getTableData(); 
        }, 
        nodeExpand(data) {
          this.expandedList.push(data.UID); // 在节点展开是添加到默认展开数组
        },
        nodeCollapse(data) {
          this.expandedList.splice(this.expandedList.indexOf(data.UID), 1); // 收起时删除数组里对应选项
        },
        // 点击设置树结构展开折叠
        clickTreeOpenOrClose(){
            let _this = this;
            _this.treeOpenOrClose = !_this.treeOpenOrClose;
            _this.expandedList = []
            if(_this.treeOpenOrClose){ 
                _this.treeOpenOrCloseText = '点击折叠'; 
                for(var i=0;i<this.$refs.tree.store._getAllNodes().length;i++){
                    _this.expandedList.push(_this.$refs.tree.store._getAllNodes()[i].data.UID)
                    _this.$refs.tree.store._getAllNodes()[i].expanded = true;
                }
            }else{
                _this.treeOpenOrCloseText = '点击展开'; 
                _this.expandedList = []
                for(var i=0;i<this.$refs.tree.store._getAllNodes().length;i++){
                    _this.$refs.tree.store._getAllNodes()[i].expanded = false;
                } 
            }
        },
        data_state(plan,pre,start,end,pre_end){
            let _this = this;
            let str_State = ''; 
           let _start = start.substr(0, 10);
           let _end = end.substr(0, 10);
           let _pre_end = pre_end.substr(0, 10);
 
            let diffdate = _this.isDuringDate(_pre_end,_start,_end);  // 判断实际时间是否在计划时间范围
 
            if(diffdate){ 
                str_State = _this.toCalculatePercentState(plan,pre);
            }else{
                if(_this.DateDiff(_pre_end,_start) < 1){
                    str_State = '超前';
                }else{
                    str_State = '滞后';
                }
            }
           return str_State;
 
        },
 
        // 保存编辑
        func_saveedit() { 
 
            let _this = this;
            let _Token = _this.$staticmethod.Get("Token");
            let _editing = null;
 
            // 改变编辑-填报时间的格式
            if(_this.m_editingdatetime.length == 10){
                _editing = _this.m_editingdatetime
            }else{
                _editing =  _this.$formatData.formatDateCheck(_this.m_editingdatetime).substr(0,10);
            }
            let _s =  new Date(this.m_editingActualStartT).getTime();
            let _editaddtime =  new Date(this.m_editingdatetime).getTime();
            if(_s > _editaddtime ){
                this.$message.error('填报时间不能早于实际开始时间');
                return
            }
            
            if(!this.m_editingActualEndT && this.m_editingpercent * 1 >= 100){
                this.$message.error('当前数据暂无实际结束时间，完成进度不能为100%，请重新填写实际完成进度')
                return
            }
 
            _this.$axios({
                method: 'post',
                data: {
                    token: _Token,
                    Progress_Name: _this.m_edit_selectednode_name,
                    Progress_createuser: _this.$staticmethod.Get('RealName'),
                    Progress_createuserid: _this.$staticmethod.Get('UserId'),
                    Progress_state: _this.m_editState,//填报状态
                    Progress_planstarttime: _this.startEditTime,
                    Progress_plannendtime:  _this.endEditTime,
                    Progress_actualstarttime: _this.m_editingActualStartT,
                    Progress_actualendtime: _this.m_editingActualEndT,
                    Progress_planfate: _this.m_editTimeDiffAdd,// 计划天数
                    Progress_planratio: _this.m_planpercent,// 计划完成比例
                    Progress_actualratio: _this.m_editingpercent,//实际完成比例
                    Progress_parentid: _this.m_addingselectednode.ParentTaskUID,
                    Progress_treeID: _this.Progress_treeID,
                    Progress_ProjectID: _this.edit_Progress_ProjectID,
                    Progress_unittime: _editing, // 填报日期
                    Progress_ID: _this.edit_Progress_ID,
                    IsSubmitAudit: false,  // 提交按钮，在这里是编辑，值传false
                    Progress_planvalue: _this.m_edit_planvalue,  // 计划完成比例
                    Progress_actualvalue: _this.m_edit_actualvalue // 实际完成比例
                },
                url: `${this.$MgrBaseUrl.EditProject}`
            }).then(x => {
                if (x.data.Ret > 0) { 
                    _this.$message.success('编辑成功');
 
                    _this.getTableData();
                    _this.status_showedit = false;
 
                } else {
                    _this.$message.error(x.data.Msg);
                }
            }).catch(x => {
                console.error(x);
            });
            
 
        },
        getDateStr(dt) {
            let year = dt.getFullYear();
            let month = dt.getMonth() + 1;
            let dat = dt.getDate();
            month = month.toString().padStart(2, "0");
            dat = dat.toString().padStart(2, "0");
            let finalStr = `${year}-${month}-${dat}`;
            return finalStr;
        },
 
        func_canceledititem() {
            let _this = this;
            _this.status_showedit = false;
        }, 
 
        // 弹出编辑对话框
        itemfunc_edit(row) {
            // console.log(row,'===编辑')
            let _this = this; 
            this.getUnitTextConfig()
            let startT = _this.$options.filters["flt_datetimefromserver"](row.Progress_planstarttime);
            let endT = _this.$options.filters["flt_datetimefromserver"](row.Progress_plannendtime);
            _this.edit_Progress_ProjectID = row.Progress_ProjectID
            _this.m_edit_selectednode_name = row.Progress_Name;   //编辑的名字
            _this.m_edit_DataTime = [startT,endT];  // 编辑的计划时间
            _this.startEditTime = startT;
            _this.endEditTime = endT; 
            _this.m_editingActualStartT = _this.$options.filters["flt_datetimefromserver"](row.Progress_actualstarttime);
            _this.m_editingActualEndT = row.Progress_actualendtime;
            _this.m_editpercentStr = row.Progress_planratio + '%';
            _this.m_editpercent = row.Progress_planratio;
            _this.m_editingpercent = row.Progress_actualratio; // 编辑的实际完成比例
            _this.m_editingdatetime = _this.$options.filters["flt_datetimefromserver"](row.Progress_unittime);
            _this.edit_Progress_ID = row.Progress_ID;
            _this.m_editState = _this.data_state(row.Progress_planratio,row.Progress_actualratio,row.Progress_planstarttime,row.Progress_plannendtime,row.Progress_unittime);
            _this.Progress_treeID = row.Progress_treeID;
 
            _this.m_edit_planvalue = row.Progress_planvalue;  // 计划完成进度
            _this.m_edit_actualvalue = row.Progress_actualvalue; // 实际完成进度
            _this.m_editTimeDiffAdd = row.Progress_planfate;  // 天数
            _this.m_edit_parentid = row.Progress_parentid;  // 天数
 
            _this.status_showedit = true;
        },
 
        // 移除填报数据
        do_deleteitem(Progress_ID) { 
            let _this = this;
            let _url = `${window.bim_config.webserverurl}/api/Schedual/Schedual/DeleteProject?Progress_ID=${Progress_ID}&token=${_this.$staticmethod.Get("Token")}`;
            _this.$axios
                .post(_url)
                .then(x => {
                if (x.status == 200) {
                    if (x.data.Ret > 0) {
                        _this.$message.success('操作成功');
                        _this.getTableData();
                        _this.getTreeData();
                    }else{
                        _this.$message.error(x.data.Msg);
                    }
                }
                })
                .catch(x => {});
        },
 
        // 触发移除进度填报数据
        itemfunc_delete(row) {
            let _this = this;
            _this.$confirm('确认移除该填报数据？', '操作确认').then(x => {
                _this.do_deleteitem(row.Progress_ID);
            }).catch(x => {
 
            });
        }, 
        // 获取进度单位
        getUnitTextConfig(){
            let _this = this;
            _this.$axios
                .get(`${this.$MgrBaseUrl.GetUnitTextConfig}?uid=${_this.m_addingselectednode.UID}&token=${_this.$staticmethod.Get("Token")}`)
                .then(res=>{
                    if(res.data.Ret > 0){
                        let _data = res.data.Data;// 0:百分比、1：里程/m、2：里程/km、3：高程/m
                        // _data.UnitType && _data.UnitType > 0 ? this.unitSetBool = true : this.unitSetBool = false;
                        // console.log(this.m_tabdata,'===tablelist')
                        this.m_tabdata.length > 0 ? this.unitSetBool = true : this.unitSetBool = false;   // 后端没有处理GetUnitTextConfig，判断改为看是有当前表格有数据、来查看是否能编辑
                        switch (_data.UnitType) {
                            case 1:
                                this.m_add_unitText = '百分比';
                                this.m_add_unitValue = '%';
                                this.proDisplayModeValue = '百分比/%';
                                this.m_add_setunitValuePer = _data.UnitValue * 1;
                                break;
                            case 2:
                                this.m_add_unitText = '里程';
                                this.m_add_unitValue = 'm';
                                this.proDisplayModeValue = '里程/m';
                                this.m_add_setunitValueLC = _data.UnitValue * 1;
                                break;
                            case 3:
                                this.m_add_unitText = '里程';
                                this.m_add_unitValue = 'km';
                                this.proDisplayModeValue = '里程/km';
                                this.m_add_setunitValueLC = _data.UnitValue * 1;
                                break;
                            case 4:
                                this.m_add_unitText = '高程';
                                this.m_add_unitValue = 'm'
                                this.proDisplayModeValue = '高程/m'
                                let val1 = _data.UnitValue.split(',')[0]*1;
                                let val2 = _data.UnitValue.split(',')[1]*1;
                                this.m_add_setunitValueDi = val1;
                                this.m_add_setunitValueDing = val2;
                                break;
                            default:
                                this.unitSetBool = false;
                                this.proDisplayModeValue = '百分比/%',
                                this.m_add_unitValue = '%';
                                this.m_add_unitText = '百分比';
                                this.m_add_setunitValuePer = 100;
                                this.m_add_setunitValueLC = 0;
                                this.m_add_setunitValueDi = 0;
                                this.m_add_setunitValueDing = 0;
                        }
                        this.computeTimeDiff();
 
                    }else{
                        this.unitSetBool = false;
                        this.m_add_setunitValuePer = 100;
                        this.m_add_unitText = '百分比';
                        this.m_add_unitValue = '%';
                        this.m_add_setunitValueLC = 0;
                        this.m_add_setunitValueDi = 0;
                        this.m_add_setunitValueDing = 0;
                    }
                })
                .catch(err=>{})
        },
        // 设置进度单位
        setUnitTextConfig(){
            let _this = this;
            let _type = 1, _Config = null;
            switch (_this.proDisplayModeValue) {
                case '百分比/%':
                    _Config = `${_this.m_add_setunitValuePer}`;
                    _type = 1;
                    if (_this.m_add_setunitValuePer > 100) {
                        _this.$message.warning('百分比最大值为100');
                        _this.m_add_setunitValuePer = 100;
                    }
                    break;
                case '里程/m':
                case '里程/km':
                    _Config = `${_this.m_add_setunitValueLC}`;
                    _type = _this.proDisplayModeValue === '里程/m' ? 2 : 3;
                    break;
                case '高程/m':
                    _Config = `${_this.m_add_setunitValueDi},${_this.m_add_setunitValueDing}`;
                    _type = 4;
                    break;
            }
 
            _this.$axios({
                method: 'post',
                data: {
                    Token: _this.$staticmethod.Get("Token"),
                    UID: _this.m_addingselectednode.UID,
                    UnitType: _type,  // 百分比=1；里程/m=2；里程/km=3；高程/m：4
                    UnitValue: _Config, // '底部，顶部'、当是高程的时候用逗号隔开的字符串
                },
                url: `${this.$MgrBaseUrl.SetUnitText}`
            }).then(x => {
                if (x.data.Ret > 0) {
                    this.submitAddSave()
                } else {
                    this.$message.error(x.data.Msg)
                }
            }).catch(x => {
                console.error(x);
            });
        },
 
        func_save() {
            if(!this.m_addingActualStartT){
                this.$message.error('请填写实际开始时间');
                return
            }
            
            // 判断实际结束时间不得大于实际开始时间
            let _s = new Date(this.m_addingActualStartT).getTime();
            let _e = new Date(this.m_addingActualEndT).getTime();
            if(_s > _e && this.m_addingActualEndT != null){
                this.$message.error('实际开始时间不得大于实际结束时间');
                return
            }
            
            if(this.m_addingActualEndT && this.m_addingActualEndT != null ){
                switch (this.proDisplayModeValue) {
                    case '百分比/%':
                        this.m_adding_actualvalue = 100
                        this.m_addingpercent = this.m_adding_actualvalue;
                        break;
                    case '里程/m':
                    case '里程/km':
                        this.m_adding_actualvalue = this.m_add_setunitValueLC
                        this.m_addingpercent = ((this.m_adding_actualvalue * 1) / (this.m_add_setunitValueLC * 1) ) * 100
                        break;
                    case '高程/m':
                        this.m_adding_actualvalue = this.m_add_setunitValueDing * 1
                        this.m_addingpercent = (this.m_adding_actualvalue / this.m_add_setunitValueDing * 1) * 100
                        break;
                }
            }
            if(!this.m_adding_actualvalue){
                this.$message.error('请填写实际完成比例');
                return
            }
            
            let _addtime =  new Date(this.$formatData.formatDateCheck(this.m_addingdatetime).substr(0,10)).getTime();
            if(_s > _addtime ){
                this.$message.error('填报时间不能早于实际开始时间');
                return
            }
            
            switch (this.proDisplayModeValue) {
                case '百分比/%':
                    if(this.m_adding_actualvalue > 100){
                        this.$message.error('实际完成进度不能大于100');
                        return
                    } 
                    break;
                case '里程/m':
                case '里程/km':
                    if(this.m_add_setunitValueLC == 0 || this.m_add_setunitValueLC < 0.001){
                        this.$message.error('请填写任务里程');
                        return
                    }
                    if(this.m_adding_actualvalue > this.m_add_setunitValueLC){
                        this.$message.error('实际完成进度不能大于任务里程');
                        return
                    }
                    break;
                case '高程/m':
                    if(Number(this.m_add_setunitValueDi) == 0 || Number(this.m_add_setunitValueDi) < 0.001){
                        this.$message.error('请填写底部高程');
                        return
                    }
                    if(Number(this.m_add_setunitValueDing) == 0 || Number(this.m_add_setunitValueDing) < 0.001){
                        this.$message.error('请填写顶部高程');
                        return
                    }
                    if(this.m_add_setunitValueDi * 1 > this.m_add_setunitValueDing * 1){
                        this.$message.error('底部高程不能大于顶部高程');
                        return
                    }
                    if(this.m_adding_actualvalue < this.m_add_setunitValueDi * 1){
                        this.$message.error('实际完成进度不能小于底部高程');
                        return
                    }
                    if(this.m_adding_actualvalue > this.m_add_setunitValueDing * 1){
                        this.$message.error('实际完成进度不能大于顶部高程');
                        return
                    }
                    break;
            }

            // 如果有实际结束时间、进度为100% 
            
            if(this.m_addingpercent >= 100 && !this.m_addingActualEndT){
                this.$message.error('请填写实际结束时间');
                return
            } 
            // 判断单位是否设置、已设置直接提交、没设置先设置在提交
            this.unitSetBool ? this.submitAddSave() : this.setUnitTextConfig();
        },
        submitAddSave(){
            let _this = this;
            let _Token = _this.$staticmethod.Get("Token");
            let _url = `${this.$MgrBaseUrl.Addproject}`;
            let _adding =  _this.$formatData.formatDateCheck(_this.m_addingdatetime).substr(0,10);
            _this.$axios({
                    method: 'post',
                    data: {
                        token: _Token,
                        Progress_Name: _this.m_addingselectednode.Name,
                        Progress_state: _this.m_planState,//填报状态
                        Progress_createuser: _this.$staticmethod.Get('RealName'),
                        Progress_createuserid: _this.$staticmethod.Get('UserId'),
                        Progress_planstarttime: _this.$options.filters["flt_datetimefromserver"](_this.m_addingselectednode.Start),
                        Progress_plannendtime:  _this.$options.filters["flt_datetimefromserver"](_this.m_addingselectednode.Finish),
                        Progress_actualstarttime: _this.m_addingActualStartT,
                        Progress_actualendtime: _this.m_addingActualEndT,
                        Progress_planfate: _this.TimeDiffAdd,// 计划天数
                        Progress_planratio: _this.m_planpercent,// 计划完成比例
                        Progress_actualratio: _this.m_addingpercent,//实际完成比例
                        Progress_treeID: _this.m_addingselectednode.UID,
                        Progress_ProjectID: _this.m_addingselectednode.ProjectUID,
                        Progress_parentid: _this.m_addingselectednode.ParentTaskUID,
                        Progress_unittime: _adding, // 填报日期
                        IsSubmitAudit: false,  // 提交按钮，在这里是新增，值传false
                        Progress_planvalue: _this.m_adding_planvalue,  // 计划完成比例
                        Progress_actualvalue: _this.m_adding_actualvalue // 实际完成比例
                    },
                    url: _url
                }).then(x => {
                    if (x.data.Ret > 0) {
                        _this.$message.success('添加成功');
                        // 刷新table
                        _this.getTableData();
                        _this.getTreeData();
                    } else {
                        _this.$message.error(x.data.Msg);
                    }
                }).catch(x => {
                    console.error(x);
                });
        },
 
        func_cancelnewitem() {
            let _this = this;
            _this.status_showadd = false;
        },
 
        func_initadding() {
            let _this = this;
            _this.m_addingdatetime = undefined;
        },
 
        // 弹出进度填报的对话框，如果选择了正确的构件叶子节点分类
        func_toaddnewitem() {
            let _this = this; 
            /*
                在这里查找是否有设置进度单位、还需要计算计划进度
                如果设置了先赋值、没设置的话直接初始化是百分比、提交的时候先判断是否有设置单位
            */
 
            this.getUnitTextConfig();
            
            this.$nextTick(()=>{
 
            _this.m_addingdatetime = new Date();
            _this.m_addingpercent = '';  // 实际完成比例
            _this.m_adding_actualvalue = 0;  // 实际完成进度
            _this.startPlanTime = _this.$options.filters["flt_datetimefromserver"](_this.m_addingselectednode.Start);  // 计划开始时间
            _this.endPlanTime = _this.$options.filters["flt_datetimefromserver"](_this.m_addingselectednode.Finish);  // 计划结束时间
            _this.m_planDataTime = [_this.startPlanTime,_this.endPlanTime];
            if(_this.m_addingselectednode.ActualStart != null && _this.m_addingselectednode.ActualStart){
                // 有实际开始时间、赋值、没有的话不可以编辑实际开始时间
                _this.m_addingActualStartT =  _this.$options.filters["flt_datetimefromserver"](_this.m_addingselectednode.ActualStart);
                _this.addingActualStartTReadonly = true;
            }else{
                _this.addingActualStartTReadonly = false;
                _this.m_addingActualStartT = "";
            }
            if(_this.m_addingselectednode.ActualFinish && _this.m_addingselectednode.ActualFinish != null ){
                _this.m_addingActualEndT =  _this.$options.filters["flt_datetimefromserver"](_this.m_addingselectednode.ActualFinish);
                _this.addingActualEndTReadonly = true;
            }else{
                _this.addingActualEndTReadonly = false;
                _this.m_addingActualEndT = "";
            }
                _this.status_showadd = true;
            })
        },
        // 改变值
        computeTimeDiff(){
            let _this = this;
            // m_addingdatetime 填报时间
            // 两者的时间差
            let edit_time = _this.$formatData.formatDateCheck(_this.m_addingdatetime).substr(0,10);  // 填报时间
 
            let TimeDiff = this.$formatData.DateDiff(_this.startPlanTime,_this.endPlanTime) + 1; // 计划天数
            _this.TimeDiffAdd = TimeDiff;
            let TimeDiffPer = this.$formatData.DateDiff(_this.startPlanTime,edit_time);  // 计划开始时间和填报时间的时间差

            let diffdate = this.isDuringDate(edit_time,_this.startPlanTime,_this.endPlanTime);  // 查看填报时间是否在开始时间和结束时间内            
            let diffPer = (100 / TimeDiff); // 100/计划天数，计算每天的百分比
            let tdiffper = TimeDiffPer * diffPer; // 天数差 * 每天的百分比 = 当前的计划百分比
            if(diffdate){
                let diffLastPer = (diffPer*1 + tdiffper*1 ).toFixed(1);
                if(diffLastPer > 100){
                    diffLastPer = 100
                }else{
                    diffLastPer = (diffPer*1 + tdiffper*1 ).toFixed(1);
                }
                // 计算计划完成比例
                _this.m_planpercentStr = diffLastPer + '%';
                _this.m_planpercent = diffLastPer * 1;
 
                // 在当前开始-结束时间范围内=计算计划完成进度
                // 百分比：计划完成进度 = 计划完成比例 _this.m_planpercent 
                // 里程：计划完成进度 = 总里程数*（计划完成比例/100）  方便计算，直接拿计划完成比例计算了
                // 高程：计划完成进度 = （（（顶部高程-底部高程）/ 总计划天数）* 当前第几天）+ 底部高程
                // （（顶部高层-底部高层）/（（当前天数进度高层+底部高层））*100%
                _this.m_planState = _this.toCalculatePercentState(_this.m_planpercent,_this.m_addingpercent)
                switch (_this.proDisplayModeValue) {
                    case '百分比/%':
                        _this.m_adding_planvalue = _this.m_planpercent 
                        break;
                    case '里程/m':
                    case '里程/km':
                        _this.m_adding_planvalue = (_this.m_add_setunitValueLC * ( _this.m_planpercent / 100)).toFixed(2);
                        break;
                    case '高程/m':
                        TimeDiffPer = TimeDiffPer + 1
                        let _val = ((Number(_this.m_add_setunitValueDing) - Number(_this.m_add_setunitValueDi)) / TimeDiff) * TimeDiffPer * 1;
                        if(TimeDiffPer == 0){
                            _this.m_adding_planvalue = (_val * 1 + _this.m_add_setunitValueDing * 1).toFixed(2)
                        }else{
                            _this.m_adding_planvalue = (_val * 1 + _this.m_add_setunitValueDi * 1).toFixed(2)
                        }
                        // console.log(this.m_adding_planvalue,`顶:${this.m_add_setunitValueDing}、底部:${ _this.m_add_setunitValueDi}、计划天数：${TimeDiff}、时间：${TimeDiffPer}、val=${_val}`)
                        break;
                }
            }else{
                if(this.DateDiff(edit_time,_this.startPlanTime) < 1){
                    _this.m_planpercentStr = '0%';
                    _this.m_planpercent = 0;
                    this.m_planState = '超前';
                    switch (_this.proDisplayModeValue) {
                        case '百分比/%':
                            _this.m_adding_planvalue = 0;
                            break;
                        case '里程/m':
                        case '里程/km':
                            _this.m_adding_planvalue = 0;
                            break;
                        case '高程/m':
                            if(TimeDiffPer == 0){
                                _this.m_adding_planvalue = _this.m_add_setunitValueDing * 1
                            }else{
                                _this.m_adding_planvalue = _this.m_add_setunitValueDi * 1
                            }
                            // _this.m_adding_planvalue = _this.m_add_setunitValueDing * 1
                            break;
                    }
                }else{
                    _this.m_planpercentStr = '100%';
                    _this.m_planpercent = 100
                    this.m_planState = '滞后';
                    switch (_this.proDisplayModeValue) {
                        case '百分比/%':
                            _this.m_adding_planvalue = 100;
                            break;
                        case '里程/m':
                        case '里程/km':
                            _this.m_adding_planvalue = _this.m_add_setunitValueLC;
                            break;
                        case '高程/m':
                            _this.m_adding_planvalue = _this.m_add_setunitValueDing * 1
                            break;
                    }
                }
            }
        },
        isDuringDate (_adding,beginDateStr, endDateStr) {
            var curDate = new Date(_adding),
                beginDate = new Date(beginDateStr),
                endDate = new Date(endDateStr);
            if (curDate >= beginDate && curDate <= endDate) {
                return true;
            }
            return false;
        },
        // 进度填报的实际完成进度
        evt_percentchange(ev) { 
            let _this = this;
            this.m_adding_actualvalue = ev;
            // 超前滞后根据计划完成进度和实际完成进度计算，填报时间大于计划开始时间-超前、填报时间大于计划结束时间-滞后，在范围内和计划完成进度比较
            this.computeTimeDiff()

            
            // 百分比：实际完成比例 = 实际完成进度
            // 里程： 实际完成比例 = （实际完成进度/总进度）*100
            // 高程：实际完成比例 = （实际完成进度/顶部高层）*100
            switch (_this.proDisplayModeValue) {
                case '百分比/%':
                    this.m_addingpercent = this.m_adding_actualvalue;
                    break;
                case '里程/m':
                case '里程/km':
                    this.m_addingpercent = ((this.m_adding_actualvalue * 1) / (this.m_add_setunitValueLC * 1) ) * 100
                    break;
                case '高程/m':
                    
                    this.m_addingpercent = (this.m_adding_actualvalue / this.m_add_setunitValueDing * 1) * 100
                    break;
            }
            if(this.m_adding_planvalue * 1 < this.m_adding_actualvalue * 1){
                _this.m_planState = '超前';
            }
            if(this.m_adding_planvalue * 1 > this.m_adding_actualvalue * 1){
                _this.m_planState = '滞后';
            }
            if(this.m_adding_planvalue * 1 == this.m_adding_actualvalue * 1){
                _this.m_planState = '正常';
            }
        },
        // 编辑信息中的实际完成比例事件
        evt_editingpercentchange(ev) {
            // m_editingpercent
            let _this = this;
            _this.m_edit_actualvalue = ev;
            
            if(this.m_edit_planvalue * 1 < this.m_edit_actualvalue * 1){
                _this.m_editState = '超前';
            }
            if(this.m_edit_planvalue * 1 > this.m_edit_actualvalue * 1){
                _this.m_editState = '滞后';
            }
            if(this.m_edit_planvalue * 1 == this.m_edit_actualvalue * 1){
                _this.m_editState = '正常';
            }
            switch (_this.proDisplayModeValue) {
                case '百分比/%':
                    
                    this.m_editingpercent = this.m_edit_actualvalue;
                    break;
                case '里程/m':
                case '里程/km':
                    this.m_editingpercent = ((this.m_edit_actualvalue * 1) / (this.m_add_setunitValueLC * 1) ) * 100
                    break;
                case '高程/m':
                    this.m_editingpercent = (this.m_edit_actualvalue / this.m_add_setunitValueDing * 1) * 100
                    break;
            }
            
        },
 
        _stopPropagation(ev) {
            ev && ev.stopPropagation && ev.stopPropagation();
        },
 
        // 根据是否选中了某一行，返回类字符串
        tableRowClassName({ row, rowIndex }) {
            return 'css-tdunder';
        },
        clickviewAuditDescription(val,viewAuditDescription){
            val == 2 ? this.viewAuditType = 0 : this.viewAuditType = 1;
            this.viewAuditDescriptionText = viewAuditDescription
            this.viewAuditDescriptionShow = true
        },
        TurnDownClick(row,type){
            type == 1 ? this.viewAuditType = 0 : this.viewAuditType = 1;
            this.turnDownType = {
                row:row,
                type:type
            }
            if(type==1){
                this.turnDowntextarea = '同意'
            }else{
                this.turnDowntextarea = ''
            }
            this.setTurnDownShow = true;
        },
        turnDown(){
            if(this.turnDowntextarea.length == 0) {
                this.$message.warning('请输入驳回原因')
                return
            }
            this.submitAudit(this.turnDownType.row,this.turnDownType.type)
        },
        // 提交审批驳回操作
        submitAudit(row,type){
            // 0 提交审批 1 审批通过 2 审批拒绝
            let _this = this;
            let _id = [],params;
            _id.push(row.Progress_ID)
            if(type == 0){
                params = {
                    token: _this.$staticmethod.Get("Token"),
                    ProgressIds: _id,
                    AuditType:type,
                } 
            }else{
                params = {
                    token: _this.$staticmethod.Get("Token"),
                    ProgressIds: _id,
                    AuditType:type,
                    AuditDescription: this.turnDowntextarea
                }
            }
            _this.$axios({
                    method: 'post',
                    data: params,
                    url: `${this.$MgrBaseUrl.submitAudit}`
                }).then(x => {
                    if (x.data.Ret > 0) {
                        _this.$message.success(x.data.Msg);
                        // 刷新table
                        _this.getTableData();
                        _this.getTreeData();
                    } else {
                        _this.$message.error(x.data.Msg);
                    }
                    if(type != 0){
                        this.setTurnDownShow=false;
                        this.turnDowntextarea=''
                    }
                }).catch(x => {
                    console.error(x);
                });
        },
 
        // 点击进去当前详情
        func_model() {
            let _this = this;
            if(_this.m_addingselectednode != {} && _this.m_addingselectednode != undefined ){
                let _model_project = _this.m_addingselectednode.ProjectUID ? _this.m_addingselectednode.ProjectUID : _this.m_addingselectednode.UID
                _this.getProjectModelOpenScene(_model_project);    
            }else{
                _this.$message.error('请先选择进度任务')
            }
        },
        // 根据id  查找是否有绑定的场景
        async getProjectModelOpenScene(planid){
            let params = {
                projectId: this.$staticmethod._Get("organizeId"),
                progressId: planid
            }
            const res = await this.$api.getProgressFillingSettings(params)
            if(res.Ret == 1){
                if(res.Data.SceneId && res.Data.SceneId.length > 0){
                    this.selectOpenSceneObj.SceneId = res.Data.SceneId
                    this.selectOpenSceneObj.SceneModelId = res.Data.SceneModelId
                    this.allPlayObj = res.Data
                    
                    // 有选择场景
                    // this.getDataSceneAndModelPlayObj = res.Data;
                    // this.getDataSceneAndModelPlayObj.SceneId && this.getDataSceneAndModelPlayObj.SceneId.length > 0 ? this.hasSelectScene = true : this.hasSelectScene = false;
                    this.getProgressList(planid)
                }else{
                    this.$message.warning('请先在进度方案中选择场景')
                    return
                }
            }
        },
        async getProgressList(planid){
            this.axiosLoading = true;

            let params = {
                planId: planid
            }
            const res = await this.$api.getProgressByPlanIdOfDate(params)
            if(res.Ret == 1){
                // 请求成功
                // 处理Details中的数据、模型id需要处理  改成场景中的对应的id
                let _data = res.Data; 
                let modelToSceneID = JSON.parse(this.selectOpenSceneObj.SceneModelId)  // 当前选中场景的场景元素ID
                let _details = _data.Details;
                let ModelIds = _data.ModelIds;

                // ModelIds = ModelIds.map(id => modelToSceneID.find(item => item.modelid === id).sceneModelid)

                let found = false;
                for (let item of modelToSceneID) {
                    if (ModelIds.includes(item.modelid)) {
                        ModelIds = [item.sceneModelid];
                        found = true;
                        break;
                    }
                }

                if (!found) {
                    ModelIds = [];
                    this.axiosLoading = false;
                    this.$message.warning('当前进度任务未正确关联相关场景')
                    return
                }

                let _str_ModelIds = ModelIds.join('|');
                
                this.modelIframeSrc = { projectID: this.$staticmethod._Get("organizeId"),modelID:_str_ModelIds}; 

                let modelToSceneIDMap = new Map(modelToSceneID.map(item => [item.modelid, item.sceneModelid]));
                // console.log(_str_ModelIds,'==ModelIds')
                // console.log(this.modelIframeSrc,'==modelToSceneIDMap')
                for (const item of _details) {
                    for (const list of item.List) {
                        if(list.PteElementIds.length > 0){
                            const sceneModelid = modelToSceneIDMap.get(list.PteElementIds[0].modelid);
                            if (sceneModelid) {
                                list.PteElementIds[0].modelid = sceneModelid;
                                const elementIds = list.PteElementIds[0].elementids.map(elementId => `${list.PteElementIds[0].modelid}^${elementId}`);
                                list.PteElementIds[0].elementids = elementIds;
                                list.elementids = elementIds;
                            }
                        }
                    }
                }


                this.getPlayingData = _data; 
                this.imageModelVisible = true;
            }
            this.axiosLoading = false;
        },  
        
        getElement(listElements) {
            let arr = [];
            listElements.forEach(item => {
                item.elementids.forEach(ids => {
                arr.push(`${item.modelid}^${ids}`);
                });
            });
            return arr;
        },
        // 修改计划时间
        getPlanTimeChange(ev) {
            let _this = this;
            // 计划时间的开始时间和结束时间
            let start = new Date(this.$staticmethod.dtToString(ev[0]));
            let end = new Date(this.$staticmethod.dtToString(ev[1]));
            this.startPlanTime = _this.$formatData.formatDateCheck(start).substr(0,10);
            this.endPlanTime = _this.$formatData.formatDateCheck(end).substr(0,10);
        }, 
        DateDiff(sDate1, sDate2) {
            var aDate, oDate1, oDate2, iDays;
            oDate1 = new Date(sDate1).getTime();
            oDate2 = new Date(sDate2).getTime();
            iDays = parseInt((oDate1 - oDate2) / 1000 / 60 / 60 / 24) //把相差的毫秒数转换为天数   
            return  iDays;
        },
        getEditAddingTimeChange(ev) {
            let _this = this;
            _this.m_editingdatetime = ev;
            let _adding = _this.$formatData.formatDateCheck(_this.m_editingdatetime).substr(0,10);  // 填报时间
            // 两者的时间差
            let TimeDiff = _this.$formatData.DateDiff(_this.startEditTime,_this.endEditTime) + 1;// 计划天数
            let TimeDiffPer = _this.$formatData.DateDiff(_this.startEditTime,_adding)// 计划开始时间和填报时间的时间差
            this.m_editTimeDiffAdd = TimeDiff;
            let diffdate = _this.isDuringDate(_adding,_this.startEditTime,_this.endEditTime);// 查看填报时间是否在开始时间和结束时间内
            let diffPer = (100 / TimeDiff); // 100/计划天数，计算每天的百分比
            let tdiffper = TimeDiffPer * diffPer; // 天数差 * 每天的百分比 = 当前的计划百分比
            
            if(diffdate){
                let diffLastPer = (diffPer*1 + tdiffper*1 ).toFixed(1);
                if(diffLastPer > 100){
                    diffLastPer = 100
                }else{
                    diffLastPer = (diffPer*1 + tdiffper*1 ).toFixed(1);
                }
                _this.m_editpercentStr = diffLastPer + '%';
                _this.m_editpercent = diffLastPer * 1;
 
                _this.m_editState = _this.toCalculatePercentState(_this.m_editpercent,_this.m_editingpercent)
                
                switch (_this.proDisplayModeValue) {
                    case '百分比/%':
                        _this.m_edit_planvalue = _this.m_editpercent 
                        break;
                    case '里程/m':
                    case '里程/km':
                        _this.m_edit_planvalue = _this.m_add_setunitValueLC * ( _this.m_editpercent / 100);
                        break;
                    case '高程/m':
                        TimeDiffPer = TimeDiffPer + 1
                        let _val = ((_this.m_add_setunitValueDing * 1 - _this.m_add_setunitValueDi * 1) / TimeDiff) * TimeDiffPer * 1;
                        if(TimeDiffPer == 0){
                            _this.m_edit_planvalue = (_val * 1 + _this.m_add_setunitValueDing * 1).toFixed(2)
                        }else{
                            _this.m_edit_planvalue = (_val * 1 + _this.m_add_setunitValueDi * 1).toFixed(2)
                        }
                        break;
                }
            }else{
                if(_this.DateDiff(_adding,_this.startEditTime) < 1){
                    _this.m_editpercentStr = '0%'
                    _this.m_editpercent = 0;
                    _this.m_editState = '超前';
                    switch (_this.proDisplayModeValue) {
                        case '百分比/%':
                            _this.m_edit_planvalue = 0;
                            break;
                        case '里程/m':
                        case '里程/km':
                            _this.m_edit_planvalue = 0;
                            break;
                        case '高程/m':
                            if(TimeDiffPer == 0){
                                _this.m_edit_planvalue = _this.m_add_setunitValueDing * 1
                            }else{
                                _this.m_edit_planvalue = _this.m_add_setunitValueDi * 1
                            }
                            break;
                    }
                }else{
                    _this.m_editpercentStr = '100%'
                    _this.m_editpercent = 100;
                    _this.m_editState = '滞后';
                    switch (_this.proDisplayModeValue) {
                        case '百分比/%':
                            _this.m_edit_planvalue = 100;
                            break;
                        case '里程/m':
                        case '里程/km':
                            _this.m_edit_planvalue = _this.m_add_setunitValueLC;
                            break;
                        case '高程/m':
                            _this.m_edit_planvalue = _this.m_add_setunitValueDing * 1
                            break;
                    }
                }
            }
 
        },
        getAddingTimeChange(ev){
            let _this = this;
            _this.m_addingdatetime = ev;
            this.computeTimeDiff();
        }, 
        getAddingActualStartTimeChange(ev){
            this.m_addingActualStartT = this.$formatData.formatDateCheck(ev).substr(0,10);
        },
        getAddingActualEndTimeChange(ev){
            this.m_addingActualEndT = this.$formatData.formatDateCheck(ev).substr(0,10);
        },
        // 进度填报的实际完成比例
        evt_percentchangePerent(ev) { 
            let _this = this;
            this.m_addingpercent = ev;
            let _adding = _this.$formatData.formatDateCheck(_this.m_addingdatetime).substr(0,10);

            let diffdate = _this.isDuringDate(_adding,_this.startPlanTime,_this.endPlanTime);// 判断实际填报时间是否在计划时间范围
            
            if(diffdate){
                let state = (_this.m_addingpercent - _this.m_planpercent).toFixed(0);
                _this.m_planState = _this.toCalculatePercentState(_this.m_planpercent,_this.m_addingpercent)
            }else{
                if(_this.DateDiff(_this.m_addingdatetime,_this.startPlanTime) < 1){
                    _this.m_planState = '超前';
                }else{
                    _this.m_planState = '滞后';
                }
            }
        },
        toCalculatePercentState(planPercent,actualPercent){
            let setToleranceValues = 0;
            // 目前没有容差值，setToleranceValues值先为0
            // 当实际完成时间在计划时间范围内使用该方法
            // 当实际比例在planPercent ± setToleranceValues内，属于正常,小于属于滞后，大于属于超前
            let percentState = '';
            if(actualPercent >= planPercent - setToleranceValues  && actualPercent <= planPercent+setToleranceValues){
                percentState = '正常';
            }else if(actualPercent > planPercent - setToleranceValues){
                percentState = '超前';                
            }else{
                percentState = '滞后';
            }
            return percentState;
        },
        handleClickInOut(){
            this.proLeftIn = !this.proLeftIn;
        },
        // 填报时候选择展示方式
        proDisplayModeChange(val){
            this.proDisplayModeValue = val;
            this.m_add_unitText = val.split('/')[0];
            this.m_add_unitValue = val.split('/')[1];
            // 如果不等于百分比，任务总量=0
            if(this.m_add_unitText != '百分比'){
                this.m_add_setunitValueLC = this.m_add_setunitValueDi = this.m_add_setunitValueDing = 0
            }
            this.computeTimeDiff() 
        },
        // 里程改变
        m_add_setunitValueLCChange(value){
            // 里程数变化的时候，要计算计划完成进度
            this.computeTimeDiff() 
        },
        // 底部高程改变
        m_add_setunitValueDiChange(value){
            // 底部数变化的时候，要计算计划完成进度
            this.computeTimeDiff() 
        },
        // 顶部高程改变
        m_add_setunitValueDingChange(value){
            // 顶部数变化的时候，要计算计划完成进度
            this.computeTimeDiff() 
        },
        validateInput(value) {
            // this.m_adding_planvalue = value.replace(/[^\d.]/g, '');
            const regex = /^[0-9.]+$/;
            regex.test(value);
            // 只能是数字
        },
        // 设置大屏数据
        handelClickSetTaskSort(){
            this.getTreeData();
            this.setTaskSortDialogShow = true;
        },
        setTaskSortCancelSave(){
            // console.log(this.$refs.setTree.getCheckedNodes(),'=====this.$refs.setTree.getCheckedNodes()')
 
            /**
             * 因为后端所要的数据非常的....所以在下面做了好多的处理
             * 最后提交的数据、是需要处理、processedChecked是所有被勾选选中的数据
             * processedChecked需要先过滤掉children.length > 0的、把processedChecked所有的IsStatistics改为true
             *  if lastIndex的判断是为了确保多选的、、如果多选就选择第一个值
             * 最后在把不需要的属性参数过滤掉
             * plusProjectSettings 是显示排序、是setTreeData的第一级、需要把index改为sort
             * PlusTaskSettings 是显示的值、这个是还是整个setTreeData，所以要把processedChecked的值赋值给setTreeData
             */
 
            let arrChecked = this.$refs.setTree.getCheckedNodes()
            let filteredChecked = arrChecked.filter(obj => obj.children.length === 0);  // 删除有children>0属性的
            let modifiedChecked = filteredChecked.map(obj => ({ ...obj, IsStatistics: true })); // 修改IsStatistics=true
 
 
            let map = {};
            // processedChecked是过滤，所有index是三级以及以上的 1-1-1 ，所有过滤完的值
            let processedChecked = modifiedChecked.filter(obj => {
                let { index, ...rest } = obj;
                if (index.includes("-") && index.split('-').length - 1 >1) {
                    let lastIndex = index.lastIndexOf("-");
                    index = index.slice(0, lastIndex);
                }
                if (map[index]) {
                    return false;
                }
                map[index] = true;
                return true;
            });
            // console.log(processedChecked,'=====过滤属性的值')
             
            let allTreeData = this.setTreeData;
            let PlusTaskSettings = processedChecked.map(({ UID, IsStatistics }) => ({ UID, IsStatistics }));
            let plusProjectSettings = allTreeData.map((item, index) => ({
                UID: item.UID,
                IsStatistics: true,
                Sort: index
            }));
            this.saveAxiosFun(PlusTaskSettings,plusProjectSettings)
        },
        saveAxiosFun(pt,pp){
            this.$axios({
                method: 'post',
                data: {
                    Token: this.$staticmethod.Get("Token"),
                    PlusTaskSettings: pt,
                    plusProjectSettings: pp,
                },
                url: `${this.$MgrBaseUrl.SetPlusTaskStatisticsSort}`
            }).then(x => {
                if (x.data.Ret > 0) { 
                    this.$message.success('编辑成功');
                } else {
                    this.$message.error(x.data.Msg);
                }
                this.setTaskSortCancel();
            }).catch(x => {
                console.error(x);
            });
        },
        setTaskSortCancel(){
            // 取消操作
            this.setTaskSortDialogShow = false
        },
        setSortnodeClick(data){
            console.log(data);
        },
        setSornodeExpand(){
            
        },
        setSornodeCollapse(){
            
        },
        getClassObject(type) {
            return {
                color0f0: type === 2,
                colorf00: type === 3
            };
        },
        updateIsStatistics(obj) {
            for (let key in obj) {
                if (typeof obj[key] === 'object') {
                    this.updateIsStatistics(obj[key]);
                } else if (key === 'IsStatistics') {
                obj[key] = true;
                }
            }
        },
    }
}
</script>
<style scoped>
._css-tree-left{
    width: 270px;
    box-sizing: border-box;
    background-color: #fff;
    position: relative;
}
._css-selectednode {
    box-sizing: 24px;
    box-sizing: border-box;
    padding: 0 12px 0 12px;
    line-height: 24px;
}
._css-percentarea {
    justify-content: space-around;
}
._css-flowAddBtnCtn {
    display: flex;
    flex-direction: row-reverse;
    height: 64px;
    align-items: center;
    box-sizing: border-box;
    padding-right: 8px;
}
._css-line {
    padding:0 24px;
    box-sizing: border-box;
    margin: 8px 0 0 0;
    display: flex;
    align-items: center;
}
.unit-number{
    display: flex;
    white-space: nowrap;
    line-height: 36px;
}
.unit-number /deep/ .el-input__inner{
    padding: 0;
}
._css-addingnameinput-ctn ._css-line.display-none{
    display: none;
}
._css-fieldvaluename {
    flex: 1;
    height: 36px;
    display: flex;
    align-items: center;
    border: 1px solid rgba(0,0,0,0.1);
    border-radius: 4px;
    box-sizing: border-box;
} 
._css-btnimport._css-innerbtn {
    height: 25px;
    line-height: 16px;
}
._css-btnimport._css-innerbtn.css-dis {
    cursor:not-allowed;
}
._css-btnimport {
    font-size: 12px;
    color: #1890FF;
    border: 1px solid #1890FF;
    border-radius: 4px;
    padding: 4px 6px 4px 6px;
    margin-right: 12px;
    cursor: pointer;
}
._css-btnimport:hover {
    color:#fff;
    background-color: #1890FF;
}
._css-button-detail{
    color: #1890FF;
    margin-right: 10px;
}
._css-button-detail:hover{
    cursor: pointer;
}
._css-button-detail i {
    font-size: 20px;
    display: inline-block;
}
._css-btngroup {
    display: flex;
    align-items: center;
    margin-left:12px;
}
._css-imgprog-tabletop {
    height: 40px;
    background-color: #fff;
    display: flex;
    align-items: center;
    border-bottom:1px solid rgba(0, 0, 0, 0.05);
    box-sizing: border-box;
    justify-content: space-between;
}
._css-imgprog-tableself {
    position: absolute;
    width: 99%;
    height: calc(100% - 80px);
    background-color: #fff;
}
._css-table-ele {
    background-color: #fff;
}
._css-imgprog-table {
    position: relative;
    padding: 24px 10px 24px 10px;
    width: calc(100% - 300px);
    box-sizing: border-box;
    margin-left: 10px;
}
._css-imgprog-table.width0{
    width: calc(100% - 30px);
}
._css-costitem {
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow-x: hidden;
    font-size: 14px;
    font-weight: normal;
}
._css-btnsctn {
    display: flex;
    align-items: center;
    width:100%;
    justify-content: space-around;
}
._css-customstyle, ._css-customstyle /deep/ .singleTable {
  height: calc(100% - 0px) !important;
}
._css-materialtypelist-head {
  height: 64px;
  display: flex;
  align-items: center;
  padding-left: 8px;
}
._css-materhead-text {
  height: 28px;
  line-height: 28px;
  flex: 1;
  text-align: left;
  margin-left: 8px;
  font-weight: 500;
  color: rgba(0, 0, 0, 1);
}
._css-materhead-text i{
    cursor: pointer;
    vertical-align: middle;
    padding-left: 10px;
}
._css-treearea {
    height: calc(99.99% - 64px - 24px);
    overflow-y: auto;
    margin-bottom: 24px;
}
._css-imgpro-all {
    height:100%;
    background: #f0f2f5;
}
._css-imgpro-body {
    display: flex;
    height: calc(100% - 54px);
    position: relative;
}
._css-turn-down{
    padding: 20px;
}
._css-addingnameinput-ctn /deep/ .el-date-editor--daterange{
  width: 100%;
}
._css-title-flowname{
    width: 25%;
    text-align: left;
}
._css-plan-percent /deep/ .el-input__inner{
    padding:0 15px;
}
.redRoom{
    color:red;
}
.greenRoom{
    color: green;
}
.normalRoom{
    color:'#606266'
} 
._css-change-addnew{
    margin-left: 20px;
}
._css-imgprog-tableself /deep/ .el-table__header,._css-imgprog-tableself /deep/ .el-table__fixed-body-wrapper{
    background:#fff;
}
._css-fieldvaluename /deep/ .el-input__inner{
    line-height: 34px;
    height: 34px;
}
._css-fieldvaluename /deep/ .el-date-editor.el-input, 
._css-fieldvaluename /deep/ .el-date-editor.el-input__inner{
    width: 100%;
}
._css-not-allowed{
    cursor: not-allowed;
}
.pro-in-out{
  position: absolute;
  top: calc(50% - 120px);
  left:270px;
  width: 16px;
  height: 120px;
  background-image: url(../../../assets/images/p-in.png) ;
  background-size: 100%;
  background-repeat: no-repeat;
  z-index: 2;
  cursor: pointer;
}
.pro-in-out.p-out{
  background-image: url(../../../assets/images/p-out.png) ;
  background-repeat: no-repeat;
  background-size: 100%;
  z-index: 2;
  left: 0;
}
.el-select{
    width: 100%;
}
.not-allow{
    cursor: not-allowed;
    pointer-events: none;
    color: #606266;
    border: 1px solid #606266;
}
.colorf00{
    color: #f00;
}
.color0f0{
	color: #0f0;
}
._css-imgpro-all /deep/ textarea::placeholder {
  color: #555; 
}
.bg-f5f7fa{
    background-color: #f5f7fa;
}
</style>