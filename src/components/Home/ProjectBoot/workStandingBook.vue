<template>
    <div class="css-book-content">
			<header><i>流程台账</i></header>
			<main>
				<div class="pro-in-out" @click.stop="handleClickInOut" :class="proLeftIn ? 'p-out' : ''"></div>
				<div class="content-left" v-show="!proLeftIn">
					<ul class="tree-list">
						<li v-for="(item,index) in standingBookList" 
							:key=item.No
							@click.stop="handelClickSelectList(item,index)"
							:class="active == index ? 'active':''"
						>
							{{ item.Name }}
						</li>
					</ul> 
				</div>
				
				<div class="content-right" :class="proLeftIn ? 'margin-in' : 'margin-out'">
					<iframe :src="listSrc" frameborder="0" width="100" height="100"></iframe>
				</div>
			</main>
    </div>
</template>
<script>
export default {
    name: 'workStandingBook',
    data(){
			return {
				cctoken: '',
				proLeftIn: false,
				standingBookList:[],
				active: 0,  // tree选中
				itemText: '',
				listSrc: '',
			}
    },
    components: {
      
    },
    created(){
      
    },
    mounted(){
			this.getToken();
    },
    methods:{
			getToken() {
				let _this = this;
				let _OrganizeId = _this.$staticmethod._Get("organizeId");
				let _Token = _this.$staticmethod.Get("Token");
				let _url = `${window.bim_config.webserverurl}/api/User/User/CCFlowLogin`;
				let _para = {
					Token: _Token,
					organizeId: _OrganizeId,
				};
				_this
					.$axios({
						method: "post",
						url: _url,
						data:_para,
					})
					.then((x) => {
						if (x.data.Ret > 0) {
							this.cctoken = x.data.Data;
							this.getFlowList(x.data.Data);
						} else {
							_this.$message.error(x.data.Data);
						}
					})
					.catch((x) => {
						console.error(x);
					});
			},
			getFlowList(cctoken){
				let _OrganizeId = this.$staticmethod._Get("organizeId");
				let userNo = this.$staticmethod.Get("Account");
				this.$axios
					.get(`${window.bim_config.CCFlowUrl}/api/v1/user/GetFlow?userNo=${userNo}&organizeId=${_OrganizeId}&token=${cctoken}`)
					.then(res=>{
						if(res.status == 200){
							this.standingBookList = res.data.Flow;
							this.handelClickSelectList(this.standingBookList[0],0)
						}else{
							this.$message.error(res.data)
						}
					})
					.catch(err=>{
					})
			},
			// 点击左边展开
			handleClickInOut(){
				this.proLeftIn = !this.proLeftIn;
			},
			handelClickSelectList(item,index){
				this.itemText = item.Name;
				this.active = index;
				this.listSrc = `${window.bim_config.CCFlowUrl}/WF/RptDfine/Search.htm?SearchType=MyDept&FK_Flow=${item.No}&Token=${this.cctoken}`
			}
		},
}
</script>
<style lang="scss" scoped>
p{
	margin-bottom: 0;
}
.css-book-content{
	display: flex;
	height: 100%;
	flex-direction: column;
	text-align: left;
	header{
		height: 54px;
		line-height: 54px;
		width: 100%;
		background:#fff;
		i{
			font-size: 16px;
			font-weight: 400;
			margin-left: 24px;
			color: rgba(0,0,0,.85);
		}
	}
	main{
		position: relative;
		flex: 1;
		display: flex;
		flex-direction: row;
		.p-list-out{
			width:0;
		}
		.content-left{
			background: #fff;
			width: 200px;
			height: calc(100vh - 64px);
			margin-right:20px;
			background: #fff;
			position: relative;
			.tree-list{
				height: calc(100% - 50px);
				overflow-y: auto;
				width: 200px;    
				white-space: nowrap;
				li{
					line-height: 40px;
					height: 40px;
					padding-left: 26px;
					position: relative;
					display: flex;
					cursor: pointer;
					flex: 1;
				}
				.active,li:hover{
					background: #F5F5F5;
					color: rgba(0, 122, 255, 1);
				} 

			}
		}
		.margin-in{
			margin: 15px 0px 15px 20px;
		}
		.margin-out{
			margin: 15px 0px 15px 8px;
		}
		.content-right{
			flex:1;
			iframe{
				width: 100%;
				height: 100%;
			}
			overflow-x: scroll;
		}
	}
	.pro-in-out{
		position: absolute;
		top: calc(50% - 120px);
		left: 200px;
		cursor: pointer;
		width: 16px;
		height: 120px;
		background-image: url(../../../assets/images/p-in.png) ;
		background-size: 100%;
		background-repeat: no-repeat;
	}
	.pro-in-out.p-out{
		background-image: url(../../../assets/images/p-out.png) ;
		background-repeat: no-repeat;
		background-size: 100%;
		left: 0;
	}
}
</style>