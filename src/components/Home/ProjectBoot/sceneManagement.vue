<template>
  <div class="scene-content" @click.stop="closeAll">
    <div class="btn-initiating-header">{{ headerTitle }}</div>

    <div class="scene-bottom-content">

      <!-- 添加场景dialog -->
      <zdialog-function
      @mousedown="_stopPropagation($event)"
        :init_title="stateTitle"
        :init_zindex="1003"
        :init_innerWidth="400"
        :init_width="400"
        init_closebtniconfontclass="icon-suggested-close"
        :init_usecustomtitlearea="false"
        @onclose="uploadSceneDialog = false"
        v-if="uploadSceneDialog"
      >
        <div slot="mainslot" class="_css-add-scene" @mousedown="_stopPropagation($event)">
          <el-form
            ref="form"
            :model="addForm"
            :rules="addRules"
            label-position="top"
          >
            <el-form-item label="场景名称" prop="SceneName">
              <el-input
              @mousedown="_stopPropagation($event)"
                v-model="addForm.SceneName"
                placeholder="请输入场景名称"
              ></el-input>
            </el-form-item>
            <el-form-item
              label="创建人"
              prop="CreateUserName"
              v-if="sceneSetState == 'edit'"
            >
              <el-input
                v-model="addForm.CreateUserName"
                class="read-only"
                readonly=""
                placeholder="请输入创建人"
              ></el-input>
            </el-form-item>
            <el-form-item
              label="创建时间"
              prop="CreateTime"
              v-if="sceneSetState == 'edit'"
            >
              <el-input
                v-model="addForm.CreateTime"
                class="read-only"
                readonly=""
                placeholder="请输入创建时间"
              ></el-input>
            </el-form-item>
            <el-form-item label="场景封面" prop="SceneLogo">
              <el-upload
                ref="uploadLogo"
                class="avatar-uploader"
                action=""
                accept="image/*"
                :show-file-list="false"
                :auto-upload="false"
                :on-change="changeFile"
              >
                <img
                  id="giftImg"
                  v-if="headImg"
                  :src="
                    headImg ? headImg : require('../../../assets/images/uploadImg.png')
                  "
                  class="avatar"
                />
                <i v-else class="el-icon-plus avatar-uploader-icon"></i>
              </el-upload>
            </el-form-item>
            <el-form-item label="描述" prop="Comment">
              <el-input
                type="textarea"
                placeholder="请输入内容"
                v-model="addForm.Comment"
                maxlength="200"
                show-word-limit
              ></el-input>
            </el-form-item>
          </el-form>
        </div>
        <div slot="buttonslot" class="_css-addBtnCtn">
          <zbutton-function
            :init_text="'确定'"
            :init_fontsize="14"
            :debugmode="true"
            :init_height="'40px'"
            :init_width="'80px'"
            @onclick="saveUploadScene"
          >
          </zbutton-function>
          <zbutton-function
            :init_text="'取消'"
            :init_color="'rgba(24, 144, 255)'"
            :init_bgcolor="'#fff'"
            :init_fontsize="14"
            :debugmode="true"
            :init_height="'40px'"
            :init_width="'80px'"
            @onclick="cancelScene"
          >
          </zbutton-function>
        </div>
      </zdialog-function>
      <!-- 添加场景dialog -->

      <div class="scene-wrapper">
        <div class="tree-wrapper" v-if="!isTreeCollapsed && scenePhaseId!=='CJGL'">
          <div class="col1-title">
            <i class="icon-newface-model"></i>
            <div class="label">场景管理</div>
            <i class="el-icon-plus import" @click="isShowingImportTemplate = true"></i>
          </div>
          <el-tree
            class="el-tree-cus"
            node-key="BusinessCode"
            ref="elTree"
            empty-text="暂无子级"
            :highlight-current="true"
            :default-expand-all="false"
            :expand-on-click-node="true"
            :auto-expand-parent="true"
            default-expand-all
            :data="treeData"
            :props="elTreeProps"
            @node-click="onElTreeNodeClick"
          >
            <div class="el-tree-node-cus" slot-scope="{ node, data }">
              <el-tooltip
                effect="dark"
                :content="node.label"
                placement="top"
                :enterable="false"
              >
                <div class="label">{{ node.label }}</div>
              </el-tooltip>
              <div class="extra" @click.stop="onExtraBtnClick(node, $event)">
                <div class="extra-btn icon-interface-list"></div>
                <div class="extra-label">{{ data.length }}</div>
              </div>
            </div>
          </el-tree>
        </div>
       <div class="center-wrapper" v-if="scenePhaseId!=='CJGL'">
         <div
           :class="isTreeCollapsed ?'collapse-bar-out' :'collapse-bar'"
           @click.stop="isTreeCollapsed = !isTreeCollapsed"
         ></div>
       </div>
        <div class="scene-list">
          <div class="up-scene-btn">
            <button @click.stop="uploadMvFile()">创建场景</button>
            <span class="txtSearch">
          <input
            type="text"
            v-model="searchName"
            placeholder="搜索场景"
            @keyup="SearchList($event)"/>
          <i class="icon-interface-search" @click.stop="SearchList($event, 1)"></i>
        </span>
          </div>
          <div class="scene-units-container">
            <div
              class="scene-unit"
              v-for="item in sceneList"
              :key="item.SceneId"
              @click.stop="handelClickSceneToDetail(item)"
            >
            <img :src="getItemImg(item)" alt="" />
            <span class="scene-name">{{ item.SceneName }}</span>
            <span class="updateDate">
            添加时间：{{ formDate(item.CreateTime) }}
            <i
              class="icon-interface-set_se setting noHide icon-color"
              :title="'菜单'"
              @click.stop="handelClickUnitList(item)"
            ></i>
            <ul
              class="set-list"
              :class="{
                'set-list-show': checkedScene && checkedSceneID == item.SceneId,
              }"
            >
              <li @click.stop="getSceneContent(item)">
                <i class="icon-bgimg icon-s-re icon-color"></i>编辑场景
              </li>
              <li @click.stop="getSceneDetail(item)" >
                <i class="icon-bgimg icon-s-fenxiang icon-color"></i>场景详情
              </li>
              <li @click.stop="deleteScene(item)" >
                <i class="icon-bgimg icon-s-del icon-color"></i>删除场景
              </li>
            </ul>
          </span>
            </div>
          </div>
        </div>

      </div>
      <!-- <div class="scene-detail-iframe" v-if="sceneiframeDetail">
        <sceneManagementDetail
          :projectID="ProjectId"
          :sceneItem="checkedSceneItem"
          @closeDetail="closeDetail"
        ></sceneManagementDetail>
      </div> -->

    </div>

    <!-- 导入模板 -->
    <CompsStepTip5
      :zIndex="1000"
      v-if="isShowingImportTemplate === true"
      width="504"
      :bc_guid="leftMenuIdActivated"
      @oncancel="isShowingImportTemplate = false"
      @onok="importFileSuccess"
      title="导入数据"
    ></CompsStepTip5>

    <!--弹窗：添加子级、重命名本级、删除本级-->
    <div
      v-if="phaseInEditInfo.target && modalInfoForPhaseActions.visible"
      class="_css-pjsection-btnlist"
      :style="styleForPhaseActions"
      @click.stop
    >
      <div
        class="_css-pjsection-btn _css-pjsection-btn-add"
        @click.stop="showAddPhaseModal"
      >
        <div class="_css-pjsection-btnicon icon-interface-addnew"></div>
        <div class="_css-pjsection-btntext">添加</div>
      </div>
      <div
        class="_css-pjsection-btn _css-pjsection-btn-rename"
        @click.stop="showRenamePhaseModal"
      >
        <div class="_css-pjsection-btnicon icon-interface-edit-model"></div>
        <div class="_css-pjsection-btntext">重命名</div>
      </div>
      <div
        class="_css-pjsection-btn _css-pjsection-btn-delete"
        @click.stop="onDelPhaseClick"
      >
        <div class="_css-pjsection-btnicon icon-interface-model-delete"></div>
        <div class="_css-pjsection-btntext">删除</div>
      </div>
    </div>
    <CompsSingleField
      v-if="modalInfoForPhaseActions.isPhaseModalVisible"
      placeholder="场景管理"
      inputicon="icon-none"
      :zIndex="1000"
      :title="
        modalInfoForPhaseActions.type == 'add'
          ? '新建场景管理'
          : `重命场景管理`
      "
      :inittext="phaseNameForModal"
      @onok="onPhaseModalOk"
      @oncancel="onPhaseModalCancel"
      @oninput="onPhaseModalInput"
    ></CompsSingleField>

  </div>
</template>
<script>
// import sceneManagementDetail from "./sceneManagementDetail.vue";

import CompsStepTip5 from "../../CompsCommon/CompsStepTip5.vue";
import CompsSingleField from "@/components/CompsCommon/CompsSingleField"; // 添加场景文件夹
const regForOnlyEmptyStr = /^\s*$/i;


export default {
  name: "sceneManagement",
  data() {
    return {
      modelManageTreeKey: "", // 用于el-tree高亮节点/编辑节点的key
      userMenuList:[],
      modalInfoForPhaseActions: {
        top: 0, //包含添加、重命名、删除按钮的弹窗的上边距
        left: 0, //包含添加、重命名、删除按钮的做边距
        visible: false, // 包含添加、重命名、删除按钮的弹窗是否可见
        type: "", // 有效值：add rename del
        isPhaseModalVisible: false, // 按钮对应的阶段修改弹窗是否可见
        phaseNameFromModal: "" // 弹窗内部用户正在编辑的名字
      }, // 添加子级、重命名本级、删除本级的相关信息
      phaseInEditInfo: {
        target: null //正在操作的树节点
      }, // 当前正在编辑的阶段信息
      clickTreeMenuID: "", // 点击的树菜单ID
      isTreeCollapsed: false,
      elTreeProps: {
        children: "Children",
        label: "MenuName"
      },
      isShowingImportTemplate: false,
      leftMenuIdActivated:'', // 左侧菜单激活的ID
      scenePhaseId: '', // 场景阶段ID
      headerTitle: '',
      hasEditAuth: false,
      hasDelAuth: false,
      userID: "", //用户ID
      ProjectId: "", // 项目ID
      searchName: "", // 搜索
      sceneList: [],
      stateTitle: "", // 添加场景or编辑场景
      sceneSetState: "", // edit/upload
      uploadSceneDialog: false, // 编辑和新增的dialog
      checkedScene: false, //
      checkedSceneID: "",
      addForm: {
        MenuId: "",
        SceneId: "",
        SceneName: "",
        SceneLogo: "",
        Comment: "",
        SceneDataJson: "",
        SceneEventDataJson: "",
        SceneEntityDataJson: "",
        CreateUserName: '',
				CreateTime: '',
      },
      addRules: {
        SceneName: [
          { required: true, message: "场景名称不能为空", trigger: "change" },
        ],
      },
      errorMsg: "",
      headImg: "",
    };
  },
  watch: {
    leftMenuActivated:{
      handler(cv,ov) {
        console.log("watch-leftMenuActivated", cv, ov);
        if (cv) {
          if (!ov) {
           /* // console.log("watch-leftMenuActivated第一次");
            this.UrlPhase = this.$route.params.Phase;
            clearTimeout(this.timerForModelConverting);
            this.timerForModelConverting = null;
            this.resetRelatedConvertingList();
            this.closeAllDialog();
            this.fileListToUpload = [];
            setTimeout(() => {
              let targetPhase = this.$route.params.Phase
              targetPhase = targetPhase === 'allmodel_phases' ? 'allmodel_phase' : targetPhase
              this.extendHighlightModelManageTree(targetPhase,true)
            }, 20);*/
          }
        }
      },
      immediately: true
    },
  },
  computed: {
    phaseNameForModal() {
      const target = this.phaseInEditInfo.target;
      if (target) {
        if (this.modalInfoForPhaseActions.type === "add") {
          return (
            target.data.MenuName +
            "-" +
            ((target.data.Children || []).length + 1)
          );
        } else {
          return target.data.MenuName;
        }
      } else {
        return "";
      }
    },
    styleForPhaseActions() {
      return {
        top: this.modalInfoForPhaseActions.top + "px",
        left: this.modalInfoForPhaseActions.left + "px"
      };
    },
    /** * 获取场景菜单列表
     */
    sceneMenu() {
      if (this.userMenuList.length > 0) {
        return this.userMenuList.find(
          item => item.MenuCode === "CJGL_1" && item.MenuLevel === 1
        );
      }
    },
    sceneMenuActivated() {
      if (!this.sceneMenu || !this.leftMenuIdActivated) return;
      return this.sceneMenu.Children.find(
        item => item.Id === this.leftMenuIdActivated
      );
    },
    treeData() {
      return this.sceneMenuActivated ? [this.sceneMenuActivated] : null;
    },
    parentId() {
      const target = this.phaseInEditInfo.target;
      if (target) {
        return target.data.Id;
      } else {
        return 0;
      }
    },
  },
  components: {
    CompsStepTip5,
    CompsSingleField
    // sceneManagementDetail,
  },
  created() {
    this.$Bus.$on("ChildMenuIdActivatedChanged", this.onLeftMenuChange);
  },
  mounted() {
    this.headerTitle = '场景管理';
    this.userID = this.$staticmethod.Get("UserId");
    this.ProjectId = this.$staticmethod._Get("organizeId");

    // 检查是否有存储的菜单激活状态，确保第一次点击时也能正确处理
    const storedMenuId = sessionStorage.getItem('ChildMenuIdActivatedChanged');
    if (storedMenuId) {
      try {
        const menuData = JSON.parse(storedMenuId);
        this.scenePhaseId = menuData.MenuCode;
        if (menuData && (menuData.MenuType === 6 || menuData.MenuCode === 'SCENE')) {
          this.onLeftMenuChange(menuData);
        }
      } catch (e) {
        console.warn('解析存储的菜单数据失败:', e);
      }
    }
    // 获取所有列表
    this.getSceneList();

/*    if (this.scenePhaseId){
      // 点击的是分类，先获取树
      this.sceneList = []
      sessionStorage.setItem("scenePhaseId", this.scenePhaseId);
    }else {
      // 获取所有列表
      this.getSceneList();
    }*/
  },
  beforeDestroy() {
    // 移除事件监听器
    this.$Bus.$off("ChildMenuIdActivatedChanged", this.onLeftMenuChange);
    sessionStorage.setItem("sceneTreeKey", "");
  },
  methods: {
    /**
     * 删除模型阶段菜单
     * @param {string} indexId - 要删除的菜单ID
     */
    getNewDeleteModelPhase(indexId) {
      // 在删除前获取被删除节点的父级ID
      const deletedNodeParentId = this.phaseInEditInfo.target.parent.data.Id || null;
      this.$axios
        .post(
          `${this.$urlPool.DeleteMenu}?id=${indexId}&token=${this.$staticmethod.Get('Token')}`
        )
        .then(res => {
          if (res.data.Ret == 1) {
            this.setMenuTreeKey(true);
            this.$message.success(res.data.Msg);
            this.clickTreeMenuID = deletedNodeParentId
            this.getSceneList()
            // 删除成功后，可以使用被删除节点的父级ID
            console.log('被删除节点的父级ID:', deletedNodeParentId);

            this.setUserMenuTree().then(() => {
              this.highlightTree(
                this.modelManageTreeKey,
                true
              );
            });
          } else {
            this.$message.error(res.data.Msg);
          }
        })
        .catch(res => {});
    },

    // 增加模型阶段，实际是增加菜单
    getNewAddModelPhase(params) {
      let _this = this;
      _this.$axios
        .post(`${_this.$urlPool.CreateMenu}?token=${_this.$staticmethod.Get('Token')}`, params)
        .then(res => {
          if (res.data.Ret == 1) {
            this.modalInfoForPhaseActions.isPhaseModalVisible = false;
            this.modalInfoForPhaseActions.visible = false;
            _this.$message.success("添加场景文件夹成功");
            sessionStorage.setItem("sceneTreeKey", params.BusinessCode); // 展开到新添加的节点
            // 接口返回ID
            this.clickTreeMenuID = res.data.Data;
            this.getSceneList();
            this.setUserMenuTree().then(() => {
              this.highlightTree(params.BusinessCode, true);
            });
          } else {
            if (res.data.Msg == "当前菜单已存在") {
              this.$message.error("当前场景文件夹已存在");
            } else {
              this.$message.error(res.data.Msg);
            }
          }
        })
        .catch(err => {});
    },
    convertDateToStr(dt) {
      var year = dt.getFullYear();
      var month = dt.getMonth() + 1;
      var date = dt.getDate();
      var hour = dt.getHours();
      var minute = dt.getMinutes();
      var second = dt.getSeconds();
      var ms = dt.getMilliseconds();
      return (
        year +
        "" +
        month +
        "" +
        date +
        "" +
        hour +
        "" +
        minute +
        "" +
        second +
        "" +
        ms
      );
    },
    // 编辑模型阶段，实际是编辑菜单
    getNewEditModelPhase() {
      let _this = this;
      const params = JSON.parse(
        JSON.stringify(this.phaseInEditInfo.target.data)
      );
      params.MenuName = params.Description = this.modalInfoForPhaseActions.phaseNameFromModal;
      this.$axios
        .post(`${this.$urlPool.ModifyMenu}?token=${ this.$staticmethod.Get('Token')}`, params)
        .then(res => {
          if (res.data.Ret == 1) {
            this.modalInfoForPhaseActions.isPhaseModalVisible = false;
            this.modalInfoForPhaseActions.visible = false;
            _this.$message.success("修改成功");
            sessionStorage.setItem("sceneTreeKey", params.BusinessCode); // 展开到被重命名的节点
            this.setUserMenuTree().then(() => {
              this.extendHighlightModelManageTree(params.BusinessCode, true);
            });
            // 这里需要判断是否修改的根节点
              _this.$emit("sceneRefresh");
            // 创建成功后清空form表单
          } else {
            if (res.data.Msg == "当前菜单已存在") {
              this.$message.error("当前场景文件夹已存在");
            } else {
              this.$message.error(res.data.Msg);
            }
          }
        })
        .catch(err => {});
    },
    onPhaseModalInput(data) {
      this.modalInfoForPhaseActions.phaseNameFromModal = data;
    },
    onPhaseModalCancel() {
      this.modalInfoForPhaseActions.visible = false; // 关闭包含添加、重命名、删除按钮的弹窗
      this.modalInfoForPhaseActions.isPhaseModalVisible = false; // 关闭添加、重命名弹窗
    },
    // 保存新增的字典项或修改的字典项
    onPhaseModalOk() {
      let newPhaseName = this.modalInfoForPhaseActions.phaseNameFromModal;

      // 调用接口保存项目阶段
      if (!newPhaseName || regForOnlyEmptyStr.test(newPhaseName)) {
        this.$message.error("场景管理文件名称不能为空");
        return;
      }

      // 添加验证：名称不能为全部模型！
      newPhaseName = newPhaseName.replace(regForEmptyStr, "");
      if (newPhaseName === "全部场景") {
        this.$message.error("场景管理文件名称不能为“全部场景”");
        return;
      }
      this.modalInfoForPhaseActions.phaseNameFromModal = newPhaseName;

      // 判断是新增或编辑
      if (this.modalInfoForPhaseActions.type == "add") {
        const newval = "s" + this.convertDateToStr(new Date());
        const _data = {
          MenuName: this.modalInfoForPhaseActions.phaseNameFromModal,
          OrganizeId: this.$staticmethod._Get("organizeId"),
          Description: this.modalInfoForPhaseActions.phaseNameFromModal,
          ParentId: this.parentId,
          RoutePath: `/#/Home/ProjectBoot/sceneManagement/@OrganizeId/@Token/${newval}`,
          ComponentPath: "",
          NextMenuId: "",
          MenuType: 6, //
          MenuCode: "SCENE",
          MenuIcon: "",
          BusinessCode: newval,
          RequiresAuth: true,
          Buttons: this.menuSystemButton
        };
        this.getNewAddModelPhase(_data);
      } else {
        // console.log('编辑',this.leftMenuActivated,this.phaseInEditInfo.target)
        this.getNewEditModelPhase();
      }
    },
    // isFromDelete标记调用是否来自于删除操作
    getIdFromPhaseInEdit(isFromDelete = false) {
      const target = this.phaseInEditInfo.target;
      if (target) {
        if (isFromDelete) {
          return target.parent.data.BusinessCode; // 删除了某个层级则自动展开到被删除层级的上一级
        } else {
          return target.data.BusinessCode;
        }
      }
    },
    // isFromDelete标记调用是否来自于删除操作
    setMenuTreeKey(isFromDelete = false) {
      const modelManageTreeKey = this.getIdFromPhaseInEdit(isFromDelete);
      if (modelManageTreeKey) {
        this.modelManageTreeKey = modelManageTreeKey;
        sessionStorage.setItem("sceneTreeKey", modelManageTreeKey); //右侧el-tree高亮节点/编辑节点的key
      }
    },
    // 显示添加阶段的弹窗
    showAddPhaseModal() {
      // this.modalInfoForPhaseActions.visible = false // 关闭包含添加、重命名、删除按钮的弹窗
      this.modalInfoForPhaseActions.type = "add";
      this.modalInfoForPhaseActions.phaseNameFromModal = this.phaseNameForModal;
      this.modalInfoForPhaseActions.isPhaseModalVisible = true;
    },
    // 显示重命名阶段的弹窗
    showRenamePhaseModal() {
      // this.modalInfoForPhaseActions.visible = false // 关闭包含添加、重命名、删除按钮的弹窗
      this.modalInfoForPhaseActions.type = "reanme";
      this.modalInfoForPhaseActions.phaseNameFromModal = this.phaseNameForModal;
      this.modalInfoForPhaseActions.isPhaseModalVisible = true;
    },
    // 响应删除阶段点击事件
    onDelPhaseClick() {
        this.$confirm("确认删除该场景管理文件？", "操作确认", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            this.modalInfoForPhaseActions.visible = false;
            this.setMenuTreeKey(true);
            this.getNewDeleteModelPhase(this.phaseInEditInfo.target.data.Id);
          })
          .catch(() => {});
    },
    /**
     * 导入成功
     */
    importFileSuccess(){
      this.isShowingImportTemplate = false
      this.setUserMenuTree();
    },
    // 处理树节点右侧按钮点击事件
    onExtraBtnClick(node, evt) {
      // console.log('onExtraBtnClick',node,evt);
      this.phaseInEditInfo.target = node;
      this.modalInfoForPhaseActions.visible = true;
      this.setMenuTreeKey();
      let top, left;
      if (evt.target) {
        const boundingRect = evt.target.getBoundingClientRect();
        top = boundingRect.top + boundingRect.height;
        left = boundingRect.left + boundingRect.width;
      } else {
        top = evt.clientY;
        left = evt.clientX;
      }
      this.modalInfoForPhaseActions.top = top;
      this.modalInfoForPhaseActions.left = left;

      // 阻止事件冒泡，防止立即触发 closeAll
      evt.stopPropagation();
    },
    // 处理树节点点击事件
    onElTreeNodeClick(data, node) {
      console.log("onElTreeNodeClick", data, node);
      this.clickTreeMenuID = data.Id;
      this.modelManageTreeKey = data.BusinessCode
      this.getSceneList();
      // this.leftMenuIdActivated = node.Id
     /* this.UrlPhase = data.BusinessCode;
      clearTimeout(this.timerForModelConverting);
      this.timerForModelConverting = null;
      this.resetRelatedConvertingList();
      this.closeAllDialog();
      this.fileListToUpload = [];
      sessionStorage.setItem("menuListHasAuth", JSON.stringify(data.Buttons));
      this.phaseInEditInfo.target = node;
      this.modalInfoForPhaseActions.visible = false;
      this.setMenuTreeKey();
      // console.log(window.bim_config.hasRouterFile ,'====', data.RoutePath)
      const _urltarget = (window.bim_config.hasRouterFile + data.RoutePath)
        .replace("@Token", this.tokenValue)
        .replace("@OrganizeId", this.VaultID);
      window.location.href = _urltarget;
      this.getAllListFeatures();*/
      // this.$router.push({name: 'Model',params: {
      //   organizeId: this.VaultID,
      //   Token: this.tokenValue,
      //   Phase: data.BusinessCode
      // }})
    },
    /**
     * 高亮树节点
     * @param modelManageTreeKey
     * @param changeUrl
     */
    highlightTree(modelManageTreeKey, changeUrl = false) {
      if(modelManageTreeKey === 'CJGL_1') {
        // this.scenePhaseId = modelManageTreeKey;
      } else {
        // this.UrlPhase = modelManageTreeKey
        setTimeout(() => {
          if (!this.$refs.elTree) return;
          if (modelManageTreeKey) {
            this.modelManageTreeKey = modelManageTreeKey;
            setTimeout(() => {
              this.$nextTick(() => {
                this.$refs.elTree.setCurrentKey(modelManageTreeKey);
              })
            }, 20);
          }
        }, 20);
      }
    },
    // 设置userMenuList
    async setUserMenuTree() {
      const res = await this.$axios
        .get(
          `${this.$urlPool.GetUserMenuTree}?token=${
            this.$staticmethod.Get('Token')
          }&organizeId=${this.ProjectId}&parentId=0`
        )
        .catch(() => {});
      if (res && res.data && res.data.Ret === 1) {
        this.userMenuList = res.data.Data;
      } else {
        this.userMenuList = [];
      }
    },
    // 响应左侧菜单点击事件
    onLeftMenuChange(menuId) {
      this.modalInfoForPhaseActions.visible = false;
      console.log('menuId',menuId);
      this.leftMenuIdActivated = menuId.Id;
      this.clickTreeMenuID = menuId.Id;
      this.scenePhaseId = menuId.MenuCode;
      this.modelManageTreeKey = menuId.BusinessCode;
      console.log('左侧菜单点击事件', this.leftMenuIdActivated);
      console.log('左侧菜单点击事件',menuId);
      this.setUserMenuTree()
      // 更新sessionStorage中的菜单数据
      sessionStorage.setItem('ChildMenuIdActivatedChanged', JSON.stringify(menuId));
      this.getSceneList()
      this.$nextTick(() => {
        this.clickTreeMenuID = menuId.Id
      })
     /* this.$nextTick(() => {
        if(!this.leftMenuActivated) return
        // 2、筛选出模型管理的菜单A modelManageMenu

        // 3、根据menuId从A中筛选出树的根节点 leftMenuActivated

        // this.extendHighlightModelManageTree(this.leftMenuActivated.BusinessCode,true)
        // 4、执行点击节点的逻辑(高亮节点、改变url、加载该阶段下模型列表)
      })*/
    },
    SearchList(e, jd) {
      if (e.keyCode == 13 || jd == 1) {
        this.getSceneList();
      }
    },
    // 加载list
    async getSceneList() {
      let params = {
        MenuId: this.scenePhaseId === 'CJGL' ? '' : this.clickTreeMenuID,
        Key: this.searchName,
        ProjectId: this.ProjectId,
      }
      const respage = await this.$api.getSceneList(params)
      if(respage.Ret == 1){
        this.sceneList = respage.Data;
        this.highlightTree(this.modelManageTreeKey,true)
      }
    },
    getItemImg(item) {
      if (item.SceneLogo === "") {
        return require("../../../assets/images/products.png");
      } else {
        return item.SceneLogo;
      }
    },
    /**
     * 点击菜单
     * @param {Object} item - 场景项目对象
     */
    handelClickUnitList(item) {
      this.checkedScene = true;
      this.checkedSceneID = item.SceneId;
      // 关闭其他弹窗
      this.modalInfoForPhaseActions.visible = false;
    },
    // 添加场景
    uploadMvFile() {
/*      if(!this.hasEditAuth) {
        this.$message.warning("没有编辑权限")
        return
      }*/
      this.stateTitle = "添加场景";
      this.sceneSetState = "upload";
      this.uploadSceneDialog = true;
    },
    changeFile(file, fileList) {
      var _this = this;
      var reader = new FileReader();
      const isLt2M = file.size / 1024 / 1024 < 3
      if (!isLt2M) {
        this.$refs.uploadLogo.clearFiles();
        this.$message({
          message: '上传文件大小不能超过 3MB!',
          type: 'warning'
        })
        return false
      }
      reader.readAsDataURL(file.raw);
      reader.onload = function (e) {
        _this.headImg = reader.result;
        _this.addForm.SceneLogo = reader.result;
      };
    },
    saveUploadScene() {
      if (this.sceneSetState == "upload") {
        this.addSceneUpload();
      } else if (this.sceneSetState == "edit") {
        this.editScene();
      }
    },
    // 添加场景
    async addSceneUpload() {
      this.addForm.MenuId = this.clickTreeMenuID
      let _data = {
        ProjectId: this.ProjectId,
        ...this.addForm,
      };
      const res = await this.$api.postsceneadd(this.userID,_data)

      if(res.Ret == 1){
        this.$message.success(res.Msg);
      }
      await this.getSceneList();
      this.cancelScene();

    },
    async editScene() {
      // let this = this;
      let _data = {
        ProjectId: this.ProjectId,
        ...this.addForm,
      };

      const res = await this.$api.postsceneupdate(this.userID,_data)
      if(res.Ret == 1){
        this.$message.success(res.Msg);
      }
      this.getSceneList();
      this.cancelScene();

      // _this.$axios
      //   .post(
      //     `${window.bim_config.webserverurl}/api/v1/scene/update?userId=${this.userID}&Token=${this.$staticmethod.Get('Token')}`,
      //     _data
      //   )
      //   .then((x) => {
      //     if (x.data.StatusCode == 200) {
      //       _this.getSceneList();
      //       _this.cancelScene();
      //     } else {
      //       console.log(x);
      //     }
      //     _this.$message.success(x.data.Message);
      //   })
      //   .catch((x) => {
      //     console.log(x);
      //   });
      // this.cancelScene();
    },
    closeAll(){
      this.checkedScene = false;
      this.modalInfoForPhaseActions.visible = false;
    },
    cancelScene() {
      this.uploadSceneDialog = false;
      this.$refs.uploadLogo.clearFiles();
      this.$refs.form.resetFields();
      this.checkedScene = false;
      this.checkedSceneID = "";
      this.stateTitle = "";
      this.sceneSetState = "";
      this.addForm = {
        SceneId: "",
        SceneName: "",
        SceneLogo: "",
        Comment: "",
        SceneDataJson: "",
        SceneEventDataJson: "",
        SceneEntityDataJson: "",
      };
      this.headImg = "";
    },
    getSceneDetail(item) {
      this.stateTitle = "编辑场景";
      this.sceneSetState = "edit";
      this.getDetailItem(item,'editDetail');
    },
    getSceneContent(item){
      this.getDetailItem(item,'editScene')
    },
    getDetailItem(item,type){
      let _this = this;

      if(type=="editDetail"){
        // 场景详情修改
        _this.detailFun(item)
      }else{
        // 查看场景或者编辑场景
        let editScene = '0'
        type=='editScene' ? editScene = '0' : editScene = '1'
        console.log(type,'点击场景  查看还是编辑');
        _this.$router.push(
          {
            path:`/Home/ProjectBoot/sceneManagementDetail`,
            query: { ProjectID: _this.ProjectId, SceneId: item.SceneId, userID: _this.userID,sceneEdit: editScene}
          }
        )
      }
    },
    async detailFun(item){
      let _data = {
        projectID: this.ProjectId,
        SceneId: item.SceneId,
        userId: this.userID,
      }
      const res = await this.$api.getscenedetail(_data)
      if(res.Ret == 1){
        this.addForm = res.Data;
        this.headImg = res.Data.SceneLogo;
        this.uploadSceneDialog = true;
      }

    },
    // 删除场景
    deleteScene(item) {
      let _this = this;
      _this
        .$confirm(
          "删除后,所有相关数据将不可恢复,确定要删除此场景?",
          "确定删除当前场景?",
          {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
          }
        )
        .then(() => {
          this.delSceneFun(item)
        })
        .catch((err) => {
          console.log(err);
        });
    },
    async delSceneFun(item){

      let _data = {
        ProjectId: this.ProjectId,
        SceneId: item.SceneId,
      }
      const res = await this.$api.postscenedelete(this.userID,_data)
      if(res.Ret == 1){
        this.$message.success('删除成功')
      }
      await this.getSceneList();

    },
    handelClickSceneToDetail(item) {
      this.getDetailItem(item,'detail')
    },
    formDate(val) {
      if (val.length >= "2019-09-16 11:14".length) {
        return val.substr(0, "2019-09-16 11:14".length);
      }
    },
    _stopPropagation(ev) {
      ev && ev.stopPropagation && ev.stopPropagation();
    },
  },
};
</script>
<style lang="scss" scoped>
.scene-content {
  text-align: left;
  height: 100%;
  overflow: hidden;
  position: relative;
  display: flex;
  flex-direction: column;
}

.scene-bottom-content{
  background-color: #F0F2F5;
  border-radius: 2px;
  height: calc(100% - 40px);
  .scene-wrapper{
    display: flex;
    height: 100%;
    .tree-wrapper {
      position: relative;
      z-index: 10;
      flex: 1 1 200px;
      min-width: 200px;
      max-width: 200px;
      height: calc(100% - 30px);
      background-color: #fff;
      transition: transform 0.1s ease-in-out;
      .col1-title {
        position: relative;
        display: flex;
        margin-bottom: 8px;
        padding-top: 12px;
        padding-left: 16px;
        justify-content: flex-start;
        align-items: center;
        .label {
          margin-left: 12px;
          margin-bottom: -3px;
        }
        .import{
          position: absolute;
          font-weight: bold;
          right: 10px;
          margin-left: auto;
          font-size: 16px;
          color: #1890ff;
          cursor: pointer;
        }
      }
      .el-tree-cus {
        position: relative;
        padding-left: 16px;
        max-height: calc(100% - 30px);
        overflow-y: auto;
        overflow-x: auto; // 添加横向滚动
        background-color: #fff;
        &.disabled {
          pointer-events: none;
          cursor: not-allowed;
          &::after {
            position: absolute;
            z-index: 20;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            content: "";
            background-color: rgba(0, 0, 0, 0.5);
            cursor: not-allowed;
          }
        }
        // 美化滚动条样式
        &::-webkit-scrollbar {
          width: 6px;
          height: 6px; // 横向滚动条高度
        }

        &::-webkit-scrollbar-thumb {
          background: #c0c4cc;
          border-radius: 3px;
        }

        &::-webkit-scrollbar-track {
          background: #f6f6f6;
        }
        /deep/ .el-tree {
          min-width: max-content; // 确保树容器有足够宽度
        }
        /deep/ .el-tree-node {
          min-width: max-content; // 确保节点内容不被截断
          white-space: nowrap; // 防止文本换行
          .el-tree-node__content {
            display: flex;
            justify-content: flex-start;
            align-items: center;
            padding-top: 8px;
            padding-bottom: 8px;
            min-width: max-content; // 确保内容容器有足够宽度
            &:hover {
              .el-tree-node-cus .extra {
                .extra-btn {
                  display: block;
                }
                .extra-label {
                  display: none;
                }
              }
            }
            .el-tree-node-cus {
              position: relative;
              display: flex;
              min-width: max-content; // 改为min-width确保内容不被截断
              justify-content: flex-start;
              align-items: center;
              .label {
                flex: 0 0 auto; // 改为不压缩
                white-space: nowrap; // 防止文本换行
                overflow: visible; // 允许内容显示
                text-overflow: unset; // 不截断文本
              }
              .extra {
                position: relative; // 改为相对定位
                z-index: 10;
                display: flex;
                justify-content: flex-end;
                align-items: center;
                width: 32px;
                height: 100%;
                margin-left: 8px; // 添加左边距
                flex-shrink: 0; // 防止被压缩
                .extra-btn {
                  display: none;
                }
                .extra-label {
                  margin-right: 5px;
                  display: block;
                }
              }
            }
          }
        }
      }

    }
    .center-wrapper{
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      .collapse-bar {
        width: 16px;
        height: 120px;
        background-image: url(../../../assets/images/p-in.png);
        background-size: 100%;
        background-repeat: no-repeat;
        z-index: 2;
        cursor: pointer;
      }
      .collapse-bar-out {
        width: 16px;
        height: 120px;
        background-image: url(../../../assets/images/p-out.png);
        background-size: 100%;
        background-repeat: no-repeat;
        z-index: 2;
        cursor: pointer;
      }

    }
    .scene-list {
      position: relative;
      margin-left: 25px;
      flex: 1;
      overflow-y: auto;
      // display: flex;
      // flex-wrap: wrap;
      height: 100%;
       display: flex;
       flex-direction: column;
       .scene-units-container {
         flex: 1;
         overflow-y: auto;
         padding: 0 16px;
       }
       .scene-unit:hover{
        box-shadow: 0px 4px 8px 0px rgba(0,55,115,0.2);
        border-radius: 4px;
        border: 1px solid #E8E8E8;
      }
      .scene-unit {
        width: 245px;
        height: 260px;
        box-shadow: 0 1px 1px rgba(0, 21, 41, 0.12);
        text-align: center;
        font-size: 0;
        float: left;
        cursor: pointer;
        position: relative;
        margin: 0 24px 25px 0;
        background: #FFFFFF;
        border-radius: 4px;
        border: 1px solid #E8E8E8;
        img {
          width: calc(100% - 12px);
          height: 178px;
          border: none;
          display: inline-block;
          margin-top: 6px;
          border-radius: 4px;
        }
        span {
          width: calc(100% - 30px);
          height: 38px;
          display: inline-block;
          line-height: 38px;
          position: relative;
          padding: 0 15px;
        }
        .scene-name {
          font-size: 16px;
          font-weight: 400;
          color: #292929;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
        .updateDate {
          font-size: 12px;
          font-weight: 400;
          color: #c4c4c4;
          position: relative;
        }
        .setting {
          position: absolute;
          width: 20px;
          height: 20px;
          right: 10px;
          top: 50%;
          transform: translateY(-50%);
          display: none;
        }
        .updateDate:hover .setting {
          display: block;
        }
        .icon-color{
          color: #1890ff;
        }
        .set-list {
          position: absolute;
          right: -85px;
          top: 10px;
          background: #fff;
          width: 100px;
          padding: 8px;
          text-align: left;
          color: #292929;
          display: none;
          border-radius: 2px;
          box-shadow: 0px 2px 4px 0px rgba(0,38,77,0.15);
          li {
            line-height: 32px;
            height: 32px;
            border-radius: 2px;
            font-size: 14px;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #666666;
            margin-bottom: 4px;
            padding: 2px 8px;
            i {
              vertical-align: middle;
              display: inline-block;
              width: 20px;
              height: 20px;
              background: url(../../../assets/images/s-re.png) no-repeat center center;
              background-size: 100% 100%;
              vertical-align: middle;
              margin-right: 8px;
            }
            .icon-s-fenxiang{
              background: url(../../../assets/images/s-fenxiang.png) no-repeat center center;
              background-size: 100% 100%;
            }
            .icon-s-del{
              background: url(../../../assets/images/s-del.png) no-repeat center center;
              background-size: 100% 100%;
            }
          }
          li:hover{
            background: #f8f8f8;
          }
        }
        .set-list-show {
          z-index: 99;
          display: block;
        }
      }
    }
  }
  .fileListToUpload {
    $minWidth: 560px;
    position: absolute;
    bottom: 20px;
    left: 224px;
    z-index: 900;
    min-width: $minWidth;
    width: 45%;
    padding-bottom: 10px;
    background-color: #fff;
    transition: left 0.1s ease-in-out;
    font-size: 14px;
    // box-shadow: 0 0 4px 4px #ccc;
    &.collapsed {
      left: 24px;
    }
    * {
      box-sizing: border-box;
    }
    .title {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 8px;
      .label {
        color: #000;
      }
      .iconClose {
        display: flex;
        justify-content: center;
        align-items: center;
        padding: 4px;
        cursor: pointer;
      }
    }
    .content {
      .header {
        width: 100%;
        height: 32px;
        padding: 8px;
        display: flex;
        border-bottom: 1px solid #ccc;
        justify-content: space-evenly;
        align-items: center;
        background-color: #fff;
        .item {
          text-align: center;
          &.name {
            flex: 1 1 auto;
          }
          &.size {
            flex: 0 0 auto;
            width: 150px;
          }
          &.status {
            flex: 0 0 auto;
            width: 90px;
          }
        }
      }
      .elScrollbarCus {
        /deep/ .el-scrollbar__bar.is-horizontal {
          display: none;
        }
        .files {
          max-height: 260px;
          .fileItem {
            display: flex;
            width: 100%;
            padding: 8px;
            justify-content: space-evenly;
            border-bottom: 1px solid #ccc;
            align-items: center;
            &:hover {
              background-color: #f0f2f5;
            }

            .item {
              text-align: center;
              &.name {
                flex: 1 1 auto;
                text-overflow: ellipsis;
                overflow: hidden;
                word-break: break-all;
                white-space: nowrap;
              }
              &.size {
                flex: 0 0 auto;
                width: 150px;
              }
              &.status {
                flex: 0 0 auto;
                width: 90px;
              }
            }
          }
        }
      }
    }
    ._css-progress-font-running {
      color: #1890ff;
    }

    .isError {
      color: #f00;
    }
  }

}
._css-pjsection-btnlist {
  position: fixed;
  z-index: 20;
  width: 120px;
  background: rgba(255, 255, 255, 1);
  box-shadow: 0px 1px 3px 0px rgba(0, 21, 41, 0.12);
  border-radius: 2px;
  box-sizing: border-box;
  padding-top: 4px;
  padding-bottom: 4px;
  transition: all 0.3s ease;
}

._css-pjsection-btnicon {
  width: 16px;
  height: 16px;
  font-size: 16px;
  margin-left: 24px;
  margin: 14px 18px 14px 14px;
}
._css-pjsection-btn {
  height: 40px;
  display: flex;
  align-items: center;
  // justify-content: space-around;
  color: rgba(0, 0, 0, 0.85);
  cursor: pointer;
  &:hover {
    background-color: rgba(0, 0, 0, 0.04);
  }
}
.up-scene-btn {
  height: 72px;
  padding: 0 16px;
  width: calc(100% - 32px);
  text-align: left;
  line-height: 72px;
  position: sticky;
  top: 0;
  background-color: #F0F2F5;
  z-index: 10;
  display: flex;
  justify-content: space-between;
  flex-shrink: 0;
  button {
    width: 106px;
    height: 40px;
    margin-top: 16px;
    background: #007AFF;
    border-radius: 2px;
    border: none;
    color: #fff;
    outline: none;
    padding: 0;
    // position: absolute;
    // left: 16px;
    // top: 16px;
  }
}
._css-addBtnCtn {
  display: flex;
  flex-direction: row-reverse;
  height: 64px;
  align-items: center;
  box-sizing: border-box;
  padding-right: 8px;
}
._css-add-scene /deep/ {
  margin: 20px;
  .el-form .el-form-item {
    margin-bottom: 21px;
  }
  .el-form .el-form-item__label {
    font-size: 14px;
    font-weight: 500;
    color: #132b4d;
    padding: 0;
    line-height: 22px;
  }
  .el-form .el-form-item__content {
    margin-top: 6px;
  }
  .read-only .el-input__inner {
    background-color: #f8f8f8 !important;
    border-color: #b8b8b8 !important;
    color: #666 !important;
  }
  .el-input__inner {
    border: 1px solid #dcdfe6;
  }
  .avatar-uploader {
    height: 84px;
  }
  .avatar-uploader .el-upload {
    border-radius: 2px;
    background: #fff;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    border: 1px solid #b8b8b8;
  }
  .avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 120px;
    height: 84px;
    line-height: 84px;
    text-align: center;
  }
  .avatar {
    width: 120px;
    height: 84px;
    display: block;
  }
  .el-upload__input {
    display: none;
  }
}
.scene-detail-iframe {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: #fff;
  width: 100%;
  height: 100%;
  z-index: 9999;
}
.txtSearch {
  display: inline-block;
  position: relative;
  margin-top: 20px;
  width: 240px;
  height: 32px;
  background-color: #ffffff;
  border: none;
  border-radius: 2px;
  border: 1px solid #D8D8D8;
  text-indent: 26px;
  font-size: 13px;
  outline: none;
}
.txtSearch i {
  position: absolute;
  top: 8px;
  right: 10px;
  width: 18px;
  height: 18px;
  text-align: left;
}
.txtSearch i::before {
  float: left;
  width: auto;
  height: auto;
  text-indent: 0px;
}
.txtSearch input {
  width: calc(100% - 14px);
  margin-left: 12px;
  float: left;
  height: calc(100% - 2px);
  border: none;
  outline: none;
  background-color: #ffffff;
}
.txtSearch input::-webkit-input-placeholder {
  font-size: 13px;
  font-family: PingFangSC-Regular;
  line-height: 32px;
}
.not-allow{
	cursor: not-allowed;
	color: #606266;
}
</style>
