<template>
  <div>
    <div class="_css-tabcontrolarea">
      <div class="_css-tabcontrolitem">工程总报</div>
    </div>
    <div class="container" :style="getContainsHeight()">
      <div class="ER_body">
        <div class="Engineering_header">
          <div class="custom_config_btn css-hover-btn" v-if="ER_edit === false"  @click="ER_cont_edit">
            编辑
          </div>
          <div  v-if="ER_edit === true">
            <div class="css-hoverable-container">
                <zbutton-function
                    :init_text="'取消'"
                    :init_bgcolor="'darkgray'"
                    :init_fontsize="14"
                    :debugmode="true"
                    :init_height="undefined"
                    :init_width="'76px'"
                    :style="'marginLeft:30px;float: right;'"
                    @onclick="ER_cont_edit_cancel"
                    >
                </zbutton-function>
            </div>
            <div class="css-hoverable-container">
                <zbutton-function
                    :init_text="'保存'"
                    :init_fontsize="14"
                    :debugmode="true"
                    :init_height="undefined"
                    :init_width="'76px'"
                    :style="'marginLeft:30px;float: right;'"
                    @onclick="ER_cont_edit_ok"
                    >
                </zbutton-function>
            </div>
            <div class="custom_config_btn css-hover-btn"  @click="custom_config_edit">
              自定义配置
            </div>
          </div>
        </div>
        <div class="ER_item">
          <div class="ER_cont">
              工程效果图：
          </div>
          <el-button v-if="!InputStatus">
            <label class="el-icon-upload el-icon--right" :disabled="InputStatus">
              上传图片
              <input type="file"
                     id="id_add_fileimage"
                     class="css-hide" accept="image/*" 
                     @change="EngineeringImageEdit($event)">
            </label>
          </el-button>
          <el-button v-else :disabled="InputStatus">
            <i class="el-icon-upload el-icon--right"></i>上传图片
          </el-button>
          <template v-if="EngineeringImage">
            <div class="project-info-img left">
              <template>
                <img :src="EngineeringImage" data-debug="25"  alt="" width="100%" height="100%">
              </template>
            </div>
            <el-button v-if="!InputStatus" style="marginLeft:20px">
              <label class="el-icon--right" @click="EngineeringImage =''">
                删除图片
              </label>
            </el-button>
          </template>
        </div>
        <div class="ER_item">
          <div class="ER_cont">
              驾驶舱选项：
          </div>
          <el-radio v-model="cockpit_radio" label="1" :disabled="InputStatus">效果图</el-radio>
          <el-radio v-model="cockpit_radio" label="2" :disabled="InputStatus">默认模型</el-radio>
        </div>
        <div class="ER_item">
          <div class="ER_cont">
              工程开工时间：
          </div>
          <el-date-picker
            v-model="start_date"
            type="date"
            placeholder="请选择日期"
            class="choose_date"
            format="yyyy 年 MM 月 dd 日"
            value-format="yyyy-MM-dd"
            :disabled="InputStatus">
          </el-date-picker>
        </div>
        <div class="ER_item">
          <div class="ER_cont">
              目标完成时间：
          </div>
          <el-date-picker
            v-model="Target_completion_date"
            type="date"
            placeholder="请选择日期"
            class="choose_date"
            format="yyyy 年 MM 月 dd 日"
            value-format="yyyy-MM-dd"
            :disabled="InputStatus">
          </el-date-picker>
        </div>
        <ul class="ER_custom_item">
            <li v-for="(item,index) in ER_custom_item" :key="item.ER_cont">
              <div class="ER_cont" :title="item.ER_cont">
                  {{item.ER_cont}}：
              </div>
              <el-input 
                v-model="ER_custom_item[index].ER_custom_input" 
                :disabled="InputStatus" 
                placeholder="请输入内容" 
                oninput="value=value.replace(/[^\d]/g,'')"
                class="ER_input"></el-input>
            </li>
        </ul>
      </div>
    </div>
    <CompsCockpitCustomEdit
      v-if="showCustomEdit"
      :CockpitCustomEditStatus = 0
      :ChooseER_date = null
      :ER_data = ER_data
      :organizeId = organizeId
      @CloseERCustomEdit="IsDialogCovery=false,showCustomEdit=false"
      @RefreshData="GetNewER()"
      >
    </CompsCockpitCustomEdit>
    <div v-show="IsDialogCovery" class="Covery"></div>
    <!-- 
        @Close='CloseCustomEdit' -->
  </div> 
</template>
<script>
import CompsCockpitCustomEdit from '@/components/CompsCockpitCustom/CompsCockpitCustomEdit'
export default {
    name:'EngineeringReport',
    components:{
      CompsCockpitCustomEdit
    },
    data(){
      return {
        organizeId:'',
        cockpit_radio: '1',//驾驶舱选项：1为效果图，2为默认模型
        EngineeringImage:'',//项目缩略图
        start_date:'',//工程开工时间：
        Target_completion_date:'', //目标完成时间：
        ER_edit:false, //编辑状态
        InputStatus:true, //禁用状态
        showCustomEdit:false, //自定义配置页
        IsDialogCovery:false,//显示遮罩层
        ER_custom_item:[
          {
            ER_cont:"工程投资总金额",
            ER_custom_input:'',
          },
          {
            ER_cont:"累计完成投资",
            ER_custom_input:''
          },
          {
            ER_cont:"年度投资计划",
            ER_custom_input:''
          },
          {
            ER_cont:"年度投资完成",
            ER_custom_input:''
          },
          {
            ER_cont:"总合同额",
            ER_custom_input:''
          },
          {
            ER_cont:"已执行合同额",
            ER_custom_input:''
          },
        ],
        ER_data:undefined,
      }
    },
    methods:{
      ER_cont_edit(){
        this.ER_edit = true
        this.InputStatus = false
      },
      ER_cont_edit_cancel(){
        this.ER_edit = false
        this.InputStatus = true
      },
      custom_config_edit(){
        this.showCustomEdit = true
        this.IsDialogCovery = true
      },
      ER_cont_edit_ok(){
        let _this = this
        if(!this.start_date||!this.Target_completion_date){
            this.$message.warning('请填写完整的日期')
            return
        }
        // let ER_custom_itemFill=_this.ER_custom_item.every(item => item.ER_custom_input)
        // if(ER_custom_itemFill === true){
        //   let SumJSON = {
        //       EngineeringImage :this.EngineeringImage,
        //       cockpit_radio :this.cockpit_radio,
        //       start_date :this.start_date,
        //       Target_completion_date :this.Target_completion_date,
        //       ER_custom_item:this.ER_custom_item,
        //   }
        //   _this.$axios({
        //     method: "post",
        //     url: `${window.bim_config.webserverurl}/api/VisualView/VisualView/EditSumJSON`,
        //     data:_this.$qs.stringify({
        //       SumJSON:JSON.stringify(SumJSON),
        //       organizeId:this.organizeId
        //     })
        //   })
        //   .then(res => {
        //     _this.ER_cont_edit_cancel()
        //   })
        //   .catch(() => {
        //   });
        // }else{
        //   _this.$message.warning('请填写完整数据')
        // }
          let SumJSON = {
              EngineeringImage :this.EngineeringImage,
              cockpit_radio :this.cockpit_radio,
              start_date :this.start_date,
              Target_completion_date :this.Target_completion_date,
              ER_custom_item:this.ER_custom_item,
          }
          _this.$axios({
            method: "post",
            url: `${window.bim_config.webserverurl}/api/VisualView/VisualView/EditSumJSON?Token=${this.$staticmethod.Get('Token')}`,
            data:_this.$qs.stringify({
              SumJSON:JSON.stringify(SumJSON),
              organizeId:this.organizeId
            })
          })
          .then(res => {
            _this.ER_cont_edit_cancel()
          })
          .catch(() => {
          });
      },
      EngineeringImageEdit(event){
          let _this = this;
          let file = event.target.files[0];
          let fr = new FileReader();
          fr.readAsDataURL(file);
          // fr.onloadend = function(e) {
          //   _this.EngineeringImage = e.target.result;
          // };
          // 添加图片后，直接拿到图片，访问 UploadImage 接口，获取 bf_guid, bf_md5 和 bf_path
          var dominputfile = document.getElementById("id_add_fileimage");
          if (dominputfile.files.length == 0) {
            return;
          }
          var File = dominputfile.files[0];
          var _Token = _this.$staticmethod.Get("Token");
          var fd = new FormData();
          fd.append("FormFile", File); 
          fd.append("IssueId", '');
          var config = {
            headers: {
                "Content-Type": "multipart/form-data"
            }
          };
          _this.$axios
          .post(
            `${this.$issueBaseUrl.UploadImage}?token=${ _this.$staticmethod.Get("Token")}`,
            fd,
            config
          )
          .then(res =>{
            this.EngineeringImage = `${window.bim_config.webserverurl}/${res.data.Data.bf_path}`
          })
      },
      GetNewER(){
        this.$axios({
          method: "post",
          url: `${window.bim_config.webserverurl}/api/VisualView/VisualView/GetNewSum?Token=${this.$staticmethod.Get('Token')}`,
          data:this.$qs.stringify({
            organizeId:this.organizeId
          })
        })
        .then(res => {
          let data = res.data.Data
          if(Object.keys(data).length!=0){
            if(data.EngineeringImage&&data.EngineeringImage!==""){
              this.EngineeringImage = data.EngineeringImage;
            }
            this.cockpit_radio = data.cockpit_radio===''?"1":data.cockpit_radio;
            this.start_date = data.start_date;
            this.Target_completion_date = data.Target_completion_date;
            this.ER_custom_item = data.ER_custom_item;
            this.ER_data = {
              ER_custom_item:data.ER_custom_item,
            }
          }else{
            this.ER_custom_item.forEach(e => {
              e.ER_custom_input = ''
              e.Engineering_Id = 0
            });
            this.ER_data = undefined
          }
        })
        .catch(() => {
        });
      },
      getContainsHeight(){
        var _s = {};
        _s["height"] = (document.body.clientHeight-115)+ 'px';
        return _s;
      }
    },
    mounted(){
      this.organizeId = this.$staticmethod._Get("organizeId");
      this.GetNewER()
    },
}
</script>
<style scoped>
  .container {
    padding: 24px;
    overflow: auto;
  }
  ._css-tabcontrolarea {
    height: 54px;
    display: flex;
    align-items: center;
    flex: none;
    background-color: #fff;
    border-bottom: 1px solid rgba(0, 0, 0, 0.15);
  }
  ._css-tabcontrolitem {
    margin-left: 40px;
    width: 64px;
    height: 24px;
    line-height: 24px;
    font-size: 16px;
    font-weight: 400;
    color: rgba(0, 0, 0, 0.85);
    margin-left: 24px;
  }
  .ER_body{
    width: 100%;
    margin: 0 auto;
    background: #fff;
    border-radius: 2px;
    padding:25px 0;
  }
  .Engineering_header{
    padding: 0px 10% 60px 70px;
    border-bottom: 1px solid #ccc;
  }
  .container .custom_config_btn{
    background: linear-gradient(224deg,rgba(0,145,255,1) 0%,rgba(0,122,255,1) 100%);
    border-radius: 4px;
    color: #FFFFFF;
    font-size: 14px;
    padding: 9px 16px;
    cursor: pointer;
    float: right;
  }
  .ER_item{
    display: flex;
    align-items: center;
    font-weight: 500;
    color: rgba(0,0,0,0.45);
    padding: 10px 0;
  }
  .ER_cont{
    color:rgba(0,0,0,.85);
    width: 165px;
    text-align: right;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    cursor: pointer;
  }
  .el-button {
    padding: 6px 10px;
    border-radius: 2px;
  }
  .choose_date,.ER_input{
    border-radius: 4px;
    border: 1px solid #dcdfe6;
    background-color: #fff;
    cursor: pointer;
  }
  .ER_custom_item{
    padding-top: 20px;
  }
  .ER_custom_item li{
    display: flex;
    align-items: center;
  }
  .ER_custom_item li .ER_cont{
    padding: 20px 0;
  }
  .ER_custom_item li .ER_input{
    width: 300px;
  }
.project-info-img {
    cursor: pointer;
    width: 105px;
    height: 60px;
    border-radius: 4px;
    overflow: hidden;
    box-shadow: 0 0 5px #b0b0b0;
    margin-left: 20px;
  }  
.Covery{
  width:100%;height:100%;background-color:rgba(0,0,0,0.4);position: absolute;top:0px;left: 0px;z-index: 11;
}
</style>