<template>
  <div class="table-model-task">
    <div class="_css-model-close icon-suggested-close" @click="closeDialog"></div>
    <el-row>
      <el-col :span="8">
        <el-table
          :data="tableDatafinished"
          border
          fixed
          height="340"
          highlight-current-row
          style="width: 100%;">
          <el-table-column label="进度超前">
            <el-table-column
              prop="task_Name" 
              fixed
              label="任务名称">
              <template slot-scope="scope">
                  <el-tooltip placement="right" class="table-hover-tooltip" effect="light" popper-class="table-hover-setcss tooltip-model-hover">
                    <div slot="content" class="hover-text">
                      <p>任务名称：<span class="c333333">{{scope.row.task_Name}}</span></p>
                      <p>计划开始时间：<span class="c333333">{{scope.row.task_Start | filterTimer}}</span></p>
                      <p>计划结束时间：<span class="c333333">{{scope.row.task_FINISH | filterTimer}}</span></p>
                      <p>实际开始时间：<span class="c333333">{{scope.row.task_ACTUALSTART | filterTimer}}</span></p>
                      <p>实际结束时间：<span class="c333333">{{scope.row.task_ACTUALFINISH | filterTimer}}</span></p>
                      <p>填报时间：<span class="c333333">{{scope.row.Task.Progress_unittime}}</span></p>
                      <p>实际完成比例：<span class="c333333" v-if="scope.row.Task.PERCENTCOMPLETE_ > 0">{{scope.row.Task.PERCENTCOMPLETE_}}%</span></p>
                      <p>任务状态：<span class="c333333">{{scope.row.Task.Progress_state}}</span></p>
                    </div>
                    <div class="_css-costitem" ><span class="c333333">{{scope.row.task_Name}}</span></div>
                  </el-tooltip>
              </template>
            </el-table-column>
            <el-table-column
              prop="Progress_unittime"
              label="填报时间">
              <template slot-scope="scope">
                <div class="_css-costitem" ><span class="c333333">{{scope.row.Task.Progress_unittime}}</span></div>
              </template>
            </el-table-column>
            <el-table-column
              prop="PERCENTCOMPLETE_"
              label="完成比例"
              width="100">
              <template slot-scope="scope">
                <div class="_css-costitem" ><span class="c333333">{{scope.row.Task.PERCENTCOMPLETE_}}%</span></div>
              </template>
            </el-table-column>
          </el-table-column>
        </el-table>
      </el-col>
      <el-col :span="8">
        <el-table
          :data="tableDatainProgress"
          border
          height="340"
          highlight-current-row
          style="width: 100%;">
          <el-table-column label="进度正常">
            <el-table-column
              prop="task_Name" 
              fixed
              label="任务名称">
              <template slot-scope="scope">
                  <el-tooltip placement="right" class="table-hover-tooltip" effect="light" popper-class="table-hover-setcss tooltip-model-hover">
                    <div slot="content" class="hover-text">
                      <p>任务名称：<span class="c333333">{{scope.row.task_Name}}</span></p>
                      <p>计划开始时间：<span class="c333333">{{scope.row.task_Start | filterTimer}}</span></p>
                      <p>计划结束时间：<span class="c333333">{{scope.row.task_FINISH | filterTimer}}</span></p>
                      <p>实际开始时间：<span class="c333333">{{scope.row.task_ACTUALSTART | filterTimer}}</span></p>
                      <p>实际结束时间：<span class="c333333">{{scope.row.task_ACTUALFINISH | filterTimer}}</span></p>
                      <p>填报时间：<span class="c333333">{{scope.row.Task.Progress_unittime}}</span></p>
                      <p>实际完成比例：<span class="c333333" v-if="scope.row.Task.PERCENTCOMPLETE_ > 0">{{scope.row.Task.PERCENTCOMPLETE_}}%</span></p>
                      <p>任务状态：<span class="c333333">{{scope.row.Task.Progress_state}}</span></p>
                    </div>
                    <div class="_css-costitem" ><span class="c333333">{{scope.row.task_Name}}</span></div>
                  </el-tooltip>
              </template>
            </el-table-column>
            <el-table-column
              prop="Progress_unittime"
              label="填报时间">
              <template slot-scope="scope">
                <div class="_css-costitem" ><span class="c333333">{{scope.row.Task.Progress_unittime}}</span></div>
              </template>
            </el-table-column>
            <el-table-column
              prop="PERCENTCOMPLETE_"
              label="完成比例"
              width="100">
              <template slot-scope="scope">
                <div class="_css-costitem" ><span class="c333333">{{scope.row.Task.PERCENTCOMPLETE_}}%</span></div>
              </template>
            </el-table-column>
          </el-table-column>
        </el-table>
      </el-col>
      <el-col :span="8">
        <el-table
          :data="tableDatanotStarted"
          border
          fixed
          height="340"
          highlight-current-row
          style="width: 100%;">
          <el-table-column label="进度滞后" >
            <el-table-column
              prop="task_Name" 
              fixed
              label="任务名称">
              <template slot-scope="scope">
                  <el-tooltip placement="right" class="table-hover-tooltip" effect="light" popper-class="table-hover-setcss tooltip-model-hover">
                    <div slot="content" class="hover-text">
                      <p>任务名称：<span class="c333333">{{scope.row.task_Name}}</span></p>
                      <p>计划开始时间：<span class="c333333">{{scope.row.task_Start | filterTimer}}</span></p>
                      <p>计划结束时间：<span class="c333333">{{scope.row.task_FINISH | filterTimer}}</span></p>
                      <p>实际开始时间：<span class="c333333">{{scope.row.task_ACTUALSTART | filterTimer}}</span></p>
                      <p>实际结束时间：<span class="c333333">{{scope.row.task_ACTUALFINISH | filterTimer}}</span></p>
                      <p>填报时间：<span class="c333333">{{scope.row.Task.Progress_unittime}}</span></p>
                      <p>实际完成比例：<span class="c333333" v-if="scope.row.Task.PERCENTCOMPLETE_ > 0">{{scope.row.Task.PERCENTCOMPLETE_}}%</span></p>
                      <p>任务状态：<span class="c333333">{{scope.row.Task.Progress_state}}</span></p>
                    </div>
                    <div class="_css-costitem" ><span class="c333333">{{scope.row.task_Name}}</span></div>
                  </el-tooltip>
              </template>
            </el-table-column>
            <el-table-column
              prop="Progress_unittime"
              label="填报时间">
              <template slot-scope="scope">
                <div class="_css-costitem" ><span class="c333333">{{scope.row.Task.Progress_unittime}}</span></div>
              </template>
            </el-table-column>
            <el-table-column
              prop="PERCENTCOMPLETE_"
              label="完成比例"
              width="100">
              <template slot-scope="scope">
                <div class="_css-costitem" ><span class="c333333">{{scope.row.Task.PERCENTCOMPLETE_}}%</span></div>
              </template>
            </el-table-column>
          </el-table-column>
        </el-table>
      </el-col>
    </el-row>
  </div>
</template>
<script>
export default {
  name: 'ProgressModelTaskTable',
  data() {
    return {
      tableDatafinished: [],
      tableDatainProgress: [],
      tableDatanotStarted: [], 
    }
  },
  props: {
    finished: {
      type: Array,
      defaults: true
    },
    inProgress: {
      type: Array,
      defaults: true
    },
    notStarted: {
      type: Array,
      defaults: true
    }
  },
  filters: {
    filterTimer(inputtime) {
      if (!inputtime || inputtime.trim().length == 0) {
        return inputtime;
      }
      if (inputtime.length >= "2019-09-16T11:14".length) {
        return inputtime.substr(0, 10);
      }
    },
  },
  watch:{
    finished(val){
      this.tableDatafinished = val;
    },
    inProgress(val){
      this.tableDatainProgress = val;
    },
    notStarted(val){
      this.tableDatanotStarted = val;
    },
  },
  mounted(){
    this.tableDatafinished = this.finished;
    this.tableDatainProgress = this.inProgress;
    this.tableDatanotStarted = this.notStarted;
  },
  methods: { 
    closeDialog(){
      this.$emit('close')
    },
    DateDiff(sDate1, sDate2) {
      if (!sDate1 || sDate1.trim().length == 0) {
        return
      }
      if (!sDate2 || sDate2.trim().length == 0) {
        return
      }
      let _sDate1 = sDate1.substr(0,10)
      let _sDate2 = sDate2.substr(0,10)
      let oDate1, oDate2, iDays ,state;
      oDate1 = new Date(_sDate1).getTime();
      oDate2 = new Date(_sDate2).getTime();
      iDays = parseInt((oDate1 - oDate2) / 1000 / 60 / 60 / 24) //把相差的毫秒数转换为天数
      if(iDays > 0){
        state = '超前';
      }else if(iDays == 0){
        state = '正常';
      }else if(iDays < 0){
        state = '滞后';
      }
      
      return  [state,Math.abs(iDays)];
    },
  }
}
</script>
<style scoped>
._css-model-close{
    position: absolute;
    right: 5px;
    bottom: 270px;
    z-index:1000;
}
._css-model-close:hover{
    cursor: pointer;
}
.table-model-task{
  width: 100%;
}
.table-model-task /deep/ .el-table td{
  border-color: #D4D6D9;
}
.table-model-task /deep/ .el-table tr:first-child th{
  text-align: center;
  background: #ffffff;
}
.table-model-task /deep/ .el-table:before{
  height: 0;
}
.table-model-task /deep/ .el-table .cell{
  height: 38px;
}
._css-costitem{
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.table-model-task /deep/ .el-table--scrollable-y .el-table__body-wrapper::-webkit-scrollbar{
  width: 0px;
}
.hover-text{
  line-height: 22px;
}
.c333333{
  color: #333333;
}
.cF008000{
  color: #008000;
}
.cFff0000{
  color: #ff0000;
}
.table-model-task /deep/ .el-tooltip__popper.is-light{
  background: #ffffff;
}

</style>
<style>
.table-hover-setcss.is-light{
  max-width: 240px;
  background-color: #FFFFFF !important;
  font-size: 12px;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #606266;
  border: none;
}
.table-hover-setcss, 
.table-hover-setcss.css-no-triangle.is-light{
  background-color: #FFFFFF !important;
  box-shadow: 0px 0px 6px 0px rgba(0, 0, 0, 0.2);
  border-radius: 4px;
}

</style>