<template>
    <div class="Model_merge">
        <div class="title"><font class="l">合并模型</font><i class="icon-suggested-close close" @click="$emit('CloseMerge')"></i></div>
        <div class="con">
            <div class="form">
                <span class="txt"><i class="icon-interface-model"></i><font>名称</font><input type="text" v-model="MergeModelName" placeholder="请输入合并模型名称" /></span>
            </div>
            <div class="views">
                <ul class="unit" v-for="item in modelList" :key="item.ID">
                    <li class="view" v-for="viewItem in item.Views" :key="item.ID +'@'+ viewItem.ID" @click="SaveCurrentViews(item.ID,viewItem.ID,currentViewIDs.indexOf(item.ID +'@'+ viewItem.ID)!=-1)">
                        <span class="t" :title="item.CreateDate">{{item.Name}}</span>
                        <i :class="currentViewIDs.indexOf(item.ID +'@'+ viewItem.ID)!=-1?'icon-checkbox-Radio-Selected':'icon-checkbox-Radio'"></i>
                        <img :src="item.Thumbnail==''?require('../../../../assets/svgs_loadbyurl/model-Default.svg'):'data:image/png;base64,'+item.Thumbnail" />
                        <span class="viewName"><p>{{viewItem.Name}}</p></span>
                    </li>
                </ul>
            </div>
        </div>
        <div class="btns">
            <button class="white" @click="$emit('CloseMerge')">取消</button>
            <button class="blue" @click="saveMerge">合并</button>
        </div>
    </div>
</template>
<script>
export default {
    name:'ModelMerge',
    data(){
        return {
            modelList:[],
            currentViewIDs:[],
            MergeModelName:"",
        };
    },
    props:{
        ProjectID:{
            type:String,
            required:false,
        },
        AllModelIds:{
            type:String,
            required:false,
        },
        modelPhase:{
            type:String,
            required:false,
        },
        StopLoading:{
            type:Number,
            required:false,
        }
    },
    created(){
        
    },
    mounted(){

    },
    methods:{
        saveMerge()
        {
            var PostData=new FormData();
            var session_Account = this.$staticmethod.Get("Account");
            var MNStr="";
            if(this.currentViewIDs.length<2)
            {
                this.$message({type:'warning',message:'必须选择两个或以上视图进行合并'});
                return;
            }
            for(let i=0;i<this.currentViewIDs.length;i++)
            {
                MNStr+=this.currentViewIDs[i];
                if(i<this.currentViewIDs.length-1)
                MNStr+="^";
            }
            PostData.append("ProjectID",this.ProjectID);
            PostData.append("Model",JSON.stringify({
                "Name":this.MergeModelName,
                "IsMerge":true,
                "Creator":"超级管理员",
                "Model_N_View":MNStr,
                "Description":"",
                "ProjectID":this.ProjectID,
                "Phase":this.modelPhase,
                "Token":this.$staticmethod.Get('Token')
            }));
            // this.$emit("LoadingModel");
            this.$axios.post(this.$urlPool.base_ModelApi+this.$urlPool.CreateMergeModel,PostData).then(res=>{
                this.$emit("CloseMerge");
                this.$message({type:"success",message:"合并成功"});
                // this.$emit("UnLoadingModel");
                this.$emit("reloadModellist");
            }).catch(error=>{});
        },
        changeViewLineheight(ViewName)
        {
            var wordNum=0;
            var ZH_Regex=/^[\u4e00-\u9fa5]+$/;
            for(let i=0;i<ViewName.length;i++)
            {
                if(ZH_Regex.test(ViewName[i]))
                    wordNum+=2;
                else
                    wordNum++;
            }

        },
        SaveCurrentViews(ModelId,ViewId,Sign)
        {
            if(Sign==true){
                this.currentViewIDs.splice(this.currentViewIDs.indexOf(ModelId+"@"+ViewId),1);
            }
            else{
                this.currentViewIDs.push(ModelId+"@"+ViewId);
            }
        }
    },
    watch:{
        AllModelIds(val)
        {
            if(this.StopLoading==1)
                return;
            // debugger;
            var doing=0;
            var num=0;
            var ModelIds=this.AllModelIds.split(',');
            var _this=this;
            //this.$emit("LoadingModel");
            _this.modelList=[];
            var t=setInterval(function(){
                if(doing==0)
                {
                    doing=1;
                    if(num==ModelIds.length)
                    {
                        clearInterval(t);
                        //_this.$emit("UnLoadingModel");
                        return;
                    }
                    if(ModelIds[num]=='')
                    {
                        clearInterval(t);
                        //_this.$emit("UnLoadingModel");
                        return;
                    }
                    _this.$axios.get(_this.$urlPool.base_ModelApi+_this.$urlPool.GetModelInfo+"?ProjectID="+_this.ProjectID+"&ModelID="+ModelIds[num]).then(res=>{
                        console.log(num);
                        var ViewItems=res.data.Views;
                        _this.modelList.push({ID:res.data.ID,Name:res.data.Name,CreateDate:res.data.CreateTime,Thumbnail:res.data.Thumbnail,Views:ViewItems});
                        setTimeout(function(){
                            num++;
                            doing=0;
                        },50);
                    });
                }
            }, 100);
        }
    }
}
</script>
<style scoped>
ul{padding: 0px;margin: 0px;}li{padding: 0px;margin: 0px;}
.Model_merge{overflow: hidden;background-color: #fff;box-shadow:0px 13px 24px -17px rgba(11,41,62,0.8);border-radius:4px;font-family:PingFangSC-Medium;width:534px;height:592px;padding:0px 16px 16px 16px;}
.Model_merge .title{width:100%;height:48px;font-size:16px;color:#333;line-height:48px;text-align:left;font-weight:bold; position: relative;overflow: hidden;}
.Model_merge .title .close{width:18px;height:18px;position: absolute;right:0px;top:15px;display:block;}
.Model_merge .title .close::before{float:left;width:18px;height:18px;display:block;text-align:left;text-indent:0px;cursor: pointer;}
.Model_merge .con{width:100%;height:calc(100% - 38px - 48px - 22px);margin-top:20px;overflow:hidden;line-height:50px;}
.Model_merge .con .form{width:100%;height:auto;}
.Model_merge .con .form .txt{display:block;width:calc(100% - 24px - 16px);height: 50px;background-color:#fafafa;border-radius:2px;padding-left:24px;font-size:0px;position: relative;}
.Model_merge .con .form .txt font{display:inline-block;width:70px;height:100%;font-size:14px;text-align:left;text-indent:5px;font-weight: bold}
.Model_merge .con .form .txt input{display:inline-block;width:calc(100% - 70px);height:100%;font-size:14px;border:none;background-color:#fafafa;outline:none;}
.Model_merge .con .form .txt i{position: absolute;display: block;top:17px;left:8px;}
.Model_merge .con .views{width:calc(100% + 11px);height:368px;overflow-x:hidden;overflow-y:scroll;margin-top:24px;}
.Model_merge .con .views .unit{display:block;width:100%;height:auto;float:left;text-align:left;font-size:0px;}
.Model_merge .con .views .unit .view{display: inline-block;width:162px;height:172px;position: relative;border-radius:2px;box-shadow:0px 1px 1px 0px rgba(0,21,41,0.12);cursor:pointer;overflow:hidden;}
.Model_merge .con .views .unit .view.sel{border:1px solid rgba(24,144,255,1);width:160px;height:170px;border-radius:2px;}
.Model_merge .con .views .unit .view:hover{box-shadow:0px 13px 24px -17px rgba(11,41,62,0.8);}
.Model_merge .con .views .unit .view .t{width:calc(100% - 24px);height:32px;text-align:center;line-height:32px;background-color:rgba(0,0,0,0.45);position: absolute;display: block;color:#fff;font-size:12px;overflow:hidden;text-overflow:ellipsis;white-space:nowrap;padding:0px 12px 0px 12px;}
.Model_merge .con .views .unit .view .viewName{width:calc(100% - 24px);height:52px;font-size:13px;padding:0px 12px 0px 12px;background-color:#fafafa;display:block;line-height:52px;position:relative;}
.Model_merge .con .views .unit .view .viewName p{margin:0px;overflow: hidden;font-size:12px;line-height:27px;width:calc(100% - 24px);text-overflow: ellipsis;display: -webkit-box;-webkit-line-clamp: 2;-webkit-box-orient: vertical;position:absolute;top:50%;left:50%;transform: translate(-50%,-50%);}
.Model_merge .con .views .unit .view img{width:100%;height:calc(100% - 52px);display: block;}
.Model_merge .con .views .unit .view i{display:block;position:absolute;width:15px;height:15px;right:8px;bottom:59px;}

.Model_merge .btns{width:100%;height:38px;line-height:38px;text-align:right;}
.Model_merge .btns button{width:56px;height: 32px;border:none;outline: none;border-radius: 3px;cursor: pointer;font-size:12px; letter-spacing:1px;}
.Model_merge .btns button:hover{opacity:0.8;}
.Model_merge .btns .blue{background-color:#1890FF;color:#fff; }
.Model_merge .btns .white{background-color:transparent;color:#666;}
::-webkit-scrollbar{width:20px; height:8px;}
::-webkit-scrollbar-thumb{border-radius: 12px;border:6px solid transparent;box-shadow: 8px 0px 0px #A5ADB7 inset;}
::-webkit-scrollbar-thumb:hover {box-shadow: 8px 0px 0px #4A4A4A inset;} 
</style>

