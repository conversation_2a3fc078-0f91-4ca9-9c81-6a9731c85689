<template>
    <!--<div :class="setAnimationClass" class="modelView" :style="getStylemodelview()">-->
    <div :class="setAnimationClassData" class="modelView" :style="getStylemodelview()" ref="modelViewRef">
        <div class="title" v-show="materialsMarShow" v-modelDrag:[windowState]>
          <div v-if="modelName!='-1'">
            <span class="ModelName" v-if="modelName!='-1' && windowState !==-1"><i :class="{'mulcolor-logo':true}"></i>{{modelName}}</span>
            <ul class="fuc" v-if="this.SelModel.ID!=undefined && windowState !== -1">
                <li @click="openTargetDoc"><i class="icon-interface-folder"></i>关联文档 (<font :style="{textDecoration:'underline',color:'red'}">{{TargetDocNum}}</font>)</li>
                <li @click="openModelInfo" v-show="showOpenModeInfo"><i class="icon-suggested-warning_circle_fill"></i>模型详情</li>
            </ul>
            <div v-if="windowState==-1" class="Bitmap-logo"></div>
            <div class="btns">
              <ul class="" v-if="windowState !== -1">
                  <li @click="btncreateissue_click" :dragstylemx="true" v-if="hasIssueEdit() && this.SelModel.ID!=undefined"><i class="icon-interface-problem issue" title="问题追踪"></i></li>
                  <!-- <li @click="openTargetDoc"><i class="icon-interface-document targetDoc" title="该模型的关联文档"></i></li> -->
                  <li 
                  
                  @click="openShare" v-if="hasShare() && this.SelModel.ID!=undefined"><i class="icon-interface-share-fill share" title="模型分享"></i></li>
              </ul>
              <div class="window-option">
                <div class="inline-block" v-if="windowingOperation">
                  <span @click="changeWindowState(-1)" v-if="windowState !== -1"><i class="icon icon-suggested-minus" title="最小化"></i></span>
                  <span @click="changeWindowState(0)" v-if="windowState !== 0"><i class="icon icon-interface-drawing" title="窗口化"></i></span>
                  <span @click="changeWindowState(1)" v-if="windowState !== 1" >
                    <i class="icon icon-checkbox-Default" title="最大化"></i>
                  </span>
                </div>
                <span @click.stop="$emit('CloseModelView');showModelShare=false;">
                  <i class="icon icon-suggested-close tip" title="关闭"></i>
                </span>
              </div>
            </div>


          </div>
        </div>

        <!-- 创建问题对话框 -->
        <CompsCreateIssue v-if="extdata.showcreate" @oncancel="cancelcreate" @onok="createok" :hasImg="true" :outertop="300" @imgsrc="_imgsrc"></CompsCreateIssue>

        <div class="view">
            <div class="minimizeBitmap" v-if="windowState==-1"><img :src="'data:image/png;base64,'+SelModel.Thumbnail" alt=""></div>
            <iframe
            @load="iframeWpload"
            v-if="modelShow"
            :id="iframeid || 'iframe_biviewerid'"
            ref="ModelWindow" :style="{height:(modelName!='-1'?'calc(100% - 48px)':'calc(100% - 28px)'),marginTop:(modelName!='-1'?'48px':'28px')}" :src="m_bimviewerurl"></iframe>
            <!-- <iframe :style="{height:(modelName!='-1'?'calc(100% - 48px)':'calc(100% - 28px)'),marginTop:(modelName!='-1'?'48px':'28px')}" src="../../../../../../static/BIMComposer/index.html" ref="ModelWindow"></iframe> -->
        </div>
        <!-- 大哥你就不能换个行么？ 你让我怎么改？还满是BUG-->
        <model-share
            v-if="showModelShare"
            :ProjectID="ProjectID"
            :VersionNO="m_VersionNO_ToShare"
            :ModelId="ModelID"
            :ModelName="modelName"
            :LoadShareUrl="LoadShareUrl"
            @CloseShareModel="showModelShare=false;"
            class="shl"
            :init_ShowNewEngine="init_ShowNewEngine"
             >
        </model-share>
        <TargetDocList v-if="showTargetDocList" :ProjectID='ProjectID' :ModelID="ModelID" @closeTargetDoc="showTargetDocList=false;IsDialogCovery=false;" @openFile="_openFile" class="TDL"></TargetDocList>
        <ViewDoc class="VD" v-if="showViewDoc" :openUrl="OpenDocViewUrl" @closeViewDoc="showViewDoc=false;OpenDocViewUrl='';"></ViewDoc>
        <div v-show="IsDialogCovery" class="Covery"></div>
    </div>
</template>
<script>

import { EventBus } from "@static/event.js";
import ModelShare from '@/components/Home/ProjectBoot/Model/ModelShare' //分享模型
import CompsCreateIssue from "@/components/CompsIssue/CompsCreateIssue";
import TargetDocList from '@/components/Home/ProjectBoot/Model/TargetDocList'//关联文档弹窗
import ViewDoc from '@/components/Home/ProjectBoot/Document/ViewDoc'
export default {
    name:"BIMViewer",
    components:{
        ModelShare,
        TargetDocList,
        CompsCreateIssue,
        ViewDoc,
    },
    data(){
        return {

            m_ShowNewEngine: true,

            // 当前人员的模型阶段操作权限
            // ------------------------
            userPhaseAuthData:[],

            // 用来判断的权限数据
            // ----------------
            authdata:[],
            issueauthdata:[],

            setAnimationClassData:'',//模型窗口动画class
            animationTime: 500,//模型窗口改变大小所执行的动画时间
            m_VersionNO_ToShare: '', // 模型版本号, mounted 中调用 getBIMViewerUrl ，该变量被赋值。
            m_bimviewerurl: '', // bimviewer地址
            showModelShare:false,
            showTargetDocList:false,
            showViewDoc:false,
            showOpenModeInfo:false,
            IsDialogCovery:false,
            ProjectId:"",
            LoadShareUrl:false,
            extdata: {
                showcreate: false, // 创建问题 默认 关闭
                issueStatusItems:[],
                funcauthdata:undefined
            },
            TargetDocNum:0,//关联文档数量
            DocList:[],//当前模型的关联文档列表
            issuestatusitems:[],
            ImageUrl:'', //当前模型图片的src
            OpenDocViewUrl:'',
            modelShow:false,
            //materialsMarShow: true || false,
            windowState: 1,//窗口的状态。-1为最小化，0为窗口化，1为最大化
            colorElements:null//获取当前着色的构件信息
        };
    },
    props:{

         init_ShowNewEngine:{
            type:Boolean,
            required:false,
            default(){
              return true;
            }
        },

        windowingOperation: {//窗口化操作 （鉴于该组件应用场景较多，避免出现混乱默认不需要）
          type: Boolean,
          default: false
        },
        bShowMtrRel:{
            type: Boolean,
            required: false
        },
        modelviewposition:{
            type: String,
            required: false
        },
        iframeid:{
              type:String,
              required: false
        },
        materialsMarShow: {
            type:Boolean,
            default: true
        },
        BIM_Session:{
            type:String,
            required:false,
        },
        ProjectID:{
            type:String,
            required:false,
        },
        ModelID:{
            type:String,
            required:false,
        },
        modelName:{
            type:String,
            required:false,
        },
        SelModel:{
            type:Object,
            required:true,
        },
        autoSetView:{
            type:String,
            required:false
        },

        materialsData: null,//模型id和构件id拼接后的集合
    },
    methods:{

      viewerurlget() {
        var _this = this;
        if (_this.m_ShowNewEngine) {
          try {_this.$staticmethod.consoleLog('显示为 window.bim_config.bimviewerurl');} catch (e) {}
          return window.bim_config.bimviewerurl;//   新版
        } else {
          try {_this.$staticmethod.consoleLog('显示为 window.bim_config.bimviewerurlold');} catch (e) {}
          return window.bim_config.bimviewerurlold;//   旧版
        }
     
      },

      // 获取当前人的所有模型阶段权限相关数据
      // ---------------------------------
      getUserPhaseAuthData(){
        var _this = this;
        var _organizeId = _this.$staticmethod._Get("organizeId");
        var _bimcomposerId = _this.$staticmethod._Get("bimcomposerId");
        var _Token = _this.$staticmethod.Get("Token");
        if (!window.projectbootvue.ProjectNewModelPhase) {
          return;
        }
        var phases = window.projectbootvue.ProjectNewModelPhase;

        var _url = `${window.bim_config.webserverurl}/api/User/Role/GetUserPhasesAuth`;
        _this.$axios({
          url: _url,
          method: 'post',
          data: _this.$qs.stringify({
            organizeId: _organizeId,
            Token: _Token,
            phases: JSON.stringify(phases)
          })
        }).then(x => {
          if (x.status == 200) {
            if (x.data.Ret > 0) {
              _this.userPhaseAuthData = x.data.Data.items;
            } else {
              _this.$message.error(`获取权限数据失败${x.data.Msg}`);
            }
          } else {
            debugger;
          }
        }).catch(x => {
          debugger;
        });
      },

      // 判断某种权限
      // -----------
      hasSomeAuth(code){

        var _this = this;

        // 使用 _this.userPhaseAuthData 来计算是否有权限
        // 获取当前模型阶段的所有角色的lr-share权限或code为null的数据
        // -------------------------------------------
        var shareauths = this.userPhaseAuthData
        .filter(x => (x.aphi_code == null || x.aphi_code == code) 
        && x.aphr_phaseval == this.SelModel.Phase);
        
        // 拿到所有 roleid
        // --------------
        var allroleIds = shareauths.map(x => x.aphr_roleId);

        // 只要任何一个 roleId 有 null 权限 及 lr-share 权限，则返回有权限
        // ------------------------------------------------------------
        var has_thiscode_auth = false;
        for (var i = 0; i <allroleIds.length; i++) {

          // 先看 null 权限，如果没有权限，则此 roleId 无权限
          // ---------------------------------------------
          var nullcode_thisrole = shareauths.filter(x => x.aphi_code == null && x.aphr_roleId == allroleIds[i])[0];
          if (nullcode_thisrole && nullcode_thisrole.aphr_hasauth == 0) {

            // 如果 null 权限没有，不用看 share 权限，跳向下一个 roleId
            // -----------------------------------------------------
            continue;
          } 

          // 看 code 为 share 的权限数据（aphi_code=='lr-share')
          // --------------------------------------------------
          var sharecode_thisrole = shareauths.filter(x => x.aphi_code == code && x.aphr_roleId == allroleIds[i])[0];
          if (sharecode_thisrole && sharecode_thisrole.aphr_hasauth == 0) {

          } else {

            // 有权限，直接 break
            // -----------------
            has_thiscode_auth = true;
            break;
          }

        }
        return has_thiscode_auth;
      },

      // 当前用户有模型分享权限
      // --------------------
      hasShare(){

        var _this = this;
        return _this.hasSomeAuth('lr-share');

      },

      // 当前用户有模型发起问题（问题编辑）权限
      // -----------------------------------
      hasIssueEdit(){
        var _this = this;
        return _this.testhasfuncbtnauth('IssueTracking', 'lr-edit');
      },

      ifinsteadlogo(){
        return window.bim_config.custom_bimviewerlogo;
      },

 // 各个功能模块页面的通用权限判断函数3 设置 auth1data
    // 需要有 extdata.funcauthdata
    // 需要有 aut1hdata
    // --------------------------------
    setAuthData: function(){
      // var _this = this;
      // var arr = [
      //   'lr_edit'
      // ];
      // for (var i = 0 ; i < arr.length; i++) {
      //   var btnname = arr[i].replace("_", "-");
      //   if (_this.testhas1funcbtnauth('IssueTracking', btnname) == true) {
      //     _this.authd1ata[arr[i]] = true;
      //   } else {
      //     _this.auth1data[arr[i]] = false;
      //   }
      // }

      // 把需要判断的权限动作列到这里
      // -------------------------
      var _this = this;
      var arr = ['lr-upload', 'lr-edit', 'lr-delete', 'lr-merge', 'lr-share'];
      for (var i = 0 ; i < arr.length; i++) {
        var btnname = arr[i].replace("_", "-");
        if (_this.testhasfuncbtnauth('BIMModel', btnname) == true) {
          _this.authdata[arr[i]] = true;
        } else {
          _this.authdata[arr[i]] = false;
        }
      }

      // 问题的权限
      // ---------
      var issarr = ['lr-edit'];
      for (var i = 0; i < issarr.length; i++) {
        var btnname = issarr[i].replace("_", "-");
        if (_this.testhasfuncbtnauth('IssueTracking', btnname) == true) {
          _this.issueauthdata[issarr[i]] = true;
        } else {
          _this.issueauthdata[issarr[i]] = false;
        }
      }

    },

    // 各个功能模块页面的通用权限判断函数2
    // 需要有 extdata.funcauthdata
    // 需要有 auth1data
    // --------------------------------
    setfuncauthdata(callback) {
      var _this = this;
      // /api/User/Role/GetUserOrgFuncAuth?organizeId=48617e7b-07f2-4748-9199-238af8f2bfc6&Token=322D1C8F
      var _OrganizeId = _this.$staticmethod._Get("organizeId");
      var _Token = _this.$staticmethod.Get("Token");
      _this.$axios
        .get(
          `${window.bim_config.webserverurl}/api/User/Role/GetUserOrgFuncAuth?organizeId=${_OrganizeId}&Token=${_Token}`
        )
        .then(x => {
          if (x.status == 200) {
            if (x.data.Ret > 0) {
              if (x.data.Data) {
                _this.extdata.funcauthdata = x.data.Data;                
                if (callback) {
                  //debugger;
                  callback();
                }
              }
            }
          }
        })
        .catch(x => {});
    },

    // 各个功能模块页面的通用权限判断函数1
    // 需要有 extdata.funcauthdata
    // 需要有 auth1data
    // -------------------------------
    testhasfuncbtnauth(bmencode, bmbencode) {
      var _this = this;
      if (!_this.extdata.funcauthdata) {
        _this.extdata.funcauthdata = JSON.parse(_this.$staticmethod.Get("funcauthdata"));
      }
      var bmIndex = _this.extdata.funcauthdata.findIndex(
        x => x.Bm_EnCode == bmencode
      );
      if (bmIndex < 0) {
        return false; // 没有找到指定bm项
      }
      var bm = _this.extdata.funcauthdata[bmIndex];
      if (bm.checkstate == "0") {
        return false; // 权限设置中，所有角色的bm设置均没有打开。
      }
      if (bm.Bmbs.length == 0) {
        return false; // 功能模块下没有按钮
      }
      var hasAuth = bm.Bmbs.findIndex(
        x => x.Roles.length > 0 && x.checkstate == "1" && x.Bmb_EnCode == bmbencode
      ); // 这个功能模块下有有角色的，且为有权限的
      return hasAuth >= 0;
    },

      //改变模型窗口大小
      changeWindowState(state) {
        this.windowState = state;
        this.setAnimationClassData = '';

        if (state) {
          //最大化
          this.setAnimationClassData = 'to-maximization';
          setTimeout(_=>{
            this.setAnimationClassData = 'class-maximization';
            this.$refs.modelViewRef.style.width = '100%';
            this.$refs.modelViewRef.style.height = '100%';
            this.$refs.modelViewRef.style.right = '0';
            this.$refs.modelViewRef.style.top = '0';
          },this.animationTime);
        }

        if (state == -1) {
          //最小化
          this.setAnimationClassData = 'to-minimize';
          setTimeout(_=>{
            this.setAnimationClassData = 'class-minimize';
            this.$refs.modelViewRef.style.width = '200px';
            this.$refs.modelViewRef.style.height = '200px';
            this.$refs.modelViewRef.style.right = '0';
            this.$refs.modelViewRef.style.top = '0';
          },this.animationTime);
        }

        if (!state) {
          //窗口化
          this.setAnimationClassData =  'to-window';
          setTimeout(_=>{
            this.setAnimationClassData = 'class-window';
            this.$refs.modelViewRef.style.width = '1000px';
            this.$refs.modelViewRef.style.height = '500px';
            this.$refs.modelViewRef.style.right = '25%';
            this.$refs.modelViewRef.style.top = '20%';
          },this.animationTime);
        }

      },

        iframeWpload(){
            let _this = this;

            // 通用权限数据读取
            // ---------------
            var iframeWindow = _this.$refs.ModelWindow.contentWindow;

            _this.$staticmethod.bimhelper_finishrender(iframeWindow, () => {
              // console.log('from BIMViewer.vue: 提示外部使用的页面，请去获取模型相关权限，再来设置这个页面上按钮的可用性');
              // _this.$emit('please_getmodel_auth');

              // 获取当前人的模型阶段权限数据
              // -------------------------
              _this.getUserPhaseAuthData();

              // 获取模型的相关权限
              // ----------------
              _this.setfuncauthdata(function(){
                _this.setAuthData();
              });

            })


            //iframeWindow.BIM1e.event.BIMeEvent.finishRender.subscribe();

            // 兼容之前写的逻辑，直接将事件向外抛
            // -------------------------------
            if (_this.bShowMtrRel != true) {
                _this.$emit('notmtr_onload', _this.$refs.ModelWindow.contentWindow);
                return;
            }

            // case bShowMtrRel == true
            // ------------------------
            var iframeWindow = null;
            iframeWindow = this.$refs.ModelWindow.contentWindow;

            if (_this.bShowMtrRel == true) {
                 _this.$emit('ismtr_onload', iframeWindow,_this.materialsData,_this.autoSetView);
                return;
            }

            // 之前写的构件管理中，点击模型操作之后的逻辑，高亮构件，zoom等操作
            // -----------------------------------------------------------

        },

        getStylemodelview(){
            var _this = this;
            var _s = {};
            //_s["position"] = 'static';
            if (_this.modelviewposition) {
                _s["position"] = _this.modelviewposition;
            }
            return _s;
        },

        JSONParseIfString(strOrObj) {
          var _this = this;
          if (typeof '' == typeof strOrObj) {
            return JSON.parse(strOrObj);
          } else {
            return strOrObj;
          }
        },

        getBIMViewerUrl(ProjectID, ModelID){

            var _this = this;
            
            _this.$axios.get(`${
              _this.$staticmethod.getBIMServer()
            }/api/Sys/GetURLParameters?ID=${this.BIM_Session}`)
                .then(x => {
                    if (x.data) {
       					var data = {};
                        try {
                            
                            data = _this.JSONParseIfString(x.data);

                            //data = JSON.parse(x.data);
                            _this.m_VersionNO_ToShare = (data.VersionNO || '').toString();
                          
                        }catch(e) {
                            data = e;
                        }
                        let issueLink = sessionStorage.getItem('BIMviewer_issueLink')
                        if(issueLink == 'true'){
                            _this.m_bimviewerurl = `${_this.viewerurlget()}?projectId=${ProjectID}&model=${ModelID}&ver=${data.VersionNO}&issueLink=true`;
                            sessionStorage.removeItem('BIMviewer_issueLink')
                        }else{
                            _this.m_bimviewerurl = `${_this.viewerurlget()}?projectId=${ProjectID}&model=${ModelID}&ver=${data.VersionNO}`;
                        }
                        
                        _this.modelShow = true
                    }
                });
        },

        openShare()
        {
            this.showModelShare=true;
            this.LoadShareUrl=true;
        },
        openTargetDoc()
        {
            this.showTargetDocList=true;
            this.IsDialogCovery=true;
        },
        _openFile(fileId,fileName,fileExName)
        {
            //debugger;
            var bimcomposerId=this.$staticmethod._Get('bimcomposerId');
            var url_iframe_all='';
            var downloadUrl=this.$urlPool.base_ModelApi+this.$urlPool.DownLoadFile+'?ProjectID='+bimcomposerId+'&keyValue='+fileId+'&FileKind=File';
            if (fileExName.indexOf("dwg") != -1) {
                // dwg 在线预览
                url_iframe_all = `${
                    this.$configjson.dwgurl
                }/Home/Index2?dwgurlcfg=${encodeURIComponent(downloadUrl)}&name=${
                fileName
                }`;
            } else {
                // office 在线预览
                // url_iframe_all = `${
                //     this.$configjson.idocviewurl
                // }/view/u11111111111111111rl?url=${encodeURIComponent(downloadUrl)}&name=${
                // fileName
                // }`;
                // ----------------------
                console.log('打开模型后，点击左上角关联文档并预览');
                url_iframe_all = this.$staticmethod.computeViewUrl(downloadUrl, fileName);
            }
            this.OpenDocViewUrl=url_iframe_all;
            this.showViewDoc=true;
        },
        changeModelUrlVersion(versionNO) {
            var _this = this;
            // 隐藏 详情小弹窗
            //_this.$emit("hideModelInfo");
            _this.m_VersionNO_ToShare = (versionNO || '').toString();
            _this.$emit("hideModelInfo", _this.m_VersionNO_ToShare);
            _this.$staticmethod.debugshowlog(`m_VersionNO_ToShare 被修改，值为${_this.m_VersionNO_ToShare}`);
            _this.m_bimviewerurl = `${_this.viewerurlget()}?projectId=${this.ProjectID}&model=${this.ModelID}&ver=${versionNO}`;
        },
        // changeModelUrlBySessionId() {
        //     var _this = this;
        //     _this.getBIMVie1werUrl(_this.ProjectID, _this.ModelID);
        // },
        applySessionID(){
            var _this = this;
            _this.$axios.get(`${
              _this.$staticmethod.getBIMServer()
            }/api/Sys/GetURLParameters?ID=${this.BIM_Session}`)
                .then(x => {
                  debugger;//方法未调用，加个debugger查看调用时机
                    if (x.data) {
       					var data = {};
                        try {
                            data = JSON.parse(x.data);
                        }catch(e) {
                            data = e;
                        }

                        _this.m_bimviewerurl = `${_this.viewerurlget()}?projectId=${this.ProjectID}&model=${this.ModelID}`;
                        _this.modelShow = true
                    }
                });
        },

        applyProjectAndModelID(projectID, modelId) {
            var _this = this;
            _this.modelShow = false;
            _this.m_bimviewerurl = `${_this.viewerurlget()}?projectId=${projectID}&model=${modelId}`;
            _this.modelShow = true
        },

        openModelInfo() {
            this.$emit('ItemopenModelInfo',this.SelModel);
            this.$emit('setModelInfoIndex',1001);
        },
        DownloadFile(fileId)
        {
            //文件下载权限判断----
            var downloadUrl=this.$urlPool.base_ModelApi+this.$urlPool.DownLoadFile;
            window.open(downloadUrl+'?ProjectID='+this.ProjectID+'&keyValue='+fileId+'&FileKind=File');
        },
        GetTargetDocBase64(fileType)
        {
            var imgdata;
            try{
                imgdata = require('../../../../assets/svgs_loadbyurl/interface-'+fileType+'.svg');
            }catch(error){
                imgdata = require('../../../../assets/svgs_loadbyurl/interface-unknown.svg');
            }
            return imgdata;
        },
        // 取消创建问题
        cancelcreate() {
        var _this = this;
        _this.extdata.showcreate = false;
        this.IsDialogCovery=false;
        },
        // 创建问题按钮点击
        btncreateissue_click() {
          var _this = this;
          //debugger;
        if (_this.testhasfuncbtnauth('IssueTracking', 'lr-edit') != true) {
             this.$message.error("没有新增问题追踪的权限");
             return;
        }
        var _this = this;
        _this.extdata.showcreate = true;
        },
        // 创建问题：确定按钮
        createok(obj) {
            var _this = this;
            //debugger;
            if (!obj.title || obj.title == "" || obj.title.trim() == '') {
              _this.$message.error("请输入标题");
              EventBus.$emit("R_InitiateProblem",true);
              return;
            }
            if (obj.deadlinetimeval == "") {
              _this.$message.error("请选择截止时间");
              EventBus.$emit("R_InitiateProblem",true);
              return;
            }

            // 参数：截止时间
            var timestr = _this.timeToString(obj.deadlinetimeval);
            // 参数：问题标题
            var title = obj.title;
            // 参数：参与人
            var joinerstr = "";
            if (obj.addingjoiners && obj.addingjoiners.length) {
                for (var i = 0; i < obj.addingjoiners.length; i++) {
                    joinerstr += `${i == 0 ? "" : ","}${obj.addingjoiners[i].UserId}`;
                }
            }
            // 参数：附件FileId
            var fileidstr = "";
            if (obj.addingFiles && obj.addingFiles.length) {
                for (var i = 0; i < obj.addingFiles.length; i++) {
                    fileidstr += `${i == 0 ? "" : ","}${obj.addingFiles[i].FileId}`;
                }
            }
            // 参数：ViewPointID
            var win = _this.$refs.ModelWindow.contentWindow;
//  获取普通视点        
             var tocall_getviewpointbasicinfo = _this.$staticmethod.bimhelper_getview(win);
            var cfg = tocall_getviewpointbasicinfo.getViewPointBasicInfo()
            // debugger

            var cfgstr = JSON.stringify(cfg)

            var cfg2 = JSON.parse(cfgstr)
            tocall_getviewpointbasicinfo.setViewPointBasicInfo(cfg2)
            // if(joinerstr.length==0){
            //     _this.$message.error("问题追踪内容不能为空");
            //     return
            // }
            var ViewPointID = JSON.stringify(cfg2)
            // 调用问题接口，添加问题，在回调中刷新问题（依据当前过滤器进行刷新）。

            //获取相机位置包含获取批注
            //相机位置
            let activecamera = win.model.BIM365API.Context.getActiveCamera()
            let pos = activecamera.position
            let rot = activecamera.rotation

            //获取剖切盒
            let sectionBox = null
            if (win.model.BIM365API.Context.getSectionState()) {
                sectionBox = win.model.BIM365API.Extension.Section.getCliperBox()
            }

            //旧引擎获取Control位置信息
            // let controlPostion = win.model.BIM365API.Context.getControlPostion()
            //新引擎获取Control位置信息
            let controlPostion = win.model.orbitControl.controls.getTarget()
            //获取当前着色的构件信息
            win.getCurrentColoredElements().then(res=>{
                // let ElementsIdAndRGBA = {}
                // for (let [key,value] of res) {
                //   ElementsIdAndRGBA.ElementsId = key
                //   ElementsIdAndRGBA.RGBA = value
                //   console.log(ElementsIdAndRGBA);
                //   typeData.push(ElementsIdAndRGBA)
                // }
                this.colorElements = [...res]
             })

            this.$nextTick(()=>{
               let override = {
                    cameraPosition: win.model.orbitControl.controls.getPosition(),
                    sectionBox,
                    cameraRotation: rot,
                    controlPostion,
                    selectedElements: win.model.BIM365API.Selector.getHighilightElementIds(),//选择的构件
                    hiddenElements: win.model.BIM365API.Selector.getHideElementIds(),//隐藏的构件
                    isolatedElements: win.model.BIM365API.Selector.getIsolateElementIds(),//隔离的构件
                    markupmsg: win.model.BIM365API.Extension.Markup.serialize(),//获取当前图形数据
                    colorElements:this.colorElements,//获取当前着色的构件信息
                    focalOffset: win.model.orbitControl.controls.getFocalOffset(),
                }
                let Isoverride = JSON.stringify(override)
                _this
                .$axios({
                  method: "post",
                  url: `${this.$issueBaseUrl.AddIssue}`,
                  data: {
                      Token: _this.$staticmethod.Get("Token"),
                      RealName: _this.$staticmethod.Get("RealName"),
                      Title: title,
                      JoinerIds: joinerstr,
                      FileIds: fileidstr,
                      EndDateStr: timestr,
                      organizeId: _this.$staticmethod._Get("organizeId"),
                      ModelID: _this.$staticmethod._Get("ModelID"),
                      IssueTypeId: obj.IssueTypeId || '',
                      ImageUrl:obj.ImageUrl,//_this.ImageUrl,
                      ViewPointID:ViewPointID,
                      ImageIds: obj.ImageIds,
                      Isoverride:Isoverride
                  }
                })
                .then(x => {
                    if (x.data.Ret < 0) {
                      _this.$message.error(x.data.Msg)
                    } else {
                      // 刷新
                      _this.refreshbystatus();
                      _this.$message.success('发起问题成功！');
                    }
                })
                .catch(x => {
                // 刷新
                        _this.refreshbystatus();
                });
            })
            
        },
        // 依据当前过滤器（仅过滤器）刷新数据
        refreshbystatus(autoOpenRight) {
        var _this = this;
        _this.extdata.showcreate = false;
        _this.extdata.showhover = false;
        _this.mask = false;
        },
        timeToString(date) {
        var _this = this;
        var year = date.getFullYear();
        var month = date.getMonth() + 1;
        var dat = date.getDate();
        var h = date.getHours();
        var m = date.getMinutes();
        var s = date.getSeconds();
        var tostr = `${year}-${month < 10 ? "0" + month : month}-${
            dat < 10 ? "0" + dat : dat
        } ${h < 10 ? "0" + h : h}:${m < 10 ? "0" + m : m}:${
            s < 10 ? "0" + s : s
        }`;
        return tostr;
        },

        _issuestatusitems(val){
            var _this = this;
            _this.issuestatusitems = val;
            //debugger;
            // console.log(val)
        },

        _imgsrc(val){
            var _this = this;
            _this.ImageUrl = val;
            // debugger;
            //console.log(val)
        },

        getAssociatedDocumentLength() {
          let _this = this;
          var formData=new FormData();
          formData.append("ProjectID",_this.ProjectID);
          formData.append("SourceID",_this.ModelID);
          _this.$axios.post(_this.$urlPool.base_ModelApi+_this.$urlPool.GetDocListBySourceID,formData).then(res=>{
            var result=res.data;
            var fileIds='';
            var RelationDateList={};
            for(let i=0;i<result.length;i++)
            {
              fileIds+=result[i].TargetId;
              RelationDateList[result[i].TargetId]=result[i].CreateDate;
              if(i<result.length-1)
                fileIds+=',';
            }
            if(fileIds!=''){
              var formData2=new FormData();
              formData2.append("ProjectID",_this.ProjectID);
              formData2.append("FileIDs",fileIds);
              _this.$axios.post(_this.$urlPool.base_ModelApi+_this.$urlPool.GetAllFileInfoByIDs,formData2).then(res=>{
                //this.DocList=res.data;
                let TargetDate;
                _this.TargetDocNum=res.data.length;
                for(let i=0;i<res.data.length;i++)
                {
                  TargetDate=RelationDateList[res.data[i].FileId];
                  _this.DocList.push({
                    FileId:res.data[i].FileId,
                    FileExtensions:res.data[i].FileExtensions.replace('.',''),
                    FileName:res.data[i].FileName,
                    TargetDate:new Date(TargetDate).toLocaleString('chinese',{hour12:false})
                  });
                }
                console.log(_this.DocList,"关联文档列表");
              });
            }
          });
        }
    },
    mounted(){
        var _this = this;

        if (_this.init_ShowNewEngine != undefined) {
          _this.m_ShowNewEngine = _this.init_ShowNewEngine;
        } else {
          _this.m_ShowNewEngine = true;
        }
        try { _this.$staticmethod.consoleLog('[bimviewer mounted m_ShowNewEngine = ]' + _this.m_ShowNewEngine) } catch (e) {}

        window.bimviewervue = _this;
        this.getAssociatedDocumentLength();
        //console.log('in mounted');
        _this.getBIMViewerUrl(this.ProjectID, this.ModelID);

        // // 获取权限数据这一部分修改到加载完模型后
        // // -----------------------------------
        // _this.setfun1cauthdata(function(){
        //   _this.setA1uthData();
        // });

    },
    created(){

        var _this = this;
        try { _this.$staticmethod.consoleLog(this.SelModel); } catch(e) {}

        this.$staticmethod._Set("needpausegetmsg", 1);
        if(this.SelModel.ID==-1)
            this.showOpenModeInfo=false;
        else
            this.showOpenModeInfo=true;

        if (_this.bShowMtrRel == true) {
            window.parent.ShowRelationInfo = function(){
                console.log( _this.selectelements)
                var data = {
                    act: 'resFromMtrModel_getSelecteds', datas: _this.selectelements
                };
                window.postMessage(data, "*");
            }
        }

    },

    directives: {
      'modelDrag': function(el,binding) {
        let odiv = el;
        odiv.onmousedown = function (e) {
          let ele = e || window.event;
          ele.stopPropagation();
          ele.preventDefault();

          let diffX = ele.clientX - odiv.parentNode.offsetLeft;
          let diffY = ele.clientY - odiv.parentNode.offsetTop;

          document.onmousemove = function (e) {
            if (binding.arg == 1) {
              return false;
            }

            let el = e || window.event;
            el.stopPropagation()
            let left = el.clientX - diffX;
            let top = el.clientY - diffY;

            if(top < 0) return false;
            if(left < 0) return false;

            //控制拖拽物体的范围只能在浏览器视窗内，不允许出现滚动条
            if (left > window.innerWidth - odiv.parentNode.offsetWidth) {
              left = window.innerWidth - odiv.parentNode.offsetWidth;
            }
            if (top > window.innerHeight - odiv.parentNode.offsetHeight) {
              top = window.innerHeight - odiv.parentNode.offsetHeight;
            }

            let topWidth = document.body.offsetWidth;
            let parentWidth = odiv.parentNode.offsetWidth;

            odiv.parentNode.style.right = (topWidth - left - parentWidth) + 'px';
            odiv.parentNode.style.top = top + 'px';
          };

          document.onmouseup = function (e) {
            document.onmousemove = null;
            document.onmouseup = null;
          };
        }
      }
    },

    watch: {
      ModelID(n,o) {
        if (n != o) {
          //清空关联文档的数据展示
          this.TargetDocNum = 0;
          this.getAssociatedDocumentLength();
          this.getBIMViewerUrl(this.ProjectID, n);
        }
      }
    },

    computed: {
      //根据窗口状态不同，执行不同的动画
      // setAnimationClass() {
      //   return {
      //     'to-maximization': this.windowState,//最大化
      //     'to-minimize': this.windowState == -1,//最小化
      //     'to-Window': !this.windowState,//窗口化
      //   }
      // },
    }
}
</script>
<style scoped>
.modelView{
    width:100%;
    height:100%;
    background-color:#ecede8;
    font-family: PingFangSC-Medium;
    /* position: relative; */
    /* position: fixed; */
}
.title{width:100%;height:48px;line-height:0px;text-align: left;position: absolute;background-color: transparent;z-index: 1}
.title .Bitmap-logo {
  left: 10px;
  top: 13px;
  position: absolute;
  background-image:url('../../../../assets/images/logo.png');
  background-position: center center;
  background-size:100%;
  width:25px;
  height:25px;
}
.view{
  width:100%;
  height:100%;
  position: relative;
}

.view .minimizeBitmap {
  width: 100%;
  height: calc(100% - 48px);
  position: absolute;
  top: 48px;
  left: 0;
}

.view .minimizeBitmap img {
  width: 100%;
  height: 100%;
}
.title .ModelName{display:inline-block;width:auto;height:100%;line-height:48px; position: absolute;padding-left: 25px;top:0px;left:50%;transform: translate(-50%,0);}
.title .ModelName i{left:0px;top:50%;position: absolute; transform:translate(0,-50%);

   background-size: 16px 16px;
background-position: center center; background-size:100%;
  width: 24px;
    height: 24px;
}

.title .ModelName i.logoinstead {

  background-size: 16px 16px;
  background-repeat: no-repeat;
}

.title .back{width:92px;height:40px;position:absolute;display:block;left:20px;top:20px;box-shadow:0px 1px 3px 0px rgba(0,21,41,0.42);border-radius:2px;padding-left:28px;line-height:0px;cursor: pointer;}
.title .back:hover{box-shadow:0px 1px 3px 0px rgba(0,21,41,0.22);background-color: #fafafa;}
.title .back button{border:none;background-color:transparent;font-size:12px;width:100%;height:100%;line-height:40px;padding: 0px;cursor: pointer;outline:none;}
.title .back .tip{position: absolute;left: 0px; top:0px;top:10px;left:10px;}

.title .fuc{
    width:auto;height:24px;padding:0px;position: absolute;top:14px;left:20px;margin:0px;
}
.title .fuc li{
    width:auto;height:100%;float:left;list-style-type:none;padding:0px 40px 0px 28px;position: relative;line-height:24px;cursor: pointer;
}
.title .fuc li:hover{
    color:#1890ff;
}
.title .fuc li::after{
    content: '|';width:20px;height:24px;font-size:24px;text-align:left;display:block;position: absolute;top:0px;right:0px;color:rgba(0,0,0,0.09);
}
.title .fuc li:last-child:after{
    content: '';
}
.title .fuc li i{
    width:20px;height:20px;font-size:20px;display: block;position: absolute;top:2px;left:0px;
}
.TargetList{
    position: absolute;top:37px;left:1px;background-color: transparent;width:auto;max-height:600px;overflow:hidden;padding:14px 1px 1px 1px;
}
.TargetList .inside{
    width:auto;height:100%;background-color:#fff;border-radius: 4px;padding:4px 0px 4px 0px;
}
.TargetList .inside span{
    display:block;width:auto;height:40px;line-height:40px;padding:0px 24px 0px 24px;text-indent: 24px;cursor: pointer;background-repeat:no-repeat;background-position:18px 10px;
}
.TargetList .inside span:hover{
    color:#1890ff;background-color:#f7fbff;
}

.title .btns{
  width:auto;
  height:40px;
  padding:0;
  padding-right:2px;
  margin:0;
  overflow: hidden;
  position: absolute;
  border-radius:2px;
  top:6px;
  right:13px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.title .btns li{width:38px;height:38px;float: left;list-style-type: none;position: relative;cursor: pointer;background-color:transparent;border-radius:2px;}.title .btns li:last-child{border:none;}.title .btns li:hover{background-color: #f2f2f2;}
.title .btns li i{position: absolute;left:50%;top:50%;transform: translate(-50%,-50%);width:24px;height:24px;font-size:24px;}
.title .share{top:20px;right:40px;}.title .issue{top:20px;right:10px;color:#f3ac00;}
iframe{width:100%; height:calc(100% - 48px);display:block;border:none;margin-top:48px;float:left;}
.shl{
  width:750px;height:auto;position: absolute;top:50%;left:50%;transform:translate(-50%,-50%);z-index:999;
}
.TDL{
  width:410px;height:480px;position: absolute;top:50%;left:50%;transform:translate(-50%,-50%);z-index:999;
}
.VD{
  width:80%;height:80%;position: absolute;top:50%;left:50%;transform:translate(-50%,-50%);z-index:1001;
}
.Covery{
  width:100%;height:100%;background-color:rgba(0,0,0,0.4);position: absolute;top:0px;left: 0px;z-index: 1;
}

/*模型窗口 动画相关*/
.title .window-option {
  margin-left: 20px;
}

.window-option span {
  cursor: pointer;
  display: inline-block;
  margin: 0 3px;
}

.window-option span:hover {
  opacity: 0.6;
}

.window-option .icon {
  vertical-align: middle;
}

.window-option .icon.icon-checkbox-Default {
  font-size: 14px;
}

/*最小化*/
@keyframes toMinimizeAnimate {
  to {
    top: 0;
    right: 0;
    transform: scale(0.1, 0.1);
    transform-origin: top right;
  }
}

.to-minimize {
  animation-name: toMinimizeAnimate;
  animation-duration: 0.5s;
  animation-fill-mode: both;
  animation-timing-function: ease-in;
}

@keyframes toWindowAnimate {
  to {
    width: 1000px;
    height: 500px;
    right: 25%;
    top: 20%;
  }
}
.to-window {
  animation-name: toWindowAnimate;
  animation-duration: 0.5s;
  animation-fill-mode: both;
  animation-timing-function: ease-in;
}

@keyframes toMaximizationAnimate {
  to {
    width: 100%;
    height: 100%;
    right: 0;
    top: 0;
  }
}
.to-maximization {
  animation-name: toMaximizationAnimate;
  animation-duration: 0.5s;
  animation-fill-mode: both;
  animation-timing-function: ease-in;
}

.class-window,
.class-minimize {
  box-shadow: 0 1px 8px 0 rgba(0,21,41,.5);
  border-radius: 4px;
  overflow: hidden;
}

.class-maximization,
.class-window,
.class-minimize {
  position: fixed;
}
/*****************/
</style>

