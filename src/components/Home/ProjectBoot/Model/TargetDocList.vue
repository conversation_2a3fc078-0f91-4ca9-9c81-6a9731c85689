<template>
    <div class="Model_TargetDoc" v-loading="isLoading">
    <div class="title">查看关联文档<i class="icon-suggested-close close" @click="$emit('closeTargetDoc')"></i></div>
    <div v-if="this.DocList.length!=0" class="con">
        <ul class="unit" v-for="item in this.DocList" :key="item.FileId">
            <li class="thumb"><i :style="{backgroundImage:'url(' + GetFileImg(item.FileExtensions.replace('.','')) + ')'}"></i></li>
            <li class="fileInfo" @click="OpenFile(item.FileId,item.FileName,item.FileExtensions)">
                <span class="title" :title="item.FileName">{{item.FileName}}</span>
                <span class="createDate">关联日期：{{item.TargetDate}}</span>
            </li>
            <li class="btn"><button @click="DownloadFile(item.FileId)"><i class="icon-interface-download-fill"></i></button></li>
        </ul>
    </div>
    <div v-else class="con">
        暂无关联文档
    </div>
    <div class="btns">
        <button class="blue" @click="$emit('closeTargetDoc')">关闭</button>
    </div>
    <div class="showView" v-if="isShowView">
        <iframe :src="openFile"></iframe>
    </div>
</div>
</template>
<script>
export default {
    name:"TargetDocs",
    data(){
        return {
            DocList:[],
            isLoading:false,
            isShowView:false,

            DocRoles:[],
        };
    },
    components:{

    },
    props:{
        ProjectID:{
            type:String,
            required:true,
        },
        ModelID:{
            type:String,
            required:true,
        }
    },
    methods:{
        GetFileImg(fileType)
        {
            var imgdata;
            try{
                imgdata = require('../../../../assets/svgs_loadbyurl/interface-'+fileType+'.svg');
            }catch(error){
                imgdata = require('../../../../assets/svgs_loadbyurl/interface-unknown.svg');
            }
            return imgdata;
        },
        DownloadFile(fileId)
        {
            //文件下载权限判断----
            // if(!this.IsHaveRole("下载"))
            // {
            //     this.$message({type:"error",message:"缺少文件下载的权限"});
            //     return;
            // }

            // 暂开放权限，这里权限判断的代码不对
            // -------------------------------
            var downloadUrl=this.$urlPool.base_ModelApi+this.$urlPool.DownLoadFile;
            window.open(downloadUrl+'?ProjectID='+this.ProjectID+'&keyValue='+fileId+'&FileKind=File');
        },
        OpenFile(FileId,FileName,FileExtensions)
        {
            //文件预览权限判断----
            // if(!this.IsHaveRole("可见"))
            // {
            //     this.$message({type:"error",message:"缺少文件预览的权限"});
            //     return;
            // }

            // 暂开放权限，这里权限判断的代码不对
            // -------------------------------
            this.$emit('openFile',FileId,FileName,FileExtensions);
        },
        IsHaveRole(m)
        {
            console.log(this.DocRoles);
            for(var i=0;i<this.DocRoles.length;i++){
                if(m==this.DocRoles[i].Bmb_FullName)
                {
                    return true;
                }
            }
            this.isLoading=false;
            return false;
        },
        GetDocRoles()
        {
            //获取文档权限
            //debugger;
            var _this = this;
            var organizeId=this.$staticmethod._Get("organizeId");
            var Token=this.$staticmethod.Get("Token");
            var AllRoleIds = this.$staticmethod.Get("user_RoleIds");
            this.$axios.get(this.$urlPool.base_MainSystemApi+this.$urlPool.GetProjectRoles+"?organizeId="+organizeId+"&Token="+Token).then(res=>{
                var result=res.data.Data;
                var ItemRoles;
                this.DocRoles=[];
                for(var i=0;i<result.length;i++)
                {
                    if(result[i].Bm_FullName=='项目文档')
                    {
                    let tempModelRoles=result[i].Bmbs;
                    for(let j=0;j<tempModelRoles.length;j++)
                    {
                        let tempRoles=tempModelRoles[j].Roles;
                        var IsHasCurrent=false;
                        for(let k=0;k<tempRoles.length;k++)
                        {
                        if(AllRoleIds.indexOf(tempRoles[k].RoleId)!=-1){
                            IsHasCurrent=true;
                            break;
                        }
                        }
                        if(IsHasCurrent)
                        this.DocRoles.push(tempModelRoles[j]);
                    }
                    }
                }
                this.RolesLoading=false;
            }).catch();
        }
    },
    created(){
        //获取文档权限
        this.GetDocRoles();
        var formData=new FormData();
        formData.append("ProjectID",this.ProjectID);
        formData.append("SourceID",this.ModelID);
        formData.append("Token", this.$staticmethod.Get("Token"));
        this.$axios.post(this.$urlPool.base_ModelApi+this.$urlPool.GetDocListBySourceID,formData).then(res=>{
            var result=res.data;
            var fileIds='';
            var RelationDateList={};
            for(let i=0;i<result.length;i++)
            {
                fileIds+=result[i].TargetId;
                RelationDateList[result[i].TargetId]=result[i].CreateDate;
                if(i<result.length-1)
                    fileIds+=',';
            }
            if(fileIds!=''){
                var formData2=new FormData();
                formData2.append("ProjectID",this.ProjectID);
                formData2.append("FileIDs",fileIds);
                formData.append("Token", this.$staticmethod.Get("Token"));
                this.$axios.post(this.$urlPool.base_ModelApi+this.$urlPool.GetAllFileInfoByIDs,formData2).then(res=>{
                    //this.DocList=res.data;
                    let TargetDate;
                    for(let i=0;i<res.data.length;i++)
                    {
                        TargetDate=RelationDateList[res.data[i].FileId];
                        this.DocList.push({
                            FileId:res.data[i].FileId,
                            FileExtensions:res.data[i].FileExtensions.replace('.',''),
                            FileName:res.data[i].FileName,
                            TargetDate:new Date(TargetDate).toLocaleString('chinese',{hour12:false})
                        });
                    }
                    console.log(this.DocList,"关联文档列表");
                });
            }
        });
    }
}
</script>
<style scoped>
ul{padding: 0px;margin: 0px;}li{padding: 0px;margin: 0px;}
.Model_TargetDoc{background-color: #fff;box-shadow:0px 13px 24px -17px rgba(11,41,62,0.8);border-radius:4px;font-family:PingFangSC-Medium;letter-spacing:0.5px;}
.Model_TargetDoc .title{width:calc(100% - 32px);height:48px;font-size:16px;color:#333;line-height:48px;text-align:left;font-weight:bold; position: relative;overflow: hidden;padding:0px 16px 0px 16px;}
.Model_TargetDoc .title .close{width:18px;height:18px;position: absolute;right:16px;top:15px;display:block;color:#aaa;}
.Model_TargetDoc .title .close::before{float:left;width:18px;height:18px;display:block;text-align:left;text-indent:0px;cursor: pointer;}
.Model_TargetDoc .con{
    height:calc(100% - 64px - 48px);margin-top:5px;overflow:hidden;line-height:50px;
    overflow-y: scroll;
    }

.con .unit{width:100%;height:80px;}.con .unit:hover{background-color:#f5f5f5;}.con .unit li{list-style-type:none;}
.con .unit .thumb{width:64px;height:100%;float:left;line-height:80px;text-align:center;position:relative;}.con .unit .thumb i{width:32px;height:32px;display:inline-block;background-repeat:no-repeat;background-size:100%;position: absolute;top:50%;left:50%;transform:translate(-50%,-50%);}
.con .unit .btn{width:80px;height:100%;float:left;line-height:80px;text-align:center;}
.con .unit .btn button{width:48px;height:32px;border:none;box-shadow:none;background-color:transparent;color:#606060;outline:none;cursor: pointer;border-radius:2px;}
.con .unit .btn button:hover{color:#1890FF;}.con .unit .btn button:active{box-shadow:0px 0px 2px #888;}
.con .unit .fileInfo{width:calc(100% - 80px - 64px);height:100%;float:left;line-height:0px;}
.con .unit .fileInfo:hover{cursor: pointer;}
.con .unit .fileInfo:hover .title{color:#1890ff;}
.con .unit .fileInfo span{width:100%;height:60%;display:inline-block;text-align:left;}
.con .unit .fileInfo .title{width:100%;height:22px;margin-top:17px;color:#333;font-size:13px;line-height:22px;overflow:hidden;text-overflow:ellipsis;white-space:nowrap;padding:0px;}
.con .unit .fileInfo .createDate{width:100%;height:20px;margin-top:11px;color:#999;font-size:12px;line-height:20px;}

.Model_TargetDoc .btns{width:calc(100% - 48px);height:64px;line-height:38px;text-align:right;padding:0px 24px 0px 24px;}
.Model_TargetDoc .btns button{width:auto;height: 40px;border:none;outline: none;border-radius: 3px;cursor: pointer;font-size:12px; letter-spacing:1px;padding:0px 25px 0px 25px;}
.Model_TargetDoc .btns button:hover{opacity:0.8;}
.Model_TargetDoc .btns .blue{background-color:#1890FF;color:#fff; }
.Model_TargetDoc .btns .white{background-color:transparent;color:#666;}

::-webkit-scrollbar{width:20px; height:8px;}
::-webkit-scrollbar-thumb{border-radius: 12px;border:6px solid transparent;box-shadow: 8px 0px 0px #A5ADB7 inset;}
::-webkit-scrollbar-thumb:hover {box-shadow: 8px 0px 0px #4A4A4A inset;} 
</style>
