
<template>
  <div class="search-list">
    <!-- <div class="expan-list" v-if="expansionListShow">
            <div class="expan-header">高级筛选</div>
            <div class="expan-content">
              <ul>
                <li>
                  <label>类别</label>
                  <el-select 
                  v-model="expanVal"  
                  :popper-append-to-body="false"
                  popper-class="select-popper"
                  @change="expanChange"
                  placeholder="请选择">
                    <el-option 
                      v-for="(item,index) in expanOptions"
                      :key="index"
                      :label="item.GroupName"
                      :value="item.GroupName">
                    </el-option>
                  </el-select>
                </li>
                <li>
                  <label>特性</label>
                  <el-select 
                  v-model="expanChildVal"  
                  :popper-append-to-body="false"
                  popper-class="select-popper"
                  placeholder="请选择">
                    <el-option 
                      v-for="(item,index) in expanOptionsChild"
                      :key="index"
                      :label="item"
                      :value="item">
                    </el-option>
                  </el-select>
                </li>
                <li>
                  <label>条件</label>
                  <el-select 
                  v-model="expanCondition"  
                  :popper-append-to-body="false"
                  popper-class="select-popper"
                  placeholder="请选择">
                    <el-option 
                      v-for="(item,index) in expanConditionOptions"
                      :key="index"
                      :label="item"
                      :value="item">
                    </el-option>
                  </el-select>
                </li>
                <li>
                  <label>条件值</label>
                  <el-input v-model="conditionInputValue" placeholder="请输入条件值"></el-input>
                </li>
              </ul>
              <div class="sure-btn" type="primary" @click="searchConFun">确定</div>
            </div>
          </div> -->
    <div v-for="(item, index) in selectList" :key="index">
			<label>类别</label>
      <el-select v-model="item.value" placeholder="请选择">
        <el-option v-for="option in options" :key="option.value" :label="option.label" :value="option.value"></el-option>
      </el-select>
    </div>
    <el-button @click="addSelect">增加</el-button>
    <el-button @click="clearAll">清空全部</el-button>
  </div>
</template>

<script>
export default {
	name: 'modelSearchList',
  data() {
    return {
      selectList: [{ value: '' }, { value: '' }, { value: '' }],
      options: [
        { label: '选项1', value: '1' },
        { label: '选项2', value: '2' },
        { label: '选项3', value: '3' }
      ]
    }
  },
  methods: {
    addSelect() {
      this.selectList.push({ value: '' }, { value: '' }, { value: '' })
    },
    clearSelect(index) {
      this.selectList[index].value = ''
    },
    clearAll() {
      for (let item of this.selectList) {
        item.value = ''
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.search-list{
	background-color: #fff;
}
</style>