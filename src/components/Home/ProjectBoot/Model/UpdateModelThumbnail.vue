<template>
<div class="Model_updateThumbnail" v-loading="isLoading">
    <div class="title">更改模型缩略图<i class="icon-suggested-close close" @click="$emit('closeUpdateThumbnail')"></i></div>
    <div class="con">
        <!-- <Clipping ref="cropImg" @onCrop="imgEdit_callBack" :circularPreview="true" :width="368" :height="300"></Clipping> -->

          <Clippings1
            ref="cropImg"
            @onCrop="imgEdit_callBack"
            :circularPreview="true"
            :width="368"
            :height="300"
          ></Clippings1>

    </div>
    <div class="btns">
        <button class="white" @click="$emit('closeUpdateThumbnail')">取消</button>
        <button class="blue" @click="saveImg">确定</button>
    </div>
</div>
</template>
<script>
import Clippings1 from '@/components/CompsCommon/CompsImageClippingUpload'

export default {
    name:'Model_updateThumbnail',
    data(){
        return {
            isLoading:false,
        };
    },
    components:{
        Clippings1,
    },
    props:{
        ProjectID:{
            type:String,
            required:true,
        },
        ModelID:{
            type:String,
            required:true,
        }
    },
    methods:{
        imgEdit_callBack(base64, binary) {
            var _this=this;
            console.log(binary,"编辑图片返回数据(文件信息)");
            if(binary.size!=0)
            {
                //debugger;
                let formData=new FormData();
                
                formData.append("ProjectID",this.ProjectID);
                formData.append("ModelID",this.ModelID);
                formData.append("Thumbnail",base64.split(',')[1]);
                formData.append("Token", this.$staticmethod.Get("Token"));
                _this.isLoading=true;
                var config={
                    onUploadProgress:event=>{
                    var complete = event.loaded/event.total * 100;
                    complete = parseInt(complete*100)/100.0;
                    _this.LoadingTxt="上传中: " + complete + "%";
                    }
                };
                _this.$axios.post(_this.$urlPool.base_ModelApi + _this.$urlPool.UpdateModelThumbnail,formData).then(res=>{
                    //debugger;
                    _this.isLoading=false;
                    _this.$message({ message: '更改成功', type: 'success'});
                    setTimeout(function(){
                        _this.$emit('reloadModellist');
                        _this.$emit('closeUpdateThumbnail');
                    }, 150); 
                }).catch(error=>{
                    _this.isLoading=false;
                });
            }
            else{
                this.$message({type:"warning",message:"文件异常"});
            }
        },
        saveImg()
        {
            this.$refs.cropImg.cropImage();
        }
    }
}
</script>
<style scoped>
ul{padding: 0px;margin: 0px;}li{padding: 0px;margin: 0px;}
.Model_updateThumbnail{background-color: #fff;box-shadow:0px 13px 24px -17px rgba(11,41,62,0.8);border-radius:4px;font-family:PingFangSC-Medium;padding:0px 16px 16px 16px;}
.Model_updateThumbnail .title{width:100%;height:48px;font-size:16px;color:#333;line-height:48px;text-align:left;font-weight:bold; position: relative;overflow: hidden;}
.Model_updateThumbnail .title .close{width:18px;height:18px;position: absolute;right:15px;top:15px;display:block;}
.Model_updateThumbnail .title .close::before{float:left;width:18px;height:18px;display:block;text-align:left;text-indent:0px;cursor: pointer;}
.Model_updateThumbnail .con{height:calc(100% - 38px - 48px - 22px);margin-top:5px;overflow:hidden;line-height:50px;}

.Model_updateThumbnail .btns{width:100%;height:38px;line-height:38px;text-align:right;}
.Model_updateThumbnail .btns button{width:auto;height: 32px;border:none;outline: none;border-radius: 3px;cursor: pointer;font-size:12px; letter-spacing:1px;padding:0px 10px 0px 10px;}
.Model_updateThumbnail .btns button:hover{opacity:0.8;}
.Model_updateThumbnail .btns .blue{background-color:#1890FF;color:#fff; }
.Model_updateThumbnail .btns .white{background-color:transparent;color:#666;}
::-webkit-scrollbar{width:20px; height:8px;}
::-webkit-scrollbar-thumb{border-radius: 12px;border:6px solid transparent;box-shadow: 8px 0px 0px #A5ADB7 inset;}
::-webkit-scrollbar-thumb:hover {box-shadow: 8px 0px 0px #4A4A4A inset;} 
</style>
