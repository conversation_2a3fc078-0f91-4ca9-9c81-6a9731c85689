<template>
    <div class="Model_ReName" v-loading="IsSaveData">
        <div class="title">重命名<i class="icon-suggested-close close" @click="$emit('CloseReName')"></i></div>
        <div class="con">
            <!--<span class="txt">
                 <i class="icon-interface-model"></i>
                <input type="text" v-model="modelName" placeholder="这是一个模型的名称" />
            </span>-->
            <compsAnimateInput class="txtModelName"
                v-model="currentTranVal1"       
                :defaultValue="modelName"     
                placeholder="请输入模型名称"   
                labelFootnote=""  
                height="100px"            
                fontSize="14px"          
                label="模型名称">             
            </compsAnimateInput>
        </div>
        <div class="btns">
            <button class="white" @click="$emit('CloseReName')">取消</button>
            <button class="blue" @click="reNameModelName">确定</button>
        </div>
    </div>
</template>
<style scoped>
    .Model_ReName{overflow: hidden;background-color: #fff;box-shadow:0px 13px 24px -17px rgba(11,41,62,0.8);border-radius:4px;font-family:PingFangSC-Medium;}
    .Model_ReName .title{width:100%;height:48px;font-size:16px;color:#333;line-height:48px;text-align:left;font-weight:bold; }
    .Model_ReName .title .close{width:18px;height:18px;position: absolute;right:15px;top:15px;display:block;}
    .Model_ReName .title .close::before{float:left;width:18px;height:18px;display:block;text-align:left;text-indent:0px;cursor: pointer;}
    .Model_ReName .btns{width: 100%;height:64px;line-height:64px;text-align: right;}
    .Model_ReName .con{width:calc(100% + 19px);height:auto;margin-top:20px;overflow-x:hidden;overflow-y:scroll;max-height:420px;min-height:40px;}
    .Model_ReName .con .txt{width:calc(100% - 30px);height:32px;background-color:#fafafa;display:block;line-height: 32px;position: relative;padding-left:30px;}
    .Model_ReName .con .txtModelName{text-align:left;}
    .Model_ReName .con .txt input{border:none;background-color:transparent;width:100%;height:100%;outline:none;}.Model_ReName .con .txt input::placeholder{font-size:12px;}
    .Model_ReName .con .txt i{display: inline-block;position:absolute;left:8px;top:8px;}
    .Model_ReName .btns button{width:56px;height: 32px;border:none;outline: none;border-radius: 3px;cursor: pointer;font-size:12px; letter-spacing:1px;}
    .Model_ReName .btns .blue{background-color:#1890FF;color:#fff; }
    .Model_ReName .btns .white{background-color:transparent;color:#666;}
    ::-webkit-scrollbar{width:20px; height:8px;}
    ::-webkit-scrollbar-thumb{border-radius: 12px;border:6px solid transparent;box-shadow: 8px 0px 0px #A5ADB7 inset;}
    ::-webkit-scrollbar-thumb:hover {box-shadow: 8px 0px 0px #4A4A4A inset;} 
</style>
<script>
import compsAnimateInput from "@/components/CompsCommon/compsAnimateInput";
export default {
    name:"ReNameForm",
    components:{
        compsAnimateInput,
    },
    data(){
        return {
            IsSaveData:false,
            currentTranVal1:{},
        };
    },
    props:{
        modelName:{
            type:String,
            required:false
        },
        modelId:{
            type:String,
            required:false
        }
    },
    methods:{
        reNameModelName(){
            if(this.currentTranVal1.value == undefined || this.currentTranVal1.value == null ){
                this.$message({type:'warning',message:'模型名没有改动'});
                return
            }
            if (this.currentTranVal1.value.trim() == '') {
                this.$message({type:'warning',message:'模型名不能为空'});
                this.IsSaveData=false;
                return;
            }
            this.$emit("changeModelName",this.currentTranVal1.value);
            
        }
    }
}
</script>
