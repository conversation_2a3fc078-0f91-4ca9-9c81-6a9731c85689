<template>
	<div class="Model_Info" :style="{ zIndex: ZIndex }">
		<div class="title">
			<font class="l">模型详情</font
			><i
				class="icon-suggested-close close"
				@click="$emit('CloseModelInfo')"
			></i>
		</div>
		<div class="con">
			<div class="p1">
				<ul>
					<li
						:data-versionno="item.versionNO"
						v-for="(item, index) in VersionList"
						:class="selCurrentVersion == item.versionNO ? 'sel' : ''"
						@click="selCurrentVersionRight(item.versionNO)"
						:key="item.versionNO"
					>
						V {{ item.versionNO }}.0

						<i
							:class="{ current: IsShowingVersion(item.versionNO, index) }"
						></i>
						<i
							class="icon-suggested-info_circle Info"
							@mouseover="showDetile($event, item)"
							@mouseout="hideDetile()"
						></i>
					</li>
				</ul>
				<button class="openCurrent" @click="openVersion">
					打开V {{ selCurrentVersion }}.0
				</button>
			</div>
			<div class="p2">
				<ul class="totalInfo">
					<li v-for="item in CountList_p2" :key="item.key">
						<span class="num" :class="item.key">{{ item.num }}</span>
						<span class="name">{{ item.name }}</span>
					</li>
				</ul>
			</div>
		</div>
		<div class="btns _css-btns-bottom">
			<div class="_css-btn-leftarea">
				<input
					id="idprojectid"
					class="_css-btn-leftinput"
					type="text"
					readonly
					:value="VaultID"
				/>
				<div @click="copyOrganizeId($event)" class="_css-btn-leftbtn">
					复制项目ID
				</div>
				<input
					id="idmodelid"
					class="_css-btn-leftinput _css-btn-leftinput2"
					type="text"
					readonly
					:value="featureItem.featureID"
				/>
				<div @click="copyModelId($event)" class="_css-btn-leftbtn">
					复制模型ID
				</div>
			</div>
			<button class="blue" @click="$emit('CloseModelInfo')">关闭</button>
		</div>
		<span v-if="IsShowDetile" class="Detile" :style="{ top: DetileY }">{{
			ModelInfoStr
		}}</span>
	</div>
</template>
<script>
export default {
	name: "NewModelInfo",
	components: {},
	props: {
		VaultID: {
			type: String,
			required: true,
		},
		featureItem: {
			type: Object,
			required: true,
		},
		VersionNO: {
			type: [Number, String],
			required: true,
		},
	},
	mounted() {},
	data() {
		return {
			CountList_p2: [
				{ key: "Count_View", name: "视图", num: 0 },
				{ key: "Count_Element", name: "构件", num: 0 },
				{ key: "Count_Space", name: "空间", num: 0 },
				{ key: "Count_System", name: "系统", num: 0 },
				{ key: "Count_Level", name: "楼层", num: 0 },
				{ key: "Count_Doc", name: "文档", num: 0 },
			],
			VersionList: [],
			ModelInfoStr: "",
			IsShowDetile: false,
			DetileY: "",
			lastOpenVersionNo: 0,
			selCurrentVersion: 0,
			ZIndex: 1001,
			R_lastTime: true,
		};
	},
	methods: {
		// 获取构件数量
		getP2value() {
			this.getSomeCount("GetAllElementCount", "Count_Element"); // 获取构件数量
			this.getSomeCount("GetAllSpaceCount", "Count_Space"); // 获取空间
			this.getSomeCount("GetAllLevelCount", "Count_Level"); // 获取楼层
			this.getModelDocumentNum(); // 文档
			this.GetAllMEPSystem(); // 系统
		},
		getSomeCount(src, type) {
			let _this = this;
			_this.$axios
				.get(
					`${this.$ip("newModelHttpUrl")}/Model/${src}?VaultID=${
						_this.VaultID
					}&ModelID=${_this.featureItem.featureID}&VersionNO=${this.VersionNO}`
				)
				.then((res) => {
					res.status === 200
						? _this.SetValOfkey(_this.CountList_p2, type, res.data)
						: _this.SetValOfkey(_this.CountList_p2, type, 0);
				})
				.catch((err) => {
					_this.SetValOfkey(_this.CountList_p2, type, 0);
				});
		},
		getModelDocumentNum() {
			let _userid = this.$staticmethod.Get("UserId");
			let _this = this;
			this.$axios
				.get(
					`${window.bim_config.webserverurl}/api/v1/file/modelfiles?modelId=${
						_this.featureItem.featureID
					}&userId=${_userid}&fileName=&Token=${this.$staticmethod.Get(
						"Token"
					)}`
				)
				.then((x) => {
					if (x.data.Data.length > 0 && x.data.Ret == 1) {
						_this.SetValOfkey(
							_this.CountList_p2,
							"Count_Doc",
							x.data.Data.length
						);
					} else {
						_this.SetValOfkey(_this.CountList_p2, "Count_Doc", 0);
					}
				})
				.catch((err) => {
					_this.SetValOfkey(_this.CountList_p2, "Count_Doc", 0);
				});
		},
		// 系统
		GetAllMEPSystem() {
			let _this = this;
			_this.$axios
				.get(
					`${this.$ip("newModelHttpUrl")}/Model/GetAllMEPSystem?VaultID=${
						_this.VaultID
					}&ModelID=${_this.featureItem.featureID}&VersionNO=${this.VersionNO}`
				)
				.then((res) => {
					res.status === 200
						? _this.SetValOfkey(
								_this.CountList_p2,
								"Count_System",
								res.data.length
						  )
						: _this.SetValOfkey(_this.CountList_p2, "Count_System", 0);
				})
				.catch((err) => {
					_this.SetValOfkey(_this.CountList_p2, "Count_System", 0);
				});
		},
		// 获取版本
		getAllVersions() {
			let _this = this;
			_this.$axios
				.get(
					`${this.$ip("newModelHttpUrl")}/Vault/GetAllVersions?VaultID=${
						_this.VaultID
					}&FeatureID=${_this.featureItem.featureID}`
				)
				.then((res) => {
					// res.status === 200 ? _this.SetValOfkey(_this.CountList_p2,'Count_System',res.data) : _this.SetValOfkey(_this.CountList_p2,'Count_System',0);
					if (res.status === 200) {
						let templist = res.data;
						_this.VersionList = templist.reverse();

						let currentVersion = 0;
						for (let i = 0; i < templist.length; i++) {
							if (templist[i].VersionNO > currentVersion) {
								currentVersion = templist[i].VersionNO;
							}
						}
						currentVersion++;
						_this.selCurrentVersion = currentVersion;
					}
				})
				.catch((err) => {});
		},

		copyOrganizeId(ev) {
			var _this = this;
			document.getElementById("idprojectid").focus();
			document.getElementById("idprojectid").select();
			document.execCommand("Copy");
			_this.$message({
				message: "复制成功",
				type: "success",
			});
		},

		copyModelId(ev) {
			var _this = this;
			document.getElementById("idmodelid").focus();
			document.getElementById("idmodelid").select();
			document.execCommand("Copy");
			_this.$message({
				message: "复制成功",
				type: "success",
			});
		},

		// 是否显示 current class
		IsShowingVersion(versionNO, index) {
			// 第一次打开ModelInfo前，并未指定版本，lastOpenVersionNo 为最新版本号
			// ---------------------------------------------------------------
			var _this = this;
			if (
				index == 0 &&
				(_this.lastOpenVersionNo == "" || _this.VersionList.length == 1)
			) {
				return true;
			}

			// 相同版本号时返回true
			if (versionNO == _this.lastOpenVersionNo) {
				return true;
			}
			return false;
		},

		SetValOfkey(list, key, val) {
			for (let i = 0; i < list.length; i++) {
				if (list[i].key == key) list[i].num = val;
			}
		},
		showDetile(e, item) {
			this.ModelInfoStr = "";
			var ty = e.y - (document.body.clientHeight - 420) / 2 - 10;
			this.DetileY = ty + "px";
			let dateStr = new Date(item.createTime).toLocaleString("chinese", {
				hour12: false,
			});
			let str__ =
				dateStr.split(" ")[0] + "\xa0\xa0\xa0" + dateStr.split(" ")[1];
			this.ModelInfoStr = `上传于${str__}`;
			this.IsShowDetile = true;
		},
		hideDetile() {
			this.IsShowDetile = false;
			this.ModelInfoStr = "";
		},

		// 打开指定版本的模型
		openVersion() {
			this.$staticmethod.debugshowlog(
				`emit openVersion this.selCurrentVersion = ${this.selCurrentVersion}`
			);
			this.$emit("openVersion", this.featureItem, this.selCurrentVersion);
		},
		selCurrentVersionRight(e) {
			this.selCurrentVersion = e;
		},
	},
	created() {
		var _this = this;

		this.ZIndex = 1001;
		// 调用函数获取详情
		this.getP2value();

		// created 时，获取 ProjectID 项目下 ModelID 的所有版本信息
		_this.$axios
			.get(
				`${this.$ip("newModelHttpUrl")}/Vault/GetAllVersions?VaultID=${
					_this.VaultID
				}&FeatureID=${_this.featureItem.featureID}`
			)
			.then((res) => {
				let templist = res.data;
				_this.VersionList = templist.reverse();

				let currentVersion = 0;
				for (let i = 0; i < _this.VersionList.length; i++) {
					if (_this.VersionList[i].versionNO > currentVersion) {
						currentVersion = _this.VersionList[i].versionNO;
					}
				}
				// currentVersion++;
				_this.selCurrentVersion = this.lastOpenVersionNo = currentVersion;
			});
	},
};
</script>
<style scoped>
._css-btns-bottom {
	width: 100%;
	height: 32px;
	line-height: 32px;
	font-size: 0px;
	position: relative;
	display: flex;
}

ul {
	padding: 0px;
	margin: 0px;
}
li {
	padding: 0px;
	margin: 0px;
	list-style-type: none;
}
.Model_Info {
	overflow: hidden;
	background-color: #fff;
	box-shadow: 0px 13px 24px -17px rgba(11, 41, 62, 0.8);
	border-radius: 4px;
	font-family: PingFangSC-Medium;
	padding: 6px 24px 16px 24px;
}
.Model_Info .title {
	width: 100%;
	height: 48px;
	font-size: 16px;
	color: #333;
	line-height: 48px;
	text-align: left;
	font-weight: bold;
	position: relative;
	overflow: hidden;
}
.Model_Info .title .close {
	width: 18px;
	height: 18px;
	position: absolute;
	right: 0px;
	top: 15px;
	display: block;
}
.Model_Info .title .close::before {
	float: left;
	width: 18px;
	height: 18px;
	display: block;
	text-align: left;
	text-indent: 0px;
	cursor: pointer;
}
.Model_Info .con {
	display: flex;
	width: 100%;
	height: calc(100% - 32px - 48px - 20px);
	margin-top: 20px;
	overflow: hidden;
	line-height: 50px;
}

._css-btn-leftbtn {
	height: 32px;
	padding-left: 8px;
	padding-right: 8px;
	color: rgba(24, 144, 255, 1) !important;
	border-radius: 4px;
	opacity: 1;
	background-color: white;
	border: 1px solid rgba(0, 0, 0, 0.25);
	font-size: 12px;
	margin-left: 8px;
	cursor: pointer;
}

._css-btn-leftinput {
	height: 24px;
	min-width: 235px;
	line-height: 24px;
	text-align: left;
	color: rgba(0, 0, 0, 0.65);
	padding-left: 8px;
	padding-right: 8px;
	border-radius: 4px;
	outline: none;
	border: 1px solid rgba(0, 0, 0, 0.25);
}

._css-btn-leftinput2 {
	margin-left: 65px;
}

._css-btn-leftarea {
	height: 100%;
	width: calc(100% - 50px);
	display: flex;
	align-items: center;
}

.Model_Info .btns button {
	width: auto;
	height: 32px;
	border: none;
	outline: none;
	border-radius: 3px;
	cursor: pointer;
	font-size: 12px;
	letter-spacing: 1px;
	font-size: 12px;
	padding: 0px 12px 0px 12px;
}
.Model_Info .btns button:hover {
	opacity: 0.8;
}
.Model_Info .btns .blue {
	background-color: #1890ff;
	color: #fff;
	position: absolute;
	right: 0;
}
.Model_Info .btns .white {
	background-color: transparent;
	color: #666;
}
.Model_Info .btns .delete {
	background-color: transparent;
	color: rgba(245, 34, 45, 1);
	float: left;
}
::-webkit-scrollbar {
	width: 20px;
	height: 8px;
}
::-webkit-scrollbar-thumb {
	border-radius: 12px;
	border: 6px solid transparent;
	box-shadow: 8px 0px 0px #a5adb7 inset;
}
::-webkit-scrollbar-thumb:hover {
	box-shadow: 8px 0px 0px #4a4a4a inset;
}

.Model_Info .con .p1 {
	width: 259px;
	height: 100%;
	border-right: 1px solid rgba(0, 0, 0, 0.09);
	overflow: hidden;
	line-height: 0px;
}
.Model_Info .con .p1 ul {
	width: calc(100% + 11px);
	height: calc(100% - 40px);
	overflow-x: hidden;
	overflow-y: scroll;
}
.Model_Info .con .p1 ul li {
	width: calc(100% - 24px - 24px + 11px);
	height: 50px;
	line-height: 50px;
	text-align: left;
	padding-left: 24px;
	position: relative;
}
.Model_Info .con .p1 ul li i {
	position: absolute;
	top: 15px;
	display: block;
	font-style: normal;
}
.Model_Info .con .p1 ul li i.current {
	width: 57px;
	height: 20px;
	background-color: #1890ff;
	border-radius: 3px;
	right: 40px;
}
.Model_Info .con .p1 ul li i.current::before {
	content: "当前版本";
	float: left;
	width: 100%;
	height: 100%;
	line-height: 20px;
	font-size: 12px;
	color: #fff;
	text-align: center;
}
.Model_Info .con .p1 ul li i.Info {
	width: 20px;
	height: 20px;
	right: 5px;
	opacity: 0.7;
	cursor: pointer;
}
.Model_Info .con .p1 ul li i.Info:hover {
	color: #1890ff;
}
.Model_Info .con .p1 ul li:hover {
	background-color: #f5f5f5;
}
.Model_Info .con .p1 ul li.sel {
	background-color: #f5f5f5;
}
.openCurrent {
	width: calc(100% - 20px);
	height: 38px;
	border-radius: 20px;
	background: #f5f5f5;
	box-shadow: 0px 1px 3px 0px rgba(0, 21, 41, 0.22);
	border: none;
	outline: none;
	cursor: pointer;
}
.openCurrent:hover {
	background-color: #1890ff;
	color: #fff;
}
.Model_Info .con .p2 {
	width: calc(100% - 260px - 135px - 8px);
	height: 100%;
	flex: 1;
}
.Model_Info .con .p2 ul {
	width: calc(100% - 40px);
	height: 100%;
	margin-left: 24px;
}
.Model_Info .con .p2 ul li {
	width: 132px;
	height: 99px;
	border: 1px solid rgba(0, 0, 0, 0.09);
	float: left;
	margin: 0px 8px 8px 0px;
	border-radius: 4px;
	box-shadow: 0px 0px 1px #e0e0e0;
}
.Model_Info .con .p2 ul li span {
	display: block;
	float: left;
}
.Model_Info .con .p2 ul li .num {
	width: 108px;
	height: 38px;
	margin: 16px 0px 0px 12px;
	line-height: 38px;
	font-size: 30px;
	color: #1da48c;
}
.Model_Info .con .p2 ul li .name {
	width: 108px;
	height: 20px;
	margin: 11px 0px 0px 12px;
	line-height: 20px;
	font-size: 12px;
	color: rgba(0, 0, 0, 0.42);
}

.Model_Info .con .p3 {
	width: 132px;
	height: 100%;
	float: left;
}
.Model_Info .con .p3 ul {
	width: 100%;
	height: 100%;
}
.Model_Info .con .p3 ul li {
	width: 132px;
	height: 99px;
	border: 1px solid rgba(0, 0, 0, 0.09);
	float: left;
	margin: 0px 8px 8px 0px;
	border-radius: 4px;
	box-shadow: 0px 0px 1px #e0e0e0;
}
.Model_Info .con .p3 ul li span {
	display: block;
	float: left;
}
.Model_Info .con .p3 ul li .num {
	width: 108px;
	height: 38px;
	margin: 16px 0px 0px 12px;
	line-height: 38px;
	font-size: 30px;
	color: #faad14;
}
.Model_Info .Count_Issue,
.Count_Doc {
	color: #faad14 !important;
}
.Model_Info .con .p3 ul li .name {
	width: 108px;
	height: 20px;
	margin: 11px 0px 0px 12px;
	line-height: 20px;
	font-size: 12px;
	color: rgba(0, 0, 0, 0.42);
}

.Detile {
	position: absolute;
	left: 260px;
	top: 0px;
	width: auto;
	height: 40px;
	padding: 0px 20px 0px 20px;
	background-color: rgba(0, 0, 0, 0.65);
	color: #fff;
	border-radius: 2px;
	line-height: 38px;
}
</style>
