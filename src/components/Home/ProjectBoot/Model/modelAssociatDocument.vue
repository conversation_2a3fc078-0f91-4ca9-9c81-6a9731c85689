<template>
  <div class="Model_TargetDoc" v-loading="isLoading">
    <div class="title">
      查看关联文档<i
        class="icon-suggested-close close"
        @click="$emit('closeDoc')"
      ></i>
    </div>
    <div v-if="docList.length != 0" class="con">
      <ul class="unit" v-for="item in docList" :key="item.FileId">
        <li class="thumb">
          <i
            :style="{backgroundImage:'url(' + GetFileImg(item.FileExtension.replace('.', '')) + ')',}"
          ></i>
        </li>
        <li
          class="fileInfo"
          @click="OpenFile(item.FileId, item.FileName, item.FileExtension)"
        >
          <span class="title" :title="item.FileName">{{ item.FileName }}</span>
          <span class="createDate">关联日期：{{ item.CreateTime }}</span>
        </li>
        <li class="btn">
          <button @click="DownloadFile(item.FileId)">
            <i class="icon-interface-download-fill"></i>
          </button>
        </li>
      </ul>
    </div>
    <div v-else class="con">暂无关联文档</div>
    <div class="btns">
      <button class="blue" @click="$emit('closeDoc')">关闭</button>
    </div>
  </div>
</template>
<script>
export default {
  name: "modelAssociatDocument",
  data() {
    return {
      isLoading: false,
      isShowView: false,
    };
  },
  components: {},
  props: {
    docList: {
      type: Array,
      required: true,
    },
  },
	created() {
  },
  methods: {
    GetFileImg(fileType) {
      var imgdata;
      try {
        imgdata = require("../../../../assets/svgs_loadbyurl/interface-" +
          fileType +
          ".svg");
      } catch (error) {
        imgdata = require("../../../../assets/svgs_loadbyurl/interface-unknown.svg");
      }
      return imgdata;
    },
    DownloadFile(fileId) {
			window.location.href = `${window.bim_config.webserverurl}/api/v1/file/download?fileId=${fileId}&userId=${this.$staticmethod.Get("UserId")}&Token=${this.$staticmethod.Get('Token')}`;
    },
    OpenFile(FileId, FileName, FileExtension) {
			this.$emit('openDoc',FileId,FileName,FileExtension);
    },
		closeDoc(){
			this.$emit('closeDoc')
		}
  },
  
};
</script>
<style scoped>
ul {
  padding: 0px;
  margin: 0px;
}
li {
  padding: 0px;
  margin: 0px;
}
.Model_TargetDoc {
  background-color: #fff;
  box-shadow: 0px 13px 24px -17px rgba(11, 41, 62, 0.8);
  border-radius: 4px;
  font-family: PingFangSC-Medium;
  letter-spacing: 0.5px;
}
.Model_TargetDoc .title {
  width: calc(100% - 32px);
  height: 48px;
  font-size: 16px;
  color: #333;
  line-height: 48px;
  text-align: left;
  font-weight: bold;
  position: relative;
  overflow: hidden;
  padding: 0px 16px 0px 16px;
}
.Model_TargetDoc .title .close {
  width: 18px;
  height: 18px;
  position: absolute;
  right: 16px;
  top: 15px;
  display: block;
  color: #aaa;
}
.Model_TargetDoc .title .close::before {
  float: left;
  width: 18px;
  height: 18px;
  display: block;
  text-align: left;
  text-indent: 0px;
  cursor: pointer;
}
.Model_TargetDoc .con {
  height: calc(100% - 64px - 48px);
  margin-top: 5px;
  overflow: hidden;
  line-height: 50px;
  overflow-y: scroll;
  padding-left: 18px;
}

.con .unit {
  width: 100%;
  height: 80px;
}
.con .unit:hover {
  background-color: #f5f5f5;
}
.con .unit li {
  list-style-type: none;
}
.con .unit .thumb {
  width: 64px;
  height: 100%;
  float: left;
  line-height: 80px;
  text-align: center;
  position: relative;
}
.con .unit .thumb i {
  width: 32px;
  height: 32px;
  display: inline-block;
  background-repeat: no-repeat;
  background-size: 100%;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}
.con .unit .btn {
  width: 80px;
  height: 100%;
  float: left;
  line-height: 80px;
  text-align: center;
}
.con .unit .btn button {
  width: 48px;
  height: 32px;
  border: none;
  box-shadow: none;
  background-color: transparent;
  color: #606060;
  outline: none;
  cursor: pointer;
  border-radius: 2px;
}
.con .unit .btn button:hover {
  color: #1890ff;
}
.con .unit .btn button:active {
  box-shadow: 0px 0px 2px #888;
}
.con .unit .fileInfo {
  width: calc(100% - 80px - 64px);
  height: 100%;
  float: left;
  line-height: 0px;
}
.con .unit .fileInfo:hover {
  cursor: pointer;
}
.con .unit .fileInfo:hover .title {
  color: #1890ff;
}
.con .unit .fileInfo span {
  width: 100%;
  height: 60%;
  display: inline-block;
  text-align: left;
}
.con .unit .fileInfo .title {
  width: 100%;
  height: 22px;
  margin-top: 17px;
  color: #333;
  font-size: 13px;
  line-height: 22px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  padding: 0px;
}
.con .unit .fileInfo .createDate {
  width: 100%;
  height: 20px;
  margin-top: 11px;
  color: #999;
  font-size: 12px;
  line-height: 20px;
}

.Model_TargetDoc .btns {
  width: calc(100% - 48px);
  height: 64px;
  line-height: 38px;
  text-align: right;
  padding: 0px 24px 0px 24px;
}
.Model_TargetDoc .btns button {
  width: auto;
  height: 40px;
  border: none;
  outline: none;
  border-radius: 3px;
  cursor: pointer;
  font-size: 12px;
  letter-spacing: 1px;
  padding: 0px 25px 0px 25px;
}
.Model_TargetDoc .btns button:hover {
  opacity: 0.8;
}
.Model_TargetDoc .btns .blue {
  background-color: #1890ff;
  color: #fff;
}
.Model_TargetDoc .btns .white {
  background-color: transparent;
  color: #666;
}

::-webkit-scrollbar {
  width: 20px;
  height: 8px;
}
::-webkit-scrollbar-thumb {
  border-radius: 12px;
  border: 6px solid transparent;
  box-shadow: 8px 0px 0px #a5adb7 inset;
}
::-webkit-scrollbar-thumb:hover {
  box-shadow: 8px 0px 0px #4a4a4a inset;
}
</style>
