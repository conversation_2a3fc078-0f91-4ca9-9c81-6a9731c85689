<template>
<div class="Model_share" v-loading="isLoading">
    <div class="title">分享：<font class="l">{{ModelName}}</font><i class="icon-suggested-close close" @click="$emit('CloseShareModel')"></i></div>
    <div class="con">
        <div class="p1">
            <div class="WX" id="qrcode" ref="qrcode"></div>
            <div class="info">
                <span class="t">请复制以下链接地址</span>
                <input id="txtWXUrl" v-model="WXUrl" type="text" />
                <div class="input-bottom">
                    <span class="t">{{(RoleInfo.public==1?'公开':'加密(密码：'+ShareObj.pwd+')')+' 且'+ EnableDaytext}}<font class="link" @click="openShareInfo">点击修改权限</font></span>
                    <span class="css-pl24 t">累计访问：<font class="css-folder">{{ visitsNum }}</font> 次</span>
                </div>
            </div>
        </div>
    </div>
    <div class="btns">
        <button class="white" @click="$emit('CloseShareModel')">取消</button>
        <button class="blue" @click="copyToTemp">复制链接</button>
    </div>
    <share-info v-if="showShareInfo" :RoleInfo="RoleInfo" :ModelName="ModelName" @ChangeRoleInfo="ChangeRoleInfo" class="SI" @CloseChangeRole="showShareInfo=false" ></share-info>
</div>
</template>

<script>
import ShareInfo from '@/components/Home/ProjectBoot/Model/ShareInfo'
import QRCode from 'qrcodejs2'

export default {
    name:'ModelShare',
    components:{
        ShareInfo,
    },
    data(){
        return{
            showShareInfo:false,
            RoleInfo:{
                public:1,
                enableDay:-1
            },//当前选择的分享设置
            EnableDaytext:"",
            WXUrl:"",
            ShareObj:{},//分享的对象 链接、是否公开、密码
            isLoading:false,
            visitsNum: 0
        };
    },
    props:{
        ModelName:{
            type:String,
            required:false
        },
        ModelId:{
            type:String,
            required:false
        },
        LoadShareUrl:{
            type:Boolean,
            required:false
        },
        VersionNO:{
            type:String,
            required: false
        }
    },
    methods:{

        modifyUrlByInit() {
            this.WXUrl = window.location.origin + this.ShareObj.url;
        },

        ChangeRoleInfo(val) {
            //debugger;
            console.log('ChangeRoleInfo', this.RoleInfo);
            this.RoleInfo=val;
        },
        openShareInfo() {

            // 二维码页面记录的权限值？

            // 设置页面的权限值？

            this.showShareInfo=true;
        },
        GetEnableDay() {
            switch(this.RoleInfo.enableDay)
            {
                case -1:
                    this.EnableDaytext = '永久有效';
                    break;
                case 7:
                    this.EnableDaytext = '一周有效';
                    break;
                case 1:
                    this.EnableDaytext = '一天有效';
                    break;
            }
        },
        copyToTemp()
        {
            var txt=document.getElementById('txtWXUrl');
            txt.select();
            document.execCommand("Copy"); 
            this.$message({type:"success",message:"复制成功"});
        },

        getVisitsNum() {
            // visitsNum
            this.$axios.get(`${window.bim_config.webserverurl}/api/User/Model/GetLinkNumber?modelId=${this.ModelId}&Token=${this.$staticmethod.Get('Token')}`).then(res=>{
                if (res.data.Ret > 0) {
                    this.visitsNum = res.data.Data
                }
            })
        }
    },
    watch:{
        LoadShareUrl(val)
        {
            // debugger;
            // if(val)
            // {
            //     var Token = this.$staticmethod.Get("Token");
            //     var session_organizeId = this.$staticmethod._Get("organizeId");
            //     this.isLoading=true;
            //     var postJson={
            //         "Token":Token,
            //         "daycount":this.RoleInfo.enableDay,
            //         "projectId":session_organizeId,
            //         "modelId":this.ModelId,
            //         "ViewpointID":"",
            //         "ViewID":"",
            //         "hasRandomPwd":(this.RoleInfo.public!=1?'1':'0')
            //     };
            //     this.$axios.post(this.$urlPool.base_MainSystemApi+this.$urlPool.Create1ModelShare,this.$qs.stringify(postJson)).then(res=>{
            //         this.ShareObj=res.data.Data;
            //         this.WXUrl=this.$urlPool.base_ShareHeader + this.ShareObj.url;
            //         this.isLoading=false;
            //     }).catch(error=>{});
            // }
        },
        ModelId(val)
        {
            // debugger;
            // if(val=="-1"||this.LoadShareUrl==false)
            //     return;
            // var Token = this.$staticmethod.Get("Token");
            // var session_organizeId = this.$staticmethod._Get("organizeId");
            // this.isLoading=true;
            // var postJson={
            //     "Token":Token,
            //     "daycount":this.RoleInfo.enableDay,
            //     "projectId":session_organizeId,
            //     "modelId":this.ModelId,
            //     "ViewpointID":"",
            //     "ViewID":"",
            //     "hasRandomPwd":(this.RoleInfo.public!=1?'1':'0')
            // };
            // this.$axios.post(this.$urlPool.base_MainSystemApi+this.$urlPool.Create1ModelShare,this.$qs.stringify(postJson)).then(res=>{
            //     this.ShareObj=res.data.Data;
            //     this.WXUrl=this.$urlPool.base_ShareHeader + this.ShareObj.url;
            //     this.isLoading=false;
            // }).catch(error=>{});
        },
        RoleInfo(val)
        {
            var _this = this;
            this.GetEnableDay();
            var Token = this.$staticmethod.Get("Token");
            var organizeId=this.$staticmethod._Get('organizeId');
            var postJson={
                "VersionNO":this.VersionNO,
                "Token":Token,
                "daycount":this.RoleInfo.enableDay,
                "projectId":organizeId,
                "modelId":this.ModelId,
                "ViewpointID":"",
                "ViewID":"",
                "hasRandomPwd":(this.RoleInfo.public!=1?'1':'0')
            };
            this.isLoading=true;
            this.$staticmethod.debugshowlog(`准备调用 CreateModelShare，模型版本号为${this.VersionNO}`);
            this.$axios.post(this.$urlPool.base_MainSystemApi+this.$urlPool.CreateModelShare,this.$qs.stringify(postJson)).then(res=>{
                // 写得神马东西！！？
                //debugger;
                this.$staticmethod.debugshowlog('弹出模型分享链接提示后，修改分享设置，完成CreateModelShare请求');
                this.ShareObj=res.data.Data;
                

                // 判断当前是否 isold
                // -----------------
                this.modifyUrlByInit();

                this.isLoading=false;
            }).catch();
        },

     

        WXUrl(val){
            //debugger;
            var qrcodeObj=this.$refs.qrcode;
            qrcodeObj.innerHTML = ''
            let qrcode = new QRCode(qrcodeObj, {
                width: 120, //图像宽度
                height: 120, //图像高度
                colorDark : "#000000", //前景色
                colorLight : "#ffffff", //背景色
                typeNumber:4, 
                correctLevel : QRCode.CorrectLevel.H //容错级别 容错级别有：（1）QRCode.CorrectLevel.L （2）QRCode.CorrectLevel.M （3）QRCode.CorrectLevel.Q （4）QRCode.CorrectLevel.H
            })
            qrcode.clear() //清除二维码 
            qrcode.makeCode(val) //生成另一个新的二维码
        }
    },
    created(){
        var _this = this;
        this.GetEnableDay();
        var Token = this.$staticmethod.Get("Token");
        var organizeId=this.$staticmethod._Get('organizeId');
        var postJson={
            "VersionNO":this.VersionNO,
            "Token":Token,
            "daycount":this.RoleInfo.enableDay,
            "projectId":organizeId,
            "modelId":this.ModelId,
            "ViewpointID":"",
            "ViewID":"",
            "hasRandomPwd":(this.RoleInfo.public!=1?'1':'0')
        };
        this.isLoading=true;
        this.$staticmethod.debugshowlog(`准备调用 CreateModelShare，模型版本号为${(this.VersionNO == undefined?'':this.VersionNO)}`);
        this.$axios.post(this.$urlPool.base_MainSystemApi+this.$urlPool.CreateModelShare,this.$qs.stringify(postJson)).then(res=>{
            _this.$staticmethod.debugshowlog('第一次完成CreateModelShare请求，弹出模型分享链接提示时');
            _this.ShareObj=res.data.Data;
            _this.WXUrl= window.location.origin + _this.ShareObj.url;

            // 判断当前是否 isold
            // -----------------
            _this.modifyUrlByInit();

            _this.isLoading=false;
        }).catch();

        this.getVisitsNum()
    }
}
</script>


<style scoped>
ul{padding: 0px;margin: 0px;}li{padding: 0px;margin: 0px;}
.Model_share{background-color: #fff;box-shadow:0px 13px 24px -17px rgba(11,41,62,0.8);border-radius:4px;font-family:PingFangSC-Medium;padding:0px 16px 16px 16px;}
.Model_share .title{width:100%;height:48px;font-size:16px;color:#333;line-height:48px;text-align:left;font-weight:bold; position: relative;overflow: hidden;}
.Model_share .title .close{width:18px;height:18px;position: absolute;right:15px;top:15px;display:block;}
.Model_share .title .close::before{float:left;width:18px;height:18px;display:block;text-align:left;text-indent:0px;cursor: pointer;}
.Model_share .con{height:calc(100% - 38px - 48px - 22px);margin-top:5px;overflow:hidden;line-height:50px;}

.Model_share .con .p1{width:100%;height:120px;}
.Model_share .con .p1 .WX{width:120px;height:100%;float:left;background-color: #f0f0f0;}
.Model_share .con .p1 .info{width:calc(100% - 120px - 30px);height:100%;float:left;margin-left:30px;font-size:0px;line-height:0px;}
.Model_share .con .p1 .info .t,.Model_share .con .p1 .info input{display:inline-block;width:100%;height:40px;line-height:40px;font-size:14px;text-align:left;}
.Model_share .con .p1 .info input{width:calc(100% - 2px);height:32px;border:1px solid rgba(24,144,255,1);outline:none;text-indent: 10px;}
.Model_share .con .p1 .info .link{color:rgba(24,144,255,1);cursor: pointer;margin-left:20px;}.Model_share .con .p1 .info .link:hover{opacity: 0.8;}

.Model_share .btns{width:100%;height:38px;line-height:38px;text-align:right;}
.Model_share .btns button{width:auto;height: 32px;border:none;outline: none;border-radius: 3px;cursor: pointer;font-size:12px; letter-spacing:1px;padding:0px 10px 0px 10px;}
.Model_share .btns button:hover{opacity:0.8;}
.Model_share .btns .blue{background-color:#1890FF;color:#fff; }
.Model_share .btns .white{background-color:transparent;color:#666;}
::-webkit-scrollbar{width:20px; height:8px;}
::-webkit-scrollbar-thumb{border-radius: 12px;border:6px solid transparent;box-shadow: 8px 0px 0px #A5ADB7 inset;}
::-webkit-scrollbar-thumb:hover {box-shadow: 8px 0px 0px #4A4A4A inset;} 

.SI{width:612px;position: absolute;top:50%;left:50%;transform:translate(-50%,-50%);z-index:1000;background-color: #fff}

.input-bottom {
    display: flex;
    justify-content: space-between;
}
</style>
