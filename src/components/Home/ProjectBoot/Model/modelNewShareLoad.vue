<template>
  <div class="model-iframe-content">
    <zdialog-function
      :init_title="'访问密码'"
      :init_zindex="1003"
      :init_innerWidth="400"
      :init_width="400"
      init_closebtniconfontclass="icon-suggested-close"
      :init_usecustomtitlearea="false"
      @onclose="psdVisible = false"
      v-if="psdVisible"
    >
      <div slot="mainslot" class="link-body">
        <el-form ref="form" :model="psdform">
            <el-form-item label="密码">
              <el-input v-model="psdform.psd" @keyup.enter.native="handlePsdConfirm"></el-input>
            </el-form-item>
          </el-form>
      </div>
      <div slot="buttonslot" class="_css-addBtnCtn">
        <zbutton-function
          :init_text=" '确定'"
          :init_fontsize="14"
          :debugmode="true"
          :init_height="'40px'"
          :init_width="'80px'"
          @onclick="handlePsdConfirm"
        >
        </zbutton-function>
      </div>
    </zdialog-function>
    <modelNewIframeLoading
      v-if="showIframe"
      class="model-iframe"
      :VaultID=VaultID
      :featureID=featureID
    ></modelNewIframeLoading>
  </div>
</template>
<script>
import modelNewIframeLoading from '@/components/Home/ProjectBoot/modelNewIframeLoading'
export default {
  name: 'modelNewShareLoad',
  components:{
    modelNewIframeLoading, 
  },
  data(){
    return{
      featureID: '',
      VaultID: '',
      showIframe: false,
      shareID: '',
      psdVisible: false,
      psdform: { // 分享密码弹窗
        psd: ''
      },
    }
  },
  created(){
    this.VaultID = this.$route.params.PID;
    this.shareID = this.$route.params.MID;
  },
  mounted(){
    this.handlePsdConfirm(0);
  },
  methods: {
    handlePsdConfirm(num){
      let _this = this;
      let _data = {
          "Id": this.shareID,
          "Password": num == 0 ? '' :this.psdform.psd
        }
      _this.$axios
        .post(
          `${window.bim_config.webserverurl}/api/v1/model/share/publish`,
          _data
        )
        .then((x) => {
          // 404=数据不存在   400=请输入密码  401=密码错误   403=当前分享已过期
          if (x.data.Ret == 1) {
            this.featureID = x.data.Data.ModelId
            this.showIframe = true;
            this.psdVisible = false;
          }else if (x.data.Ret == 400) {
            this.showCancel = true;
            this.psdVisible = true
          } else {
            this.$message.error(x.data.Msg)
          } 
        })
        .catch((x) => {
          console.log(x);
        });
    }
  }
}
</script>
<style lang="scss" scoped>
.model-iframe-content{
  position: fixed;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  width: 100%;
  height: 100%;
}
.link-body{
  margin: 10px 20px;
  text-align: left;
  .link-top, .link-mid, .link-btm{
    width: 100%;
    margin-bottom: 24px;
  }
  
  .el-radio{
    line-height: 26px;
  }
  .top-title{
    display:flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    .title{
      font-size: 14px;
    }
  }
  .share-title{
    font-size: 14px;
  }
  .share-link{
    width: 100%;
    height: 32px;
    line-height: 24px;
    border: 1px solid #ccc;
    border-radius: 6px;
    font-size: 14px;
    padding: 4px;
    margin: 4px 0;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    cursor: pointer;
  }
  .share-txt{
    font-size: 12px;
  }
  
  .share-radio{
    color: #fff;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    padding-top: 8px;
    /deep/ .el-radio{
      margin-bottom: 8px;
    }
    
  }
  .cancelBtn{
    width: 70px;
    height: 24px;
    line-height: 24px;
    text-align: center;
    font-size: 12px;
    border: 1px solid #98A3B3;
    border-radius: 4px;
    background: transparent;
    cursor: pointer;
    &:hover{
      border: 1px solid #1890ff;
      color: #1890ff;
    }
  }
}
._css-addBtnCtn {
  display: flex;
  flex-direction: row-reverse;
  height: 64px;
  align-items: center;
  box-sizing: border-box;
  padding-right: 8px;
}
</style>
<style scoped>
.link-body /deep/ .el-input__inner{
  border:1px solid rgba(0,0,0,.09) !important;
  border-width: 1px !important;
  border-radius: 4px;
}
</style>