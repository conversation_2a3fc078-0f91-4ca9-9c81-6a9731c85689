<template>
    <div class="Model_ViewList">
        <div class="title"><font class="l">视图列表</font><i class="icon-suggested-close close" @click="$emit('closeViewList')"></i></div>
        <div class="con">
            <div class="views">
                <ul class="unit" v-for="item in modelList" :key="item.ID">
                    <li class="view" v-for="viewItem in item.Views" :key="item.ID +'@'+ viewItem.ID" @click="SaveCurrentViews(item.ID,viewItem.ID)">
                        <span class="t" :title="item.CreateDate">{{item.Name}}</span>
                        <i :class="currentViewID ==item.ID +'@'+ viewItem.ID?'icon-checkbox-Radio-Selected':'icon-checkbox-Radio'"></i>
                        <img :src="item.Thumbnail==''?require('../../../../assets/svgs_loadbyurl/model-Default.svg'):'data:image/png;base64,'+item.Thumbnail" />
                        <span class="viewName"><p>{{viewItem.Name}}</p></span>
                    </li>
                </ul>
            </div>
        </div>
        <div class="btns">
            <button class="white" @click="$emit('closeViewList')">取消</button>
            <button class="blue" @click="openView">打开视图</button>
        </div>
    </div>
</template>
<script>
export default {
    name:'ModelViewList',
    data(){
        return {
            modelList:[],
            currentViewID:""
        };
    },
    props:{
        ProjectID:{
            type:String,
            required:false,
        },
        Model:{
            type:Object,
            required:false,
        },
        modelPhase:{
            type:String,
            required:false,
        },
        StopLoading:{
            type:Number,
            required:false,
        }
    },
    created(){
        let ModelId=this.Model.ID;
        //debugger;
        if(this.StopLoading==1)
            return;
        this.modelList=[];
        this.$axios.get(this.$urlPool.base_ModelApi+this.$urlPool.GetModelInfo+"?ProjectID="+this.ProjectID+"&ModelID="+ModelId).then(res=>{
            var ViewItems=res.data.Views;
            this.modelList.push({ID:res.data.ID,Name:res.data.Name,CreateDate:res.data.CreateTime,Thumbnail:res.data.Thumbnail,Views:ViewItems});
        });
    },
    mounted(){

    },
    methods:{
        SaveCurrentViews(ModelId,ViewId)
        {
            this.currentViewID=ModelId +'@'+ ViewId;
        },
        openView()
        {
            if(this.currentViewID!=""){
                this.$emit('openView',this.Model,this.currentViewID.split('@')[1],'');
                this.$emit('closeViewList');
            }
            else{
                this.$message({type:"warning",message:"清选择要打开的视图"});
            }
        }
    },
    watch:{
        Model(val)
        {
            let ModelId=val.ID;
            //debugger;
            if(this.StopLoading==1)
                return;
            this.modelList=[];
            this.$axios.get(this.$urlPool.base_ModelApi+this.$urlPool.GetModelInfo+"?ProjectID="+this.ProjectID+"&ModelID="+ModelId).then(res=>{
                var ViewItems=res.data.Views;
                this.modelList.push({ID:res.data.ID,Name:res.data.Name,CreateDate:res.data.CreateTime,Thumbnail:res.data.Thumbnail,Views:ViewItems});
            });
        }
    }
}
</script>
<style scoped>
ul{padding: 0px;margin: 0px;}li{padding: 0px;margin: 0px;}
.Model_ViewList{overflow: hidden;background-color: #fff;box-shadow:0px 13px 24px -17px rgba(11,41,62,0.8);border-radius:4px;font-family:PingFangSC-Medium;width:534px;height:592px;padding:0px 16px 16px 16px;}
.Model_ViewList .title{width:100%;height:48px;font-size:16px;color:#333;line-height:48px;text-align:left;font-weight:bold; position: relative;overflow: hidden;}
.Model_ViewList .title .close{width:18px;height:18px;position: absolute;right:0px;top:15px;display:block;}
.Model_ViewList .title .close::before{float:left;width:18px;height:18px;display:block;text-align:left;text-indent:0px;cursor: pointer;}
.Model_ViewList .con{width:100%;height:calc(100% - 38px - 48px - 22px);margin-top:20px;overflow:hidden;line-height:50px;}
.Model_ViewList .con .form{width:100%;height:auto;}
.Model_ViewList .con .form .txt{display:block;width:calc(100% - 24px - 16px);height: 50px;background-color:#fafafa;border-radius:2px;padding-left:24px;font-size:0px;position: relative;}
.Model_ViewList .con .form .txt font{display:inline-block;width:70px;height:100%;font-size:14px;text-align:left;text-indent:5px;font-weight: bold}
.Model_ViewList .con .form .txt input{display:inline-block;width:calc(100% - 70px);height:100%;font-size:14px;border:none;background-color:#fafafa;outline:none;}
.Model_ViewList .con .form .txt i{position: absolute;display: block;top:17px;left:8px;}
.Model_ViewList .con .views{width:calc(100% + 11px);height:100%;overflow-x:hidden;overflow-y:scroll;margin-top:24px;}
.Model_ViewList .con .views .unit{display:block;width:100%;height:auto;float:left;text-align:left;font-size:0px;margin-left:15px;align-content: flex-start;
  flex-wrap: wrap;
  display: flex;}
.Model_ViewList .con .views .unit .view{display: inline-block;width:162px;height:172px;position: relative;border-radius:2px;box-shadow:0px 1px 1px 0px rgba(0,21,41,0.12);cursor:pointer;overflow:hidden;margin:5px 0px 0px 5px;}
.Model_ViewList .con .views .unit .view.sel{border:1px solid rgba(24,144,255,1);width:160px;height:170px;border-radius:2px;}
.Model_ViewList .con .views .unit .view:hover{box-shadow:0px 13px 24px -17px rgba(11,41,62,0.8);}
.Model_ViewList .con .views .unit .view .t{width:calc(100% - 24px);height:32px;text-align:center;line-height:32px;background-color:rgba(0,0,0,0.45);position: absolute;display: block;color:#fff;font-size:12px;overflow:hidden;text-overflow:ellipsis;white-space:nowrap;padding:0px 12px 0px 12px;}
.Model_ViewList .con .views .unit .view .viewName{width:calc(100% - 24px);height:52px;font-size:13px;padding:0px 12px 0px 12px;background-color:#fafafa;display:block;line-height:52px;position:relative;}
.Model_ViewList .con .views .unit .view .viewName p{margin:0px;overflow: hidden;font-size:12px;line-height:27px;width:calc(100% - 24px);text-overflow: ellipsis;display: -webkit-box;-webkit-line-clamp: 2;-webkit-box-orient: vertical;position:absolute;top:50%;left:50%;transform: translate(-50%,-50%);}
.Model_ViewList .con .views .unit .view img{width:100%;height:calc(100% - 52px);display: block;}
.Model_ViewList .con .views .unit .view i{display:block;position:absolute;width:15px;height:15px;right:8px;bottom:59px;}

.Model_ViewList .btns{width:100%;height:38px;line-height:38px;text-align:right;}
.Model_ViewList .btns button{width:auto;height: 32px;border:none;outline: none;border-radius: 3px;cursor: pointer;font-size:12px; letter-spacing:1px;padding:0px 10px 0px 10px;}
.Model_ViewList .btns button:hover{opacity:0.8;}
.Model_ViewList .btns .blue{background-color:#1890FF;color:#fff; }
.Model_ViewList .btns .white{background-color:transparent;color:#666;}
::-webkit-scrollbar{width:20px; height:8px;}
::-webkit-scrollbar-thumb{border-radius: 12px;border:6px solid transparent;box-shadow: 8px 0px 0px #A5ADB7 inset;}
::-webkit-scrollbar-thumb:hover {box-shadow: 8px 0px 0px #4A4A4A inset;} 
</style>

