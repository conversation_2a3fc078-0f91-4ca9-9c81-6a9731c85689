<template>
    <div class="Model_merge">
        <div class="title"><font class="l">合并模型</font><i class="icon-suggested-close close" @click="$emit('CloseMergeForm')"></i></div>
        <div class="con">
            <ul>
                <li>
                    <span>名称</span>
                    <input v-model="txtMergeName" type="text" />
                </li>
            </ul>
        </div>
        <div class="btns">
            <button class="white" @click="$emit('CloseMergeForm')">取消</button>
            <button class="blue" @click="saveMerge">合并</button>
        </div>
    </div>
</template>
<script>
export default {
    name:'MergeForm',
    data(){
        return {
            txtMergeName:''
        };
    },
    props:{
        selViews:{
            type:Array,
            required:true,
        },
        modelPhase:{
            type:String,
            required:true,
        }
    },
    methods:{
        saveMerge()
        {
            if(this.txtMergeName=='')
            {
                this.$message({type:'warning',message:'请输入合并名称'});
                return;
            }
            var PostData=new FormData();
            var session_Account = this.$staticmethod.Get("Account");
            var bimcomposerId=this.$staticmethod._Get("bimcomposerId");
            var MNStr="";
            for(let i=0;i<this.selViews.length;i++)
            {
                MNStr+=this.selViews[i].SelModelID+"@"+this.selViews[i].SelViewId;
                if(i<this.selViews.length-1)
                MNStr+="^";
            }
            PostData.append("ProjectID",bimcomposerId);
            PostData.append("Model",JSON.stringify({
                "Name":this.txtMergeName,
                "IsMerge":true,
                "Creator":this.$staticmethod.Get("UserId"),
                "Model_N_View":MNStr,
                "Description":"",
                "ProjectID":bimcomposerId,
                "Phase":this.modelPhase
            }));

            //debugger;

            // this.$emit("LoadingModel");
            // var config={
            //     headers: {"Content-Type": "multipart/form-data"}
            // };
            // this.$axios.post(this.$urlPool.base_ModelApi+this.$urlPool.C1reateMergeModel, PostData,config).then(res=>{
            //     this.$message({type:"success",message:"合并成功"});
            //     // this.$emit("UnLoadingModel");
            //     this.$emit("reloadModellist");
            //     this.$emit("CloseMerge");
            // }).catch(error=>{});
            var _this=this;
            var oReq = new XMLHttpRequest();
            oReq.open("POST", _this.$urlPool.base_ModelApi+_this.$urlPool.CreateMergeModel, true);
            oReq.onload = function (xhr) {
                if (oReq.status == 200) {
                    _this.$message({type:"success",message:"合并成功"});
                    _this.$emit("reloadModellist");
                    _this.$emit("CloseMerge");
                }
                else {
                    top.dialogAlert(oReq.responseText, -1);
                    Finish = 1;
                }
            };
            oReq.send(PostData);
        }
    }
}
</script>
<style scoped>
ul{padding: 0px;margin: 0px;}li{padding: 0px;margin: 0px;}
.Model_merge{overflow: hidden;background-color: #fff;box-shadow:0px 13px 24px -17px rgba(11,41,62,0.8);border-radius:4px;font-family:PingFangSC-Medium;padding:0px 24px 16px 24px;}
.Model_merge .title{width:100%;height:48px;font-size:16px;color:#333;line-height:48px;text-align:left;font-weight:bold; position: relative;overflow: hidden;}
.Model_merge .title .close{width:18px;height:18px;position: absolute;right:0px;top:15px;display:block;}
.Model_merge .title .close::before{float:left;width:18px;height:18px;display:block;text-align:left;text-indent:0px;cursor: pointer;}
.Model_merge .con{width:100%;height:calc(100% - 32px - 48px - 20px);margin-top:20px;overflow:hidden;line-height:50px;}

.Model_merge .btns{width:100%;height:32px;line-height:32px;text-align:right;font-size:0px;}
.Model_merge .btns button{width:auto;height: 32px;border:none;outline: none;border-radius: 3px;cursor: pointer;font-size:12px; letter-spacing:1px;font-size:12px;padding:0px 12px 0px 12px;}
.Model_merge .btns button:hover{opacity:0.8;}
.Model_merge .btns .blue{background-color:#1890FF;color:#fff; }
.Model_merge .btns .white{background-color:transparent;color:#666;}
.Model_merge .btns .delete{background-color:transparent;color:rgba(245,34,45,1);float:left;}
::-webkit-scrollbar{width:20px; height:8px;}
::-webkit-scrollbar-thumb{border-radius: 12px;border:6px solid transparent;box-shadow: 8px 0px 0px #A5ADB7 inset;}
::-webkit-scrollbar-thumb:hover {box-shadow: 8px 0px 0px #4A4A4A inset;} 


.Model_merge .con ul{
    width:100%;height:100%;overflow:hidden;
}
.Model_merge .con ul li{
    width: 100%;height:70px;text-align: left;line-height: 0px;font-size:0px;margin-bottom: 5px;
}
.Model_merge .con ul li span,.Model_merge .con ul li input{
    width:100%;display:inline-block;
}
.Model_merge .con ul li span{
    height:20px;line-height:20px;font-size:12px;
}
.Model_merge .con ul li input{
    height:33px;line-height:33px;outline:none;border:none;border-bottom:1px solid #000;font-size:16px;
}
.Model_merge .con ul li .editOfGISView{
    width:auto;height:40px;float:right;line-height:40px;padding: 0px 18px 0px 18px;border-radius: 2px;color:#fff;background-color:#3ca48c;display:block;border:none;outline:none;cursor: pointer;
}
</style>
