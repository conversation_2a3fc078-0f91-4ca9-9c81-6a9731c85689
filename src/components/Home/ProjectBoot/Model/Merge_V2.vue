<template>
    <div class="Model_merge" v-loading='isLoading'>
        <div class="head">
            <div class="btns">
                <button @click="$emit('CloseMerge')"><i class="icon-suggested-close-fill"></i>模型列表</button>
                <button class="blue" @click="Merge()">合并</button>
            </div>
            <div class="steps">
                <ul>
                    <li class="sel">合并模型</li>
                </ul>
            </div>
        </div>
        <div class="con">
            <div class="mergeList" data-debug="Merge_V2_line15 222">
                <div class="unit one">
                    <span class="title">模型列表</span>
                    <ul>
                        <li v-for="item in AllModelInfo" :class="selModelID==item.ID?'sel':''" :key="item.ID" @click="selModelID=item.ID;selModelName=item.Name;">
                            <el-tooltip effect="dark"  :content="item.Name" placement="top-end">
                                <span>{{item.Name}}</span>
                            </el-tooltip>
                            <i :title="'该模型有'+item.Views.length+'个视图'">{{item.Views.length}}</i>
                        </li>
                    </ul>
                </div>
                <div class="unit two">
                    <span class="title">视图列表</span>
                    <ul>
                        <li v-for="item in viewList" class="" :key="item.ID" @click="ChangeSelViewId(item.ID,item.Name)">
                            <i :class="$staticmethod.JSONArreyIndexOf(selViews,{'SelModelID':selModelID,'SelViewId':item.ID,'SelModelName': selModelName,'SelViewName':item.Name})!=-1?'icon-checkbox-Radio-Selected':'icon-checkbox-Radio'"></i>
                            <el-tooltip effect="dark" :content="item.Name" placement="top-end">
                                <span>{{item.Name}}</span>
                            </el-tooltip>
                        </li>
                    </ul>
                </div>
                <div class="unit three">
                    <span class="title">合并模型列表</span>
                    <ul>
                        <li v-for="item in selViews" :key="item.SelViewId">
                            <el-tooltip effect="dark"  :content="item.SelModelName" placement="top-end">
                                <span class="t" :MoldeId='item.SelModelID' >{{item.SelModelName}}</span>
                            </el-tooltip>
                            <el-tooltip effect="dark"  :content="item.SelViewName" placement="top-end">
                                <span class="i">{{item.SelViewName}}</span>
                            </el-tooltip>
                            <i class="icon-interface-model-delete del" title="删除" @click="removeView(item)"></i>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
        <MergeForm class="mf" v-if="showMerge" :selViews="selViews" :modelPhase="modelPhase" @CloseMerge="$emit('CloseMerge')" @CloseMergeForm="showMerge=false;IsDialogCovery=false;" @reloadModellist="$emit('reloadModellist')"></MergeForm>
        <div v-show="IsDialogCovery" class="Covery"></div>
    </div>
</template>
<script>
// 我改你的代码向来是难以下手
import MergeForm from '@/components/Home/ProjectBoot/Model/Merge_V2/MergeForm'

export default {
    name:'Model_merge',
    components:{
        MergeForm
    },
    data(){
        return {
            isLoading:false,
            showMerge:false,
            IsDialogCovery:false,

            AllModelInfo:[],//所有的模型、视图 数据
            ModelList:[],//所有模型数据
            viewList:[],//当前模型的视图数据
            selViewList:[],//所有选中的视图

            selModelID:'',//当前选中的modelID
            selModelName:'',//选中模型名
            selViews:[],//当前选中的ViewIds
        };
    },
    props:{
        AllModelIds:{
            type:String,
            required:true
        },
        modelPhase:{
            type:String,
            required:true
        }
    },
    methods:{
        ChangeSelViewId(ID,ViewName)
        {
            if(this.$staticmethod.JSONArreyIndexOf(this.selViews,{'SelModelID':this.selModelID,'SelViewId': ID,'SelModelName': this.selModelName,'SelViewName':ViewName})!=-1)
            {
               let newArrey=[];
               for(let i=0;i<this.selViews.length;i++)
               {
                   if(this.selViews[i]!={'SelModelID':this.selModelID,'SelViewId': ID,'SelModelName': this.selModelName,'SelViewName':ViewName})
                        newArrey.push(this.selViews[i]);
               }
               this.selViews=newArrey;
            }
            else{
                for(let i=0;i<this.selViews.length;i++)
                {
                    if(this.selViews[i].SelModelID==this.selModelID)
                    {
                        this.selViews.splice(i,1);
                        this.selViews.push({'SelModelID':this.selModelID,'SelViewId':ID,'SelModelName':this.selModelName,'SelViewName':ViewName});
                        return;
                    }
                }
                this.selViews.push({'SelModelID':this.selModelID,'SelViewId':ID,'SelModelName':this.selModelName,'SelViewName':ViewName});
            }
        },
        removeView(item)
        {
            for(let i=0;i<this.selViews.length;i++)
            {
                if(this.selViews[i].SelViewId==item.SelViewId)
                {
                    this.selViews.splice(i,1);
                }
            }
        },
        Merge()
        {
            if(this.selViews.length<2)
            {
                this.$message({type:'warning',message:'必须选择两个或以上视图进行合并'});
                return;
            }
            this.showMerge=true;
            this.IsDialogCovery=true;
        }
    },
    watch:{
        selModelID(val)
        {
            for(let i=0;i<this.AllModelInfo.length;i++)
            {
                if(this.AllModelInfo[i].ID==val)
                    this.viewList=this.AllModelInfo[i].Views;
            }
        }
    },
    created(){
        
        var ModelIds=this.AllModelIds;
        ModelIds=ModelIds.substr(ModelIds.length-1,1)==','?ModelIds.substr(0,ModelIds.length-1):ModelIds;
        
        if (ModelIds == '') {
            return;
        }
        
        var ModelIDArrey=ModelIds.split(',');
        if(ModelIDArrey.length==0)
            return;
        var bimcomposerId=this.$staticmethod._Get('bimcomposerId');
        var complete=1,num=-1;//完成标志、执行次数
        var _this=this;
        this.isLoading=true;

        // 请求接口
        // -------
        var _Url = `${
            _this.$staticmethod.getBIMServer()
        }/api/Prj/GetModels_Simple?Token=${this.$staticmethod.Get('Token')}`;
        _this.$axios({
            url: _Url,
            method: "post",
            data: _this.$qs.stringify({
                ProjectID: bimcomposerId,
                ModelIDs: ModelIds
            })
        }).then(x => {
            _this.isLoading=false;
            _this.AllModelInfo = x.data;

            // 默认值
            // -----
            if (_this.AllModelInfo && _this.AllModelInfo.length > 0) {
                _this.viewList=_this.AllModelInfo[0].Views;
                _this.selModelID=_this.AllModelInfo[0].ID;
                _this.selModelName=_this.AllModelInfo[0].Name;
            }

        }).catch(x => {
            _this.isLoading=false;
            console.error(x);
        });
        return;

        // 这写的什么烂代码？setInterval 里请求吗？
        // if else 不加大括号真是醉了！
        // var t=setInterval(function(){
        //     if(complete==0) 
        //         return;
        //     else
        //         complete=0;
        //     num++;
        //     // /api/Prj/GetModel
        //     // 以下属性值不需要返回：
        //     //      Children
        //     //      CreateTime
        //     //      Creator
        //     //      CurrentVersion
        //     //      Description
        //     //      ExtentionInfo
        //     // 返回 ID
        //     //      IsMerge
        //     //      MeshCompression
        //     //      ModelMD5
        //     //      Model_N_View
        //     // 返回 Name
        //     //      Phase
        //     //      Platform
        //     //      PlatformVersion
        //     //      ProjectID
        //     //      Status
        //     //      Thumbnail
        //     // 返回 Views
        //     var getmodelurl = `${window.bim_config.b1imserverurl}/api/Prj/GetModel_Simple?ProjectID=${bimcomposerId}&ModelID=${ModelIDArrey[num]}`;
        //     _this.$axios.get(getmodelurl).then(res=>{
        //         var item=res.data;
        //         // 你不加个判断吗？
        //         if (!item) {
        //             _this.isLoading=false;
        //             return;
        //         }
        //         console.log(item);
        //         // var ItemView1List=item.Views;
        //         _this.AllModelInfo.push(item);
        //         //_this.ModelList.push({'ModelID':item.ID,'ModelName':item.Name,'Num':item.Views.length});
        //         complete=1;
        //         if(num==ModelIDArrey.length-1)
        //         {
        //             _this.viewList=_this.AllModelInfo[0].Views;
        //             _this.selModelID=_this.AllModelInfo[0].ID;
        //             _this.selModelName=_this.AllModelInfo[0].Name;
        //             _this.isLoading=false;
        //             clearInterval(t);
        //             return;
        //         }
        //     });
        // },100);
    }
}
</script>
<style scoped>
ul{padding:0px;margin: 0px;}li{padding:0px;margin: 0px;list-style-type:none;}
.Model_merge{
    background-color: #f0f2f5;font-family:PingFangSC-Regular;overflow: hidden;
}
.Model_merge .head{
    width:100%;height:100px;background-color: #fff;box-shadow:0px 1px 1px 0px rgba(0,21,41,0.12);
}
.Model_merge .head .btns{
    width:calc(100% - 40px);overflow: hidden;position:absolute;top:20px;left:20px;
}
.Model_merge .head .btns button{
    width:auto;height:32px;padding: 0px 8px 0px 8px;padding-left:32px;position: relative;line-height:32px;border:none;outline:none;cursor: pointer;background-color:transparent;float:left;
}
.Model_merge .head .btns button.blue{
    height:35px;float:right;background-color: #1890FF;border-radius: 2px;text-align:center;color:#fff;font-size:12px;padding:0px 30px 0px 30px;
}
.Model_merge .head .btns button:hover{
    background-color:#eaeaea;
}
.Model_merge .head .btns button.blue:hover{
    background-color: #80C2FF;
}
.Model_merge .head .btns button i{
    position: absolute;top:6px;left:8px;width:20px;height: 20px;
}

.Model_merge .head .steps{
    width:100%;height:40px;margin-top:60px;position: relative;float:left;
}
.Model_merge .head .steps ul{
    width:auto;height:100%;position: relative;margin:0auto;position: absolute;top:50%;left:50%;transform:translate(-50%,-50%);
}
.Model_merge .head .steps ul li{
    width:200px;height:100%;line-height:40px;color:#1890FF;font-weight:400;float:left;
}
.Model_merge .head .steps ul li.sel{
    border-bottom: 2px solid rgba(24,144,255,1);height:calc(100% - 2px);
}

.Model_merge .con{
    width:100%;height:calc(100% - 100px + 11px);position: relative;overflow-x:scroll;overflow-y:hidden;text-align: center;
}
.Model_merge .con .mergeList{
    width:auto;height:calc(100% - 80px);overflow: hidden;position: relative;display:inline-block;padding-top:40px;
}
.Model_merge .con .mergeList .unit{
    width:325px;height:100%;overflow:hidden;display:block;float: left;text-align: left;border-right:1px solid rgba(0,0,0,0.09)
}
.Model_merge .con .mergeList .unit .title{
    width:calc(100% - 24px);height:28px;line-height:28px;font-size:20px;font-weight:500;color:#000;text-align:left;display: inline-block;padding-left:24px;
}
.Model_merge .con .mergeList .unit ul{
    width:calc(100% - 24px + 11px);height:calc(100% - 28px - 22px);margin-top:22px;padding-left:24px;overflow-x:hidden;overflow-y: scroll;
}
.Model_merge .con .mergeList .unit ul li{
    font-size:14px;
}
.Model_merge .con .mergeList .unit ul li:hover{
    background-color:#e6e8eb;cursor: default;
}
.Model_merge .con .mergeList .unit ul li.sel{
    background-color:#e6e8eb;
}
.Model_merge .con .mergeList .unit.one ul li{
    height:50px;width:calc(100% - 48px);line-height:50px;position: relative;padding-left:24px;
}
.Model_merge .con .mergeList .unit.one ul li{
    overflow: hidden;text-overflow: ellipsis;white-space: nowrap;
}
.Model_merge .con .mergeList .unit.one ul li span{
    width:calc(100% - 24px - 32px);height:100%;display:block;border-radius: 2px;overflow:hidden;text-overflow:ellipsis;white-space: nowrap;
}
.Model_merge .con .mergeList .unit.one ul li i{
    width:24px;height:20px;display:block;position: absolute;top:17px;right:16px;background-color:#3b91ff;border-radius: 4px;color:#fff;text-indent: 0px;text-align:center;line-height:20px;font-style: normal;font-size: 12px;
}
.Model_merge .con .mergeList .unit.two ul li{
    text-indent:56px;height:64px;border-bottom: 1px solid rgba(0,0,0,0.04);width:calc(100% - 24px);line-height:64px;position: relative;
}
.Model_merge .con .mergeList .unit.two ul li i{
    width:16px;height:16px;position: absolute;left:24px;top:24px;text-indent: 0px;text-align: left;font-size:16px;cursor: pointer;
}
.Model_merge .con .mergeList .unit.three ul li{
    height:70px;line-height:70px;background:#e7e9eb;margin-bottom: 8px;width:calc(100% - 24px - 32px);box-shadow: 0px 1px 1px #cecece;padding:0px 16px 0px 16px;cursor:default;position: relative;
}
.Model_merge .con .mergeList .unit.three ul li:hover{
    background-color:#e6ebf1;
}
.Model_merge .con .mergeList .unit.three ul li .del{
    position: absolute;bottom:8px;right:8px;display:none;cursor: pointer;
}
.Model_merge .con .mergeList .unit.three ul li:hover .del{
    display:block;color:#3b91ff;
}
.Model_merge .con .mergeList .unit.three ul li .t{
    width:100%;height:55%;line-height:45px;font-size:14px;color:#000;display: block;overflow: hidden;text-overflow: ellipsis;white-space:nowrap;
}
.Model_merge .con .mergeList .unit.three ul li .i{
    width:100%;height:45%;line-height:25px;font-size:12px;color:rgba(0,0,0,0.34);display: block;overflow: hidden;text-overflow: ellipsis;white-space:nowrap;
}
.btnMerge{
    background-color: transparent;border:none;outline:none;cursor: pointer;width:auto;height:25px;line-height:25px;position: relative;display: block;padding-left: 25px;font-size:14px;
}
.btnMerge:hover{
    color:#3b91ff;
}
.btnMerge i{
    position: absolute;top:4px;left:0px;display:block;color:#3b91ff;
}
.btnMerge.unset{
    opacity: 0.6;cursor:not-allowed;
}
.mf{
    width:410px;height:210px;position: absolute;top:50%;left:50%;transform:translate(-50%,-50%);z-index:999;
}
.Covery{
    width:100%;height:100%;background-color:rgba(0,0,0,0.4);position: absolute;top:0px;left: 0px;z-index: 1;
}
</style>
