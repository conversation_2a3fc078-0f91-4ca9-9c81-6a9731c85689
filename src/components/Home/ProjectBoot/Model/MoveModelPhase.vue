<template>
    <div class="Model_MovePhase" v-loading="loadingSavePhase">
        <div class="title"><font class="l">移动【</font><font class="modelName" :title="modelName">{{modelName}}</font><font class="r">】到</font><i class="icon-suggested-close close" @click="$emit('CloseMovePhase')"></i></div>
        <div class="con">
            <!-- <span class="word" v-for="item in PhaseList" :key="item.BusinessCode" @click="selPhase=item.BusinessCode;">
                {{item.MenuName}}
                <i v-show="item.BusinessCode==selPhase" class="icon-checkbox-Selected-Disabled-dis-blue"></i>
            </span> -->
            <el-tree
              class="el-tree-cus"
              node-key="Id"
              default-expand-all
              highlight-current
              :indent="16"
              :data="PhaseList"
              :props="elTreeProps"
              @node-click="onPhaseClick"
              >
              <div class="tree-node-cus" :class="{disabled:data.disabled}" slot-scope="{ node, data }">
                <div class="node-label">
                  <span>{{ node.label }}</span>
                  <i v-show="modelPhase!==selPhase && data.BusinessCode==selPhase" class="icon-checkbox-Selected-Disabled-dis-blue"></i>
                </div>
              </div>
            </el-tree>
        </div>
        <div class="btns">
            <button class="white" @click="$emit('CloseMovePhase')">取消</button>
            <button class="blue" :disabled='selPhase==modelPhase||isEmptyStr(selPhase)' :class="(selPhase==modelPhase||isEmptyStr(selPhase))?'unenable':''"  @click="savePhase">确定</button>
        </div>
    </div>
</template>
<style scoped>
    .Model_MovePhase{overflow: hidden;background-color: #fff;box-shadow:0px 13px 24px -17px rgba(11,41,62,0.8);border-radius:4px;font-family:PingFangSC-Medium;}
    .Model_MovePhase .title{width:calc(100% - 16px);height:48px;font-size:16px;color:#333;line-height:48px;text-align:left;font-weight:bold;padding-left:16px; position: relative;overflow: hidden;}
    .Model_MovePhase .title .modelName{display: block;max-width:calc(100% - 130px);height:100%;line-height:48px;overflow:hidden;text-overflow: ellipsis;white-space: nowrap; float:left;}
    .Model_MovePhase .title .r{float:left;}.Model_MovePhase .title .l{float:left;}
    .Model_MovePhase .title .close{width:18px;height:18px;position: absolute;right:15px;top:15px;display:block;}
    .Model_MovePhase .title .close::before{float:left;width:18px;height:18px;display:block;text-align:left;text-indent:0px;cursor: pointer;}
    .Model_MovePhase .btns{width:calc(100% - 16px);height:64px;line-height:64px;text-align: right;}
    .Model_MovePhase .con{width:calc(100% + 11px);height:calc(100% - 64px - 48px - 22px);margin-top:20px;overflow-x:hidden;overflow-y:scroll;}
    .Model_MovePhase .el-tree-cus {margin: 0 16px;}
    .Model_MovePhase .el-tree-cus .tree-node-cus {width:100%}
    .Model_MovePhase .el-tree-cus .tree-node-cus .node-label {display: flex;padding-right:8px;justify-content: space-between;align-items: center;}
    /* .Model_MovePhase .el-tree-cus .tree-node-cus.disabled:hover {cursor: not-allowed;} */
    .Model_MovePhase .con .word{width:calc(100% - 30px);height:50px;background-color:#fff;display:block;line-height: 50px;position: relative;padding-left:30px;text-align:left;}
    .Model_MovePhase .con .word i{position: absolute;top:17px;right:20px;}
    .Model_MovePhase .con .sel{background-color:#f9f9f9;}.Model_MovePhase .con .word:hover{background-color:#f9f9f9;}
    .Model_MovePhase .btns button{width:56px;height: 32px;border:none;outline: none;border-radius: 3px;cursor: pointer;font-size:12px; letter-spacing:1px;}
    .Model_MovePhase .btns .blue{background-color:#1890FF;color:#fff; }
    .Model_MovePhase .btns .white{background-color:transparent;color:#666;}
    .Model_MovePhase .btns .unenable{cursor:not-allowed;background-color:#ddd;}
    ::-webkit-scrollbar{width:20px; height:8px;}
    ::-webkit-scrollbar-thumb{border-radius: 12px;border:6px solid transparent;box-shadow: 8px 0px 0px #A5ADB7 inset;}
    ::-webkit-scrollbar-thumb:hover {box-shadow: 8px 0px 0px #4A4A4A inset;}
</style>
<script>

import { EventBus } from "@static/event.js";
export default {
    name:"MoveModelPhase",
    data(){
        return {
            PhaseList:[],
            loadingSavePhase:false,
            selPhase:'',
            elTreeProps: {
              label: 'MenuName',
              children: 'Children'
            },
        };
    },
    props:{
        modelName:{
            type:String,
            required:false
        },
        modelId:{
            type:String,
            required:false
        },
        modelPhase:{
            type:String,
            required:false
        }
    },
    created(){
        this.getPhase();
    },
    methods:{
        getPhase(){
            let VaultID = this.$staticmethod._Get("organizeId");
            let _token = this.$staticmethod.Get("Token");
            this.$axios
            .get(`${this.$urlPool.GetModelMenuTree}?organizeId=${VaultID}&token=${_token}`)
            .then(res=>{
                if(res.data.Ret == 1){
                this.PhaseList = res.data.Data;

                }else{
                this.$staticmethod.debug(x);
                }
            })
            .catch(err=>{

            })
        },
        savePhase(){
            let VaultID = this.$staticmethod._Get("organizeId");
            this.$axios
            .get(`${this.$ip('newModelHttpUrl')}/Vault/UpdateFeaturePhase?VaultID=${VaultID}&FeatureID=${this.modelId}&Phase=${this.selPhase}`)
            .then(res=>{
                if (res.status === 200){
                    this.loadingSavePhase=false;
                    this.$message({message:"移动成功", type:"success"});
                    this.$emit('reloadModellist');
                    this.$emit("CloseMovePhase");
                    // EventBus.$emit("Rupdatephasecnt");
                }else{
                    this.$message({message:"移动失败", type:"error"});
                    this.loadingSavePhase=false;
                }
            })
            .catch(err=>{
                this.$message({message:"移动失败", type:"error"});
                this.loadingSavePhase=false;
            })


        },
        // el-tree中phase点击事件处理函数
        onPhaseClick(data) {
          this.selPhase = data.BusinessCode
          if(this.selPhase === this.modelPhase) {
            this.$message({
              showClose: true,
              message: '不允许移动到相同文件层级',
              type: 'warning'
            })
          }
        },
        // 验证是空的字符串
        isEmptyStr(targetStr) {
          return /^\s*$/i.test(targetStr)
        }
    }
}
</script>
