<template>
	<div class="_css-pano-lable">
		<div class="set-title">
			<p @click.stop="close">
				<i class="icon-arrow-left_outline"></i>标签管理
			</p>
			<p class="fc" @click="addMaterial">
				<i class="icon-interface-addnew"></i>
				添加
			</p>
		</div>
		<div class="set-list">
			<ul class="_css_bugs_and_bb_noWork css-miniscroll">
				<li v-for="item in tagOptionsArr" :key="item.LabelId">
					<div
            class="li-name"
						:style="getListStyle(item)"
					>
						{{ item.LabelName }}
					</div>
					<div class="edit">
						<i class="icon-interface-edit_se" @click.stop="editMaterial(item)"></i>
						<i class="icon-suggested-close i_close" @click.stop="predel(item)"></i>
					</div>
				</li>
			</ul>
		</div>
    <div
      class="maker"
      @click.stop="dialogConfig.show = false"
      v-show="dialogConfig.show"
    >
      <div class="center" @click.stop>
        <header>
          <span>{{ dialogConfig.type == 0 ? "添加新标签" : "修改标签" }}</span>
          <i
            class="icon-suggested-close"
            @click.stop="dialogConfig.show = false"
          ></i>
        </header>
        <main>
          <p>标签名称<el-input
            v-model="dialogConfig.input"
            placeholder="请输入标签名称"
          ></el-input></p>
          
          <div class="color-list">
            <div
              v-for="item in colorList"
              :key="item"
              :style="{ background: item }"
              @click.stop="colorListClick(item)"
            >
              <i
                class="icon-suggested-check"
                v-show="dialogConfig.selectColor == item"
              ></i>
            </div>
          </div>
          <div class="btn-wp">
            <div class="btns-right">
              <div @click.stop="cancelediting">取消</div>
              <div @click.stop="submit">确定</div>
            </div>
          </div>
        </main>
      </div>
    </div>
	</div>
</template>
<script>
export default {
	name: "PanoSetLable",
	components: {},
	data() {
		return {
      tagOptionsArr: [], // 标签
      dialogConfig: {
        show: false,
        input: "",
        id: "",
        selectColor: "",
        type: 0, //0新增
      },
      colorList: [
        "rgba(230, 126, 126, 1)",
        "rgba(192, 113, 86, 1)",
        "rgba(242, 174, 36, 1)",
        "rgba(87, 130, 217, 1)",
        "rgba(0, 170, 255, 1)",
        "rgba(3, 173, 173, 1)",
        "rgba(35, 173, 104, 1)",
      ],
    };
	},
  props: {
    tagOptions: {
      type: Array,
      default: [] 
    }
  },  
  watch: {
    tagOptions(val){
      this.tagOptionsArr = val
    }
  },
	mounted() {
    this.tagOptionsArr = this.tagOptions;
    // this.tagOptionsArr = [{"LabelId":"015dc5ed-5d28-4cdf-b8d9-3124a38fecf8","CreateTime":"2022-04-28T14:24:50","ModifyTime":"2022-04-28T17:18:15","DelMark":false,"EnableMark":true,"OrganizeId":"f3b4278d-8554-46fe-a670-2261aed3360e","BackGroundColor":"rgba(230, 126, 126, 1)","FontColor":"rgba(255, 255, 255, 1)","LabelName":"土建施工#1","LabelSort":0},{"LabelId":"078c2934-1e67-4434-b552-0e734185e1c1","CreateTime":"2022-05-27T15:07:35","ModifyTime":"2022-05-27T15:07:35","DelMark":false,"EnableMark":true,"OrganizeId":"f3b4278d-8554-46fe-a670-2261aed3360e","BackGroundColor":"rgba(35, 173, 104, 1)","FontColor":"rgba(255, 255, 255, 1)","LabelName":"监管库","LabelSort":0},{"LabelId":"4ba6fbd4-cc86-4871-b87d-a2b26be58c9c","CreateTime":"2022-04-28T14:31:22","ModifyTime":"2022-04-28T17:18:24","DelMark":false,"EnableMark":true,"OrganizeId":"f3b4278d-8554-46fe-a670-2261aed3360e","BackGroundColor":"rgba(87, 130, 217, 1)","FontColor":"rgba(255, 255, 255, 1)","LabelName":"土建施工#2","LabelSort":0},{"LabelId":"95ec8e26-5646-42bd-bc3c-35f217f050de","CreateTime":"2022-05-05T17:13:19","ModifyTime":"2022-05-05T17:44:10","DelMark":false,"EnableMark":true,"OrganizeId":"f3b4278d-8554-46fe-a670-2261aed3360e","BackGroundColor":"rgba(35, 173, 104, 1)","FontColor":"rgba(255, 255, 255, 1)","LabelName":"测试标签","LabelSort":0},{"LabelId":"9bc51211-d550-475d-918d-d1b867f452c5","CreateTime":"2022-04-28T17:18:41","ModifyTime":"2022-04-28T17:18:41","DelMark":false,"EnableMark":true,"OrganizeId":"f3b4278d-8554-46fe-a670-2261aed3360e","BackGroundColor":"rgba(192, 113, 86, 1)","FontColor":"rgba(255, 255, 255, 1)","LabelName":"土建施工#4","LabelSort":0},{"LabelId":"ace40693-b4ea-4a48-841a-ec2b0b60dee3","CreateTime":"2022-04-28T17:18:34","ModifyTime":"2022-04-28T17:18:34","DelMark":false,"EnableMark":true,"OrganizeId":"f3b4278d-8554-46fe-a670-2261aed3360e","BackGroundColor":"rgba(3, 173, 173, 1)","FontColor":"rgba(255, 255, 255, 1)","LabelName":"土建施工#3","LabelSort":0},{"LabelId":"d4c5df49-7969-4f72-aeff-ae51b1211951","CreateTime":"2022-07-26T09:25:04","ModifyTime":"2022-07-26T09:25:04","DelMark":false,"EnableMark":true,"OrganizeId":"f3b4278d-8554-46fe-a670-2261aed3360e","BackGroundColor":"rgba(0, 170, 255, 1)","FontColor":"rgba(255, 255, 255, 1)","LabelName":"珠三角测试","LabelSort":0},{"LabelId":"dfc74b0e-717b-4f9c-b44f-e3e6e89b82bd","CreateTime":"2022-10-26T17:30:03","ModifyTime":"2022-10-26T17:30:03","DelMark":false,"EnableMark":true,"OrganizeId":"f3b4278d-8554-46fe-a670-2261aed3360e","BackGroundColor":"rgba(230, 126, 126, 1)","FontColor":"rgba(255, 255, 255, 1)","LabelName":"阿斯顿法师法","LabelSort":0}]
  },
	methods: {
    close(){
      this.$emit("close")
    },
    // 点击添加标签
    addMaterial() {
      this.dialogConfig.selectColor = "rgba(230, 126, 126, 1)";
      this.dialogConfig.input = "";
      this.dialogConfig.type = 0;
      this.dialogConfig.show = true;
    },
    // 标签弹窗取消
    cancelediting() {
      this.dialogConfig.show = false;
    },
    // 标签弹窗确定
    submit() {
      // if (this.dialogConfig.input.length > 0) {
      if(!window.tool.GetValIsEmpty(this.dialogConfig.input)) {
					let _labelId = "";let _msg = "";
					this.dialogConfig.type == 0? _labelId = "" :  _labelId = this.dialogConfig.id;
					this.dialogConfig.type == 0? _msg = "添加成功" :  _msg = "修改成功";
          let data = {
						"labelId": _labelId,
						"organizeId": this.$staticmethod._Get("organizeId"),
						"backGroundColor": this.dialogConfig.selectColor,
						"fontColor": 'rgba(255, 255, 255, 1)',
						"labelName": this.dialogConfig.input,
						"labelSort": 0
          };
          this.$axios
            .post(
              `${window.bim_config.webserverurl}/api/Panorama/Label/Save?Token=${this.$staticmethod.Get('Token')}`,
              data
            )
            .then((res) => {
              if (res.data.Ret != -1) {
                this.$message({
                  message: _msg,
                  type: "success",
                });
                this.$parent.getTagOption();
                this.$parent.getList();
                this.dialogConfig.show = false;
              } else {
                this.$message.error(res.data.Msg);
              }
            })
						.catch(()=>{
							this.$message.error("服务器异常，请稍后再试");
						});
      } else {
        this.$message.error("请输入标签名称");
      }
    },
    colorListClick(item) {
      this.dialogConfig.selectColor = item;
    },
    editMaterial(item){
      this.dialogConfig.selectColor = item.BackGroundColor;
      this.dialogConfig.input = item.LabelName;
      this.dialogConfig.type = 1;
      this.dialogConfig.id = item.LabelId;
      this.dialogConfig.show = true;
    }, 
    getListStyle(item){
      let _s = {}
      let _ind = item.BackGroundColor.lastIndexOf(',')
      let str = item.BackGroundColor.slice(0,_ind) + ',0.1)'
      _s['background'] = str;
      _s['border'] = `1px solid  ${item.BackGroundColor}`
      _s['color'] = item.BackGroundColor
      return _s
    },
    predel(item) {
      var _this = this;
      _this
        .$confirm("确认删除该标签？", "操作确认", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        })
        .then((x) => {
          _this.del(item);
        })
        .catch((x) => {});
    },
    del(item) {
      let data = {
        Id: item.LabelId,
      };
      this.$axios
        .post(
          `${window.bim_config.webserverurl}/api/Panorama/Label/DelLabel?Token=${this.$staticmethod.Get('Token')}`,
					data
        )
        .then((res) => {
          if (res.data.Ret == 1) {
            this.$message({
              message: "删除成功",
              type: "success",
            });
            this.$parent.getTagOption();
            this.dialogConfig.show = false;
          }else {
						this.$message.error(res.data.Msg);
					}
        })
				.catch(()=>{
					this.$message.error('服务器异常，请稍后再试');
				});
    },
  },
};
</script>
<style lang="stylus" scoped rel="stylesheet/stylus">
._css-pano-lable{
  display: flex;
  flex-direction: column;
  .set-title{
    height: 48px;
    background: #F5F5F5;
    display: flex;
    align-content: center;
    line-height: 48px;
    padding: 0 16px 0 20px;
    justify-content: space-between; 
    width: calc(100% - 36px);
    p{
      font-size: 14px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #222222;
      i{
        cursor pointer;
        vertical-align: middle;
        padding-right: 4px;
      }
    }
    p.fc{
      color: #007AFF;
      i{
        padding-right: 8px
      }
    }
  }
  .set-list{
    ._css_bugs_and_bb_noWork li:hover .edit{
      display: flex;
    }
    ul{
      li{
        display: flex;
        margin-top: 20px;
        margin-right: 16px;

      }
      .li-name{
        height: 30px;
        line-height: 30px;
        width: 122px; 
        border-radius: 2px; 
        margin: 0 14px 0 16px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        padding: 0 10px;
      }
      
      .edit{
        display: none;
        i{
          color: #007AFF;
          font-size: 20px;
          margin-top: 5px; 
          margin-bottom: 5px;
          cursor: pointer; 
        }
        .i_close{
          margin-left:10px 
        }
      }
    }
  }
}
.maker {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1001;

  .center {
    width: 410px;
    height: 274px;
    background: #ffffff;
    border-radius: 4px;
    box-shadow: 0px 13px 24px -17px rgba(11, 41, 62, 0.8);
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    display: flex;
    flex-direction: column;

    header {
      height: 64px;
      line-height: 64px;
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      align-items: center;
      padding: 0 24px;

      span {
        font-size: 20px;
        color: rgba(0, 0, 0, 0.85);
      }

      i {
        color: #bfbfbf;
        cursor: pointer;
      }
    }

    main {
      padding: 0 24px;
      flex: 1;

      .btn-wp {
        display: flex;
        flex-direction: row;
        justify-content: flex-end;

        &>div {
          color: red;
          line-height: 40px;
          cursor: pointer;

          &.btns-right {
            display: flex;
            flex-direction: row;

            div {
              color: #ffffff;
              width: 76px;
              height: 40px;
              line-height: 40px;
              background: #1890FF;
              border-radius: 2px;
              margin-left: 16px;
              opacity: 0.8;

              &:hover {
                opacity: 1;
              }

              &:first-child {
                color: rgba(0, 0, 0, 0.85);
                background: #fff;
              }
            }
          }
        }
      }

      p {
        margin-top: 10px;
        font-size: 14px;
        color: rgba(0,0,0,.65);
        display: flex;
        line-height: 50px;
        background: #f8f8f8;
        padding-left: 20px;
        border-radius: 4px;
        /deep/ .el-input{
          width: calc(100% - 80px);
        }
      }

      .color-list {
        height: 80px;
        display: flex;
        flex-direction: row;
        align-items: center;

        div {
          width: 40px;
          height: 40px;
          border-radius: 4px;
          cursor: pointer;
          margin-left: 12px;
          display: flex;
          align-items: center;
          justify-content: center;
          color: #fff;

          &:first-child {
            margin-left: 5px;
          }
        }
      }
    }
  }
}

</style>