<template>
  <div class="_css-pano-all" @click="closeall()">
    <div class="btn-initiating-header">{{ headerTitle }}</div>
    <div class="paon-bottom-content">
      <!-- 分享对话框 -->
      <zdialog-function
      :init_title="'全景图分享'"
      :init_zindex="1002"
      :init_innerWidth="752"
      :init_width="752"
      init_closebtniconfontclass="icon-suggested-close"
      v-if="status_panosharing"
      @onclose="func_closepanosharing()"
      >
        <!-- 内容区域 -->
        <div class="_css-qrcodearea" slot="mainslot" @mousedown="_stopPropagation($event)">
          <!-- 左图，右链接及文字 -->
          <div class="_css-qrcodeandtext">
              <div class="_css-qrcode" :style="style_qrcodestyle()"></div>
              <div class="_css-qrcode-descs">
                <div class="_css-qrcode-desc-label">
                  请复制以下链接地址发送给同事或朋友
                </div>
                <div class="_css-qrcode-desc-text">
                  <input id="id_panoshareurl" type="text" class="_css-qrcode-desc-input" readonly
                  :value="func_getpanoshareurl()"
                  />
                </div>
              </div>
          </div>
          <!-- //左图，右链接及文字 -->
        </div>
        <!-- //内容区域 -->

        <div slot="buttonslot" class="css-common-zdialogbtnctn" >
            <zbutton-function
                :init_text="'关闭'"
                :init_fontsize="14"
                :debugmode="true"
                :init_height="undefined"
                :init_width="'76px'"
                :init_bgcolor="'#fff'"
                :init_color="'#1890FF'"
                @onclick="func_closepanosharing()"
                >
            </zbutton-function>
            <zbutton-function
                :init_text="'复制链接'"
                :init_fontsize="14"
                :debugmode="true"
                :init_height="undefined"
                :init_width="'76px'"
                @onclick="func_copyshareurl()"
                >
            </zbutton-function>
        </div>
      </zdialog-function>
      <!-- //分享对话框 -->

      <!-- 重命名对话框 -->
      <zdialog-function
      :init_title="'全景图名称'"
      :init_zindex="2"
      init_closebtniconfontclass="icon-suggested-close"
      v-if="status_namemodifing"
      @onclose="func_closenameedit()"
      >
          <!-- 输入区域 -->
          <div slot="mainslot">
            <div class="_css-line css-common-line">
            <div class="_css-title _css-title-flowname">
              请输入名称：
              </div>
              <div class="_css-fieldvalue css-common-fieldvaluename">
                <input
                @mousedown="_stopPropagation($event)"
                v-model="m_modifingname" type="text" class="css-common-fieldvaluename-in">
              </div>
            </div>
          </div><!-- //输入区域 -->

          <div slot="buttonslot" class="css-common-zdialogbtnctn" >
            <zbutton-function
                :init_text="'取消'"
                :init_fontsize="14"
                :debugmode="true"
                :init_height="undefined"
                :init_width="'76px'"
                :init_bgcolor="'#fff'"
                :init_color="'#1890FF'"
                @onclick="func_closenameedit()"
                >
            </zbutton-function>
              <zbutton-function
                :init_text="'保存'"
                :init_fontsize="14"
                :debugmode="true"
                :init_height="undefined"
                :init_width="'76px'"
                @onclick="func_savepanoname()"
                >
            </zbutton-function>
          </div>
      </zdialog-function><!-- //重命名对话框 -->

      <!-- gis 选点的小窗 -->
      <div
      @click="closeGisSel()"
      v-if="extdata.isShowGisSel"
      class="_css-gis-selectpoint" >
        <div class="_css-pano-close icon-suggested-close"></div>
        <iframe id="id_gis_sel" ></iframe>
      </div>

      <!-- 浏览全景图的iframe -->
      <div
      v-if="extdata.isShowingPano"
      class="_css-pano-viewer" >

        <!-- 时间轴 -->
        <div v-if="isTimelineVisible" class="_css-panoshowing-timeline">
          <div class="timeline-header">
            <div class="title">时间轴</div>
            <div class="tail" @click.stop="isDotsRelatedVisible = !isDotsRelatedVisible">
              <div class="dot"></div>
              <div class="dot"></div>
              <div class="dot"></div>
            </div>
          </div>
          <div class="timeline-content-wrapper">
            <el-timeline class="timeline-content" :reverse="reverse" >
              <el-timeline-item
                v-for="(item, index) in m_timelineitems"
                :key="index"
                class="css-panoshowing-lineitem"
                :color="item.source.PsScenename === currentPsSceneName?'#409eff':'#fff'"
                :class="{'css-panoshowing-lineitemselected': item.source.PsScenename === currentPsSceneName}"
                :timestamp="item.timestamp">
                <div class="_css-timelinetitle" @click="onTimelineItemClick(item.source)">{{item.content}}</div>
              </el-timeline-item>
            </el-timeline>
          </div>
          <div v-if="isDotsRelatedVisible" class="timeline-dots-related">
            <div class="content-wrapper">
              <div class="item">
                <div>自动播放</div>
                <el-radio-group :disabled="isAutoPlayingKrpano" class="playOrder" v-model="krpanoPlayOrder">
                  <el-radio :label="1">正序</el-radio>
                  <el-radio :label="2">倒序</el-radio>
                </el-radio-group>
              </div>
              <div class="item">
                <el-input-number :disabled="isAutoPlayingKrpano" placeholder="播放间隔" class="playInterval" size="small" v-model="krpanoPlayInterval" :min="1" controls-position="right"></el-input-number>
                <div class="btnPlay" @click="togglePlayKrpano">
                  <div class="icon" :class="iconClassForPlayKrpano"></div>
                  <div class="label">{{ labelForPlayKrpano }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <!-- //上面的全景图集 -->

        <!-- <div
        @click="closeviewer($event)"
        class="_css-pano-close icon-suggested-close" ></div> -->

        <div class="css-title-pano">
          <div class="css-title-left">
              {{ openIframeCheckedName.PbName }}
          </div>
          <div class="css-title-menu">
            <div v-if="!isKrpanoXmlError" class="css-title-right" :class="clickpanoindex == 0 ? 'css-title-right-checked' : ''" @click="showAddHotSpot"  >
              <div class="css-image icon-pano-anchor"></div>
              <div class="css-menu">添加锚点</div>
            </div>
            <div v-if="!isKrpanoXmlError" class="css-title-right" :class="clickpanoindex == 1 ? 'css-title-right-checked' : ''" @click="addNewScene">
              <div class="css-image icon-pano-addpano"></div>
              <div class="css-menu">上传全景图</div>
            </div>
            <div v-if="!isKrpanoXmlError" class="css-title-right" :class="clickpanoindex == 2 ? 'css-title-right-checked' : ''" @click="showSelectModelShow">
              <div class="css-image icon-pano-rel"></div>
              <div class="css-menu">关联场景</div>
            </div>
            <div v-if="!isKrpanoXmlError&&isRemoveSceneVisible" class="css-title-right" :class="clickpanoindex == 3 ? 'css-title-right-checked' : ''" @click="RemoveScene" >
              <div class="css-image icon-pano-delpano"></div>
              <div class="css-menu">删除全景图</div>
            </div>
            <div class="css-title-right" @click="closeviewer($event)">
              <div class="css-image icon-pano-closepano"></div>
              <div class="css-menu">退出全景图</div>
            </div>
          </div>
        </div>
        <div class="_css-iframe-open-pano">
          <div id="panoviewpreview" class="_css-iframe-open-pano" :class="selectModelForRelevancew && clickpanoindex==2 ? '_css-pano-iframe-small' : ''">
          </div>
          <div v-if="isLoadingKrpano" class="_css-loading-krpano">
            <div class="content">Loading...</div>
          </div>
          <!-- <iframe name="name_panoifri" id="pure-scene-iframe" class="_css-pano-ifri" @load="iframeLoadEnd" :class="selectModelForRelevancew && clickpanoindex==2 ? '_css-pano-iframe-small' : ''" :src="extdata.srcShowingPano"></iframe> -->
          <div v-if="selectModelForRelevancew && clickpanoindex==2"  class="_css-pano-rel-element">
            <SelectiveModelForRelevance
              @closeSelectModel="closeSelectModel"
              :sceneData="sceneData"
              @relPointFun="relPointFun"
            ></SelectiveModelForRelevance>
          </div>
        </div>
      </div>


      <!-- 新增场景对话框：全景图内上传全景图 -->

  <!-- extdata.uploadm_append.isshowing -->

      <div
      v-if="extdata.uploadm_append.isshowing"
      class="_css-pano-upload" >
        <div class="_css-pano-upload-in" >
          <CompsDialogHeader @oncancel="_oncancel_upload_append" title="上传"></CompsDialogHeader>

          <!-- 采集时间 -->
          <div class="_css-upload-file-label">采集时间</div>
          <div class="_css-upload-file-inputctn" >
            <div class="_css-upload-file-inputctnin">
              <el-date-picker
                v-model="m_collecttime_append"
                type="datetime"
                class="css-dtwidthfull"
                placeholder="请选择采集时间">
              </el-date-picker>
            </div>
          </div>
          <!-- //采集时间 -->

          <!-- 备注 -->
          <!-- <div class="_css-upload-file-label">备注</div>
          <div class="_css-upload-file-inputctn2" >
            <div class="_css-upload-file-inputctnin2">
              <textarea class="_css-comment-appending css-miniscroll "   v-model="m_comment_append"></textarea>
            </div>
          </div> -->
          <!-- //备注 -->

          <!-- 请选择一张图片 -->
          <div class="_css-upload-file-label2">请选择一张图片</div>
          <div class="_css-upload-file-inputctn" >
            <input id="id_file_append" class="_css-upload-file-inputfile" type="file"  accept="image/*" />
          </div>
          <!-- //请选择一张图片 -->

          <!-- 显示进度信息 -->
          <template v-if="extdata.uploadprogress_append.isshowing">
            <div class="_css-upload-file-label2">{{extdata.uploadprogress_append.infotext}}</div>
            <div class="_css-upload-file-inputctn" >
              <div class="_css-upload-file-inputshowpb" >
                <div
                :style="getPbWidth_append()"
                class="_css-upload-file-inputshowpb-in" >
                </div>
              </div>
            </div>
          </template>
          <!-- //显示进度信息 -->

          <div class="issue-btn">
            <span class="btn-css" @click="_oncancel_upload_append">取消</span>
            <span class="btn-css btn-ok" @click="_onok_upload_append">确定</span>
          </div>
        </div>
      </div>
      <!-- //新增场景对话框 -->





      <!-- 上传新图片对话框：外部创建全景图 -->
      <div
      v-if="extdata.uploadm.isshowing"
      class="_css-pano-upload" >
        <div class="_css-pano-upload-in" >
          <CompsDialogHeader @oncancel="_oncancel_upload" title="新建全景图"></CompsDialogHeader>

          <!-- 请输入名称 -->
          <div class="_css-upload-file-label2">名称</div>
          <div class="_css-upload-file-inputctn" >
            <div class="_css-upload-file-inputctnin">
              <input id='id_newname' class="_css-upload-file-inputname" placeholder-class="placeholder-inputname" placeholder="输入全景图名称" type="text"  />
            </div>
          </div>
          <!-- //请输入名称 -->

          <!-- 采集时间 -->
          <div class="_css-upload-file-label2">采集时间</div>
          <div class="_css-upload-file-inputctn" >
            <div class="_css-upload-file-inputctnin">
              <el-date-picker
                v-model="m_collecttime"
                type="datetime"
                prefix-icon=""
                class="css-dtwidthfull"
                placeholder="选择时间">
              </el-date-picker>
            </div>
          </div>
          <!-- //采集时间 -->

          <div class="_css-upload-file-label2">
            <!-- <i class="icon-suggested-plus"></i> -->
            标签</div>
          <div class="_css-upload-file-inputctn select-panorama-tag">
            <el-select class="select-panorama-tag" v-model="selectPanoTag" placeholder="选择标签" style="width:100%">
              <el-option
                v-for="item in tagOptions"
                :key="item.LabelId"
                :label="item.LabelName"
                :value="item.LabelId">
              </el-option>
            </el-select>
          </div>


          <!-- 请选择一张或多张图片 -->
          <div class="_css-upload-file-label2">请选择一张或多张图片</div>
          <div class="_css-upload-file-inputctn" >
            <input id="id_files" class="_css-upload-file-inputfile" type="file" multiple="multiple" accept="image/*" />
          </div>
          <!-- //请选择一张或多张图片 -->

          <!-- 显示进度信息 -->
          <template v-if="extdata.uploadprogress.isshowing">
            <div class="_css-upload-file-label2">{{extdata.uploadprogress.infotext}}</div>
            <div class="_css-upload-file-inputctn" >
              <div class="_css-upload-file-inputshowpb" >
                <div
                :style="getPbWidth()"
                class="_css-upload-file-inputshowpb-in" >

                </div>
              </div>
            </div>
          </template>
          <!-- //显示进度信息 -->


          <div class="issue-btn">
            <span class="btn-css" @click="_oncancel_upload">取消</span>
            <span class="btn-css btn-ok" :class="isuploading ? 'uploading-false' : ''" @click="_onok_upload">确定</span>
          </div>

        </div>
      </div>

      <!-- 上下文菜单 -->
      <div
      v-show="extdata.contextpos.isshowing"
      class="_css-pano-contextmenu"
        :style="getContextStyle()"
      >
        <div class="_css-pano-contextmenu-i">
          <div
            @click="evt_SceneView($event)"
            class="_css-pano-contextmenu-in">
              <div class="_css-pano-contextmenu-inicon icon-edit icon-pano-timeline"></div>
              <div class="_css-pano-contextmenu-intext">全景图集</div>
          </div>
        </div>

        <div class="_css-pano-contextmenu-i _css-pano-edit-classify">
          <div
            @click.stop="evt_ReviseClassify($event)"
            class="_css-pano-contextmenu-in">
              <div class="_css-pano-contextmenu-inicon icon-edit icon-pano-edittag"></div>
              <div class="_css-pano-contextmenu-intext">修改标签</div>

          </div>
          <div class="_css-pano-reset-classify" v-if="changeTagShow && tagOptions.length > 0" :style="getPanoEditStyle()">
            <div class="select-tag-pan-resetclass">
              <ul v-for="(item,index) in tagOptions" :key="item.LabelId">
                <li
                  v-if="item.LabelId.length>2"
                  @click.stop="selectChangeTag(item,index)"
                  :class="{'select-color':index==changeTagclickindex}">
                  {{item.LabelName}}
                </li>
              </ul>
              <!-- <el-select class="select-panorama-tag" v-model="editClassifyPanoTag" placeholder="选择标签" style="width:100%">
                <el-option
                  v-for="item in tagOptions"
                  :key="item.LabelId"
                  :label="item.LabelName"
                  :value="item.LabelId">
                </el-option>
              </el-select> -->
            </div>
            <div class="_css-dialog-pano-edit" @click.stop="sureChangeTag">确定</div>

          </div>
        </div>

        <div class="_css-pano-contextmenu-i">
          <div
          @click="evt_showshare($event)"
          class="_css-pano-contextmenu-in">
            <div class="_css-pano-contextmenu-inicon icon-edit icon-pano-share"></div>
            <div class="_css-pano-contextmenu-intext">分享</div>
          </div>
        </div>


        <div class="_css-pano-contextmenu-i">
          <div
          v-if="auth_modifyauth"
          @click="evt_showNameEdit($event)"
          class="_css-pano-contextmenu-in">
            <div class="_css-pano-contextmenu-inicon icon-edit icon-pano-resetname"></div>
            <div class="_css-pano-contextmenu-intext">重命名</div>
          </div>
          <div
          v-else
          @click="_stopPropagation($event)"
          class="_css-pano-contextmenu-in _css-dis">
            <div class="_css-pano-contextmenu-inicon icon-edit icon-pano-resetname"></div>
            <div class="_css-pano-contextmenu-intext">重命名</div>
          </div>
        </div>

        <div class="_css-pano-contextmenu-i">
          <div
          v-if="auth_modifyauth"
          @click="confirmToDel($event)"
          class="_css-pano-contextmenu-in">
            <div class="_css-pano-contextmenu-inicon icon-edit icon-pano-del"></div>
            <div class="_css-pano-contextmenu-intext">删除</div>
          </div>
          <div
          v-else
          @click="_stopPropagation($event)"
          class="_css-pano-contextmenu-in _css-dis">
            <div class="_css-pano-contextmenu-inicon icon-edit icon-pano-del"></div>
            <div class="_css-pano-contextmenu-intext">删除</div>
          </div>
        </div>
      </div>
      <!-- //上下文菜单 -->

      <div class="_css-pano-top">
        <!-- 上传按钮 -->
        <div class="pano-top-left">
          <div
          v-if="auth_modifyauth"
          @click="toUpload($event)"
          class="_css-pano-addbtn">创建全景图</div>
          <div
          v-else
          class="_css-pano-addbtn _css-dis"
          >
          创建全景图
          </div>
          <!-- //上传按钮 -->
          <div class="pano-set-icon pano-choose-set" @click.stop="setLabelFun"></div>
        </div>
        <div class="pano-top-right">
          <div class="_css-select">
            <i class="icon-interface-filter"></i>
            <el-select
              v-model="selectValue"
              filterable
              clearable
              placeholder="输入标签筛选"
              @change="selectChangeFun"
            >
              <el-option
                v-for="item in tagOptions"
                :key="item.LabelId"
                :label="item.LabelName"
                :value="item.LabelId"
              >
              </el-option>
            </el-select>
          </div>
          <div class="right-input">
            <el-input
              v-model="inputVal"
              placeholder="请输入全景图名称"
              @input="searchName"
            ></el-input>
            <i class="icon-interface-search"></i>
          </div>
        </div>
      </div>

      <div class="_css-pano-bottom">
        <div class="_css-pano-list"
        v-if="extdata.datalist.length"
        >
          <div v-for="itemFirst in extdata.datalist"
            :key="itemFirst.LabelId"
            class="_css-pano-list-for">
            <div class="list-content">
              <div class="title" :style="computeTitleBg(itemFirst.BackGroundColor || 'rgba(230, 126, 126, 1)')">
                {{ itemFirst.LabelName || '未分组'}}
              </div>
              <div class="all-list-child">
                <div
                @click="openurl(item)"
                v-for="item in itemFirst.Panoramas"
                :key="item.PbGuid"
                class="_css-pano-i">
                  <div v-show="hasGis(item)" class="_css-pano-gisflag" ></div>
                  <div class="_css-pano-iimg"
                  :style="computeItemImage(item)"
                  ></div>
                  <div class="_css-pano-itext">{{ item.PbName }} {{ getStatusText(item.PqFlag) }}</div>

                  <div class="_css-pano-istatus">
                    <div class="_css-pano-istatustext">采集时间：{{func_formatsuckstring(item.PbUpdatetime)}}</div>
                    <div @click.stop="showmenu($event, item)" class="_css-pano-idelbtn icon-interface-set_se"></div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div
        v-else
        class="_css-pano-list _css-panolistempty"
        >
          <div class="_css-pano-listempty" >暂无数据</div>
        </div>

      </div>
      <panoDetailsList
        v-if="detailsListShow"
        :posStyle="extdata.isShowingPano"
        :m_selectedobj="m_selectedobj"
        :panoList="panoList"
        @changeList="changeList"
        @close="detailsListShow=false"
      ></panoDetailsList>
    </div>
    <transition v-show="drawerEditShow">
      <div class="labelSetDialog" ref="labelSetDialog">
        <panoSetLable
          :tagOptions="tagOptions"
          @close="setLabelFun"
        ></panoSetLable>
      </div>

    </transition>

    <!--选择场景 -->
    <progressRelationSceneList
        v-if="sceneDialog"
        @open="handelClickOpenScene"
        @close="handleClickCloseBimScene"
      ></progressRelationSceneList>

  </div>
</template>
<script>
import CompsDialogHeader from "@/components/CompsDialog/CompsDialogHeader";
import SelectiveModelForRelevance from '@/components/CompsCommon/SelectiveModelForRelevance'
import panoSetLable from '@/components/Home/ProjectBoot/PanoSetLabel'
import panoDetailsList from "@/components/CompsPano/panoDetailsList";
import progressRelationSceneList from '@/components/CompsProgress/progressRelationSceneList'

export default {
  name: "Pano",
  components: {
    CompsDialogHeader,
    panoDetailsList,
    SelectiveModelForRelevance,
    panoSetLable,
    progressRelationSceneList
  },
  data() {
    return {
      headerTitle: '',
      inputVal: '', // 搜索全景图
      searchPbName: '',  // 搜索input的值
			searchlabelId: '',  // 选择标签类型的值
      drawerEditShow: false,
      selectValue: '',
      reverse: false,
      m_step:1,
      m_timelineitems:[],
      m_marks: {
          0: '0°C',
          8: '8°C',
          37: '37°C',
          50: {
            style: {
              color: '#1989FA'
            },
            label: this.$createElement('strong', '50%')
          }
      },

      m_timelinevalue: 0,
      // 新时间线，左侧显示高亮的那个
      m_timelineselectedname: '',
      m_timeline_pbguid:'',
      // 时间轴是否显示
      isTimelineVisible: false,
      // 有gis权限
      m_hasgisauth: false,
      // 全景图分享的弹层是否正在显示
      status_panosharing: false,
      // 全景图上传时指定的采集时间
      m_collecttime:'',
      // 全景图追加时指定的采集时间
      m_collecttime_append:'',
      // 全景图追加时添加的备注信息
      m_comment_append:'',
      // 当前操作者的权限
      auth_modifyauth: true,
      // 全景图正在编辑的名称
      m_modifingname: '',
      // 呼出右键菜单的那一条数据
      m_selectedobj:{},
      // 是否显示全景图图集名称的编辑对话框
      status_namemodifing: false,
      extdata: {
        // 权限数据
        funcauthdata: undefined,
        isShowGisSel: false, // 是否显示GIS的选点窗体
        isShowingPano: false,          // 正在显示全景图页面
        srcShowingPano: 'about:blank', // 全景图页面地址
        datalist:[],
        SelectedPbGuid:'',
        gisbtntext:'',
        contextpos: {
          top: 0,
          left: 0,
          isshowing: false
        },
        uploadm:{
          isshowing: false,
          disinput: false,
          canceltoken_source: undefined
        },
        uploadprogress:{
          isshowing: false,
          infotext:'',
          pbwidth:0
        },

        uploadm_append:{
          isshowing: false,
          disinput: false,
          canceltoken_source: undefined,
          pbguid:''
        },

        uploadprogress_append:{
          isshowing: false,
          infotext:'',
          pbwidth:0
        }


      },
      tagShow: false,
      tagChecked: '',  // 选择的标签
      selectPanoTag: "", // 上传全景图选择的标签
      tagOptions: [],
      detailsListShow: false,
      panoList: [],
      selectModelForRelevancew: false,  // 点击关联场景
      sceneDialog: false,
      clickpanoindex: -1,
      editClassifyPanoTag: [], // 编辑全景图标签
      changeTagShow: false, // 修改标签按钮
      changeTagclickindex: -1,
      openIframeCheckedName: {}, // 点击进去全景图的名称
      clickpano_req_changeScene:{}, // 选择全景图集的全景图，全景图的属性
      iframeFromClickList: false, // 判断打开方式是全景图list还是直接点击进入
      checkedModelAttr: {},  // 关联视点选择的模型和视点
      isuploading: false, // 正在上传全景图
      sceneData: null,
      krpanoInterfaceObj: null, // 实例化的krpano对象引用
      isLoadingKrpano: false, // 是否在加载krpano图集
      sceneList: [], // GetScenesByPbGuid接口的响应
      currentPsSceneName: "", // krpano原本显示的名字
      currentPsRealName: "", // 当前的真实名字
      krpanoPlayOrder: 1, // 全景图集播放顺序 1正序 2倒序
      krpanoPlayInterval: 2, // 全景图集播放间隔，秒
      isDotsRelatedVisible: false, // 三个点相关的dom结构是否可见
      isAutoPlayingKrpano: false, // 是否在自动播放全景图
      timerForAutoPlayingKrpano: null, // 自动播放全景图的定时器
      orderedTimelineItems: [], // 按照krpanoPlayOrder排序的items
      isRemoveSceneVisible: false, // 删除全景图是否可见
      isKrpanoXmlError: false // krpano的xml下载或者解析是否错误
    };
  },
  watch: {
    sceneList(cv) {
      if(!cv||!cv.length||cv.length <= 1) {
        this.isRemoveSceneVisible = false
      } else {
        this.isRemoveSceneVisible = true
      }
    }
  },
  computed: {
    labelForPlayKrpano() {
      return this.isAutoPlayingKrpano ? "停止" : "播放"
    },
    iconClassForPlayKrpano() {
      return this.isAutoPlayingKrpano ? "stop" : "start"
    }
  },
  created () {
    const scriptEle = document.getElementById('pano-tour')
    if (!scriptEle) {
      const a = document.createElement('script')
      a.src = './static/vtour/tour.js'
      a.id = 'pano-tour'
      // a.onload = function () {
      //   console.log('加载完成tour.js')
      // }
      a.onload = callOnLoad
      document.head.appendChild(a)

      function callOnLoad() {
        console.log('加载完成tour.js')
        requestAnimationFrame(() => {
          a.onload = null
        })
      }
    }
    this.genKrpanoRelatedHandler()
  },
  mounted() {
    var _this = this;
    window.panovue = this;
    this.headerTitle = this.$staticmethod._Get("menuText") || '';

    _this.$emit("onmounted", "pano");

    // msg
    // window.addEventListener("message", function (data) {
    //     // 处理消息 data 数据
    //     _this.processMessageData(data);
    // });
    window.addEventListener("message",_this.processMessageData);
    _this.getTagOption();
    // 加载后请求数据
    _this.getList();
    this.auth_modifyauth = this.$staticmethod.hasSomeAuth('QJTGL_Edit')

    // _this.setfuncauthdata(function() {
    //   _this.auth_modifyauth = true// _this.testhasfuncbtnauth('Pano', 'lr-edit');
    //   //console.log('当前人有编辑权限：' + _this.auth_modifyauth);
    // });

    // _this.testhasfuncbtnauth('GIS', 'lr-visible', (v)=>{
    //   _this.m_hasgisauth = v;
    // });

  },
  beforeDestroy() {
    window.removeEventListener("message",this.processMessageData);
  },
  methods: {

    // func_testifeq(item_content){
    //   var _this = this;

    //   if (/\(\d\d\d\d\-\d\d\-\d\d\-\d\d\-\d\d\-\d\d\)$/.test(item_content)) {

    //       // 取出后面的时间部分
    //       var timestr = item_content.substr(item_content.length - "(2020-12-12-12-12-12)".length);//abc(123)

    //       var timestr2 = _this.m_timelineselectedname.substr(_this.m_timelineselectedname.length - "(2020-12-12-12-12-12)".length);//abc(123)
    //       return timestr == timestr2;
    //   }
    //   return item_content == _this.m_timelineselectedname;
    // },

    // func_testclicktimeline(item) {

    //   // 发消息，切换场景
    //   var win = document.getElementById('pure-scene-iframe').contentWindow;
    //   if (win) {
    //     var data = {
    //         act: 'pano_req_changeScene', data: 'scene_' + item.content
    //     };
    //     win.postMessage(data, "*");
    //   }
    // },

    // 复制分享链接
    func_copyshareurl() {
      var _this = this;
      var txt=document.getElementById('id_panoshareurl');
      txt.select();
      document.execCommand("Copy");
      _this.$message.success('复制成功');
    },

    // 全景图分享地址
    func_getpanoshareurl() {
      var _this = this;
      var _organizeId = _this.$staticmethod._Get("organizeId");
      if (!_this.m_selectedobj || !_this.m_selectedobj.PbGuid) {
        return undefined;
      }
      var innerUrl = `${window.location.origin}${window.bim_config.hasRouterFile}/#/LinkShare/PanoShare/${_this.m_selectedobj.PbGuid}/${_organizeId}`;
      return innerUrl;
    },

    // 二维码图片地址
    style_qrcodestyle() {
      var _this = this;
      var _s = {};
      var enurl = encodeURIComponent(_this.func_getpanoshareurl());
      var url = `${this.$urlPool.QRCode}?encodedUrl=${enurl}&token=${this.$staticmethod.Get("Token")}`;
      _s["background-image"] = `url('${url}')`;
      _s["background-repeat"] = 'no-repeat';
      _s["background-size"] = 'contain';
      return _s;
    },

    // 关闭全景图分享对话框
    func_closepanosharing() {
      var _this = this;
      _this.status_panosharing = false;
    },

    // 显示分享对话框
    evt_showshare(ev) {
      var _this = this;
      // console.log(_this.m_selectedobj);
      _this.status_panosharing = true;
    },
    // 修改标签
    evt_ReviseClassify(){
      let _this = this;
      _this.changeTagShow = true;
    },
    // 点击全景图集
    evt_SceneView(ev){
      let _this = this;
      if (_this.m_selectedobj.PqFlag != 2) {
        _this.$message.warning('该全景图仍处于后台转换状态，请稍后刷新页面再试');
        return;
      }
      _this.detailsListShow = false;

      _this.$axios
        .get(
          `${window.bim_config.webserverurl}/api/Panorama/PanoramaFile/GetScenesByPbGuid?pbguid=${_this.m_selectedobj.PbGuid}&Token=${this.$staticmethod.Get('Token')}`
        )
        .then((x) => {
          console.log(x)
          if (x.data && x.data.Data && x.data.Data.length) {
            _this.panoList = x.data.Data;
            _this.detailsListShow = true;
          }else{
						_this.$message.error("暂无场景");
						_this.panoList = [];
					}
        })
        .catch((x) => {
          _this.$message.error("获取场景信息失败");
        });
    },

    // xxxx年xx月xx日
    func_formatsuckstring(uptime) {
      if (!uptime) {
        return '<无>';
      }
      var _this = this;
      var d = new Date(uptime);
      var year = d.getFullYear();
      var month = d.getMonth() + 1;
      var date = d.getDate();
      return `${year}年${month}月${date}日`;
    },

    //  // 各个功能模块页面的通用权限判断函数1
    // // 需要有 extdata.funcauthdata
    // // 需要有 authdata
    // testhasfuncbtnauth(bmencode, bmbencode, callback) {
    //   var _this = this;
    //   //debugger;
    //   if (!_this.extdata.funcauthdata) {
    //     // _this.setfuncauthdata(()=>{
    //     //   _this.testhasfuncbtnauth(bmencode, bmbencode, callback);
    //     // });
    //     return;
    //   }
    //   var bmIndex = _this.extdata.funcauthdata.findIndex(
    //     x => x.Bm_EnCode == bmencode
    //   );
    //   if (bmIndex < 0) {
    //     return false; // 没有找到指定bm项
    //   }
    //   var bm = _this.extdata.funcauthdata[bmIndex];
    //   if (bm.checkstate == "0") {
    //     return false; // 权限设置中，所有角色的bm设置均没有打开。
    //   }
    //   if (bm.Bmbs.length == 0) {
    //     return false; // 功能模块下没有按钮
    //   }
    //   var hasAuth = bm.Bmbs.findIndex(
    //     x => x.Roles.length > 0 && x.checkstate == "1" && x.Bmb_EnCode == bmbencode
    //   ); // 这个功能模块下有有角色的，且为有权限的
    //   //callback && callback(hasAuth >= 0);
    //   //return hasAuth >= 0;

    //   if (callback) {
    //     callback(hasAuth >= 0);
    //   } else {
    //     return hasAuth >= 0;
    //   }
    // },

    // setfuncauthdata(callback) {
    //   var _this = this;
    //   var _OrganizeId = _this.$staticmethod._Get("organizeId");
    //   var _Token = _this.$staticmethod.Get("Token");
    //   _this.$axios
    //     .get(
    //       `${window.bim_config.webserverurl}/api/User/Role/GetUserOrgFuncAuth?organizeId=${_OrganizeId}&Token=${_Token}`
    //     )
    //     .then(x => {
    //       if (x.status == 200) {
    //         if (x.data.Ret > 0) {
    //           if (x.data.Data) {
    //             _this.extdata.funcauthdata = x.data.Data;
    //             _this.$staticmethod.Set("funcauthdata", JSON.stringify(x.data.Data));
    //             if (callback) {
    //               //debugger;
    //               callback();
    //             }
    //           }
    //         }
    //       }else{
		// 				_this.$message.error(x.data.Msg);
    //       }
    //     })
    //     .catch(x => {});
    // },

    // 保存全景图名称
    func_savepanoname() {

      // 准备数据
      var _this = this;
      var _PbOrganizeId = _this.$staticmethod._Get("organizeId");
      var _PbGuid = _this.m_selectedobj.PbGuid;
      var _PbName = _this.m_modifingname;
      if(window.tool.GetValIsEmpty(_PbName)){
        _this.$message.warning("请输入名称")
        return
      }

      // 显示 loading
      var _LoadingIns = _this.$loading({
        text: '处理中',
        target: document.getElementById("id_jingruizhang_probim_vue_zdialog_inner")
      });

      // 请求并返回
      var _Url = `${window.bim_config.webserverurl}/api/Panorama/PanoramaFile/RenameItem?Token=${this.$staticmethod.Get('Token')}`;
      _this.$axios({
        url: _Url,
        method: 'post',
        data: {
          PbGuid: _PbGuid,
          PbName: _PbName,
          PbOrganizeId: _PbOrganizeId
        }
      }).then(x => {
        _LoadingIns.close();
        if (x.data.Ret > 0) {
            _this.$message.success('操作成功');
            _this.status_namemodifing = false;
            _this.getList();
        } else if (x.data.Ret < 0) {
            _this.$message.error(x.data.Msg);
        }
      }).catch(x => {
        _LoadingIns.close();
        _this.$message.error('请求异常，请稍后再试');
        debugger;
      });

    },

    // 显示全景图图集名称的编辑对话框
    evt_showNameEdit() {

      // 先获取当前数据的已有名称
      var _this = this;
      _this.m_modifingname = _this.m_selectedobj.PbName;
      _this.status_namemodifing = true;

    },

    // 关闭全景图图集名称的编辑对话框
    func_closenameedit() {
      var _this = this;
      _this.status_namemodifing = false;
    },

    _stopPropagation(ev) {
      var _this = this;
      ev && ev.stopPropagation && ev.stopPropagation();
    },

    do_processMessageData(gisinfostr, pbguid) {
      var _this = this;
      var posturl = `${window.bim_config.webserverurl}/api/Panorama/PanoramaFile/ReGisItem?Token=${this.$staticmethod.Get('Token')}`;
      _this.$axios({
          method:'post',
          url: posturl,
          data: _this.$qs.stringify({
            //PbGuid, PbGisinfo
            PbGuid: pbguid,
            PbGisinfo: gisinfostr
          })
        }).then(x => {
          if (x.status == 200 && x.data.Ret > 0) {
            _this.$message.success('操作成功');
            _this.extdata.isShowGisSel = false;
            _this.getList();
          } else {
            _this.$message.error(x.data.Msg);
            debugger;
          }
        }).catch(x => {
          debugger;
        });
    },

    // 构造 slider label 对象
    func_makeobj(obj) {
      var _this = this;
      var obj = {
        style: {
          color: '#fff'
        },
        label: obj.PsScenename,//this.$createElement('strong', '50%')
        time: obj.PsCollecteddatetime.replace("T", " ")
      }
      return obj;
    },

    processMessageData(data) {
      var _this = this;
      // console.log(data)
      if (data.data.act == "gisPano_res_onPosClick") {
        if (data.data.msg && data.data.msg.point) {
          var gisinfostr = JSON.stringify(data.data.msg.point);
          var pbguid = _this.extdata.SelectedPbGuid;
          _this.$confirm('确定选择该位置？', '操作确认', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(x => {
            _this.do_processMessageData(gisinfostr, pbguid);
            _this.closeall();
          }).catch(x => {
            _this.closeall();
          });
        }

        // 定位全景图集
      } else if (data.data.act == 'pano_res_setTimelineCurrent') {

        var scenename = data.data.msg.scenename;
        scenename = scenename.substr(6);
        console.log(scenename);

        panovue.m_timelineselectedname = scenename;

        for (var attr in panovue.m_marks) {
            if (panovue.m_marks[attr].label == scenename) {
                panovue.m_timelinevalue = parseInt(attr);
                break;
            }
        }

        // 显示全景图集
      } else if (data.data.act == "pano_res_showTimeline") {
        if (data.data.msg && data.data.msg.pbguid) {


           if (!_this.isTimelineVisible) {

              // 显示全景图集
              // 显示前调用接口，获取所有的时间、场景名称
              _this.m_marks = {};
              _this.$axios.get(`${window.bim_config.webserverurl}/api/Panorama/PanoramaFile/GetScenesByPbGuid?pbguid=${data.data.msg.pbguid}&Token=${this.$staticmethod.Get('Token')}`).then(x => {
                // 全景图集需要分为100份。
                // 取出最小时间
                var mindatetime = undefined;
                var maxdatetime = undefined;
                if(x.data && x.data.Data && !x.data.List) {x.data.List = x.data.Data}
                if (x.data && x.data.List && x.data.List.length) {
                  mindatetime = new Date(x.data.List[0].PsCollecteddatetime.replace('T', ' '));
                  maxdatetime = new Date(x.data.List[x.data.List.length - 1].PsCollecteddatetime.replace('T', ' '));
                }

                // 计算差值（天）
                var diffdays = maxdatetime - mindatetime;
                diffdays = diffdays / 1000 / 3600 / 24;

                // 得出 刻度/天 的值
                var length_day = 100;
                if (diffdays == 0) {
                  length_day = 100;
                } else if (diffdays == 1) {
                  length_day = 100;
                } else {
                  //100刻度/30天
                  length_day = 100.0/diffdays;
                }

                // 构造时间线需要的参数
                _this.m_timelineitems = [];
                var _tarr = [];
                if (x.data && x.data.List) {
                  var l2 = x.data.List;
                  // for (var i = 0; i < l2.length; i++) {
                  //   var _data = {
                  //     timestamp: l2[i].PsCollecteddatetime.substr(0, 16).replace('T',' '),
                  //     content: l2[i].PsScenename
                  //   };
                  //   _tarr.push(_data);
                  // }
                  for (var i = l2.length - 1; i >= 0; i--) {
                    var _data = {
                      timestamp: l2[i].PsCollecteddatetime.substr(0, 16).replace('T',' '),
                      content: l2[i].PsScenename,
                    };
                    _tarr.push(_data);
                  }
                }
                _this.m_timelineitems = _tarr;

                // 构造全景图集上显示的数据数组
                if (x.data && x.data.List) {

                  for (var i = 0; i < x.data.List.length; i++ ) {
                    if (i == 0) {
                      _this.m_marks[0] = _this.func_makeobj(x.data.List[i]);
                    } else if (i == x.data.List.length - 1) {
                      _this.m_marks[100] = _this.func_makeobj(x.data.List[x.data.List.length - 1]);
                    } else {

                      // 取出天数差
                      var thisdt = new Date(x.data.List[i].PsCollecteddatetime.replace('T', ' '));
                      var diffofpre = thisdt - mindatetime;
                      diffofpre = diffofpre / 1000 / 3600 / 24;

                      // 计算刻度
                      var keynum = diffofpre * length_day;
                      keynum = Math.floor(keynum);
                      _this.m_marks[keynum] = _this.func_makeobj(x.data.List[i]);

                    }

                  }
                }

                console.log(data.data.msg.pbguid);
                _this.isTimelineVisible = true;
                _this.m_timeline_pbguid = data.data.msg.pbguid;
              }).catch(x => {
                _this.$message.error('获取场景信息失败');
              });


           } else {

              // 隐藏全景图集
              _this.isTimelineVisible = false;
              _this.m_timeline_pbguid = '';

           }
        }
        // 显示添加新场景对话框
      } else if (data.data.act == "pano_res_showAddNewScene") {
        if (data.data.msg && data.data.msg.pbguid) {

          // 进度条归位
          _this.resetuploadform_append();

          // 时间清空
          _this.m_collecttime_append = '';

          // 备注清空
          _this.m_comment_append = '';

          // pbguid 为当前打开的全景图的 pbguid
          // 隐藏全景图集
          //_this.m_timelineitems
          _this.extdata.uploadm_append.isshowing = true;
          _this.extdata.uploadm_append.pbguid = data.data.msg.pbguid;
        }
      } else if (data.data.act == "pano_req_relModelPoint"){
        debugger
        // 关联视点获取当前的全景图
        if (data.data.msg &&  data.data.msg.psbuid) {
          _this.relModelPointFun(data.data.msg.psbuid)
        }
      } else if (data.data.type == "FROM_PANO_OPENLINK"){
        // 点击全景图锚点图片跳转到路由
        if(data.data.data.length > 0){
          window.open(data.data.data)
        }
      }
    },

    // 关闭gis选点
    closeGisSel(){
      var _this = this;
      _this.extdata.isShowGisSel = false;
    },

    // 关闭全景图
    closeviewer(ev){
      var _this = this;
      _this.extdata.isShowingPano = false;
      _this.selectModelForRelevancew = false;
      _this.extdata.srcShowingPano = 'about:blank';
      _this.isKrpanoXmlError = false
      _this.resetTimelineRelatedData();
      if(_this.krpanoInterfaceObj) {
        _this.krpanoInterfaceObj.unload()
      }
    },



    // 生成krpano相关的handler
    genKrpanoRelatedHandler() {
      window.func_viewchanged = this.onKrpanoViewChanged // 生成在action中调用的函数
      window.func_loadcomplete = this.onKrpanoLoadComplete // 生成在action中调用的函数

      window.handlerMapForLayer = {
        toggleTimeline: this.toggleTimeline,
        refreshCurrentTour: this.refreshCurrentTour
      } // 代码添加的layer的事件处理器映射
    },

    // toggle时间轴
    toggleTimeline() {
      this.isTimelineVisible = !this.isTimelineVisible
      if(this.isTimelineVisible) {
        this.setTimelineRelatedData()
      } else {
        this.isAutoPlayingKrpano = false
      }
    },

    // toggle播放全景图集
    togglePlayKrpano() {
      if(this.isAutoPlayingKrpano) {
        this.stopPlayingKrpano()
      } else {
        this.startPlayingKrpano()
      }
    },

    // 根据播放参数获取排序后的数据
    setOrderedTimelineItems() {
      if(!this.sceneList || !this.sceneList.length) {
        this.orderedTimelineItems = []
        return
      }
      const itemsDeepCopy = JSON.parse(JSON.stringify(this.sceneList))
      if(this.krpanoPlayOrder === 1) { // 按照收集时间升序
        this.orderedTimelineItems = itemsDeepCopy.sort((a,b) => {
          return this.dayjs(a.PsCollecteddatetime).valueOf() - this.dayjs(b.PsCollecteddatetime).valueOf()
        })
      } else { // 按照收集时间降序
        this.orderedTimelineItems = itemsDeepCopy.sort((a,b) => {
          return this.dayjs(b.PsCollecteddatetime).valueOf() - this.dayjs(a.PsCollecteddatetime).valueOf()
        })
      }
    },

    // 预定下一次自动加载执行
    scheduleNextRun(runImmediadely) {
      let maxIndex = this.orderedTimelineItems.length
      if(!maxIndex) {
        this.stopPlayingKrpano()
        return
      }
      maxIndex = maxIndex - 1
      const currentIndex = this.orderedTimelineItems.findIndex(item => item.PsScenename == this.currentPsSceneName)
      if(currentIndex !== -1) {
        let nextIndex = currentIndex + 1
        if(nextIndex > maxIndex ) {
          nextIndex = 0
        }
        const nextItem = this.orderedTimelineItems[nextIndex]
        if(nextItem) {
          if(runImmediadely) {
            this.loadKrpanoScene(nextItem)
          } else {
            this.timerForAutoPlayingKrpano = setTimeout(() => {
              this.loadKrpanoScene(nextItem)
            }, this.krpanoPlayInterval * 1000);
          }
        }
      } else {
        this.stopPlayingKrpano()
      }
    },

    // 停止播放全景图集
    stopPlayingKrpano() {
      clearTimeout(this.timerForAutoPlayingKrpano)
      this.isAutoPlayingKrpano = false
    },
    // 开始播放全景图集
    startPlayingKrpano() {
      this.setOrderedTimelineItems()
      this.isAutoPlayingKrpano = true
      this.scheduleNextRun(false)
    },

    async getSceneList() {
      const baseUrl= `${window.bim_config.webserverurl}/api/Panorama/PanoramaFile`
      const pbguid = this.openIframeCheckedName.PbGuid
      const token = this.$staticmethod.Get('Token')
      const x = await this.$axios.get(`${baseUrl}/GetScenesByPbGuid?pbguid=${pbguid}&Token=${token}`).catch(() => {})
      if(!x || !x.data || !x.data.Data) {
        return []
      } else {
        return x.data.Data
      }
    },

    // 设置时间轴相关数据
    async setTimelineRelatedData() {
      this.m_marks = {}
      this.m_timelineitems = []
      this.sceneList = []
      const baseUrl= `${window.bim_config.webserverurl}/api/Panorama/PanoramaFile`
      const pbguid = this.openIframeCheckedName.PbGuid
      const token = this.$staticmethod.Get('Token')
      const x = await this.$axios.get(`${baseUrl}/GetScenesByPbGuid?pbguid=${pbguid}&Token=${token}`).catch(()=>{
        this.$message.error('获取场景信息失败');
      })
      if(!x || !x.data || !x.data.Data) {
        this.resetTimelineRelatedData()
        return
      }
      this.sceneList = x.data.Data
      x.data.List = x.data.Data
      // 全景图集需要分为100份。
      // 取出最小时间
      var mindatetime = undefined;
      var maxdatetime = undefined;
      if (x.data && x.data.List && x.data.List.length) {
        mindatetime = new Date(x.data.List[0].PsCollecteddatetime.replace('T', ' '));
        maxdatetime = new Date(x.data.List[x.data.List.length - 1].PsCollecteddatetime.replace('T', ' '));
      }

      // 计算差值（天）
      var diffdays = maxdatetime - mindatetime;
      diffdays = diffdays / 1000 / 3600 / 24;

      // 得出 刻度/天 的值
      var length_day = 100;
      if (diffdays == 0) {
        length_day = 100;
      } else if (diffdays == 1) {
        length_day = 100;
      } else {
        //100刻度/30天
        length_day = 100.0/diffdays;
      }

      // 构造时间线需要的参数
      this.m_timelineitems = [];
      var _tarr = [];
      if (x.data && x.data.List) {
        var l2 = x.data.List;
        // for (var i = 0; i < l2.length; i++) {
        //   var _data = {
        //     timestamp: l2[i].PsCollecteddatetime.substr(0, 16).replace('T',' '),
        //     content: l2[i].PsScenename
        //   };
        //   _tarr.push(_data);
        // }
        for (var i = l2.length - 1; i >= 0; i--) {
          var _data = {
            timestamp: l2[i].PsCollecteddatetime.substr(0, 16).replace('T',' '),
            content: l2[i].PchChname,
            source: l2[i]
          };
          _tarr.push(_data);
        }
      }
      this.m_timelineitems = _tarr;

      // 构造全景图集上显示的数据数组
      if (x.data && x.data.List) {

        for (var i = 0; i < x.data.List.length; i++ ) {
          if (i == 0) {
            this.m_marks[0] = this.func_makeobj(x.data.List[i]);
          } else if (i == x.data.List.length - 1) {
            this.m_marks[100] = this.func_makeobj(x.data.List[x.data.List.length - 1]);
          } else {

            // 取出天数差
            var thisdt = new Date(x.data.List[i].PsCollecteddatetime.replace('T', ' '));
            var diffofpre = thisdt - mindatetime;
            diffofpre = diffofpre / 1000 / 3600 / 24;

            // 计算刻度
            var keynum = diffofpre * length_day;
            keynum = Math.floor(keynum);
            this.m_marks[keynum] = this.func_makeobj(x.data.List[i]);

          }

        }
      }
      this.m_timeline_pbguid = this.openIframeCheckedName.PbGuid
    },

    // 重置时间轴相关的数据
    resetTimelineRelatedData() {
      this.m_marks = {}
      this.isTimelineVisible = false
      this.m_timeline_pbguid = ''
      this.m_timelineitems = []
      this.sceneList = []
      this.isDotsRelatedVisible = false
      this.currentPsRealName = this.currentPsSceneName = ""
      this.stopPlayingKrpano()
      this.orderedTimelineItems = []
      this.isRemoveSceneVisible = false
      this.isKrpanoXmlError = false
    },

    // 刷新全景图
    refreshCurrentTour() {
      this.resetTimelineRelatedData()
      if(this.krpanoInterfaceObj) {
        this.krpanoInterfaceObj.unload() // 卸载当前的
        this.$nextTick(() => {
          this.openurl(this.openIframeCheckedName,false)
        })
      }
    },

    // 响应krpano嵌入完成事件
    onKrpanoEmbedDone(krpanoInterfaceObj) {
      // console.log("onKrpanoEmbedDone")
      window.abc = krpanoInterfaceObj
      this.krpanoInterfaceObj = krpanoInterfaceObj
      this.registerKrpanoXmlErrorHandler()
      this.clickpanoindex = -1
      this.selectModelForRelevancew = false
      this.detailsListShow = false
      this.extdata.isShowingPano = true
      this.extdata.isTimelineVisible = false
      this.setTimelineRelatedData()
      // this.bingKrpanoRootClickHandler()
    },

    // 注册krpano的xml解析、下载错误事件处理器
    registerKrpanoXmlErrorHandler() {
      if(this.krpanoInterfaceObj) {
        this.krpanoInterfaceObj.events.addListener("onxmlerror",this.onKrpanoXmlError)
      }
    },

    // 响应krpano的xml解析、下载错误事件
    onKrpanoXmlError() {
      this.isLoadingKrpano = false
      this.isKrpanoXmlError = true
      const rootKrpanoElement = document.querySelector("#krpanoSWFObject")
      if(rootKrpanoElement && this.krpanoInterfaceObj) {
        const errorDom = document.createElement("div")
        errorDom.setAttribute("id","krpanoXmlError")
        errorDom.style.cssText="display:flex;width:100%;height:100%;color:#fff;flex-direction:column;justify-content:center;align-items:center;"
        errorDom.innerHTML = "<div style='width:100%;padding:8px;border: 1px solid #fff;'>" + this.krpanoInterfaceObj.lasterror.replace(/\[/g,"<").replace(/\]/g,"/>") + "</div>"
        rootKrpanoElement.innerHTML=""
        rootKrpanoElement.appendChild(errorDom)
      }
    },


    // 给krpano的根元素绑定点击事件处理器
    // bingKrpanoRootClickHandler() {
    //   const rootKrpanoElement = document.querySelector("#krpanoSWFObject")
    //   if(rootKrpanoElement) {
    //     rootKrpanoElement.addEventListener("click",this.onKrpanoBtnsClick,true)
    //   }
    // },

    // onKrpanoBtnsClick(e) {
    //   console.log("onKrpanoBtnsClick",e,e.target)
    //   if(this.isAutoPlayingKrpano && e) {
    //     e.stopPropagation()
    //     return false
    //   }
    // },

    // 响应点击时间轴元素
    onTimelineItemClick(item) {
      // if(this.isAutoPlayingKrpano) {
      //   return
      // } else {
      this.loadKrpanoScene(item)
      // }
    },

    // 加载krpano的场景
    loadKrpanoScene(item) {
      if(item) {
        const { PsScenename} = item
        if(PsScenename == this.currentPsSceneName) {
          return
        } else {
          this.krpanoInterfaceObj.call(`loadscene('scene_${PsScenename}', null, 'MERGE', 'BLEND(1)')`)
        }
      }
    },

    // 响应krpano视角改变事件
    onKrpanoViewChanged(...args) {
      // console.log("onKrpanoViewChanged",args);
    },

    // 响应krpano场景加载完成事件
    onKrpanoLoadComplete(...args) {
      // console.log("onKrpanoLoadComplete",args);
      this.updateKrpanoBtns()
      this.$nextTick(() => {
        this.walkThroughUpdateRealName()
        this.isLoadingKrpano = false
        if(this.isAutoPlayingKrpano) {
          this.scheduleNextRun(false)
        }
      })
    },

    // 遍历krpano的dom，修改包含场景名字的dom的innerHTML为图片名字
    walkThroughUpdateRealName() {
      // krpano相关的dom
      const targetDom = document.querySelector("#idForSceneName")
      if(targetDom) {
        const content = targetDom.innerHTML.replace(/\s*/g,"")
        const targetValue = content == this.currentPsRealName ? this.currentPsSceneName : content
        const data = this.sceneList.find(item => item.PsScenename == targetValue)
        if(data) {
          targetDom.innerHTML = data.PchChname
          this.currentPsRealName = data.PchChname
          this.currentPsSceneName = data.PsScenename
        } else {
          this.currentPsRealName = ""
          this.currentPsSceneName = ""
        }
      } else {
        const { walkThroughTreesByDepthFirst } = window.tool
        const _this = this
        let currentPsSceneName = ""
        let currentPsRealName = ""
        let shouldDelayCall = false
        walkThroughTreesByDepthFirst([document.querySelector("#krpanoSWFObject")], (node) => {
          if(node && node.nodeName == "DIV") { // div节点
            const content = node.innerHTML.replace(/\s*/g, "") // 去除空白
            if(content) {
              const data = _this.sceneList.find(item => item.PsScenename == content)
              if(data) {
                if(node.parentNode === document.querySelector("#krpanoSWFObject")) {
                  shouldDelayCall = true
                  return true
                }
                node.setAttribute("id","idForSceneName")
                node.innerHTML = data.PchChname
                currentPsRealName = data.PchChname
                currentPsSceneName = data.PsScenename
                return true
              }
            }
          }
        },"children",true)
        if(shouldDelayCall) {
          // console.log("延迟调用")
          setTimeout(this.walkThroughUpdateRealName, 1000)
        } else {
          _this.currentPsSceneName = currentPsSceneName
          _this.currentPsRealName = currentPsRealName
        }
      }
    },

    // 修改krpano的操作按钮:隐藏一些、添加一些,实际上这里可以把无用的layer、plugin都去掉以提升性能
    updateKrpanoBtns() {
      const { krpanoInterfaceObj } = this
      if(krpanoInterfaceObj) {
        const layersToDelete = ["skin_map_container","skin_btn_map","skin_btn_fs"]
        layersToDelete.forEach(layer => {
          krpanoInterfaceObj.removelayer(layer)
        })

        this.$nextTick(() => {
          let layerOffset = 50
          const regForBtnPrev = /skin_btn_prev/i // 上一张
          const regForBtnNext = /skin_btn_next/i // 下一张
          const layerNames = krpanoInterfaceObj.layer.getArray().filter(item => item.visible && item.enabled).map(item => item.name)
          if(layerNames.some(item => regForBtnPrev.test(item) || regForBtnNext.test(item))) {
            layerOffset = 95
          }

          // 增加时间轴
          const layerTimeline = krpanoInterfaceObj.addlayer("skin_btn_timeline");
          layerTimeline.setvars({
            type:"image",
            url: `${window.bim_config.webserverurl}/Panorama/time.png`,
            parent: "skin_control_bar_buttons",
            align: "left",
            x: layerOffset,
            y: "0",
            width: "24",
            height: "24",
            keep: true,
            onclick: "js(handlerMapForLayer.toggleTimeline())",
          })
          layerTimeline.loadstyle("skin_glow")

          // 增加刷新
          const layerRefresh = krpanoInterfaceObj.addlayer("skin_btn_refresh");
          layerRefresh.setvars({
            type:"image",
            url: `${window.bim_config.webserverurl}/Panorama/global_img/interface_refresh.svg`,
            parent: "skin_control_bar_buttons",
            align: "left",
            x: layerOffset + 40,
            y: "0",
            width: "32",
            height: "32",
            keep: true,
            onclick: "js(handlerMapForLayer.refreshCurrentTour())",
          })
          layerRefresh.loadstyle("skin_glow")
        })
      }
    },

    // 打开全景图
    openurl(item,objparms) {
      var _this = this;
      console.log(item)

      _this.openIframeCheckedName = item;
      objparms ? _this.iframeFromClickList = true : _this.iframeFromClickList = false;

      // 先判断是否还在转换中
      if (item.PqFlag != 2) {
        _this.$message.warning('该全景图仍处于后台转换状态，请稍后刷新页面再试');
        return;
      }

      // // // var viewurl = window.bim_config.integrated_innerview + '/-Panorama' + item.PbUrl + '/vtour/tour2.html?organizeId=' + _this.$staticmethod._Get('organizeId');


      // var viewurl = _this.$staticmethod.getPanoUrl(item.PbUrl, _this.$staticmethod._Get('organizeId'), '');

      // if (!_this.auth_modifyauth) {
      //   viewurl += '&isDis=1';
      // }
      // if(objparms) viewurl += "&startscene=scene_" + this.clickpano_req_changeScene.PsScenename;
      // var randomnumber=Math.floor(Math.random()*100000)
      //  viewurl += "&timestamp=" + randomnumber;

      // _this.extdata.srcShowingPano = viewurl;

      const basepath = `${window.bim_config.webserverurl}/Panorama${item.PbUrl}/vtour/`
      const xmlurl = `${basepath}tour.xml?r=${(Math.random() * 100000 + 1)}`
      let setting = {}
      if (objparms) {
        const scenename = 'scene_' + this.clickpano_req_changeScene.PsScenename
        setting = { startscene: scenename }
      }
      this.detailsListShow = false
      this.extdata.isShowingPano = true
      this.isLoadingKrpano = true

      this.$nextTick(() => {
        // eslint-disable-next-line
        embedpano({
          xml: xmlurl,
          target: 'panoviewpreview',
          basepath,
          vars: setting,// 用来添加新的配置或者覆盖在xml中已经定义的变量
          html5: 'auto',
          passQueryParameters: true,
          // onready: function (krpano) {
          //   // _this.firstloadxml = true
          //   // _this.krpano = krpano
          //   // krpano.events.addListener('onclick', function () {
          //   //   if (_this.chooseP === false) {
          //   //     return
          //   //   }
          //   //   var mx = _this.krpano.get('mouse.x')
          //   //   var my = _this.krpano.get('mouse.y')
          //   //   var pnt = _this.krpano.screentosphere(mx, my)
          //   //   var h = pnt.x.toFixed(2)
          //   //   var v = pnt.y.toFixed(2)
          //   //   if (_this.choosePointType === 'TextHotSpot') {
          //   //     _this.textHotSpotInfo.LocationX = h
          //   //     _this.textHotSpotInfo.LocationY = v
          //   //     _this.addHotSpot = true
          //   //     _this.inputChangeSettingHS()
          //   //   } else if (_this.choosePointType === 'NavigationHotSpot') {
          //   //     _this.navigationHotSpotInfo.LocationX = h
          //   //     _this.navigationHotSpotInfo.LocationY = v
          //   //     _this.addNavigationHotSpot = true
          //   //     _this.inputChangeSettingNav()
          //   //   } else if (_this.choosePointType === 'AssetHotSpot') {
          //   //     _this.assetHotSpotInfo.LocationX = h
          //   //     _this.assetHotSpotInfo.LocationY = v
          //   //     _this.addAssetHotSpot = true
          //   //     // _this.inputChangeSettingHS()
          //   //   }
          //   //   _this.chooseP = false
          //   // })
          //   // krpano.events.addListener('onxmlcomplete', function () {
          //   //   const sceneName = krpano.xml.scene.split('scene_')[1]
          //   //   for (const panoListElement of _this.panoList) {
          //   //     if (panoListElement.PsScenename === sceneName) {
          //   //       krpano.set('scene[' + krpano.xml.scene + '].title', panoListElement.PchChname)
          //   //     }
          //   //   }
          //   //   _this.getSceneView()
          //   //   if (_this.firstloadxml) {
          //   //     _this.getPanoTagList()
          //   //     _this.firstloadxml = false
          //   //   }
          //   // })
          //   // krpano.events.addListener('onviewchange', function () {
          //   //   if (_this.assetHotspotArr.length > 0 & !_this.firstloadxml) {
          //   //     _this.UpdateCoordinates()
          //   //   }
          //   // })
          //   _this.isTimelineVisible = false;// 关闭全景图集
          //   _this.clickpanoindex = 5;
          //   _this.selectModelForRelevancew = false;
          //   _this.detailsListShow = false;
          // },
          onready: this.onKrpanoEmbedDone

        });
      })


    },
    // iframeLoadEnd(){
    //   let _this = this;
    //   if(_this.iframeFromClickList){
    //     setTimeout(()=>{
    //       const win = document.getElementById('pure-scene-iframe').contentWindow;
    //       // 添加事件调用视角转换
    //       if (win) {
    //         const data = {
    //             act: 'pano_req_changeScene', data: 'scene_' + _this.clickpano_req_changeScene.PsScenename
    //         };
    //         win.postMessage(data, "*");
    //       }
    //     },1000)
    //   }
    // },
    //
    getPbWidth(){
      var _this = this;
      var _s = {};
      _s['width'] = _this.extdata.uploadprogress.pbwidth + '%';
      return _s;
    },

    //
    getPbWidth_append(){
      var _this = this;
      var _s = {};
      _s['width'] = _this.extdata.uploadprogress_append.pbwidth + '%';
      return _s;
    },

    newid(){
      var _this = this;
      return (_this.S4() + _this.S4() + "-" + _this.S4() + "-" + _this.S4() + "-" + _this.S4() + "-" + _this.S4() + _this.S4() + _this.S4());
    },

    // Generate four random hex digits.
    S4() {
        return (((1 + Math.random()) * 0x10000) | 0).toString(16).substring(1);
    },

    // 进度条归位（隐藏，因为名称重复等错误）
    // 让上传按钮恢复可用状态
    resetuploadform() {
      var _this = this;
      _this.extdata.uploadm.disinput = false;
      _this.extdata.uploadprogress.isshowing = false;
      _this.extdata.uploadprogress.infotext = '';
      _this.extdata.uploadprogress.pbwidth = 0;
    },

    // 进度条归位（添加新场景）
    resetuploadform_append() {
      var _this = this;
      _this.extdata.uploadm_append.disinput = false;
      _this.extdata.uploadprogress_append.isshowing = false;
      _this.extdata.uploadprogress_append.infotext = '';
      _this.extdata.uploadprogress_append.pbwidth = 0;
    },



    // 追加后续图片，该函数为异步递归调用，第二个参数为此次调用应该上传的图片的所在索引
    func_appenduploading(files_from1, shouldUploadIndex, originnewid) {

      // 判断图片的所在索引如果超出了 files_from1 的有效最大索引，则直接弹出上传成功，会刷新列表
      var _this = this;
      if (files_from1.length <= shouldUploadIndex) {

        // 关闭对话框并刷新
        _this.$message.success('上传成功');
        _this.extdata.uploadm.isshowing = false;
        _this.getList();
        return;
      }

      // 上传并递归上传，上传时使用追加图片的接口

      // 得到一个新的guid
      var newguid = _this.$staticmethod.NewGuid();

      var posturl = `${window.bim_config.webserverurl}/api/Panorama/PanoramaFile/UploadImages?pbguid=${newguid}&willappend=0&Token=${this.$staticmethod.Get('Token')}`;


      // 准备 fd 参数
      var fd = new FormData();
      fd.append("F1", files_from1[shouldUploadIndex]);

      // 准备 config 参数
      _this.extdata.uploadm.canceltoken = _this.$axios.CancelToken;
      _this.extdata.uploadm.canceltoken_source = _this.extdata.uploadm.canceltoken.source();
       var config = {
        headers: {
          "Content-Type": "application/json"
        },
        onUploadProgress: progressEvent => {
          // 使用本地 progress 事件
          if (progressEvent.lengthComputable) {
            var num = Math.round(
              (progressEvent.loaded / progressEvent.total) * 100
            );

            // num 需要除以图片总数，再加上 100 / 图片总数 * （当前图片所在索引 - 1）
            var everypercent100 = 100.0 / files_from1.length;
            num = (num / files_from1.length) + everypercent100 * (shouldUploadIndex);

            _this.extdata.uploadprogress.isshowing = true;
            _this.extdata.uploadprogress.infotext = '正在上传文件...' + num.toFixed(2) + '%';
            _this.extdata.uploadprogress.pbwidth = num;

            //
            if (progressEvent.loaded == progressEvent.total) {
              _this.extdata.uploadprogress.infotext = `【图片${shouldUploadIndex + 1}/${files_from1.length}】服务器正在执行批处理...`;
            }
          }
        },
        cancelToken: _this.extdata.uploadm.canceltoken_source.token
      };

      // 执行上传
       _this.$axios.post(
        posturl,
        fd,
        config
      ).then(x => {

        // 追加后续图片，该函数为异步递归调用，第二个参数为此次调用应该上传的图片的所在索引
        //_this.func_appenduploading(files, 1);
        // http://www.probim.cn:8508/api/Home/File/copyFromSomeToSome?newpatch=
        // 62c27877-f904-26c2-81c3-44f932a12e62&oldpatch=cb3a5413-7649-586f-c6f0-43832ee0c8e0
        // 从 old copy 到 newpatch

        // 追加上传后，要拷贝服务器文件

        // 定义接口地址
        // oldpatch == originnewid
        var syncfileurl = `${window.bim_config.webserverurl}/api/Panorama/PanoramaFile/copyFromSomeToSome?Token=${this.$staticmethod.Get('Token')}&newpatch=${newguid}&oldpatch=${originnewid}`;
        _this.$axios.get(syncfileurl).then(y => {
          _this.func_appenduploading(files_from1, shouldUploadIndex + 1, originnewid);
        }).catch(y => {

        });


      }).catch(x => {
        _this.$message.warning('上传操作取消');
        _this.extdata.uploadm.disinput = false;
        _this.extdata.uploadm.isshowing = false;
      });

    },

    // 开始上传（新场景）：全景图内上传全景图
    _onok_upload_append() {

      var _this = this;
      if (_this.extdata.uploadm_append.disinput) {
        return;
      }

      // 无需输入图集名称
      // ///////////////

      // 得到一个新的guid
      var newguid = _this.$staticmethod.NewGuid();
      if(_this.m_collecttime_append == ''){
        _this.$message.error('请选择采集时间');
        return;
      }
      // 拿到 dom 及文件
      var files = document.getElementById("id_file_append").files;
      if (files.length == 0) {
        _this.$message.error('未选择全景图文件');
        return;
      }

      if(files[0].type.split('/')[0] !== 'image'){
        _this.$message.error('请选择图片文件');
        return
      }
      _this.extdata.uploadm_append.disinput = true;

      // 构造 formdata
      var fd = new FormData();
      fd.append("Files", files[0]);
      fd.append('pbguid',newguid);
      fd.append("willappend",0);
      fd.append("targetPatchGuid", _this.extdata.uploadm_append.pbguid);
      fd.append("collectDate", _this.$staticmethod.dtToString2(_this.m_collecttime_append));
      fd.append("organizeId", _this.$staticmethod._Get("organizeId"));

      //fd.append("comment", _this.m_comment_append); // 上传时填写的备注

      // 构造 url
      var _url = `${window.bim_config.webserverurl}/api/Panorama/PanoramaFile/UploadImages?Token=${this.$staticmethod.Get('Token')}`; //`${getConfig().panoApi}/api/Home/File/UploadImages?pbguid=${newguid}&willappend=0`,

      _this.extdata.uploadm_append.canceltoken = _this.$axios.CancelToken;
      _this.extdata.uploadm_append.canceltoken_source = _this.extdata.uploadm_append.canceltoken.source();

      var config = {
        // headers: {
        //   "Content-Type": "application/json"
        // },
        onUploadProgress: progressEvent => {
          // 使用本地 progress 事件
          if (progressEvent.lengthComputable) {
            var num = Math.round(
              (progressEvent.loaded / progressEvent.total) * 100
            );
            _this.extdata.uploadprogress_append.isshowing = true;
            _this.extdata.uploadprogress_append.infotext = '正在上传文件...' + num + '%';
            _this.extdata.uploadprogress_append.pbwidth = num;

            //
            if (progressEvent.loaded == progressEvent.total) {
              //_this.extdata.uploadprogress.infotext = '服务器正在执行批处理...';
            }
          }
        },
        cancelToken: _this.extdata.uploadm_append.canceltoken_source.token
      };

      _this.$axios.post(
          _url,
          fd,
          config
        ).then(x => {
          if(x.status == 200 && x.data.Ret > 0){
            // 弹出提示
            _this.$message.success('添加场景成功，即将自动刷新或稍后手动刷新重试');
            // 进度条归位
            _this.resetuploadform_append();
            // 关闭对话框
            _this.extdata.uploadm_append.isshowing = false;
            this.$nextTick(this.refreshCurrentTour) // 刷新当前全景图
          }else{
            _this.$message.error(x.data.Msg);
            _this.resetuploadform_append();
            _this.extdata.uploadm_append.isshowing = false;
          }
        }).catch(x => {
          debugger;
          _this.resetuploadform_append();
        });


      _this.clickpanoindex = -1;
    },

    // 开始上传：外部确定创建全景图
    _onok_upload(){
      var _this = this;
      if(_this.isuploading) return;

      if (_this.extdata.uploadm.disinput) {
        return;
      }

      // 判断图集名称是否已输入
      var pbname = document.getElementById('id_newname').value;
      if (!pbname || pbname.trim() == '') {
        _this.$message.error('请输入名称');
        return;
      }
      if(_this.m_collecttime == ''){
        _this.$message.error('请选择采集时间');
        return;
      }
      if (!_this.selectPanoTag){
        _this.$message.error('请选择标签')
        return
      }

      var fd = new FormData();
      var newid = _this.newid();
      fd.append("collectDate", _this.$staticmethod.dtToString(_this.m_collecttime));
      fd.append("organizeId", _this.$staticmethod._Get("organizeId"));

      fd.append("gisinfo", '');
      fd.append("pbname", document.getElementById('id_newname').value);
      fd.append("labelId", _this.selectPanoTag);
      var filedom = document.getElementById('id_files');
      var files = filedom.files;
      if (files.length == 0) {
        _this.$message.error('请上传图片');
        _this.extdata.uploadm.disinput = false;
        return false;
      }
      if(files[0].type.split('/')[0] !== 'image'){
        _this.$message.error('请选择图片文件');
        _this.extdata.uploadm.disinput = false;
        return false
      }
      _this.isuploading = true;
      _this.extdata.uploadm.disinput = true;
      for (var i = 0; i < files.length; i++) {
        //fd.append(`File${i}`, files[i]);
        fd.append("Files",files[i]);
      }
      _this.extdata.uploadm.canceltoken = _this.$axios.CancelToken;
      _this.extdata.uploadm.canceltoken_source = _this.extdata.uploadm.canceltoken.source();

      var config = {
        headers: {
          "Content-Type": "application/json"
        },
        onUploadProgress: progressEvent => {
          // 使用本地 progress 事件
          if (progressEvent.lengthComputable) {
            var num = Math.round(
              (progressEvent.loaded / progressEvent.total) * 100
            );
            _this.extdata.uploadprogress.isshowing = true;
            _this.extdata.uploadprogress.infotext = '正在上传文件...' + num + '%';
            _this.extdata.uploadprogress.pbwidth = num;

            //
            if (progressEvent.loaded == progressEvent.total) {
              //_this.extdata.uploadprogress.infotext = '服务器正在执行批处理...';
            }
          }
        },
        cancelToken: _this.extdata.uploadm.canceltoken_source.token,
        transformRequest: [function(data,headers){
          delete headers.post["Content-Type"];
          return data;
        }]
      };

    _this.isuploading = true;
    fd.append("pbguid",newid);
    fd.append("willappend",0);
    _this.$axios.post(
        `${window.bim_config.webserverurl}/api/Panorama/PanoramaFile/UploadImages?Token=${this.$staticmethod.Get('Token')}`,
        fd,
        config
      ).then(x => {
        // 关闭对话框并刷新
        if(x.status == 200 && x.data.Ret > 0){
          x.data.Data.length > 0 ?  _this.$message.success('上传成功') : _this.$message.warning(x.data.Msg);
          _this.isuploading = false;
          _this.extdata.uploadm.isshowing = false;
          _this.selectPanoTag = '';
          _this.getList();
        }else{
          _this.extdata.uploadprogress.isshowing = false;
          _this.$message.error(x.data.Msg);
          _this.isuploading = false;
          _this.extdata.uploadm.disinput = false;
        }
      }).catch(x => {
        debugger;
        _this.$message.error('请求出错');
        _this.extdata.uploadm.disinput = false;
        _this.extdata.uploadm.isshowing = false;
        _this.selectPanoTag = '';
        _this.isuploading = false;

      });

    },

    // 关闭上传：全景图内取消上传全景图
    // 取消请求
    _oncancel_upload_append() {

      // 取消请求
      var _this = this;
      _this.extdata.uploadm_append.canceltoken_source
      && _this.extdata.uploadm_append.canceltoken_source.cancel('cancel');

      // 关闭上传
      _this.extdata.uploadm_append.isshowing = false;

      this.clickpanoindex = -1
    },

    // 关闭上传：外部取消创建全景图
    // 取消请求
    _oncancel_upload(){

      // 取消请求
      var _this = this;
      _this.extdata.uploadm.canceltoken_source
      && _this.extdata.uploadm.canceltoken_source.cancel('cancel');

      // 关闭上传
      _this.extdata.uploadm.isshowing = false;
      _this.selectPanoTag = '';
      _this.isuploading = false;
      _this.extdata.uploadm.isshowing = false;

    },

    // 弹出上传
    toUpload(ev) {
      var _this = this;
      _this.closeall();
      _this.extdata.uploadm.isshowing = true;
      _this.extdata.uploadm.disinput = false;
      _this.extdata.uploadprogress.isshowing = false;
      _this.extdata.uploadprogress.infotext = '';
      ev.stopPropagation();
    },
    // 设置标签
    setLabelFun(){
      // 设置标签的时候刷新标签列表
      this.getTagOption();
      this.drawerEditShow = !this.drawerEditShow
      if(this.drawerEditShow){
        this.$refs.labelSetDialog.style.left = 0;
      }else{
        this.$refs.labelSetDialog.style.left = "-240px"
      }
    },

    // 按照标签搜索全景图
    selectChangeFun(val){
      if(val== null || val== undefined)  val = ""
			this.searchlabelId = val;
      this.getList();
    },
    // 搜索全景图
    searchName(val) {
			this.searchPbName = val;
			this.getList();
    },
    // 按 pbguid 移除
    delByPbGuid(pbguid) {
      var _this = this;
      _this.$axios({
        url:`${window.bim_config.webserverurl}/api/Panorama/PanoramaFile/RemoveItem?Token=${this.$staticmethod.Get('Token')}`,
        method:'post',
        data: {PbGuid: pbguid}
      }).then(x => {
        if (x.status == 200 && x.data.Ret > 0) {
          _this.getList();
          _this.$message.success('删除成功')
        } else {
          _this.$message.error(x.data.Msg);
          debugger;
        }
      }).catch(x => {
        debugger;
      });
    },

    // 弹出是否删除的对话框
    confirmToDel(ev) {
      var _this = this;
      _this.$confirm('确定删除该项？', '删除确认', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
         type: 'warning'
      }).then(x => {
        _this.delByPbGuid(_this.extdata.SelectedPbGuid);
        _this.closeall();
      }).catch(x => {
        _this.closeall();
      });
    },
    // 某一个标题的背景颜色
    computeTitleBg(item) {
      let _b = {};
      _b["background"] = item;
      return _b;
    },
    // 某一项显示的背景图
    computeItemImage(item) {
      var _this = this;
      var _s = {};
      _s["background-image"] = `url('${window.bim_config.webserverurl}/Panorama${item.PbUrl}/cover.png')`;//  'url(\'' + item.PbUrl + 'cover.png'+ '\')';
      return _s;
    },
    getItemImageUrl(item) {
      return `${window.bim_config.webserverurl}/Panorama${item.PbUrl}/cover.png`
    },
    getTagOption(){
      let _organizeId =  this.$staticmethod._Get('organizeId')
      this.$axios
        .get(
          `${window.bim_config.webserverurl}/api/Panorama/Label/GetList?OrganizeId=${_organizeId}&Token=${this.$staticmethod.Get('Token')}`
        )
        .then((res) => {
          // this.tagOptions = res.data.Data;
          this.tagOptions = res.data.Data.sort((a,b) => this.dayjs(b.CreateTime).valueOf() - this.dayjs(a.CreateTime).valueOf())
        });
    },
    // 获取数据
    getList(){
      var _this = this;
      var _organizeId = _this.$staticmethod._Get("organizeId");

      // 显示loading
      var _LoadingIns = _this.$loading({
        text:'加载中',
        target: document.getElementsByClassName('_css-pano-bottom')[0]
      });

      setTimeout(() => {

         _this.$axios({
            method:'get',
            url:`${window.bim_config.webserverurl}/api/Panorama/PanoramaFile/GetListByLabelGroup?organizeId=${_organizeId}&Token=${this.$staticmethod.Get('Token')}&labelId=${_this.searchlabelId}&pbName=${_this.searchPbName}`,
            data:{}
          }).then(x => {
            _LoadingIns.close();
            if (x.status == 200 && x.data.Ret > 0) {
              _this.extdata.datalist = x.data.Data;
            } else {
              _this.$message.error(x.data.Msg);
              debugger;
            }
          }).catch(x => {
            _LoadingIns.close();
            debugger;
          });
      }, 0);


    },

    // 计算菜单样式
    getContextStyle(){
      let _this = this;
      let _s = {};

      _s["left"] = _this.extdata.contextpos.left + 'px';
      _s["top"] = _this.extdata.contextpos.top + 'px';
      return _s;
    },
    getPanoEditStyle(){
      let _this = this;
      let _s = {};
      _s["left"] = _this.extdata.contextpos.left + 160 + 'px';
      _s["top"] = _this.extdata.contextpos.top + 50 + 'px';
      return _s;
    },

    // 是否已发布到GIS
    hasGis(item){
      return item.PbGisinfo && item.PbGisinfo.indexOf('{') >= 0;
    },

    // 关闭全部
    closeall() {
      var _this = this;
      _this.extdata.contextpos.isshowing = false;
    },

    // 显示菜单
    showmenu(ev, item) {
      // 获取所点击元素（按钮）的top及left
      var _this = this;
      var rect = ev.target.getBoundingClientRect();
      _this.changeTagShow = false;
      // 修改“发布到gis”菜单的中文
      // item.PbGisinfo && item.PbGisinfo.indexOf('{') >= 0
      if (_this.hasGis(item)) {
        _this.extdata.gisbtntext = '重新发布坐标';
      } else {
        _this.extdata.gisbtntext = '发布坐标';
      }

      // 设置上下文菜单的left及top
      _this.extdata.contextpos.top = rect.y + 12;
      _this.extdata.contextpos.left = rect.x + 12;

      // 记录所点击的id
      _this.extdata.SelectedPbGuid = item.PbGuid;
      _this.m_selectedobj = item;

      // 记录上下文菜单的宽及高度
      var context_h = 282;
      var context_w = 160;

      // 根据当前屏幕高度及菜单本身高度，修改contextpos.top的值
      // _this.extdata.contextpos.left = _this.extdata.contextpos.left > document.body.clientWidth - context_w
      // ?document.body.clientWidth - context_w
      // :_this.extdata.contextpos.left;
      // _this.extdata.contextpos.top = _this.extdata.contextpos.top > document.body.clientHeight - context_h
      // ?document.body.clientHeight - context_h
      // :_this.extdata.contextpos.top;
      if (_this.extdata.contextpos.left > document.body.clientWidth - context_w) {
        _this.extdata.contextpos.left = document.body.clientWidth - context_w;
      }
      if (_this.extdata.contextpos.top > document.body.clientHeight - context_h) {
        _this.extdata.contextpos.top = document.body.clientHeight - context_h;
      }

      // 显示出来菜单
      _this.detailsListShow = false;
      _this.extdata.contextpos.isshowing = true;
      ev.stopPropagation();

    },
    getEditClassifyPanoTag(){
      let _this = this;
      if(!_this.m_selectedobj.PbGuid || _this.m_selectedobj.PbGuid.length < 1 ) return
      _this.$axios
        .get(
          `${window.bim_config.webserverurl}/api/Panorama/Label/GetPanoramaLabel?pbGuid=${_this.m_selectedobj.PbGuid}&Token=${this.$staticmethod.Get('Token')}`
        )
        .then(x=>{
          if (x.status == 200 && x.data.Ret > 0) {
            _this.editClassifyPanoTag = x.data.Data;
					} else {
						_this.$message.error(x.data.Msg);
            return;
					}
        })
        .catch(x=>{
          _this.$message.error('请求异常，请稍后再试')
        })
    },

    selectChangeTag(item,index){
      // this.
      this.changeTagLableId = item.LabelId;
      this.changeTagclickindex = index;
    },
    sureChangeTag(){
      let _this = this;
      let _pbg = [];
      _pbg.push(_this.m_selectedobj.PbGuid);
      let _data = {
        "labelId": _this.changeTagLableId,
        "pbGuid": _pbg
      };
      _this.$axios
        .post(
          `${window.bim_config.webserverurl}/api/Panorama/Label/BindPanorama?Token=${this.$staticmethod.Get('Token')}`,
          _data
        )
        .then((res) => {
          if (res.status == 200 && res.data.Ret > 0) {
            _this.$message({
              message: '标签修改成功',
              type: "success",
            });
            _this.getList();
            _this.closeall();
          } else {
            _this.$message.error(res.data.Msg);
          }
        });
    },
    // 点击选择标签种类
    changeTagOptions(item){
      let _this = this;
      _this.tagShow = false;
      _this.tagChecked = item.label;
    },
    clickAddTag(){
      let _this = this;
      _this.tagShow = true;
    },
    changeList(item,selectedobj){
      this.clickpano_req_changeScene = item;  // 记录当前选中的全景图，在全景图iframe加载完之后，跳转到当前全景图
      // 打开全景图
      this.openurl(selectedobj,true)
      // 然后切换
    },
    // 全景图关联视点
    showSelectModelShow(){
      this.clickpanoindex = 2;
      this.sceneDialog = true
      // this.selectModelForRelevancew = true;
    },
    // 全景图添加锚点
    showAddHotSpot(){
      this.$message({
        type: 'info',
        message: '功能优化中'
      });
      return
      this.clickpanoindex = 0;
      const win = document.getElementById('pure-scene-iframe').contentWindow;
      // 添加事件调用视角转换
      if (win) {
        const data = {
            act: 'pano_req_addHotSpot', data: 'setpoint'
        };
        win.postMessage(data, "*");
      }
    },
    // 全景图上传全景图：全景图内上传全景图
    addNewScene(){
      let _this = this;
      _this.clickpanoindex = 1;

      // 进度条归;
      _this.resetuploadform_append();

      // 时间清空
      _this.m_collecttime_append = '';

      _this.extdata.uploadm_append.isshowing = true;
      _this.extdata.uploadm_append.pbguid = _this.openIframeCheckedName.PbGuid;

    },
    // 删除全景图:全景图内删除全景图
    RemoveScene(){
      if(this.sceneList.length <=1) {
        this.$message({
          type: "warn",
          message: "全景图内禁止删除"
        })
        return
      }
      this.clickpanoindex = 3;
      this.$confirm(`确认删除【${this.currentPsRealName}】全景图吗？`, '删除', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          // // 删除场景  RemoveScene
          // const win = document.getElementById('pure-scene-iframe').contentWindow;
          // // 添加事件调用视角转换
          // if (win) {
          //   const data = {
          //       act: 'pano_req_RemoveScene', data: 'setpoint'
          //   };
          //   win.postMessage(data, "*");
          // }
          // this.clickpanoindex = -1;
          const baseUrl= `${window.bim_config.webserverurl}/api/Panorama/PanoramaFile`
          const params = {
            PbGuid: this.openIframeCheckedName.PbGuid,
            SceneName: this.currentPsSceneName,
            ProjectId: this.$staticmethod._Get("organizeId"),
            Token: this.$staticmethod.Get('Token'),
          }
          this.$axios.post(`${baseUrl}/RemoveScene`,params).then((res) => {
            if(res && res.data && res.data.Ret == 1) {
              this.$message({
                type: 'success',
                message: '删除全景图成功'
              })
              this.refreshCurrentTour()
            } else {
              this.$message({
                type: 'error',
                message: res && res.data && res.data.Msg || '服务器端错误'
              })
            }
          }).catch(() => {
            this.$message({
              type: 'error',
              message: '删除全景图失败'
            })
          }).finally(() => {
            this.clickpanoindex = -1
          })
        }).catch(() => {
          this.clickpanoindex = -1
          this.$message({
            type: 'info',
            message: '已取消删除'
          });
        });
      // message 删除全景图 RemoveScene
    },
    handelClickOpenScene (data) {
      const _data = JSON.parse(data)
      this.sceneData = _data
      this.selectModelForRelevancew = true
      this.sceneDialog = false
    },
    handleClickCloseBimScene() {
      this.sceneDialog = false
      this.clickpanoindex = -1
    },
    closeSelectModel(){
      this.selectModelForRelevancew = false;
      this.clickpanoindex = -1
    },
    // 参数是由SelectiveModelForRelevance.vue组件中传出的：bimCameraInfo是scene.getCameraInfo()的值，bimSceneId是bim场景的SceneId
    relPointFun(bimCameraInfo,bimSceneId){
      let _this = this;
      _this.checkedModelAttr = {
        bimCameraInfo,
        bimSceneId
      }
      const win = document.getElementById('pure-scene-iframe').contentWindow;
      console.log(win, 1234)
      // 添加事件调用视角转换
      if (win) {
        this.bindModelView()
      }
    },
    bindModelView(PsScenename){
      let _this = this;
      if(!_this.selectModelForRelevancew || _this.clickpanoindex != 2) return
      let _data = {
        PsScenename: this.sceneData.SceneName,  // 选择的某个全景图列表的PsScenename
        PbGuid: _this.openIframeCheckedName.PbGuid,  // 选择的某个全景图的pbGuid
        ModelId: _this.checkedModelAttr.bimSceneId, // 选择的模型
        viewId: JSON.stringify(_this.checkedModelAttr.bimCameraInfo)   // 获取模型的视点
      };

      _this.$axios
        .post(
          `${window.bim_config.webserverurl}/api/Panorama/PanoramaScene/BindModelView?Token=${this.$staticmethod.Get('Token')}`,
          // `http://172.17.1.96:6311/api/Panorama/PanoramaScene/BindModelView?Token=${this.$staticmethod.Get('Token')}`,
          _data
        )
        .then((res) => {
          if (res.status == 200 && res.data.Ret > 0) {
           _this.$message.success(res.data.Msg);
          } else {
            console.log(res.data.Msg);
          }
        });
    },
    getStatusText(num){
      let _status = "";
      switch(num){
        case 0:
          _status="(待转换)";
          break;
        case 1:
          _status="(转换中)";
          break;
        case 2:
          _status="";
          break;
        case 3:
          _status="(转换失败)";
          break;
        default:
          _status="(待转换)";
      }
      return _status;
    }
  },
};
</script>
<style lang="scss" scoped>
._css-timelinetitle {
    cursor:pointer;
  }

  ._css-timelinetitle:hover {
    text-decoration:underline;
  }

  ._css-pano-time-line-tooltip{
    color:#1890FF;
  }

  ._css-pano-time-line {
    height:100%;
  }

  ._css-panoshowing-timeline {
      position: fixed;
      z-index: 9999;
      height: calc(100% - 320px);
      box-sizing: border-box;
      padding-left: 24px;
      padding-top: 32px;
      padding-right: 20px;
      bottom: 80px;
      left: 16px;
      border-radius: 8px;
      background-color: rgba(0, 0, 0, 0.3);
      // overflow-x: hidden;
      // overflow-y: auto;
      // margin-left: 16px;
      width:238px;
      .timeline-header {
        box-sizing: border-box;
        position: absolute;
        display: flex;
        top: 0;
        left: 0;
        padding-left: 24px;
        width: 100%;
        height: 32px;
        font-size: 14px;
        justify-content: space-between;
        align-items: center;
        color: #fff;
        border-top-left-radius: 8px;
        border-top-right-radius: 8px;
        background-color: rgba(0,0,0,0.5);
        .title {
          letter-spacing: 1px;
        }
        .tail {
          display: flex;
          height: 100%;
          padding-right: 8px;
          font-weight: 500;
          justify-content: flex-end;
          align-items: center;
          cursor: pointer;
          .dot {
            width: 4px;
            height: 4px;
            border-radius: 100%;
            background-color: #fff;
            &:not(:last-of-type) {
              margin-right: 3px;
            }
          }
        }
      }
      .timeline-content-wrapper {
        padding-top: 16px;
        max-height: calc(100% - 32px);
        overflow-y: auto;
        .timeline-content {
          .css-panoshowing-lineitem {
            margin-left: 3px;
          }
        }
      }
      .timeline-dots-related {
        position: absolute;
        left: 100%;
        top: 0;
        background-color: rgba(0,0,0,0.7);
        border: 1px solid #fff;
        color: #fff;
        border-radius: 8px;
        .content-wrapper {
          min-width: 200px;
          padding: 14px;
          .item {
            display: flex;
            height: 32px;
            justify-content: space-between;
            align-items: center;
            &:not(:last-of-type) {
              margin-bottom: 8px;
            }
            .playOrder {
              /deep/ .el-radio {
                margin-right: 0;
                &:not(:first-of-type) {
                  margin-left: 16px;
                }
                color: #fff;
                .el-radio__label {
                  padding-left: 6px;
                }
              }
            }
            .playInterval {
              /deep/ .el-input {
                .el-input__inner {
                  border-radius: 6px;
                  background-color: transparent;
                  color: #fff;
                  border: 1px solid #fff;
                }
              }
              /deep/ .el-input-number__decrease,
              /deep/ .el-input-number__increase {
                background-color: transparent;
                color: #fff;
              }
            }
            .btnPlay {
              display: flex;
              justify-content: flex-start;
              align-items: center;
              padding: 3px 8px;
              color: #fff;
              border-radius: 6px;
              font-size: 14px;
              border: 1px solid #fff;
              cursor: pointer;
              .icon {
                &.start {
                  width: 0;
                  height: 0;
                  border-top: 4px solid transparent;
                  border-left: 8px solid #fff;
                  border-bottom: 4px solid transparent;
                }
                &.stop {
                  width: 8px;
                  height: 8px;
                  background-color: #fff;
                }
              }
              .label {
                margin-left: 6px;
              }
            }

          }
        }
      }
  }


  ._css-qrcode-desc-input {
    height:100%;
    outline: none;
    border:none;
    width:100%;
    color:rgba(0, 0, 0, 0.65);
  }

  ._css-qrcode-desc-text {
      width: 100%;
      margin-top: 24px;
      border-radius: 4px;
      height: 32px;
      border: 1px solid rgba(24, 144, 255, 1);
      box-sizing: border-box;
      padding: 5px 16px 5px 16px;
  }

  ._css-qrcode-descs {
      height:120px;
      margin-right: 84px;
      margin-left: 24px;
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      flex: 1;
  }

  ._css-qrcode {
    width:120px;
    height:120px;
    margin-left:85px;
  }

  ._css-qrcodeandtext {
    padding-left:16px;
    padding-right: 16px;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    border-top:1px solid rgba(0,0,0,0.00);
  }

  ._css-qrcodearea {
    margin-top: 16px;
    height: 120px;
  }

  ._css-upload-file-inputctnin .el-input__prefix i.el-input__icon.el-icon-time {
      display: flex !important;
      align-items: center !important;
  }

  ._css-pano-all {
    box-sizing: border-box;
    width: 100%;
    height: 100%;
  }
  ._css-pano-top {
    height: 72px;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  .pano-top-left{
    display: flex;
    align-items: center;
  }
  .pano-top-right{
    position: relative;
    width: 480px;
    height: 32px;
    background: #FFFFFF;
    border-radius: 2px;
    margin-right: 24px;
    display: flex;
  }
  .right-input,._css-select{
    width:230px;
    border: 1px solid #D8D8D8;
  }

  ._css-select {
    position: relative;
    height: 32px;
    border-radius: 2px;
    border: 1px solid #D9D9D9;
    margin-right: 24px;
    i {
      position: absolute;
      top: 7px;
      right: 8px;
      color: #999999;
    }
    /deep/ .el-icon-arrow-up:before{
      content: ''
    }
    /deep/ .el-select, /deep/ .el-input{
        width: 90%;
      }
  }
 .pano-top-right .right-input i {
    position: absolute;
    top: 6px;
    right: 8px;
    color: #999999;
  }
  .pano-top-right  /deep/ .el-input__inner{
    line-height: 32px;
  }
  ._css-pano-bottom {
    height: calc(100% - 64px);
    margin-left: 16px;
    box-sizing: border-box;
  }
  ._css-pano-addbtn {
    width: 106px;
    height: 40px;
    background: #007AFF;
    border-radius: 2px;
    border: none;
    color: #fff;
    outline: none;
    margin-left: 16px;
    line-height: 40px;
    cursor: pointer;
    opacity: 0.8;
    text-align: center;
  }
  ._css-pano-addbtn:hover {
    opacity: 1;
  }
  ._css-pano-addbtn._css-dis {
    cursor: not-allowed;
    opacity: 0.3;
  }
  ._css-pano-list {
    width: 100%;
    height: 100%;
    box-sizing: border-box;
    display: flex;
    /* flex-wrap: wrap; */
    overflow-x: auto;
    align-content: flex-start;
  }
  ._css-pano-list-for{
    width: 290px;
    border-radius: 8px;
    margin-right: 24px;
    height: 100%;
  }
  .list-content{
    height: 100%;
  }
  .list-content .title {
    line-height: 38px;
    font-size: 16px;
    font-family: PingFangSC-Medium, PingFang SC;
    font-weight: 500;
    color: #FFFFFF;
    border-radius: 8px 8px 0px 0px;
  }
  .all-list-child {
    max-height: calc(100% - 60px);
    overflow-x: hidden;
    overflow-y: auto;
    background: #FFFFFF;
    border-radius: 0 0 8px 8px;
    border: 1px solid #e8e8e8;
  }
  ._css-pano-list._css-panolistempty {
    justify-content: space-around;
  }

  ._css-pano-i {
    box-sizing: border-box;
    cursor: pointer;
    background-color: #fff;
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
    width: 242px;
    height: 240px;
    border-radius: 4px;
    border: 1px solid #E8E8E8;
    margin: 24px 24px 0;
    &:last-child {
      margin-bottom: 24px;
    }
    &:hover {
      box-shadow: 0px 13px 24px -17px rgba(11, 41, 62, 0.8);
      border-color: #007AFF;
      ._css-pano-idelbtn {
        display: block;
      }
    }
  }
  ._css-pano-gisflag{
    position: absolute;
    right:8px;
    top:8px;
    width:26px;
    height:26px;
    background-image: url('../../../assets/svgs_loadbyurl/model_hasGIS.svg');
    background-size: contain;
      background-repeat: no-repeat;
  }
  ._css-pano-iimg {
    width: 100%;
    height: 168px;
    background-repeat: no-repeat;
    background-size: cover;
    background-position: center center;
  }
  ._css-pano-itext {
    font-size: 16px;
    height: 38px;
    overflow-x: hidden;
    text-overflow: ellipsis;
    width: 100%;
    white-space: nowrap;
    box-sizing: border-box;
    padding: 0;
    line-height: 37px;
    font-weight: 500;
    color: #222222;
  }
  ._css-pano-istatus {
    width: 100%;
    height: 38px;
    line-height: 38px;
    font-size: 12px;
    position: relative;
  }
  ._css-pano-idelbtn {
    position: absolute;
    right: 12px;
    top: calc(50% - 9px);
    display: none;
    color: #999;
  }
  ._css-pano-idelbtn:hover {
    color: #1890ff;
  }
  ._css-pano-istatustext {
    color: #c4c4c4;
    text-align: center;
  }
  ._css-pano-contextmenu {
    position: fixed;
    /* top: 0;
    left: 200px; */
    min-height: 48px;
    z-index: 4;
    box-shadow: 0px 0px 8px 0px rgba(0, 0, 0, 0.15);
    border-radius: 4px;
    width: 160px;
    background-color: #fff;
  }
  ._css-pano-contextmenu-i {
    height: 48px;
    padding-top: 4px;
    padding-bottom: 4px;
    box-sizing: border-box;
  }
  ._css-pano-contextmenu-in {
    height: 100%;
    width: 100%;
    cursor: pointer;
    display: flex;
    align-items: center;
  }

  ._css-pano-contextmenu-in._css-dis {
    opacity: 0.5;
    cursor: not-allowed;
  }

  ._css-pano-contextmenu-in:not(._css-dis):hover {
    background-color: rgba(0, 0, 0, 0.04);
  }
  ._css-pano-contextmenu-inicon {
    width: 16px;
      line-height: 40px;
      height: 100%;
      margin-left: 24px;
      font-size: 16px;
  }
  ._css-pano-contextmenu-intext {
    margin-left: 8px;
    font-size: 14px;
    height: 100%;
    line-height: 40px;
  }
  ._css-pano-upload{
    /* z-index: 4; */
    z-index: 1001;
    position: fixed;
    width:100%;
    height:100%;
    left:0;
    top:0;
    display: flex;
    align-items: center;
    justify-content: space-around;
    background-color: rgba(0, 0, 0, 0.3);
  }
  ._css-pano-upload-in{
    width: 410px;
      /* min-height: 294px; */
      background: rgba(255, 255, 255, 1);
      -webkit-box-shadow: 0px 13px 24px -17px rgba(11, 41, 62, 0.8);
      box-shadow: 0px 13px 24px -17px rgba(11, 41, 62, 0.8);
      border-radius: 4px;
  }
  ._css-upload-file-label {
    padding-left: 16px;
      box-sizing: border-box;
      font-size: 12px;
      color: rgba(0,0,0,0.5);
      margin-top: 4px;
      text-align: left;
  }
  ._css-upload-file-label2 {
    padding-left: 16px;
    box-sizing: border-box;
    margin-top: 24px;
    text-align: left;
    font-size: 14px;
    color: #222222;
  }
  ._css-upload-file-inputctn {
    height: 36px;
      width: 100%;
      box-sizing: border-box;
      padding: 0 16px 0 16px;
      margin-top: 4px;
      display: flex;
      align-items: center;
  }
  ._css-upload-file-inputctn2 {

      width: 100%;
      box-sizing: border-box;
      padding: 0 16px 0 16px;
      margin-top: 4px;
      display: flex;
      align-items: center;
  }
  ._css-upload-file-inputfile{
    width:100%;
  }
  ._css-upload-file-inputname{
    width: 100%;
      outline: none;
      height: 24px;
      line-height: 24px;
      border: none;
      background-color: #fff;
  }
  ._css-upload-file-inputshowpb {
    width: 100%;
      height: 24px;
      border-radius: 8px;
      overflow: hidden;
  }
  ._css-upload-file-inputshowpb-in{
      height: 100%;
      background-color: #1890FF;
  }
  ._css-upload-file-inputctnin{
      width: 100%;
      border: 1px solid #E8E8E8;
      height: 36px;
      display: flex;
      align-items: center;
      box-sizing: border-box;
      padding:0 8px 0 8px;
  }
  ._css-upload-file-inputctnin2 {
        border-radius: 4px;
      width: 100%;
      border: 1px solid rgba(0, 0, 0, 0.3);
      display: flex;
      align-items: center;
      box-sizing: border-box;
      padding:0 8px 0 8px;
  }

  ._css-comment-appending {
      width: 100%;
      height: 60px;
      resize: none;
      border: none;
      margin: 12px 4px 12px 4px;
      outline: none;
      color: rgba(0, 0, 0, 0.65) !important;
      font-size: 14px;
  }

  ._css-pano-viewer {
      position: fixed;
      width: 100%;
      height: 100%;
      top: 0;
      left: 0;
      z-index: 1000;
      background-color: #fff;
  }
  ._css-pano-ifri{
      border: none;
      width: 100%;
      height: 100%;
  }
  ._css-iframe-open-pano{
    position: relative;
    /* margin-top: 72px; */
    width: 100%;
    height: 100%;
    /* height: calc( 100% - 72px); */
    display: flex;
  }
  ._css-loading-krpano {
    position: absolute;
    display: flex;
    z-index: 99999;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
    justify-content: center;
    align-items: center;
    .content {
      color: #fff;
      font-size: 32px;
      font-weight: 500;
    }
  }
  ._css-pano-iframe-small{
      height: 100%;
    width: 50% !important;
  }
  ._css-pano-rel-element{
    width: 50%;
    height: 100%;
  }
  ._css-pano-close{
      font-size: 20px;
      flex: none;
      width: 30px;
      height: 30px;
      position: absolute;
      background-image: url(/Content/images/ProjectManage/Project/close_normal.png);
      background-repeat: no-repeat;
      cursor: pointer;
      color: rgba(0, 0, 0, 0.4);
      border: 1px solid transparent;
      border-radius: 50%;
      background-color: rgba(255, 255, 255, 0.8);
      right: 0;
      top: 0;
      line-height: 30px;
      display: flex;
      justify-content: space-around;
  }
  ._css-gis-selectpoint {
      width: 80%;
      height: 80%;
      position: fixed;
      top: 10%;
      left: 10%;
      z-index: 4;
  }
  iframe#id_gis_sel {
      width: 100%;
      height: 100%;
      border: none;
  }
  .select-panorama-tag /deep/ .el-input__inner{
    border: 1px solid #E8E8E8;
    border-radius: 0px;
    padding-left: 10px;
    line-height: 34px;
  }

  input::-webkit-input-placeholder { /* WebKit browsers */
    color:#999999 !important;
  }
  .select-panorama-tag /deep/ .el-select{
    width:100%;
    height: 34px;
  }
  .css-title-pano{
    position: absolute;
    top: 0;
    left: 0;
    z-index: 1;
    background: linear-gradient(180deg, rgba(7, 28, 72, 0.5) 0%, rgba(7, 28, 72, 0.03) 100%);;
    width: 100%;
    height: 72px;
    color:#fff;
  }
  .css-title-left{
    position: absolute;
    left: 24px;
    top: 24px;
  }
  .css-title-menu{
    position: absolute;
    top: 0px;
    right: 50px;
    display: flex;
  }
  .css-title-right{
    margin-right:10px;
    cursor: pointer;
    background: transparent;
    padding: 12px;
  }
  .css-title-right-checked{
    background: rgba(255, 255, 255, 0.15);
    padding: 12px;
  }
  .css-menu{
    font-size: 12px;
  }
  .css-image{
    width: 24px;
    height: 24px;
    margin: 0 auto 6px;
    background-image: url('../../../assets/images/icon-pano-gis.png');
    background-size: contain;
    background-repeat: no-repeat;
  }

.icon-edit{
  display: inline-block;
  width:16px;
  height: 16px;
  background-image: url('../../../assets/images/icon-pano-gis.png');
  background-size: contain;
  background-repeat: no-repeat;
}
.icon-pano-gis{
  background-image: url('../../../assets/images/icon-pano-gis.png');
}
._css-pano-contextmenu-in:hover ._css-pano-contextmenu-intext{
  color: #007AFF;
}
._css-pano-contextmenu-in:hover .icon-pano-gis{
  background-image: url('../../../assets/images/icon-pano-gis-hover.png');
}
.icon-pano-timeline{
  background-image: url('../../../assets/images/icon-pano-timeline.png');
}
._css-pano-contextmenu-in:hover .icon-pano-timeline{
  background-image: url('../../../assets/images/icon-pano-timeline-hover.png');
}
.icon-pano-share{
  background-image: url('../../../assets/images/icon-pano-share.png');
}
._css-pano-contextmenu-in:hover .icon-pano-share{
  background-image: url('../../../assets/images/icon-pano-share-hover.png');
}
.icon-pano-del{
  background-image: url('../../../assets/images/icon-pano-del.png');
}
._css-pano-contextmenu-in:hover .icon-pano-del{
  background-image: url('../../../assets/images/icon-pano-del-hover.png');
}
.icon-pano-resetname{
  background-image: url('../../../assets/images/icon-pano-resetname.png');
}
._css-pano-contextmenu-in:hover .icon-pano-resetname{
  background-image: url('../../../assets/images/icon-pano-resetname-hover.png');
}
.icon-pano-edittag{
  background-image: url('../../../assets/images/icon-pano-edittag.png');
}
._css-pano-contextmenu-in:hover .icon-pano-edittag{
  background-image: url('../../../assets/images/icon-pano-edittag-hover.png');
}
.icon-pano-closepano{
  background-image: url('../../../assets/images/icon-pano-closepano.png');
}
.icon-pano-delpano{
  background-image: url('../../../assets/images/icon-pano-delpano.png');
}
.icon-pano-rel{
  background-image: url('../../../assets/images/icon-pano-rel.png');
}
.icon-pano-anchor{
  background-image: url('../../../assets/images/icon-pano-anchor.png');
}
.icon-pano-addpano{
  background-image: url('../../../assets/images/icon-pano-addpano.png');
}

._css-pano-reset-classify{
  /* display: none; */
  position: fixed;
  width: 114px;
  padding: 8px;
  background: #FFFFFF;
  box-shadow: 0px 2px 4px 0px rgba(0, 38, 77, 0.15);
  border-radius: 2px;
}

.select-tag-pan-resetclass{
  max-height: 320px;
  min-height: 64px;
  overflow-y: auto;
}
.select-tag-pan-resetclass ul li{
  line-height: 32px;
  margin-top: 2px;
  border-radius: 2px;
}
.select-tag-pan-resetclass ul li:hover{
  background: #F8F8F8;
}
.select-color{
  color: #007AFF;
  background: #F8F8F8;
}
._css-dialog-pano-edit{
  width: 43px;
  line-height: 20px;
  background: #007AFF;
  border-radius: 2px;
  color: #FFFFFF;
  float: right;
  text-align: center;
  cursor: pointer;
  margin-top: 8px;
  font-size:12px;
}
.edit-cancel{
  line-height:28px;
  padding: 0 10px;
  font-size: 12px;
  cursor: pointer;
  text-align: center;
}
.edit-sure{
  color: #007AFF;
}
.issue-btn{
  text-align: end;
  margin: 16px 14px 14px 16px;
}
.btn-css{
  display: inline-block;
  line-height: 40px;
  padding: 0 25px;
  font-size: 14px;
  margin-right: 10px;
  border-radius: 4px;
  cursor: pointer;
}
.btn-ok{
  color: #fff;
  background: #1890ff!important;
}
.btn-css.uploading-false{
  background: #999999 !important;
  cursor: not-allowed;
}

.paon-bottom-content{
  margin: 16px 20px;
  background: #FFFFFF;
  border-radius: 2px;
  height: calc(100% - 50px - 32px);
}
.pano-set-icon{
  cursor: pointer;
  width: 20px;
  height: 20px;
  background-image: url('../../../assets/images/p-sx.png');
  background-size: contain;
  background-repeat: no-repeat;
  margin: 0 20px;
}

.choose-lable{
  position: absolute;
  top: 35px;
  left: -20px;
  width: 160px;
  max-height: 207px;
  overflow-y: auto;
  background: #FFFFFF;
  box-shadow: 0px 2px 4px 0px rgba(0,38,77,0.15);
  border-radius: 2px;
  z-index: 1;
  ul{
    margin:8px 11px;

    li{
      height: 32px;
      line-height: 32px;
      border-radius: 2px;
      background: #FAFAFA;
      font-size: 14px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #666666;
      width: 126px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    li:hover{
      color: #007AFF;
    }
  }
}
.pano-choose-set{
  background-image: url('../../../assets/images/p-bq.png');
}
.labelSetDialog{
  position: fixed;
  left: -240px;
  top: 0;
  z-index: 55;
  width: 240px;
  height: 100%;
  background: #fff;
  transition: all 0.5s ease;
  overflow: auto;
}
</style>
