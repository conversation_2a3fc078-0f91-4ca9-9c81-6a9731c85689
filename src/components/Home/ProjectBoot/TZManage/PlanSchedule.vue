<template>
  <div
    class="tz-page-plan-edit-wrapper"
    v-loading="isUpdating"
    element-loading-text="加载中"
    element-loading-spinner="el-icon-loading"
    element-loading-background="rgba(0,0,0,0.6)"
  >
    <div class="header page">
      <el-tooltip effect="dark" content="点击切换到图表模式" placement="top">
        <div
          class="item changePriviewType"
          @click.stop="changePreviewToChartMode"
        >
          <img
            class="changeTypeIcon"
            src="../../../../assets/images/chart.png"
            alt=""
          />
          <div class="changeTypeLabel">切换</div>
        </div>
      </el-tooltip>

      <div v-if="hasAuth('TZJH_EDIT')" class="rest">
        <template v-if="isLocalEditMode">
          <el-button
            class="btn"
            @click.stop="quitLocalEditMode"
            :disabled="isUpdating"
            >取消</el-button
          >
          <el-button
            class="btn save"
            @click.stop="doSaveBusiness"
            :disabled="isUpdating"
            :loading="isUpdating"
            >保存</el-button
          >
        </template>
        <el-button v-else>
          <el-button
            class="btn edit"
            @click.stop="isLocalEditMode = true"
            :disabled="isUpdating"
            >编辑</el-button
          >
        </el-button>
      </div>
    </div>
    <div class="main">
      <div class="row">
        <div class="header">
          <div class="item label">工程总投资</div>
          <div class="item">单位：&nbsp;万元</div>
          <div class="rest">
            <el-button
              v-if="isLocalEditMode && hasAuth('TZJH_EDIT')"
              class="btn"
              @click.stop="addTableRow(phaseTable.data, 'phaseTable')"
            >
              新增投资阶段
            </el-button>
          </div>
        </div>
        <div class="row-content">
          <el-table
            :data="phaseTable.data"
            :border="true"
            empty-text="暂无数据,请添加阶段"
          >
            <el-table-column
              type="index"
              width="70"
              label="序号"
              :resizable="false"
            >
            </el-table-column>
            <el-table-column
              v-for="item in phaseTable.columns"
              :key="item.prop"
              :property="item.prop"
              :label="item.label"
              :width="item.width"
              :resizable="false"
            >
              <template slot-scope="scope">
                <div class="valueWrapper">
                  <template v-if="isLocalEditMode">
                    <el-checkbox
                      v-if="item.prop === 'totalInvestmentFrom'"
                      v-model="scope.row.totalInvestmentFrom"
                      @change="
                        onTotalInvestmentFromChange(phaseTable.data, scope, 1)
                      "
                    ></el-checkbox>
                    <el-input
                      v-else-if="item.prop === 'investmentAmount'"
                      class="inputCus"
                      placeholder="请输入"
                      v-model="scope.row[item.prop]"
                      type="number"
                      @input="syncModelValue(scope.row, item.prop)"
                      @change="syncModelValue(scope.row, item.prop)"
                      clearable
                    ></el-input>
                    <template v-else>
                      <el-input
                        class="inputCus"
                        placeholder="请输入"
                        v-model="scope.row[item.prop]"
                        type="text"
                        clearable
                      ></el-input>
                    </template>
                  </template>
                  <template v-else>
                    <el-checkbox
                      v-if="item.prop === 'totalInvestmentFrom'"
                      v-model="scope.row.totalInvestmentFrom"
                      :disabled="true"
                    ></el-checkbox>
                    <template v-else>
                      {{ scope.row[item.prop] }}
                      <i
                        v-if="
                          item.prop === 'investmentPhase' &&
                          scope.row.totalInvestmentFrom === true
                        "
                        class="phaseCurrent el-icon-star-on"
                      ></i>
                    </template>
                  </template>
                </div>
              </template>
            </el-table-column>
            <el-table-column width="70" label="操作" :resizable="false">
              <template slot-scope="scope">
                <div class="operation">
                  <el-button
                    @click.stop="
                      removeTableRow(phaseTable.data, scope.row, 'phaseTable')
                    "
                    :disabled="!isLocalEditMode || !hasAuth('TZJH_DELETE')"
                    plain
                    class="btn danger"
                  >
                    删除
                  </el-button>
                </div>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
      <div class="row">
        <div class="header">
          <div class="item label">年度计划</div>
          <div class="item">单位：&nbsp;万元</div>
          <div class="rest">
            <el-button
              v-if="isLocalEditMode && hasAuth('TZJH_EDIT')"
              class="btn"
              @click.stop="addTableRow(yearPlanTable.data, 'yearPlanTable')"
            >
              新增年度
            </el-button>
            <el-button
              v-if="isLocalEditMode && hasAuth('TZJH_EDIT')"
              class="btn"
              @click.stop="
                addTableColumn(
                  yearPlanTable.data,
                  yearPlanTable.columns,
                  'yearPlanTable'
                )
              "
            >
              新增资金需求分类
            </el-button>
            <el-button
              v-if="isLocalEditMode && hasAuth('TZJH_EDIT')"
              class="btn"
              @click.stop="showRemoveColumnModal(yearPlanTable.columns)"
            >
              删除资金需求分类
            </el-button>
          </div>
        </div>
        <div class="row-content">
          <el-table
            :data="yearPlanTable.data"
            :border="true"
            tooltip-effect="dark"
          >
            <el-table-column
              type="index"
              width="70"
              label="序号"
              :resizable="false"
            >
            </el-table-column>
            <el-table-column
              v-for="item in yearPlanTable.columns"
              :show-overflow-tooltip="true"
              :key="item.prop"
              :property="item.prop"
              :label="item.label"
              :width="item.width"
              :resizable="
                item.resizable ? item.resizable : item.width ? false : true
              "
            >
              <template slot="header" slot-scope="scope">
                <div v-if="isLocalEditMode" class="valueWrapper isHeader">
                  <el-input
                    class="inputCus"
                    v-model="scope.column.label"
                    placeholder="请输入"
                    clearable
                    :disabled="getTableColumnDisabled(item.prop)"
                    @change="
                      onTableColumnLabelChange(
                        $event,
                        yearPlanTable.columns,
                        item.prop
                      )
                    "
                    @input="
                      onTableColumnLabelChange(
                        $event,
                        yearPlanTable.columns,
                        item.prop
                      )
                    "
                  />
                </div>
                <template v-else>
                  {{ item.label }}
                </template>
              </template>

              <template slot-scope="scope">
                <div class="valueWrapper">
                  <template v-if="isLocalEditMode">
                    <el-date-picker
                      v-if="item.prop === 'year'"
                      class="dateCus"
                      type="year"
                      v-model="scope.row[item.prop]"
                      placeholder="请选择"
                    ></el-date-picker>
                    <el-input
                      v-else-if="item.prop === 'moneyRequires'"
                      :disabled="true"
                      class="inputCus"
                      v-model="scope.row[item.prop]"
                      placeholder="请输入"
                    ></el-input>
                    <el-input
                      v-else
                      class="inputCus"
                      v-model="scope.row[item.prop]"
                      placeholder="请输入"
                      clearable
                      type="number"
                      @input="calcMoneyRequiresAndFormatProp(scope.row)"
                      @change="calcMoneyRequiresAndFormatProp(scope.row)"
                    ></el-input>
                  </template>
                  <template v-else>
                    <el-date-picker
                      v-if="item.prop === 'year'"
                      class="dateCus"
                      v-model="scope.row.year"
                      type="year"
                      :disabled="true"
                    ></el-date-picker>
                    <div v-else>
                      {{ scope.row[item.prop] }}
                    </div>
                  </template>
                </div>
              </template>
            </el-table-column>
            <el-table-column width="70" label="操作" :resizable="false">
              <template slot-scope="scope">
                <div class="operation">
                  <el-button
                    class="btn danger"
                    plain
                    :disabled="!isLocalEditMode || !hasAuth('TZJH_DELETE')"
                    @click.stop="
                      removeTableRow(
                        yearPlanTable.data,
                        scope.row,
                        'yearPlanTable'
                      )
                    "
                  >
                    删除
                  </el-button>
                </div>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
      <div class="row">
        <div class="header">
          <div class="item label">月度计划</div>
          <div class="item">单位：&nbsp;万元</div>
        </div>
        <div class="row-content">
          <el-table :data="monthPlanTable.data" :border="true">
            <el-table-column
              type="index"
              width="70"
              label="序号"
              :resizable="false"
            >
            </el-table-column>
            <el-table-column
              v-for="item in monthPlanTable.columns"
              :key="item.prop"
              :property="item.prop"
              :label="item.label"
              :width="item.width"
              :resizable="false"
            >
              <template slot-scope="scope">
                <div class="valueWrapper">
                  <template v-if="isLocalEditMode">
                    <el-date-picker
                      v-if="item.prop === 'year'"
                      class="dateCus"
                      type="year"
                      v-model="scope.row[item.prop]"
                      placeholder="请选择"
                      disabled
                    ></el-date-picker>
                    <el-input
                      v-else
                      class="inputCus"
                      v-model="scope.row[item.prop]"
                      placeholder="请输入"
                      clearable
                      type="number"
                      @input="syncModelValue(scope.row, item.prop)"
                      @change="syncModelValue(scope.row, item.prop)"
                    ></el-input>
                  </template>
                  <template v-else>
                    <el-date-picker
                      v-if="item.prop === 'year'"
                      class="dateCus"
                      v-model="scope.row.year"
                      type="year"
                      :disabled="true"
                    ></el-date-picker>
                    <div v-else>
                      {{ scope.row[item.prop] }}
                    </div>
                  </template>
                </div>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
      <div class="row">
        <div class="header">
          <div class="item label">合同信息</div>
          <div class="item">单位：&nbsp;万元</div>
          <div class="rest">
            <el-button
              v-if="isLocalEditMode && hasAuth('TZJH_EDIT')"
              class="btn"
              @click.stop="addTableRow(contractTable.data, 'contractTable')"
            >
              新增合同
            </el-button>
          </div>
        </div>
        <div class="row-content">
          <el-table :data="contractTable.data" :border="true">
            <el-table-column
              type="index"
              width="70"
              label="序号"
              :resizable="false"
            >
            </el-table-column>
            <el-table-column
              v-for="item in contractTable.columns"
              :key="item.prop"
              :property="item.prop"
              :label="item.label"
              :width="item.width"
              :resizable="
                item.resizable ? item.resizable : item.width ? false : true
              "
            >
              <template slot-scope="scope">
                <div class="valueWrapper">
                  <template v-if="isLocalEditMode">
                    <template v-if="item.prop === 'contractType'">
                      <el-select
                        size="mini"
                        class="selectCus"
                        v-model="scope.row.contractType"
                        placeholder="请选择"
                      >
                        <el-option
                          v-for="item in contractTypeList"
                          :key="item.id"
                          :label="item.label"
                          :value="item.id"
                        ></el-option>
                      </el-select>
                    </template>
                    <el-input
                      v-else-if="item.prop === 'contractAmount'"
                      class="inputCus"
                      v-model="scope.row[item.prop]"
                      placeholder="请输入"
                      clearable
                      type="number"
                      @input="syncModelValue(scope.row, item.prop)"
                      @change="syncModelValue(scope.row, item.prop)"
                    ></el-input>
                    <el-input
                      v-else-if="item.prop === 'contractContent'"
                      class="inputCus textareaCus"
                      v-model="scope.row[item.prop]"
                      placeholder="请输入"
                      type="textarea"
                      :maxlength="200"
                      :autosize="{ minRows: 4 }"
                      :show-word-limit="true"
                    ></el-input>
                    <el-input
                      v-else
                      class="inputCus"
                      v-model="scope.row[item.prop]"
                      placeholder="请输入"
                      :maxlength="30"
                      :show-word-limit="true"
                      clearable
                      type="text"
                    ></el-input>
                  </template>
                  <template v-else>
                    <el-tooltip
                      v-if="item.prop === 'contractContent'"
                      effect="dark"
                      placement="top"
                    >
                      <div
                        slot="content"
                        style="max-width: 500px; line-height: 20px"
                      >
                        {{ scope.row[item.prop] }}
                      </div>
                      <div class="singleText">
                        {{ scope.row[item.prop] }}
                      </div>
                    </el-tooltip>
                    <div v-else class="singleText">
                      {{ scope.row[item.prop] }}
                    </div>
                  </template>
                </div>
              </template>
            </el-table-column>
            <el-table-column width="70" label="操作" :resizable="false">
              <template slot-scope="scope">
                <div class="operation">
                  <el-button
                    class="btn danger"
                    plain
                    :disabled="!isLocalEditMode || !hasAuth('TZJH_DELETE')"
                    @click.stop="
                      removeTableRow(
                        contractTable.data,
                        scope.row,
                        'contractTable'
                      )
                    "
                  >
                    删除
                  </el-button>
                </div>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </div>
    <div class="modal-yearPlanColumns">
      <el-dialog
        custom-class="dialogCus"
        title="删除资金需求分类"
        :visible.sync="isModalVisible"
        top="26vh"
        width="400px"
      >
        <div class="contentWrapper">
          <div v-for="column in modalColumns" :key="column.prop" class="column">
            <div class="columnName">
              <CommonSVG icon-class="money" :size="16"></CommonSVG>
              <div class="label">{{ column.label }}</div>
            </div>
            <el-button
              class="btnRemove"
              @click.stop="removeYearPlanColumn(column)"
            >
              删除
            </el-button>
          </div>
        </div>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import CommonSVG from "@/components/Common/CommonSVG";
const regForEmpty = /^\s*$/i;
const regForAllZero = /^\s*0+$/i;
const regForZeroStart = /^\s*0+/i;
const FixedColumns1 = [
  {
    prop: "year",
    label: "年度",
    width: "120",
  },
  {
    prop: "yearPlan",
    label: "年度计划",
    width: "120",
    resizable: true,
  },
  {
    prop: "moneyRequires",
    label: "资金需求",
    width: "120",
    resizable: true,
  },
];
const FixedColumns2 = [
  {
    prop: "others",
    label: "其他",
    width: "120",
  },
];
export default {
  name: "PlanSchedule",
  props: {
    isEditMode: {
      type: Boolean,
      default: false,
    },
  },
  components: { CommonSVG },
  data() {
    return {
      isModalVisible: false,
      disabledProps: [],
      modalColumns: [],
      isLocalEditMode: false,
      isUpdating: false,
      projectId: "",
      // 合同类型列表
      contractTypeList: [
        {
          id: "施工合同",
          label: "施工合同",
        },
        {
          id: "采购合同",
          label: "采购合同",
        },
        {
          id: "咨询服务合同",
          label: "咨询服务合同",
        },
        {
          id: "租赁合同",
          label: "租赁合同",
        },
        {
          id: "征地拆迁",
          label: "征地拆迁",
        },
        {
          id: "其他",
          label: "其他",
        },
      ],

      // 工程总投资，阶段表数据
      phaseTable: {
        columns: [
          {
            prop: "investmentPhase",
            label: "投资阶段",
            width: "",
          },
          {
            prop: "investmentAmount",
            label: "投资金额",
            width: "",
          },
          {
            prop: "totalInvestmentFrom",
            label: "总投资取值",
            width: "",
          },
        ],
        data: [],
      },

      // 年度计划
      yearPlanTable: {
        columns: [],
        data: [],
        nextYear: new Date().getFullYear(),
      },

      // 月度计划
      monthPlanTable: {
        columns: [
          {
            prop: "year",
            label: "年度",
            width: "120",
          },
          {
            prop: "1",
            label: "1月",
            width: "",
          },
          {
            prop: "2",
            label: "2月",
            width: "",
          },
          {
            prop: "3",
            label: "3月",
            width: "",
          },
          {
            prop: "4",
            label: "4月",
            width: "",
          },
          {
            prop: "5",
            label: "5月",
            width: "",
          },
          {
            prop: "6",
            label: "6月",
            width: "",
          },
          {
            prop: "7",
            label: "7月",
            width: "",
          },
          {
            prop: "8",
            label: "8月",
            width: "",
          },
          {
            prop: "9",
            label: "9月",
            width: "",
          },
          {
            prop: "10",
            label: "10月",
            width: "",
          },
          {
            prop: "11",
            label: "11月",
            width: "",
          },
          {
            prop: "12",
            label: "12月",
            width: "",
          },
        ],
        data: [],
      },

      // 合同信息
      contractTable: {
        columns: [
          {
            prop: "contractName",
            label: "合同名称",
            width: "400",
            resizable: true,
          },
          {
            prop: "contractType",
            label: "合同类型",
            width: "160",
          },
          {
            prop: "contractAmount",
            label: "合同金额",
            width: "200",
            resizable: true,
          },
          {
            prop: "partnerUnit",
            label: "乙方单位",
            width: "400",
            resizable: true,
          },
          {
            prop: "contractContent",
            label: "合同支付条款",
            width: "",
            resizable: true,
          },
        ],
        data: [],
      },
    };
  },
  computed: {
    isPropsEditMode() {
      return this.isEditMode;
    },
  },
  created() {
    this.disabledProps = FixedColumns1.concat(FixedColumns2).map(
      (item) => item.prop
    );
    this.isLocalEditMode = this.isEditMode;
    this.projectId = sessionStorage.getItem("organizeId");
  },
  mounted() {
    this.doBusiness();
  },
  methods: {
    changePreviewToChartMode() {
      this.$emit("changeEditMode", false);
      this.$emit("changePreviewType", "chart");
    },
    hasAuth(buttonCode) {
      const auths = JSON.parse(sessionStorage.getItem("menuListHasAuth"));
      if (
        auths &&
        auths.length &&
        auths.some((item) => item.ButtonCode === buttonCode)
      ) {
        return true;
      } else {
        return false;
      }
    },
    getTableColumnDisabled(prop) {
      return this.disabledProps.includes(prop);
    },
    doBusiness() {
      this.$api
        .GetProjectDetail({ projectId: this.projectId })
        .then((res) => {
          if (res && res.Ret === 1) {
            // 还原投资阶段表格数据
            this.setPhaseTableRelatedData(res.Data);

            // 还原年度计划表格列、数据
            this.setYearPlanTableRelatedData(res.Data);

            // 还原月度计划数据
            this.setMonthPlanTableRelatedData(res.Data);

            // 还原合同信息
            this.setContractTableRelatedData(res.Data);
          } else {
            this.resetPhaseTableRelatedData();
            this.resetYearPlanTableRelatedData();
            this.resetMonthPlanTableRelatedData();
            this.resetContractTableRelatedData();
          }
        })
        .catch(() => {
          this.resetPhaseTableRelatedData();
          this.resetYearPlanTableRelatedData();
          this.resetMonthPlanTableRelatedData();
          this.resetContractTableRelatedData();
        });
    },

    setPhaseTableRelatedData(res) {
      if (!res || !res.ProjectPhases || !res.ProjectPhases.length) {
        this.resetPhaseTableRelatedData();
      } else {
        this.phaseTable.data = res.ProjectPhases.map((item) => {
          return {
            investmentPhase: item.PhaseName,
            investmentAmount: item.Money,
            totalInvestmentFrom: item.IsTotal,
          };
        });
      }
    },
    resetPhaseTableRelatedData() {
      this.phaseTable.data = [];
    },
    onTotalInvestmentFromChange(source, scope, tableType) {
      switch (tableType) {
        case 1: {
          source.forEach((item) => {
            item.totalInvestmentFrom = false;
          });
          scope.row.totalInvestmentFrom = true;
          break;
        }
      }
    },
    checkPhaseTableData(showMsg = true) {
      const dataLen = this.phaseTable.data.length;
      if (dataLen < 1) {
        return true;
      } else {
        let isValid = true;
        let hasEmpty = false;
        let hasMultiple = false;
        const phases = [];
        for (const row of this.phaseTable.data) {
          const investmentPhase = row.investmentPhase.trim();
          if (regForEmpty.test(investmentPhase)) {
            isValid = false;
            hasEmpty = true;
            break;
          } else {
            if (phases.includes(investmentPhase)) {
              isValid = false;
              hasMultiple = true;
              break;
            } else {
              phases.push(investmentPhase);
            }
          }
        }
        const hasTotalFrom = this.phaseTable.data.some(item => item.totalInvestmentFrom === true);
        if (!isValid && showMsg) {
          if (hasEmpty) {
            this.$message({
              message: "工程总投资 -【投资阶段】列不能有空值",
              type: "warning",
            });
          }
          if (hasMultiple) {
            this.$message({
              message: "工程总投资 -【投资阶段】列不能有重复值",
              type: "warning",
            });
          }
          if(!hasTotalFrom) {
            this.$message({
              message: "工程总投资 - 【总投资取值】列必须选择1项",
              type: "warning",
            });
          }
        }
        return isValid;
      }
    },

    setYearPlanTableRelatedData(res) {
      if (!res || !res.ProjectYearPlans || !res.ProjectYearPlans.length) {
        this.resetYearPlanTableRelatedData();
      } else {
        // 还原年度计划表格列数据
        const columnsFromData = res.ProjectYearPlans[0].Datas.filter(
          (item) => item.PropValue !== "others"
        ).map((item) => {
          return {
            prop: item.PropValue,
            label: item.CategoryName,
            width: "",
          };
        });
        this.yearPlanTable.columns =
          FixedColumns1.concat(columnsFromData).concat(FixedColumns2);

        if (this.yearPlanTable.columns.length <= 4) {
          const lastOne = this.yearPlanTable.columns.slice(-1)[0];
          lastOne.width = "";
        }

        // 还原年度计划表格数据
        const allYears = [];
        this.yearPlanTable.data = res.ProjectYearPlans.map((item) => {
          allYears.push(Number(item.Year));
          return {
            year: new Date(`${item.Year}`),
            yearPlan: item.YearPlanMoney,
            moneyRequires: item.FundingMoney,
            ...item.Datas.reduce((acc, next) => {
              acc[next.PropValue] = next.Money;
              return acc;
            }, {}),
          };
        });
        if (allYears.length) {
          this.yearPlanTable.nextYear = Math.max.apply(null, allYears) + 1;
        }
      }
    },
    resetYearPlanTableRelatedData() {
      this.yearPlanTable.columns = [
        {
          prop: "year",
          label: "年度",
          width: "",
        },
        {
          prop: "yearPlan",
          label: "年度计划",
          width: "",
        },
        {
          prop: "moneyRequires",
          label: "资金需求",
          width: "",
        },
        {
          prop: "others",
          label: "其他",
          width: "",
        },
      ];
      this.yearPlanTable.data = [];
      this.yearPlanTable.nextYear = new Date().getFullYear();
    },
    checkYearPlanRowData(row, showMsg = true) {
      let isRowValid = false;
      if (row) {
        let currentKey = "";
        const keys = Object.keys(row);
        isRowValid = keys.every((key) => {
          currentKey = key;
          return (
            row[key] !== null &&
            row[key] !== undefined &&
            !regForEmpty.test(row[key])
          );
        });
        if (!isRowValid && showMsg) {
          let columnLabel = "";
          const column = this.yearPlanTable.columns.find(
            (item) => item.prop === currentKey
          );
          if (column) {
            columnLabel = column.label;
          }
          this.$message({
            message: `年度计划${
              columnLabel ? " -【" + columnLabel + "】列" : ""
            }不能包含空值`,
            type: "warning",
          });
        }
      }
      return isRowValid;
    },
    checkYearPlanTableData(showMsg = true) {
      let isColumnsValid = true;
      let hasEmpty = false;
      let hasMultiple = false;
      const columnLabels = [];
      for (const column of this.yearPlanTable.columns) {
        const columnLabel = column.label;
        if (regForEmpty.test(columnLabel)) {
          isColumnsValid = false;
          hasEmpty = true;
          break;
        } else {
          if (columnLabels.includes(columnLabel)) {
            isColumnsValid = false;
            hasMultiple = true;
            break;
          } else {
            columnLabels.push(columnLabel);
          }
        }
      }
      if (!isColumnsValid && showMsg) {
        if (hasEmpty) {
          this.$message({
            message: "年度计划表头不能包含空值",
            type: "warning",
          });
        }
        if (hasMultiple) {
          this.$message({
            message: "年度计划表头不能包含重复值",
            type: "warning",
          });
        }
      }

      let isDataValid = true;
      hasMultiple = false;
      const rowYears = [];
      for (const row of this.yearPlanTable.data) {
        const isRowDataValid = this.checkYearPlanRowData(row, showMsg);
        if (!isRowDataValid) {
          isDataValid = false;
          break;
        } else {
          if (rowYears.includes(row.year.getFullYear())) {
            isDataValid = false;
            hasMultiple = true;
            break;
          } else {
            rowYears.push(row.year.getFullYear());
          }
        }
      }
      if (!isDataValid && showMsg) {
        if (hasMultiple) {
          this.$message({
            message: `年度计划-【年度】列不能包含重复值`,
            type: "warning",
          });
        }
      }
      return isColumnsValid && isDataValid;
    },

    setMonthPlanTableRelatedData(res) {
      if (!res || !res.ProjectMonthPlans || !res.ProjectMonthPlans.length) {
        this.resetMonthPlanTableRelatedData();
      } else {
        this.monthPlanTable.data = res.ProjectMonthPlans.map((item) => {
          return {
            year: new Date(`${item.Year}`),
            ...item.Datas.reduce((acc, next) => {
              acc[next.Month] = next.Money;
              return acc;
            }, {}),
          };
        });
      }
    },
    resetMonthPlanTableRelatedData() {
      this.monthPlanTable.data = [];
    },
    checkMonthPlanRowData(row, showMsg = true) {
      let isRowValid = false;
      if (row) {
        let currentKey = "";
        const keys = Object.keys(row);
        isRowValid = keys.every((key) => {
          currentKey = key;
          return (
            row[key] !== null &&
            row[key] !== undefined &&
            !regForEmpty.test(row[key])
          );
        });
        if (!isRowValid && showMsg) {
          let columnLabel = "";
          const column = this.monthPlanTable.columns.find(
            (item) => item.prop === currentKey
          );
          if (column) {
            columnLabel = column.label;
          }
          this.$message({
            message: `月度计划${
              columnLabel ? " -【" + columnLabel + "】列" : ""
            }不能包含空值`,
            type: "warning",
          });
        }
      }
      return isRowValid;
    },
    checkMonthPlanTableData(showMsg = true) {
      let isDataValid = true;
      let hasMultiple = false;
      const rowYears = [];
      for (const row of this.monthPlanTable.data) {
        const isRowDataValid = this.checkMonthPlanRowData(row, showMsg);
        if (!isRowDataValid) {
          isDataValid = false;
          break;
        } else {
          if (rowYears.includes(row.year.getFullYear())) {
            isDataValid = false;
            hasMultiple = true;
            break;
          } else {
            rowYears.push(row.year.getFullYear());
          }
        }
      }
      if (!isDataValid && showMsg) {
        if (hasMultiple) {
          this.$message({
            message: `月度计划-【年度】列不能包含重复值`,
            type: "warning",
          });
        }
      }
      return isDataValid;
    },

    setContractTableRelatedData(res) {
      if (!res || !res.ProjectContracts || !res.ProjectContracts.length) {
        this.resetContractTableRelatedData();
      } else {
        this.contractTable.data = res.ProjectContracts.map((item) => {
          return {
            contractName: item.ContractName,
            contractType: item.ContractType,
            contractAmount: item.Money,
            partnerUnit: item.ContractCompany,
            contractContent: item.ContractComment,
          };
        });
      }
    },
    resetContractTableRelatedData() {
      this.contractTable.data = [];
    },
    checkContractTableData(showMsg = true) {
      const dataLen = this.contractTable.data.length;
      if (dataLen < 1) {
        return true;
      } else {
        let isValid = true;
        let hasEmpty = false;
        let hasMultiple = false;
        const phases = [];
        for (const row of this.contractTable.data) {
          const contractName = row.contractName.trim();
          if (regForEmpty.test(contractName)) {
            isValid = false;
            hasEmpty = true;
            break;
          } else {
            if (phases.includes(contractName)) {
              isValid = false;
              hasMultiple = true;
              break;
            } else {
              phases.push(contractName);
            }
          }
        }
        if (!isValid && showMsg) {
          if (hasEmpty) {
            this.$message({
              message: "合同信息 -【合同名称】列不能有空值",
              type: "warning",
            });
          }
          if (hasMultiple) {
            this.$message({
              message: "合同信息 -【合同名称】列不能有重复值",
              type: "warning",
            });
          }
        }
        return isValid;
      }
    },

    async doSaveBusiness() {
      const checkResults = [];
      checkResults.push(
        this.checkPhaseTableData(),
        this.checkYearPlanTableData(),
        this.checkMonthPlanTableData(),
        this.checkContractTableData()
      );
      if (checkResults.every((item) => item === true)) {
        this.isUpdating = true;
        await this.UpdateProjectDetail();
        this.isUpdating = false;
      }
    },
    getYearPlanRowDatas(row, columns) {
      const propsToSkip = ["Id", "year", "yearPlan", "moneyRequires"];
      return Object.keys(row)
        .filter((key) => !propsToSkip.includes(key))
        .reduce((acc, prop) => {
          acc.push({
            PropValue: prop,
            CategoryName: columns.find((item) => item.prop === prop).label,
            Money: Number(row[prop]),
          });
          return acc;
        }, []);
    },
    getMonthPlanRowDatas(row, columns) {
      const propsToSkip = ["Id", "year"];
      return Object.keys(row)
        .filter((key) => !propsToSkip.includes(key))
        .reduce((acc, prop) => {
          acc.push({
            Month: Number(prop),
            MonthName: columns.find((item) => item.prop === prop).label,
            Money: Number(row[prop]),
          });
          return acc;
        }, []);
    },
    async UpdateProjectDetail() {
      const params = {
        ProjectPhases: this.phaseTable.data.map((item) => {
          return {
            PhaseName: item.investmentPhase,
            Money: Number(item.investmentAmount),
            IsTotal: !!item.totalInvestmentFrom,
          };
        }),
        ProjectYearPlans: this.yearPlanTable.data.map((item) => {
          return {
            Year: item.year.getFullYear(),
            YearName: item.year.getFullYear() + "年",
            YearPlanMoney: Number(item.yearPlan),
            FundingMoney: Number(item.moneyRequires),
            Datas: this.getYearPlanRowDatas(item, this.yearPlanTable.columns),
          };
        }),
        ProjectMonthPlans: this.monthPlanTable.data.map((item) => {
          return {
            Year: item.year.getFullYear(),
            Datas: this.getMonthPlanRowDatas(item, this.monthPlanTable.columns),
          };
        }),
        ProjectContracts: this.contractTable.data.map((item) => {
          return {
            ContractName: item.contractName,
            ContractType: item.contractType,
            Money: Number(item.contractAmount),
            ContractCompany: item.partnerUnit,
            ContractComment: item.contractContent,
          };
        }),
        ProjectId: this.projectId,
      };
      const res = await this.$api.UpdateProjectDetail(params).catch(() => {});
      if (res && res.Ret === 1) {
        this.$message({
          message: "保存成功",
          type: "success",
        });
        this.isLocalEditMode = false;
        this.doBusiness();
      }
    },
    syncModelValue(row, prop) {
      const cv = row[prop] + "";
      if (regForEmpty.test(cv)) {
        row[prop] = "";
      } else {
        row[prop] = this.getFormatValue(row[prop]);
      }
    },
    getFormatValue(v, num = 4) {
      const vT = typeof v;
      if (vT === "string" || vT === "number") {
        const vc = v + "";
        const dotIdx = vc.lastIndexOf(".");
        if (dotIdx !== -1) {
          const numPart = vc.substring(0, dotIdx);
          const dotPart = vc.substring(dotIdx, dotIdx + (num + 1));
          return numPart + dotPart;
        } else {
          if (regForAllZero.test(vc)) {
            return "0";
          } else if (regForZeroStart.test(vc)) {
            return vc.replace(regForZeroStart, "");
          } else {
            return v;
          }
        }
      } else {
        return v;
      }
    },

    calcMoneyRequiresAndFormatProp(row) {
      const propsToSkip = ["Id", "year", "yearPlan", "moneyRequires"];
      row.moneyRequires = Object.keys(row).reduce((acc, next) => {
        let v = 0;
        if (propsToSkip.includes(next)) {
          v = 0;
          if (next === "yearPlan") {
            const yV = row[next];
            if (regForEmpty.test(yV)) {
              row[next] = "";
            } else {
              row[next] = this.getFormatValue(yV);
            }
          }
        } else {
          v = row[next];
          if (regForEmpty.test(v)) {
            return acc + 0;
          }
          row[next] = this.getFormatValue(v);
        }
        return acc + Number(v);
      }, 0);
      row.moneyRequires = this.getFormatValue(row.moneyRequires);
      return row.moneyRequires;
    },
    onTableColumnLabelChange(v, columns, prop) {
      if (columns && prop) {
        const targetColumn = columns.find((item) => item.prop === prop);
        if (targetColumn) {
          targetColumn.label = v;
        }
      }
    },
    quitLocalEditMode() {
      this.$confirm("退出后,所做编辑不被保存,是否退出?", "退出编辑", {
        confirmButtonText: "确定退出",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.isLocalEditMode = false;
          this.doBusiness();
        })
        .catch(() => {});
    },
    addTableColumn(source, columns, tableType) {
      switch (tableType) {
        case "yearPlanTable": {
          const targetColumnIdx = columns.findIndex(
            (item) => item.prop === "others"
          );
          const newProp = `prop-${window.tool.NewGuid()}`;
          columns.splice(targetColumnIdx, 0, {
            prop: newProp,
            label: `新列${window.tool.S4()}`,
            width: "",
          });
          source.forEach((item) => {
            this.$set(item, newProp, 0);
          });
          break;
        }
      }
    },
    showRemoveColumnModal(columns) {
      this.modalColumns = columns.filter(
        (item) => !this.getTableColumnDisabled(item.prop)
      );
      this.isModalVisible = true;
    },
    removeYearPlanColumn(column) {
      if (column) {
        this.$confirm(
          "删除资金需求分类后将重新计算资金需求,是否确定删除?",
          "删除资金需求分类",
          {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
          }
        )
          .then(() => {
            this.modalColumns = this.modalColumns.filter(
              (item) => item !== column
            );
            const { calcMoneyRequiresAndFormatProp } = this;
            const newColumns = FixedColumns1.concat(this.modalColumns).concat(
              FixedColumns2
            );
            this.yearPlanTable.columns = newColumns;
            this.yearPlanTable.data = this.yearPlanTable.data.map((item) => {
              const newObj = newColumns.reduce((acc, column) => {
                acc[column.prop] = item[column.prop];
                return acc;
              }, {});
              newObj.moneyRequires = calcMoneyRequiresAndFormatProp(newObj);
              return newObj;
            });
            this.$message({
              message: "删除资金需求分类成功",
              type: "success",
            });
            if (!this.modalColumns.length) {
              this.isModalVisible = false;
              const lastOne = this.yearPlanTable.columns.slice(-1)[0];
              lastOne.width = "";
            }
          })
          .catch(() => {});
      }
    },
    addTableRow(source, tableType) {
      let newRow;
      switch (tableType) {
        case "phaseTable": {
          newRow = this.phaseTable.columns
            .map((item) => item.prop)
            .reduce((acc, next) => {
              acc[next] = "";
              return acc;
            }, {});
          newRow.totalInvestmentFrom = false;
          newRow.investmentPhase = "";
          newRow.investmentAmount = 0;
          if(!source.some(item => item.totalInvestmentFrom === true)) {
            newRow.totalInvestmentFrom = true;
          }
          break;
        }
        case "yearPlanTable": {
          newRow = this.yearPlanTable.columns
            .map((item) => item.prop)
            .reduce((acc, next) => {
              acc[next] = 0;
              return acc;
            }, {});
          newRow.year = new Date(`${this.yearPlanTable.nextYear++}`);
          this.$nextTick(() => {
            this.addTableRow(this.monthPlanTable.data, "monthPlanTable");
          });
          break;
        }
        case "monthPlanTable": {
          newRow = this.monthPlanTable.columns
            .map((item) => item.prop)
            .reduce((acc, next) => {
              acc[next] = 0;
              return acc;
            }, {});
          newRow.year = new Date(`${this.yearPlanTable.nextYear - 1}`);
          break;
        }
        case "contractTable": {
          newRow = this.contractTable.columns
            .map((item) => item.prop)
            .reduce((acc, next) => {
              acc[next] = "";
              return acc;
            }, {});
          newRow.contractType = "施工合同";
          newRow.contractAmount = 0;
          break;
        }
        default:
          break;
      }
      source.push(newRow);
    },
    removeTableRow(source, target, tableType) {
      switch (tableType) {
        case "phaseTable": {
          this.$confirm(
            "删除后相关数据将被清空,是否确定删除?",
            "删除投资阶段",
            {
              confirmButtonText: "确定",
              cancelButtonText: "取消",
              type: "warning",
            }
          )
            .then(() => {
              if (source && target) {
                const idx = source.findIndex((item) => item === target);
                if (idx !== -1) {
                  source.splice(idx, 1);
                  this.$message({
                    type: "success",
                    message: "删除投资阶段成功!",
                  });
                }
              }
            })
            .catch(() => {});

          break;
        }
        case "yearPlanTable": {
          this.$confirm(
            "删除后相关数据将被清空,是否确定删除?",
            "删除投资年度计划",
            {
              confirmButtonText: "确定",
              cancelButtonText: "取消",
              type: "warning",
            }
          )
            .then(() => {
              if (source && target) {
                const idx = source.findIndex((item) => item === target);
                if (idx !== -1) {
                  source.splice(idx, 1);
                  this.$message({
                    type: "success",
                    message: "删除投资年度计划成功!",
                  });
                  this.$nextTick(() => {
                    const monthTarget = this.monthPlanTable.data.find(
                      (item) =>
                        item.year.getFullYear() === target.year.getFullYear()
                    );
                    this.removeTableRow(
                      this.monthPlanTable.data,
                      monthTarget,
                      "monthPlanTable"
                    );
                  });
                }
              }
            })
            .catch(() => {});
          break;
        }
        case "monthPlanTable": {
          const idx = source.findIndex((item) => item === target);
          if (idx !== -1) {
            source.splice(idx, 1);
          }
          break;
        }
        case "contractTable": {
          this.$confirm("删除后相关数据将被清空,是否确定删除?", "删除合同", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
          })
            .then(() => {
              if (source && target) {
                const idx = source.findIndex((item) => item === target);
                if (idx !== -1) {
                  source.splice(idx, 1);
                  this.$message({
                    type: "success",
                    message: "删除合同成功!",
                  });
                }
              }
            })
            .catch(() => {});
          break;
        }
        default:
          break;
      }
    },
  },
};
</script>

<style>
body {
  overflow: hidden;
}
</style>

<style lang="scss" scoped>
.tz-page-plan-edit-wrapper {
  display: flex;
  width: 100%;
  height: 100%;
  overflow-x: hidden;
  overflow-y: auto;
  flex-direction: column;
  justify-content: flex-start;
  align-items: stretch;
  color: #000;
  background-color: #f0f2f5;
  * {
    box-sizing: border-box;
    user-select: none;
  }
  .header {
    flex: 0 0 auto;
    display: flex;
    width: 100%;
    height: 48px;
    padding: 12px 0;
    justify-content: flex-start;
    align-items: center;
    /deep/ .el-button {
      border: none;
    }
    .item {
      margin-right: 24px;
      color: #081a33;
      font-size: 14px;
      &:last-of-type {
        margin-right: 0;
      }
      &.label {
        color: #222222;
        font-size: 16px;
      }
      &.changePriviewType {
        display: flex;
        border: 1px solid rgba(0, 122, 255, 1);
        padding: 2px 8px;
        justify-content: center;
        align-items: center;
        cursor: pointer;
        .changeTypeIcon {
          width: 16px;
          height: 16px;
        }
        .changeTypeLabel {
          margin-left: 8px;
          font-size: 14px;
        }
      }
      /deep/ .el-date-editor {
        .el-input__inner {
          border: 1px solid #d8d8d8;
          line-height: 30px;
          background-color: #fff;
          &:focus,
          &:focus-within,
          &:focus-visible {
            border-color: #007aff;
          }
        }
      }
    }
    .rest {
      flex: 1 1 auto;
      display: flex;
      justify-content: flex-end;
      align-items: center;
      .btn {
        margin-right: 8px;
        height: 26px;
        padding: 4px 12px;
        border-radius: 3px;
        color: #007aff;
        border: 1px solid #007aff;
        &:last-of-type {
          margin-right: 0;
        }
        &.save,
        &.edit {
          border: none;
          color: #fff;
          background: #007aff;
        }
      }
    }
    &.page {
      padding: 12px 24px;
      box-shadow: 0px 4px 8px 0px rgba(0, 0, 0, 0.05);
    }
  }

  .main {
    flex: 1 1 auto;
    width: calc(100% + 6px);
    padding-left: 24px;
    padding-right: 24px;
    padding-bottom: 24px;
    overflow-x: hidden;
    overflow-y: auto;
    .row {
      margin-bottom: 24px;
      &:last-of-type {
        margin-bottom: 0;
      }
      .row-content {
        display: flex;
        justify-content: flex-start;
        align-items: space-between;
        align-content: center;
        flex-wrap: nowrap;
        width: 100%;
        /deep/ .el-table {
          .el-table__fixed-header-wrapper,
          .el-table__header-wrapper tr th {
            background-color: rgba(245, 245, 245, 1);
            border-right: 1px solid rgba(0, 0, 0, 0.1);
            .cell {
              padding-left: 20px;
            }
          }
          .el-table__body-wrapper .el-table__row td {
            border-right: 1px solid rgba(0, 0, 0, 0.1);
            border-bottom: 1px solid rgba(0, 0, 0, 0.1);
            .cell {
              padding-left: 20px;
            }
          }

          .el-table__header-wrapper,
          .el-table__body-wrapper {
            .valueWrapper {
              display: flex;
              width: 100%;
              justify-content: flex-start;
              align-items: center;
              input::-webkit-outer-spin-button,
              input::-webkit-inner-spin-button {
                -webkit-appearance: none !important;
                margin: 0;
              }
              .inputCus,
              .selectCus {
                width: calc(100% + 20px);
                margin-left: -10px;
                .el-input__inner {
                  padding-left: 10px;
                  padding-right: 10px;
                  line-height: 32px;
                  border: 1px solid #ddd;
                  background-color: #fff;
                  &:focus,
                  &:focus-within,
                  &:focus-visible {
                    border-color: #007aff;
                  }
                }
              }
              .phaseCurrent {
                margin-left: 14px;
                margin-top: -2px;
                color: orange;
                font-size: 20px;
              }
              .dateCus {
                width: calc(100% + 10px);
                margin-left: -10px;
                height: 34px;
                line-height: 34px;
                .el-input__inner {
                  height: 34px;
                  line-height: 34px;
                  border: 1px solid #ddd;
                  background-color: #fff;
                  &:focus,
                  &:focus-within,
                  &:focus-visible {
                    border-color: #007aff;
                  }
                }
              }
              .textareaCus {
                margin-top: 8px;
                margin-bottom: 8px;
              }
              &.isHeader {
                width: calc(100% + 40px);
                margin-left: -20px;
              }
              .singleText {
                text-overflow: ellipsis;
                overflow: hidden;
                word-break: break-all;
                white-space: nowrap;
              }
            }
            .el-table__empty-block {
              min-height: 51px;
              height: 51px;
              .el-table__empty-text {
                color: #000;
              }
            }
          }

          .operation {
            .btn {
              border: none;
              background-color: transparent;
              &:not(:last-of-type) {
                margin-right: 8px;
              }
              &.danger:not(.is-disabled) {
                color: #f00;
              }
            }
          }
        }
      }
    }
  }

  .modal-yearPlanColumns {
    /deep/ .dialogCus {
      .el-dialog__header,
      .el-dialog__body,
      .el-dialog__footer {
        padding: 0;
      }
      .el-dialog__header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        text-align: left;
        padding: 12px 24px 10px 24px;
        border-bottom: 1px solid rgba(232, 232, 232, 1);
        border-top-left-radius: 4px;
        border-top-right-radius: 4px;
        .el-dialog__title {
          font-size: 18px;
          color: #222;
        }
        .el-dialog__headerbtn {
          position: static;
          .el-dialog__close {
            font-size: 20px;
            color: #222;
          }
        }
      }
      .el-dialog__body {
        border-bottom-left-radius: 4px;
        border-bottom-right-radius: 4px;
        .contentWrapper {
          overflow-x: hidden;
          overflow-y: auto;
          padding: 16px 0;
          // min-height: 176px;
          max-height: 512px;
          .column {
            display: flex;
            padding: 12px 24px;
            height: 48px;
            justify-content: space-between;
            align-items: center;
            &:hover {
              background-color: #f5f7fa;
              .remove {
                color: #f00;
              }
            }
            .columnName {
              display: flex;
              justify-content: flex-start;
              align-items: center;
              .label {
                margin-left: 10px;
                font-size: 14px;
                color: #222;
              }
            }
            .btnRemove {
              display: none;
              padding: 4px 10px;
              border-radius: 4px;
              border: 1px solid #007aff;
              color: #007aff;
              background-color: #fff;
            }
            &:hover {
              background-color: rgba(0, 122, 255, 0.05);
              .btnRemove {
                display: block;
              }
            }
          }
        }
      }
    }
  }
}
</style>
