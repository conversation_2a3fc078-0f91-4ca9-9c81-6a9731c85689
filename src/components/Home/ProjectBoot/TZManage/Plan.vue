<template>
  <div class="tz-page-wrapper">
    <div class="page-header">
      <div class="title">投资计划</div>
    </div>
    <div class="page-main">
      <template v-if="isChartPreview">
        <PlanOverview
          @changeEditMode="onIsEditModeChange"
          @changePreviewType="onPreviewTypeChange"
        ></PlanOverview>
      </template>
      <template v-else>
        <PlanSchedule
          @changePreviewType="onPreviewTypeChange"
          :isEditMode="isEditMode"
        ></PlanSchedule>
      </template>
    </div>
  </div>
</template>

<script>
import PlanSchedule from "./PlanSchedule";
import PlanOverview from "./PlanOverview";
export default {
  name: "Plan",
  components: { PlanSchedule, PlanOverview },
  data() {
    return {
      isEditMode: false,
      previewType: "chart",
    };
  },
  computed: {
    isChartPreview() {
      return this.previewType === "chart";
    },
  },
  methods: {
    onIsEditModeChange(data) {
      this.isEditMode = data;
    },
    onPreviewTypeChange(data) {
      this.previewType = data;
    },
  },
};
</script>

<style lang="scss" scoped>
.tz-page-wrapper {
  width: 100%;
  height: 100%;
  color: #000;
  background-color: #f0f2f5;
  * {
    box-sizing: border-box;
    user-select: none;
  }
  .page-header {
    display: flex;
    width: 100%;
    height: 54px;
    background-color: #fff;
    border-bottom: 1px solid hsla(0, 0%, 100%, 0.2);
    justify-content: flex-start;
    align-items: center;
    .title {
      margin-left: 24px;
      font-size: 16px;
      font-weight: 700;
    }
  }
  .page-main {
    width: 100%;
    height: calc(100% - 54px);
    overflow-x: hidden;
    overflow-y: auto;
  }
}
</style>
