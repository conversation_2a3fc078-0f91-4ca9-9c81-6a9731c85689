<template>
  <div class="tz-page-fill-wrapper">
    <div class="page-header">
      <div class="title">投资填报</div>
    </div>
    <div class="page-main">
      <div class="rows">
        <div class="row">
          <div class="header">
            <div class="item label">月度完成投资一览</div>
            <div class="item">单位：&nbsp;万元</div>
            <div class="item">
              <el-date-picker
                v-model="monthDate"
                type="month"
                placeholder="选择月"
                @change="doBusiness"
              >
              </el-date-picker>
            </div>
          </div>
          <div class="overviewItemsWrapper">
            <div
              v-for="(items, idx) in overviewItems"
              :key="idx"
              class="overviewItems"
            >
              <div v-for="item in items" :key="item.id" class="overviewItem">
                <div class="label">{{ item.label }}:</div>
                <div class="value">
                  {{ item.value }}
                  <span
                    v-if="
                      item.id === 'totalInvestmentCompletePercent' ||
                      item.id === 'yearInvestmentCompletePercent'
                    "
                    >%</span
                  >
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="row">
          <div class="header">
            <div class="item label">月度完成投资明细</div>
            <div class="item">单位：&nbsp;万元</div>
            <div class="rest" v-if="hasAuth('TZTB_EDIT')">
              <el-button
                v-if="!details.isEditMode"
                class="btn primary"
                @click.stop="details.isEditMode = true"
                >编辑</el-button
              >
              <template v-else>
                <el-button
                  class="btn"
                  :disabled="details.isSaving"
                  @click.stop="quitDetailsEditMode"
                >
                  取消
                </el-button>
                <el-button
                  class="btn primary"
                  @click.stop="doSaveMonthDetailsBusiness"
                  :disabled="details.isSaving"
                  :loading="details.isSaving"
                  >保存</el-button
                >
              </template>
            </div>
          </div>
          <div class="row-content">
            <el-table
              :data="details.tableData"
              :border="true"
              :max-height="details.maxHeight"
              :key="details.tableKey"
            >
              <el-table-column
                type="index"
                width="70"
                label="序号"
                :resizable="false"
              >
              </el-table-column>
              <el-table-column
                v-for="item in details.tableColumns"
                :key="item.prop"
                :property="item.prop"
                :label="item.label"
                :width="item.width"
                :resizable="item.resizable ? true : (item.width ? false : true)"
              >
                <template slot-scope="scope">
                  <div class="valueWrapper">
                    <el-input
                      v-if="
                        details.isEditMode === true &&
                        (item.prop === 'currentMonthCompleteInvestment' ||
                          item.prop === 'currentMonthCompletePay')
                      "
                      class="inputCus"
                      v-model="scope.row[item.prop]"
                      placeholder="请输入"
                      clearable
                      type="number"
                      @input="syncModelValue(scope.row, item.prop)"
                      @change="syncModelValue(scope.row, item.prop)"
                    ></el-input>
                    <template v-else>
                      <el-tooltip
                        v-if="
                          item.prop === 'contractPayContent' ||
                          item.prop === 'contractName' ||
                          item.prop === 'partnerUnit'
                        "
                        placement="top"
                        effect="dark"
                      >
                        <div
                          slot="content"
                          style="max-width: 500px; line-height: 20px"
                        >
                          {{ scope.row[item.prop] }}
                        </div>
                        <div class="singleText">
                          {{ scope.row[item.prop] }}
                        </div>
                      </el-tooltip>
                      <div v-else class="">
                        {{ scope.row[item.prop] }}
                      </div>
                    </template>
                  </div>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
        <div class="row">
          <div class="header">
            <div class="item label">月度形象进度</div>
            <div class="rest" v-if="hasAuth('TZTB_EDIT')">
              <el-button class="btn primary" @click.stop="addProgressTableRow">
                新增进度填报
              </el-button>
            </div>
          </div>
          <div
            class="row-content progress"
            @mousedown="onProgressTableDown"
            @mouseup="onProgressTableUp"
            @mousemove="onProgressTableMove"
          >
            <el-table :data="progress.tableData" :border="true">
              <el-table-column
                type="index"
                width="70"
                label="序号"
                :resizable="false"
              >
              </el-table-column>
              <el-table-column
                v-for="item in progress.tableColumns"
                :key="item.prop"
                :property="item.prop"
                :label="item.label"
                :width="item.width"
                :resizable="
                  item.resizable ? item.resizable : item.width ? false : true
                "
              >
                <template slot-scope="scope">
                  <div class="valueWrapper">
                    <template v-if="scope.row.isNewAdd || scope.row.isEditMode">
                      <el-date-picker
                        class="dateCus"
                        v-if="item.prop === 'fillDate'"
                        type="date"
                        v-model="scope.row.fillDate"
                      ></el-date-picker>
                      <el-input
                        v-else
                        ref="refTextarea"
                        class="inputCus textareaCus"
                        v-model="scope.row[item.prop]"
                        placeholder="请输入"
                        type="textarea"
                        :maxlength="500"
                        :show-word-limit="true"
                        :autosize="{ minRows: 3, maxRows: 30 }"
                      ></el-input>
                    </template>
                    <template v-else>
                      <el-tooltip
                        v-if="item.prop !== 'fillDate'"
                        effect="dark"
                        placement="top"
                      >
                        <div
                          slot="content"
                          style="max-width: 600px; line-height: 20px"
                        >
                          {{ scope.row[item.prop] }}
                        </div>
                        <div class="singleText">{{ scope.row[item.prop] }}</div>
                      </el-tooltip>
                      <el-date-picker
                        v-else
                        class="dateCus"
                        type="date"
                        :readonly="true"
                        v-model="scope.row.fillDate"
                      ></el-date-picker>
                    </template>
                  </div>
                </template>
              </el-table-column>
              <el-table-column width="100" label="操作" :resizable="false">
                <template slot-scope="scope">
                  <div class="operation">
                    <el-button
                      v-if="hasAuth('TZTB_DELETE')"
                      class="btn danger"
                      :disabled="scope.row.isLoading"
                      @click.stop="removeProgressTableRow(scope.row)"
                    >
                      删除
                    </el-button>
                    <el-button
                      v-if="
                        hasAuth('TZTB_EDIT') &&
                        !scope.row.isNewAdd &&
                        !scope.row.isEditMode
                      "
                      :disabled="scope.row.isLoading"
                      class="btn primary"
                      @click.stop="editProgressTableRow(scope.row)"
                    >
                      编辑
                    </el-button>
                    <el-button
                      v-if="hasAuth('TZTB_EDIT') && scope.row.isEditMode"
                      :disabled="scope.row.isLoading"
                      :loading="scope.row.isLoading"
                      class="btn primary"
                      @click.stop="doSaveProgressRow(scope.row)"
                    >
                      {{ scope.row.isNewAdd ? "保存" : "更新" }}
                    </el-button>
                    <el-button
                      v-if="
                        hasAuth('TZTB_EDIT') &&
                        !scope.row.isNewAdd &&
                        scope.row.isEditMode
                      "
                      :disabled="scope.row.isLoading"
                      class="btn primary"
                      @click.stop="quitProgressRowEditMode(scope.row)"
                    >
                      退出编辑
                    </el-button>
                  </div>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
const regForEmpty = /^\s*$/i;
const regForAllZero = /^\s*0+$/i;
const regForZeroStart = /^\s*0+/i;
import { getToken } from "@/utils/token";
// import { throttle } from "throttle-debounce";
export default {
  name: "TZFill",
  data() {
    return {
      progressTableDownTarget: null,
      isSaving: false,
      projectId: "",
      token: "",
      // 合同类型列表
      contractTypeList: [
        {
          id: "construction",
          label: "施工合同",
        },
        {
          id: "consulting",
          label: "咨询合同",
        },
        {
          id: "land-requisition",
          label: "征地拆迁",
        },
        {
          id: "others",
          label: "其他",
        },
      ],
      monthDate: new Date(),
      overviewItems: [
        [
          {
            id: "totalInvestment",
            label: "总投资",
            value: "",
          },
          {
            id: "yearPlan",
            label: "年度计划",
            value: "",
          },
          {
            id: "monthCompleteInvestment",
            label: "本月完成投资",
            value: "",
          },
          {
            id: "zdcq",
            label: "征地拆迁",
            value: "",
          },
        ],
        [
          {
            id: "totalCompleteInvestment",
            label: "总完成投资",
            value: "",
          },
          {
            id: "yearCompleteInvestment",
            label: "年完成投资",
            value: "",
          },
          {
            id: "monthCompleteYield",
            label: "本月完成产值",
            value: "",
          },
        ],
        [
          {
            id: "totalCompleteYield",
            label: "总完成产值",
            value: "",
          },
          {
            id: "yearCompleteYield",
            label: "年完成产值",
            value: "",
          },
        ],
        [
          {
            id: "totalInvestmentCompletePercent",
            label: "总投资完成占比",
            value: "",
          },
          {
            id: "yearInvestmentCompletePercent",
            label: "年投资完成占比",
            value: "",
          },
        ],
      ],
      details: {
        tableKey: "1",
        isEditMode: false,
        maxHeight: 565,
        isSaving: false,
        tableColumns: [
          {
            prop: "contractName",
            label: "合同名称",
            width: "160",
          },
          {
            prop: "contractType",
            label: "合同类型",
            width: "160",
            resizable: true
          },
          {
            prop: "totalCompleteInvestment",
            label: "总完成投资",
            width: "120",
          },
          {
            prop: "totalCompletePay",
            label: "总完成支付",
            width: "120",
          },
          {
            prop: "currentYearCompleteInvestment",
            label: "本年完成投资",
            width: "140",
          },
          {
            prop: "currentYearCompletePay",
            label: "本年完成支付",
            width: "140",
          },
          {
            prop: "currentMonthCompleteInvestment",
            label: "本月完成投资",
            width: "140",
          },
          {
            prop: "currentMonthCompletePay",
            label: "本月完成支付",
            width: "140",
          },
          {
            prop: "partnerUnit",
            label: "乙方单位",
            width: "",
          },
          {
            prop: "contractPayContent",
            label: "合同支付条款",
            width: "",
          },
        ],
        tableData: [],
        tableDataBak: [],
      },
      progress: {
        isEditMode: false,
        tableColumns: [
          {
            prop: "fillDate",
            label: "填报时间",
            width: "160",
          },
          {
            prop: "progressDesc",
            label: "进度工况",
            width: "620",
            resizable: true,
          },
          {
            prop: "progressIssue",
            label: "存在问题",
            width: "",
          },
        ],
        tableData: [],
        tableDataBak: [],
      },
    };
  },
  created() {
    this.projectId = sessionStorage.getItem("organizeId");
    this.token = getToken();
    // this.onProgressTableMove = throttle(17, this.onProgressTableMove, {
    //   noLeading: false,
    //   noTrailing: false,
    // });
  },
  mounted() {
    this.doBusiness();
  },
  methods: {
    quitProgressRowEditMode(target) {
      if (target) {
        const targetBak = this.progress.tableDataBak.find(
          (item) => item.id === target.id
        );
        if (targetBak) {
          target.fillDate = targetBak.fillDate;
          target.progressDesc = targetBak.progressDesc;
          target.progressIssue = targetBak.progressIssue;
        }
        this.$set(target, "isEditMode", false);
      }
    },
    quitDetailsEditMode() {
      this.$confirm("退出后,所做编辑不被保存,是否退出?", "退出编辑", {
        confirmButtonText: "确定退出",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.details.isEditMode = false;
          this.details.tableData = JSON.parse(
            JSON.stringify(this.details.tableDataBak)
          );
        })
        .catch(() => {});
    },
    hasAuth(buttonCode) {
      const auths = JSON.parse(sessionStorage.getItem("menuListHasAuth"));
      if (
        auths &&
        auths.length &&
        auths.some((item) => item.ButtonCode === buttonCode)
      ) {
        return true;
      } else {
        return false;
      }
    },
    async doSaveMonthDetailsBusiness() {
      //todo 数据校验
      this.details.isSaving = true;
      const res = await this.$api
        .UpdateProjectMonthContract({
          ProjectId: this.projectId,
          Year: this.monthDate.getFullYear(),
          Month: this.monthDate.getMonth() + 1,
          MonthContractDetails: this.details.tableData.map((item) => {
            return {
              ContractName: item.contractName,
              CurrentMonthComTZ: Number(item.currentMonthCompleteInvestment),
              CurrentMonthComPay: Number(item.currentMonthCompletePay),
            };
          }),
        })
        .catch(() => {});
      this.details.isSaving = false;
      this.details.isEditMode = false;
      if (res && res.Ret === 1) {
        this.$message({
          message: "保存月度完成投资明细成功",
          type: "success",
        });
        this.doBusiness();
      }
    },
    doBusiness() {
      const year = this.monthDate.getFullYear();
      const month = this.monthDate.getMonth() + 1;
      this.$api
        .GetProjectMonthTzPlan({ projectId: this.projectId, year, month })
        .then((res) => {
          if (res && res.Ret === 1) {
            const data = res.Data;
            this.overviewItems.forEach((items) => {
              items.forEach((item) => {
                switch (item.id) {
                  case "totalInvestment": {
                    item.value = data.TzTotal;
                    break;
                  }
                  case "yearPlan": {
                    item.value = data.YearPlanMoney;
                    break;
                  }
                  case "monthCompleteInvestment": {
                    item.value = data.CurrentMonthCompletedTz;
                    break;
                  }
                  case "zdcq": {
                    item.value = data.ZDCQ;
                    break;
                  }
                  case "totalCompleteInvestment": {
                    item.value = data.TzCompletedTotal;
                    break;
                  }
                  case "yearCompleteInvestment": {
                    item.value = data.YearTzCompletedTotal;
                    break;
                  }
                  case "monthCompleteYield": {
                    item.value = data.CurrentMonthCompleteCreated;
                    break;
                  }
                  case "totalCompleteYield": {
                    item.value = data.TotalCompleteCreated;
                    break;
                  }
                  case "yearCompleteYield": {
                    item.value = data.YearCompleteCreated;
                    break;
                  }
                  case "totalInvestmentCompletePercent": {
                    item.value = data.TzCompletedTotalPercent;
                    break;
                  }
                  case "yearInvestmentCompletePercent": {
                    item.value = data.YearTzCompletedTotalPercent;
                    break;
                  }
                }
              });
            });

            this.details.tableData = data.ProjectMonthPlanContractDetails.map(
              (item) => {
                return {
                  contractName: item.ContractName,
                  contractType: item.ContractType,
                  totalCompleteInvestment: item.TzCompletedTotal,
                  totalCompletePay: item.ZFCompletedTotal,
                  currentYearCompleteInvestment:
                    item.CurrentYearTzCompletedTotal,
                  currentYearCompletePay: item.CurrentYearZFCompletedTotal,
                  currentMonthCompleteInvestment:
                    item.CurrentMonthTzCompletedTotal,
                  currentMonthCompletePay: item.CurrentMonthZFCompletedTotal,
                  partnerUnit: item.ContractCompany,
                  contractPayContent: item.ContractComment,
                };
              }
            );
            this.$nextTick(() => {
              this.details.tableKey = Math.random();
            });

            this.details.tableDataBak = JSON.parse(
              JSON.stringify(this.details.tableData)
            );

            this.progress.tableData = data.ProjectMonthImageDetails.map(
              (item) => {
                return {
                  id: item.Id,
                  fillDate: this.getYMD(new Date(item.Reporttime)),
                  progressDesc: item.ProgressStatus,
                  progressIssue: item.Problem,
                  isLoading: false,
                };
              }
            );
            this.progress.tableDataBak = JSON.parse(
              JSON.stringify(this.progress.tableData)
            );
          }
        })
        .catch(() => {});
    },
    getYMD(date, joinBy = "-") {
      if (date instanceof Date) {
        return [date.getFullYear(), date.getMonth() + 1, date.getDate()].join(
          joinBy
        );
      }
    },
    syncModelValue(row, prop) {
      const cv = row[prop] + "";
      if (regForEmpty.test(cv)) {
        row[prop] = "";
      } else {
        row[prop] = this.getFormatValue(row[prop]);
      }
    },
    getFormatValue(v, num = 4) {
      const vT = typeof v;
      if (vT === "string" || vT === "number") {
        const vc = v + "";
        const dotIdx = vc.lastIndexOf(".");
        if (dotIdx !== -1) {
          const numPart = vc.substring(0, dotIdx);
          const dotPart = vc.substring(dotIdx, dotIdx + (num + 1));
          return numPart + dotPart;
        } else {
          if (regForAllZero.test(vc)) {
            return "0";
          } else if (regForZeroStart.test(vc)) {
            return vc.replace(regForZeroStart, "");
          } else {
            return v;
          }
        }
      } else {
        return v;
      }
    },
    addProgressTableRow() {
      const newRow = this.progress.tableColumns
        .map((item) => item.prop)
        .reduce((acc, next) => {
          acc[next] = "";
          return acc;
        }, {});
      newRow.fillDate = new Date();
      this.$set(newRow, "isLoading", false);
      this.$set(newRow, "isNewAdd", true);
      this.$set(newRow, "isEditMode", true);
      this.progress.tableData.push(newRow);
    },
    removeProgressTableRow(target) {
      if (target.isNewAdd) {
        const idx = this.progress.tableData.findIndex(
          (item) => item === target
        );
        if (idx !== -1) {
          this.progress.tableData.splice(idx, 1);
        }
      } else {
        this.$confirm(
          "删除后相关数据将被清空,是否确定删除?",
          "删除月度形象进度",
          {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
          }
        )
          .then(async () => {
            target.isLoading = true;
            const res = await this.$api
              .DeleteProjectMonthImage({ Id: target.id })
              .catch(() => {});
            target.isLoading = false;
            if (res && res.Ret === 1) {
              this.$message({
                type: "success",
                message: "删除月度形象进度成功!",
              });
              this.doBusiness();
            }
          })
          .catch(() => {});
      }
    },
    editProgressTableRow(target) {
      this.$set(target, "isEditMode", true);
    },
    doSaveProgressRow(target) {
      if (target.isNewAdd) {
        this.saveProgressTableRow(target);
      } else {
        this.updateProgressTableRow(target);
      }
    },
    async saveProgressTableRow(target) {
      if (!this.checkProgressTableData()) return;
      target.isLoading = true;
      const res = await this.$api
        .AddProjectMonthImage({
          ProjectId: this.projectId,
          Year: this.monthDate.getFullYear(),
          Month: this.monthDate.getMonth() + 1,
          Problem: target.progressIssue,
          Reporttime: this.dayjs(target.fillDate).format("YYYY-MM-DD"),
          ProgressStatus: target.progressDesc,
        })
        .catch(() => {});
      target.isLoading = false;
      if (res && res.Ret === 1) {
        this.$message({
          message: "添加月度形象进度成功",
          type: "success",
        });
        this.doBusiness();
      }
    },
    async updateProgressTableRow(target) {
      if (!this.checkProgressTableData()) return;
      target.isLoading = true;
      const res = await this.$api
        .UpdateProjectMonthImage({
          Id: target.id,
          Problem: target.progressIssue,
          Reporttime: this.dayjs(target.fillDate).format("YYYY-MM-DD"),
          ProgressStatus: target.progressDesc,
        })
        .catch(() => {});
      target.isLoading = false;
      if (res && res.Ret === 1) {
        this.$message({
          message: "更新月度形象进度成功",
          type: "success",
        });
        this.$set(target, "isEditMode", false);
        this.$set(target, "isNewAdd", false);
      }
    },
    checkProgressTableData(showMsg = true) {
      let isValid = true;
      for (const row of this.progress.tableData) {
        isValid = this.checkProgressTableRow(row);
        if (!isValid) {
          break;
        }
      }
      const allFillDates = this.progress.tableData.map((item) =>
        this.dayjs(item.fillDate).format("YYYY-MM-DD")
      );
      const len1 = allFillDates.length;
      const len2 = new Set(allFillDates).size;
      if (len1 !== len2) {
        isValid = false;
        showMsg &&
          this.$message({
            message: "填报时间不能重复",
            type: "warning",
          });
      }
      return isValid;
    },
    checkProgressTableRow(target, showMsg = true) {
      let isValid = true;
      const keys = Object.keys(target);
      for (const key of keys) {
        const v = target[key];
        if (v === null || v === undefined || regForEmpty.test(v)) {
          isValid = false;
          break;
        }
      }
      if (!isValid && showMsg) {
        this.$message({
          message: "月度形象进度数据不能包含空值",
          type: "warning",
        });
      }
      return isValid;
    },
    onProgressTableDown(evt) {
      // console.log("onProgressTableDown", evt);
      if (evt) {
        this.progressTableDownTarget = evt.target;
      } else {
        this.progressTableDownTarget = null;
      }
    },
    onProgressTableUp() {
      // console.log("onProgressTableUp");
      requestAnimationFrame(() => {
        this.onProgressTableMove();
        this.progressTableDownTarget = null;
      });
    },
    onProgressTableMove() {
      const { progressTableDownTarget } = this;
      if (
        progressTableDownTarget &&
        progressTableDownTarget.classList.contains("el-textarea__inner")
      ) {
        const refs = this.$refs.refTextarea;
        if (refs && refs.length) {
          const targetIdx = refs.findIndex(
            (item) => item.$refs.textarea === progressTableDownTarget
          );
          console.log("targetIdx", targetIdx);
          if (targetIdx !== -1) {
            const finalHeight = progressTableDownTarget.offsetHeight;
            let siblingIdx = -1;
            if (targetIdx % 2 === 0) {
              siblingIdx = targetIdx + 1;
            } else {
              siblingIdx = targetIdx - 1;
            }
            const siblingTextarea = refs[siblingIdx];
            if (siblingTextarea) {
              this.$nextTick(() => {
                siblingTextarea.$refs.textarea.style.height = `${finalHeight}px`;
              });
            }
          }
        }
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.tz-page-fill-wrapper {
  width: 100%;
  height: 100%;
  color: #000;
  background-color: #f0f2f5;
  * {
    box-sizing: border-box;
    user-select: none;
  }
  .page-header {
    display: flex;
    width: 100%;
    height: 54px;
    background-color: #fff;
    border-bottom: 1px solid hsla(0, 0%, 100%, 0.2);
    justify-content: flex-start;
    align-items: center;
    .title {
      margin-left: 24px;
      font-size: 16px;
      font-weight: 700;
    }
  }
  .header {
    flex: 0 0 auto;
    display: flex;
    width: 100%;
    height: 48px;
    padding: 12px 0;
    justify-content: flex-start;
    align-items: center;
    /deep/ .el-button {
      border: none;
    }
    .item {
      margin-right: 24px;
      color: #081a33;
      font-size: 14px;
      &:last-of-type {
        margin-right: 0;
      }
      &.label {
        color: #222222;
        font-size: 16px;
      }
      &.changePriviewType {
        font-size: 16px;
        cursor: pointer;
      }
      /deep/ .el-date-editor {
        width: 140px;
        .el-input__inner {
          border: 1px solid #d8d8d8;
          line-height: 30px;
          background-color: #fff;
          color: #007aff;
          &:focus,
          &:focus-within,
          &:focus-visible {
            border-color: #007aff;
          }
        }
        .el-input__prefix {
          color: #007aff;
        }
      }
    }
    .rest {
      flex: 1 1 auto;
      display: flex;
      justify-content: flex-end;
      align-items: center;
      .btn {
        margin-right: 8px;
        height: 26px;
        padding: 4px 12px;
        border-radius: 3px;
        color: #007aff;
        border: 1px solid #007aff;
        &:last-of-type {
          margin-right: 0;
        }
        &.primary {
          border: none;
          color: #fff;
          background: #007aff;
        }
      }
    }
  }
  .page-main {
    width: 100%;
    height: calc(100% - 54px);
    padding-bottom: 24px;
    .rows {
      padding: 0 24px;
      height: 100%;
      overflow-x: hidden;
      overflow-y: auto;
      .row {
        &:first-of-type {
          margin-top: 8px;
        }
        margin-bottom: 16px;
        .overviewItemsWrapper {
          padding: 24px;
          background-color: #fff;
          .overviewItems {
            display: flex;
            margin-bottom: 24px;
            justify-content: flex-start;
            align-items: center;
            flex-wrap: wrap;
            &:last-of-type {
              margin-bottom: 0;
            }
            .overviewItem {
              flex: 0 0 auto;
              display: flex;
              width: 25%;
              justify-content: flex-start;
              align-items: center;
              .label {
                width: 120px;
                text-align: left;
              }
            }
          }
        }
        .row-content {
          display: flex;
          justify-content: flex-start;
          align-items: space-between;
          align-content: center;
          flex-wrap: nowrap;
          width: 100%;
          /deep/ .el-table {
            .el-table__header-wrapper tr {
              width: 100%;
            }
            .el-table__header-wrapper tr th {
              background-color: rgba(245, 245, 245, 1);
              border-right: 1px solid rgba(0, 0, 0, 0.1);
              .cell {
                padding-left: 20px;
              }
            }
            .el-table__body-wrapper .el-table__row {
              width: 100%;
              td {
                border-right: 1px solid rgba(0, 0, 0, 0.1);
                border-bottom: 1px solid rgba(0, 0, 0, 0.1);
                .cell {
                  padding-left: 20px;
                }
              }
            }

            .el-table__header-wrapper,
            .el-table__body-wrapper {
              .valueWrapper {
                display: flex;
                width: 100%;
                justify-content: flex-start;
                align-items: center;
                input::-webkit-outer-spin-button,
                input::-webkit-inner-spin-button {
                  -webkit-appearance: none !important;
                  margin: 0;
                }
                .inputCus,
                .selectCus {
                  width: calc(100% + 20px);
                  margin-left: -10px;
                  .el-input__inner {
                    padding-left: 10px;
                    padding-right: 10px;
                    line-height: 32px;
                    border: 1px solid #ddd;
                    background-color: #fff;
                    &:focus,
                    &:focus-within,
                    &:focus-visible {
                      border-color: #007aff;
                    }
                  }
                }
                .dateCus {
                  width: calc(100% + 10px);
                  margin-left: -10px;
                  height: 34px;
                  line-height: 34px;
                  .el-input__inner {
                    height: 34px;
                    line-height: 34px;
                    border: 1px solid #ddd;
                    background-color: #fff;
                    &:focus,
                    &:focus-within,
                    &:focus-visible {
                      border-color: #007aff;
                    }
                  }
                  .el-input__inner[readonly] {
                    &:focus,
                    &:focus-within,
                    &:focus-visible {
                      border-color: #ddd;
                    }
                  }
                  .el-input__prefix {
                    color: #007aff;
                  }
                }
                .textareaCus {
                  margin-top: 15px;
                  margin-bottom: 15px;
                }
                .singleText {
                  text-overflow: ellipsis;
                  overflow: hidden;
                  word-break: break-all;
                  white-space: nowrap;
                }
              }
              .el-table__empty-block {
                min-height: 51px;
                height: 51px;
              }
            }

            .operation {
              display: flex;
              justify-content: flex-start;
              flex-wrap: wrap;
              align-items: center;
              .btn {
                margin-bottom: 8px;
                border: none;
                &:not(:last-of-type) {
                  margin-right: 8px;
                }
                &.danger {
                  color: #f00;
                }
                &.primary {
                  color: #007aff;
                }
              }
            }
          }
        }
      }
    }
  }
}
</style>
