<template>
  <div class="tz-page-plan-preview-wrapper">
    <div class="header page">
      <el-tooltip effect="dark" content="点击切换到列表模式" placement="top">
        <div
          class="item changePriviewType"
          @click.stop="changePreviewToListMode"
        >
          <img
            class="changeTypeIcon"
            src="../../../../assets/images/list.png"
            alt=""
          />
          <div class="changeTypeLabel">切换</div>
        </div>
      </el-tooltip>
      <div v-if="hasAuth('TZJH_EDIT')" class="rest">
        <el-button class="btn edit" @click.stop="enterEditMode">编辑</el-button>
      </div>
    </div>
    <div class="main">
      <div class="row-1">
        <div class="header">
          <div class="item label">工程总投资</div>
          <div class="item">单位：&nbsp;万元</div>
        </div>
        <div class="content">
          <template v-if="investmentPhases.length">
            <div v-for="item in investmentPhases" :key="item.Id" class="item">
              <div class="header">
                <div class="line"></div>
                <div class="phase-label">{{ item.PhaseName }}</div>
                <i
                  v-if="item.IsTotal"
                  class="phase-current el-icon-star-on"
                ></i>
              </div>
              <div class="content">
                <div class="phase-value">
                  <div class="value">{{ item.Money }}</div>
                  <div class="unit">万元</div>
                </div>
                <div class="value-icon">
                  <CommonSVG class="icon" iconClass="touzi"></CommonSVG>
                </div>
              </div>
            </div>
          </template>
          <div v-else class="empty">暂无数据,请添加投资阶段</div>
        </div>
      </div>
      <div class="row-2">
        <div class="row-left">
          <div class="header">
            <div class="item label">年度计划</div>
            <div class="item">单位：&nbsp;万元</div>
            <div class="item">
              <el-date-picker
                type="year"
                v-model="yearPlanDate"
                @change="onYearPlanDateChange"
                :clearable="false"
              ></el-date-picker>
            </div>
          </div>
          <div class="content">
            <template v-if="yearPlanData">
              <div class="chart">
                <v-chart
                  id="yearPlanChart"
                  style="width: 100%; height: 100%"
                  :options="yearPlanChartOptions"
                />
              </div>
              <div class="footer">
                <div class="item label">年度计划</div>
                <div class="item value">{{ yearPlanMoney }}</div>
              </div>
            </template>
            <div v-else class="empty">暂无数据,请添加年计划</div>
          </div>
        </div>
        <div class="row-right">
          <div class="header">
            <div class="item label">月度计划</div>
            <div class="item unit">单位：&nbsp;万元</div>
          </div>
          <div class="content">
            <div v-if="monthData" class="chart">
              <v-chart
                id="yearPlanChart"
                style="width: 100%; height: 100%"
                :options="monthPlanChartOptions"
              />
            </div>
            <div v-else class="empty">暂无数据</div>
          </div>
        </div>
      </div>
      <div class="row-3">
        <div class="header">
          <div class="item label">合同信息</div>
          <div class="item unit">单位：&nbsp;万元</div>
        </div>
        <div class="content">
          <el-table
            :data="contractsTableData"
            :border="true"
            empty-text="暂无数据,请添加合同"
          >
            <el-table-column
              type="index"
              width="70"
              label="序号"
              :resizable="false"
            >
            </el-table-column>
            <el-table-column
              v-for="item in contractsTableColumns"
              :key="item.prop"
              :property="item.prop"
              :label="item.label"
              :width="item.width"
              :resizable="
                item.resizable ? item.resizable : item.width ? false : true
              "
            >
              <template slot-scope="scope">
                <el-tooltip
                  v-if="
                    item.prop === 'contractName' ||
                    item.prop === 'partnerUnit' ||
                    item.prop === 'contractContent'
                  "
                  effect="dark"
                  placement="top"
                >
                  <div
                    slot="content"
                    style="max-width: 500px; line-height: 20px"
                  >
                    {{ scope.row[item.prop] }}
                  </div>
                  <div class="singleText">{{ scope.row[item.prop] }}</div>
                </el-tooltip>
                <div v-else class="singleText">{{ scope.row[item.prop] }}</div>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import CommonSVG from "@/components/Common/CommonSVG";
const ColorList = [
  "#3AFFF4",
  "#3281FF",
  "#FFD500",
  "#FF745A",
  "#51fe00",
  "#0b00ff",
];
export default {
  name: "PlanOverview",
  components: { CommonSVG },
  data() {
    return {
      projectId: "",
      yearPlanDate: new Date(),
      yearPlanData: null,
      yearPlanMoney: "",
      monthData: null,
      investmentPhases: [],
      contractsTableColumns: [
        {
          prop: "contractName",
          label: "合同名称",
          width: "200",
          resizable: true,
        },
        {
          prop: "contractType",
          label: "合同类型",
          width: "200",
        },
        {
          prop: "contractAmount",
          label: "合同金额",
          width: "240",
          resizable: true,
        },
        {
          prop: "partnerUnit",
          label: "乙方单位",
          width: "240",
          resizable: true,
        },
        {
          prop: "contractContent",
          label: "合同支付条款",
          width: "",
          resizable: true,
        },
      ],
      contractsTableData: [],
      yearPlanChartOptions: {
        tooltip: {
          trigger: "item",
          formatter: "{b} : {c} ({d}%)",
        },
        legend: {
          orient: "vertical",
          left: "60%",
          top: "15%",
          data: [],
        },
        series: [
          {
            type: "pie",
            radius: ["50%", "80%"],
            center: ["35%", "50%"],
            data: [],
            label: {
              show: false,
              position: "center",
            },
            labelLine: {
              show: false,
            },
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: "rgba(0, 0, 0, 0.5)",
              },
            },
          },
        ],
      },
      monthPlanChartOptions: {
        backgroundColor: "#fff",
        title: {
          text: "投资金额",
          x: "center",
          y: "4%",
          textStyle: {
            color: "#222222",
            fontSize: 16,
          },
        },
        tooltip: {
          trigger: "item",
        },
        grid: {
          top: "15%",
          right: "3%",
          left: "5%",
          bottom: "12%",
        },
        xAxis: [
          {
            type: "category",
            data: [],
            axisLine: {
              show: false,
            },
            axisLabel: {
              color: "#000",
              textStyle: {
                fontSize: 14,
              },
            },
            axisTick: {
              show: false,
            },
          },
        ],
        yAxis: [
          {
            axisLine: {
              show: false,
            },
            axisLabel: {
              formatter: "{value}",
              color: "#666",
            },
            axisTick: {
              show: false,
            },
            splitLine: {
              lineStyle: {
                color: "#F4F4F4",
              },
            },
          },
        ],
        series: [
          {
            type: "bar",
            data: [],
            barWidth: "24px",
            label: {
              show: true,
              position: "top",
              padding: 4,
              color: "#2979ff",
              fontSize: 14,
              formatter: "{c}",
            },
            itemStyle: {
              color: {
                type: "linear",
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  { offset: 0, color: "#33A9FF" },
                  { offset: 1, color: "#CCEAFF" },
                ],
                global: false,
              },
              shadowColor: "#f0f2f5",
              shadowBlur: 4,
            },
          },
        ],
      },
    };
  },
  created() {
    this.projectId = sessionStorage.getItem("organizeId");
  },
  mounted() {
    this.doBusiness();
  },
  methods: {
    onYearPlanDateChange() {
      this.doBusiness();
    },
    hasAuth(buttonCode) {
      const auths = JSON.parse(sessionStorage.getItem("menuListHasAuth"));
      if (
        auths &&
        auths.length &&
        auths.some((item) => item.ButtonCode === buttonCode)
      ) {
        return true;
      } else {
        return false;
      }
    },
    getCategoryColor(idx) {
      const cL = ColorList.length;
      return ColorList[idx % cL];
    },
    doBusiness() {
      this.$api
        .GetProjectDetail({ projectId: this.projectId })
        .then((res) => {
          if (res && res.Ret === 1) {
            this.setInvestmentPhases(res.Data);
            this.setYearPlanRelatedData(res.Data);
            this.setMonthPlanRelatedData(res.Data);
            this.setContractsTableData(res.Data);
          } else {
            this.resetInvestmentPhases();
            this.resetYearPlanRelatedData();
            this.resetMonthRelatedData();
            this.resetContractsTableData();
          }
        })
        .catch(() => {
          this.resetInvestmentPhases();
          this.resetYearPlanRelatedData();
          this.resetMonthRelatedData();
          this.resetContractsTableData();
        });
    },
    setInvestmentPhases(res) {
      if (!res || !res.ProjectPhases) {
        this.resetInvestmentPhases();
      } else {
        this.investmentPhases = res.ProjectPhases;
      }
    },
    resetInvestmentPhases(res) {
      this.investmentPhases = [];
    },
    setYearPlanRelatedData(res) {
      if (!res || !res.ProjectYearPlans || !res.ProjectYearPlans.length) {
        this.resetYearPlanRelatedData();
      } else {
        const year = this.yearPlanDate.getFullYear();
        const selectedYearData = res.ProjectYearPlans.find(
          (item) => item.Year === year
        );
        if (!selectedYearData) {
          this.resetYearPlanRelatedData();
        } else {
          this.yearPlanData = selectedYearData;
          this.yearPlanMoney = selectedYearData.YearPlanMoney;
          if (selectedYearData.Datas) {
            const legends = [];
            const seriesData = [];
            selectedYearData.Datas.forEach((item, idx) => {
              legends.push(item.CategoryName);
              seriesData.push({
                name: item.CategoryName,
                value: item.Money,
                itemStyle: { color: this.getCategoryColor(idx) },
              });
            });
            this.yearPlanChartOptions.legend.data = legends;
            this.yearPlanChartOptions.series[0].data = seriesData;
          }
        }
      }
    },
    resetYearPlanRelatedData() {
      this.yearPlanData = null;
      this.yearPlanMoney = "";
      this.yearPlanChartOptions.legend.data = [];
      this.yearPlanChartOptions.series[0].data = [];
    },
    setMonthPlanRelatedData(res) {
      if (!res || !res.ProjectMonthPlans || !res.ProjectMonthPlans.length) {
        this.resetMonthRelatedData();
      } else {
        const year = this.yearPlanDate.getFullYear();
        const selectedMonthData = res.ProjectMonthPlans.find(
          (item) => item.Year === year
        );
        if (!selectedMonthData) {
          this.resetMonthRelatedData();
        } else {
          this.monthData = selectedMonthData;
          const xData = [];
          const seriesData = [];
          selectedMonthData.Datas.forEach((item) => {
            xData.push(item.MonthName);
            seriesData.push(item.Money);
          });
          this.monthPlanChartOptions.xAxis[0].data = xData;
          this.monthPlanChartOptions.series[0].data = seriesData;
        }
      }
    },
    resetMonthRelatedData() {
      this.monthData = null;
      this.monthPlanChartOptions.xAxis[0].data = [];
      this.monthPlanChartOptions.series[0].data = [];
    },
    setContractsTableData(res) {
      if (!res || !res.ProjectMonthPlans || !res.ProjectContracts.length) {
        this.resetContractsTableData();
      }
      this.contractsTableData = res.ProjectContracts.map((item) => {
        return {
          contractName: item.ContractName,
          contractType: item.ContractType,
          contractAmount: item.Money,
          partnerUnit: item.ContractCompany,
          contractContent: item.ContractComment,
        };
      });
    },
    resetContractsTableData() {
      this.contractsTableData = [];
    },
    changePreviewToListMode() {
      this.$emit("changeEditMode", false);
      this.$emit("changePreviewType", "list");
    },
    enterEditMode() {
      this.$emit("changeEditMode", true);
      this.$emit("changePreviewType", "list");
    },
  },
};
</script>

<style lang="scss" scoped>
.tz-page-plan-preview-wrapper {
  display: flex;
  width: 100%;
  height: 100%;
  overflow-x: hidden;
  overflow-y: auto;
  flex-direction: column;
  justify-content: flex-start;
  align-items: stretch;
  color: #000;
  background-color: #f0f2f5;
  * {
    box-sizing: border-box;
    user-select: none;
  }
  .singleText {
    text-overflow: ellipsis;
    overflow: hidden;
    word-break: break-all;
    white-space: nowrap;
  }
  .header {
    flex: 0 0 auto;
    display: flex;
    width: 100%;
    height: 48px;
    padding: 12px 0;
    justify-content: flex-start;
    align-items: center;
    /deep/ .el-button {
      border: none;
    }
    .item {
      margin-right: 24px;
      color: #081a33;
      font-size: 14px;
      &:last-of-type {
        margin-right: 0;
      }
      &.label {
        color: #222222;
        font-size: 16px;
      }
      &.changePriviewType {
        display: flex;
        border: 1px solid rgba(0, 122, 255, 1);
        padding: 2px 8px;
        justify-content: center;
        align-items: center;
        cursor: pointer;
        .changeTypeIcon {
          width: 16px;
          height: 16px;
        }
        .changeTypeLabel {
          margin-left: 8px;
          font-size: 14px;
        }
      }
      /deep/ .el-date-editor {
        .el-input__inner {
          padding-left: 10px;
          border: 1px solid #d8d8d8;
          line-height: 30px;
          background-color: #fff;
          color: #007aff;
          &:focus,
          &:focus-within,
          &:focus-visible {
            border-color: #007aff;
          }
        }
        .el-input__prefix {
          right: 0;
          left: auto;
          color: #007aff;
        }
      }
    }
    .rest {
      flex: 1 1 auto;
      display: flex;
      justify-content: flex-end;
      align-items: center;
      .btn {
        margin-left: 8px;
        height: 26px;
        padding: 4px 12px;
        border-radius: 3px;
        &.edit {
          background-color: #007aff;
          color: #fff;
        }
      }
    }
    &.page {
      padding: 12px 24px;
      box-shadow: 0px 4px 8px 0px rgba(0, 0, 0, 0.05);
    }
  }
  .main {
    flex: 1 1 auto;
    width: calc(100% + 6px);
    padding-left: 24px;
    padding-right: 24px;
    padding-bottom: 24px;
    overflow-x: hidden;
    overflow-y: auto;
    .row-1 {
      .content {
        display: flex;
        justify-content: flex-start;
        align-items: space-between;
        align-content: center;
        flex-wrap: wrap;
        width: 100%;
        .item {
          flex: 1 1 calc(25% - 24px);
          padding: 16px 36px;
          background-color: #fff;
          margin-right: 24px;
          margin-bottom: 24px;
          box-shadow: 0px 4px 8px 0px rgba(0, 0, 0, 0.05);
          &:nth-of-type(4n),
          &:last-of-type {
            margin-right: 0;
          }
          .header {
            display: flex;
            justify-content: flex-start;
            align-items: center;
            width: 100%;
            .line {
              width: 4px;
              height: 16px;
              border-radius: 2px;
              background-color: #007aff;
            }
            .phase-label {
              margin-left: 12px;
              font-size: 18px;
            }
            .phase-current {
              margin-left: 16px;
              margin-top: -3px;
              color: orange;
              font-size: 24px;
            }
          }
          .content {
            display: flex;
            justify-content: space-between;
            align-items: baseline;
            .phase-value {
              display: flex;
              justify-content: flex-start;
              align-items: center;
              .value {
                font-weight: 500;
                font-size: 28px;
                color: #007aff;
                padding-left: 16px;
              }
              .unit {
                margin-left: 16px;
              }
            }
            .value-icon {
              .icon {
                width: 50px;
                height: 50px;
              }
            }
          }
        }
        .empty {
          display: flex;
          justify-content: center;
          align-items: center;
          background-color: #fff;
          width: 100%;
          height: 120px;
          color: #000;
        }
      }
    }
    .row-2 {
      display: flex;
      margin-bottom: 24px;
      justify-content: flex-start;
      align-items: stretch;
      .row-left,
      .row-right {
        display: flex;
        flex-direction: column;
        justify-content: flex-start;
        align-items: stretch;
        .content {
          flex: 1 1 auto;
          background-color: #fff;
          .chart {
            width: 100%;
            height: 220px;
          }
          .footer {
            .item {
              background-color: rgba(0, 122, 255, 0.05);
              text-align: center;
              &:not(:last-of-type) {
                border-bottom: 1px solid rgba(229, 230, 235, 1);
              }
              &.label {
                padding: 8px 0;
              }
              &.value {
                padding: 8px;
                color: #007aff;
                font-size: 24px;
              }
            }
          }
          .empty {
            display: flex;
            justify-content: center;
            align-items: center;
            background-color: #fff;
            width: 100%;
            height: 310px;
            color: #000;
          }
        }
      }
      .row-left {
        width: 542px;
      }
      .row-right {
        flex: 1 1 auto;
        padding-left: 24px;
        .content {
          height: 100%;
          .chart {
            height: 100%;
          }
        }
      }
    }
    .row-3 {
      flex: 1 1 auto;
      display: flex;
      flex-direction: column;
      justify-content: flex-start;
      align-items: stretch;
      .content {
        height: 100%;
        /deep/ .el-table {
          .el-table__header-wrapper tr th {
            background-color: rgba(245, 245, 245, 1);
            border-right: 1px solid rgba(0, 0, 0, 0.1);
          }
          .el-table__body-wrapper tr td {
            border-right: 1px solid rgba(0, 0, 0, 0.1);
            border-bottom: 1px solid rgba(0, 0, 0, 0.1);
          }
          .el-table__empty-text {
            color: #000;
          }
        }
      }
    }
  }
}
</style>
