<template>
  <div class="table-model-task">
   
    <div class="list-table" v-for="(item,index) in allList" :key="index">
      <div class="title">
        <i class="icon-newface-pro-jd" :style="{color: item.color}"></i>
        {{item.name}}
      </div>
      <div class="table-content">
        <el-table
          :data="item.data"
          border
          fixed
          height="210"
          highlight-current-row
          style="width: 100%;">
            <el-table-column
              prop="Progress_Name" 
              align="center"
              label="任务名称">
              <template slot-scope="scope">
                  <el-tooltip placement="right" class="table-hover-tooltip" effect="light" popper-class="table-hover-setcss tooltip-model-hover">
                    <div slot="content" class="hover-text">
                      <p>任务名称：<span class="c333333">{{scope.row.Progress_Name}}</span></p>
                      <p>计划开始时间：<span class="c333333">{{scope.row.Progress_planstarttime | filterTimer}}</span></p>
                      <p>计划结束时间：<span class="c333333">{{scope.row.Progress_plannendtime | filterTimer}}</span></p>
                      <p>实际开始时间：<span class="c333333">{{scope.row.Progress_actualstarttime | filterTimer}}</span></p>
                      <p>实际结束时间：<span class="c333333">{{scope.row.Progress_actualendtime | filterTimer}}</span></p>
                      <p>填报时间：<span class="c333333">{{scope.row.Progress_unittime}}</span></p>
                      <p>实际完成比例：<span class="c333333">{{scope.row.Progress_actualratio}}%</span></p>
                      <p>任务状态：<span class="c333333">{{scope.row.Progress_state}}</span></p>
                    </div>
                    <div class="_css-costitem" ><span class="c333333">{{scope.row.Progress_Name}}</span></div>
                  </el-tooltip>
              </template>
            </el-table-column>
            
            <el-table-column
              prop="Progress_actualratio"
              label="实际进度"
              align="center"
              width="100">
              <template slot-scope="scope">
                <div class="_css-costitem" ><span class="c333333">{{scope.row.Progress_actualratio}}%</span></div>
              </template>
            </el-table-column>
            <el-table-column
              align="center"
              prop="Progress_plannendtime"
              label="计划完成时间">
              <template slot-scope="scope">
                <div class="_css-costitem" ><span class="c333333">{{scope.row.Progress_plannendtime}}</span></div>
              </template>
            </el-table-column>
        </el-table>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  name: 'ProgressPlayingListTable',
  data() {
    return {
      allList:[],
    }
  },
  props: {
    allPlayingTableList: {
      type: Array,
      defaults: true
    }, 
  },
  filters: {
    filterTimer(inputtime) {
      if (!inputtime || inputtime.trim().length == 0) {
        return '-';
      }
      if (inputtime.length >= "2019-09-16".length) {
        return inputtime.substr(0, 10);
      }
    },
  },
  watch:{
    allPlayingTableList(val){
      this.allList = val;
    }, 
  },
  mounted(){
    this.allList = this.allPlayingTableList//this.finished;
    
  },
  methods: {
    stringToArray(color){
        const match = color.match(/[\d.]+/g);
        const r = match[0]*1;
        const g = match[1]*1;
        const b = match[2]*1;
        const a = match[3]*1;
        let arr = [r,g,b,a]
        return JSON.stringify(arr);
    },
    cellStyle({ row, column, rowIndex, columnIndex }) {
      return 'text-align: center;';
    },
  }
}
</script>
<style scoped>
.table-model-task{
  width: calc(100% - 24px);
    overflow-y: auto;
    height: 100%;
  margin: 0 12px;
}
.table-model-task /deep/ .el-table td{
  border-color: #D4D6D9;
}
.table-model-task /deep/ .el-table tr:first-child th{
  text-align: center;
  background: #ffffff;
}
.table-model-task /deep/ .el-table:before{
  height: 0;
}
.table-model-task /deep/ .el-table .cell{
  height: 38px;
  line-height: 38px;
  display: block;
}
.table-model-task /deep/ .el-table--scrollable-y .el-table__body-wrapper::-webkit-scrollbar{
  width: 0px;
}
.table-model-task /deep/ .el-tooltip__popper.is-light{
  background: #ffffff;
}
.hover-text{
  line-height: 22px;
}
._css-costitem{
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.title{
  text-align: left;
  font-size: 16px;
  font-family: PingFangSC, PingFang SC;
  font-weight: 500;
  color: #333333;
  display: flex;
  align-items: center;
  margin-top: 20px;
  margin-bottom: 10px;

}
.title i{
  padding-right: 10px;
}
</style>
<style>
.table-hover-setcss.is-light{
  max-width: 240px;
  background-color: #FFFFFF !important;
  font-size: 12px;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #606266;
  border: none;
}
.table-hover-setcss, 
.table-hover-setcss.css-no-triangle.is-light{
  background-color: #FFFFFF !important;
  box-shadow: 0px 0px 6px 0px rgba(0, 0, 0, 0.2);
  border-radius: 4px;
}

</style>