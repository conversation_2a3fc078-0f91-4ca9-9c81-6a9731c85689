<template>
    <div :class="headerHide?'_css-model-progress':'_css-model-progress-big'">
        <div class="_css-model-close icon-suggested-close" v-if="headerHide" @click="closeDialog"></div>
        <div class="_css-time-line">
           <div v-show="!isProgress">
                <div class="_css-play-time-text" v-if="playsign != 'end' && changeTime ">
                    当前时间：{{changeTime | flt_datetimefromList}}
                </div>
                <div class="_css-time-slider" v-if="!timeResNull">
                    <div class="_css-timeline">
                        <el-slider
                            v-model="timeSlidervalue" 
                            :format-tooltip="timestepToolTip"
                            @change="sliderChange"
                            :marks="marks">
                        </el-slider>
                    </div>
                    <div class="_css-time-detail">
                        <div
                            v-if="playsign == 'end'"
                            class="playmodel"
                            @click="mockprogressclick"
                            :class="{'ban-click':bStartDis}">
                            <i class="icon-interface-play"></i>
                            开始模拟
                        </div>
                        <div
                            v-if="playsign == 'pause'"
                            class="playmodel"
                            @click="mockprogressclick"
                            :class="{'ban-click':false && loadingModelEnd == true}"><i class="icon-interface-play"></i>
                            继续模拟
                        </div>
                        <div
                            v-if="playsign == 'start'"
                            class="pausemodel"
                            @click="pausemodelClick"
                            :class="{'ban-click':false && loadingModelEnd == true}"><i class="icon-interface-time-out"></i>
                            暂停模拟
                        </div>
                        <div
                            v-if="playsign != 'end'"
                            class="stopmodel"
                            @click="stopPlayModel()"
                            :class="{'ban-click':false && loadingModelEnd == true}"><i class="icon-interface-stop"></i>
                            停止模拟
                        </div>
                    </div>
                </div>
                <div class="_css-null" v-if="timeResNull" >当前项目不存在形象进度数据</div>
           </div>
            
        </div>
        
        <div class="model-iframe">
            <div :class="headerHide ? '_css-legend' : '_css-legend-big'" v-show="!isProgress">
                <ul>
                    <li v-for="(item,index) in legendColor" :key="index" :style="{color:!headerHide?'rgba(255,255,255,.8)':''}">
                        <span class="_css-color" :style="{background:item.color}"></span>
                        <span class="_css-num">{{item.num}}</span>
                    </li>
                </ul>
            </div>
            <iframe 
                v-if="headerHide" 
                width="100%" 
                height="100%" 
                ref="modelRefTime" 
                @load="imageProLoad" 
                :src="modelIframeSrc" 
                frameborder="0"></iframe> 
        </div>
        <div :class="headerHide?'slider-list-content':'slider-list-content-big'" v-if="listSetColor.length>0">
            <div class="list-progress" ref="listProgress">
                <div v-for="(item,index) in listSetColor" :key="index" >
                    <p>工程结构：{{item.bmc_name}}</p>
                    <p>计划时间： {{item.bip_planStartDT | flt_datetimefromList}}--{{item.bip_planEndDT | flt_datetimefromList}}</p>
                    <p>计划完成比例：{{item.bip_planPercent}}%</p>
                    <p>填报时间： {{item.bip_datetime | flt_datetimefromList}}</p>
                    <p>实际完成比例： {{item.bip_percent}}%</p>
                    <p>状态: {{data_state(item.bip_planPercent,item.bip_percent,item.bip_planStartDT,item.bip_planEndDT,item.bip_datetime)}}</p>
                    <p>填报人： {{item.RealName}}</p>
                    <el-divider v-if="index<listSetColor.length-1"></el-divider>
                </div>
            </div>
        </div> 
    </div>
</template>
<script> 
import Vue from "vue"
let vue = Vue
import { EventBus } from "@static/event.js";
export default {
    data() {
        return { 
            timeSlidervalue:  0,
            marks: {},
            dateMin: '',
            diffStep: 1,
            changeTime: null,
            timeResNull: false, 
            isProgress: true,
            dataDetailVisible:false,
            alllistSetColor: [],
            listSetColor: [],
            legendColor: [
                {color: 'rgba(111,111,111,.5)', num: '0%'},
                {color: 'rgba(255,255,0,.5)', num: '1%-50%'}, 
                {color: 'rgba(255,0,0,.5)', num: '51%-100%'}, 
                {color: 'rgba(30,255,0,.5)', num: '100%'},
            ],
            elementArr: [], // 着色的构件和颜色

            playsign: 'end', // 当前的模型模拟状态 
            bStartDis: false, // 刚点击完开始模拟时，立即dis掉，当 playSign 修改为 start 时，立即取消dis.
            loadingModelEnd: true,
            playTimeHandler: 0,  
            setValue: 0,  // 模拟时候记录时间值
            timeAutoData: 1000,   // 播放秒数
            playingBtn: true,
            sliderMinTime: '',  // 播放的最小时间
            sliderMaxTime: '',  // 播放的最大时间
            sliderTimeDiff: 0,
            revalue: 0,
            allPro_ModelID: [],  // 该值为allPro_ModelID的存值，大屏项目时候该值为全部的allPro_ModelID，平台形象进度进入时候为传入的值
            Pro_bmtypeId: [],  // 该值为Pro_bmtypeId的存值，大屏项目时候该值为全部的Pro_bmtypeId，平台形象进度进入时候为传入的值
            Pro_ToleranceValues: [],  // 该值为Pro_ToleranceValues的存值，大屏项目时候该值为全部的Pro_ToleranceValues，平台形象进度进入时候为传入的值
            headerHide: false, // 大屏显示使用该组件，header不展示 
        };
    },
    props: {
        modelIframeSrc: {
            type: String,
            required: false
        },
        modelid: {
            type: String,
            required: false
        },
        allModelID: {
            type: Array,
            required: false
        },
        bmtypeId: {
            type: String,
            required: false
        },
        ToleranceValues: {
            type: Number,
            required: false
        }
    },
    filters: {
        flt_datetimefromserver(str) {
            var _this = this;
            var str1 = str;
            if (str) {
                str1 = str.replace("T", " ");
            }
            return str1;
        },
        flt_datetimefromList(str) {
            var _this = this;
            var str1 = str;
            if (str) {
                str1 = str.substr(0,4) + '年' + str.substr(5,2) + '月' + str.substr(8,2) + '日'
            }
            return str1;
        }, 
    },
    components: {
         
    },
    
    mounted() {
        let _this = this;

        // 大屏项目直接进入形象进度模拟页面，根据路由title判断，大屏直接进入的直接展示全部，bmtypeId=-1000，ToleranceValues = 0;
        // 初始化时候，如果是大屏进入，默认不让dom加载，在大屏那边获取 modelID和projectID  来加载模型
        // 如果不是大屏项目加载iframe
        if(this.$route.query.title == '形象进度'){
            EventBus.$once("modelMounted", (obj) => { 
                _this.$model = obj.model
                window.model = obj.model.bim365
                _this.$model.Options.isShowTexture(false); 
                _this.isProgress = false;
                // _this.sliderChange(0);
            })
            let _organizeId = _this.$staticmethod._Get("organizeId") || '48617e7b-07f2-4748-9199-238af8f2bfc6'
            
           
            _this.headerHide = false;
            // 如果是14号线  Pro_bmtypeId就展示土建结果的数据
            if(_organizeId == 'bc0d82ea-267a-4895-8ef8-cb71bbfad317'){
                _this.Pro_bmtypeId = 'f4ca0f9d-d0b5-4573-912f-ce7ea6120de1';// -1000
            }else{
                _this.Pro_bmtypeId = '-1000';
            }

            _this.Pro_ToleranceValues = 0;
            let bimAllModel = []
            var _url = `${window.bim_config.webserverurl}/api/Material/Mtr/GetRelationDatasByOrgIdAndBMTypeId?organizeId=${_organizeId}&Token=${_this.$staticmethod.Get('Token')}&bmtypeId=${_this.Pro_bmtypeId}`; 
            _this.$axios.get(_url).then(x => {
                if (x.data.Ret > 0) {
                    if (x.data.Data) {
                        let _list = x.data.Data;
                        _list.forEach((el) => {
                            bimAllModel.push(el.modelid);
                        })
                        if(bimAllModel.length<1){
                            _this.$message.error('当前形象进度下没有关联的模型'); 
                            return
                        } 
                        
                        _this.allPro_ModelID = bimAllModel;
                        _this.getTailTimes();
                        let _modelId = [...new Set(this.allPro_ModelID)];
                        EventBus.$emit("setModel", _modelId);
                        window.model.BIM365API.Events.finishRender.once('default',()=>{//监听模型加载完成事件
                            this.sliderChange(0);
                        }) 
                    } 
                } else {
                    bimAllModel = [];
                    _this.allPro_ModelID = [];
                    console.error(x.data.Msg);
                }
            }).catch(x => {
                console.error(x);
            });
            
        }else{
            _this.headerHide = true;
            _this.$nextTick(()=>{
                _this.allPro_ModelID = _this.allModelID;
                _this.Pro_bmtypeId = _this.bmtypeId;
                _this.Pro_ToleranceValues = _this.ToleranceValues;
                _this.getTailTimes();
            })
        }
    },
    methods: {
        data_state(plan,pre,start,end,pre_end){
            let _this = this;
            let str_State = ''; 
            let _start = start.substr(0, 10);
            let _end = end.substr(0, 10);
            let _pre_end = pre_end.substr(0, 10);

            let diffdate = _this.isDuringDate(_pre_end,_start,_end);  // 判断实际时间是否在计划时间范围

            if(diffdate){
                str_State = _this.toCalculatePercentState(plan,pre);
            }else{
                if(_this.DateDiff(_pre_end,_start) < 1){
                    str_State = '超前';
                }else{
                    str_State = '滞后';
                }
            }
            return str_State;

        },
        DateDiff(sDate1, sDate2) {
            var aDate, oDate1, oDate2, iDays;
            oDate1 = new Date(sDate1).getTime();
            oDate2 = new Date(sDate2).getTime();
            iDays = parseInt((oDate1 - oDate2) / 1000 / 60 / 60 / 24) //把相差的毫秒数转换为天数   
            return  iDays;
        },
        isDuringDate (_adding,beginDateStr, endDateStr) {
            var curDate = new Date(_adding),
                beginDate = new Date(beginDateStr),
                endDate = new Date(endDateStr);
            if (curDate >= beginDate && curDate <= endDate) {
                return true;
            }
            return false;
        },
        /*
            setToleranceValues 容差值
            planPercent  计划完成比例
            actualPercent  实际完成比例
        */
        toCalculatePercentState(planPercent,actualPercent){
            let _this = this;
            let setToleranceValues = _this.Pro_ToleranceValues;
            // 当实际完成时间在计划时间范围内使用该方法
            // 当实际比例在planPercent ± setToleranceValues内，属于正常,小于属于滞后，大于属于超前
            let percentState = '';
            if(actualPercent >= planPercent - setToleranceValues  && actualPercent <= planPercent+setToleranceValues){
                percentState = '正常';
            }else if(actualPercent > planPercent - setToleranceValues){
                percentState = '超前';                
            }else{
                percentState = '滞后';
            }
            return percentState;
        },
        closeDialog() {
            let _this = this;
            this.listSetColor = []; 
            _this.playingBtn = true; 
            _this.timeSlidervalue = 0;
            _this.setValue = 0;
            clearInterval(_this.playTimeHandler);
            _this.playsign = 'end';
            this.$emit('close')
        },
        timestepToolTip(value){ 
            if(value>100){
                return;
            }
            let date = new Date(this.dateMin);
            date.setDate(date.getDate() + value*this.diffStep*0.1);
            var datestrTool = this.$formatData.dateInit(date);
            return datestrTool;
        },
        sliderChange(value) { 
            if(value>100){
                return;
            }
            this.setValue = value;
            this.timeSlidervalue = this.setValue; 
            let date = new Date(this.dateMin);
            date.setDate(date.getDate() + value*this.diffStep*0.1);
            this.changeTime = this.$formatData.dateInit(date); 
            this.getListTimePoint();
        },
        // 获取开始时间结束时间
        getTailTimes(){
            let _OrganizeId = this.$staticmethod._Get("organizeId");
            let _this = this;
            // `${window.bim_config.webserverurl}/api/ImageProgress/BIP/GetHeadTailTimes_bmcGuid?organizeId=${_OrganizeId}&bmcGuid=${_this.Pro_bmtypeId}`
            // `${window.bim_config.webserverurl}/api/ImageProgress/BIP/GetHeadTailTimes?organizeId=${_OrganizeId}`
            _this.$axios
                .get(
                `${window.bim_config.webserverurl}/api/ImageProgress/BIP/GetHeadTailTimes_bmcGuid?organizeId=${_OrganizeId}&Token=${_this.$staticmethod.Get('Token')}&bmcGuid=${_this.Pro_bmtypeId}`
                )
                .then(x => {
                if (x.status == 200) {
                    if (x.data.Ret > 0) {
                        if (x.data.Data) {
                            let _max = x.data.Data.Max;
                            let _min = x.data.Data.Min;
                            _this.sliderMinTime = _min;
                            _this.sliderMaxTime = _max;
                            let diffDay = this.$formatData.DateDiff(_min,_max);

                            _this.sliderTimeDiff = this.$formatData.DateDiff(_min,_max);
                            // console.log(_this.sliderTimeDiff,'两者时间差');
                            _max = _this.$options.filters["flt_datetimefromserver"](_max).substr(0, 10);
                            _min = _this.$options.filters["flt_datetimefromserver"](_min).substr(0, 10);
                            
                            _this.dateMin = _min;
                            _this.diffStep = diffDay/10;
                            _this.marks = {
                                0: _min.toString(),
                                100: _max.toString()
                            } 
                            if(_min == null && _max == null){
                                _this.timeResNull = true;
                            } 
                        }
                    }
                }
                })
                .catch(x => {});
        },
        getDay(value){
            let date = new Date(this.dateMin);
            date.setDate(date.getDate() + value*this.diffStep);
            var datestrTool = this.$formatData.dateInit(date);
            return datestrTool;
        },
        // 根据时间获取所有模型ID着色
        getListTimePoint(){
            let _this = this;
            let _OrganizeId = this.$staticmethod._Get("organizeId");
            let _timestr = this.changeTime + ' 23:59:59';
            let _allpromodelId = [...new Set(this.allPro_ModelID)]
            let _data = {};
            let _url = ''
            if(_this.Pro_bmtypeId == '-1000'){   //  全部分类
                _url =  `${window.bim_config.webserverurl}/api/ImageProgress/BIP/GetListByTimePoint_MulModelIds?Token=${_this.$staticmethod.Get('Token')}`;
                _data = _this.$qs.stringify({
                    organizeId: _OrganizeId,
                    timestr: _timestr,
                    modelids: _allpromodelId.toString()
                })
            }else{  //非全部分类
                _url =  `${window.bim_config.webserverurl}/api/ImageProgress/BIP/GetListByTimePoint_MulModelIds_bmcGuid?Token=${_this.$staticmethod.Get('Token')}`;
                _data = _this.$qs.stringify({
                    organizeId: _OrganizeId,
                    timestr: _timestr,
                    modelids: _allpromodelId.toString(),
                    bmcGuid: _this.Pro_bmtypeId
                })
            }
            _this.$axios({
                url: _url,
                method: 'post',
                data: _data
            }).then(x => {
                if (x.data.Ret > 0) {
                    if (x.data.Data) {
                        _this.listSetColor = x.data.Data.List;
                        // console.log(_this.listSetColor)
                        let element_AllArr=[];
                        let isolateColor = [];
                        let isolateArr = [];
                        _this.listSetColor.forEach(element => {
                            element.PteElementIds.forEach(c_ele=>{
                                element_AllArr.push({ele:c_ele.elementids,color:element.bip_percent,modelid: c_ele.modelid});
                                // console.log({ele:c_ele.elementids,color:element.bip_percent,modelid: c_ele.modelid})
                            })
                            isolateColor.push({color: element.bip_percent});
                            isolateArr.push({ElementArr: element.PteElementIds});
                        });
                        
                        let arrReset = _this.getElement(element_AllArr);  //  所有的构件
                         
                        _this.$nextTick(()=>{
                            _this.$refs.listProgress.scrollTop = 0;
                        })
                            
                        let modelRefTime = null
                        if(_this.headerHide){
                            modelRefTime = _this.$refs.modelRefTime.contentWindow;
                        }else{
                            modelRefTime = window;
                        }

                        _this.setColorBIM(modelRefTime,arrReset);
                        
                    }

                } else {
                    _this.$message.error(x.data.Msg);
                }
            }).catch(x => {
                console.error(x);
            });
 
        },
        // getTimerChange(sliderchangetime) {
        //     let _this = this;
        //     let element_AllArr=[];
        //     let isolateColor = [];
        //     let isolateArr = [];
        //     _this.listSetColor = [];
        //     for(let i in _this.alllistSetColor){
        //         let sub_i = i.substr(0,10);
        //         if(sub_i == sliderchangetime){
        //             _this.listSetColor = _this.alllistSetColor[i]
        //             let element_AllArr=[];
        //             let isolateColor = [];
        //             let isolateArr = [];
        //             _this.listSetColor.forEach(element => {
        //                 element.PteElementIds.forEach(c_ele=>{
        //                     element_AllArr.push({ele:c_ele.elementids,color:element.bip_percent,modelid: c_ele.modelid});
        //                     // console.log({ele:c_ele.elementids,color:element.bip_percent,modelid: c_ele.modelid})
        //                 })
        //                 isolateColor.push({color: element.bip_percent});
        //                 isolateArr.push({ElementArr: element.PteElementIds});
        //             });
                    
        //             let arrReset = _this.getElement(element_AllArr);  //  所有的构件 
        //             _this.$nextTick(()=>{
        //                 _this.$refs.listProgress.scrollTop = 0;
        //             })
        //             let modelRefTime = null;
        //             if(_this.headerHide){
        //                 modelRefTime = _this.$refs.modelRefTime.contentWindow;
        //             }else{
        //                 modelRefTime = window;
        //             }

        //             _this.setColorBIM(modelRefTime,arrReset);
        //         }
        //     }
            
        // },
        setColorBIM(thewin,eles) {
            var _this = this;
            var colorUtility = _this.$staticmethod.bimhelper_getColorUtility(thewin); 
            // 着色是根据不同的完成百分比，来确定当前构件的颜色，将构件循环着色
            colorUtility.isolateElementByElementId(eles).then(x => {
                _this.elementArr.forEach(res=>{
                    let r,g,b,a;
                    let num = res.color;
                    if(res.ele && res.ele.length>0){
                        if(num<=1){
                            r = 111;
                            g=111;
                            b=111;
                            a=0.5;
                        }else if(num>1  && num<=50){
                            r = 255;
                            g = 255;
                            b = 0,
                            a = 0.5
                        }else if(num>50  && num<100){
                            r = 255;
                            g = 0;
                            b = 0,
                            a = 0.5
                        }else{
                            r = 30;
                            g = 255;
                            b = 0,
                            a = 0.5
                        } 
                        colorUtility.setElementColor(res.ele,r,g,b,a,false,false)
                    }

                })
                
            }); 
            

        }, 
        getElement(listElements) {
            let arr = [];
            let _ele_=[];
            listElements.forEach(item => {
                let item_ele = [];
                item.ele.forEach(ids => {
                    arr.push(`${item.modelid}^${ids}`);
                    item_ele.push(`${item.modelid}^${ids}`);
                })
                _ele_.push({ele:item_ele,color:item.color})
            })
            this.elementArr = _ele_;
            return arr;
        },
        showDataDetail(){
            this.dataDetailVisible = true;
        }, 
        // iframe 加载完毕后
        imageProLoad() {
            
            let _this  = this;
            let iframeWindow = _this.$refs.modelRefTime.contentWindow;
            iframeWindow.model.BIM365API.Events.finishRender.on('default',()=>{
                _this.isProgress = false;
                _this.sliderChange(0);  //  调用：初始化时候要显示第一天的数据
            })
        },
        // 开始模拟，继续模拟
        mockprogressclick() {
            let _this = this;
            let timeauto = _this.timeAutoData; 

            if(_this.timeSlidervalue == 100){
                _this.timeSlidervalue = 0;
                _this.setValue = 0;
                _this.sliderChange(0);
                _this.timestepToolTip(0);
            }
            
            _this.playTimeHandler = setTimeout(()=>{
                _this.frameExecuteUnit(timeauto,_this.setValue);
            }, timeauto);
            _this.playingBtn = false;
            _this.playsign = 'start'; 
        },
        frameExecuteUnit(timeauto,val){
            let _this = this;
            _this.revalue = val;
            if(val > 100){  
                _this.timeSlidervalue = 100;
                clearInterval(_this.playTimeHandler);
                _this.playsign = 'end';
                _this.setValue = 100;
                _this.sliderChange(100);
                _this.timestepToolTip(100);
                return
            }
            if(_this.sliderTimeDiff == 0){
                _this.setValue = (val + 100);
            }else{
                _this.setValue = (val + (100 / _this.sliderTimeDiff));
            }
            // console.log(_this.setValue,'===setValue');
            
            _this.playTimeHandler = setTimeout(()=>{
                _this.sliderChange(_this.setValue);
                _this.timestepToolTip(_this.setValue);
                _this.timeSlidervalue = _this.setValue;
                _this.frameExecuteUnit(timeauto,_this.setValue);
            }, timeauto);
        },
        // 暂停模拟
        pausemodelClick() {
            let _this = this;
            _this.setValue = _this.revalue;
            _this.playingBtn = true;
            _this.playsign = 'pause';
            clearInterval(_this.playTimeHandler);
        },
        // 停止模拟
        stopPlayModel() {
            let _this = this;
            _this.playingBtn = true;
            _this.sliderChange(0);
            _this.timestepToolTip(0);
            _this.timeSlidervalue = 0;
            _this.setValue = 0;
            clearInterval(_this.playTimeHandler);
            _this.playsign = 'end';
        }, 

    },
    beforeDestroy() {
        
        clearInterval(this.playTimeHandler);
    },
    destroyed() {
        clearInterval(this.playTimeHandler);
    }


}
</script>
<style scoped>
._css-model-progress{
    width: 100%;
    height: 100%;
    position: fixed;
    z-index: 1000;
    top:0;
    left: 0;
    /* background: #fff; */
    background: #f7f7f7;
}
._css-model-progress-big{
    position: absolute;
    z-index: 1000;
    top:20px;
    left: 20px;
    right: 20px;
    bottom: 20px;
    pointer-events: none;
}
._css-model-close{
    position: fixed;
    right: 10px;
    top: 10px;
    z-index:1000;
}
._css-model-close:hover{
    cursor: pointer;
}
._css-time-line{
    pointer-events: all;
    height: 50px;
    padding: 60px 2% 0 3%;
}
.model-iframe{
    width: 100%;
    height: calc(100% - 100px);
}

._css-time-slider{
    /* margin: 60px 2% 0 3%; */
    display: flex;
}

._css-time-slider /deep/ .el-slider__marks-text{
    white-space: nowrap;
}
._css-null{
    margin: 0 5%;
    text-align: left;
}
._css-time-detail{
    /* width: 150px; */
    display: flex;
    text-align: right;
    line-height: 40px;
    font-size: 14px;
    font-weight: 500;
    margin-left: 35px;
    
}
._css-time-detail:hover{
    cursor: pointer;
}
._css-time-detail div{
    padding: 0 8px;
    height: 32px;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
    -ms-flex-direction: row;
    flex-direction: row;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    border-radius: 4px;
    border: 1px solid #1890ff;
    /* color: #1890ff; */
    /* background: #f7f7f7; */
    margin-left: 16px;
    /* opacity: .7; */
    cursor: pointer;
}

._css-time-detail div.pausemodel:hover{
    opacity:1;
}
._css-time-detail div.pausemodel{
  background: #FAAD14;
  color: #fff;
  border-color: #FAAD14;
   opacity:.7;
}
._css-time-detail div.stopmodel{
  background: #F5222D;
  color: #fff;
  border-color: #F5222D;
   opacity:.7;
}
._css-time-detail div.stopmodel:hover{
    opacity:1;
}
._css-time-detail div.playmodel{
  background: #1DA48C;
  color: #fff;
  border-color: #1DA48C;
   opacity:.7;
}
._css-time-detail div.playmodel:hover{
    opacity:1;
}
._css-timeline{
    flex:1;
}
.content-list p{
    line-height: 30px;
}
.content-list{
    height: 200px;
    overflow-y: auto;
}
.content-list /deep/ .el-divider--horizontal{
    margin: 10px 0;    
}
._css-legend{
    position: fixed;
    top: 20%;
    left: 2%;
    text-align: left;
}
._css-legend-big{
    position: absolute;
    top: 20%;
    left: 3%;
    text-align: left;
}
._css-legend li,._css-legend-big li{
    line-height: 26px;
}
._css-color{
    display: inline-block;
    width: 20px;
    height: 8px;
    border-radius: 1px;
    margin-right: 5px;
}

.slider-list-content{
    position: fixed;
    top: 35%;
    left: 2%;
    text-align: left; 
    min-width:300px;
    max-height: 500px;
    max-width: 600px;
    background: rgba(0, 0, 0, 0.5);
    border-radius: 10px;
    color: #fff;
    padding: 12px;
}
.slider-list-content-big{
    position: absolute;
    top: 35%;
    left: 3%;
    text-align: left; 
    min-width:300px;
    max-height: 500px;
    max-width: 600px;
    background: rgba(0, 0, 0, 0.5);
    border-radius: 10px;
    color: #fff;
    padding: 12px;
    pointer-events: all;
}
.list-progress{
    max-height: 500px;
    overflow: auto;
}
.slider-list-content p,.slider-list-content-big p {
    line-height: 24px;
}
.model-loading{
   width: 250px;
   height: 100px;
   overflow: hidden;
}
div.ban-click{
  background: rgba(0, 0, 0, .25);
  color:#fff;
  cursor: not-allowed;
  border: 1px solid transparent;
}
._css-play-time-text{
    position: absolute;
    left: 3%;
    top: 27px;
    width: 215px;
    height: 34px;
    background: rgba(0,0,0,0.65);
    color: #fff;
    line-height: 34px;
    border-radius: 6px;
    font-size: 15px;
}
</style>