<template>
  <div class="_css-materials-mgr">
    <MaterialsMgr :showEdit="false" ref="materialsMgr"></MaterialsMgr>
  </div>
</template>
<script>
import MaterialsMgr from "@/components/Home/ProjectBoot/MaterialsMgr";
export default {
  name: "CompRelMater",
  data() {
    return {
			currentData: {},
		};
  },
  components: {
    MaterialsMgr,
  },
  created() {
    // let _bimcomposerId = this.$route.params.bimcomposerId;
    // let _organizeId = this.$route.params.organizeId;
    // let _token = this.$route.params.token;
		// this.$staticmethod.Set("Token", _token);
		// sessionStorage.setItem('organizeId',_organizeId)
		// sessionStorage.setItem('bimcomposerId',_bimcomposerId)

    // let _bm_guid = this.$route.params.guid;
    // let _bc_guid_materialtype = this.$route.params.type;
    // this.currentData.bm_guid = _bm_guid;
		// this.currentData.bc_guid_materialtype = _bc_guid_materialtype;

    let _arr = this.$route.params.guid.split('|')
    // let _bm_guid = _arr[0];
    // let _bc_guid_materialtype = _arr[1]

		this.currentData.bm_guid =_arr[0];
		this.currentData.bc_guid_materialtype =_arr[1];
		console.log(this.currentData,'currentData')
		this.m_formmaterialselectedObjFun(this.currentData)
  },
  methods: {
    // 调用跳转到点击的构件
    m_formmaterialselectedObjFun(currentData) {
      console.log(currentData, "点击关联构件列表");
      this.loading = true;
      this.$axios
        .get(
          `${this.$configjson.webserverurl}/api/Material/Mtr/GetCategoryArrayByBmGuid?bm_guid=${currentData.bm_guid}&Token=${this.$staticmethod.Get('Token')}`
        )
        .then((res) => {
          console.log(res, "点击关联构件列表");
          if (res.data.Ret == 1) {
            this.MaterialsMgrShow = true;
            let currentParentDatas = res.data.Data;
            setTimeout(() => {
              this.$refs.materialsMgr.setSomeTreeChecked(
                currentData,
                currentParentDatas
              );
            }, 500);
          } else {
            this.$message.error(res.data.Msg);
          }
          this.loading = false;
        })
        .catch((res) => {
          this.loading = false;
        });
    },
  },
};
</script>
<style lang="scss" scoped>
._css-materials-mgr {
  width: 100%;
  height: 100%;
  z-index: 4;
  background: #fff;
}
</style>