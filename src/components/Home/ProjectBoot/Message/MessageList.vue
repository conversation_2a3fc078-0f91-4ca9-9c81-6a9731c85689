<template>
    <!-- 看你的代码比看UnReal源码还费劲 -->
    <div data-debug="MessageList_vue_line2" class="MessageList">
        <div class="head">
            <button class="back" @click="$emit('CloseList')">
                <div class="icon-suggested-close-fill _css-backtolist-icon" ></div>
                <div class="_css-backtolist-text">返回</div>
            </button>
            <ul class="options">
                <!-- 已无力吐槽 -->
                <li :class="m_moduleSign=='站内信'?'sel':''" 
                @click="PageIndex=1;m_moduleSign='站内信';evt_moduleOrTypeChange('站内信','全部');">
                    站内信
                    <i v-if="totalNum.Web_Notify">{{totalNum.Web_Notify}}</i>
                </li>
                <li :class="m_moduleSign=='问题追踪'?'sel':''" @click="PageIndex=1;m_moduleSign='问题追踪';evt_moduleOrTypeChange('问题追踪',m_itemTypeSign);">问题追踪<i v-if="totalNum.IssueNum_UnRead">{{totalNum.IssueNum_UnRead}}</i></li>
                <li :class="m_moduleSign=='项目文档'?'sel':''" @click="PageIndex=1;m_moduleSign='项目文档';evt_moduleOrTypeChange('项目文档',m_itemTypeSign);">项目文档<i v-if="totalNum.DocNum_UnRead">{{totalNum.DocNum_UnRead}}</i></li>
                <li :class="m_moduleSign=='项目流程'?'sel':''" @click="PageIndex=1;m_moduleSign='项目流程';evt_moduleOrTypeChange('项目流程',m_itemTypeSign);">项目流程<i v-if="totalNum.ProcessNum_UnRead">{{totalNum.ProcessNum_UnRead}}</i></li>
            </ul>
        </div>
        <div class="con" id="id_middle_content">
            <div class="btns">
                <button :class="m_itemTypeSign=='全部'?'sel':''" @click="m_itemTypeSign='全部';evt_moduleOrTypeChange(m_moduleSign,'全部');">全部</button>
                <button v-show="m_moduleSign != '站内信'" :class="m_itemTypeSign=='新增'?'sel':''" @click="m_itemTypeSign='新增';evt_moduleOrTypeChange(m_moduleSign,'新增')">新增</button>
                <button v-show="m_moduleSign != '站内信'" :class="m_itemTypeSign=='更新'?'sel':''" @click="m_itemTypeSign='更新';evt_moduleOrTypeChange(m_moduleSign,'更新')">更新</button>
                <button v-show="m_moduleSign != '站内信'" :class="m_itemTypeSign=='删除'?'sel':''" @click="m_itemTypeSign='删除';evt_moduleOrTypeChange(m_moduleSign,'删除')">删除</button>
                <el-tooltip
                  popper-class="css-no-triangle"
                  effect="dark"
                  content="全部设为已读"
                  placement="right"
                >
                    <div 
                    @click.stop="setallread()"
                    class="_css-setread-btn _css-absolute" >
                        <div class="icon-suggested-check_circle _css-setread-btnin">
                        </div>
                    </div>
                </el-tooltip>
            </div>
            <div class="list">
                <ul>
                    <li v-for="item in PageListData" :class="{'_css-msgitem':true,'sel':selectLine==item.mu_guid}" :key="item.mu_guid" >
                        <span :data-debug="item.mu_guid"  class="IsUnRead" :style="{backgroundColor:item.isRead==0?'rgba(24, 144, 255, 1)':'transparent'}"></span>
                        
                        <span class="t _css-msgitem-content">
                            {{MessageContent(item)}}
                            <!-- 弹出的受邀请机构的消息中的确认按钮 -->
                            <div @click="joinToCompany(item, $event)" v-if="item.LogAndMsgType == 50" class="_css-join-btn" style="">确认加入</div>
                            <!-- //弹出的受邀请机构的消息中的确认按钮 -->
                        </span>

                        <span class="CreateTime">{{item.mm_createdatetimestr?item.mm_createdatetimestr.substr(0, 16):''}}</span>
                        <el-tooltip
                            popper-class="css-no-triangle"
                            effect="dark"
                            content="设为已读"
                            placement="right"
                            v-if="item.isRead == 0"
                        >
                            <div 
                            @click="setitemread(item.mu_guid)"
                            class="_css-setread-btn" >
                                <div class="icon-suggested-check_circle _css-setread-btnin">
                                </div>
                            </div>
                        </el-tooltip>
                    </li>
                </ul>
            </div>
        </div>
        <div class="Page">
            <page-object class="PgCss" @changePageInfo='changePageInfo' :PageIndex=PageIndex :PageNum=PageNum 
                :PageTotal="TraversePagesItemCount">
            </page-object>
        </div>
    </div>
</template>
<script>
import pageObject from '@/components/CompsCustom/PageObject'
export default {
    name:'MessageList',
    components:{
        pageObject,
    },
    data(){
        return {
            TraversePagesItems: {},
            SearchData:[],
            PageListData:[],
            PageIndex:1,
            PageNum:15,

            // 总条目数
            // -------
            TraversePagesItemCount:500,
            
            m_moduleSign: "站内信",
            m_itemTypeSign: "全部",
            selectLine:"",
            totalNum:{

                // 各模块未读消息数量
                // -----------------
                Web_Notify: 0,
                IssueNum_UnRead:0,
                DocNum_UnRead:0,
                ProcessNum_UnRead:0
              
            },
            dicdata:{
               Issue_Create: 4,
               Issue_Modify: 5,
               Issue_Delete: 6,
               Issue_Comment_Create: 7,
               Issue_Comment_Delete: 8,
               Issue_Doc_Create: 9,
               Issue_Doc_Delete: 10,

               Doc_NewFile: 21,
               Doc_RemoveFile: 22,
               Doc_ModifyFile: 23,
               Doc_MoveFile: 24,
               Doc_NewDir: 27,

               Issue_Comment_Create_At: 1007
            }
        };
    },
    methods:{

        do_setallread(){
            var _this = this;
            console.log(_this.m_moduleSign);
            var typearr = [];
            if (_this.m_moduleSign == '问题追踪') {
                typearr = [
                    _this.dicdata.Issue_Create,
                    _this.dicdata.Issue_Modify,
                    _this.dicdata.Issue_Delete,
                    _this.dicdata.Issue_Comment_Create,
                    _this.dicdata.Issue_Comment_Delete,
                    _this.dicdata.Issue_Doc_Create,
                    _this.dicdata.Issue_Doc_Delete
                ];
            } else if (_this.m_moduleSign == '项目文档') {
                typearr = [
                    _this.dicdata.Doc_NewFile,
                    _this.dicdata.Doc_RemoveFile,
                    _this.dicdata.Doc_ModifyFile,
                    _this.dicdata.Doc_MoveFile,
                    _this.dicdata.Doc_NewDir
                ];
            } else if (_this.selectType == '项目流程'){
                typearr = [
                    _this.dicdata.Flow_Examine,
                    _this.dicdata.Flow_Reject,
                    _this.dicdata.Flow_Submit
                ];
            } else {
                typearr = [
                ];
            }
            var arrstr = typearr.join(',');
            console.log(arrstr);
            console.log(_this.$staticmethod.Get("Token"));

            // 调用接口，修改指定Token的某些类型消息为已读
            _this.$axios({
                url:`${window.bim_config.webserverurl}/api/Message/JPush/SetMsgReadByTypeAndToken`,
                method:'post',
                data: _this.$qs.stringify({
                    Types: arrstr,
                    Token: _this.$staticmethod.Get("Token")
                })
            }).then(x => {
                if (x.status == 200) {
                    if (x.data.Ret > 0) {
                        //debugger;
                        _this.$message.success('操作成功');
                        _this.func_getList();
                    } else {
                        console.error(x);
                    }
                } else {
                    console.error(x);    
                }
            }).catch(x => {
                console.error(x);
            });
        },

        // 全部设为已读
        setallread(){
            var _this = this;

            var notreadlen = _this.TraversePagesItems.List.filter(x => x.isRead == '0').length;
            if (notreadlen == 0) {
                _this.$message.warning('当前不存在未读消息');
                return;
            }

            _this
            .$confirm("确定全部置为已读？", "操作确认", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning"
            })
            .then(x => {
                _this.do_setallread();
            })
            .catch(x => {});
        },

        setitemread(mu_guid){
            var _this = this;
            _this.selectLine = mu_guid;

            // 设为已读
            _this.$axios({
                url:`${window.bim_config.webserverurl}/api/Message/JPush/SetMsgRead?Token=${this.$staticmethod.Get('Token')}`,
                method:'post',
                data:_this.$qs.stringify({
                    mu_guids: mu_guid
                })
            }).then(x => {
                if (x.status == 200) {
                    if (x.data.Ret > 0) {
                        // 刷新
                        _this.$message.success('操作成功');
                        _this.func_getList();

                    } else {
                        console.error(x.data.Msg);
                    }
                } else {
                    console.error(x);
                }
            }).catch(x => {
                console.error(x);
            });
        },

      MessageContent(msgobj){
            var _this = this;  
            if (msgobj.Op_RealName == '') {
                msgobj.Op_RealName = null;
            }
             console.log(msgobj.LogAndMsgType);
            //debugger;
            var outJson={};
            if (msgobj.LogAndMsgType == _this.dicdata.Issue_Create) {
                // 弹出消息
                outJson={
                    title: '【问题】',
                     message: msgobj.ExtDataContent,
                };
            } else if (msgobj.LogAndMsgType == _this.dicdata.Issue_Delete){
                outJson={
                    title: '【问题】',
                     message: msgobj.ExtData,
                };
            } else if (msgobj.LogAndMsgType == _this.dicdata.Issue_Modify) {
                outJson={
                    title: '【问题】',
                    message: msgobj.BI_Title?`【${msgobj.Op_RealName}】修改了问题【${msgobj.BI_Title}】`:msgobj.ExtData,
                };
            } else if (msgobj.LogAndMsgType == _this.dicdata.Issue_Comment_Create) {
                outJson={
                    title: '【问题】',
                    message: msgobj.ExtDataContent,
                };
            } else if (msgobj.LogAndMsgType == _this.dicdata.Issue_Comment_Delete) {
                outJson={
                    title: '【问题】',
                    message: `【${msgobj.Op_RealName}】移除了问题【${msgobj.BI_Title}】中的评论`,
                };
            } else if (msgobj.LogAndMsgType == _this.dicdata.Issue_Doc_Create) {
                outJson={
                    title: '【问题】',
                    message: `【${msgobj.Op_RealName}】向问题【${msgobj.BI_Title}】中添加了关联文档`,
                };
                //************ */
            } else if (msgobj.LogAndMsgType == _this.dicdata.Issue_Doc_Delete) {
                outJson={
                    title: '【问题】',
                    message: `【${msgobj.Op_RealName}】移除了问题【${msgobj.BI_Title}】中的关联文档`,
                };
                //*************** */
            } else if (msgobj.LogAndMsgType == _this.dicdata.Doc_NewFile) {
                outJson={
                    title: '【文档】',
                    message: msgobj.Op_RealName?`【${msgobj.Op_RealName}】上传了文档【${msgobj.mm_objname}】`:msgobj.ExtData,
                };
            } else if (msgobj.LogAndMsgType == _this.dicdata.Doc_RemoveFile) {
                outJson={
                    title: '【文档】',
                    message: msgobj.Op_RealName?`【${msgobj.Op_RealName}】删除了文档【${msgobj.mm_objname}】`:msgobj.ExtData,
                };
            } else if (msgobj.LogAndMsgType == _this.dicdata.Doc_ModifyFile) {
                outJson={
                    title: '【文档】',
                    message: msgobj.Op_RealName?`【${msgobj.Op_RealName}】修改了文档【${msgobj.mm_objname}】`:msgobj.ExtData,
                };
            } else if (msgobj.LogAndMsgType == _this.dicdata.Doc_MoveFile) {
                outJson={
                    title: '【文档】',
                    message: msgobj.Op_RealName?`【${msgobj.Op_RealName}】移动了文档【${msgobj.mm_objname}】`:msgobj.ExtData,
                };
            } else if (msgobj.LogAndMsgType == _this.dicdata.Doc_NewDir) {
                outJson={
                    title: '【文档】',
                    message: msgobj.Op_RealName?`【${msgobj.Op_RealName}】新增了文件夹【${msgobj.mm_objname}】`:msgobj.ExtData,
                };
            } else if (msgobj.LogAndMsgType == _this.dicdata.Issue_Comment_Create_At) {
                outJson={
                    title: '【问题】',
                    dangerouslyUseHTMLString: true,
                     message: `【提到了你】${msgobj.ExtDataContent}`,
                };
            } else if (msgobj.LogAndMsgType == _this.dicdata.Web_Notify) {
                 outJson={
                    title: '【站内信】',
                    message: msgobj.orgm?`【${msgobj.Op_RealName}】邀请你加入机构【${msgobj.orgm}】`:msgobj.ExtData,
                };

            // 流程相关
            } else if (msgobj.LogAndMsgType == _this.dicdata.Flow_Submit
            || msgobj.LogAndMsgType == _this.dicdata.Flow_Examine
            || msgobj.LogAndMsgType == _this.dicdata.Flow_Reject
            ) {
                 outJson={
                    title: '【流程】',
                    message: `${msgobj.ExtDataContent}`,
                };

            } else {
                //console.warn('有消息类型未处理', msgobj);
                //debugger;
            }

            if (outJson && outJson.message) {
                return outJson.message.replace('【】','');
            } else {
                return '';
            }
            
        },

        // created 后会自动调用
        // 第一次调用时，当前模块是哪个，以及取什么操作类型已静态确定
        // -----------------------------------------------------
        func_getList(){

            // 取参数 Token
            // 显示 loading
            // ------------
            var token = this.$staticmethod.Get('Token');
            var _this=this;
            var _LoadingIns = _this.$loading({
                text: "加载中",
                target:document.getElementById("id_middle_content")
            });

            // 请求地址
            // buttonSign 为 “站内信” 时，忽略 fucSign
            // --------------------------------------
            var _url = `${window.bim_config.webserverurl}/api/Message/JPush/CurMsg_WithNum?buttonSign=${encodeURIComponent(_this.m_moduleSign)}&fucSign=${encodeURIComponent(_this.m_itemTypeSign)}&Token=${token}`;

            // 请求不同的模块、不同的数据操作类型接口后，设定总页码数
            // -------------------------------------------------
            this.$axios.get(_url).then(res=>{
                
                // AllListData： 当前选中的功能模块、当前数据类型（新增、全部等分类）下的分页前的数据
                // TraversePagesItemCount 当前选中的功能模块、当前数据类型（新增、全部等分类）下的分页前的数据 的总条目数
                // PageListData 用来渲染页面元素的数组
                // SearchData 是干嘛用的？
                // ---------
                _this.TraversePagesItems=res.data.Data; 

                // 加载各模块的未读消息数量
                // ----------------------
                _this.func_getUnreadCountEachModule();

                // 取分页后的数据，赋给用于渲染的 PageListData
                // -----------------------------------------
                _this.PageListData=_this.TraversePagesItems.List.slice(0,_this.PageNum);

                // 赋值 TraversePagesItemCount
                // ---------------------------
                _this.TraversePagesItemCount=_this.TraversePagesItems.List.length;

                _LoadingIns.close();

            }).catch(x => {

                _LoadingIns.close();

            });
        },

        // 通过截取数据来得到 PageListData。 i 为 pageIndex, n 为 pageNum
        // ------------------------------------------------------------
        changePageInfo(i,n) {

            var _this = this;
            _this.PageIndex = i;
            _this.PageNum = n;
            _this.PageListData = _this.TraversePagesItems.List.slice((i - 1) * n, i * _this.PageNum);
            _this.TraversePagesItemCount = _this.TraversePagesItems.List.length;

        },

        // 加入机构
        // --------
        joinToCompany(item, ev) {

            // 弹出操作确认
            // -----------
            var _this = this;
//             item.mu_guid
// "65b3ecd6-2b49-4ec5-9d3b-26a3a31f952c"
// item.orgid
// "d3cb14ea-9db0-48fc-b53c-6388cb3f6777"
            var _mu_guid = item.mu_guid;
            var _orgid = item.mme_ProjectID;
            var _orgm = item.orgm;
            _this.$confirm(`确认加入机构？`, '操作确认', {
                confirmButtonText:'确定',
                cancelButtonText:'取消',
                type:'warning'
            }).then(x => {
                _this.do_JoinToCompany(_mu_guid, _orgid, _orgm);
            }).catch(x => {

            });

        },

        // 执行加入机构
        // -----------
        do_JoinToCompany(mu_guid, orgid, orgm) {
            var _this = this;
            var _url = `${window.bim_config.webserverurl}/api/Message/JPush/DoJoinCompany?Token=${this.$staticmethod.Get('Token')}`;
            _this.$axios({
                method:'post',
                url:_url,
                data: _this.$qs.stringify({
                    mu_guid: mu_guid,
                    OrganizeId: orgid
                })
            }).then(x => {
                if (x.status == 200) {
                    if (x.data.Ret > 0) {

                        // 提示用户已加入机构，并修改logo和名称（如果显示）
                        // -------------------------------------------
                        _this.$message.success(`您已加入机构${x.data.Data.FullName}`);

                        // 给上面发通知，刷新logo和名称
                        // --------------------------
                        if (window.location.href.indexOf('Home/Boot/') >= 0) {
                            //window.location.href = `${window.bim_config.hasRouterFile}/#/Home/Boot/${_this.$staticmethod.Get("Token")}`;
                            window.location.reload();
                        } else {

                            // 通知外面把图标更新一下
                            // --------------------
                            _this.$emit("do_refresh_from_beusing");

                            // 把小界面上的数字刷一下
                            // ------------------
                            _this.conditionSearch(_this.selectType, _this.selectFuc);
                        }

                    } else {
                        _this.$message.error(x.data.Msg);
                    }
                } else {
                    console.error(x);
                }
            }).catch(x => {
                console.error(x);
            });
        },

        // 加载各模块的未读消息数量（各模块全部）
        // ----------------------------------
        func_getUnreadCountEachModule(){

            var _this = this;

            // 消息大列表站内信未读消息数量
            // 消息大列表文档模块未读消息数量
            // 消息大列表问题模块的未读消息数量
            // 消息大列表流程模块的未读消息数量
            // ----------------------------- //
            _this.totalNum.Web_Notify = _this.TraversePagesItems.CntObj.NotifyUnReadCnt;
            _this.totalNum.IssueNum_UnRead = _this.TraversePagesItems.CntObj.IssueUnReadCnt;
            _this.totalNum.DocNum_UnRead = _this.TraversePagesItems.CntObj.DocUnReadCnt;
            _this.totalNum.ProcessNum_UnRead = _this.TraversePagesItems.CntObj.FlowUnReadCnt;

        },

        // 得到查询后、分页前的数据
        // 调用前必须确保 _this.m_moduleSign 及 _this.m_itemTypeSign 已被重新赋值
        // --------------------------------------------------------------------
        changeList(){

            // 从vue属性中取出最近要读取的是哪个功能模块、哪种操作类型
            // 应拿着 _this.m_moduleSign 与 _this.m_itemTypeSign 重新请求接口，赋值
            // ------------------------------------------------------------------
            var _this = this;
            _this.func_getList();
        },

        // 点击功能模块或消息类型（全部 新增 更新 删除）时调用
        // 参数 buttonSign: 站内信 问题追踪 项目文档 项目流程
        // 参数 fucSign: 全部 新增 更新 删除
        // -------------------------------
        evt_moduleOrTypeChange(buttonSign,fucSign){

            // 将 fucSign 的值拷贝给 m_itemTypeSign
            // ------------------------------
            this.m_itemTypeSign = fucSign;
            this.m_moduleSign = buttonSign;

            // 得到查询后，分页前的数据
            // ----------------------
            this.changeList();

            // 截取数据，得到 => PageListData
            // -----------------------------
            this.changePageInfo(this.PageIndex,this.PageNum);
        },
        cancelUnRead(){
            //取消未读标识
        }
    },
    created(){

        // 设置当前页码为1
        // 加载消息
        // -------
        var _this = this;
        _this.PageIndex=1;
        window.mlvue = _this;

        // 拷贝 _this.dicdata
        // ------------------
        _this.dicdata = _this.$staticmethod.getMsgTypesObj();

        // 初始化加载消息，内部会使用预设的消息模块及数据操作类型
        // -------------------------------------------------
        _this.func_getList();

    }
}
</script>
<style scoped>

._css-join-btn{
    padding: 2px 6px 2px 6px;
    line-height: 20px;
    border: 1px solid transparent;
    background-color: #1890FF;
    color: #fff;
    border-radius: 4px;
    text-align: center;
    box-sizing: border-box;
    cursor: pointer;
    opacity: 0.8;
}

._css-join-btn:hover{
    opacity: 1;
}

._css-absolute{
    position: absolute;
}
._css-msgitem{
    display: flex;
    align-items: center;
    position: relative;
}
._css-setread-btn {
    height: 24px;
    width: 24px;
    right: 14px;
    display: flex;
    align-items: center;
    justify-content: space-around;
    color:rgba(0,0,0,0.45);
    cursor:pointer;
}
._css-setread-btn:hover {
    background-color: rgba(24, 144, 255, 0.1);
    color:rgba(24, 144, 255, 1);
}

._css-backtolist-icon{
        height: 20px;
    width: 20px;
    margin-left: 8px;
    font-size: 20px;
}
._css-backtolist-text{
    line-height: 32px;
    height:32px;
}
ul,li{padding:0px;margin:0px;}
.MessageList{
    width:100%;height:100%;background-color: #fff;position: absolute;left:0px;top:0px;z-index:999;
}
.MessageList .head{
    width:100%;height:100px;background-color:#fff; box-shadow: 0px 2px 2px #e0e0e0;position: relative;
}
.MessageList .head .back{
    width:90px;height:32px;position: absolute;left:20px;top:20px;border:none;outline:none;background: none;cursor: pointer;border-radius: 2px;padding:0px 0px 0px 15px;
    display: flex;
    align-items: center;
}
.MessageList .head .back:hover{
    background-color:#f0f2f5;
}
.MessageList .head .options{
    width:100%;height:40px;font-size:0px;position: absolute;bottom: 0px;left:0px;line-height: 40px;
}
.MessageList .head .options li{
    width:110px;height:40px;display: inline-block;font-size: 14px;position: relative;cursor: pointer;
}
.MessageList .head .options li.sel{
    height:38px;border-bottom:2px solid rgba(24, 144, 255, 1);
}
.MessageList .head .options li i{
    display: block;position:absolute;right:10px;top:13px;width:14px;height:14px;line-height: 14px;text-align: center;font-style: normal;color:#fff;background-color: rgba(245, 34, 45, 1);font-size:12px;border-radius: 2px;
}

.MessageList .con{
    width:100%;height:calc(100% - 180px);background-color:#f0f2f5; 
}
.MessageList .con .btns{
    width:900px;height:56px;line-height: 56px;font-size:0px;margin: 0 auto;text-align: left;padding-top:24px;
    display: flex;
    align-items: center;
    position: relative;
}
.MessageList .con .btns button{
    width:60px;height:32px;padding: 0px;font-size:12px;border:none;border-radius: 2px;outline: none;background-color:#e6e8eb;color:rgba(0,0,0,0.65);margin-right: 16px;cursor: pointer;
}
.MessageList .con .btns button:last-child{
    margin-right: 0px;
}
.MessageList .con .btns button.sel{
    background-color:rgba(24, 144, 255, 1);color:#fff;
}
.MessageList .list{
    width:900px;height:calc(100% - 80px);overflow: hidden;margin: 0 auto;
}
.MessageList .list ul{
    width:calc(100% + 17px);height:100%;overflow-x:hidden;overflow-y:scroll;
}
.MessageList .list ul li{
    width:100%;
    height:56px;
    line-height: 56px;
    text-align: left;
    cursor:pointer;
}
.MessageList .list ul li:hover{
    background-color:rgba(0,0,0,0.02);
}
.MessageList .list ul li.sel{
    background-color:rgba(0,0,0,0.04);
}
.MessageList .list ul li:hover{
    background-color:rgba(24, 144, 255, 0.1);
}
.MessageList .list ul li span{
    display:block;
    float:left;
    line-height: 20px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}
.MessageList .list ul li .IsUnRead{
    width:6px;
    height:6px;
    border-radius: 10px;
    background-color:rgba(24, 144, 255, 1);
    margin:0 7px 0px 0px;
}

/* 什么玩意儿 */
._css-msgitem-content {
    width: calc(100% - 180px - 29px);
    height: 40px;
    margin-right: 8px;
    display: flex !important;
    align-items: center;
}
.MessageList .list ul li .CreateTime{
    width:150px;height:20px;color:rgba(0,0,0,0.25);
}
.MessageList .Page{
    width:100%;height:80px;background-color:#fff; position: relative;
}
.MessageList .Page .PgCss{
    position: absolute; left:50%;top:50%;transform: translate(-50%,-50%);
}
</style>