<template>
    <div data-debug="MessageList_vue_line2" class="MessageList">
        <div class="head">
            <button class="back" @click="$emit('CloseList')">
                <div class="icon-suggested-close-fill _css-backtolist-icon"></div>
                <div class="_css-backtolist-text">返回</div>
            </button>
            <ul class="options">
                <li v-for="(item, idx) in msgModules" :key="idx" :class="{ sel: m_moduleSign == item.Module }"
                    @click.stop="onClickMsgModule(item)">
                    {{ item.Module }}<i v-if="item.UnReadQuantity">{{ item.UnReadQuantity }}</i>
                </li>
            </ul>
        </div>
        <div class="con" id="id_middle_content">
            <div class="list" v-loading="islistloading" element-loading-text="加载中">
                <div class="btns">
                    <el-tooltip popper-class="css-no-triangle" effect="dark" content="全部设为已读" placement="right"
                        v-if="currentModuleMsgListLen">
                        <div @click.stop="setallread" class="_css-setread-btn">
                            <div class="icon-suggested-check_circle _css-setread-btnin">
                            </div>
                        </div>
                    </el-tooltip>
                </div>
                <ul>
                    <li v-for="(item, idx) in currentModuleMsgList" class="_css-msgitem" :key="idx">
                        <span class="IsUnRead"
                            :style="{ backgroundColor: !item.HasRead ? 'rgba(24, 144, 255, 1)' : 'transparent' }"></span>

                        <span class="t _css-msgitem-content">
                            <template v-if="item.Module === '流程'">
                                <el-tooltip v-if="item.Content.length > 40" effect="dark" :content="item.ContentModified"
                                    placement="top">
                                    <span>{{ item.ContentModified }}</span>
                                </el-tooltip>
                                <span v-else>{{ item.ContentModified }}</span>
                            </template>
                            <template v-else>
                                <el-tooltip v-if="item.Content.length > 40" effect="dark" :content="item.Content"
                                    placement="top">
                                    <span>{{ item.Content }}</span>
                                </el-tooltip>
                                <span v-else>{{ item.Content }}</span>
                            </template>
                        </span>

                        <span class="CreateTime">{{ item.CreateTime ? item.CreateTime.substr(0, 16) : '' }}</span>
                        <el-tooltip popper-class="css-no-triangle" effect="dark" content="设为已读" placement="right"
                            v-if="!item.HasRead">
                            <div @click.stop="setItemsRead([item])" class="_css-setread-btn">
                                <div class="icon-suggested-check_circle _css-setread-btnin">
                                </div>
                            </div>
                        </el-tooltip>
                    </li>
                </ul>
            </div>
        </div>
        <div class="Page">
            <page-object class="PgCss" @changePageInfo='changePageInfo' :PageIndex.sync="PageIndex" :PageNum="PageNum"
                :PageTotal="currentModuleMsgListLen" :PageSizes="PageSizes">
            </page-object>
        </div>
    </div>
</template>
<script>
import pageObject from '@/components/CompsCustom/PageObject'
const regForStr1 = /[,，]\s*打开http(?:s)?:\/\/.+$/i
export default {
    name: 'MessageList2',
    components: {
        pageObject,
    },
    data() {
        return {
            PageIndex: 1,
            PageNum: 15,
            m_moduleSign: "站内信",
            m_itemTypeSign: "-1",
            msgInfo: {},
            islistloading: false,
            PageSizes: [15, 20, 30, 50]
        };
    },
    computed: {
        msgModules() {
            if (this.msgInfo && this.msgInfo.MessageModuleInfos && this.msgInfo.MessageModuleInfos.length) {
                return this.msgInfo.MessageModuleInfos
            } else {
                return []
            }
        },
        allModuleDataMap() {
            const result = {};
            if (this.msgInfo && this.msgInfo.List) {
                this.msgInfo.List.forEach((item) => {
                    const module = item.Module;
                    if (module === "流程") {
                        item.ContentModified = item.Content.replace(regForStr1, '')
                    }
                    if (!result[module]) {
                        result[module] = [item];
                    } else {
                        result[module].push(item);
                    }
                });
            }
            return result;
        },
        currentModuleMsgList() {
            const cml = this.allModuleDataMap[this.m_moduleSign]
            if (cml && cml.length) {
                const { PageIndex, PageNum } = this
                return cml.slice((PageIndex - 1) * PageNum, PageIndex * PageNum)
            } else {
                return []
            }
        },
        currentModuleMsgListLen() {
            const cml = this.allModuleDataMap[this.m_moduleSign]
            if (cml && cml.length) {
                return cml.length
            } else {
                return 0
            }
        }
    },
    methods: {
        // 响应点击消息Module
        onClickMsgModule(msgModule) {
            this.PageIndex = 1;
            this.evt_moduleOrTypeChange(msgModule.Module, "-1");
        },

        // 获取消息
        setMessageInfo(args) {
            const _this = this
            _this.msgInfo = {}
            if (!_this.$staticmethod.isObject(args)) {
                args = { Module: "站内信", Type: -1 } // 目前测试来看好像只要Module有值就行
            }
            const token = _this.$staticmethod.Get("Token")
            const queryString = _this.$qs.stringify(args)
	        if(!token){return}

            const url = `${window.bim_config.webserverurl}/api/User/Message/List?${queryString}&Token=${token}`
            _this.$axios
                .get(url)
                .then((x) => {
                    if (x.status == 200) {
                        if (x.data.Ret > 0) {
                            _this.msgInfo = x.data.Data || null
                          console.log('msgInfo', _this.msgInfo)
                          if(_this.msgInfo.List.length > 0) {
                            const toRemove = ['质量安全', '质量管理', '安全管理', '安全', '质量', '问题', '档案管理'];
                            const filterList = _this.msgInfo.List.filter(item => !toRemove.includes(item.Module));
                            console.log('filterList', JSON.stringify(filterList))
                            let bool = false
                            filterList.findIndex(item => item.HasRead === false) > -1 ? bool = true : bool = false;
                            this.$emit('updateDotStatus', bool)
                          }
                        } else {
                            // _this.$message.error(x.data.Msg)
                            console.error(x.data.Msg)
                        }
                    } else {
                        console.error(x)
                    }
                })
                .catch((x) => {
                    console.error(x)
                });
        },

        // 全部设为已读
        setallread() {
            const _this = this;
            const msgListNotRead = this.currentModuleMsgList.filter(item => !item.HasRead)
            if (msgListNotRead.length == 0) {
                _this.$message.warning("当前不存在未读消息");
                return;
            }
            _this
                .$confirm("确定全部置为已读？", "操作确认", {
                    confirmButtonText: "确定",
                    cancelButtonText: "取消",
                    type: "warning",
                })
                .then((x) => {
                    this.setItemsRead(msgListNotRead);
                })
                .catch((x) => { });
        },

        setItemsRead(items) {
            const _this = this;
            _this.islistloading = true;
            // 设为已读
            _this
                .$axios({
                    url: `${window.bim_config.webserverurl
                        }/api/User/Message/Read?Token=${_this.$staticmethod.Get(
                            "Token"
                        )}`,
                    method: "post",
                    data: items.map((item) => item.MessageId),
                })
                .then((x) => {
                    if (x.status == 200) {
                        if (x.data.Ret > 0) {
                            _this.$message.success("操作成功");
                            _this.islistloading = false;
                            this.setMessageInfo({ Module: this.m_moduleSign, Type: -1 })
                        } else {
                            console.error(x.data.Msg);
                            _this.islistloading = false;
                        }
                    } else {
                        console.error(x);
                        _this.islistloading = false;
                    }
                })
                .catch((x) => {
                    console.error(x);
                    _this.islistloading = false;
                    // _this.$emit("do_refresh_from_beusing");
                });
        },

        // 响应分页信息改变事件
        changePageInfo(i, n) {
            const _this = this;
            _this.PageIndex = i;
            _this.PageNum = n;
        },

        // 点击功能模块或消息类型（全部 新增 更新 删除）时调用
        // 参数 buttonSign: 站内信 问题追踪 项目文档 项目流程
        // 参数 fucSign: 全部 新增 更新 删除
        // -------------------------------
        evt_moduleOrTypeChange(buttonSign, fucSign) {

            this.m_moduleSign = buttonSign;
            this.m_itemTypeSign = fucSign;

            // 截取数据，得到 => PageListData
            // -----------------------------
            this.changePageInfo(this.PageIndex, this.PageNum);
        },
        handleUpdateMsg() {
            const msgModuleFromSR = JSON.parse(sessionStorage.getItem("moduleFromSignalR"))
            if (msgModuleFromSR && msgModuleFromSR !== this.m_moduleSign) {
                this.m_moduleSign = msgModuleFromSR
            }
            this.setMessageInfo({
                Module: this.m_moduleSign,
                Type: -1
            })
            this.evt_moduleOrTypeChange(this.m_moduleSign, "-1");
        }
    },
    created() {
        this.PageIndex = 1
        this.PageNum = this.PageSizes[0]
        this.setMessageInfo()
        this.$Bus.$on('UpdateMsg', this.handleUpdateMsg)// 注册更新消息事件
    },
    beforeDestroy() {
        this.$Bus.$off('UpdateMsg', this.handleUpdateMsg)
    },
}
</script>
<style scoped>
._css-join-btn {
    padding: 2px 6px 2px 6px;
    line-height: 20px;
    border: 1px solid transparent;
    background-color: #1890FF;
    color: #fff;
    border-radius: 4px;
    text-align: center;
    box-sizing: border-box;
    cursor: pointer;
    opacity: 0.8;
}

._css-join-btn:hover {
    opacity: 1;
}

._css-absolute {
    position: absolute;
}

._css-msgitem {
    display: flex;
    align-items: center;
    position: relative;
}

._css-setread-btn {
    height: 24px;
    width: 24px;
    right: 14px;
    display: flex;
    align-items: center;
    justify-content: space-around;
    color: rgba(0, 0, 0, 0.45);
    cursor: pointer;
}

._css-setread-btn:hover {
    background-color: rgba(24, 144, 255, 0.1);
    color: rgba(24, 144, 255, 1);
}

._css-backtolist-icon {
    height: 20px;
    width: 20px;
    margin-left: 8px;
    font-size: 20px;
}

._css-backtolist-text {
    line-height: 32px;
    height: 32px;
}

ul,
li {
    padding: 0px;
    margin: 0px;
}

.MessageList {
    width: 100%;
    height: 100%;
    background-color: #fff;
    position: absolute;
    left: 0px;
    top: 0px;
    z-index: 999;
}

.MessageList .head {
    width: 100%;
    height: 100px;
    background-color: #fff;
    box-shadow: 0px 2px 2px #e0e0e0;
    position: relative;
}

.MessageList .head .back {
    width: 90px;
    height: 32px;
    position: absolute;
    left: 20px;
    top: 20px;
    border: none;
    outline: none;
    background: none;
    cursor: pointer;
    border-radius: 2px;
    padding: 0px 0px 0px 15px;
    display: flex;
    align-items: center;
}

.MessageList .head .back:hover {
    background-color: #f0f2f5;
}

.MessageList .head .options {
    width: 100%;
    height: 40px;
    font-size: 0px;
    position: absolute;
    bottom: 0px;
    left: 0px;
    line-height: 40px;
}

.MessageList .head .options li {
    width: 110px;
    height: 40px;
    display: inline-block;
    font-size: 14px;
    position: relative;
    cursor: pointer;
}

.MessageList .head .options li.sel {
    height: 38px;
    border-bottom: 2px solid rgba(24, 144, 255, 1);
}

.MessageList .head .options li i {
    display: block;
    position: absolute;
    right: 10px;
    top: 13px;
    width: 14px;
    height: 14px;
    line-height: 14px;
    text-align: center;
    font-style: normal;
    color: #fff;
    background-color: rgba(245, 34, 45, 1);
    font-size: 12px;
    border-radius: 2px;
}

.MessageList .con {
    width: 100%;
    height: calc(100% - 180px);
    background-color: #f0f2f5;
}

.MessageList .con .btns {
    width: 900px;
    height: 56px;
    line-height: 56px;
    font-size: 0px;
    margin: 0 auto;
    text-align: left;
    padding-top: 24px;
    display: flex;
    align-items: center;
    position: relative;

    justify-content: flex-end;
    padding-right: 14px;
    box-sizing: border-box;
}

.MessageList .con .btns button {
    width: 60px;
    height: 32px;
    padding: 0px;
    font-size: 12px;
    border: none;
    border-radius: 2px;
    outline: none;
    background-color: #e6e8eb;
    color: rgba(0, 0, 0, 0.65);
    margin-right: 16px;
    cursor: pointer;
}

.MessageList .con .btns button:last-child {
    margin-right: 0px;
}

.MessageList .con .btns button.sel {
    background-color: rgba(24, 144, 255, 1);
    color: #fff;
}

.MessageList .list {
    width: 900px;
    height: calc(100% - 40px);
    overflow: hidden;
    margin: 0 auto;
}

.MessageList .list ul {
    position: relative;
    width: calc(100% + 17px);
    height: 100%;
    overflow-x: hidden;
    overflow-y: scroll;
}

.MessageList .list ul li {
    width: 100%;
    height: 56px;
    line-height: 56px;
    text-align: left;
    cursor: pointer;
}

.MessageList .list ul li:hover {
    background-color: rgba(0, 0, 0, 0.02);
}

.MessageList .list ul li.sel {
    background-color: rgba(0, 0, 0, 0.04);
}

.MessageList .list ul li:hover {
    background-color: rgba(24, 144, 255, 0.1);
}

.MessageList .list ul li span {
    display: block;
    float: left;
    line-height: 20px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.MessageList .list ul li .IsUnRead {
    width: 6px;
    height: 6px;
    border-radius: 10px;
    background-color: rgba(24, 144, 255, 1);
    margin: 0 7px 0px 0px;
}

/* 什么玩意儿 */
._css-msgitem-content {
    width: calc(100% - 180px - 29px);
    height: 40px;
    margin-right: 8px;
    display: flex !important;
    align-items: center;
}

.MessageList .list ul li .CreateTime {
    width: 150px;
    height: 20px;
    color: rgba(0, 0, 0, 0.25);
}

.MessageList .Page {
    width: 100%;
    height: 80px;
    background-color: #fff;
    position: relative;
}

.MessageList .Page .PgCss {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
}
</style>
