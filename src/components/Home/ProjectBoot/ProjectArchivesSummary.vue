<template>
	<div class="_css-materials-all">
		<div class="btn-initiating-header">{{ headerTitleText }}</div>
		<div class="_css-bottomarea">
			<div class="left-content" ref="lefttree" v-if="proTreeIn">
				<!-- <div class="_css-materialtypelist-head">
					<span class="_css-materhead-text">档案管理</span>
				</div> -->
				<div class="project-select-wrapper" @click.stop="clickSelectProject">
					<i class="css-ml8 icon-new-dangan"></i>
					<el-tooltip class="tooltip" effect="dark" :content="this.projectName" placement="top-start">
						<span class="project-name">{{ this.projectName }}</span>
					</el-tooltip>
					<img class="project-select-icon" src="../../../assets/images/DocumentSummary/select-project-icon.png" alt="">
				</div>

				<el-tree
					:data="treeData"
					node-key="Id"
					ref="tree"
					class="_css-customstyle"
					:highlight-current="true"
					:auto-expand-parent="true"
					default-expand-all
					:props="defaultProps"
					:expand-on-click-node="false"
					:default-checked-keys="defaultCheckedKeys"
					@current-change="treefunc_current_change"
				>
					<span
						class="css-fc _css-treenode-content"
						slot-scope="{ node, data }"
					>
						<i
							class="css-mr4 css-fs16 css-fs18 css-fc css-jcsa"
							:class="
								data.HasChildren ? 'icon-new-tree-last' : 'icon-new-tree'
							"
						></i>
						<span :title="node.label" class="css-ml4 _css-treenodelabel">
							<el-tooltip
								popper-class="css-no-triangle"
								v-if="data.CategoryName.length > 12"
								effect="dark"
								:content="data.CategoryName"
								placement="top"
							>
								<div class="_css-treenodellabelname overflow-point">
									{{ data.CategoryName }}
								</div>
							</el-tooltip>
							<div class="_css-treenodellabelname" v-else>
								{{ data.CategoryName }}
							</div>
							<div
								@click.stop="func_tree_showmenu($event, data)"
								:class="data.ArchivesAuth.Export ? 'c007AFF' : '_css-dis'"
								class="_css-treenode-menubtn icon-interface-download-fill"
							></div>
						</span>
					</span>
				</el-tree>
			</div>
			<div class="_css-choose-switch-parent">
				<div class="_css-choose-switch" @click.stop="chooseCloseOrOpen">
					<i
						class="pro-in-out"
						:class="tableClose ? 'pro-in-out' : 'p-out'"
					></i>
				</div>
			</div>
			<div class="content-right" :class="proTreeIn ? '' : 'width100'">
				<div class="project-container" v-if="isShowProject">
           <el-input v-model="searchProjectValue" placeholder="输入项目名称" prefix-icon="el-icon-search" @change="getProjectList"></el-input>
            <ul>
              <li
								v-for="(item,index) in projectList" @click.stop="clickProjectItem(index,item)"
								:key="index"
								:class="item.selected ? 'selected': 'normal'"
								>
								<i class="css-ml8 icon-new-dangan"></i>

                <!-- <img class="icon" src="../../../assets/images/DocumentSummary/project-icon.png" alt=""> -->
                <el-tooltip class="tooltip" effect="dark" :content="item.ProjectName" placement="top-start">
                  <span>{{ item.ProjectName }}</span>
                </el-tooltip>
                <img class="right-icon" src="../../../assets/images/DocumentSummary/project-select-icon.png" alt="" v-if="item.selected">
              </li>
            </ul>
            <div class="btn-wrapper">
              <span class="confirm" @click="confirmProjectSelect">确定</span>
              <span class="cancel" @click="cancelProjectSelect">取消</span>
            </div>
        </div>
				<div class="_css-materialtypetab-head">
					<div class="right-input">
						<el-input
							v-model="inputVal"
							placeholder="请输入档案关键字"
							@input="searchName"
						></el-input>
						<i class="icon-interface-search"></i>
					</div>
				</div>
				<div class="_css-materialtypetab-body">
				  <el-table
						element-loading-text="数据加载中..."
						element-loading-spinner="el-icon-loading"
						element-loading-background="rgba(0, 0, 0, 0)"
						ref="multipleTable"
						highlight-current-row
						:border="true"
						:stripe="false"
						use-virtual
						height="70%"
						:data="tableData"
						style="width: 100%"
						:default-sort="{ prop: 'date', order: 'descending' }"
						class="_css-table-ele css-scroll _css-customstyle _css-qualitystyle"
						:header-cell-style="{ 'background-color': 'transparent' }"
						:row-height="rowHeight"
					>
						<el-table-column
							type="index"
							class-name="center-text"
							align="center"
							label="序号"
							width="50"
						></el-table-column>
            <el-table-column property="ArchivesName" label="档案名称" minWidth="150">
							<template slot-scope="scope">
								<el-tooltip class="item" v-if="scope.row.ArchivesName.length > 24" popper-class="tooltip-model-hover"  effect="dark" :content="scope.row.ArchivesName" placement="top-start">
									<div class="overflow-point _css-itemname">{{ scope.row.ArchivesName }}</div>
								</el-tooltip>
								<div v-else class="_css-itemname">{{ scope.row.ArchivesName || '-' }}</div>
							</template>
						</el-table-column>
						<el-table-column property="ArchivalDepartment" label="归档部门" width="160">
              <template slot-scope="scope">
								<div class="normal-font normal-center">{{ scope.row.ArchivalDepartment || '-' }}</div>
							</template>
            </el-table-column>
						<el-table-column property="ArchivesNumber" label="档案份数" width="100">
              <template slot-scope="scope">
								<div class="normal-font normal-center">{{ scope.row.ArchivesNumber || '-' }}</div>
							</template>
            </el-table-column>
						<el-table-column property="CreateTime" label="申请时间" width="180">
              <template slot-scope="scope">
								<div class="normal-font normal-center">{{ scope.row.CreateTime || '-' }}</div>
							</template>
            </el-table-column>
            <el-table-column property="ArchivalDescription" label="档案说明" minWidth="150">
							<template slot-scope="scope">
								<el-tooltip class="item" v-if="scope.row.ArchivalDescription.length > 10" popper-class="tooltip-model-hover"  effect="dark" :content="scope.row.ArchivalDescription" placement="top-start">
									<div class="overflow-point normal-font">{{ scope.row.ArchivalDescription || '-' }}</div>
								</el-tooltip>
								<div v-else class="normal-font">{{ scope.row.ArchivalDescription  || '-'}}</div>
							</template>
						</el-table-column>
						<el-table-column property="ArchivedUserId" label="归档人" width="140">
              <template slot-scope="scope">
								<div class="normal-font normal-center">{{ scope.row.ArchivedUserName || '-' }}</div>
							</template>
            </el-table-column>
						<el-table-column property="ArchivedTime" label="归档时间" width="180">
              <template slot-scope="scope">
								<div class="normal-font normal-center">{{ scope.row.ArchivedTime || '-' }}</div>
							</template>
            </el-table-column>
						<el-table-column
              property="Status"
              label="当前节点"
              width="120"
            >
              <template slot="header">
                <div class="_css-table-title _css-table-title-hover" @click.stop="handleHeaderClick($event)">
                  <span class="_css-dataitemcode">当前节点</span>
                  <i
                    ref="materialcode"
                    class="_css-table-header-icon-el"
                    :class="statusDialogShow?'el-icon-caret-bottom':'el-icon-caret-top'"
                  ></i>
                </div>
              </template>
              <template slot-scope="scope">
								<div class="normal-font normal-center" :class="scope.row.Status == 3 ? 'c0C800C' : ''">{{ scope.row.Status | filterStatus }}</div>
							</template>
            </el-table-column>
            <el-table-column
							:resizable="true"
							class="_css-col-relmodel _css-celllongcolumn"
							label="操作"
							prop="bhas_relexam"
							width="100"
						>
							<template slot-scope="scope">
								<div class="_css-costitem _css-btnsctn normal-center" >
									<div
                    v-if="scope.row.HasApprovalAuth"
										@click="submitAudit(scope.row)"
										class="_css-btnimport _css-innerbtn c007AFF css-cp"
										:class="scope.row.HasApprovalAuth ? '' : 'not-allow'"
									>
										<div>处理</div>
									</div>
									<div
                    v-if="!scope.row.HasApprovalAuth"
										@click="submitFillIn(scope.row)"
										class="_css-btnimport _css-innerbtn c007AFF css-cp"
										:class="
											!scope.row.HasApprovalAuth
												? ''
												: 'not-allow'
										"
									>
										<div>查看</div>
									</div>
								</div>
							</template>
						</el-table-column>

					</el-table>
					<el-pagination
						@size-change="handleSizeChange"
						@current-change="handleCurrentPaginationChange"
						:current-page="pageNum"
						:page-sizes="[20, 50, 100, 200]"
						:page-size="pageSize"
						layout="total, sizes, prev, pager, next, jumper"
						:total="paginationPageLength"
					>
					</el-pagination>
				</div>
			</div>

      <CompsArchivesDialog
        v-if="archivesDialogShow"
        :selectTreeData="selectTreeData"
        :processingType="processingType"
        :archivesCode="archivesCode"
        :detailId="detailId"
        @close="archivesDialogClose"
				@previewFile="previewFile"
      ></CompsArchivesDialog>
      <div
				class="first-dialog status-dialog"
				:style="statusDialogStyle()"
				v-if="statusDialogShow"
			>
        <div class="check-box">
          <el-checkbox-group v-model="tableStatusCheck" @change="handletableStatusChange">
              <div class="group" v-for="tableitem in tableStatusArr" :key="tableitem.value">
                  <el-checkbox :label="tableitem.value" >
                      <span class="name-text">{{ tableitem.text }}</span>
                  </el-checkbox>
              </div>
          </el-checkbox-group>
        </div>
        <div class="set-btn">
          <div class="check-btn css-cp" @click.stop="resetTableStatusArr">重置</div>
          <div class="check-btn css-cp bg" @click.stop="searchTableStatusArr">筛选</div>
        </div>
			</div>
		</div>
	</div>
</template>
<script>
import CompsArchivesDialog from "@/components/CompsArchives/CompsArchivesDialog";
import {getProjectName} from "../../../utils/projectName";
export default {
	components: {
		CompsArchivesDialog,
	},
	name: "ProjectArchivesSummary",
	data() {
		return {
			token: '',
			projectName:'',
      projectList:[],
      searchProjectValue: '',
      isShowProject: false,

			headerTitleText: "", // header显示文字
			organizeId: "",
			proTreeIn: true,
			tableClose: false,
			treeData: [],
      firstTreeDataId: '', // 记录项目名称的id
			defaultProps: {
				children: "Children",
				label: "CategoryName",
			},
			defaultCheckedKeys: [],
			searchTableName: "",
			inputVal: "",
			selectTreeData: {
        Id: '0',
				CategoryName: '全部',
				CategoryCode: '0',
        HasChildren: true,
      },
      tableData: [],
      tableStatusCheck: [0,1,2,3],
      tableStatusArr: [
        //  0  待提交 1  待审核 2  待归档  3  已归档
        {
          text: '待提交',
          value: 0,
        },
        {
          text: '待审核',
          value: 1,
        },
        {
          text: '待归档',
          value: 2,
        },
        {
          text: '已归档',
          value: 3,
        },
      ],
			rowHeight: 40,
			pageNum: 1, // 第几页
			pageSize: 20, // 每页多少条
			paginationPageLength: 0, // 总条数
      statusDialogShow: false,
      status_contextpos2: {
				top: 0,
				left: 0,
			},
      processingType: '', // 当前处理的类型（新增0、被驳回待提交1、审批2、驳回3、归档4、退回查看5、归档查看6、详情查看7）
      archivesDialogShow: false,
      archivesCode: '', // 获取档案编码、传值给子组件
      detailId: '', // 当前选中的表格数据的ID 传值给子组件
		};
	},
	watch: {},
	created() {
		this.projectName = getProjectName()
		this.token = this.$staticmethod.Get("Token");
		this.organizeId = this.$staticmethod._Get("organizeId");
		this.headerTitleText = this.$staticmethod._Get("menuText") || "";
	},
	async mounted() {
		const urlChangedBy = this.$staticmethod._Get("UrlChangedBy");
		const msgRelated = sessionStorage.getItem("MsgRelatedData");
		if(urlChangedBy === 'Msg-档案' && msgRelated) {
			const msgRelatedData = JSON.parse(msgRelated);
			let _OrganizeId = this.$staticmethod._Get("_OrganizeId");
			const res = await this.$axios.get(`${window.bim_config.webserverurl}/api/User/Project/QueryProjectPaged?organizeId=${_OrganizeId}&token=${this.token}&pageNum=1&pageSize=3000&keyword=${this.searchProjectValue}&sort=`);
			const projectList = res.data.Data.rows;
			const index = projectList.findIndex((item) => item.ProjectId === msgRelatedData.OrganizeId);
			if (index !== -1) {
				const project = projectList[index];
				this.organizeId = project.ProjectId;
				this.projectName = project.ProjectName;
				await this.GetCategoryTree();
        this.handleRoueChange();
			}
		} else {
			this.GetCategoryTree();
		}
	},
	filters: {
    filterStatus(number){
      let text = '-'
			switch(number) {
				case 0:
					text = '待提交';
					break;
				case 1:
					text = '待审核';
					break;
				case 2:
					text = '待归档';
					break;
			  	case 3:
					text = '已归档';
					break;
			}
			return text
    },
  },
	computed: {},
	methods: {
		previewFile(row){
			let FileExtension = row.AttachmentName.substring(row.AttachmentName.lastIndexOf("."));
			let doc = {
					FileId: row.AttachmentId,
					FileName: row.AttachmentName,
					FileExtension: FileExtension,
			};
			var url = `${window.bim_config.webserverurl}/api/v1/attach/preview?id=${doc.FileId}&Token=${this.$staticmethod.Get("Token")}`;
			// 根据扩展名获取在线浏览地址
			var url_iframe_all;
			if (doc.FileName.toLowerCase().indexOf(".dwg") > 0) {
					this.$emit("set_projectboot_extdata", "_docviewtype", "dwg");
					url_iframe_all = `${this.$configjson.dwgurl}/Home/Index2?dwgurlcfg=${encodeURIComponent(url)}&name=${doc.FileName}`;
			} else {
					this.$emit("set_projectboot_extdata","_docviewtype","office");
					url_iframe_all = this.$staticmethod.getHuangNewcomputeViewUrl(
							url,
							doc.FileName,
							doc.FileExtension
					);
			}
			this.$emit("set_projectboot_extdata", "_show_idocview", true);
			this.$emit("set_projectboot_extdata","_idocviewurl",url_iframe_all);
		},
		handleRoueChange() {
			const urlChangedBy = this.$staticmethod._Get("UrlChangedBy")
			if(urlChangedBy === 'Msg-档案') {
				this.$Bus.$emit('UnFoldMenuAndSelect','DAHZ') // ProjectBoot.vue中注册该事件:更新左侧菜单展开节点并选择菜单
				const a = sessionStorage.getItem("MsgRelatedData");
				const msgRelatedData = JSON.parse(sessionStorage.getItem("MsgRelatedData")) // 消息关联的数据
				const refProgressPlan = this.$refs.tree
				if(refProgressPlan && msgRelatedData) {
					this.$nextTick(() => {
						this.$refs.tree.setCurrentKey(msgRelatedData.CategoryId);
						let category = this.$refs.tree.getNode(msgRelatedData.CategoryId).data;
						this.treefunc_current_change(category);
					})
				}
				this.$staticmethod._Set("MsgRelatedData","")
				this.$staticmethod._Set("UrlChangedBy","")  // 处理完成重置
			}
	  },
		_stopPropagation(ev) {
			ev && ev.stopPropagation && ev.stopPropagation();
		},
    // 获取树结构
		async GetCategoryTree(type) {
			const checkedKeys = this.$refs.tree.getCurrentKey();
			let params = {
				organizeId: this.organizeId,
				parentId: 0,
				verifyPermissions: false
			};
      let res = await this.$api.GetCategoryTree(params);
			if(res.Ret == 1 && res.Data){
				this.treeData = res.Data
				res.Data.length > 0 ? this.firstTreeDataId = res.Data[0].Id : this.firstTreeDataId = ''
				if(type == 1){
					this.selectTreeData = res.Data[0];
				}
				if(res.Data && res.Data.length > 0 && res.Data[0].Children.length > 0){
					this.selectTreeData = res.Data[0].Children[0];
					const urlChangedBy = this.$staticmethod._Get("UrlChangedBy");
					if(urlChangedBy !== 'Msg-档案') {
						this.getTableList()
					}
				}else{
					this.tableData = []
					this.paginationPageLength = 0
				}
				this.$nextTick(() => {
					this.$refs.tree.setCurrentKey(checkedKeys);
				});
			}
		},
    archivesExport(isExportAll){
      let categoryId = isExportAll ? this.firstTreeDataId : this.selectTreeData.Id;
      let d_url = `${window.bim_config.webserverurl}/api/Archives/Archives/Export?organizeId=${this.organizeId}&categoryId=${categoryId}&isExportAll=${isExportAll}&token=${this.$staticmethod.Get("Token")}&verifyPermissions=false`
      window.location.href = d_url
    },
    // 新增树结构数据
		async postCategoryCreate() {
			let params = {
				OrganizeId: this.organizeId,
				CategoryName: this.editDialog.CategoryName,
				CategoryCode: this.editDialog.CategoryCode,
				ParentId: this.selectTreeData.Id || this.firstTreeDataId,
			};
			let res = await this.$api.postCategoryCreate(params);
      if(res.Ret == 1){
        this.$message.success(res.Msg)
        this.status_showedit = false;
        this.GetCategoryTree();
      }
		},

		chooseCloseOrOpen() {
			this.proTreeIn = !this.proTreeIn;
		},
		treefunc_current_change(data) {
      console.log(data,'---tree-data')
			this.inputVal = this.searchTableName = "";
			this.selectTreeData = data;
			this.getTableList();
		},
		searchName(val) {
			this.searchTableName = val;
			this.getTableList();
		},
		func_tree_showmenu(ev, data) {
			if(!data.ArchivesAuth.Export){
				this.$$message.warning('您无导出权限');
				return
			}
      if(data.CategoryCode == '0'){
        this.archivesExport(true)
      }else{
				this.archivesExport(false)
      }
		},
    statusDialogStyle() {
			let _s = {};
			_s["left"] = this.status_contextpos2.left + "px";
			_s["top"] = this.status_contextpos2.top + "px";
			return _s;
		},
    getTableList(){
      let _andStatus = this.$qs.stringify({ Status: this.tableStatusCheck }, { arrayFormat: 'repeat' })
      let url = `${window.bim_config.webserverurl}/api/Archives/Archives/Paged?OrganizeId=${this.organizeId}&PageNum=${this.pageNum}&PageSize=${this.pageSize}&CategoryId=${this.selectTreeData.Id}&IsSearchAll=${this.selectTreeData.HasChildren}&KeyWord=${this.searchTableName}&${_andStatus}&token=${this.$staticmethod.Get("Token")}`
      this.$axios
        .get(url)
        .then(x=>{
          if (x.status == 200) {
						if (x.data.Ret == 1 ) {
							this.tableData = x.data.Data.Data;
              this.paginationPageLength = x.data.Data.Total
						}else{
							this.tableData = []
							this.paginationPageLength = 0
						}
					}else{
						this.tableData = []
						this.paginationPageLength = 0
            this.$message.error(x.data.Msg)
          }
        }).catch(err=>{

        })
    },
    // 筛选
    handletableStatusChange(value){
      this.tableStatusCheck = value
    },
    handleSizeChange(val) {
			this.pageSize = val;
			this.getTableList();
		},
		handleCurrentPaginationChange(val) {
			this.pageNum = val;
			this.getTableList();
		},

    handleHeaderClick(event){
      console.log(event)
        this.status_contextpos2.left = event.clientX - 240;
        this.status_contextpos2.top = event.clientY - 30;
        this.statusDialogShow = !this.statusDialogShow;
    },
    archivesDialogClose(){
      this.archivesDialogShow=false;
      this.getTableList()
    },
    // 点击处理
    submitAudit(row){
      // 处理   待审核   审核，点击展示详情，除了档案位置可以编辑，其他的都不可以编辑，
      this.detailId = row.Id;

      // （新增0、被驳回待提交1、审批2、驳回3、归档4、退回查看5、归档查看6、详情查看7）
			switch(row.Status) {
				case 0:
					// '待提交'
          this.processingType = '1';
					break;
				case 1:
					// '待审核'
          this.processingType = '2';
					break;
				case 2:
					// '待归档'
          this.processingType = '3';
					break;
			  	case 3:
					// '已归档'
          this.processingType = '4';
					break;
			}
      this.archivesDialogShow = true;
    },
    resetTableStatusArr(){
      this.tableStatusCheck = [0, 1, 2, 3]
    },
    searchTableStatusArr(){
      this.getTableList();
      this.statusDialogShow = false;
    },
    // 点击查看
    submitFillIn(row){
      // 查看，先请求详情接口，所有的都不能编辑
      this.detailId = row.Id;
      // （新增0、被驳回待提交1、审批2、驳回3、归档4、退回查看5、归档查看6、详情查看7）
      switch(row.Status) {
				case 0:
					// '待提交'
          this.processingType = '5';
					break;
				case 1:
					// '待审核'
          this.processingType = '6';
					break;
				case 2:
					// '待归档'
          this.processingType = '6';
					break;
			  case 3:
					// '已归档'
          this.processingType = '6';
					break;
        default:
          this.processingType = '7';
					break;
			}
      this.archivesDialogShow = true;
    },
		// 选择项目
    clickSelectProject(){
			this.getProjectList()
      this.isShowProject = true;
		},
		clickProjectItem(index,item){
      // for (const projectListElement of this.projectList) {
      //   projectListElement.selected = false
      // }
			this.projectList.forEach(item=>{
				return item.selected = false
			})
			this.projectList[index].selected = true
			// console.log(this.projectList,'=this.projectList')
			this.$set(this.projectList[index],this.projectList[index].selected,true)
    },
		// 获取项目列表
		getProjectList(){
			let _OrganizeId = this.$staticmethod._Get("_OrganizeId"); // 机构ID
      this.$axios
        .get(`${window.bim_config.webserverurl}/api/User/Project/QueryProjectPaged?organizeId=${_OrganizeId}&token=${this.token}&pageNum=1&pageSize=3000&keyword=${this.searchProjectValue}&sort=`)
        .then(res=>{
          //  ProjectId
          this.projectList = res.data.Data.rows;
					for (const projectListElement of this.projectList) {
            projectListElement.selected = false
          }

					const index = this.projectList.findIndex((item) => item.ProjectId === this.organizeId);
					if (index !== -1){
						this.projectList[index].selected = true
						this.projectName = this.projectList[index].ProjectName
					}
        })
        .catch(err=>{})
    },
		// 选择项目确认
		confirmProjectSelect(){
      // 切换ProjectId 获取文档数据
      this.isShowProject = false
      const project = this.projectList.find(item => item.selected)
      this.organizeId = project.ProjectId
      this.projectName = project.ProjectName
      this.searchProjectValue = ''
			this.GetCategoryTree()
    },
		// 取消选择 关闭界面
    cancelProjectSelect(){
      this.searchProjectValue = ''
      this.isShowProject = false
    },
	},
	beforeDestroy() {},
};
</script>
<style lang="scss" scoped>
@import url("../../../assets/css/MaterialsMgrStyle.css");
.pro-in-out {
	display: inline-block;
	width: 16px;
	height: 120px;
	background-image: url(../../../assets/images/p-in.png);
	background-size: 100%;
	background-repeat: no-repeat;
}
.pro-in-out.p-out {
	background-image: url(../../../assets/images/p-out.png);
	background-repeat: no-repeat;
	background-size: 100%;
	left: 0;
}
._css-materialtypelist-head{
	height: 40px;
	// background: #EBEDF0;
}
._css-bottomarea {
	position: relative;
	display: flex;
	.left-content {
		background: #fff;
		min-width: 270px;
		max-width: 370px;
	}
	.content-right {
		position: relative;
		margin: 0 12px;
		// padding: 0 12px;
		flex: 1;
		width: calc(100% - 364px);
	}
	.content-right.width100 {
		width: calc(100% - 64px);
	}
	._css-materialtypetab-body {
		position: relative;
		height: calc(100% - 64px);
	}
}
.right-input {
	width: 230px;
	border: 1px solid #d8d8d8;
	position: absolute;
	top: 16px;
	right: 8px;
}
.right-input i {
	position: absolute;
	top: 6px;
	right: 8px;
	color: #999999;
}
.right-input /deep/ .el-input__inner {
	line-height: 32px;
}
._css-customstyle {
	height: 90% !important;
	overflow: auto;
}
.first-dialog {
	position: absolute;
	background-color: #fff;
	width: 96px;
	box-shadow: 0px 2px 4px 0px rgba(0, 38, 77, 0.15);
	border-radius: 2px;
	z-index: 5;
}

/deep/ ._css-step-tableimagearea{
  height: 100px !important;
}
/deep/ ._css-stop-tableimage{
  width: 330px;
}
._css-materialtypetab-body /deep/ ._css-qualitystyle.el-table th {
	font-size: 14px !important;
}
._css-materialtypetab-body /deep/ ._css-qualitystyle.el-table thead th .cell , ._css-materialtypetab-body /deep/ ._css-qualitystyle .center-text .cell{
	justify-content: center !important;
	padding: 0;
}
.c0C800C{
  color: #0C800C !important;
}
._css-itemname{
  font-weight: 500;
  font-size: 12px;
  color: #222222;
}
.normal-font{
  font-weight: 400;
  font-size: 12px;
  color: #222222;
}
.normal-center{
  width: 100%;
  text-align: center;
}
.status-dialog{
  width: 140px;
  // height: 160px;
  background: #FFFFFF;
  box-shadow: 0px 0px 6px 0px #E5E6EB;
  border-radius: 2px;
  .check-box{
    text-align: left;
    margin: 8px 0;
  }
  .group{
    padding-left: 8px;
    line-height: 28px;
  }
  .group:hover{
    background: rgba(0,122,255,0.05);
  }
}
.set-btn{
  display: flex;
  margin-bottom: 8px;
  justify-content: center;
  .check-btn{
    width: 48px;
    height: 24px;
    line-height: 24px;
    border-radius: 2px;
    border: 1px solid #007AFF;
    background: #fff;
    color: #007AFF;;
  }
  .check-btn.bg{
    background: #007AFF;
    color: #fff;
    margin-left: 8px;
  }
}
._css-table-header-icon-el{
  font-size: 18px;
  color: #888888;
}
.project-select-wrapper{
    cursor: pointer;
    background-color: white;
    width: 100%;
    height: 40px;
    display: flex;
    align-items: center;
    border-bottom: #E5E6EB 1px solid;
    .icon{
      margin-left: 8px;
      width: 16px;
      height: 16px;
    }
    .project-name{
      margin-left: 8px;
      height: 20px;
      font-weight: 500;
      font-size: 14px;
      color: #333333;
      /* 超出部分隐藏 */
      overflow: hidden;
      /* 不换行 */
      white-space: nowrap;
      /* 溢出用省略号代替 */
      text-overflow: ellipsis;
      /* 设置宽度，超出这个宽度就会被截断 */
      max-width: 200px;
    }
    .project-select-icon{
      margin-right: 10px;
      margin-left: auto;
      width: 16px;
      height: 16px;
    }
}
.project-container{
    width: 300px;
    background-color: white;
    border-radius: 4px;
    position: absolute;
    top: 10px;
    left: -5px;
    z-index: 999;
		box-shadow: 4px 0px 8px 0px rgba(0,0,0,0.05), inset 1px 0px 0px 0px rgba(0,0,0,0.05);
    .el-input{
      width: 280px;
      border-radius: 2px;
      border: 1px solid #D8D8D8;
      margin: 8px;
      height: 32px;
      /deep/ .el-input__inner{
        border: 0;
        height: 32px;
        line-height: 32px;
      }
    }
    ul{
      max-height: 510px;
      overflow: auto;
      li{
        margin: 0;
        cursor: pointer;
        height: 46px;
        display: flex;
        align-items: center;
        .icon{
          margin-left: 16px;
          width: 16px;
          height: 16px;
        }
        span{
          margin-left: 7px;
          font-weight: 400;
          font-size: 14px;
          /* 超出部分隐藏 */
          overflow: hidden;
          /* 不换行 */
          white-space: nowrap;
          /* 溢出用省略号代替 */
          text-overflow: ellipsis;
          /* 设置宽度，超出这个宽度就会被截断 */
          max-width: 230px;
        }
        &.normal{
          color: #222222;
          background-color: white;
        }
        &.selected{
          color: #007AFF;
          background: rgba(0,122,255,0.05);
        }
        .right-icon{
          margin-left: auto;
          margin-right: 16px;
          width: 20px;
          height: 20px;
        }
        &:hover{
          color: #007AFF;
          background: rgba(0,122,255,0.05);
        }
      }
    }
    .btn-wrapper{
      position: relative;
      margin-top: 16px;
      display: flex;
      height: 25px;
      padding-bottom: 10px;
      .confirm{
        position: absolute;
        right: 80px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        font-weight: 400;
        font-size: 12px;
        background-color:#1890FF ;
        color: white;
        width: 48px;
        height: 24px;
        border-radius: 4px;
        border: 1px solid #1890FF;
      }
      .cancel{
        position: absolute;
        right: 10px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        font-weight: 400;
        font-size: 12px;
        color: #1890FF;
        width: 48px;
        height: 24px;
        border-radius: 4px;
        border: 1px solid #1890FF;
      }
    }
}
</style>
