<template>
  <!-- 主体 -->
  <div class="_css-issueadmin-all">
    <div @click="backToIssue" >issueAdmin</div>
  </div>
  <!-- //主体 -->
</template>
<script>

export default {
  name: "IA",
  components: {   
  },
  data() {
    return {
    };
  },
  mounted() {
    var _this = this;
    window.rolevue = _this;
    _this.$emit("onmounted", "IssueAdmin");
  },
  filters: {
    
  },
  methods: {

      // 页面会刷新！
      backToIssue(){
          var _this = this;
          var _orgId = _this.$staticmethod._Get("organizeId");
          var _Token = _this.$staticmethod.Get("Token");
          var url = `${window.bim_config.hasRouterFile}/#/Home/ProjectBoot/Issue/${_orgId}/${_Token}`;
          window.location.href = url;
      }
  
  }
};
</script>
<style scoped>
</style>