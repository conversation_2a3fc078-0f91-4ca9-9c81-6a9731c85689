<template>
  <!-- 项目内页下部分 -->
  <el-container class="_css-page-body css-fc">
    <CompsInvite
      @oncancel="CompsInvite_oncancel"
      @onok="CompsInvite_onok"
      :zIndex="4"
      v-if="extdata.showinvite == true && extdata._currentisProjectManager == true"
    ></CompsInvite>

    <!-- 项目内页的右上角按钮组 -->
    <div class="_css-righttop-group" >
      <!-- 用户头像、邀请按钮、消息按钮、全屏按钮 -->
      <el-tooltip popper-class="css-no-triangle" v-if="getUserShow()" effect="dark" content="个人信息" placement="bottom">
        <div
          class="_css-currentuser-icon css-prel"
          @click.stop="toggleUserMenuListState"
        >
          <div>{{isMain !== 0 ? realName: RealNameLastName}}</div>

          <CompsCurrentUserMenuList
            v-if="isShowHeader"
            @onitemclick="itemclick"
            :toggleState="currentMenuListState"
            :isComMgr="isComMgr"
            ref="userMenuList"
          ></CompsCurrentUserMenuList>
        </div>
      </el-tooltip>
      <!-- <div
        v-if="extdata._currentisProjectManager == true"
        class="_css-invite-outer css-fc"
        @click.stop="showInvite"
      >
        <el-tooltip popper-class="css-no-triangle" effect="dark" content="邀请" placement="bottom">
          <div
            class="css-bsb css-b css-cp icon-interface-user_fill basic-btn-normal-color basic-btn-normal-size"
          ></div>
        </el-tooltip>
      </div> -->
      <div class="_css-bell-outer css-fc" v-if="getConfigBell()" @click="IsShowMessageMini=!IsShowMessageMini">
        <el-tooltip popper-class="css-no-triangle" effect="dark" content="消息提醒" placement="bottom">
          <el-badge :is-dot="hasUnreadMsg && isDot">
						<div class="css-bsb css-b css-cp icon-interface-bell basic-btn-normal-color basic-btn-normal-size css-global-bell">
						</div>
					</el-badge>
        </el-tooltip>
      </div>
      <div
      id="id_fullscreen_btn"
      class="_css-full-outer css-fc" @click.stop="changeisfull">
        <el-tooltip
          popper-class="css-no-triangle"
          effect="dark"
          :content="extdata._isfullscreen?'取消全屏':'全屏'"
          placement="bottom"
        >
          <div
            class="css-bsb css-b css-cp basic-btn-normal-color basic-btn-normal-size"
            :class="extdata._isfullscreen?'icon-arrow-fullscreen_exit':'icon-arrow-fullscreen'"
          ></div>
        </el-tooltip>
      </div>
      <!-- //用户头像、邀请按钮、消息按钮、全屏按钮 -->
    </div>
    <!-- //项目内页的右上角按钮组 -->

    <!-- 左菜单栏，右主体 -->
    <!-- 200px 宽度菜单栏 -->
    <div
    @click.stop="hideall()"
      class="css-h100 css-flexn css-bsb css-usn _css-leftmenu"
      :class="'_css-left-state_' + _homedata.leftmenustate.toString()"
    >
      <!-- <div class="_css-organize2-namearea css-bsb css-h100 css-fc">
        <div
          @click.stop="jump_to_project_list"
          class="_css-leftbackbtn css-icon20 css-b css-bsb css-cp css-pabs icon-arrow-left_outline"
        ></div>
        <div
          class="_css-organize2-namearea-text css-fs14 css-usn _css-logotext_0 _css-logo-text_ellipsis"
        >{{extdata._currentProjectName}}</div>
      </div> -->
      <div class="title-back">
        <div class="back-icon" @click.stop="jump_to_project_list">
          <i class="icon-arrow-left_outline"></i>
          返回
        </div>
        <i>|</i>
        <div class="project-name">
          {{extdata._currentProjectName}}
        </div>
      </div>

      <!-- 左边菜单部分 -->
      <div class="_css-organize2-menuarea css-miniscroll left-menu">
        <el-menu
          :default-active="defaultActive"
          class="el-menu-vertical-demo css-menu-list"
          @select="handelSelectMenuList"
        >
          <div
            class="first-div-class"
            v-for="(item,index) in menuListArr"
            :key='index'
            :index="index+1+''"
          >
            <el-submenu class="qqqq" :index="index+1+''" v-if="item.Children.length > 0">
              <template slot="title" >
                <!-- <i class="icon-bg icon-img icon-img-before" v-if="getIconClass(item.MenuIcon)" :class="item.MenuIcon"></i>
								<i class="icon-bg icon-img" v-else :class="item.MenuIcon"></i> -->
                <!-- <span class="text-margin4" :title="item.MenuCode">{{item.MenuName}}</span> -->
                <div class="title-model title-set-css">
                    <i class="icon-bg icon-img icon-img-before" v-if="getIconClass(item.MenuIcon)" :class="item.MenuIcon"></i>
								    <i class="icon-bg icon-img" v-else :class="item.MenuIcon"></i>
                    <span class="text-margin4" :title="item.MenuCode">{{ item.MenuName }}</span>
                    <div
                      class="css-fc css-jcsa model-display-hover"
                      v-if="item.MenuCode == 'MODEL' && extdata._currentisProjectManager == true"
                    >
                      <el-tooltip
                        popper-class="css-no-triangle"
                        effect="dark"
                        content="添加模型文件夹"
                        placement="bottom"
                      >
                        <i
                          class="css-fc css-fs12 css-icon12 css-cp icon-suggested-plus _css-pb-add"
                          @click.stop="_addprojectsection(item)"
                        ></i>
                      </el-tooltip>
                    </div>
                    <div
                    class="css-fc css-jcsa model-display-hover"
                    v-if="item.MenuCode == 'CJGL_1' && extdata._currentisProjectManager == true"
                  >
                    <el-tooltip
                      popper-class="css-no-triangle"
                      effect="dark"
                      content="添加场景文件夹"
                      placement="bottom"
                    >
                      <i
                        class="css-fc css-fs12 css-icon12 css-cp icon-suggested-plus _css-pb-add"
                        @click.stop="_addprojectsection(item,false)"
                      ></i>
                    </el-tooltip>
                  </div>
                  </div>
              </template>
              <div
                v-for="(itemC,indexC) in item.Children" :key='indexC'
                :index="index + 1 + '-' + indexC +1"
                 @click.native="handleSelectClick(itemC,'222',$event)"
              >
                <!-- <el-submenu :index="index+1 + '-' + indexC + 1" v-if="itemC.Children.length > 0">
                  <template slot="title">
                    <div class="title-model">
                      <span class="text-margin12" :title="itemC.MenuCode">{{ itemC.MenuName }}</span>
                      <div
                        class="css-icon40 css-fc css-jcsa"
                        v-if="itemC.MenuCode == 'MODEL' && extdata._currentisProjectManager == true"
                      >
                        <el-tooltip
                          popper-class="css-no-triangle"
                          effect="dark"
                          content="添加模型文件夹"
                          placement="bottom"
                        >
                          <i
                            class="css-fc css-fs12 css-icon12 css-cp icon-suggested-plus _css-pb-add"
                            @click.stop="_addprojectsection(itemC)"
                          ></i>
                        </el-tooltip>
                      </div>
                      <div class="_css-menu-child model-phase-list" v-if="itemC.MenuCode =='MODEL' && itemC.MenuName != '全部模型' && itemC.MenuName != '默认'">
                          <el-tooltip
                            v-if="itemC.MenuType==4 && itemC.MenuName.length > 10"
                            effect="dark"
                            class="item"
                            placement="top-start"
                            :content="itemC.MenuName"
                          >
                            <span class="text-margin38" :title="itemC.MenuCode">{{itemC.MenuName}}</span>
                          </el-tooltip>
                          <span v-else class="text-margin38">{{itemC.MenuName}}</span>
                          <span class="model-phase-number" v-if="itemC.MenuType==4">{{func_getphasenum(itemC)}}</span>
                          <div v-if="itemC.MenuType==4" class="_css-pjsection-edit-innerbtn icon-interface-list"></div>
                      </div>
                      <div v-else class="model-phase-list">
                        <el-tooltip
                          v-if="itemC.MenuType==4 && itemC.MenuName.length > 10"
                          effect="dark"
                          class="item"
                          placement="top-start"
                          :content="itemC.MenuName"
                        >
                          <span class="text-margin38" :title="itemC.MenuCode">{{itemC.MenuName}}</span>
                        </el-tooltip>
                        <span v-else class="text-margin38">{{itemC.MenuName}}</span>
                        <span v-if="itemC.MenuType==4"> {{func_getphasenum(itemC)}}</span>
                      </div>
                    </div>
                  </template>
                    <el-menu-item
                      @click.native="handleSelectClick(itemC3,'333',$event)"
                      v-for="(itemC3,indexC3) in itemC.Children" :key='indexC3'
                      :index="index+1+'-' +indexC+ 1+'-' + indexC3 + 1">
                        <div class="_css-menu-child model-phase-list" v-if="item.MenuCode == 'MODEL' && itemC3.MenuName != '全部模型' && itemC3.MenuName != '默认'">
                          <el-tooltip
                            v-if="itemC3.MenuType==4 && itemC3.MenuName.length > 10"
                            effect="dark"
                            class="item"
                            placement="top-start"
                            :content="itemC3.MenuName"
                          >
                            <span class="text-margin38" :title="itemC3.MenuCode">{{itemC3.MenuName}}</span>
                          </el-tooltip>
                          <span v-else class="text-margin38">{{itemC3.MenuName}}</span>
                          <span class="model-phase-number" v-if="itemC3.MenuType==4">{{func_getphasenum(itemC3)}}</span>
                          <div v-if="itemC3.MenuType==4" class="_css-pjsection-edit-innerbtn icon-interface-list"></div>
                        </div>
                        <div v-else class="model-phase-list">
                          <el-tooltip
                            v-if="itemC3.MenuName.length > 10"
                            effect="dark"
                            class="item"
                            placement="top-start"
                            :content="itemC3.MenuName"
                          >
                            <span class="text-margin38" :title="itemC3.MenuCode">{{itemC3.MenuName}}</span>
                          </el-tooltip>
                          <span v-else class="text-margin38">{{itemC3.MenuName}}</span>
                          <span v-if="itemC3.MenuType==4"> {{func_getphasenum(itemC3)}}</span>
                        </div>
                    </el-menu-item>
                </el-submenu> -->

                <el-menu-item @click.native="handleSelectClick(itemC,'222',$event)" :index="index+1+'-' + indexC + 1">
                  <div class="title-model">
                    <!-- <span class="text-margin12" :title="itemC.MenuCode">{{itemC.MenuName}}</span> -->
                    <div class="_css-menu-child model-phase-list" v-if="itemC.MenuName != '全部模型'">
                          <el-tooltip
                            v-if="itemC.MenuType==4 && itemC.MenuName.length > 10"
                            effect="dark"
                            class="item"
                            placement="top-start"
                            :content="itemC.MenuName"
                          >
                            <span class="text-margin12 overflow-hidden-point" :title="itemC.MenuCode">{{itemC.MenuName}}</span>
                          </el-tooltip>
                          <span v-else class="text-margin12 overflow-hidden-point">{{itemC.MenuName}}</span>
                          <span class="model-phase-number" v-if="itemC.MenuType==4">{{func_getphasenum(itemC)}}</span>
                          <div v-if="itemC.MenuType === 4 || itemC.MenuType === 6" class="_css-pjsection-edit-innerbtn icon-interface-list"></div>
                      </div>
                      <div v-else class="model-phase-list">
                        <el-tooltip
                          v-if="itemC.MenuType==4 && itemC.MenuName.length > 10"
                          effect="dark"
                          class="item"
                          placement="top-start"
                          :content="itemC.MenuName"
                        >
                          <span class="text-margin12" :title="itemC.MenuCode">{{itemC.MenuName}}</span>
                        </el-tooltip>
                        <span v-else class="text-margin12">{{itemC.MenuName}}</span>
                        <span v-if="itemC.MenuType==4"> {{func_getphasenum(itemC)}}</span>
                      </div>
                    <!-- 添加阶段 -->
                    <!-- <div
                      class="css-icon40 css-fc css-jcsa"
                      v-if="itemC.MenuCode == 'MODEL' && extdata._currentisProjectManager == true"
                    >
                      <el-tooltip
                        popper-class="css-no-triangle"
                        effect="dark"
                        content="添加模型文件夹"
                        placement="bottom"
                      >
                        <i
                          class="css-fc css-fs12 css-icon12 css-cp icon-suggested-plus _css-pb-add"
                          @click.stop="_addprojectsection(itemC)"
                        ></i>
                      </el-tooltip>
                    </div> -->

                  </div>
                </el-menu-item><!--不管有没有子级都绑定为el-menu-item:模型管理右侧页面里的树数据来源是重新调用接口获取的-->
              </div>
            </el-submenu><!--一级菜单(有子菜单)-->
            <el-menu-item :index="index+1+''" @click.native="handleSelectClick(item,'111')" v-else>
              <i class="icon-bg icon-img icon-img-before" v-if="getIconClass(item.MenuIcon)" :class="item.MenuIcon"></i>
							<i class="icon-bg icon-img" v-else :class="item.MenuIcon"></i>
              <span class="text-margin4" :title="item.MenuCode">{{item.MenuName}}</span>
            </el-menu-item><!--一级菜单(无子菜单)-->
          </div>
        </el-menu>
      </div>
      <!-- //左边菜单部分 -->
    </div>
    <div data-debug="line116" @click="extdata.addPhaseDialogShow=false" v-if="extdata.addPhaseDialogShow" class="_css-pjsection-btnlist" :style="getsectionbtnstyle()" >
      <!--<div
        v-if="ifAddPhaseBtnRender"
        class="_css-pjsection-btn _css-pjsection-btn-add"
        @click.stop="showaddpjsection(extdata.addPhaseDialogMenu)"
      >
        <div class="_css-pjsection-btnicon icon-newface-add"></div>
        <div class="_css-pjsection-btntext">添加</div>
      </div>-->
      <div
        class="_css-pjsection-btn _css-pjsection-btn-rename"
        @click.stop="showeditpjsection(extdata.addPhaseDialogMenu)"
      >
        <div class="_css-pjsection-btnicon icon-interface-edit-model"></div>
        <div class="_css-pjsection-btntext">重命名</div>
      </div>
      <div
        class="_css-pjsection-btn _css-pjsection-btn-delete"
        @click.stop="delpjsection(extdata.addPhaseDialogMenu)"
      >
        <div class="_css-pjsection-btnicon icon-interface-model-delete"></div>
        <div class="_css-pjsection-btntext">删除</div>
      </div>
    </div>
    <!-- //200px 宽度菜单栏 -->
    <!-- 自适应宽度页面区域 -->
    <div
    @click.stop="hideall()"
      class="css-flexn css-bsb css-h100"
      :class="'_css-right-state_' + _homedata.leftmenustate.toString()"
    >

      <!-- 项目内页右侧的子vue页面 -->
      <router-view
        @set_extdata="_set_extdata"
        @set_projectboot_extdata="_set_projectboot_extdata"
        @onmounted="_childmounted"
        @modelcntrefresh="getMenuTree"
        @sceneRefresh="refreshMenuTree"
        :phaseModelCountMap="m_phasecnt"
      />

      <!-- //项目内页右侧的子vue页面 -->
    </div>
    <!-- //自适应宽度页面区域 -->
    <!-- //左菜单栏，右主体 -->
    <!-- idocview , dwg 预览 iframe 载体 -->
    <div class="_css-doc-preview" :class="extdata._show_idocview?'css-fc':'css-hide'">
      <div class="_css-doc-preview-beforeiframe">
        <div class="_css-doc-preview-beforeiframe-01"></div>
        <div class="_css-doc-preview-beforeiframe-02"></div>
        <div class="_css-doc-preview-beforeiframe-03"></div>

        <!-- 新标签打开按钮 -->
        <div
          :title="'在新标签页中查看'"
          class="icon-interface-attributes _css-docpreview-newtab _css-canfull"
           :class="{'_css-isfulling':m_docPreviewIsFull}"
           @click="func_openDocNewtab($event)"
        ></div><!-- //新标签打开按钮 -->

        <!-- 当前页全屏按钮 -->
        <div
        @click="func_switchfull($event)"
        :title="m_docPreviewIsFull?'取消全屏':'全屏'"
          :class="{'_css-docpreview-fullscreen':true
          , '_css-isfulling':m_docPreviewIsFull
          , '_css-canfull': true
          , 'icon-arrow-fullscreen_exit':m_docPreviewIsFull
          , 'icon-arrow-fullscreen':!m_docPreviewIsFull}"
        ></div><!-- //当前页全屏按钮 -->

        <!-- 关闭预览 -->
        <div
          :title="'关闭预览'"
            class="icon-suggested-close _css-canfull"
            :class="(m_docPreviewIsFull?'_css-isfulling':'')  + ' '+'_css-doc-preview-closebtn-' + extdata._docviewtype"
            @click="close_idocview($event)"
          ></div><!-- //关闭预览 -->

        <div class="_css-doc-preview-beforeiframe-04 __web-inspector-hide-shortcut__"></div>
      </div>
      <iframe class="_css-doc-preview-iframe"
      :class="{'_css-previewfull':m_docPreviewIsFull}"
      :src="extdata._idocviewurl"></iframe>
    </div>
    <!-- //idocview , dwg 预览 iframe 载体 -->

    <CompsSingleField
      :zIndex="1000"
      @onok="begin_savepjsection"
      :title="extdata.isshowing_pjsection_editingtype=='add'?'新建模型管理文件':'修改模型管理文件'"
      :inittext="extdata.isshowing_pjsection_editingname"
      v-if="extdata.isshowing_pjsection == true"
      @oncancel="cancelpjsection"
      @oninput="pjsection_input"
      placeholder="模型管理文件"
      inputicon="icon-none"
    ></CompsSingleField>

    <CompsSingleField
      :zIndex="1000"
      @onok="addSceneMenu"
      :title="sceneType ==='add'?'新建场景管理文件夹':'修改场景管理文件夹'"
      :inittext="addSceneMenuContent"
      v-if="isShowAddScene === true"
      @oncancel="cancelpjsection"
      @oninput="getInputValue"
      placeholder="场景管理文件夹"
      inputicon="icon-none"
    ></CompsSingleField>

    <!-- 下载模态框  :before-close="handleClose"关闭确认事件 暂时没用到 看后续要不要加-->
    <el-dialog
      title="下载"
      :visible.sync="download_box"
      width="410px">
      <!-- Revit导出插件 -->
      <div class="Revit_ExportPlug">
        <div class="Revit_ExportPlug_left">
          <div class="Revit_ExportPlug_left_icon Revit_ExportPlug_left_icon_Revit"></div>
          <div class="Revit_ExportPlug_left_text">Revit转换插件</div>
        </div>
        <div class="Revit_ExportPlug_right" @click.stop="downloadrevit()">下载</div>
      </div>
      <!-- Revit导出插件 -->

      <!-- Naviswork导出插件 -->
      <div class="Revit_ExportPlug">
        <div class="Revit_ExportPlug_left">
          <div class="Revit_ExportPlug_left_icon Revit_ExportPlug_left_icon_Revit_Naviswork"></div>
          <div class="Revit_ExportPlug_left_text">Naviswork转换插件</div>
        </div>
        <div class="Revit_ExportPlug_right" @click.stop="downloadnaviswork()">下载</div>
      </div>
      <!-- Naviswork导出插件 -->

      <!-- Sketch Up导出插件 -->
      <div class="Revit_ExportPlug">
        <div class="Revit_ExportPlug_left">
          <div class="Revit_ExportPlug_left_icon Revit_ExportPlug_left_icon_Revit_Sketch"></div>
          <div class="Revit_ExportPlug_left_text">Sketch Up转换插件</div>
        </div>
        <div class="Revit_ExportPlug_right" @click.stop="downloadsketchup()">下载</div>
      </div>
      <!-- Sketch Up导出插件 -->

      <!-- 其他导出插件 -->
      <div class="Revit_ExportPlug">
        <div class="Revit_ExportPlug_left">
          <div class="Revit_ExportPlug_left_icon Revit_ExportPlug_left_icon_Revit_other"></div>
          <div class="Revit_ExportPlug_left_text">Microstation转换插件</div>
        </div>
        <div class="Revit_ExportPlug_right" @click.stop="microstationdownload()">下载</div>
      </div>
      <!-- 其他导出插件 -->

      <!-- footer部分 -->
      <div class="dialog-footer">
        <div class="dialog-footer-right" @click="download_box = false">关闭</div>
      </div>
      <!-- footer部分 -->
    </el-dialog>
    <!-- 下载模态框 结束 -->
    <!-- app更新记录 -->
    <el-dialog
      class="app-dialog"
      title="APP更新"
      :visible.sync="isShowUpdateList"
      width="430px" >
      <el-table
        :fixed="true"
        highlight-current-row
        :header-cell-style="{'background-color':'#F8F8F8'}"
        :data="appVersionData"
        height="300"
        style="width: 100%;overflow: auto">
        <el-table-column
          label="版本号"
          width="100">
          <template slot-scope="scope">
            <span>{{ scope.row.AppCode }}</span>
          </template>
        </el-table-column>
        <el-table-column
          label="上传时间"
          width="200">
          <template slot-scope="scope">
            <span>{{ scope.row.CreateTime }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作">
          <template slot-scope="scope">
            <img
              style="cursor: pointer"
              width="15"
              height="15"
              src="../../assets/images/app-delete.png"
              @click="deleteApk(scope.row.Id)" alt=""/>
          </template>
        </el-table-column>
      </el-table>
      <!-- footer部分 -->
      <div class="app-dialog-footer">
        <div class="app-dialog-footer-right" @click="openUpdate">
          新增
        </div>
        <div class="app-dialog-footer-left" @click="isShowUpdateList = false">
          取消
        </div>
      </div>
      <!-- footer部分 -->
    </el-dialog>
    <!-- app更新记录 -->
    <!-- app上传更新 -->
    <el-dialog
      :before-close="closeUpdate"
      class="app-dialog"
      title="新增版本"
      :visible.sync="isShowUpdate"
      width="422px" >
      <div class="version-input-wrapper">
        <span>版本号：</span>
        <el-input
          placeholder="输入版本号 ( 例如: 1.0.0 )"
          v-model="appVersion">
        </el-input>
      </div>
      <div class="version-select-wrapper">
        <span class="tooltip">安装包：</span>
        <div class="upload-wrapper">
          <el-upload
            style="display: flex"
            action=""
            accept=".apk"
            :show-file-list="false"
            :auto-upload="false"
            :on-change="onSelectChange">
            <div class="button-upload">上传文件</div>
          </el-upload>
          <div class="apk-file" v-if="appFileName">
            <span>{{appFileName}}</span>
          </div>
        </div>
      </div>
      <!-- footer部分 -->
      <div class="app-dialog-footer">
        <div class="app-dialog-footer-right" @click="beginUpdate">
          确定
        </div>
        <div class="app-dialog-footer-left" @click="closeUpdate">
          取消
        </div>
      </div>
      <!-- footer部分 -->
    </el-dialog>
    <!-- app上传更新 -->
    <Message2
    v-if="IsShowMessageMini"
    ref="messageref"
    @OnShow="onMessageDialogShow"
    @OpenMessageList="on_OpenMessageList"
    @updateDotStatus = getDotStatus
    ></Message2>
    <MessageList2 v-if="IsShowMessageList" @CloseList="IsShowMessageList=false"></MessageList2>
    <div class="_css-model-showShare" v-if="showViewpointShare">
        <div class="_css-model-sharebox">
          <div class="_css-shareTitle">
            分享：<span>{{viewpointShareTitle}}</span><i class="icon-suggested-close close" @click="showViewpointShare=false"></i>
          </div>
          <div class="_css-share-qrcode">
            <div class="_css-share-qrcode-img">
              <img :src="shareImg">
            </div>
            <div class="_css-share-qrcode-url">
              <p>请复制以下链接地址</p>
              <input id="viewpointShare" type="text" :value="viewpointShareUrl">
            </div>
          </div>
          <div class="_css-share-btn">
              <button class="_css-share-white" @click="showViewpointShare=false">取消</button>
              <button class="_css-share-blue" @click="copyToTemp">复制链接</button>
          </div>
        </div>
      </div>
  </el-container>

  <!-- //项目内页下部分 -->
</template>
<script>
import { EventBus } from "@static/event.js";
import CompsInvite from "@/components/CompsProject/CompsInvite";  // 邀请成员
import CompsCurrentUserMenuList from "@/components/CompsCustom/CompsCurrentUserMenuList";
import CompsSingleField from "@/components/CompsCommon/CompsSingleField"; // 添加模型文件夹
import Message2 from '@/components/Home/ProjectBoot/Message2'
import MessageList2 from '@/components/Home/ProjectBoot/Message/MessageList2'


export default {
  components: {
    CompsInvite,
    CompsCurrentUserMenuList,
    CompsSingleField,
    Message2,
    MessageList2,
  },
  data() {
    return {
      isShowAddScene: false,
      addSceneMenuContent: '',
      sceneType:'add', // add 新增场景， modify 修改场景
      isDot: true,
      realName:'',
      isShowHeader: false,
      isMain: 0,
      isShowUpdate: false,
      isShowUpdateList: false,
      loading:true,
      appFileName:null,
      appVersion: null,
      appFile: null,
      appVersionData:[],
      // 表示模型阶段及模型个数
      m_phasecnt: {},
      // 在线预览是否已全屏
      m_docPreviewIsFull: false,
			MessageInfo: { dataSource:{}, msgFromSignalR:false }, // 消息数据
      extdata: {
        _addMenuList: '', // 记录新增时点击的Id
        addPhaseDialogShow: false,  // 编辑项目阶段，重命名、删除
        addPhaseDialogMenu: {}, // 当前编辑的阶段
        wzwjson:{},
        secbtntop: 0,
        secbtnleft: 0,
        funcauthdata: undefined, // 当前人在当前项目下的权限数据（角色取并集，如果是公开项目且当前人不在任何角色，视为Public角色）
        showing_pjsection_editlist: "", // 显示的项目阶段的下拉按钮
        cachedata_pjdata: "", // 项目字典接口中获取的数据。
        isshowing_pjsection_editingname: "", // 正在编辑的阶段名称
        // isshowing_pjsection_editingvalue: "", // 正在编辑的阶段值（新增时为-1， 编辑时为值）
        isshowing_pjsection_editingtype: "add", // 正在编辑还是正在添加 add, modify
        isshowing_pjsection: false, // 正在显示项目阶段名称输入对话框
        _isfullscreen: false, // 当前是否为全屏
        _currentisProjectManager: false, // 当前人是项目管理员
        showinvite: false, // 是否已显示邀请对话框
        _currentProjectName: "", // 当前进入的项目的项目名称
        _show_idocview: false, // 显示 idocview
        _idocviewurl: "about:blank", // 在线预览文档地址
        _docviewtype: "document" // ''默认， 'office', 'dwg'
      },
      projectmenuPhase: [],  // 所有阶段菜单
      currentMenuListState: false, //用户菜单列表状态
      IsShowMessageMini:false,
      IsShowMessageList:false,

      download_box:false, //下载模态框 默认关闭
      showViewpointShare: false,   // 是否显示分享信息
      viewpointShareTitle: "",   //  分享title
      shareImg: "",   // 分享图片
      viewpointShareUrl: "",  // 分享链接路径
      tokenValue: '',
      organizeIdValue: '',
      menuListArr: [], // 新修改的菜单数组
      defaultActive: '1',
      menuSystemButton: [], // 记录模型按钮权限
      defaultActiveTemp: '', // 临时记录处于激活态的menu key
      defaultActiveBak: '', // 当前处于激活态的menu key备份
      getMenuTreePromise: '' // 获取左侧菜单数据的Promise
    };
  },

  created() {
    window.projectbootvue = this;
    this.viewpointShowModelShare = false;
    this.$Bus.$on("UnFoldMenuAndSelect",this.UnFoldMenuAndSelect)// 注册展开左侧树并选择菜单事件
		this.$Bus.$on('UpdateMsg', this.handleUpdateMsg)// 注册更新消息事件
    this.isMain = this.$route.params.isMain || 0;
    this.$staticmethod._Set("isMain", this.isMain)
    if (this.isMain !== 0) {
      this.tokenValue = this.$route.params.Token;
      this.$staticmethod.Set("Token", this.tokenValue)
      this.organizeIdValue = this.$route.params.organizeId
      this.$staticmethod._Set("organizeId", this.organizeIdValue)
      this.getUserInfo()
    }else {
      this.isShowHeader = true
      this.tokenValue = this.$staticmethod.Get("Token");
      this.organizeIdValue = this.$staticmethod._Get("organizeId");
    }
  },

  mounted() {
    var _this = this;
    window.projectbootvue = _this;
    // 调用接口，获取消息个数（或添加红点标识）
    // _this.getMessageCount(2); //这是做站内信的，先隐藏

    _this.getMessage()
    _this.$emit("set_extdata", "ellipsislogotext", true);
    _this.$emit("set_extdata_hidecollapsebtn", 0);
    // EventBus.$on('Rupdatephasecnt',()=>{
    //   _this.getMenuTree()
    // })
    this.$Bus.$on('Rupdatephasecnt',this.getMenuTree)
    document.addEventListener('click',this.onDocumentClick,true)

    // 初始化设置菜单
    this.defaultActive = sessionStorage.getItem('defaultActive') || '1'
    this.defaultActiveBak = this.defaultActive
    sessionStorage.setItem('defaultActive', this.defaultActive)
    _this.loadprojectadmin();
  },

  beforeDestroy() {
    sessionStorage.removeItem("leftMenuIdActivated","")
    document.removeEventListener('click',this.onDocumentClick,true)
    this.$Bus.$off("UnFoldMenuAndSelect",this.UnFoldMenuAndSelect)
    this.$Bus.$off("UpdateMsg", this.handleUpdateMsg)
    this.$Bus.$off('Rupdatephasecnt', this.getMenuTree)
  },
  // watch: {
  //   $route(to, from) {
  //     // 对路由变化作出响应...
  //     // 这里要判断是否是由消息跳转触发的，在点击消息后，设置一个变量，在刷新后，将这个变量再重置
  //   },
  // },
  methods: {
    /**
     * 获取输入文字
     * @param value
     */
    getInputValue(value){
      this.addSceneMenuContent = value;
    },
    getDotStatus(isDot){
      console.log('isDot',isDot)
      this.isDot = isDot;
    },
    /**
     * 获取当前token下的机构信息
     */
    getOrganizeId() {
      // 根据当前 Token 获取其所在机构，如果为-1的话，则显示默认。
      var _this = this;
      _this
        .$axios({
          method: "get",
          url: `${window.bim_config.webserverurl}/api/User/User/GetTokenComInfo?Token=${_this.tokenValue}`,
        })
        .then((x) => {
          //debugger;
          if (x.status == 200 && x.data.Ret > 0 && x.data.Data) {
            sessionStorage["_OrganizeId"] = x.data.Data.OrganizeId;
            if (x.data.Data.IsShowname) {
              _this.extdata.info_companyname = x.data.Data.FullName;
            } else {
              _this.extdata.info_companyname = "";
            }
            x.data.Data.ThumbnailUrl && x.data.Data.ThumbnailUrl.length > 0 ? _this.extdata.info_companylogo = x.data.Data.ThumbnailUrl : _this.extdata.info_companylogo = ''

            //debugger;
            _this.loginOrganized = x.data.Data.OrganizeId;
          }
        })
        .catch((x) => {});
    },
    /**
     * 系统管理员获取token
     */
    loginBySystem(){
      this.$axios.get(this.$urlPool.base_MainSystemApi+this.$urlPool.LoginByOrganizeId+'?organizeId='+this.organizeIdValue+'&token='+this.tokenValue).then(res=>{
        let result=res.data;
        if(result.Ret===1) {
          this.tokenValue = result.Data.Token
          this.$staticmethod.Set("Token",result.Data.Token);
          this.$staticmethod.Set('RealName', result.Data.RealName);
          this.$staticmethod.Set('Account', result.Data.Account); // 上传文件时，参数：CreateUserName。
          this.$staticmethod.Set("UserId", result.Data.UserId); // Cache UserId
          this.$staticmethod.Set("Email", result.Data.Email); // Cache Email
          this.getOrganizeId()
          this.getCCToken()
          this.getMenuTree()
        }else{
          this.$message({type:'error',message:result.Msg});
        }
      });
    },
    /**
     * 获取用户信息
     */
    getUserInfo(){
      this.$axios.get(`${window.bim_config.webserverurl}/api/User/Home/GetUser?token=${this.tokenValue}`)
        .then(x => {
          if (x.data.Ret > 0) {
            let realName =  x.data.Data.RealName
            this.$staticmethod.Set("RealName", x.data.Data.RealName);
            this.$staticmethod.Set("Email", x.data.Data.Email);
            this.$staticmethod.Set("Account", x.data.Data.Account);
            if (realName && realName.length > 0) {
              realName = realName.substr(realName.length - 1);
            } else {
              realName = "无";
            }
            this.realName = realName;
            this.isShowHeader = true
            if (x.data.Data.Account === 'System'){
              this.loginBySystem()
            }else {
              this.getOrganizeId()
              this.getMenuTree()
              this.getCCToken()
            }
          }
        }).catch(x => {
      });
    },
    /**
     * 流程相关登录
     */
    getCCToken() {
      // 获取cc的token
      let _this = this;
      let _url = `${window.bim_config.webserverurl}/api/User/User/CCFlowLogin`;
      let _para = {
        Token: this.tokenValue,
        organizeId: this.organizeIdValue,
      };
      _this
        .$axios({
          method: "post",
          url: _url,
          data: _para,
        })
        .then((x) => {
          if (x.data.Ret > 0) {
            // userno   uesrname   // OrgNo 机构ID
            window.localStorage.setItem("CC_Token", x.data.Data);
              // 根据屏幕宽度判断跳转路径
              if (_this.extdata.smallScreenShow == false) {
                // 小屏，拼接url地址
                let urlissue = `${window.location.origin}${window.bim_config.hasRouterFile}/#/Home/ProjectBoot/Issue/${_OrganizeId}/${_this.$staticmethod.Get("Token")}`
                console.log(urlissue);
              } else {
                this.toRouterInFirstMenu()
                // window.location.href = `${window.bim_config.hasRouterFile}/#/Home/ProjectBoot/Main/${_OrganizeId}/${_this.$staticmethod.Get("Token")}`;
            }
          }else if(x.data.Ret == -9999){
            this.$message.error(x.data.Msg);
            return
          }
          else {
            _this.$message.error(x.data.Data);
          }
        })
        .catch((x) => {
          console.log(x);
        });
    },
    // 点击进入项目、默认进入第一个菜单的页面
    toRouterInFirstMenu(){
      // 获取当前项目的树结构、判断跳转页面地址
      this.getMenuTreePromise = this.$axios
        .get(`${this.$urlPool.GetUserMenuTree}?token=${this.tokenValue}&organizeId=${this.organizeIdValue}&parentId=0`)
        .then(res=>{
          if(res.data.Ret == 1) {
            let data = res.data.Data;
            if (data[0]) {
              let routePath = data[0].RoutePath.length > 0 ? data[0].RoutePath : data[0].Children[0].RoutePath;
              let _urlorigin = window.bim_config.hasRouterFile + routePath;
              let _urltarget = _urlorigin
                .replace("@Token", this.tokenValue)
                .replace("@OrganizeId", this.organizeIdValue);
              window.location.href = _urltarget;
              sessionStorage.setItem('defaultActive', data[0].RoutePath.length > 0 ? '1' : '1-01');
            } else {
              this.$message.error('当前项目菜单配置地址有误，请检查菜单地址');
              return;
            }
          }else{
            console.error(res.data.Msg);
          }
        })
        .catch(err=>{})
    },
    /**
     * 删除单体数据
     */
    async deleteApk(Id){
      const res = await this.$api.DeleteApp({
        Token: this.$staticmethod.Get("Token"),
        Id
      })
      if (res.Ret === 1){
        this.$message.success('删除成功！')
        await this.getAppVersionsList()
      }else {
        this.$message.error(res.Msg)
      }
    },
    /**
     * 文件选择变化时
     * @param file
     */
    onSelectChange (file) {
      console.log('file',file)
      this.appFileName = file.name
      this.appFile = file.raw
    },
    /**
     * 打开更新app
     */
    openUpdate(){
      this.isShowUpdateList  = false
      this.isShowUpdate = true
    },
    /**
     * 取消上传
     */
    closeUpdate(){
      this.appVersion = null
      this.isShowUpdate = false
      this.appFileName = null
      this.appFile = null
    },
    /**
     * 调用接口上传apk
     */
    async beginUpdate() {
      if (!this.appVersion){
        this.$message.warning('请填写版本号！')
        return
      }
      if (!this.appFile){
        this.$message.warning('请选择文件！')
        return
      }
      const form = new FormData()
      form.append('Token',this.$staticmethod.Get("Token"))
      form.append('File',this.appFile)
      form.append('AppCode',this.appVersion)
      form.append('AppType','Android')
      const loading = this.$loading({
        target: 'el-dialog'
      });
      const res = await this.$api.AddAppVersions(form)
      if (res.Ret === 1){
        this.$message.success('上传成功！')
        this.closeUpdate()
      }else {
        this.$message.error(res.Msg)
      }
      loading.close()
    },
    /**
     * 获取更新列表
     */
    async getAppVersionsList(){
      this.isShowUpdateList = true;
      const res = await this.$api.GetAppVersions({
        Token: this.$staticmethod.Get("Token"),
        PageNum :1,
        PageSize: 999
      })
      if (res.Ret === 1){
        this.appVersionData = res.Data.Data
      }else {
        this.$message.error(res.Message)
      }
    },
    onDocumentClick() {
      this.extdata.addPhaseDialogShow = false;
    },
    // 展开左侧菜单并选择菜单项，或者在有任何需要消息推送并定位要求的地方将左侧菜单的defaultActive传递给后端，点击消息时再带回来
    UnFoldMenuAndSelect(attrVal,attr) {
      console.log('UnFoldMenuAndSelect');
      if(this.getMenuTreePromise) { // 代表获取左侧菜单数据的请求已经发出
        this.getMenuTreePromise.then(()=>this.doUnfoldLeftMenuAndSelect(attrVal,attr))
      } else {
        this.getMenuTree()
        this.getMenuTreePromise.then(()=>this.doUnfoldLeftMenuAndSelect(attrVal,attr))
      }
    },

    // 只找了2级
    // 根据attrVal,返回激活的菜单和左侧菜单el-menu的key
    getElMenuActiveKeyByAttr(attrVal,attr) {
      if(attrVal === 'MODEL') return
      for(let i = 0,len = this.menuListArr.length; i < len; i++) {
          const menuItem = this.menuListArr[i]
          const elMenuKeys = [] // 父在前，子在后
          let shouldNextLoop = true
          const leftMenuItemActived = this.$staticmethod.walkThroughTreesByDepthFirst([menuItem],(item) => {
            if(item[attr] === attrVal) {
              elMenuKeys.push(i+1)
              return true
            }
          },"Children",true)

          if(elMenuKeys.length) { //有长度则代表找到了父级菜单的索引：当前的menuItem包含着attrVal的菜单
            const childrenArr = menuItem.Children
            if(childrenArr && childrenArr.length) {
              for(let j = 0,len2 = childrenArr.length; j < len2; j++) {
                const childMenuItem = childrenArr[j]
                if(childMenuItem[attr] === attrVal) {
                  elMenuKeys.push(j+'1') // el-menu的index绑定逻辑里有个固定的字符串1作为结尾
                  shouldNextLoop = false
                  break
                }
              } // 找menuCode的菜单在menuItem的Children中的索引
            }
          }

          if(!shouldNextLoop) {
            return  [leftMenuItemActived,elMenuKeys.join("-")]
          }
      }
    },

    // 展开并选中左侧菜单
    doUnfoldLeftMenuAndSelect(attrVal) {
      console.log('doUnfoldLeftMenuAndSelect')
      if(!attrVal) return
      const [leftMenuItemActived,resultKey] = this.getElMenuActiveKeyByAttr(attrVal,'MenuCode')
      if(leftMenuItemActived && resultKey) {
        this.$nextTick(() => {
          this.defaultActive = resultKey
          this.defaultActiveBak = this.defaultActiveTemp = this.defaultActive
          sessionStorage.setItem("defaultActive",this.defaultActive)
          this.handleSelectClick(leftMenuItemActived,'222',null)// 选中菜单
          this.$Bus.$emit("UpdateAuth") // 更新页面权限
        })
      }
    },

    onMessageDialogShow() {
      this.MessageInfo.msgFromSignalR = false // 显示消息弹窗的时候将该标记设置为false
    },

    // 获取消息
    getMessage(args) {
      const _this = this
      if(!_this.$staticmethod.isObject(args)) {
        args = {Module:"站内信",Type:-1} // 目前测试来看好像只要Module有值就行
      }
      const queryString = _this.$qs.stringify(args)
	    if(!this.tokenValue){return}

      const url = `${window.bim_config.webserverurl}/api/User/Message/List?${queryString}&Token=${this.tokenValue}`
      _this.$axios
        .get(url)
        .then((x) => {
          if (x.status == 200) {
            if (x.data.Ret > 0) {
              _this.MessageInfo.dataSource = x.data.Data || null
            } else {
              // _this.$message.error(x.data.Msg)
			        console.error(x.data.Msg)

            }
          } else {
            console.error(x)
          }
        })
        .catch((x) => {
          console.error(x)
        });
    },
    // 响应UpdateMsg事件
    handleUpdateMsg(data) {
      if(this.IsShowMessageMini) {
        // 当前消息窗口是显示的：更新消息窗口的数据
        this.MessageInfo.msgFromSignalR = false
        this.$refs.messageref.refreshMessage(data) // 调用Message2组件的refreshMessage方法
      } else {
        // 消息弹窗是没有显示的：将该标记设置为true表示收到了来自SignalR服务端推送的消息（添加了小红点）
        // 这里可以从右上角弹出一个Nofify，数据来自data.Msg
        this.MessageInfo.msgFromSignalR = true
      }
    },

    getUserShow(){
			// 不显示个人信息
			if(window.bim_config.custom_hideuserInfo == "1"){
				return false
			}else{
				return true
			}
		},
    getIconClass(icon){
			if(icon == null) return true
			let bool = true
			icon.indexOf('newface') == -1 ? bool = true : bool = false
			return bool
		},
    /**
     * 刷新菜单树
     */
    refreshMenuTree(){
      if(this.$route.query.fromname && this.$route.query.fromname == 'longyun'){
        this.organizeIdValue = this.$route.query.ProjectID
      }
      this.getMenuTreePromise = this.$axios
        .get(`${this.$urlPool.GetUserMenuTree}?token=${this.tokenValue}&organizeId=${this.organizeIdValue}&parentId=0`)
        .then(res=>{
          if(res.data.Ret == 1) {
            this.menuListArr = res.data.Data;
          }else{
            console.error(res.data.Msg);
          }
        })
        .catch(err=>{})
    },
    // 加载菜单树结构:同时设置模型管理菜单各阶段的模型数量并将各阶段模型数量传递给右侧匹配的组件
		getMenuTree(){
      if(this.$route.query.fromname && this.$route.query.fromname == 'longyun'){
        this.organizeIdValue = this.$route.query.ProjectID
      }
      this.getMenuTreePromise = this.$axios
        .get(`${this.$urlPool.GetUserMenuTree}?token=${this.tokenValue}&organizeId=${this.organizeIdValue}&parentId=0`)
        .then(res=>{
          if(res.data.Ret == 1) {
            this.menuListArr = res.data.Data;
            // 获取到菜单树后，获取模型菜单下的阶段，然后获取模型个数
            this.getNewModelCount();
          }else{
            console.error(res.data.Msg);
          }
        })
        .catch(err=>{})
		},
    // 根据各阶段获取模型数量
    getNewModelCount(){
      let _this = this;
      let _organizeId = _this.$staticmethod._Get("organizeId");
      _this.$axios
        .get(`${_this.$ip('newModelHttpUrl')}/Vault/GetAllFeaturesNoThumbnail?VaultID=${_organizeId}`)
        .then(res => {
          if(res.status === 200){
            let allModel = this.getPhaseList(res.data)
            _this.m_phasecnt = {};  // 清空之前的设置，重置m_phasecnt
            // console.log(allModel,'这是获取到所有模型')
            // 获取到模型阶段、模型数量后，直接修改下模型菜单
            this.$nextTick(()=>{
              for(let i in allModel){
                _this.$set(_this.m_phasecnt, i, allModel[i].length);
              }
            })
          }
        })
        .catch(err => {
        })
    },
    getPhaseList(featuresNoThumbnail) {
  //     const map = new Map();
  // 　　list.forEach((item, index, arr) => {
  // 　　　　if (!map.has(item.phase)) {
  // 　　　　map.set(
  // 　　　　　　item.phase,
  // 　　　　　　arr.filter(a => a.phase == item.phase)
  // 　　　　　　);
  // 　　　　}
  // 　　});
  // 　　return Object.fromEntries(Array.from(map).map(item => [item[0], item[1]]));
      const phaseCountMap = {}
      if(!featuresNoThumbnail || !featuresNoThumbnail.length) return phaseCountMap
      const modelManageMenu = this.menuListArr.find(item => item.MenuCode === 'MODEL' && item.MenuLevel === 1) // 找到模型管理菜单
      const childPhaseMap = {}
      if(modelManageMenu) {
        const targetList = modelManageMenu.Children
        if(targetList && targetList.length) {
          let currentMenuItem
          let currentPhase
          const walkByDF = this.$staticmethod.walkThroughTreesByDepthFirst
          for(let i = 0, len = targetList.length; i < len; i++) {
            currentMenuItem = targetList[i]
            currentPhase = currentMenuItem.BusinessCode
            if(currentPhase === 'allmodel_phase') {
              phaseCountMap[currentPhase] = featuresNoThumbnail
            } else {
              let allChildPhases
              walkByDF([currentMenuItem], (node) => {
                const nodeBC = node.BusinessCode
                phaseCountMap[nodeBC] = featuresNoThumbnail.filter(item => item.phase === nodeBC) // 找到节点自身带有的模型
                allChildPhases = []

                walkByDF([node], (item) => {
                  const childBC = item.BusinessCode
                  if(childBC && childBC !== nodeBC) {
                    allChildPhases.push(childBC)
                  }
                }, "Children", false) // 收集当前节点的所有后代BusinessCode(phase)

                childPhaseMap[nodeBC] = allChildPhases
              }, "Children", false)
            }
          }
        }
      }
      Object.keys(childPhaseMap).forEach((phase) => {
        const allChilds = childPhaseMap[phase].reduce((acc,next) => {
          acc.push(...phaseCountMap[next])
          return acc
        },[]) // 将后代元素汇总成一个值
        phaseCountMap[phase] = phaseCountMap[phase].concat(allChilds)
      })
      // console.log(phaseCountMap)
      // console.log(childPhaseMap)
      return phaseCountMap
    },
    // 根据模型阶段菜单对象，获取模型个数  ==这个逻辑在接入模型后需要修改==后期
    func_getphasenum(item){
      return this.m_phasecnt[item.BusinessCode]
      // var _this = this;
      // // 如果是全部模型，则显示累加的和
      // if (item.BusinessCode == "allmodel_phase") {
      //   try {
      //     let keys = []
      //     for(let i in _this.m_phasecnt){
      //       keys.push(i)
      //     }
      //     var sum = 0;
      //     if (keys && keys.length) {
      //       for (var i = 0; i < keys.length; i++) {
      //         sum += (_this.m_phasecnt[(keys[i]).toString()] || 0);
      //       }
      //     }
      //     return sum;
      //   } catch(e) {
      //     debugger;
      //   }
      // }
      // // 不是全部模型的逻辑
      // var phaseval = item.BusinessCode;
      // if (_this.m_phasecnt) {
      //   return _this.m_phasecnt[phaseval.toString()] || 0;
      // } else {
      //   return 0;
      // }
    },

    // 全屏切换
    func_switchfull(ev) {
      var _this = this;
      if (_this.m_docPreviewIsFull) {
        _this.m_docPreviewIsFull = false;
      } else {
        _this.m_docPreviewIsFull = true;
      }
    },

    // 在新标签页中打开在线预览
    func_openDocNewtab(ev) {
      var _this = this;
      window.open(_this.extdata._idocviewurl, '_blank');
    },
    // getMessageCount(params){
    //   let _this = this;
    //   let _token = _this.$staticmethod.Get("Token");
    //   let url = `${window.bim_config.webserverurl}/api/Message/JPush/GetCurrentUserNotifyOrMsgCnt?Token=${_token}`;
    //   _this.$axios.get(url).then(x => {
    //     if (x.status == 200) {
    //       if (x.data.Ret > 0) {
    //         let retdata = x.data.Data;

    //         if(params == 1){
    //           // 调用 Message 组件的消息更新函数
    //           if (_this.$refs.messageref) {
    //             _this.$refs.messageref.updateMsgCntNumber(retdata.NotifyUnReadCnt
    //             , retdata.IssueUnReadCnt, retdata.DocUnReadCnt, retdata.FlowUnReadCnt, true);
    //           }
    //         }
    //         if(params == 2){
    //           // 获取消息个数，仅适用于第一次加载，不适用于手动（代码）调用
    //           _this.MessageNumber.InitNotifyUnReadCnt = retdata.NotifyUnReadCnt;
    //           _this.MessageNumber.InitIssueUnReadCnt = retdata.IssueUnReadCnt;
    //           _this.MessageNumber.InitDocUnReadCnt = retdata.DocUnReadCnt;
    //           _this.MessageNumber.InitFlowUnReadCnt = retdata.FlowUnReadCnt;
    //         }

    //         // 红点的显示与隐藏
    //         _this.MessageNumber.Total = retdata.Total;
    //       }
    //     } else {
    //       console.error(x);
    //     }
    //   }).catch(x => {
    //     console.error(x);
    //   });
    // },
    hideall(){
      var _this =this;
      _this.IsShowMessageMini = false;
      // 收起项目阶段的右键弹出菜单
      _this.extdata.showing_pjsection_editlist = '';
      _this.closeall();
    },
    microstationdownload(){
      window.location.href = 'https://www.probim.cn:8076/Resource/PluginFile/Microstation%E8%BD%AC%E6%8D%A2%E6%8F%92%E4%BB%B6.zip';
    },
    downloadsketchup(){
      window.location.href = 'https://www.probim.cn:8076/Resource/PluginFile/BIMeforSU.zip';
    },
    downloadnaviswork(){
      window.location.href = 'https://www.probim.cn:8076/Resource/PluginFile/Navisworks%E8%BD%AC%E6%8D%A2%E6%8F%92%E4%BB%B6.zip';
    },
    downloadrevit(){
      window.location.href = 'https://www.probim.cn:8076/Resource/PluginFile/Revit%E8%BD%AC%E6%8D%A2%E6%8F%92%E4%BB%B6.zip';
    },
    copyToTemp(){
        var viewpointShare=document.getElementById('viewpointShare');
        viewpointShare.select();
        document.execCommand("Copy");
        this.$message({type:"success",message:"复制成功"});
    },
    on_OpenMessageList(){
      this.IsShowMessageList=true;
      this.IsShowMessageMini=false;
      this.MessageInfo.msgFromSignalR = false
    },
    HideMessage(e){
      if(e.target.className.indexOf('el-tooltip')==-1&&e.target.className.indexOf('bottomDiv')==-1)
      {
        this.IsShowMessageMini=false;
      }
    },

    delpjsection(item) {
      var _this = this;
      _this.closeall();
      _this.extdata.addPhaseDialogShow = false;
      // 先判断是否已经有模型处于此阶段中
      let vaultID = _this.$staticmethod._Get("organizeId");
      let _phase = item.BusinessCode
      _this.$axios
          .get(`${this.$ip('newModelHttpUrl')}/Vault/GetFeaturesByPhase?VaultID=${vaultID}&Phase=${_phase}`)
          .then(res => {
            if (res.status === 200) {
              let models = res.data;
              if (models.length > 0) {
                _this.$message.error("该模型管理文件下有模型，无法删除");
              } else {
                _this
                  .$confirm("确认删除该模型管理文件？", "操作确认", {
                    confirmButtonText: "确定",
                    cancelButtonText: "取消",
                    type: "warning"
                  })
                  .then(()=> {
                    this.getNewDeleteModelPhase(item.Id);
                    this.extdata.addPhaseDialogShow = false;
                  })
                  .catch(() => {});
              }
            }
          })
          .catch(err => {
          })
    },

    // // 显示添加阶段弹窗
    // showaddpjsection(item) {
    //   var _this = this;
    //   _this.closeall();
    //   this._addprojectsection(item)
    // },
    showeditpjsection(item) {
      var _this = this;
      _this.closeall();
      _this.extdata.addPhaseDialogShow = false;
      _this.extdata.isshowing_pjsection = true;
      _this.extdata.isshowing_pjsection_editingtype = "modify";
      _this.extdata.isshowing_pjsection_editingname = item.MenuName;
      // _this.extdata.isshowing_pjsection_editingvalue = item.listtype.substr(6);
    },

    // popup close
    closeall() {
      var _this = this;
      _this.extdata.showing_pjsection_editlist = "";
      // 收起个人下拉菜单
      _this.currentMenuListState = false;
    },

    getsectionbtnstyle(){
      var _this = this;
      var _s = {};
      _s["position"] = "fixed";
      _s["top"] = _this.extdata.secbtntop +15+ "px";
      _s["left"] = 130 + "px";
      return _s;
    },

    // 显示或隐藏项目阶段的下拉
    showorhide_sectionedit(item, ev) {
      var _this = this;
      this.extdata.addPhaseDialogShow = true;
      this.extdata.addPhaseDialogMenu = this.$staticmethod.DeepCopy(item);
      if (_this.extdata.showing_pjsection_editlist == item.listtype) {
        _this.extdata.showing_pjsection_editlist = "";
      } else {
        _this.extdata.showing_pjsection_editlist = item.listtype;
        // 修改弹出菜单的位置，先改为fixed,再修改top和left。
        _this.extdata.secbtntop = ev.clientY;
        _this.extdata.secbtnleft = ev.clientX;
      }
    },

    pjsection_input(str) {
      var _this = this;
      _this.extdata.isshowing_pjsection_editingname = str;
    },

    // 保存新增的字典项或修改的字典项
    begin_savepjsection() {
      let _this = this;

      // 调用接口保存项目阶段
      if (_this.extdata.isshowing_pjsection_editingname == '' || _this.extdata.isshowing_pjsection_editingname.trim() == '') {
        _this.$message.error("模型管理文件名称不能为空");
        return;
      }

      // 添加验证：名称不能为全部模型！
      if ('全部模型' == _this.extdata.isshowing_pjsection_editingname
      || _this.extdata.isshowing_pjsection_editingname.trim() == '全部模型'
      ) {
        _this.$message.error("模型管理文件名称不能为“全部模型”");
        return;
      }

      // 判断是新增或编辑
      if (_this.extdata.isshowing_pjsection_editingtype == "add") {

        let newval = "v" + _this.convertDateToStr(new Date());
        let _data = {
          MenuName: this.extdata.isshowing_pjsection_editingname,
          OrganizeId: _this.$staticmethod._Get("organizeId"),
          Description: this.extdata.isshowing_pjsection_editingname,
          ParentId: _this.extdata._addMenuList,
          RoutePath: `/#/Home/ProjectBoot/Model/@OrganizeId/@Token/${newval}`,
          ComponentPath:'',
          NextMenuId:'',
          MenuType: 4,  //  模型菜单
          MenuCode:'MODEL',
          MenuIcon:'',
          BusinessCode:newval,
          RequiresAuth: true,
          Buttons: _this.menuSystemButton,
        }
        this.getNewAddModelPhase(_data)
      } else {
          console.log(this.extdata.addPhaseDialogMenu,'编辑')
          this.extdata.addPhaseDialogMenu.MenuName = this.extdata.addPhaseDialogMenu.Description = this.extdata.isshowing_pjsection_editingname
          // 新版本编辑模型阶段
          this.getNewEditModelPhase(this.extdata.addPhaseDialogMenu);
        }
    },
    /**
     * 添加新的场景阶段
     */
    addSceneMenu() {
      let _this = this;

      // 调用接口保存项目阶段
      if (_this.addSceneMenuContent == '' || _this.addSceneMenuContent.trim() == '') {
        _this.$message.error("场景管理文件名称不能为空");
        return;
      }

      // 判断是新增或编辑
      if (_this.sceneType == "add") {

        let newval = "s" + _this.convertDateToStr(new Date());
        let _data = {
          MenuName: this.addSceneMenuContent,
          OrganizeId: _this.$staticmethod._Get("organizeId"),
          Description: this.addSceneMenuContent,
          ParentId: _this.extdata._addMenuList,
          RoutePath: `/#/Home/ProjectBoot/sceneManagement/@OrganizeId/@Token/${newval}`,
          ComponentPath:'',
          NextMenuId:'',
          MenuType: 6,  //  模型菜单
          MenuCode:'SCENE',
          MenuIcon:'',
          BusinessCode:newval,
          RequiresAuth: true,
          Buttons: _this.menuSystemButton,
        }
        this.getNewAddModelPhase(_data,false)
      } else {
        console.log(this.extdata.addPhaseDialogMenu,'编辑')
        this.extdata.addPhaseDialogMenu.MenuName = this.extdata.addPhaseDialogMenu.Description = this.extdata.addSceneMenuContent
        // 新版本编辑模型阶段
        this.getNewEditModelPhase(this.extdata.addPhaseDialogMenu);
      }
    },
    getMenuBtn(isModel = true) {
      let code = isModel ? 'MODEL' : 'CJGL_1';
			this.$axios
				.get(`${this.$urlPool.GetSystemButton}?menuCode=${code}&token=${this.tokenValue}`)
				.then(res=>{
					if(res.data.Ret == 1){
						let _d = res.data.Data;
						let _btn = [];
						_d.forEach((item)=>{
							_btn.push(
								{
									ButtonCode:item.ButtonCode,
									ButtonName:item.ButtonName,
									ButtonIcon:item.ButtonIcon,
									Description:item.ButtonName,
									ButtonSort: item.ButtonSort
								}
							)
						})
						this.menuSystemButton = _btn;
					}else{
					  this.menuSystemButton = [];
					}
				})
		},
    convertDateToStr(dt) {
      var year = dt.getFullYear();
      var month = dt.getMonth() + 1;
      var date = dt.getDate();
      var hour = dt.getHours();
      var minute = dt.getMinutes();
      var second = dt.getSeconds();
      var ms = dt.getMilliseconds();
      return (
        year +
        "" +
        month +
        "" +
        date +
        "" +
        hour +
        "" +
        minute +
        "" +
        second +
        "" +
        ms
      );
    },
    cancelpjsection() {
      var _this = this;
      _this.extdata.isshowing_pjsection = false;
      this.isShowAddScene = false; // 关闭添加场景的弹窗
    },
    // add project section
    _addprojectsection(item,isModel = true) {
      var _this = this;
      _this.getMenuBtn(isModel);  // 先获取菜单权限在创建菜单
      _this.extdata._addMenuList = item.Id;
      if (isModel) {
        _this.extdata.isshowing_pjsection_editingtype = "add";
        _this.extdata.isshowing_pjsection_editingname = "";
        // _this.extdata.isshowing_pjsection_editingvalue = "__unbelievable__";
        _this.extdata.isshowing_pjsection = true;
      }else {
        _this.sceneType = "add";
        _this.addSceneMenuContent = "";
        // _this.extdata.isshowing_pjsection_editingvalue = "__unbelievable__";
        _this.isShowAddScene = true;
      }
    },
    // 清除登录信息并返回登录页面(if tologin)
    clearlogininfo(tologin) {
      var _this = this;
      _this.$staticmethod.Set("Token", "");
      _this.$staticmethod.Set("RealName", "");
      _this.$staticmethod.Set("Account", "");
      _this.$staticmethod.Set("UserId", "");
      _this.$staticmethod.Set("Email", "");
      if (tologin) {
        // 返回登录页
        window.location.href = `${window.bim_config.hasRouterFile}/#/`;
      }
    },

    itemclick(item) {
      var _this = this;
      if (item.code == "exit") {
        // 收起个人菜单
        _this.currentMenuListState = false;

        // 弹出提示，确认是否退出当前账号
        _this
          .$confirm("确定退出当前账号", "操作确认", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning"
          })
          .then(x => {
            // 调用接口
            var _Token = _this.$staticmethod.Get("Token");
            _this
              .$axios({
                method: "post",
                url: `${_this.$urlPool.RemoveLogin}`,
                data: {Token: _Token}
              })
              .then(x => {
                if (x.status == 200 && x.data.Ret > 0) {
                  // 前端数据清除
                  _this.clearlogininfo(true);
                }
              })
              .catch(() => {});
          })
          .catch(() => {

          });
      } else if (item.code == "official") {
        // 官网
        window.open("http://www.probim.com.cn/");
      }
      // 下载  当点击下载时 弹出模态框
      else if(item.code == "download"){
        this.download_box = true;
      }
      // 个人设置  当点击个人设置 切换页面
      else if(item.code == "settings"){
        // 带参数Token 跳转
        var _Token = _this.$staticmethod.Get("Token")
        this.$router.push({name: 'Settings',params: {Token:_Token}})
      }else if (item.code == 'update'){
        // app更新管理
        this.getAppVersionsList()
      }
    },
    toggleUserMenuListState() {
      this.currentMenuListState = !this.currentMenuListState;
    },
    closeUserMenuListState() {
      this.currentMenuListState = false;
    },

    // 先发请求，并得到当前 Token 是否是项目管理员
    // 如果当前Token 是项目管理员，则加载相关菜单项及子项
    // 项目设置（角色权限 项目成员 GIS设置 项目日志 表单模板 流程模板） 功能设置（构件、问题、现场管理）
    // 向外部 emit onload projectdetail
    // 设置 _currentisProjectManager 及左上角名称文本
    loadprojectadmin() {
      var _this = this;
      var _Token = _this.$staticmethod.Get("Token");
      var _OrganizeId = _this.$staticmethod._Get("organizeId");
      if(!_Token || !_OrganizeId) return
      _this
        .$axios({
          method: "get",
          url: `${_this.$configjson.webserverurl}/api/User/Project/TestTokenIsProjectManager?Token=${_Token}&OrganizeId=${_OrganizeId}`
        })
        .then(x => {
          _this.extdata._currentisProjectManager = false;
          sessionStorage.setItem('currentisProjectManager', '0');
          if (
            x.status == 200 &&
            x.data.Ret > 0 &&
            x.data.Data &&
            x.data.Data.IsProjectMgr == true
          ) {

            // 如果当前Token 是项目管理员，则加载相关菜单项及子项
            // 项目设置（角色权限 项目成员 GIS设置 项目日志 表单模板 流程模板） 功能设置（构件、问题、现场管理）

            _this.$emit(
              "onload",
              "projectdetail",
              true,
              x.data.Data.ProjectObj
            );
            sessionStorage.setItem('currentisProjectManager', '1')

            _this.extdata._currentisProjectManager = x.data.Data.IsProjectMgr;  // 当前人是项目管理员，添加项目设置菜单
          } else if(x.data.Ret == -9999){
            this.$message.error(x.data.Msg);
            this.jump_to_project_list()
            return
          }else {
            _this.$emit(
              "onload",
              "projectdetail",
              false,
              x.data.Data.ProjectObj
            );
          }
          _this.getMenuTree();

          // 设置项目内页的左上角名称文本
          _this.extdata._currentProjectName = x.data.Data.ProjectObj.FullName;
        })
        .catch(() => {
          _this.$emit("onload", "projectdetail", undefined);
          console.log('error');
        });
    },
    changeisfull() {
      var _this = this;
      if (_this.extdata._isfullscreen) {
        _this.extdata._isfullscreen = false;
        _this.$staticmethod.ExitFullScreen();
      } else {
        _this.extdata._isfullscreen = true;
        _this.$staticmethod.FullScreen();
      }
    },
    showInvite() {
      var _this = this;
      _this.extdata.showinvite = true;
    },
    // 项目邀请人员对话框：确定
    CompsInvite_onok(defaultobj, objs) {
      // 邀请对话框中，预设的角色选择判空
      var _this = this;
      // 找到所有选择了角色并输入了邮箱地址的数据，如果没有这样的数据，则提示。
      var arr_role_emails = [];

      // 判断预设是否进行了正确填写
      if (defaultobj.selectedId && defaultobj.selectedId != ""
      && defaultobj.email
      && defaultobj.email != "") {
        arr_role_emails.push(defaultobj);
      }

      // 判断其它的是否进行了正确填写
      for (var i = 0; i < objs.length; i++) {
        if (objs[i].selectedId != "" && objs[i].email != "") {
          arr_role_emails.push(objs[i]);
        }
      }

      // 如果没有任何一项进行了正确填写，提示并跳出
      if (arr_role_emails.length == 0) {
        _this.$message({
          message: "请至少指定一个角色和一个邮箱地址",
          type: "warning"
        });
        return;
      }

      // 对每一个邮箱地址进行正则验证
      var convertToServerModelArr = [];
      for (var i = 0; i < arr_role_emails.length; i++) {
        if (!_this.$staticmethod.validEmail(arr_role_emails[i].email)) {
          _this.$message({
            message: "邮箱地址不正确：【" + arr_role_emails[i].email + "】",
            type: "warning"
          });
          return;
        } else {
          convertToServerModelArr.push({
            RoleId: arr_role_emails[i].selectedId,
            Email: arr_role_emails[i].email
          });
        }
      }

      // 将数据提交给接口，进行邀请。
      var arr_role_emails_str = JSON.stringify(convertToServerModelArr);
      var _Token = _this.$staticmethod.Get("Token");
      _this
        .$axios({
          method: "post",
          url: `${_this.$urlPool.InviteUsers}?token=${_Token}`,
          data: {
            OrganizeId: _this.$staticmethod._Get("organizeId"),
            RoleAndUsers: convertToServerModelArr,
          }
        })
        .then(x => {
          if (x.status != 200) {
            _this.$message({
              message: "服务器错误",
              type: "error"
            });
            _this.$staticmethod.debug(x);
          } else if (x.data.Ret < 0) {
            _this.$message({
              message: x.data.Msg,
              type: "error"
            });
            _this.$staticmethod.debug(x);
          } else {
            // // 邀请成功，弹出提示，关闭对话框
            // // 已成功给……发送邀请。// 已给xxx发送邀请，是否需要弹出个会自动关闭的对话框？

            // 判断 x.data.Data.Exists_suc 及 NotExists_suc 如果都为空，则提示 当前指定的邮箱和角色关系均已存在
            if (!x.data.Data.Exists_suc && !x.data.Data.NotExists_suc) {
              _this.$message.warning('当前指定的邮箱和角色关系均已存在');
            } else {
              _this.$message.success('已成功发送邀请');
              _this.extdata.showinvite = false;
            }

          }
        })
        .catch(x => {
          _this.$message({
            message: "服务器错误",
            type: "error"
          });
          _this.$staticmethod.debug(x);
        });
    },
    CompsInvite_oncancel() {
      // 隐藏“邀请设置”对话框
      var _this = this;
      _this.extdata.showinvite = false;
    },
    // 跳转到项目列表
    jump_to_project_list() {
      var _this = this;
      sessionStorage.removeItem("defaultActive");
      sessionStorage.removeItem("menuText");
      this.$staticmethod.Set("editGanttShow", "");
      this.$staticmethod.Set("currentGanttDataInfo", "");

      var _token = _this.$staticmethod.Get("Token");
      // 判断栈中有无项目数组元素数据，如果有，则进入栈顶的项目ID，否则进入到项目列表
      var _ParentChStacks = _this.$staticmethod._Get("ParentChStacks");
      if (!_ParentChStacks) {
        _ParentChStacks = '';
      }
      // 以,分隔
      var arr = _ParentChStacks.split(',');
      arr = arr.filter(x => x && x != '');

      // 判断如果没有元素，返回到项目列表
      if (arr.length == 0) {
        window.location.href = `${window.bim_config.hasRouterFile}/#/Home/Boot/${_token}`;
      } else {
        // 进入到栈top项目
        var orgid = arr[arr.length - 1];
        var urltojump = `${window.location.origin}${window.bim_config.hasRouterFile}/#/Home/ProjectBoot/Main/${orgid}/${_token}`;
        _this.$staticmethod._Set("WillRefreshLeft", 1);
        _this.$staticmethod._Set("WillRefreshMain", 1);
        _this.$staticmethod._Set("ParentChStacks", arr.splice(0, arr.length - 1).join(','));
        window.location.href = urltojump;
      }
    },

    _childmounted(compname) {
      var _this = this;
      if (compname.indexOf("model") < 0) {
        _this.extdata._docviewtype = compname;
      } else {
        _this.extdata._docviewtype = compname;
      }
    },

    // 关闭 idocview 在线预览
    close_idocview(ev) {
      ev.stopPropagation();
      var _this = this;
      _this.extdata._idocviewurl = "about:blank";
      _this.extdata._show_idocview = false;
    },
    // 开放给子页面的针对 projectboot.extdata 修改的方法
    _set_projectboot_extdata(prop, val) {
      var _this = this;
      _this.extdata[prop] = val;
    },
    // 继承父页面的针对 home.extdata 修改的方法
    _set_extdata(prop, val) {
      var _this = this;
      _this.$emit("set_extdata", prop, val);
    },

    // 增加模型阶段，实际是增加菜单
    getNewAddModelPhase(params,isModel = true){
      let _this = this;
      _this.$axios
				.post(`${_this.$urlPool.CreateMenu}?token=${_this.tokenValue}`,params)
				.then(res=>{
					if(res.data.Ret == 1){
						_this.extdata.isshowing_pjsection = false;
            _this.isShowAddScene = false
						_this.getMenuTree();
            if (isModel){
              _this.$message.success('添加模型文件夹成功');
            }else {
              _this.$message.success('添加场景管理文件夹成功');
            }
					}else{
					  if(res.data.Msg == "当前菜单已存在"){
              if (isModel){
                this.$message.error('当前模型文件夹已存在');
              }else {
                this.$message.error('当前场景管理文件夹已存在');
              }
            }else{
              this.$message.error(res.data.Msg);
            }
					}
				})
				.catch(err=>{})
    },
    // 编辑模型阶段，实际是编辑菜单
    getNewEditModelPhase(params){
      let _this = this;
      this.$axios
				.post(`${this.$urlPool.ModifyMenu}?token=${this.tokenValue}`, params)
				.then(res=>{
					if(res.data.Ret == 1){
						_this.getMenuTree();
            _this.extdata.isshowing_pjsection = false;
            _this.$message.success("修改成功");
						// 创建成功后清空form表单
					}else{
            if(res.data.Msg == "当前菜单已存在"){
              this.$message.error('当前模型文件夹已存在');
            }else{
              this.$message.error(res.data.Msg);
            }
					}
				})
				.catch(err=>{})
    },
    // 删除模型阶段菜单
    getNewDeleteModelPhase(indexId){
      this.$axios
					.post(`${this.$urlPool.DeleteMenu}?id=${indexId}&token=${this.tokenValue}`)
					.then(res=>{
						if(res.data.Ret == 1){
							this.$message.success(res.data.Msg);
							this.getMenuTree();
						}else{
							this.$message.error(res.data.Msg);
						}
					})
					.catch(res=>{})
    },
    handelSelectMenuList(key){ // 该函数的的执行早于handleSelectClick的执行:点三个横杠、文字都会触发
      // this.defaultActive = key;
      this.defaultActiveBak = this.defaultActive // 备份当前的
      this.defaultActiveTemp = key // 临时保存被点击的
      this.defaultActive = '' // 清空当前的
      this.extdata.addPhaseDialogShow = false;
    },
    handleSelectClick(menuitem,num,event){
      if (menuitem.MenuType === 6 || menuitem.MenuCode === 'CJGL'){
        console.log('handleSelectClick', menuitem)
        // 将菜单数据存储到sessionStorage，确保组件能够获取到
        sessionStorage.setItem('ChildMenuIdActivatedChanged', JSON.stringify(menuitem));
        // 使用 $nextTick 确保组件已经渲染完成后再发送事件
        this.$nextTick(() => {
          this.$Bus.$emit('ChildMenuIdActivatedChanged', menuitem)
        })
      }
      /*
        MenuType
				普通系统菜单 1
				自定义菜单 2
				流程菜单 3
				模型菜单 4
				文档类菜单 5
			*/
      if(num == '222' && event && event.target._prevClass.indexOf("icon-interface-list") > -1){ // 点击三个横杠触发的则保持defaultActive不变
        this.defaultActive = this.defaultActiveBak
        this.defaultActiveBak = this.defaultActiveTemp = ""
        sessionStorage.setItem('defaultActive', this.defaultActive) // 保存当前被点击菜单的key
        this.showorhide_sectionedit(menuitem, event)
        return
      }
      this.defaultActive = this.defaultActiveTemp // 不是点击三个横杠触发的则用defaultActiveTemp给defaultActive赋值
      this.defaultActiveBak = this.defaultActiveTemp = ""
      sessionStorage.setItem('defaultActive', this.defaultActive) // 保存当前被点击菜单的key
      // this.$store.commit('setLeftMenuActivated', menuitem) // 保存当前被点击菜单到store中
      sessionStorage.setItem('leftMenuIdActivated', menuitem.Id) // 保存当前被点击菜单的Id到sessionStorage中:模型管理页面直接刷新页面后，根据该值找到右侧的树数据
      this.$Bus.$emit('LeftMenuIdActivatedChanged', menuitem.Id) // 触发事件：模型管理Model.vue中注册了该事件
      sessionStorage.removeItem('menuListHasAuth')
      sessionStorage.setItem('menuText', menuitem.MenuName)
      if(menuitem.BusinessCode && menuitem.BusinessCode.length > 0){
        this.$store.commit({
          type:'setMenuListChange',
          state:  this.defaultActive
        })
      }
      let _this = this;
      let _organizeId = _this.$staticmethod._Get("organizeId");
      let _Token = _this.$staticmethod.Get("Token");
      if(menuitem.MenuType == 1 || menuitem.MenuType == 4 || menuitem.MenuType == 6){
        sessionStorage.setItem('menuListHasAuth', JSON.stringify(menuitem.Buttons))
        let _urlorigin = window.bim_config.hasRouterFile + menuitem.RoutePath;
        console.log(window.bim_config.hasRouterFile ,'====', menuitem.RoutePath)
        if(_urlorigin && _urlorigin.length > 0){
          let _urltarget = _urlorigin
            .replace("@Token", _Token)
            .replace("@OrganizeId", _organizeId);
          window.location.href = _urltarget;
        }else{
          this.$message.error('当前菜单没有配置url或未设置子菜单权限')
        }
      }
      else if(menuitem.MenuType == 2){
        window.open(menuitem.RoutePath)
      }else if(menuitem.MenuType == 3){
        if(menuitem.BusinessCode && menuitem.BusinessCode.length > 0){
          window.location.href = `${window.bim_config.hasRouterFile}/#/Home/ProjectBoot/FormFlow/${_organizeId}/${_Token}/${menuitem.BusinessCode}`
          return
        }else{
          this.$message.error('当前菜单未绑定流程')
        }
      }
      else if(menuitem.MenuType == 5){
        // 文档菜单
      }

    },
    // 是否显示消息提醒
    getConfigBell(){
      if(window.bim_config.custom_hidebootbell){
        return true
      }else{
        return false
      }
		},
  },
  computed: {
    isComMgr(){
      return this.$store.state.comMgr.isComMgr
    },
    hasUnreadMsg() {
      if(this.MessageInfo.msgFromSignalR) {
        return true
      } else {
        const dataSource = this.MessageInfo.dataSource
        if(JSON.stringify(dataSource) !== '{}') {
          const toRemove = ['质量安全', '质量管理','安全管理' ,'安全','质量','问题','档案管理'];
          const filterList = dataSource.List.filter(item => !toRemove.includes(item.Module));
          // console.log('filterList', JSON.stringify(filterList))
          let bool = false
          filterList.findIndex(item => item.HasRead === false) > -1 ? bool = true : bool = false;
          return bool
        } else {
          return false
        }
      }
    },
    RealNameLastName: {
      get() {
        var _this = this;
        var realName = _this.$staticmethod.Get("RealName");
        var namelast;
        if (realName && realName.length > 0) {
          namelast = realName.substr(realName.length - 1);
        } else {
          namelast = "无";
        }
        return namelast;
      }
    },

    // // 添加阶段按钮是否渲染
    // ifAddPhaseBtnRender() {
    //   let ifRender = true
    //   if(!this.extdata||!this.extdata.addPhaseDialogMenu||this.extdata.addPhaseDialogMenu.MenuLevel<=2) {
    //     ifRender = false
    //   }
    //   return ifRender
    // }
  },
  props: {
    _homedata: {
      type: Object,
      required: true
    }
  }
};
</script>
<style scoped lang="scss">
.app-dialog{
  /deep/ .el-dialog__body{
    padding-top: 10px;
  }
  /deep/ .el-table__row>td{
    border-bottom: 1px solid #ebeef5;
  }
  .app-dialog-footer{
    width: 362px;
    height: 40px;
    margin-top: 20px;
    .app-dialog-footer-left {
      width: 76px;
      height: 40px;
      font-size: 14px;
      font-family: PingFangSC-Regular;
      font-weight: 400;
      color: #007AFF;
      line-height: 40px;
      cursor: pointer;
      float: right;
    }
    .app-dialog-footer-right {
      width: 76px;
      height: 40px;
      background: #1890FF;
      font-size: 14px;
      font-family: PingFangSC-Regular;
      font-weight: 400;
      color: rgba(255, 255, 255, 1);
      line-height: 40px;
      cursor: pointer;
      float: right;
    }
  }
  .version-input-wrapper{
    display: flex;
    align-items: center;
    span{
      font-size: 14px;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      color: #222222;
      line-height: 20px;
    }
    .el-input{
      height: 28px;
      width: 80%;
      border: 1px solid #E5E5E5 ;
      border-radius: 4px;
      /deep/ .el-input__inner{
        height: 28px;
      }
    }
  }
  .version-select-wrapper{
    padding-bottom: 48px;
    margin-top: 48px;
    display: flex;
    .tooltip{
      margin-top: 5px;
      font-size: 14px;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      color: #222222;
      line-height: 20px;
    }
    .upload-wrapper{
      .button-upload{
        padding-top: 5px;
        padding-bottom: 5px;
        font-size: 12px;
        color: #007AFF;
        cursor: pointer;
        width: 64px;
        border-radius: 2px;
        border: 1px solid #007AFF;
      }
      .apk-file{
        padding: 6px;
        display: flex;
        background-color: #F8F8F8;
        margin-top: 10px;
        width: 300px;
        span{
          font-size: 14px;
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          color: #132B4D;
          line-height: 20px;
        }
      }
    }
  }
}
</style>

<style scoped lang="less">
.left-menu {
  text-align: left;
  .icon-img{
    display: inline-block;
    // width: 20px;
    // height: 20px;
		font-size: 14px;
    background-size: 100% 100%;
    background-position: 50%;
    background-repeat: no-repeat;
  }
  .icon-img-before{
		&:before {
      display: none;
    }
	}
  .icon-interface-home {
    background-image: url('../../assets/images/menu-icon/index.png')
  }
  .icon-interface-model_list {
    background-image: url('../../assets/images/menu-icon/model.png')
  }
  .icon-interface-document {
    background-image: url('../../assets/images/menu-icon/document.png')
  }
  .icon-interface-720 {
    background-image: url('../../assets/images/menu-icon/interface.png')
  }
  .icon-interface-associated-component {
    background-image: url('../../assets/images/menu-icon/associated.png')
  }
  .icon-interface-model-statistics {
    background-image: url('../../assets/images/menu-icon/statistics.png')
  }
  .icon-interface-component_cost {
    background-image: url('../../assets/images/menu-icon/cost.png')
  }
  .icon-interface-project-process {
    background-image: url('../../assets/images/menu-icon/project-progress.png')
  }
  .icon-interface-meeting {
    background-image: url('../../assets/images/menu-icon/meeting.png')
  }
  .icon-interface-set_se {
    background-image: url('../../assets/images/menu-icon/interface-set.png')
  }
  .icon-interface-quality {
    background-image: url('../../assets/images/menu-icon/quality.png')
  }
}
._css-organize2-menuarea{
  scrollbar-width: none;
  -ms-overflow-style: none;
  height: calc(100% - 60px - 10px);
  overflow-y: auto;
  margin-top: 8px;
}
._css-organize2-menuarea::-webkit-scrollbar {
  display: none; /* Chrome Safari */
}

._css-pjsection-btnicon {
  width: 16px;
  height: 16px;
  font-size: 16px;
  margin-left: 24px;
  margin: 14px 8px 14px 14px;
}

._css-pjsection-btntext {
  height: 22px;
  line-height: 22px;
  flex: 1;
  text-align: left;
  margin-left: 8px;
}

._css-pjsection-btn {
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: space-around;
  color: rgba(0, 0, 0, 0.85);
  cursor: pointer;
}

._css-pjsection-btn:hover {
  background-color: rgba(0, 0, 0, 0.04);
}

._css-pjsection-edit-innerbtn._css-showinner ._css-pjsection-btnlist {
  display: block;
}

._css-leftmenu {
  position: relative;
  z-index: 4;
}

._css-pjsection-btnlist {
  z-index: 30;
  width: 120px;
  min-height: 86px;
  background: rgba(255, 255, 255, 1);
  box-shadow: 0px 1px 3px 0px rgba(0, 21, 41, 0.12);
  border-radius: 2px;
  // display: none;
  box-sizing: border-box;
  padding-top: 4px;
  padding-bottom: 4px;
}
._css-menu-child ._css-pjsection-edit-innerbtn {
  display: none;
  justify-content: space-around;
  align-items: center;
  font-size: 14px;
  position: relative;
}
._css-menu-child:hover ._css-pjsection-edit-innerbtn {
  display: flex;
}
._css-menu-child .model-phase-number{
  display: block;

}
._css-menu-child:hover .model-phase-number{
  display: none;
}
._css-pjsection-edit {
  flex: 1;
  height: 100%;
  display: flex;
  align-items: center;
  flex-direction: row-reverse;
}
._css-pb-add {
  width: 30px;
  height: 30px;
  font-size: 18px;
  display: flex;
  // margin-right: 55px;
  justify-content: space-around;
}
._css-leftbackbtn {
  height: 54px;
  width: 44px;
  text-align: center;
  line-height: 54px;
  color: rgba(255, 255, 255, 0.65);
}
._css-leftbackbtn:hover {
  background: rgba(255,255,255,0.2);
  color: rgba(255, 255, 255, 1);
}
/* 右上角相关 */
._css-righttop-group {
  position: fixed;
  right: 24px;
  top: 12px;
  height: 32px;
  display: flex;
  align-items: center;
  flex-direction: row-reverse;
  z-index: 1000;
}
._css-currentuser-icon {
  width: 32px;
  height: 32px;
  border-radius: 4px;
  background-color: #282e3d;
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: space-around;
  font-size: 12px;
  cursor: pointer;
  user-select: none;
}
.title-back{
  height: 53px;
  color: rgba(255, 255, 255, 0.65);
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  padding: 0 8px 0 0 ;
  border-bottom: 1px solid rgba(255,255,255,0.2);
  .back-icon{
    display: flex;
    height: 53px;
    align-items: center;
    line-height: 53px;
    padding: 0 8px;
    cursor: pointer;
    font-size: 12px;
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    color: #DDDDD0;
  }
  .back-icon:hover{
    background: rgba(255,255,255,0.2);
    color: rgba(255, 255, 255, 1);
  }
  .project-name{
    width: 113px;
    overflow-x: hidden;
    padding-left: 5px;
    text-overflow: ellipsis;
    white-space: nowrap;
    text-align: left;
    font-size: 14px;
    font-family: PingFangSC, PingFang SC;
    font-weight: 500;
    color: #FFFFFF;
  }
}

/* //右上角相关 */
._css-organize2-namearea {
  height: 54px;
  color: rgba(255, 255, 255, 0.65);
}
._css-organize2-namearea-text {
  width: 156px;
  overflow-x: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  text-align: left;
}
/* 折叠左边主菜单时，下半部分的样式过渡 */
._css-left-state_0 {
  transition-timing-function: ease-out;
  transition: all 0.2s;
  width: 200px;
}
._css-right-state_0 {
  transition: all 0.2s;
  width: calc(100% - 200px);
  background-color: #f0f2f5;
}
._css-left-state_1 {
  transition-timing-function: ease-out;
  transition: all 0.2s;
  width: 64px;
}
._css-right-state_1 {
  transition: all 0.2s;
  width: calc(100% - 64px);
}
/* //折叠左边主菜单时，下半部分的样式过渡 */

._css-menu-text {
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  transition: all 0.2s;
  margin-left: 8px;
  display: inline-block;
}
._css-left-state_1 ._css-menu-text {
  transition: all 0.2s;
  margin-left: 50px;
}
._css-doc-preview {
  z-index: 70000;
  position: fixed;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  background-color: rgba(0, 0, 0, 0.45);
  opacity: 1;
  justify-content: space-around;
}
._css-doc-preview-iframe {
  height: 80%;
  width: 80%;
  border-width: 0;
  background-color: #fff;
}
._css-doc-preview-iframe._css-previewfull {
  height: 100%;
  width: 100%;
}
._css-docpreview-newtab {
  font-size: 20px;
  flex: none;
  width: 30px;
  height: 30px;
  position: fixed;
  background-repeat: no-repeat;
  cursor: pointer;
  color: rgba(0, 0, 0, 0.4);
  border: 1px solid transparent;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.8);
  right: calc(10% + 55px);
  top: calc(10% - 15px);
  line-height: 30px;
}

._css-docpreview-fullscreen {
    font-size: 20px;
  flex: none;
  width: 30px;
  height: 30px;
  position: fixed;
  background-repeat: no-repeat;
  cursor: pointer;
  color: rgba(0, 0, 0, 0.4);
  border: 1px solid transparent;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.8);
  right: calc(10% + 20px);
  top: calc(10% - 15px);
  line-height: 30px;
}

._css-doc-preview-closebtn-,
._css-doc-preview-closebtn-office {
  font-size: 20px;
  flex: none;
  width: 30px;
  height: 30px;
  position: fixed;
  background-repeat: no-repeat;
  cursor: pointer;
  color: rgba(0, 0, 0, 0.4);
  border: 1px solid transparent;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.8);
  right: calc(10% - 15px);
  top: calc(10% - 15px);
  line-height: 30px;
}

._css-doc-preview-closebtn-dwg {
  font-size: 20px;
  flex: none;
  width: 30px;
  height: 30px;
  position: fixed;
  background-image: url(/Content/images/ProjectManage/Project/close_normal.png);
  background-repeat: no-repeat;
  cursor: pointer;
  color: rgba(0, 0, 0, 0.4);
  border: 1px solid transparent;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.8);
  right: calc(10% - 15px);
  top: calc(10% - 15px);
  line-height: 30px;
}
._css-canfull._css-isfulling {
  top:0;
  right: 0px;
}
._css-docpreview-newtab._css-isfulling {
  right: 68px;
}
._css-docpreview-fullscreen._css-isfulling {
  right: 34px;
}
._css-doc-preview-beforeiframe {
  position: fixed;
  width: 30px;
  height: 40px;
  top: 0;
  right: 35px;
  background-color: transparent;
  display: flex;
  align-items: center;
  font-family: "微软雅黑";
}
._css-doc-preview-beforeiframe-01 {
  flex: none;
  width: 20px;
}
._css-doc-preview-beforeiframe-02 {
  flex: none;
  font-size: 20px;
  font-weight: normal;
  font-stretch: normal;
  letter-spacing: 0px;
  color: #ffffff;
}
._css-doc-preview-beforeiframe-03 {
  flex: 1;
  height: 100%;
}
._css-doc-preview-beforeiframe-04 {
  flex: none;
  width: 25px;
  height: 100%;
}
/* 下载模态框样式 */
.el-dialog__header{
  text-align: left;
}
.Revit_ExportPlug{
  width:334px;
  height:32px;
  padding: 9px 16px 9px 12px;
  /* background: red; */
  display: flex;
}
.Revit_ExportPlug:hover{
  background: rgba(0,0,0,0.02);
}
.Revit_ExportPlug_left{
  width: 258px;
  height: 32px;
  display: flex;
  align-items: center;
}
.Revit_ExportPlug_right{
  width: 76px;
  height: 32px;
  border:1px solid rgba(0,0,0,1);
  font-size:12px;
  font-family:PingFangSC-Regular;
  font-weight:400;
  color:rgba(0,0,0,1);
  line-height: 32px;
  cursor: pointer;
}
.Revit_ExportPlug_left_icon{
  width: 20px;
  height: 20px;
  font-size: 20px;
}
/* 图表样式 暂时用背景图 */
.Revit_ExportPlug_left_icon_Revit{
  background: url("../../../static/images/downloadres/revit-2017.png") no-repeat;
}
.Revit_ExportPlug_left_icon_Revit_Naviswork{
  background: url("../../../static/images/downloadres/navisworks-manage-2019.png") no-repeat;
}
.Revit_ExportPlug_left_icon_Revit_Sketch{
  background: url("../../../static/images/downloadres/SketchUp.png") no-repeat;
}
.Revit_ExportPlug_left_icon_Revit_other{
  background: url("../../../static/images/downloadres/Microstation.png") no-repeat;
}
/* 图表样式 暂时用背景图 收尾*/
.Revit_ExportPlug_left_text{
  width: 200px;
  height: 20px;
  margin-left: 16px;
  text-align: left;
}
.dialog-footer{
  width: 362px;
  height: 40px;
  margin-top: 20px;
}
.dialog-footer-right{
  width: 76px;
  height: 40px;
  background: #1890FF;
  font-size:14px;
  font-family:PingFangSC-Regular;
  font-weight:400;
  color:rgba(255,255,255,1);
  line-height:40px;
  cursor: pointer;
  float: right;
}
._css-model-sharebox{
  width:750px;
  height:auto;
  position: absolute;
  top:50%;
  left:50%;
  transform:translate(-50%,-50%);
  z-index:999999;
  background-color: #fff;
  box-shadow:0px 13px 24px -17px rgba(11,41,62,0.8);border-radius:4px;
  font-family:PingFangSC-Medium;
  padding:0px 16px 16px 16px;
}
._css-shareTitle{
  width:100%;
  height:48px;
  font-size:16px;
  color:#333;
  line-height:48px;
  text-align:left;
  font-weight:bold;
  position: relative;
  overflow: hidden;
}
._css-shareTitle .close{
  width:18px;
  height:18px;
  position: absolute;
  right:15px;
  top:15px;
  display:block;
}
._css-shareTitle .close::before{
  float:left;
  width:18px;
  height:18px;
  display:block;
  text-align:left;
  text-indent:0px;
  cursor: pointer;
}
._css-share-qrcode{
  height: calc(100% - 38px - 48px - 22px);
  margin-top: 5px;
  overflow: hidden;
  line-height: 50px;
}
._css-share-qrcode-img{
  float: left;
  width: 120px;
  height: 100%;
  float: left;
  background-color: #f0f0f0;
}
._css-share-qrcode-img img{
  width:120px;
  height: 120px;
}
._css-share-qrcode-url{
  width: calc(100% - 120px - 30px);
  height: 100%;
  float: left;
  margin-left: 30px;
  font-size: 0px;
  line-height: 0px;
}
._css-share-qrcode-url p{
  display: inline-block;
  width: 100%;
  height: 40px;
  line-height: 40px;
  font-size: 14px;
  text-align: left;
}
._css-share-qrcode-url input{
  width: calc(100% - 2px);
  height: 32px;
  border: 1px solid rgba(24,144,255,1);
  outline: none;
  text-indent: 10px;
}
._css-share-btn{
  width:100%;
  height:38px;
  line-height:38px;
  text-align:right;
}
._css-share-btn button{
  width:auto;
  height: 32px;
  border:none;
  outline: none;
  border-radius: 3px;
  cursor: pointer;
  font-size:12px;
  letter-spacing:1px;
  padding:0px 10px 0px 10px;
}
._css-share-btn button:hover{opacity:0.8;}
._css-share-btn ._css-share-blue{
  background-color:#1890FF;
  color:#fff;
}
._css-share-btn ._css-share-white{
  background-color:transparent;
  color:#666;
}
.left-menu /deep/ .el-menu-item:focus,
.left-menu /deep/ .el-menu-item:hover,
.left-menu /deep/ .el-submenu__title:hover{
  background: rgba(255,255,255,0.2);
  color: rgba(255, 255, 255, 1);
  border-radius: 8px;
  margin: 0 8px;
}

.left-menu /deep/ .el-menu--horizontal>.el-menu-item:not(.is-disabled):focus,
.left-menu /deep/ .el-menu--horizontal>.el-menu-item:not(.is-disabled):hover,
.left-menu /deep/ .el-menu--horizontal>.el-submenu .el-submenu__title:hover{
  background-color: #0d1640;
}
.left-menu /deep/ .el-menu-item.is-active{
  color: #007aff;
  background-color: #fff;
  border-radius: 8px;
  margin: 0 8px;
}
.left-menu /deep/ .el-menu{
  background: transparent;
}
.left-menu /deep/ .el-menu-item,
.left-menu /deep/ .el-submenu__title{
  margin: 0 8px;
  color: #ddd;
  padding: 0 10px !important;
}
.left-menu /deep/ .el-menu-item,
.left-menu /deep/ .el-submenu__title,
.left-menu /deep/ .el-submenu .el-menu-item{
  height: 34px;
  line-height: 34px;
}
.left-menu /deep/ .el-submenu .el-menu-item{
  padding: 0;
  min-width: 190px;
  text-align: left;
}
.left-menu /deep/ .el-menu-item.is-active {
  .icon-img{
		&:before {
			color: #007AFF;
    }
	}
  .icon-interface-home {
    background-image: url('../../assets/images/menu-icon/index-active.png')
  }
  .icon-interface-model_list {
    background-image: url('../../assets/images/menu-icon/model-active.png')
  }
  .icon-interface-document {
    background-image: url('../../assets/images/menu-icon/document-active.png')
  }
  .icon-interface-720 {
    background-image: url('../../assets/images/menu-icon/interface-active.png')
  }
  .icon-interface-associated-component {
    background-image: url('../../assets/images/menu-icon/associated-active.png')
  }
  .icon-interface-model-statistics {
    background-image: url('../../assets/images/menu-icon/statistics-active.png')
  }
  .icon-interface-component_cost {
    background-image: url('../../assets/images/menu-icon/cost-active.png')
  }
  .icon-interface-project-process {
    background-image: url('../../assets/images/menu-icon/project-progress-active.png')
  }
  .icon-interface-meeting {
    background-image: url('../../assets/images/menu-icon/meeting-active.png')
  }
  .icon-interface-set_se {
    background-image: url('../../assets/images/menu-icon/interface-set-active.png')
  }
  .icon-interface-quality {
    background-image: url('../../assets/images/menu-icon/quality-active.png')
  }
}
.left-menu /deep/ .text-margin4{
  margin-left: 4px;
}
.left-menu /deep/ .text-margin12{
  margin-left: 12px;
  padding-left: 15px;
}
.left-menu .text-margin12.overflow-hidden-point{
  max-width: 180px;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}
.left-menu .title-model,.left-menu .model-phase-list{
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin: 5px 0;
}
.left-menu .title-model{
  line-height: 34px;
}
.left-menu .model-phase-list{
  line-height: 24px;
}

.left-menu .model-phase-list{
  width: 100%;
}
.left-menu .title-model.title-set-css{
  justify-content: flex-start;
  display: flex;
}
.left-menu .title-model.title-set-css .model-display-hover{
  display: none;
  margin-left: 20px;
}
.left-menu .title-model.title-set-css:hover .model-display-hover{
  display: flex;
}
.left-menu /deep/ .text-margin38{
  margin-left: 38px;
  max-width: 200px;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}

.left-menu /deep/ .el-menu--inline{
  background: #0d1640;
}
.first-div-class{
  margin: 5px 0;
  line-height: 34px;
}
</style>
