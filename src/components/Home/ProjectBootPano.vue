<template>
  <div class="_css-pano-all" @click="closeall()">

    <!-- 重命名对话框 -->
    <zdialog-function
    :init_title="'全景图名称'"
    :init_zindex="2"
    init_closebtniconfontclass="icon-suggested-close"
    v-if="status_namemodifing"
    @onclose="func_closenameedit()"
    >
        <!-- 输入区域 -->
        <div slot="mainslot">
          <div class="_css-line css-common-line">
          <div class="_css-title _css-title-flowname">
            请输入名称：
            </div> 
            <div class="_css-fieldvalue css-common-fieldvaluename">
              <input 
              @mousedown="_stopPropagation($event)"
              v-model="m_modifingname" type="text" class="css-common-fieldvaluename-in">
            </div>
          </div>
        </div><!-- //输入区域 -->

        <div slot="buttonslot" class="css-common-zdialogbtnctn" >
           <zbutton-function
              :init_text="'取消'"
              :init_fontsize="14"
              :debugmode="true"
              :init_height="undefined"
              :init_width="'76px'"
              :init_bgcolor="'#fff'"
              :init_color="'#1890FF'"
              @onclick="func_closenameedit()"
              >
          </zbutton-function>
            <zbutton-function
              :init_text="'保存'"
              :init_fontsize="14"
              :debugmode="true"
              :init_height="undefined"
              :init_width="'76px'"
              @onclick="func_savepanoname()"
              >
          </zbutton-function>
        </div>
    </zdialog-function><!-- //重命名对话框 -->

    <!-- gis 选点的小窗 -->
    <div 
    @click="closeGisSel()"
    v-if="extdata.isShowGisSel"
    class="_css-gis-selectpoint" >
      <div class="_css-pano-close icon-suggested-close"></div>
      <iframe id="id_gis_sel" ></iframe>
    </div>

    <!-- 浏览全景图的iframe -->
    <div 
    v-if="extdata.isShowingPano"
    class="_css-pano-viewer" >
      <div 
      @click="closeviewer($event)"
      class="_css-pano-close icon-suggested-close" ></div>
      <iframe id="id_panoifri" class="_css-pano-ifri" :src="extdata.srcShowingPano" />
    </div>

    <!-- 上传新图片对话框 -->
    <div 
    v-if="extdata.uploadm.isshowing"
    class="_css-pano-upload" >
      <div class="_css-pano-upload-in" >
        <CompsDialogHeader @oncancel="_oncancel_upload" title="上传"></CompsDialogHeader>

        <!-- 请输入名称 -->
        <div class="_css-upload-file-label">请输入名称</div>
        <div class="_css-upload-file-inputctn" >
          <div class="_css-upload-file-inputctnin">
            <input id='id_newname' class="_css-upload-file-inputname" type="text"  />
          </div>
        </div>
        <!-- //请输入名称 -->


        <!-- 请选择一张或多张图片 -->
        <div class="_css-upload-file-label2">请选择一张或多张图片</div>
        <div class="_css-upload-file-inputctn" >
          <input id="id_files" class="_css-upload-file-inputfile" type="file" multiple="multiple" accept="image/*" />
        </div>
        <!-- //请选择一张或多张图片 -->

        <!-- 显示进度信息 -->
        <template v-if="extdata.uploadprogress.isshowing">
          <div class="_css-upload-file-label2">{{extdata.uploadprogress.infotext}}</div>
          <div class="_css-upload-file-inputctn" >
            <div class="_css-upload-file-inputshowpb" >
              <div 
              :style="getPbWidth()"
              class="_css-upload-file-inputshowpb-in" >
              
              </div>
            </div>
          </div>
        </template>
        <!-- //显示进度信息 -->

        <CompsDialogBtns
          @onok="_onok_upload_withcontinue"
          @oncancel="_oncancel_upload"
        ></CompsDialogBtns>
      </div>
    </div>

    <!-- 上下文菜单 -->
    <div
    v-show="extdata.contextpos.isshowing"
     class="_css-pano-contextmenu"
       :style="getContextStyle()"
     >
      <div class="_css-pano-contextmenu-i">
        <!-- （重新/）发布到GIS 按钮 -->
        <div 
        v-if="auth_modifyauth"
        @click="rePubGis($event)"
        class="_css-pano-contextmenu-in">
          <div class="_css-pano-contextmenu-inicon icon-model-GIS"></div>
          <div class="_css-pano-contextmenu-intext">{{extdata.gisbtntext}}</div>
        </div>
         <div 
        v-else
        @click="_stopPropagation($event)"
        class="_css-pano-contextmenu-in _css-dis">
          <div class="_css-pano-contextmenu-inicon icon-model-GIS"></div>
          <div class="_css-pano-contextmenu-intext ">{{extdata.gisbtntext}}</div>
        </div><!-- //（重新/）发布到GIS 按钮 -->
      </div>
      <div class="_css-pano-contextmenu-i">
        <div 
        v-if="auth_modifyauth"
        @click="evt_showNameEdit($event)"
        class="_css-pano-contextmenu-in">
          <div class="_css-pano-contextmenu-inicon icon-interface-edit_se"></div>
          <div class="_css-pano-contextmenu-intext">重命名</div>
        </div>
        <div 
        v-else
        @click="_stopPropagation($event)"
        class="_css-pano-contextmenu-in _css-dis">
          <div class="_css-pano-contextmenu-inicon icon-interface-edit_se"></div>
          <div class="_css-pano-contextmenu-intext">重命名</div>
        </div>
      </div>
      <div class="_css-pano-contextmenu-i">
        <div 
        v-if="auth_modifyauth"
        @click="confirmToDel($event)"
        class="_css-pano-contextmenu-in">
          <div class="_css-pano-contextmenu-inicon icon-interface-model-delete"></div>
          <div class="_css-pano-contextmenu-intext">删除</div>
        </div>
        <div 
        v-else
        @click="_stopPropagation($event)"
        class="_css-pano-contextmenu-in _css-dis">
          <div class="_css-pano-contextmenu-inicon icon-interface-model-delete"></div>
          <div class="_css-pano-contextmenu-intext">删除</div>
        </div>
      </div>
    </div>
    <!-- //上下文菜单 -->

    <div class="_css-pano-top">
      <!-- 返回按钮 -->
      <div
      @click="func_backtoprojectlist($event)"
      class="_css-pano-addbtn">返回</div>
      <!-- //返回按钮 -->
    </div>
    <div class="_css-pano-bottom">

      <div class="_css-pano-list"
      v-if="extdata.datalist.length"
      >
        <div 
        @click="openurl($event, item)"
        v-for="item in extdata.datalist"
        :key="item.PbGuid"
        class="_css-pano-i">
          <div v-show="hasGis(item)" class="_css-pano-gisflag" ></div>
          <div class="_css-pano-iimg"
          :style="computeItemImage(item)"
          ></div>
          <div class="_css-pano-itext">{{item.PbName}}</div>
          <div class="_css-pano-istatus">
            <div class="_css-pano-istatustext">更新时间：{{(item.PbUpdatetime || '').replace('T', ' ')}}</div>
            <div v-if="false" @click="showmenu($event, item)" class="_css-pano-idelbtn icon-interface-set_se"></div>
          </div>
        </div>
      </div>

      <div
      v-else
      class="_css-pano-list _css-panolistempty"
      >
        <div class="_css-pano-listempty" >暂无数据</div>
      </div>

    </div>
  </div>
</template>
<script>
import CompsDialogHeader from "@/components/CompsDialog/CompsDialogHeader";
import CompsDialogBtns from "@/components/CompsDialog/CompsDialogBtns";
export default {
  name: "Pano",
  components: {
    CompsDialogHeader,
    CompsDialogBtns
  },
  data() {
    return {

      // 当前操作者的权限
      // ---------------
      auth_modifyauth: false,

      // 全景图正在编辑的名称
      // ------------------
      m_modifingname: '',

      // 呼出右键菜单的那一条数据
      // ----------------------
      m_selectedobj:{}, 

      // 是否显示全景图图集名称的编辑对话框
      // -------------------------------
      status_namemodifing: false,

      extdata: {

        // 权限数据
        // -------
        funcauthdata: undefined,

        isShowGisSel: false, // 是否显示GIS的选点窗体
        isShowingPano: false,          // 正在显示全景图页面
        srcShowingPano: 'about:blank', // 全景图页面地址
        datalist:[],
        SelectedPbGuid:'',
        gisbtntext:'',
        contextpos: {
          top: 0,
          left: 0,
          isshowing: false
        },
        uploadm:{
          isshowing: false,
          disinput: false,
          canceltoken_source: undefined
        },
        uploadprogress:{
          isshowing: false,
          infotext:'',
          pbwidth:0
        }
      }
    };
  },
  mounted() {
    var _this = this;
    window.panovue = this;
    _this.$emit("onmounted", "pano");

    // msg
    window.addEventListener("message", function (data) {

        // 处理消息 data 数据
        _this.processMessageData(data);

    });

    // 加载后请求数据
    _this.getList();

    _this.setfuncauthdata(function() {
      _this.auth_modifyauth = _this.testhasfuncbtnauth('Pano', 'lr-edit');
      //console.log('当前人有编辑权限：' + _this.auth_modifyauth);
    });

  },
  methods: {

      // 返回项目列表
      // -----------
      func_backtoprojectlist(ev) {
          var _this = this;
          var Token = _this.$staticmethod.Get("Token");
          window.location.href = `${window.bim_config.hasRouterFile}/#/Home/Boot/${Token}?name=ProjectBootPano`;
      },

    // 判断有无某功能权限。依据funcauthdata, Bm_EnCode 及 bmbencode ( 如 BIMModel, 'lr-add')
    testhasfuncbtnauth(bmencode, bmbencode) {
      var _this = this;
      if (!_this.extdata.funcauthdata) {
        _this.extdata.funcauthdata = JSON.parse(_this.$staticmethod.Get("funcauthdata"));
      }
      var bmIndex = _this.extdata.funcauthdata.findIndex(
        x => x.Bm_EnCode == bmencode
      );
      if (bmIndex < 0) {
        return false; // 没有找到指定bm项
      }
      var bm = _this.extdata.funcauthdata[bmIndex];
      if (bm.checkstate == "0") {
        return false; // 权限设置中，所有角色的bm设置均没有打开。
      }
      if (bm.Bmbs.length == 0) {
        return false; // 功能模块下没有按钮
      }
      var hasAuth = bm.Bmbs.findIndex(
        x => x.Roles.length > 0 && x.checkstate == "1" && x.Bmb_EnCode == bmbencode
      ); // 这个功能模块下有有角色的，且为有权限的
      return hasAuth >= 0;
    },

    setfuncauthdata(callback) {
      var _this = this;
      // /api/User/Role/GetUserOrgFuncAuth?organizeId=48617e7b-07f2-4748-9199-238af8f2bfc6&Token=322D1C8F
      var _OrganizeId = _this.$staticmethod._Get("organizeId");
      var _Token = _this.$staticmethod.Get("Token");
      _this.$axios
        .get(
          `${window.bim_config.webserverurl}/api/User/Role/GetUserOrgFuncAuth?organizeId=${_OrganizeId}&Token=${_Token}`
        )
        .then(x => {
          if (x.status == 200) {
            if (x.data.Ret > 0) {
              if (x.data.Data) {
                _this.extdata.funcauthdata = x.data.Data;
                _this.$staticmethod.Set("funcauthdata", JSON.stringify(x.data.Data));
                if (callback) {
                  //debugger;
                  callback();
                }
              }
            }
          }
        })
        .catch(x => {});
    },

    // 保存全景图名称
    // -------------
    func_savepanoname() {
      
      // 准备数据
      // -------
      var _this = this;
      var _PbOrganizeId = _this.$staticmethod._Get("organizeId");
      var _PbGuid = _this.m_selectedobj.PbGuid;
      var _PbName = _this.m_modifingname;

      // 显示 loading
      // ------------
      var _LoadingIns = _this.$loading({
        text: '处理中',
        target: document.getElementById("id_jingruizhang_probim_vue_zdialog_inner")
      });

      // 请求并返回
      // ---------
      var _Url = `${window.bim_config.webserverurl}/api/Panorama/PanoramaFile/RenameItem?Token=${this.$staticmethod.Get('Token')}`;
      _this.$axios({
        url: _Url,
        method: 'post',
        data: _this.$qs.stringify({
          PbGuid: _PbGuid,
          PbName: _PbName,
          PbOrganizeId: _PbOrganizeId
        })
      }).then(x => {
        _LoadingIns.close();
        if (x.data.Ret > 0) {
            _this.$message.success('操作成功');
            _this.status_namemodifing = false;
            _this.getList();
        } else if (x.data.Ret < 0) {
            _this.$message.warning(x.data.Msg);
        }
      }).catch(x => {
        _LoadingIns.close();
        debugger;
      });

    },

    // 显示全景图图集名称的编辑对话框
    // ---------------------------
    evt_showNameEdit() {
      
      // 先获取当前数据的已有名称
      // ----------------------
      var _this = this;
      _this.m_modifingname = _this.m_selectedobj.PbName;
      _this.status_namemodifing = true;

    },

    // 关闭全景图图集名称的编辑对话框
    // ---------------------------
    func_closenameedit() {
      var _this = this;
      _this.status_namemodifing = false;
    },

    _stopPropagation(ev) {
      var _this = this;
      ev && ev.stopPropagation && ev.stopPropagation();
    },

    do_processMessageData(gisinfostr, pbguid) {
      var _this = this;
      var posturl = `${window.bim_config.webserverurl}/api/Panorama/PanoramaFile/ReGisItem?Token=${this.$staticmethod.Get('Token')}`;
      _this.$axios({
          method:'post',
          url: posturl,
          data: _this.$qs.stringify({
            //PbGuid, PbGisinfo
            PbGuid: pbguid,
            PbGisinfo: gisinfostr
          })
        }).then(x => {
          if (x.status == 200) {
            if (x.data.Ret > 0) {
              //debugger;
              _this.$message.success('操作成功');
              _this.extdata.isShowGisSel = false;
              _this.getList();
            } else {
              _this.$message.error(x.data.Msg);
            }
          } else {
            debugger;
          }
        }).catch(x => {
          debugger;
        });
    },

    processMessageData(data) {
      var _this = this;
      if (data.data.act == "gisPano_res_onPosClick") {
        if (data.data.msg && data.data.msg.point) {
          var gisinfostr = JSON.stringify(data.data.msg.point);
          var pbguid = _this.extdata.SelectedPbGuid;
          _this.$confirm('确定选择该位置？', '操作确认', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(x => {
            _this.do_processMessageData(gisinfostr, pbguid);
            _this.closeall();
          }).catch(x => {
            _this.closeall();
          });
        }
      }
    },

    // 关闭gis选点
    closeGisSel(){
      var _this = this;
      _this.extdata.isShowGisSel = false;
    },

    // 关闭全景图
    closeviewer(ev){
      var _this = this;
      _this.extdata.isShowingPano = false;
      _this.extdata.srcShowingPano = 'about:blank';
    },

    // 打开全景图
    openurl(ev, item) {
      var _this = this;
      
      //var viewurl = window.bim_config.integrated_innerview + '/-Panorama' + item.PbUrl + '/vtour/tour2.html?organizeId=' + _this.$staticmethod._Get('organizeId');
      var viewurl = _this.$staticmethod.getPanoUrl(item.PbUrl, _this.$staticmethod._Get('organizeId'), '');
      
      if (!_this.auth_modifyauth) {
        viewurl += '&isDis=1';
      }
      _this.extdata.srcShowingPano = viewurl;
      _this.extdata.isShowingPano = true;
    },

    // 
    getPbWidth(){
      var _this = this;
      var _s = {};
      _s['width'] = _this.extdata.uploadprogress.pbwidth + '%';
      return _s;
    },

    // 工具方法
    toolpost(para) {

      // 创建 XMLHttpRequest 对象
      var xhr = new XMLHttpRequest();
      var isasync = para.async == true ? true : false;
      var method = 'POST';
      var url = para.url;
      xhr.onreadystatechange = function () {
          if (xhr.readyState == 4) {
              if (xhr.status == 200) {
                  para.success && para.success(xhr.responseText);
              } else {
                  para.error && para.error(xhr);
              }
          } else {
              para.sending && para.sending(xhr);
          }
      };
      xhr.upload.onprogress = function (ev) {
          if (ev.lengthComputable) {
              para.onprogress && para.onprogress(ev.loaded, ev.total);
          }
      };
      xhr.open(method, url, isasync);
      xhr.send(para.fd);
    },

    GetPercent(num, total) {
        num = parseFloat(num);
        total = parseFloat(total);
        if (isNaN(num) || isNaN(total)) {
            return "-";
        }
        return total <= 0 ? "0" : (Math.round(num / total * 10000) / 100.00);
    },

    newid(){
      var _this = this;
      return (_this.S4() + _this.S4() + "-" + _this.S4() + "-" + _this.S4() + "-" + _this.S4() + "-" + _this.S4() + _this.S4() + _this.S4());
    },

    // Generate four random hex digits.
    S4() {
        return (((1 + Math.random()) * 0x10000) | 0).toString(16).substring(1);
    },

    // 进度条归位（隐藏，因为名称重复等错误）
    // 让上传按钮恢复可用状态
    // --------------------
    resetuploadform() {
      var _this = this;
      _this.extdata.uploadm.disinput = false;
      _this.extdata.uploadprogress.isshowing = false;
      _this.extdata.uploadprogress.infotext = '';
      _this.extdata.uploadprogress.pbwidth = 0;
    },

    // 上传全景图改为先上传第一张，后续再追加后续图片
    // ------------------------------------------
    _onok_upload_withcontinue() {

      // 防重复点击
      // ---------
      var _this = this;
      if (_this.extdata.uploadm.disinput) {
        return;
      }

      // 判断图集名称是否已输入
      // --------------------
      var pbname = document.getElementById('id_newname').value;
      if (!pbname || pbname.trim() == '') {
        _this.$message.error('请输入全景图集名称');
        return;
      }

   

      // 准备参数
      // --------
      var fd = new FormData();
      var newid = _this.newid();
      fd.append("organizeId", _this.$staticmethod._Get("organizeId"));
      fd.append("gisinfo", '');
      fd.append("pbname", document.getElementById('id_newname').value);
      var filedom = document.getElementById('id_files');
      var files = filedom.files;
      if (files.length == 0) {
        _this.$message.error('未选择文件');
        return false;
      }

      // 赋值防重复点击的变量
      // ------------------
      _this.extdata.uploadm.disinput = true;

      // for (var i = 0; i < files.length; i++) {
      //   fd.append(`File${i}`, files[i]);
      // }
      fd.append(`File0`, files[0]);
      _this.extdata.uploadm.canceltoken = _this.$axios.CancelToken;
      _this.extdata.uploadm.canceltoken_source = _this.extdata.uploadm.canceltoken.source();
     
      var config = {
        headers: {
          "Content-Type": "application/json"
        },
        onUploadProgress: progressEvent => {
          // 使用本地 progress 事件
          if (progressEvent.lengthComputable) {
            var num = Math.round(
              (progressEvent.loaded / progressEvent.total) * 100
            );

            // num 需要除以图片总数，再加上 100 / 图片总数 * （当前图片所在索引 - 1）
            // ------------------------------------------------------
            var everypercent100 = 100.0 / files.length;
            num = (num / files.length) + everypercent100 * (1 - 1);

            _this.extdata.uploadprogress.isshowing = true;
            _this.extdata.uploadprogress.infotext = '正在上传文件...' + num.toFixed(2) + '%';
            _this.extdata.uploadprogress.pbwidth = num;

            // 
            if (progressEvent.loaded == progressEvent.total) {
              _this.extdata.uploadprogress.infotext = `【图片1/${files.length}】服务器正在执行批处理...`;
            }
          }
        },
        cancelToken: _this.extdata.uploadm.canceltoken_source.token
      };
    _this.$axios.post(
        `${window.bim_config.webserverurl}/api/Panorama/PanoramaFile/UploadImages?pbguid=${newid}&willappend=0&Token=${this.$staticmethod.Get('Token')}`,
        fd,
        config
      ).then(x => {

        // 第一张图片会带有图集名称信息，验重
        // -------------------------------
        if (x.data.Ret < 0) {
          _this.$message.error(x.data.Msg);

          // 进度条，和按钮可点击性复位
          // ------------------------
          _this.resetuploadform();

          return;
        }

        // 关闭对话框并刷新
        // _this.$message.success('上传成功');
        // _this.extdata.uploadm.isshowing = false;
        // _this.getList();

        // 追加后续图片，该函数为异步递归调用，第二个参数为此次调用应该上传的图片的所在索引
        // 第三个参数为本批的 newid
        // -----------------------
        _this.func_appenduploading(files, 1, newid);


      }).catch(x => {
        _this.$message.warning('上传操作取消');
        _this.extdata.uploadm.disinput = false;
        _this.extdata.uploadm.isshowing = false;
      });

    },

    // 追加后续图片，该函数为异步递归调用，第二个参数为此次调用应该上传的图片的所在索引
    // -------------------------------------------------------------------------
    func_appenduploading(files_from1, shouldUploadIndex, originnewid) {

      // 判断图片的所在索引如果超出了 files_from1 的有效最大索引，则直接弹出上传成功，会刷新列表
      // --------------------------------------------------------------------------------
      var _this = this;
      if (files_from1.length <= shouldUploadIndex) {

        // 关闭对话框并刷新
        // --------------
        _this.$message.success('上传成功');
        _this.extdata.uploadm.isshowing = false;
        _this.getList();
        return;
      }

      // 上传并递归上传，上传时使用追加图片的接口
      // ------------------------------------

      // 得到一个新的guid
      // ---------------
      var newguid = _this.$staticmethod.NewGuid();
      var posturl = `${window.bim_config.webserverurl}/api/Panorama/PanoramaFile/UploadImages?pbguid=${newguid}&willappend=0&Token=${this.$staticmethod.Get('Token')}`;

      // 准备 fd 参数
      // -----------
      var fd = new FormData();
      fd.append("F1", files_from1[shouldUploadIndex]);

      // 准备 config 参数
      // ---------------
      _this.extdata.uploadm.canceltoken = _this.$axios.CancelToken;
      _this.extdata.uploadm.canceltoken_source = _this.extdata.uploadm.canceltoken.source();
       var config = {
        headers: {
          "Content-Type": "application/json"
        },
        onUploadProgress: progressEvent => {
          // 使用本地 progress 事件
          if (progressEvent.lengthComputable) {
            var num = Math.round(
              (progressEvent.loaded / progressEvent.total) * 100
            );

            // num 需要除以图片总数，再加上 100 / 图片总数 * （当前图片所在索引 - 1）
            // ------------------------------------------------------
            var everypercent100 = 100.0 / files_from1.length;
            num = (num / files_from1.length) + everypercent100 * (shouldUploadIndex);

            _this.extdata.uploadprogress.isshowing = true;
            _this.extdata.uploadprogress.infotext = '正在上传文件...' + num.toFixed(2) + '%';
            _this.extdata.uploadprogress.pbwidth = num;

            // 
            if (progressEvent.loaded == progressEvent.total) {
              _this.extdata.uploadprogress.infotext = `【图片${shouldUploadIndex + 1}/${files_from1.length}】服务器正在执行批处理...`;
            }
          }
        },
        cancelToken: _this.extdata.uploadm.canceltoken_source.token
      };

      // 执行上传
      // -------
       _this.$axios.post(
        posturl,
        fd,
        config
      ).then(x => {

        // 追加后续图片，该函数为异步递归调用，第二个参数为此次调用应该上传的图片的所在索引
        // -------------------------------------------------------------------------
        //_this.func_appenduploading(files, 1);
        // http://www.probim.cn:8508/api/Home/File/copyFromSomeToSome?newpatch=
        // 62c27877-f904-26c2-81c3-44f932a12e62&oldpatch=cb3a5413-7649-586f-c6f0-43832ee0c8e0
        // 从 old copy 到 newpatch

        // 追加上传后，要拷贝服务器文件
        // -------------------------

        // 定义接口地址
        // oldpatch == originnewid
        // -----------------------
        var syncfileurl = `${window.bim_config.webserverurl}/api/Panorama/PanoramaFile/copyFromSomeToSome?newpatch=${newguid}&oldpatch=${originnewid}&Token=${_this.$staticmethod.Get('Token')}`;
        _this.$axios.get(syncfileurl).then(y => {
          _this.func_appenduploading(files_from1, shouldUploadIndex + 1, originnewid);
        }).catch(y => {

        });


      }).catch(x => {
        _this.$message.warning('上传操作取消');
        _this.extdata.uploadm.disinput = false;
        _this.extdata.uploadm.isshowing = false;
      });

    },

    // 开始上传
    _onok_upload(){
      var _this = this;
      if (_this.extdata.uploadm.disinput) {
        return;
      }

      // 判断图集名称是否已输入
      // --------------------
      var pbname = document.getElementById('id_newname').value;
      if (!pbname || pbname.trim() == '') {
        _this.$message.error('请输入全景图集名称');
        return;
      }

      _this.extdata.uploadm.disinput = true;
      var fd = new FormData();
      var newid = _this.newid();
      fd.append("organizeId", _this.$staticmethod._Get("organizeId"));
      fd.append("gisinfo", '');
      fd.append("pbname", document.getElementById('id_newname').value);
      var filedom = document.getElementById('id_files');
      var files = filedom.files;
      if (files.length == 0) {
        _this.$message.error('未选择文件');
        return false;
      }
      for (var i = 0; i < files.length; i++) {
        fd.append(`File${i}`, files[i]);
      }
      _this.extdata.uploadm.canceltoken = _this.$axios.CancelToken;
      _this.extdata.uploadm.canceltoken_source = _this.extdata.uploadm.canceltoken.source();
     
      var config = {
        headers: {
          "Content-Type": "application/json"
        },
        onUploadProgress: progressEvent => {
          // 使用本地 progress 事件
          if (progressEvent.lengthComputable) {
            var num = Math.round(
              (progressEvent.loaded / progressEvent.total) * 100
            );
            _this.extdata.uploadprogress.isshowing = true;
            _this.extdata.uploadprogress.infotext = '正在上传文件...' + num + '%';
            _this.extdata.uploadprogress.pbwidth = num;

            // 
            if (progressEvent.loaded == progressEvent.total) {
              _this.extdata.uploadprogress.infotext = '服务器正在执行批处理...';
            }
          }
        },
        cancelToken: _this.extdata.uploadm.canceltoken_source.token
      };
    _this.$axios.post(
        `${window.bim_config.webserverurl}/api/Panorama/PanoramaFile/UploadImages?pbguid=${newid}&willappend=0&Token=${this.$staticmethod.Get('Token')}`,
        fd,
        config
      ).then(x => {
        // 关闭对话框并刷新
        _this.$message.success('上传成功');
        _this.extdata.uploadm.isshowing = false;
        _this.getList();
      }).catch(x => {
        _this.$message.warning('上传操作取消');
        _this.extdata.uploadm.disinput = false;
        _this.extdata.uploadm.isshowing = false;
      });

    },

    // 关闭上传
    // 取消请求
    _oncancel_upload(){
       
      // 取消请求
      // -------
      var _this = this;
      _this.extdata.uploadm.canceltoken_source
      && _this.extdata.uploadm.canceltoken_source.cancel('cancel');

      // 关闭上传
      // -------
      _this.extdata.uploadm.isshowing = false;
    },

    // // 弹出上传
    // xxtoUpload(ev) {
    //   var _this = this;
    //   _this.closeall();
    //   _this.extdata.uploadm.isshowing = true;
    //   _this.extdata.uploadm.disinput = false;
    //   _this.extdata.uploadprogress.isshowing = false;
    //   _this.extdata.uploadprogress.infotext = '';
    //   ev.stopPropagation();
    // },

    // 按 pbguid 移除
    delByPbGuid(pbguid) {
      var _this = this;
      _this.$axios({
        url:`${window.bim_config.webserverurl}/api/Panorama/PanoramaFile/RemoveItem&Token=${this.$staticmethod.Get('Token')}`,
        method:'post',
        data:_this.$qs.stringify(
          {
            PbGuid: pbguid
          }
        )
      }).then(x => {
        if (x.status == 200) {
          if (x.data.Ret > 0) {
            _this.getList();
          } else {
            _this.$message.error(x.data.Msg);
          }
        } else {
          debugger;
        }
      }).catch(x => {
        debugger;
      });
    },

    // 发布/重新发布到GIS
    rePubGis(ev) {
      var _this = this;
      _this.extdata.isShowGisSel = true;
      _this.$nextTick(x => {
        var ifr = document.getElementById('id_gis_sel');
        var srcurl = window.bim_config.webserverurl_gis;
        ifr.setAttribute("src", srcurl);
      });
    },

    // 弹出是否删除的对话框
    confirmToDel(ev) {
      var _this = this;
      _this.$confirm('确定删除该项？', '删除确认', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
         type: 'warning'
      }).then(x => {
        _this.delByPbGuid(_this.extdata.SelectedPbGuid);
        _this.closeall();
      }).catch(x => {
        _this.closeall();
      });
    },

    // 某一项显示的背景图
    computeItemImage(item) {
      var _this = this;
      var _s = {};
      _s["background-image"] = `url('${window.bim_config.integrated_innerview}/Panorama${item.PbUrl}/cover.png')`;//  'url(\'' + item.PbUrl + 'cover.png'+ '\')';
      return _s;
    },

    // 获取数据
    getList(){
      var _this = this;
      var _organizeId = _this.$staticmethod._Get("organizeId");

      // 显示loading
      // -----------
      var _LoadingIns = _this.$loading({
        text:'加载中',
        target: document.getElementsByClassName('_css-pano-bottom')[0]
      });

      setTimeout(() => {
         _this.$axios({
            method:'get',
            url:`${window.bim_config.webserverurl}/api/Panorama/PanoramaFile/GetList?organizeId=${_organizeId}&Token=${this.$staticmethod.Get('Token')}`,
            data:{}
          }).then(x => {
            _LoadingIns.close();
            if (x.status == 200) {
              if (x.data.Ret > 0) {
                _this.extdata.datalist = x.data.Data;
              } else {
                _this.$message.error(x.data.Msg);
              }
            } else {
              debugger;
            }
          }).catch(x => {
            _LoadingIns.close();
            debugger;
          });
      }, 0);

     
    },

    // 计算菜单样式
    getContextStyle(){
      var _this = this;
      var _s = {};
      _s["left"] = _this.extdata.contextpos.left + 'px';
      _s["top"] = _this.extdata.contextpos.top + 'px';
      return _s;
    },

    // 是否已发布到GIS
    hasGis(item){
      return item.PbGisinfo && item.PbGisinfo.indexOf('{') >= 0;
    },

    // 关闭全部
    closeall() {
      var _this = this;
      _this.extdata.contextpos.isshowing = false;
    },

    // 显示菜单
    showmenu(ev, item) {

      // 获取所点击元素（按钮）的top及left
      var _this = this;
      var rect = ev.target.getBoundingClientRect();

      // 修改“发布到gis”菜单的中文
      // item.PbGisinfo && item.PbGisinfo.indexOf('{') >= 0
      if (_this.hasGis(item)) {
        _this.extdata.gisbtntext = '重新发布到GIS';
      } else {
        _this.extdata.gisbtntext = '发布到GIS';
      }

      // 设置上下文菜单的left及top
      _this.extdata.contextpos.top = rect.y + 12;
      _this.extdata.contextpos.left = rect.x + 12;

      // 记录所点击的id
      _this.extdata.SelectedPbGuid = item.PbGuid;
      _this.m_selectedobj = item;

      // 记录上下文菜单的宽及高度
      var context_h = 144;
      var context_w = 160;

      // 根据当前屏幕高度及菜单本身高度，修改contextpos.top的值
      // _this.extdata.contextpos.left = _this.extdata.contextpos.left > document.body.clientWidth - context_w
      // ?document.body.clientWidth - context_w
      // :_this.extdata.contextpos.left;
      // _this.extdata.contextpos.top = _this.extdata.contextpos.top > document.body.clientHeight - context_h
      // ?document.body.clientHeight - context_h
      // :_this.extdata.contextpos.top;
      if (_this.extdata.contextpos.left > document.body.clientWidth - context_w) {
        _this.extdata.contextpos.left = document.body.clientWidth - context_w;
      }
      if (_this.extdata.contextpos.top > document.body.clientHeight - context_h) {
        _this.extdata.contextpos.top = document.body.clientHeight - context_h;
      }

      // 显示出来菜单
      _this.extdata.contextpos.isshowing = true;
      ev.stopPropagation();
    }
  }
};
</script>
<style scoped>
._css-pano-all {
  box-sizing: border-box;
  width: 100%;
  height: 100%;
}
._css-pano-top {
  height: 64px;
  display: flex;
  align-items: center;
}
._css-pano-bottom {
  height: calc(100% - 64px);
  box-sizing: border-box;
}
._css-pano-addbtn {
  width: 120px;
  height: 40px;
  border-radius: 3px;
  background-color: #1890ff;
  border: none;
  color: #fff;
  outline: none;
  margin-left: 24px;
  line-height: 40px;
  cursor: pointer;
  opacity: 0.8;
  text-align: center;
}
._css-pano-addbtn:hover {
  opacity: 1;
}
._css-pano-addbtn._css-dis {
  cursor: not-allowed;
  opacity: 0.3;
}
._css-pano-list {
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  display: flex;
  flex-wrap: wrap;
  overflow-y: auto;
  align-content: flex-start;
}

._css-pano-list._css-panolistempty {
  justify-content: space-around;
}

._css-pano-i {
  width: 245px;
  height: 260px;
  box-sizing: border-box;
  cursor: pointer;
  box-shadow: 0px 1px 1px 0px rgba(0, 21, 41, 0.12);
  margin-left: 24px;
  margin-top: 24px;
  background-color: #fff;
  display: flex;
  flex-direction: column;
  align-items: center;
  border-radius: 4px;
  position: relative;
}
._css-pano-gisflag{
  position: absolute;
  right:8px;
  top:8px;
  width:26px;
  height:26px;
  background-image: url('../../assets/svgs_loadbyurl/model_hasGIS.svg');
  background-size: contain;
    background-repeat: no-repeat;
}
._css-pano-i:hover {
  box-shadow: 0px 13px 24px -17px rgba(11, 41, 62, 0.8);
}
._css-pano-iimg {
  width: 233px;
  height: 178px;
  margin-top: 6px;
  border-radius: 4px;

  background-repeat: no-repeat;
  background-size: cover;
  background-position: center center;
}
._css-pano-itext {
  font-size: 16px;
  height: 38px;
  overflow-x: hidden;
  text-overflow: ellipsis;
  width: 100%;
  white-space: nowrap;
  box-sizing: border-box;
  padding: 0 8px 0 8px;
  line-height: 38px;
}
._css-pano-istatus {
  width: 100%;
  height: 38px;
  line-height: 38px;
  font-size: 12px;
  position: relative;
}
._css-pano-idelbtn {
  position: absolute;
  right: 12px;
  top: calc(50% - 9px);
  display: none;
  color: #999;
}
._css-pano-i:hover ._css-pano-idelbtn {
  display: block;
}
._css-pano-idelbtn:hover {
  color: #1890ff;
}
._css-pano-istatustext {
  color: #c4c4c4;
  text-align: center;
}
._css-pano-contextmenu {
  position: fixed;
  /* top: 0;
  left: 200px; */
  min-height: 48px;
  z-index: 4;
  box-shadow: 0px 0px 8px 0px rgba(0, 0, 0, 0.15);
  border-radius: 4px;
  width: 160px;
  background-color: #fff;
}
._css-pano-contextmenu-i {
  height: 48px;
  padding-top: 4px;
  padding-bottom: 4px;
  box-sizing: border-box;
}
._css-pano-contextmenu-in {
  height: 100%;
  width: 100%;
  cursor: pointer;
  display: flex;
  align-items: center;
}

._css-pano-contextmenu-in._css-dis {
  opacity: 0.5;
  cursor: not-allowed;
}

._css-pano-contextmenu-in:not(._css-dis):hover {
  background-color: rgba(0, 0, 0, 0.04);
}
._css-pano-contextmenu-inicon {
  width: 16px;
    line-height: 40px;
    height: 100%;
    margin-left: 24px;
    font-size: 16px;
}
._css-pano-contextmenu-intext {
  margin-left: 8px;
  font-size: 14px;
  height: 100%;
  line-height: 40px;
}
._css-pano-upload{
  z-index: 4;
  position: fixed;
  width:100%;
  height:100%;
  left:0;
  top:0;
  display: flex;
  align-items: center;
  justify-content: space-around;
  background-color: rgba(0, 0, 0, 0.3);
}
._css-pano-upload-in{
  width: 410px;
    /* min-height: 294px; */
    background: rgba(255, 255, 255, 1);
    -webkit-box-shadow: 0px 13px 24px -17px rgba(11, 41, 62, 0.8);
    box-shadow: 0px 13px 24px -17px rgba(11, 41, 62, 0.8);
    border-radius: 4px;
}
._css-upload-file-label {
  padding-left: 16px;
    box-sizing: border-box;
    font-size: 12px;
    color: rgba(0,0,0,0.5);
    margin-top: 4px;
    text-align: left;
}
._css-upload-file-label2 {
  padding-left: 16px;
    box-sizing: border-box;
    font-size: 12px;
    color: rgba(0,0,0,0.5);
    margin-top: 24px;
    text-align: left;
}
._css-upload-file-inputctn {
  height: 36px;
    width: 100%;
    box-sizing: border-box;
    padding: 0 16px 0 16px;
    margin-top: 4px;
    display: flex;
    align-items: center;
}
._css-upload-file-inputfile{
  width:100%;
}
._css-upload-file-inputname{
  width: 100%;
    outline: none;
    height: 24px;
    line-height: 24px;
    border: none;
    background-color: #fff;
}
._css-upload-file-inputshowpb {
  width: 100%;
    height: 24px;
    border-radius: 8px;
    overflow: hidden;
}
._css-upload-file-inputshowpb-in{
    height: 100%;
    background-color: #1890FF;
}
._css-upload-file-inputctnin{
      border-radius: 4px;
    width: 100%;
    border: 1px solid rgba(0, 0, 0, 0.3);
    height: 36px;
    display: flex;
    align-items: center;
    box-sizing: border-box;
    padding:0 8px 0 8px;
}
._css-pano-viewer {
    position: fixed;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    z-index: 1000;
    background-color: #fff;
}
iframe#id_panoifri {
    border: none;
    width: 100%;
    height: 100%;
}
._css-pano-close{
    font-size: 20px;
    flex: none;
    width: 30px;
    height: 30px;
    position: absolute;
    background-image: url(/Content/images/ProjectManage/Project/close_normal.png);
    background-repeat: no-repeat;
    cursor: pointer;
    color: rgba(0, 0, 0, 0.4);
    border: 1px solid transparent;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.8);
    right: 0;
    top: 0;
    line-height: 30px;
    display: flex;
    justify-content: space-around;
}
._css-gis-selectpoint {
    width: 80%;
    height: 80%;
    position: fixed;
    top: 10%;
    left: 10%;
    z-index: 4;
}
iframe#id_gis_sel {
    width: 100%;
    height: 100%;
    border: none;
}
</style>