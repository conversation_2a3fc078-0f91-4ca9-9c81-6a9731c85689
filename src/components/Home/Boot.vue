  <template>
	<div
		:data-debug_leftmenustate="_homedata.leftmenustate"
		class="css-bsb css-h100 _css-projectlistall"
	>
		<!-- 项目列表数据显示区域 -->
		<div
			class="css-h100 _css-boot-whole"
			style="z-index: 0"
			@click.stop="hideall"
		>

			<!-- 上面的创建按钮，切换视图模式及搜索 -->
			<div v-show="extdata.smallScreenShow">
				<div
					class="_css-boot-funcarea"
				>
					<el-row class="_css-issue-headinner">
						<el-col :span="18" class="_css-issue-headinneritem">
							<template>
								<!-- <div class="_css-logoboot" :style="logos1tyle" :class="{'temp-longlogo':willshow1longlogo()}"></div> -->
								<img class="_css-logoboot" :src="logosrc" />
								<div class="_css-organize-namearea-text">
									{{ extdata.info_companyname }}
								</div>

							</template>
						</el-col>

						<el-col :span="6" class="_css-issue-headinneritem">
							<!-- 项目内页的右上角按钮组 -->
							<div class="_css-righttop-group">
								<!-- 用户头像、邀请按钮、消息按钮、全屏按钮 -->
								<el-tooltip
									popper-class="css-no-triangle"
									effect="dark"
									content="个人信息"
									placement="bottom"
									v-if="getUserShow()"

								>
									<div
										class="_css-currentuser-icon css-prel"
										@click.stop="toggleUserMenuListState"
									>
										<div>{{ RealNameLastName }}</div>

										<CompsCurrentUserMenuList
											@onitemclick="itemclick"
											:toggleState="currentMenuListState"
                      :isComMgr="extdata.is_commgr"
											ref="userMenuList"
										></CompsCurrentUserMenuList>
									</div>
								</el-tooltip>

								<!-- 机构管理按钮（齿轮） -->

								<div
									class="_css-commgr-btn"
									@click.stop="turnToComMgr()"
									v-if="extdata.is_commgr == true"
								>
									<el-tooltip
										popper-class="css-no-triangle"
										effect="dark"
										content="机构管理"
										placement="bottom"
									>
										<div
											class="icon-interface-set_se basic-btn-normal-size basic-btn-normal-color"
										></div>
									</el-tooltip>
								</div>

								<!-- //机构管理按钮（齿轮） -->

								<!-- <div
									v-if="extdata._currentisProjectManager == true"
									class="_css-invite-outer css-fc"
									@click.stop="showInvite"
								>
									<div
										class="css-bsb css-b css-cp icon-interface-user_fill basic-btn-normal-color basic-btn-normal-size"
									></div>
								</div> -->

								<div
									class="_css-bell-outer css-fc"
									v-if="getConfigBell()"
									@click.stop="OpenMiniMessage()"
								>
									<el-tooltip
											popper-class="css-no-triangle"
											effect="dark"
											content="消息提醒"
											placement="bottom"
										>
											<el-badge :is-dot="hasUnreadMsg && isDot">
												<div
													class="css-bsb css-b css-cp icon-interface-bell basic-btn-normal-color basic-btn-normal-size css-global-bell"
												>
												</div>
											</el-badge>
									</el-tooltip>
								</div>
								<div class="_css-bell-outer css-fc" @click.stop="">
									<el-tooltip
										popper-class="css-no-triangle"
										effect="dark"
										content="机构日志"
										placement="bottom"
									>
										<el-button
											@click="OrganizationLog()"
											:type="extdata.showtype === 3 ? 'primary' : ''"
											icon="icon-interface-log _css-toptype-btn "
											class="_css-btn4832 css-br2 css-bordernoneim basic-btn-normal-color"
											:class="{ '_css-bgcolor_trans': extdata.showtype !== 3 }"
										>
										</el-button>
									</el-tooltip>
								</div>
								<!-- <div class="_css-bell-outer css-fc" v-if="hadenabledbimcomp()">
									<el-tooltip
										popper-class="css-no-triangle"
										effect="dark"
										content="构件库管理"
										placement="bottom"
									>
										<el-button
											@click="OrganizationComponentLibrary()"
											:type="extdata.showtype === 2 ? 'primary' : ''"
											icon="icon-interface-clan _css-toptype-btn"
											class="_css-btn4832 css-br2 css-bordernoneim basic-btn-normal-color"
											:class="{ '_css-bgcolor_trans': extdata.showtype !== 2 }"
										>
										</el-button>
									</el-tooltip>
								</div> -->


								<div class="_css-fullscroll-outer" @click.stop="changeisfull">
									<el-tooltip
										popper-class="css-no-triangle"
										effect="dark"
										:content="extdata._isfullscreen ? '取消全屏' : '全屏'"
										placement="bottom"
									>
										<div
											class="css-bsb css-b css-cp basic-btn-normal-color basic-btn-normal-size"
											:class="
												extdata._isfullscreen
													? 'icon-arrow-fullscreen_exit'
													: 'icon-arrow-fullscreen'
											"
										></div>
									</el-tooltip>
								</div>

								<div
									class="_css-fullscroll-outer"
									v-if="getIsSystem()"
									@click.stop="backUpToAdmin"
								>
									<el-tooltip
										popper-class="css-no-triangle"
										effect="dark"
										:content="'回到机构列表'"
										placement="bottom"
									>
										<div
											class="css-bsb css-b css-cp basic-btn-normal-color basic-btn-normal-size icon-interface-restore"
										></div>
									</el-tooltip>
								</div>

								<!-- 大屏入口 -->
								<div
									class="_css-fullscroll-outer"
									v-if="notdisabled_bigScreen()"
								>
									<el-tooltip
										popper-class="css-no-triangle"
										effect="dark"
										content="数据可视化大屏"
										placement="bottom"
									>
										<div
											class="css-bsb css-b css-cp basic-btn-normal-color basic-btn-normal-size"
											:class="'icon-interface_bigscreen _css-formexcel'"
											@click.stop="screenJump"
										></div>
									</el-tooltip>
								</div>
								<!-- //用户头像、邀请按钮、消息按钮、全屏按钮 -->
							</div>
							<!-- //项目内页的右上角按钮组 -->
						</el-col>
					</el-row>
				</div>
			</div>
			<!-- //上面的创建按钮，切换视图模式及搜索 -->
			<!-- 项目列表展示 -->
			<div class="_css-small-screen-show-top" v-show="!extdata.smallScreenShow">
				<div class="_css-small-screen-show">
					项目列表
					<span
						class="_css-typeitem-close icon-interface-guanbimoxing"
						@click.stop="smallScreenClose()"
					></span>
				</div>
				<div class="_css-funcarea_search-small">
					<CompsSearchInput
						style="width: 270px"
						v-show="extdata.showtype == 0"
						placeholder="请输入项目名称"
						v-model="extdata.keyword"
					></CompsSearchInput>
				</div>
			</div>
			<!-- 项目列表展示 -->

			<!-- 新增项目按钮及切换视图按钮 -->
			<div
				class="_css-newlist-funcarea"
				v-show="extdata.smallScreenShow"
			>
				<div class="css-h100 css-bsb css-fc _css-newlist-funcarea-in">
					<!-- 两个按钮：发起问题，过滤 -->

					<div class="css-zbutton-textname" @click="btncreateissue_click">新建项目</div>
					<!--	:init_text="'新建项目'"这部分在做项目复制时候在打开，暂时不需要做，先注释掉  -->
					<!-- <zbutton-function
						v-if="extdata.is_commgr && extdata.showtype == 0"
						:init_text="'新建项目'"
						:init_fontsize="14"
						:debugmode="true"
						:init_height="undefined"
						:init_width="undefined"
						:init_ifchildren="m_createbtns"
						@onclick="_createprojectbtns"
					>
					</zbutton-function> -->

					<!-- 拷贝项目对话框 -->
					<zdialog-function
						:init_id="'zdialog_id_copycreateproj'"
						v-if="copyproj_show"
						:init_title="'从项目创建'"
						:init_zindex="2"
						:init_innerWidth="undefined"
						:init_width="undefined"
						:debugmode="false"
						init_closebtniconfontclass="icon-suggested-close"
						@onclose="_closecopyproj()"
					>
						<!-- 暂用v-show来控制 -->
						<div
							v-show="!copyproj_status"
							slot="mainslot"
							class="_css-copydialogcenter"
						>
							<div class="_css-copyprojseltitle">项目</div>
							<zselect-function
								ref="ref_copytemplateid"
								:init_items="project_copy_selectarr"
								:init_width="'300px'"
								:init_height="40"
								:init_listzindex="undefined"
								:init_iconclass="'icon-arrow-down_outline'"
								:debugmode="false"
								placeholder="请选择要复制的项目"
							></zselect-function>
							<div class="_css-copynuminput">数量</div>
							<div class="_css-copynuminput-ctn">
								<input
									id="id_copyproj_num"
									@mousedown="_stopPropagation($event)"
									@input="copynum_valid($event)"
									class="_css-copynuminput-ctn-in"
									placeholder="请输入要复制的数量（1~99）"
									type="text"
								/>
							</div>
							<div @click="copyProjAdv" class="_css-copyprojAdvCfg">
								高级设置
							</div>
						</div>
						<!-- //暂用v-show来控制 -->

						<!-- 点击了开始 -->
						<div
							v-show="copyproj_status == 'running'"
							slot="mainslot"
							class="_css-copydialogcenter"
						>
							<!-- 显示数量 -->
							<div class="_css-copy-runningnum">
								{{ copyproj_num_finished }}/{{ copyproj_num }}
							</div>
							<!-- //显示数量 -->

							<!-- 显示进度条 -->
							<div class="_css-copy-runningshow">
								<zprogressbar-function
									ref="ref_copyprogressbar"
									:init_width="'100%'"
									:init_height="undefined"
									:debugmode="false"
								>
								</zprogressbar-function>
							</div>
							<!-- //显示进度条 -->

							<!-- 显示描述 -->
							<div class="_css-copy-runningdesc">内容测试</div>
							<!-- //显示描述 -->
						</div>
						<!-- //点击了开始 -->

						<div slot="buttonslot" class="_css-copyprojBtnCtn">
							<zbutton-function
								v-show="copyproj_status == 'running'"
								:init_text="'取消'"
								:init_bgcolor="'rgba(245, 34, 45, 1)'"
								:init_fontsize="14"
								:debugmode="true"
								:init_height="undefined"
								:init_width="'300px'"
								@onclick="_cancelcopycreate"
								:init_ifchildren="[]"
							>
							</zbutton-function>
							<zbutton-function
								v-show="!copyproj_status"
								:init_text="'开始'"
								:init_fontsize="14"
								:debugmode="true"
								:init_height="undefined"
								:init_width="'300px'"
								@onclick="_startcopycreate"
								:init_ifchildren="[]"
							>
							</zbutton-function>
						</div>
					</zdialog-function>
					<!-- //拷贝项目对话框 -->

					<!-- //拷贝项目-高级设置对话框 -->
					<zdialog-function
						:init_id="'zdialog_id_copyAdvCfg'"
						v-if="copyprojadmin_show"
						:init_title="'高级设置'"
						:init_zindex="2"
						:init_innerWidth="undefined"
						:init_width="undefined"
						:debugmode="false"
						init_closebtniconfontclass="icon-suggested-close"
						@onclose="_closecopyprojadmin()"
					>
						<div slot="mainslot" class="_css-copydialogcenter-itemctn">
							<div
								@click="
									copycfgcache.hasSelected_auth = !copycfgcache.hasSelected_auth
								"
								class="_css-copydialogcenter-item"
							>
								<div
									class="_css-copycfg-checkbox"
									:class="{
										'mulcolor-interface-checkbox-selected':
											copycfgcache.hasSelected_auth,
									}"
								></div>
								<div class="_css-copycfg-label">同步人员权限</div>
							</div>
							<div
								@click="
									copycfgcache.hasSelected_doc = !copycfgcache.hasSelected_doc
								"
								class="_css-copydialogcenter-item"
							>
								<div
									class="_css-copycfg-checkbox"
									:class="{
										'mulcolor-interface-checkbox-selected':
											copycfgcache.hasSelected_doc,
									}"
								></div>
								<div class="_css-copycfg-label">同步文档数据</div>
							</div>
							<div
								@click="
									copycfgcache.hasSelected_model =
										!copycfgcache.hasSelected_model
								"
								class="_css-copydialogcenter-item"
							>
								<div
									class="_css-copycfg-checkbox"
									:class="{
										'mulcolor-interface-checkbox-selected':
											copycfgcache.hasSelected_model,
									}"
								></div>
								<div class="_css-copycfg-label">同步模型数据</div>
							</div>
							<div
								@click="
									copycfgcache.hasSelected_progress =
										!copycfgcache.hasSelected_progress
								"
								class="_css-copydialogcenter-item"
							>
								<div
									class="_css-copycfg-checkbox"
									:class="{
										'mulcolor-interface-checkbox-selected':
											copycfgcache.hasSelected_progress,
									}"
								></div>
								<div class="_css-copycfg-label">同步进度管理数据</div>
							</div>
							<div
								@click="
									copycfgcache.hasSelected_issue =
										!copycfgcache.hasSelected_issue
								"
								class="_css-copydialogcenter-item"
							>
								<div
									class="_css-copycfg-checkbox"
									:class="{
										'mulcolor-interface-checkbox-selected':
											copycfgcache.hasSelected_issue,
									}"
								></div>
								<div class="_css-copycfg-label">同步问题数据</div>
							</div>
						</div>

						<div slot="buttonslot" class="_css-copyprojBtnCtn">
							<zbutton-function
								:init_text="'确定'"
								:init_fontsize="14"
								:debugmode="true"
								:init_height="undefined"
								:init_width="'300px'"
								@onclick="_save_copycfg"
							>
							</zbutton-function>
						</div>
					</zdialog-function>
					<!-- //拷贝项目-高级设置对话框 -->

					<!-- 右侧剩余空间 -->
					<!-- <div class="_css-funcarea-right">
						<div
							@click.stop="sorttype_click"
							class="_css-funcarea-sorttype css-usn css-cp"
							:class="{ _clicked: extdata.isshowing_sortoptions == true }"
							v-if="extdata.showtype == 0"
						>
							<div class="_css-sorttype-text">{{ extdata.sortlabel }}</div>
							<div class="_css-sorttype-icon icon-arrow-down"></div>
							<div
								class="_css-sorttype-options"
								@click="clickOutClose"
								:class="{ _open: extdata.isshowing_sortoptions == true }"
							>
								<div
									class="_css-sorttype-optionitem"
									@click.stop="sorttypechange(null, '最新创建')"
								>
									最新创建
								</div>
								<div
									class="_css-sorttype-optionitem"
									@click.stop="sorttypechange('CreateDate%20asc', '最早创建')"
								>
									最早创建
								</div>

								<div
									class="_css-sorttype-optionitem"
									@click.stop="sorttypechange('FullName%20asc', '名称正序')"
								>
									名称正序
								</div>

								<div
									class="_css-sorttype-optionitem"
									@click.stop="sorttypechange('FullName%20desc', '名称倒序')"
								>
									名称倒序
								</div>
							</div>
						</div>
						<div
							class="sorttype-mask"
							@click="extdata.isshowing_sortoptions = false"
							v-if="extdata.isshowing_sortoptions"
						></div>
					</div> -->
					<!-- //右侧剩余空间 -->

					<div class="_css-funcarea_search_input">
						<CompsSearchInput
							v-show="extdata.showtype == 0"
							placeholder="请输入项目名称"
							v-model="extdata.keyword"
						></CompsSearchInput>
					</div>
				</div>
			</div>
			<!-- //新增项目按钮及切换视图按钮 -->

			<section
				v-if="extdata.showtype == 0 && extdata.canshowlist == true"
				id="_id_loading_projects"
				:style="getStyleSmallScreen()"
				class="_css-projectlist-body css-bsb css-usn css-miniscroll css-noscroll"
				@scroll="projectlistscroll($event)"
			>
				<div
					@click="enterproject(item.ProjectId,item.CopyFlowStatus,item.ProjectName)"
					:style="getStyleOfsmallImg()"
					class="_css-projectitem css-cp"
					:class="{
						'css-nonvisibility': item.ProjectId == -1,
					}"
					v-for="(item, index) in extdata.projectdatas"
					:data-index="index"
					:key="index"
					:title="item.ProjectName"
				>
					<!-- 项目图片显示区域 -->
					<div class="_css-projectitemimg" :style="getStyleOfPj(item)"></div>
					<!-- //项目图片显示区域 -->

					<!-- 项目名称显示区域 6 h24 6 mt 8 -->
					<div class="_css-projectitem-fullname">{{ item.ProjectName }}</div>
					<!-- //项目名称显示区域 6 h24 6 mt 8 -->

					<!-- 隶属于-显示区域 -->
					<div
						:title="item.OrganizeName || '<无>'"
						class="_css-projectitem-obeycom"
					>
						隶属于：{{ item.OrganizeName || "<无>" }}
					</div>

					<!-- <div
						class="_css-projectitem-obeycom _css2"
					>
						到期时间：{{
							item.ExpiryTime ? item.ExpiryTime.split(" ")[0] : "&lt;无&gt;"
						}}
					</div> -->
					<!-- 底部 -->
					<div
						class="_css-projectitem-bottom"
						v-if="false"
					>
					<!-- v-if="false" 谢鹏提出的需求，要隐藏 -->
						<div class="_css-pi-bottom-left">
							<!-- 显示姓名或模型数量 -->
							<div class="_css-pi-bottom-lefticon">
								{{ (item.OrganizeManager || "") | setAbbreviation }}
							</div>

							<!-- <div
								v-if="Iscockpit == '1'"
								class="_css-pi-bottom-lefttext cockpit"
								@click.stop="
									VisualizationPanel_open(item.ProjectId)
								"
							>
								驾驶舱
							</div> -->

						</div>
						<div
							v-if="get_mask_ispublic()"
							class="_css-pi-bottom-ispub"
							:class="item.IsPublic ? '_ispub': '_isnotpub'"
						>
							{{ item.IsPublic ? "公开" : "非公开" }}
						</div>
					</div>
					<!-- //底部 -->
				</div>
			</section>
		</div>
		<!-- //项目列表数据显示区域 -->
		<!-- 创建项目对话框 -->
		<div class="_css-createproj" v-if="extdata.iscreatingproj">
			<div class="_css-createproj-in">
				<CompsDialogHeader
					@oncancel="creatingproj_cancel"
					:title="'新建项目'"
				></CompsDialogHeader>
				<div class="_css-createproj-m">
					<div class="_css-ispublic-select">
						<!-- 下拉框 -->
						<CompsEloSelect
							:datas="extdata.creatingprojdata.isPublicDatas"
							:text="extdata.creatingprojdata.text"
							width="76px"
							@onselected="ispubselected"
						></CompsEloSelect>
						<!-- //下拉框 -->
					</div>

					<div class="_css-proj-name">
						<!-- 新建项目的名称输入框 -->
						<CompsUsersInput
							placeholder="请输入项目名称"
							width="100%"
							@oninput="_onnewprojectinput"
						></CompsUsersInput>
						<!-- //新建项目的名称输入框 -->
					</div>


					<!-- 项目名称 -->
					<!-- //项目名称 -->
				</div>
				<div class="_css-createproj-m css-mt12">
					<div class="_css-ispublic-select css-line-height">
						项目编码
					</div>

					<div class="_css-proj-name">
						<!-- 新建项目的名称输入框 -->
						<CompsUsersInput
							placeholder="请输入项目编码"
							width="100%"
							@oninput="_onnewprojectinputprojectCode"
						></CompsUsersInput>
						<!-- //新建项目的名称输入框 -->
					</div>
				</div>
				<CompsDialogBtns
					@onok="creatingproj_ok"
					@oncancel="creatingproj_cancel"
				></CompsDialogBtns>
			</div>
		</div>
		<!-- //创建项目对话框 -->

		<!-- 下载模态框  :before-close="handleClose"关闭确认事件 暂时没用到 看后续要不要加-->
		<el-dialog title="下载" :visible.sync="download_box" width="410px">
			<!-- Revit导出插件 -->
			<div class="Revit_ExportPlug">
				<div class="Revit_ExportPlug_left">
					<div
						class="Revit_ExportPlug_left_icon Revit_ExportPlug_left_icon_Revit"
					></div>
					<div class="Revit_ExportPlug_left_text">Revit转换插件</div>
				</div>
				<div class="Revit_ExportPlug_right" @click.stop="downloadrevit()">
					下载
				</div>
			</div>
			<!-- Revit导出插件 -->

			<!-- Naviswork导出插件 -->
			<div class="Revit_ExportPlug">
				<div class="Revit_ExportPlug_left">
					<div
						class="Revit_ExportPlug_left_icon Revit_ExportPlug_left_icon_Revit_Naviswork"
					></div>
					<div class="Revit_ExportPlug_left_text">Naviswork转换插件</div>
				</div>
				<div class="Revit_ExportPlug_right" @click.stop="downloadnaviswork()">
					下载
				</div>
			</div>
			<!-- Naviswork导出插件 -->

			<!-- Sketch Up导出插件 -->
			<div class="Revit_ExportPlug">
				<div class="Revit_ExportPlug_left">
					<div
						class="Revit_ExportPlug_left_icon Revit_ExportPlug_left_icon_Revit_Sketch"
					></div>
					<div class="Revit_ExportPlug_left_text">Sketch Up转换插件</div>
				</div>
				<div class="Revit_ExportPlug_right" @click.stop="downloadsketchup()">
					下载
				</div>
			</div>
			<!-- Sketch Up导出插件 -->

			<!-- 其他导出插件 -->
			<div class="Revit_ExportPlug">
				<div class="Revit_ExportPlug_left">
					<div
						class="Revit_ExportPlug_left_icon Revit_ExportPlug_left_icon_Revit_other"
					></div>
					<div class="Revit_ExportPlug_left_text">Microstation转换插件</div>
				</div>
				<div
					class="Revit_ExportPlug_right"
					@click.stop="microstationdownload()"
				>
					下载
				</div>
			</div>
			<!-- 其他导出插件 -->

			<!-- footer部分 -->
			<div class="dialog-footer">
				<div class="dialog-footer-right" @click="download_box = false">
					关闭
				</div>
			</div>
			<!-- footer部分 -->
		</el-dialog>
		<!-- 下载模态框 结束 -->
    <!-- app更新记录 -->
    <el-dialog
      class="app-dialog"
      title="APP更新"
      :visible.sync="isShowUpdateList"
      width="430px" >
      <el-table
        :fixed="true"
        highlight-current-row
        :header-cell-style="{'background-color':'#F8F8F8'}"
        :data="appVersionData"
        height="300"
        style="width: 100%;overflow: auto">
        <el-table-column
          label="版本号"
          width="100">
          <template slot-scope="scope">
            <span>{{ scope.row.AppCode }}</span>
          </template>
        </el-table-column>
        <el-table-column
          label="上传时间"
          width="200">
          <template slot-scope="scope">
            <span>{{ scope.row.CreateTime }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作">
          <template slot-scope="scope">
            <img
              style="cursor: pointer"
              width="15"
              height="15"
              src="../../assets/images/app-delete.png"
              @click="deleteApk(scope.row.Id)" alt=""/>
          </template>
        </el-table-column>
      </el-table>
      <!-- footer部分 -->
      <div class="app-dialog-footer">
        <div class="app-dialog-footer-right" @click="openUpdate">
          新增
        </div>
        <div class="app-dialog-footer-left" @click="isShowUpdateList = false">
          取消
        </div>
      </div>
      <!-- footer部分 -->
    </el-dialog>
    <!-- app更新记录 -->
    <!-- app上传更新 -->
    <el-dialog
      :before-close="closeUpdate"
      class="app-dialog"
      title="新增版本"
      :visible.sync="isShowUpdate"
      width="422px" >
      <div class="version-input-wrapper">
        <span>版本号：</span>
        <el-input
          placeholder="输入版本号 ( 例如: 1.0.0 )"
          v-model="appVersion">
        </el-input>
      </div>
      <div class="version-select-wrapper">
        <span class="tooltip">安装包：</span>
        <div class="upload-wrapper">
          <el-upload
            style="display: flex"
            action=""
            accept=".apk"
            :show-file-list="false"
            :auto-upload="false"
            :on-change="onSelectChange">
            <div class="button-upload">上传文件</div>
          </el-upload>
          <div class="apk-file" v-if="appFileName">
            <span>{{appFileName}}</span>
          </div>
        </div>
      </div>
      <!-- footer部分 -->
      <div class="app-dialog-footer">
        <div class="app-dialog-footer-right" @click="beginUpdate">
          确定
        </div>
        <div class="app-dialog-footer-left" @click="closeUpdate">
          取消
        </div>
      </div>
      <!-- footer部分 -->
    </el-dialog>
    <!-- app上传更新 -->
		<Message2
			v-if="IsShowMessageMini"
			ref="messageref"
			@OnShow="onMessageDialogShow"
			@OpenMessageList="onOpenMessageList"
      @updateDotStatus = getDotStatus
		></Message2>
		<MessageList2
			v-if="IsShowMessageList"
			@CloseList="IsShowMessageList = false"
		></MessageList2>
	</div>
</template>
<script>
import CompsLeftMenuItem from "@/components/CompsBoot/CompsLeftMenuItem";
import CompsUsersInput from "@/components/CompsAuth/CompsUsersInput";
import CompsCurrentUserMenuList from "@/components/CompsCustom/CompsCurrentUserMenuList";
import CompsDialogHeader from "@/components/CompsDialog/CompsDialogHeader";
import CompsDialogBtns from "@/components/CompsDialog/CompsDialogBtns";
import CompsEloSelect from "@/components/CompsElOverride/CompsEloSelect";
import CompsSearchInput from "@/components/CompsCommon/CompsSearchInput";
import Message2 from "@/components/Home/ProjectBoot/Message2";
import MessageList2 from "@/components/Home/ProjectBoot/Message/MessageList2";
import {IS_COM_MGR_MUTATION} from "../../store/types/mutation-types";
import {setProjectName} from "../../utils/projectName";
export default {
	data() {
		return {
      isDot: true,
      loading:true,
      appFileName:null,
      appVersion: null,
      appFile: null,
      appVersionData:[],
			ComponentLibraryOrganized: null,
			loginOrganized: "",
			m_createbtns: [
				{ text: "直接创建", sign: "" },
				{ text: "从项目创建", sign: "copycreate" },
			],
			// 选中的父级项目
			parentObj: undefined,

			//gis项目列表数据
			project_gis_list: [],
			//gis项目列表hover数据
			project_gis_dialog: {
				show: false,
				top: 0,
				left: 0,
				defaultImg: require("../../assets/images/default.jpg"),
				data: {},
				timer: null,
			},
			m_lastsortstr: "",
			prjModelCnt: [],
			bZSJ_ShowConstructionUnitAndSoOn: undefined, // 是否显示为珠三角定制界面（显示施工单位、监理单位及模型个数）
			copyproj_canceltoken: undefined,
			copyproj_canceltoken_source: undefined,
			copyproj_timeoutH: 0,
			copyproj_isquerying: false, // 正在请求拷贝创建
			copyproj_percent: 0, // 进度条百分比显示
			copyproj_templateid: "", // 选择的模板项目
			copyproj_num_finished: 0, // 已拷贝完成的项目个数
			copyproj_failednum: 0, // 失败的个数
			copyproj_num: 0, // 输入的拷贝项目个数
			lastCopyNum: 1,
			project_copy_selectarr: [],
			copyproj_status: undefined, // undefined: 未开始， 'running': 进行中。控制是否显示进度条（running 显示）。
			copyproj_show: false,
			copyprojadmin_show: false,
			copycfg: {
				hasSelected_auth: false,
				hasSelected_doc: false,
				hasSelected_model: false,
				hasSelected_progress: false,
				hasSelected_issue: false,
			},
			// 点击时，直接操作本变量
			// 高级设置打开时，copycfg赋值给此
			// 保存时，本变量赋值给copycfg
			// 取消时，不做操作
			copycfgcache: {
				hasSelected_auth: false,
				hasSelected_doc: false,
				hasSelected_model: false,
				hasSelected_progress: false,
				hasSelected_issue: false,
			},

			MessageInfo: { dataSource:{}, msgFromSignalR:false }, // 消息数据
			currentMenuListState: false, //用户菜单列表状态
			screenWidth: document.body.clientWidth,
			smallScreenStyle: {
				top: "136px",
				height: "calc(100% - 136px - 24px);",
			},
			IsShowMessageMini: false, //显示消息弹窗
			IsShowMessageList: false,
			extdata: {

				// 在线预览类型及地址
				_docviewtype: "",
				_idocviewurl: "",
				smallScreenShow: true, // 北京院小屏展示
				gisloaded: false,
				sortlabel: "最新创建", // 排序方式
				isshowing_sortoptions: false, // 正在显示排序选项
				canshowlist: false, // 可以显示矩阵视图了（需要优先加载gis视图）
				is_commgr: false, // 有创建项目权限
				info_companyname: "", // 左上角公司名称
				info_companylogo: "", // 左上角logo base64str
				creatingprojdata: {
					hasmakesureclose: false, // 已经确认了要关闭正在创建项目的窗体
					hcreatingtip: "", // 创建时提示框的句柄
					newpjname: "", // 正在创建项目的项目名称
					text: "非公开",
					id: "0",
					isPublicDatas: [
						{ id: "0", text: "非公开" },
            { id: "1", text: "公开" }
					],
					coordinary: {}, // 正在创建项目的相关数据
					address: "", // 正在创建项目的地址名称
					projectID: "", // 正在创建项目的项目ID
					projectCode: "", // 正在创建项目的项目编码
				},
				iscreatingproj: false, // （控制是否显示创建项目的小对话框（包含项目名称输入））
				_isfullscreen: false, // 当前是否为全屏
				gisisready: false,
				showcreategis: false, // 控制是否显示创建项目的GIS界面
				showtype: 0,  // 0为默认，1为GIS，2为族库
				menuitem_activeid: "allprojects", // 激活的菜单项 "allprojects" "usually" "teammgr"
				keyword: "", // 搜索使用的关键字
				sortstr: null,
				lastkeyword: "", // 上一次搜索使用的关键字（下拉时使用）
				//projectdatas_cache:[], // 项目列表缓存数据（搜索结果显示前缓存全体到这里）
				projectdatas: [], // 项目列表数据，可能包含占位数据
				everytake: 17, // 每次下拉追加新请求的数据个数
				hasfinishied: false, // true 表示已经加载完全部的项目数据了
				total: 0, // 条件后，分页前的总项目数
			},
			download_box: false, //下载模态框 默认关闭
			Iscockpit: "", //是否隐藏驾驶舱功能，0隐藏，1展示
      isShowUpdate: false,
      isShowUpdateList: false
  };
	},
	components: {
		CompsLeftMenuItem,
		CompsUsersInput,
		CompsCurrentUserMenuList,
		CompsDialogHeader,
		CompsDialogBtns,
		CompsEloSelect,
		CompsSearchInput,
		Message2,
		MessageList2,
	},
	watch: {
		screenWidth(val) {
			if (!this.timer) {
				// 一旦监听到的screenWidth值改变，就将其重新赋给data里的screenWidth
				this.screenWidth = val;
				this.timer = true;
				let _this = this;
				setTimeout(function () {
					console.log(_this.screenWidth);
					_this.timer = false;
					let _smallScreenWidth = _this.$smallScreenWidth;
					_this.screenWidth < _smallScreenWidth
						? (_this.extdata.smallScreenShow = false)
						: (_this.extdata.smallScreenShow = true);
				}, 400);
			}
		},
		"extdata.keyword": {
			handler: function (newval, oldval) {
				// var _this = this;
				// _this.loadProjectDatas({
				// 	Skip: 0,
				// 	Take: 999,
				// 	keyword: _this.extdata.keyword,
				// 	reloadall: true,
				// 	sorttype: _this.m_lastsortstr,
				// });
				this.getProjectPagedList()
			},
		},
	},
  mounted() {


		var _this = this;

		window.bootvue = _this;

		// 设置 cancelToken
		_this.copyproj_canceltoken = _this.$axios.CancelToken;
		_this.copyproj_canceltoken_source = _this.copyproj_canceltoken.source();

		// 调用接口，获取消息
		_this.getMessage();

		_this.$emit("set_extdata", "ellipsislogotext", false);
		_this.$emit("set_extdata_hidecollapsebtn", 1);
		_this.$emit("set_extdata_collapse", 0);
		_this.$emit("onload", "projectlist");
		this.$staticmethod.Set("editGanttShow", "");
		this.$staticmethod.Set("currentGanttDataInfo", "");


		// 加载左侧菜单项（项目列表的）

		_this.loadtokencominfo();
		_this.loadcompanyitems();

		// _this.loadProjectDatas({
		// 	Skip: 0,
		// 	Take: 999,
		// 	keyword: "",
		// 	reloadall: true, // 指定该值会重置 total, hasfinishied, 及 projectdatas。页面初始化可视为 reloadall
		// });

		// _this.listenwindowresize();
		window.addEventListener('resize', this.onWindowResize);
    // 注册更新消息事件
		_this.$Bus.$on('UpdateMsg', this.handleUpdateMsg)

		_this.extdata.canshowlist = true;
		_this.getScreenWidthChange();
		// window.onresize = () => {
		// 	return (() => {
		// 		window.screenWidth = document.body.clientWidth;
		// 		_this.screenWidth = window.screenWidth;
		// 	})();
		// };

		sessionStorage.removeItem("menuText");
		sessionStorage.removeItem("defaultActive");
		sessionStorage.removeItem("menuListHasAuth");
    this.getProjectPagedList();
	},
  beforeDestroy() {
    this.$Bus.$off('UpdateMsg',this.handleUpdateMsg)
    window.removeEventListener('resize',this.onWindowResize)
  },
	props: {
		_homedata: {
			type: Object,
			required: true,
		},
	},
	methods: {
    getDotStatus(isDot){
      this.isDot = isDot;
      console.log('isDot',isDot)
    },
    /**
     * 删除单体数据
     */
    async deleteApk(Id){
      const res = await this.$api.DeleteApp({
        Token: this.$staticmethod.Get("Token"),
        Id
      })
      if (res.Ret === 1){
        this.$message.success('删除成功！')
        await this.getAppVersionsList()
      }else {
        this.$message.error(res.Msg)
      }
    },
    /**
     * 文件选择变化时
     * @param file
     */
    onSelectChange (file) {
      console.log('file',file)
      this.appFileName = file.name
      this.appFile = file.raw
    },
    /**
     * 打开更新app
     */
    openUpdate(){
      this.isShowUpdate = true
      this.$nextTick(()=>{
        this.isShowUpdateList = false
      })
    },
    /**
     * 取消上传
     */
    closeUpdate(){
      this.appVersion = null
      this.isShowUpdate = false
      this.appFileName = null
      this.appFile = null
    },
    /**
     * 调用接口上传apk
     */
    async beginUpdate() {
      if (!this.appVersion){
        this.$message.warning('请填写版本号！')
        return
      }
      if (!this.appFile){
        this.$message.warning('请选择文件！')
        return
      }
      const form = new FormData()
      form.append('Token',this.$staticmethod.Get("Token"))
      form.append('File',this.appFile)
      form.append('AppCode',this.appVersion)
      form.append('AppType','Android')
      const loading = this.$loading({
        target: 'el-dialog'
      });
      const res = await this.$api.AddAppVersions(form)
      if (res.Ret === 1){
        this.$message.success('上传成功！')
        this.closeUpdate()
      }else {
        this.$message.error(res.Msg)
      }
      loading.close()
    },
    /**
     * 获取更新列表
     */
    async getAppVersionsList(){
      this.isShowUpdateList = true;
      const res = await this.$api.GetAppVersions({
        Token: this.$staticmethod.Get("Token"),
        PageNum :1,
        PageSize: 999
      })
      if (res.Ret === 1){
        this.appVersionData = res.Data.Data
      }else {
        this.$message.error(res.Message)
      }
    },
    // 响应窗口resize事件:整合原listenwindowresize方法逻辑和onresize指定函数的逻辑
	onWindowResize() {
      const _this = this
      window.screenWidth = document.body.clientWidth;
	    _this.screenWidth = window.screenWidth;

      const counteveryline = _this.getcounteveryline();
			_this.extdata.hasfinishied = false;
			_this.projectdata_repatch(counteveryline);
    },
    onMessageDialogShow() {
      this.MessageInfo.msgFromSignalR = false // 显示消息弹窗的时候将该标记设置为false
    },
    onOpenMessageList() {
      this.IsShowMessageList = true
      this.IsShowMessageMini = false
      this.MessageInfo.msgFromSignalR = false
    },
	// 获取消息
    getMessage(args) {
      const _this = this
      if(!_this.$staticmethod.isObject(args)) {
        args = {Module:"站内信",Type:-1} // 目前测试来看好像只要Module有值就行
      }
      const _token = _this.$staticmethod.Get("Token")
      const queryString = _this.$qs.stringify(args)
	  if(!_token){return}
      const url = `${window.bim_config.webserverurl}/api/User/Message/List?${queryString}&Token=${_token}`
      _this.$axios
        .get(url)
        .then((x) => {
          if (x.status == 200) {
            if (x.data.Ret > 0) {
              _this.MessageInfo.dataSource = x.data.Data || null
            } else {
            //   _this.$message.error(x.data.Msg)
			   console.error(x.data.Msg)
            }
          } else {
            console.error(x)
          }
        })
        .catch((x) => {
          console.error(x)
        });
    },
    // 响应UpdateMsg事件
    handleUpdateMsg(data) {
      if(this.IsShowMessageMini) {
        // 当前消息窗口是显示的：更新消息窗口的数据
        this.MessageInfo.msgFromSignalR = false
        this.$refs.messageref.refreshMessage(data) // 调用Message2组件的refreshMessage方法
      } else {
        // 消息弹窗是没有显示的：将该标记设置为true表示收到了来自SignalR服务端推送的消息（添加了小红点）
        // 这里可以从右上角弹出一个Nofify，数据来自data.Msg
        this.MessageInfo.msgFromSignalR = true
      }
    },

    getProjectPagedList(){
      let _this = this;
      let Token = _this.$staticmethod.Get("Token");
      _this.$axios
        .get(`${_this.$urlPool.ProjectPaged}?token=${Token}&pageNum=1&pageSize=3000&keyword=${_this.extdata.keyword}&sort=${this.extdata.sortstr}`)
        .then(res=>{
          _this.extdata.projectdatas = res.data.Data.rows;

        })
        .catch(err=>{})
    },

		clickOutClose() {
			this.extdata.isshowing_sortoptions = false;
		},
		getUserShow(){
			// 不显示个人信息
			if(window.bim_config.custom_hideuserInfo == "1"){
				return false
			}else{
				return true
			}
		},
		screenJump() {
			window.open(`${window.bim_config.hasRouterFile}/#/gis`, "_blank");
		},

		// 是否隐藏到期时间
		cfg_ifhidedeadline() {
			var _this = this;
			if (window.bim_config.custom_hideProjectDeadline) {
				return true;
			} else {
				return false;
			}
		},

		// 是否隐藏是否公开
		get_mask_ispublic() {
			var _this = this;
			if (window.bim_config.custom_hidebootispublic) {
				return true;
			} else {
				return false;
			}
		},

		closecopy() {
			var _this = this;
			_this.copyproj_show = false;
			_this.copyproj_status = undefined;
			_this.copyprojadmin_show = false;
		},

		_stopPropagation(ev) {
			if (ev) {
				ev.stopPropagation();
			}
		},

		//copynum_valid
		copynum_valid(ev) {
			var _this = this;
			var valuetotal = ev.target.value;
			_this.copyproj_num = valuetotal;

			// 判断如果不为空，则强制纠正，并提示。
			if (!valuetotal || valuetotal == "") {
				_this.copyproj_num = 0;
				return;
			}
			// 尝试转为数字
			var ipar = parseInt(valuetotal);
			if (isNaN(ipar)) {
				// 提示，并恢复到上一次输入
				_this.$message.error("输入的数字不正确");
				ev.target.value = (_this.lastCopyNum || 1).toString();
				_this.copyproj_num = ev.target.value;
			} else {
				if (ipar.toString() != valuetotal) {
					_this.$message.error("输入的数字不正确");
				}
				if (ipar <= 0) {
					_this.$message.error("输入的数字应在[1, 99]范围内");
					ipar = 0;
				}
				if (ipar > 99) {
					_this.$message.error("输入的数字应在[1, 99]范围内");
					ipar = 99;
				}
				ev.target.value = ipar;
				_this.copyproj_num = ipar;
				_this.lastCopyNum = ev.target.value;
			}
		},

		// 保存拷贝创建项目设置
		_save_copycfg() {
			// 将 copycfgcache => copycfg
			var _this = this;
			for (var prop in _this.copycfgcache) {
				_this.copycfg[prop] = _this.copycfgcache[prop];
			}
			_this.copyprojadmin_show = false;
		},

		// 取消拷贝
		_cancelcopycreate() {
			var _this = this;
			_this.try_cancelorcloseCopy(false);
		},

		// 开始进行拷贝创建
		_startcopycreate() {
			// 判断输入（模板项目，拷贝个数）
			var _this = this;
			// 重置已拷贝个数和总数
			_this.copyproj_num_finished = 0;
			_this.copyproj_failednum = 0;

			// id_copyproj_num 赋值给 _this.copyproj_num
			_this.copyproj_num = document.getElementById("id_copyproj_num").value;

			if (
				!(
					_this.$refs.ref_copytemplateid.attrvalue &&
					_this.$refs.ref_copytemplateid.attrvalue.value
				)
			) {
				_this.$message.error("请选择项目模板");
				return;
			}

			var item = _this.$refs.ref_copytemplateid.attrvalue;
			if (!item || !item.text) {
				_this.$message.error("请选择项目模板");
				return;
			}

			if (_this.copyproj_num <= 0) {
				_this.$message.error("请输入正确的项目个数");
				return;
			}

			// 修改 copypro1j_status 为进行中
			_this.copyproj_status = "running";

			// 再调用方法计算百分比
			_this.computePercent();
			_this.$refs.ref_copyprogressbar.setvalue(_this.copyproj_percent);

			// 设置 copyproj1_status 为 'running' 后，进度条显示出来
			// 此时开始执行队列请求，并改变进度条
			_this.exec_copyprojects();
		},
		// 根据 copyproj_num_finished 和 copyproj_num 来计算 copyproj_percent
		computePercent() {
			var _this = this;
			if (_this.copyproj_num_finished == 0) {
				_this.copyproj_percent = 0;
				return;
			}
			if (_this.copyproj_num == 0) {
				_this.copyproj_percent = 0;
				return;
			}
			if (_this.copyproj_num <= _this.copyproj_num_finished) {
				_this.copyproj_percent = 100;
				return;
			}
			// 除法计算百分比
			_this.copyproj_percent = Math.round(
				(_this.copyproj_num_finished / _this.copyproj_num) * 100
			);
		},
		// 重置请求变量
		resetQueryVariables() {
			var _this = this;
			_this.copyproj_num_finished = 0;
			_this.copyproj_num = 0;
			_this.computePercent();
			if (_this.copyproj_timeoutH) {
				clearTimeout(_this.copyproj_timeoutH);
				_this.copyproj_timeoutH = 0;
			}
			if (_this.copyproj_isquerying) {
				_this.copyproj_isquerying = false;
			}
			// cancelToken 不用重置
		},

		// 经过用户操作确认，执行 cancelorcloseCopy
		do_cancelorcloseCopy(isclose) {
			var _this = this;
			if (_this.copyproj_isquerying == true) {
				// 中止！执行一些额外的处理。
				// 判断 timeout句柄，并clearTimeout 重置 copyproj_timeoutH
				// 判断 cancelToken，并cancel，重置 cancel 及 token 变量。
				// 重置 copyproj_isquerying
				// 重置 copyproj_percent
				_this.resetQueryVariables();

				// 取消请求
				_this.copyproj_canceltoken_source &&
					_this.copyproj_canceltoken_source.cancel("cancel");
			}
			// copyproj_isquerying 与 ！copyproj_isquerying 共通的操作
			// 判断是否 isclose，并返回上一步，或是直接关闭。
			if (isclose == true) {
				// 直接关闭拷贝创建项目的对话框
				_this.copyproj_show = false;
			} else {
				// 回到拷贝创建项目的上一步
				_this.copyproj_status = undefined;
			}
		},

		// 取消或关闭拷贝创建项目的对话框
		// 参数isclose为true时表示直接关闭，否则是取消
		try_cancelorcloseCopy(isclose) {
			// 如果正在执行拷贝创建，则先弹出提示
			var _this = this;
			if (_this.copyproj_isquerying == true) {
				// 提示用户当前正在拷贝创建项目，是否确认操作
				_this
					.$confirm("当前正在进行拷贝创建操作，是否中止？", "操作确认", {
						confirmButtonText: "中止",
						cancelButtonText: "取消",
						type: "warning",
					})
					.then((x) => {
						_this.do_cancelorcloseCopy(isclose);
					})
					.catch((x) => {});
			} else {
				_this.do_cancelorcloseCopy(isclose);
			}
		},
		// 开始执行项目拷贝，并改变进度条指示
		exec_copyprojects() {
			var _this = this;
			// 确定本次创建项目的项目名称
			// 先读取所选的项目模板的项目名称
			var item = _this.$refs.ref_copytemplateid.attrvalue;

			var templateprojname = item.text;

			// 绑定 setTimeout 句柄，并注意在关闭及取消时，给出操作提示，确认操作后clearTimeout
			// 除了 clearTimeout ，注意使用 cancelToken 结束请求
			// 并且绑定一个标识变量，标识当前正在请求接口
			_this.copyproj_isquerying = true;

			// 读取所有项目的名称，找出新的编号
			var samenameprojs = _this.project_copy_selectarr.filter(
				(x) => x.text.indexOf(`${templateprojname}（`) == 0
			);
			if (samenameprojs.length == 0) {
				_this.copyproj_startOffset = 0;
			} else {
				// 遍历每一个名称，取出数字部分，再找到最大数。
				// copyproj_startOffset 直接赋值为其中的最大数
				var nums = [];
				for (var i = 0; i < samenameprojs.length; i++) {
					var indexStart = samenameprojs[i].text.lastIndexOf("（");
					var indexEnd = samenameprojs[i].text.lastIndexOf("）");
					var numPart = samenameprojs[i].text.substring(
						indexStart + 1,
						indexEnd
					);
					if (/\d+/.test(numPart)) {
						nums.push(parseInt(numPart));
					}
				}
				if (nums.length > 0) {
					_this.copyproj_startOffset = Math.max.apply(Math, nums);
				} else {
					_this.copyproj_startOffset = 0;
				}
			}
			// 测试本次请求接口，所传的项目名称值
			var thistimename = `${templateprojname}（${
				_this.copyproj_num_finished + 1 + _this.copyproj_startOffset
			}）`;
			// 声明 ajax 的 config
			var config = {
				headers: {
					"Content-Type": "application/json",
				},
				cancelToken: _this.copyproj_canceltoken_source.token,
			};
			const loading = this.$loading({
				lock: true,
				text: "正在创建项目，请您耐心等待...",
				spinner: "el-icon-loading",
				background: "rgba(255, 255, 255, 0.7)",
			});
			// 发起请求
			_this
				.$axios({
					method: "post",
					url: `${window.bim_config.webserverurl}/api/User/Project/CreateProject`,
					data: _this.$qs.stringify({
						Token: _this.$staticmethod.Get("Token"),
						Name: thistimename,
						templateId: _this.$refs.ref_copytemplateid.attrvalue.value,

						// 拷贝创建项目的选项：同步数据
						SyncAuth: _this.copycfg.hasSelected_auth ? "1" : "0",
						SyncDoc: _this.copycfg.hasSelected_doc ? "1" : "0",
						SyncModel: _this.copycfg.hasSelected_model ? "1" : "0",
						SyncProgress: _this.copycfg.hasSelected_progress ? "1" : "0",
						SyncIssue: _this.copycfg.hasSelected_issue ? "1" : "0",
					}),
					config: config,
				})
				.then((x) => {
					if (x.data.Ret < 0) {
						_this.copyproj_failednum++;
						_this.$message.warning("创建项目失败：" + x.data.Msg);
					}
					// 这个地方需要多次执行ajax，且需要改变进度条
					_this.copyproj_num_finished += 1;

					// 通过 copyproj_num_finished 及 copyproj_num 计算 copyproj_percent
					_this.computePercent();
					_this.$refs.ref_copyprogressbar.setvalue(_this.copyproj_percent);
					_this.createVault(x.data.Data.organizeId);
					if (_this.copyproj_num_finished >= _this.copyproj_num) {
						// 需要记录当前第几个，共几个。
						// 拷贝完成
						_this.copyproj_isquerying = false;
						// 关闭对话框，并刷新页面
						_this.$message.warning(x.data.Msg);
						// 关闭对话框
						_this.closecopy();
						// 重新加载数据
						_this.loadProjectDatas({
							Skip: 0,
							Take: 999,
							keyword: "",
							reloadall: true, // 指定该值会重置 total, hasfinishied, 及 projectdatas。页面初始化可视为 reloadall
						});
						loading.close();
					} else {
						_this.exec_copyprojects();
						loading.close();
					}
				})
				.catch((x) => {
					// 暂时测试
					// 这个地方需要多次执行ajax，且需要改变进度条
					_this.copyproj_num_finished += 1;

					// 通过 copyproj_num_finished 及 copyproj_num 计算 copyproj_percent
					_this.computePercent();
					_this.$refs.ref_copyprogressbar.setvalue(_this.copyproj_percent);

					if (_this.copyproj_num_finished >= _this.copyproj_num) {
						// 需要记录当前第几个，共几个。
						_this.copyproj_isquerying = false;
					} else {
						_this.exec_copyprojects();
					}
					loading.close();
				});
		},
		// 创建模型的vaultID==新模型需要的
		createVault(VaultID) {
			let _this = this;
			_this.$axios
				.get(
					`${this.$ip("newModelHttpUrl")}/Vault/CreateVault?VaultID=${VaultID}`
				)
				.then((res) => {
					if (res.status === 200) {
						// _this.$message.success('创建成功')
					} else {
						// _this.$message.error('项目模型存储区创建失败,请删除重新创建项目,否则模型模块将无法使用!')
					}
				})
				.catch((err) => {
					console.log(err);
				});
		},
		copyProjAdv() {
			var _this = this;
			if (_this.copyprojadmin_show) {
				_this.copyprojadmin_show = false;
			} else {
				// 将 copycfg => copycfgcache
				for (var prop in _this.copycfg) {
					_this.copycfgcache[prop] = _this.copycfg[prop];
				}

				_this.copyprojadmin_show = true;
			}
		},
		// 关闭拷贝创建项目的“高级设置”对话框
		_closecopyprojadmin() {
			var _this = this;
			_this.copyprojadmin_show = false;
		},
		_closecopyproj() {
			var _this = this;
			_this.try_cancelorcloseCopy(true);
		},
		_createprojectbtns(item) {
			var _this = this;
			console.log('点击')
			if (item && item.sign == "copycreate") {
				// 从 _this.extdata.projectdatas 中读取所有项目数据，赋值给下拉框
				_this.project_copy_selectarr = _this.extdata.projectdatas
					.map(function (item) {
						return { value: item.ProjectId, text: item.fullname };
					})
					.filter((x) => x.value && x.value != "-1");

				// 初始化对话框内的属性
				_this.copyproj_status = undefined;
				_this.copyproj_num = 0;
				_this.copyproj_show = true;
				_this.$nextTick(() => {
					_this.$refs.ref_copytemplateid.attrvalue = {};
				});
			}else {
				_this.btncreateissue_click();
			}
		},

		// getMessageCount(params_) {
		// 	let _this = this;
    //   // 这部分的东西后端还没开发完，后期在做，20230605
		// 	// let _token = _this.$staticmethod.Get("Token");
		// 	// let url = `${window.bim_config.webserverurl}/api/Message/JPush/GetCurrentUserNotifyOrMsgCnt?Token=${_token}`;
		// 	// _this.$axios
		// 	// 	.get(url)
		// 	// 	.then((x) => {
		// 	// 		if (x.status == 200) {
		// 	// 			if (x.data.Ret > 0) {
		// 	// 				let retdata = x.data.Data;
		// 	// 				if (params_ == 1) {
		// 	// 					// 调用 Message 组件的消息更新函数
		// 	// 					if (_this.$refs.messageref) {
		// 	// 						_this.$refs.messageref.updateMsgCntNumber(
		// 	// 							retdata.NotifyUnReadCnt,
		// 	// 							retdata.IssueUnReadCnt,
		// 	// 							retdata.DocUnReadCnt,
		// 	// 							retdata.FlowUnReadCnt,
		// 	// 							true
		// 	// 						);
		// 	// 					}
		// 	// 				}
		// 	// 				if (params_ == 2) {
		// 	// 					// 获取消息个数，仅适用于第一次加载，不适用于手动（代码）调用
		// 	// 					// 进入或刷新 Boot 页面会进入此函数
		// 	// 					_this.MessageNumber.InitNotifyUnReadCnt =
		// 	// 						retdata.NotifyUnReadCnt;
		// 	// 					_this.MessageNumber.InitIssueUnReadCnt = retdata.IssueUnReadCnt;
		// 	// 					_this.MessageNumber.InitDocUnReadCnt = retdata.DocUnReadCnt;
		// 	// 					_this.MessageNumber.InitFlowUnReadCnt = retdata.FlowUnReadCnt;
		// 	// 				}
		// 	// 				// 红点的显示与隐藏
		// 	// 				_this.MessageNumber.Total = retdata.Total;
		// 	// 			} else {
		// 	// 				_this.$message.error(x.data.Msg);
		// 	// 			}
		// 	// 		} else {
		// 	// 			console.error(x);
		// 	// 		}
		// 	// 	})
		// 	// 	.catch((x) => {
		// 	// 		console.error(x);
		// 	// 	});
		// },

		//IsShowMessageMini=!IsShowMessageMini
		OpenMiniMessage() {
			var _this = this;
			if (_this.IsShowMessageMini) {
				_this.IsShowMessageMini = false;
			} else {
				_this.IsShowMessageMini = true;
			}
		},
		// 是否已经开启了构件库模块
		hadenabledbimcomp() {
			var enabled = window.bim_config.custom_enabledbimcomp;
			if (enabled && this.loginOrganized === this.ComponentLibraryOrganized) {
				return true;
			} else {
				return false;
			}
		},

		// 是否未禁用大屏
		notdisabled_bigScreen() {
			if (window.bim_config.toggle_big_screen) {
				return true;
			} else {
				return false;
			}
		},
		hideall() {
			var _this = this;
			_this.IsShowMessageMini = false;
			_this.$staticmethod.debugshowlog("597");

			// 判断如果个人中心已打开，才关闭。
			if (_this.currentMenuListState == true) {
				_this.currentMenuListState = false;
			}
		},
		microstationdownload() {
			window.location.href =
				"https://www.probim.cn:8076/Resource/PluginFile/Microstation%E8%BD%AC%E6%8D%A2%E6%8F%92%E4%BB%B6.zip";
		},
		downloadsketchup() {
			window.location.href =
				"https://www.probim.cn:8076/Resource/PluginFile/BIMeforSU.zip";
		},
		downloadnaviswork() {
			window.location.href =
				"https://www.probim.cn:8076/Resource/PluginFile/Navisworks%E8%BD%AC%E6%8D%A2%E6%8F%92%E4%BB%B6.zip";
		},
		downloadrevit() {
			window.location.href =
				"https://www.probim.cn:8076/Resource/PluginFile/Revit%E8%BD%AC%E6%8D%A2%E6%8F%92%E4%BB%B6.zip";
		},
		HideMessage(e) {
			console.log(e.target.className);
			if (
				e.target.className.indexOf("el-tooltip") == -1 &&
				e.target.className.indexOf("bottomDiv") == -1
			)
				this.IsShowMessageMini = false;
		},
		getStyleOfPj(item) {
			var _this = this;
			var _s = {};
			_s["background-image"] = `url('${item.Thumbnail}')`;
			_s["background-repeat"] = "no-repeat";
			_s["background-size"] = "cover";
			_s["background-position"] = "center";
			return _s;
		},
		getIsSystem() {
			var IsSystem = this.$staticmethod.Get("IsSystem");
			return IsSystem == "1";
		},
		loginAdmin() {
			var NowTime = new Date().getTime();
			var p = this.$md5("probim" + (NowTime - 999));
			let LoadingIns = this.$loading({
				text: "执行中",
			});
			this.$axios
				.get(`${this.$urlPool.LoginAdmin}?p=${p}&dateStr=${NowTime}&Token=${this.$staticmethod.Get("Token")}`)
				.then((res) => {
					let result = res.data;
					LoadingIns.close();
					if (result.Ret == 1) {
						this.$staticmethod.Set("Token", result.Data.Token);
						this.$staticmethod.Set("RealName", result.Data.RealName);
						this.$staticmethod.Set("Account", result.Data.Account); // 上传文件时，参数：CreateUserName。
						this.$staticmethod.Set("UserId", result.Data.UserId); // Cache UserId
						this.$staticmethod.Set("Email", result.Data.Email); // Cache Email
						this.$staticmethod.Set("IsSystem", "1");
						window.location.href = `${window.bim_config.hasRouterFile}/#/Admin/Index_V2/${result.Data.Token}`;
					}
				})
				.catch((res) => {
					LoadingIns.close();
				});
		},
		backUpToAdmin() {
			this.loginAdmin();
		},
		sorttype_click() {
			var _this = this;
			if (_this.extdata.isshowing_sortoptions == true) {
				_this.extdata.isshowing_sortoptions = false;
			} else {
				_this.extdata.isshowing_sortoptions = true;
			}
		},

		//机构管理
		turnToComMgr() {
			var _this = this;
			window.location.href = `${window.bim_config.hasRouterFile}/#/Home/OrganizeManage/${_this.$staticmethod.Get(
				"Token"
			)}`;
		},
		//机构日志
		OrganizationLog() {
			var _this = this;
			window.location.href = `${window.bim_config.hasRouterFile}/#/Home/OrganizationLog/${_this.$staticmethod.Get(
				"Token"
			)}`;
		},
		//构件库管理
		OrganizationComponentLibrary() {
			var _this = this;
			window.location.href = `${window.bim_config.hasRouterFile}/#/Home/OrganizationComponentLibrary/${_this.$staticmethod.Get(
				"Token"
			)}`;
		},

		// 获取当前token下的机构信息
		loadtokencominfo() {

			// 根据当前 Token 获取其所在机构，如果为-1的话，则显示默认。
			var _this = this;
			var _Token = _this.$staticmethod.Get("Token");

			_this
				.$axios({
					method: "get",
					url: `${window.bim_config.webserverurl}/api/User/User/GetTokenComInfo?Token=${_Token}`,
				})
				.then((x) => {
					//debugger;
					if (x.status == 200 && x.data.Ret > 0 && x.data.Data) {
						sessionStorage["_OrganizeId"] = x.data.Data.OrganizeId;
						if (x.data.Data.IsShowname) {
							_this.extdata.info_companyname = x.data.Data.FullName;
						} else {
							_this.extdata.info_companyname = "";
						}
						x.data.Data.ThumbnailUrl && x.data.Data.ThumbnailUrl.length > 0 ? _this.extdata.info_companylogo = x.data.Data.ThumbnailUrl : _this.extdata.info_companylogo = ''

						//debugger;
						_this.loginOrganized = x.data.Data.OrganizeId;
					}
				})
				.catch((x) => {});
		},
		loadcompanyitems(){
			let _this = this;
			_this
        .$axios({
          method: "get",
					url: `${this.$urlPool.TestTokenIsComMgr}?Token=${this.$staticmethod.Get("Token")}`
        })
        .then(x => {
          if (x.status == 200 && x.data.Ret > 0) {
            if (x.data.Data != null && x.data.Data.OrganizeId != null) {
              _this.extdata.is_commgr = true;
              _this.$store.commit(IS_COM_MGR_MUTATION, true);
            } else {
              _this.extdata.is_commgr = false;
              _this.$store.commit(IS_COM_MGR_MUTATION, false);
            }
            _this.ComponentLibraryOrganized = x.data.Data.OrganizeId

          } else {
            _this.extdata.is_commgr = false;
            _this.$store.commit(IS_COM_MGR_MUTATION, false);
          }
        })
        .catch(x => {
          _this.extdata.is_commgr = false;
          _this.$store.commit(IS_COM_MGR_MUTATION, false);
        });
		},


		// 创建项目时，输入项目名称。
		_onnewprojectinput(str) {
			var _this = this;
			_this.extdata.creatingprojdata.newpjname = str;
			//console.log(_this.extdata.creatingprojdata.newpjname);
		},
		_onnewprojectinputprojectCode(str){
			this.extdata.creatingprojdata.projectCode = str;
		},
		ispubselected(obj) {
			var _this = this;
			_this.extdata.creatingprojdata.text = obj.text;
			_this.extdata.creatingprojdata.id = obj.id;
		},

		// 创建项目的弹窗，点击OK
		creatingproj_ok() {
			let _this = this;
			if (
				_this.extdata.creatingprojdata.newpjname.length == 0 ||
				_this.extdata.creatingprojdata.newpjname.trim().length == 0
			) {
				_this.$message.error("请输入项目名称");
				return;
			}
			if (
				_this.extdata.creatingprojdata.projectCode.length == 0 ||
				_this.extdata.creatingprojdata.projectCode.trim().length == 0
			) {
				_this.$message.error("请输入项目编码");
				return;
			}

			const loading = this.$loading({
				lock: true,
				text: "正在创建项目，请您耐心等待...",
				spinner: "el-icon-loading",
				background: "rgba(255, 255, 255, 0.7)",
			});
			let data = {
				Token: _this.$staticmethod.Get("Token"),
				Name: _this.extdata.creatingprojdata.newpjname,
				Flag: _this.extdata.creatingprojdata.id == 0 ? false : true,
				ProjectCode: _this.extdata.creatingprojdata.projectCode,
			}
			_this
				.$axios({
					method: "post",
					headers:{
						'Content-Type':'application/json'
					},
					url: `${this.$urlPool.CreateProject}`,
					data: data
				})
				.then((x) => {
					if (x.status == 200) {
						if (x.data.Ret > 0) {
							// 返回并刷新页面。
							_this.createVault(x.data.Data.OrganizeId);
							_this.$message.success("创建成功！");
							_this.extdata.iscreatingproj = false;
						 	_this.getProjectPagedList();
							loading.close();
						} else {
							_this.$message.error(x.data.Msg);
							loading.close();
						}
					} else {
					}
				})
				.catch((x) => {
					loading.close();
				});
		},

		creatingproj_cancel() {
			// 未禁用 GIS 时，按原来的逻辑
			let _this = this;
			// 现在的逻辑删除了gis，后期做项目复制的时候在看逻辑，现在这里是错误的，没有删除
			_this.extdata.iscreatingproj = false;
		},

		// 清除登录信息并返回登录页面(if tologin)
		clearlogininfo(tologin) {
			var _this = this;
			_this.$staticmethod.Set("Token", "");
			_this.$staticmethod.Set("RealName", "");
			_this.$staticmethod.Set("Account", "");
			_this.$staticmethod.Set("UserId", "");
			_this.$staticmethod.Set("Email", "");
			if (tologin) {
				// 返回登录页
				window.location.href = `${window.bim_config.hasRouterFile}/#/`;
			}
		},

		changeisfull() {
			var _this = this;
			if (_this.extdata._isfullscreen) {
				_this.extdata._isfullscreen = false;
				_this.$staticmethod.ExitFullScreen();
			} else {
				_this.extdata._isfullscreen = true;
				_this.$staticmethod.FullScreen();
			}
		},

		itemclick(item) {
			var _this = this;
			// debugger
			if (item.code == "exit") {
				// 收起个人菜单
				_this.currentMenuListState = false;

				// 弹出提示，确认是否退出当前账号
				_this
					.$confirm("确定退出当前账号", "操作确认", {
						confirmButtonText: "确定",
						cancelButtonText: "取消",
						type: "warning",
					})
					.then((x) => {
						if(window.bim_config.CCFlowUrl && window.bim_config.CCFlowUrl.length > 0){
							_this.getCCToken(1); // 获取最新的CCtoken调用CC退出接口
						}

						// debugger

						// 调用接口
						var _Token = _this.$staticmethod.Get("Token");
						_this
							.$axios({
								method: "post",
								url: `${_this.$urlPool.RemoveLogin}`,
								data: {
									Token: _Token,
								},
							})
							.then((x) => {
								if (x.status == 200 && x.data.Ret > 0) {
									// 前端数据清除
									_this.clearlogininfo(true);
								}
							})
							.catch((x) => {});
					})
					.catch((x) => {
						// nothing
					});
			} else if (item.code == "official") {
				// 官网
				window.location.href = "http://www.probim.com.cn/";
			}
			// 下载  当点击下载时 弹出模态框
			else if (item.code == "download") {
				this.download_box = true;
			}
			// 个人设置  当点击个人设置 切换页面
			else if (item.code == "settings") {
				// 带参数Token 跳转
				var _Token = _this.$staticmethod.Get("Token");
				this.$router.push({ name: "Settings", params: { Token: _Token } });
			}else if (item.code == 'update'){
        // app更新管理
        this.getAppVersionsList()
      }
		},

		toggleUserMenuListState() {
			var _this = this;
			_this.currentMenuListState = !this.currentMenuListState;
		},

		closeUserMenuListState() {
			var _this = this;
			_this.currentMenuListState = false;
		},

		// 直接创建项目或选择了父项目后再创建项目
		btncreateissue_click() {
			let _this = this;
			_this.extdata.iscreatingproj = true;
		},

		// 点击某一个项目
		enterproject(organizeid,CopyFlowStatus,projectName) {
      setProjectName(projectName)
			// 再次刷新当前列表
			if(CopyFlowStatus == 0){
				this.$message.warning('等待复制')
				this.getProjectPagedList()
				return
			}
			if(CopyFlowStatus == 1){
				this.$message.warning('复制中')
				this.getProjectPagedList()
				return
			}
			if(CopyFlowStatus == 3){
				this.$message.warning('流程复制失败，请联系管理员')
				this.getProjectPagedList()
				return
			}

			this.$staticmethod._Set("organizeId", organizeid);
			// 点击某个项目  保存项目id，然后前端手动判断到期时间、在跳转到当前项目
			if(window.bim_config.CCFlowUrl && window.bim_config.CCFlowUrl.length > 0){
				this.getCCToken(); // 获取最新的CCtoken
			}else{
				// 根据屏幕宽度判断跳转路径
				if (this.extdata.smallScreenShow == false) {
					// 小屏，拼接url地址
					let urlissue = `${window.location.origin}${window.bim_config.hasRouterFile}/#/Home/ProjectBoot/Issue/${organizeid}/${this.$staticmethod.Get("Token")}`
					console.log(urlissue);
				} else {
					this.toRouterInFirstMenu(organizeid)
					// window.location.href = `${window.bim_config.hasRouterFile}/#/Home/ProjectBoot/Main/${organizeid}/${this.$staticmethod.Get("Token")}`;
				}
			}
		},
		getCCToken(num) {
			// 获取cc的token
			let _this = this;
			let _OrganizeId = _this.$staticmethod._Get("organizeId");
			let _Token = _this.$staticmethod.Get("Token");
			let _url = `${window.bim_config.webserverurl}/api/User/User/CCFlowLogin`;
			let _para = {
				Token: _Token,
				organizeId: _OrganizeId,
			};
			_this
				.$axios({
					method: "post",
					url: _url,
					data: _para,
				})
				.then((x) => {
					if (x.data.Ret > 0) {
						// userno   uesrname   // OrgNo 机构ID
						window.localStorage.setItem("CC_Token", x.data.Data);
						if (num == 1) {
							_this.ccout(x.data.Data);
						}
						if(num != 1){
							// 根据屏幕宽度判断跳转路径
							if (_this.extdata.smallScreenShow == false) {
								// 小屏，拼接url地址
								let urlissue = `${window.location.origin}${window.bim_config.hasRouterFile}/#/Home/ProjectBoot/Issue/${_OrganizeId}/${_this.$staticmethod.Get("Token")}`
								console.log(urlissue);
							} else {
								this.toRouterInFirstMenu(_OrganizeId)
								// window.location.href = `${window.bim_config.hasRouterFile}/#/Home/ProjectBoot/Main/${_OrganizeId}/${_this.$staticmethod.Get("Token")}`;
							}
						}
					}else if(x.data.Ret == -9999){
						this.$message.error(x.data.Msg);
						return
					}
					 else {
						_this.$message.error(x.data.Data);
					}
				})
				.catch((x) => {
					console.log(x);
				});
		},
		// 点击进入项目、默认进入第一个菜单的页面
		toRouterInFirstMenu(organizeId){
			let token = this.$staticmethod.Get("Token")
			// 获取当前项目的树结构、判断跳转页面地址
			this.getMenuTreePromise = this.$axios
				.get(`${this.$urlPool.GetUserMenuTree}?token=${token}&organizeId=${organizeId}&parentId=0`)
				.then(res=>{
					if(res.data.Ret == 1) {
						let data = res.data.Data;
            const index = data.findIndex(item => item.MenuCode === 'XMZY')
						if (data[index]) {
							let routePath = data[index].RoutePath.length > 0 ? data[index].RoutePath : data[index].Children[index].RoutePath;
							let _urlorigin = window.bim_config.hasRouterFile + routePath;
							let _urltarget = _urlorigin
								.replace("@Token", token)
								.replace("@OrganizeId", organizeId);
							window.location.href = _urltarget;
							sessionStorage.setItem('defaultActive', data[index].RoutePath.length > 0 ? index + 1 + '' : index + 1 + '-01');
						} else {
							this.$message.error('当前项目菜单配置地址有误，请检查菜单地址');
							return;
						}
					}else{
						console.error(res.data.Msg);
					}
				})
				.catch(err=>{})
		},
		// 流程模块需要在这里请求下他们的退出接口
		ccout(cctoken) {
			let _this = this;
			let _UserId = _this.$staticmethod.Get("UserId");
			this.$axios({
				url: `${window.bim_config.CCFlowUrl}/api/v1/user/loginOut?userNo=${_UserId}&token=${cctoken}`,
				method: "post",
			})
				.then((x) => {
					console.log("退出axios", x);
				})
				.catch((x) => {});
		},


		// 数据区域滚动事件监听
		projectlistscroll(ev) {
			var _this = this;
			return;
			if (_this.extdata.hasfinishied == true) {
				// hasfinishied 表示已经加载完全部数据了（仅限此次页面数据过滤）
				return;
			}
			if (
				ev.srcElement.scrollTop + ev.srcElement.clientHeight + 0 >=
				ev.srcElement.scrollHeight
			) {
				// 如果滚动条拉到了最下面（或滚动滚轮到了最下面），追加数据（依据已经加载的宏观数据个数）
				_this.loadProjectDatas({
					Skip: _this.projectdata_validlength,
					Take: _this.extdata.everytake,
					keyword: _this.extdata.lastkeyword,
				});
			}
		},

		// listenwindowresize() {
		// 	var _this = this;
		// 	window.onresize = function () {
		// 		var counteveryline = _this.getcounteveryline();
		// 		_this.extdata.hasfinishied = false;
		// 		_this.projectdata_repatch(counteveryline);
		// 	};
		// },

		// 依据页面总宽度计算每行个数
		getcounteveryline() {
			var window_innerWidth = window.innerWidth;
			if (window_innerWidth <= 1366) {
				window_innerWidth = 1366;
			}

			// 左padding 右padding 宽度
			var cr = Math.floor(
				(window_innerWidth - 124 - 124) / (window_innerWidth * 0.2028)
			);
			// 补丁
			if (window_innerWidth >= 1708 && window_innerWidth <= 1999) {
				cr = 5;
			}
			return cr;
		},

		// 依据 getcounteveryline 计算出的每行个数，补充空元素补充 flex 内部元素占位
		projectdata_repatch(num) {
			var _this = this;
			// 移除原有的 organizeid 为-1的数据
			_this.extdata.projectdatas = _this.extdata.projectdatas.filter(
				(x) => x.organizeid != -1
			);
			if (_this.$route.params.organizeId) {
				_this.enterproject(_this.$route.params.organizeId);
			}

			// 计算总有效个数对每行个数（num）的余数
			var lcount = _this.extdata.projectdatas.length % num;
			if (lcount == 0) {
				return;
			}
		},

		// 有写操作的数据接口也需要 To1ken。验证数据接口的 Tok1en 失败后，弹出报错信息

		sorttypechange(sortstr, zhcnsortstr) {
			var _this = this;
			// _this.loadProjectDatas({
			// 	Skip: 0,
			// 	Take: 999,
			// 	keyword: _this.extdata.keyword,
			// 	reloadall: true, // 指定该值会重置 total, hasfinishied, 及 projectdatas。页面初始化可视为 reloadall
			// 	sorttype: sortstr,
			// });
			_this.extdata.sortlabel = zhcnsortstr;
			_this.extdata.sortstr = sortstr;
			_this.extdata.isshowing_sortoptions = false;
			this.getProjectPagedList()
		},

		// jsonpara.Skip
		// jsonpara.Take
		// jsonpara.keyword
		// jsonpara.reloadall   // 是否重新加载数据，并重置total, hasfinishied, projectdatas
		// jsonpara.sorttype    // CreateDate%20desc    CreateDate%20asc    ''
		loadProjectDatas(jsonpara) {
			// 这个是刷新页面列表的，后期删除
			var _this = this;

			// 此处改为读取排序配置
			var sortstr = "CreateDate%20desc";
			if (
				window.bim_config.custom_sorttype_project ||
				window.bim_config.custom_sorttype_project == ""
			) {
				sortstr = window.bim_config.custom_sorttype_project; // 配置排序
			} else {
				sortstr = "CreateDate%20desc"; // 默认
			}

			if (jsonpara.sorttype) {
				sortstr = jsonpara.sorttype;
			}

			// 最后一次排序的值
			_this.m_lastsortstr = sortstr;

		},
		// 项目列表小屏展示点击关闭
		smallScreenClose() {
			// 点击关闭
			if (!BIMeClient) {
				console.error("BIMeClient 未注册");
				return;
			}
			if (!BIMeClient.closeProjectList) {
				console.error("closeProjectList 未实现");
				return;
			}
			BIMeClient.closeProjectList();
		},
		getStyleSmallScreen() {
			let _this = this;
			if (!_this.extdata.smallScreenShow) {
				let _s = {};
				_s["top"] = 100 + "px";
				_s["height"] = "calc(100% - 78px)";
				return _s;
			}
		},
		getStyleOfsmallImg() {
			let _this = this;
			if (!_this.extdata.smallScreenShow) {
				let _s = {};
				_s["margin"] = "6px auto";
				return _s;
			}
		},
		getScreenWidthChange() {
			let _this = this;
			let _smallScreenWidth = _this.$smallScreenWidth;
			_this.screenWidth < _smallScreenWidth
				? (_this.extdata.smallScreenShow = false)
				: (_this.extdata.smallScreenShow = true);
		},
    // 是否显示消息提醒
		getConfigBell(){
			if(window.bim_config.custom_hidebootbell){
        return true
      }else{
        return false
      }
		},
		VisualizationPanel_open(organizeId, bimcomposerid) {
			sessionStorage.setItem("organizeId", organizeId);
			sessionStorage.setItem("bimcomposerId", bimcomposerid);
			const routeData = this.$router.resolve({
				path: `/VisualizationPanel/VisualizationPanelShow/${organizeId}/${this.$staticmethod.Get(
					"Token"
				)}`,
			});
			window.open(routeData.href, "_blank");
		},
	},
	computed: {
    hasUnreadMsg() {
      if(this.MessageInfo.msgFromSignalR) {
        return true
      } else {
        const dataSource = this.MessageInfo.dataSource
        if(JSON.stringify(dataSource) !== '{}') {
          const toRemove = ['质量安全', '质量管理','安全管理' ,'安全','质量','问题','档案管理'];
          const filterList = dataSource.List.filter(item => !toRemove.includes(item.Module));
          // console.log('filterList', JSON.stringify(filterList))
          let bool = false
          filterList.findIndex(item => item.HasRead === false) > -1 ? bool = true : bool = false;
          return bool
        } else {
          return false
        }
      }
    },
		searchProjectdatas() {
			let storage = [];
			this.extdata.projectdatas.forEach((item) => {
				if (
					item.fullname &&
					item.fullname
						.toLowerCase()
						.search(this.extdata.keyword.toLowerCase()) != -1
				) {
					storage.push(item);
				}
			});

			return storage;
		},

		logosrc: {
			get() {
				var _this = this;
				return _this.extdata.info_companylogo;
			},
		},

		logostyle: {
			get() {
				var _this = this;
				var _s = {};
				return _s;
			},
		},
		RealNameLastName: {
			get() {
				var _this = this;
				var realName = _this.$staticmethod.Get("RealName");
				var namelast;
				if (realName && realName.length > 0) {
					namelast = realName.substr(realName.length - 1);
				} else {
					namelast = "无";
				}
				return namelast;
			},
		},
		projectdata_validlength: {
			get: function () {
				var _this = this;
				return _this.extdata.projectdatas.filter((x) => x.organizeid != -1)
					.length;
			},
		},
	},

};
</script>
  <style scoped lang="scss">
  .app-dialog{
    /deep/ .el-dialog__body{
      padding-top: 10px;
    }
    /deep/ .el-table__row>td{
      border-bottom: 1px solid #ebeef5;
    }
    .app-dialog-footer{
      width: 362px;
      height: 40px;
      margin-top: 20px;
      .app-dialog-footer-left {
        width: 76px;
        height: 40px;
        font-size: 14px;
        font-family: PingFangSC-Regular;
        font-weight: 400;
        color: #007AFF;
        line-height: 40px;
        cursor: pointer;
        float: right;
      }
      .app-dialog-footer-right {
        width: 76px;
        height: 40px;
        background: #1890FF;
        font-size: 14px;
        font-family: PingFangSC-Regular;
        font-weight: 400;
        color: rgba(255, 255, 255, 1);
        line-height: 40px;
        cursor: pointer;
        float: right;
      }
    }
    .version-input-wrapper{
      display: flex;
      align-items: center;
      span{
        font-size: 14px;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        color: #222222;
        line-height: 20px;
      }
      .el-input{
        height: 28px;
        width: 80%;
        border: 1px solid #E5E5E5 ;
        border-radius: 4px;
        /deep/ .el-input__inner{
          height: 28px;
        }
      }
    }
    .version-select-wrapper{
      padding-bottom: 48px;
      margin-top: 48px;
      display: flex;
      .tooltip{
        margin-top: 5px;
        font-size: 14px;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        color: #222222;
        line-height: 20px;
      }
      .upload-wrapper{
        .button-upload{
          padding-top: 5px;
          padding-bottom: 5px;
          font-size: 12px;
          color: #007AFF;
          cursor: pointer;
          width: 64px;
          border-radius: 2px;
          border: 1px solid #007AFF;
        }
        .apk-file{
          padding: 6px;
          display: flex;
          background-color: #F8F8F8;
          margin-top: 10px;
          width: 300px;
          span{
            font-size: 14px;
            font-family: PingFangSC, PingFang SC;
            font-weight: 400;
            color: #132B4D;
            line-height: 20px;
          }
        }
      }
    }
  }
  </style>
<style scoped lang="stylus" >
.mt10 {
	margin-top: 10px;
}

._css-sorttype-optionitem {
	height: 40px;
	padding-left: 24px;
	box-sizing: border-box;
	line-height: 40px;
	text-align: left;
	font-size: 14px;
	color: rgba(0, 0, 0, 0.85);
	cursor: pointer;
}
._css-sorttype-optionitem:hover {
	background-color: rgba(0, 0, 0, 0.04);
}
._css-sorttype-options {
	top: calc(100% + 12px);
	right: 0;
	position: absolute;
	width: 180px;
	padding-top: 5px;
	padding-bottom: 5px;
	background: rgba(255, 255, 255, 1);
	box-shadow: 0px 1px 3px 0px rgba(0, 21, 41, 0.12);
	border-radius: 2px;
	z-index: 1;
	display: none;
}
._css-sorttype-options._open {
	display: block;
}
._css-sorttype-text {
	flex: 1;
	font-size: 14px;
}
._css-sorttype-icon {
	margin-left: 8px;
	width: 24px;
	height: 24px;
	font-size: 24px;
	line-height: 24px;
}
._css-funcarea-sorttype {
	height: 40px;
	color: rgba(0, 0, 0, 0.85);
	background-color: transparent;
	border: 1px solid transparent;
	border-radius: 2px;
	display: flex;
	align-items: center;
	padding-right: 8px;
	padding-left: 12px;
	position: relative;
	z-index: 101;
}
._css-funcarea-sorttype._clicked {
	color: rgba(24, 144, 255, 1);
	background-color: rgba(24, 144, 255, 0.1);
	border: 1px solid rgba(24, 144, 255, 0.45);
}
.sorttype-mask {
	width: 100%;
	height: 100%;
	background-color: rgba(0, 0, 0, 0);
	position: fixed;
	left: 0;
	top: 0;
	z-index: 100;
}
._css-funcarea-right {
	height: 100%;
	align-items: center;
	flex-direction: row-reverse;
	display: flex;
	margin-left: 24px;
}
._css-funcarea_search_input {
	flex: 1;
	display: flex;
	flex-direction: row-reverse;
}
._css-pi-bottom-ispub {
	height: 20px;
	padding-right: 4px;
	padding-left: 4px;
	border-radius: 2px;
	line-height: 20px;
	color: rgba(0, 0, 0, 1);
	background-color: rgba(0, 0, 0, 0.1);
	border: 1px solid rgba(0, 0, 0, 0.45);
}
._css-pi-bottom-ispub._ispub {
	color: rgba(24, 144, 255, 1);
	background-color: rgba(24, 144, 255, 0.1);
	border: 1px solid rgba(24, 144, 255, 0.45);
}
._css-pi-bottom-ispub._isnotpub {
	color: rgba(250, 84, 28, 1);
	background-color: rgba(250, 84, 28, 0.1);
	border: 1px solid rgba(250, 84, 28, 0.45);
}
._css-pi-bottom-lefticon {
	width: 24px;
	height: 24px;
	background-color: rgba(40, 46, 61, 1);
	color: #fff;
	line-height: 24px;
	border-radius: 4px;
}
._css-pi-bottom-lefttext {
	margin-left: 8px;
	height: 20px;
	line-height: 20px;
	font-size: 12px;
	color: #000;
}
._css-pi-bottom-left {
	display: flex;
	align-items: center;
	flex: 1;
}
._css-projectitem-fullname {
	margin-left: 6px;
	margin-right: 6px;
	height: 24px;
	line-height: 24px;
	margin-top: 8px;
	color: rgba(0, 0, 0, 0.65);
	font-weight: 600;
	text-align: left;
	overflow-x: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}
._css-projectitem-obeycom {
	margin-left: 6px;
	margin-right: 6px;
	height: 22px;
	line-height: 22px;
	margin-top: 6px;
	color: rgba(0, 0, 0, 0.45);
	text-align: left;
	overflow-x: hidden;
	white-space: nowrap;
	text-overflow: ellipsis;
}
._css-projectitem-bottom {
	height: 24px;
	margin-top: 9px;
	margin-left: 6px;
	margin-right: 6px;
	display: flex;
	align-items: center;
}
._css-newlist-funcarea {
	justify-content: space-around;
	position: absolute;
	top: 58px;
	width: 100%;
	height: 68px;
	display: flex;
	align-items: center;
	box-sizing: border-box;
}
._css-logoboot {
	margin-left: 21px;
	height: 30px;
	width: auto;
	background-size: contain;
}
._css-backto-list-text {
	margin-left: 4px;
	height: 22px;
	line-height: 22px;
	color: rgba(0, 0, 0, 0.85);
	font-size: 14px;
}
._css-backto-list-icon {
	width: 20px;
	height: 20px;
	line-height: 20px;
	margin-left: 8px;
	color: rgba(0, 0, 0, 0.85);
}
._css-backto-list {
	margin-left: 20px;
	height: 32px;
	display: none;
	align-items: center;
	min-width: 124px;
	cursor: pointer;
}
._css-backto-list:hover {
	background-color: rgba(0, 0, 0, 0.04);
}
._css-commgr-btn {
	width: 32px;
	height: 32px;
	line-height: 32px;
	margin-right: 18px;
	cursor: pointer;
	display: flex;
	align-items: center;
	justify-content: space-around;
}
._css-commgr-btn:hover, ._css-fullscroll-outer:hover {
	background-color: rgba(0, 0, 0, 0.02);
}
._css-fullscroll-outer {
	margin-right: 18px;
	width: 32px;
	height: 32px;
	line-height: 32px;
	color: rgba(0, 0, 0, 0.45);
	cursor: pointer;
	display: flex;
	align-items: center;
	justify-content: space-around;
}
._css-proj-name {
	height: 100%;
	flex: 1;
	display: flex;
	align-items: center;
	margin-left: 8px;
	margin-right: 16px;
}
._css-ispublic-select {
	height: 100%;
	width: 76px;
	background-color: rgba(0, 0, 0, 0.02);
	border-radius: 2px;
	margin-left: 16px;
}
._css-createproj-m {
	display: flex;
	align-items: center;
	height: 32px;
}
._css-createproj-in {
	width: 400px;
	min-height: 48px;
	background: rgba(255, 255, 255, 1);
	box-shadow: 0px 13px 24px -17px rgba(11, 41, 62, 0.8);
	border-radius: 4px;
}
._css-createproj {
	position: fixed;
	background-color: rgba(0, 0, 0, 0.3);
	width: 100%;
	height: 100%;
	top: 0;
	left: 0;
	display: flex;
	align-items: center;
	justify-content: space-around;
	z-index: 11;
}
/* 右上角相关 */
._css-righttop-group {
	position: fixed;
	right: 24px;
	top: 12px;
	height: 32px;
	display: flex;
	align-items: center;
	flex-direction: row-reverse;
	z-index: 1000;
}
._css-currentuser-icon {
	width: 32px;
	height: 32px;
	border-radius: 4px;
	background-color: #282e3d;
	color: #fff;
	display: flex;
	align-items: center;
	justify-content: space-around;
	font-size: 12px;
	cursor: pointer;
	user-select: none;
}
/* //右上角相关 */

._css-bgcolor_trans {
	background-color: transparent;
	border: none !important;
}

.css-bordernoneim.el-button--primary {
	color: #409eff !important;
	background-color: transparent !important;
	border: none !important;
}
._css-issue-headinneritem {
	height: 100%;
	display: flex;
	align-items: center;
}
._css-issue-headinner {
	height: 100%;
}
._css-boot-funcarea {
	height: 54px;
	position: absolute;
	top: 0;
	width: calc(100% - 0px);
	z-index: 2;
	background-color: #ffffff;
	box-shadow: 0px 1px 1px 0px rgba(0, 21, 41, 0.12);
}
._css-organize-namearea {
	height: 60px;
	margin-bottom: 10px;
	color: #fff;
	background-color: #343434;
}
._css-organize-namearea-text {
	overflow-x: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
	margin-left: 9px;
	color: rgba(0, 0, 0, 0.85);
	height: 24px;
	line-height: 24px;
	font-size: 16px;
	font-weight: 500;
	max-width: calc(100% - 190px);
}
._css-boot-whole {
	display: flex;
	position: relative;
}
._css-projectlist-body {
	height: calc(100% - 136px - 16px);
	margin-bottom: 24px;
	position: relative;
	top: 128px;
	display: flex;
	flex-wrap: wrap;
	align-content: flex-start;
	overflow-y: auto;
	box-sizing: border-box;
	border-left: 1px solid transparent;
	border-right: 1px solid transparent;
}
._css-newlist-funcarea-in {
	box-sizing: border-box;
	border-left: 1px solid transparent;
	border-right: 1px solid transparent;
}
@media screen and (min-width: 1921px) {
	._css-projectlist-body {
		width: calc((292px + 12px) * 5 + 10px + (100% - ((292px + 12px) * 5 + 10px)));
		padding-left: calc(((100% - ((292px + 12px) * 5 + 10px)) / 2));
		padding-right: calc(((100% - ((292px + 12px) * 5 + 10px)) / 2));
	}

	._css-newlist-funcarea-in {
		width: calc((292px + 12px) * 5 + 10px);
	}
}
@media screen and (max-width: 1920px) {
	._css-projectlist-body {
		width: calc((292px + 12px) * 4 + 10px + (100% - ((292px + 12px) * 4 + 10px)));
		padding-left: calc(((100% - ((292px + 12px) * 4 + 10px)) / 2));
		padding-right: calc(((100% - ((292px + 12px) * 4 + 10px)) / 2));
	}
	._css-newlist-funcarea-in {
		width: calc((292px + 12px) * 4 + 10px);
	}
}
._css-projectitem {
	width: 292px;
	height: 290px;
	// height: 348px;
	box-sizing: border-box;
	background-color: transparent;
	padding: 6px;
}
._css-projectitem._css-zsjclass {
	height: 288px;
}
._css-projectitem:hover {
	background: rgba(255, 255, 255, 1);
	box-shadow: 0px 13px 24px -17px rgba(11, 41, 62, 0.8);
	border-radius: 4px;
}
._css-projectitemimg {
	height: 210px;
	width: 100%;
	border-radius: 4px;
	background-color: rgba(0, 0, 0, 0.04);
	display: flex;
	align-items: center;
	justify-content: space-around;
	overflow: hidden;
	background-size: cover;
}
._css-projectitemimg img {
	border-radius: 4px;
}
._css-projectitem:hover img {
	width: 110%;
	height: 110%;
}
._css-public {
	color: #13c2c2;
	padding-left: 5px;
	padding-right: 5px;
	background: rgba(19, 194, 194, 0.1);
	border-radius: 2px;
	border: 1px solid rgba(19, 194, 194, 0.45);
	position: absolute;
	bottom: 10px;
	right: 10px;
	min-width: 48px;
	height: 20px;
	font-size: 12px;
	line-height: 20px;
}
._css-nonpublic {
	color: #f5222d;
	background: rgba(245, 34, 45, 0.1);
	border: 1px solid rgba(245, 34, 45, 0.45);
}
._css-projectlistall {
	height: calc(100%);
	background-color: rgba(240, 242, 245, 1);
	width: 100%;
}

/* 下载模态框样式 */
.el-dialog__header {
	text-align: left;
}

.Revit_ExportPlug {
	width: 334px;
	height: 32px;
	padding: 9px 16px 9px 12px;
	display: flex;
}
.Revit_ExportPlug:hover {
	background: rgba(0, 0, 0, 0.02);
}
.Revit_ExportPlug_left {
	width: 258px;
	height: 32px;
	display: flex;
	align-items: center;
}
.Revit_ExportPlug_right {
	width: 76px;
	height: 32px;
	border: 1px solid rgba(0, 0, 0, 1);
	font-size: 12px;
	font-family: PingFangSC-Regular;
	font-weight: 400;
	color: rgba(0, 0, 0, 1);
	line-height: 32px;
	cursor: pointer;
}

.Revit_ExportPlug_left_icon {
	width: 20px;
	height: 20px;
	font-size: 20px;
}

/* 图表样式 暂时用背景图 */
.Revit_ExportPlug_left_icon_Revit {
	background: url('../../../static/images/downloadres/revit-2017.png') no-repeat;
}

.Revit_ExportPlug_left_icon_Revit_Naviswork {
	background: url('../../../static/images/downloadres/navisworks-manage-2019.png') no-repeat;
}

.Revit_ExportPlug_left_icon_Revit_Sketch {
	background: url('../../../static/images/downloadres/SketchUp.png') no-repeat;
}

.Revit_ExportPlug_left_icon_Revit_other {
	background: url('../../../static/images/downloadres/Microstation.png') no-repeat;
}

/* 图表样式 暂时用背景图 收尾 */
.Revit_ExportPlug_left_text {
	width: 200px;
	height: 20px;
	margin-left: 16px;
	text-align: left;
}
.dialog-footer {
	width: 362px;
	height: 40px;
	margin-top: 20px;
}

.dialog-footer-right {
	width: 76px;
	height: 40px;
	background: #1890FF;
	font-size: 14px;
	font-family: PingFangSC-Regular;
	font-weight: 400;
	color: rgba(255, 255, 255, 1);
	line-height: 40px;
	cursor: pointer;
	float: right;
}

._css-small-screen-show-top {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100px;
}

._css-small-screen-show {
	width: 100%;
	background-color: #fff;
	height: 44px;
	line-height: 44px;
	text-align: left;
	padding-left: 15px;
}

._css-funcarea_search-small {
	width: 270px;
	margin: 10px auto;
}

._css-small-screen-show ._css-typeitem-close {
	position: absolute;
	top: 12px;
	right: 20px;
	color: rgba(0, 0, 0, 0.45);
	cursor: pointer;
}

._css-copydialogcenter {
	display: flex;
	flex-direction: column;
	align-items: center;
	margin-top: 5px;
}

._css-copyprojseltitle {
	height: 22px;
	color: rgba(0, 0, 0, 0.85);
	font-size: 14px;
	padding: 0 24px 0 24px;
	box-sizing: border-box;
	margin-top: 11px;
	text-align: left;
	width: 100%;
}

._css-copynuminput {
	height: 22px;
	color: rgba(0, 0, 0, 0.85);
	font-size: 14px;
	padding: 0 24px 0 24px;
	box-sizing: border-box;
	margin-top: 17px;
	text-align: left;
	width: 100%;
}

._css-copyprojAdvCfg {
	height: 22px;
	color: rgba(245, 34, 45, 1);
	font-size: 14px;
	padding: 0 24px 0 24px;
	box-sizing: border-box;
	margin-top: 51px;
	text-align: left;
	width: 100%;
}

._css-copynuminput-ctn {
	height: 40px;
	width: 298px;
	display: flex;
	align-items: center;
	justify-content: space-around;
	border: 1px solid rgba(0, 0, 0, 0.09);
	border-radius: 4px;
	padding: 0 24px 0 24px;
	box-sizing: border-box;
}

._css-copynuminput-ctn-in {
	width: 100%;
	height: 24px;
	border: none;
	outline: none;
}

._css-copynuminput-ctn-in::placeholder {
	color: rgba(0, 0, 0, 0.35);
}

._css-copyprojBtnCtn {
	border-top: 1px solid rgba(0, 0, 0, 0.09);
	margin-top: 17px;
	height: 64px;
	display: flex;
	align-items: center;
	justify-content: space-around;
}

._css-copydialogcenter-itemctn {
	min-height: 100px;
	max-height: 213px;
	overflow-y: scroll;
}

._css-copydialogcenter-item {
	height: 22px;
	display: flex;
	width: 100%;
	align-items: center;
	margin-top: 18px;
}

._css-copycfg-label {
	margin-left: 16px;
	color: rgba(0, 0, 0, 0.65);
	font-size: 14px;
}

._css-copycfg-checkbox {
	width: 14px;
	height: 14px;
	margin-left: 24px;
	border-radius: 4px;
	cursor: pointer;
	border: 1px solid #ecdfe6;
}

._css-copycfg-checkbox.mulcolor-interface-checkbox-selected {
	border: 1px solid rgb(48, 146, 251);
}

._css-copy-runningnum {
	font-size: 14px;
	text-align: left;
	color: rgba(0, 0, 0, 0.65);
	width: 100%;
	padding: 0 24px 0 24px;
	box-sizing: border-box;
	margin-top: 22px;
}

._css-copy-runningdesc {
	font-size: 14px;
	text-align: left;
	color: rgba(0, 0, 0, 0.25);
	width: 100%;
	padding: 0 24px 0 24px;
	box-sizing: border-box;
	margin-top: 8px;
}

._css-copy-runningshow {
	width: 100%;
	padding: 0 24px 0 24px;
	box-sizing: border-box;
	margin-top: 8px;
}

.docx-zsj {
	position: fixed;
	top: 54px;
	left: 0;
	width: 100%;
	height: calc(100% - 54px);
	z-index: 102;
}

.close-iframe-zsj {
	display: inline-block;
	width: 24px;
	height: 24px;
	line-height: 24px;
	background: rgba(0, 0, 0, 0.4);
	border-radius: 50%;
	text-align: center;
	position: absolute;
	top: 2px;
	right: 2px;
	color: #ffffff;
	font-size: 20px;
	cursor: pointer;
}

.cockpit {
	border: 1px solid #c8c8c8;
	padding: 3px 8px;
	border-radius: 5px;
}
.css-zbutton-textname{
	width: 120px;
	height: 40px;
	line-height: 40px;
	border-radius: 4px;
	background-color: rgb(24, 144, 255);
	color: rgb(255, 255, 255);
	font-size: 14px;
	text-align: center;
	cursor: pointer;
}
.css-line-height{
	line-height: 32px;
}
</style>
<style>
.jingruizhang-probim-vue.css-zdialog-titleclosebtn {
	line-height: 24px !important;
	margin-right: 12px !important;
}
.jingruizhang-probim-vue.css-zdialog-titleclosebtn:hover {
	color: #1890ff;
}
</style>
