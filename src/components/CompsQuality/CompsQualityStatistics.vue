<template>
  <div class="quality-statistics">
    <div class="statistical-chart quality-burn-down-chart">
      <div class="top-title">
        <span class="text">燃烧图</span>
        <div class="burndown-legend">
          <span class="reference-line legend-line">参考线&nbsp;</span>
          <span class="actual-line legend-line">实际线&nbsp;</span>
        </div>
        <el-date-picker
          v-model="qualityBurnDownChartDate"
          type="daterange"
          align="right"
          unlink-panels
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="yyyy-MM-dd"
          @change="setQualityBurnDownChartDfaultDate"
          :clearable="false"
          :picker-options="pickerOptions">
        </el-date-picker>
      </div>

      <div class="chart-content">
        <CompsQualityBurnDownChart
          :totalDatasNum="qualityList.length"
          ref="qualityBurnDownChart"
          :data="qualityBurnDownChart"></CompsQualityBurnDownChart>
      </div>
    </div>

    <div class="css-fc">
      <div class="statistical-chart css-mr20">
        <div class="top-title">
          <span class="text">严重等级分布</span>
        </div>

        <div class="chart-content">
          <CompsQualityPieChart ref="severityGradePieChart" :data="severityGradeData"></CompsQualityPieChart>
        </div>
      </div>

      <div class="statistical-chart">
        <div class="top-title">
          <span class="text">状态分布</span>
          <div class="right statistical-select">
            <el-select @change="getStateChartData" v-model="statisticalSelectoptions" placeholder="请选择">
              <el-option
                v-for="item in statisticalTypeList"
                :key="item.aedt_guid"
                :label="item.AedtName"
                :value="item.aedt_guid">
              </el-option>
            </el-select>
          </div>
        </div>

        <div class="chart-content">
          <CompsQualityPieChart ref="statePieChart" :data="stateChartData"></CompsQualityPieChart>
        </div>
      </div>
    </div>


    <div class="css-fc">
      <!-- <div class="statistical-chart css-mr20">
        <div class="top-title">
          <span class="text">按单位分布</span>
        </div>

        <div class="chart-content css-prel">
          <div v-if="qualityBarChartData.load" class="noData">暂无数据</div>
          <CompsQualityBarChart ref="qualityBarChart" :data="qualityBarChartData"></CompsQualityBarChart>
        </div>
      </div> -->

      <div class="statistical-chart">
        <div class="top-title">
          <span class="text">检查类别</span>
        </div>

        <div class="chart-content css-prel">
          <div v-if="typeOfInspectionData.load" class="noData">暂无数据</div>
          <CompsQualityBarChart ref="typeOfInspection" :data="typeOfInspectionData"></CompsQualityBarChart>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
  import CompsQualityBurnDownChart from './CompsQualityBurnDownChart'
  import CompsQualityPieChart from './CompsQualityPieChart'
  import CompsQualityBarChart from './CompsQualityBarChart'

  export default {
    //质量统计图
    name: "CompsQualityStatistics",
    components: {
      CompsQualityBurnDownChart,
      CompsQualityPieChart,
      CompsQualityBarChart
    },

    props:['qualityList','qualityTaskType','typelist'],

    data() {
      return {
        pickerOptions: {
          disabledDate: (time) => {
              return this.dealDisabledDate(time)
          }
        },
        qualityBurnDownChartDate: [],
        defaultBurnDownChartDate: [],//设置燃烧图时间范围
        statisticalTypeList: [],//问题类别集合，需手动插入一个“全部”选项
        statisticalSelectoptions: 1,//已选择的问题类别id
        qualityBurnDownChart: {//质量燃烧图数据
          colors: ['#1DA48C'],
          xAxis: [],
          item: []
        },

        severityGradeData: {//严重等级分布饼图数据
          colors: [],//'#D8D8D8', '#1890FF', '#F5222D'
          name: '严重等级',
          item: []
        },

        stateChartData: {//状态分布饼图数据
          colors: [],//'#1DA48C', '#F5222D', '#FAAD14'
          name: '状态',
          item: []
        },

        qualityBarChartData: {//各单位质量分布柱状图数据
          colors: ['#1DA48C', 'rgba(29,164,140,0.1)'],
          xAxis: [],
          item: [],
          load: false
        },

        typeOfInspectionData: {//检查类型柱状图数据
          colors: ['#1890FF'],
          xAxis: [],
          item: [],
          load: false
        }
      }
    },

    created() {
      this.setStatisticalTypeList();
      this.setDataList();
      this.setQualityBurnDownChartDfaultDate();
      this.getSeverityGradeData();
      this.getStateChartData();
      // this.getQualityBarChartData();//暂时隐藏 按单位分布
      this.getTypeOfInspectionData();
    },

    methods: {
      //测试用函数
      test(val) {
        console.log(val);
      },
      //设置数据类型下拉框
      setStatisticalTypeList() {
        this.statisticalTypeList = this.$staticmethod.DeepCopy(this.qualityTaskType);
        this.statisticalTypeList.unshift({aedt_guid: 1,AedtName: '全部'});
      },

      //设置时间选择范围
      dealDisabledDate(time) {
          if (this.defaultBurnDownChartDate.length>0) {
              let minTime =  new Date(this.defaultBurnDownChartDate[0]).getTime();
              let maxTime =  new Date(this.defaultBurnDownChartDate[1]).getTime();

              return time.getTime() < minTime  || time.getTime() > maxTime;
          }
      },

      //上访传输数据生保民脸中为前曾和
      //设置燃烧图起止时间
      setDataList() {
        if(this.qualityList.length == 0) return
        let storages = this.qualityList;
        //将任务列表数据 按开始时间从小到大排血
        storages.sort(function( a , b){
            return new Date(a['ExamineDate']).getTime() - new Date(b['ExamineDate']).getTime();
        });
        this.qualityBurnDownChartDate[0] = storages[0].ExamineDate;//最小开始时间
        this.defaultBurnDownChartDate[0] = storages[0].ExamineDate;//最小开始时间

        //将任务列表数据 按截止时间从大到小排序
        storages.sort(function( a , b){
            return new Date(b['RectificateDate']).getTime() - new Date(a['RectificateDate']).getTime();
        });
        this.qualityBurnDownChartDate[1] = storages[0].RectificateDate;//最小开始时间
        this.defaultBurnDownChartDate[1] = storages[0].RectificateDate;//最小开始时间
      },

      //设置燃烧图的数据
      setQualityBurnDownChartDfaultDate(value) {
        if (value) {
          this.qualityBurnDownChartDate = value;

          this.qualityBurnDownChart.xAxis = [];
          this.qualityBurnDownChart.item = [];
        }
        this.getQualityBurnDownChart();
      },

      //获取并设置质量燃烧图数据
      getQualityBurnDownChart() {
        if(this.qualityBurnDownChartDate.length == 0) return
        let setMinTime = this.qualityBurnDownChartDate[0].split('T')[0];
        let setMaxTime = this.qualityBurnDownChartDate[1].split('T')[0];
        // 原接口/api/Examine/Exam/Exam_Statistics 改为api/Examine/Exam/GetStatisticsData
        this.$axios.get(`${this.$configjson.webserverurl}/api/Examine/Exam/GetStatisticsData?Token=${this.$staticmethod.Get('Token')}`, {
          params: {
            organizeId: this.$staticmethod._Get("organizeId"),
            minStartdt: `${setMinTime}`,
            maxEndDt: `${setMaxTime}`,
            linkType: this.typelist
          }
        }).then(res => {
          // console.log(res,'获取并设置质量燃烧图数据');
          if (res.data.Ret == 1) {
            let list = res.data.Data.List;
            for (let key in list) {
              this.qualityBurnDownChart.xAxis.push(key);//日期
               this.qualityBurnDownChart.item.push(list[key]);//未合格数量
            }
            this.$refs.qualityBurnDownChart.initBurnDownChart();
          } else {
            this.$message.error(res.data.Msg)
          }
        }).catch(res => {
          console.log(res);
        });

      },

      //获取并设置 严重等级分布饼图数据
      getSeverityGradeData() {
        //依次为 一般，严重，非常严重,其他的
        this.severityGradeData.colors = ['#007AFF','#FF7700','#F41515','#D8D8D8'];
        let normal = 0;//一般
        let serious = 0;//严重
        let verySerious = 0;//非常严重
        let other = 0;//没有严重等级的

        //循环统计各个状态的个数
        this.qualityList.forEach(element=>{
          //一般
          if (element.aede_severitylevel == '一般') {
            normal += 1;
          }

          //严重
          if (element.aede_severitylevel == '严重') {
            serious += 1;
          }

          //非常严重
          if (element.aede_severitylevel == '非常严重') {
            verySerious += 1;
          }

          //没有严重等级的
          if (element.aede_severitylevel == null || element.aede_severitylevel == '') {
            other += 1;
          }
        });

        this.severityGradeData.item[0] = {name: '一般',value: normal};
        this.severityGradeData.item[1] = {name: '严重',value: serious};
        this.severityGradeData.item[2] = {name: '非常严重',value: verySerious};
        this.severityGradeData.item[3] = {name: '-',value: other};

        setTimeout(()=>{
          this.$refs.severityGradePieChart.initPieChart();
        },1000);

      },

      //获取并设置 状态分布饼图数据
      getStateChartData() {

        //依次为 待检查、待整改、待验收、已合格
        this.stateChartData.colors = ['#616F7D','#B35900','#006BB3','#008073'];
        let noPass = 0;//待检查
        let needToDo = 0;//待整改
        let needPass = 0;//待验收
        let pass = 0;//已合格

        //循环统计各个状态的个数
        this.filterTQualityDataList.forEach(element=>{
          //待检查
          if (element.ExamineResult == 'A_ToBeCheck') {
            noPass += 1;
          }

          //待整改
          if (element.ExamineResult == 'B_ToBeRectified') {
            needToDo += 1;
          }

          //待验收
          if (element.ExamineResult == 'C_ToBeRecheck') {
            needPass += 1;
          }

          //已合格
          if (element.ExamineResult == 'D_Qualified') {
            pass += 1;
          }
        });

        this.stateChartData.item[0] = {name: '待检查',value: noPass};
        this.stateChartData.item[1] = {name: '待整改',value: needToDo};
        this.stateChartData.item[2] = {name: '待验收',value: needPass};
        this.stateChartData.item[3] = {name: '已合格',value: pass};

        setTimeout(()=>{
          this.$refs.statePieChart.initPieChart();
        },1000);
      },

      //获取并设置各单位质量分布柱状图数据
      // getQualityBarChartData() {
      //   this.$axios.get(`${this.$configjson.webserverurl}/api/Examine/Exam/GetUserCompanyStatisticsData?Token=${this.$staticmethod.Get('Token')}`, {
      //     params: {
      //       bimcomposerId: this.$staticmethod._Get("bimcomposerId"),
      //     }
      //   }).then(res => {
      //     // console.log(res);
      //     if (res.data.Ret = 1) {
      //       let data = res.data.Data.List;
      //       let PassCnt = [];//已解决质量数
      //       let TotalCnt = [];//全部质量数

      //       data.forEach(item => {
      //         this.qualityBarChartData.xAxis.push(item.FullName);
      //         PassCnt.push(item.PassCnt);
      //         TotalCnt.push(item.TotalCnt);
      //       });

      //       this.qualityBarChartData.item = [
      //         {name: '已解决质量数', data: PassCnt},
      //         {name: '全部质量数', data: TotalCnt},
      //       ];

      //       this.$refs.qualityBarChart.initBarChart();

      //       if (data.length == 0) {
      //         this.qualityBarChartData.load = true;
      //       }
      //     }
      //   }).catch(res => {
      //     console.log(res);
      //   });

      //   // this.qualityBarChartData.colors = ['#1DA48C', 'rgba(29,164,140,0.1)'];
      //   // this.qualityBarChartData.xAxis = ["单位000-1", "单位000-2", "单位000-3", "单位000-4", "单位000-5", "单位000-6", "单位000-7", "单位000-8", "单位000-9"];
      //   // this.qualityBarChartData.item = [
      //   //   {name: '已解决质量数', data: [1, 8, 19, 4, 1, 12, 7, 28, 9]},
      //   //   {name: '全部质量数', data: [5, 20, 36, 10, 10, 20, 15, 30, 19]},
      //   // ]
      // },

      //获取并设置检查类型柱状图数据
      getTypeOfInspectionData() {
        // typeOfInspectionData: {//检查类型柱状图数据
        //   colors: ['#1890FF', 'rgba(24,144,255,0.1)'],
        //   xAxis: [],
        //   item: [],
        //   load: false
        // }
        let storages = [];
        this.qualityTaskType.forEach(item=>{

          //设置x轴数据
          this.typeOfInspectionData.xAxis.push(item.AedtName);
          storages.push(0);
        });

        this.typeOfInspectionData.xAxis.forEach((item,index)=>{
          this.qualityList.forEach(element=>{
            //跟数据列表循环对比，如果问题类型相同，则对应的数据+1
            if (item == element.aedt_name) {
              storages[index] += 1;
            }
          });
        });

        this.typeOfInspectionData.item.push({name: '个数',data: storages})
        // this.typeOfInspectionData.item[0].name = '';
        // this.typeOfInspectionData.item[0].data = storages;

        // console.log(this.typeOfInspectionData,'检查类型柱状图数据')
        setTimeout(()=>{
          this.$refs.typeOfInspection.initBarChart();
        },1000)



        // this.$axios.get(`${this.$configjson.webserverurl}/api/Examine/Exam/GetExamTypeStatisticsData`, {
        //   params: {
        //     bimcomposerId: this.$staticmethod._Get("bimcomposerId"),
        //   }
        // }).then(res => {
        //   // console.log(res);
        //   if (res.data.Ret = 1) {
        //     let data = res.data.Data.List;

        //     let PassCnt = [];//合格数量
        //     let TotalCnt = [];//全部数量

        //     data.forEach(item => {
        //       this.typeOfInspectionData.xAxis.push(item.TypeName);
        //       PassCnt.push(item.passcnt);
        //       TotalCnt.push(item.totalcnt);
        //     });

        //     this.typeOfInspectionData.item = [
        //       {name: '合格数量', data: PassCnt},
        //       {name: '全部数量', data: TotalCnt},
        //     ];

        //     this.$refs.typeOfInspection.initBarChart();

        //     if (data.length == 0) {
        //       this.typeOfInspectionData.load = true;
        //     }
        //   }
        // });

        // this.typeOfInspectionData.colors = ['#1890FF', 'rgba(24,144,255,0.1)'];
        // this.typeOfInspectionData.xAxis = ["单位000-1", "单位000-2", "单位000-3", "单位000-4", "单位000-5", "单位000-6", "单位000-7", "单位000-8", "单位000-9"];
        // this.typeOfInspectionData.item = [
        //   {name: '合格数量', data: [1, 8, 19, 4, 1, 12, 7, 28, 9]},
        //   {name: '全部数量', data: [5, 20, 36, 10, 10, 20, 15, 30, 19]},
        // ]
      },
    },

    computed: {
      filterTQualityDataList() {
        let storages = []

        if (this.statisticalSelectoptions == 1) {
          storages = this.qualityList;
        } else {
          this.qualityList.forEach(item=>{
            if (item.aedt_guid.search((this.statisticalSelectoptions)) != -1) {
              storages.push(item);
            }
          });
        }
        
        return storages;
      }
    }

  }
</script>

<style>
  .quality-statistics .quality-burn-down-chart .el-input__inner {
    height: 40px;
    border-radius: 4px;
    border-width: 1px;
  }

  .statistical-chart .statistical-select .el-input__inner {
    border-radius: 5px;
    border: 1px solid #dcdcdc;
    padding-left: 5px;
  }
</style>

<style scoped>
  .quality-statistics {
    height: 100%;
  }

  .quality-statistics .statistical-chart {
    width: 100%;
    border-radius: 4px;
    margin-bottom: 20px;
    padding-bottom: 20px;
    background-color: #FFFFFF;
    box-shadow: 0 1px 1px 0 rgba(0, 21, 41, 0.12);
  }

  .quality-statistics .statistical-chart .top-title {
    height: 60px;
    line-height: 60px;
    text-align: left;
    padding: 0 28px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.09);
  }

  .quality-statistics .statistical-chart .top-title > .text {
    font-size: 16px;
    font-weight: 500;
  }

  .quality-statistics .quality-burn-down-chart .top-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .quality-statistics .statistical-chart .chart-content .noData {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    padding-top: 15%;
  }

  .burndown-legend .reference-line::after {
    background-color: #0B5EA4;
  }

  .burndown-legend .actual-line {
    margin-left: 15px;
  }

  .burndown-legend .actual-line::after {
    background-color: #1DA48C;
  }

  .burndown-legend .legend-line::after {
    content: '';
    content: '';
    width: 40px;
    height: 5px;
    display: inline-block;
    margin-bottom: 3px;
  }
</style>
