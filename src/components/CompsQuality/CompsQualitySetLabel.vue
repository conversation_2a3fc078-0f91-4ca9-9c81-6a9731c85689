<template>
	<div class="_css-pano-lable">
		<div class="set-title">
			<p @click.stop="close">
				<i class="icon-arrow-left_outline"></i>问题类型
			</p>
			<p class="fc" @click="addMaterial">
				<i class="icon-interface-addnew"></i>
				添加
			</p>
		</div>
		<div class="set-list">
			<ul class="_css_bugs_and_bb_noWork css-miniscroll">
				<li v-for="item in tagOptionsArr" :key="item.AedtGuid">
					<div
            class="li-name" 
					>
						{{ item.AedtName }}
					</div>
					<div class="edit">
						<i class="icon-interface-edit_se" @click.stop="editMaterial(item)"></i>
						<i class="icon-suggested-close i_close" @click.stop="predel(item)"></i>
					</div>
				</li>
			</ul>
		</div>
    <div
      class="maker"
      @click.stop="dialogConfig.show = false"
      v-show="dialogConfig.show"
    >
      <div class="center" @click.stop>
        <header>
          <span>{{ dialogConfig.type == 0 ? "添加问题类型" : "修改问题类型" }}</span>
          <i
            class="icon-suggested-close"
            @click.stop="dialogConfig.show = false"
          ></i>
        </header>
        <main>
          <p>类型名称<el-input
            v-model="dialogConfig.AedtName"
            placeholder="请输入类型名称"
          ></el-input></p>
           
          <div class="btn-wp">
            <div class="btns-right">
              <div @click.stop="cancelediting">取消</div>
              <div @click.stop="submit">确定</div>
            </div>
          </div>
        </main>
      </div>
    </div>
	</div>
</template>
<script>
export default {
	name: "CompsQualitySetLabel",
	components: {},
	data() {
		return {
      tagOptionsArr: [], // 问题类型
      dialogConfig: {
        show: false,
        AedtName: "",
        AedtGuid: "",
        type: 0, //0新增
      },
      
    };
	},
  props: {
    tagOptions: {
      type: Array,
      default: [] 
    },
    AedtType: {
      type: String,
      default: 'quality'
    },
  },  
  watch: {
    tagOptions(val){
      this.tagOptionsArr = val
    }
  },
	mounted() {
    this.tagOptionsArr = this.tagOptions;
  },
	methods: {
    close(){
      this.$emit("close")
    },
    // 点击添加问题类型
    addMaterial() {
      this.dialogConfig.AedtName = "";
      this.dialogConfig.type = 0;
      this.dialogConfig.show = true;
    },
    // 问题类型弹窗取消
    cancelediting() {
      this.dialogConfig.show = false;
    },
    // 问题类型弹窗确定
    submit() {
      if (this.dialogConfig.AedtName.length > 0) { 
          if(this.dialogConfig.type == 0){
            // 添加
            let data = { 
              organizeId: this.$staticmethod._Get("organizeId"),
              AedtType: this.AedtType,
              AedtName:this.dialogConfig.AedtName
            };
            this.createExamineDataType(data)
          }else{
            let editdata = { 
              AedtGuid: this.dialogConfig.AedtGuid,
              AedtName:this.dialogConfig.AedtName
            };
            this.modifyExamineDataType(editdata)
          }
          
      } else {
        this.$message.error("请输入问题类型名称");
      }
    },
    async createExamineDataType(data){
      const res = await this.$api.postCreateExamineDataType(data)
      if(res.Ret == 1){
        this.$message({
          message: '添加成功',
          type: "success",
        });
        this.$parent.getTagOption();
        this.$parent.getQualityList();
        this.dialogConfig.show = false;
      }
    },
    async modifyExamineDataType(data){
      const res = await this.$api.postModifyExamineDataType(data)
      if(res.Ret == 1){
        this.$message({
          message: '编辑成功',
          type: "success",
        });
        this.$parent.getTagOption();
        this.$parent.getQualityList();
        this.dialogConfig.show = false;
      }
    }, 
    editMaterial(item){ 
      this.dialogConfig.AedtName = item.AedtName;
      this.dialogConfig.type = 1;
      this.dialogConfig.AedtGuid = item.AedtGuid;
      this.dialogConfig.show = true;
    },  
    predel(item) {
      var _this = this;
      _this
        .$confirm("确认删除该问题类型？", "操作确认", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        })
        .then((x) => {
          _this.del(item);
        })
        .catch((x) => {});
    },
    async del(item) { 
      const res = await this.$api.postDeleteExamineDataType(item.AedtGuid)
      if(res.Ret == 1){
        this.$message({
          message: '删除成功',
          type: "success",
        });
        this.$parent.getTagOption();
        this.dialogConfig.show = false;
      }
    },
  },
};
</script>
<style lang="stylus" scoped rel="stylesheet/stylus">
._css-pano-lable{
  display: flex;
  flex-direction: column;
  .set-title{
    height: 48px;
    background: #F5F5F5;
    display: flex;
    align-content: center;
    line-height: 48px;
    padding: 0 16px 0 20px;
    justify-content: space-between; 
    width: calc(100% - 36px);
    p{
      font-size: 14px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #222222;
      i{
        cursor pointer;
        vertical-align: middle;
        padding-right: 4px;
      }
    }
    p.fc{
      color: #007AFF;
      i{
        padding-right: 8px
      }
    }
  }
  .set-list{
    ._css_bugs_and_bb_noWork li:hover .edit{
      display: flex;
    }
    ul{
      li{
        display: flex;
        margin-top: 20px;
        margin-right: 16px;

      }
      .li-name{
        height: 30px;
        line-height: 30px;
        width: 122px; 
        border-radius: 2px; 
        margin: 0 14px 0 16px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        padding: 0 10px;
        background: rgba(0, 170, 255, 0.1)
        border: 1px solid rgb(0, 170, 255);
        color: rgb(0, 170, 255);
      }
      
      .edit{
        display: none;
        i{
          color: #007AFF;
          font-size: 20px;
          margin-top: 5px; 
          margin-bottom: 5px;
          cursor: pointer; 
        }
        .i_close{
          margin-left:10px 
        }
      }
    }
  }
}
.maker {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1001;

  .center {
    width: 410px;
    height: 204px;
    background: #ffffff;
    border-radius: 4px;
    box-shadow: 0px 13px 24px -17px rgba(11, 41, 62, 0.8);
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    display: flex;
    flex-direction: column;

    header {
      height: 64px;
      line-height: 64px;
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      align-items: center;
      padding: 0 24px;

      span {
        font-size: 20px;
        color: rgba(0, 0, 0, 0.85);
      }

      i {
        color: #bfbfbf;
        cursor: pointer;
      }
    }

    main {
      padding: 0 24px;
      flex: 1;

      .btn-wp {
        margin-top: 20px;
        display: flex;
        flex-direction: row;
        justify-content: flex-end;

        &>div {
          color: red;
          line-height: 40px;
          cursor: pointer;

          &.btns-right {
            display: flex;
            flex-direction: row;

            div {
              color: #ffffff;
              width: 76px;
              height: 40px;
              line-height: 40px;
              background: #1890FF;
              border-radius: 2px;
              margin-left: 16px;
              opacity: 0.8;

              &:hover {
                opacity: 1;
              }

              &:first-child {
                color: rgba(0, 0, 0, 0.85);
                background: #fff;
              }
            }
          }
        }
      }

      p {
        margin-top: 10px;
        font-size: 14px;
        color: rgba(0,0,0,.65);
        display: flex;
        line-height: 50px;
        background: #f8f8f8;
        padding-left: 20px;
        border-radius: 4px;
        /deep/ .el-input{
          width: calc(100% - 80px);
        }
      }

      .color-list {
        height: 80px;
        display: flex;
        flex-direction: row;
        align-items: center;

        div {
          width: 40px;
          height: 40px;
          border-radius: 4px;
          cursor: pointer;
          margin-left: 12px;
          display: flex;
          align-items: center;
          justify-content: center;
          color: #fff;

          &:first-child {
            margin-left: 5px;
          }
        }
      }
    }
  }
}

</style>