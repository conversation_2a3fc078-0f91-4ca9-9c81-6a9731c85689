<template>
  <div class="quality-manage-set" @click.stop="parentClickEvent()" :key="refreshTree">
    <div class="top-bar">
      <span @click="vClickOutClose" class="icon icon-arrow-left_outline"></span>
      <span class="title">现场管理设置</span>
      <span v-if="modelSrc != ''" class="button" @click="modelSrc=currentOpenModelID=''">关闭模型</span>
      <span v-if="modelSrc != ''" class="correlation-model button" @click="qualityElementsRelate">关联构件</span>
    </div>

    <div class="bottom-container">
      <div class="structural-tree" ref="structuralTree" v-loading="loading">
        <div class="css-prel">
          <div class="drag" v-tree-drag></div>

          <div class="add-organization-structure">
            <span class="icon icon-interface-quality"></span>
            <span class="title">工程结构</span>
            <span @click.stop="toggleStructureMenu()" class="add icon-interface-addnew css-cp">
            </span>

            <div class="structure-menu-list" ref="structureMenuList" v-if="toggleStructureMenuState">
              <ul>
                <li class="menu-list" @click.stop="showQualityFilesList">
                  <span class="icon icon-interface-cloud-upload"></span>
                  导入工程结构
                  <!--<input type="file" style="display:none" accept=".xlsx,.xls" multiple ref="qualityManageInput" @change="selectQualityManageFile" />-->
                </li>
                <li class="menu-list" @click.stop="addQualityManage('structureMenuList')">
                  <span class="icon icon-interface-addnew"></span>
                  新建工程结构
                </li>
              </ul>
            </div>
          </div>

          <div class="tree-container">
            <el-tree
              ref="qualityTree"
              :props="defaultProps"
              :expand-on-click-node="false"
              node-key="ec_guid"
              lazy
              highlight-current
              @node-click="selectCurrentTreeNode"
              :load="loadTreeNode">
              <div class="tree-node" slot-scope="{ node, data }">
                <span class="node-name">{{ data.ec_name }}</span>
                <div class="css-prel">
                  <!--<span class="css-mr5">{{ data.num == 0 ? '' : data.num}}</span>-->
                  <el-tooltip :open-delay=1000 effect="dark" content="在模型中查看" placement="right">
                    <span class="icon-menu icon-interface-guanlianmoxing"
                          v-if="data.containsElements > 0"
                          @click.stop="() => selectCurrentTreeNode(data, node,'')"></span>
                  </el-tooltip>
                  <span class="icon-menu icon-interface-list"
                        @click.stop="($event) => openTreeMenu(data,node, $event)"></span>
                </div>
              </div>
            </el-tree>
          </div>
        </div>
      </div>

      <div class="drag-cover"></div>

      <div class="model-window" :style="{backgroundImage: `url(${defaultImg.src})`}" v-loading="fullscreenLoading" element-loading-text="正在请求模型相关数据...">
        <span @click="correlationModelEvent" :class="[{'not-allowed': !isRootTree},'button correlation-model']">选择关联模型</span>
        <div class="model-iframe-window" v-if="modelSrc != ''">
          <iframe width="100%" height="100%" ref="iframeWp" :src="modelSrc" frameborder="0"></iframe>
        </div>
      </div>

    </div>

    <CompsSelectModel
      v-if="modelListState"
      @getModelInfo="getModelInfo"
      @close="modelListState=false"></CompsSelectModel>

    <!--工程结构输入框弹窗-->
    <CompsQualityManageDialog
      @close-dialog="closeQualityManageDialog"
      :qualityManageDialogData=qualityManageDialogData
      @onclk="handleDialogCallback"
      v-if="qualityManageDialogData.state"></CompsQualityManageDialog>

    <div ref="treeMenuListRef" class="tree-menu-list" :style="menuCoordinate" v-show="menuListState" v-click-out-close="closeTreeMenu">
      <div class="top-bar">
        <span class="title">工程结构菜单</span>
        <span @click.stop="closeTreeMenu" class="icon icon-suggested-close"></span>
      </div>

      <div class="container">
        <ul>
          <li @click.stop="correlationModelEvent" v-if="isRootTree">
            <span class="icon icon-interface-guanlianmoxing"></span>
            {{ hasCorrelationModel ? '更换关联模型' : '选择关联模型'}}
          </li>
          <li @click.stop="deleteModelOrComponent('root')" v-if="isRootTree && hasCorrelationModel">
            <span class="icon icon-interface-delete-fill"></span>
            删除关联模型
          </li>
          <li @click.stop="changeCorrelationModelEvent" v-if="hasCorrelationModel && isTreeLeaf">
            <span class="icon icon-interface-associated-component"></span>
            更新关联模型构件
          </li>

          <li @click.stop="deleteModelOrComponent('leaf')" v-if="hasCorrelationModel && isTreeLeaf">
            <span class="icon icon-interface-delete-fill"></span>
            删除关联模型构件
          </li>
          <li v-for="(item,index) in menuListData" :key=index @click.stop="treeMenuHandle(item)">
            <span :class="item.iconClass" class="icon"></span>
            {{ item.text }}
          </li>

        </ul>
      </div>
    </div>

    <CompsStepTip2
      :zIndex=10
      mubanimgclass="template-QualitySetimport"
      :useNewClick="true"
      v-if="showingImportTemplate"
      width="504"
      @oncancel="showingImportTemplate = false"
      @onok="_onimportok"
      @ontemplate="_ontemplate"
      title="导入工程结构"
    ></CompsStepTip2>
  </div>
</template>

<script>
  import CompsQualityManageDialog from './CompsQualityManageDialog';
  import CompsSelectModel from "@/components/CompsMaterial/CompsSelectModel"
  import CompsStepTip2 from "@/components/CompsCommon/CompsStepTip2";//导入问题

  export default {
    //质量管理设置
    name: "CompsQualityManageSet",

    components: {
      CompsQualityManageDialog,
      CompsSelectModel,
      CompsStepTip2,
    },

    data() {
      return {
        showingImportTemplate: false,
        currentOpenModelID: "",//当前打开的模型ID
        refreshTree: Date.parse(new  Date()),
        loading: false,
        modelListState: false,//关联模型的列表状态
        fullscreenLoading: false,//加载模型loading
        modelSrc: '',//模型的src
        selectelements: [], // 选中的构件数据
        OrganizeId: '',
        Token: '',
        toggleStructureMenuState: false,//切换顶部工程结构菜单
        defaultImg: {
          src: require('../../assets/svgs_loadbyurl/interface-hebingmoxing.svg') 
        },

        qualityManageDialogData: {//质量管理输入框对话框
          state: false,//状态
          title: '',
          Subheading: '',
          coordinate: {//输入对话框坐标
            top: 0,
            left: 0,
          },
          type: '',
        },

        menuCoordinate: {//menu-list对话框坐标
          top: 0,
          left: 0,
        },

        menuListState: false,//tree菜单状态

        menuListData: [
          {iconClass: 'icon-interface-edit', text: '编辑节点', type: 'edit'},
          {iconClass: 'icon-interface-addnew', text: '添加子节点', type: 'add'},
          {iconClass: 'icon-interface-delete', text: '删除节点', type: 'delete'},
        ],
        isRootTree: false,//当前点击的树 是否是根级树
        isTreeLeaf: false,//当前点击的树 是否是叶子节点

        hasCorrelationModel: false,//当前点击的树 是否已经关联模型

        currentTreeNodeData: {//当前操作的树节点的数据
          node: {},
          data: {}
        },
        initTreeNodeData: {//初始化时的树节点的数据
        },
        parentTreeNodeData: {},//当前点击的叶子节点的父节点数据

        // data: [
        //   {
        //     ec_code: "001",
        //     ec_curlevelnumber: 0,
        //     ec_elementids: "",
        //     ec_guid: "0511f63f-f0a8-11e9-b43e-fa163e36b61c",
        //     ec_name: "a1433",
        //     ec_organizeId: "48617e7b-07f2-4748-9199-238af8f2bfc6",
        //     ec_type: "quality",
        //   }
        // ]
        defaultProps: {
          children: 'children',
          label: 'ec_name',
        }

      }
    },

    created() {
      // this.GetQualitySafeCategories();
      this.getRelatedParameters();
    },

    methods: {
      test(e) {
        // this.toggleStructureMenuState=false;
      },
      //父节点关联模型
      parentQualityModel() {

      },

      //点击导入按钮
      _onimportok(fileinput) {

        let files = fileinput.files;
        if (files.length == 0) {
          this.$message.error('请先选择导入文件');
          return;
        }

        this.ImportQualityCategories(fileinput.files[0]);
      },

      //下载模板
      _ontemplate() {
        window.location.href = `${this.$configjson.webserverurl}/Content/Resource/Template/质量安全任务分解结构导入模板.xlsx`;
      },

      //关联模型构件
      qualityElementsRelate () {

        if (Object.keys(this.currentTreeNodeData.node).length == 0) {
          this.$message.error('请在左侧树形列表中选择一个底层节点(叶子节点)');
          return false;
        } else {
          if (!this.currentTreeNodeData.node.expanded || !this.currentTreeNodeData.node.isLeaf) {
            this.$message.error('请确保选择的是一个底层节点(叶子节点)');
            return false;
          }
        }

        if (this.selectelements.length == 0) {
          this.$message.error('请在模型上至少选择一个构件');
          return false;
        }

        //获取当前相机位置
        var tocall_getviewpointbasicinfo = this.$staticmethod.bimhelper_getview(this.$refs.iframeWp.contentWindow);
        let viewPoint = tocall_getviewpointbasicinfo.getViewPointBasicInfo();//this.$refs.iframeWp.contentWindow.BI1Me.view.BIMeV1iewpoint.getViewPointBasicInfo();
        let bme_elementids = [{
          modelid: this.selectelements[0].split('^')[0],//模型id
          elementids: [],//构件id集合
          viewpoint: JSON.stringify(viewPoint),//相机位置
        }];

        //遍历截取构件id
        this.selectelements.forEach(item =>{
          let elementids = item.split('^')[1];
          bme_elementids[0].elementids.push(elementids);
        });

        // console.log(bme_elementids);
        // console.log(this.currentTreeNodeData.node);

        this.loading = true;
        let guid = this.currentTreeNodeData.data.ec_guid;
        this.modifyQualitySafeCategorie('', guid, bme_elementids, 'relation')
      },

      //点击选择当前的子节点
      selectCurrentTreeNode(data, node, arr) {
        // console.log(data, node, arr);
        this.parentClickEvent();
        this.currentTreeNodeData.data = data;
        this.currentTreeNodeData.node = node;

        this.treeStateSelection(data,node);

        //如果当前点击的节点的父节点为undefined 则说明其是根节点
        if (node.parent.data == undefined) {
          this.isRootTree = true;
        } else {
          this.isRootTree = false;
        }

        //每次点击树或者预览图标前，如果模型已打开 则清空已选择的构件
        if (this.modelSrc != '') {

          // 新：model.BIM365API.Controller.removeAl1lHighlightElements
          // 旧：B1IMe.control.BIMeSelector.removeAllSel1ectorElements
          //this.$refs.iframeWp.contentWindow.BI1Me.control.BIMeSelector.remove1AllSelectorElements();
          //this.$staticmethod.bimhelper_cancelhi1ghlight(this.$refs.iframeWp.contentWindow)();
        
          var chlobj = this.$staticmethod.bimhelper_cancelhighlight(this.$refs.iframeWp.contentWindow);
          this.$staticmethod.bimhelper_callcancelhighlight(chlobj);
        
        }

        //清除之前选择的构件id;
        this.selectelements = [];

        //字符串分割，取第一位，也就是最顶级父元素的ec_code
        let code = data.ec_code.split('-')[0];
        //将最顶级父元素的ec_code传给openModelWindow，获取当前树关联的模型id
        if (arr == '') {
          //如果arr为空 则说明是点击模型图标预览
          this.previewModelWindow(data, node, arr);
          //点击模型图标不会触发高亮显示当前选中的tree，需手动设置高亮
          this.$refs.qualityTree.setCurrentKey(data.ec_guid);
        } else {
          this.previewModelWindow('', '', code);
        }

        // let params = {};
        // if (data.firstElements == '') {
        //   this.modelSrc = '';
        // } else {
        //   params.modelid = JSON.parse(data.firstElements)[0].modelid;
        //   this.getModelInfo(params);
        // }
      },

      ImportQualityCategories(file) {
        let fd = new FormData();
        fd.append('organizeId',this.$staticmethod._Get("organizeId"));
        fd.append('f',file);
        this.$axios.post(`${this.$configjson.webserverurl}/api/Examine/Exam/ImportQualityCategories?Token=${this.$staticmethod.Get('Token')}`,fd).then(res=>{
          if (res.data.Ret == 1) {
            this.$message.success('导入成功');
            this.refreshTree = Date.parse(new  Date());
            this.showingImportTemplate = false;
          } else {
            this.$message.error('导入失败，请刷新再试');
          }
        }).catch(res=>{
          console.log(res);
        });
      },

      //导入工程结构文件
      selectQualityManageFile(e) {
        if (e.target.files[0] != '' && e.target.files[0] != undefined) {

          let hasSuffix = e.target.files[0].name.lastIndexOf('.');
          let fileSuffix = '';
          if(hasSuffix != -1) {
            fileSuffix =  e.target.files[0].name.substr(hasSuffix + 1,e.target.files[0].name.length).toLowerCase();
          } else {
            fileSuffix = '';
          }

          if(fileSuffix != 'xlsx' && fileSuffix != 'xls') {
            this.$message({
              showClose: true,
              message: '请选择一个格式为.xlsx的文件',
              type: 'error'
            });
          } else {
            this.ImportQualityCategories(e.target.files[0]);
          }
        }

      },

      getRelatedParameters() {
        this.OrganizeId = this.$staticmethod._Get("organizeId");
        this.Token = this.$staticmethod.Get("Token");
      },

      //获取选择的模型信息
      getModelInfo(data) {
        // console.log(data);
        this.modelListState = false;//关闭模型选择列表

        let bme_elementids = [{
          modelid: data.modelid,//模型id
          elementids: [],//构件id集合
        }];
        this.loading = true;
        let guid = this.currentTreeNodeData.data.ec_guid;

        new Promise((resolve,reject) => {
          this.modifyQualitySafeCategorie('', guid, bme_elementids, 'relation');
          // reject('当前项目相关数据为空');
          resolve(data);
        }).then(data => {
          this.openModelWindow(data);
        }).catch(res => {
          console.log(res);
        });

      },

      //打开模型窗口
      openModelWindow(data) {
        var _this = this;
        this.currentOpenModelID = data.modelid;
        this.fullscreenLoading = true;
        this.modelSrc = `${this.$configjson.bimviewerurl}?projectId=${this.$staticmethod._Get("bimcomposerId")}&model=${data.modelid}`;
        let iframeWindow = null;
        this.$nextTick(() => {
          this.$refs.iframeWp.onload = () => {
            this.fullscreenLoading = false;
            iframeWindow = this.$refs.iframeWp.contentWindow;


            _this.$staticmethod.bimhelper_finishrender(iframeWindow, () => {

              //高亮显示关联的构件
              if (data.elementids) {
                setTimeout(()=>{
                  //iframeWindow.B1IMe.control.BIM1eZoom.zoomE1lementByElementId(data.elementids);
                  _this.$staticmethod.bimhelper_getzoomer(iframeWindow).zoomElementByElementId(data.elementids);
                },1000)
              }

              this.$staticmethod.bimhelper_onSelect(iframeWindow,  elementid => {
                // console.log(elementid);
                this.selectelements = elementid;
              });
              // iframeWindow.BIM1e.control.BIMeSelector.selectorCallBack(
              //   elementid => {
              //     // console.log(elementid);
              //     this.selectelements = elementid;
              //   }
              // );

              //iframeWindow.B1IMe.view.BIMeSelection.isShowMultipleSelection(false);
              console.error('暂不支持 isShowMultipleSelection');

              //如果相机位置数据不为空，则调整视角到保存的相机位置视角
              if (data.viewpoint != '' && data.viewpoint != null) {
                this.$staticmethod.bimhelper_getview(this.$refs.iframeWp.contentWindow).setViewPointBasicInfo(JSON.parse(data.viewpoint));
              }

            });

            //iframeWindow.B1IMe.event.BIMeEvent.finishRender.subsc1ribe();
          };
        });
      },

      loadTreeNode(node, resolve) {
        //首次加载  获取顶级节点
        if (node.level === 0) {
          this.initTreeNodeData.node = node;
          this.initTreeNodeData.resolve = resolve;
          // return resolve([{ ec_name: '一级'}])
          this.GetQualitySafeCategories('', resolve);
        }

        //打开父节点
        if (node.level >= 1) {
          //传父节点的ec_code
          let likepara = node.data.ec_code;
          this.GetQualitySafeCategories(likepara, resolve);
        }

      },

      //获取质量数据分类（tree列表）
      GetQualitySafeCategories(likepara, resolve) {
        let type = 'quality';
        // let type = 'security';
        this.$axios.get(`${this.$configjson.webserverurl}/api/Examine/Exam/GetQualitySafeCategories?Token=${this.$staticmethod.Get('Token')}`, {
          params: {
            organizeId: this.OrganizeId,
            type: type,
            likepara: likepara,
          }
        }).then(res => {
          // console.log(res);
          if (res.data.Ret == 1) {
            let data = res.data.Data;
            resolve(data);
          }
        })
      },

      //关闭输入对话框
      //state: 为true 关闭所有弹窗  false 返回上级菜单
      closeQualityManageDialog(state) {
        this.qualityManageDialogData.title = '';
        this.qualityManageDialogData.type = '';
        this.qualityManageDialogData.coordinate.top = 0;
        this.qualityManageDialogData.coordinate.left = 0;
        this.qualityManageDialogData.state = false;
        if (state) {
          this.parentClickEvent();
        }
      },

      //对话框点击提交后的事件处理
      //type：操作的类型
      //name：节点的名字
      handleDialogCallback(type, name) {
        // console.log(type, name);

        this.loading = true;
        if (type == 'add') {
          let parentCode = '';
          //如果currentTreeNodeData的属性为空，说明是添加根节点
          if (Object.keys(this.currentTreeNodeData.node).length == 0) {
            //如果是根节点 parentCode传空
            parentCode = '';
          } else {
            //获取父节点的ec_code
            parentCode = this.currentTreeNodeData.data.ec_code;
          }
          this.addQualitySafeCategories(name, parentCode);

        } else if (type == 'edit') {
          let guid = this.currentTreeNodeData.data.ec_guid;
          this.modifyQualitySafeCategorie(name, guid,'','name')
        }

        this.closeQualityManageDialog(true);
      },

      //修改机构名称/关联构件
      modifyQualitySafeCategorie(name, guid, elementids, type) {
        let para = {
          Token: this.Token,
          ec_name: name,//要修改的数据的 ec_name（当调用修改名称接口时，不能为空）
          ec_guid: guid,//要修改的数据id
          ec_elementids: elementids!='' ? JSON.stringify(elementids) : '',//要修改的关联数据（当调用关联构件接口时，不能为空）
          modifytype: type//操作类型：修改名称传‘name’， 关联构件传‘relation’
        };

        this.$axios.post(`${this.$configjson.webserverurl}/api/Examine/Exam/ModifyQualitySafeCategorie`,
          this.$qs.stringify(para)).then(res => {
          // console.log(res);
          if (res.data.Ret == 1) {
            //修改名称
            if (type == 'name') {
              this.currentTreeNodeData.data.ec_name = name;
              this.$message.success('修改成功！');
            } else {
              this.$message.success('关联成功！');
              this.recursionSetContainsElementsID(JSON.stringify(elementids),this.currentTreeNodeData.data)
            }

          } else {
            this.$message.error(res.data.Msg);
          }
        }).catch(res => {
          console.log(res);
        });
        this.loading = false;
      },

      //递归设置父级的containsElements
      recursionSetContainsElementsID(elementids,nodeData) {
        let currentNode = this.$refs.qualityTree.getNode(nodeData);
        let num = 1;
        if (currentNode.data.hasOwnProperty("containsElements")) {
          num += currentNode.data.containsElements;
        }
        this.$set(currentNode.data,'containsElements',num);

        //如果为空说明 该树是第一次关联模型，需要将elementids保存，以便后续操作
        if (!currentNode.data.hasOwnProperty('firstElements') || currentNode.data.firstElements == '') {
          currentNode.data.firstElements = elementids;
          // this.$set(currentNode.data,'containsElements',1);
        }

        //如果父级还有数据，则递归赋值
        if (currentNode.parent.data != undefined) {
          this.recursionSetContainsElementsID(elementids,currentNode.parent.data)
        }
      },

      //添加质量分类子树
      addQualitySafeCategories(name, parentCode) {
        let data = {
          Token: this.Token,
          ec_organizeId: this.OrganizeId,
          ec_type: 'quality',
          ec_name: name,//节点名称
          parent_ec_code: parentCode//父节点ec_code。 顶级根节点传空
        };

        this.$axios.post(`${this.$configjson.webserverurl}/api/Examine/Exam/AddQualitySafeCategories`,
          this.$qs.stringify(data)).then(res => {
          // console.log(res)
          if (res.data.Ret == 1) {
            let data = res.data.Data;

            //如果parentCode为空 则传根级node，否则传当前选中的node
            if (parentCode == '') {
              // this.$refs.qualityTree.insertBefore(data, this.initTreeNodeData.node.childNodes[0]);
              this.initTreeNodeData.node.childNodes = [];
              this.loadTreeNode(this.initTreeNodeData.node, this.initTreeNodeData.resolve)
            } else {
              this.$refs.qualityTree.append(data, this.currentTreeNodeData.node);
            }

            this.$message.success('添加成功！');

          } else {
            this.$message.error(res.data.Msg);
          }
        }).catch(res => {
          console.log(res)
        });
        this.loading = false;

        /*以上是添加子节点不刷新当前tree，如需刷新当前tree 则需要用下方的代码*/

        // //parentCode为空 说明是添加顶级根节点 需要清空this.initTreeNodeData.node.childNodes
        // if (parentCode == '') {
        //   this.initTreeNodeData.node.childNodes = [];
        // }
        // //子节点清空，避免再次调用loadTreeNode 的时候 造成节点重复
        // this.currentTreeNodeData.node.childNodes = [];
        // this.loadTreeNode(this.initTreeNodeData.node, this.initTreeNodeData.resolve)
      },

      //删除工程结构
      removeQualitySafeCategories() {
        let para = {
          Token: this.Token,
          ec_code: this.currentTreeNodeData.data.ec_code,
          ec_guid: this.currentTreeNodeData.data.ec_guid,
          ec_organizeId: this.OrganizeId
        };

        this.$axios.post(`${this.$configjson.webserverurl}/api/Examine/Exam/RemoveQualitySafeCategories`,
          this.$qs.stringify(para)).then(res => {
          if (res.data.Ret == 1) {
            //获取当前删除的节点的父节点
            let parentNode = this.$refs.qualityTree.getNode(this.currentTreeNodeData.node.parent.data);
            this.$refs.qualityTree.remove(this.currentTreeNodeData.node);

            // console.log(this.currentTreeNodeData.node);
            this.recursionDeleteContainsElementsID(parentNode);

            this.$message({
              type: 'success',
              message: '删除成功!'
            });
          } else {
            this.$message.error(res.data.Msg);
          }
        }).catch(res => {
          console.log(res);
        });
        this.loading = false;
      },

      recursionDeleteContainsElementsID(parentNode) {
        if (parentNode != undefined && parentNode != null) {
          // console.log(parentNode);
          let num = 0;
          //如果父节点下 无其他子节点
          if (parentNode.childNodes.length == 0) {
            num = 0
          } else {
            parentNode.childNodes.forEach(item => {
              if (!item.data.hasOwnProperty('containsElements')) {
                num += 0;
              } else {
                num += item.data.containsElements;
              }
            })
          }
          this.$set(parentNode.data,'containsElements',num);

          if (parentNode.parent.data != undefined) {
            this.recursionDeleteContainsElementsID(parentNode.parent);
          }
        }
      },

      //tree菜单事件
      treeMenuHandle(item) {
        //删除工程结构
        if (item.type == 'delete') {
          this.$confirm('此操作将删除此节点及其所有子节点, 是否继续?', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            this.removeQualitySafeCategories();
          }).catch(() => {
            this.$message({
              type: 'info',
              message: '已取消删除'
            });
          });
        } else {
          //编辑或添加工程
          this.qualityManageDialogData.title = item.text;
          this.qualityManageDialogData.type = item.type;
          this.qualityManageDialogData.coordinate.top = this.menuCoordinate.top;
          this.qualityManageDialogData.coordinate.left = this.menuCoordinate.left;
          this.qualityManageDialogData.state = true;
        }

      },

      //递归删除子节点关联的模型数据
      //nodeData 传this.currentTreeNodeData.data
      recursionDeleteChildrensContainsElements(childNodes) {
        for (let i = 0; i<childNodes.length; i++) {
          if (childNodes[i].data.containsElements > 0) {
            //通过data获取当前节点的node
            let currentNode = this.$refs.qualityTree.getNode(childNodes[i].data);
            //关联构件的个数。 归零
            this.$set(currentNode.data,'containsElements',0);
            //关联的构件id。清空
            this.$set(currentNode.data,'ec_elementids','');
            //第一次关联的模型相关数据。清空
            this.$set(currentNode.data,'firstElements','');

            //如果还有子节点，则递归清空
            if (currentNode.childNodes.length > 0) {
              this.recursionDeleteChildrensContainsElements(currentNode.childNodes)
            }
          }
        }
        // console.log('1222222');
      },

      //清除已关联模型信息
      deleteModelOrComponent(type) {
        //isTreeLeaf isRootTree
        let msg = '';
        if (type == 'root') {
          msg = '删除模型则会清除该树下所有叶子节点已关联的构件。是否确定删除?';
        } else {
          msg = '确定删除当前节点与之对应构件的关联？'
        }

        this.$confirm(msg, '警告', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          new Promise((resolve,reject) => {
            this.clearQualitySafeCategoryRel(resolve,reject);
          }).then((data) => {
            let currentNode = this.$refs.qualityTree.getNode(this.currentTreeNodeData.data);
            this.$set(currentNode.data,'containsElements',0);
            this.$set(currentNode.data,'ec_elementids','');
            this.$set(currentNode.data,'firstElements','');
            if (type == 'leaf') {
              let elementId = [];
              if (this.currentTreeNodeData.data.ec_elementids.length > 0) {
                let elementids = JSON.parse(this.currentTreeNodeData.data.ec_elementids);
                elementids[0].elementids.forEach(item => {
                  elementId.push(`${elementids[0].modelid}^${item}`);
                });

                //取消高亮显示的构件
                //this.$refs.iframeWp.contentWindow.BI1Me.control.BIMeSelector.removeSelectorByElementId(elementId);
                //this.$staticmethod.bimhelper_cancelhighlight(this.$refs.iframeWp.contentWindow)(elementId);
                var chlobj = this.$staticmethod.bimhelper_cancelhighlight(this.$refs.iframeWp.contentWindow);
                this.$staticmethod.bimhelper_callcancelhighlight(chlobj);
              
              }
            }

            if (type == 'root') {
              this.recursionDeleteChildrensContainsElements(this.currentTreeNodeData.node.childNodes);
              this.modelSrc = '';
              this.currentOpenModelID = '';
            }
            this.selectelements = [];
            this.$message.success('删除成功');
          },(res) => {
            this.$message.error('数据请求出错，请稍后再试');
          });

        }).catch(() => {});
      },

      //清除已关联模型信息ajax
      clearQualitySafeCategoryRel(resolve,reject) {
        let params = {
          Token: this.Token,
          ec_guid: this.currentTreeNodeData.data.ec_guid,
        };
        this.$axios.post(`${this.$configjson.webserverurl}/api/Examine/Exam/ClearQualitySafeCategoryRel`,
          this.$qs.stringify(params)).then(res=>{
          // console.log(res);
          if (res.data.Ret == 1) {
            resolve(true);
          } else {
            reject(true);
          }
        }).catch(res=>{
          console.warn(res);
        });
      },

      //关联模型
      correlationModelEvent() {
        //如果该树已经关联模型，则需提示
        if (this.hasCorrelationModel) {
          this.$confirm('该树已关联模型,更换模型则会清除该树下所有叶子节点已关联的构件。是否要更换模型?', '警告', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            new Promise((resolve,reject) => {
              this.clearQualitySafeCategoryRel(resolve,reject);
              // reject('当前项目相关数据为空');
            }).then((data) => {
              this.$message.success('已清除关联模型及相关数据，可关联新模型');
              this.modelListState = true;
            }, (err) => {
                this.$message.error('数据请求出错，请稍后再试');
            });

          }).catch(() => {});
        } else {
          //打开模型选择列表
          this.modelListState = true;
        }

        // if (Object.keys(this.currentTreeNodeData.node).length == 0) {
        //   this.$message.error('请在左侧树形列表中选择一个顶级节点(根节点)');
        //   return false;
        // }

        // let code = this.currentTreeNodeData.data.ec_code.split('-')[0];//字符串分割，取第一位，也就是最顶级父元素的ec_code
        // //将最顶级父元素的ec_code传给openModelWindow，获取当前树关联的模型id
        // this.previewModelWindow('', '', code);

        this.closeTreeMenu();
      },

      //更改关联模型
      changeCorrelationModelEvent() {

      },

      //点击模型图标打开模型
      previewModelWindow(data, node, code) {
        //获取关联的模型id
        this.$axios.get(`${this.$configjson.webserverurl}/api/Examine/Exam/GetQualitySafeCategorie`,{
          params: {
            organizeId: this.OrganizeId,
            type: 'quality',
            codepara: code !='' ? code : data.ec_code,
            Token: this.$staticmethod.Get("Token")
          }
        }).then(res=>{
          // console.log(res);
          if (res.data.Ret == 1) {
            let params = {};

            //如果Relations有数据，说明当前的树之前是有关联过模型的，直接打开之前关联的模型
            if (res.data.Data.Relations.length > 0) {
              params.modelid = res.data.Data.Relations[0].modelid;//模型id
              if (res.data.Data.Relations[0].viewpoint != '' && res.data.Data.Relations[0].viewpoint != null) {
                params.viewpoint = res.data.Data.Relations[0].viewpoint;
              }
              //code为空 则说明是点击模型预览图标，则需要加上构件id
              if (code == '') {
                let elementIds = [];
                //遍历 拼接字符串格式为 “模型ID^构件ID”
                res.data.Data.Relations[0].elementids.forEach(item => {
                  elementIds.push(`${params.modelid}^${item}`)
                });

                params.elementids = elementIds;
              }

              // console.log(params.elementids);
              //如果modelSrc和请求的模型id相同，说明即将预览的模型已打开，直接高亮构件即可
              if (this.currentOpenModelID == params.modelid) {

                //恢复默认视角
                //this.$refs.iframeWp.contentWindow.BI1Me.control.BIMeUtility.mainViewpoint();
                this.$staticmethod.bimhelper_mainViewpoint(this.$refs.iframeWp.contentWindow);

                if (params.elementids != '' && params.elementids != undefined) {
                  //高亮显示构件


                  // 旧的方法： .BI1Me.control.BIMeZoom.zoomEleme1ntByElementId
                  // 新的方法： model.BIM365API.Controller.zoomElem1entByElementId
                  // this.$refs.iframeWp.contentWindow.BI1Me.control.BIMeZoom.zoomElem1entByElementId(params.elementids);
                  // ------------------
                  this.$staticmethod.bimhelper_getzoomer(this.$refs.iframeWp.contentWindow).zoomElementByElementId(params.elementids);

                  //this.$refs.iframeWp.contentWindow.BI1Me.view.BIMeSelection.isShowMultipleSelection(false);
                  console.error('暂不支持 isShowMultipleSelection');
                }

                //如果相机位置数据不为空，则调整视角到保存的相机位置视角
                if (params.viewpoint != '' && params.viewpoint != null) {
                  var tocall_getviewpointbasicinfo = this.$staticmethod.bimhelper_getview(this.$refs.iframeWp.contentWindow);
                  tocall_getviewpointbasicinfo.setViewPointBasicInfo(JSON.parse(params.viewpoint));
                }
              } else {
                this.openModelWindow(params);
              }

            } else {
              //否则的话，就清空modelSrc
              this.modelSrc = '';
              this.currentOpenModelID = ''
              // this.modelListState = !this.modelListState;
            }
          } else {
            this.$message.error('数据请求失败，请刷新再试');
          }
        }).catch(res=>{
          console.warn(res);
        });

        //data.modelid
      },

      //状态筛选
      treeStateSelection(data,node) {
        //是否是叶子节点
        this.isTreeLeaf = node.isLeaf;
        //是否是根节点
        node.parent.data == undefined ? this.isRootTree = true : this.isRootTree = false;
        //是否已关联模型
        this.hasCorrelationModel = data.containsElements > 0;
      },

      //切换树的菜单
      //参数：e  当前点击的dom
      openTreeMenu(data, node, e) {
        this.currentTreeNodeData.data = data;
        this.currentTreeNodeData.node = node;
        //由于禁止冒泡 点击菜单无法选中当前树，需手动触发
        this.$refs.qualityTree.setCurrentKey(data.ec_guid);

        // console.log(data, node, e);
        //保存当前点击的节点的数据(后续操作要用)
        // this.selectCurrentTreeNode(data, node);
        //hasCorrelationModel && isRootTree

        this.treeStateSelection(data,node);

        let domRect = e.target.getBoundingClientRect();
        let menuTop = 0;
        if (domRect.top + domRect.height + 260 > document.body.clientHeight) {
          menuTop = document.body.clientHeight - 260;
        } else {
          menuTop = domRect.top + domRect.height;
        }

        this.menuCoordinate.top = `${menuTop}px`;
        this.menuCoordinate.left = `${domRect.left - 110 + domRect.width / 2}px`;
        this.menuListState = true;
        this.toggleStructureMenuState = false;
      },

      closeTreeMenu() {
        this.menuListState = false;
      },

      showQualityFilesList() {
        this.showingImportTemplate = true;
        // this.$refs.qualityManageInput.click();
      },

      //新建工程结构
      addQualityManage(ref) {
        //关闭菜单时重置currentTreeNodeData
        this.currentTreeNodeData.node = {};
        this.currentTreeNodeData.data = {};
        let domRect = this.$refs[ref].getBoundingClientRect();
        this.qualityManageDialogData.title = '新建工程结构';
        this.qualityManageDialogData.type = 'add';
        this.qualityManageDialogData.coordinate.top = `${domRect.top}px`;
        this.qualityManageDialogData.coordinate.left = `${domRect.left}px`;
        this.qualityManageDialogData.state = true;
      },

      vClickOutClose() {
        let url = window.location.href;
        this.$emit('onclose',url);
      },

      //切换 新建工程结构 菜单状态
      toggleStructureMenu() {
        this.toggleStructureMenuState = !this.toggleStructureMenuState;
        this.menuListState = false;
      },

      parentClickEvent() {
        this.toggleStructureMenuState = false;//关闭顶部工程结构菜单
        this.closeTreeMenu();
      }
    },

    computed: {
      // menuListDisplay() {
      //   let storage = [];
      //   storage = this.menuListData.filter(item=>{
      //     return item.display
      //   });
      //
      //   return storage;
      // }
    },

    directives: {
      'tree-drag': {
        inserted(el) {
          let parent = document.getElementsByClassName('structural-tree')[0];
          let cover = document.getElementsByClassName('drag-cover')[0];
          el.onmousedown = function (e) {
            let startX = e.clientX;
            let boxWidth = parent.offsetWidth;
            cover.style.display = 'block';

            document.onmousemove = function (e) {
              let moveX = e.clientX;
              let moverWidth = boxWidth + (moveX - startX);
              if (moverWidth <= 400) {
                moverWidth = 400;
              }
              parent.style.width = moverWidth + "px"
            };

            document.onmouseup = function (evt) {
              document.onmousemove = null;
              document.onmouseup = null;
              cover.style.display = 'none';
            }
          }
        }
      }
    },
  }
</script>

<style>
  .structural-tree .tree-container .el-tree .el-tree-node__content {
    height: 40px;
  }

  .structural-tree .tree-container .el-tree .el-tree-node__content:hover,
  .structural-tree .tree-container .el-tree--highlight-current .el-tree-node.is-current>.el-tree-node__content {
    background: rgba(24, 144, 255, 0.1);
    color: #1890FF;
  }

  .structural-tree .tree-container .el-tree .el-tree-node__content .tree-node .icon-menu:hover {
    background-color: rgba(24, 144, 255, 0.1);
  }

  .structural-tree .tree-container .el-tree .el-tree-node__content:hover .tree-node .icon.icon-interface-list {
    display: inline-block;
  }
</style>

<style scoped>
  .quality-manage-set {
    position: fixed;
    top: 0;
    right: 0;
    left: 0;
    bottom: 0;
    z-index: 1;
  }

  .quality-manage-set > .top-bar {
    position: relative;
    z-index: 12;
    width: 100%;
    height: 64px;
    padding: 0 24px;
    box-sizing: border-box;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: #FFFFFF;
    box-shadow: 0 1px 3px 0 rgba(0, 21, 41, 0.12);
  }

  .quality-manage-set > .top-bar > span {
    font-weight: 500;
  }

  .quality-manage-set > .top-bar > span.icon {
    width: 28px;
    height: 28px;
    line-height: 28px;
    cursor: pointer;
  }

  .quality-manage-set > .top-bar span.title {
    text-align: left;
    flex-grow: 1;
    padding: 0 10px;
    font-size: 16px;
  }

  span.button {
    cursor: pointer;
    height: 40px;
    padding: 0 17px;
    line-height: 40px;
    border-radius: 4px;
  }

  span.correlation-model {
    color: #FFFFFF;
    background-color: #1890FF;
  }

  span.button:hover {
    opacity: 0.8;
  }

  span.not-allowed {
    cursor: not-allowed !important;
    background-color: rgba(0, 0, 0, 0.25) !important;
  }

  /******************bottom******************/

  .quality-manage-set .bottom-container {
    position: relative;
    height: calc(100% - 64px);
    background-color: #FFFFFF;
  }

  .quality-manage-set .bottom-container .structural-tree {
    width: 400px;
    height: 100%;
    box-shadow: 0 1px 3px 0 rgba(0, 21, 41, 0.2);
    background-color: #FFFFFF;
    border-radius: 2px;
    padding: 10px 0;
    box-sizing: border-box;
    position: absolute;
    left: 0;
    top: 0;
    z-index: 10;
  }

  .quality-manage-set .bottom-container .structural-tree > .css-prel {
    height: 100%;
  }

  .structural-tree .drag {
    position: absolute;
    top: 0;
    right: 0;
    z-index: 1;
    width: 4px;
    height: 100%;
    cursor: ew-resize;
  }

  .bottom-container .structural-tree .add-organization-structure {
    position: relative;
    height: 40px;
    line-height: 40px;
    border-radius: 4px;
    padding: 0 24px;
    background-color: rgba(0, 0, 0, 0.04);
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .bottom-container .structural-tree .add-organization-structure > span {
    color: rgba(0, 0, 0, 0.64);
    font-weight: 400;
  }

  .bottom-container .structural-tree .add-organization-structure > span.title {
    text-align: left;
    flex-grow: 1;
    padding: 0 10px;
  }

  .bottom-container .structural-tree .add-organization-structure > span.add:hover {
    color: #1890FF;
  }

  .structural-tree .add-organization-structure .structure-menu-list {
    width: 160px;
    position: absolute;
    right: -160px;
    top: 0;
    background-color: #FFFFFF;
    box-shadow: 0 6px 14px 0 rgba(26, 26, 26, 0.1);
    border-radius: 4px;
    /*overflow: hidden;*/
    padding: 4px 0;
  }

  .structural-tree .add-organization-structure .structure-menu-list li {
    height: 40px;
    cursor: pointer;
    text-align: left;
    padding-left: 15px;
  }

  .structural-tree .add-organization-structure .structure-menu-list li:hover {
    background-color: rgba(0, 0, 0, 0.04);
  }

  .structural-tree .add-organization-structure .structure-menu-list li .icon {
    vertical-align: text-bottom;
    margin-right: 5px;
  }

  /****************tree***************/
  .structural-tree .tree-container {
    padding-left: 35px;
    box-sizing: border-box;
    overflow: auto;
    height: calc(100% - 40px);
  }

  .structural-tree .tree-container .tree-node {
    width: calc(100% - 54px);
    height: 40px;
    flex-grow: 1;
    padding-right: 8px;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .structural-tree .tree-container .tree-node .node-name {
    text-overflow: ellipsis;
    text-align: left;
    width: calc(100% - 72px);
    overflow: hidden;
  }

  .structural-tree .tree-container .tree-node .icon-menu {
    margin: 0 3px;
    border-radius: 4px;
    padding: 5px;
    cursor: pointer;
  }

  /****************model-window***************/
  .quality-manage-set .bottom-container .model-window {
    position: relative;
    width: calc(100% - 400px);
    height: 100%;
    float: right;
    background-color: #F0F2F5;
    background-repeat: no-repeat;
    background-position: center 40%;
  }

  .quality-manage-set .bottom-container .model-window .model-iframe-window {
    width: 100%;
    height: 100%;
    position: relative;
    z-index: 1;
  }

  .quality-manage-set .bottom-container .model-window span.button {
    position: absolute;
    top: 50%;
    left: 50%;
    margin-left: -59px;
    margin-top: 45px;
  }

  /****************tree-menu-list***************/
  .quality-manage-set .tree-menu-list {
    top: 80px;
    left: 600px;
    position: fixed;
    z-index: 10;
    width: 220px;
    background-color: rgba(255, 255, 255, 1);
    box-shadow: 0 6px 14px 0 rgba(26, 26, 26, 0.06);
    border-radius: 4px;
    overflow: hidden;
  }

  .quality-manage-set .tree-menu-list > .top-bar {
    width: 100%;
    height: 44px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px;
    box-sizing: border-box;
    border-bottom: 1px solid rgba(0, 0, 0, 0.09);
  }

  .quality-manage-set .tree-menu-list > .top-bar .icon {
    width: 20px;
    color: #BFBFBF;
    font-weight: bold;
    cursor: pointer;
  }

  .quality-manage-set .tree-menu-list > .top-bar .icon:hover {
    opacity: 0.8;
  }

  .quality-manage-set .tree-menu-list > .top-bar .title {
    flex-grow: 1;
    font-size: 16px;
    font-weight: 500;
    text-align: center;
    color: #2c3e50 !important;
  }

  .quality-manage-set .tree-menu-list > .container {
    padding: 4px 0;
  }

  .quality-manage-set .tree-menu-list > .container li {
    height: 40px;
    line-height: 40px;
    font-weight: 400;
    padding: 0 16px;
    text-align: left;
    cursor: pointer;
    color: rgba(0, 0, 0, 0.85);
  }

  .quality-manage-set .tree-menu-list > .container li:hover {
    background-color: rgba(0, 0, 0, 0.04);
  }

  .quality-manage-set .tree-menu-list > .container li .icon {
    vertical-align: text-bottom;
    color: #8C8C8C;
    margin-right: 8px;
  }

  .quality-manage-set .bottom-container .drag-cover {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 5;
    display: none;
  }

</style>
