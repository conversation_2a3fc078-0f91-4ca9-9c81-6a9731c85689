<template>
  <div class="quality-details-container">
    <div class="details-dialog">
      <div class="dialog-top-title">
        <span class="title">质量详情</span>
        <div class="opation-menu">
          <span @click="closeDtailsDialog" class="icon icon-suggested-close"></span>
        </div>
      </div>

      <!--下方详情-->
      <div class="dialog-bottom-content" v-loading="loading">
        <div class="content-left item-content">
          <div class="item-list">
            <span class="title">检查类别</span>
            <span class="text-content">{{ detailsInfo.BasicInfo.aedt_name || '—' }}</span>
          </div>

          <div class="item-list">
            <span class="title">限期整改完成时间</span>
            <span class="text-content">{{ detailsInfo.BasicInfo.RectificateDate | setExamineDate }}</span>
          </div>

          <div class="item-list">
            <span class="title">严重等级</span>
            <span class="color-text-content" :class="setSeverityLevelClass(detailsInfo.BasicInfo.aede_severitylevel)">
              {{ detailsInfo.BasicInfo.aede_severitylevel || '—'}}
            </span>
          </div>

          <div class="item-list">
            <span class="title">状态</span>
            <span class="color-text-content" :class="setStatusClass(detailsInfo.BasicInfo.Status)">
              {{ detailsInfo.BasicInfo.Status || '—'}}
            </span>
          </div>

          <div class="item-list">
            <span class="title">关联图片</span>
            <div class="div-content">
              <span v-if="detailsInfo.Attachments.length == 0">—</span>
              <span class="img-thumbnail"
                    @click="showThumbnailCarousel(detailsInfo.Attachments,index)"
                    v-for="(item,index) in detailsInfo.Attachments"
                    :key="item.ExamineAttachmentID">
                <img v-if="item.AttachmentUrl != ''" :src="$configjson.webserverurl+item.AttachmentUrl" :alt="item.AttachmentName">
                <img v-else :src="noImage" alt="暂无图片">
              </span>
            </div>
          </div>

        </div>
        <div class="content-right item-content">
          <div class="rectification-content">
            <div class="title">整改人员</div>
            <div class="personnel">
              <small v-if="detailsInfo.BasicInfo.PrincipalObjs.length == 0">暂无整改人员</small>
              <div class="item" v-for="(item,index) in detailsInfo.BasicInfo.PrincipalObjs">
                <span>{{ item.RealName | setAbbreviation }}</span>{{ item.RealName }}
              </div>
            </div>
          </div>

          <div class="rectification-content">
            <div class="title">验收人员</div>
            <div class="personnel">
              <small v-if="detailsInfo.BasicInfo.aede_checkeruserobjs.length == 0">暂无验收人员</small>
              <div class="item" v-for="(item,index) in detailsInfo.BasicInfo.aede_checkeruserobjs">
                <span>{{ item.RealName | setAbbreviation }}</span>{{ item.RealName }}
              </div>
            </div>
          </div>

          <div class="rectification-content">
            <div class="title">检查方</div>
            <div class="personnel">
              <small v-if="detailsInfo.BasicInfo.Examiner == ''">暂无验收人员</small>
              <div class="item">
                <span>{{ detailsInfo.BasicInfo.Examiner | setAbbreviation }}</span>
                {{ detailsInfo.BasicInfo.Examiner }}
              </div>
            </div>
          </div>

          <div class="rectification-content">
            <div class="title">整改记录</div>
            <div v-if="detailsInfo.Rectifications.length==0"><small>暂无整改记录</small></div>
            <div v-for="(item,index) in detailsInfo.Rectifications" :key="item.RectificationID">
              <div class="personnel" style="justify-content: space-between;">
                <div class="item">
                  <span>{{ item.RectificationOperator | setAbbreviation }}</span>
                  {{ item.RectificationOperator }}
                </div>
                <div class="results" :class="setStatusClass(item.RectificationCheckResult)">{{ item.RectificationCheckResult }}</div>
              </div>
              <div class="results-desc">
                <p v-if="item.RectificationRemark!=''">{{ item.RectificationRemark }}</p>
                <p v-else><small>暂无整改说明</small></p>
                <!--<div class="files"><span class="icon icon-interface-image"></span>图片则显示图片的缩略图</div>-->
                <!--<div class="files"><span class="icon mulcolor-interface-pdf"></span>文件则是文件的图标</div>-->
                <!--<div class="files"><span class="icon icon-interface-video"></span>手机上的录音文件</div>-->
                <div class="time">{{ item.RectificationCheckDate | setExamineDate }}</div>
              </div>
            </div>
          </div>

        </div>
      </div>
    </div>
    <div @click.stop="closeDtailsDialog" class="mask"></div>

    <div class="thumbnail-carousel" v-if="carouselState">
      <span class="icon icon-suggested-close_circle" @click.stop="carouselState=false"></span>
      <el-carousel trigger="click" height="500px" :autoplay="false" :initial-index="defaultThumbnailIndex">
        <el-carousel-item v-for="(item,index) in thumbnailCarouselData" :key="item.ExamineAttachmentID">
          <img v-if="item.AttachmentUrl==''" :src="noImage" alt="">
          <img v-else :src="$configjson.webserverurl+item.AttachmentUrl" alt="">
          <div class="img-describe">{{ item.AttachmentName ? item.AttachmentName : `图片${index+1}` }}</div>
        </el-carousel-item>
      </el-carousel>
    </div>
    <div v-if="carouselState" @click.stop="carouselState=false" class="carousel-mask"></div>
  </div>
</template>

<script>
  //问题详情
    export default {
      name: "ComQualityDetails",

      props: [
        'data'
      ],

      data() {
          return {
            loading: true,
            detailsInfo: {//详情信息
              Attachments: [],
              Rectifications: [],
              BasicInfo: {
                PrincipalObjs: [],
                aede_checkeruserobjs: [],
                Examiner: '',
              },
            },
            noImage: require('../../../src/assets/svgs_loadbyurl/interface-hebingmoxing.svg'),
            carouselState: false,//轮播图弹窗状态
            thumbnailCarouselData: [],//图片幻灯数据
            defaultThumbnailIndex: 0,//轮播图默认展示第几张图片
          }
      },

      created() {
      },

      mounted() {
        this.getQualityItem();
      },

      methods: {
        //展示图片轮播图
        showThumbnailCarousel(data,index) {
          this.defaultThumbnailIndex = index;
          this.thumbnailCarouselData = data;
          this.carouselState = true;
        },

        //关闭弹窗
        closeDtailsDialog() {
          this.$emit('onClose',false);
        },

        //获取详情
        getQualityItem() {
          this.$axios.get(`${this.$configjson.webserverurl}/api/Examine/Exam/GetItem?Token=${this.$staticmethod.Get('Token')}`,{
            params: {
              ExamineID: this.data.ExamineID
            }
          }).then(res=>{
            if (res.data.Ret == 1) {
              this.detailsInfo = res.data.Data;
            }
            console.log(this.detailsInfo);
          }).catch(res=>{
            console.error(res);
          });
          this.loading = false;
        }
      },

      computed: {
        //严重等级class
        setSeverityLevelClass() {
          return function (val) {
            return {
              'grade-color slight': val == '轻微',
              'grade-color normal': val == '一般',
              'grade-color serious': val == '严重',
            }
          }
        },

        //状态class
        setStatusClass() {
          return function (val) {
            return {
              'state-color pass': val == '整改完成',
              'state-color noPass': val == '逾期',
              'state-color needToDo': val == '待整改' || val == '整改',
            }
          }
        },
      },

      filters: {
        //时间过滤器
        setExamineDate(val) {
          let storage = '';
          if (val) {
            storage = val.replace(/T/,' ');
          } else {
            storage = '—';
          }
          return storage;
        }
      }
    }
</script>

<style scoped>
.quality-details-container {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  overflow: auto;
  z-index: 1001;
}

.quality-details-container .details-dialog {
  position: relative;
  width: 1200px;
  max-height: 700px;
  background:rgba(255,255,255,1);
  box-shadow:0 13px 24px -17px rgba(11,41,62,0.8);
  border-radius:4px;
  overflow: hidden;
  margin: 5vh auto 50px;
  z-index: 1;
}

.quality-details-container .details-dialog .dialog-top-title {
  width: 100%;
  height: 60px;
  padding: 0 24px;
  border-bottom: 1px solid rgba(0,0,0,0.09);
  box-sizing: border-box;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.details-dialog .dialog-top-title .title {
  font-size: 16px;
  font-weight: 500;
  color: rgba(0,0,0,1);
}

.details-dialog .dialog-top-title .opation-menu .icon {
  cursor: pointer;
}

/*****bottom content******/
.quality-details-container .details-dialog .dialog-bottom-content {
  display: flex;
  min-height: 100px;
  overflow: hidden;
  max-height: 640px;
}

.details-dialog .dialog-bottom-content .item-content {
  overflow: auto;
}

.details-dialog .dialog-bottom-content .content-left {
  width: 760px;
  text-align: left;
  padding: 24px 40px;
  box-sizing: border-box;
  border-right: 1px solid rgba(0,0,0,0.09);
}

.dialog-bottom-content .content-left .item-list {
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 30px;
}

.dialog-bottom-content .content-left .item-list .title {
  width: 130px;
  display: inline-block;
  color: rgba(0,0,0,0.45);
}

.dialog-bottom-content .content-left .item-list .text-content {
  color: rgba(0,0,0,1);
}

.dialog-bottom-content .content-left .item-list .div-content {
  margin-top: 20px;
}

.dialog-bottom-content .content-left .item-list .img-thumbnail {
  width: 200px;
  overflow: hidden;
  cursor: pointer;
  display: inline-block;
  box-shadow: 0 0 5px #9c9c9c;
}

.dialog-bottom-content .content-left .item-list .img-thumbnail:hover {
  box-shadow: 0 0 5px #5d5d5d;
}

.dialog-bottom-content .content-left .item-list .img-thumbnail:nth-child(3n+2) {
  margin: 0 20px;
}

.dialog-bottom-content .content-left .item-list .img-thumbnail img {
  width: 100%;
  vertical-align: middle;
}

  /*等级 颜色*/
.grade-color,
.state-color {
  width: 76px;
  height: 22px;
  overflow: hidden;
  border-radius: 4px;
  border-width: 1px;
  border-style: solid;
  display: inline-block;
  text-align: center;
  line-height: 24px;
  font-size: 12px;
}

.grade-color.slight {
  color: #9B9B9B;
  border-color: #9B9B9B;
  box-shadow: inset 0 0 4px #9B9B9B;
}

.grade-color.normal {
  color: #1890FF;
  border-color: #1890FF;
  box-shadow: inset 0 0 4px #1890FF;
}

.grade-color.serious {
  color: #F5222D;
  border-color: #F5222D;
  box-shadow: inset 0 0 4px #F5222D;
}

/*状态 颜色*/
.state-color.pass {
  color: #1DA48C;
  border-color: #1DA48C;
  box-shadow: inset 0 0 4px #1DA48C
}

.state-color.noPass {
  color: #F5222D;
  border-color: #F5222D;
  box-shadow: inset 0 0 4px #F5222D
}

.state-color.needToDo {
  color: #FAAD14;
  border-color: #FAAD14;
  box-shadow: inset 0 0 4px #FAAD14
}

.details-dialog .dialog-bottom-content .content-right {
  flex-grow: 1;
  text-align: left;
}

.details-dialog .dialog-bottom-content .rectification-content {
  width: 100%;
  padding: 24px;
  box-sizing: border-box;
  border-bottom: 1px solid rgba(0,0,0,0.09);
}

.dialog-bottom-content .rectification-content .title {
  font-size: 16px;
  font-weight: 500;
  color: rgba(0,0,0,1);
  margin-bottom: 24px;
}

.dialog-bottom-content .rectification-content .personnel {
  display: flex;
  flex-wrap: wrap;
}

.rectification-content .personnel .item {
  margin: 0 15px 15px 0;
  font-size: 12px;
  color: rgba(0,0,0,0.65);
  line-height: 20px;
}

.rectification-content .personnel .item span {
  width: 26px;
  height: 26px;
  text-align: center;
  font-size: 12px;
  padding: 3px 7px;
  background-color: #1C324B;
  color: #FFFFFF;
  border-radius: 4px;
  margin-right: 5px;
  display: inline-block;
  box-sizing: border-box;
}

.rectification-content .results-desc {
  padding: 15px 0 10px 33px;
}

.rectification-content .results-desc p {
  margin-bottom: 10px;
}

.rectification-content .results-desc .files {
  background:rgba(240,242,245,1);
  padding: 5px 0;
  margin-bottom: 10px;
}

.rectification-content .results-desc .files .icon {
  width: 20px;
  height: 20px;
  display: inline-block;
  vertical-align: middle;
}

.rectification-content .results-desc .time {
  font-size: 12px;
  font-weight: 400;
  color: rgba(0,0,0,0.45);
  text-align: right;
}

.quality-details-container .carousel-mask,
.quality-details-container .mask {
  position: fixed;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  opacity: .5;
  background: #000;
}

.quality-details-container .thumbnail-carousel {
  width: 1000px;
  height: 500px;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255,255,255,1);
  box-shadow: 0 0 5px 1px rgba(0, 0, 0, 0.5);
  border-radius: 4px;
  margin: 8vh auto 50px;
  z-index: 10;
}

.quality-details-container .thumbnail-carousel .icon.icon-suggested-close_circle {
  position: absolute;
  cursor: pointer;
  top: -15px;
  right: -15px;
  z-index: 11;
  color: #FFFFFF;
  font-size: 2rem;
}
.quality-details-container .thumbnail-carousel .icon.icon-suggested-close_circle:hover {
  opacity: 0.8;
}

.quality-details-container .thumbnail-carousel img {
  width: 100%;
  height: 100%;
}

.quality-details-container .thumbnail-carousel .img-describe {
  width: 100%;
  position: absolute;
  top: 0;
  height: 24px;
  color: #FFFFFF;
  line-height: 24px;
  background-color: rgba(0, 0, 0, 0.4);
}

.quality-details-container .carousel-mask {
z-index: 5;
}
</style>
