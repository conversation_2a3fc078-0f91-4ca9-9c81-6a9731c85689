<template>
  <div class="model-iframe-content"> 
    <iframe id="scene-iframe" width="100%" height="100%" class="content-frame" src="" frameborder="0"></iframe>
    <!-- <iframe id="aaa" src="static/modelPreview.html" frameborder="0"></iframe> -->
  </div>
</template>
<script>

export default {
  name: 'modelNewIframeLoading',
  props: { 
    featureID:{
      type: String,
      default: ''
    },
    VaultID: {
      type: String,
      default: ''
    },
    modelVersion:{
      type: [String,Number],
      default: ''
    }
  }, 
  data(){
    return{
      isLoad: 0,
      modelId: '',
      version: '',
    }
  },  
  created(){
    if(this.$route.params.vaultID && this.$route.params.vaultID.length > 0){
      this.VaultID = this.$route.params.vaultID;
      this.featureID = this.$route.params.featureID;
    }
  },
  mounted(){
    window.addEventListener('message', this.receiveMessageFromIndex, false)
    this.$store.commit({
      type:'setModelLoad',
      state: 'start'
    })
    this.version = this.modelVersion;
    this.modelId = this.featureID.split('|')
    const iframe = document.getElementById('scene-iframe')
    const ifrSrc = this.getHttpUrl();
    iframe.src = ifrSrc + `?projectId=${this.VaultID}&lang=cn&edit=false`;
  },
  methods: {
    receiveMessageFromIndex (event) {
      // console.log(event,'====event',event.data)
      if(event.data == 'featureLoad'){
        this.modelId.map((v, i) => {
          this.loadmodel2(v, i);
        })
      }
    },
    getHttpUrl(){
      return window.bim_config.newModelApi
    },
    // 加载模型
    async loadmodel2 (modelID, index) {
      const contentWindow = document.getElementById('scene-iframe').contentWindow
      contentWindow.renderSceneMainMenu({name: 'widgetSource', attr: 'isShow', value: false})
      contentWindow.renderSceneMainMenu({name: 'setup', attr: 'isShow', value: false})
      contentWindow.toggleSceneManageEditMode(false);
      window.scene = contentWindow.scene
      // 添加中午天空盒
      const sk1 = window.scene.addFeature('skybox')
      sk1.name = '中午'
      sk1.load()
      sk1.setTime('noon')
      // 场景编辑器数据同步 天空盒
      contentWindow.deepUpdateScene('skybox')
      const model3 = window.scene.addFeature('model',modelID)
      // 指定模型服务地址
      // window.model3.server = process.env.NODE_ENV === 'production' ? window.IP_CONFIG.MODEL_URL : 'http://localhost:8080/MODEL_URL'
      model3.server = window.bim_config.newModelHttpUrl // this.httpUrl//window.bim_config.newModelHttpUrl ;//"https://multiverse-server.vothing.com"; //'/newModelApi'
      // 指定vaultID
      model3.vaultID = this.VaultID
      // 基点的经纬度坐标
      model3.origin = [0, 0]
      // 基点的高程
      model3.altitude = 0
      // 基点的正北旋转角度
      model3.rotation = 0
      // 相对于基点的XYZ偏移
      model3.offset = [0, 0, 0]
      // 加载模型
      await model3.load().then(() => {
        // 加载模型主视图
        model3.activeView().then(() => {
          // 加载成功后，定位到当前模型
          // window.scene.fit2Feature(model3)
          // 场景编辑器数据同步 模型结构树
          // contentWindow.deepUpdateScene('model');

          const skybox = model3.config.skybox
          const sk1 = window.scene.addFeature('skybox')
          sk1.load()
          // 设置环境：morning-早晨、noon-中午、evening-傍晚、night-夜间
          if (skybox) {
            sk1.setTime(skybox)
          } else {
            // 默认设置为中午
            sk1.setTime('noon')
          }
          this.skybox = sk1
          // 场景编辑器数据同步 天空盒
          contentWindow.deepUpdateScene('skybox')
          // 设置默认视角
          const defaultViewpoint = model3.config.drefaultViewpoint
          // 加载成功后，定位到当前模型
          if (defaultViewpoint) {
            window.scene.resetCamera(defaultViewpoint)
            // window.scene.setCamera(defaultViewpoint)
          } else {
            window.scene.fit2Feature(model3)
          }

          this.$store.commit({
            type:'setModelLoad',
            state: 'end'
          });
          this.$emit('load')
        })
        // model3.loadSystem()
        // 场景编辑器数据同步 模型结构树
        contentWindow.deepUpdateScene('model')
      })
    },
  },
  destroyed(){
    window.removeEventListener('message', this.receiveMessageFromIndex)
  },
}
</script>
<style lang="scss">
.model-iframe-content{
  width: 100%;
  height: 100%;
}
.content-frame{
  width: 100%;
  height: 100%;
  // height: calc(100% - 56px);
}

</style>