<template>
  <div class="css-prel">
    <div :class="[{'quality-list-response' : showModelView},'quality-list']">
      <div class="top-menu">
        <div class="menu-item menu-left">
          <!--暂时隐藏-->
            <!--<el-dropdown trigger="click" class="css-mr16 css-hover-btn">-->
              <!--<span class="el-dropdown-link">-->
                <!--检查任务<i class="el-icon-arrow-down el-icon&#45;&#45;right"></i>-->
              <!--</span>-->
              <!--<el-dropdown-menu slot="dropdown">-->
                <!--<el-dropdown-item command="1" icon="el-icon-plus">选项1</el-dropdown-item>-->
                <!--<el-dropdown-item command="2" icon="el-icon-circle-plus">选项2</el-dropdown-item>-->
                <!--<el-dropdown-item command="3" icon="el-icon-circle-plus-outline">选项3</el-dropdown-item>-->
                <!--<el-dropdown-item command="4" icon="el-icon-check">选项4</el-dropdown-item>-->
                <!--<el-dropdown-item command="5" icon="el-icon-circle-check">选项5</el-dropdown-item>-->
              <!--</el-dropdown-menu>-->
            <!--</el-dropdown>-->

            <div class="originating-task-btn css-hover-btn" @click="openTaskDialog">
              <i class="icon icon-interface-addnew"></i>发起检查任务
            </div>
            <div class="originating-task-btn css-hover-btn" style="margin-left:20px;" @click="openNewModel">
              在模型中查看
            </div>
            <div class="q-set-icon" @click.stop="setLabelFun"></div>
        </div>

        <ul class="menu-item menu-right">
          <li>
            <el-dropdown trigger="click" class="css-hover-btn" @command="handleCreateTimeSort">
              <span class="el-dropdown-link">
                按创建时间<i class="el-icon-arrow-down el-icon--right"></i>
              </span>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item command="2">按创建时间升序</el-dropdown-item>
                <el-dropdown-item command="1">按创建时间降序</el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </li>

          <!--暂时隐藏-->
          <!--<li>-->
            <!--<el-dropdown trigger="click" class="css-hover-btn">-->
              <!--<span class="el-dropdown-link">-->
                <!--筛选<i class="el-icon-arrow-down el-icon&#45;&#45;right"></i>-->
              <!--</span>-->
              <!--<el-dropdown-menu slot="dropdown">-->
                <!--<el-dropdown-item command="1" icon="el-icon-plus">筛选1</el-dropdown-item>-->
                <!--<el-dropdown-item command="2" icon="el-icon-circle-plus">筛选2</el-dropdown-item>-->
              <!--</el-dropdown-menu>-->
            <!--</el-dropdown>-->
          <!--</li>-->

          <!--暂时隐藏-->
          <!--<li :class="{'function-btn-prohibition':!authdata.lr_edit}" @click="!authdata.lr_edit ? '' : showingImportTemplate=true">-->
            <!--<span class="icon icon-interface-download-fill"></span>导入-->
          <!--</li>-->


          <!--<li><span class="icon"></span>&nbsp;</li>占位用，后期删除-->
          <!--<li><span class="icon icon-interface-setting"></span>整改</li>-->
          <!--<li><span class="icon icon-interface-safety"></span>验收</li>-->
          <!--<li><span class="icon icon-interface-filter"></span>筛选</li>-->

          <!--<li>-->
            <!--<el-select v-model="modelSelectId" placeholder="">-->
              <!--<el-option-->
                <!--v-for="item in modelSelectList"-->
                <!--:key="item.ModelID + new Date().getTime()"-->
                <!--:label="item.ModelName"-->
                <!--:value="item.ModelID">-->
                <!--<el-tooltip effect="dark" :content="item.ModelName" :open-delay=1000 :enterable=false>-->
                  <!--<span class="model-name-hide">{{ item.ModelName }}</span>-->
                <!--</el-tooltip>-->
              <!--</el-option>-->
            <!--</el-select>-->
          <!--</li>-->
        </ul>
      </div>

      <div class="list-container">
        <div class="table-top-bar" v-if="multipleSelection.length > 0">
          <ul class="menu-item menu-left" style="height: 100%">

            <li>
              <el-checkbox
                :indeterminate="tableTopBarState.isIndeterminate"
                v-model="tableTopBarState.checkAll"
                @change="handleTableTopBarCheckAllChange">
              </el-checkbox>
            </li>

            <li @click="exportSceneAllData" v-if="tableTopBarState.checkAll">
              <span class="icon icon-interface-download"></span>导出
            </li>

            <li :class="{'function-btn-prohibition':!authdata.lr_del}" @click="deleteSelectQualityItems">
              <span class="icon icon-interface-delete"></span>删除
            </li>

            <li :class="{'function-btn-prohibition':!authdata.lr_edit}" @click="closeSelectQualityItems">
              <span class="icon el-icon-circle-close"></span>关闭
            </li>
            <li><span class="desc">已选择{{ multipleSelection.length }}项</span></li>
          </ul>
        </div>

        <el-table
          :header-cell-style="listTableHeaderStyle"
          ref="multipleTable"
          border
          :data="filterTableData"
          tooltip-effect="dark"
          style="width: 100%"
          height="100%"
          @selection-change="handleSelectionChange">
          <el-table-column
            fixed
            type="index"
            label="序号"
            align="center"
            width="70">
          </el-table-column>
          <el-table-column
            align="center"
            type="selection"
            width="55">
          </el-table-column>

          <el-table-column
            align="center"
            min-width="150"
            label="标题"
            show-overflow-tooltip>
            <template slot-scope="scope">
              <div @click.stop="showQualityDetails(scope.row)" class="css-w100 css-tal longDesc css-cp">
                {{ scope.row.ExamineRemark || '--'}}
              </div>
            </template>
          </el-table-column>

          <el-table-column
            v-if="!showModelView"
            width="95"
            align="center"
            prop="ExamineResult"
            :filters="filterOriginatingTaskTypeStatusList"
            :filter-multiple="false"
            :filter-method="filterOriginatingTaskStatus"
            label="状态">
            <template slot-scope="scope">

              <div v-html="$options.filters.setExamineResult(scope.row)"></div>
            </template>
          </el-table-column>

          <el-table-column
            v-if="!showModelView"
            align="center"
            :filters="filterOriginatingTaskSeverity"
            :filter-multiple="false"
            prop="aedt_name"
            :filter-method="filterOriginatingTaskSeverityHandle"
            width="95"
            label="严重等级">
            <template slot-scope="scope">
              <span :class="setSeverityLevelClass(scope.row.aede_severitylevel)">
                {{ scope.row.aede_severitylevel || '-'}}
              </span>
            </template>
          </el-table-column>

          <el-table-column
            v-if="!showModelView"
            align="center"
            width="120"
            :filters="filterOriginatingTaskTypeList"
            :filter-multiple="false"
            prop="aedt_name"
            :filter-method="filterOriginatingTaskType"
            label="类别">
            <template slot-scope="scope">
              {{ scope.row.aedt_name || '-' }}
            </template>
          </el-table-column>

          <el-table-column
            v-if="!showModelView"
            width="120"
            align="center"
            prop="bu_checker_RealName"
            label="检查人">
          </el-table-column>

          <el-table-column
            width="170"
            align="center"
            prop=""
            label="开始时间">
            <template slot-scope="scope">
              {{ scope.row.ExamineDate | setExamineDate}}
            </template>
          </el-table-column>

          <el-table-column
            align="center"
            width="170"
            label="截止时间">
            <template slot-scope="scope">
              {{ scope.row.RectificateDate | setExamineDate}}
            </template>
          </el-table-column>

          <el-table-column
            v-if="!showModelView"
            width="170"
            align="center"
            label="检查时间">
            <template slot-scope="scope">
              {{ scope.row.FirstCheckDate == null ? '-' : scope.row.FirstCheckDate | setExamineDate}}
            </template>
          </el-table-column>

          <el-table-column
            width="170"
            align="center"
            label="完成时间">
            <template slot-scope="scope">
             {{ scope.row.SecondCheckDate == null ? '-' : scope.row.SecondCheckDate | setExamineDate}}
            </template>
          </el-table-column>

        </el-table>
      </div>
    </div>

    <div class="model-window" v-if="showModelView">
      <bim-viewer
        ref="ref_bimviewer"
        :BIM_Session="BIM_Session"
        :ProjectID="ProjectId"
        :ModelID="SelModelObj.ID"
        :modelName="SelModelObj.Name"
        :SelModel="SelModelObj"
        @notmtr_onload="modelWindowOnload"
        @ItemopenModelInfo="openModelInfo"
        @setModelInfoIndex="setTranIndex"
        @CloseModelView="onmodelview_close">
      </bim-viewer>

      <ModelInfo class="mi"
       v-if="showModelInfo"
       :lastOpenVersionNo="lastOpenVersionNo"
       :ProjectID='ProjectId'
       :ModelID="SelModelObj.ID"
       :Phase="(SelModelObj.Phase || '').toString()"
       :Creator="SelModelObj.Creator"
       :CreateTime="SelModelObj.CreateTime"
       :IsMerge="SelModelObj.IsMerge"
       :tranIndex="tranIndex"
       :SelModel="SelModelObj"
       @openVersion="openModel_Version"
       @CloseModelInfo='showModelInfo=false;
      IsDialogCovery=false;'>
      </ModelInfo>
    </div>

    <ComQualityDetails
      v-if="qualityDetailsState"
      :currentCheckedData="currentExamineData"
      :projectMemberData="projectMemberData"
      :constructionProblemsList="originatingTaskType"
      @onClose="closeDtailsDialog"></ComQualityDetails>

    <!-- 新增分类对话框 -->
    <zdialog-function
      v-if="originatingTaskDialogState"
      init_title="发起检查任务"
      :init_zindex="1000"
      :init_innerWidth="500"
      init_closebtniconfontclass="icon-suggested-close"
      @onclose="closeTaskDialog">

      <div slot="mainslot" v-loading="taskDialogLoad">
        <div class="_css-line css-common-line originating-task-container">
          <div class="task-title">
            <div class="task-type-select">
              <el-dropdown trigger="click" @command="handleTaskTypeCommand">
                <el-button type="primary">
                  <i class="el-icon-arrow-down el-icon--left"></i>{{ setOriginatingTaskTypeSelectData }}
                </el-button>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item
                    v-for="(item,index) in originatingTaskType"
                    :key="item.AedtGuid + index"
                    :command="item.AedtGuid">{{ item.AedtName }}</el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
            </div>
            <textarea
              @mousedown="_stopPropagation"
              @blur="validateOriginatingTaskDialogForm"
              v-model="originatingTaskDialogForm.ExamineRemark"
              class="task-title-area"
              placeholder="输入标题创建检查任务"
              name="task-title-area"
              rows="6"
              style="resize:none"></textarea>
          </div>

          <div class="originating-task-form">
            <el-form ref="originatingTaskDialogForm" label-position="top" :model="originatingTaskDialogForm">
              <el-form-item label="起止日期">
                <el-col :span="11">
                  <el-date-picker
                    @change="validateOriginatingTaskDialogForm"
                    clear-icon="icon-suggested-close_circle-outline"
                    type="date"
                    placeholder="请选择开始日期"
                    :picker-options="taskStarPickerOptions"
                    v-model="originatingTaskDialogForm.ExamineDate"
                    style="width: 100%;"></el-date-picker>
                </el-col>
                <el-col class="line" :span="2">&nbsp;</el-col>
                <el-col :span="11">
                  <el-date-picker
                    @change="validateOriginatingTaskDialogForm"
                    clear-icon="icon-suggested-close_circle-outline"
                    type="date"
                    placeholder="请选择截止日期"
                    :picker-options="taskEndPickerOptions"
                    v-model="originatingTaskDialogForm.RectificateDate"
                    style="width: 100%;"></el-date-picker>
                </el-col>
              </el-form-item>
              <el-form-item label="检查人">
                <el-select
                  @change="validateOriginatingTaskDialogForm"
                  v-model="originatingTaskDialogForm.CheckerUserId"
                  filterable
                  placeholder="请选择检查人">
                  <el-option
                    v-for="(item,index) in projectMemberData"
                    :key="item.UserId + index"
                    :label="item.RealName"
                    :value="item.UserId">
                    <div class="inspectors-select-list">
                      <div class="name-icon"><span>{{ item.RealName | setAbbreviation}}</span></div>
                      <span class="inspectors-name">{{ item.RealName }}</span>
                      <span class="inspectors-identity">{{ item.RoleName }}</span>
                    </div>
                  </el-option>
                </el-select>
              </el-form-item>

              <el-form-item>
                <div>
                  发起整改&nbsp;&nbsp;&nbsp;&nbsp;<el-checkbox v-model="r_supervisor_checked" @change="supervisor_checked"></el-checkbox>
                </div>
                <!-- 折叠组件 -->
                <collapse>
                    <div class="supervisor_open" v-show="r_supervisor_checked">
                        <el-form-item label="检查内容" class="supervisor_open_con">
                          <textarea
                          @mousedown="_stopPropagation"
                          @change="validateOriginatingTaskDialogForm"
                          v-model="originatingTaskDialogForm_other.RectificationRemark"
                          placeholder="请输入检查内容"
                          rows="4"
                          style="resize:none;width:100%;text-indent: 15px;border:none;outline:none;border-bottom: 1px solid #ccc">
                          </textarea>
                        </el-form-item>
                        <el-form-item label="严重等级" class="supervisor_open_con">
                            <el-radio-group v-model="originatingTaskDialogForm_other.aede_severitylevel" @change="validateOriginatingTaskDialogForm" size="small">
                              <el-radio-button label="一般"></el-radio-button>
                              <el-radio-button label="严重"></el-radio-button>
                              <el-radio-button label="非常严重"></el-radio-button>
                            </el-radio-group>
                        </el-form-item>
                        <!-- <el-form-item label="状态" class="supervisor_open_con">
                            <el-radio-group v-model="originatingTaskDialogForm_other.IsPassed" @change="validateOriginatingTaskDialogForm" size="small">
                              <el-radio-button label="合格"></el-radio-button>
                              <el-radio-button label="不合格"></el-radio-button>
                            </el-radio-group>
                        </el-form-item> -->
                        <el-form-item label="人员" class="supervisor_open_con">
                            <el-select
                              @change="validateOriginatingTaskDialogForm"
                              v-model="originatingTaskDialogForm_other.RelationMemberID"
                              filterable
                              placeholder="请选择整改人"
                              style="marginBottom:20px">
                              <el-option
                                v-for="(item,index) in projectMemberData"
                                :key="item.UserId + index"
                                :label="item.RealName"
                                :value="item.UserId">
                                <div class="inspectors-select-list">
                                  <div class="name-icon"><span>{{ item.RealName | setAbbreviation}}</span></div>
                                  <span class="inspectors-name">{{ item.RealName }}</span>
                                  <span class="inspectors-identity">{{ item.br_FullName }}</span>
                                </div>
                              </el-option>
                            </el-select>
                            <el-select
                              @change="validateOriginatingTaskDialogForm"
                              v-model="originatingTaskDialogForm_other.PrincipalID"
                              filterable
                              placeholder="请选择验收人"
                              style="marginBottom:20px">
                              <el-option
                                v-for="(item,index) in projectMemberData"
                                :key="item.UserId + index"
                                :label="item.RealName"
                                :value="item.UserId">
                                <div class="inspectors-select-list">
                                  <div class="name-icon"><span>{{ item.RealName | setAbbreviation}}</span></div>
                                  <span class="inspectors-name">{{ item.RealName }}</span>
                                  <span class="inspectors-identity">{{ item.RoleName }}</span>
                                </div>
                              </el-option>
                            </el-select>
                        </el-form-item>
                    </div>
                </collapse>
              </el-form-item>
            </el-form>

            <div class="margin-bottom-20" style="color: #606266">关联</div>
            <div class="connect-operation">
              <div @click="evt_materialbrowserclose" class="connect-item css-hover-btn">
                关联构件<i class="icon icon-interface-associated-component"></i>
              </div>
              <div @click="evt_taskbrowserclose()" class="connect-item css-hover-btn">
                关联任务<i class="icon icon-interface-problem-status"></i>
              </div>
            </div>

            <template v-if="m_formtasksselectedObj && m_formtasksselectedObj.length">
              <div class="connect-operation-list">
                <div class="title">已关联任务</div>
                <div class="list-item" v-for="item in m_formtasksselectedObj" :key="item.modelid">
                  <span class="icon icon-interface-problem-status"></span>
                  <span class="desc">{{ item.NAME_ }}</span>
                  <span @click="func_removetask($event, item.UID_)" class="icon-close icon-suggested-close_circle"></span>
                </div>
              </div>
            </template>

            <template v-if="m_formmaterialselectedObj && m_formmaterialselectedObj.length">
              <div class="connect-operation-list">
                <div class="title">已关联构件</div>
                <div class="list-item" v-for="item in m_formmaterialselectedObj" :key="item.bm_guid">
                  <span class="icon icon-interface-associated-component"></span>
                  <span class="desc">{{ item.bm_materialname }}</span>
                  <span @click="func_removematerial($event, item.bm_guid)" class="icon-close icon-suggested-close_circle"></span>
                </div>
              </div>
            </template>

          </div>

        </div>
      </div>

      <div slot="buttonslot" class="css-common-zdialogbtnctn originating-task-submit">
        <div
          @click="originatingTaskFormSubmit('next')"
          :class="[{'available':originatingTaskFormSubmitState},'task-submit-next-btn btn css-mr16 css-hover-btn']">提交并发起下一个</div>
        <div
          @click="originatingTaskFormSubmit('close')"
          :class="[{'available':originatingTaskFormSubmitState},'task-submit-btn btn  css-hover-btn']">提交</div>
      </div>
    </zdialog-function><!-- //新增分类对话框 -->

    <!-- 进度任务浏览器 -->
    <CompsTaskBrowser
      v-if="status_taskbrowser"
      :init_zIndex="1002"
      :init_width="'800px'"
      :init_height="'640px'"
      :init_organizeId="getOrganizeId()"
      init_title="请选择关联任务"
      @onclose="evt_taskbrowserclose"
      @onok="evt_taskbrowseronok"
    ></CompsTaskBrowser>
    <!-- //进度任务浏览器 -->

    <!-- 构件浏览器 -->
    <CompsMaterialBrowser
      v-if="status_materialbrowser"
      :init_zIndex="1002"
      :init_width="'800px'"
      :init_height="'640px'"
      :init_bimcomposerId="getBIMComposerId()"
      :init_organizeId="getOrganizeId()"
      init_title="请选择关联构件"
      @onclose="evt_materialbrowserclose"
      @onok="evt_materialbrowseronok"
    ></CompsMaterialBrowser>
    <!-- //构件浏览器 -->
    <ModelViewer :tableData=$refs.multipleTable.tableData
        @close=closeModel
        :params=ajaxParams
        v-if="showModel"></ModelViewer>
    <transition v-show="drawerEditShow">
      <div class="labelSetDialog" ref="labelSetDialog">
        <CompsQualitySetLabel
          :AedtType="typelist == '1' ? 'quality' : 'security'"
          :tagOptions="originatingTaskType"
          @close="setLabelFun"
        ></CompsQualitySetLabel>
      </div>

    </transition>
  </div>
</template>

<script>
  import ModelViewer from '@/components/CompsQuality/CompsQualityModel'
  import BimViewer from '@/components/Home/ProjectBoot/Model/BIMViewer' //打开模型
  import ModelInfo from "@/components/Home/ProjectBoot/Model/ModelInfo";
  //import CompsNewQualityProblem from "@/components/CompsQuality/CompsNewQualityProblem";
  import ComQualityDetails from "@/components/CompsQuality/ComQualityDetails";//质量详情
  // import CompsStepTip2 from "@/components/CompsCommon/CompsStepTip2";//导入问题

  import CompsMaterialBrowser from '@/components/CompsCommon/CompsMaterialBrowser'
  import CompsTaskBrowser from '@/components/CompsCommon/CompsTaskBrowser'
  import collapse from "@/assets/js/collapse.js";
  import CompsQualitySetLabel from './CompsQualitySetLabel'
  export default {
    //质量管理列表
    name: "CompsQualityList",

    components: {
      ModelViewer,
      BimViewer,
      ModelInfo,
      ComQualityDetails,
      // CompsStepTip2,
     // CompsNewQualityProblem,
      CompsMaterialBrowser,
      CompsTaskBrowser,
      collapse,
      CompsQualitySetLabel
    },

    props: {
      searchCheckDetails: {//搜索内容（检查详情）
        type: String,
      },
      typelist: {
        type: String,
        default: '',
      }

      
    },

    data() {
      return {
        showModel:false,
        // 最后一次选择打开的模型版本为 VersionNO
        lastOpenVersionNo: '',

        listTableHeaderStyle: {
          // backgroundColor: '#f0f2f5'
        },
        ajaxParams: {
          BIMComposerId: '',
          Token: '',
          OrganizeId: '',
        },
        showSetting: false,//质量管理设置
        tableTopBarState: {//表格数据选中后 出现的菜单相关数据
          isIndeterminate: false,//表格表头全选框状态
          checkAll: false,
        },
        tableData: [
          // {
          //   ExamineDate: "2019-12-02T10:38:31",
          //   ExamineID: "ef3fd304-1508-489a-87bd-4ca641903cc0",
          //   ExamineRemark: "这是一条测试质量检查数据66",
          //   ExamineResult: "合格",
          //   ExaminerID: "8a0f9c1c-05d7-4968-ab7e-47c434fd78c9",
          //   ModelID: "",
          //   RealName: "薛友松",
          //   ViewpointID: "",
          //   aede_severitylevel: "一般",
          //   aedt_name: "",
          //   ec_name: "--",
          //   ifpassignoreifintime: 0,
          //   ifpassintime: 0,
          // },
        ],
        multipleSelection: [],//已选择的数据

        //KC
        showModelInfo:false,//显示模型详情
        showModelView:false,
        BIM_Session:"",//模型参数返回的Session
        ProjectId:"",
        SelModelObj:{},//选择的当前模型对象
        // //KC
        currentModelData: {},//当前打开的模型的相关数据
        modelSelectId: '全部',//下拉选中的模型id
        modelSelectList: [//下拉选中的模型id
          {
            ModelName: '全部',
            ModelID: '1'
          }
        ],
        qualityDetailsState: false,//质量详情弹窗状态
        currentExamineData: {},//当前点击的质量数据
        showingImportTemplate: false,//导入现场数据弹窗状态
        modelIframeRef: null,//模型窗口的ref对象
        authdata:[],// 整理过的权限数据
        extdata: {
          funcauthdata:undefined,// mounted 中调用接口后会赋值给 funcauthdata
        },

        originatingTaskDialogState: false,//发起检查任务弹窗状态

        status_taskbrowser: false,//进度任务浏览器 弹窗状态
        status_materialbrowser: false,//构件浏览器 弹窗状态
        // 发起流程中，已经选中的任务
        m_formtasksselectedObj: null,

        // 发起流程中，已经选中的构件
        m_formmaterialselectedObj: null,
        createTimeSort: null,//创建时间排序
        originatingTaskDialogForm: {
          // Token: "",
          AedtGuid: "",//现场数据类别，通过接口来获取当前项目下的所有类别 ,
          ExamineRemark: "",//现场数据检查任务的标题 ,
          ExamineDate: "",//任务期限：开始时间 ,
          RectificateDate: "",//任务期限：结束时间 ,
          CheckerUserId: "",//指定的检查人（单个userId）通过接口来获取所有备选人 ,
          // rel_materialjson: "",//选择过的构件数据 ,
          // rel_taskjson: "",//选择过的任务数据 ,
          // bimcomposerId: "",//模型项目ID ,
          // organizeId: "",//项目ID
        },
        r_supervisor_checked:false,//是否为监理人
        originatingTaskDialogForm_other: {
          RectificationRemark:'',//监理检查内容
          aede_severitylevel:'一般',//监理状态
          IsPassed:'不合格',//监理严重等级
          RelationMemberID:'',//指定的整改人
          PrincipalID:'',//指定的验收人
        },

        needUpdateComponentsKey: false,//是否需要更新父组件key
        taskDialogLoad: false,
        originatingTaskType: null,//检查任务类型
        filterOriginatingTaskTypeStatusList:[
          {
            text:'待检查',
            value:'待检查'
          },
          {
            text:'待整改',
            value:'待整改'
          },
          {
            text:'待验收',
            value:'待验收'
          },
          {
            text:'已合格',
            value:'已合格'
          },
          {
            text:'已关闭',
            value:'已关闭'
          },
        ],//按状态筛选的下拉列表
        filterOriginatingTaskTypeList: [],//按类别筛选的下拉列表
        filterOriginatingTaskSeverity:[],
        projectMemberData: null,//检察人员列表信息
        originatingTaskFormSubmitState: false,//发起任务弹窗表单是否可提交
        materialbrowserAuthority: false,//是否有 打开关联构件弹窗的权限
        taskbrowserAuthority: false,//是否有 打开关联任务弹窗的权限
        R_lastTime:true, //记录上一次点击
        UserId:'',//当前用户userid
        drawerEditShow: false, // 设置标签


        taskStarPickerOptions:{
          disabledDate: (time) => {
            return time.getTime() < Date.now() - 3600 * 1000 * 24;
          }
        },
        taskEndPickerOptions:{
          disabledDate: (time) => {
            if(this.originatingTaskDialogForm.ExamineDate != ''){
                return time.getTime() < new Date(this.originatingTaskDialogForm.ExamineDate).getTime()
            }else {
                return time.getTime() < Date.now() - 3600 * 1000 * 24;
            }
          }
        }
      }

    },
    watch:{
      typelist(number){
          let _this = this;
          this.getNecessaryParameters();
        this.getQualityList();
        let editStr = ''
        number == '1' ? editStr = 'XCXC_Edit' : editStr = "XCXCAQ_Edit"
        _this.authdata.lr_edit = _this.$staticmethod.hasSomeAuth(editStr);
        // console.log(this.authdata,this.extdata);

        this.exam_GetExamTypes();
        this.getProjectMember();
      }
    },


    created() {
      this.getNecessaryParameters();
      this.getQualityList();
    },

    mounted() {

      let _this = this;
      let editStr = ''
      this.typelist == '1' ? editStr = 'ZLXCXC_Edit' : editStr = "AQXCXC_Edit"
      this.typelist == '1' ? editStr = 'ZLXCXC_Delete' : editStr = "AQXCXC_Delete"
      _this.authdata.lr_edit = _this.$staticmethod.hasSomeAuth(editStr);
      _this.authdata.lr_del = _this.$staticmethod.hasSomeAuth(editStr);

      // console.log(this.authdata,this.extdata);

      this.exam_GetExamTypes();
      this.getProjectMember();

    },
    methods: {

      closeModel(){
        this.showModel = false
      },
      //导出所有现场数据
      exportSceneAllData() {
        this.$message('下载现场管理数据');
        let url = `${this.$issueBaseUrl.ExportMission}?organizeId=${this.ajaxParams.OrganizeId}&linkType=${this.typelist}&token=${this.ajaxParams.Token}`;
        window.open(url,'_blank');
      },
      _hideModelInfo(){
        var _this = this;
        _this.showModelInfo=false;
        _this.IsDialogCovery=false;
      },
      openNewModel(){
        if (this.$refs.multipleTable.tableData.length == 0) {
          this.$message.error('无数据')
        }
        this.showModel = true
      },
      openModel_Version(Model, ViewID, versionNo) {
        var _this = this;
        this._hideModelInfo();
        this.lastOpenVersionNo = versionNo;
        if (this.showModelView == true) {
          this.$refs.ref_bimviewer.changeModelUrlVersion(versionNo);
          return;
        }
      },
      //模型窗口加载完毕
      //modelIframe 模型iframe的DOM对象
      modelWindowOnload(modelIframe) {
        this.modelIframeRef = modelIframe;
        this.$staticmethod.bimhelper_finishrender(modelIframe, () => {
          this.setAnchorCoordinatesImages(modelIframe)
        });
      },

      //设置锚点图标
      setAnchorCoordinatesImages(modelIframe) {

        //先清除之前遗留的锚点
        this.$staticmethod.bimhelper_point(modelIframe).clearAllAnchorpoint();

        let typeTest = null;
        try{
          JSON.parse(this.currentModelData.ViewpointID);
          //typeTest 为true说明ViewpointID有值，并且是json字符串
          typeTest = true;
        } catch (e) {
          typeTest = false;
        }

        if (this.currentModelData.ViewpointID != '' && typeTest){
          //点击的是哪条数据，就以该条数据为主 **start**
          let relation = JSON.parse(this.currentModelData.ViewpointID);
          //如果相机位置数据不为空，则调整视角到保存的相机位置视角
          if (relation.viewpoint != null && relation.viewpoint != '') {
            let childViewpoint = JSON.parse(relation.viewpoint);
            //以当前数据的相机位置为主
            var tocall_getviewpointbasicinfo = this.$staticmethod.bimhelper_getview(modelIframe);
            tocall_getviewpointbasicinfo.setViewPointBasicInfo(childViewpoint);
          }
          this.setAnchorRelatedCode(relation,modelIframe);
          //点击的是哪条数据，就以该条数据为主 **end**

          let storage = [];
          this.tableData.forEach(item=>{
            //根据当前打开的模型id 遍历筛选出质量问题列表中拥有相同模型id的数据
            if (item.ModelID == this.currentModelData.ModelID) {
              let relations = JSON.parse(item.ViewpointID);
              this.setAnchorRelatedCode(relations,modelIframe);
            }
          });
        }

      },
      setAnchorRelatedCode(relation,modelIframe) {
        var _this = this;
        //是否包含有elementids
        // 都是旧的逻辑  没用了

      },

      clearCurrentData() {
        this.modelSelectList = {
          ModelName: '全部',
          ModelID: '1'
        };
        this.tableData = [];
        this.multipleSelection = [];
        this.getQualityList();
      },

      //显示问题详情
      showQualityDetails(row) {
        this.qualityDetailsState = true;
        this.currentExamineData = row;
      },

      //关闭问题详情弹窗
      closeDtailsDialog(state) {
        this.qualityDetailsState = false;
        if (state) {
          this.$emit('updateComponentsKey');
        }
      },

      onmodelview_close(){
        var _this = this;
        _this.showModelView=false;
        _this.showModelInfo=false;
        //_this.IsDialogCovery=false;
        setTimeout(function(){
          _this.$staticmethod._Set("needpausegetmsg", 0);
        }, 1000);
      },
      openModelInfo(item) {
        this.showModelInfo=true;
        this.CurrentModelId='-1';
        this.SelModelObj=item;
        this.IsDialogCovery=true;
      },
      setTranIndex(val) {
        this.tranIndex=val;
      },

      //删除已选择的质量问题数据
      deleteSelectQualityItems() {
        //lr_edit为false 为无权限
        let RealName = this.$staticmethod.Get('RealName')
        if (!this.authdata.lr_edit) {
          return false;
        }
        if(this.multipleSelection.length==1&&this.multipleSelection[0].bu_examiner_name!=RealName){
          this.$message.warning('只有发起人才能删除');
          return
        }
        this.multipleSelection = this.multipleSelection.filter(item =>item.bu_examiner_name == RealName)
        this.$confirm('是否删除所选任务?  (只能删除发起人为自己的任务)', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          let storage = [];
          let params = {
            Token: this.ajaxParams.Token,
            Ids: ''
          };
          if(this.multipleSelection.length==0)return
          this.multipleSelection.forEach(item=>{
            storage.push(item.ExamineID);
          });
          params.Ids = storage

          this.$axios.post(`${this.$issueBaseUrl.RemoveItems}`,
            params).then(res=>{
            if (res.data.Ret == 1) {
              this.multipleSelection.forEach(item=>{
                this.filterTableData.forEach((data,index,arr)=>{
                  if(item.ExamineID == data.ExamineID) {
                    arr.splice(index,1);
                  }
                });
              });
              this.$message.success('删除成功');
              this.multipleSelection = [];
            } else {
              this.$message.error('删除失败，请刷新再试');
            }
          }).catch(res=>{
            console.log(res);
          });
        }).catch(() => {});
      },
      //关闭选择的问题
      closeSelectQualityItems(){
          let RealName = this.$staticmethod.Get('RealName')
          if (!this.authdata.lr_edit) {
            return false;
          }
          if(this.multipleSelection.length==1&&this.multipleSelection[0].bu_examiner_name!=RealName){
            this.$message.warning('只有发起人才能关闭');
            return
          }
          this.multipleSelection = this.multipleSelection.filter(item =>item.bu_examiner_name == RealName)
          this.$confirm('是否关闭所选数据? (只能关闭发起人为自己的任务)', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            let storage = [];
            let params = {
              Token: this.ajaxParams.Token,
              Ids: ''
          };
          if(this.multipleSelection.length==0)return
            this.multipleSelection.forEach(item=>{
              storage.push(item.ExamineID);
            });
            params.Ids = storage;

            this.$axios.post(`${this.$issueBaseUrl.UpdateItems}`,
              params).then(res=>{
              if (res.data.Ret == 1) {
                this.multipleSelection.forEach(item=>{
                  this.filterTableData.forEach((data,index,arr)=>{
                    if(item.ExamineID == data.ExamineID) {
                      arr.splice(index,1);
                    }
                  });
                });
                this.multipleSelection = [];
                this.getQualityList();
              } else {
                this.$message.error('关闭失败，请刷新再试');
              }
            }).catch(res=>{
              console.log(res);
            });
          }).catch(() => {});
      },
      //获取相关参数
      getNecessaryParameters() {
        this.ajaxParams.BIMComposerId = this.$staticmethod._Get("bimcomposerId");
        this.ajaxParams.Token = this.$staticmethod.Get("Token");
        this.ajaxParams.OrganizeId = this.$staticmethod._Get("organizeId");
      },

      getQualityList() {
        // let params = {
        //   keyword: "",//关键字
        //   Token: this.ajaxParams.Token,
        //   LinkType: this.typelist,
        //   OrganizeId: this.ajaxParams.OrganizeId,
        //   StateType: "",//针对状态的过滤，使用英文逗号来拼接，可选值：A_ToBeCheck（待检查） B_ToBeRectified（待整改） C_ToBeRecheck（待验收） D_Qualified（已合格） ,
        //   AuthorType: "",//针对操作人的过滤，传空字符串或以下其中之一，可选值：AsChecker（我检查）AsRechecker（我复检）AsRectifier（我整改） ,
        //   Severitylevel: "",//针对严重等级的过滤，使用英文逗号来拼接，可选值为：一般、严重、非常严重 ,
        //   Types: "",//针对检查类型的过滤，使用英文逗号来拼接，可选值为：Exam_GetExamTypes 接口返回的数据中的 aedt_guid 字段 ,
        //   SortField: "",//排序依赖的字段，有：创建时间 CreateDate/''， 结束时间 RectificateDate， 状态 State ,
        //   SortIsAsc: "",//传1为正序，其它为倒序 ,
        //   Skip: "",//跳过多少条数据。若转换失败则取全部 ,
        //   Take: "",//取多少条数据。若 Skip 无效或转换为数字失败则取剩余全部
        // };
        
        this.$axios
          .get(`${this.$issueBaseUrl.GetMissions}?keyword=&Token=${this.ajaxParams.Token}&LinkType=${this.typelist}&OrganizeId=${this.ajaxParams.OrganizeId}&StateType=&AuthorType=&Severitylevel=&Types=&SortField=&SortIsAsc=&Skip=&Take=`)
          .then(res=>{
          // console.log(res.data.Data.List,'获取任务列表');

          if (res.data.Ret == 1) {
            this.tableData = res.data.Data.List;
            this.$emit('getQualityList',res.data.Data.List);
          } else {
            this.$message.error('列表数据获取失败，请稍后再试');
          }

        }).catch(res=>{
          this.$message.error('列表数据获取失败，请稍后再试');
        })
      },
      //对象数组去重
      arrayUniquePublic(arr,key){
        let hash = {};
        return arr.reduce(function(item, next) {
          if (next[key] != '') {
            hash[next[key]] ? "" : (hash[next[key]] = true && item.push(next));
          }
          return item;
        }, []);
      },

      handleTableTopBarCheckAllChange(val) {
        // console.log(val)
        this.multipleSelection = val ? this.filterTableData : [];
        this.tableTopBarState.isIndeterminate = false;
        this.$refs.multipleTable.toggleAllSelection();
      },

      //全选/全不选
      handleSelectionChange(val) {
        this.multipleSelection = val;
        //设置第二表头的全选框状态
        let checkedCount = val.length;
        this.tableTopBarState.checkAll = checkedCount === this.filterTableData.length;
        this.tableTopBarState.isIndeterminate = checkedCount > 0 && checkedCount < this.filterTableData.length;
      },

      //打开/关闭 设置页面
      toggleSetting() {
        this.showSetting = !this.showSetting;
        //将状态传给父级
        this.$emit('stateToggle',this.showSetting);
      },
      //关联任务 弹窗状态
      evt_taskbrowserclose() {
        if (!this.taskbrowserAuthority) {
          this.$message.error('当前用户无进度管理权限，无法关联任务');
          return false
        }
        this.status_taskbrowser = !this.status_taskbrowser;
      },

      //关联构件 弹窗状态
      evt_materialbrowserclose() {
        if (!this.materialbrowserAuthority) {
          this.$message.error('当前用户无构件管理权限，无法关联构件');
          return false
        }
        this.status_materialbrowser = !this.status_materialbrowser;
      },

      getOrganizeId() {
        return this.$staticmethod._Get("organizeId");
      },

      getBIMComposerId() {
        return this.$staticmethod._Get("bimcomposerId");
      },

      evt_taskbrowseronok(arr) {
        console.log(arr,'选中任务');
        var _this = this;
        if (!arr.length) {
          _this.$message.warning('未选中任务');
          return;
        }
        _this.func_tasksmerge(arr);
        _this.status_taskbrowser = false;

      },

      // 关闭构件浏览对话框点击确定后
      evt_materialbrowseronok(arr) {
        console.log(arr,'选中构件');
        var _this = this;
        if (!arr.length) {
          _this.$message.warning('未选中构件');
          return;
        }

        // 确保 m_formmaterialselectedObj 是个有效数组，并与 arr 数组合成并集
        _this.func_materialsmerge(arr);

        // debugger;
        // _this.m_formdocselectedObj = fileobj[0];
        _this.status_materialbrowser = false;

      },

      func_tasksmerge(arr) {
        var _this = this;
        if (!_this.m_formtasksselectedObj || !_this.m_formtasksselectedObj.length) {
          _this.m_formtasksselectedObj = [];
        }
        for (var i = 0; i < arr.length; i++) {
          var indexThis = _this.m_formtasksselectedObj.findIndex(x => x.UID_ == arr[i].UID_);
          if (indexThis < 0) {
            _this.m_formtasksselectedObj.push(arr[i]);
          }
        }
      },

      // 确保 m_formmaterialselectedObj 是个有效数组，并与 arr 数组合成并集
      func_materialsmerge(arr) {
        var _this = this;
        if (!_this.m_formmaterialselectedObj || !_this.m_formmaterialselectedObj.length) {
          _this.m_formmaterialselectedObj = [];
        }
        for (var i = 0; i < arr.length; i++) {
          var indexThis = _this.m_formmaterialselectedObj.findIndex(x => x.bm_guid == arr[i].bm_guid);
          if (indexThis < 0) {
            _this.m_formmaterialselectedObj.push(arr[i]);
          }
        }
      },

      func_removematerial(ev, id) {
        var _this = this;
        ev && ev.stopPropagation && ev.stopPropagation();
        _this.$confirm("确认移除该构件?", "操作确认").then(x => {
          _this.m_formmaterialselectedObj = _this.m_formmaterialselectedObj.filter(x => x.bm_guid != id);
        });
      },

      func_removetask(ev, id) {
        var _this = this;
        ev && ev.stopPropagation && ev.stopPropagation();
        _this.$confirm("确认移除关联任务?", "操作确认").then(x => {
          _this.m_formtasksselectedObj = _this.m_formtasksselectedObj.filter(x => x.UID_ != id);
        });
      },
        _stopPropagation(ev) {
            ev && ev.stopPropagation && ev.stopPropagation();
        },

      //获取现场数据类型
      exam_GetExamTypes() {
        // 参数aedtType   质量为"quality"  安全为"security"
        let _type = 'quality'

        this.typelist == '1' ? _type = 'quality' : _type = 'security'
        this.$axios.get(`${this.$issueBaseUrl.Exam_GetExamTypes}?organizeId=${this.ajaxParams.OrganizeId}&Token=${this.$staticmethod.Get('Token')}&aedtType=${_type}`).then(res=>{
          // console.log(res,'获取现场数据类型');
          if (res.data.Ret == 1) {
            this.filterOriginatingTaskTypeList = []
            this.originatingTaskType = res.data.Data.List;
            this.$emit('getTaskType',this.originatingTaskType)
            // console.log(this.originatingTaskType)
            this.originatingTaskType.forEach(item=>{
              this.filterOriginatingTaskTypeList.push({text:item.AedtName,value:item.AedtName})
            })

          } else {
            this.$message.error('获取现场数据类型失败，请尝试刷新');
          }
        }).catch(res=>{
          console.log(res);
        })
      },

      //打开发起任务弹窗
      openTaskDialog() {

        //判断是否有 选择构件的权限
        this.getRelevanceDialogAuthority('MaterialsMgr');
        //判断是否有 选择任务的权限
        this.getRelevanceDialogAuthority('CompsProgress');
        this.UserId = this.$staticmethod.Get("UserId")
        this.originatingTaskDialogState = true;
        this.r_supervisor_checked = false;

        // 下面两行是谢鹏提出默认选中第一个任务类型、在此直接赋值
        if(this.originatingTaskType.length > 0){
          this.setOriginatingTaskTypeSelectData = this.originatingTaskType[0].AedtName;
          this.originatingTaskDialogForm.AedtGuid = this.originatingTaskType[0].AedtGuid;
        }


          this.originatingTaskDialogForm_other.RectificationRemark = ''
          this.originatingTaskDialogForm_other.aede_severitylevel = '一般'
          this.originatingTaskDialogForm_other.IsPassed = '不合格'
          this.originatingTaskDialogForm_other.RelationMemberID = ''
          this.originatingTaskDialogForm_other.PrincipalID = ''
        // this.taskDialogLoad = true;
      },

      closeTaskDialog() {
        this.originatingTaskDialogState = false;
        this.clearOriginatingTaskFormData();
        this.originatingTaskFormSubmitState = false;
        if (this.needUpdateComponentsKey) {
          this.$emit('updateComponentsKey');
        }
      },

      getRelevanceDialogAuthority(type) {
        this.materialbrowserAuthority = true;
        this.taskbrowserAuthority = true;
      },

      //发起任务弹窗 选择任务类型
      handleTaskTypeCommand(command) {
        //保存选择的任务类型（aedt_guid）
        this.originatingTaskDialogForm.AedtGuid = command;
        this.validateOriginatingTaskDialogForm();
      },

      //验证表单是否为空
      validateOriginatingTaskDialogForm() {
        for (let key in this.originatingTaskDialogForm) {
          if (this.originatingTaskDialogForm[key] == '' || this.originatingTaskDialogForm[key] == null) {
            this.originatingTaskFormSubmitState = false;
            break;
          } else {
            this.originatingTaskFormSubmitState = false;
            if(this.r_supervisor_checked === false){
              this.originatingTaskFormSubmitState = true;
            }else if(this.originatingTaskDialogForm_other.RectificationRemark != ''&&
                     this.originatingTaskDialogForm_other.RelationMemberID != ''&&
                     this.originatingTaskDialogForm_other.PrincipalID != ''){

                this.originatingTaskFormSubmitState = true;

            }
          }
        }
        if(this.originatingTaskDialogForm.CheckerUserId != this.UserId){
          this.r_supervisor_checked = false
          for (let key in this.originatingTaskDialogForm) {
          if (this.originatingTaskDialogForm[key] == '' || this.originatingTaskDialogForm[key] == null) {
            this.originatingTaskFormSubmitState = false;
            break;
          } else {
            this.originatingTaskFormSubmitState = true;

          }}

        }

        // console.log(this.originatingTaskFormSubmitState,'表单状态')
      },
      //是否选择监理人
      supervisor_checked(){
        this.originatingTaskDialogForm.CheckerUserId = this.UserId
        this.validateOriginatingTaskDialogForm()
      },
      //获取项目成员
      getProjectMember() {
        let url = `${this.$urlPool.GetUserPaged}?PageNum=1&PageSize=3000&KeyWord=&OrganizeId=${this.ajaxParams.OrganizeId}&searchType=0&RoleId=&Token=${this.ajaxParams.Token}`

        this.$axios.get(url).then(res => {
          // console.log(res,'获取项目成员');
          if (res.data.Ret > 0) {
            this.projectMemberData = res.data.Data.list;
          }
          // this.taskDialogLoad = false;
        }).catch(res => {
          // this.taskDialogLoad = false;
        })
      },

      //发起任务 提交表单
      originatingTaskFormSubmit(state) {
        if (!this.originatingTaskFormSubmitState) {
          return false;
        }
        if(this.R_lastTime == true){
            this.R_lastTime = false
            this.taskDialogLoad = true;

            let selectArr = [];
            if(this.m_formtasksselectedObj && this.m_formtasksselectedObj.length > 0){
              this.m_formtasksselectedObj.forEach(item=>{
                selectArr.push(
                  {PROJECTUID_: item.PROJECTUID_,UID_: item.UID_}
                )
              })
            }

            let originatingTaskDialogForm = {
              Token: this.ajaxParams.Token,
              Aedt_guid: this.originatingTaskDialogForm.AedtGuid,//现场数据类别，通过接口来获取当前项目下的所有类别 ,
              ExamineRemark: this.originatingTaskDialogForm.ExamineRemark,//现场数据检查任务的标题 ,
              ExamineDate: this.originatingTaskDialogForm.ExamineDate,//任务期限：开始时间 ,
              RectificateDate: this.originatingTaskDialogForm.RectificateDate,//任务期限：结束时间 ,
              CheckerUserId: this.originatingTaskDialogForm.CheckerUserId,//指定的检查人（单个userId）通过接口来获取所有备选人 ,
              Rel_materialjson: this.m_formmaterialselectedObj != null ? JSON.stringify(this.m_formmaterialselectedObj) : '[]',//选择过的构件数据 ,
              Rel_taskjson: this.m_formtasksselectedObj != null ? JSON.stringify(selectArr) : '[]',//选择过的任务数据 ,
              LinkType: this.typelist,
              organizeId: this.ajaxParams.OrganizeId,//项目ID
              // ImageIds: '',
              // ImageUrl: '',
              // ExamId: '',
            };

            // console.log(originatingTaskDialogForm,'表单数据')

            this.$axios.post(`${this.$issueBaseUrl.AddMission}`,originatingTaskDialogForm).then(res=>{
              if (res.data.Ret == 1) {
                if(this.r_supervisor_checked === true){
                    let originatingTaskDialogForm_other = {
                      Token: this.ajaxParams.Token,
                      ExamineID: res.data.Data,//现场数据类别，通过接口来获取当前项目下的所有类别 ,
                      RectificationRemark: this.originatingTaskDialogForm_other.RectificationRemark,//现场数据检查任务的标题 ,
                      aede_severitylevel:this.originatingTaskDialogForm_other.aede_severitylevel,//监理状态
                      IsPassed:this.originatingTaskDialogForm_other.IsPassed === '合格'?1:0,//监理严重等级
                      RelationMemberID:this.originatingTaskDialogForm_other.RelationMemberID,//指定的整改人
                      PrincipalID:this.originatingTaskDialogForm_other.PrincipalID,//指定的验收人
                      organizeId: this.ajaxParams.OrganizeId,//项目ID
                      // Files: []
                    };
                    let params = this.$qs.stringify(originatingTaskDialogForm_other);
                    this.$axios.post(`${this.$issueBaseUrl.CheckMission}`,params).then(r => {
                      console.log(r.data.Data);
                      this.clearOriginatingTaskFormData();
                      //清空表单后 重置提交按钮的状态
                      this.originatingTaskFormSubmitState = false;
                      if (state == 'close') {
                        this.originatingTaskDialogState = false;
                        this.$emit('updateComponentsKey');
                      }

                      if (state == 'next') {
                        this.needUpdateComponentsKey = true;
                        this.getQualityList();

                        if(this.originatingTaskType.length > 0){
                          this.setOriginatingTaskTypeSelectData = this.originatingTaskType[0].AedtName;
                          this.originatingTaskDialogForm.AedtGuid = this.originatingTaskType[0].AedtGuid;
                        }
                      }
                    }).catch(x => {
                      console.log(x);
                    })
                }else {
                  this.clearOriginatingTaskFormData();
                  //清空表单后 重置提交按钮的状态
                  this.originatingTaskFormSubmitState = false;
                  if (state == 'close') {
                    this.originatingTaskDialogState = false;
                    this.$emit('updateComponentsKey');
                  }

                  if (state == 'next') {
                    this.needUpdateComponentsKey = true;
                    this.getQualityList();

                    if(this.originatingTaskType.length > 0){
                      this.setOriginatingTaskTypeSelectData = this.originatingTaskType[0].AedtName;
                      this.originatingTaskDialogForm.AedtGuid = this.originatingTaskType[0].AedtGuid;
                    }
                  }

                }
              } else {
                this.$message.error('发布失败');
              }
              this.R_lastTime = true
              this.taskDialogLoad = false;
            }).catch(x => {
              console.log(x);
              this.taskDialogLoad = false;
            });
        }
      },

      //清空发起检查任务的表单数据
      clearOriginatingTaskFormData() {
        for (let key in this.originatingTaskDialogForm) {
          this.originatingTaskDialogForm[key] = '';
        }

        this.m_formmaterialselectedObj = null;
        this.m_formtasksselectedObj = null;
      },
      filterOriginatingTaskSeverityHandle(value,row){
        return row.aede_severitylevel == value
      },
      //按任务类别过滤列表
      filterOriginatingTaskType(value, row,column) {
        console.log(value,row)
        // return row.aedt_guid === value;
        return row.aedt_name === value;
      },
      //按状态过滤列表
      filterOriginatingTaskStatus(value, row,column) {
        // console.log(value,row)
        // // return row.aedt_guid === value;
        // return row.aedt_name === value;
        let childHtml = '';
        switch (row.ExamineResult) {
          case 'A_ToBeCheck'://待检查
            childHtml = '待检查';
            break;

          case 'B_ToBeRectified'://待整改
            childHtml = '待整改';
            break;

          case 'C_ToBeRecheck'://待验收
            childHtml = '待验收';
            break;

          case 'D_Qualified'://已合格
            childHtml = '已合格';
            break;

          case 'E_Closed'://已合格
            childHtml = '已关闭';
            break;

          default:
            childHtml =  row.ExamineResult +'';
        }

        return childHtml === value;
      },

      //按创建时间排序，1 为降序 2 为升序
      handleCreateTimeSort(action) {
        this.createTimeSort = action;
      },
      setLabelFun(){
        // 设置标签的时候刷新标签列表
        this.getTagOption();
        this.getQualityList()
        this.drawerEditShow = !this.drawerEditShow
        if(this.drawerEditShow){
          this.$refs.labelSetDialog.style.left = 0;
        }else{
          this.$refs.labelSetDialog.style.left = "-220px"
        }
      },
      // 获取
      getTagOption(){
        this.exam_GetExamTypes()
      }

    },

    filters: {
      //新版状态过滤
      setExamineResult(row) {
        let childHtml = '';
        switch (row.ExamineResult) {
          case 'A_ToBeCheck'://待检查
            childHtml = '<span class="task-state-item noPass">待检查</span>';
            break;

          case 'B_ToBeRectified'://待整改
            childHtml = '<span class="task-state-item needToDo">待整改</span>';
            break;

          case 'C_ToBeRecheck'://待验收
            childHtml = '<span class="task-state-item needPass">待验收</span>';
            break;

          case 'D_Qualified'://已合格
            childHtml = '<span class="task-state-item pass">已合格</span>';
            break;

          case 'E_Closed'://已关闭
            childHtml = '<span class="task-state-item close">已关闭</span>';
            break;

          default:
            childHtml = '<span class="task-state-item default">'+ row.ExamineResult +'</span>';
        }

        return childHtml;
      },

      setRectification(row) {
        if (row.ExamineResult == '合格') {
          return '<span class="state-color pass">整改完成</span>';
        } else {
          if (row.ifpassintime == 1) {
            return '<span class="state-color pass">整改完成</span>';
          } else {
            if (row.ifpassignoreifintime == 1) {
              return '<span class="state-color noPass">逾期</span>';
            } else {
              return '<span class="state-color needToDo">待整改</span>';
            }
          }
        }
        // ExamineResult == '合格' ? 整改完成 : (ifpassintime == 1? '整改完成': (ifpassignoreifintime == 1?'逾期':'待整改'))
      },

      setExamineDate(val) {
        let storage = val.substr(0, 10);
        return storage;
      }
    },

    computed: {
      setOriginatingTaskTypeSelectData() {
        //任务类别
        let msg = '';
        for (let i = 0; i < this.originatingTaskType.length; i++) {
          if (this.originatingTaskType[i].AedtGuid == this.originatingTaskDialogForm.AedtGuid) {
            msg = this.originatingTaskType[i].AedtName;
            break;
          } else {
            msg = '任务类别';
          }
        }
        return msg;
      },

      setSeverityLevelClass() {
        return function (val) {
          return {
            'grade-color slight': val == '轻微',
            'grade-color normal': val == '一般',
            'grade-color serious': val == '严重',
            'grade-color verySerious': val == '非常严重',
          }
        }
      },

      filterTableData() {
        let tableData = [];
        let _this = this;

        if (this.modelSelectId == '全部' || this.modelSelectId == '1') {
          tableData = this.tableData;
        } else {
          this.tableData.forEach(item => {
            if (item.ModelID == this.modelSelectId ) {
              tableData.push(item);
            }
          })
        }

        let searchData = []
        let severitylevel = []
        tableData.forEach(item => {
          if ((item.ExamineRemark.toLowerCase()).search((this.searchCheckDetails.toLowerCase())) != -1) {
            searchData.push(item);
            if(item.aede_severitylevel) severitylevel.push(item.aede_severitylevel)

          }
        });
        severitylevel = [...new Set(severitylevel)]
        this.filterOriginatingTaskSeverity = []
        severitylevel.forEach(item=>{
            this.filterOriginatingTaskSeverity.push({text:item,value:item})
        })
        //升序降序排列  2: 从小到大  1：从大到小
        //判断，如果createTimeSort不为空，说明要进行排序
        if(this.createTimeSort !== null){
          searchData.sort(function( a , b){
            if(_this.createTimeSort == 1){
              return new Date(b['CreateDate']).getTime() - new Date(a['CreateDate']).getTime();
            }else{
              return new Date(a['CreateDate']).getTime() - new Date(b['CreateDate']).getTime();
            }
          });
        }
        window.tree = this.$refs.multipleTable
        return searchData;
      },

      ProhibitionbuttonClickClass() {
        if (!this.authdata.lr_edit) {
          return 'function-btn-prohibition';
        }
      },
    }
  }
</script>

<style>
  .quality-list .list-container .el-table,
  .quality-list .list-container .el-table__expanded-cell {
    background-color: rgba(0,0,0,0);
  }

  .quality-list .list-container .el-table th,
  .quality-list .list-container .el-table tr {
    background-color: #FFFFFF;
  }

  .quality-list .list-container .el-table .el-table__row .cell {
    justify-content: center;
  }

  .quality-list .list-container .el-table td {
    border-color: #EBEEF5;
  }

  .quality-list .list-container .el-table .has-gutter .gutter {
    background-color: rgb(240, 242, 245);
  }

  /*等级 颜色*/
  .quality-list .list-container .grade-color.slight {
    color: #9B9B9B;
  }

  .quality-list .list-container .grade-color.normal {
    color: #007AFF;
  }

  .quality-list .list-container .grade-color.serious {
    color: #FF7700;
  }

  .quality-list .list-container .grade-color.verySerious {
    color: #F41515;
  }

  /*状态 颜色*/
  .quality-list .list-container .state-color.pass {
    color: #1DA48C;
  }

  .quality-list .list-container .state-color.noPass {
    color: #F5222D;
  }

  .quality-list .list-container .state-color.needToDo {
    color: #FAAD14;
  }

  /*新版 状态标签*/
  .quality-list .list-container .task-state-item {
    height: 24px;
    line-height: 24px;
    padding: 0 10px;
    color: #FFFFFF;
    font-size: 12px;
    display: inline-block;
    border-radius: 4px;
  }

  .quality-list .list-container .task-state-item.needPass {
    background-color: #006BB3;
  }

  .quality-list .list-container .task-state-item.pass {
    background-color: #008073;
  }

  .quality-list .list-container .task-state-item.noPass {
    background-color: #616F7D;
  }

  .quality-list .list-container .task-state-item.needToDo {
    background-color: #B35900;
  }

  .quality-list .list-container .task-state-item.default {
    color: #606266;
    border: 1px solid #606266;
  }

  .quality-list .list-container .task-state-item.close {
    background-color: #880000;
  }

  .quality-list .menu-item li .el-input__inner {
    border-radius: 4px;
    padding: 0 35px 0 15px;
    background-color: #FFF;
  }

  .originating-task-container .task-title .task-type-select .el-button {
    padding: 6px 10px;
    border-radius: 4px;
    font-size: 12px;
  }

  .originating-task-container .originating-task-form .el-select {
    display: block;
  }

  .originating-task-container .originating-task-form .el-input__inner {
    border-radius: 4px;
    border: 1px solid #DCDFE6;
  }

  .originating-task-container .originating-task-form .el-select .el-input__inner {
    padding-left: 15px;
  }
</style>

<style scoped>
  .css-prel {
    height: 100%;
  }

  .quality-list {
    height: 99%;
  }

  .quality-list.quality-list-response {
    width: 550px;
  }

  .quality-list .top-menu {
    display: flex;
    justify-content: space-between;
    margin-bottom: 20px;
  }

  .quality-list .menu-item {
    display: flex;
    align-items: center;
    font-weight: 500;
    color: rgba(0,0,0,0.45);
  }

  .quality-list .menu-left .originating-task-btn {
    background: linear-gradient(224deg,rgba(0,145,255,1) 0%,rgba(0,122,255,1) 100%);
    border-radius: 4px;
    color: #FFFFFF;
    font-size: 12px;
    padding: 5px 10px;
    cursor: pointer;
  }

  .menu-left .originating-task-btn .icon {
    font-size: 12px;
    margin-right: 6px;
    vertical-align: middle;
  }

  .quality-list .menu-item li {
    cursor: pointer;
  }

  .quality-list .menu-item.menu-left li {
    margin-right: 25px;
  }

  .quality-list .menu-item.menu-right li {
    margin-left: 25px;
  }

  .quality-list .menu-item li .icon {
    vertical-align: text-bottom;
    margin-right: 5px;
  }

  .quality-list .menu-item li .desc {
    color: rgba(0,0,0,0.25);
  }

  .quality-list .menu-item li:hover {
    opacity: 0.8;
  }

  span.model-name-hide {
    max-width: 200px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    display: inline-block;
  }

  .quality-list .list-container {
    position: relative;
    height: calc(100% - 45px);
  }

  .quality-list .list-container .table-top-bar {
    position: absolute;
    top: 0;
    left: 0;
    height: 50px;
    width: 100%;
    background-color: #FFFFFF;
    z-index: 10;
    text-align: left;
    padding-left: 91px;
    box-sizing: border-box;
  }

  .quality-list .list-container .longDesc {
    overflow-x: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  /*model-window*/
  .model-window {
    position: absolute;
    top: -22px;
    right: -20px;
    width: calc(100% - 550px);
    height: calc(100% + 45px);
  }

  .mi {
    width: 869px;
    height: 420px;
    position: absolute;
    top: 50%;
    left: 50%;
    -webkit-transform: translate(-50%, -50%);
    transform: translate(-50%, -50%);
    z-index: 97;
  }

  .quality-list .function-btn-prohibition {
    cursor: not-allowed !important;
    opacity: 0.5 !important;
  }

  .originating-task-container {
    flex-direction: column;
    max-height: 500px;
    overflow: auto;
  }

  .originating-task-container .task-title {
    width: 100%;
    margin-bottom: 10px;
    display: flex;
    justify-content: space-between;
    border-bottom: 1px solid rgba(236, 238, 240, 1);
  }

  .originating-task-container .task-title .task-title-area {
    border: none;
    outline: none;
    width: calc(100% - 100px);
  }

  .originating-task-container .originating-task-form {
    text-align: left;
  }

  .inspectors-select-list {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .inspectors-select-list .name-icon {
    width: 24px;
    height: 24px;
    background: rgba(40,58,79,1);
    border-radius: 12px;
    display: inline-block;
    text-align: center;
    line-height: 24px;
    color: #FFFFFF;
    font-size: 12px;
    margin-right: 6px;
  }

  .inspectors-select-list .inspectors-name {
    flex: 1;
    width: 100%;
    font-weight: 500;
    color: rgba(40,58,79,1);
  }

  .inspectors-select-list .inspectors-identity {
    color:rgba(97,111,125,1);
  }

  .originating-task-container .originating-task-form .connect-operation {
    display: flex;
    justify-content: space-between;
  }

  .originating-task-form .connect-operation .connect-item {
    width: 48%;
    height: 40px;
    border-radius: 4px;
    line-height: 38px;
    padding: 0 10px;
    box-sizing: border-box;
    color: rgba(166,174,182,1);
    border: 2px dashed rgba(166,174,182,0.4);
  }

  .originating-task-form .connect-operation .connect-item .icon {
    float: right;
    margin-top: 10px;
  }

  .originating-task-form .connect-operation-list {
    margin-top: 10px;
    padding: 10px;
    box-sizing: border-box;
    border-radius: 4px;
    border: 1px solid rgba(236,238,240,1);
  }

  .originating-task-form .connect-operation-list .title {
    font-weight:400;
    margin-bottom: 5px;
    color:rgba(166,174,182,1);
  }

  .originating-task-form .connect-operation-list .list-item {
    height: 32px;
    cursor: pointer;
    font-size: 13px;
    margin-bottom: 5px;
    display: flex;
    align-items: center;
    padding: 0 8px;
    box-sizing: border-box;
  }

  .originating-task-form .connect-operation-list .list-item:hover {
    background-color: rgba(247,247,247,1);
  }

  .originating-task-form .connect-operation-list .list-item .icon-close {
    visibility: hidden;
  }

  .originating-task-form .connect-operation-list .list-item:hover .icon-close {
    visibility: visible;
  }

  .originating-task-form .connect-operation-list .list-item .desc {
    max-width: 330px;
    margin: 0 8px;
    flex: 1;
    overflow-x: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
  .originating-task-submit .btn {
    border-radius: 4px;
    font-weight: 500;
    padding: 6px 18px;
    cursor: not-allowed;
  }

  .originating-task-submit .btn.available {
    cursor: pointer;
  }

  .originating-task-submit .task-submit-next-btn {
    color: rgba(166,174,182,1);
    background-color: rgba(40,58,79,0.05);
    border:1px solid rgba(40,58,79,0.2);
  }

  .originating-task-submit .task-submit-btn {
    color: #FFFFFF;
    background-color: rgba(166,174,182,1);
  }

  .originating-task-submit .task-submit-next-btn.available {
    border: 1px solid rgba(24, 144, 255, 1);
    background-color: rgba(255, 255, 255, 1);
    color: rgba(24, 144, 255, 1);
  }

  .originating-task-submit .task-submit-btn.available {
    background-color: rgba(24, 144, 255, 1);
  }
  .supervisor_open{
    border: 1px solid #ccc;
    border-radius: 2px;
  }
  .supervisor_open .supervisor_open_con{
    width: 94%;
    padding: 0 3%;
  }
  .q-set-icon{
    cursor: pointer;
    width: 20px;
    height: 20px;
    background-image: url('../../assets/images/p-bq.png');
    background-size: contain;
    background-repeat: no-repeat;
    margin: 0 20px;
  }
  .labelSetDialog{
    position: fixed;
    left: -220px;
    top: 0;
    z-index: 55;
    width: 220px;
    height: 100%;
    background: #fff;
    transition: all 0.5s ease;
    overflow: auto;
  }
</style>
