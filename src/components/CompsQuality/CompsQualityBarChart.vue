<template>
  <div style="width: 100%" class="quality-bar-chart">
    <v-chart :options="polar"/>
  </div>
</template>

<script>
    export default {
        name: "CompsQualityBarChart",

        props: {
            data: Object
            /*
              e.g:
              data: {
                  colors: ['#D8D8D8', '#1890FF'],
                  xAxis: ["单位000-1", "单位000-2", "单位000-3"],
                  item: [
                      {name:'已解决质量数',data: [1, 8, 19, 4, 1, 12, 7, 28, 9]},
                      {name:'全部质量数',data: [5, 20, 36, 10, 10, 20, 15, 30, 19]},
                  ]
              }

              //ps:
                  1、colors的元素顺序  一定要跟item的元素一一对应
                  2、xAxis的元素顺序  一定要跟item下的data的元素一一对应
            */
        },

        data() {
            return {
                polar: {
                    tooltip: {
                        trigger: 'axis'
                    },

                    xAxis: {
                        type: 'category',
                        data: [{
                          textStyle: {

                          }
                        }],
                        axisLine: {
                            lineStyle: {
                                color: '#D9D9D9',
                                type: 'dashed'
                            }
                        },

                        axisTick: {
                            show: false
                        },

                        axisLabel: {
                            formatter: function (value) {
                              let text = value.length > 6 ? (value.slice(0,6)+"...") : value;
                              return '{value|' + text + '}';
                            },
                            rich: {
                              value: {
                                color: 'rgba(0,0,0,0.45)',
                                fontWeight: 'bold',
                                fontSize: '14',
                              }
                            },
                          rotate: -30
                        }
                    },
                    yAxis: {
                        splitLine: {
                            lineStyle: {
                                color: '#D9D9D9',
                                type: 'dashed',
                            }
                        },

                        axisLine: {
                            show: false
                        },

                        axisTick: {
                            show: false
                        },

                        axisLabel: {
                            color: 'rgba(0,0,0,0.45)',
                            fontWeight: 'bold',
                            fontSize: '14',
                        }
                    },
                    series: [
                        {
                            name: '',
                            type: 'bar',
                            data: [],
                            barMaxWidth: '40'
                        },

                        // {
                        //     name: '全部质量数',
                        //     type: 'bar',
                        //     barGap: '-100%',
                        //     data: [],
                        //     barMaxWidth: '40'

                        // },
                    ],
                    color: []
                }
            }
        },

        created() {
            // this.initBarChart();
        },

        methods: {
            initBarChart() {
                // data: {
                //     colors: ['#D8D8D8', '#1890FF'],
                //         xAxis: ["单位000-1", "单位000-2", "单位000-3"],
                //         item: [
                //         {name:'已解决质量数',data: [1, 8, 19, 4, 1, 12, 7, 28, 9]},
                //         {name:'全部质量数',data: [5, 20, 36, 10, 10, 20, 15, 30, 19]},
                //     ]
                // }
                console.log(this.data,'-----')

                this.polar.color = this.data.colors;//设置柱子颜色
                this.polar.xAxis.data = this.data.xAxis;//设置X轴展示数据
                // this.polar.series[0].data = this.data.item
                this.data.item.forEach((item,index)=>{
                    this.polar.series[index].name = item.name;
                    this.polar.series[index].data = item.data;
                });
            },

            noDataLoadingOption() {
              let graphic = {
                  type: 'text',
                  z: 100,
                  left: 'center',
                  top: 'middle',
                  style: {
                    fill: '#333',
                    text: ['横轴表示温度，单位是°C'],
                  }
              };
              // this.polar.graphic.push(graphic);
            }
        }
    }
</script>

<style>
  .quality-bar-chart .echarts {
    width: 100%;
    height: 500px;
  }
</style>
<style scoped>

</style>
