<template>
  <div class="new-build-quality">
    <div class="quality-dialog">
      <div class="top-bar"></div>

    </div>

    <div class="mask"></div>
  </div>
</template>

<script>
    export default {
      //新建质量问题
        name: "CompsNewQualityProblem"
    }
</script>

<style scoped>
.new-build-quality {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  overflow: auto;
  z-index: 1001;
}

.new-build-quality .quality-dialog {
  position: relative;
  width: 1000px;
  min-height: 100px;
  max-height: 700px;
  background:rgba(255,255,255,1);
  box-shadow:0 13px 24px -17px rgba(11,41,62,0.8);
  border-radius:4px;
  overflow: hidden;
  margin: 5vh auto 50px;
  z-index: 1;
}

.new-build-quality .mask {
  position: fixed;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  opacity: .5;
  background: #000;
}

.new-build-quality .top-bar {

}
</style>
