<template>
  <div style="width: 100%">
    <v-chart style="width:100%" :options="polar"/>
  </div>
</template>

<script>
  export default {
    name: "CompsQualityPieChart",

    props: {
      data: Object,
      /*
      e.g:
      data: {
          colors: ['#D8D8D8', '#1890FF', '#F5222D'],
          name: '严重等级',
          item: [
              {name:'一般',value: 18},
              {name:'轻微',value: 54},
              {name:'严重',value: 28},
          ]
      }

      //ps: colors的元素顺序  一定要跟item的元素一一对应
      */
    },

    data() {
      return {
        polar: {
          color: [],
          tooltip: {
            trigger: 'item',
            formatter: "{a} <br/>{b}: {c} ({d}%)"
          },
          legend: {
            show: false
          },
          series: [
            {
              name: '严重等级',
              type: 'pie',
              radius: ['50%', '70%'],
              avoidLabelOverlap: true,

              data: [
                // {
                //     value: 54,
                //     name: '轻微',
                //     label: {
                //         normal: {
                //             formatter: [
                //                 '{value|轻微}{rate|54%}'
                //             ].join('\n'),
                //
                //             rich: {
                //                 value: {
                //                     width: 20,
                //                     align: 'left',
                //                     color: '#9e9e9e',
                //                 },
                //                 rate: {
                //                     width: 40,
                //                     align: 'right',
                //                     color: '#9e9e9e',
                //                 },
                //             },
                //         },
                //     },
                // },
              ],
              labelLine: {
                show: true,
                length: 10,
                length2: 50,
                smooth: false,
              },

              label: {
                position: 'outer',
                alignTo: 'labelLine',
                distanceToLabelLine: 5
              },
            }
          ]
        }
      }
    },

    created() {
      // this.initPieChart();
    },

    methods: {
      initPieChart() {
        this.polar.series[0].data = [];
        this.polar.color = this.data.colors;//设置扇形颜色
        this.polar.series[0].name = this.data.name;//设置标题
        this.data.item.forEach(item => {
          let option = {
            value: 0,
            name: '',
            label: {
              normal: {
                // formatter: [
                //     '{value|轻微}{rate|54%}'
                // ].join('\n'),

                rich: {
                  value: {
                    padding: [0, 10, 0, 0],
                    align: 'left',
                    color: '#9e9e9e',
                  },
                  rate: {
                    align: 'center',
                    color: '#9e9e9e',
                  },
                },
              },
            },
          };
          option.value = item.value;//严重等级（扇形占比）
          option.name = item.name;//扇形标题
          option.label.normal.formatter = ['{value|' + item.name + '}{rate|{d}%}'].join('\n');//单个扇区的标签配置
          this.polar.series[0].data.push(option);
        })
      }
    }
  }
</script>

<style scoped>

</style>
