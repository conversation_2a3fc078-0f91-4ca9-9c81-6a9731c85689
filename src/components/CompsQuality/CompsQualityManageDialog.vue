<template>
  <div class="quality-manage-dialog-mask" @click.stop="">
    <div class="quality-manage-dialog" :style="qualityManageDialogData.coordinate">
      <div class="top-bar">
        <span @click.stop="closeDialog(false)" class="icon icon-arrow-left_outline"></span>
        <span class="title">{{ qualityManageDialogData.title }}</span>
        <span @click="closeDialog(true)" class="icon icon-suggested-close"></span>
      </div>

      <div class="container">
        <p class="title">{{ qualityManageDialogData.title | setSubheading }}</p>
        <input class="name" v-model="organizationName" type="text" placeholder="请输入名称">
      </div>

      <div class="bottom">
        <span :class="{'not-allowed' : organizationName ==''}" @click="handleSubmit()">确定</span>
      </div>
    </div>
  </div>
</template>

<script>
    export default {
        name: "CompsQualityManageDialog",
        props: {
            qualityManageDialogData: Object,//对话框相关数据
        },

        data() {
            return {
                organizationName: ''
            }
        },

        methods: {
            handleSubmit() {
                if (this.organizationName == '') {
                    this.$message.error('组织结构名称不可为空');
                    return false;
                }
                //第一个参数为该次操作的类型，如“edit”或“add”
                //第二个参数为组织结构的名称
                this.$emit('onclk', this.qualityManageDialogData.type, this.organizationName)
            },

            //关闭弹窗
            closeDialog(state) {
                this.organizationName = '';
                this.$emit('close-dialog', state);
            },
        },

        filters: {
            setSubheading(str) {
                return `${str.substring(2, str.length)}名称`
            }
        }
    }
</script>

<style scoped>
  .quality-manage-dialog-mask {
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: 20;
  }

  .quality-manage-dialog {
    position: absolute;
    z-index: 11;
    display: flex;
    justify-content: space-between;
    flex-direction: column;
    width: 220px;
    min-height: 252px;
    background-color: rgba(255, 255, 255, 1);
    box-shadow: 0 6px 14px 0 rgba(26, 26, 26, 0.06);
    border-radius: 4px;
    overflow: hidden;
  }

  .quality-manage-dialog .top-bar {
    width: 100%;
    height: 44px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px;
    box-sizing: border-box;
    border-bottom: 1px solid rgba(0, 0, 0, 0.09);
  }

  .quality-manage-dialog .top-bar .icon {
    width: 20px;
    color: #BFBFBF;
    font-weight: bold;
    cursor: pointer;
  }

  .quality-manage-dialog .top-bar .title {
    flex-grow: 1;
    font-size: 16px;
    font-weight: 500;
  }

  /***************container***************/
  .quality-manage-dialog .container {
    flex-grow: 1;
    text-align: left;
    padding: 15px 12px 0
  }

  .quality-manage-dialog .container .title {
    margin-bottom: 5px;
  }

  .quality-manage-dialog .container .name {
    width: 100%;
    height: 40px;
    padding: 9px 16px;
    box-sizing: border-box;
    border-radius: 4px;
    border: 1px solid rgba(0, 0, 0, 0.09);
  }

  input.name::-webkit-input-placeholder {
    color: rgba(0, 0, 0, 0.25);
  }

  input.name::-moz-placeholder {
    color: rgba(0, 0, 0, 0.25);
  }

  input.name:-moz-placeholder {
    color: rgba(0, 0, 0, 0.25);
  }

  input.name:-ms-input-placeholder {
    color: rgba(0, 0, 0, 0.25);
  }

  /***************bottom***************/
  .quality-manage-dialog .bottom {
    width: 100%;
    height: 64px;
    padding: 12px;
    box-sizing: border-box;
    border-top: 1px solid rgba(0, 0, 0, 0.09);
  }

  .quality-manage-dialog .bottom span {
    width: 100%;
    height: 40px;
    cursor: pointer;
    display: inline-block;
    line-height: 40px;
    border-radius: 4px;
    background-color: #1890FF;
    color: #FFFFFF;
  }

  .quality-manage-dialog .top-bar span.icon:hover,
  .quality-manage-dialog .bottom span:hover {
    opacity: 0.8;
  }

  span.not-allowed {
    cursor: not-allowed !important;
    background-color: rgba(0, 0, 0, 0.25) !important;
  }
</style>
