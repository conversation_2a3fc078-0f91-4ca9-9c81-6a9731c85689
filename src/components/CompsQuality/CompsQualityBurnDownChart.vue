<template>
  <div style="width: 100%">
    <v-chart ref="asd" style="width:100%" :options="polar"/>
  </div>
</template>

<script>
    export default {
        //质量燃烧图
        name: "CompsQualityBurnDownChart",

        props: {
            data: Object,
            totalDatasNum: [String,Number],//问题的总个数
            /*
              e.g:
              data: {
                  colors: ['#1DA48C'],
                  xAxis: ['10-04', '10-05', '10-06'],
                  item: [20, 32, 91, 34, 72, 33, 32, 90]
              }

              //ps:
                  2、xAxis的元素顺序  一定要跟item的元素一一对应
            */
        },

        data() {
            return {
                polar: {
                    tooltip: {
                        trigger: 'axis',
                        axisPointer: {
                            type: 'cross',
                            label: {
                                backgroundColor: '#6a7985'
                            }
                        },

                        formatter: (params) => {
                          let dom = `<div style="text-align:left"><span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;
                                    background-color:#1DA48C;"></span> 已合格：${this.totalDatasNum - params[0].value}<br/>
                                    <span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;
                                    background-color:#0B5EA4;"></span> 未合格：${params[0].value}</div>`;
                          return dom;
                        }
                    },

                    grid: {
                        left: '3%',
                        right: '4%',
                        bottom: '3%',
                        containLabel: true,
                    },
                    xAxis: [
                        {
                            // type: 'category',
                            axisLine: {
                                show: false,
                            },
                            axisTick: {
                                show: false
                            },

                            axisLabel: {
                                color: 'rgba(0,0,0,0.45)',
                                fontWeight: 'bold',
                                fontSize: '14',
                            },
                            // data: ["2000-11-20","2000-11-21","2000-11-22","2000-11-23","2000-11-24","2000-11-25",
                            // "2000-11-26","2000-11-27","2000-11-28","2000-11-29","2000-11-30"]
                            data: []
                        }
                    ],
                    yAxis: [
                        {
                            type: 'value',
                            nameTextStyle: {
                                color: '#DCDCDC',
                                fontWeight: 'bold',
                            },

                            max: 14,
                            axisLine: {
                                show: false
                            },

                            axisTick: {
                                show: false
                            },

                            splitLine: {
                                lineStyle: {
                                    color: '#D9D9D9',
                                    type: 'dashed'
                                }
                            },

                            axisLabel: {
                                color: 'rgba(0,0,0,0.45)',
                                fontWeight: 'bold',
                                fontSize: '14',
                            }
                        }
                    ],
                    series: [
                        {
                            name: '',
                            type: 'line',
                            symbolSize: '7',
                            areaStyle: {
                                color: '#1DA48C',
                                opacity: 0.1,
                            },
                            // data: [12, 12, 9, 9, 9, 9, 9, 9, 9, 9, 9],
                            data: [],

                            markLine: {
                              lineStyle: {
                                type: 'solid',
                                width: 2,
                                color: '#0B5EA4'
                              },
                              data: [[
                                { coord: [0, 14], symbol: 'none'},
                                { coord: ['2000-11-30', 0], symbol: 'none' },

                              ]],
                            },
                        }, 

                    ],
                    color: ['#1DA48C'],
                }
            }
        },

        created() {
        },
        mounted() {
            // this.$refs.asd.showLoading();
            // this.initBurnDownChart();
        },

        methods: {
            initBurnDownChart() {
                this.polar.xAxis[0].data = this.data.xAxis;//设置x轴数据
                this.polar.color = this.data.colors;//设置颜色
                this.polar.series[0].data = this.data.item;//设置折线数据
                this.polar.yAxis[0].max = this.totalDatasNum;//设置y轴数据

                //以下设置基准线
                this.polar.series[0].markLine.data[0][0].coord[0] = 0;
                this.polar.series[0].markLine.data[0][0].coord[1] = this.totalDatasNum;
                this.polar.series[0].markLine.data[0][1].coord[0] = this.data.xAxis[this.data.xAxis.length - 1];
                this.polar.series[0].markLine.data[0][1].coord[1] = 0;
            },
        },
    }
</script>

<style scoped>

</style>
