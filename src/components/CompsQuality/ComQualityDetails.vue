<template>
  <div class="quality-details-container">
    <div class="details-dialog">
      <div class="dialog-top-title">
        <span v-if="!editableState" class="personnel-tag">{{ detailsInfo.aedt_name }}</span>
        <div v-else class="option-button">
          <el-dropdown trigger="click" @command="handleTaskTypeCommand">
            <el-button type="primary">
              <i class="el-icon-menu el-icon--left"></i>{{ setConstructionProblemsSelectTitle }}
            </el-button>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item
                v-for="(item,index) in constructionProblemsList"
                :key="item.AedtName + index"
                :command="item.AedtName">{{ item.AedtName }}</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>

          <!--<div class="css-hover-btn toggle-button prev"><i class="css-mr5 el-icon-arrow-up"></i>上一条</div>-->
          <!--<div class="css-hover-btn toggle-button next"><i class="css-mr5 el-icon-arrow-down"></i>下一条</div>-->
        </div>

        <div class="option-menu">
          <span @click="closeDtailsDialog" class="icon icon-suggested-close"></span>
        </div>

      </div>

      <!--下方详情-->
      <div class="dialog-bottom-content" v-loading="loading">
        <div class="content-left item-content">
          <div class="item-container">
            <div v-if="!editableState" class="detaile-title">{{ detailsInfo.ExamineRemark }}</div>
            <div v-else>
              <template v-if="!detaileTitleState">
                <el-tooltip effect="dark" content="单击可编辑内容" :open-delay="500">
                  <div @click="handleEditDetailTitle($event)" class="detaile-title">{{ detailsInfo.ExamineRemark }}</div>
                </el-tooltip>
              </template>
              <textarea
                autofocus
                rows="4"
                class="detaile-title-input"
                @blur="handleEditDetailTitle($event)"
                v-if="detaileTitleState"
                type="text"
                v-model="editDetailsFormData.title"></textarea>
            </div>
            <div :class="setStateTagClass(detailsInfo.ExamineResult)" class="state-tag">{{ detailsInfo.ExamineResult_CH }}</div>
          </div>

          <div class="item-container">
            <div class="subtitle">基本信息</div>
            <template v-if="!editableState">
              <div class="item-list">
                <span class="title">起止时间</span>
                <div class="text-content margin-bottom-10">
                  {{ currentCheckedData.ExamineDate | setExamineDate }}
                  <span class="color-black-45 css-ml24 css-mr24">至</span>
                  {{ currentCheckedData.RectificateDate | setExamineDate }}
                </div>
              </div>

              <div class="item-list">
                <span class="title">检查人</span>
                <div class="text-content">
                  <span class="personnel-tag margin-bottom-10 inline-block">{{ currentCheckedData.bu_checker_RealName }}</span>
                </div>
              </div>
            </template>
            <template v-else>
              <div class="item-list">
                <span class="title">起止时间</span>
                <div class="text-content">
                  <el-date-picker
                    @change="handleEditDetaileTime"
                    v-model="editDetailsFormData.time"
                    type="daterange"
                    start-placeholder="开始日期"
                    end-placeholder="结束日期"
                    value-format="yyyy-MM-dd HH:mm:SS"
                    :picker-options="taskStarPickerOptions"
                    :default-time="['00:00:00', '23:59:59']">
                  </el-date-picker>
                </div>
              </div>

              <div class="item-list">
                <span class="title">检查人</span>
                <div class="text-content">
                  <el-select
                    size="small"
                    v-model="editDetailsFormData.checkerID"
                    filterable
                    @change="handleEditDetailChecker"
                    placeholder="请选择检查人">
                    <el-option
                      v-for="(item,index) in projectMemberData"
                      :key="item.UserId + index"
                      :label="item.RealName"
                      :value="item.UserId">
                    </el-option>
                  </el-select>
                </div>
              </div>
            </template>
          </div>

          <div class="item-container" v-if="!editableState">
            <div class="subtitle">检查情况</div>
            <div class="check-condition" v-if="detailsInfo.RecordWithAttachments && detailsInfo.RecordWithAttachments.length > 0">
              <p>{{ detailsInfo.RecordWithAttachments[0].RectificationRemark }}</p>
              <div class="media-list">
                <div class="media-content" v-if="detailsInfo.RecordWithAttachments[0].Attachments.length > 0">
                  <div class="media-item" v-for="(img,eq) in detailsInfo.RecordWithAttachments[0].Attachments" :key=eq>
                    <img
                      width="100%"
                      height="100%"
                      v-if="img.AttachmentType == '.png' || img.AttachmentType == '.jpg' || img.AttachmentType == '.jpeg'"
                      :src="$configjson.webserverurl+'/'+img.AttachmentUrl"
                      :alt="img.AttachmentName"
                      @click="showThumbnailCarousel(detailsInfo.RecordWithAttachments[0].Attachments,eq)">
                    <div @click="showThumbnailCarousel(detailsInfo.RecordWithAttachments[0].Attachments,eq)" class="video css-prel" v-if="img.AttachmentType == '.mp4' || img.AttachmentType == '.avi'">
                      <i class="icon el-icon-video-play css-hover-btn"></i>
                      <video
                        width="100%"
                        height="100%"
                        poster
                        :src="$configjson.webserverurl+img.AttachmentUrl"></video>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="item-container" v-if="!editableState">
            <div class="subtitle">等级</div>
            <div class="item-list">
              <span class="title">严重等级</span>
              <div class="text-content">
                <span class="margin-bottom-10 inline-block" :class="setSeverityLevelClass(detailsInfo.aede_severitylevel)">
                  {{ detailsInfo.aede_severitylevel }}
                </span>
              </div>
            </div>
          </div>

          <div class="item-container" v-if="!editableState">
            <div class="subtitle">人员</div>
            <div class="item-list">
              <span class="title">整改人</span>
              <div class="text-content">
                <span class="personnel-tag css-ml12 margin-bottom-10 inline-block" v-for="item in detailsInfo.RelationMember_Users" :key="item.UserId">
                  {{ item.RealName || '-' }}
                </span>
              </div>
            </div>

            <div class="item-list">
              <span class="title">验收人</span>
              <div class="text-content">
                <span class="personnel-tag css-ml12 margin-bottom-10 inline-block" v-for="item in detailsInfo.Principal_Users" :key="item.UserId">
                  {{ item.RealName || '-' }}
                </span>
              </div>
            </div>
          </div>

          <!--<div class="item-container">-->
            <!--<div class="subtitle">基本信息</div>-->
            <!--<div class="item-list">-->
              <!--<span class="title">检查类别</span>-->
              <!--<span class="text-content"></span>-->
            <!--</div>-->
          <!--</div>-->

          <div class="item-container">
            <div class="subtitle" v-if="!editableState">关联内容</div>
            <div class="item-list" v-else>
              <span class="title">关联内容</span>
              <div class="text-content">
                <div class="connect-operation">
                  <div @click="evt_materialbrowserclose" class="connect-item css-hover-btn css-mr10">
                    关联构件<i class="icon icon-interface-associated-component"></i>
                  </div>
                  <div @click="evt_taskbrowserclose()" class="connect-item css-hover-btn">
                    关联任务<i class="icon icon-interface-problem-status"></i>
                  </div>
                </div>
              </div>
            </div>

            <div class="item-list" v-if="m_formmaterialselectedObj && m_formmaterialselectedObj.length">
              <span class="title">关联构件</span>
              <div class="text-content connect-operation-list">
                <div class="list-item " v-for="item in m_formmaterialselectedObj" :key="item.bm_guid" @click="m_formmaterialselectedObjFun(item)">
                  <span class="icon icon-interface-associated-component"></span>
                  <span class="desc">{{ item.bm_materialname }}</span>
                  <span v-if="editableState" @click="func_removematerial($event, item.bm_guid)" class="icon-close icon-suggested-close_circle"></span>
                </div>
              </div>
            </div>

            <div class="item-list" v-if="m_formtasksselectedObj && m_formtasksselectedObj.length">
              <span class="title">关联任务</span>
              <div class="text-content connect-operation-list">
                <div class="list-item" v-for="item in m_formtasksselectedObj" :key="item.modelid">
                  <span class="icon icon-interface-problem-status"></span>
                  <span class="desc">{{ item.NAME_ }}</span>
                  <span v-if="editableState" @click="func_removetask($event, item.UID_)" class="icon-close icon-suggested-close_circle"></span>
                </div>
              </div>
            </div>
          </div>

        </div>

        <div class="content-right item-content">
          <div class="right-subtitle">流转记录</div>

          <div class="rectification-content">
            <div class="top-title">
              <div class="title-main">
                <i class="icon icon-interface-addnew"></i>
                <span class="subtitle">{{ currentCheckedData.bu_examiner_name }} 创建了任务</span>
              </div>
              <div class="time">{{ currentCheckedData.CreateDate | setExamineDate }}</div>
            </div>
            <div class="bottom-container">
              <div class="description">
                <p>{{ currentCheckedData.ExamineRemark }}</p>
              </div>
            </div>
          </div>

          <template v-if="detailsInfo.RecordWithAttachments && detailsInfo.RecordWithAttachments.length > 0">
            <div class="rectification-content" v-for="(item,index) in detailsInfo.RecordWithAttachments" :key=index>
              <div class="top-title">
                <div class="title-main">
                  <i v-if="index==0" class="icon icon-interface-inbox"></i>
                  <i v-else class="icon" :class="setTaskActionIcon(item)"></i>

                  <span v-if="index==0"  class="subtitle">{{ item.RectificationOperator }} 检查了任务</span>
                  <span v-else class="subtitle">{{ item.RectificationOperator }} {{ item | setTaskActionData }}</span>
                  <span :class="setStatusClass(item.aer_counterpart)">「{{ item.aer_counterpart }}」</span>
                </div>
                <div class="time">{{ item.CreateDate | setExamineDate }}</div>
              </div>
              <div class="bottom-container">
                <div class="description">
                  <p>{{ item.RectificationRemark }}</p>
                  <div class="media-content" v-if="item.Attachments.length > 0">
                    <div class="media-item" v-for="(img,eq) in item.Attachments" :key=eq>
                      <img
                        v-if="img.AttachmentType == '.png' || img.AttachmentType == '.jpg' || img.AttachmentType == '.jpeg'"
                        :src="$configjson.webserverurl+'/'+img.AttachmentUrl"
                        :alt="img.AttachmentName"
                        @click="showThumbnailCarousel(item.Attachments,eq)">
                      <div @click="showThumbnailCarousel(item.Attachments,eq)" class="video css-prel" v-if="img.AttachmentType == '.mp4' || img.AttachmentType == '.avi'">
                        <i class="icon el-icon-video-play css-hover-btn"></i>
                        <video
                          width="100%"
                          height="100%"
                          poster
                          :src="$configjson.webserverurl+'/'+img.AttachmentUrl"></video>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </template>
        </div>
      </div>
    </div>
    <div @click.stop="closeDtailsDialog" class="mask"></div>

    <div class="thumbnail-carousel" v-if="carouselState">
      <span class="icon icon-suggested-close_circle" @click.stop="carouselState=false"></span>
      <el-carousel trigger="click" height="500px" :autoplay="false" :initial-index="defaultThumbnailIndex">
        <el-carousel-item v-for="(item,index) in thumbnailCarouselData" :key="item.ExamineAttachmentID">
          <template v-if="item.AttachmentType == '.png' || item.AttachmentType == '.jpg' || item.AttachmentType == '.jpeg'">
            <img v-if="item.AttachmentUrl==''" :src="noImage" alt="">
            <img v-else :src="$configjson.webserverurl+'/'+item.AttachmentUrl" alt="">
            <div class="img-describe">{{ item.AttachmentName ? item.AttachmentName+'-'+index : `图片${index+1}` }}</div>
          </template>
          <template v-if="item.AttachmentType == '.mp4' || item.AttachmentType == '.avi'">
            <video width="100%" height="100%" :src="$configjson.webserverurl+'/'+item.AttachmentUrl" poster controls></video>
            <div class="img-describe">{{ item.AttachmentName ? item.AttachmentName : `视频${index+1}` }}</div>
          </template>
        </el-carousel-item>
      </el-carousel>
    </div>
    <div v-if="carouselState" @click.stop="carouselState=false" class="carousel-mask"></div>

    <!-- 进度任务浏览器 -->
    <CompsTaskBrowser
      v-if="status_taskbrowser"
      :init_zIndex="1002"
      :init_width="'800px'"
      :init_height="'640px'"
      :init_bimcomposerId="getBIMComposerId()"
      :init_organizeId="getOrganizeId()"
      init_title="请选择关联任务"
      @onclose="evt_taskbrowserclose"
      @onok="evt_taskbrowseronok"
    ></CompsTaskBrowser>
    <!-- //进度任务浏览器 -->

    <!-- 构件浏览器 -->
    <CompsMaterialBrowser
      v-if="status_materialbrowser"
      :init_zIndex="1002"
      :init_width="'800px'"
      :init_height="'640px'"
      :init_bimcomposerId="getBIMComposerId()"
      :init_organizeId="getOrganizeId()"
      init_title="请选择关联构件"
      @onclose="evt_materialbrowserclose"
      @onok="evt_materialbrowseronok"
    ></CompsMaterialBrowser>
    <!-- //构件浏览器 -->
    <!-- 构件管理 -->
    <div class="_css-dialog-materials" v-if="MaterialsMgrShow">
      <div class="_css-materials-mgr">
        <MaterialsMgr :showEdit="false" ref="materialsMgr">
          <div slot="btn" class="del-btn" @click="closeMaterialsMgr">
            <template>
              <span class="icon icon-suggested-close" ></span>
            </template>
          </div>
        </MaterialsMgr>
      </div>
    </div>
    <!-- 构件管理 -->
  </div>
</template>

<script>
  //问题详情
  import CompsMaterialBrowser from '@/components/CompsCommon/CompsMaterialBrowser'
  import CompsTaskBrowser from '@/components/CompsCommon/CompsTaskBrowser'
  import MaterialsMgr from '@/components/Home/ProjectBoot/MaterialsMgr'
    export default {
      name: "ComQualityDetails",
      components: {
        CompsMaterialBrowser,
        CompsTaskBrowser,
        MaterialsMgr
      },
      props: [
        'currentCheckedData','constructionProblemsList','projectMemberData'
      ],

      data() {
          return {
            taskStarPickerOptions:{},
            test: '',
            editableState: true,//详情页是否可以编辑
            loading: true,
            detailsInfo: {},//详情信息
            noImage: require('../../../src/assets/svgs_loadbyurl/interface-hebingmoxing.svg'),
            carouselState: false,//轮播图弹窗状态
            thumbnailCarouselData: [],//图片幻灯数据
            defaultThumbnailIndex: 0,//轮播图默认展示第几张图片

            detailDialogForm: {
              AedtGuid: '',
            },
            ajaxParams: {
              BIMComposerId: '',
              Token: '',
              OrganizeId: '',
            },
            materialbrowserAuthority: false,//是否有 打开关联构件弹窗的权限
            taskbrowserAuthority: false,//是否有 打开关联任务弹窗的权限
            status_taskbrowser: false,//进度任务浏览器 弹窗状态
            status_materialbrowser: false,//构件浏览器 弹窗状态
            // 发起流程中，已经选中的任务
            // ------------------------
            m_formtasksselectedObj: null,

            // 发起流程中，已经选中的构件
            // ------------------------
            m_formmaterialselectedObj: null,

            editDetailsFormData: {
              title: '',//标题
              time: [],//起止时间
              checkerID: '',//检查人
            },

            detaileTitleState: false,//标题可编辑状态
            needUpdateList: false,//关闭详情页时，是否需要刷新列表页
            MaterialsMgrShow: false

          }
      },

      created() {
        this.getNecessaryParameters();
        // console.log(this.currentCheckedData,'当前点击的数据')
      },

      mounted() {
        this.getQualityItem();

        this.taskStarPickerOptions = {
          disabledDate(time) {
            return time.getTime() < Date.now() - 3600 * 1000 * 24;
          }
        };

        //判断是否有 选择构件的权限
        this.getRelevanceDialogAuthority('MaterialsMgr');
        //判断是否有 选择任务的权限
        this.getRelevanceDialogAuthority('CompsProgress');

        this.setFormData();
      },

      methods: {
        // POST
        detailesModifyMissionInfo(data,type) {
          // 为空或为1时，表示修改 Title；
          // 为2时：BeginTimeStr及EndTimeStr；
          // 为3时 修改 aedt_guid；
          // 为4时 修改 rel_materialjson；
          // 为5时 修改 rel_taskjson ,
          let params = {
            Title: type==1 ? data : '',//指定的检查新标题 ,
            BeginTimeStr: type==2 ? data[0] : '',//指定新的开始时间 ,
            EndTimeStr: type==2 ? data[1] : '',//指定新的结束时间 ,
            aedt_guid: type==3 ? data : '',//新的类别
            rel_materialjson: type==4 ? data : '[]',//选择过的构件数据 ,
            rel_taskjson: type==5 ? data : '[]',//选择过的任务数据 ,
            modify_flag: type + '',
            Token: this.ajaxParams.Token,
            ExamineID: this.currentCheckedData.ExamineID,
            OrganizeId: this.ajaxParams.OrganizeId,
            Token: this.$staticmethod.Get("Token")
          };

          let msg = '';
          switch (type) {
            case 1:
              msg = '标题';
              break;
            case 2:
              msg = '起止时间';
              break;
            case 3:
              msg = '类别';
              break;
            case 4:
              msg = '关联构件';
              break;
            case 5:
              msg = '关联任务';
              break;
          }

          this.$axios.post(`${this.$issueBaseUrl.ModifyMissionInfo}`,params).then(res=>{
            if (res.data.Ret == 1) {
              if (type == 1) {
                //如果修改的是标题，则修改成功后 展示修改后的标题
                this.detailsInfo.ExamineRemark = this.editDetailsFormData.title;
              }
              this.$message.success(`${msg} 修改成功`);
              this.needUpdateList = true;
            } else {
              this.$message({
                showClose: true,
                message: res.data.Msg,
                type: 'error',
                duration: 3000
              });
            }
          });
        },

        //修改检查人
        handleEditDetailChecker(){
          let fd = {
            Token: this.ajaxParams.Token,
            ExamineID: this.currentCheckedData.ExamineID,
            OrganizeId: this.ajaxParams.OrganizeId,
            CheckerUserId: this.editDetailsFormData.checkerID
          };
          this.$axios.post(`${this.$issueBaseUrl.ModifyMissionChecker}`,fd).then(res=>{
            // console.log(res,'修改检查人');
            if (res.data.Ret == 1) {
              this.$message.success('检查人修改成功');
              this.needUpdateList = true;
            } else {
              this.$message.error('检查人修改失败，请重新尝试修改');
            }
          }).catch()
        },

        //修改检查时间
        handleEditDetaileTime(val) {
          // console.log(this.editDetailsFormData.time,'起止时间');
          // 为2时：BeginTimeStr及EndTimeStr；
          this.detailesModifyMissionInfo(this.editDetailsFormData.time,2);
        },

        //修改标题
        handleEditDetailTitle(event) {
          this.detaileTitleState = !this.detaileTitleState;

          if (event.type == 'click') {
            //如果是点击去编辑标题，就把标题赋值给表单
            this.editDetailsFormData.title = this.detailsInfo.ExamineRemark;
          }

          if (event.type == 'blur') {
            //失去焦点的话，则需要提交表单,保存标题
            this.detailesModifyMissionInfo(this.editDetailsFormData.title,1);
            // 为空或为1时，表示修改 Title；
          }

        },

        setFormData() {
          let time = `${this.currentCheckedData.ExamineDate},${this.currentCheckedData.RectificateDate}`;
          this.editDetailsFormData.time = time.split(',');//保存开始结束时间
          this.editDetailsFormData.checkerID = this.currentCheckedData.bu_checker_UserId;//保存检查人ID
        },

        //获取相关参数
        getNecessaryParameters() {
          this.ajaxParams.BIMComposerId = this.$staticmethod._Get("bimcomposerId");
          this.ajaxParams.Token = this.$staticmethod.Get("Token");
          this.ajaxParams.OrganizeId = this.$staticmethod._Get("organizeId");
        },

        //修改类别
        handleTaskTypeCommand(command) {
          //保存选择的施工问题（aedt_guid）
          this.detailDialogForm.AedtGuid = command;
          this.detailesModifyMissionInfo(this.detailDialogForm.AedtGuid,3)
          // 为3时 修改 aedt_guid；
        },

        //展示图片轮播图
        showThumbnailCarousel(data,index) {
          this.defaultThumbnailIndex = index;
          this.thumbnailCarouselData = data;
          this.carouselState = true;
        },

        //关闭弹窗
        closeDtailsDialog() {
          this.$emit('onClose',this.needUpdateList);
        },

        getQualityItem() {
          this.$axios.get(`${this.$issueBaseUrl.GetMission}?examineID=${this.currentCheckedData.ExamineID}&token=${this.ajaxParams.Token}`)
            .then(res=>{
              if (res.data.Ret == 1) {
                this.detailsInfo = res.data.Data.List;
                this.detailsInfo.RecordWithAttachments.reverse();
                // console.log(this.detailsInfo,'获取详情');

                // A_ToBeCheck（待检查） B_ToBeRectified（待整改） C_ToBeRecheck（待验收） D_Qualified（已合格）
                //如果该任务是‘待检查’状态 则可编辑
                if (this.detailsInfo.ExamineResult == 'A_ToBeCheck') {
                  this.editableState = true;
                } else {
                  this.editableState = false;
                }

                //如果有关联的构件，则展示关联的构件
                if (this.detailsInfo.Materials.length > 0) {
                  this.m_formmaterialselectedObj = this.detailsInfo.Materials;
                  console.log(this.m_formmaterialselectedObj)
                }

                if (this.detailsInfo.Tasks.length > 0) {
                  this.m_formtasksselectedObj = this.detailsInfo.Tasks;
                }

                this.loading = false;
              } else {
                this.$message.error('获取任务详情失败，请尝试重新打开');
              }
          }).catch(res=>{
            console.error(res);
          });
        },

        getRelevanceDialogAuthority(type) {
          this.taskbrowserAuthority = true;
          this.materialbrowserAuthority = true;
          // materialbrowserAuthority: false,//是否有 打开关联构件弹窗的权限
          //   taskbrowserAuthority: false,//是否有 打开关联任务弹窗的权限

        //   this.$axios.get(`${this.$configjson.webserverurl}/api/User/Role/TestTokenSomeAuth`,{
        //     params: {
        //       Token: this.ajaxParams.Token,
        //       OrganizeId: this.ajaxParams.OrganizeId,
        //       bm_encode: type,
        //       bmb_encode: 'lr-visible',
        //     }
        //   }).then(res=>{
        //     if (res.data.Data > 0) {
        //       if (type == 'MaterialsMgr') {
        //         }
        //       if (type == 'CompsProgress') {
        //         this.taskbrowserAuthority = true;
        //         this.materialbrowserAuthority = true;
        //       }
        //     } else {
        //       if (type == 'MaterialsMgr') {
        //         this.materialbrowserAuthority = false;
        //       }
        //       if (type == 'CompsProgress') {
        //         this.taskbrowserAuthority = false;
        //       }
        //     }
        //   }).catch(res=>{
        //     this.$message.error('获取进度管理权限或构件管理权限失败，请重新发起任务');
        //     this.originatingTaskDialogState = false;
        //   });
        },

        //关联任务 弹窗状态
        evt_taskbrowserclose() {
          if (!this.taskbrowserAuthority) {
            this.$message.error('您无进度管理权限，无法关联任务');
            return false
          }
          this.status_taskbrowser = !this.status_taskbrowser;
        },

        //关联构件 弹窗状态
        evt_materialbrowserclose() {
          if (!this.materialbrowserAuthority) {
            this.$message.error('您无构件管理权限，无法关联构件');
            return false
          }
          this.status_materialbrowser = !this.status_materialbrowser;
        },

        getOrganizeId() {
          return this.$staticmethod._Get("organizeId");
        },

        getBIMComposerId() {
          return this.$staticmethod._Get("bimcomposerId");
        },

        evt_taskbrowseronok(arr) {
          // console.log(arr,'选中任务');
          var _this = this;
          if (!arr.length) {
            _this.$message.warning('未选中任务');
            return;
          }
          _this.func_tasksmerge(arr);
          _this.status_taskbrowser = false;

        },

        // 关闭构件浏览对话框点击确定后
        // -------------------------
        evt_materialbrowseronok(arr) {
          // console.log(arr,'选中构件');
          var _this = this;
          if (!arr.length) {
            _this.$message.warning('未选中构件');
            return;
          }

          // 确保 m_formmaterialselectedObj 是个有效数组，并与 arr 数组合成并集
          // --------------------------------------------------------------
          _this.func_materialsmerge(arr);

          // debugger;
          // _this.m_formdocselectedObj = fileobj[0];
          _this.status_materialbrowser = false;

        },

        func_tasksmerge(arr) {
          var _this = this;
          if (!_this.m_formtasksselectedObj || !_this.m_formtasksselectedObj.length) {
            _this.m_formtasksselectedObj = [];
          }
          let selectArr = []
          for (var i = 0; i < arr.length; i++) {
            var indexThis = _this.m_formtasksselectedObj.findIndex(x => x.UID_ == arr[i].UID_);
            if (indexThis < 0) {
              _this.m_formtasksselectedObj.push(arr[i]);
              selectArr.push(
                {PROJECTUID_: arr[i].ProjectUID,UID_: arr[i].UID_}
              )
            }
          }

          this.detailesModifyMissionInfo(JSON.stringify(selectArr),5);
          // 为5时 修改 rel_taskjson ,
        },

        // 确保 m_formmaterialselectedObj 是个有效数组，并与 arr 数组合成并集
        // ---------------------------------------------------------------
        func_materialsmerge(arr) {
          var _this = this;
          if (!_this.m_formmaterialselectedObj || !_this.m_formmaterialselectedObj.length) {
            _this.m_formmaterialselectedObj = [];
          }
          for (var i = 0; i < arr.length; i++) {
            var indexThis = _this.m_formmaterialselectedObj.findIndex(x => x.bm_guid == arr[i].bm_guid);
            if (indexThis < 0) {
              _this.m_formmaterialselectedObj.push(arr[i]);
            }
          }

          this.detailesModifyMissionInfo(JSON.stringify(this.m_formmaterialselectedObj),4);
        },

        func_removematerial(ev, id) {
          var _this = this;
          ev && ev.stopPropagation && ev.stopPropagation();
          _this.$confirm("确认移除该构件?", "操作确认").then(x => {
            _this.m_formmaterialselectedObj = _this.m_formmaterialselectedObj.filter(x => x.bm_guid != id);
            _this.detailesModifyMissionInfo(JSON.stringify(this.m_formmaterialselectedObj),4);
          });
        },

        func_removetask(ev, id) {
          var _this = this;
          ev && ev.stopPropagation && ev.stopPropagation();
          _this.$confirm("确认移除关联任务?", "操作确认").then(x => {
            _this.m_formtasksselectedObj = _this.m_formtasksselectedObj.filter(x => x.UID_ != id);
            _this.detailesModifyMissionInfo(JSON.stringify(this.m_formtasksselectedObj),5);
          });
        },

        editTaskFormSubmit() {
          let params = {
            Token: this.ajaxParams.Token,
            ExamineID: this.currentCheckedData.ExamineID,// 所修改数据的id（条目主键）（必传）
            CategoryID: this.currentCheckedData.CategoryID,//工程结构：（按层级选择，传递id）（可选，为空、空白字符串或null时，不修改此值）
            ExamineResult: "",//检查结果：传递文字“合格”、“整改”（可选，为空、空白字符串或null时，不修改此值） ,
            RectificateDate: "string",//限期整改时间：（时间格式字符串：yyyy-MM-dd HH:mm:ss）（可选，为空、空白字符串或null时，不修改此值）
            PrincipalID: "string",
            aede_checkeruserids: "string",
            aede_examinetype: "string",
            aede_severitylevel: "string"
          }
        },
        // 点击关联构件列表
        m_formmaterialselectedObjFun(currentData){
          console.log(currentData,'点击关联构件列表')
          this.loading = true;
          this.$axios.get(`${this.$configjson.webserverurl}/api/Material/Mtr/GetCategoryArrayByBmGuid?bm_guid=${currentData.bm_guid}&Token=${this.$staticmethod.Get('Token')}`
          ).then(res=>{
            console.log(res,'点击关联构件列表')
            if (res.data.Ret == 1) {
              this.MaterialsMgrShow = true;
              let currentParentDatas = res.data.Data;
              setTimeout(()=>{
                this.$refs.materialsMgr.setSomeTreeChecked(currentData,currentParentDatas)
              },500)     
            } else {
              this.$message.error(res.data.Msg);
            }
            this.loading = false;
          }).catch(res=>{
            this.loading = false;
          })

          
        },
        closeMaterialsMgr(){
          this.MaterialsMgrShow = false
        }

      },

      computed: {
        setConstructionProblemsSelectTitle() {
          //施工问题
          let msg = '';
          for (let i = 0; i < this.constructionProblemsList.length; i++) {
            if (this.constructionProblemsList[i].AedtGuid == this.detailDialogForm.AedtGuid) {
              msg = this.constructionProblemsList[i].AedtName;
              break;
            } else {
              msg = this.detailsInfo.aedt_name || '施工问题';
            }
          }
          return msg;
        },

        //严重等级class
        setSeverityLevelClass() {
          return function (val) {
            return {
              'grade-color slight': val == '轻微',
              'grade-color normal': val == '一般',
              'grade-color serious': val == '严重' || val == '非常严重',
            }
          }
        },

        //状态class
        setStatusClass() {
          return function (val) {
            return {
              'state-color pass': val == '已合格',
              'state-color noPass': val == '不合格',
              'state-color needToDo': val == '需整改' || val == '验收中',
            }
          }
        },

        //问题的状态class
        setStateTagClass() {
          return function (val) {
            return {
              'state-tag A_ToBeCheck' : val == 'A_ToBeCheck',//待检查
              'state-tag B_ToBeRectified' : val == 'B_ToBeRectified',// 待整改
              'state-tag C_ToBeRecheck' : val == 'C_ToBeRecheck',//待验收
              'state-tag D_Qualified' : val == 'D_Qualified',//已合格
            }
          }
        },

        setTaskActionIcon() {
          return function (params) {
            return {
              'icon-interface-link-fill' : params.RectificationOperateFlag == 2,
              'el-icon-s-check' : params.RectificationOperateFlag == 1,
            }
          }
        },
      },

      filters: {
        //时间过滤器
        setExamineDate(val) {
          let storage = '';
          if (val) {
            storage = val.replace(/T/,' ');
          } else {
            storage = '—';
          }
          return storage;
        },

        //设置流转记录中的操作行为
        setTaskActionData(params) {
          let msg = '';
          switch (params.RectificationOperateFlag) {
            case 2:
              msg = '申请复检';
              break;
            case 1:
              msg = '进行了验收';
              break;
          }

          return msg
        },
      }
    }
</script>

<style>
  .quality-details-container .dialog-top-title .option-button .el-button {
    padding: 6px 10px;
    border-radius: 4px;
    font-size: 12px;
  }

  .dialog-bottom-content .content-left .item-list .el-input__inner {
    border-radius: 4px;
    border-width: 1px;
    padding: 0 15px;
  }
</style>

<style scoped>
.quality-details-container {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  overflow: auto;
  z-index: 1001;
}

.quality-details-container .details-dialog {
  position: relative;
  width: 1200px;
  height: calc(100% - 100px);
  background:rgba(255,255,255,1);
  box-shadow:0 13px 24px -17px rgba(11,41,62,0.8);
  border-radius:4px;
  overflow: hidden;
  margin: 5vh auto 50px;
  z-index: 1;
}

.quality-details-container .details-dialog .dialog-top-title {
  width: 100%;
  height: 55px;
  padding: 0 24px;
  border-bottom: 1px solid rgba(0,0,0,0.09);
  box-sizing: border-box;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.quality-details-container .details-dialog .dialog-top-title .option-button {
  display: flex;
}

.details-dialog .dialog-top-title .option-button .toggle-button {
  margin-left: 16px;
  background-color: rgba(97,111,125,1);
  border-radius: 4px;
  color: #FFFFFF;
  font-size: 12px;
  padding: 5px 10px;
}


.details-dialog .dialog-top-title .option-menu .icon {
  cursor: pointer;
}

/*****bottom content******/
.quality-details-container .details-dialog .dialog-bottom-content {
  display: flex;
  min-height: 100px;
  overflow: hidden;
  height: calc(100% - 55px);
}

.details-dialog .dialog-bottom-content .item-content {
  overflow: auto;
}

.details-dialog .dialog-bottom-content .content-left {
  width: 760px;
  text-align: left;
  flex-shrink: 0;
  /*padding: 12px 0;*/
  box-sizing: border-box;
  border-right: 1px solid rgba(0,0,0,0.09);
}

.details-dialog .dialog-bottom-content .item-content .item-container {
  position: relative;
  padding: 24px;
  border-bottom: 1px solid rgba(236,238,240,1);;
}

.details-dialog .dialog-bottom-content .item-content .item-container > .subtitle {
  font-weight: 500;
  color: rgba(40,58,79,1);
  margin-bottom: 15px;
  letter-spacing: 1px;
}

.details-dialog .dialog-bottom-content .item-content .item-container .check-condition p {
  background-color: rgba(247,247,247,1);
  border-radius: 6px;
  padding: 15px 24px;
  line-height: 30px;
}

.dialog-bottom-content .item-content .item-container .check-condition .media-content {
  display: flex;
  flex-wrap: wrap;
}

.item-content .item-container .check-condition .media-content .media-item {
  width: 168px;
  height: 128px;
  margin-right: 10px;
  margin-top: 10px;
  border-radius: 6px;
  overflow: hidden;
  background-color: rgba(216,216,216,1);
}

.item-content .item-container .check-condition .media-content .media-item:nth-child(4n) {
  margin-right: 0;
}

.item-content .item-container .check-condition .media-content .media-item .video {
  width: 100%;
  height: 100%;
}

.item-content .item-container .check-condition .media-content .media-item .video .icon {
  position: absolute;
  top: 50%;
  left: 50%;
  color: #FFFFFF;
  font-size: 2.5em;
  z-index: 1;
  -webkit-transform: translate(-50%,-50%);
  -moz-transform: translate(-50%,-50%);
  -ms-transform: translate(-50%,-50%);
  -o-transform: translate(-50%,-50%);
  transform: translate(-50%,-50%);
}

.dialog-bottom-content .item-content .item-container .detaile-title {
  margin-right: 70px;
  font-size: 16px;
  font-weight: 500;
}

.dialog-bottom-content .item-content .item-container .detaile-title-input {
  border: none;
  background-color: rgb(247, 247, 247);
  font-size: 16px;
  font-weight: 500;
  padding: 0;
  margin-right: 70px;
  max-width: calc(100% - 70px);
  min-width: calc(100% - 70px);
  min-height: 22px;
  outline: 10px solid rgb(247,247,247);
}

.dialog-bottom-content .item-content .item-container .state-tag {
  border-radius: 13px 0 0 13px;
  position: absolute;
  right: 0;
  top: 12px;
  padding: 4px 12px;
  color: #FFFFFF;
}

/*待检查*/
.state-tag.A_ToBeCheck {
  background-color: rgba(97,111,125,1);
}

/*待整改*/
.state-tag.B_ToBeRectified {
  background-color: rgba(211,116,0,1);
}

/*待验收*/
.state-tag.C_ToBeRecheck {
  background-color: rgba(0,108,180,1);
}

/*已合格*/
.state-tag.D_Qualified {
  background-color: rgba(0,141,127,1);
}

.dialog-bottom-content .item-content .item-container .detaile-title:hover {
  background-color: rgba(247,247,247,1);
  outline: 10px solid rgba(247,247,247,1);
}

.dialog-bottom-content .content-left .item-list {
  display: flex;
  align-items: center;
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 10px;
}

.dialog-bottom-content .content-left .item-list .title {
  width: 130px;
  display: inline-block;
  color: rgba(0,0,0,0.45);
  flex-shrink: 0;
  margin-bottom: 10px;
}

.dialog-bottom-content .content-left .item-list .text-content {
  width: calc(100% - 130px);
  color: rgba(0,0,0,1);
}

.item-list .text-content .connect-operation .connect-item {
  width: 45%;
  height: 40px;
  display: inline-block;
  border-radius: 4px;
  line-height: 38px;
  padding: 0 10px;
  box-sizing: border-box;
  color: rgba(166,174,182,1);
  border: 2px dashed rgba(166,174,182,0.4);
}

.item-list .text-content .connect-operation .connect-item .icon {
  float: right;
  margin-top: 10px;
}

.content-left .item-list .connect-operation-list {
  margin-top: 10px;
  padding: 10px;
  box-sizing: border-box;
  border-radius: 4px;
  border: 1px solid rgba(236,238,240,1);
}

.content-left .item-list .connect-operation-list .list-item {
  height: 32px;
  color: rgba(40,58,79,1);
  cursor: pointer;
  font-size: 13px;
  margin-bottom: 5px;
  display: flex;
  align-items: center;
  padding: 0 12px;
  box-sizing: border-box;
}

.content-left .item-list .connect-operation-list .list-item:hover {
  background-color: rgba(247,247,247,1);
}

.content-left .item-list .connect-operation-list .list-item .icon-close {
  visibility: hidden;
}

.content-left .item-list .connect-operation-list .list-item:hover .icon-close {
  visibility: visible;
}

.content-left .item-list .connect-operation-list .list-item .desc {
  width: 100%;
  margin: 0 8px;
  flex: 1;
  overflow-x: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}


.dialog-bottom-content .content-left .item-list .div-content {
  margin-top: 20px;
}

.dialog-bottom-content .content-left .item-list .img-thumbnail {
  width: 200px;
  overflow: hidden;
  cursor: pointer;
  display: inline-block;
  box-shadow: 0 0 5px #9c9c9c;
}

.dialog-bottom-content .content-left .item-list .img-thumbnail:hover {
  box-shadow: 0 0 5px #5d5d5d;
}

.dialog-bottom-content .content-left .item-list .img-thumbnail:nth-child(3n+2) {
  margin: 0 20px;
}

.dialog-bottom-content .content-left .item-list .img-thumbnail img {
  width: 100%;
  vertical-align: middle;
}

  /*等级 颜色*/
.grade-color {
  font-size: 12px;
  font-weight: 500;
  padding: 5px 17px;
  border-radius:4px;
}

.grade-color.slight {
  color: rgba(0, 122, 255, 1);
  background-color: rgba(0,122,255,0.1);
  border: 1px solid rgba(0,122,255,0.5);
}

.grade-color.normal {
  color: rgba(97,111,125,1);
  background-color: rgba(40,58,79,0.05);
  border: 1px solid rgba(40,58,79,0.2);
}

.grade-color.serious {
  color: rgba(244,21,21,1);
  background-color: rgba(244,21,21,0.1);
  border: 1px solid rgba(244,21,21,0.5);
}

/*状态 颜色*/
.state-color.pass {
  color: #1DA48C;
}

.state-color.noPass {
  color: #F5222D;
}

.state-color.needToDo {
  color: #FAAD14;
}

.details-dialog .dialog-bottom-content .content-right {
  flex-grow: 1;
  text-align: left;
}

.details-dialog .dialog-bottom-content .content-right .right-subtitle {
  font-weight: 500;
  color: rgba(40,58,79,1);
  padding: 24px 24px 0;
}

.details-dialog .dialog-bottom-content .rectification-content {
  width: 100%;
  padding: 24px;
  box-sizing: border-box;
  border-bottom: 1px solid rgba(0,0,0,0.09);
}

.details-dialog .dialog-bottom-content .rectification-content .top-title {
  display: flex;
  justify-content: space-between;
  color: rgba(97,111,125,1);
  font-size: 12px;
  margin-bottom: 10px;
}

.dialog-bottom-content .rectification-content .top-title .title-main {
  display: flex;
  align-items: center;
}

.dialog-bottom-content .rectification-content .top-title .title-main .icon {
  font-size: 14px;
  color: rgba(97,111,125,1);
}

.dialog-bottom-content .rectification-content .top-title .title-main .subtitle {
  margin-left: 10px;
  vertical-align: text-bottom;
}

.dialog-bottom-content .rectification-content .bottom-container {
  margin-left: 21px;
}

.dialog-bottom-content .rectification-content .bottom-container .description {
  background-color: rgba(247,247,247,1);
  border-radius: 6px;
  padding: 12px;
}

.dialog-bottom-content .rectification-content .bottom-container .description > p {
  font-size: 12px;
  line-height: 22px;
  word-break: break-all;
}

.rectification-content .description .media-content {
  display: flex;
  margin-top: 10px;
  flex-wrap: wrap;
}

.rectification-content .description .media-content .media-item {
  border-radius: 6px;
  overflow: hidden;
  width: 100px;
  height: 75px;
  margin-bottom: 20px;
  cursor: pointer;
}

.rectification-content .description .media-content .media-item:hover {
  box-shadow: 3px 3px 3px #757575;
}

.rectification-content .description .media-content .media-item:nth-child(3n+2) {
  margin-left: 20px;
  margin-right: 20px;
}

.rectification-content .description .media-content .media-item img,
.rectification-content .description .media-content .media-item .video {
  width: 100%;
  height: 100%;
  vertical-align: middle;
}

.rectification-content .description .media-content .media-item .video .icon {
  position: absolute;
  left: 50%;
  top: 50%;
  color: #FFFFFF;
  font-size: 2em;
  transform: translate(-50%, -50%);
  font-weight: bold;
  z-index: 1;
}


.quality-details-container .carousel-mask,
.quality-details-container .mask {
  position: fixed;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  opacity: .5;
  background: #000;
}

.quality-details-container .thumbnail-carousel {
  width: 1000px;
  height: 500px;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255,255,255,1);
  box-shadow: 0 0 5px 1px rgba(0, 0, 0, 0.5);
  border-radius: 4px;
  margin: 8vh auto 50px;
  z-index: 10;
}

.quality-details-container .thumbnail-carousel .icon.icon-suggested-close_circle {
  position: absolute;
  cursor: pointer;
  top: -15px;
  right: -15px;
  z-index: 11;
  color: #FFFFFF;
  font-size: 2rem;
}
.quality-details-container .thumbnail-carousel .icon.icon-suggested-close_circle:hover {
  opacity: 0.8;
}

.quality-details-container .thumbnail-carousel img {
  width: 100%;
  height: 100%;
}

.quality-details-container .thumbnail-carousel .img-describe {
  width: 100%;
  position: absolute;
  top: 0;
  height: 24px;
  color: #FFFFFF;
  line-height: 24px;
  background-color: rgba(0, 0, 0, 0.4);
}

.quality-details-container .carousel-mask {
  z-index: 5;
}

.personnel-tag {
  background-color: rgba(166,174,182,1);
  border-radius: 4px;
  color: #FFFFFF;
  font-weight: 500;
  font-size: 12px;
  padding: 6px 24px;
}
._css-dialog-materials{
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 3;
  background: rgba(0, 0, 0, 0.4);
}
._css-materials-mgr{
  position: absolute;
  top: 5%;
  left: 10%;
  border-radius: 5px;
  /* margin-left:-600px; */
  width: 80%;
  height: 90%;
  z-index: 4;
  background: #fff;
}
.del-btn{
  position: absolute;
  right: 15px;
  top: 15px;
  cursor: pointer;
}
</style>
