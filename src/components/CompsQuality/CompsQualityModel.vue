<template>
  <!-- <div class="quality-model-wrapper quality-list" v-loading="showloading" ref="qualityModel"> -->
  <div
    :class="[closeHover==false?'NewSize':'quality-model-wrapper quality-list']"
    ref="qualityModel"
    v-if="!isScreen"
  >
    <i class="el-icon-close" @click="closeHandle" v-if="closeHover"></i>
    <el-table
      class="list-container model-table"
      ref="multipleTable"
      border
      :data="currentTableData"
      v-if="showTable && !isScreen"
      @row-click="tableClickHandle"
      tooltip-effect="dark"
    >
      <el-table-column
        fixed
        type="index"
        label="序号"
        align="center"
        width="70"
      >
      </el-table-column>
      <el-table-column
        align="center"
        min-width="150"
        label="标题"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          <div class="longDesc css-cp">
            {{ scope.row.ExamineRemark || "--" }}
          </div>
        </template>
      </el-table-column>
      <el-table-column
        width="95"
        align="center"
        prop="ExamineResult"
        label="状态"
      >
        <template slot-scope="scope">
          <div v-html="$options.filters.setExamineResult(scope.row)"></div>
        </template>
      </el-table-column>
      <el-table-column
        align="center"
        prop="aedt_name"
        width="95"
        label="严重等级"
      >
        <template slot-scope="scope">
          <span :class="setSeverityLevelClass(scope.row.aede_severitylevel)">
            {{ scope.row.aede_severitylevel || "-" }}
          </span>
        </template>
      </el-table-column>
      <el-table-column align="center" width="120" prop="aedt_name" label="类别">
        <template slot-scope="scope">
          {{ scope.row.aedt_name || "-" }}
        </template>
      </el-table-column>
      <el-table-column align="center" label="关联构件">
        <template slot-scope="scope">
          <i
            v-if="hasElement(scope.row.ExamineID)"
            class="icon-interface-guanlianmoxing"
          ></i>
          <span v-else>-</span>
        </template>
      </el-table-column>
    </el-table>
    
    <div class="model-viewer" ref="modelView" v-if="!isScreen">
      <div
      class="projectInfoDialog"
      ref="projectInfoDialog"
      :style="{
        top: projectInfoData.top + 'px',
        left: projectInfoData.left + 'px',
        position:isScreen?'fixed':'absolute',
        color: '#000'
      }"
      v-show="projectInfoData.show"
    >
      <div>{{ projectInfoData.data.title }}</div>
      <div class="mt10">
        <span>状态:</span>
        <span>{{ projectInfoData.data.state }}</span>
      </div>
      <div class="mt10">
        <span>严重等级:</span>
        <span>{{ projectInfoData.data.serverity }}</span>
      </div>
      <div class="mt10">
        <span>类别:</span>
        <span>{{ projectInfoData.data.category }}</span>
      </div>
    </div>
      <!-- <iframe :src="bimViewerUrl" 
        ref="bimviewer"
        @load="iframeWpload" 
      frameborder="0"></iframe> -->
      <IframeLoadModel
        v-if="featureID.length > 0"
        :VaultID='VaultID'
        @load="iframeWpload" 
        :featureID='featureID'
      ></IframeLoadModel>
      <div
        class="no-modelids"
        v-show="!showloading && currentAllModelIds.length == 0"
      >
        无关联模型
      </div>
      
    </div>
    
  </div>
  <div
      class="projectInfoDialog"
      ref="projectInfoDialog"
      :style="{
        top: projectInfoData.top + 'px',
        left: projectInfoData.left + 'px',
        position:isScreen?'fixed':'absolute',
        color: '#000'
      }"
      v-else
      v-show="projectInfoData.show"
    >
      <div>{{ projectInfoData.data.title }}</div>
      <div class="mt10">
        <span>状态:</span>
        <span>{{ projectInfoData.data.state }}</span>
      </div>
      <div class="mt10">
        <span>严重等级:</span>
        <span>{{ projectInfoData.data.serverity }}</span>
      </div>
      <div class="mt10">
        <span>类别:</span>
        <span>{{ projectInfoData.data.category }}</span>
      </div>
    </div>
</template>

<script>
import modelLoading from "@/components/Home/ProjectBoot/modelLoadingProgress.vue";
import IframeLoadModel from './IframeLoadModel'
import { EventBus } from "@static/event.js";
import Vue from "vue";
let vue = Vue;
export default {
  components: {
    modelLoading,
    IframeLoadModel,
    bimViewerUrl:''
  },
  props: {
    tableData: {
      default: () => {
        return [];
      },
    },
    params: {
      default: () => {
        return null;
      },
    },
    closeHover:{
      default:true,
      type: Boolean,
    },
  },
  data() {
    return {
      currentData: [],
      currentPageElementData: new Map(), //方便table点击 ExamineID=》elementid
      currentAllModelIds: [],
      showTable: false,
      projectInfoData: {
        top: 0,
        left: 0,
        data: {
          title: "",
          state: "",
          serverity: "",
          category: "",
        },
        show: false,
      },
      showloading: true,
      percentage: 0,
      modelFinishRender: false,
      currentParams: null,
      currentTableData: [],
      isScreen: true,
      initpointCallback: null,
      VaultID: '',
      featureID: '',
      step: 0
    };
  },
  filters: {
    //新版状态过滤
    setExamineResult(row) {
      let childHtml = "";
      switch (row.ExamineResult) {
        case "A_ToBeCheck": //待检查
          childHtml = '<span class="task-state-item noPass">待检查</span>';
          break;

        case "B_ToBeRectified": //待整改
          childHtml = '<span class="task-state-item needToDo">待整改</span>';
          break;

        case "C_ToBeRecheck": //待验收
          childHtml = '<span class="task-state-item pass">待验收</span>';
          break;

        case "D_Qualified": //已合格
          childHtml = '<span class="task-state-item pass">已合格</span>';
          break;

        default:
          childHtml =
            '<span class="task-state-item default">' +
            row.ExamineResult +
            "</span>";
      }

      return childHtml;
    },
  },
  watch: {},
  computed: {
    setSeverityLevelClass() {
      return function (val) {
        return {
          "grade-color slight": val == "轻微",
          "grade-color normal": val == "一般",
          "grade-color serious": val == "严重" || val == "非常严重",
        };
      };
    },
  },
  methods: {
    // 新模型=高亮
    newModelselectorElementByElementId(eles){
        let elements = scene.selectedObjects;
        // 判断集合是否为空
        if (elements.size > 0) {
            let elementIds = Array.from(elements.keys())

            for(let i = 0; i < elementIds.length; i++ ){
                let element = window.scene.findObject(elementIds[i])
                element.selected = false
                window.scene.render();                 
            }
        }
        for(let i = 0; i < eles.length; i++ ){
            let element = window.scene.findObject(eles[i])
            element.selected = true
            window.scene.render();                 
        } 
        let elements_zoom = window.scene.selectedObjects;
        window.scene.fit(Array.from(elements_zoom.keys()));
    },
    closeHandle() {
      this.$emit("close");
    },
    getExamineResult(ExamineResult) {
      let childHtml = "";
      switch (ExamineResult) {
        case "A_ToBeCheck": //待检查
          childHtml = "待检查";
          break;

        case "B_ToBeRectified": //待整改
          childHtml = "待整改";
          break;

        case "C_ToBeRecheck": //待验收
          childHtml = "待验收";
          break;

        case "D_Qualified": //已合格
          childHtml = "已合格";
          break;
      }

      return childHtml;
    },
    hasElement(id) {
      let active = false;
      this.currentData.forEach((data) => {
        if (data.ExamineID == id) {
          data.ElementID.length == 0 ? (active = false) : (active = true);
        }
      });
      console.log(active);
      return active;
    },
    bm_guid2ElementInfo(array) {
      let data = [];
      let serverity =[]
      console.log('---------------------',array)
      array.forEach((arr, index) => {
        arr.Materials.forEach((material) => {
          if (material.bm_guid) {
            this.currentData[index].Materials.push(material.bm_guid);
            data.push(material.bm_guid);
            serverity.push(arr.aede_severitylevel)
          }
        });
      });
      let obj = {
        BmGuids: data//data.join(","),
      };
      return this.$axios.post(`${this.$configjson.webserverurl}/api/Material/Mtr/GetMaterialElementsByBMGuids?token=${this.$staticmethod.Get("Token")}`,data)
            
    },
    getPointColor(ExamineID) {
        let type
        for(let i = 0;i<this.currentTableData.length;i++){
            if(this.currentTableData[i].ExamineID == ExamineID){
                type = this.currentTableData[i].aede_severitylevel
                break
            }
        }
      let color;

      switch (type) {
        case "非常严重":
          color = "red";
          break;

        case "一般":
          color = "#0087ff";
          break;

        case "" || null || '-':
          color = "#c8c8c8";
          break;

        case "严重":
          color = "#ffb500";
          break;
        default:
          color = '#c8c8c8'
      }
      return color;
    },
    initPoint(api) {
      let _this = this;
      let allElementids = [];
      let elementInfo = [];
      console.log(this.currentPageElementData, this.currentData,  '1111111341241243');
      const tableObj = {}
      this.currentTableData.forEach(v => {
        tableObj[v.ExamineID] = v
      })

      const statusObj = {
        A_ToBeCheck: '待检查',
        B_ToBeRectified: '待整改',
        C_ToBeRecheck: '待验收',
        D_Qualified: '已合格'
      }



      this.currentPageElementData.forEach((data, key) => {
        let elementid = []
        data.elementids.forEach((item) => {
          elementid.push(`${data.modelid}^${item}`);
        });
        let id = elementid[0]
        let ele = window.scene.findObject(id)
        // xyz转换为经纬度坐标
        let realCenter = window.scene.mv.tools.coordinate.vector2mercator(ele.AABBWorld.realCenter)
        console.log(tableObj[key].ExamineRemark, key, '1341243312341242141241234')
        let annotation =  null;
          /**
           * @function addFeature
           * @description 添加场景元素
           * @param type {String} 元素类型, 'annotation'为二维标签
           * @return Map 返回元素对象，Map类型
           * @example scene.addFeature(type):Map
          */
          annotation = scene.addFeature('annotation');
          // 指定二维标签发布的经纬度坐标
          annotation.origin = [realCenter[0], realCenter[1]];
          // // 指定高程
          annotation.altitude = realCenter[2]

          // 指定名称
          annotation.name = `二维标签`;

          let anchorJS = `annotation.addEventListener("mouseenter",e=>{annotation.querySelector(".container").style.display="block";});
          annotation.addEventListener("mouseleave",e=>{annotation.querySelector(".container").style.display="none";});`
          let datas = {
              // 面板标题
              title: tableObj[key].ExamineRemark,
              // 面板数据体
              dataList: [
                  {key: '状态', value: statusObj[tableObj[key].ExamineResult] || tableObj[key].ExamineResult},
                  {key: '严重等级', value: tableObj[key].aede_severitylevel || '-'},
                  {key: '类别', value: tableObj[key].aedt_name},
              ],
              // 场景标识(忽略)
              sceneDataSign: '',
              // 二维标签图片的宽度
              width: 30,
              // 二维标签图片的高度
              height:30,
          }

          //获取当前选择的锚点类型
          let anchorHTML = `<div class="otherAnchorPanel anchor-leadWire-default">
            <div class="container">
                <div class="panel-title">
                    <span>\${panelTitle}</span>
                </div>
                <loop>
                    <div class="item-list">
                        <span class="title">\${loop.key}</span>
                        <span class="desc">\${loop.value}</span>
                    </div>
                </loop>
                <span class="corner-marker one"></span>
                <span class="corner-marker two"></span>
                <span class="corner-marker three"></span>
                <span class="corner-marker four"></span>
            </div>
            <div class="panel-point-img">
                <img class="point-img" src="http://multiverse.vothing.com/image/anchor/1_point.png">
            </div>
        </div>`

        let anchorCSS = `.otherAnchorPanel {
                position: relative;
                font-size: 12px;
            }

            .otherAnchorPanel .container {
                min-width: 200px;
                border: 1px solid #3599ff;
                padding: 0 10px 4px;
                background-color: rgba(0, 0, 0,0.7);
                overflow: hidden;
                color: #FFFFFF;
                position: absolute;
                bottom: 100%;
                transform: translateX(-50%);
                margin-left: 15px;
                display: none;
            }

            .container .corner-marker {
                position: absolute;
                width: 15px;
                height: 15px;
                border: 1px solid #19B2FF;
            }

            .corner-marker.one {
                top: 0;
                left: 0;
                border-right: none;
                border-bottom: none;
            }

            .corner-marker.two {
                top: 0;
                right: 0;
                border-left: none;
                border-bottom: none;
            }

            .corner-marker.three {
                bottom: 0;
                left: 0;
                border-right: none;
                border-top: none;
            }

            .corner-marker.four {
                bottom: 0;
                right: 0;
                border-left: none;
                border-top: none;
            }

            .otherAnchorPanel .panel-title {
                position: relative;
                height: 30px;
                margin-bottom: 10px;
                display: flex;
                align-items: center;
                overflow: hidden;
                white-space: nowrap;
                justify-content: center;
                background: linear-gradient(90deg, rgba(0,147,255,0) 0%, rgba(0,147,255,0.2) 49%, rgba(0,147,255,0) 100%);
            }
            .otherAnchorPanel .panel-title:before {
                content: '';
                width: 50px;
                height: 2px;
                background: rgba(25,178,255,0.9);
                border-radius: 0px 0px 2px 2px;
                top: 0;
                left: 50%;
                position: absolute;
                margin-left: -25px;
            }

            .otherAnchorPanel .panel-title>span{
                font-size: 14px;
            }
            .otherAnchorPanel .container .item-list {
                height: 24px;
                display: flex;
                justify-content: space-between;
                flex-wrap: nowrap;
                align-items: center;
                white-space: nowrap;
                margin-bottom: 2px;
            }
            .otherAnchorPanel .container .item-list .desc {
                margin-left: 15px;
                color: #99FFFF;
            }

            .otherAnchorPanel .container .item-list:nth-child(odd) .desc {
                color: #99FFFF;
            }

            .otherAnchorPanel .container .item-list:nth-child(even) .desc {
                color: #FFB3D9;
            }

            .text-color-default {
                color: rgba(255, 253, 109,0.8)
            }

            .text-color-99FFFF {
                color: #99FFFF;
            }

            .text-color-FFB3D9 {
                color: #FFB3D9;
            }

            .otherAnchorPanel.anchor-leadWire .panel-point-img:before,
            .otherAnchorPanel.anchor-leadWire .panel-point-img:after {
                content: '';
            }

            .otherAnchorPanel.anchor-leadWire-default .panel-point-img{
                width: 100%;
                text-align: center;
            }

            .otherAnchorPanel.anchor-leadWire-default .panel-point-img:hover .panel-point-img + .container {
              display: block;
            }

            .panel-content-1 {
                display: flex;
                justify-content: space-around;
                align-content: center;
                align-items: center;
                margin-bottom: 5px;
            }`

          let postData = {
              position:{x: 0, y: 0, z: 0},//偏移量
              // 面板数据，如无面板传空，传空的话引擎不会走设置数据的逻辑。
              content: datas,
              // 二维标签html，自定义html字符串
              htmlCode:anchorHTML,
              // 二维标签样式设置，自定义css字符串
              // 如果二次添加的锚点样式相同，该参数可为空
              cssCode:anchorCSS,
              // 二维标签js，自定义字符串
              jsCode:anchorJS,
              // 自定义锚点类型，默认为default,自定义标签为custom
              setType: {anchorType: 'custom'}
          };
          // 二维标签数据标识
          annotation.dataKey = 'annotation-'+annotation.id;
          // 二维标签数据设置
          annotation.data = { position:{x:0, y:0, z:0} }
          // 设置标签数据
          scene.postData(postData, 'annotation-'+annotation.id);
          // 加载二维标签
          annotation.load();
      });




      // this.currentPageElementData.forEach((data, key) => {
            // let elementid = []
            // data.elementids.forEach((item) => {
            //   elementid.push(`${data.modelid}^${item}`);

            // });
            



            // let pos = this.$model.Utility.getElementsCenterPoint(elementid);
            // let pointInfo = this.$model.Extension.Point.addAnchorPointByPosition(pos,"",{
            //         examine: key,
            //         element: elementid,
            //     }
            // );
            // pointInfo.viewpointIcon.style.position = "fixed";
            // pointInfo.viewpointIcon.style.width = pointInfo.viewpointIcon.style.height = '20px'
            // pointInfo.viewpointIcon.innerHTML = `<div class="model-point" style="
            //   width: 20px;height: 20px;border-radius: 50%;background: #fff;border: 1px solid #c8c8c8;
            //   position: absolute;
            // ">
            //     <div style="background:${this.getPointColor(
            //       key
            //     )};width: 14px;height: 14px;border-radius: 50%;position: absolute;
            //     top: 0;bottom: 0;left: 0;right: 0;margin: auto;"></div>
            // </div>`;
            // pointInfo.viewpointIcon.addEventListener("click", () => {
            //     this.$model.Controller.zoomElementByElementId(
            //     pointInfo.data.element
            //     );
            // });
            // pointInfo.viewpointIcon.addEventListener("mouseenter", (e) => {
            //     let event = e
            //     _this.currentTableData.forEach((table) => {
            //     if (table.ExamineID == pointInfo.data.examine) {
            //         _this.projectInfoData.data.title = table.ExamineRemark;
            //         _this.projectInfoData.data.state = _this.getExamineResult(
            //             table.ExamineResult
            //         );
            //         _this.projectInfoData.data.serverity =
            //         table.aede_severitylevel || "-";
            //         _this.projectInfoData.data.category = table.aedt_name || "-";
            //     }
            //     });
            //     if (_this.$refs.projectInfoDialog.getBoundingClientRect().height + event.clientY > window.innerHeight ) {
            //         _this.projectInfoData.top =
            //             event.clientY -
            //             _this.$refs.projectInfoDialog.getBoundingClientRect().height;
            //             _this.projectInfoData.left = event.clientX + 20;
            //     } else {
            //         _this.projectInfoData.top = event.clientY - 20;
            //         _this.projectInfoData.left = event.clientX + 20;
            //     }
            //     _this.projectInfoData.show = true;
            // });
            // pointInfo.viewpointIcon.addEventListener("mouseleave", (e) => {
            //     _this.projectInfoData.show = false;
            // });

      // });
      // let elements = this.$model.Selector.getElementByElementID(
      //   allElementids
      // );
        // console.log(this.currentData,this.currentPageElementData)
      // this.currentData.forEach((item,index) => {
      //     if (item.ElementID.length > 0) {
      //         item.ElementID.forEach((elements,index1) =>{
      //           let elementids = []
      //           elements.forEach(elementid=>{
      //               elementids.push(`${item.ModelID[index1]}^${elementid}`)
      //           })
      //           let pos = this.$model.Utility.getElementsCenterPoint(elementids);
      //           let pointInfo = this.$model.Extension.Point.addAnchorPointByPosition(
      //               pos,
      //               "",
      //               {
      //               examine: item.ExamineID,
      //               element: elementids,
      //               }
      //           );
      //           pointInfo.viewpointIcon.style.position = "fixed";
      //     pointInfo.viewpointIcon.innerHTML = `<div class="model-point">
      //           <div style="background:${this.getPointColor(
      //             item.ServerityLevel
      //           )}"></div>
      //       </div>`;
      //     pointInfo.viewpointIcon.addEventListener("click", () => {
      //       this.$model.Controller.zoomElementByElementId(
      //         pointInfo.data.element
      //       );
      //     });
      //     pointInfo.viewpointIcon.addEventListener("mouseenter", (e) => {
      //         console.log(pointInfo.data.examine)
      //       this.currentTableData.forEach((table) => {
      //         if (table.ExamineID == pointInfo.data.examine) {
      //           this.projectInfoData.data.title = table.ExamineRemark;
      //           this.projectInfoData.data.state = this.getExamineResult(
      //             table.ExamineResult
      //           );
      //           this.projectInfoData.data.serverity =
      //             table.aede_severitylevel || "-";
      //           this.projectInfoData.data.category = table.aedt_name || "-";
      //         }
      //       });
      //       if (
      //         this.$refs.projectInfoDialog.getBoundingClientRect().height +
      //           e.clientY >
      //         window.innerHeight
      //       ) {
      //         this.projectInfoData.top =
      //           e.clientY -
      //           this.$refs.projectInfoDialog.getBoundingClientRect().height;
      //         this.projectInfoData.left = e.clientX + 20;
      //       } else {
      //         this.projectInfoData.top = e.clientY - 20;
      //         this.projectInfoData.left = e.clientX + 20;
      //       }
      //           _this.projectInfoData.show = true;
      //     });
      //     pointInfo.viewpointIcon.addEventListener("mouseleave", (e) => {
      //        _this.projectInfoData.show = false;
      //     });
      //       })
      //     }
      // });
    },
    loadModel() {
      let _this = this;
      // alert(0)
      console.log(this.currentAllModelIds, 1341234123412)
      let modelids = (this.currentAllModelIds = [
        ...new Set(this.currentAllModelIds),
      ]);

      // if(modelids.length == 0){
      //     this.showTable = true
      //     return
      // }
      this.showTable = true;
      this.showloading = false;
      this.VaultID = this.$staticmethod._Get("organizeId");
      this.featureID = modelids.join('|')

      if (this.isScreen) {
        // 把modelid传给 NewBIM
        EventBus.$emit("setModel", modelids);
      } else {
        // let obj = {
        //   modelID: modelids,
        //   projectID: this.currentParams.BIMComposerId,
        //   versionNO: "",
        //   viewID: "",
        //   DOM: this.$refs.modelView,
        // };
        // let model = (window.$model = new bim365.BIMModel(obj));
        // model.load();
        this.featureID = modelids.join('|')
        this.bimViewerUrl = `${window.bim_config.bimviewerurl}?projectId=${this.currentParams.BIMComposerId}&model=${modelids.join('|')}&ver=`
        console.log(modelids,'=======modelidsmodelids',modelids.join('|'))
        
      }
    },
    iframeWpload(){
      // vue.prototype.$model = this.$refs.bimviewer.contentWindow.model.BIM365API
      // this.modelEventHandle();
      this.step ++ 
      const featureLen = this.featureID.split('|')
      if (this.step === featureLen.length) {
        console.log('134143132341241241242134124')
        this.initPoint();
      }

    },
    modelEventHandle() {
      let _this = this;
      // this.$model.Events.updateProgressBar.on("default", (val) => {
      //   if (val == 0) this.modelFinishRender = false;
      //   this.percentage = val;
      // });
      // _this.$nextTick(() => {
      //     this.initPoint();
      //   });
      // this.$model.Events.finishRender.on("default", () => {
      //   this.$model.Options.setShowBackground(false);
      //   this.modelFinishRender = true;
      //   _this.$nextTick(() => {
      //     this.initPoint();
      //   });

      //   this.$model.Context.onCanvasResize();
      // });
    },
    tableClickHandle(row) {
      let data = this.currentPageElementData.get(row.ExamineID);
      if (!data) return;


      let allElement = [];
      data.elementids.forEach((elementid, index) => {
        allElement.push(`${data.modelid}^${elementid}`);
      });

      this.newModelselectorElementByElementId(allElement)
      
      // this.$model.Controller.zoomElementByElementId(allElement);
    },
    getTableData() {
      return new Promise((resolve, rej) => {
        // let params = {
        //   keyword: "", //关键字
        //   Token: this.currentParams.Token,
        //   bimcomposerId: this.currentParams.BIMComposerId,
        //   StateType: "", //针对状态的过滤，使用英文逗号来拼接，可选值：A_ToBeCheck（待检查） B_ToBeRectified（待整改） C_ToBeRecheck（待验收） D_Qualified（已合格） ,
        //   AuthorType: "", //针对操作人的过滤，传空字符串或以下其中之一，可选值：AsChecker（我检查）AsRechecker（我复检）AsRectifier（我整改） ,
        //   Severitylevel: "", //针对严重等级的过滤，使用英文逗号来拼接，可选值为：一般、严重、非常严重 ,
        //   Types: "", //针对检查类型的过滤，使用英文逗号来拼接，可选值为：Exam_GetExamTypes 接口返回的数据中的 aedt_guid 字段 ,
        //   SortField: "", //排序依赖的字段，有：创建时间 CreateDate/''， 结束时间 RectificateDate， 状态 State ,
        //   SortIsAsc: "", //传1为正序，其它为倒序 ,
        //   Skip: "", //跳过多少条数据。若转换失败则取全部 ,
        //   Take: "", //取多少条数据。若 Skip 无效或转换为数字失败则取剩余全部
        // };
        let _url = `${this.$issueBaseUrl.GetMissions}?keyword=&Token=${this.currentParams.Token}&LinkType=${this.$staticmethod._Get('typelist')}&OrganizeId=${this.VaultID}StateType=&AuthorType=&Severitylevel=&Types=&SortField=&SortIsAsc=&Skip=&Take=`

        this.$axios
          .get(_url)
          .then((res) => {
            // console.log(res.data.Data.List,'获取任务列表');

            if (res.data.Ret == 1) {
              resolve(res.data.Data.List);
            } else {
              rej("数据失败");
            }
          })
          .catch((res) => {
            rej(res);
          });
      });
    },
    getNecessaryParameters() {
      return {
        BIMComposerId: this.$staticmethod._Get("bimcomposerId"),
        Token: this.$staticmethod.Get("Token"),
        OrganizeId: this.$staticmethod._Get("organizeId"),
      };
    },
    getAllDetails() {
      return new Promise((resolve1) => {
        let tasklist = [];
        console.log(this.currentTableData)
        this.currentTableData.forEach((data) => {
            this.currentData.push({
                ExamineID: data.ExamineID,
                Materials: [],
                ExamineResult: data.ExamineResult,
                ModelID: [],
                ElementID: [],
                ServerityLevel: data.aede_severitylevel,
            });
          tasklist.push(
            new Promise((resolve) => {
              this.$axios
                .get(
                  `${this.$issueBaseUrl.GetMission}?examineID=${data.ExamineID}&token=${this.currentParams.Token}`
                )
                .then((res) => {
                  
                  resolve(res.data.Data.List);
                });
            })
          );
        });
        Promise.all(tasklist).then((res) => {
            this.bm_guid2ElementInfo(res).then((res) => {
              console.log(res)
            if (res.data.Ret == -1) {
              this.loadModel();
              return;
            }
            let resData = res.data.Data;
            for (let key in resData) {
              this.currentData.forEach((data) => {
                data.Materials.forEach((material) => {
                  if (material == key) {
                    this.currentAllModelIds.push(
                      resData[key][0].modelid
                    );
                    if(!this.currentPageElementData.get(data.ExamineID)){
                      this.currentPageElementData.set(
                        data.ExamineID,
                        resData[key][0]
                      );
                    }else{
                      let d = this.currentPageElementData.get(data.ExamineID)
                      d.elementids = d.elementids.concat(resData[key][0].elementids)
                      this.currentPageElementData.set(
                        data.ExamineID,
                        d
                      );
                    }
                    console.log(this.currentPageElementData)
                    data.ModelID.push(resData[key][0].modelid);
                    resData[key][0].elementids;
                    data.ElementID.push(
                      resData[key][0].elementids
                    );
                  }
                });
              });
              console.log(this.currentData)
            }
            resolve1();
            this.loadModel();
          });
        });
      });
    },
  },
  created() {
  },
  mounted() {
    let _this = this;
    this.VaultID = this.$staticmethod._Get("organizeId");
    if (this.$props.params) {
      this.currentParams = this.$props.params;
    } else {
      this.currentParams = this.getNecessaryParameters();
    }
    if (this.$props.tableData.length == 0) {
      // this.$refs.qualityModel.style.position = "static";
      this.isScreen = true;
      this.getTableData().then((data) => {
        this.currentTableData = data;
        this.getAllDetails().then(() => {
          if (this.currentAllModelIds != model.modelid) return;
          this.$model = model.model;
          this.modelEventHandle();
        });
      });
    } else {
      this.isScreen = false;
      // this.$refs.qualityModel.style.position = "fixed";
      this.currentTableData = this.$props.tableData;
      this.getAllDetails();
    }
    // EventBus.$once("modelMounted", (model) => {
    //   if (_this.currentAllModelIds) {
    //     if (_this.currentAllModelIds != model.modelid) return;
    //     _this.$model = null;
    //     _this.$model = model.model;
    //     this.$model.Controller.removeAllIsolateElement();
    //     if (!model.isFinishRender) {
    //       model.model.Events.finishRender.once("default", () => {
    //         this.initPoint();
    //       });
    //     } else {
    //       this.initPoint();
    //     }
    //   } else {
    //     _this.getTableData().then((data) => {
            
    //       _this.currentTableData = data;
    //       _this.getAllDetails().then(() => {
    //         if ([...new Set(_this.currentAllModelIds)] != model.modelid) return;
    //         _this.$model = model.model;
    //         _this.modelEventHandle();
    //       });
    //     });
    //   }
    // });
  },
  destroyed() {},
};
</script>
<style lang="stylus" rel="stylesheet/stylus">
.mt10 {
  margin-top: 10px;
}
.NewSize {
  box-sizing: border-box;
  margin-bottom: -10px;
  position fixed
  top:100px;
  right: 0;
  bottom: 0;
  left:418px;
  padding: 20px 0 20px 20px;
  z-index: 1000;
  height: 100%;
  display: flex;
  flex-direction: row;
  background #f0f2f5

  .el-icon-close {
    width: 50px;
    height: 50px;
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 10;
    font-size: 25px;
    cursor: pointer;

    &:hover {
      color: #1890ff;
    }
  }

  .el-table .cell {
    text-align: center;
    justify-content: center;
  }

  .projectInfoDialog {
    z-index: 30;
    position: fixed;
    width: 210px;
    background: #fff;
    box-shadow: 0px 13px 24px -17px rgba(11, 41, 62, 0.8);
    border-radius: 8px;
    display: flex;
    flex-direction: column;
    padding: 20px;

    div {
      text-align: left;
      display: flex;
      flex-direction: row;

      span {
        flex: 1;
      }
    }
  }

  .model-table {
    flex: 1;
    margin-right: 20px;
    background: #fff;
    z-index: 10;
    overflow: auto;
  }

  .model-viewer {
    width:50%
    margin-bottom: 80px;
    flex: 1;
    position: relative;
    overflow: hidden;
    iframe{
      width 100%
      height 100%
    }
  }

  .model-point {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: #fff;
    border: 1px solid #c8c8c8;
    position: absolute;

    div {
      width: 14px;
      height: 14px;
      border-radius: 50%;
      position: absolute;
      top: 0;
      bottom: 0;
      left: 0;
      right: 0;
      margin: auto;
      background: red;
    }
  }
}
.quality-model-wrapper {
  box-sizing: border-box;
  margin-bottom: -10px;
  position fixed
  top:0;
  right: 0;
  bottom: 0;
  left:200px;
  padding: 20px 0 20px 20px;
  z-index: 1000;
  height: 100%;
  display: flex;
  flex-direction: row;
  background #f0f2f5

  .el-icon-close {
    width: 50px;
    height: 50px;
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 10;
    font-size: 25px;
    cursor: pointer;

    &:hover {
      color: #1890ff;
    }
  }

  .el-table .cell {
    text-align: center;
    justify-content: center;
  }

  .projectInfoDialog {
    z-index: 30;
    position: fixed;
    width: 210px;
    background: #fff;
    box-shadow: 0px 13px 24px -17px rgba(11, 41, 62, 0.8);
    border-radius: 8px;
    display: flex;
    flex-direction: column;
    padding: 20px;

    div {
      text-align: left;
      display: flex;
      flex-direction: row;

      span {
        flex: 1;
      }
    }
  }

  .model-table {
    flex: 1;
    margin-right: 20px;
    background: #fff;
    z-index: 10;
    overflow: auto;
  }

  .model-viewer {
    flex: 1;
    position: relative;
    overflow: hidden;
    iframe{
      width 100%
      height 100%
    }
  }

  .model-point {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: #fff;
    border: 1px solid #c8c8c8;
    position: absolute;

    div {
      width: 14px;
      height: 14px;
      border-radius: 50%;
      position: absolute;
      top: 0;
      bottom: 0;
      left: 0;
      right: 0;
      margin: auto;
      background: red;
    }
  }
}
</style>