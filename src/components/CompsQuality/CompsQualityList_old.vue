<template>
  <div class="css-prel">
    <div :class="[{'quality-list-response' : showModelView},'quality-list']">
      <div class="top-menu">
        <div class="title">
          <span class="icon icon-interface-toleft"></span>
          现场数据
        </div>

        <ul class="menu-item menu-right">
          <!--<li @click="newQualityProblemState=true"><span class="icon icon-interface-attributes"></span>新建</li>-->
          <li :class="{'function-btn-prohibition':!authdata.lr_edit}" @click="!authdata.lr_edit ? '' : showingImportTemplate=true">
            <span class="icon icon-interface-download-fill"></span>导入
          </li>
          <!--<li><span class="icon"></span>&nbsp;</li>占位用，后期删除-->
          <!--<li><span class="icon icon-interface-setting"></span>整改</li>-->
          <!--<li><span class="icon icon-interface-safety"></span>验收</li>-->
          <!--<li><span class="icon icon-interface-filter"></span>筛选</li>-->
          <li>
            <el-select v-model="modelSelectId" placeholder="">
              <el-option
                v-for="item in modelSelectList"
                :key="item.ModelID + new Date().getTime()"
                :label="item.ModelName"
                :value="item.ModelID">
                <el-tooltip effect="dark" :content="item.ModelName" :open-delay=1000 :enterable=false>
                  <span class="model-name-hide">{{ item.ModelName }}</span>
                </el-tooltip>
              </el-option>
            </el-select>
          </li>
        </ul>
      </div>

      <div class="list-container">
        <div class="table-top-bar" v-if="multipleSelection.length > 0">
          <ul class="menu-item menu-left" style="height: 100%">

            <li>
              <el-checkbox
                :indeterminate="tableTopBarState.isIndeterminate"
                v-model="tableTopBarState.checkAll"
                @change="handleTableTopBarCheckAllChange">
              </el-checkbox>
            </li>

            <li @click="ExportSceneData">
              <span class="icon icon-interface-download"></span>导出
            </li>

            <li :class="{'function-btn-prohibition':!authdata.lr_edit}" @click="deleteSelectQualityItems">
              <span class="icon icon-interface-delete"></span>删除
            </li>
            <li><span class="desc">已选择{{ multipleSelection.length }}项</span></li>
          </ul>
        </div>

        <el-table
          :header-cell-style="listTableHeaderStyle"
          ref="multipleTable"
          :data="filterTableData"
          tooltip-effect="dark"
          style="width: 100%"
          height="100%"
          @selection-change="handleSelectionChange">
          <el-table-column
            fixed
            type="index"
            label="序号"
            align="center"
            width="70">
          </el-table-column>
          <el-table-column
            align="center"
            type="selection"
            width="55">
          </el-table-column>

          <el-table-column
            align="center"
            min-width="200"
            label="检查详情"
            show-overflow-tooltip>
            <template slot-scope="scope">
              <div @click.stop="showQualityDetails(scope.row)" class="longDesc css-cp">{{ scope.row.ExamineRemark || '--'}}</div>
            </template>
          </el-table-column>

          <el-table-column
            align="center"
            min-width="200"
            label="结构"
            show-overflow-tooltip>
            <template slot-scope="scope">
              <div @click.stop="showQualityDetails(scope.row)" class="longDesc css-cp">{{ scope.row.ec_name }}</div>
            </template>
          </el-table-column>

          <el-table-column
            align="center"
            width="100"
            label="在模型查看">
            <template slot-scope="scope">
              <div v-if="scope.row.ViewpointID" @click.stop="viewInModel(scope.row)">
                <span class="css-cp icon-interface-guanlianmoxing"></span>
              </div>
            </template>
          </el-table-column>

          <el-table-column
            v-if="!showModelView"
            align="center"
            width="95"
            label="严重等级">
            <template slot-scope="scope">
              <span :class="setSeverityLevelClass(scope.row.aede_severitylevel)">
                {{ scope.row.aede_severitylevel }}
              </span>
            </template>
          </el-table-column>

          <el-table-column
            v-if="!showModelView"
            align="center"
            width="95"
            label="检查类别">
            <template slot-scope="scope">
              {{ scope.row.aedt_name || '-' }}
            </template>
          </el-table-column>

          <el-table-column
            v-if="!showModelView"
            align="center"
            width="95"
            label="整改情况">
            <template slot-scope="scope">
              <div v-html="$options.filters.setRectification(scope.row)"></div>
            </template>
          </el-table-column>

          <el-table-column
            v-if="!showModelView"
            width="95"
            align="center"
            prop="ExamineResult"
            label="验收结果">
          </el-table-column>

          <el-table-column
            v-if="!showModelView"
            width="100"
            align="center"
            prop="RealName"
            label="检查人">
          </el-table-column>

          <el-table-column
            v-if="!showModelView"
            width="170"
            align="center"
            label="检查时间">
            <template slot-scope="scope">
              {{ scope.row.ExamineDate | setExamineDate}}
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>

    <div class="model-window" v-if="showModelView">
      <bim-viewer
        ref="ref_bimviewer"
        :BIM_Session="BIM_Session"
        :ProjectID="ProjectId"
        :ModelID="SelModelObj.ID"
        :modelName="SelModelObj.Name"
        :SelModel="SelModelObj"
        @notmtr_onload="modelWindowOnload"
        @ItemopenModelInfo="openModelInfo"
        @setModelInfoIndex="setTranIndex"
        @CloseModelView="onmodelview_close">
      </bim-viewer>

      <ModelInfo class="mi"
       v-if="showModelInfo"
       :lastOpenVersionNo="lastOpenVersionNo"
       :ProjectID='ProjectId'
       :ModelID="SelModelObj.ID"
       :Phase="(SelModelObj.Phase || '').toString()"
       :Creator="SelModelObj.Creator"
       :CreateTime="SelModelObj.CreateTime"
       :IsMerge="SelModelObj.IsMerge"
       :tranIndex="tranIndex"
       :SelModel="SelModelObj"
       @openVersion="openModel_Version"
       @CloseModelInfo='showModelInfo=false;
      IsDialogCovery=false;'>
      </ModelInfo>
    </div>

    <ComQualityDetails
      v-if="qualityDetailsState"
      :data="currentExamineData"
      @onClose="closeDtailsDialog"></ComQualityDetails>

    <!--<CompsNewQualityProblem v-if="newQualityProblemState"></CompsNewQualityProblem>-->

    <CompsStepTip2
      :zIndex=10
      mubanimgclass="template-Qualityimport"
      :useNewClick="true"
      v-if="showingImportTemplate"
      width="504"
      @oncancel="showingImportTemplate = false"
      @onok="_onimportok"
      @ontemplate="_ontemplate"
      title="导入现场数据"
    ></CompsStepTip2>
  </div>
</template>

<script>
  import BimViewer from '@/components/Home/ProjectBoot/Model/BIMViewer' //打开模型
  import ModelInfo from "@/components/Home/ProjectBoot/Model/ModelInfo";
  //import CompsNewQualityProblem from "@/components/CompsQuality/CompsNewQualityProblem";
  import ComQualityDetails from "@/components/CompsQuality/ComQualityDetails";//质量详情
  import CompsStepTip2 from "@/components/CompsCommon/CompsStepTip2";//导入问题
  export default {
    //质量管理列表
    name: "CompsQualityList",

    components: {
      BimViewer,
      ModelInfo,
      ComQualityDetails,
      CompsStepTip2,
     // CompsNewQualityProblem,
    },

    props: {
      searchCheckDetails: {//搜索内容（检查详情）
        type: String,
      }
    },

    data() {
      return {
        newQualityProblemState: false,//新建现场数据弹窗状态

        // 最后一次选择打开的模型版本为 VersionNO
        // -----------------------------------
        lastOpenVersionNo: '',

        listTableHeaderStyle: {
          backgroundColor: '#f0f2f5'
        },
        ajaxParams: {
          BIMComposerId: '',
          Token: '',
          OrganizeId: '',
        },
        showSetting: false,//质量管理设置
        tableTopBarState: {//表格数据选中后 出现的菜单相关数据
          isIndeterminate: false,//表格表头全选框状态
          checkAll: false,
        },
        tableData: [
          // {
          //   ExamineDate: "2019-12-02T10:38:31",
          //   ExamineID: "ef3fd304-1508-489a-87bd-4ca641903cc0",
          //   ExamineRemark: "这是一条测试质量检查数据66",
          //   ExamineResult: "合格",
          //   ExaminerID: "8a0f9c1c-05d7-4968-ab7e-47c434fd78c9",
          //   ModelID: "",
          //   RealName: "薛友松",
          //   ViewpointID: "",
          //   aede_severitylevel: "一般",
          //   aedt_name: "",
          //   ec_name: "--",
          //   ifpassignoreifintime: 0,
          //   ifpassintime: 0,
          // },
        ],
        multipleSelection: [],//已选择的数据

        //KC
        showModelInfo:false,//显示模型详情
        showModelView:false,
        BIM_Session:"",//模型参数返回的Session
        ProjectId:"",
        SelModelObj:{},//选择的当前模型对象
        // //KC
        currentModelData: {},//当前打开的模型的相关数据
        modelSelectId: '全部',//下拉选中的模型id
        modelSelectList: [//下拉选中的模型id
          {
            ModelName: '全部',
            ModelID: '1'
          }
        ],
        qualityDetailsState: false,//质量详情弹窗状态
        currentExamineData: {},//当前点击的质量数据
        showingImportTemplate: false,//导入现场数据弹窗状态
        modelIframeRef: null,//模型窗口的ref对象
        authdata:[],// 整理过的权限数据
        extdata: {
          funcauthdata:undefined,// mounted 中调用接口后会赋值给 funcauthdata
        },
      }
    },

    created() {
      this.getNecessaryParameters();
      this.getQualityList();
    },

    mounted() {
      let _this = this;
      _this.setfuncauthdata(function(){
        _this.setAuthData();
      });
      console.log(this.authdata,this.extdata);
    },

    methods: {

      //导出
      ExportSceneData() {
        let selectedDataID = '';
        //拼接已选择数据的ID
        this.multipleSelection.forEach(item=>{
          selectedDataID += item.ExamineID + ',';
        });
        let examExportPara = {
          Ids: selectedDataID,
          OrganizeId: this.ajaxParams.OrganizeId,
          BIMComposerId: this.ajaxParams.BIMComposerId
        };

        new Promise((resolve,reject)=>{
          this.$axios.post(`${this.$configjson.webserverurl}/api/Examine/Exam/ExportExam`,
            this.$qs.stringify(examExportPara)
          ).then(res=>{
            if (res.data.Ret == 1) {
              //如果请求成功 返回res.data.Data，即appGuid
              resolve(res.data.Data);
            } else {
              //失败 则new一个error对象 在控制台报错
              reject(new Error(res.data.Msg));
              //失败 则弹窗提示用户
              this.$message.error(res.data.Msg);
            }
          });
        }).then(appGuid=>{
          let downloadLink = `${this.$configjson.webserverurl}/api/Examine/Exam/ExportExam_Get?appGuid=${appGuid}&OrganizeId=${this.ajaxParams.OrganizeId}&BIMComposerId=${this.ajaxParams.BIMComposerId}`;
          let aLink = document.createElement('a');
          aLink.download = '';
          aLink.style.display = 'none';
          aLink.href = downloadLink;
          document.body.appendChild(aLink);
          aLink.click();
          document.body.removeChild(aLink);
        }).catch(res=>{
          console.error(res);
        });

      },

      _hideModelInfo(){
        var _this = this;
        _this.showModelInfo=false;
        _this.IsDialogCovery=false;
      },

      openModel_Version(Model, ViewID, versionNo) {
        var _this = this;
        this._hideModelInfo();
        this.lastOpenVersionNo = versionNo;
        if (this.showModelView == true) {
          this.$refs.ref_bimviewer.changeModelUrlVersion(versionNo);
          return;
        }
      },

      // 各个功能模块页面的通用权限判断函数3 设置 authdata
      // 需要有 extdata.funcauthdata
      // 需要有 authdata
      // --------------------------------
      setAuthData: function(){
        var _this = this;
        var arr = [
          'lr_edit'
        ];
        for (var i = 0 ; i < arr.length; i++) {
          var btnname = arr[i].replace("_", "-");
          if (_this.testhasfuncbtnauth('QualityManage', btnname) == true) {
            // _this.authdata[arr[i]] = true;
            _this.$set(_this.authdata,arr[i],true);
          } else {
            // _this.authdata[arr[i]] = false;
            _this.$set(_this.authdata,arr[i],false);
          }
        }
      },

      // 各个功能模块页面的通用权限判断函数2
      // 需要有 extdata.funcauthdata
      // 需要有 authdata
      // --------------------------------
      setfuncauthdata(callback) {
        var _this = this;
        var _OrganizeId = _this.$staticmethod._Get("organizeId");
        var _Token = _this.$staticmethod.Get("Token");
        _this.$axios
          .get(
            `${window.bim_config.webserverurl}/api/User/Role/GetUserOrgFuncAuth?organizeId=${_OrganizeId}&Token=${_Token}`
          )
          .then(x => {
            if (x.status == 200) {
              if (x.data.Ret > 0) {
                if (x.data.Data) {
                  _this.extdata.funcauthdata = x.data.Data;
                  if (callback) {
                    //debugger;
                    callback();
                  }
                }
              }
            }
          })
          .catch(x => {});
      },

      // 各个功能模块页面的通用权限判断函数1
      // 需要有 extdata.funcauthdata
      // 需要有 authdata
      // -------------------------------
      testhasfuncbtnauth(bmencode, bmbencode) {
        var _this = this;
        if (!_this.extdata.funcauthdata) {
          _this.extdata.funcauthdata = JSON.parse(_this.$staticmethod.Get("funcauthdata"));
        }
        var bmIndex = _this.extdata.funcauthdata.findIndex(
          x => x.Bm_EnCode == bmencode
        );
        if (bmIndex < 0) {
          return false; // 没有找到指定bm项
        }
        var bm = _this.extdata.funcauthdata[bmIndex];
        if (bm.checkstate == "0") {
          return false; // 权限设置中，所有角色的bm设置均没有打开。
        }
        if (bm.Bmbs.length == 0) {
          return false; // 功能模块下没有按钮
        }
        var hasAuth = bm.Bmbs.findIndex(
          x => x.Roles.length > 0 && x.checkstate == "1" && x.Bmb_EnCode == bmbencode
        ); // 这个功能模块下有有角色的，且为有权限的
        return hasAuth >= 0;
      },

      //模型窗口加载完毕
      //modelIframe 模型iframe的DOM对象
      modelWindowOnload(modelIframe) {
        this.modelIframeRef = modelIframe;
        //modelIframe.BI1Me.event.BIMeEvent.finishRender.sub1scribe();

        this.$staticmethod.bimhelper_finishrender(modelIframe, () => {
          console.log(modelIframe);
          this.setAnchorCoordinatesImages(modelIframe)
          //展示锚点
          // modelIframe.BI1Me.control.BIMeUtility.addAnchorPointByPosition(
          // { x: 169.41610819937378, y: 58.54838327641112, z: 7.236944469838754 },
          // 'http://localhost:8080/static/images/interface-anchor-img.png');
        });

      },

      //设置锚点图标
      setAnchorCoordinatesImages(modelIframe) {
        
        //先清除之前遗留的锚点
        //modelIframe.BIM1e.control.BIMeUtility.clear1AllAnchorpoint();
        this.$staticmethod.bimhelper_point(modelIframe).clearAllAnchorpoint();

        let typeTest = null;
        try{
          JSON.parse(this.currentModelData.ViewpointID);
          //typeTest 为true说明ViewpointID有值，并且是json字符串
          typeTest = true;
        } catch (e) {
          typeTest = false;
        }

        if (this.currentModelData.ViewpointID != '' && typeTest){
          //点击的是哪条数据，就以该条数据为主 **start**
          let relation = JSON.parse(this.currentModelData.ViewpointID);
          //如果相机位置数据不为空，则调整视角到保存的相机位置视角
          if (relation.viewpoint != null && relation.viewpoint != '') {
            let childViewpoint = JSON.parse(relation.viewpoint);
            //以当前数据的相机位置为主
            console.log(childViewpoint);
            var tocall_getviewpointbasicinfo = this.$staticmethod.bimhelper_getview(modelIframe);
            tocall_getviewpointbasicinfo.setViewPointBasicInfo(childViewpoint);
          }
          this.setAnchorRelatedCode(relation,modelIframe);
          //点击的是哪条数据，就以该条数据为主 **end**

          let storage = [];
          this.tableData.forEach(item=>{
            //根据当前打开的模型id 遍历筛选出质量问题列表中拥有相同模型id的数据
            if (item.ModelID == this.currentModelData.ModelID) {
              let relations = JSON.parse(item.ViewpointID);
              this.setAnchorRelatedCode(relations,modelIframe);
            }
          });
        }

      },

      setAnchorRelatedCode(relation,modelIframe) {
        var _this = this;
        //是否包含有elementids
        if (relation.hasOwnProperty('elementids') && relation.elementids.length > 0 ) {
          let elementIds = [];
          //遍历 拼接字符串格式为 “模型ID^构件ID”
          relation.elementids.forEach(item => {
            let montage = `${relation.modelid}^${item}`;
            elementIds.push(montage);

            //根据构件id获取 构件的坐标
            //let elementInfo = modelIframe.BI1Me.control.BIMeUtility.getElementPosition(montage);
            let elementInfo = _this.$staticmethod.bimhelper_getElementPosition(modelIframe, montage);

            //给每个构件设置锚点,并取到每个锚点的返回值（返回值包含该锚点的dom数据）
            let anchorPointData = {};

            // 新：model.BIM365API.Extension.Point.addAnchorPointByPosition(pos,'http://www.probim.cn:8088/bimexample/img/point.png',elementid[0])
            // anchorPointData = modelIframe.BI1Me.control.BIMeUtility.addAnchorPointByPosition(
            //   { x: elementInfo.x,
            //     y: elementInfo.y,
            //     z: elementInfo.z
            //   },
            //   `${window.location.host}/static/images/interface-anchor-img.png`,
            // );

            _this.$staticmethod.bimhelper_point(modelIframe).addAnchorPointByPosition({ x: elementInfo.x,
                y: elementInfo.y,
                z: elementInfo.z
              },
              `${window.location.host}/static/images/interface-anchor-img.png`);


            //这是每个锚点的宽高
            modelIframe.document.getElementById(anchorPointData.id).style.width ='30px';
            modelIframe.document.getElementById(anchorPointData.id).style.height ='30px';
          });

          //高亮显示构件
          //modelIframe.BI1Me.control.BI1MeZoom.zoomE1lementByElementId(elementIds);
          _this.$staticmethod.bimhelper_getzoomer(modelIframe).zoomElementByElementId(elementIds);

          //modelIframe.B1IMe.view.BIMeSelection.isShowMultipleSelection(false);
          console.error('暂不支持 isShowMultipleSelection');

        }
      },

      clearCurrentData() {
        this.modelSelectList = {
          ModelName: '全部',
          ModelID: '1'
        };
        this.tableData = [];
        this.multipleSelection = [];
        this.getQualityList();
      },

      //点击导入按钮
      _onimportok(fileinput) {

        let files = fileinput.files;
        if (files.length == 0) {
          this.$message.error('请先选择导入文件');
          return;
        }

        let fd = new FormData();
        fd.append("OrganizeId", this.$staticmethod._Get("organizeId"));
        fd.append("Token", this.$staticmethod.Get("Token"));
        fd.append("ExamFile", files[0]);

        this.$axios.post(`${this.$configjson.webserverurl}/api/Examine/Exam/ImportExam`,fd).then(res=>{
          if (res.data.Ret > 0) {
            this.$message.success('导入成功');
            this.showingImportTemplate = false;
            this.$emit('updateComponentsKey');
            // this.clearCurrentData();
          } else {
            this.$message.error(res.data.Msg);
          }
        }).catch(res=>{
          console.error(res);
        })
      },

      //下载模板
      _ontemplate() {
        let _organizeId = this.$staticmethod._Get("organizeId");
        window.location.href = `${this.$configjson.webserverurl}/api/Examine/Exam/DownloadProjectExamTemplate?organizeId=${_organizeId}`;
      },

      //显示问题详情
      showQualityDetails(row) {
        this.qualityDetailsState = true;
        this.currentExamineData = row;
      },

      //关闭问题详情弹窗
      closeDtailsDialog() {
        this.qualityDetailsState = false;
      },

      viewInModel(data) {
        // this.showModelView = true;
        console.log(data);

        //如果即将打开的模型与已经打开的模型相同，则直接定位到对应的锚点
        if (this.currentModelData.ModelID == data.ModelID) {
          this.setAnchorCoordinatesImages(this.modelIframeRef)
        } else {
          //如果即将打开的模型跟已经打开的模型不同 则重新请求数据
          this.preopenmodel(data.ModelID);
        }
        //保存当前打开的模型 相关数据
        this.currentModelData = data;
      },

      // KC
      preopenmodel(modelId){
        console.assert(this.ajaxParams.BIMComposerId, "_ProjectID 错误");
        this.$axios.get(`${
          this.$staticmethod.getBIMServer()
        }/api/Prj/GetModel?ProjectID=${this.ajaxParams.BIMComposerId}&ModelID=${modelId}`)
          .then(x => {
            console.assert(x.status == 200, `请求服务器失败：${x.status}`);
            console.log(x);
            this.SelModelObj = x.data;
            this.openModel(this.SelModelObj, '', this.SelModelObj.Name);
          });
      },
      onmodelview_close(){
        var _this = this;
        _this.showModelView=false;
        _this.showModelInfo=false;
        //_this.IsDialogCovery=false;
        setTimeout(function(){
          _this.$staticmethod._Set("needpausegetmsg", 0);
        }, 1000);
      },
      openModelInfo(item) {
        this.showModelInfo=true;
        this.CurrentModelId='-1';
        this.SelModelObj=item;
        this.IsDialogCovery=true;
      },
      setTranIndex(val) {
        this.tranIndex=val;
      },
      // //KC

      openModel(Model,ViewID) {



        // 先获取当前人的权限数据
        //console.log(window.projectbootvue.extdata.funcauthdata);

        // 判断有无浏览模型权限
        var _authindex = window.projectbootvue.extdata.funcauthdata.findIndex(x => x.checkstate == '1' && x.Bm_EnCode == "BIMModel");
        if (_authindex < 0) {
          this.$message({type:"error",message:"缺少浏览模型的权限"});
        }

        //debugger;
        var ModelId=Model.ModelID;
        var session_ProjectId = this.$staticmethod._Get("bimcomposerId");
        var session_ProjectBim365Id = this.$staticmethod._Get("organizeId");
        var session_Account = this.$staticmethod.Get("Account");
        var session_UserNameCN = this.$staticmethod.Get("RealName");
        var trans_Roles=[];
        var _this=this;
        this.ProjectId=session_ProjectId;
        this.SelModelObj=Model;
        this.LoadShareUrl=false;
        var _ProjectRoles = window.projectbootvue.extdata.funcauthdata;
        for(var i=0;i<_ProjectRoles.length;i++){
          trans_Roles.push({"ModuleCode":_ProjectRoles[i].Bm_EnCode,"ModuleName":_ProjectRoles[i].Bm_FullName,"Accesses":[]});
          var items=_ProjectRoles[i].Bmbs;
          for(var j=0;j<items.length;j++)
          {
            trans_Roles[i].Accesses.push({"ModuleButtonCode":items[j].Bmb_EnCode,"ModuleButtonName":items[j].Bmb_FullName});
          }
        }
        var workflow=[];//流程数据给空
        //视点类型、批注类型 先一起执行，再post这些数据
        this.GetCategory(function(a,b){
          var params={};
          params["ProjectID"]=session_ProjectId;
          params["BIM365ProjectID"]=session_ProjectBim365Id;
          params["ProjectType"]="Activity";
          params["ModelID"]=ModelId;
          params["VersionNO"]="";
          params["ViewpointID"]="";
          params["Snapshot"]="false";
          params["Texture"]="true";
          params["UserName"]=session_Account;
          params["UserNameCN"]=session_UserNameCN;
          params["ViewID"]=ViewID;
          params["Access"]=trans_Roles;
          params["MarkupCategory"]=a;
          params["ViewpointCategory"]=b;
          params["Workflow"]=[];
          //debugger;
          //console.log(params);
          var formData=new FormData();
          formData.append("Params",JSON.stringify(params));
          _this.$axios.post(_this.$urlPool.base_ModelApi+_this.$urlPool.PostModelParams,formData).then(res=>{
            _this.BIM_Session=res.data;
            //debugger;
            _this.showModelView=true;
          });
        });
        //打开bimViewer
      },

      GetCategory(CallBack) {
        var token=this.$staticmethod.Get("Token");
        var _this=this;
        this.$axios.all([
          _this.$axios.get(_this.$urlPool.base_MainSystemApi+_this.$urlPool.GetDataItemListJson+"?queryJson=" + JSON.stringify({ EnCode: "MarkupCategory", Category: "2" })+"&Token="+token),
          _this.$axios.get(_this.$urlPool.base_MainSystemApi+_this.$urlPool.GetDataItemListJson+"?queryJson=" + JSON.stringify({ EnCode: "ViewpointCategory", Category: "2" })+"&Token="+token),
        ]).then(_this.$axios.spread(function (one, two) {
          // 两个请求现在都执行完成
          var result1=one.data.Data;
          var result2=two.data.Data;
          CallBack(result1,result2);
        }));
      },

      //删除已选择的质量问题数据
      deleteSelectQualityItems() {
        //lr_edit为false 为无权限
        if (!this.authdata.lr_edit) {
          return false;
        }

        this.$confirm('是否删除所选数据?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          let storage = [];
          let params = {
            Token: this.ajaxParams.Token,
            Ids: ''
          };

          this.multipleSelection.forEach(item=>{
            storage.push(item.ExamineID);
          });
          params.Ids = storage.join(',');

          this.$axios.post(`${this.$configjson.webserverurl}/api/Examine/Exam/RemoveItems`,
            this.$qs.stringify(params)).then(res=>{
            if (res.data.Ret == 1) {
              this.multipleSelection.forEach(item=>{
                this.filterTableData.forEach((data,index,arr)=>{
                  if(item.ExamineID == data.ExamineID) {
                    arr.splice(index,1);
                  }
                });
              });
              this.$message.success('删除成功');
              this.multipleSelection = [];
            } else {
              this.$message.error('删除失败，请刷新再试');
            }
          }).catch(res=>{
            console.log(res);
          });
        }).catch(() => {});
      },

      //获取相关参数
      getNecessaryParameters() {
        this.ajaxParams.BIMComposerId = this.$staticmethod._Get("bimcomposerId");
        this.ajaxParams.Token = this.$staticmethod.Get("Token");
        this.ajaxParams.OrganizeId = this.$staticmethod._Get("organizeId");
      },

      //获取质量安全问题列表
      getQualityList() {
        this.$axios.post(`${this.$configjson.webserverurl}/api/Examine/Exam/GetItems`,
          this.$qs.stringify({BIMComposerId:this.ajaxParams.BIMComposerId})).then(res=>{
            if (res.data.Ret == 1) {
              this.tableData = res.data.Data.List;
              let storage = [];
              storage = this.arrayUniquePublic(this.tableData,'ModelID');
              storage.forEach(item=>{
                if (item.ModelName == '') {
                  item.ModelName = '默认模型';
                }
                this.modelSelectList.push(item);
              });

            } else {
              this.$message.error('列表数据获取失败，请稍后再试');
            }
        }).catch(res=>{
          console.warn(res);
        })
      },

      //对象数组去重
      arrayUniquePublic(arr,key){
        let hash = {};
        return arr.reduce(function(item, next) {
          if (next[key] != '') {
            hash[next[key]] ? "" : (hash[next[key]] = true && item.push(next));
          }
          return item;
        }, []);
      },

      handleTableTopBarCheckAllChange(val) {
        // console.log(val)
        this.multipleSelection = val ? this.filterTableData : [];
        this.tableTopBarState.isIndeterminate = false;
        this.$refs.multipleTable.toggleAllSelection();
      },

      //全选/全不选
      handleSelectionChange(val) {
        this.multipleSelection = val;
        console.log(val);

        //设置第二表头的全选框状态
        let checkedCount = val.length;
        this.tableTopBarState.checkAll = checkedCount === this.filterTableData.length;
        this.tableTopBarState.isIndeterminate = checkedCount > 0 && checkedCount < this.filterTableData.length;
      },

      //打开/关闭 设置页面
      toggleSetting() {
        this.showSetting = !this.showSetting;
        //将状态传给父级
        this.$emit('stateToggle',this.showSetting);
      },

      // //关闭设置页面内
      // qualityManageSetClose() {
      //   this.showSetting = !this.showSetting;
      // }
    },

    filters: {
      setRectification(row) {
        if (row.ExamineResult == '合格') {
          return '<span class="state-color pass">整改完成</span>';
        } else {
          if (row.ifpassintime == 1) {
            return '<span class="state-color pass">整改完成</span>';
          } else {
            if (row.ifpassignoreifintime == 1) {
              return '<span class="state-color noPass">逾期</span>';
            } else {
              return '<span class="state-color needToDo">待整改</span>';
            }
          }
        }
        // ExamineResult == '合格' ? 整改完成 : (ifpassintime == 1? '整改完成': (ifpassignoreifintime == 1?'逾期':'待整改'))
      },

      setExamineDate(val) {
        let storage = val.replace(/T/,' ');
        return storage;
      }
    },

    computed: {
      setSeverityLevelClass() {
        return function (val) {
          return {
            'grade-color slight': val == '轻微',
            'grade-color normal': val == '一般',
            'grade-color serious': val == '严重',
          }
        }
      },

      filterTableData() {
        let tableData = [];
        if (this.modelSelectId == '全部' || this.modelSelectId == '1') {
          tableData = this.tableData;
        } else {
          this.tableData.forEach(item => {
            if (item.ModelID == this.modelSelectId ) {
              tableData.push(item);
            }
          })
        }

        let searchData = []
        tableData.forEach(item => {
          if ((item.ExamineRemark.toLowerCase()).search((this.searchCheckDetails.toLowerCase())) != -1) {
            searchData.push(item);
          }
        });

        return searchData;
      },

      ProhibitionbuttonClickClass() {
        if (!this.authdata.lr_edit) {
          return 'function-btn-prohibition';
        }
      }
    }
  }
</script>

<style>
  .quality-list .list-container .el-table,
  .quality-list .list-container .el-table__expanded-cell {
    background-color: rgba(0,0,0,0);
  }

  .quality-list .list-container .el-table th,
  .quality-list .list-container .el-table tr {
    background-color: #FFFFFF;
  }

  .quality-list .list-container .el-table .el-table__row .cell {
    justify-content: center;
  }

  .quality-list .list-container .el-table td {
    border-color: #EBEEF5;
  }

  .quality-list .list-container .el-table .has-gutter .gutter {
    background-color: rgb(240, 242, 245);
  }

  /*等级 颜色*/
  .quality-list .list-container .grade-color.slight {
    color: #9B9B9B;
  }

  .quality-list .list-container .grade-color.normal {
    color: #1890FF;
  }

  .quality-list .list-container .grade-color.serious {
    color: #F5222D;
  }

  /*状态 颜色*/
  .quality-list .list-container .state-color.pass {
    color: #1DA48C;
  }

  .quality-list .list-container .state-color.noPass {
    color: #F5222D;
  }

  .quality-list .list-container .state-color.needToDo {
    color: #FAAD14;
  }

  .quality-list .menu-item li .el-input__inner {
    border-radius: 4px;
    padding: 0 35px 0 15px;
    background-color: #FFF;
  }
</style>

<style scoped>
  .css-prel {
    height: 100%;
  }

  .quality-list {
    height: 100%;
  }

  .quality-list.quality-list-response {
    width: 550px;
  }

  .quality-list .top-menu {
    display: flex;
    justify-content: space-between;
    margin-bottom: 20px;
  }

  .quality-list .title {
    font-size: 16px;
    font-weight: 500;
  }

  .quality-list .title .icon {
    font-size: 25px;
    vertical-align: middle;
  }

  .quality-list .menu-item {
    display: flex;
    align-items: center;
    font-weight: 500;
    color: rgba(0,0,0,0.45);
  }

  .quality-list .menu-item li {
    cursor: pointer;
  }

  .quality-list .menu-item.menu-left li {
    margin-right: 25px;
  }

  .quality-list .menu-item.menu-right li {
    margin-left: 25px;
  }

  .quality-list .menu-item li .icon {
    vertical-align: text-bottom;
    margin-right: 5px;
  }

  .quality-list .menu-item li .desc {
    color: rgba(0,0,0,0.25);
  }

  .quality-list .menu-item li:hover {
    opacity: 0.8;
  }

  span.model-name-hide {
    max-width: 200px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    display: inline-block;
  }

  .quality-list .list-container {
    position: relative;
    height: calc(100% - 45px);
  }

  .quality-list .list-container .table-top-bar {
    position: absolute;
    top: 0;
    left: 0;
    height: 50px;
    width: 100%;
    background-color: #FFFFFF;
    z-index: 10;
    text-align: left;
    padding-left: 91px;
    box-sizing: border-box;
  }

  .quality-list .list-container .longDesc {
    overflow-x: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  /*model-window*/
  .model-window {
    position: absolute;
    top: -22px;
    right: -20px;
    width: calc(100% - 550px);
    height: calc(100% + 45px);
  }

  .mi {
    width: 869px;
    height: 420px;
    position: absolute;
    top: 50%;
    left: 50%;
    -webkit-transform: translate(-50%, -50%);
    transform: translate(-50%, -50%);
    z-index: 97;
  }

  .quality-list .function-btn-prohibition {
    cursor: not-allowed !important;
    opacity: 0.5 !important;
  }
</style>
