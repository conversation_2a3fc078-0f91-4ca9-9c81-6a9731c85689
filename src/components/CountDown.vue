<template>
  <div class="_css-all-ProjectAcceptInvite">
    <div class="_css-form-PAI">
      <div class="_css-form-PAI-inner">

<div class="_css-title" >
    <div class="_css-title-inner " >
        <div class="_css-title-icon icon-interface-lock" ></div>
        <div class="_css-title-text">
            <div>{{leftseconds}}{{'秒后跳转到登录页'}}</div>
        </div>
    </div>
</div>

      
      

        <div class="_css-btnok">
          <CompsEloButton
            @onclick="btnok_click"
            :disabled="isbtndisabled"
            :text="'立即跳转'"
            :color="'#fff'"
            :fontSize="12"
            :width="352"
            :height="40"
          ></CompsEloButton>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import CompsUsersInput from "@/components/CompsAuth/CompsUsersInput";
import CompsEloButton from "@/components/CompsElOverride/CompsEloButton";

export default {
  data() {
    return {
        passwordsettinginfo:{
            pwd:'',
            pwd2:''
        },
        newaccount: false // 为true时，显示密码输入框
        ,isbtndisabled: false
        , leftseconds: 5
        , intervalPtr:0
    };
  },
  mounted(){
      var _this = this;
      _this.intervalPtr = setInterval(()=>{
          _this.leftseconds--;
          if (_this.leftseconds <= 0) {
            clearInterval(_this.intervalPtr);
            window.location.href = `${window.bim_config.hasRouterFile}/#/`;
          }
      }, 1000);
  },
  methods: {
    _pwd(str) {
        var _this = this;
        _this.passwordsettinginfo.pwd = str;
    },
    _pwd2(str) {
        var _this = this;
        _this.passwordsettinginfo.pwd2 = str;
    },
    btnok_click() {
        var _this = this;
        window.location.href = `${window.bim_config.hasRouterFile}/#/`;
    }
  },
  components: {
    CompsUsersInput,
    CompsEloButton
  }
};
</script>
<style scoped>
._css-title-inner{
    display: flex;
    align-items: center;
    justify-content:center;

}
._css-title{
    height:28px;
    margin-top:46px;
    color:rgba(0,0,0,0.85);
    font-size: 20px;
}
._css-title-icon{
    height:20px;
    width:20px;
    color:#1890FF;
}
._css-title-text{
    margin-left: 8px;
    display: flex;
    align-items: center;
}
._css-pwd {
    margin-top: 28px;
}
._css-pwd2 {
    margin-top: 24px;
}
._css-btnok{
    margin-top: 32px;
}
._css-form-PAI {
  width: 400px;
  background: rgba(255, 255, 255, 1);
  box-shadow: 0px 13px 24px -17px rgba(11, 41, 62, 0.8);
  border-radius: 2px;
  min-height: 48px;
  padding-bottom: 50px;
  user-select: none;
}
._css-all-ProjectAcceptInvite {
  display: flex;
  align-items: center;
  justify-content: space-around;
  background: rgba(240, 242, 245, 1);
  height: 100%;
  width: 100%;
}
._css-form-PAI-inner {
  margin-left: 24px;
  margin-right: 24px;
}
</style>