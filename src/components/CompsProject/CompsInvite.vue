<template>
    <div class="_css-all-compsinvite"
    :style="styleobj"
    @click="hideall()"
    >

        <div 
         class="_css-front" v-drag="greet"  :style="style" >

            <!-- title 组件 -->
            <CompsDialogHeader
            @oncancel="_oncancel"
             :title="'邀请成员进入项目'"></CompsDialogHeader>
            <!-- //title 组件 -->

            <!-- 中间动态区域 -->
            <div class="_css-middle-compsinvite" >

                <!-- 说明文字 -->
                <div class="_css-invite-selects-description">
                    <!-- 角色下拉框占位：说明 -->
                    <div class="_css-invite-role css-fc" >
                       <div class="_css-invite-role-desc css-w100">角色</div>
                    </div>
                    <!-- //角色下拉框占位 -->

                    <!-- 邮箱输入框占位：说明 -->
                    <div class="_css-invite-emailinput css-fc" >
                      <div class="_css-invite-emaildesc css-w100">被邀请邮箱地址</div>
                    </div>
                    <!-- //邮箱输入框占位 -->
                </div>
                <!-- //说明文字 -->

                 <!-- 角色下拉框及邮箱地址输入框 -->
                <div class="_css-invite-rolesselandemailinput">

                    <!-- 角色下拉框占位 -->
                    <div class="_css-invite-role" >
                        <CompsRoleSelect 
                        :ref="'mainSel'"
                        :text="toinviteitem_default.text"
                        :selectedId="toinviteitem_default.selectedId"
                        :indexnumber="-1"
                        @onselected="_onselected"
                        :datas="rolespool" 
                    
                        :innerwidth="'92px'"
                        :width="'140px'"
                        ></CompsRoleSelect>
                    </div>
                    <!-- //角色下拉框占位 -->

                    <!-- 邮箱输入框占位 -->
                    <div class="_css-invite-emailinput" >
                        <CompsUsersInput   
                        :ref="'mainInput'"
                            :init_isemails="true"
                            @oninput="_oninput($event, -1, 'mainInput')"
                            :indexnumber="-1"
                            :placeholder='"请输入英文逗号分隔的多个邮箱地址"'
                            :iconclass='"icon-interface-email"'
                            :is100percent="true"
                        ></CompsUsersInput>
                    </div>
                    <!-- //邮箱输入框占位 -->

                    <div class="_css-rightbtn   " @click.stop="addinviteobject" >
                        <div class="_css-rightbtn-inner icon-suggested-plus_square basic-btn-normal-color"></div>
                    </div>

                </div>
                <!-- //角色下拉框及邮箱地址输入框 -->

                <!-- 已经选中了角色及输入了邮箱地址 -->
                <div class="_css-invite-rolesselandemailinput" v-for="(toinviteitem,index) in toinviteitems" :key="toinviteitem.number">
                    <div class="_css-invite-role" >
                        <CompsRoleSelect 
                          :ref="'theSel' + toinviteitem.number"
                        :text="toinviteitem.text"
                        :selectedId="toinviteitem.selectedId"
                        :indexnumber="toinviteitem.number"
                        @onselected="_onselected"
                        :datas="rolespool" 
                       
                        :innerwidth="'92px'"
                        :width="'140px'"
                        ></CompsRoleSelect>
                    </div>
                    <div class="_css-invite-emailinput" >
                        <CompsUsersInput
                         :ref="'theInput' + toinviteitem.number"
                            @oninput="_oninput($event, index, 'theInput' + toinviteitem.number)"
                            :init_isemails="true"
                            :indexnumber="toinviteitem.number"
                            :placeholder='"请输入英文逗号分隔的多个邮箱地址"'
                            :iconclass='"icon-interface-email"'
                            :is100percent="true"
                        ></CompsUsersInput>
                    </div>
                    <div class="_css-rightbtn  " @click.stop="removeinviteobj(toinviteitem.number)">
                        <div class="_css-rightbtn-inner icon-suggested-minus_square basic-btn-normal-color "></div>
                    </div>
                </div>
                <!-- //已经选中了角色及输入了邮箱地址 -->

               

            </div>
            <!-- //中间动态区域 -->

            <!-- 底部按钮组件 -->
            <!-- <CompsDialogBtns></CompsDialogBtns> -->
                 <CompsDialogBtns
            @onok="_onok"
            @oncancel="_oncancel"
            :oktext="'邀请'"
            ></CompsDialogBtns>
            <!-- //底部按钮组件 -->

        </div>

    </div>
</template>
<script>
/*
input:
    
events:
    onok(toinviteitem_default, toinviteitems)
*/
import CompsDialogHeader from '@/components/CompsDialog/CompsDialogHeader'
import CompsRoleSelect from '@/components/CompsProject/CompsRoleSelect'
import CompsDialogBtns from '@/components/CompsDialog/CompsDialogBtns'
import CompsUsersInput from '@/components/CompsAuth/CompsUsersInput'
export default {
    data(){
        return {
            val:'123',
            style: {
                position: 'fixed',
                right: 'calc(50% - 300px)',
                top: 'calc(50% - 115px)'
            },
            rolespool:[] // 可选择的角色池
            ,toinviteitem_default:{
                selectedId:'',
                text:'请选择角色',
                email: ''
            } // 预设的邀请的数据条目
            ,toinviteitems:[] // 将要邀请的数据条目
            ,toinviteitemnewnumber:0 // 新invite数据的number
        }
    },
    computed:{
        styleobj:{
            get(){
                var _this = this;
                if (_this.zIndex) {
                    return {
                        'z-index':_this.zIndex < 1000?1000:_this.zIndex
                    };
                } else {
                    return {
                        'z-index': 1000
                    };
                }
            }
        }
    },
    props:{
        zIndex:{
            type:Number,
            required:false
        }
    },
    components:{
        CompsDialogHeader,
        CompsDialogBtns,
        CompsRoleSelect,
        CompsUsersInput
    },
    mounted(){

        // 设置 rolespool
        var _this =this;
        window.vueobj = _this;
        var _Token = _this.$staticmethod.Get("Token");
        var _OrgId = _this.$staticmethod._Get("organizeId");
       

        // 请求
        _this.$axios({
            method: 'get',
            url: `${_this.$configjson.webserverurl}/api/User/Role/GetRoles?organizeId=${_OrgId}&token=${_Token}`
        }).then(x => {
            if (x.status != 200) {
                _this.$message({
                    message: '服务器错误',
                    type: 'error'
                });
                _this.$staticmethod.debug(x);
            } else if (x.data.Ret < 0) {
                _this.$message({
                    message: x.data.Msg,
                    type: 'error'
                });
                _this.$staticmethod.debug(x);
            } else if (x.data.Data) {
                _this.rolespool = x.data.Data;
            }
        }).catch(x => {
            _this.$message({
                message: '服务器错误',
                type: 'error'
            });
            _this.$staticmethod.debug(x);
        });

    },
    methods:{

        // 得到全部的 input 组件
        // -------------------
        allInputs(){
            var _this = this;
            var arrinput = [];
            arrinput.push(_this.$refs["mainInput"]);
            var i = 0;
            for (i = 0; i < _this.toinviteitems.length; i++) {
                arrinput.push(_this.$refs["theInput" + _this.toinviteitems[i].number][0]);
            }
            return arrinput;
        },

        // 关闭全部
        hideall(){
            var _this = this;

            // 得到所有的ref的sel组件
            var arrsel = [];
            arrsel.push(_this.$refs["mainSel"]);
            // var i = 0; 
            // for (i = 0; i < _this.toinviteitems.length; i++) {
            //     arrsel.push(_this.$refs["theSel" + i][0]);
            // }

            // // 全部关闭！
            // for (i = 0; i < arrsel.length; i++) {
            //     arrsel[i].closeoption();
            // }

            // 遍历  toinviteitems ，按照 number 进行 closeoption
            // -------------------------------------------------
            _this.$refs.mainSel.closeoption();
            for (var i = 0; i < _this.toinviteitems.length; i++) {
                var num = _this.toinviteitems[i].number;
                _this.$refs["theSel" + num][0].closeoption();
            }
            
        },

        greet(val){
            var _this = this;
            _this.val = val;
        },

        removeinviteobj(number){
            var _this = this;
            _this.toinviteitems = _this.toinviteitems.filter(x => x.number != number);
        },

        addinviteobject(){
            var _this = this;
            if (_this.toinviteitems.length < 4) {
                _this.toinviteitems.push({
                    number: _this.toinviteitemnewnumber,
                    selectedId: '',
                    text:'请选择角色',
                    email: ''
                });
                _this.toinviteitemnewnumber++;
            }
        },
        _onselected(role, index){

            // 原有的用到的 theinviteitem 逻辑
            // ------------------------------
            var _this = this;
            //debugger;
            if (index == -1) {
                _this.toinviteitem_default.text = role.FullName;
                _this.toinviteitem_default.selectedId = role.RoleId;
            } else {
                // _this.toinviteitems[index].text = role.FullName;
                // _this.toinviteitems[index].selectedId = role.RoleId;
                var theinviteitem = _this.toinviteitems.filter(x => x.number == index)[0];
                theinviteitem.text = role.FullName;
                theinviteitem.selectedId = role.RoleId;
            }

            // 新的处理，将 selectedId 赋予给 input 组件内部
            // -------------------------------------------
            if (index == -1) {

                // 默认的第一个文本框
                // ----------------
                _this.$refs["mainInput"].func_setselectedId(role.RoleId);

            } else {

                // 其它文本框
                // ---------
                (_this.$refs["theInput" + index][0]).func_setselectedId(role.RoleId);
            }
            
        },

        // 记录某一个 inviteitem 是否包含重复的邮箱
        // -------------------------------------
        func_testThisItemHasRepeat(refname) {
            var _this = this;
            var vueobj = _this.$refs[refname][0] || _this.$refs[refname];
            var dataOfThis = vueobj.func_getdata();
            //console.log(dataOfThis);

            // 从 dataOfThis 中取出所有邮箱
            // ---------------------------
            var allemail = [];
            var emailarr = dataOfThis.inputarr.map(x => x.Email.toLowerCase());
            var emailarr2 = dataOfThis.inputtext.toLowerCase().split(',');
            allemail = emailarr.concat(emailarr2);
            var set1 = new Set(allemail);
            if (set1.size < allemail.length) {
                //console.log('有重复');
                _this.$message.warning(`邮箱地址重复，当前填写的内容为${dataOfThis.inputtext}`);
                return true;
            } else {
                //console.log('无重复');
                return false;
            }

        },

        // 项目中邀请成员时，在 input 中输入邮箱地址时的回调
        // ---------------------------------------------
        _oninput(str, index, refname){
            
            var _this = this;
            //console.log(`str=${str}, index=${index}`);
            if (index == -1) {
                _this.toinviteitem_default.email = str;
                _this.toinviteitem_default.hasrepeat = _this.func_testThisItemHasRepeat(refname);
            } else {

                //var theinviteitem = _this.toinviteitems.filter(x => x.number == index)[0];
                // -------------------------------------------------------------------------
                var theinviteitem = _this.toinviteitems[index];
                theinviteitem.email = str;
                theinviteitem.hasrepeat = _this.func_testThisItemHasRepeat(refname);
            }
            
        },
        _onok(){
            var _this = this;

            // 判断邮箱地址是否有重复的
            // ----------------------
            if (_this.toinviteitem_default.hasrepeat) {
                _this.$message.warning(`邮箱地址重复，请修改后重新操作。填写内容为：${_this.toinviteitem_default.email}`);
                return;
            }
            for (var i = 0; i < _this.toinviteitems.length; i++) {
                if (_this.toinviteitems[i].hasrepeat) {
                    _this.$message.warning(`邮箱地址重复，请修改后重新操作。填写内容为：${_this.toinviteitems[i].email}`);
                    return;
                }
            }

            var allInputs = _this.allInputs();
            //var toEmitFirst = {};
            var toEmitArr = [];
            var i = 0;
            for (i = 0; i < allInputs.length; i++) {
                var data = allInputs[i].func_getdata();

                // 如果没有选择角色，直接跳过
                // -----------------------
                if (data.selectedId == '') {
                    continue;
                }

                // 如果没有人员，直接跳过
                // --------------------
                var emails = data.inputtext.split(',');
                emails = emails.filter(x => x && x.trim() != '');
                if (data.inputarr.length == 0 && emails.length == 0) {
                    continue;
                }

                //debugger;

                // 从数组中取
                // ---------
                var j = 0;
                if (data.inputarr.length > 0) {
                    for (j = 0; j < data.inputarr.length; j++) {
                        toEmitArr.push({
                            selectedId: data.selectedId,
                            email: data.inputarr[j].Email
                        });
                    }
                }

                // 从文本中取
                // ---------
                if (emails.length > 0) {
                    for (j = 0; j < emails.length; j++) {
                        toEmitArr.push({
                            selectedId: data.selectedId,
                            email: emails[j]
                        });
                    }
                }
            }

            /*
                RoleId: arr_role_emails[i].selectedId,
            Email: arr_role_emails[i].email
            */
            _this.$emit("onok", {}, toEmitArr);
        },
        _oncancel(){
            var _this = this;
            _this.$emit("oncancel");
        }
    }
}
</script>
<style scoped>

._css-invite-role-desc{
    font-size:14px;
    text-align: left;
    padding-left:12px;
    color:rgba(0,0,0,0.65);
}

._css-invite-emaildesc {
    font-size:14px;
    text-align: left;
    padding-left:8px;
    color:rgba(0,0,0,0.65);
    text-overflow: ellipsis;
    overflow-x: hidden;    
    width:100%;
}

._css-invite-selects-description{
    margin-top:24px;
    box-sizing: border-box;
    display: flex;
    padding-left:16px;
    padding-right:16px;
}

._css-rightbtn {
    width: 20px;
    height: 20px;
    margin-left: 4px;
    display: flex;
    align-items: center;
    justify-content: space-around;
    cursor: pointer;
}

._css-rightbtn-inner{
    font-size: 18px;
}

._css-invite-role{
    width:140px;
    height:32px;
    box-sizing:border-box;
    display:flex;
    cursor:pointer;
}

._css-invite-emailinput{
    min-height: 32px;
    width:calc(100% - 92px - 16px);
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    /* border: 1px solid transparent; */
    -webkit-box-flex: 1;
    -ms-flex: 1;
    flex: 1;
    margin-left: 8px;

    display: flex;
    align-items: center;
}

._css-invite-rolesselandemailinput{
    margin-top:12px;
    padding-left:16px;
    padding-right:16px;
    box-sizing: border-box;
    /* height:32px; */
    display: flex;
    align-items: center;
}

._css-middle-compsinvite {
    /* height: 120px; */
    border: 1px solid transparent;
    box-sizing: border-box;
    margin-bottom: 32px;
    overflow-y: auto;
    max-height: 282px;
}

._css-middle-compsinvite::-webkit-scrollbar {/*滚动条整体样式*/
  width: 6px;     /*高宽分别对应横竖滚动条的尺寸*/
  height: 6px;
}
._css-middle-compsinvite::-webkit-scrollbar-thumb {/*滚动条里面小方块*/
  border-radius: 5px;
  -webkit-box-shadow: inset 0 0 5px rgba(0,0,0,0.2);
  background: rgba(0,0,0,0.2);
}
._css-middle-compsinvite::-webkit-scrollbar-track {/*滚动条里面轨道*/
  -webkit-box-shadow: inset 0 0 5px rgba(0,0,0,0.2);
  border-radius: 5px;
  background: rgba(0,0,0,0.1);
}

._css-front{
    width:600px;
    min-height:40px;
    background-color:rgba(255,255,255,1);
    box-shadow:0px 13px 24px -17px rgba(11,41,62,0.8);
    border-radius:4px;
}
._css-all-compsinvite{
    position:fixed;
    display: flex;
    align-items: center;
    justify-content: space-around;
    /* z-index:1; */
    width:100%;
    height:100%;
    left:0;
    top:0;
    background-color: rgba(0,0,0,0.3);
}
</style>