<template>
  <div class="_css-panoshare-all" ref="krpanoWrapper">
    <!-- 时间轴 -->
    <div v-if="isTimelineVisible" class="_css-panoshowing-timeline">
      <div class="timeline-header">
        <div class="title">时间轴</div>
        <div
          class="tail"
          @click.stop="isDotsRelatedVisible = !isDotsRelatedVisible"
        >
          <div class="dot"></div>
          <div class="dot"></div>
          <div class="dot"></div>
        </div>
      </div>
      <el-scrollbar
        @hook:updated="onElScreenUpdated"
        ref="scrollbar"
        style="height: auto"
      >
        <el-timeline class="timeline-content" :reverse="reverse">
          <el-timeline-item
            v-for="(item, index) in sceneList"
            :key="index"
            class="css-panoshowing-lineitem"
            :color="
              item.PsScenename === currentPsSceneName ? '#409eff' : '#fff'
            "
            :class="{
              'css-panoshowing-lineitemselected':
                item.PsScenename === currentPsSceneName,
            }"
            :timestamp="item.PsCollecteddatetime"
            :hide-timestamp="isOnlyShowTimestamp"
          >
            <div class="_css-timelinetitle" @click="onTimelineItemClick(item)">
              <template v-if="isOnlyShowTimestamp">{{ item.PsCollecteddatetime }}</template>
              <template v-else>{{ item.PchChname }}</template>
            </div>
          </el-timeline-item>
        </el-timeline>
      </el-scrollbar>
      <div v-if="isDotsRelatedVisible" class="timeline-dots-related">
        <div class="content-wrapper">
          <div class="item">
            <div>自动播放</div>
            <el-radio-group
              :disabled="isAutoPlayingKrpano"
              class="playOrder"
              v-model="krpanoPlayOrder"
            >
              <el-radio :label="1">正序</el-radio>
              <el-radio :label="2">倒序</el-radio>
            </el-radio-group>
          </div>
          <div class="item">
            <el-input-number
              :disabled="isAutoPlayingKrpano"
              placeholder="播放间隔"
              class="playInterval"
              size="small"
              v-model="krpanoPlayInterval"
              :min="1"
              controls-position="right"
            ></el-input-number>
            <div class="btnPlay" @click="togglePlayKrpano">
              <div class="icon" :class="iconClassForPlayKrpano"></div>
              <div class="label">{{ labelForPlayKrpano }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- 加载标志 -->
    <div v-if="isLoadingKrpano" class="_css-loading-krpano">
      <div class="content">Loading...</div>
    </div>
    <div id="krpanoView" class="_css-krpano-view"></div>
  </div>
</template>

<script>
import { debounce } from "throttle-debounce";
export default {
  name: "Krpano",
  props: {
    PbGuid: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      observerForKrpanoWrapper: null,
      reverse: false,
      isTimelineVisible: false, //时间轴是否可见
      isKrpanoLibLoaded: false, //krpano库是否已加载
      krpanoInterfaceObj: null, // 实例化的krpano对象引用
      isLoadingKrpano: false, // 是否在加载krpano图集
      sceneList: [], // GetScenesByPbGuid接口的响应
      currentPsSceneName: "", // krpano原本显示的名字
      currentPsRealName: "", // 当前的真实名字
      krpanoPlayOrder: 1, // 全景图集播放顺序 1正序 2倒序
      krpanoPlayInterval: 2, // 全景图集播放间隔，秒
      isDotsRelatedVisible: false, // 三个点相关的dom结构是否可见
      isAutoPlayingKrpano: false, // 是否在自动播放全景图
      timerForAutoPlayingKrpano: null, // 自动播放全景图的定时器
      orderedTimelineItems: [], // 按照krpanoPlayOrder排序的items
      isKrpanoXmlError: false, // krpano的xml下载或者解析是否错误

      dataFromUrlQuery: {} // 来自地址栏的查询参数
    };
  },
  computed: {
    localPbGuid() {
      return (this.PbGuid || this.$route.params.PanoId) + "";
    },
    PbUrl() {
      return this.localPbGuid.substring(0, 8);
    },
    labelForPlayKrpano() {
      return this.isAutoPlayingKrpano ? "停止" : "播放";
    },
    iconClassForPlayKrpano() {
      return this.isAutoPlayingKrpano ? "stop" : "start";
    },
    startScene() {
      return this.$route.params.StartScene;
    },
    isOnlyShowTimestamp() {
      return this.dataFromUrlQuery.isOnlyShowTimestamp=='1'?true:false
    },
    isTimestampHMSHidden() {
      return this.dataFromUrlQuery.isTimestampHMSHidden=='1'?true:false
    },
    isTimelineInitShow() {
      return this.dataFromUrlQuery.isTimelineInitShow=='1'?true:false
    },
    isActionBarInitHidden() {
      return this.dataFromUrlQuery.isActionBarInitHidden=='1'?true:false
    }
  },
  watch: {
    isKrpanoLibLoaded(cv) {
      if (cv) {
        this.$nextTick(this.loadTourScenes);
      }
    },
  },
  created() {
    window.addEventListener("message", this.handleMessage);
    this.dataFromUrlQuery = this.$route.query
    this.onKrpanoWrapperSizeChanged = debounce(
      500,
      this.onKrpanoWrapperSizeChanged,
      { atBegin: false }
    );
    if (this.checkPbGuid()) {
      this.loadKrpanoLibrary();
      this.genKrpanoRelatedHandler();
    }
  },
  mounted() {
    this.observeKrpanoWrapper();
  },
  beforeDestroy() {
    this.unObserveKrpanoWrapper();
    if (this.krpanoInterfaceObj) {
      this.krpanoInterfaceObj.unload();
    }
    window.removeEventListener('message',this.handleMessage);
  },
  methods: {
    handleMessage(info) {
			const data = info.data
      if (data && data.type === "setViewAngle") {
          const angleObj = info.data.value
          this.krpanoInterfaceObj.set("view.vlookat", angleObj.vlookat);
          this.krpanoInterfaceObj.set("view.hlookat", angleObj.hlookat);
          this.krpanoInterfaceObj.set("view.fov", angleObj.fov);
        }
    },
    onElScreenUpdated() {
      this.updateRelatedDomSize();
    },
    updateRelatedDomSize() {
      const maxHeight = document.documentElement.clientHeight - 80 - 32;
      let dataHeight =
        this.$refs.scrollbar.$refs.resize.getBoundingClientRect().height;
      const timelineWrapper = document.querySelector(
        "._css-panoshowing-timeline"
      );
      if (dataHeight >= maxHeight) {
        timelineWrapper.style.top = "20px";
        timelineWrapper.style.maxHeight = "calc(100% - 80px - 20px)";
        dataHeight = maxHeight - 20;
      } else {
        timelineWrapper.style.top = "auto";
        timelineWrapper.style.maxHeight = "calc(100% - 80px)";
      }
      this.$refs.scrollbar.$el.style.height = dataHeight + "px";
      this.$refs.scrollbar.update();
    },

    // 监测全景图最外层dom尺寸变更
    observeKrpanoWrapper() {
      if (this.$refs.krpanoWrapper) {
        this.observerForKrpanoWrapper = new ResizeObserver(
          this.onKrpanoWrapperSizeChanged
        );
        this.observerForKrpanoWrapper.observe(this.$refs.krpanoWrapper);
      }
    },
    // 取消监测全景图最外层dom尺寸变更
    unObserveKrpanoWrapper() {
      if (this.observerForKrpanoWrapper && this.$refs.krpanoWrapper) {
        this.observerForKrpanoWrapper.unobserve(this.$refs.krpanoWrapper);
      }
    },
    // 响应全景图最外层dom尺寸变更
    onKrpanoWrapperSizeChanged() {
      this.loadTourScenes();
    },
    // 加载库
    loadKrpanoLibrary() {
      const scriptEle = document.getElementById("pano-tour");
      if (!scriptEle) {
        const a = document.createElement("script");
        a.src = "./static/vtour/tour.js";
        a.id = "pano-tour";
        a.onload = callOnLoad;
        document.head.appendChild(a);
        const _this = this;
        function callOnLoad() {
          console.log("加载完成tour.js");
          _this.isKrpanoLibLoaded = true;
          a.onload = null;
        }
      }
    },

    // 检测PbGuid
    checkPbGuid() {
      let isValid = true;
      const { localPbGuid } = this;
      if (!localPbGuid || /^\s*$/i.test(localPbGuid)) {
        isValid = false;
      } else {
        isValid = localPbGuid.length > 8;
      }
      if (!isValid) {
        this.$message({
          type: "error",
          message: "PbGuid参数异常",
        });
      }
      return isValid;
    },

    // 获取krpano的vars参数
    getKrpanoSettings() {
      const settings = {};
      if (this.startScene) {
        settings.startscene = `scene_${this.startScene}`;
      }
      return settings;
    },

    // 加载全景图
    loadTourScenes() {
      if (
        !this.checkPbGuid() ||
        !this.isKrpanoLibLoaded ||
        this.isLoadingKrpano
      )
        return;
      if (this.krpanoInterfaceObj) {
        this.krpanoInterfaceObj.unload();
        this.krpanoInterfaceObj = null;
      }
      this.$nextTick(() => {
        const basepath = `${
          window.bim_config.webserverurl
        }/Panorama/${this.PbUrl.split("").join("/")}/${
          this.localPbGuid
        }/vtour/`;
        const xmlurl = `${basepath}tour.xml?r=${Math.random() * 100000 + 1}`;
        this.isLoadingKrpano = true;
        embedpano({
          xml: xmlurl,
          target: "krpanoView",
          basepath,
          vars: this.getKrpanoSettings(),
          html5: "auto",
          passQueryParameters: true,
          onready: this.onKrpanoEmbedDone,
        });
      });
    },

    // 生成krpano相关的handler
    genKrpanoRelatedHandler() {
      window.func_viewchanged = this.onKrpanoViewChanged; // 生成在action中调用的函数
      window.func_loadcomplete = this.onKrpanoLoadComplete; // 生成在action中调用的函数

      window.handlerMapForLayer = {
        toggleTimeline: this.toggleTimeline,
        refreshCurrentTour: this.refreshCurrentTour,
      }; // 代码添加的layer的事件处理器映射
    },

    // toggle时间轴
    toggleTimeline() {
      this.isTimelineVisible = !this.isTimelineVisible;
      if (this.isTimelineVisible) {
        this.setTimelineRelatedData();
      } else {
        this.isAutoPlayingKrpano = false;
      }
    },

    // toggle播放全景图集
    togglePlayKrpano() {
      if (this.isAutoPlayingKrpano) {
        this.stopPlayingKrpano();
      } else {
        this.startPlayingKrpano();
      }
    },

    // 根据播放参数获取排序后的数据
    setOrderedTimelineItems() {
      if (!this.sceneList || !this.sceneList.length) {
        this.orderedTimelineItems = [];
        return;
      }
      const itemsDeepCopy = JSON.parse(JSON.stringify(this.sceneList));
      if (this.krpanoPlayOrder === 1) {
        // 按照收集时间升序
        this.orderedTimelineItems = itemsDeepCopy.sort((a, b) => {
          return (
            this.dayjs(a.PsCollecteddatetime).valueOf() -
            this.dayjs(b.PsCollecteddatetime).valueOf()
          );
        });
      } else {
        // 按照收集时间降序
        this.orderedTimelineItems = itemsDeepCopy.sort((a, b) => {
          return (
            this.dayjs(b.PsCollecteddatetime).valueOf() -
            this.dayjs(a.PsCollecteddatetime).valueOf()
          );
        });
      }
    },

    // 预定下一次自动加载执行
    scheduleNextRun(runImmediadely) {
      let maxIndex = this.orderedTimelineItems.length;
      if (!maxIndex) {
        this.stopPlayingKrpano();
        return;
      }
      maxIndex = maxIndex - 1;
      const currentIndex = this.orderedTimelineItems.findIndex(
        (item) => item.PsScenename == this.currentPsSceneName
      );
      if (currentIndex !== -1) {
        let nextIndex = currentIndex + 1;
        if (nextIndex > maxIndex) {
          nextIndex = 0;
        }
        const nextItem = this.orderedTimelineItems[nextIndex];
        if (nextItem) {
          if (runImmediadely) {
            this.loadKrpanoScene(nextItem);
          } else {
            this.timerForAutoPlayingKrpano = setTimeout(() => {
              this.loadKrpanoScene(nextItem);
            }, this.krpanoPlayInterval * 1000);
          }
        }
      } else {
        this.stopPlayingKrpano();
      }
    },

    // 停止播放全景图集
    stopPlayingKrpano() {
      clearTimeout(this.timerForAutoPlayingKrpano);
      this.isAutoPlayingKrpano = false;
    },
    // 开始播放全景图集
    startPlayingKrpano() {
      this.setOrderedTimelineItems();
      this.isAutoPlayingKrpano = true;
      this.scheduleNextRun(false);
    },

    // 设置时间轴相关数据
    async setTimelineRelatedData() {
      this.sceneList = [];
      const baseUrl = `${window.bim_config.webserverurl}/api/Panorama/PanoramaFile`;
      const token = this.$staticmethod.Get("Token");
      const x = await this.$axios
        .get(
          `${baseUrl}/GetScenesByPbGuid?PbGuid=${this.localPbGuid}&Token=${token}`
        )
        .catch(() => {
          this.$message.error("获取场景信息失败");
        });
      if (!x || !x.data || !x.data.Data) {
        this.resetTimelineRelatedData();
        return;
      }
      this.sceneList = x.data.Data.reverse();
      if(this.isTimestampHMSHidden) {
        this.sceneList.forEach(item => {
          if(item.PsCollecteddatetime){
            item.PsCollecteddatetime = this.dayjs(item.PsCollecteddatetime).format('YYYY-MM-DD')
          }
        })
      }
    },

    // 重置时间轴相关的数据
    resetTimelineRelatedData() {
      this.isTimelineVisible = false;
      this.sceneList = [];
      this.isDotsRelatedVisible = false;
      this.currentPsRealName = this.currentPsSceneName = "";
      this.stopPlayingKrpano();
      this.orderedTimelineItems = [];
      this.isKrpanoXmlError = false;
    },

    // 刷新全景图
    refreshCurrentTour() {
      this.resetTimelineRelatedData();
      if (this.krpanoInterfaceObj) {
        this.krpanoInterfaceObj.unload(); // 卸载当前的
        this.$nextTick(() => {
          window.location.reload();
        });
      }
    },

    // 响应krpano嵌入完成事件
    onKrpanoEmbedDone(krpanoInterfaceObj) {
      console.log("onKrpanoEmbedDone");
      this.krpanoInterfaceObj = krpanoInterfaceObj;
      this.registerKrpanoXmlErrorHandler();
      this.setTimelineRelatedData();

      if(this.isTimelineInitShow) {
        this.isTimelineVisible = this.isTimelineInitShow
      }
      if(this.isActionBarInitHidden) {
        this.$nextTick(()=>{
          this.krpanoInterfaceObj.call('skin_hideskin')
        })
      }
    },

    // 注册krpano的xml解析、下载错误事件处理器
    registerKrpanoXmlErrorHandler() {
      if (this.krpanoInterfaceObj) {
        this.krpanoInterfaceObj.events.addListener(
          "onxmlerror",
          this.onKrpanoXmlError
        );
      }
    },

    // 响应krpano的xml解析、下载错误事件
    onKrpanoXmlError() {
      this.isLoadingKrpano = false;
      this.isKrpanoXmlError = true;
      const rootKrpanoElement = document.querySelector("#krpanoSWFObject");
      if (rootKrpanoElement && this.krpanoInterfaceObj) {
        const errorDom = document.createElement("div");
        errorDom.setAttribute("id", "krpanoXmlError");
        errorDom.style.cssText =
          "display:flex;width:100%;height:100%;color:#fff;flex-direction:column;justify-content:center;align-items:center;";
        errorDom.innerHTML =
          "<div style='width:100%;padding:8px;border: 1px solid #fff;'>" +
          this.krpanoInterfaceObj.lasterror
            .replace(/\[/g, "<")
            .replace(/\]/g, "/>") +
          "</div>";
        rootKrpanoElement.innerHTML = "";
        rootKrpanoElement.appendChild(errorDom);
      }
    },

    // 响应krpano视角改变事件
    onKrpanoViewChanged() {
      const angleObj = {
				hlookat: this.krpanoInterfaceObj.get("view.hlookat"),
				vlookat: this.krpanoInterfaceObj.get("view.vlookat"),
				fov: this.krpanoInterfaceObj.get("fov")
			}
      window.parent.postMessage(
        {
          type: "viewChanged",
          angleObj: angleObj
        },
        "*"
      );
    },
    // 响应krpano场景加载完成事件
    onKrpanoLoadComplete(...args) {
      // console.log("onKrpanoLoadComplete",args);
      this.updateKrpanoBtns();
      this.$nextTick(() => {
        this.walkThroughUpdateRealName();
        this.isLoadingKrpano = false;
        if (this.isAutoPlayingKrpano) {
          this.scheduleNextRun(false);
        }
      });
    },
    // 遍历krpano的dom，修改包含场景名字的dom的innerHTML为图片名字
    walkThroughUpdateRealName() {
      // krpano相关的dom
      const targetDom = document.querySelector("#idForSceneName");
      if (targetDom) {
        const content = targetDom.innerHTML.replace(/\s*/g, "");
        const targetValue =
          content == this.currentPsRealName ? this.currentPsSceneName : content;
        const data = this.sceneList.find(
          (item) => item.PsScenename == targetValue
        );
        if (data) {
          targetDom.innerHTML = data.PchChname;
          this.currentPsRealName = data.PchChname;
          this.currentPsSceneName = data.PsScenename;
        } else {
          this.currentPsRealName = "";
          this.currentPsSceneName = "";
        }
      } else {
        const { walkThroughTreesByDepthFirst } = window.tool;
        const _this = this;
        let currentPsSceneName = "";
        let currentPsRealName = "";
        let shouldDelayCall = false;
        walkThroughTreesByDepthFirst(
          [document.querySelector("#krpanoSWFObject")],
          (node) => {
            if (node && node.nodeName == "DIV") {
              // div节点
              const content = node.innerHTML.replace(/\s*/g, ""); // 去除空白
              if (content) {
                const data = _this.sceneList.find(
                  (item) => item.PsScenename == content
                );
                if (data) {
                  if (
                    node.parentNode ===
                    document.querySelector("#krpanoSWFObject")
                  ) {
                    shouldDelayCall = true;
                    return true;
                  }
                  node.setAttribute("id", "idForSceneName");
                  node.innerHTML = data.PchChname;
                  currentPsRealName = data.PchChname;
                  currentPsSceneName = data.PsScenename;
                  return true;
                }
              }
            }
          },
          "children",
          true
        );
        if (shouldDelayCall) {
          // console.log("延迟调用")
          setTimeout(this.walkThroughUpdateRealName, 1000);
        } else {
          _this.currentPsSceneName = currentPsSceneName;
          _this.currentPsRealName = currentPsRealName;
        }
      }
    },
    // 修改krpano的操作按钮:隐藏一些、添加一些,实际上这里可以把无用的layer、plugin都去掉以提升性能
    updateKrpanoBtns() {
      const { krpanoInterfaceObj } = this;
      if (krpanoInterfaceObj) {
        const layersToDelete = [
          "skin_map_container",
          "skin_btn_map",
          "skin_btn_fs",
        ];
        layersToDelete.forEach((layer) => {
          krpanoInterfaceObj.removelayer(layer);
        });

        this.$nextTick(() => {
          let layerOffset = 50;
          const regForBtnPrev = /skin_btn_prev/i; // 上一张
          const regForBtnNext = /skin_btn_next/i; // 下一张
          const layerNames = krpanoInterfaceObj.layer
            .getArray()
            .filter((item) => item.visible && item.enabled)
            .map((item) => item.name);
          if (
            layerNames.some(
              (item) => regForBtnPrev.test(item) || regForBtnNext.test(item)
            )
          ) {
            layerOffset = 95;
          }

          // 增加时间轴
          const layerTimeline =
            krpanoInterfaceObj.addlayer("skin_btn_timeline");
          layerTimeline.setvars({
            type: "image",
            url: `${window.bim_config.webserverurl}/Panorama/time.png`,
            parent: "skin_control_bar_buttons",
            align: "left",
            x: layerOffset,
            y: "0",
            width: "24",
            height: "24",
            keep: true,
            onclick: "js(handlerMapForLayer.toggleTimeline())",
          });
          layerTimeline.loadstyle("skin_glow");

          // 增加刷新
          const layerRefresh = krpanoInterfaceObj.addlayer("skin_btn_refresh");
          layerRefresh.setvars({
            type: "image",
            url: `${window.bim_config.webserverurl}/Panorama/global_img/interface_refresh.svg`,
            parent: "skin_control_bar_buttons",
            align: "left",
            x: layerOffset + 40,
            y: "0",
            width: "32",
            height: "32",
            keep: true,
            onclick: "js(handlerMapForLayer.refreshCurrentTour())",
          });
          layerRefresh.loadstyle("skin_glow");
        });
      }
    },

    // 响应点击时间轴元素
    onTimelineItemClick(item) {
      this.loadKrpanoScene(item);
    },

    // 加载krpano的场景
    loadKrpanoScene(item) {
      if (item) {
        const { PsScenename } = item;
        if (PsScenename == this.currentPsSceneName) {
          return;
        } else {
          this.krpanoInterfaceObj.call(
            `loadscene('scene_${PsScenename}', null, 'MERGE', 'BLEND(1)')`
          );
        }
      }
    },
  },
};
</script>

<style lang="scss" scoped>
._css-panoshare-all {
  position: fixed;
  width: 100%;
  height: 100%;
  z-index: 1;
  top: 0;
  left: 0;

  ._css-panoshowing-timeline {
    position: absolute;
    z-index: 10;
    top: auto;
    bottom: 80px;
    left: 16px;
    box-sizing: border-box;
    width: 238px;
    max-height: calc(100% - 80px);
    padding-left: 24px;
    padding-top: 32px;
    padding-right: 20px;
    border-radius: 8px;
    background-color: rgba(0, 0, 0, 0.3);
    .timeline-header {
      box-sizing: border-box;
      position: absolute;
      display: flex;
      top: 0;
      left: 0;
      padding-left: 24px;
      width: 100%;
      height: 32px;
      font-size: 14px;
      justify-content: space-between;
      align-items: center;
      color: #fff;
      border-top-left-radius: 8px;
      border-top-right-radius: 8px;
      background-color: rgba(0, 0, 0, 0.5);
      .title {
        letter-spacing: 1px;
      }
      .tail {
        display: flex;
        height: 100%;
        padding-right: 8px;
        font-weight: 500;
        justify-content: flex-end;
        align-items: center;
        cursor: pointer;
        .dot {
          width: 4px;
          height: 4px;
          border-radius: 100%;
          background-color: #fff;
          &:not(:last-of-type) {
            margin-right: 3px;
          }
        }
      }
    }
    .timeline-content {
      padding-top: 16px;
      max-height: calc(100% - 32px);
      .css-panoshowing-lineitem {
        margin-left: 3px;
        cursor: pointer;
      }
    }
    .timeline-dots-related {
      position: absolute;
      left: 100%;
      top: 0;
      background-color: rgba(0, 0, 0, 0.7);
      border: 1px solid #fff;
      color: #fff;
      border-radius: 8px;
      .content-wrapper {
        min-width: 200px;
        padding: 14px;
        .item {
          display: flex;
          height: 32px;
          justify-content: space-between;
          align-items: center;
          &:not(:last-of-type) {
            margin-bottom: 8px;
          }
          .playOrder {
            /deep/ .el-radio {
              margin-right: 0;
              &:not(:first-of-type) {
                margin-left: 16px;
              }
              color: #fff;
              .el-radio__label {
                padding-left: 6px;
              }
            }
          }
          .playInterval {
            /deep/ .el-input {
              .el-input__inner {
                border-radius: 6px;
                background-color: transparent;
                color: #fff;
                border: 1px solid #fff;
              }
            }
            /deep/ .el-input-number__decrease,
            /deep/ .el-input-number__increase {
              background-color: transparent;
              color: #fff;
            }
          }
          .btnPlay {
            display: flex;
            justify-content: flex-start;
            align-items: center;
            padding: 3px 8px;
            color: #fff;
            border-radius: 6px;
            font-size: 14px;
            border: 1px solid #fff;
            cursor: pointer;
            .icon {
              &.start {
                width: 0;
                height: 0;
                border-top: 4px solid transparent;
                border-left: 8px solid #fff;
                border-bottom: 4px solid transparent;
              }
              &.stop {
                width: 8px;
                height: 8px;
                background-color: #fff;
              }
            }
            .label {
              margin-left: 6px;
            }
          }
        }
      }
    }
  }

  ._css-loading-krpano {
    position: absolute;
    display: flex;
    z-index: 99999;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    justify-content: center;
    align-items: center;
    .content {
      color: #fff;
      font-size: 32px;
      font-weight: 500;
    }
  }

  ._css-krpano-view {
    width: 100%;
    height: 100%;
  }
}
</style>
