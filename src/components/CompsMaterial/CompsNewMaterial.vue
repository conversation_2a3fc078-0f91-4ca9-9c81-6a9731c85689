<template>
  <div class="_css-newmaterial-all basic-hover" :style="gethoverstyle()">
    <!-- 中间的新建构件对话框 -->
    <div
    id="id_newmaterial_add"
    v-drag="draggreet" :style="dragstyle" class="_css-newmaterial-dialog">
      <!-- 通用的对话框头组件 -->
      <!-- <CompsDialogHeader @oncancel="_oncancel" title="新建构件"></CompsDialogHeader> -->
      <!-- //通用的对话框头组件 -->
      <div class="_css-typeitem-title">
              添加单元
        <span class="_css-typeitem-close icon-interface-guanbimoxing" @click.stop="_oncancel()"></span>
      </div>
      <!-- 可滚动-开始 -->

      <div
        id="id_middle_scrollable"
        class="_css-middle-scrollable css-miniscroll"
        @mousedown="_stopPropagation($event)"
      >
        <div class="_css-newmaterial-title">基本信息</div>

        <!-- 编码ID -->
        <div class="_css-material-field _css-material-code" @mousedown="_stopPropagation($event)">
          <div class="_css-material-label">编码</div>
          <div class="_css-material-textdiv">
            <input
              placeholder="输入编码（必填）"
              class="_css-material-text"
              type="text"
              @keyup="validform()"
              v-model="extdata.materialcode"
            />
          </div>
        </div>
        <!-- //编码ID -->

        <!-- 单元名称label及输入框 -->
        <div class="_css-material-field _css-material-name">
          <div class="_css-material-label">单元名称</div>
          <div class="_css-material-textdiv">
            <input
              placeholder="输入单元名称（必填）"
              class="_css-material-text"
              type="text"
              @keyup="validform()"
              v-model="extdata.materialname"
            />
          </div>
        </div>
        <!-- //单元名称label及输入框 -->



        <!-- 计划到场时间和更多 -->
        <!-- <div class="_css-material-timeandmore"> -->

          <!-- 时间控件图标及label -->
          <!-- <div class="_css-material-time _css-mulbtn"> -->
            <!-- <div
              class="_css-material-time-icon _css-hoverin-045 icon-interface-history basic-fonticon-20"
            ></div>-->
            <!-- <div class="_css-timeiconandtime css-timeiconandtime">
              <div class="_css-planatime-icon icon-interface-history"></div>
              <el-date-picker
                @change="change_time"
                v-model="extdata.dtPlanArriveTime"
                format="yyyy-MM-dd HH:mm"
                type="datetime"
                align="left"
                prefix-icon="icon-interface-history"
                :picker-options="pickerOptions"
                placeholder="计划到场时间"
              >
                <div class="_css-material-timelabel _css-material-label _css-hoverin-045">计划到场时间</div>
              </el-date-picker>
            </div>
            <div
              @click.stop="clearPlanArriveTime()"
              class="_css-planatime-close icon-suggested-close_circle"
            ></div>
          </div> -->
          <!-- //时间控件图标及label -->

          <!-- more -->
          <!-- <div @click.stop="toggleMore()" class="_css-material-more _css-mulbtn">
            <div
              class="_css-material-more-icon _css-hoverin-045 icon-interface-list basic-fonticon-20"
            ></div>
            <div
              class="_css-material-more-text _css-material-label _css-hoverin-045"
            >{{extdata.bIsShowingMore?'收起':'更多'}}</div>
          </div> -->
          <!-- //more -->
        <!-- </div> -->
        <!-- //计划到场时间和更多 -->

        <!-- 更多区域 -->
         <!-- v-show="extdata.bIsShowingMore" -->
        <div class="_css-more-area">

            <!-- 型号 -->
        <div class="_css-material-field _css-material-code" @mousedown="_stopPropagation($event)">
          <div class="_css-material-label">型号</div>
          <div class="_css-material-textdiv">
            <input
              placeholder="输入构件型号"
              class="_css-material-text"
              type="text"
              v-model="extdata.materialmodel"
            />
          </div>
        </div>
        <!-- //型号 -->

        <!-- 数量 -->
        <div class="_css-material-field _css-material-num" @mousedown="_stopPropagation($event)">
          <div class="_css-material-label">数量</div>
          <div class="_css-material-textdiv">
            <input
              placeholder="输入构件数量"
              class="_css-material-text"
              @keyup="numkeyup($event);"
              type="text"
              v-model="extdata.materialnum"
            />
          </div>
        </div>
        <!-- //数量 -->

        <!-- 单位 -->
        <div class="_css-material-field _css-material-unit" @mousedown="_stopPropagation($event)">
          <div class="_css-material-label">单位</div>
          <div
            id="id_newmaterial_unitdiv"
            class="_css-material-textdiv _css-material-selectouter _css-click-noselect"
            @click.stop="toggleunitoptions()"
          >
            <!-- <input
              class="_css-material-text _css-click-noselect"
              type="text"
              v-model="extdata.materialunit"
              readonly="readonly"
            />-->

            <div class="_css-material-divinner">{{extdata.materialunit}}</div>
          </div>
        </div>
        <!-- //单位 -->

        <!-- 厂家 -->
        <div class="_css-material-field _css-material-code" @mousedown="_stopPropagation($event)">
          <div class="_css-material-label">厂家</div>
          <div class="_css-material-textdiv">
            <input
              placeholder="输入厂家名称"
              class="_css-material-text"
              type="text"
              v-model="extdata.materialfac"
            />
          </div>
        </div>
        <!-- //厂家 -->
        <!-- 所有自定义字段 -->
        <div class="_css-newmaterial-title" v-if="extdata.customobj.length>0">自定义信息</div>
        <div
        v-for="custom in extdata.customobj"
        :key="custom.fieldid"
        class="_css-material-field _css-material-code" @mousedown="_stopPropagation($event)">
          <div
          :title="custom.fieldname"
          class="_css-material-label">{{custom.fieldname}}</div>
          <div class="_css-material-textdiv">
            <input
              :placeholder="'输入' + custom.fieldname"
              class="_css-material-text"
              type="text"
              @keyup="validform()"
              v-model="custom.fieldval"
            />
          </div>
        </div>
        <!-- //所有自定义字段 -->

          <!-- 下方的Spliter -->
          <div class="_css-spliter"></div>
          <!-- //下方的Spliter -->

          <!-- 三个关联按钮 -->
          <div class="_css-rel-btns">
            <!-- <div class="_css-rel-btn">
              <div class="_css-btn-icon icon-interface-associated-component"></div>
              <div class="_css-btn-text">关联构件</div>
            </div>-->
            <div @click.stop="open_relimg" class="_css-rel-btn">
              <div class="_css-btn-icon icon-interface-model-picture"></div>
              <div class="_css-btn-text">关联图片</div>
            </div>
            <div @click.stop="open_relfile" class="_css-rel-btn _css-rel-btnlast">
              <div class="_css-btn-icon icon-interface-folder"></div>
              <div class="_css-btn-text">关联附件</div>
            </div>
          </div>
          <!-- //三个关联按钮 -->

          <!-- 如果有关联的构件 -->
          <!-- <div class="_css-rel-eles">
              <div class="_css-rel-title" >关联的构件</div>
              <div class="_css-rel-body-item" >
                  <div class="_css-rel-bodyi-icon icon-interface-associated-component" ></div>
                  <div class="_css-rel-bodyi-text" >模型显示27个字超型显示27个型显示27个型显示27个出现tooltip</div>
                  <div class="_css-rel-bodyi-num" >4562</div>
              </div>
          </div>-->
          <!-- //如果有关联的构件 -->

          <!-- 如果有关联的图片 -->
          <div
          v-if="extdata.relimages.length > 0"
          class="_css-rel-imgs">
            <div class="_css-rel-title">关联的图片</div>
            <div class="_css-rel-body-imagebody">
              <div
              :style="getImageBgStyle(extdata.currentshowurl, 'cover')"
              class="_css-rel-body-imagein">
                <div class="_css-rel-imagehead">{{extdata.currentshowtitle}}</div>
                <div class="_css-rel-imagebottom">
                  <!-- 向左翻动 -->
                  <div class="_css-relimage-left _css-relimage-lr" @click.stop="clickscroll(-1)">
                    <div class="icon-arrow-left_outline _css-lrbtn"></div>
                  </div>
                  <!-- //向左翻动 -->

                  <!-- 中间的小图区域 -->
                  <div
                    id="id_relimage_middle"
                    class="_css-relimage-middlearea css-miniscroll css-littlescroll"
                  >
                    <div
                    v-for="img in extdata.relimages"
                    :key="img.bf_guid"
                    class="_css-relimage-middlei">
                      <div
                      @click.stop="removeaddingimg(img.bf_guid)"
                      class="icon-suggested-close_circle _css-suggested-close_circle" ></div>
                      <div class="_css-relimage-middlein"
                      @click.stop="setcurrentshowimg(img)"
                      :style="getImageBgStyle(img.bf_path)"
                      >

                      </div>

                    </div>

                  </div>
                  <!-- //中间的小图区域 -->

                  <!-- 向右翻动 -->
                  <div class="_css-relimage-right _css-relimage-lr" @click.stop="clickscroll(1)">
                    <div class="icon-arrow-right_outline _css-lrbtn"></div>
                  </div>
                  <!-- //向右翻动 -->
                </div>
              </div>
            </div>
          </div>
          <!-- //如果有关联的图片 -->

          <!-- 如果有关联的文件 -->
          <div class="_css-rel-files"
          v-if="extdata.relattachments.length > 0"
          >
            <div class="_css-rel-title">关联的附件</div>
            <!-- <div class="_css-rel-body-item" >
                  <div class="_css-rel-bodyi-icon mulcolor-interface-doc" ></div>
                  <div class="_css-rel-bodyi-text" >模型显示27个字超型显示27个型显示27个型显示27个出现tooltip</div>
              </div>
               <div class="_css-rel-body-item" >
                  <div class="_css-rel-bodyi-icon mulcolor-interface-doc" ></div>
                  <div class="_css-rel-bodyi-text" >模型显示27个字超型显示27个型显示27个型显示27个出现tooltip</div>
            </div>-->
            <div
              v-for="rela in extdata.relattachments"
              :key="rela.fileid"
              class="_css-rel-body-item"
            >
              <div class="_css-rel-bodyi-icon "
              :class="$staticmethod.getIconClassByExtname(rela.filename, 1)"
              ></div>
              <div class="_css-rel-bodyi-text">{{rela.filename}}</div>
              <div
              @click.stop="removerelfile(rela.fileid)"
              class="icon-suggested-close_circle _css-file-close" ></div>
            </div>
          </div>
          <!-- //如果有关联的文件 -->
        </div>
        <!-- //更多区域 -->
      </div>

      <!-- 按钮区域 -->
      <div class="_css-bottombtn-area" @mousedown="_stopPropagation($event)">
        <!-- 完成按钮 -->
        <div
        @click.stop="addmaterial()"
        class="_css-btn _css-default "
        :class="{'_css-dis': extdata.formvalid == false}"
        >完成</div>
        <!-- //完成按钮 -->

        <!-- 完成并新建下一个按钮 -->
        <div
        @click.stop="addmaterial(true)"
        class="_css-btn "
        :class="{'_css-dis': extdata.formvalid == false}"
        >完成并新建下一个</div>
        <!-- //完成并新建下一个按钮 -->
      </div>
      <!-- //按钮区域 -->
    </div>
    <!-- //中间的新建构件对话框 -->

    <!-- 下拉选项 -->
    <div
      class="_css-material-selectbody"
      :class="{'_css-showdiv':extdata.bIsShowingOptions}"
      :style="getUnitOptionStyle()"
    >
      <div @click.stop="selectunit('个')" class="_css-material-selectoption">个</div>
      <div @click.stop="selectunit('吨')" class="_css-material-selectoption">吨</div>
    </div>
    <!-- //下拉选项 -->

    <!-- 添加关联图片的input -->
    <input
      type="file"
      style="display:none"
      id="id_relimgopener"
      accept="image/*"
      @click.stop="_stopPropagation($event)"
      @change="on_relimgopener_change()"
    />
    <!-- //添加关联图片的input -->

    <!-- 添加关联文档 -->
    <input
      type="file"
      style="display:none"
      id="id_relfileopener"
      @click.stop="_stopPropagation($event)"
      @change="on_relfileopener_change()"
    />
    <!-- //添加关联文档 -->
  </div>
</template>
<script>
import CompsDialogHeader from "@/components/CompsDialog/CompsDialogHeader";
export default {
  components: {
    CompsDialogHeader
  },
  data() {
    return {
      val: "0",
      dragstyle: {
        position: "fixed",
        right: "calc(50% - 224px)",
        top: "calc(50% - 430px)",
        "max-height": "565px"
      },
      pickerOptions: {},
      extdata: {
        customobj:[
        ], // 自定义字段及值
        formvalid:false,
        unitoptionLeft: 0,
        unitoptionTop: 0,
        dtPlanArriveTime: "", // 时间控件结束时间
        iUnitScrollSize: 24, // 点击滚动的单位像素
        bIsShowingOptions: false, // 是否显示下拉
        bIsShowingMore: false, // 是否显示更多
        materialname: "", // 单元名称
        materialcode: "", // 构件code（编码：ID）
        materialmodel: "", // 型号
        materialnum: "", // 数量
        materialnum_bak: "", // 数量（备份）
        materialunit: "个",
        materialfac: "",
        relattachments: [
        ],                 // 关联的附件
        relimages:[
        ],                 // 关联的图片
        currentshowtitle:'', // 当前正在显示的图片名称
        currentshowurl:''  // 当前正在显示的图片路径（使用方法得到全地址）
        ,currentshowbfid:'', // 当前正在显示的图片的bf_guid
      }
    };
  },
  watch: {
    val: {
      handler: function(val, oldval) {
        var _this = this;
        console.log(_this.extdata.unitoptionLeft);
        var rect = document
          .getElementById("id_newmaterial_unitdiv")
          .getBoundingClientRect();
        _this.extdata.unitoptionLeft = rect.left;
        _this.extdata.unitoptionTop = rect.top + 45;
      },
      deep: true
    }
  },
  mounted() {
    var _this = this;
    window.newmaterial = _this;

    // 根据selectedtypeid 获取所有自定义字段
    // _this.loadcustomfields();

    // 根据屏幕大小确定中间的高度
    // (document.body.clientHeight - 64 * 2)/2

    // 确定 _this.dragstyle.top 的值及 中间区域height 的值

    _this.windowsizechange();
    window.onresize = function() {
      console.log(177);
      _this.windowsizechange();
    };
  },
  props: {
    zIndex: {
      // def: 3
      type: Number,
      required: false
    },
    selectedtypeid:{
      type:String,
      required:false
    }
  },
  methods: {

    //
    loadcustomfields(){
      var _this = this;
      var _LoadingIns = _this.$loading({
        text: "正在加载",
        target: document.getElementById("id_middle_scrollable")
      });
      _this.$axios.get(`${window.bim_config.webserverurl}/api/Material/MaterialCategory/GetCategoryExtJson?bmc_guid=${_this.selectedtypeid}&Token=${_this.$staticmethod.Get('Token')}`)
      .then(x => {
        if (x.status == 200) {
          if (x.data.Ret > 0) {

            if (x.data.Data.fields) {
              var arr = [];
              for (var i = 0; i < x.data.Data.fields.length; i++) {
                var ti = x.data.Data.fields[i];
                arr.push({
                  fieldid:ti.fieldid,
                  fieldname:ti.fieldname,
                  fieldval:''
                });
              }
              _this.extdata.customobj = arr;
            } else {
              _this.extdata.customobj = [];
            }

          } else {
            _this.$message.error(x.data.Msg);
          }
        } else {
          console.error(x);
        }
        _LoadingIns.close();
      })
      .catch(x => {
        console.error(x);
        _LoadingIns.close();
      });
    },

    //
    removerelfile(fileid){
      var _this = this;
      _this.$confirm('确认移除此附件?', '操作确认', {
        confirmButtonText: '确定',
        cancelButtonText:'取消',
        type: 'warning'
      }).then(x => {
        _this.extdata.relattachments = _this.extdata.relattachments.filter(x => x.fileid != fileid);
      }).catch(x => {
      });
    },

    //
    removeaddingimg(bfguid){
      var _this = this;
      _this.$confirm('确认移除此图片?', '操作确认', {
        confirmButtonText: '确定',
        cancelButtonText:'取消',
        type: 'warning'
      }).then(x => {

        // 得到当前图片的索引
        var currentRMIndex = _this.extdata.relimages.findIndex(x => x.bf_guid == bfguid);
        _this.extdata.relimages = _this.extdata.relimages.filter(x => x.bf_guid != bfguid);

        // 判断点击的是否为刚刚删除的
        if (_this.extdata.currentshowbfid == bfguid) {

          // 优先取前一个（如果前一个存在）
          if (currentRMIndex - 1 >= 0) {
            _this.extdata.currentshowbfid = _this.extdata.relimages[currentRMIndex - 1].bf_guid;
            _this.extdata.currentshowtitle = _this.extdata.relimages[currentRMIndex - 1].bf_filename;
            _this.extdata.currentshowurl = _this.extdata.relimages[currentRMIndex - 1].bf_path;
          } else if (currentRMIndex < _this.extdata.relimages.length){
            // 否则尝试取下一个。 判断 currentRMIndex 这个索引是否还在范围内
            _this.extdata.currentshowbfid = _this.extdata.relimages[currentRMIndex].bf_guid;
            _this.extdata.currentshowtitle = _this.extdata.relimages[currentRMIndex].bf_filename;
            _this.extdata.currentshowurl = _this.extdata.relimages[currentRMIndex].bf_path;

          } else {
            // 清空三个属性
            _this.extdata.currentshowtitle = '';
            _this.extdata.currentshowurl = '';
            _this.extdata.currentshowbfid = '';
          }

        }

      }).catch(x => {

      });
    },

    // 清空所有字段信息
    clearall(){
      var _this = this;
      _this.extdata.formvalid = false;
      _this.extdata.dtPlanArriveTime = '';
      _this.extdata.bIsShowingOptions = false;
      _this.extdata.bIsShowingMore = false;
      _this.extdata.materialname = '';
      _this.extdata.materialcode = '';
      _this.extdata.materialmodel = '';
      _this.extdata.materialnum = '';
      _this.extdata.materialnum_bak = '';
      _this.extdata.materialunit = '个';
      _this.extdata.materialfac = '';
      _this.extdata.relattachments = [];
      _this.extdata.relimages = [];
      _this.extdata.currentshowtitle = '';
      _this.extdata.currentshowurl = '';
      _this.extdata.currentshowbfid = '';
    },

    // 添加一条构件信息，
    // 如果goonadd为true，表示不关闭弹出框，继续添加
    addmaterial(goonadd){

      // 按钮禁用时不作动作
      var _this = this;
      if (!_this.extdata.formvalid) {
        return;
      }

      // 准备参数关联的图片和附件
      var _relimageid = '';
      var _relfileid = '';
      if (_this.extdata.relimages && _this.extdata.relimages.length > 0) {
        for (var i = 0; i < _this.extdata.relimages.length; i++) {
          _relimageid
          +=
          `${_this.extdata.relimages[i].bf_guid}${i == _this.extdata.relimages.length - 1?'':','}`;
        }
      }
      if (_this.extdata.relattachments && _this.extdata.relattachments.length > 0) {
        for (var i = 0; i < _this.extdata.relattachments.length; i++) {
          _relfileid
          +=
          `${_this.extdata.relattachments[i].fileid}${i == _this.extdata.relattachments.length - 1?'':','}`;
        }
      }

      // _this.selectedtypeid
      if (!_this.selectedtypeid){
        _this.$message.error('请先选择类别');
        return;
      }

      // 发起请求前加载loading
      var _LoadingIns = _this.$loading({
        text: "操作中",
        target: document.getElementById("id_newmaterial_add")
      });

      // 发起请求
      _this.$axios({
        url:`${this.$MgrBaseUrl.AddMaterialItem}`,
        method:'post',
        data: {
          Token: _this.$staticmethod.Get("Token"),
          bm_materialname: _this.extdata.materialname,
          bm_materialcode: _this.extdata.materialcode,
          bc_guid_materialtype:_this.selectedtypeid,
          bm_materialmodel: _this.extdata.materialmodel,
          bm_materialcount: _this.extdata.materialnum,
          bm_materialunit: _this.extdata.materialunit,
          bm_materialfac: _this.extdata.materialfac,
          bm_planarrtime: _this.$staticmethod.dtToString(_this.extdata.dtPlanArriveTime),
          bm_extjson: JSON.stringify(_this.extdata.customobj),
          bf_guids:_relimageid,
          fileids:_relfileid
        }
      }).then(x => {
        if (x.status == 200) {
          if (x.data.Ret > 0) {
            // 是否关闭弹出框， 并刷新
            _this.$emit("onadded", goonadd);
            // 清空所有内容
            _this.clearall();
          } else {
            _this.$message.error(x.data.Msg);
          }
        } else {
          console.error('服务器错误：', x);
        }
        _LoadingIns.close();
      }).catch(x => {
        console.error('服务器错误：', x);
        _LoadingIns.close();
      })

    },

    // 验证填写的内容并确定下方两个按钮（完成并新建下一个、完成）的可用性
    // 单元名称和编码是必填字段
    validform(){

      // 判断单元名称及编码是否为空白
      var _this = this;
      if (!_this.extdata.materialname || _this.extdata.materialname.trim() == ''
        || !_this.extdata.materialcode || _this.extdata.materialcode.trim() == '') {

          // 将下方两个按钮设为不可用
          _this.extdata.formvalid = false;

      } else {

          // 将下方两个按钮设为可用
          _this.extdata.formvalid = true;

      }

    },

    // 输入“个数”的动态验证
    numkeyup(ev){

      // 得到输入后的值
      var _this = this;

      if (_this.extdata.materialnum == '') {
        _this.extdata.materialnum_bak = _this.extdata.materialnum;
      } else {
         // 如果校验通过赋值给back，否则提示，并使用back赋值给这个字段
        if (/^[0-9]\d*(\.\d*)?$/.test(_this.extdata.materialnum)) {
          _this.extdata.materialnum_bak = _this.extdata.materialnum;
        } else {
          _this.$message.error('输入的数字不正确');
          _this.extdata.materialnum = _this.extdata.materialnum_bak;
        }
      }

      //_this.validform(); 只有输入单元名称和编码时才会进行验证

    },

    // 点击小图，显示大图和图的名称
    setcurrentshowimg(img){
      var _this = this;
      _this.extdata.currentshowtitle = img.bf_filename;
      _this.extdata.currentshowurl = img.bf_path;
      _this.extdata.currentshowbfid = img.bf_guid;
    },

    //
    getImageBgStyle(url, bsize){
      var _this = this;
      var _s = {};
      var totalurl = window.bim_config.webserverurl + url;
      _s["background-image"] = `url(${totalurl})`;
      _s["background-repeat"] = "no-repeat";
      _s["background-size"] = "contain";
      if (bsize) {
        _s["background-size"] = bsize;
      }
      _s["background-position"] = "center";
      return _s;
    },

    //
    uploadfile(ele, successcb, errorcb) {
      var _this = this;
      if (!ele || !ele.files || !ele.files.length || ele.files.length == 0) {
        return;
      }
      var File = ele.files[0];
      var ProjectID = _this.$staticmethod._Get("organizeId");
      var userid = _this.$staticmethod.Get("UserId");
      let FileKey=this.$md5(new Date().getTime()+''+File.name);
      var fd = new FormData();
      fd.append("ProjectID", ProjectID);
      fd.append("BusinessId", 1);
      fd.append("AttachmentType", '1');
      fd.append("FileName", File.name);
      fd.append("FileKey", FileKey);
      fd.append("FileSize", File.size);
      fd.append("ChunkNumber", 1);
      fd.append("Index", 1);
      fd.append("UserId", userid);
      fd.append("File", File);
      var config = {
        headers: {
          "Content-Type": "multipart/form-data"
        }
      };
      _this.$axios
        .post(
          `${window.bim_config.webserverurl}/api/v1/attach/upload?Token=${this.$staticmethod.Get('Token')}`,
          fd,
          config
        )
        .then(x => {
          if (successcb) {
            successcb(x);
          } else {
            console.warn("not specify successcb");
          }
        })
        .catch(x => {
          if (errorcb) {
            errorcb(x);
          } else {
            console.warn("not specify errorcb");
          }
        });
    },

    //
    uploadimg(ele, successcb, errorcb) {
      var _this = this;
      if (!ele || !ele.files || !ele.files.length || ele.files.length == 0) {
        return;
      }
      var File = ele.files[0];
      var fd = new FormData();
      fd.append("Token", _this.$staticmethod.Get("Token"));
      fd.append("FormFile", File);
      var config = {
        headers: {
          "Content-Type": "multipart/form-data"
        }
      };
      _this.$axios
        .post(
          `${this.$issueBaseUrl.UploadImage}?token=${ _this.$staticmethod.Get("Token")}`,
          fd,
          config
        )
        .then(x => {
          if (successcb) {
            if (x.data.Ret < 0) {
              _this.$message.error(x.data.Msg);
              return;
            } else {
              successcb(x);
            }
          } else {
            console.warn("not specify successcb");
          }
        })
        .catch(x => {
          if (errorcb) {
            errorcb(x);
          } else {
            console.warn("not specify errorcb");
          }
        });
    },

    // 关联文件input变动
    on_relfileopener_change() {
      var _this = this;
      var dom = document.getElementById("id_relfileopener");

      // 判断此次选择的文件是否已存在于 relfiles 中了
      if (dom.files && dom.files.length > 0) {
        var index =
        _this.extdata.relattachments.findIndex(x => x.filename == dom.files[0].name);
        if (index >= 0) {
          _this.$message.error('已添加了相同名称的附件');
          return;
        }
      }

      _this.uploadfile(
        dom,
        function(x) {
          if (x.status == 200) {
            let _data = x.data.Data.Data
            if (_data.AttachmentId) {
              _this.extdata.relattachments.push({
                fileid:_data.AttachmentId,
                filename:_data.AttachmentName
              });
              setTimeout(x => {
                  document.getElementById("id_middle_scrollable").scrollTop += 110;
              }, 500);
            } else {
              console.error(x);
            }
          } else {
            console.error(x);
          }

        },
        function(x) {
          //debugger;
        }
      );
    },

    // 关联图片input变动
    on_relimgopener_change() {
      // 调用上传图片接口（不关联），并得到bf_guid
      var _this = this;

      // 调用上传图片方法（其内部调用接口）
      var dom = document.getElementById("id_relimgopener");

      // 判断此次选择的图片是否已存在于 relimages 中了
      if (dom.files && dom.files.length > 0) {
        var index =
        _this.extdata.relimages.findIndex(x => x.bf_filename == dom.files[0].name);
        if (index >= 0) {
          _this.$message.error('已添加了相同名称的图片');
          return;
        }
      }

      // 显示loading
      var _LoadingIns = _this.$loading({
        text: "操作中",
        target: document.getElementById("id_middle_scrollable")
      });


      _this.uploadimg(
        dom,
        function(x) {
          //debugger;
          if (x.status == 200) {
            if (x.data.Ret > 0) {
              if (x.data.Data)  {
                _this.extdata.relimages.push(x.data.Data);
                if (_this.extdata.relimages.length == 1) {
                  _this.extdata.currentshowtitle = x.data.Data.bf_filename;
                  _this.extdata.currentshowurl =  '/' + x.data.Data.bf_path;
                  _this.extdata.currentshowbfid =x.data.Data.bf_guid;
                  setTimeout(x => {
                    document.getElementById("id_middle_scrollable").scrollTop += 324;
                  }, 500);
                }
              }
            } else {

            }
          } else {

          }
          _LoadingIns.close();
        },
        function(x) {
          _LoadingIns.close();
        }
      );
    },

    // 点击关联图片
    open_relimg() {
      var _this = this;
      var dom = document.getElementById("id_relimgopener");
      dom.value = "";
      dom.click();
    },

    // 点击关联文件
    open_relfile() {
      var _this = this;
      var dom = document.getElementById("id_relfileopener");
      dom.value = "";
      dom.click();
    },

    selectunit(unit) {
      var _this = this;
      _this.extdata.materialunit = unit;
      _this.extdata.bIsShowingOptions = false;
    },

    getUnitOptionStyle() {
      var _this = this;
      var _s = {};
      _s["left"] = _this.extdata.unitoptionLeft + "px";
      _s["top"] = _this.extdata.unitoptionTop + "px";
      return _s;
    },

    change_time() {
      var _this = this;
    },

    clickscroll(para) {
      var _this = this;
      document.getElementById("id_relimage_middle").scrollLeft +=
        para * _this.extdata.iUnitScrollSize;
    },

    toggleMore() {
      var _this = this;
      if (_this.extdata.bIsShowingMore == true) {
        _this.extdata.bIsShowingMore = false;
      } else {
        // 显示更多
        _this.extdata.bIsShowingMore = true;
      }
    },
    windowsizechange() {
      var _this = this;
      var middledialogheight = document.body.clientHeight - 64 * 2;
      var _top = `calc(50% - ${middledialogheight / 2}px)`;

      // 修改top的值
      _this.dragstyle.top = _top;

      // 修改高度 _css-newmaterial-dialog
      _this.dragstyle["max-height"] = middledialogheight + "px";

      // var rect = document.getElementById("id_newmaterial_unitdiv").getBoundingClientRect();
      // _this.extdata.unitoptionLeft = rect.left;
      // _this.extdata.unitoptionTop = rect.top + 45;
      // 窗口大小变化时，隐藏单位选择下拉框
      _this.extdata.bIsShowingOptions = false;
    },
    _oncancel() {
      var _this = this;
      _this.$emit("oncancel");
    },
    toggleunitoptions() {
      var _this = this;

      if (_this.extdata.bIsShowingOptions == true) {
        _this.extdata.bIsShowingOptions = false;
      } else {
        // 调整选择单位选项下拉框的位置
        var rect = document
          .getElementById("id_newmaterial_unitdiv")
          .getBoundingClientRect();
        _this.extdata.unitoptionLeft = rect.left;
        _this.extdata.unitoptionTop = rect.top + 45;
        _this.extdata.bIsShowingOptions = true;
      }
    },
    _stopPropagation(ev) {
      if (ev) {
        ev.stopPropagation();
      }
    },
    draggreet(val) {
      var _this = this;
      _this.val = val;
    },
    gethoverstyle() {
      var _this = this;
      var _s = {};
      _s["z-index"] = _this.zIndex || 1001; // 0 -> 3
      return _s;
    }
  }
};
</script>
<style scoped>
/* ._css-material-time .el-input--prefix .el-input__inner{
  padding-left:0 !important;
  padding-right: 0 !important;
} */

._css-rel-btnlast {
  margin-left: 40px;
}

._css-material-divinner {
  width: 100%;
  box-sizing: border-box;
  /* padding-left: 24px; */
  padding-right: 24px;
  text-align: left;
  height: 24px;
  line-height: 24px;
  user-select: none;
  cursor: pointer;
}

._css-relimage-lr {
  display: flex;
  align-items: center;
  justify-content: space-around;
}

._css-relimage-lr:hover {
  opacity: 0.8;
}

._css-lrbtn {
  width: 12px;
  height: 12px;
  font-size: 12px;
  color: rgb(236, 237, 233);
  line-height: 12px;
  text-align: center;
}

._css-relimage-middlei {
  width: 54px;
  height: 54px;
  background-color: transparent;
  border-radius: 4px;
  margin-left: 3px;
  margin-right: 3px;
  flex: none;
  overflow: visible;
  display: flex;
  align-items: center;
  justify-content: space-around;
    position:relative;
}

._css-suggested-close_circle{
  position:absolute;
  right:-4px;
  top:-4px;
  display: none;
  cursor: pointer;
  color:rgba(24, 144, 255, 1);
}

._css-relimage-middlein {
  width: 50px;
  height: 50px;
  background-color: rgb(236, 237, 233);
  box-shadow: 0px 1px 1px 0px rgba(0, 21, 41, 0.12);
  border-radius: 4px;
  cursor: pointer;
  transition: all 200ms;

}

._css-relimage-middlei:hover ._css-relimage-middlein{
  width: 54px;
  height: 54px;
  transition: all 200ms;
}

._css-relimage-middlei:hover ._css-suggested-close_circle {
  display: block;
}

._css-relimage-left {
  width: 16px;
  height: 58px;
  border-radius: 4px;
  background-color: rgba(52, 52, 52, 1);
  margin-left: 5px;
  margin-right: 5px;
  cursor: pointer;
}

._css-relimage-middlearea {
  flex: 1;
  height: 100%;
  margin-left: 5px;
  margin-right: 5px;
  display: flex;
  align-items: center;
  overflow-x: auto;
  border-right: 1px solid transparent;
  border-left: 1px solid transparent;
}

._css-relimage-right {
  width: 16px;
  height: 58px;
  border-radius: 4px;
  background-color: rgba(52, 52, 52, 1);
  margin-left: 5px;
  margin-right: 5px;
  cursor: pointer;
}

._css-rel-imagehead {
  position: absolute;
  top: 0;
  height: 32px;
  width: 100%;
  background-color: rgba(0, 0, 0, 1);
  opacity: 0.45;
  font-size: 12px;
  color: #fff;
  text-align: center;
  line-height: 32px;

  border-top-left-radius: 4px;
  border-top-right-radius: 4px;
}

._css-rel-imagebottom {
  position: absolute;
  bottom: 0;
  height: 66px;
  width: 100%;
  background-color: rgba(0, 0, 0, 0.45);
  /* opacity: 0.45; */
  display: flex;
  align-items: center;

  border-bottom-left-radius: 4px;
  border-bottom-right-radius: 4px;
}

._css-rel-bodyi-icon {
  width: 20px;
  height: 20px;
  font-size: 20px;
  line-height: 20px;
}

._css-rel-bodyi-text {
  margin-left: 8px;
  flex: 1;
  text-overflow: ellipsis;
  overflow-x: hidden;
  white-space: nowrap;
  height: 22px;
  line-height: 22px;
  text-align: left;
}

._css-rel-bodyi-num {
  margin-left: 8px;
  flex: none;
  text-align: right;
  height: 22px;
  line-height: 22px;
  color: rgba(0, 0, 0, 0.25);
  font-size: 14px;
}

._css-rel-body-imagebody {
  height: 264px;

  padding-left: 48px;
  padding-right: 48px;
}

._css-rel-body-imagein {
  height: 100%;
  box-shadow: 0px 13px 24px -17px rgba(11, 41, 62, 0.8);
  border-radius: 4px;
  position: relative;

  /* background-image: url(https://www.probim.cn:8080/ProjectImgs/2019_08_18/9119c30d-2159-441f-a375-a470465cf672_min.bmp);
  background-repeat: no-repeat;
  background-size: cover;
  background-position: center center; */
}

._css-rel-body-item {
  height: 50px;
  display: flex;
  align-items: center;
  padding-left: 48px;
  padding-right: 48px;
}

._css-file-close{
  display: none;
  cursor: pointer;
}

._css-rel-body-item:hover ._css-file-close{
  display:block;
}

._css-file-close:hover{
  color:rgba(24, 144, 255, 1);
}

._css-rel-title {
  font-size: 14px;
  color: rgba(0, 0, 0, 0.45);
  text-align: left;
  height: 50px;
  line-height: 50px;
  padding-left: 48px;
}

._css-rel-files {
  min-height: 100px;
  margin-top: 10px;
}

._css-rel-imgs {
  margin-top: 10px;
  height: 314px;
}

._css-rel-eles {
  margin-top: 10px;
  height: 100px;
  border: 1px solid transparent;
  display: flex;
  flex-direction: column;
}

._css-btn-text {
  width: 56px;
  height: 22px;
  line-height: 22px;
  font-size: 14px;
}

._css-btn-icon {
  width: 20px;
  height: 20px;
  font-size: 20px;
  line-height: 20px;
}

._css-rel-btn {
  width: 84px;
  height: 22px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  color: rgba(24, 144, 255, 1);
  cursor: pointer;
}

._css-rel-btn:hover {
  color: #49b5ff;
}

._css-rel-btns {
  height: 78px;
  display: flex;
  /* justify-content: space-around; */
  align-items: center;
  border-bottom: 1px solid rgba(0, 0, 0, 0.09);
  padding-left: 24px;
  /*
  padding-right: 24px; */
}

._css-middle-scrollable {
  flex: 1;
  overflow-y: auto;
}

/* 按钮 */
._css-btn {
  margin-right: 24px;
  min-width: 48px;
  height: 38px;
  border-radius: 2px;
  padding-left: 13px;
  padding-right: 13px;
  font-size: 14px;
  text-align: center;
  line-height: 38px;
  cursor: pointer;

  border: 1px solid rgba(24, 144, 255, 1);
  background-color: rgba(255, 255, 255, 1);
  color: rgba(24, 144, 255, 1);
}

._css-btn._css-dis {
  border: 1px solid rgba(0, 0, 0, 0.15);
  background-color: rgba(255, 255, 255, 1);
  color: rgba(0, 0, 0, 0.25);
  cursor: not-allowed;
}

._css-btn._css-default {
  border: 1px solid rgba(24, 144, 255, 1);
  background-color: rgba(24, 144, 255, 1);
  color: rgba(255, 255, 255, 1);
}

._css-btn._css-default._css-dis {
  border: 1px solid rgb(191, 191, 191);
  background-color: rgb(191, 191, 191);
  color: rgba(255, 255, 255, 1);
  cursor: not-allowed;
}

/* //按钮 */
._css-bottombtn-area {
  height: 40px;
  margin-top: 12px;
  margin-bottom: 12px;
  display: flex;
  flex-direction: row-reverse;
  align-items: center;
}

._css-material-label {
  font-size: 14px;
  text-align: left;
  text-overflow: ellipsis;
  overflow-x: hidden;
  white-space: nowrap;
  width: calc(100% - 0px);
}

._css-mulbtn ._css-hoverin-045 {
  color: rgba(0, 0, 0, 0.45);
}

._css-mulbtn:hover ._css-hoverin-045 {
  color: rgba(24, 144, 255, 1);
}

.el-input__prefix [class^="icon-"],
.el-input__prefix [class*=" icon-"] {
  line-height: 40px !important;
}

._css-material-more {
  min-width: 58px;
  height: 22px;
  line-height: 22px;
  display: flex;
  justify-content: space-between;
  cursor: pointer;
  align-items: center;
}
._css-material-time {
  min-width: 112px;
  height: 22px;
  line-height: 22px;
  display: flex;
  justify-content: space-between;
  cursor: pointer;
  align-items: center;
  position: relative;
}

._css-planatime-icon {
  position: absolute;
  left: 0;
  height: 22px;
  line-height: 20px;
  width: 30px;
  box-sizing: border-box;
  border: 1px solid transparent;
  background-color: #fff;
  z-index: 1;
  color: rgb(140, 140, 140);
}

._css-timeiconandtime {
  display: flex;
  align-items: center;
}

/* ._css-timeiconandtime:hover ._css-planatime-close{
  display: block;
}
*/

._css-timeiconandtime:hover ._css-planatime-icon {
  color: rgba(24, 144, 255, 1);
}

._css-material-time ._css-planatime-close {
  position: absolute;
  right: 40px;
  height: 22px;
  line-height: 20px;
  width: 30px;
  box-sizing: border-box;
  border: 1px solid transparent;
  background-color: #fff;
  z-index: 1;
  color: rgb(140, 140, 140);
  display: none;
}

._css-material-time:hover ._css-planatime-close {
  display: block;
}

._css-planatime-close:hover {
  color: rgba(24, 144, 255, 1);
}

._css-material-timeandmore {
  height: 22px;
  margin-top: 19px;
  padding-bottom: 19px;
  width: calc(100% - 24px - 24px);
  padding-left: 24px;
  padding-right: 24px;
  display: flex;
  justify-content: space-between;
  border-bottom: 1px solid rgba(0, 0, 0, 0.09);
}
._css-material-selectoption {
  height: 40px;
  line-height: 40px;
  cursor: pointer;
  padding-left: 16px;
  padding-right: 16px;
  text-align: left;
  font-size: 14px;
  color: rgba(0, 0, 0, 0.65);
}
._css-material-selectoption:hover {
  background-color: rgba(0, 0, 0, 0.04);
}
._css-material-selectbody {
  position: fixed;
  /* top: 0;
  left: 0; */
  z-index: 2;
  width: 402px;
  height: 0;
  transition: padding-top 200ms, height 200ms, padding-bottom 200ms;
  background-color: rgba(255, 255, 255, 1);
  box-shadow: 0px 4px 20px 0px rgba(0, 0, 0, 0.15);
  padding-top: 0px;
  padding-bottom: 0px;
  box-sizing: border-box;
  overflow-y: hidden;
  border-radius: 4px;
}
._css-material-selectbody._css-showdiv {
  height: 88px;
  transition: padding-top 200ms, height 200ms, padding-bottom 200ms;
  padding-top: 4px;
  padding-bottom: 4px;
}
._css-material-selectouter {
  position: relative;
}
._css-click-noselect {
  cursor: pointer;
}
._css-material-text {
  outline: none;
  border: none;
  width: calc(100% - 48px);
  height: 22px;
  line-height: 22px;
}
._css-material-textdiv {
  height: 40px;
  border-radius: 4px;
  border: 1px solid rgba(0, 0, 0, 0.09);
  display: flex;
  align-items: center;
  /* justify-content: space-around; */
  padding-left: 16px;
  width: calc(100% - 20px);
}
._css-material-unit {
  margin-top: 17px;
}
._css-material-num {
  margin-top: 17px;
}
._css-material-code {
  margin-top: 15px;
}
._css-material-name {
  margin-top: 15px;
}
._css-material-field {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: flex-start;
  height: 68px;
  margin-left: 24px;
  margin-right: 24px;
}
._css-newmaterial-dialog {
  width: 448px;
  /* min-height:561px; */
  /* height:768px; */
  background-color: rgba(255, 255, 255, 1);
  box-shadow: 0px 13px 24px -17px rgba(11, 41, 62, 0.8);
  border-radius: 4px;
  display: flex;
  flex-direction: column;
}
._css-newmaterial-all {
  background-color: rgba(0, 0, 0, 0.15);
}
._css-typeitem-title {
  text-align: center;
  position: relative;
  line-height: 44px;
  font-size: 16px;
  font-weight: 500;
  color: rgba(0, 0, 0, 1);
  border-bottom: 1px solid rgba(0, 0, 0, 0.09);
}
._css-typeitem-title ._css-typeitem-close {
  position: absolute;
  top: 12px;
  right: 12px;
  color: rgba(0, 0, 0, 0.45);
  cursor: pointer;
}
._css-newmaterial-title{
  text-align: left;
  font-size:16px;
  margin-top: 15px;
  font-family:'PingFangSC-Medium,PingFang SC';
  font-weight:500;
  color:rgba(140,140,140,1);
  margin-left: 24px;
}
</style>
