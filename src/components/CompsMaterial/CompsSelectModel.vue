<template>
  <div class="wrapper">
      <section class="dialog-wp">
        <div class="dialog">
            <div class="_css-dialog-wp-title">
            <span>选择模型</span>
            <i class="_css-dialog-wp-title-close icon-interface-guanbimoxing" @click="cancel"></i>
            </div>
            <div class="dialog-center">
            <div class="left-list">
                <p>
                模型阶段
                </p>
                <el-tree
                  class="el-tree-cus"
                  highlight-current
                  node-key="BusinessCode"
                  ref="elTree"
                  empty-text="暂无子级"
                  :default-expand-all="false"
                  :expand-on-click-node="true"
                  :auto-expand-parent="true"
                  :default-expanded-keys="treeExpandedKeys"
                  :data="treeData"
                  :props="elTreeProps"
                  @node-click="stageClick">
                    <div class="el-tree-node-cus" slot-scope="{ node }">
                      <el-tooltip effect="dark" :content="node.label" placement="top" :enterable="false">
                        <div class="label">{{ node.label }}</div>
                      </el-tooltip>
                    </div>
                </el-tree>
            </div>
            <div class="right-list" id="id_rightlist">
              <ul>
                <li
                  v-for="model in currentModelList"
                  :key="model.featureID"
                  :class="{active: selectModelID == model.featureID}"
                  @click.stop="modelListClick(model)"
                >
                  <img class="img-model-list" :src="getItemImg(model)" alt="">
                  <div>
                    <p>{{model.featureName}}</p>
                    <span>更新时间：{{model.createTime.split('T')[0]}}</span>
                  </div>
                  <i
                    class="icon-checkbox-Selected-Disabled-dis-blue"
                    v-show="selectModelID == model.featureID"
                  ></i>
                </li>
              </ul>
            </div>

            </div>
            <div class="btn-wp">
              <div @click.stop="submit">确定</div>
              <div class="_css-notdef _css-marginrightzero" @click.stop="cancel">取消</div>
            </div>
        </div>
        </section>
  </div>
</template>

<script>
export default {
  components:{},
  props:{},
  data(){
    return {
        projectStage:[],
        currentModelList: [],
        selectModelID:'',
        selectModelName:'',
      selectModelExtension:'',
        defaultImg: { src: require("../../assets/images/ModelDefault.jpg") },
        VaultID: '',
        treeData: [],
        elTreeProps: {
          children: 'Children',
          label: 'MenuName'
        },
        treeKey:"",
        treeExpandedKeys:[],  // 默认展开节点,当前选中的节点
    }
  },
  watch:{},
  computed:{

  },
  created(){},
  mounted(){
    this.VaultID = this.$staticmethod._Get("organizeId");
    this.getMenuTree()
  },
  methods:{
    getMenuTree(){
      const loading = this.$loading({
				lock: true,
				text: 'Loading',
				spinner: 'el-icon-loading',
				background: 'rgba(255, 255, 255, 0.7)'
			});
			this.$axios
				.get(`${this.$urlPool.GetUserMenuTree}?token=${this.$staticmethod.Get("Token")}&organizeId=${this.VaultID}&parentId=0`)
				.then(res=>{
          loading.close();
					if(res.data.Ret == 1) {
            let modelTree = res.data.Data.find(index=>index.MenuCode == 'MODEL');
            let newArr = modelTree.Children.filter(item => item.BusinessCode !== 'allmodel_phase');
            this.treeData = newArr
            this.stageClick(newArr[0])
            this.highlightTree(newArr[0].BusinessCode)
					}else{
						this.$message.error(res.data.Msg);
					}
				})
				.catch(err=>{})
		},
    highlightTree(UrlPhase) {
      if(UrlPhase && UrlPhase !== 'allmodel_phases') {
        const treeKey = UrlPhase
        this.$nextTick(() => {
          this.$refs.elTree.setCurrentKey(treeKey)
        })
      }
    },
    getItemImg (item) {
      if (item.thumbnail === '') {
        return require('../../assets/images/default.jpg')
      } else {
        return `data:image/png;base64,${item.thumbnail}`
      }
    },
    cancel(){
        this.$emit('close')
    },
    submit(){
      if(this.selectModelID && this.selectModelName){
        this.$emit('getModelInfo',{modelid:this.selectModelID,modelname:this.selectModelName,extension: this.selectModelExtension})
      }else{
        this.$message.warning('请选择模型')
      }
    },
    stageClick(phase) {
        var _this = this;
        _this.$axios
          .get(`${this.$ip('newModelHttpUrl')}/Vault/GetFeaturesByPhase?VaultID=${_this.VaultID}&Phase=${phase.BusinessCode}`)
          .then(res => {
            if (res.status === 200) {
              if(res.data.length > 0){
                this.selectModelID = res.data[0].featureID;
                this.selectModelName = res.data[0].featureName;
                this.selectModelExtension = res.data[0].extension;
              }
              this.currentModelList = res.data;
            }
          })
          .catch(err => {
          })
    },
    modelListClick(model) {
      this.selectModelID = model.featureID;
      this.selectModelName = model.featureName;
      this.selectModelExtension = model.extension;
    },
  },

}
</script>
<style lang="stylus" scoped rel="stylesheet/stylus">
._css-marginrightzero{
  margin-right : 0 !important;
}
._css-dialog-wp-title{
  height:44px;
  line-height:44px;
  border-bottom:1px solid rgba(0,0,0,.09);
  margin-bottom:16px;
  text-align:center;
}
._css-dialog-wp-title span{
  font-size:16px;
  font-weight:900;
}
._css-dialog-wp-title-close{
  position:absolute;
  right:17px;
  top:7px;
  color:rgba(0, 0, 0, 0.35);
  padding:5px;
  cursor:pointer;
}
    .dialog-wp {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.15);
    z-index: 1002;

    .dialog {
      width: 680px;
      height: 600px;
      background: #ffffff;
      box-shadow: 0px 13px 24px -17px rgba(11, 41, 62, 0.8);
      border-radius: 4px;
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      margin: auto;
      display: flex;
      flex-direction: column;

      .dialog-center {
        flex: 1;
        padding: 0 24px 0;
        display: flex;
        position: relative;



        .left-list {
          max-height: 455px;
          overflow: auto;
          flex: 1;

          li {
            width: 200px;
            height: 40px;
            line-height: 40px;
            padding: 0 12px 0 28px;
            text-align: left;
            display: flex;
            justify-content: space-between;
            color: rgba(0, 0, 0, 0.65);
            border-radius: 4px;

            p {
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
              width: 130px;
              display: inline-block;
            }

            &:hover, &.active {
              background: rgba(24, 144, 255, 0.1);
              color: #1890FF;
              cursor: pointer;

              span {
                color: #1890FF;
              }
            }

            span {
              color: rgba(0, 0, 0, 0.25);
            }
          }

          p {
            display: flex;
            align-items: center;
            font-size: 16px;
            text-align: left;
            margin: 0;
            margin-bottom: 15px;

            i {
              margin-right: 8px;
            }
          }
        }

        .right-list {
          width: 378px;
          max-height: 455px;
          height: 455px;
          border-radius: 2px;
          overflow-y: auto;
          white-space: nowrap;

          li {
            padding: 12px;
            display: flex;
            flex-direction: row;
            border-radius: 2px;
            position: relative;

            &:hover, &.active {
              background: rgba(0, 0, 0, 0.04);
              cursor: pointer;
            }

            i {
              position: absolute;
              top: 0;
              bottom: 0;
              right: 12px;
              margin: auto;
              line-height: 114px;
            }

            img {
              width: 120px;
              height: 90px;
              border-radius:4px;
            }

            div {
              flex: 1;
              margin-left: 8px;
              display: flex;
              flex-direction: column;
              justify-content: space-between;

              p {
                font-size: 14px;
                color: rgba(0, 0, 0, 0.65);
                width: 190px;
                overflow: hidden;
                text-overflow: ellipsis;
                display: -webkit-box;
                -webkit-line-clamp: 2;
                -webkit-box-orient: vertical;
                text-align: left;
                font-weight: 400;
              }

              span {
                font-size: 12px;
                font-weight: 400;
                color: rgba(0, 0, 0, 0.25);
                height: 20px;
                line-height: 20px;
                text-align: left;
                display: inline-block;
              }
            }
          }
        }
      }
      .btn-wp {
        height:64px;
        border-top:1px solid rgba(0,0,0,.09);
        div {
          height: 40px;
          text-align: center;
          line-height: 40px;
          font-size: 14px;
          cursor: pointer;
         background: #1890FF;
          color: #fff;
          float: right;
          margin: 12px 24px;
          width: 120px;
          border-radius:4px;
          &:hover {
            background: #49B5FF;
          }
          &._css-notdef {
            background: rgba(0, 0, 0, 0.25);
            &:hover {
              background: rgba(0, 0, 0, 0.35);
            }
          }
        }
      }
    }
  }
  .el-tree-cus{
    /deep/ .el-tree-node {
          .el-tree-node__content {
            display: flex;
            justify-content: flex-start;
            align-items: center;
            padding-top: 8px;
            padding-bottom: 8px;
          }
    }
  }
</style>
