<template>
  <div class="wrapper" @click.stop="closeall()">
    <header>
      <div @click.stop="_closerelationmodel()">
        <i class="icon-interface-back"></i>
        <span class="close-relation-model">关联模型</span>
      </div>
      <div
        @click.stop="relationtomaterial"
        :class="{'_css-dis':cmpt_btnreltomaterial_dis}"
        class="_css-relation-btn"
      >关联模型</div>
    </header>
    <main class="center">
      <section class="right-conter">
        <div class="header">
          <!-- 左边的下拉菜单，选择构件类别的 -->
          <div 
            :title="oSelectedCateObj.bc_categoryname || '&lt;无分类&gt;'"
            class="_css-materialrel-cateselect"
          >
            <div class="_css-cateselect-name">{{oSelectedCateObj.bc_categoryname || '&lt;无分类&gt;'}}</div>
            <div class="_css-cateselect-icon icon-arrow-down"></div>
            <div v-if="bIsShowingCateDrops==true" class="_css-drop-items css-miniscroll">
              <div
                @click.stop="selectThisCate(mtype)"
                v-for="mtype in dropDownData"
                :key="mtype.bc_guid"
                class="_css-drop-item"
              >
                <div class="_css-drop-itemlabel">{{mtype.bc_categoryname}}</div>
                <div class="_css-drop-itemcnt">{{mtype.MaterialCnt}}</div>
              </div>
            </div>


        </div>
        <div class="relation-wp">已关联/未关联：{{relationElementNumber}}/{{tableData.length-relationElementNumber}}</div>

          <el-popover ref="popverWp" placement="bottom" width="360" trigger="click">
            <div class="el-screen-menu">
              <el-input v-model="screenConfig.inputVal" placeholder="请输入内容" clearable></el-input>
              <div class="el-state">
                <div class="el-left-state">构件状态</div>
                <el-popover
                  placement="bottom"
                  popper-class="state-poper"
                  width="160"
                  v-model="screenConfig.stateShow"
                  trigger="hover"
                >
                  <div class="el-right-state">
                    <ul>
                      <li
                        v-for="statelist in stateDetailList"
                        :key="statelist.statusid"
                        @click.stop="stateClick(statelist.statusid,statelist.statusname)"
                      >
                        <div
                          :style="{color:statelist.statusmaincolor,
                                  borderColor:statelist.statusmaincolor,
                                  background:statelist.statusmaincolor.replace('1)', '.1)')}"
                        >{{statelist.statusname}}</div>
                        <i
                          class="icon-checkbox-Selected-Disabled-dis-blue"
                          v-show="screenConfig.state.indexOf(statelist.statusid) !== -1"
                        ></i>
                      </li>
                    </ul>
                    <div class="btn" @click.stop="stateSubmit">确定</div>
                  </div>
                  <div
                    slot="reference"
                    class="state-btn"
                  >{{screenConfig.stateText.length>0?screenConfig.stateText.join():'请筛选'}}</div>
                </el-popover>
              </div>
              <div class="el-element">
                <div class="el-left-element">关联构件：</div>
                <div class="el-right-element">
                  <el-radio label="1" v-model="screenConfig.relation">全部</el-radio>
                  <el-radio label="2" v-model="screenConfig.relation">已关联</el-radio>
                  <el-radio label="3" v-model="screenConfig.relation">未关联</el-radio>
                </div>
              </div>
              <div class="el-btn" @click="initScreen">重置筛选</div>
            </div>
          </el-popover>
        </div>
        <el-table
          @row-click="row_click"
          @cell-mouse-enter="tooltipMouseover"
          height="300"
          class="el-list-table"
          :data="tableData" 
        >
          <el-table-column prop="date" label cell-class-name="wzw" width="50">
            <template slot-scope="scope">
              <div
                :class="{'_css-checked':scope.row.bm_guid == SelectedMaterialId}"
                class="_css-radio"
              >
                <div class="_css-radio-in"></div>
              </div>
            </template>
          </el-table-column>
          
          <el-table-column prop="bm_materialcode" label="编码（ID）"></el-table-column>

          <el-table-column
            :resizable="true"
            class="_css-col-filename"
            prop="bm_materialname"
            label="构件名称"
            min-width="218"
          >
            <template slot-scope="scope" >
              <template>
                <span
                  class="_css-dataitemname css-cp css-hoverunder css-ellipsis basic-font-color-emphasize"
                >{{scope.row.bm_materialname}}</span>
                <div class="_css-trbtn-container"></div>
              </template>
            </template>
          </el-table-column>

          <el-table-column  label="关联构件" >
            <template slot="header">关联构件</template>
            <template slot-scope="scope">
              <el-tooltip placement="top" v-if="scope.row.bhaselerel">
                <div slot="content">{{tooltipConfig.text}}</div>
                <div class="_css-relele icon-interface-guanlianmoxing"></div>
              </el-tooltip>
              <div class="_css-relele" v-else>-</div>
            </template>
          </el-table-column>
        </el-table>
      </section>

      <section class="left-conter" v-loading="fullscreenLoading">
        <div class="model-wp" v-if="modelViewShow">
          <modelNewIframeLoading 
            ref="loadmodel"
            :VaultID=VaultID
            :featureID=selectModelID
          ></modelNewIframeLoading>
        </div>
        <div
          class="switch-model"
          @click="selectModelConfig.show=true"
          v-show="!selectModelConfig.show"
          @mouseleave="switchTextShow= false"
          @mouseover="switchTextShow=true"
        >
          <i class="icon-interface-model_list"></i>
          <div
            class="text-wp"
            ref="modelSwitchText"
            :class="{'switch-focus':switchTextShow}"
          >{{selectModelName}}</div>
        </div>
        <div class="wp">
          <img src="./hebingmoxing.svg" alt />
          <span @click="selectModelConfig.show=true">选择关联模型</span>
        </div>
      </section>
      <CompsSelectModel
        v-if="selectModelConfig.show"
        @getModelInfo="openModel"
        @close="closerSelectModel"
      ></CompsSelectModel>
      <zdialog-function
				:init_title="'手动关联构件'"
				:init_zindex="1003"
				:init_innerWidth="350"
				:init_width="350"
				init_closebtniconfontclass="icon-suggested-close"
				@onclose="changeSelectRelationType = false"
				v-if="changeSelectRelationType"
			>
				<div
					slot="mainslot"
					class="_css-addingnameinput-ctn css-mt12"
					@mousedown="_stopPropagation($event)"
				>
					<div class="_css-line _css-line-name">
						<el-radio-group v-model="relationTypeRadio">
              <el-radio :label="0">新增关联构件</el-radio>
              <el-radio :label="1">覆盖已关联构件</el-radio> 
            </el-radio-group>
					</div>
				 
				</div>

				<div slot="buttonslot" class="_css-flowAddBtnCtn">
					<zbutton-function
						:init_text="'确定'"
						:init_fontsize="14"
						:debugmode="true"
						:init_height="'40px'"
						:init_width="'120px'"
						@onclick="func_saveRelation"
					>
					</zbutton-function>
					<zbutton-function
						:init_text="'取消'"
						:init_color="'rgba(24, 144, 255)'"
						:init_bgcolor="'#fff'"
						:init_fontsize="14"
						:debugmode="true"
						:init_height="'40px'"
						:init_width="'120px'"
						@onclick="changeSelectRelationType=false"
					>
					</zbutton-function>
				</div>
			</zdialog-function>
    </main> 
  </div>
</template>

<script>
import modelNewIframeLoading from '@/components/Home/ProjectBoot/modelNewIframeLoading'
import CompsSelectModel from '@/components/CompsMaterial/CompsSelectModel'
const dialogWp = () => import("../CompsDialog/CompsDialogHeader");
export default {
  components: {
    dialogWp
  },
  props: {
    allMaterialTypes: {
      type: Array,
      required: true
    },
    stateDetailList: {
      type: Array
      //   ,default:[{statusmaincolor:'rgba(115, 113, 157, 1)',statusname:"运输中",statusid:1}]
    }, 
    selectCateObj:{
      type:Object,
      default:null
    }
  },
  components: {
    modelNewIframeLoading,
    CompsSelectModel,
  },
  data() {
    return {
      relationElementNumber:0,
      switchTextShow: false,
      SelectedMaterialId: "",
      screenConfig: {
        inputVal: "",
        state: [],
        stateText: [],
        relation: "1",
        stateShow: false
      },
      dropDownData: [],
      tableData: [],
      selectelements: "", // 选中的构件数据
      oSelectedCateObj: {}, // 所选中的下拉项数据对象
      bIsShowingCateDrops: false, // 是否显示类型下拉项
   
      selectModelConfig: {
        show: false
      },
      defaultImg: { src: require("../../assets/images/ModelDefault.jpg") },
      
      selectModelID: "",
      projectID: "",
      modelViewShow: false,
      fullscreenLoading: false,
      currentSelectElementID: [],
      tableSourceData: [],
      timer: null,
      tooltipConfig: {
        text: "",
        id: ""
      },
      selectModelName: "",
      VaultID: '',
      changeSelectRelationType: false, // 选择关联方式
      relationCheckedArr: [], // 关联构件，记录arr
      relationTypeRadio: 0,
    };
  },
  watch: {
    tableData:function(val){
      let nub = 0
      val.forEach(v=>{
        if(v.bhaselerel){
          nub++
        }
      })
      this.relationElementNumber = nub
    },
    "screenConfig.relation": function(val) {
      this.matchTableData();
    },
    "screenConfig.inputVal": function(val) {
      let _this = this;
      clearTimeout(this.timer);
      this.timer = setTimeout(() => {
        _this.matchTableData();
      }, 500);
    }
  },
  computed: {
    cmpt_btnreltomaterial_dis: {
      get() {
        var _this = this;
        return _this.tableData.length == 0;
      }
    },
  },
  methods: {
    _stopPropagation2(ev) {
      ev && ev.stopPropagation && ev.stopPropagation();
    },
    tooltipMouseover(row) {
      if (row.bhaselerel == 0) return;
      if (this.tooltipConfig.id == row.bm_guid) return;

      let _this = this;
      _this.$axios
        .get(`${this.$ip('newModelHttpUrl')}/Vault/GetFeature?VaultID=${_this.VaultID}&FeatureID=${row.relelejson}`)
        .then(res => {
          if (res.status === 200) {
            _this.tooltipConfig.text = res.data.featureName;
            _this.tooltipConfig.id = row.bm_guid;
          }else{
            _this.newModelTitle = ""
          }
        })
        .catch(err => {
        })

      
    },
    matchTableData() {
      let newArr = [];
      for (let table of this.tableSourceData) {
        let inptV = this.screenConfig.inputVal;
        if (inptV != "") {
          if (table.bm_materialname.indexOf(inptV) == -1) continue;
        }
        let state = this.screenConfig.state;
        if (state.length != 0) {
          if (state.indexOf(table.bc_guid_materialstatus) == -1) continue;
        }
        let relation = this.screenConfig.relation;
        switch (relation) {
          case "1":
            newArr.push(table);
            break;
          case "2":
            if (table.bhaselerel != 0) {
              newArr.push(table);
            }
            break;
          case "3":
            if (table.bhaselerel == 0) {
              newArr.push(table);
            }
            break;
        }
      }
      this.tableData = newArr;
    },
    initScreen() {
      this.tableData = this.tableSourceData;
      this.screenConfig.state = [];
      this.screenConfig.stateText = [];
      this.screenConfig.inputVal = "";
      this.screenConfig.relation = "1";
    },
    stateSubmit() {
      //筛选
      let _this = this;
      this.screenConfig.stateShow = false;
      this.matchTableData();
    },
    stateClick(id, text) {
      let index = this.screenConfig.state.indexOf(id);
      if (index !== -1) {
        this.screenConfig.state.splice(index, 1);
        this.screenConfig.stateText.splice(index, 1);
      } else {
        this.screenConfig.state.push(id);
        this.screenConfig.stateText.push(text);
      }
    },
    // 关联到构件
    relationtomaterial() {

      var _this = this;
      // 获取新模型选中构件
      let cmpt_selectelements = window.scene.selectedObjects;
       
      let modelElement = []
      // 循环获取到构件id
      cmpt_selectelements.forEach(function (value, key, map) {
        modelElement.push(key)
      })
      
      var arr = [];

        for (var i = 0; i < modelElement.length; i++) {
          // 将 67170069-1711-4f4c-8ee0-a715325942a1^211675_568190 格式的值分割为两部分，模型ID和构件ID
          var strs = modelElement[i].split("^");
        
          // 格式错误的情况
          if (strs.length != 2) {
            continue;
          }

          // 查询 arr 有无 modelid 为 strs[0] 的模型对象
          var _index = arr.findIndex(x => x.modelid == strs[0]);

          // 确定 themodel
          var themodel;
          if (_index >= 0) {
            themodel = arr[_index];
          } else {
            themodel = { modelid: this.selectModelID };
            arr.push(themodel);
          }

          // 为 themodel 赋值属性：elementids
          if (!themodel.elementids) {
            themodel.elementids = [];
          }

          // elementids 添加 strs[1]
          if (themodel.elementids.indexOf(strs[1]) < 0) {
            themodel.elementids.push(strs[1]);
          }
        }
      if (_this.cmpt_btnreltomaterial_dis) {
        console.warn("按钮已禁用");
        return;
      }

      if (arr.length == 0) {
        _this.$message.error("请先选择构件");
        return;
      }
      this.relationCheckedArr = arr
      this.changeSelectRelationType = true;

      // console.log(arr,'_this.SelectedMaterialId',_this.SelectedMaterialId,'selectModelID',this.selectModelID)
      
    },
    func_saveRelation(){
      this.$emit(
        "oncomplete",
        this.SelectedMaterialId,
        this.relationCheckedArr,
        this.selectModelID,
        this.relationTypeRadio
      );
      this.changeSelectRelationType = false;
    },

    // 选择某一条构件
    row_click(row, column, ev) {
      var _this = this;
      ev.stopPropagation();
      this.SelectedMaterialId = row.bm_guid;
    },

    reload(){
      var _this = this;
      _this.selectThisCate_force(_this.oSelectedCateObj);
    },

      // 选择某一种分类
    selectThisCate_force(type) {
        this.oSelectedCateObj = type;
        this.bIsShowingCateDrops = false;
        this.selectThisAxios(type.bc_guid)
    },
    selectThisAxios(bc_guid){
      /*
      updatetime_desc   按照更新时间倒序
      updatetime_asc 按照更新时间升序
      materialname_desc  按照名称  倒序
      materialname_asc 按照名称  升序   
      materialcode_desc 按照编码  倒序
      materialcode_asc  按照编码 升序 默认
      */ 
      fetch(
          `${this.$MgrBaseUrl.GetMaterialList}?TypeId=${bc_guid}&Sortmethod=materialcode_asc&Bhaselerel=&Statusids=&KeyWorld=&token=${this.$staticmethod.Get("Token")}`
        )
        .then(res => {
          return res.json();
        })
        .then(res => {
          this.tableData = res.Data.List;
          this.tableSourceData = res.Data.List;
        }); 
    },
 
    // 选择某一种分类
    selectThisCate(type) {
      // console.log(type)
      if (type.bc_guid === this.oSelectedCateObj.bc_guid) {
        this.bIsShowingCateDrops = false;
      } else {
        this.selectThisCate(type)
      } 
    },

    // 关闭所有弹出界面
    closeall() {
      var _this = this;
      _this.bIsShowingCateDrops = false;
      this.$refs.popverWp.doClose();
    }, 
    //
    _closerelationmodel() {
      var _this = this;
      _this.$emit("oncancel");
    },

    getwebserverurl() { 
      return window.bim_config.bimviewerurl;
    },
    openModel(model) {
      this.selectModelID = model.modelid;
      this.selectModelName = model.modelname;
      let _this = this; 
      if(this.modelViewShow){
        // 销毁上一个模型 
        window.model3.dispose();
        // 加载下一个模型 
        this.$refs.loadmodel.loadmodel2(this.selectModelID)
      }else{
        this.modelViewShow = true;
      }
      this.selectModelConfig.show = false;
      this.fullscreenLoading = false;

    }, 
    closerSelectModel() {
      this.selectModelConfig.show = false;
    }
  },
  created() {
    // 辅助调试
    var _this = this;
    window.materialrelvue = _this;
    window.top.ShowRelationInfo = function(){
      console.log( _this.selectelements)
      var data = {
          act: 'resFromMtrModel_getSelecteds', datas: _this.selectelements
      };
      window.postMessage(data, "*");
    }
  },
  mounted() {
    this.projectID = this.$staticmethod._Get("bimcomposerId");
    this.VaultID = this.$staticmethod._Get("organizeId");

    //获取下拉列表
    fetch(
      `${this.$MgrBaseUrl.GetMaterialTypes}?type=materialtype&token=${this.$staticmethod.Get("Token")}&organizeId=${this.$staticmethod._Get(
        "organizeId"
      )}`
    )
      .then(res => {
        return res.json();
      })
      .then(res => {
        this.dropDownData = res.Data;
      });

      if(this.$props.selectCateObj !== null){
        //debugger;
        this.oSelectedCateObj.bc_categoryname = this.$props.selectCateObj.bmc_name;
        this.oSelectedCateObj.bc_guid = this.$props.selectCateObj.bmc_guid;
        this.selectThisAxios(this.oSelectedCateObj.bc_guid)
      }
  }
};
</script>
<style lang="stylus" scoped rel="stylesheet/stylus">
.relation-wp{
  flex: 1;
  text-align: right;
  font-size 14px
  color rgba(0,0,0,.45)
}
* {
  user-select: none;
}

.wrapper {
  li {
    list-style: none;
  }

  ul {
    margin: 0;
    padding: 0;
  }

  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  // background: #f0f2f5;
  z-index: 1001;
  display: flex;
  flex-direction: column;

  header {
    height: 64px;
    line-height: 64px;
    padding: 0 28px;
    background: #fff;
    display: flex;
    justify-content: space-between;
    align-items: center;

    div {
      display: flex;
      align-items: center;
      cursor: pointer;

      span {
        margin-left: 5px;
      }

      &:nth-child(2) {
        width: 100px;
        height: 40px;
        text-align: center;
        justify-content: center;
      }
    }
  }

  main {
    flex: 1;
    height: calc(100% - 64px);
    background: red;
    background: rgba(240, 242, 245, 1);
    flex-direction: row;
    display: flex;

    section {

      &.left-conter {
        box-shadow: 2px 0px 4px 0px rgba(0, 21, 41, 0.12);
        flex: 1;
        position: relative;

        .switch-model {
          cursor: pointer;
          width: 50px;
          height: 50px;
          background: rgba(240, 242, 245, 1);
          box-shadow: 0px 1px 3px 0px rgba(0, 21, 41, 0.12);
          border-radius: 0px 2px 2px 0px;
          position: absolute;
          top: 10px;
          left: 84px;
          text-align: center;
          line-height: 50px;
          font-size: 20px;
          z-index: 9;

          .text-wp {
            background: rgba(240, 242, 245, 1);
            width: 0;
            height: 50px;
            box-shadow: 0px 1px 3px 0px rgba(0, 21, 41, 0.12);
            border: none;
            position: absolute;
            top: 0;
            left: 51px;
            text-align: left;
            padding: 0;
            text-overflow: ellipsis;
            overflow: hidden;
            white-space: nowrap;
            transition: width 0.5s;
          }

          .switch-focus {
            width: 256px;
            padding: 0 12px;
            border-right: 4px solid #1890ff;
          }
        }

        .model-wp {
          background: #f0f2f5;
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          z-index: 9;

          iframe {
            margin: 0;
            padding: 0;
            border: none;
            width: 100%;
            height: 100%;
          }
        }

        .wp {
          position: absolute;
          top: 30%;
          width: 180px;
          left: 0;
          right: 0;
          margin: auto;
          display: flex;
          flex-direction: column;
          align-items: center;
        }

        img {
          width: 400px;
          height: 238px;
        }

        span {
          width: 123px;
          height: 40px;
          line-height: 40px;
          border-radius: 4px;
          background: #1890ff;
          color: #fff;
          text-align: center;
          display: inline-block;
          cursor: pointer;
        }
      }

      &.right-conter {
        width:552px;
        padding: 0 24px;
        background:#f7f7f7;
        .header {
          height: 72px;
          line-height: 72px;
          display: flex;
          flex-direction: row;
          justify-content: space-between;
          align-items: center;

          i {
            cursor: pointer;
          }

          .el-dropdown-link {
            font-size: 20px;
            font-weight: 500;
            color: rgba(0, 0, 0, 0.85);
          }
        }
      }
    }
  }
}
</style>
<style scoped>

._css-marginr0 {
  margin-right:0 !important;
}

._css-relation-btn {
  border-radius: 4px;
  background-color: #1890ff;
  color: #fff;
  cursor: pointer;
}
._css-relation-btn._css-dis {
  background-color: rgba(0, 0, 0, 0.25);
  color: #fff;
  cursor: not-allowed;
}

._css-drop-itemcnt {
  flex: none;
  min-width: 18px;
  height: 22px;
  line-height: 22px;
  margin-right: 12px;
  margin-left: 12px;
  color: rgba(0, 0, 0, 0.25);
}
._css-drop-itemlabel {
  flex: 1;
  margin-left: 28px;
  text-align: left;
  color: rgba(0, 0, 0, 0.65);
  height: 38px;
  line-height: 38px;
}
._css-drop-item:hover ._css-drop-itemlabel {
  color: rgba(0, 0, 0, 0.85);
}
._css-drop-item {
  height: 40px;
  box-sizing: border-box;
  display: flex;
  align-items: center;
}
._css-drop-item:hover {
  background-color: rgba(0, 0, 0, 0.04);
}
._css-drop-items {
  position: absolute;
  width: 100%;
  top: calc(100% + 6px);
  left: 0;
  background-color: rgba(255, 255, 255, 1);
  box-shadow: 0px 4px 20px 0px rgba(0, 0, 0, 0.15);
  border-radius: 2px;
  z-index: 4;
  max-height: 288px;
  overflow-y: auto;
  padding-top: 4px;
  padding-bottom: 4px;
}
._css-materialrel-cateselect {
  height: 32px;
  width: 314px;
  display: flex;
  align-items: center;
  /* cursor: pointer; */
  color: rgba(0, 0, 0, 0.85);
  position: relative;
}
._css-cateselect-icon {
  width: 28px;
  height: 28px;
  flex: none;
  margin-right: 12px;
  margin-left: 8px;
  font-size: 28px;
  visibility: hidden;
}
._css-cateselect-name {
  flex: 1;
  margin-left: 12px;
  height: 28px;
  line-height: 28px;
  text-align: left;
  font-size: 20px;
  overflow-x: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
._css-radio {
  width: 14px;
  height: 14px;
  border-radius: 50%;
  border: 1px solid rgba(0, 0, 0, 0.45);
  display: flex;
  align-items: center;
  justify-content: space-around;
  cursor: pointer;
}

._css-radio:hover {
  border: 1px solid #1890ff;
}

._css-radio ._css-radio-in {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: #1890ff;
  display: none;
}
._css-checked {
  border-color: #1890ff;
}
._css-radio._css-checked ._css-radio-in {
  display: block;
}
/* .el-list-table /deep/ .el-table__body-wrapper .el-table__row{
  height: 60px;
  line-height:60px;
} */
 .el-list-table /deep/ .el-table td{
   border:1px solid rgba(0,0,0,.09);
 }
.el-list-table {
  flex: none !important;
  width: 550px;
  background:#fff;
  
  height: calc(100% - 82px) !important;
  /* height:auto; */
}
/* .el-table__fixed-right-patch{
    background-color:red;
} */
.right-conter {
  height: 100% !important;
}
._css-dialog-wp-title{
  height:44px;
  line-height:44px;
  border-bottom:1px solid rgba(0,0,0,.09);
  margin-bottom:16px;
}
._css-dialog-wp-title span{
  font-size:16px;
  font-weight:900;
}
._css-dialog-wp-title-close{ 
  position:absolute;
  right:17px;
  top:7px;
  color:rgba(0, 0, 0, 0.35);
  padding:5px;
  cursor:pointer;
}
.close-relation-model{
  font-size:16px;
  font-weight:500;
  color:rgba(0, 0, 0, 0.85);
  margin-left:12px;
} 
.right-conter /deep/ .el-table td, .right-conter /deep/ .el-table th.is-leaf{
  border-right:1px solid rgba(0,0,0, 0.09) !important;
}
.right-conter /deep/ .el-table td{
  border-bottom-color:rgba(0,0,0, 0.09) !important;
}
 
.right-conter /deep/ .el-table tr{
  /* border-bottom-color:rgba(0,0,0, 0.09) !important; */
  border-right-color:rgba(0,0,0, 0.09) !important;
} 
.right-conter /deep/ .el-table__body-wrapper .el-table__row{
  height: 40px !important;
  border-bottom: 1px solid rgba(0, 0, 0, .09);
  line-height: 40px !important;
}
.right-conter /deep/ .el-table__header-wrapper tr{
  height: 44px !important;
  border-bottom: 1px solid rgba(0, 0, 0, .09);
  border-right: 1px solid rgba(0, 0, 0, .09);
  line-height: 44px !important;
}  
.right-conter /deep/ .el-table td:last-child, .right-conter /deep/ .el-table th.is-leaf:last-child{ 
  border-right: none !important; 
} 
.right-conter /deep/ .el-table__header-wrapper .cell, 
.right-conter /deep/ .el-table__header-wrapper th div,
.right-conter /deep/ .el-table--border td:first-child .cell, 
.right-conter /deep/ .el-table--border th:first-child .cell{
  padding:0;
}
.right-conter /deep/ .el-table .cell {
  line-height: initial;
  padding:0 16px !important;
}
</style>