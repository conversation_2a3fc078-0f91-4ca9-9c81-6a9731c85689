<template>
    
  <div class="auto-relation-wrapper">
      <selectDialog
        style="z-index:1002"
        @getModelInfo="getModelInfo"
        @close=close
        v-if="selectModelShow"
        ></selectDialog>
        <dialogwp
        style="z-index:1001"
        width="390px"
        @maskClick=maskClick
        @close=maskClick
        v-if="dialogShow"
        title="自动关联模型">
            <div slot="btn"><div style="background:rgba(24,144,255,1);color:#fff;" @click="submit">开始关联</div></div>
            <div slot="main" class="auto-relation-main">
                <p>请选择模型</p>
                <div class="add" @click="openSelectView"><i class="icon-interface-addnew"></i> <span>添加模型</span></div>
                <ul class="chose-list" v-show="choseModelList.length>0">
                    <li v-for="(item,index) in choseModelList" :key="item.modelid">
                        <i class="icon-interface-associated-component"></i>
                        <span>{{item.modelname}}</span>
                        <i class="icon-suggested-close_circle" @click="delModelList(index)"></i>
                    </li>
                </ul>
                
                <p>构件列表中的属性名称</p>
                <el-select v-model="elementpropertyval" placeholder="请选择" style="margin-top:15px;">
                    <el-option
                    v-for="item in options"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value">
                    </el-option>
                </el-select>
                <p>模型中构件属性名称</p>
                <el-input v-model="elementName" placeholder="请输入内容" class="auto-input"></el-input>
                <el-radio-group v-model="relationTypeRadio" class="mb20">
                    <el-radio :label="0">新增关联构件</el-radio>
                    <el-radio :label="1">覆盖已关联构件</el-radio> 
                </el-radio-group>
            </div>
        </dialogwp>
  </div>
</template>

<script>
import selectDialog from '../CompsMaterial/CompsSelectModel'
import dialogwp from '../CompsDialog/CompsDialogProgress'
export default {
  components:{dialogwp,selectDialog},
  props:{},
  data(){
    return {
        elementpropertyval:'',
        options:[
            {
            value: 'bm_materialcode',
            label: '编码'
            },
            {
            value: 'bm_materialname',
            label: '任务项'
            }

        ],
        selectModelShow:false,
        dialogShow:true,
        elementName:'',
        choseModelList:[],
        relationTypeRadio: 0,
    }
  },
  watch:{},
  computed:{},
  methods:{
      delModelList(index){
          this.choseModelList.splice(index,1)
      },
      openSelectView(){
          this.selectModelShow = true
      },
      close(){
          this.selectModelShow = false
      },
      getModelInfo(info){
        //   console.log(info)
          this.choseModelList = [];   //  原逻辑为添加模型时候是多选(多个模型)，现将自动关联改为了只可关联一个模型
          this.choseModelList.push(info)
          this.selectModelShow = false
      },
      maskClick(){
          //this.dialogShow = false
          this.$emit("autoRelmaskclick");
      },
      submit(){
        //   console.log(this.choseModelList);
        //   console.log(this.elementpropertyval);
        //   console.log(this.elementName);
          this.$emit("onsubmit", this.choseModelList, this.elementpropertyval, this.elementName,this.relationTypeRadio);
      }
  },
  created(){},
  mounted(){}
}
</script>
<style lang="stylus" scoped rel="stylesheet/stylus">
    .auto-relation-main{
        padding 0 24px
        p{
            text-align left 
            margin-top 15px
            font-size 14px
            color rgba(0,0,0,.65)
        }
        .chose-list{
            width 100%
            max-height 200px
            overflow auto
            li{
                background rgba(247,247,247,1) 
                height 40px
                line-height 40px
                border-radius 4px
                margin-top 8px
                display flex
                flex-direction row
                align-items center
                justify-content space-between
                padding 0 16px
                color rgba(0,0,0,.65)
                span{
                    margin-left 12px
                    flex 1
                    text-align left 
                    overflow hidden
                    text-overflow ellipsis
                    white-space nowrap
                }
                i:last-child{
                    cursor pointer
                }
            }
        }
        .add{
            cursor pointer
            height 40px
            line-height 40px
            border-radius 4px
            border 1px solid rgba(24,144,255,1)
            color #1890FF
            margin-top 15px
            display flex
            flex-direction row
            align-items center
            i {
                margin-left 16px
            }
            span{
                margin-left 12px
            }
        }

    }
    .auto-relation-wrapper .auto-input,.mb20{
        margin-bottom: 20px !important;
    }
</style>