<template>
  <div class="_css-materialinfo-all" :style="gethoverstyle()">
    <div
      @click.stop="closeall"
      v-drag="draggreet"
      :style="dragstyle"
      class="_css-materialinfo-front"
    >
      <!-- 通用的对话框头组件 -->
      <CompsDialogHeader @oncancel="_oncancel" title="单元详情"></CompsDialogHeader>
      <!-- //通用的对话框头组件 -->

      <!-- 顶部的元素：查看外部页面 -->
      <!-- <div  class="_css-view-outerpage"
      :class="{'_css-1890ff':externalLinksShow}"
      >
          <div class="_css-view-op-icon icon-interface-link" ></div>
          <div 
          @click="func_switchouter($event)"
          class="_css-view-op-text">{{externalLinksShow?"隐藏":'显示'}}外部页面</div>
      </div> -->
      <!-- //顶部的元素：查看外部页面 -->

      <!-- 内容区域 -->
      <div @mousedown="_stopPropagation($event)" class="_css-materialinfo-body" id="id_body"
      element-loading-text="加载中"
      v-loading="m_allloading"
      >
        <div class="_css-materialbody-left _css-materialbody-part">
          <div class="_css-material-state-top">


            <!-- 显示状态下拉的地方 -->
            <!-- <div class=" _css-material-statusouter">
              <div class=" _css-material-statusinner" @click.stop="showstatuslist()" >
                <div
                  class="_css-material-statusin"
                  :style="getcurrentstatusstyle()"
                >
                  <div class="_css-material-statusin-title">
                    {{extdata.materialstatusobj.statusname}}
                  </div>
                  <p>
                    <i class="_css-statusin-up-down icon-arrow-down_outline"></i>
                  </p>
                </div> 
              </div>

              <div v-if="extdata.isshow_filter_mat_status" class="_css-matertialstatusoptions">
                <div class="_css-mat-status-options css-miniscroll">
                  <div
                    class="_css-mat-status-option"
                    v-for="status in allstatus"
                    :key="status.statusid"
                  >
                    <div
                      @click.stop="modifystatus(status)"
                      class="_css-mat-status-optionicon"
                    >
                      <div
                        class="_css-mat-status-optionicon-in"
                        :style="getstatusstyle(status.statusmaincolor)"
                      >{{status.statusname}}</div>
                    </div>
                  </div> 
                </div>
              </div>
            </div> -->
              <!-- //显示状态下拉的地方 -->




            <div class="_css-right-basicinfo _css-part-item">
              
              <div class="_css-right-state-img">
                <div class="_css-right-qrcode">
                   <!-- <i class="_css-right-qrcode-icon icon-interface-erweima"></i> -->
                  <!-- <span class="_css-qrcode-shade"></span> -->
                  <img :src="getQRcode(bm_guid)" class="_css-right-qrcode-icon">
                  <!-- <div class="_css-right-down-img" @click.stop="handleDownQRcode">
                    <i class="_css-right-down-icon icon-interface-download-fill"></i>
                  </div> -->
                </div>
              </div>
            </div>

            <!-- 可点击下载的 label -->
            <div class="_css-clickdownload-label">
              <div class="_css-clickdownload-labelinner"
              @click="handleDownQRcode"
              >
                <div class="_css-clickdownload-labeltext">
                  扫码跟踪单元信息
                </div>
                <div class="_css-clickdownload-labelicon icon-interface-download-fill">

                </div>
              </div>
            </div>
            <!-- //可点击下载的 label -->

          </div>
          <!-- <div class="_css-left-basicinfo _css-part-item">
            <div class="_css-common-title">基本信息</div>
          </div>-->
          <div class="_css-left-custominfo _css-part-leave _css-part-item css-miniscroll">
            <div class="_css-common-title">基本信息</div>
            
            <div class="_css-fieldarea">
              <div class="_css-fieldlabel">编码</div>
              <!-- 对编码正在进行编辑 -->
              <input
                @keyup.stop="canceledit($event,'bm_materialcode', extdata.FullData.bm.bm_materialcode)"
                @change.stop="blurtosave('bm_materialcode', extdata.FullData.bm.bm_materialcode)"
                v-if="extdata.isediting['bm_materialcode'] == true"
                class="_css-fieldval"
                type="text"
                :class="!buttonEditClick?'':'_css-cpointerandhaveborder'"
                :disabled="buttonEditClick"
                v-model="extdata.FullData.bm.bm_materialcode"
              />
              <!-- //对编码正在进行编辑 -->
              <!-- 只读的编码 -->
              <input
                @click.stop="clicktoedit('bm_materialcode')"
                v-else
                :readonly="buttonEditClick"
                class="_css-fieldval _css-cpointerandnoborder"
                :class="!buttonEditClick?'':'_css-cpointerandhaveborder'"
                type="text"
                v-model="extdata.FullData.bm.bm_materialcode"
              />
              <!-- //只读的编码 -->
            </div>
            <div class="_css-fieldarea _css-first">
              <div class="_css-fieldlabel">单元名称</div>
              <!-- <input @click.stop="clicktoedit()" readonly class="_css-fieldval" type="text" v-model="extdata.FullData.bm.bm_materialname" /> -->
              <!-- 对构件名称正在进行编辑 -->
              <input
                @keyup.stop="canceledit($event, 'bm_materialname', extdata.FullData.bm.bm_materialname)"
                @change.stop="blurtosave('bm_materialname', extdata.FullData.bm.bm_materialname)"
                v-if="extdata.isediting['bm_materialname'] == true"
                class="_css-fieldval"
                type="text"
                :class="!buttonEditClick?'':'_css-cpointerandhaveborder'"
                v-model="extdata.FullData.bm.bm_materialname"
              />
              <!-- //对构件名称正在进行编辑 -->
              <!-- 只读的构件名称 -->
              <input
                @click.stop="clicktoedit('bm_materialname')"
                v-else
                :readonly="buttonEditClick"
                class="_css-fieldval _css-cpointerandnoborder"
                :class="!buttonEditClick?'':'_css-cpointerandhaveborder'"
                type="text"
                v-model="extdata.FullData.bm.bm_materialname"
              />
              <!-- //只读的构件名称 -->
            </div>
            <div class="_css-fieldarea _css-first">
              <div class="_css-fieldlabel">型号</div>
              <!-- <input @click.stop="clicktoedit()" readonly class="_css-fieldval" type="text" v-model="extdata.FullData.bm.bm_materialmodel" /> -->
              <!-- 对型号正在进行编辑 -->
              <input
                @keyup.stop="canceledit($event, 'bm_materialmodel', extdata.FullData.bm.bm_materialmodel)"
                @change.stop="blurtosave('bm_materialmodel', extdata.FullData.bm.bm_materialmodel)"
                v-if="extdata.isediting['bm_materialmodel'] == true"
                class="_css-fieldval"
                :class="!buttonEditClick?'':'_css-cpointerandhaveborder'"
                type="text"
                v-model="extdata.FullData.bm.bm_materialmodel"
              />
              <!-- //对型号正在进行编辑 -->
              <!-- 只读的型号 -->
              <input
                @click.stop="clicktoedit('bm_materialmodel')"
                v-else
                :readonly="buttonEditClick"
                :class="!buttonEditClick?'':'_css-cpointerandhaveborder'"
                class="_css-fieldval _css-cpointerandnoborder"
                type="text"
                v-model="extdata.FullData.bm.bm_materialmodel"
              />
              <!-- //只读的型号 -->
            </div>
            <div class="_css-fieldarea">
              <div class="_css-fieldlabel">数量</div>

              <!-- <div class="_css-fieldval">{{extdata.FullData.bm.bm_materialcount}}</div> -->
              <!-- <input readonly class="_css-fieldval" type="text" v-model="extdata.FullData.bm.bm_materialcount" /> -->

              <!-- 对数量正在进行编辑 -->
              <input
                @keyup.stop="canceledit($event, 'bm_materialcount', extdata.FullData.bm.bm_materialcount)"
                @change.stop="blurtosave('bm_materialcount', extdata.FullData.bm.bm_materialcount)"
                v-if="extdata.isediting['bm_materialcount'] == true"
                class="_css-fieldval"
                :class="!buttonEditClick?'':'_css-cpointerandhaveborder'"
                type="text"
                :disabled="buttonEditClick"
                v-model="extdata.FullData.bm.bm_materialcount"
              />
              <!-- //对数量正在进行编辑 -->
              <!-- 只读的数量 -->
              <input
                @click.stop="clicktoedit('bm_materialcount')"
                v-else
                :readonly="buttonEditClick"
                :class="!buttonEditClick?'':'_css-cpointerandhaveborder'"
                class="_css-fieldval _css-cpointerandnoborder"
                type="text"
                v-model="extdata.FullData.bm.bm_materialcount"
              />
              <!-- //只读的数量 -->
            </div>
            <div class="_css-fieldarea _css-unitedit-container">
              <div class="_css-fieldlabel">单位</div>
              <!-- <div class="_css-fieldval">{{extdata.FullData.bm.bm_materialunit}}</div> -->
              <input
                @keyup.stop="canceledit($event)"
                @click.stop="switchunitoptions()"
                :class="!buttonEditClick?'':'_css-cpointerandhaveborder'"
                class="_css-fieldval _css-unitedit"
                type="text"
                readonly
                :disabled="buttonEditClick"
                v-model="extdata.FullData.bm.bm_materialunit"
              />

              <!-- 所有下拉项 -->
              <div v-if="extdata.bIsShowingUnitOptions == true" class="_css-unitedit-options">
                <div @click="modifyunit('个')" class="_css-unitedit-option">个</div>
                <div @click="modifyunit('吨')" class="_css-unitedit-option">吨</div>
              </div>
              <!-- //所有下拉项 -->
            </div>
            <div class="_css-fieldarea _css-first">
              <div class="_css-fieldlabel">厂家</div>
              <!-- <input @click.stop="clicktoedit()" readonly class="_css-fieldval" type="text" v-model="extdata.FullData.bm.bm_materialfac" /> -->
              <!-- 对厂家正在进行编辑 -->
              <input
                @keyup.stop="canceledit($event, 'bm_materialfac', extdata.FullData.bm.bm_materialfac)"
                @change.stop="blurtosave('bm_materialfac', extdata.FullData.bm.bm_materialfac)"
                v-if="extdata.isediting['bm_materialfac'] == true"
                class="_css-fieldval"
                type="text"
                :class="!buttonEditClick?'':'_css-cpointerandhaveborder'"
                v-model="extdata.FullData.bm.bm_materialfac"
              />
              <!-- //对厂家正在进行编辑 -->
              <!-- 只读的厂家 -->
              <input
                @click.stop="clicktoedit('bm_materialfac')"
                v-else
                :readonly="buttonEditClick"
                class="_css-fieldval _css-cpointerandnoborder"
                type="text"
                :class="!buttonEditClick?'':'_css-cpointerandhaveborder'"
                v-model="extdata.FullData.bm.bm_materialfac"
              />
              <!-- //只读的厂家 -->
            </div>
            <div class="_css-common-title" v-if="extdata.customobj.length>0">自定义信息</div>
            <!-- 自定义区域 -->
            <div
              v-for="(customkv, index) in extdata.customobj"
              :key="customkv.fieldid + '_' + index"
              class="_css-fieldarea"
            >
              <div 
              :title="customkv.fieldname"
              class="_css-fieldlabel">{{customkv.fieldname}}</div>

              <!-- <input readonly class="_css-fieldval" type="text" v-model="customkv.fieldval" /> -->

              <!-- 对编码正在进行编辑 -->
              <input
                @keyup.stop="canceledit_cus($event)"
                @change.stop="blurtosave_cus()"
                v-if="testThisFieldIsEditing(customkv.fieldname)"
                class="_css-fieldval"
                type="text"
                :disabled="buttonEditClick"
                v-model="customkv.fieldval"
              />
              <!-- //对编码正在进行编辑 -->
              <!-- 只读的编码 -->
              <input
                @click.stop="clicktoedit_cus(customkv.fieldname)"
                v-else
                :readonly="buttonEditClick"
                class="_css-fieldval _css-cpointerandnoborder"
                :class="!buttonEditClick?'':'_css-cpointerandhaveborder'"
                type="text"
                v-model="customkv.fieldval"
              />
              <!-- //只读的编码 -->
            </div>
            <!-- //自定义区域 -->
            <!-- <div class="_css-fieldarea"> -->
            <!-- <div class="_css-fieldlabel">计划到场时间</div> -->

            <!-- <div class="_css-fieldval">{{(extdata.FullData.bm.bm_planarrtime || '--').replace('T', ' ')}}</div> -->
            <!-- <div class="_css-plantimediv">
                <el-date-picker
                  @change="change_time"
                  v-model="extdata.FullData.bm.bm_planarrtime"
                  format="yyyy-MM-dd HH:mm"
                  type="datetime"
                  align="left"
                  :picker-options="pickerOptions"
                  placeholder="请选择时间"
                >
                  <div class="_css-material-timelabel _css-material-label _css-hoverin-045">计划到场时间</div>
                </el-date-picker>
            </div>-->
            <!-- </div> -->

            
          </div>
          <div class="_css-left-edit-bottom">
            <template v-if="auth_mtreditable" >
              <div class="_css-bottom-edit-button" v-if="buttonEditClick" @click.stop="handelClickbuttonEdit">编辑</div>
              <div class="_css-bottom-two-button" v-if="!buttonEditClick">
                <div class="_css-bottom-sure _css-bottom-cancel" @click.stop="handelClickCancel">取消</div>
                <div class="_css-bottom-sure" @click.stop="handelClickSure">确定</div>
              </div>
            </template>
            <template v-else >
              <div class="_css-bottom-edit-button css-dis" >编辑</div>
            </template>
          </div>
        </div>
        <div class="_css-materialbody-splitter"></div>
        <div class="_css-materialbody-center _css-materialbody-part">
          
          <!-- 上方显示区域：关联内容，关联现场数据，关联进度任务 h30 t22 b? -->
          <div class="_css-contents-topbtnarea" >
            <div class="_css-topbtn-i _css-first "
            :class="{'_css-focus':m_contentshowingtype == 'content'}"
            @click="func_changecontentshowing('content')"
            >附件</div>
            <div 
            v-if="auth_examinevisible"
            class="_css-topbtn-i"
            :class="{'_css-focus':m_contentshowingtype == 'exam'}"
            @click="func_changecontentshowing('exam')"
            >现场数据</div>
            <div
            v-if="auth_flowvisible"
             class="_css-topbtn-i"
            :class="{'_css-focus':m_contentshowingtype == 'progress'}"
            @click="func_changecontentshowing('progress')"
            >项目流程</div>
          </div>
          <!-- //上方显示区域：关联内容，关联现场数据，关联进度任务 h30 t22 b? -->

          <!-- 高度为40的按钮区域 -->
          <div class="_css-addbtnsarea-content" v-if="m_contentshowingtype == 'content'" >
                <template v-if="auth_mtreditable">
                  <div class="_css-rel-input" @click.stop="open_relfile">
                    <span class="_css-relinput-label">添加关联附件</span>
                    <i class="icon-dl-doc _css-relinput-icon"></i>
                  </div>
                    <div class="_css-rel-input" @click.stop="open_relimg">
                    <span class="_css-relinput-label">添加关联图片 </span>
                    <i class="icon-interface-image _css-relinput-icon"></i>
                  </div>
                </template>
                <template v-else>
                  <div class="_css-rel-input css-dis" >
                    <span class="_css-relinput-label">添加关联附件（需要编辑权限）</span>
                    <i class="icon-dl-doc _css-relinput-icon"></i>
                  </div>
                    <div class="_css-rel-input css-dis" >
                    <span class="_css-relinput-label">添加关联图片（需要编辑权限）</span>
                    <i class="icon-interface-image _css-relinput-icon"></i>
                  </div>
                </template>
          </div>
          <!-- //高度为40的按钮区域 -->

          <div class="_css-material-state-bottom" >
            <div class="_css-button-three">
                
              <!-- 三个关联按钮 -->
              <div class="_css-rel-centercontents">
                <!-- <div class="_css-rel-btn">
                  <div class="_css-btn-icon icon-interface-associated-component"></div>
                  <div class="_css-btn-text">关联构件</div>
                </div>-->



                <!-- 已关联附件显示区域 -->
                <div class="_css-attachments" v-if="m_contentshowingtype == 'content'">
                  <div class="_css-rel-btn _css-rel-noneicon-btn">
                    <!-- <i class="_css-btn-icon icon-interface-folder"></i> -->
                    <span class="_css-btn-text">关联附件</span>
                  </div>
                  <div 
                  class="_css-rel-rightarea"
                  v-if="extdata.FullData.brs_fileinfo.length > 0">
                    <!-- <div class="_css-fieldarea _css-first">
                      <div class="_css-fieldlabel  _css-fieldlabel-title">已关联附件</div>
                    </div> -->
                    <div
                      v-for="f in extdata.FullData.brs_fileinfo"
                      :key="f.RelationID"
                      @click.stop="previewdoc(f.file.FileId, f.file.FileName)"
                      class="_css-relation-item _css-associated-attachments-list"
                    >
                      <div
                        class="_css-relationi-icon"
                        :class="$staticmethod.getIconClassByExtname(f.file.FileName, 1)"
                      ></div>
                      <div class="_css-relationi-text">{{f.file.FileName}}</div>
                      <div
                        @click.stop="clickDownattachment(f.file.FileId, f.file.FileName)"
                        class="_css-relationi-delfile _css-down-attachment icon-interface-download-fill"
                      ></div>
                      <div v-if="auth_mtreditable"
                        @click.stop="removeattachment(f.file.FileId, f.file.FileName)"
                        class="_css-relationi-delfile icon-suggested-close_circle"
                      ></div>
                    </div>
                  </div>
                </div>
                <!-- //已关联附件显示区域 -->

                   <div class="_css-attachments _css-notfirst"  v-if="m_contentshowingtype == 'content'">
                      <div class="_css-rel-btn _css-rel-noneicon-btn">
                        <!-- <i class="_css-btn-icon icon-interface-model-picture"></i> -->
                        <span class="_css-btn-text">关联图片</span>
                      </div>

                      <div class="_css-rel-imgs" v-if="extdata.FullData.brbfs.length > 0">
                        <div class="_css-rel-body-imagebody-smallimg">
                          <div class="_css-rel-body-imagein-smallimg">
                            <!-- <div class="_css-rel-imagehead">{{extdata.currentshowtitle}}</div> -->
                            <div class="_css-rel-imagebottom-smallimg">
                              <!-- 向左翻动 -->
                              <!-- <div class="_css-relimage-left _css-relimage-lr" @click.stop="clickscroll(-1)">
                                <div class="icon-arrow-left_outline _css-lrbtn"></div>
                              </div> -->
                              <!-- //向左翻动 -->

                              <!-- 中间的小图区域 -->
                              <div
                                id="id_relimage_middle"
                                class="_css-rel-rightarea"
                              >
                                <!-- class="_css-relimage-middlearea css-miniscroll css-littlescroll" -->
                                <div
                                  v-for="bf in extdata.FullData.brbfs"
                                  :key="bf.bf_guid"
                                    @click.stop="setcurrentshowimg(bf.fileobj)"
                                      class="_css-relation-item _css-associated-attachments-list"
                                >
                                  <div
                                    class="_css-relationi-icon"
                                    :class="$staticmethod.getIconClassByExtname(bf.fileobj.bf_filename, 1)"
                                  ></div>
                                  <div class="_css-relationi-text">{{bf.fileobj.bf_filename}}</div>
                                  <div v-if="auth_mtreditable"
                                    @click.stop="removeexistsimg(bf.fileobj.bf_guid, bf.fileobj.bf_filename)"
                                    class="_css-relationi-delfile icon-suggested-close_circle"
                                  ></div>
                                </div>
                              </div>
                              <!-- //中间的小图区域 -->
                              
                            </div>
                          </div>
                        </div>
                      </div>
                   </div>

                  <div class="_css-attachments _css-thewhole" v-if="m_contentshowingtype == 'exam'">
                    <CompsShowRelExams
                    :init_bmguid="bm_guid"
                    ></CompsShowRelExams>
                  </div>

                  <div class="_css-attachments _css-thewhole" v-if="m_contentshowingtype == 'progress'">
                    <CompsShowRelFlows
                    :init_bmguid="bm_guid"
                    ></CompsShowRelFlows>
                  </div>
             
              </div>
              <template v-if="extdata.componentDetails != '' && m_contentshowingtype == 'content'">
                  <div class="_css-fieldarea _css-first" ref="associatedModel">
                    <div class="_css-fieldlabel _css-fieldlabel-title _css-fieldmodel">关联模型</div>
                    <div class="_css-fieldlabel _css-fieldlabel-title _css-fieldlabel-cancel _css-fieldmodel">
                      <p @click="cancelRelevance">取消关联模型</p>
                    </div>
                  </div>

                  <div
                    class="_css-relation-item _css-associated-model"
                    v-for="(item,index) in extdata.componentDetails"
                    :key="index"
                    @click.stop="handelAssociatedModel($event,item)"
                  >
                    <div class="_css-relation-model-title">
                      <div class="_css-relationi-icon _css-details-icon icon-interface-associated-component"></div>
                      <div class="_css-relationi-text">
                        {{item.elementID}}
                        <span class="_css-relation-name">• {{item.name}}</span>
                      </div>
                    </div>
                   
                  </div>
                </template>
            </div>
            <!-- //三个关联按钮 -->
            <!-- <div v-if="extdata.FullData.brbfs.length > 0" class="_css-fieldarea _css-first">
              <div class="_css-fieldlabel _css-fieldlabel-title">已关联图片</div>
            </div> -->
            <!-- 如果有关联的图片 -->
            <!-- <div class="_css-rel-imgs" v-if="extdata.FullData.brbfs.length > 0">
              <div class="_css-rel-body-imagebody">
                <div
                  :style="getImageBgStyle(extdata.currentshowurl, 'cover')"
                  class="_css-rel-body-imagein"
                >
                  <div class="_css-rel-imagehead">{{extdata.currentshowtitle}}</div>
                  <div class="_css-rel-imagebottom">
                    向左翻动--注释
                    <div class="_css-relimage-left _css-relimage-lr" @click.stop="clickscroll(-1)">
                      <div class="icon-arrow-left_outline _css-lrbtn"></div>
                    </div>
                    //向左翻动--注释

                    中间的小图区域--注释
                    <div
                      id="id_relimage_middle"
                      class="_css-relimage-middlearea css-miniscroll css-littlescroll"
                    >
                      <div
                        v-for="bf in extdata.FullData.brbfs"
                        :key="bf.bf_guid"
                        class="_css-relimage-middlei"
                      >
                        <div
                          @click.stop="removeexistsimg(bf.fileobj.bf_guid, bf.fileobj.bf_filename)"
                          class="icon-suggested-close_circle _css-suggested-close_circle"
                        ></div>
                        <div
                          class="_css-relimage-middlein"
                          @click.stop="setcurrentshowimg(bf.fileobj)"
                          :style="getImageBgStyle(bf.fileobj.bf_path)"
                        ></div>
                      </div>
                    </div>
                    //中间的小图区域--注释

                    向右翻动--注释
                    <div class="_css-relimage-right _css-relimage-lr" @click.stop="clickscroll(1)">
                      <div class="icon-arrow-right_outline _css-lrbtn"></div>
                    </div>
                    //向右翻动--注释
                  </div>
                </div>
              </div>
            </div> -->
            <!-- //如果有关联的图片 -->
            <!-- <template v-if="extdata.FullData.brs_fileinfo.length > 0">
              <div class="_css-fieldarea _css-first">
                <div class="_css-fieldlabel  _css-fieldlabel-title">已关联附件</div>
              </div>
              <div
                v-for="f in extdata.FullData.brs_fileinfo"
                :key="f.RelationID"
                class="_css-relation-item"
              >
                <div
                  class="_css-relationi-icon"
                  :class="$staticmethod.getIconClassByExtname(f.file.FileName, 1)"
                ></div>
                <div class="_css-relationi-text">{{f.file.FileName}}</div>
                <div
                  @click.stop="removeattachment(f.file.FileId, f.file.FileName)"
                  class="_css-relationi-delfile icon-suggested-close_circle"
                ></div>
              </div>
            </template> -->
            <!-- <div class="_css-common-title">操作记录</div>
            <div class="_css-right-custominfo _css-part-leave _css-part-item css-miniscroll">
              <div v-for="r in extdata.materialrecords" :key="r.bmr_guid" class="_css-operation-item">
                <div class="_css-operationi-title">
                  <div
                    class="_css-operationit-icon"
                    :class="getIconClassByOperDescription(r.bmr_operDescription)"
                  ></div>
                  <div class="_css-operationit-operaname">{{r.bmr_userName}}</div>
                  <div class="_css-operationit-operatext">{{materialoperTrans(r.bmr_operDescription)}}</div>
                  <div class="_css-operationit-operaobjname">{{getFirstLevelName(r)}}</div>
                  <div class="_css-operationit-operatimetxt">{{r.bmr_operdate | fltpasttime}}</div>
                </div>
                <div v-if="willshowdetail(r.bmr_operDescription)" class="_css-operationi-detail">
                  <div class="_css-operationi-detail-l"></div>
                  <div class="_css-operationi-detail-splitter"></div>
                  <div class="_css-operationi-detail-r">
                    
                    <div
                      v-for="(boia,index) in r.bmr_operItemsArr"
                      :key="boia.Id"
                      :class="{'_css-first': index == 0}"
                      class="_css-operationi-detail-i"
                    >
                      <div
                        class="_css-operationi-detaili-icon"
                        :style="{'visibility':r.bmr_operDescription=='MtrOper_ConnectElement'?'hidden':'visible'}"
                        :class="$staticmethod.getIconClassByExtname(boia.Name, 1)"
                      ></div>
                      <div class="_css-operationi-detaili-text">{{boia.Name}}</div>
                      <div class="_css-operationi-detaili-leave">{{getCntIfRelEle(r, boia.Id)}}</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>-->
            <div class="_css-iframe _css-materialinfo" v-if="externalLinksShow">
              <iframe 
              @load="evt_iframeload"
              :src="externalLinks" frameborder="0" border="none" width="100%" :height="iframeHeight"></iframe>
            </div>
          </div>
        </div>

        <div class="_css-materialbody-splitter"></div>

        <div class="_css-materialrecord" >

          <!-- 上方显示区域：关联内容，关联现场数据，关联进度任务 h30 t22 b? -->
          <div class="_css-contents-topbtnarea" >
            <div class="_css-topbtn-i _css-first "
            :class="{'_css-focus':true}"
            >跟踪记录</div>
          </div>
          <!-- //上方显示区域：关联内容，关联现场数据，关联进度任务 h30 t22 b? -->

          <!-- 下方的可滚动区域 -->
          <div class="_css-contents-recordlist" >

            <div class="_css-contents-recordi"
            v-for="mtrr in extdata.materialrecords"
            :key="mtrr.bmr_guid"
            >
              <div class="_css-contents-recordi-title">
                <div class="_css-recordi-icon " 
                :class="func_getIconClass(mtrr)"
                ></div>
                <div class="_css-recordi-label" >{{mtrr.bmr_userName}} {{mtrr.bmr_operDescription | flt_operstr}}</div>
                <div class="_css-recordi-dt" >{{mtrr.bmr_operdate | flt_timestr}}</div>
              </div>
              <div class="_css-contents-recordi-body"
              v-if="func_getAttachmentObj(mtrr) != null"
              >
                <div class="_css-imageitem">
                  <div class="_css-imageitem-icon" 
                  :class="$staticmethod.getIconClassByExtname(func_getAttachmentName(mtrr), 1)"
                  ></div>
                  <div class="_css-imageitem-text"
                  :class="{'_css-isdel':func_isdel(mtrr)}"
                   >{{func_getAttachmentName(mtrr)}}</div>
                </div>

              </div>
            </div>

          </div>
          <!-- //下方的可滚动区域 -->

        </div>


      </div>
      <!-- //内容区域 -->
    
    <!-- 点击查看大图 -->
      <div class="_css-detail-bigimg" v-show="detailBigImgShow">
        <div class="_css-typeitem-title">
          <p class="_css-typeitem-opentitle">{{detailBigImgName}}</p>
          <span class="_css-typeitem-close icon-interface-guanbimoxing" @click.stop="closeall()"></span>
        </div>
        <div class="_css-detail-contain-text">
          <div class="_css-detail-contain">
            <img :src="detailBigImgSrc" ref="containImg">
          </div>
        </div>
      </div>
    <!-- 点击查看大图 -->


 
      <!-- 点击已关联模型-明细 -->
    <div class="_css-click-model-detail" :style="getmodeldetailstyle()">
      <div class="_css-typeitem-title">
        <p class="_css-typeitem-opentitle">{{modeldetailtypeName}}</p>
        <span class="_css-typeitem-close icon-interface-guanbimoxing" @click.stop="closeall()"></span>
      </div>
      <div 
        class="_css-moldel-content" 
        :style="getmodeldetailcontentstyle()"
      >
        <div class="_css-model-custom"  v-for="(item,index) in extdata.modelDetailList"
        :key="index">
          <p class="_css-model-custom-title">{{item[0].group}}</p>
          <ul v-for="(itemChild,indexChild) in item" :key="indexChild">
            <li>
              <label>{{itemChild.name}}</label><span>{{itemChild.value}}</span>
            </li>
          </ul>
        </div>
      </div>
    </div>

    <!-- 点击已关联模型-明细 -->
    </div>

    <!-- 添加关联图片的input -->
    <input
      type="file"
      style="display:none"
      id="id_relimgopener"
      accept="image/*"
      @click.stop="_stopPropagation($event)"
      @change="on_relimgopener_change()"
    />
    <!-- //添加关联图片的input -->

    <!-- 添加关联文件的input -->
    <input
      type="file"
      style="display:none"
      id="id_relfileopener"
      @click.stop="_stopPropagation($event)"
      @change="on_relfileopener_change()"
    />
    <!-- //添加关联文件的input -->

    
  </div>
</template>
<script>
import CompsDialogHeader from "@/components/CompsDialog/CompsDialogHeader";
import CompsShowRelExams from "@/components/CompsCommon/CompsShowRelExams";
import CompsShowRelFlows from "@/components/CompsCommon/CompsShowRelFlows";
export default {
  components: {
    CompsDialogHeader,
    CompsShowRelExams,
    CompsShowRelFlows
  },
  data() {
    return {
      _OrganizeId: '',
      // 流程可见权限
      // -----------
      auth_flowvisible: true,

      // 现场数据可见权限
      // ---------------
      auth_examinevisible:true,

      // 权限编辑权限
      // -----------
      auth_mtreditable: true,

      // 中间部分显示哪块
      // ---------------
      m_allloading: false,
      m_contentshowingtype: 'content', // exam, progress

      externalLinksShow: false,
      externalLinks: '',  // iframe地址
      iframeHeight: '500',
      buttonEditClick: true,
      pickerOptions: {},
      val: "0",
      dragstyle: {
        position: "fixed",
        right: "calc(50% - 672px)",
        top: "32px"
      },
      qrcodeurl: '',
      modeldetailtypeName: '',
      detailBigImgShow: false,
      detailBigImgName: '图片名称',
      detailBigImgSrc: '',
      extdata: {

        funcauthdata:undefined,
        isshow_filter_mat_status: false, // 是否显示状态下拉选项
        bIsShowingUnitOptions: false, // 是否显示单位选择下拉框

        customeditingfield: [], // 正在编辑的自定义字段

        // isediting_bm_materialname:false, // 正在编辑构件名称
        // isediting_bm_materialcode:false, // 正在编辑构件编码
        isediting: {
          bm_materialname: false, // 正在编辑构件名称
          bm_materialcode: false, // 正在编辑构件编码
          bm_materialcount: false // 正在编辑的数量
        }, 
        sureEditParam: {
          bc_guid_materialstatus: '',
          bm_materialname:'',
          bm_materialcode:'',
          bm_materialmodel:'',
          bm_materialcount:'',
          bm_materialunit:'',
          bm_materialfac:'',
          bm_extjson:''
        },
        materialrecords: [], // 构件操作记录
        materialstatusobj: {}, // 当前构件的状态数据对象
        customobj: [],
        iUnitScrollSize: 24, // 点击滚动的单位像素
        currentshowtitle: "", // 当前正在显示的图片名称
        currentshowurl: "", // 当前正在显示的图片路径（使用方法得到全地址）
        currentshowbfid: "", // 当前正在显示的图片的bf_guid
        componentDetails: [],
        FullData: {
          bm: {},
          bmrs: [],
          brbfs: [],
          brs_fileinfo: []
        },
        // showModelDetail:false,
        modeldetail: {
          left: 0,
          top:0,
          display:false,
          maxHeight:545
        },
        modeldetailcontent:{
          maxHeight: 500
        },
        model_id: '',
        modelDetailList: [],
        modelDetailListChild: []
      },
    };
  },
  created() {
    var _this = this;
    // 读取详情数据
    _this.getdetail();
  },
  mounted() {
    var _this = this;
    this._OrganizeId = _this.$staticmethod._Get("organizeId");
    window.materialinfovue = _this;
    _this.windowsizechange();
    window.onresize = function() {
      // console.log(177);
      _this.windowsizechange();
    };
    this.auth_mtreditable = this.$staticmethod.hasSomeAuth('GCJG_Edit')
    // // 权限数据
    // // -------
    // _this.testhasfuncbtnauth('MaterialsMgr', 'lr-edit', (v)=>{
    //   _this.auth_mtreditable = v;
    //   console.log(`_this.auth_mtreditable = ${_this.auth_mtreditable}`);
    // });

    // // 现场数据权限
    // // -----------
    // _this.extdata.funcauthdata = undefined;
    // _this.testhasfuncbtnauth('QualityManage', 'lr-visible', (v2)=>{
    //   _this.auth_examinevisible = v2;
    //   console.log(`_this.auth_examinevisible = ${_this.auth_examinevisible}`);
    // });

    // // 流程权限
    // // --------
    // _this.extdata.funcauthdata = undefined;
    // _this.testhasfuncbtnauth('workflow', 'lr-visible', (v3)=>{
    //   _this.auth_flowvisible = v3;
    //   console.log(`_this.auth_flowvisible = ${_this.auth_flowvisible}`);
    // });

  },

  filters: {

    flt_operstr(str) {
      var _this = this;
      if (!str) {
        return '';
      }
      if (str == 'MtrOper_Create') {
        return '创建了单元';
      } else if (str == 'MtrOper_AttachFileOrImage') {
        return '添加了关联内容';
      } else if (str == 'MtrOper_DettachFileOrImage') {
        return '移除了关联内容';
      } else if (str == 'MtrOper_ConnectElement') {
        return '关联了模型构件';
      }
    },

    flt_timestr(str) {
      var _this = this;
      if (!str) {
        return '';
      }
      var rafter = str.replace('T', ' ');
      var retstr = rafter.substr(0, rafter.length - 3);
      return retstr;
    },

    // 格式化已过去的时间，例：12小时前。如果大于1天，则显示日期 yyyy-MM-dd HH:mm
    fltpasttime(input) {
      //debugger;
      var _this = this;
      var now = new Date();
      var inputdate = new Date(input);
      var begintime_ms = Date.parse(now); //begintime 为开始时间
      var endtime_ms = Date.parse(inputdate); // endtime 为结束时间
      var msdiff = begintime_ms - endtime_ms; // 用现在减去过去的时间点

      // UTC时间转为北京时间，时间戳转为时间
      // var utc_datetime = "2017-03-31T08:02:06Z";
      function utc2beijing(utc_datetime) {
        // 转为正常的时间格式 年-月-日 时:分:秒
        var T_pos = utc_datetime.indexOf("T");
        var Z_pos = utc_datetime.indexOf("Z");
        var year_month_day = utc_datetime.substr(0, T_pos);
        var hour_minute_second = utc_datetime.substr(
          T_pos + 1,
          Z_pos - T_pos - 1
        );
        var new_datetime = year_month_day + " " + hour_minute_second; // 2017-03-31 08:02:06

        // 处理成为时间戳
        timestamp = new Date(Date.parse(new_datetime));
        timestamp = timestamp.getTime();
        timestamp = timestamp / 1000;

        // 增加8个小时，北京时间比utc时间多八个时区
        var timestamp = timestamp + 8 * 60 * 60;

        // 时间戳转为时间
        var beijing_datetime = new Date(parseInt(timestamp) * 1000)
          .toLocaleString()
          .replace(/年|月/g, "-")
          .replace(/日/g, " ");
        return beijing_datetime; // 2017-03-31 16:02:06
      }

      if (msdiff < 0) {
        return input;
      }

      // 计算出天数
      var daydiff = Math.floor(msdiff / (24 * 3600 * 1000));
      msdiff -= daydiff * (24 * 3600 * 1000);

      // 计算出小时
      var hdiff = Math.floor(msdiff / (3600 * 1000));
      msdiff -= hdiff * (3600 * 1000);

      // 计算出分钟
      var mdiff = Math.floor(msdiff / (60 * 1000));
      // debugger

      // 大于1天，直接显示时间
      if (daydiff > 1) {
        // return input;
        return input.replace("T", " "); //utc2beijing(input);
      } else if (daydiff > 0) {
        return "1天前";
      } else if (hdiff > 0) {
        return `${hdiff}小时前`;
      } else if (mdiff > 0) {
        return `${mdiff}分钟前`;
      } else {
        return "刚刚";
      }
    }
  },

  methods: {

    setfuncauthdata(callback) {
      var _this = this;
      // /api/User/Role/GetUserOrgFuncAuth?organizeId=48617e7b-07f2-4748-9199-238af8f2bfc6&Token=322D1C8F
      var _OrganizeId = _this.$staticmethod._Get("organizeId");
      var _Token = _this.$staticmethod.Get("Token");
      _this.$axios
        .get(
          `${window.bim_config.webserverurl}/api/User/Role/GetUserOrgFuncAuth?organizeId=${_OrganizeId}&Token=${_Token}`
        )
        .then(x => {
          if (x.status == 200) {
            if (x.data.Ret > 0) {
              if (x.data.Data) {
                _this.extdata.funcauthdata = x.data.Data;
                _this.$staticmethod.Set("funcauthdata", JSON.stringify(x.data.Data));
                if (callback) {
                  //debugger;
                  callback();
                }
              }
            }
          }
        })
        .catch(x => {});
    },

    // // 各个功能模块页面的通用权限判断函数1
    // // 需要有 extdata.funcauthdata
    // // 需要有 authdata
    // // -------------------------------
    // testhasfuncbtnauth(bmencode, bmbencode, callback) {
    //   var _this = this;
    //   //debugger;
    //   if (!_this.extdata.funcauthdata) {
    //     _this.setfuncauthdata(()=>{
    //       _this.testhasfuncbtnauth(bmencode, bmbencode, callback);
    //     });
    //     return;
    //   }
    //   var bmIndex = _this.extdata.funcauthdata.findIndex(
    //     x => x.Bm_EnCode == bmencode
    //   );
    //   if (bmIndex < 0) {
    //     callback && callback(false);
    //     return false; // 没有找到指定bm项
    //   }
    //   var bm = _this.extdata.funcauthdata[bmIndex];
    //   if (bm.checkstate == "0") {
    //     callback && callback(false);
    //     return false; // 权限设置中，所有角色的bm设置均没有打开。
    //   }
    //   if (bm.Bmbs.length == 0) {
    //     callback && callback(false);
    //     return false; // 功能模块下没有按钮
    //   }
    //   var hasAuth = bm.Bmbs.findIndex(
    //     x => x.Roles.length > 0 && x.checkstate == "1" && x.Bmb_EnCode == bmbencode
    //   ); // 这个功能模块下有有角色的，且为有权限的
    //   //callback && callback(hasAuth >= 0);
    //   //return hasAuth >= 0;

    //   if (callback) {
    //     callback(hasAuth >= 0);
    //   } else {
    //     return hasAuth >= 0;
    //   }
    // },

    evt_iframeload() {
      var _this = this;
      _this.m_allloading = false;
    },

    func_switchouter(ev) {
      var _this = this;
      if (_this.externalLinksShow) {
        _this.externalLinksShow = false;
        _this.m_allloading = false;
      } else {
        
        if (_this.externalLinks) {
          _this.externalLinksShow = true;

          // 显示 loading
          // ------------
          _this.m_allloading = true;

        } else {
          _this.$message.warning('当前单元分类无外部页面');
          _this.externalLinksShow = false;
          _this.m_allloading = false;
        }
      }
    },

    func_getIconClass(itemr) {
      var _this = this;
      if (itemr.bmr_operDescription == 'MtrOper_Create') {
        return 'icon-interface-addnew';
      } else {
        return 'icon-dl-nodeicon';
      }
    },

    func_isdel(itemr) {
      var _this = this;
      var arr = ['MtrOper_DettachFileOrImage'];
      if (itemr.bmr_operDescription && arr.indexOf(itemr.bmr_operDescription) >= 0) {
        return true;
      }
    },

    func_getAttachmentName(itemr) {
      var _this = this;
      var obji = _this.func_getAttachmentObj(itemr);
      if (!obji) {
        return '';
      }
      if (!obji.datai || !obji.datai.length) {
        return '';
      }
      return obji.datai[0].Name;
    },

    // 通过 记录的数据对象 返回附件的对象
    // -------------------------------
    func_getAttachmentObj(itemr) {
      var _this = this;
      var retobj = null;
      if (itemr.bmr_operDescription == 'MtrOper_Create') {
        return retobj;
      } else if (itemr.bmr_operDescription == 'MtrOper_AttachFileOrImage') {
        retobj = {
          datai : JSON.parse(itemr.bmr_operItems),
          type : 'attach'
        }
      } else if (itemr.bmr_operDescription == 'MtrOper_DettachFileOrImage') {
        retobj = {
          datai : JSON.parse(itemr.bmr_operItems),
          type : 'dettach'
        }
      }
      return retobj;
    },

    // 切换中间显示的哪块
    // -----------------
    func_changecontentshowing(typestr) {
      var _this = this;
      if (_this.m_contentshowingtype != typestr) {
        _this.m_contentshowingtype = typestr;
      }
    },

    // 告诉外面需要刷新列表
    // ------------------
    evt_onrefresh() {
      var _this = this;
      console.log('info 页面触发 onrefresh');
      _this.$emit("onrefresh");
    },

    // 在线预览
    previewdoc(fileid, filename){
      // 获得文件的下载地址
      var _this = this;      
      _this.dwgurl = window.bim_config.dwgurl;      
      _this.idocviewurl = window.bim_config.idocviewurl;

      let index = filename.lastIndexOf('.');
      let FileExtension = filename.substring(index,filename.length)
      let filedownloadurl = `${window.bim_config.webserverurl}/api/v1/attach/preview?id=${fileid}&Token=${this.$staticmethod.Get('Token')}`

      // 根据扩展名获取在线浏览地址
      var url_iframe_all;
      if (filename.toLowerCase().indexOf(".dwg") > 0) {
        _this.extdata._docviewtype = "dwg";
        url_iframe_all = `${
          _this.dwgurl
        }/Home/Index2?dwgurlcfg=${encodeURIComponent(filedownloadurl)}&name=${
          filename
        }`;
      } else {
        _this.extdata._docviewtype = "office";
        console.log('PC端构件详情，点击非dwg关联文档进行在线预览');
        url_iframe_all = _this.$staticmethod.getHuangNewcomputeViewUrl(filedownloadurl,filename,FileExtension);
      }
      _this.$emit("viewidoc", true, url_iframe_all, _this.extdata._docviewtype);
    },
    
    // 点击下载附件   
    clickDownattachment(fileid, filename){
      let filedownloadurl = `${window.bim_config.webserverurl}/api/v1/attach/download?attachmentId=${fileid}&Token=${this.$staticmethod.Get('Token')}`;      
      window.location.href = filedownloadurl;
    },
    // 编辑--点击编辑
    handelClickbuttonEdit() {
      let _this = this;
      _this.buttonEditClick = false;
      // 所有input 可以点击
    },
    // 编辑--点击取消
    handelClickCancel() {
      let _this = this;
      _this.buttonEditClick = true;
      _this.getStatusExtdata();
      _this.getdetail()
    },
    getStatusExtdata() {
      let _this = this;
      _this.extdata.bIsShowingUnitOptions == false;
      _this.extdata.isediting["bm_materialname"] = false;
      _this.extdata.isediting["bm_materialcode"] = false;
      _this.extdata.isediting["bm_materialcount"] = false;
      _this.extdata.isshow_filter_mat_status = false;
    },
    // 编辑--点击确定
    handelClickSure() {
      // 获取值，提交后台
      var _this = this;
      // console.log(_this.extdata.FullData.bm)
      // console.log(_this.extdata.sureEditParam.bc_guid_materialstatus)
      _this.extdata.sureEditParam.bc_guid_materialstatus = _this.extdata.FullData.bm.bc_guid_materialstatus
      _this.extdata.sureEditParam.bm_materialname = _this.extdata.FullData.bm.bm_materialname;
      _this.extdata.sureEditParam.bm_materialcode = _this.extdata.FullData.bm.bm_materialcode;
      _this.extdata.sureEditParam.bm_materialmodel = _this.extdata.FullData.bm.bm_materialmodel;
      _this.extdata.sureEditParam.bm_materialcount = _this.extdata.FullData.bm.bm_materialcount;
      _this.extdata.sureEditParam.bm_materialunit = _this.extdata.FullData.bm.bm_materialunit;
      _this.extdata.sureEditParam.bm_materialfac = _this.extdata.FullData.bm.bm_materialfac;
      _this.extdata.sureEditParam.bm_extjson = _this.extdata.FullData.bm.bm_extjson
      // _this.extdata.isediting = false;
      let materialNameReg = /[[\]/:*?"<>|]/;
      if(materialNameReg.test(_this.extdata.sureEditParam.bm_materialname)){
        _this.$message.error('构件分类不能包含非法字符');
      }else{
        _this.$nextTick(() => {
          _this.changeButtom()
        })
      }
    },
    // 点击确定修改--请求接口
    changeButtom() {
      let _this = this;

      // 如果即将修改的新状态为待修正，给出提示
      if (_this.extdata.sureEditParam.bc_guid_materialstatus == 'tocorrect') {

        _this.$confirm("您将修改的新状态为\"待修正\"，修改后无法再次修改状态，是否确认？", "操作确认", {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(y => {
           _this
        .$axios({
          url: `${this.$MgrBaseUrl.ModifyMaterialBasicInfo}`,
          method: "post",
          data: {
            bm_guid:  _this.bm_guid,
            Token: _this.$staticmethod.Get("Token"),
            bc_guid_materialstatus: _this.extdata.sureEditParam.bc_guid_materialstatus,
            bm_materialname: _this.extdata.sureEditParam.bm_materialname,
            bm_materialcode: _this.extdata.sureEditParam.bm_materialcode,
            bm_materialmodel: _this.extdata.sureEditParam.bm_materialmodel,
            bm_materialcount: _this.extdata.sureEditParam.bm_materialcount + '',
            bm_materialunit: _this.extdata.sureEditParam.bm_materialunit,
            bm_materialfac: _this.extdata.sureEditParam.bm_materialfac,
            bm_extjson: _this.extdata.sureEditParam.bm_extjson
          }
        })
        .then(x => {
          if ((x.status = 200)) {
            if (x.data.Ret > 0) {
              //debugger;

              _this.$message.success("修改成功");
              _this.evt_onrefresh();
              // _this.closeall();
              // _this.loadmaterialitems(_this.extdata.selectedtypeid);
              _this.getdetail();
              _this.closeall();
              _this.buttonEditClick = true;

            } else {
              _this.$message.error(x.data.Msg);
            }
            _this.getStatusExtdata();
          } else {
            console.error(x);
             _this.getStatusExtdata();
          }
        })
        .catch(x => {
          console.error(x);
           _this.getStatusExtdata();
        });
        }).catch(x => {

        });

      } else {

        //
         _this
        .$axios({
          url: `${this.$MgrBaseUrl.ModifyMaterialBasicInfo}`,
          method: "post",
          data: {
            bm_guid:  _this.bm_guid,
            Token: _this.$staticmethod.Get("Token"),
            bc_guid_materialstatus: _this.extdata.sureEditParam.bc_guid_materialstatus,
            bm_materialname: _this.extdata.sureEditParam.bm_materialname,
            bm_materialcode: _this.extdata.sureEditParam.bm_materialcode,
            bm_materialmodel: _this.extdata.sureEditParam.bm_materialmodel,
            bm_materialcount: _this.extdata.sureEditParam.bm_materialcount + '',
            bm_materialunit: _this.extdata.sureEditParam.bm_materialunit,
            bm_materialfac: _this.extdata.sureEditParam.bm_materialfac,
            bm_extjson: _this.extdata.sureEditParam.bm_extjson
          }
        })
        .then(x => {
          if ((x.status = 200)) {
            if (x.data.Ret > 0) {
              // debugger;
              _this.$parent.cancelRelevanceReloadTble(); 
              _this.$message.success("修改成功");
              _this.evt_onrefresh();
              // _this.closeall();
              // _this.loadmaterialitems(_this.extdata.selectedtypeid);
              _this.getdetail();
              _this.closeall();
              _this.buttonEditClick = true;

            } else {
              _this.$message.error(x.data.Msg);
            }
            _this.getStatusExtdata();
          } else {
            console.error(x);
             _this.getStatusExtdata();
          }
        })
        .catch(x => {
          console.error(x);
           _this.getStatusExtdata();
        });
      }
    
    },
    getmodeldetailcontentstyle() {
      let _this = this;
      let _height = {}
      let contengheight = _this.extdata.modeldetail.maxHeight - 44
      _height["maxHeight"] = contengheight + "px"
      return _height;
    },
    getmodeldetailstyle() {
      var _this = this;
      var _s = {};
      _s["left"] = _this.extdata.modeldetail.left + "px";
      _s["top"] = _this.extdata.modeldetail.top + "px";
      _s["display"] = _this.extdata.modeldetail.display ? "block" : "none";
      _s["maxHeight"] = _this.extdata.modeldetail.maxHeight + "px"
      return _s;
    },
    // 点击已关联模型查看详情
    handelAssociatedModel(ev,typeList) {
      let _this = this
      let typeID = typeList.elementID 
      let clientmodeldetail = ev.target.getBoundingClientRect()
      _this.extdata.modeldetail.left = 20;
      
      _this.extdata.modeldetail.display = 'block'
      _this.extdata.modeldetail.maxHeight = document.documentElement.clientHeight - 265;
      _this.extdata.modeldetail.top = `calc(50% - ${(document.documentElement.clientHeight - 265) / 2}px)`;
      _this.modeldetailtypeName = typeList.name 


      let pro_id = _this.$staticmethod._Get("bimcomposerId");
      let model_id = _this.extdata.model_id
      let vaultID = _this.$staticmethod._Get("organizeId");
      _this.$axios
					.get(`${this.$ip('newModelHttpUrl')}/Model/GetElementProperty?VaultID=${vaultID}&ModelID=${model_id}&ElementID=${typeID}`)
					.then(res => {
						if (res.status === 200) {
              let dataList = res.data 

              let map = new Map();
              let newArr = [];
              dataList.forEach(item => {
                map.has(item.group) ? map.get(item.group).push(item) : map.set(item.group, [item]);
              })
              
              newArr = [...map.values()];
              // console.log(newArr,'======newArr')
              _this.extdata.modelDetailList = newArr 
            } else {
              _this.$message.error('明细信息为空');
            }
					})
					.catch(err => {
					})
      let _url = `${
        _this.$staticmethod.getBIMServer()
      }/api/Model/GetElementProperty?ProjectID=${pro_id}&ModelID=${model_id}&VersionNO=&ElementID=${typeID}`; 
      

      // _this.$axios
      //   .get(_url)
      //   .then(x => {
      //     if (x.status != 200) {
      //       console.error(x);
      //     } else if (x.data && x.data.length > 0) {
      //       let dataList = x.data 

      //       let map = new Map();
      //       let newArr = [];
      //       dataList.forEach(item => {
      //         map.has(item.Group) ? map.get(item.Group).push(item) : map.set(item.Group, [item]);
      //       })
            
      //       newArr = [...map.values()];
                
      //       _this.extdata.modelDetailList = newArr 
      //     } else {
      //       _this.$message.error('明细信息为空');
      //     }
      //   })
      //   .catch(x => {
      //     console.error(x);
      //   });
     
    },
    // 点击下载二维码
    handleDownQRcode() {
      // alert('点击下载二维码')
      window.location.href = `${window.bim_config.webserverurl}/api/Tool/File/QRCodeDownload?encodedUrl=${this.qrcodeurl}`;
    },
    // 点击取消关联模型
    cancelRelevance() {
      let _this = this;

        _this
        .$confirm("确定取消关联？", "操作确认", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
        .then(y => {
           _this
            .$axios({
              url: `${this.$MgrBaseUrl.ClearMaterialElements}`,
              method: "post",
              data: {
                Token: _this.$staticmethod.Get("Token"),
                bm_guid: _this.bm_guid
              }
            })
            .then(x => {
              // console.log("点击取消关联成功")
              // console.log(x)
              // console.log("点击取消关联成功")
              if ((x.status = 200)) {
                if (x.data.Ret > 0) { 
                  _this.$message.success("操作成功");
                  _this.extdata.modeldetail['display'] = false
                  _this.extdata.componentDetails = []
                  _this.$emit('cancelRelevanceReloadTble')
                } else {
                  _this.$message.error(x.data.Msg);
                }
              } else {
                console.error(x);
                // _this.extdata.isediting[fieldname] = false;
              }
            })
            .catch(x => {
              console.error(x);
              // _this.extdata.isediting[fieldname] = false;
            });
        })
        .catch(y => {});



     
    },
    // 获取二维码
    getQRcode(materialid) {
      let _this = this; //:idocviewurl
     
      let encodeDwgurl = encodeURIComponent(window.bim_config.dwgurl); 
      let Url = `${window.location.origin}${window.bim_config.hasRouterFile}/#/MaterialsMgrMobile/${materialid}/${encodeDwgurl}/${this.$staticmethod.Get("Token")}`;
       
      let encodedUrl = encodeURIComponent(Url);

      // debugger;
      let codeImgUrl = `${this.$urlPool.QRCode}?encodedUrl=${encodedUrl}&token=${this.$staticmethod.Get("Token")}`;
      _this.qrcodeurl = codeImgUrl
      return codeImgUrl;
    },
    modifystatus(status) {
      var _this = this;
      _this.extdata.materialstatusobj = status
      _this.extdata.isshow_filter_mat_status = false
      _this.blurtosave("bc_guid_materialstatus", status.statusid);
    },

    getstatusstyle(colorstr) {
      var _this = this;
      var _s = {};
      _s["background-color"] = colorstr.replace("1)", "0.1)");
      _s["color"] = colorstr;
      _s["border"] = `1px solid ${colorstr.replace("1)", "0.1)")}`;
      return _s;
    },

    // showstatuslist
    showstatuslist() {
      var _this = this;
      
      if(!_this.buttonEditClick){
        
        // if (_this.extdata.materialstatusobj.statusid == 'tocorrect') {
        //   _this.$message.error('待修正状态无法进行修改');
        //   return;
        // }

        if (_this.extdata.isshow_filter_mat_status == true) {
          _this.extdata.isshow_filter_mat_status = false;
        } else {
          _this.extdata.isshow_filter_mat_status = true;
        }
      }
    },

    // modifyunit
    modifyunit(unit) {
      var _this = this;
      _this.blurtosave("bm_materialunit", unit);
    }, 
    // closeall
    closeall() {
      var _this = this;
      _this.extdata.bIsShowingUnitOptions = false;
      _this.extdata.isshow_filter_mat_status = false;
      _this.extdata.customeditingfield = [];
      _this.extdata.isediting["bm_materialname"] = false;
      _this.extdata.isediting["bm_materialcode"] = false;
      _this.extdata.isediting["bm_materialcount"] = false;
      _this.extdata.modeldetail['display'] = false
      _this.detailBigImgShow = false;
    },

    // canceledit
    canceledit(ev, fieldname, fieldval) {
      var _this = this;
      //debugger;
      if (ev.keyCode == 27) {
        _this.closeall();
        _this.getdetail();
      } else if (ev.keyCode == 13) {
        if (fieldname) {
          _this.blurtosave(fieldname, fieldval);
        }
      }
    },

    canceledit_cus(ev) {
      var _this = this;
      if (ev.keyCode == 27) {
        _this.closeall();
        _this.getdetail();
      } else if (ev.keyCode == 13) {
        _this.blurtosave_cus();
      }
    },

    // change_time
    change_time() {
      var _this = this;
      var timestr = _this.$staticmethod.dtToString(
        _this.extdata.FullData.bm.bm_planarrtime
      );
      _this.blurtosave("bm_planarrtime", timestr);
    },

    // switchunitoptions
    switchunitoptions() {
      var _this = this;
      if (_this.extdata.bIsShowingUnitOptions == true) {
        _this.extdata.bIsShowingUnitOptions = false;
      } else {
        _this.extdata.bIsShowingUnitOptions = true;
      }
    },

    // testThisFieldIsEditing
    testThisFieldIsEditing(fieldname) {
      var _this = this;
      return _this.extdata.customeditingfield.indexOf(fieldname) >= 0;
    },

    // clicktoedit_cus
    clicktoedit_cus(fieldname) {
      var _this = this;
      _this.closeall();
      _this.extdata.customeditingfield.push(fieldname);
    },

    // blurtosave1_cus
    blurtosave_cus() {
      var _this = this;
      var val = JSON.stringify(_this.extdata.customobj);
      _this.blurtosave("bm_extjson", val);

      //_this.extdata.customeditingfield = _this.extdata.customeditingfield.filter(x => x != fieldname);
    },
    // blurt1osave
    blurtosave(fieldname, fieldval) {
      // 先提交服务器，成功后执行刷新。
      //_this.extdata.isediting[fieldname] = false;
      var _this = this;
      _this.extdata.FullData.bm[fieldname] = fieldval;
      /*_this
        .$axios({
          url: `${window.bim_config.webserverurl}/api/Material/Mtr/ModifyMaterialItem`,
          method: "post",
          data: _this.$qs.stringify({
            Token: _this.$staticmethod.Get("Token"),
            bm_guid: _this.bm_guid,
            FieldName: fieldname,
            FieldVal: fieldval 
          })
        })
        .then(x => {
          if ((x.status = 200)) {
            if (x.data.Ret > 0) { 

              _this.$message.success("修改成功"); 
              _this.getdetail();
              _this.closeall();
            } else {
              _this.$message.error(x.data.Msg);
            }
            _this.extdata.isediting[fieldname] = false;
          } else {
            console.error(x);
            _this.extdata.isediting[fieldname] = false;
          }
        })
        .catch(x => {
          console.error(x);
          _this.extdata.isediting[fieldname] = false;
        });
      */
    },

    // 点击某个input，点击后自动变为可编辑
    // 并自动获得焦点
    clicktoedit(fieldname) {
      var _this = this;
      _this.closeall();
      if(!_this.buttonEditClick){
        _this.extdata.isediting[fieldname] = true;
      }
    },

    //
    getCntIfRelEle(r, boia_id) {
      var _this = this;
      if (r.bmr_operDescription == "MtrOper_ConnectElement") {
        return boia_id;
      } else {
        return "";
      }
    },

    //
    getIconClassByOperDescription(description) {
      var _this = this;
      if (description == "MtrOper_Create") {
        return "icon-interface-addnew";
      } else if (description == "MtrOper_Update") {
        return "icon-interface-edit";
      } else if (description == "MtrOper_StatusChange") {
        return "icon-interface-problem-status";
      } else if (description == "MtrOper_AttachFileOrImage") {
        return "icon-interface-folder";
      } else if (description == "MtrOper_DettachFileOrImage") {
        return "icon-interface-folder";
        // 显示模型图标
      } else if (description == "MtrOper_ConnectElement") {
        return "icon-interface-associated-component";
      } else if (false) {
        return "";
      } else {
        return "";
      }
    },

    // 根据构件操作记录，获取第一级直接操作的对象（构件）
    // 创建：构件名称。
    getFirstLevelName(r) {
      var _this = this;
      if (r.bmr_operDescription == "MtrOper_Create") {
        return r.bmr_operItemsArr[0].Name;
      } else if (r.bmr_operDescription == "MtrOper_Update") {
        return r.bmr_operItemsArr[0].Name;
      } else if (r.bmr_operDescription == "MtrOper_StatusChange") {
        // debugger;
        // return r.bmr_operItemsArr[0].Name;
        return r.bmr_operItemsArr[0].Id == ""
          ? "未开始"
          : r.bmr_operItemsArr[0].Name;
      } else {
        return "";
      }
    },

    // 是否显示附件那部分区域内容
    willshowdetail(eng) {
      var _this = this;
      if (eng == "MtrOper_Create") {
        return false;
      } else if (eng == "MtrOper_Update") {
        return false;
      } else if (eng == "MtrOper_StatusChange") {
        return false;
      } else if (eng == "MtrOper_AttachFileOrImage") {
        return true;
      } else if (eng == "MtrOper_DettachFileOrImage") {
        return true;
      } else if (eng == "MtrOper_ConnectElement") {
        return true;
      } else if (eng == "MtrOper_DisconnectElement") {
        return true;
      }
    },

    // 根据 MtrOper_AttachFileOrImage 得到中文的文本
    materialoperTrans(eng) {
      var _this = this;
      if (eng == "MtrOper_Create") {
        return "创建了单元";
      } else if (eng == "MtrOper_Update") {
        return "修改了单元";
      } else if (eng == "MtrOper_AttachFileOrImage") {
        return "关联了附件";
      } else if (eng == "MtrOper_DettachFileOrImage") {
        return "取消了关联";
      } else if (eng == "MtrOper_StatusChange") {
        return "更新了状态";
      } else if (eng == "MtrOper_ConnectElement") {
        return "关联了构件";
      } else {
        return eng;
      }
    },

    //
    getcurrentstatusstyle() {
      var _this = this; 
      var _s = {};
      var _strcolorsource = _this.extdata.materialstatusobj.statusmaincolor;
      if (_strcolorsource) {
        // _s["color"] = _strcolorsource;
        // _s["border"] = `2px solid ${_strcolorsource.replace("1)", "1)")}`;
        // _s["background-color"] = _strcolorsource.replace("1)", "0.1)");

        _s["color"] = '#fff';
        _s["background-color"] = _strcolorsource;
      }
      return _s;
    },

    // do_removeattachment
    do_removeattachment(fileid, filename) {
      var _this = this;
      var _LoadingIns = _this.$loading({
        text: "操作中",
        target: document.getElementById("id_middle_scrollable")
      });
      _this
        .$axios({
          url: `${window.bim_config.webserverurl}/api/Material/Mtr/DettachImageOrFile`,
          method: "post",
          data: {
            Token: _this.$staticmethod.Get("Token"),
            SourceID: _this.bm_guid,
            TargetID: fileid,
            TargetName: filename
          }
        })
        .then(x => {
          if (x.status == 200) {
            if (x.data.Ret > 0) {
              _this.$message.success("操作成功");
              _this.getdetail();
            } else {
              _this.$message.error(x.data.Msg);
            }
          } else {
            console.error(x);
          }
          _LoadingIns.close();
        })
        .catch(x => {
          _LoadingIns.close();
          console.error(x);
        });
    },
    // 按 relationid 移除附件ID
    removeattachment(fileid, filename) {
      //
      var _this = this;
      _this
        .$confirm("确定移除该附件？", "操作确认", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
        .then(x => {
          _this.do_removeattachment(fileid, filename);
        })
        .catch(x => {});
    },

    // do_removeexistsimg
    do_removeexistsimg(bf_guid, bf_filename) {
      // 调用接口，移除该图片对于此构件的关联
      var _this = this;
      // alert(bf_guid);
      // alert(_this.bm_guid);
      var _LoadingIns = _this.$loading({
        text: "操作中",
        target: document.getElementById("id_middle_scrollable")
      });

      _this
        .$axios({
          url: `${window.bim_config.webserverurl}/api/Material/Mtr/DettachImageOrFile`,
          method: "post",
          data: {
            Token: _this.$staticmethod.Get("Token"),
            SourceID: _this.bm_guid,
            TargetID: bf_guid,
            TargetName: bf_filename
          }
        })
        .then(x => {
          if (x.status == 200) {
            if (x.data.Ret > 0) {
              //debugger;
              _this.getdetail();
            } else {
              _this.$message.error(x.data.Msg);
            }
          } else {
            console.error(x);
          }
          _LoadingIns.close();
        })
        .catch(x => {
          _LoadingIns.close();
          console.error(x);
        });
    },

    // 移除已关联的图片
    removeexistsimg(bf_guid, bf_filename) {
      // 确认操作
      var _this = this;
      // console.log(bf_guid);
      // _this.bm_guid;
      _this
        .$confirm("确认移除此图片?", "操作确认", {
          confirmButtonText: "确认",
          cancelButtonText: "取消",
          type: "warning"
        })
        .then(x => {
          _this.do_removeexistsimg(bf_guid, bf_filename);
        })
        .catch(x => {});
    },

    // 点击小图，显示大图和图的名称
    setcurrentshowimg(img) {
      var _this = this;
      _this.detailBigImgName = img.bf_filename
      _this.detailBigImgShow = true;
      _this.detailBigImgSrc =  `${window.bim_config.webserverurl}/${img.bf_path}`;
      
      _this.detailBigImgShow = true;
    },

    clickscroll(para) {
      var _this = this;
      document.getElementById("id_relimage_middle").scrollLeft +=
        para * _this.extdata.iUnitScrollSize;
    },

    // 点击关联图片
    open_relimg() {
      var _this = this;
      var dom = document.getElementById("id_relimgopener");
      dom.value = "";
      dom.click();
    },

    // open_relfile
    open_relfile() {
      var _this = this;
      var dom = document.getElementById("id_relfileopener");
      dom.value = "";
      dom.click();
    },

    uploadfile(ele, successcb, errorcb) {
      var _this = this;
      if (!ele || !ele.files || !ele.files.length || ele.files.length == 0) {
        return;
      }
      var File = ele.files[0];
      // console.log(_this.extdata.FullData,'=====00000')
      // debugger
      var userid = _this.$staticmethod.Get("UserId");
      var ProjectID = _this.$staticmethod._Get("bimcomposerId");
      
      var fd = new FormData();
       
      let FileKey=this.$md5(new Date().getTime()+''+File.name);
      fd.append("ProjectID", ProjectID);
      fd.append("BusinessId", _this.extdata.FullData.bm.bm_guid);
      fd.append("AttachmentType", '1');
      fd.append("FileName", File.name);
      fd.append("FileKey", FileKey);
      fd.append("FileSize", File.size);
      fd.append("ChunkNumber", 1);
      fd.append("Index", 1);
      fd.append("UserId", userid);
      fd.append("File", File);


      var config = {
        headers: {
          "Content-Type": "multipart/form-data"
        }
      };
      // `${ _this.$staticmethod.getDocServer()}/api/Doc/UploadFileToHideFolder_MulProp`,
      _this.$axios
        .post(
          `${window.bim_config.webserverurl}/api/v1/attach/upload?Token=${this.$staticmethod.Get('Token')}`,
          fd,
          config
        )
        .then(x => {
          if (successcb) {
            successcb(x);
          } else {
            console.warn("not specify successcb");
          }
        })
        .catch(x => {
          if (errorcb) {
            errorcb(x);
          } else {
            console.warn("not specify errorcb");
          }
        });
    },

    // addfi1lerelation
    addfilerelation(fileId, filename) {
      // 访问接口
      var _this = this;
      var _LoadingIns = _this.$loading({
        text: "操作中",
        target: document.getElementById("id_middle_scrollable")
      });
      _this
        .$axios({
          url: `${this.$MgrBaseUrl.AttachImageOrFile}`,
          method: "post",
          data: {
            Token: _this.$staticmethod.Get("Token"),
            SourceID: _this.bm_guid,
            TargetID: fileId,
            TargetName: filename,
            Type: "File"
          }
        })
        .then(x => {
          if (x.status == 200) {
            if (x.data.Ret > 0) {
              _this.$message.success("添加成功");
              _this.getdetail();
            } else {
              _this.$message.error(x.data.Msg);
            }
          } else {
            console.error(x);
          }
          _LoadingIns.close();
        })
        .catch(x => {
          _LoadingIns.close();
          console.error(x);
        });
    },

    // 关联文件Input变动
    on_relfileopener_change() {
      var _this = this;
      var dom = document.getElementById("id_relfileopener");
      if (dom.files && dom.files.length > 0) {
        //debugger;
        var index = _this.extdata.FullData.brs_fileinfo.findIndex(
          x => x.file.FileName == dom.files[0].name
        );
        if (index >= 0) {
          _this.$message.error("已存在同名文件");
          return;
        }

        // 调用上传文件接口，得到fileid
        _this.uploadfile(
          dom,
          function(x) {
            if (x.status == 200) {
              if (x.data.Data.Data.AttachmentId) {
                _this.addfilerelation(x.data.Data.Data.AttachmentId, x.data.Data.Data.AttachmentName);
              } else {
                console.error(x);
              }
            } else {
              console.error(x);
            }
          },
          function(x) {
             _this.$message.error(`服务器处理发生错误，可能是文件类型不符合白名单规则，请检查文件`);
          }
        );
      }
    },

    // 关联图片input变动
    on_relimgopener_change() {
      // 调用上传图片接口（不关联），并得到bf_guid
      var _this = this;

      // 调用上传图片方法（其内部调用接口）
      var dom = document.getElementById("id_relimgopener");
      // debugger
      // 判断此次选择的图片是否已存在于 relimages 中了
      if (dom.files && dom.files.length > 0) {
        var index = _this.extdata.FullData.brbfs.findIndex(
          x => x.bf_filename == dom.files[0].name
        );
        if (index >= 0) {
          _this.$message.error("已存在同名文件");
          return;
        }
      }

      // 显示loading
      var _LoadingIns = _this.$loading({
        text: "操作中",
        target: document.getElementById("id_middle_scrollable")
      });

      _this.uploadimg(
        dom,
        function(x) {
          //debugger;
          if (x.status == 200) {
            if (x.data.Ret > 0) {
              if (x.data.Data) {
                // 临时使用强刷新的方式
                _this.getdetail();
                //_this.extdata.FullData.brbfs.push(x.data.Data);
                // if (_this.extdata.FullData.brbfs.length == 1) {
                //   _this.extdata.currentshowtitle = x.data.Data.bf_filename;
                //   _this.extdata.currentshowurl = x.data.Data.bf_path;
                //   _this.extdata.currentshowbfid = x.data.Data.bf_guid;
                //   // setTimeout(x => {
                //   //   document.getElementById("id_middle_scrollable").scrollTop += 324;
                //   // }, 500);
                //   debugger;
                // }
              }
            } else {
            }
          } else {
          }
          _LoadingIns.close();
        },
        function(x) {
          _LoadingIns.close();
        }
      );
    },

    _stopPropagation(ev) {
      if (ev) {
        ev.stopPropagation();
      }
    },

    //
    uploadimg(ele, successcb, errorcb) {
      var _this = this;
      if (!ele || !ele.files || !ele.files.length || ele.files.length == 0) {
        return;
      }
      var File = ele.files[0];
      var fd = new FormData();
      // fd.append("Token", _this.$staticmethod.Get("Token"));
      fd.append("SomeId", _this.bm_guid);
      fd.append("SomeTypeStr", "物料");
      fd.append("FormFile", File);
      // fd.append("SomeId", _this.bm_guid);
      // fd.append("SomeTypeStr", "物料");
      // fd.append("FileUploading", File);
      var config = {
        headers: {
          "Content-Type": "multipart/form-data"
        }
      };

      //debugger;
      _this.$axios
        .post(
          `${window.bim_config.webserverurl}/api/Tool/File/UploadImage?token=${this.$staticmethod.Get("Token")}`,
          fd,
          config
        )
        .then(x => {
          if (successcb) {
            if (x.data.Ret < 0) {
              _this.$message.error(x.data.Msg);
              if (errorcb) {
                errorcb(x.data.Msg);
              }
              return;
            } else {
              successcb(x);
            }
          } else {
            console.warn("not specify successcb");
          }
        })
        .catch(x => {
          //debugger;
          if (errorcb) {
            errorcb(x);
          } else {
            console.warn("not specify errorcb");
          }
        });
    },

    getImageBgStyle(url, bsize) {
      var _this = this;
      var _s = {};
      var totalurl = window.bim_config.webserverurl + url;
      _s["background-image"] = `url(${totalurl})`;
      _s["background-repeat"] = "no-repeat";
      _s["background-size"] = "contain";
      if (bsize) {
        _s["background-size"] = bsize;
      }
      _s["background-position"] = "center";
      return _s;
    },
    // 获取已关联构件明细
    getComponentDetailsList(dataMris) {
      let _this = this;
      let ModelIDParam = dataMris.modelid;
      _this.extdata.model_id = dataMris.modelid;
      // let ElementIDsParam = '09c78392-5b51-4a85-af4b-2cdada0d6fa2^573547,09c78392-5b51-4a85-af4b-2cdada0d6fa2^477865'
      // let a = encodeURLComponent(['573547','477865'])
      let a = []
      // GetElement这个接口查询的是一个，所以要循环ElementIDsParam,获取详情
      for(let m = 0; m < dataMris.elementids.length; m++){
        _this.getElementDetail(ModelIDParam,dataMris.elementids[m])
        
      }
      

       
      // let ElementIDsParam = dataMris.elementids.join(","); // 旧模型使用的

      // _this
      //   .$axios({
      //     url: `${
      //       _this.$staticmethod.getBIMServer()
      //     }/api/Model/GetElementsByIdList`,
      //     method: "post",
      //     data: _this.$qs.stringify({
      //       ProjectID: _this.$staticmethod._Get("organizeId"),
      //       ModelID: ModelIDParam,
      //       VersionNO: "",
      //       ElementIDs: ElementIDsParam
      //     })
      //   })
      //   .then(x => {
      //     if (x.status == 200) {
      //       _this.extdata.componentDetails = x.data;
      //     } else {
      //       console.error(x);
      //     }
      //   })
      //   .catch(x => {
      //     console.error(x);
      //   });
    },
    getElementDetail(ModelIDParam,ElementID){
      this.$axios
        .post(`${this.$ip('newModelHttpUrl')}/Model/GetElement?VaultID=${this._OrganizeId}&ModelID=${ModelIDParam}&ElementID=${ElementID}`)
        .then(res => {
          if (res.status === 200) {
            // return res.data
            let newData = res.data
            var elementIDs = this.extdata.componentDetails.map(item => item.elementID);
            if (!elementIDs.includes(newData.elementID)) {
              this.extdata.componentDetails.push(newData);
            }
            // this.extdata.componentDetails.push(res.data)
          }
        })
        .catch(err => {
        })
     
    },
    getdetail() {
      // 显示loading
      var _this = this;
      var _LoadingIns = _this.$loading({
        text: "加载中",
        target: document.getElementById("id_body")
      });

      // 查询数据，在complete中隐藏loading
      var bimcomposerId = _this.$staticmethod._Get("bimcomposerId");
      _this.$axios
        .get(
          `${this.$MgrBaseUrl.GetMaterialDetail}?bm_guid=${_this.bm_guid}&token=${this.$staticmethod.Get("Token")}`
        )
        .then(x => {
          if (x.status == 200) {
            if (x.data.Ret > 0) {
              let dataMris = [];
              if (x.data.Data.mris) {
                dataMris = x.data.Data.mris; // 获取已关联构件详情
              }
              if(x.data.Data.outerlink == "about:blank"){
                _this.externalLinksShow = false
                _this.iframeHeight = "auto"
                // _this.externalLinks = '../../static/images/linkimg.png'
                // console.log(_this.externalLinks)
              }else{
                _this.externalLinks = x.data.Data.outerlink
              }

              console.log(x.data.Data.outerlink)
              _this.extdata.FullData = x.data.Data;

              //
              _this.extdata.materialrecords = x.data.Data.bmrs;

              if (x.data.Data.mris && dataMris.length > 0) {
                for(let i=0; i<dataMris.length; i++){
                  // dataMris = dataMris[i];

                  _this.getComponentDetailsList(dataMris[i]);
                }
              }

              for (var i = 0; i < _this.extdata.materialrecords.length; i++) {
                _this.extdata.materialrecords[i].bmr_operItemsArr = JSON.parse(
                  _this.extdata.materialrecords[i].bmr_operItems
                );
              }

              var thestatusid = x.data.Data.bm.bc_guid_materialstatus;
              var _index = _this.allstatus.findIndex(
                x => x.statusid == thestatusid
              );
              if (_index >= 0) {
                _this.extdata.materialstatusobj = _this.allstatus[_index];
              } else {
                _this.extdata.materialstatusobj = {
                  statusid: "-1",
                  statusmaincolor: "rgba(0, 0, 0, 1)",
                  statusname: "无"
                };
              }

              // 如果有图片，默认显示第一张
              if (_this.extdata.FullData.brbfs.length > 0) {
                var bf = _this.extdata.FullData.brbfs[0];
                _this.extdata.currentshowtitle = bf.fileobj.bf_filename;
                _this.extdata.currentshowurl = bf.fileobj.bf_path;
                _this.extdata.currentshowbfid = bf.fileobj.bf_guid;
              }

              // 扩展字段相关
              if (_this.extdata.FullData.bm.bm_extjson) {
                _this.extdata.customobj = JSON.parse(
                  _this.extdata.FullData.bm.bm_extjson
                );

                // 为 isediting 赋初始值，均为 false
                // for (var i = 0; i < _this.extdata.customobj.length; i++) {
                //   _this.extdata.isediting[_this.extdata.customobj[i].fieldname] = false;
                // }

                // for (var i = 0; i < _this.extdata.customobj.length; i++) {
                //    //console.log(_this.extdata.customobj[i].fieldname);
                //    //_this.extdata.isediting[_this.extdata.customobj[i].fieldname] = false;
                //    _this.extdata.customobj[i].fieldiseding = false;
                // }
              } else {
                _this.extdata.customobj = [];
              }
            } else {
              _this.$message.error(x.data.Msg);
            }
          } else {
            console.error(x);
          }
          _LoadingIns.close();
        })
        .catch(x => {
          _LoadingIns.close();
          console.error(x);
        });
    },

    _oncancel() {
      var _this = this;
      _this.$emit("oncancel");
    },
    windowsizechange() {
      var _this = this;
      // _this.dragstyle.right = `calc(10%)`;
      // _this.dragstyle.top = `32px`;
    },
    draggreet(val) {
      var _this = this;
      _this.val = val;
    },
    gethoverstyle() {
      var _this = this;
      var _s = {};
      _s["z-index"] = _this.zIndex || 3; // 0 -> 3
      return _s;
    }
  },
  props: {
    zIndex: {
      type: Number,
      required: false
    },
    frontWidth: {
      type: Number,
      required: false // 880
    },
    bm_guid: {
      // 构件id
      type: String,
      required: false
    },
    allstatus: {
      type: Array,
      required: true
    }
  }
};
</script>
<style scoped>

._css-fieldlabel._css-fieldmodel {
  height:100%;
}

._css-view-op-text {
  margin-left: 8px;
}

._css-view-outerpage {
    cursor: pointer;
    position: absolute;
    line-height: 64px;
    left: 120px;
    top: 18px;
    display: flex;
    align-items: center;
    height: 28px;
    background-color: rgba(97,111,125,1);
    border-radius: 4px;
    color: #fff;
    font-size: 12px;
    padding: 0 8px 0 8px;
}

._css-view-outerpage._css-1890ff {
  background-color:#1890FF;
}

._css-imageitem-text {
  margin-left:10px;
  margin-right: 10px;
  height: 22px;
  line-height: 22px;
  flex:1;
  text-align: left;
  overflow-x:hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

._css-isdel {
  color: #A6AEB6;
}

._css-imageitem-icon {
  width:20px;
  height:20px;
  margin-left: 24px;
  border:1px dotted transparent;
}

._css-imageitem {
    height: 56px;
    background-color: #F7F7F7;
    display: flex;
    align-items: center;
}

._css-contents-recordi-body {
    padding-left: 24px;
}

._css-recordi-dt {
  flex:none;
  color:#616F7D;
}

._css-recordi-label {
  flex:1;
  text-align: left;
  margin-left: 8px;
  margin-right: 8px;
  color:#616F7D;

  text-overflow: ellipsis;
  overflow-x: hidden;
  white-space: nowrap;
}

._css-recordi-icon {
  width:16px;
  height:16px;
  font-size: 16px;
  line-height: 16px;
  border:1px dotted transparent;
}

._css-contents-recordi-title {
  height: 36px;
  display: flex;
  align-items: center;
}

._css-contents-recordi {
  min-height: 36px;
}

._css-contents-recordlist {
    flex: 1;
    overflow-y: auto;
    height: calc(100% - 74px);
    margin-left: 24px;
    margin-right: 24px;
}

._css-rel-imgs {
  flex:1;
}

._css-rel-rightarea {
  flex:1;
  padding: 12px;
  border-radius:4px;
border:1px solid rgba(236,238,240,1);
}

._css-attachments {
  display: flex;
  margin-left: 24px;
  margin-right: 24px;
  min-height: 74px;
}

._css-attachments._css-thewhole {
  height:100%;
}

._css-attachments._css-notfirst {
  margin-top:10px;
}

._css-relinput-icon {
  color:#A6AEB6;
}

._css-relinput-label {
    margin-left: 10px;
    flex: 1;
    color: #A6AEB6;
}

._css-addbtnsarea-content {
  height:40px;
  display: flex;
  justify-content: space-between;
  padding-left: 24px;
  padding-right: 24px;
  margin-bottom: 20px;
}
._css-addbtnsarea-content ._css-rel-input{
  border: 1px solid rgba(0,0,0,.09);
}
._css-addbtnsarea-content ._css-rel-input ._css-relinput-label{
  color:#1890ff;
}
._css-topbtn-i {
  margin-left: 40px;
  min-width: 60px;
  height:30px;
  line-height: 20px;
    box-sizing: border-box;
    border-bottom: 2px solid transparent;
    cursor: pointer;
}

._css-topbtn-i._css-focus {
  border-bottom-color: #1890FF;
}

._css-topbtn-i._css-first {
  margin-left:24px;
}

._css-contents-topbtnarea {
  height:30px;
  margin-top:24px;
  margin-bottom: 20px;
  flex:none;
  display: flex;
  align-items: center;
}

._css-material-statusouter {
  top:0;
  right:0;
  position:absolute;
  /* width:95px; */
  height:28px;

  display: flex;
  user-select: none;

   top:20px;
  right:0;
}

._css-material-statusinner {
  display: flex;
  user-select: none;
 
 
}

._css-materialrecord {
  width:384px;
  flex:none;
  height: 100%;
}

._css-mat-status-btn {
  height: 32px;
  margin-top: 16px;
  margin-bottom: 16px;
  width: calc(100% - 32px);
  border-radius: 4px;
  background-color: rgba(24, 144, 255, 1);
  color: rgba(255, 255, 255, 1);
  font-size: 14px;
  text-align: center;
  line-height: 32px;
  cursor: pointer;
  user-select: none;
}

._css-mat-status-optionicon-in {
  width: 131px;
  height: 28px;
  border-radius: 2px;

  /* border: 1px solid rgba(250, 173, 20, 1);
  background-color: rgba(250, 173, 20, 0.1);
  color: rgba(250, 173, 20, 1); */

  text-align: center;
  line-height: 28px;
  font-size: 12px;
  margin-left: 16px;
}
._css-mat-status-optionicon {
  flex: 1;
  height: 100%;
  display: flex;
  align-items: center;
}
._css-mat-status-optionstatus {
  height: 20px;
  width: 20px;
  margin-right: 10px;
}
._css-mat-status-option {
  height: 40px;
  display: flex;
  align-items: center;
  cursor: pointer;
}
._css-mat-status-option:hover {
  background-color: rgba(0, 0, 0, 0.04);
}
._css-mat-status-options {
  flex: 1;
  width: 100%;
  overflow-y: auto;
  padding-top: 4px;
  padding-bottom: 4px;
  box-sizing: border-box;
}

._css-matertialstatusoptions {
  position: absolute;
  width: 171px;
  max-height: 310px;
  background-color: rgba(255, 255, 255, 1);
  box-shadow: 0px 4px 20px 0px rgba(0, 0, 0, 0.15);
  border-radius: 2px;
  display: flex;
  flex-direction: column;
  align-items: center;
}

._css-plantimediv {
  margin-left: 8px;
}

._css-unitedit-option {
  height: 40px;
  text-align: left;
  padding-left: 16px;
  line-height: 40px;
  cursor: pointer;
}

._css-unitedit-option:hover {
  background-color: rgba(0, 0, 0, 0.04);
}

._css-unitedit-options {
  position: absolute;
  width: calc(100% - 130px);
  /* height: 88px; */
  top: calc(100% + 0px);
  right: 0;
  padding-top: 4px;
  padding-bottom: 4px;
  background-color: rgba(255, 255, 255, 1);
  box-shadow: 0px 4px 20px 0px rgba(0, 0, 0, 0.15);
  z-index: 1;
}

._css-unitedit-container {
  position: relative;
}

._css-unitedit {
  cursor: pointer;
}

._css-fieldval._css-cpointerandnoborder {
  /* cursor: pointer; */
  cursor: text;
}
._css-fieldval._css-cpointerandhaveborder{
 border-color: transparent;
}
._css-material-status {
  /* height: 50px;
  margin-top: 6px; */
  user-select: none;
  display: flex;
}
._css-status-up-icon {
  flex: 1;
  text-align: left;
}
._css-status-up-icon p {
  width: 28px;
  height: 28px;
  margin-top: 2px;
  background-color: rgba(0, 0, 0, 0.04);
  text-align: center;
}
._css-statusin-up-down {
  vertical-align: middle;
    line-height: 16px;
    font-size: 14px;
    margin-right: 4px;
}
._css-material-statusin {
  height: 100%;
  /* width: 131px; */
  background-color: rgba(0, 0, 0, 0.1);
  font-size: 12px;
  line-height: 26px;
  text-align: center;
  border: 0px solid transparent !important;
  color: rgba(0, 0, 0, 1);
  cursor: pointer;
  /* margin-right: 8px; */
  display: flex;

  border-top-left-radius: 14px;
    border-bottom-left-radius: 14px;
}
._css-material-statusin-title{
  width: 73px;
  /* margin-left: 8px;
  margin-right: 12px; */
  font-size: 14px;
  line-height: 28px;
}
._css-operationi-detail-i {
  height: 20px;
  box-sizing: border-box;
  margin-top: 12px;
  margin-left: 12px;
  display: flex;
  align-items: center;
}

._css-operationi-detaili-leave {
  color: rgba(0, 0, 0, 0.25);
  height: 20px;
  line-height: 20px;
  text-align: right;
}

._css-operationi-detaili-text {
  flex: 1;
  height: 20px;
  line-height: 20px;
  font-size: 12px;
  color: rgba(0, 0, 0, 0.65);
  margin-left: 8px;
  text-align: left;
  overflow-x: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

._css-operationi-detaili-icon {
  width: 20px;
  height: 20px;
  font-size: 20px;
  line-height: 20px;
}
._css-operationi-detail-i._css-first {
  margin-top: 0;
}
._css-operation-item {
  margin-top: 3px;
  width: 387px;
}

._css-operationi-detail {
  display: flex;
}

._css-operationi-detail-l {
  width: 36px;
  flex: none;
}

._css-operationi-detail-splitter {
  flex: none;
  width: 4px;
  background-color: rgba(24, 144, 255, 1);
  border-radius: 2px;
}

._css-operationi-detail-r {
  /* flex: 1; */
  width: 347px;
}

._css-operationit-operatimetxt {
  margin-left: 8px;
  color: rgba(0, 0, 0, 0.45);
  height: 20px;
  line-height: 20px;
}

._css-operationit-operaobjname {
  flex: 1;
  margin-left: 8px;
  color: rgba(0, 0, 0, 0.45);
  height: 20px;
  line-height: 20px;
  overflow-x: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  text-align: left;
}

._css-operationit-operatext {
  margin-left: 8px;
  color: rgba(0, 0, 0, 0.65);
  width: 70px;
  height: 20px;
  line-height: 20px;
}

._css-operationit-operaname {
  margin-left: 16px;
  width: 42px;
  overflow-x: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  height: 20px;
  color: rgba(0, 0, 0, 0.45);
}

._css-operationit-icon {
  width: 16px;
  height: 16px;
  font-size: 16px;
  line-height: 16px;
  color: rgba(0, 0, 0, 0.45);
}

._css-operationi-title {
  height: 20px;
  padding-top: 15px;
  padding-bottom: 15px;
  display: flex;
  align-items: center;
}

/* 关联的一个对象 */
._css-relationi-number {
  text-align: right;
  color: rgba(0, 0, 0, 0.25);
}
._css-relation-model-title:hover{
  cursor: pointer;
  background: rgba(0, 0, 0, 0.04)
}
._css-relation-item ._css-relationi-delfile {
  text-align: right;
  /* color:rgba(0, 0, 0, 0.25); */
  /* color: rgb(44, 62, 80); */
  color:#878787;
  width: 20px;
  height: 20px;
  line-height: 20px;
  cursor: pointer;
  display: none;
}
._css-down-attachment{
  margin-right: 10px;
}

._css-relationi-delfile:hover {
  color: rgba(24, 144, 255, 1);
}

._css-relationi-text {
  flex: 1;
  overflow-x: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  text-align: left;
  height: 40px;
  line-height: 40px;
  color: rgba(0, 0, 0, 0.85);
  font-size: 14px;
  margin-left: 8px;
}
._css-relationi-text ._css-relation-name{
  color: rgba(0, 0, 0, 0.25);
  padding-left:5px;
}
._css-relationi-icon {
  font-size: 20px;
  height: 20px;
  width: 20px;
  line-height: 20px;
  color: rgba(0, 0, 0, 0.45);
  flex: none;
}
._css-details-icon {
  color: #1890ff;
  padding-right: 12px;
  padding-left: 8px;
  vertical-align: middle;
  line-height: 40px;
}
/* ._css-relation-item {
  height: 50px;
  width: 100%;
  box-sizing: border-box;
  padding-right: 24px;
  align-items: center;
} */
._css-relation-item {
  /* height: 50px; */
  width: 100%;
  box-sizing: border-box;
  padding-left: 24px;
  padding-right: 24px;
  display: flex;
  align-items: center;
  cursor:pointer;
}
._css-relation-model-title{
  display: flex;
  width:100%;
  margin:0 24px;
}
._css-relation-item:hover ._css-relationi-delfile {
  display: block;
}

/* 中间关联图片相关 */
._css-rel-title {
  font-size: 14px;
  color: rgba(0, 0, 0, 0.45);
  text-align: left;
  height: 50px;
  line-height: 50px;
  padding-left: 48px;
}
._css-rel-body-imagebody {
  height: 264px;
  padding-left: 24px;
  padding-right: 24px;
}
._css-rel-body-imagebody-smallimg{
  height: auto; 
  margin-bottom: 32px;
}
._css-rel-body-imagein {
  height: 100%;
  box-shadow: 0px 13px 24px -17px rgba(11, 41, 62, 0.8);
  border-radius: 4px;
  position: relative;
  background-image: url(https://www.probim.cn:8080/ProjectImgs/2019_08_18/9119c30d-2159-441f-a375-a470465cf672_min.bmp);
  background-repeat: no-repeat;
  background-size: cover;
  background-position: center center;
}
._css-rel-body-imagein-smallimg {
  height: 100%; 
}
._css-rel-imagehead {
  position: absolute;
  top: 0;
  height: 32px;
  width: 100%;
  background-color: rgba(0, 0, 0, 1);
  opacity: 0.45;
  font-size: 12px;
  color: #fff;
  text-align: center;
  line-height: 32px;
  border-top-left-radius: 4px;
  border-top-right-radius: 4px;
}
._css-rel-imagebottom {
  position: absolute;
  bottom: 0;
  height: 66px;
  width: 100%;
  background-color: rgba(0, 0, 0, 0.45);
  display: flex;
  align-items: center;
  border-bottom-left-radius: 4px;
  border-bottom-right-radius: 4px;
}
._css-rel-imagebottom-smallimg{
  text-align: left;
  width: 100%;
  border-bottom-left-radius: 4px;
  border-bottom-right-radius: 4px;
}
._css-relimage-left {
  width: 16px;
  height: 58px;
  border-radius: 4px;
  background-color: rgba(52, 52, 52, 1);
  margin-left: 5px;
  margin-right: 5px;
  cursor: pointer;
}
._css-relimage-lr {
  display: flex;
  align-items: center;
  justify-content: space-around;
}

._css-relimage-lr:hover {
  opacity: 0.8;
}
._css-lrbtn {
  width: 12px;
  height: 12px;
  font-size: 12px;
  color: rgb(236, 237, 233);
  line-height: 12px;
  text-align: center;
}
._css-relimage-middlearea {
  flex: 1;
  height: 100%;
  margin-left: 5px;
  margin-right: 5px;
  display: flex;
  align-items: center;
  overflow-x: auto;
  border-right: 1px solid transparent;
  border-left: 1px solid transparent;
}

._css-relimage-middlei {
  width: 54px;
  height: 54px;
  background-color: transparent;
  border-radius: 4px;
  margin-left: 3px;
  margin-right: 3px;
  flex: none;
  overflow: visible;
  display: flex;
  align-items: center;
  justify-content: space-around;
  position: relative;
}

._css-suggested-close_circle {
  position: absolute;
  right: -4px;
  top: -4px;
  display: none;
  cursor: pointer;
  color: rgba(24, 144, 255, 1);
}
._css-suggested-close_circle-smallimg {
  position: absolute;
  right: -8px;
  top: -8px;
  display: none;
  cursor: pointer;
  color: #191718;
}
._css-relimage-middlei-smallimg{
  width: 76px;
  height: 76px;
  background-color: transparent;
  border-radius: 4px; 
  margin-right: 11px; 
  margin-top: 10px;
  justify-content: space-around;
  position: relative;
  display: inline-block;
}
._css-relimage-middlei-smallimg:hover ._css-suggested-close_circle-smallimg {
  display: block;
}
._css-relimage-middlei:hover ._css-suggested-close_circle{
  display: block;
}

._css-relimage-middlein {
  width: 50px;
  height: 50px;
  background-color: rgb(236, 237, 233);
  box-shadow: 0px 1px 1px 0px rgba(0, 21, 41, 0.12);
  border-radius: 4px;
  cursor: pointer;
  transition: all 200ms;
}

._css-relimage-middlei:hover ._css-relimage-middlein {
  width: 54px;
  height: 54px;
  transition: all 200ms;
}
._css-relimage-middlein-smallimg {
  width: 76px;
  height: 76px;
  background-color: rgb(236, 237, 233);
  box-shadow: 0px 1px 1px 0px rgba(0, 21, 41, 0.12);
  border-radius: 4px;
  cursor: pointer;
  transition: all 200ms;
}

._css-relimage-middlei:hover ._css-relimage-middlein-smallimg {
  width: 76px;
  height: 76px;
  transition: all 200ms;
} 
._css-relimage-right {
  width: 16px;
  height: 58px;
  border-radius: 4px;
  background-color: rgba(52, 52, 52, 1);
  margin-left: 5px;
  margin-right: 5px;
  cursor: pointer;
}
._css-relimage-lr {
  display: flex;
  align-items: center;
  justify-content: space-around;
}
/* //中间关联图片相关 */

._css-rel-eles {
  margin-top: 10px;
  height: 100px;
  border: 1px solid transparent;
  display: flex;
  flex-direction: column;
}
._css-rel-noneicon-btn{
  padding-left: 0 !important;
  text-align: left;
}
._css-rel-btn {
  flex:none;
  width: 96px;
  align-items: center;
  color: rgba(0, 0, 0, 0.65);
  padding-left: 16px;
  color:#666666;
  font-size: 14px;
}
._css-btn-icon {
  vertical-align: middle;
  padding-right: 12px;
}
._css-rel-input {
  text-align: left;
  border: 1px dashed #A6AEB6;
  border-radius: 4px;
  line-height: 40px;
  display: flex;
  align-items: center;
  color: #1890ff;
  vertical-align: middle;
  cursor: pointer;
      height: 40px;
    width: 265px;
}
._css-rel-input i {
  margin: auto 12px auto 16px;
}
/* ._css-rel-btn:hover {
  color: #49b5ff;
} */

._css-rel-centercontents {
  /* height: 78px; */
  /* display: flex; */
  /* justify-content: space-around; */
  align-items: center;
  /* height: 100%; */
  padding:0;
  /* padding-right: 0px; */
}

._css-right-custominfo {
  padding-left: 24px;
  padding-right: 24px;
  padding-bottom: 24px;
  box-sizing: border-box;
  overflow-y: auto;
}

._css-fieldval {
  flex: 1;
  text-align: left;
  margin-left: 16px;
  height: 32px;
  line-height: 32px;
  font-size: 14px;
  color: rgba(0, 0, 0, 0.85);
  outline: none;
  border: 1px solid rgba(0, 0, 0, 0.04);
  border-radius: 4px;
  padding-left: 16px;
  padding-right: 16px;
  font-weight: 500;
  background: #fff;
}

._css-fieldlabel {
  width: 88px;
  text-align: left;
  margin-left: 24px;
  height: 22px;
  line-height: 22px;
  font-size: 14px;
  margin: 16px 0 16px 24px;
  color: rgba(0, 0, 0, 0.45);
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow-x: hidden;
}

._css-fieldarea {
  /* height: 50px; */
  display: flex;
  align-items: center;
  box-sizing: border-box;
}

/* ._css-fieldarea._css-first {
  margin-top: 16px;
} */

._css-common-title {
  height: 24px;
  line-height: 24px;
  color: rgba(0, 0, 0, 0.85);
  font-size: 14px !important;
  margin: 20px 0 10px 0;
  text-align: left;
  font-weight: 500;
  color: #283A4F;
}

._css-left-custominfo {
  height: calc(100% - 310px);
  overflow-y: auto;
  padding-left: 24px;
  padding-right: 24px;
  box-sizing: border-box;
}
._css-left-edit-bottom{
  height: 60px;
  border-top: 1px solid rgba(0, 0, 0, 0.09);
}
._css-bottom-two-button{
  width:90%;
  height: 40px;
  display: flex;
  margin: 12px auto;
}
._css-bottom-sure{
  border: 1px solid #1890FF;
  width: 202px;
  background: #1890FF;
  color: #fff;
  text-align: center;
  line-height: 40px;
  border-radius: 4px;
  cursor: pointer;
}
._css-bottom-cancel{
  border: 1px solid #1890FF;
  background:#fff;
  color: #1890FF;
  margin-right: 16px;
}
._css-bottom-edit-button{
  width:90%;
  height: 40px;
  background: #1890FF;
  color: #fff;
  margin: 12px auto;
  line-height: 40px;
  border-radius: 4px;
  cursor: pointer;
}
/* 右上角构件状态 */
._css-right-basicinfo {
  /* height: 124px; */
  /* border-bottom: 1px solid rgba(0, 0, 0, 0.09); */
  box-sizing: border-box;
  padding-left: 24px;
  padding-right: 24px;
  display: flex;
  justify-content: space-around;
}

._css-part-item {
  width: 100%;
}

._css-right-state-img {
  width: 140px;
  align-items: center;
  justify-content: center;
}
._css-right-down-img {
  position: absolute;
  bottom: 0;
  left: 0;
  height: 24px;
  border-radius: 0 0 4px 4px;
  width: 100%;
  background-color: #1890ff;
  display: none;
}
._css-right-down-icon {
  line-height: 24px;
  color: #fff;
}
._css-right-qrcode {
  width: 140px;
  position: relative;
  height: 140px;
  background-color: #f7f7f7;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 16px;
  border-radius: 4px;
  margin-bottom: 10px;

  box-shadow:0px 2px 10px -3px rgba(0,19,84,0.2);
}
._css-qrcode-shade{
  position: absolute;
  top: 0;
  left: 0;
  width:90px;
  height: 90px; 
  border-radius: 4px;
  width: 100%;
  background-color: rgba(24,144,255,0.1);
  display: none;
}
._css-right-qrcode:hover ._css-qrcode-shade{
  display: inline-block;
}
._css-right-qrcode:hover ._css-right-down-img {
  display: inline-block;
}
._css-right-qrcode ._css-right-qrcode-icon {
  width: 130px;
  height: 130px;
  font-size: 75px;
  align-items: center;
  justify-content: center;
  line-height: 78px;
}
/* 左边或右边剩下的部分 */
._css-part-leave {
  flex: 1;
}

/* 左边的基本信息部分 */
._css-left-basicinfo {
  flex: none;
  height: 310px;
  padding-left: 24px;
  padding-right: 24px;
  box-sizing: border-box;
}

._css-materialbody-part {
  display: flex;
  flex-direction: column;
}

._css-materialinfo-body {
  flex: 1;
  display: flex;
  height: calc(100% - 64px);
  padding-bottom: 4px;
  box-sizing: border-box;
}

._css-materialbody-left {
  /* flex: 1; */
  /* width:468px; */
  width: 360px;
  height: 100%;
}

._css-materialbody-splitter {
  flex: none;
  border-left: 0.5px solid rgba(0, 0, 0, 0.09);
  border-right: 0.5px solid rgba(0, 0, 0, 0.09);
}

._css-materialbody-center {
    flex: 1;
    display: flex;
    flex-direction: column;
    height: 100%;
}
._css-material-state-top{
  height: 200px;
  border-bottom:1px solid transparent;
  position:relative;
}
._css-material-state-bottom{
  overflow-y: scroll;
  display: flex;
  flex-direction: column;
  /* flex:none; */
  flex:1;
  margin-bottom: 12px;
}

._css-materialinfo-all {
  position: fixed;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  background-color: rgba(0, 0, 0, 0.3);
}

._css-materialinfo-front {
  width: 80%;
  min-width: 1344px;
  background-color: rgba(255, 255, 255, 1);
  box-shadow: 0px 13px 24px -17px rgba(11, 41, 62, 0.8);
  border-radius: 4px;
  display: flex;
  flex-direction: column;
  height: calc(100% - 64px);
}
._css-head {
  flex: none;
  border-bottom: 1px solid rgba(0, 0, 0, 0.09);
}
._css-fieldlabel-title{
  color: #666666;
  font-size:14px;
  font-weight:500;
  line-height: 32px;
}
div::-webkit-scrollbar {
  width: 5px;
  height: 5px;
}
div::-webkit-scrollbar-track {
  background: transparent;
  border-radius: 2px;
}
div::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 10px;
}
div::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.04);
}
div::-webkit-scrollbar-corner {
  background: rgba(0, 0, 0, 0.3);
}
._css-fieldlabel-cancel{
  flex: 1;
}
._css-fieldlabel-cancel p{
  width: 110px;
  border-radius: 4px;
  border: 1px solid #1890ff;
  text-align: center;
  line-height: 32px;
  color: #1890ff;
  float: right;
  font-size: 14px;
  cursor: pointer;
  margin-right:24px;
}
._css-typeitem-title {
  text-align: center;
  position: relative;
  line-height: 44px;
  font-size: 16px;
  font-weight: 500;
  color: rgba(0, 0, 0, 1);
  border-bottom: 1px solid rgba(0, 0, 0, 0.09);
}
._css-typeitem-title ._css-typeitem-close {
  position: absolute;
  top: 12px;
  right: 12px;
  color: rgba(0, 0, 0, 0.45);
  cursor: pointer;
}
._css-typeitem-opentitle{
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
  padding: 0 40px;
}
._css-click-model-detail{
  position: absolute;
  top:200px;
  left:500px;
  background:rgba(255,255,255,1);
  box-shadow:0px 2px 14px 0px rgba(26,26,26,0.1);
  border-radius:4px;
  width:384px; 
}
._css-moldel-content{
  overflow-y:auto;
}
._css-model-custom{
  text-align: left;
}
._css-model-custom-title{
  background-color: #F7F7F7;
  line-height: 40px;
  padding-left:16px;
  color: rgba(0, 0, 0, 0.85);
  font-weight: 500;
}
._css-model-custom ul li{
  line-height: 36px;
  padding-left:16px;
  display: flex;
  color: rgba(0, 0, 0, 0.45);
}
._css-model-custom ul li label{
  width:217px;
}
._css-model-custom ul li span{
  flex:1;
}
._css-associated-model{
  margin: 0;
  padding:0;
  margin-bottom: 12px;
}
._css-associated-attachments-list{
  margin-bottom:8px;
}
._css-associated-attachments-list:hover{
  background: #F7F7F7;
}
._css-bigimg{
  position: absolute;
  top:0;
  left:0;
  bottom:0;
  right:0;
  background: #000;
}
._css-detail-bigimg{
  position: absolute;
  top:0;
  left:0;
  bottom:0;
  right:0;
  background:rgba(255,255,255,1);
  box-shadow:0px 2px 14px 0px rgba(26,26,26,0.1);
  border-radius:4px;
  width: 800px;
  max-height: 500px;
  margin: auto;
}
._css-detail-img-title{
  position: fixed;
  top:0;
  left:0;
  line-height: 44px;
  font-size: 16px;
  color:rgba(0, 0, 0, 0.85);
  border-bottom: 1px solid rgba(0, 0, 0, 0.65);
}
._css-detail-contain-text{
  overflow-y: auto;
  max-height: 420px;
  margin:20px 5%;
}
._css-detail-contain{
  width: 100%;
  text-align: center;
} 
._css-detail-contain img{
  width: 100%;
  /* height: 400px; */
  /* display: block; */
}
</style>
<style scoped>

._css-button-three {
  height:100%;
}

._css-clickdownload-labelicon {
  color: #666666;
  height:20px;
  line-height: 20px;
}

._css-clickdownload-labeltext {
  max-width: 112px;
  color: #666666;
  font-size: 14px;
}

._css-clickdownload-labelinner {
  width:160px;
  display: flex;
  justify-content: space-between;
  cursor: pointer;
}

._css-clickdownload-label {
  height:20px;
  display: flex;
  justify-content: space-around;
}

._css-materialinfo-front ._css-title ._css-title-main{
  font-size: 16px !important;
  font-weight: 500;
}
._css-materialinfo-front ._css-title {
  margin-left:24px !important;
}
._css-materialinfo-front ._css-head{
  background: rgba(0, 0, 0, 0.04);
}
._css-iframe._css-materialinfo{
  /* margin:16px 10px 16px 24px;

 */

  position: absolute;
    width: 100%;
    height: calc(100% - 64px);
    left: 0;
    top: 64px;
}

._css-iframe._css-materialinfo iframe {
  height:100%;
  width:100%;
}

::-webkit-scrollbar {
    width: 5px;
    height: 5px;
}
::-webkit-scrollbar-thumb{
    background-color: #999;
    -webkit-border-radius: 5px;
    border-radius: 5px;
}
::-webkit-scrollbar-thumb:vertical:hover{
    background-color: #666;
}
::-webkit-scrollbar-thumb:vertical:active{
    background-color: #333;
}
::-webkit-scrollbar-button{
    display: none;
}
::-webkit-scrollbar-track{
    background-color: #f1f1f1;
}
     
</style>