<template>
  <div class="pano-content" :style="getContentStyle()">
		<div class="header-title">{{m_selectedobj.PbName}}</div>
		<div class="header-close header-back" v-if="panoSplitScreen" @click="backLevel">返回上一级</div>
		<div class="header-close icon-suggested-close" @click="close"></div>
    <div class="content-list">
      <ul>
        <li 
					class="pano-list-li"
					v-for="(item, index) in panoList" 
					:key="index" 
					@click="changeList(item)">
          <p class="list-img" :class="panoList.length == 1 ? 'list-img-margin':''">
            <span class="img" :style="getStyle(item.PsScenename)"></span>
            <span class="list-rel" v-if="item.ModelId"></span>
          </p>
          <p class="time-line">
            <span
              class="line"
              :class="[
                index == 0 && panoList.length !=1 ? 'line-one' : '',
                index == panoList.length - 1 && index != 0 ? 'line-last' : '',
              ]"
            ></span>
            <span class="ponit"></span>
            <span class="time">{{ item.PsCollecteddatetime | filtTime }}</span>
          </p>
        </li>
      </ul>
    </div>
  </div>
</template>
<script>
export default {
  data() {
    return {
      panoList_:[],  // 当前数组
			selectedobj: {}
    };
  },
  props: {
    m_selectedobj: {
      type: Object,
      default: {},
    },
		panoList: {
			type: Array,
			default: []
		},
		panoSplitScreen:{
			type: Boolean,
			default: false
		},
		posStyle:{
			type: Boolean,
			default: false
		},
  },
	watch:{
		m_selectedobj(val){
			this.selectedobj = val;
		},
		panoList(val){
			this.panoList_ = val;
		}
	},
  filters: {
    filtTime(inputtime) {
      if (!inputtime || inputtime.trim().length == 0) {
        return inputtime;
      }
      if (inputtime.length >= "2019-09-16T11:14".length) {
        return inputtime.substr(0, 10);
      }
    },
  },
  mounted() {
		this.selectedobj = this.m_selectedobj;
		this.panoList_ = this.panoList;
	},
  methods: {
    getStyle(item) {
      let _s = {};
    	_s["background-image"] = `url("${window.bim_config.webserverurl}/Panorama/${this.selectedobj.PbUrl}/vtour/panos/${item}.tiles/thumb.jpg")`;
      return _s;
    },
		changeList(item){
			this.$emit('changeList',item,this.selectedobj)
		},
		close(){
			this.$emit('close')
		},
		backLevel(){
			this.$emit('backLevel')
		},
		getContentStyle(){
			let _s = {}
			this.posStyle ? _s['right'] = "calc( 10% - 24px)" : _s['right'] = "80px";
			this.posStyle ? _s['width'] = "80%" : _s['width'] = "calc(100% - 256px - 108px - 64px - 48px)";
			return _s;
		}
     
  },
};
</script>
<style scoped>
.pano-content {
  position: fixed;
  top: 40%;
  right: calc( 10% - 24px);
  margin: 0 auto;
  height: 250px;
  width: 80%;
	padding: 24px;
	background: rgba(255,255,255,.9);
	border-radius: 8px;
  border: 1px solid #e8e8e8;
	z-index: 1002;
}
.header-title{
	font-size: 18px;
	font-family: PingFangSC-Medium, PingFang SC;
	font-weight: 500;
	color: #222222;;
	text-align: left;
}
.header-close.header-back{
	position: absolute;
  top: 12px;
  right: 64px;
	width: 100px;
	font-size: 18px;
}
.header-close{
	position: absolute;
  top: 6px;
  right: 6px;
  width: 20px;
  height: 20px;
  color: #222222;
	cursor: pointer;
}
.content-list {
  margin-top: 10px;
  width: 100%;
  height: 93%;
  overflow-x: auto;
}
.content-list ul {
  display: flex;
}
.content-list ul li {
  text-align: center;
	cursor: pointer;
	
}
.pano-list-li:hover .list-img{
	border: 2px solid #FFFFFF;
}
.pano-list-li:hover .ponit{
	border: 4px solid #0C6CF2;
}
.pano-list-li:hover .time {
	color: #0C6CF2;
}
.list-img {
  position: relative;
	width: 200px;
	height: 138px;
	border-radius: 4px;
	border: 2px solid #FFFFFF;
	margin: 10px 24px 16px 0;
}
.list-img-margin{
	margin: 10px 0px 16px 0;
}

.list-img:hover{
  border: 2px solid #666666;
}

.img {
  display: inline-block;
  width: 200px;
	height: 138px;
  background-repeat: no-repeat;
  background-position: 0px 0px;
  background-size: 100% 100%;
}
.list-rel {
  position: absolute;
  right: 16px;
  top: 13px;
	width: 16px;
  height: 16px;
  border-radius: 50%;
  background-image: url('../../assets/images/pano-list-rel.png');
  background-size: contain;
  background-repeat: no-repeat;
}
.time-line {
  position: relative;
}
.time-line span {
  display: block;
}
.line {
  position: absolute;
  top: 10px;
  left: 0;
  width: 100%;
  height: 2px;
  background: #666666;
}
.line-one {
  left: 50%;
  width: 50%;
}
.line-last {
  left: 0;
  width: 50%;
}
.ponit {
  position: absolute;
  top: 5px;
  left: 50%;
  margin-left: -5px;
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: #fff;
	border: 4px solid #666666;
}
.time {
  position: absolute;
  top: 30px;
  margin: 0 auto;
  width: 100%;
  color: #666666;
	font-size: 16px;
	font-family: PingFangSC-Medium, PingFang SC;
	font-weight: 500;
}

.pano-content div::-webkit-scrollbar {
  width: 5px;
  height: 5px;
}
.pano-content div::-webkit-scrollbar-track {
  background: transparent;
  border-radius: 2px;
}
.pano-content div::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.19);
  border-radius: 10px;
}
.pano-content div::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.09);
}
.pano-content div::-webkit-scrollbar-corner {
  background: rgba(0, 0, 0, 0.39);
}

</style>