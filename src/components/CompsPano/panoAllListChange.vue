<template>
  <div class="pano-content">
    <div class="pano-list-header">
      <div class="header-title">全景图总图</div>
      <div class="_css-select">
        <i class="icon-interface-filter"></i>
        <el-select
          v-model="selectValue"
          filterable
					clearable
          placeholder="输入标签筛选"
          @change="selectChangeFun"
        >
          <el-option
            v-for="item in selectOption_"
            :key="item.LabelId"
						:label="item.LabelName"
						:value="item.LabelId"
          >
          </el-option>
        </el-select>
      </div>
      <div class="_css-select search-input">
        <i class="icon-interface-search"></i>
        <el-input
          v-model="inputVal"
          placeholder="搜索全景图名称"
          @input="searchName"
        ></el-input>
      </div>
      <div class="header-close icon-suggested-close" @click="close"></div>
    </div>
    <div class="content-list">
      <div
        v-for="itemfirst in panoList_"
        :key="itemfirst.LabelId"
        class="_css-pano-list"
      >
      <ul>
        <li 
					class="pano-list-li"
					v-for="item in itemfirst.Panoramas" 
					:key="item.PbGuid" 
					@click="ClickCheckedListByPano(item)">
          <p class="list-img">
            <span class="img" :style="getStyle(item)"></span>
          </p>
          <p class="time-line">
            <span class="time pano-name">{{ item.PbName }}</span>
            <span class="time">采集时间：{{ item.PbUpdatetime | filtTime }}</span>
          </p>
        </li>
      </ul>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  data() {
    return {
      panoList_:[],  // 当前数组
      selectValue: "", // 标签搜索选择
      inputVal: "", // 搜索全景图名称
      searchlabelId: '',
      searchPbName:'',
      selectOption_: [],
    };
  },
  props: {
    selectOption: {
			type: Array,
			default: false
		},
  },
	watch:{ 
    selectOption(val){
			this.selectOption_ = val;
		},
	},
  filters: {
    filtTime(inputtime) {
      if (!inputtime || inputtime.trim().length == 0) {
        return inputtime;
      }
      if (inputtime.length >= "2019-09-16T11:14".length) {
        return inputtime.substr(0, 10);
      }
    },
  },
  mounted() {
    this.selectOption_ = this.selectOption;
    this.searchlabelId = '';
    this.searchPbName = '';
    this.getList();
	},
  methods: { 
    selectChangeFun(val) {
			if(val== null || val== undefined)  val = "" 
			this.searchlabelId = val;
			this.getList();
    },
    searchName(val) {
			this.searchPbName = val;
			this.getList();
    },
    getStyle(item) {
      let _s = {};
      _s["background-image"] = `url("${window.bim_config.webserverurl}/Panorama${item.PbUrl}/cover.png")`;
      return _s;
    },
		ClickCheckedListByPano(item){
			this.$emit('ClickCheckedListByPano',item)
		},
		close(){
			this.$emit('close')
		},
    getList(){
      let _this = this;
      let _organizeId = _this.$staticmethod._Get("organizeId");
      let _LoadingIns = _this.$loading({
        text: "加载中",
        background: 'rgba(0, 0, 0, 0.7)',
        target: document.getElementsByClassName("pano-content")[0],
      });
      _this.$axios
					.get(
          	`${window.bim_config.webserverurl}/api/Panorama/PanoramaFile/GetListByLabelGroup?organizeId=${_organizeId}&Token=${_this.$staticmethod.Get('Token')}&labelId=${_this.searchlabelId}&pbName=${_this.searchPbName}`,
        	)
          .then((x) => {
            _LoadingIns.close();
            if (x.status == 200 && x.data.Ret > 0) {
              _this.panoList_ = x.data.Data;
            } else {
              _this.$message.error(x.data.Msg);
              debugger;
            }
          })
          .catch((x) => {
            _LoadingIns.close();
            _this.$message.error(x.data.Msg);
          });
    }
     
  },
};
</script>
<style scoped>
.pano-content {
  position: fixed;
  top: 40%;
  right: calc( 10% - 24px);
  height: 260px;
  width: 80%;
	padding: 15px 24px 24px;
	background: rgba(255,255,255,.9);
	border-radius: 8px;
	z-index: 1001;
}
.pano-list-header{
  display: flex;
}
.header-title{
  font-size: 18px;
  line-height: 40px;
	font-family: PingFangSC-Medium, PingFang SC;
	font-weight: 500;
	color: #666666;
	text-align: left;
  margin-right:24px
}
._css-select{
  position: relative;
  width: 200px;
  height: 40px;
  padding-left: 20px;
  background: #F8F8F8;
  border-radius: 2px;
  border: 1px solid #D9D9D9;
  margin-right: 24px;
}
._css-select i {
  position: absolute;
  top: 10px;
  left: 8px;
  color: #999999;
}
.search-input {
  padding-left: 32px;
}
.header-close{
	position: absolute;
  top: 16px;
  right: 24px;
  width: 20px;
  height: 20px;
  color: #222222;
	cursor: pointer;
}
.content-list {
  padding-top: 4px;
  width: 100%;
  height: 93%;
  overflow-x: auto;
  display: flex;
}
.content-list ul {
  display: flex;
}
.content-list ul li {
  text-align: center;
	cursor: pointer;
	
}
._css-pano-list{
  display: flex;
}
.pano-list-li:hover .list-img{
	border: 2px solid #ffffff;
}


.list-img {
  position: relative;
	width: 200px;
	height: 138px;
	border-radius: 4px;
	border: 2px solid #FFFFFF;
	margin: 10px 24px 13px 0;
}
.list-img-margin{
	margin: 10px 0px 16px 0;
}

.list-img:hover{
  border: 2px solid #fff;
}

.img {
  display: inline-block;
  width: 200px;
	height: 138px;
  background-repeat: no-repeat;
  background-position: 0px 0px;
  background-size: 100% 100%;
}
.time-line {
  position: relative;
}
.time-line span {
  display: block;
}
.line {
  position: absolute;
  top: 10px;
  left: 0;
  width: 100%;
  height: 2px;
  background: #666666;
}


.time {
  position: absolute;
  top: 30px;
  margin: 0 auto;
  width: 100%;
  color: #666666;
	font-size: 16px;
	font-family: PingFangSC-Medium, PingFang SC;
	font-weight: 500;
}
.pano-name{
  position: absolute;
  top: 0px;
}

.pano-content div::-webkit-scrollbar {
  width: 5px;
  height: 5px;
}
.pano-content div::-webkit-scrollbar-track {
  background: transparent;
  border-radius: 2px;
}
.pano-content div::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.19);
  /* background: #f00; */
  border-radius: 10px;
}
.pano-content div::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.09);
}
.pano-content div::-webkit-scrollbar-corner {
  background: rgba(0, 0, 0, 0.39);
}

</style>