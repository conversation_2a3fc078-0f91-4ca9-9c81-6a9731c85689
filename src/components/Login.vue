<template>
  <div class="hello css-h100 css-prel">
    <!-- 左中右 -->
    <el-row class="css-h100 _css-maininner"
    :style="style_getLoginBackgroundImage()"
    >
      <el-col :span="8" class="css-h100">
        <div class="_css-leftpart">
          <div class="login-right-title"
          :style="{'visibility': getVisibility()}"
          >
            <div class="login-right-title-icon mulcolor-logo-bg" v-if="isShowLogo"></div>
            <div
            :style="style_lefttoptitle()"
            class="login-right-title-text">{{getTitle()}}</div>
          </div>
        </div>
      </el-col>
      <el-col :span="8" class="css-h100">
        <div class="css-h100 css-h100-center">
          <div class="_css-loginform">
            <!-- 登录表单主体 -->
            <div class="css-h100 css-w100 css-prel login-body" v-if="join_body_box">
              <!-- logo及标题 -->
              <div class="css-h40 css-fc css-bsb css-b css-w100">
                <div class="login-text-left">登录</div>
                <div
                v-if="!customfunc_hideapp()"
                class="login-text-right" >
                  <div class="login-text-right-BIM">下载 App</div>
                  <div class="login-text-right-quickmack icon-interface-qrcode" @click="QR_code"></div>
                </div>
              </div>
              <!-- //logo及标题 -->
              <!-- 用户名 -->
              <div class="css-w100 css-mt34 css-h32 css-bsb _css-username h30 css-tal">
                <compsAnimateInput
                  :autofocus="true"
                  v-model="inputName"
                  @keyup.enter.native="enterup($event);"
                  :defaultValue="inputName.value"
                  prefix-icon="el-icon-search"
                  labelFootnote=""
                  label="邮箱/账号/手机号">
                </compsAnimateInput>
              </div>
              <!-- //用户名 -->
              <!-- 密码 -->
              <div class="css-w100 css-mt24 css-h32 css-bsb _css-password h30 css-tal">
                <compsAnimateInput
                  type="password"
                  v-model="inputPassword"
                  @keyup.enter.native="enterup($event);"
                  :defaultValue="inputPassword.value"
                  prefix-icon="el-icon-search"
                  labelFootnote=""
                  label="请输入密码">
                </compsAnimateInput>
              </div>
              <!-- //密码 -->
              <!-- 自动登录及忘记密码框 -->
              <div class="css-h20 css-mt26 css-bsb css-b css-w100 css-prel">
                <el-checkbox v-model="extdata.autologin" class="css-pabs css-l0">记住我</el-checkbox>
                <el-button type="text" class="css-pabs css-r0" @click="forgetpassword_click">忘记密码</el-button>
              </div>
              <!-- //自动登录及忘记密码框 -->
              <div class="css-h32 css-mt26 css-bsb css-b">
                <el-button
                  :loading="extdata.islogining"
                  type="primary"
                  class="css-h100 css-w100 css-br2 css-bl2"
                  @click="testlogin($event)"
                >登录</el-button>
              </div>
              <div
              v-show="notHidetry()"
              class="css-h22 css-mt16 css-bsb css-b css-tc css-fc css-jcsa css-prel">
                <div class="_css-line css-pabs css-l56"></div>
                <el-button
                @click.stop="guestlogin();"
                type="text" class="css-w80 css-h22 css-fs12">体验账户登录</el-button>
                <div class="_css-line css-pabs css-r56"></div>
              </div>
            </div>
            <!-- //登录表单主体 -->


            <!-- 下载二维码页面 -->
            <div class="css-h100 css-w100 css-prel login-body" v-if="QR_code_box" >
              <!-- logo及标题 -->
              <div class="css-h40 css-fc css-bsb css-b css-w100">
                <div class="login-text-left-code">下载 App</div>
                <div class="login-text-right">
                  <div class="login-text-right-BIM-code" @click="jion_body_code">登录</div>
                  <div class="login-text-right-quickmack-app icon-interface-diannaodenglu" @click="jion_body_code"></div>
                </div>
              </div>
              <!-- 二维码图标 -->
              <div class="_css-qrcode-container">
                <img class="_css-qrcode-image" :src="qrcodeurl" alt="">
              </div>
              <div class="css-h22 css-mt16 css-bsb css-b css-tc css-fc css-jcsa css-prel QR_code-bottom">手机扫一扫，免费下载 安卓/iOS App</div>
            </div>
            <!-- //下载二维码页面  -->

          </div>
        </div>
      </el-col>
      <el-col :span="8" class="css-h100">
        <div class="css-h100"></div>
      </el-col>
    </el-row>
    <!-- //左中右 -->
    <!-- 备、公司名称 -->
    <div class="css-pabs css-b26 css-tc css-w100 css-fc css-jcsa">
      <div
      v-if="!customfunc_hideloginbottomorigin()"
      class="_css-login-bottom _patched  css-h20 css-fs12">
        <template  >
          <!-- <div @click="jumptobeian()" class="_css-bottom-itemi css-cp">{{getICPString()}}</div>
          <div class="_css-bottom-itemi">{{getCompanyString()}}</div>
          <div class="_css-bottom-itemi _css-patchshow">版本: {{Version_number}}</div> -->
          <div class="_css-bottom-itemi">
            {{getCompanyString()}}
            <span class="_css-bottom-itemi _css-patchshow" v-if="Version_number && Version_number.length > 1">版本: {{Version_number}}</span>
          </div>
          <div class="_css-bottom-itemi css-cp" v-if="getBeiAnString().length > 1 || getICPString().length > 1 ">
            <i class="guohui"></i>
            <div @click="jumptobeian()">
              {{getBeiAnString()}}
            </div>
            <div class="css-pl16" @click="jumptoICP()">
              {{getICPString()}}
            </div>
          </div>
        </template>
      </div>
      <div
      v-else
      :style="style_loginbottomtext()"
        class="_css-login-bottom css-w310 css-h20 css-prel css-fs12">
        {{customfunc_loginbottomtext()}}
      </div>

    </div>
    <el-dialog
      :visible.sync="forgetpassword"
      width="410px"
      center>
      <div class="retrieve-password"><span class="icon-interface-lock"></span>找回密码</div>
      <div class="forget-mailbox">
        <el-input
        placeholder="请填写您的登录邮箱"
        prefix-icon="icon-interface-email"
        v-model="input_mailbox"
        clearable
        ></el-input>
      </div>
      <div class="forget-code">
        <el-input
          class="forget-code-left"
          placeholder="请输入验证码"
          prefix-icon="icon-interface-lock"
          v-model="input_code"
        ></el-input>
        <el-button type="text" v-show="sendAuthCode" class="icon-interface-doc forget-code-right auth_text_blue" @click="SendForgotPwdEmail">点击发送验证码</el-button>
        <el-button type="text" v-show="!sendAuthCode" class="icon-suggested-time forget-code-right">重新发送(<span class="auth_text_blue">{{auth_time}}</span>S)</el-button>
        <div class="code-msg" v-show="!sendAuthCode"><span class="icon-suggested-check_circle"></span>验证码已发送至邮箱，若未收到邮件，点击重新发送</div>
      </div>
      <div class="next-step" @click="ValidResetToken">下一步</div>
      <div class="to-log-in" @click="forgetpassword = false">去登录</div>
    </el-dialog>
    <!-- 设置新密码模态框 -->
    <el-dialog
      :visible.sync="setnewpassword"
      width="410px"
      center>
      <div class="set-password"><span class="icon-interface-lock"></span>设置新密码</div>
      <div class="new-password">
        <el-input
        type="password"
        placeholder="请输入密码,6-32位字符,至少使用两种字符组合"
        prefix-icon="icon-interface-lock"
        maxlength="32"
        v-model="input_newpassword"
        ></el-input>
      </div>
      <div class="confirm-password">
        <el-input
          type="password"
          placeholder="请再次输入密码"
          prefix-icon="icon-interface-lock"
          maxlength="32"
          v-model="input_confirmpassword"
        ></el-input>
      </div>
      <div class="next-step confirm" @click="ResetPwd">确定</div>
      <div class="to-log-in" @click="setnewpassword = false">去登录</div>
    </el-dialog>

    <!-- 新密码设置成功弹出框 -->
    <el-dialog
      :visible.sync="Password_changed_success"
      width="410px"
      center>
      <div class="password-success"><span class="icon-interface-lock"></span>恭喜,密码更改成功</div>
      <div class="gray-line"></div>
      <div class="login-ID">登录账号</div>
      <div class="login-mail">{{inputName.value}}</div>
      <div class="next-step confirm" @click="Password_changed_success = false">立即登录</div>
    </el-dialog>
  </div>
</template>

<script>
import compsAnimateInput from "./CompsCommon/compsAnimateInput";
export default {
  name: "Login",
  data() {
    return {
      isShowLogo: true,
      qrcodeurl: '', // 下载APP二维码的图片地址
      imageQR: '',
      inputPassword: {},
      inputName: {},
      extdata: {
        islogining: false,
        title: "BIMe协作平台",
        username: "",
        userpassword: "",
        checkList: "",
        autologin: false,
        returnurl: "" //回跳地址
      },
      forgetpassword: false,  //忘记密码模态框默认关闭状态
      setnewpassword: false,  //设置新密码模态框默认关闭状态
      Password_changed_success: false,  //设置新密码提示成功模态框默认关闭
      input_mailbox:'',  //忘记密码输入框(邮箱)
      input_code:'', //忘记密码输入框(验证码)
      input_newpassword:'', //设置新密码输入框(新密码)
      input_confirmpassword:'',  //设置新密码输入框(确认新密码)
      account_UserId:'', //忘记密码点击下一步，成功状态下后端返回的当前用户ID(在下一步设置新密码，用来发送给后端)
      sendAuthCode:true,/*布尔值，通过v-show控制显示‘获取按钮’还是‘倒计时’ */
      auth_time: 0, /*倒计时 计数器*/

      join_body_box: true,  //登录页面 盒子默认打开
      QR_code_box:false,  //下载二维码页面 默认关闭
      Version_number:''  //版本号

    };
  },
  created(){
    this.isShowLogo = window.bim_config.isShowLogo
    var _this = this;
    var _autologin = _this.$staticmethod.Get("autologin")=="true";

    _this.extdata.autologin = _autologin?true:false;
    if (_autologin) {
      _this.extdata.username = _this.$staticmethod.Get("username");
      _this.inputName.value =  _this.$staticmethod.Get("username");
      _this.extdata.userpassword = _this.$staticmethod.Get("userpassword");
      _this.inputPassword.value = _this.$staticmethod.Get("userpassword");

    } else {
       _this.extdata.username = "";
        _this.inputName.value =  "";
      _this.extdata.userpassword = "";
       _this.inputPassword.value = "";
    }
    _this.Version_number = window.bim_config.Version_number
  },
  mounted() {

    // 当前是否是记住我
    var _this = this;
    window.debugvue = _this;

    // 为二维码图片地址属性赋值
    let url = `${window.location.origin}${window.bim_config.hasRouterFile}/#/LinkShare/AppDownloadLinkQR`;
    this.qrcodeurl = `${this.$urlPool.QRCode}?encodedUrl=${encodeURIComponent(url)}&token=${this.$staticmethod.Get("Token")}`;

    // 判断缓存的Token，直接跳转到 /#/Token页面
    var ifHasLogon = _this.$staticmethod.Get("Token");

    // 判断有无Token参数
    // 没有Token 参数且sessionStorage中Token不为空，则根据情况跳转到不同的页面
    if (ifHasLogon && !_this.$route.params.Token) {
      if (_this.$staticmethod.Get("RealName") == '超级管理员') {
        //window.location.href = '/#/Admin/Index/' + ifHasLogon;
        // window.location.href = '/#/Admin/Index_V2/' + ifHasLogon;
        window.location.href = `${window.bim_config.hasRouterFile}/#/Admin/Index_V2/${ifHasLogon}`;
      } else {
        window.location.href = `${window.bim_config.hasRouterFile}/#/Home/Boot/${ifHasLogon}`;//'/#/Home/Boot/' + ifHasLogon;
      }
      return;
    }
    //使页面获取焦点，避免浏览器自动补全账号时 输入框动画不能正确执行
    window.focus();

    // 如果有 Token 参数，且正确的话，则不跳
    // 如果当前没有携带 Token 参数【，且 sso_isusing 的话】，执行 _this.$staticmethod.jumpssomvc();
    _this.$staticmethod.jumpssomvc();

  },
  components: {
    compsAnimateInput
  },

  computed: {
    //生成app下载地址的二维码
    getQRCode() {
      let url =  `${window.location.host}${window.location.hasRouterFile}/#/LinkShare/AppDownloadLinkQR`;
      let imageQR = `${this.$urlPool.QRCode}?encodedUrl=${encodeURIComponent(url)}&token=${this.$staticmethod.Get("Token")}`;
      return imageQR;
    }
  },
  methods: {
    // 跳转到备案什么地址
    jumptobeian() {
      window.open("http://www.beian.gov.cn/portal/registerSystemInfo?recordcode=31010602006959");
    },
    jumptoICP() {
      window.open("https://beian.miit.gov.cn/#/Integrated/index");
    },
    // 登录页下方文字内容
    customfunc_loginbottomtext() {
      var text = window.bim_config.custom_loginbottomtext;
      return text || '';
    },
    // 登录页下方文字样式
    style_loginbottomtext() {
      var styleobjstr = window.bim_config.custom_loginbottomstylejson;
      var _s = {};
      try {
        _s = JSON.parse(styleobjstr);
      }catch(e) {

      }
      return _s;
    },
    // 是否隐藏登录页原有的底部信息
    customfunc_hideloginbottomorigin() {
      var _this = this;
      var ret = window.bim_config.custom_hideloginbottomorigin;
      return ret == 1;
    },
    // 左上角logo右侧名称样式
    style_lefttoptitle() {
      var stylelefttop = window.bim_config.custom_stylejson;
      var _s = {};
      try {
        _s = JSON.parse(stylelefttop);
      }catch(e) {

      }
      return _s;
    },
    // 配置文件中设置为隐藏app下载二维码按钮
    customfunc_hideapp() {
      var ret = window.bim_config.custom_hideapp == "1";
      return ret;
    },
    // 登录页面的背景图片样式
    style_getLoginBackgroundImage() {
      var _s = {};
      if (window.bim_config.custom_loginimageshow == "1") {
        _s["background-repeat"] = "no-repeat";
        _s["background-size"] = "cover";
        _s["background-position"] = "center";
        _s["background-image"] = `url("../../static/images/globalimgs/login.png")`;
      }
      return _s;
    },
    // 未隐藏试用按钮（链接）
    notHidetry(){
      if (window.bim_config.custom_hidetry == '1') {
        return false;
      } else {
        return true;
      }
    },
    // 登录页面左上角文字内容
    getTitle(){
      if (window.bim_config.custom_lefttoptext) {
        return window.bim_config.custom_lefttoptext;
      } else {
        return 'BIMe协作平台';
      }
    },
    getCompanyString() {
      if (window.bim_config.standard_icpCompany) {
        return window.bim_config.standard_icpCompany;
      } else {
        return '北京东晨工元科技发展有限公司';
      }
    },
    getICPString(){
      var _this = this;
      if (window.bim_config.standard_icp) {
        return window.bim_config.standard_icp;
      } else {
        return '京ICP备14047424号-2';
      }
    },
    getBeiAnString(){
      var _this = this;
      if (window.bim_config.standard_beian) {
        return window.bim_config.standard_beian;
      } else {
        return '京公网安备 11010502047278号';
      }
    },
    getVisibility(){
        return 'visible';
    },

    // 游客登录
    guestlogin(){
      window.open('http://www.probim.com.cn/bimesysq', '_blank');
      return;
    },
    // 回车登录
    enterup(ev) {
      this.testlogin();
    },
    testlogin() {
      let _this = this;
      _this.extdata.username = _this.inputName.value;
      _this.extdata.userpassword = _this.inputPassword.value;
      // 统一处理是否记住我，将其值存入 Set("username")及 Set("userpassword")
      // 如果当前点击了“记住我”，则将值写入 localStorage.
      if (_this.extdata.autologin) {
        _this.$staticmethod.Set("username", _this.extdata.username);
        _this.$staticmethod.Set("userpassword", _this.extdata.userpassword);
        _this.$staticmethod.Set("autologin", true);
      } else {
        _this.$staticmethod.Set("username", '');
        _this.$staticmethod.Set("userpassword", '');
        _this.$staticmethod.Set("autologin", false);
      }

      let data = {
        UserName: _this.inputName.value.trim(),
        Password: _this.$staticmethod.computepwdstr(
          _this.extdata.userpassword + _this.$configjson.publickey,
          _this.$CryptoJS
        ),
        IfSingleLogin: '',
      };

      _this.extdata.islogining = true;

      // 清除LastLoginActionTime
      this.$staticmethod.RemoveLocalStorage("LastActionTime");

      this.$axios({
        method: "post",
        headers:{
          'Content-Type':'application/json'
        },
        url: `${this.$urlPool.Login}`,
        data: data
      })
        .then(res => {
          if (res.data.Ret < 0) {

            // 如果为-1000,则自动点击“忘记密码”
            if (res.data.Ret == -1000) {
              _this.$message.warning(res.data.Msg);
              setTimeout(function(){
                _this.forgetpassword_click();
                _this.input_mailbox = (res.data.Data || _this.inputName.value.trim());
              }, 1000);
            } else {
              _this.$message.error(res.data.Msg);
            }

          } else if (res.data.Ret == 2) { // SuperSystem
            window.location.href = `${window.bim_config.hasRouterFile}/#/Admin/Valid/${res.data.Data.Token}`;
          } else if (res.data.Ret > 0 && res.data.Data) {
            // 判断有无回跳地址，并跳转到正确的地址
            let _tval = res.data.Data.token;
            _this.$staticmethod.Set("Token", _tval);
            let jumpurl = "";
            if (_this.$route.params.ReturnUrl) {
              let returl = _this.$route.params.ReturnUrl;
              if (returl.indexOf("/:Token?") > 0) {
                returl = returl.replace("/:Token?", `/${_tval}`);
              } else if (returl.indexOf("/:Token") > 0) {
                returl = returl.replace("/:Token", `/${_tval}`);
              } else {
                if(_this.extdata.usern1ame.toLowerCase()=="system"){
                  returl = `${window.bim_config.hasRouterFile}/#/Admin/Index_V2/${_tval}`;
                  this.$staticmethod.Set('IsSystem','1');
                }
                else{
                  returl = `${window.bim_config.hasRouterFile}/#/Home/Boot/${_tval}`;
                  this.$staticmethod.Set('IsSystem','0');
                }
              }
              jumpurl = returl;
            } else {
              if(_this.extdata.username.trim().toLowerCase()=="system"){
              jumpurl = `${window.bim_config.hasRouterFile}/#/Admin/Index_V2/${_tval}`;
              this.$staticmethod.Set('IsSystem','1');
              }
              else{
              jumpurl = `${window.bim_config.hasRouterFile}/#/Home/Boot/${_tval}`;
              this.$staticmethod.Set('IsSystem','0');
              }
            }

            // 将 UserId 写入 Set(UserId)
            _this.$staticmethod.consoleLog('[cl-auth][token][tokenkey]2');
            var userId = res.data.Data.alpha.replace(/\+/g, '-');
            _this.$staticmethod.Set("UserId", userId);

            // //判断有无回跳地址，并跳转到正确的地址
            // 调用新的接口，来获取 RealName
            _this.$axios.get(`${window.bim_config.webserverurl}/api/User/Home/GetUser?token=${_tval}`)
            .then(x => {
              if (x.data.Ret > 0) {
                 _this.$staticmethod.Set("RealName", x.data.Data.RealName);
                 _this.$staticmethod.Set("Email", x.data.Data.Email);
                 _this.$staticmethod.Set("Account", x.data.Data.Account);
              } else {
              }
              window.location.href = jumpurl;
            }).catch(x => {
              debugger;
            });
          }
          _this.extdata.islogining = false;
        })
        .catch(res => {
          console.warn(res);
          _this.extdata.islogining = false;
        });
    },

    // 点击发送验证码接口方法(向后端发送邮箱)
    SendForgotPwdEmail() {
      var _this = this;
      _this
        .$axios({
          method: "get",
          url: `${
            window.bim_config.webserverurl
          }/api/User/User/SendForgotPwdEmail?account=${this.input_mailbox}`
        })
        .then(x => {
          if (x.status == 200 && x.data.Ret > 0) {
            this.sendAuthCode = false;
            this.auth_time = 30;
            var auth_timetimer =  setInterval(()=>{
                this.auth_time--;
                if(this.auth_time<=0){
                    this.sendAuthCode = true;
                    clearInterval(auth_timetimer);
                }
            }, 1000);
          } else {
            _this.$message.warning(x.data.Msg);
          }
        })
        .catch(x => {

        });
    },
    // 输入好验证码点击下一步触发的接口方法(后端判断验证码是否正确，正确跳转到设置新页面页面)
    ValidResetToken() {
      var _this = this;
      _this
        .$axios({
          method: "post",
          url: `${window.bim_config.webserverurl}/api/User/User/ValidResetToken`,
          data: {
            // Token: _this.$staticmethod.Get("Token"),
            Account: this.input_mailbox,
            Account_SecurityCode: this.input_code,
          }
        })
        .then(x => {
          if (x.status == 200 && x.data.Ret > 0) {
            _this.account_UserId = x.data.Data;
            _this.forgetpassword = false;
            _this.setnewpassword = true;

            // 清空之前遗留得新密码输入框
            _this.input_newpassword = '';
            _this.input_confirmpassword = '';
          }else{
            _this.$message({
                  message: x.data.Msg,
                  type: 'error'
              });
          }
        })
        .catch(x => {});
    },

    // 设置好新密码点击确定触发接口方法(发送密码，确认密码)
    ResetPwd() {
      var _this = this;
      if(this.input_newpassword.length<6||this.input_confirmpassword.length<6){
        this.$message.warning('密码不能少于6个字符')
        return
      }
      if(this.input_newpassword!==this.input_confirmpassword){
        this.$message.warning('两次输入密码不一致')
        return
      }
       let input_confirmpassword = _this.$staticmethod.computepwdstr(
          _this.input_confirmpassword + _this.$configjson.publickey,
          _this.$CryptoJS
      );
      let input_newpassword = _this.$staticmethod.computepwdstr(
          _this.input_newpassword + _this.$configjson.publickey,
          _this.$CryptoJS
      );
      _this
        .$axios({
          method: "post",
          url: `${window.bim_config.webserverurl}/api/User/User/ResetPwd`,
          data: {
            Token: _this.$staticmethod.Get("Token"),
            Account_UserId: this.account_UserId,
            Account_SecurityCode: this.input_code,
            Pwd: input_newpassword,
            ConfirmPwd: input_confirmpassword
          }
        })
        .then(x => {
          if (x.status == 200 && x.data.Ret > 0) {
            _this.setnewpassword = false;
            _this.Password_changed_success = true
          }else{
            if (x.status == 200) {
              _this.$message.error(x.data.Msg);
            } else {
              _this.$message.error('服务器错误，请查看日志信息');
              console.error(x);
            }
          }
        })
        .catch(x => {});
    },
    // 控制二维码页面显示和隐藏 按钮事件
    QR_code(){
      var _this = this;
      _this.QR_code_box = !_this.QR_code_box;
      _this.join_body_box = false;
    },
    jion_body_code(){
      var _this = this;
      _this.QR_code_box = false;
      _this.join_body_box = true;
    },
    forgetpassword_click(){
      var _this = this;
      _this.forgetpassword = true;
      _this.input_mailbox = '';
      _this.input_code = '';
    }
  }
};
</script>

<style scoped>
._css-bottom-itemi{
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 4px;
}
._css-patchshow {
  padding-left: 20px;
  color:rgba(0,0,0,0.3);
}
._patched {
  /* display: flex;
  justify-content: space-around; */
  width: 520px;
}
._css-maininner{
  margin-bottom: 0 !important;
}
._css-leftpart{
  height:calc(100% - 29px);
}
._css-qrcode-container{
  margin-top:40px;
}

._patched .guohui{
  display: inline-block;
  background-image: url("../assets/images/guohui.png");
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
  width: 12px;
  height: 12px;
}
._css-qrcode-image{
  width:140px;
  height:140px;
}

._css-title {
  height: 32px;
  font-size: 24px;
  font-weight: 500;
  color: rgba(0, 0, 0, 1);
}
._css-line {
  height: 1;
  border: 0.5px solid black;
  width: 24px;
  background-color: rgba(0, 0, 0, 0.25);
}
.hello {
  background-color: rgba(240, 242, 245, 1);
}
._css-loginform {
  width: calc(40px + 214px + 8px);
  height: 100%;
  margin: auto;
  padding-top: 20%;
  box-sizing: border-box;
}
._css-illustration-outer {
  height: 178px;
  bottom: 60px;
  left: auto;
  right: auto;
  top: auto;
}
._css-illustration {
  width: 246px;
  background-image: url("../assets/images/illustration.png");
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
  z-index: 1;
}
/* 忘记密码弹出框样式 */
.forget-mailbox{
  width:362px;
  height:50px;
  background:rgba(0,0,0,0.02)
}
.forget-code{
  width:362px;
  height:50px;
  margin-top: 20px;
}
.forget-code-left{
  width:230px;
  height:50px;
  background:rgba(0,0,0,0.02);
}
.forget-code-right{
  width:124px;
  height:50px;
  background:rgba(0,0,0,0.04);
  float: right;
  font-size: 12px;
  color: rgba(0,0,0,1);
}
.code-msg{
  width:362px;
  height:20px;
  font-size:12px;
  color:rgba(0,0,0,1);
  line-height: 20px;
  margin-top: 8px;
}
.icon-suggested-check_circle{
  line-height: center;
}
.next-step{
  width:362px;
  height:50px;
  background:rgba(128,194,255,1);
  margin-top: 40px;
  text-align: center;
  line-height: 50px;
  color: white;
  cursor: pointer;
}
.to-log-in{
  width:80px;
  height:20px;
  font-size:12px;
  font-family:PingFangSC-Regular;
  font-weight:400;
  color:rgba(24,144,255,1);
  line-height:20px;
  margin-top: 18px;
  margin-left: 165px;
  user-select: none;
  cursor: pointer;
}
.retrieve-password{
  width:108px;
  height:28px;
  font-size:20px;
  font-family:PingFangSC-Medium;
  font-weight:500;
  color:rgba(0,0,0,1);
  line-height:28px;
  margin-left: 137px;
  margin-bottom: 28px;
  user-select: none;
}
.icon-interface-lock{
  font-size: 20px;
  color: rgba(24,144,255,1);
  margin-right: 8px;
}
/* 设置新密码弹框样式 */
.set-password{
  width:128px;
  height:28px;
  font-size:20px;
  font-family:PingFangSC-Medium;
  font-weight:500;
  color:rgba(0,0,0,1);
  line-height:28px;
  margin-left: 127px;
  margin-bottom: 28px;
  user-select: none;
}
.new-password{
  width:362px;
  height:50px;
  background:rgba(0,0,0,0.02)
}
.confirm-password{
  width:362px;
  height:50px;
  margin-top: 20px;
  background:rgba(0,0,0,0.02)
}
/* 新密码设置成功弹出框样式 */
.password-success{
  width:200px;
  height:28px;
  font-size:20px;
  font-family:PingFangSC-Medium;
  font-weight:500;
  color:rgba(0,0,0,1);
  line-height:28px;
  margin-left: 85px;
  margin-bottom: 28px;
  user-select: none;
}
.to-skip{
  width:120px;
  height:20px;
  font-size:12px;
  font-family:PingFangSC-Regular;
  font-weight:400;
  color:rgba(24,144,255,1);
  line-height:20px;
  margin-top: 18px;
  margin-left: 136px;
  user-select: none;
  cursor: pointer;
}
.gray-line{
  width:64px;
  height:1px;
  background:rgba(0,0,0,0.09);
  text-align: center;
  margin-top: 27px;
  margin-left: 148px;
}
.login-ID{
  width:80px;
  height:24px;
  font-size:16px;
  font-family:PingFangSC-Medium;
  font-weight:500;
  color:rgba(0,0,0,1);
  line-height:24px;
  text-align: center;
  margin-top: 24px;
  margin-left: 140px;
}
.login-mail{
  width:175px;
  height:22px;
  font-size:14px;
  font-family:PingFangSC-Regular;
  font-weight:400;
  color:rgba(0,0,0,1);
  line-height:22px;
  text-align: center;
  margin-top: 17px;
  margin-left: 91px;
}

/* 登陆页面样式重排 */
.login-body{
  width: 381px;
  height: 300px;
  margin-left: -103px;
  padding: 33px 40px;
  background:rgba(255,255,255,1);
}
.login-text-left{
  font-size:24px;
  font-family:PingFangSC-Medium;
  font-weight:500;
  color:rgba(0,0,0,1);
  width: 160px;
  text-align: left;
  cursor: pointer;
}
.login-text-left-code{
  font-size:24px;
  font-family:PingFangSC-Medium;
  font-weight:500;
  color:rgba(0,0,0,1);
  width: 180px;
  text-align: left;
}
.login-text-right{
  width: 221px;
  height: 50px;
}
.login-text-right-BIM{
  width: 104px;
  height: 32px;
  line-height: 32px;
  background:rgba(24,144,255,0.1);
  float: left;
  margin-top: 9px;
  margin-left:60px;
  border-radius:2px;
  border:1px solid rgba(24,144,255,0.45);
  font-size:12px;
  font-family:PingFangSC-Regular;
  font-weight:400;
  color:rgba(24,144,255,1);
  cursor: pointer;
}
.login-text-right-BIM-code{
  width: 56px;
  height: 32px;
  line-height: 32px;
  background:rgba(24,144,255,0.1);
  float: left;
  margin-top: 9px;
  margin-left:85px;
  border-radius:2px;
  border:1px solid rgba(24,144,255,0.45);
  font-size:12px;
  font-family:PingFangSC-Regular;
  font-weight:400;
  color:rgba(24,144,255,1);
  cursor: pointer;
}
.login-text-right-quickmack{
  width: 50px;
  height: 50px;
  font-size: 50px;
  color:rgba(0,0,0,0.25);
  float: right;
  background-size: contain;
  cursor: pointer;
}
.login-text-right-quickmack:hover{
  background-size: contain;
  color: rgba(24,144,255,1);
}
.login-text-right-quickmack-app{
  width: 50px;
  height: 50px;
  font-size: 50px;
  color:rgba(0,0,0,0.25);
  float: right;
  background-size: cover;
  cursor: pointer;
}
.login-text-right-quickmack-app:hover{
  background-size: cover;
  color: rgba(24,144,255,1);
}
.css-h40{
  height: 50px;
  margin-top: -9px;
}
/* 二维码 盒子样式 */
.QR_code-mack{
  width: 140px;
  height: 140px;
  font-size: 140px;
  margin-left: 120px;
  margin-top: 41px;
}
.QR_code-mack>img{
  width: 140px;
  height: 140px;
}
.QR_code-bottom{
  margin-top: 60px;
}

/* 登陆首页右上角头部 */
.login-right-title{
  display: flex;
  margin-top: 29px;
  margin-left: 37px;
}
.login-right-title-icon{
  width: 30px;
  height: 30px;
  margin-right: 10px;
  font-size:30px;
}
.login-right-title-icon>img{
  width: 30px;
  height: 30px;
}
.login-right-title-text{
  height: 30px;
  font-size:20px;
  font-family:PingFangSC-Medium;
  font-weight:500;
  color:rgba(0,0,0,1);
  line-height:30px;
}
.mulcolor-logo-bg{
  width:32px;
  height: 32px;
  background-image: url(../assets/images/logo.png) ;
  background-size: 100%;
  background-repeat: no-repeat;
}
</style>
