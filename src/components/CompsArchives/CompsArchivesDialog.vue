<template>
	<div class="archives-content">
		<zdialog-function
			:init_title="'档案管理'"
			:init_zindex="1003"
			:init_innerWidth="820"
			:init_width="820"
			init_closebtniconfontclass="icon-suggested-close"
			@onclose="close"
		>
			<div
				slot="mainslot"
				class="_css-addingnameinput-ctn"
				@mousedown="_stopPropagation($event)"
			>
                <div class="archives-module">
                    <div class="line-text"><div class="line">基本信息</div></div>
                    <div class="module-form">
                        <el-form class="top16" :rules="formRules" ref="infoFormData" :label-position="labelPosition" label-width="80px" :model="infoFormData">
                            <el-row :gutter="40">
                                <el-col :span="8">
                                    <el-form-item label="档案名称" prop="ArchivesName">
                                        <el-input v-model="infoFormData.ArchivesName" :disabled="editOrView" maxlength="20" placeholder="请输入档案名称"></el-input>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="8">
                                    <el-form-item label="档案编码" prop="ArchivesCode">
                                        <el-input v-model="infoFormData.ArchivesCode" disabled placeholder="请输入档案编码"></el-input>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="8">
                                    <el-form-item label="发文字号">
                                        <el-input v-model="infoFormData.IssuedNumber" :disabled="editOrView" maxlength="20" placeholder="请输入发文字号"></el-input>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            <el-row :gutter="40">
                                <el-col :span="8">
                                    <el-form-item label="档案分类" prop="CategoryName">
                                        <el-input v-model="selectTreeData.CategoryName" disabled placeholder="请输入档案分类"></el-input>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="8">
                                    <el-form-item label="编制单位">
                                        <el-input v-model="infoFormData.OrganizationCompany" :disabled="editOrView" placeholder="请输入编制单位"></el-input>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="8">
                                    <el-form-item label="档案份数">
                                        <el-input v-model="infoFormData.ArchivesNumber" :disabled="editOrView" @input="handleInputInteger" placeholder="请输入档案份数"></el-input>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            <el-row :gutter="40">
                                <el-col :span="8">
                                    <el-form-item label="责任人" prop="PersonLiable">
                                        <el-input v-model="infoFormData.PersonLiable" disabled placeholder="请输入责任人"></el-input>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="8">
                                    <el-form-item label="归档部门">
                                        <el-input v-model="infoFormData.ArchivalDepartment" :disabled="editOrView" placeholder="请输入归档部门"></el-input>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            <el-row >
                                <el-col :span="24">
                                    <el-form-item label="档案说明">
                                        <el-input v-model="infoFormData.ArchivalDescription" :disabled="editOrView"  maxlength="50" show-word-limit type="textarea" placeholder="请输入档案说明"></el-input>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                        </el-form>
                    </div>
                    <div class="module-form width98" v-if="nodeTypeIsArchived">
                        <el-row :gutter="40">
                            <el-col :span="8">
                                <div class="line-title">保管期限</div>
                                <div class="line-content">
                                    <el-select v-model="StoragePeriodValue" placeholder="请选择" :disabled='ArchivalAddressEdit'>
                                        <el-option
                                            v-for="item in StoragePeriodOption"
                                            :key="item.value"
                                            :label="item.label"
                                            :value="item.value">
                                        </el-option>
                                    </el-select>
                                </div>
                            </el-col>
                            <el-col :span="16">
                                <div class="line-title">保管位置</div>
                                <div class="line-content line-no-padding">
                                    <el-col :span="16" >
                                        <el-cascader
                                            :disabled='ArchivalAddressEdit'
                                            filterable
                                            v-model="ArchivalAddressValue"
                                            :options="ArchivalAddressOptions"
                                            @change="archivalAddressChange"></el-cascader>
                                    </el-col>
                                    <el-col :span="8">
                                        <el-input v-model="ArchivalAddressDetail" :disabled='ArchivalAddressEdit' class="address" type="text" placeholder="请输入详细保管位置"></el-input>
                                    </el-col>
                                </div>
                            </el-col>
                        </el-row>
                    </div>
                    <div class="line-text">
                        <div class="line">档案详情</div>
                        <div class="line-btn" v-if="!editOrView">
                            <span class="file-btn del" @click.stop="delFile">删除</span>
                            <span class="file-btn add" @click.stop="selectDoc">线上文档</span>
                            <span class="file-btn add" @click.stop="addFile">本地文档</span>
                        </div>
                    </div>
                    <div class="module-form">
                        <el-table
                            element-loading-text="数据加载中..."
                            element-loading-spinner="el-icon-loading"
                            element-loading-background="rgba(0, 0, 0, 0)"
                            ref="multipleTable"
                            highlight-current-row
                            :border="true"
                            :stripe="false"
                            use-virtual
                            :data="fileTableData"
                            style="width: 770px;max-height:300px;overflow: auto;"
                            class="_css-table-ele css-scroll _css-customstyle _css-file-table"
                            :header-cell-style="{ 'background-color': '#F5F5F5' }"
                            @selection-change="handleSelectionChange"
                            :row-height="rowHeight"
                        >
                            <el-table-column
                                type="index"
                                class-name="center-text"
                                align="center"
                                label="序号"
                                width="50"
                            ></el-table-column>
                            <el-table-column type="selection" v-if="!editOrView" class-name="center-text" align="center" width="50"></el-table-column>
                            <el-table-column property="AttachmentName" label="文档名称">
                                <template slot-scope="scope">
                                    <el-tooltip class="item" v-if="scope.row.AttachmentName.length > 6" popper-class="tooltip-model-hover"  effect="dark" :content="scope.row.AttachmentName" placement="top-start">
                                        <div class="overflow-point">{{ scope.row.AttachmentName | filterName }}</div>
                                    </el-tooltip>
                                    <div v-else>{{ scope.row.AttachmentName | filterName  }}</div>
                                </template>
                            </el-table-column>
                            <el-table-column property="ArchivalDescription" label="文件编号" width="90">
                               <template slot-scope="scope">
                                    <el-tooltip class="item" v-if="scope.row.AttachmentId.length > 8" popper-class="tooltip-model-hover"  effect="dark" :content="scope.row.AttachmentId" placement="top-start">
                                        <div class="overflow-point">{{ scope.row.AttachmentId }}</div>
                                    </el-tooltip>
                                </template>
                            </el-table-column>
                            <el-table-column property="ArchivalDescription" label="文件时间" width="180">
                                <template slot-scope="scope">
                                    <!-- 编辑 -->
                                    <div v-if="editOrView">
                                        {{ scope.row.AttachmentTime }}
                                    </div>
                                    <div v-else>
                                        <el-date-picker
                                            :disabled="editOrView"
                                            v-model="scope.row.AttachmentTime"
                                            type="datetime"
                                            placeholder="文件时间"
                                        >
                                        </el-date-picker>
                                    </div>
                                </template>
                            </el-table-column>
                            <el-table-column property="ArchivalDescription" label="档案类型" width="120">
                                <template slot-scope="scope">
                                    <!-- 编辑 -->
                                    <div v-if="editOrView">
                                        {{ scope.row.ArchivesType | filterArchivesType }}
                                    </div>
                                    <div v-else>
                                        <el-select v-model="scope.row.ArchivesType" placeholder="请选择"  :disabled="editOrView">
                                            <el-option
                                                v-for="item in scope.row.ArchivesTypeOption"
                                                :key="item.value"
                                                :label="item.label"
                                                :value="item.value">
                                            </el-option>
                                        </el-select>
                                    </div>

                                </template>
                            </el-table-column>
                            <el-table-column property="ArchivalDescription" label="页数" width="80">
                                <template slot-scope="scope">
                                    <!-- 编辑 -->
                                    <div  v-if="editOrView">{{ scope.row.PageNumber }}</div>
                                    <div v-else>
                                        <el-input v-model="scope.row.PageNumber" placeholder="页数" :disabled="editOrView"></el-input>
                                    </div>
                                </template>
                            </el-table-column>
                            <el-table-column
                                :resizable="true"
                                class="_css-col-relmodel _css-celllongcolumn"
                                label="操作"
                                prop="bhas_relexam"
                                width="100"
                            >
                                <template slot-scope="scope">
                                    <div class="_css-costitem _css-btnsctn" >
                                        <div class="c007AFF css-mr8" :class="selectTreeData.ArchivesAuth.Privew ? '' : 'btn-dis'" @click.stop="previewFile(scope.row)">预览</div>
                                        <div class="c007AFF" :class="selectTreeData.ArchivesAuth.Download ? '' : 'btn-dis'" @click.stop="downFile(scope.row)">下载</div>
                                    </div>
                                </template>
                            </el-table-column>
                        </el-table>
                    </div>
                    <div class="line-text" v-if="processingType == '0' || processingType == '1' || processingType == '2'"><div class="line">发送节点</div></div>
                    <div class="module-form last-form" v-if="processingType == '0' || processingType == '1' || processingType == '2'">
                        <el-row :gutter="40">
                            <el-col :span="8">
                                <div class="line-title">节点名称</div>
                                <div class="line-content">
                                    <el-select v-model="nodeValue" placeholder="请选择" @change="nodeSelectChange">
                                        <el-option
                                            v-for="item in nodeOption"
                                            :key="item.value"
                                            :label="item.label"
                                            :value="item.value">
                                        </el-option>
                                    </el-select>
                                </div>
                            </el-col>
                            <el-col :span="16">
                                <div class="line-title">接收人</div>
                                <div class="line-content">
                                    <div class="check">
                                        <div class="check-person css-cp" :class="checkUserShow? 'checked' : ''" @click.stop="clickCheckUser">选择接收人</div>
                                        <div class="checkbox-person-select" v-if="checkUserShow">
                                            <span class="title p-8">选择人员 <i class="icon-suggested-close css-cp" @click="checkUserShow=false"></i></span>
                                            <div class="right-input p-8">
                                                <el-input
                                                    v-model="inputValPerson"
                                                    placeholder="请输入搜索人员名称"
                                                    @input="searchNamePerson"
                                                ></el-input>
                                                <i class="icon-interface-search"></i>
                                            </div>
                                            <div class="checkbox-user-list">
                                                <el-checkbox-group v-model="checkUserList" @change="handleCheckedPersonChange">
                                                    <div class="group" v-for="itemPerson in searchPersonList" :key="itemPerson.UserId">
                                                        <el-checkbox :label="itemPerson.UserId" >
                                                            <span class="name-text">{{ itemPerson.RealName }}</span>
                                                            <span>{{ itemPerson.Account }}</span>
                                                        </el-checkbox>
                                                    </div>
                                                </el-checkbox-group>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="list-width">
                                        <div class="checked-person-list"
                                            v-for="itemPerson in checkPersonListCanDel"
                                            :key="itemPerson.UserId"
                                        >{{ itemPerson.RealName }} <i class="icon-suggested-close" @click.stop="removePerson(itemPerson.UserId)"></i></div>
                                    </div>
                                </div>
                            </el-col>
                        </el-row>
                    </div>
                    <div class="line-text" v-if="approvalRecordShow">
                        <div class="line">审批记录</div>
                    </div>
                    <div class="module-form" v-if="approvalRecordShow">
                        <el-table
                            element-loading-text="数据加载中..."
                            element-loading-spinner="el-icon-loading"
                            element-loading-background="rgba(0, 0, 0, 0)"
                            ref="multipleTable"
                            highlight-current-row
                            :border="true"
                            :stripe="false"
                            use-virtual
                            :data="approvalRecordTableData"
                            style="width: 770px;max-height:300px;overflow: auto;"
                            :default-sort="{ prop: 'date', order: 'descending' }"
                            class="_css-table-ele css-scroll _css-customstyle _css-file-table"
                            :header-cell-style="{ 'background-color': '#F5F5F5' }"
                            :row-height="rowHeight"
                        >
                            <el-table-column property="ApprovalTime" label="时间" width="180">
                                <template slot-scope="scope">
                                    <div>
                                        {{ scope.row.ApprovalTime }}
                                    </div>
                                </template>
                            </el-table-column>
                            <el-table-column property="ApprovalNode" label="节点名称" width="100">
                                <template slot-scope="scope">
                                    <div>
                                        {{ scope.row.ApprovalNode | filterApprovalNode }}
                                    </div>
                                </template>
                            </el-table-column>
                            <el-table-column property="ApprovalUserName" label="操作者" width="100">
                                <template slot-scope="scope">
                                    <div>
                                        {{ scope.row.ApprovalUserName }}
                                    </div>
                                </template>
                            </el-table-column>
                            <el-table-column property="ApprovalStatus" label="操作" width="100">
                                <template slot-scope="scope">
                                    <div>
                                        {{ scope.row.ApprovalStatus | filterApprovalStatus }}
                                    </div>
                                </template>
                            </el-table-column>
                            <el-table-column property="ApprovalDescription" label="处理意见">
                               <template slot-scope="scope">
                                    <el-tooltip class="item" v-if="scope.row.ApprovalDescription.length > 20" popper-class="tooltip-model-hover"  effect="dark" :content="scope.row.ApprovalDescription" placement="top-start">
                                        <div class="overflow-point">{{ scope.row.ApprovalDescription }}</div>
                                    </el-tooltip>
                                    <div v-else>{{ scope.row.ApprovalDescription }}</div>
                                </template>
                            </el-table-column>
                        </el-table>
                    </div>
                </div>
			</div>

			<div slot="buttonslot" class="_css-flowAddBtnCtn">
				<zbutton-function
                    v-if="processingType == '0' || processingType == '1' || processingType == '2'"
					:init_text="'发送'"
					:init_fontsize="14"
					:debugmode="true"
					:init_height="'28px'"
					:init_width="'68px'"
					@onclick="func_saveedit"
				>
				</zbutton-function>
                <zbutton-function
                    v-if="processingType == '3'"
					:init_text="'归档'"
					:init_fontsize="14"
					:debugmode="true"
					:init_height="'28px'"
					:init_width="'68px'"
					@onclick="archiveDialog=true"
				>
				</zbutton-function>
				<zbutton-function
                    v-if="processingType == '2' || processingType == '3' || processingType == '4'"
					:init_text="'驳回'"
					:init_color="'rgba(24, 144, 255, 1)'"
                    class="no-pass-btn"
					:init_bgcolor="'#fff'"
					:init_fontsize="14"
					:debugmode="true"
					:init_height="'28px'"
					:init_width="'68px'"
					@onclick="func_NoPass"
				>
				</zbutton-function>
                <zbutton-function
					:init_text="'取消'"
					:init_color="'rgba(24, 144, 255)'"
					:init_bgcolor="'#fff'"
					:init_fontsize="14"
					:debugmode="true"
					:init_height="'28px'"
					:init_width="'68px'"
					@onclick="func_canceledit"
				>
				</zbutton-function>
			</div>
		</zdialog-function>

        <el-dialog
            title="档案退回"
            class="no-pass-el-dialog"
            :visible.sync="nopassDialog"
            width="400"
        >
            <div class="_css-line _css-line-name" style="">
                <div class="_css-title _css-title-flowname">退回原因：</div>
                <div class="_css-fieldvalue _css-fieldvaluename1 _css-selectednode">
                    <el-input v-model="noPassDescription" type="textarea" maxlength="50" show-word-limit placeholder="请输入退回原因"></el-input>
                </div>
            </div>
            <span slot="footer" class="dialog-footer">
                <el-button @click="nopassDialog = false">取 消</el-button>
                <el-button type="primary" @click="nopassSubmit">确 定</el-button>
            </span>
            </el-dialog>
            <el-dialog
                title="归档说明"
                class="no-pass-el-dialog"
                :visible.sync="archiveDialog"
                width="20%"
            >
            <div class="_css-line _css-line-name" >
                <div class="_css-title _css-title-flowname">归档说明：</div>
                <div class="_css-fieldvalue _css-fieldvaluename1 _css-selectednode">
                    <el-input v-model="ArchivingInstructions" type="textarea" maxlength="50" placeholder="请输入归档说明"></el-input>
                </div>
            </div>
            <span slot="footer" class="dialog-footer">
                <el-button @click="archiveDialog = false">取 消</el-button>
                <el-button type="primary" @click="archiveSubmit">确 定</el-button>
            </span>
        </el-dialog>
        <CompsDocumentDialog
            v-if="documentShow"
            @previewFile="previewFile"
            @close="documentShow=false"
            @selectDocList="selectDocList"
        >
        </CompsDocumentDialog>
        <input
            style="display:none"
            id="id_files"
            class="_css-upload-file-inputfile"
            type="file"
            accept=""
            multiple="multiple"
            @change="getFileChange()"
        />
	</div>
</template>
<script>
import CompsDocumentDialog from '@/components/CompsCommon/CompsDocumentDialog'
export default {
	name: "CompsArchives",
    components: {
        CompsDocumentDialog,
	},
	data() {
		return {
            organizeId: '',
            labelPosition: 'top',
            editOrView: false, // 当前状态是编辑还是查看
            dialogId: '', // 当前这个弹窗的id
            // 基本信息相关参数
            infoFormData: {
                ArchivesName: '', // 档案名称
                ArchivesCode: '', // 编码
                IssuedNumber: '', // 发文字号
                CategoryId: '', // 取值selectTreeData.Id
                OrganizationCompany: '', // 编制单位
                ArchivesNumber: null, // 档案份数
                PersonLiable: '', // 责任人
                ArchivalDepartment: '', // 归档部门
                ArchivalDescription: '', // 档案说明
            },
            formRules: {
                ArchivesName: [
                    { required: true, message: '请输入档案名称', trigger: 'blur' },
                    { min: 1, max: 20, message: '长度不能大于20个字符', trigger: 'blur' }
                ],
                ArchivesCode:[
                    { required: true, }
                ],
                CategoryName: [
                    { required: true, }
                ],
                PersonLiable: [
                    { required: true, }
                ],
            },

            fileTableData: [],
            delFileIdArr: [], //删除数据的id集合
            rowHeight: 30,
            nodeTypeIsArchived: false, // 默认是档案审核，=2是归档显示
            nodeOption: [
                {
                    value: '1',
                    label: '档案审核'
                },
                {
                    value: '2',
                    label: '档案归档'
                }
            ],
            nodeValue: '1',
            StoragePeriodValue: 10,
            StoragePeriodOption: [
                {
                    value: 10,
                    label: '10年'
                },
                {
                    value: 20,
                    label: '20年'
                },
                {
                    value: 0,
                    label: '永久'
                }
            ],
            ArchivalAddressEdit: false, // 保管位置能否编辑，false可编辑，true不可编辑
            ArchivalAddressValue: [], // 保管位置的选中的id数组
            ArchivalAddressOptions: [],
            ArchivalAddressDetail: '', // 详细位置
            personList: [], // 所有的人员列表、固定值
            searchPersonList: [], // 记录搜索的人员列表，变化值
            checkPersonListCanDel: [], // 已选择的人员
            checkUserList: [], // 选择的人员
            inputValPerson: '',
            checkUserShow: false,
            nopassDialog: false, // 驳回
            noPassDescription: '', // 驳回原因
            archiveDialog: false, // 归档弹窗
            ArchivingInstructions: '', // 归档说明
            approvalRecordTableData: [], // 审批记录表格
            approvalRecordShow: false, // 审批记录是否显示  true显示  false不显示
            documentShow: false, // 在线文档
        };
	},
	props: {
		// 树结构选中的数据
		selectTreeData: {
			type: Object,
			required: true,
		},
		// 当前处理的类型（新增0、被驳回待提交1、审批2、驳回3、归档4、退回查看5、归档查看6、详情查看7）
		processingType: {
			type: String,
			required: true,
		},
        archivesCode: {
            type: String,
		    default: '',
        },
        // 当前选择的id
        detailId: {
            type: String,
		    required: true,
        }
	},
	watch: {},
	created() {
		this.organizeId = this.$staticmethod._Get("organizeId");
	},
	mounted() {
        this.getUserList()
        this.loadCompanyDicDatas_ArchivesRoom()

        this.infoFormData.PersonLiable = this.$staticmethod.Get("Account");  // 档案编码赋值
        console.log(this.processingType, '=====processingType ',typeof(this.processingType))
        if(this.processingType == '0'){
            this.editOrView = false; // 可编辑
            this.ArchivalAddressEdit = false;   // 保管地址可编辑
            this.infoFormData.ArchivesCode = this.archivesCode;  // 档案编码赋值
            this.approvalRecordShow = false; // 审批记录不显示
        }else{
            if(this.processingType == '1'){
                this.editOrView = false;
                this.nodeTypeIsArchived = false;
            }else{
                this.editOrView = true;
            }
            this.approvalRecordShow = true; // 审批记录显示   审批记录只有在新增的时候不显示

            if(this.processingType == '1' || this.processingType == '2' || this.processingType == '3' || this.processingType == '4'){
                this.ArchivalAddressEdit = false;  // 保管地址可编辑
            }else{
                this.ArchivalAddressEdit = true;   // 保管地址不可编辑
            }

            this.GetArchivesInfo();
        }
	},
	filters: {
        filterName(name){
            let str = name.substring(0,name.lastIndexOf("."))
            return str
        },
        filterApprovalNode(number){
            let text = '-'
            // 档案提交 0    档案审批  1   档案归档  2
			switch(number) {
				case 0:
					text = '档案提交';
					break;
				case 1:
					text = '档案审批';
					break;
				case 2:
					text = '档案归档';
					break;
			}
			return text
        },
        filterApprovalStatus(number){
            let text = '-'
            // 1 通过 2 驳回
			switch(number) {
				case 1:
					text = '通过';
					break;
				case 2:
					text = '驳回';
					break;
			}
			return text
        },
        filterArchivesType(number){
            let text = '-'
            // 0  纸质原件 1 电子版
			switch(number) {
				case 0:
					text = '纸质原件';
					break;
				case 1:
					text = '电子版';
					break;
			}
			return text
        },
    },
	computed: {},
	methods: {
		_stopPropagation(ev) {
			ev && ev.stopPropagation && ev.stopPropagation();
		},
        // 获取档案位置
        async loadCompanyDicDatas_ArchivesRoom(){
			let data = {
				OrganizeId: this.$staticmethod._Get("_OrganizeId"),
				ParentId: '',
			}
			const res = await this.$api.GetArchivesRoomTree(data);
			if(res.Ret == 1){
				let data = res.Data;
                let result = data.map(item => this.extractIdsAndNames(item));
                this.ArchivalAddressOptions = result;
			}
		},
        // 处理所需参数
        extractIdsAndNames(obj) {
            let result = {
                value: obj.Id,
                label: obj.RoomName
            };
            if (obj.Children.length > 0) {
                result.children = obj.Children.map(child => this.extractIdsAndNames(child));
            }
            return result;
        },
        // 获取人员
        async getUserList(){
            let data = {
                PageNum: 1,
                PageSize: 9999,
                OrganizeId: this.$staticmethod._Get("_OrganizeId"),
                KeyWord: this.inputValPerson
            }
            let res = await this.$api.GetOrganizeUserPaged(data)
            this.personList = this.searchPersonList = res.Data.list
        },
        async GetArchivesInfo(){
            let params = {
				id: this.detailId,
			};
            let res = await this.$api.GetArchivesInfo(params)
            if(res.Ret == 1){
                let _data = res.Data;
                // 如果是查看、没有的值改为-   如果是待提交
                if(this.processingType == '1'){
                    this.infoFormData = {
                        ArchivesName: _data.ArchivesName, // 档案名称
                        ArchivesCode: _data.ArchivesCode, // 编码
                        IssuedNumber: _data.IssuedNumber, // 发文字号
                        CategoryId: _data.CategoryId, // 取值selectTreeData.Id
                        OrganizationCompany: _data.OrganizationCompany, // 编制单位
                        ArchivesNumber: _data.ArchivesNumber, // 档案份数
                        PersonLiable: _data.PersonLiable, // 责任人
                        ArchivalDepartment: _data.ArchivalDepartment, // 归档部门
                        ArchivalDescription: _data.ArchivalDescription, // 档案说明
                    }
                }else{
                    this.infoFormData = {
                        ArchivesName: _data.ArchivesName, // 档案名称
                        ArchivesCode: _data.ArchivesCode, // 编码
                        IssuedNumber: _data.IssuedNumber || '-', // 发文字号
                        CategoryId: _data.CategoryId, // 取值selectTreeData.Id
                        OrganizationCompany: _data.OrganizationCompany || '-', // 编制单位
                        ArchivesNumber: _data.ArchivesNumber || '-', // 档案份数
                        PersonLiable: _data.PersonLiable, // 责任人
                        ArchivalDepartment: _data.ArchivalDepartment || '-', // 归档部门
                        ArchivalDescription: _data.ArchivalDescription || '-', // 档案说明
                    }
                }
                let _fileData = _data.Attachment;
                _fileData.forEach(item=>{
                    item.ArchivesTypeOption =  [
                        {
                            value: 0,
                            label: '纸质原件'
                        },
                        {
                            value: 1,
                            label: '电子版'
                        }
                    ]
                })
                this.fileTableData = _fileData;  // 文件表格数据
                this.approvalRecordTableData = _data.ArchivesApprovalLog; // 审批节点表格数据

                if(_data.StoragePeriod !== -1 || this.processingType == '2' || this.processingType == '3' || this.processingType == '4'){
                    this.ArchivalAddressDetail = _data.ArchivalAddress;
                    this.nodeTypeIsArchived = true
                }
                if (_data.ArchivesRooms || this.processingType == '2' || this.processingType == '3' || this.processingType == '4'){
                  this.ArchivalAddressValue = _data.ArchivesRooms
                  this.nodeTypeIsArchived = true
                }
                if(_data.ArchivesApprovalLog){
                    this.approvalRecordShow = true
                }
                this.StoragePeriodValue = _data.StoragePeriod
                // 获取历史接收人
                if(_data.ArchivesApprovalLog[_data.ArchivesApprovalLog.length - 1].ApprovalStatus == '2'){
                    this.nodeValue = _data.ArchivesApprovalLog[_data.ArchivesApprovalLog.length - 1].ApprovalNode + ''; // 当前节点
                    this.GetHistorySelector(_data.Id,this.nodeValue);
                    // console.log(this.nodeValue,'=this.nodeValue')
                }
            }
        },
        async GetHistorySelector(id,node){
            let data={
                archivesId: id,
                archivesNode: node
            }
            let res = await this.$api.GetHistorySelector(data)
            if(res.Ret == 1){
                // 给接收人赋值
                let data = res.Data;
                data.forEach(item=>{
                    item.UserId = item.Id
                })
                let hList = data.map(item=>{
                    return item.Id
                })
                this.checkPersonListCanDel =  data.filter(obj => obj.UserId !== id);
                this.checkUserList = hList; // 当前选中人id的数组
            }
        },
        close(){
            this.$emit('close')
        },
        func_saveedit(){
            // =0 是新增 =1 待提交  =2 审批
            if(this.processingType == '0'){
                this.infoFormData.CategoryId = this.selectTreeData.Id
                this.postArchivesCreate()
            }else if(this.processingType == '1'){
                this.postArchivesAudit(true, '提交')
            }else if(this.processingType == '2'){
                this.postArchivesAudit(true, '审批')
            }
        },
        // 新建接口
        async postArchivesCreate(){
            // console.log(this.StoragePeriodValue,'====StoragePeriodValue')
            this.fileTableData.forEach(item=>{
                delete item.ArchivesTypeOption;
                delete item.AttachmentName;
            })
            let roomid = this.ArchivalAddressValue && this.ArchivalAddressValue.length > 0 ? roomid = this.ArchivalAddressValue[this.ArchivalAddressValue.length -1] : roomid = null

            let data = {
                ...this.infoFormData,  // 基本信息
                OrganizeId: this.organizeId,
                Attachments: this.fileTableData,  // 文件
                NextStepInfo: {
                    NextNodeType: this.nodeValue,  // 节点名称
                    ApprovalUserIds: this.checkUserList,  // 接收人
                    ArchivesApprovalStepInfo:{
                        ArchivalRoomId: roomid, // 后端只要当前选中的最后一个id，档案保存位置
                        ArchivalAddress: this.ArchivalAddressDetail,   // 保管位置明细
                        StoragePeriod: this.StoragePeriodValue,    // 保管期限
                    }
                },
            }
            let res = await this.$api.postArchivesCreate(data)
            if(res.Ret == 1){
                this.$message.success(res.Msg)
                this.close()
            }else if(res.Ret == 409){
                this.GetCurrentArchivesCode()
            }
        },
        // 审批接口
        async postArchivesAudit(isPass,type){
            // isPass  通过还是驳回
            let des = '';
            if(type == '审批'){
                des = '同意'
            }else if(type == '驳回'){
                des = this.noPassDescription
            }else if(type == '归档'){
                des = this.ArchivingInstructions
            }else if(type == '提交'){
                des = '提交'
            }
            let roomid = this.ArchivalAddressValue && this.ArchivalAddressValue.length > 0 ? roomid = this.ArchivalAddressValue[this.ArchivalAddressValue.length -1] : roomid = null
            if(this.infoFormData.ArchivesNumber == '-'){
                this.infoFormData.ArchivesNumber = null
            }
            let data = {
                Id: this.detailId,
                IsPass: isPass,
                Description: des,  // 审批意见（驳回原因，审批的时候没有意见）
                ArchivesApprovalStepInfo:{
                    ArchivalRoomId: roomid, // 后端只要当前选中的最后一个id，档案保存位置
                    ArchivalAddress: this.ArchivalAddressDetail,   // 保管位置明细
                    StoragePeriod: this.StoragePeriodValue,    // 保管期限
                },
                ArchivingInstructions: des, // 归档说明
                ApprovalUserIds: this.checkUserList,  // 接收人
                ArchivesInfo: {
                    NextNodeType: this.nodeValue,  // 节点名称
                    ...this.infoFormData,
                    Attachments: this.fileTableData,  // 文件
                },
            }
            let res = await this.$api.postArchivesAudit(data)
            if(res.Ret == 1){
                this.$message.success(res.Msg)
                this.close()
            }
        },
        // 档案驳回点击确定
        nopassSubmit(){
            this.postArchivesAudit(false,'驳回')
        },
        // archiveSubmit 归档说明填写确定
        archiveSubmit(){
            this.postArchivesAudit(true, '归档')
        },
        // 请求编码
        async GetCurrentArchivesCode(){
            let params = {
                    organizeId: this.organizeId,
                    categoryId: this.selectTreeData.Id
                };
            let res = await this.$api.GetCurrentArchivesCode(params);
            if(res.Ret == 1){
                this.infoFormData.ArchivesCode = res.Data;
                this.$message.warning('编码重复，编码已刷新，请重新提交')
            }
        },
        func_canceledit(){
            this.close()
        },
        // 驳回
        func_NoPass(){
            this.nopassDialog = true;
        },
        // 删除文件
        delFile(){
          if (this.delFileIdArr.length === 0 ) return
          this
				.$confirm("是否确定删除，删除后相关数据将被清空", "操作确认", {
					confirmButtonText: "确认",
					cancelButtonText: "取消",
					type: "warning"
				})
			.then(x => {
        if (this.detailId){
          this.delFun();
        }else {
          this.fileTableData = this.fileTableData.filter(item => !this.delFileIdArr.includes(item.AttachmentId));
        }
			})
			.catch(x => {});
        },
        async delFun(){
            let res = await this.$api.postDeleteBulk(this.delFileIdArr);
            if(res.Ret == 1){
                this.$message.success(res.Msg)
                // 删除文件、当前文件表格删除相应数据
                this.fileTableData = this.fileTableData.filter(item => !this.delFileIdArr.includes(item.AttachmentId));
            }else{
                this.$message.error(res.Msg);
            }
        },
        // 新增文件
        addFile(){
            var dominputfile = document.getElementById("id_files");
            dominputfile.value = "";
            dominputfile.click();
        },
        getFileChange(){
            let filedom = document.getElementById("id_files");
			let files = filedom.files;
            console.log(files,'====files')
            // supportedformat支持上传的文件类型
            let supportedformat = ['doc', 'docx', 'dwg', 'jpeg', 'jpg', 'pdf', 'png', 'ppt', 'pptx', 'txt', 'xls', 'xlsx'];
            let uploadSuffixName = [] // 上传的文件类型
            for (var i = 0; i < files.length; i++) {
                let suffixName = files[i].name.substring(files[i].name.lastIndexOf('.') + 1);
                uploadSuffixName.push(suffixName)
            }

            let allSupported = uploadSuffixName.every(item => supportedformat.includes(item));
            if (allSupported) {
                for (var i = 0; i < files.length; i++) {
                    this.handleUploadFlie(files[i]);
                }
            } else {
                this.$message.warning(`可上传类型：${supportedformat}`)
            }
        },
        // 文件上传
        handleUploadFlie(file) {
			let _this = this;
			let File = file;
			let userid = _this.$staticmethod.Get("UserId");
			// 提交给服务器
			let fd = new FormData();
			let FileKey = this.$md5(new Date().getTime() + "" + File.name);
			fd.append("ProjectID", this.organizeId);
			fd.append("AttachmentType", "4");
			fd.append("FileName", File.name);
			fd.append("FileKey", FileKey);
			fd.append("FileSize", File.size);
			fd.append("ChunkNumber", 1);
			fd.append("Index", 1);
			fd.append("UserId", userid);
			fd.append("File", File);
			let config = {
				headers: {
					"Content-Type": "multipart/form-data",
				},
			};
			_this.$axios
				.post(
					`${
						window.bim_config.webserverurl
					}/api/v1/attach/upload?Token=${this.$staticmethod.Get("Token")}`,
					fd,
					config
				)
				.then((x) => {
					if (x.data.Ret == 1 && x.data) {
						let _data = x.data.Data.Data;
                        let obj = {
                            AttachmentId: _data.AttachmentId,
                            AttachmentName: _data.AttachmentName,
                            AttachmentTime: this.$staticmethod.dtToString2(new Date()),
                            ArchivesType: 0,
                            IsFile: false,
                            ArchivesTypeOption: [
                                {
                                    value: 0,
                                    label: '纸质原件'
                                },
                                {
                                    value: 1,
                                    label: '电子版'
                                }
                            ]
                        }
                        _this.fileTableData.push(obj)
					}

				})
				.catch((x) => {
					_this.$message.error(
						`服务器处理发生错误，可能是文件类型不符合白名单规则，请检查文件`
					);
				});
		},
        selectDocList(arr){
            this.documentShow = false
            this.fileTableData = arr.concat(this.fileTableData)
        },
        // 预览文件
        previewFile(row){
            this.$emit('previewFile',row)
        },
        // 下载文件
        downFile(row){
          if (row.Type === 0 ){
            window.location.href = `${window.bim_config.webserverurl}/api/v1/file/download?fileId=${row.AttachmentId}&Token=${this.$staticmethod.Get('Token')}`;

          }else {
            window.location.href = `${window.bim_config.webserverurl}/api/v1/attach/download?attachmentId=${row.AttachmentId}&Token=${this.$staticmethod.Get('Token')}`;

          }        },
        handleSelectionChange(val){
            this.delFileIdArr = val.map(item => item.AttachmentId);
        },
        // 人员选择
        handleCheckedPersonChange(val){
            this.checkUserList = val
            this.checkPersonListCanDel = this.personList.filter(obj => val.includes(obj.UserId));

        },
        removePerson(id){
            this.checkPersonListCanDel =  this.checkPersonListCanDel.filter(obj => obj.UserId !== id);
            this.checkUserList = this.checkUserList.filter(item => item !== id);
        },
        searchNamePerson(val){
            this.inputValPerson = val;
            if(val.length > 0){
                this.searchPersonList = this.personList.filter(obj => obj.RealName.includes(val));
            }else{
                this.searchPersonList = this.personList
            }
			// this.getUserList();
        },
        clickCheckUser(){
            if(this.processingType == '0' || this.processingType == '1' || this.processingType == '2'){
                this.checkUserShow = !this.checkUserShow
            }
        },
        nodeSelectChange(val){
            // val == 2  归档   新增和待提交时候不显示
            // val == 2 && this.processingType != '0' && this.processingType != '1' ? this.nodeTypeIsArchived = true : this.nodeTypeIsArchived = false
        },
        // 选择档案位置
        archivalAddressChange(changevalue){
            console.log(this.ArchivalAddressValue,this.ArchivalAddressValue[this.ArchivalAddressValue.length -1])
        },
        // 限制输入整数
        handleInputInteger(){
            this.infoFormData.ArchivesNumber=  this.infoFormData.ArchivesNumber.replace(/[^\d]/g, '');
        },
        // 点击添加文件
        selectDoc(){
            this.documentShow = true
        },

	},
	beforeDestroy() {},
};
</script>
<style lang="scss" scoped>
.archives-module{
    max-height: 600px;
    overflow: auto;
    .line{
        text-align: left;
        font-weight: 500;
        font-size: 16px;
        color: #007AFF;
        margin-bottom: 4px;
    }
    .line::before{
        content: '';
        display: inline-block;
        width: 2px;
        height: 12px;
        background: #007AFF;
        border-radius: 1px;
        margin-right: 4px;
    }
    .line-text{
        margin: 10px 24px 0;
        display: flex;
        justify-content: space-between;
    }
    .module-form{
        margin-left: 24px;
        margin-bottom: 15px;
    }
    .file-btn{
        width: 44px;
        height: 26px;
        background: #FFFFFF;
        border-radius: 4px;
        color: #007AFF;
        border: 1px solid #007AFF;
        padding: 5px 10px;
        cursor: pointer;

    }
    .file-btn.add{
        background: #007AFF;
        color: #fff;
        margin-left: 8px;
    }
    .line-title{
        text-align: left;
        font-weight: 400;
        font-size: 14px;
        color: #222222;
        margin: 0px 0 8px 0;
    }
    .line-content /deep/ .el-select{
        width: 100%;
    }
    .line-content{
        display: flex;
    }
    .check{
        position: relative;

    }
    .check-person{
        border: 1px solid transparent;
        text-align: center;
        border-radius: 2px;
        // color: #222222;
        padding: 8px 0;
        margin-right: 20px;
        width: 80px;
        font-weight: 400;
        font-size: 12px;
    }
    .check-person.checked ,.check-person:hover{
        border-radius: 2px;
        border: 1px solid #007AFF;
        color: #007AFF;

    }
    .list-width{
        max-width: 470px;
        overflow-x: auto;
        display: flex;
    }
    .checked-person-list{
        background: #F2F8FF;
        padding: 0 4px;
        border-radius: 2px;
        line-height: 24px;
        display: flex;
        align-items: center;
        margin-right: 6px;
        white-space: pre;
        i{
            padding-left: 4px;
            cursor: pointer;
        }
    }
    .checkbox-person-select{
        position: absolute;
        top: -250px;
        left: 0;
        width: 250px;
        height: 238px;
        // overflow: auto;
        background: #FFFFFF;
        box-shadow: 0px 0px 6px 0px #E5E6EB;
        border-radius: 2px;
        text-align: left;
        z-index: 1004;
        .title{
            font-family: PingFangSC, PingFang SC;
            font-weight: 500;
            font-size: 12px;
            color: #081A33;
            display: flex;
            justify-content: space-between;
            padding-top: 5px;
            // line-height: 32px;
        }
        .p-8{
            padding-left: 8px;
        }
        .right-input{
            width: 90%;
            position: relative;
            margin: 8px 0;
            i{
                position: absolute;
                top: 8px;
                right: 10px;
                color: #999999;
            }
        }

        .checkbox-user-list{
            height: 170px;
            overflow: auto;
            .group{
                line-height: 28px;
                padding-left: 8px;
            }
            .group:hover{
                background: rgba(0,122,255,0.05);
            }
            /deep/ .el-checkbox__label{
                font-family: PingFangSC, PingFang SC;
                font-weight: 400;
                font-size: 12px;
                color: #4E5969;
                display: inline-block;
                margin-left: 8px;
                .name-text{
                    display: inline-block;
                    width: 60px;
                    padding-right: 26px;
                }
            }
        }
    }

}
 /deep/ ._css-file-table .el-table thead th .cell , /deep/ ._css-file-table .center-text .cell{
	justify-content: center !important;
	padding: 0;
}
 /deep/ ._css-file-table .el-table__empty-block{
    min-height: 42px;
}
 /deep/ ._css-file-table .el-table__empty-text{
    line-height: 42px;
}
/deep/ .el-input__inner{
    border-width: 1px;
    border-radius: 2px;
    line-height: 32px;
}
/deep/ .el-form--label-top .el-form-item__label{
    float: left;
    padding: 0;
    color: #222222;
    line-height: 28px;
    font-family: PingFangSC, PingFang SC;
}
/deep/ .el-form-item{
    margin-bottom: 6px;
}
/deep/ .el-form ,/deep/ .last-form .el-row{
    width: calc(100% - 20px);
}
._css-btnsctn{
    display: flex;
    .btn-dis{
        color: #666;
        pointer-events: none;
    }
}
/deep/ .el-date-editor.el-input, /deep/ .el-date-editor.el-input__inner{
    width: auto;
}

/deep/ .tooltip-model-hover{
    max-height: 50px;
    line-height: 50px;
    height: 50px;
    min-height: 50px;
}
/deep/ .el-table td{
    // border-color: #E5E6EB ;
    // border-left: 1px solid #E5E6EB !important;
    border-top: 1px solid #E5E6EB !important;
    // border-bottom: 1px solid #E5E6EB !important;
    border-right: 1px solid #E5E6EB !important;
}
/deep/ ._css-table-ele.el-table--border{
    border-left: 1px solid #E5E6EB !important;
    // border-top: 1px solid #E5E6EB !important;
    border-bottom: 1px solid #E5E6EB !important;
    border-right: 1px solid #E5E6EB !important;
}
// ._css-customstyle /deep/ .el-table__fixed-right-patch, ._css-customstyle /deep/ th.is-leaf{
//     border-right-color: #E5E6EB;
// }
._css-file-table /deep/ .el-input--prefix .el-input__inner{
    padding-left: 10px;
}
._css-file-table /deep/ .el-input--suffix .el-input__inner{
    padding-right: 10px;
    font-size: 12px;
}
._css-file-table /deep/ .el-input__prefix {
    display: none;
}
._css-file-table /deep/ .el-table .cell{
    line-height: 30px;
}
._css-file-table /deep/ .el-input__inner{
    line-height: 24px;
}

/deep/ .el-cascader,/deep/ ._css-file-table .el-table .cell{
    line-height: 34px;
}
.address{
    margin-left: 10px;
    text-align: left;
}
.width98{
    width: calc(100% - 50px);
}
/deep/ ._css-fieldvaluename1{
    flex: 1;
    border: 1px solid rgba(0, 0, 0, .1);
    border-radius: 4px;
    .el-textarea__inner{
        height: 150px;
    }
}
/deep/ .no-pass-dialog._css-addingnameinput-ctn ._css-line{
    align-items: flex-start;
}
._css-title-flowname{
    margin-top: 10px;
}
.no-pass-btn{
    border: 1px solid #1890FF;
    margin-right: 16px;
}
.no-pass-el-dialog /deep/ {
    .el-dialog{
        width: 400px;
    }
    .el-dialog__body ._css-line{
        display: flex;
    }
    .el-dialog{
        text-align: left;
    }
    .el-button{
        width: 68px;
        height: 32px;
        border-radius: 2px;
    }
    .el-dialog__header{
        border-bottom: 1px solid #E8E8E8;
    }
}
/deep/ .el-cascader.is-disabled .el-cascader__label{
    color: #081A33;
}
/deep/ .el-input.is-disabled .el-input__inner,/deep/ .el-textarea.is-disabled .el-textarea__inner{
    background-color: rgba(0, 122, 255, 0.05);
    border-color: #D8D8D8;
    color: #081A33;
    cursor: not-allowed;
}
.line-no-padding {
    /deep/ .el-col{
        padding-left: 0 !important;
        padding-right: 0 !important;
    }
    /deep/ .el-cascader{
        width: 100%;
    }
}
</style>
