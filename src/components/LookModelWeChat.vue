<template>
  <div class="_css-mobile-materials-all">
    <ul v-if="modelList.length>0">
      <li class="model-list" v-for="(item,index) in modelList" :key='index' @click.stop="handelClickList(item)">
        <div class="list-img">
          <img :src="getQRcode(item.ImageUrl)" alt="" width="100%" height="100%">
        </div>
        <div class="list-content">
          <p>{{item.FullName}}</p>
          <span>模型数量：{{getPassedNum(item.BIMComposerID) || 0}}</span>
        </div>
      </li>
    </ul>
    <div v-if="modelListNo" class="no-list">暂无数据</div>
  </div>
</template>
<script>
import '@/assets/css/mobStyle.css'; 
export default {
  name:"LookModelWeChat",
  data() {
    return {
      modelList: [],
      modelListNo: false,
      parentIdParam: '',
      // parentIdParam: '997223d1-fe87-48df-9eea-cf01c8a57dbf',
      exceptGuidParam: '',
      passedNum_list: [],
    };
  },
  created(){
     
  },
  mounted() {
    
    if(this.$route.params.parentId !=='' || this.$route.params.exceptGuid !==''){
      this.parentIdParam  = this.$route.params.parentId;
      this.exceptGuidParam = this.$route.params.exceptGuid
      sessionStorage.setItem('parentIdParam',this.parentIdParam);
      sessionStorage.setItem('exceptGuidParam',this.exceptGuidParam);
    }else{
      if( sessionStorage.getItem('parentIdParam')){
        this.parentIdParam  =  sessionStorage.getItem('parentIdParam')
        this.exceptGuidParam  =  sessionStorage.getItem('exceptGuidParam')
      }
    }
    this.getModelList();
  },
  filters:{
    
  },
  computed:{
    
  },
  methods: {
    handelClickList(item){
      let _item = JSON.stringify(item)
      sessionStorage.setItem('firstClickItem',_item);
      window.location.href = `${window.bim_config.hasRouterFile}/#/detailModelList`
    },
    getModelList() {
      let _this = this;
      
      _this.$axios
        .get(`${window.bim_config.webserverurl}/api/RemoteScreen/RSProject/GetProjectList?parentId=${_this.parentIdParam}&exceptGuid=${_this.exceptGuidParam}&Token=${this.$staticmethod.Get('Token')}`)
        .then( x => {
          if(x.status == 200 && x.data.Data.List.length > 0) {
            let _modelarr
            let index = 0;
            _modelarr = x.data.Data.List;
            let nameEncoded = encodeURIComponent('施工模型');

            for(let i=0;i<_modelarr.length; i++){
              index = i;
              _this.$axios
              .get(`${
                _this.$staticmethod.getBIMServer()
              }/api/Prj/GetSpecificNamePhaseModelCount?ProjectID=${_modelarr[i].BIMComposerID}&NameEncoded=${nameEncoded}&Token=${this.$staticmethod.Get('Token')}`)
              .then( x => {
                _modelarr[i].passedNum = x.data;
                _this.passedNum_list.push({
                  passedNum: x.data,
                  id: _modelarr[i].BIMComposerID
                })
              }).catch( error => {

              })
            }
            
            _this.modelList = _modelarr;
            if(this.modelList.length<=0){
              this.modelListNo = true;
            }else{
              this.modelListNo = false;
            }
         
            
          }

        }).catch( error => {

        })
    },
    getQRcode(url){
      let imgSrc =  `${window.bim_config.webserverurl}`+ url
      return imgSrc;
    },
    getPassedNum(id){
      let item = this.passedNum_list.filter(a => a.id == id)[0]
      if(item){
        return item.passedNum;
      }
      return 0
    },
  },
  
};
</script>
<style scoped>
  ._css-mobile-materials-all{
    background: #F7F7F7;
    font-size: 0.85rem;
    height: 100%;
    overflow-y: scroll;
  }
  .model-list{
    display: flex;
    height: 4.7rem;
    background: #FFFFFF;
    box-shadow: 0px 4px 4px 0px rgba(41, 44, 54, 0.01);
    border-radius: 6px;
    margin: 15px 15px 0;
    padding: 10px;
  }
  .list-img{
    width: 4.66rem;
    height: 4.66rem;
    border-radius: 6px;
  }
  .list-content {
    margin-left: 10px;
    display: flex;
    flex:1;
    flex-direction: column;
    overflow-x: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  .list-content p{
    font-size: 1rem;
    font-weight: 500;
    color: #283A4F;
    line-height: 20px;
    letter-spacing: 1px;
    overflow-x: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  .list-content span{
    font-size: 0.85rem;
    color: #A6AEB6;
    margin-top:0.7rem;
    overflow-x: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  .no-list{
    margin: 1.5rem auto;
    line-height: 2rem;
    font-size: 0.85rem;
    text-align: center;
  }
</style>