<template>
    <div v-loading="isLoadingUsers" class="Contariner" style="height:350px;">
        <div class="Search">
            <input type="text" value="" placeholder="请输入要搜索的账号名或真实姓名" v-model="searchUserVal" @keyup="SearchUsers(searchUserVal,searchUserVal)" /><i class="icon-interface-search" @click="SearchUsers(searchUserVal,searchUserVal)"></i>
        </div>
        <div class="l">
            <div class="organzieUnit" v-for="item in OrganizeTreeList" :key="item.OrganizeId" >
                <span class="unit" :class="currentOpenOrganizeId==item.OrganizeId?'select':''" @click="clickTreeTip(item.OrganizeId);"><i class="" :class="(item.OrganizeId==currentOpenOrganizeId&&treeClickSign)?'icon-arrow-down':'icon-arrow-right'"></i>{{item.OrganizeName}}</span>
                <!-- <div class="list" v-show="item.OrganizeId==currentOpenOrganizeId&&treeClickSign">
                    <span class="unit" :class="currentOpenDepartmentId==ditem.DepartmentId&&dtreeClickSign?'select':''" v-for="ditem in JSON.parse(item.DepartmentList)" :key="ditem.DepartmentId" @click="clickDepartment(ditem.DepartmentId)" >{{ditem.FullName}}</span>
                </div> -->
            </div>
        </div>
        <div class="r">
            <ul>
                <li v-for="item in currentUserList" :key="item.UserId"><label :title="item.RealName"><input :id="'user_'+item.UserId" type="radio" name="1" @click="selUser('user_'+item.UserId,item.UserId,item.RealName)" />{{item.RealName}}</label></li>
            </ul>
        </div>
        <div class="btns">
            <button class="blue2" @click="$emit('selUser',choiceUser);$emit('closeDialog');">确定</button>
        </div>
    </div>
</template>
<style scoped>
ul{padding: 0px;margin: 0px;}ul li{list-style-type: none;}i::before{float: left;text-indent: -1px;text-align:center;}
.Contariner{border:1px solid #ccc;border-radius: 4px;background-color: #fff;}
.Contariner .l{width:60%;height:calc(100% - 42px - 35px);float:left;overflow-x:hidden;overflow-y:scroll;}.Contariner .r{width:40%;height:calc(100% - 42px - 35px);float:left;overflow-x:hidden;overflow-y:scroll;}
.Contariner .l .list{width:calc(100% - 15px);margin-left:15px;overflow: hidden;}
.Contariner .l .unit{display:block;line-height:30px;height:30px;overflow:hidden;text-overflow:ellipsis;white-space:nowrap; width:100%;text-indent:22px;position: relative;cursor:default;text-align: left;}
.Contariner .l .unit:hover{background-color: #f6f6f0;}
.Contariner .l .unit.select{background-color: #e9e9e9;}
.Contariner .l .unit i{position: absolute;left:5px;top:5px;cursor: pointer;}
.Contariner .l .select{background-color: #e9e9e9;}

.Contariner .r ul{width:100%;height:auto;}
.Contariner .r ul li{width:100%;height:30px;line-height: 30px;text-align:left;text-indent: 18px;overflow:hidden;text-overflow: ellipsis;white-space: nowrap;}
.Contariner .r ul li input{vertical-align: middle;margin-top:-1px;}

.Contariner .Search{width: calc(100% - 10px);height:35px;line-height:35px;position: relative;}.Contariner .Search input{width:calc(100% - 20px);height:calc(100% - 2px);border:none;display: inline-block;outline:none;text-indent:20px;}
.Contariner .Search i{width:18px;height: 18px;display: inline-block;cursor: default;position: absolute;top:calc(50% - 1px);right:2px;transform: translate(-50%,-50%);}.Contariner .Search i:hover{border:1px dashed #333;right:0px;}

.btns{line-height:42px;text-align: right;margin-left:5px;height:42px;width:calc(100% - 10px);}
.btns button{width:56px;height:32px;line-height:32px;outline:none;border:none;border-radius:2px;text-align: center;overflow: hidden;cursor: pointer;}
.btns button.blue1{background-color:#80C2FF;color: #fff; }
.btns button.blue2{background-color:#1890FF;color: #fff; }
.btns button.transparent{background-color: transparent;color: #8a8a8a;}
::-webkit-scrollbar {/*滚动条整体样式*/
    width: 8px;     /*高宽分别对应横竖滚动条的尺寸*/
    height: 8px;
}
::-webkit-scrollbar-thumb {/*滚动条里面小方块*/
    border-radius: 10px;
    box-shadow: inset 0 0 5px #A5ADB7;
    background: #535353;
}
::-webkit-scrollbar-track {/*滚动条里面轨道*/
    box-shadow: inset 0 0 5px rgba(0,0,0,0.2);
    border-radius: 10px;
    background: #EDEDED;
}
.TreeNode_in{ animation: 0.4s showTreeNode_in forwards;}
.TreeNode_out{ animation: 0.4s showTreeNode_out forwards;}
@keyframes showTreeNode_in
{
    from{height: 0px;opacity: 0;}
    to{height:auto;opacity: 1;}
}
@keyframes showTreeNode_out
{
    from{height: auto;opacity: 1;}
    to{height:0px;opacity: 0;}
}
</style>
<script>
export default {
    name:"SelectUser",
    components:{
        
    },
    data(){
        return {
            currentOpenOrganizeId:"",
            currentOpenDepartmentId:"",
            OrganizeTreeList:[],
            departmentUserList:{},
            treeClickSign:false,//当前树节点展开切换
            dtreeClickSign:false,//部门点击选中切换标志

            currentUserList:[],
            isLoadingUsers:false,
            searchUserVal:"",//搜索用户的值
            choiceUser:{},//选择的管理员
        };
    },
    methods:{
        clickTreeTip(itemOrganizeId){
            if(this.currentOpenOrganizeId==itemOrganizeId)
                this.treeClickSign=!this.treeClickSign;
            else{
                this.currentOpenOrganizeId=itemOrganizeId;
                this.treeClickSign=true;//切换新标签，直接展开
                this.getUserList(itemOrganizeId,"");
                this.searchUserVal="";
            }
        },
        clickDepartment(itemDepartmentId){
            if(this.currentOpenDepartmentId==itemDepartmentId)
                this.dtreeClickSign=!this.dtreeClickSign
            else{
                this.currentOpenDepartmentId=itemDepartmentId;
                this.dtreeClickSign=true;
                this.getUserList("",itemDepartmentId);
                this.searchUserVal="";
            }
        },
        getUserList(OrganizeId,DepartmentId){
            var Token = this.$staticmethod.Get("Token");
            this.isLoadingUsers=true;
            let _this=this;
            this.$axios.get(this.$urlPool.base_MainSystemApi + this.$urlPool.GetUsersByOrganizeIdOrDepartmentId+"?Token="+Token+"&OrganizeId="+OrganizeId+"&DepartmentId="+DepartmentId).then(res=>{
                this.currentUserList=JSON.parse(res.data.Data);
                setTimeout(function(){
                    _this.isLoadingUsers=false;
                }, 150); 
                console.log(this.currentUserList,'选中的当前的用户列表');
            });
        },
        SearchUsers(Account,RealName){
            if(Account==""&&RealName=="")
                return;
            var Token = this.$staticmethod.Get("Token");
            this.isLoadingUsers=true;
            let _this=this;
            this.$axios.get(this.$urlPool.base_MainSystemApi + this.$urlPool.GetUserByAccountOrRealName+"?Token="+Token+"&Account="+Account+"&RealName="+RealName).then(res=>{
                this.currentUserList=JSON.parse(res.data.Data);
                this.currentOpenOrganizeId="";
                this.currentOpenDepartmentId="";
                setTimeout(function(){
                    _this.isLoadingUsers=false;
                }, 150); 
                console.log(this.currentUserList,'搜索出的用户');
            });
        },
        selUser(radioid,userId,realName){
            var sign=document.getElementById(radioid).checked;
            if(sign)
            {
                this.choiceUser={'userId':userId,'realName':realName};
            }
            console.log(this.choiceUser,"选择的用户");
        }
    },
    created(){
        
    },
    mounted(){
        let _this=this;
        setTimeout(function(){
            _this.isLoadingUsers=true;
            _this.$axios.get(`${_this.$urlPool.base_MainSystemApi}${_this.$urlPool.GetOrganizeTree_Url}?Token=${this.$staticmethod.Get('Token')}`).then(res=>{
                _this.OrganizeTreeList=JSON.parse(res.data.Data);
                setTimeout(function(){
                    _this.isLoadingUsers=false;
                }, 150); 
                console.log(this.OrganizeTreeList,"机构部门树");
            });
        },150);
    }
}
</script>