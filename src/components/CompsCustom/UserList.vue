<template>
  <div class="userList" :class="organizeId" v-loading="isLoading">
    <div class="list" :class="organizeId" v-if="users.length>0">
      <el-row class="header" :class="organizeId">
        <el-col class="col" :class="organizeId" :span="6">
          <div class="nc" :class="organizeId">账号</div>
        </el-col>
        <el-col class="col" :class="organizeId" :span="10">
          <div class="nc" :class="organizeId">姓名</div>
        </el-col>
        <el-col class="col" :class="organizeId" :span="8">
          <div class="nc" :class="organizeId">邮箱</div>
        </el-col>
      </el-row>
      <el-row class="body" :class="organizeId" v-for="item in users" :key="item.userId">
        <el-col class="col" :class="organizeId" :span="6">
          <div class="nc" :class="organizeId">{{item.account}}</div>
        </el-col>
        <el-col class="col" :class="organizeId" :span="10">
          <div class="nc" :class="organizeId">{{item.userRealName}}</div>
        </el-col>
        <el-col class="col" :class="organizeId" :span="8">
          <div class="nc" :class="organizeId">{{item.email}}</div>
        </el-col>
      </el-row>
    </div>
    <div class="list" style="line-height:120px;" v-else>
      <el-row class="header">
        <el-col class="col" :class="organizeId" :span="6">
          <div class="nc" :class="organizeId">账号</div>
        </el-col>
        <el-col class="col" :class="organizeId" :span="10">
          <div class="nc" :class="organizeId">姓名</div>
        </el-col>
        <el-col class="col" :class="organizeId" :span="8">
          <div class="nc" :class="organizeId">邮箱</div>
        </el-col>
      </el-row>暂无数据
    </div>
  </div>
</template>
<script>
export default {
  name: "userlist",
  data() {
    return {
      users: [],
      isLoading: false
    };
  },
  props: {
    organizeId: {
      type: String,
      required: false
    }
  },
  created() {
    var Token = this.$staticmethod.Get("Token");
    this.isLoading = true;
    if (this.organizeId == "-1") return;
    this.users = [];
    this.$axios
      .get(
        this.$urlPool.base_MainSystemApi +
          this.$urlPool.GetUsersByOrganizeIdOrDepartmentId +
          "?Token=" +
          Token +
          "&OrganizeId=" +
          this.organizeId +
          "&DepartmentId="
      )
      .then(res => {
        let result = JSON.parse(res.data.Data);
        //debugger;
        for (let i = 0; i < result.length; i++) {
          this.users.push({
            account: result[i].Account,
            userId: result[i].UserId,
            userRealName:
              result[i].RealName == "" ? "暂无数据" : result[i].RealName,
            email: result[i].Email == "" ? "--" : result[i].Email,
            Tel: result[i].Telephone == "" ? "暂无数据" : result[i].Telephone
          });
        }
        this.isLoading = false;
        //debugger;
        console.log(this.users, "部门成员");
      });
  },
  watch: {
    organizeId(Val) {
      if (Val == "-1") return;
      var Token = this.$staticmethod.Get("Token");
      this.isLoading = true;
      this.users = [];
      this.$axios
        .get(
          this.$urlPool.base_MainSystemApi +
            this.$urlPool.GetUsersByOrganizeIdOrDepartmentId +
            "?Token=" +
            Token +
            "&OrganizeId=" +
            Val +
            "&DepartmentId="
        )
        .then(res => {
          let result = JSON.parse(res.data.Data);
          for (let i = 0; i < result.length; i++) {
            this.users.push({
              account: result[i].Account,
              userId: result[i].UserId,
              userRealName:
                result[i].RealName == "" ? "暂无数据" : result[i].RealName,
              email: result[i].Email == "" ? "--" : result[i].Email,
              Tel: result[i].Telephone == "" ? "暂无数据" : result[i].Telephone
            });
          }
          this.isLoading = false;
          console.log(this.users, "部门成员");
        });
    }
  }
};
</script>
<style scoped>
.nc{text-align:center;width: 100%;height: 40px;display: block;}
.userList{overflow: hidden;}
.list{overflow-x:hidden;overflow-y:scroll;width:calc(100% + 19px);height:100%}
.header,.body{height:40px;width:450px;} 
.col{height:100%;line-height:40px;display:block;border-bottom: 1px solid #ebeef5;}
::-webkit-scrollbar {width: 20px;height: 8px;}
::-webkit-scrollbar-thumb{border-radius: 12px;border: 6px solid #0000;box-shadow: 8px 0px 0px #A5ADB7 inset;}
::-webkit-scrollbar-thumb:hover {box-shadow: 8px 0px 0px #4A4A4A inset;}
</style>

