<template>
    <div class="LikeList">
        <ul>
            <li v-for="item in SearchList" :key="item.id" @click="$emit('selUser',item);$emit('closeDialog');">{{item.name}}</li>
        </ul>
    </div>
</template>
<style scoped>
.LikeList{height:250px;overflow:hidden;background-color: #fff;box-shadow:0px 0px 4px #ccc;padding: 5px 0px 5px 0px;} ul,li{padding:0px;margin: 0px}
.LikeList ul{width:calc(100% + 10px);height: 100%;overflow-x:hidden;overflow-y:scroll;}
.LikeList ul li{width:100%;height:25px;line-height:25px;text-align:left;text-indent:20px;overflow: hidden;text-overflow:hidden;white-space: nowrap;border-radius:3px;}
.LikeList ul li:hover{background-color: #f0f0f0;cursor: pointer;}
</style>
<script>
export default {
    name:"searchUser",
    components:{
        
    },
    props:{
        AllDatas:{
            type:Array,
            required:false
        },
        SearchName:{
            type:String,
            required:false
        }
    },
    data(){
        return {
            SearchList:[],
        };
    },
    created(){

    },
    watch:{
        SearchName(val){
            if(this.SearchName=="")
                this.SearchList=this.AllDatas;
            else{
                this.SearchList=[];
                for(let i=0;i<this.AllDatas.length;i++)
                {
                    if(this.AllDatas[i].name.indexOf(this.SearchName)!=-1)
                        this.SearchList.push(this.AllDatas[i]);
                }
            }
        }
    }
}
</script>
