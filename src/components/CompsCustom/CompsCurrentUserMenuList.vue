<template>
      <div
      @click="func_switchstate($event)"
      class="currentuser-menu"
      :class="{'_css-showing': toggleState}">
      <div class="menu-head-msg">
        <div class="user-icon">{{ realName | setAbbreviation }}</div>
        <div class="user-account-msg">
          <p class="name">{{ realName }}</p>
          <p>{{ email }}</p>
        </div>
      </div>

      <div class="menu-list">
        <ul>
          <li v-for="(item,index) in menuList" :key="index"
          @click.stop="menuclick(item)"
          >
            <i :class="['css-mr5 icon',item.icon]"></i>
            {{ item.menuItem }}
            <i class="icon icon-arrow-right_outline"></i>
          </li>
        </ul>
      </div>
    </div>
</template>

<script>
    export default {
        name: "CompsCurrentUserMenuList",
        data() {
            return {
                realName: '',
                email: '',
                menuList: [
                ]
            }
        },

        props: {
          toggleState: {
              type: Boolean,
          },
          isComMgr:{
            type: Boolean
          }
        },

        mounted() {
            this.getUserMsg();
            this.handleMenu()

        },

        methods: {

            // 显示或隐藏个人的下拉菜单
            // ----------------------
            func_switchstate(ev) {
              var _this = this;
            },

            menuclick(item){
              var _this = this;
              _this.$emit("onitemclick", item);
            },

            getUserMsg() {
                this.realName = this.$staticmethod.Get("RealName");
                this.email = this.$staticmethod.Get("Email");
            },
           handleMenu(){
             // 珠三角不需要官网
             this.menuList = [
               {icon: 'icon-interface-set_se', menuItem: '个人设置', code: 'settings'},
               {icon: 'icon-interface-download-fill', menuItem: '下载', code: 'download'},
               {icon: 'icon-interface-home', menuItem: '官网', code: 'official'},
               {icon: 'icon-newface-app-update', menuItem: 'APP更新（Android）', code: 'update'},
               {icon: 'icon-suggested-close-fill', menuItem: '安全退出', code: 'exit'},
             ];
             // 如果需要隐藏个人设置：
             // ---------------
             if (window.bim_config.custom_hideuserdrop_settings == '1') {
               this.menuList = this.menuList.filter(x => x.code != 'settings');
             }

             // 如果需要隐藏下载：
             // ---------------
             if (window.bim_config.custom_hideuserdrop_download == '1') {
               this.menuList = this.menuList.filter(x => x.code != 'download');
             }

             // 如果需要隐藏官网：
             // ---------------
             if (window.bim_config.custom_hideuserdrop_officialwebsite == '1') {
               this.menuList = this.menuList.filter(x => x.code != 'official');
             }
             // 如果需要隐藏app更新：
             // ---------------
             if (window.bim_config.cwi_mobile_show == '1' || !this.isComMgr) {
               this.menuList = this.menuList.filter(x => x.code != 'update');
             }
          }
        },
      watch:{
        isComMgr(val){
          this.handleMenu()
        }
      }
    }
</script>

<style scoped>
  .currentuser-menu {
    min-width: 240px;
    position: absolute;
    top: 37px;
    right: 0;
    background:rgba(255,255,255,1);
    box-shadow:0 1px 3px 0 rgba(0,21,41,0.12);
    border-radius:2px;
    overflow: hidden;
    max-height:0;
    transition: max-height 0.5s;
  }

  .currentuser-menu._css-showing {
    max-height:320px;
    transition: max-height 0.5s;
  }

  ._css-currentuser-icon .currentuser-menu .menu-head-msg {
    display: flex;
    margin: 15px;
    border-bottom: 1px dashed rgba(217,217,217,1);
    padding-bottom: 15px;
  }

  .currentuser-menu .menu-head-msg .user-icon {
    width: 48px;
    height: 48px;
    line-height: 48px;
    border-radius: 4px;
    background: rgba(40,46,61,1);
    font-weight: 500;
    flex-shrink: 0;
  }

  .currentuser-menu .menu-head-msg .user-account-msg {
    padding: 0 15px;
  }

  .user-account-msg p {
    margin: 0;
    font-size: 14px;
    font-weight: 400;
    color: rgba(0,0,0,0.45);
    padding: 3px 0;
    text-align: left;
  }

  .user-account-msg p.name {
    color: rgba(0,0,0,0.85);
    font-weight: 500;
  }

  .currentuser-menu .menu-list {
    font-weight: 400;
    color: rgba(0,0,0,0.45);
  }

  .currentuser-menu .menu-list ul {
    margin: 0;
    padding: 0;
  }

  .currentuser-menu .menu-list ul li {
    list-style: none;
    text-align: left;
    padding: 0 15px 0 20px;
    height: 32px;
    line-height: 32px;
    margin-bottom: 12px;
  }

  .currentuser-menu .menu-list ul li:hover {
    background:rgba(0,0,0,0.02);
    color: rgba(0,0,0,0.65);
  }

  .currentuser-menu .menu-list ul li i.icon {
    vertical-align: sub;
  }

  .currentuser-menu .menu-list ul li i.icon-arrow-right_outline {
    float: right;
    margin-top: 5px;
  }


  /* .slide-enter-active,
  .slide-leave-active {
    transition: height 0.5s;
  } */

  /* .slide-enter {
    height: 0;
  }
  .slide-enter-to {
    height: 183px;
  }

  .slide-leave {
    height: 183px;
  }
  .slide-leave-to {
    height: 0;
  } */
</style>
