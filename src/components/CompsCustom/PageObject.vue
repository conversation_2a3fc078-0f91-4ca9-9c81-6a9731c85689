<template>
  <div class="block">
    <el-pagination :style="{padding:'0px'}"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      :current-page.sync="iPageIndex"
      :page-sizes="_PageSizes"
      :page-size="iPageNum"
      layout="total, sizes, prev, pager, next, jumper"
      :total="PageTotal">
    </el-pagination>
  </div>
</template>
<script>
export default {
    name:'pageObject',
    components:{

    },
    data(){
        return {
            // iPageIndex:this.PageIndex,
            iPageNum:this.PageNum,
        };
    },
    props:{
        PageIndex:{
            type:Number,
            required:true
        },
        PageNum:{
            type:Number,
            required:true
        },
        PageTotal:{
            type:Number,
            required:true
        },
        PageSizes: {
          type: Array,
          required: false,
          default() {
            return [15,20,30,50]
          }
        }
    },
    computed: {
      iPageIndex: {
        get() {
          return this.PageIndex
        },
        set(v) {
          return this.$emit('update:PageIndex',v)
        }
      },
      _PageSizes() {
        if(this.PageSizes) {
          return this.PageSizes
        } else {
          return [15,20,30,50]
        }
      }
    },
    methods:{
      handleSizeChange(val) {
        this.iPageNum=val;
        this.$emit('changePageInfo',this.iPageIndex,this.iPageNum);
        console.log(`每页 ${val} 条`);
      },
      handleCurrentChange(val) {
        this.iPageIndex=val;
        this.$emit('changePageInfo',this.iPageIndex,this.iPageNum);
        console.log(`当前页: ${val}`);
      }
    }
}
</script>
<style scoped>

</style>

