<template>
	<div class="invest-table">
        <div class="handel-btns">
            <div class="btn btn-add" :class="!reportShow && hasEditAuth ? '' : 'not-click'" @click.stop="createMentBtn()">创建</div>
            <div class="btn btn-add" :class="!reportShow && hasEditAuth && detailTableItem.Id ? '' : 'not-click'" @click.stop="editTable()">编辑</div>
            <div class="btn btn-add" :class="!reportShow && hasEditAuth && detailTableItem.Id ? '' : 'not-click'" @click.stop="deteleMent()">删除</div>
            <div class="btn btn-add" @click.stop="getViewReport">{{ reportShow ? '关闭报表' : '报表' }}</div>
        </div>
        <div class="report-table" v-if="reportShow">
            <el-table 
                :data="reportTableData"
                :height="(windowHeight * 2) + 40"
                highlight-current-row
                @current-change="handleCurrentChange"
                class="_css-table-ele _css-table-ele2 css-scroll _css-customstyle css-fixedleftgbcolor-white css-table-cellborder"
                style="width: 100%">
                <el-table-column prop="LandAcquisitionArea" label="征地区域" width="150"> 
                    <template slot-scope="scope">
                        <div class="_css-text-align" >{{scope.row.LandAcquisitionArea}}</div>
                    </template>
                </el-table-column>
                <el-table-column prop="DermanentLandHandoverTarget" label="永久交地目标（亩）" width="180"> 
                    <template slot-scope="scope">
                        <div class="_css-text-align" >{{scope.row.DermanentLandHandoverTarget}}</div>
                    </template>
                </el-table-column>
                <el-table-column prop="TemporaryLandHandoverTarget" label="临时交地目标" width="180"> 
                    <template slot-scope="scope">
                        <div class="_css-text-align" >{{scope.row.TemporaryLandHandoverTarget}}</div>
                    </template>
                </el-table-column>
                <el-table-column prop="DermanentLandHandoverCompletion" label="永久交地完成" width="180"> 
                    <template slot-scope="scope">
                        <div class="_css-text-align" >{{scope.row.DermanentLandHandoverCompletion}}</div>
                    </template>
                </el-table-column>
                <el-table-column prop="TemporaryLandHandoverCompletion" label="临时交地完成" width="180"> 
                    <template slot-scope="scope">
                        <div class="_css-text-align" >{{scope.row.TemporaryLandHandoverCompletion}}</div>
                    </template>
                </el-table-column>
                <el-table-column prop="NewPermanentLandHandover" label="新增永久交地" width="180"> 
                    <template slot-scope="scope">
                        <div class="_css-text-align" >{{scope.row.NewTemporaryLandHandover}}</div>
                    </template>
                </el-table-column>
                <el-table-column prop="NewTemporaryLandHandover" label="新增临时交地" width="180"> 
                    <template slot-scope="scope">
                        <div class="_css-text-align" >{{scope.row.NewTemporaryLandHandover}}</div>
                    </template>
                </el-table-column>
                <el-table-column prop="PermanentLandPay" label="永久用地支付（万）" width="180"> 
                    <template slot-scope="scope">
                        <div class="_css-text-align" >{{scope.row.PermanentLandPay}}</div>
                    </template>
                </el-table-column>
                <el-table-column prop="TemporaryLandPay" label="临时用地支付" width="180"> 
                    <template slot-scope="scope">
                        <div class="_css-text-align" >{{scope.row.TemporaryLandPay}}</div>
                    </template>
                </el-table-column>
                <el-table-column prop="NewPermanentPay" label="新增永久支付" width="180"> 
                    <template slot-scope="scope">
                        <div class="_css-text-align" >{{scope.row.NewPermanentPay}}</div>
                    </template>
                </el-table-column>
                <el-table-column prop="NewTemporaryPay" label="新增临时支付" width="180"> 
                    <template slot-scope="scope">
                        <div class="_css-text-align" >{{scope.row.NewTemporaryPay}}</div>
                    </template>
                </el-table-column>
                <el-table-column prop="FillingTime" label="统计时间" width="130">
                    <template slot-scope="scope">
                        <div class="_css-text-align" >{{scope.row.FillingTime | flt_FillingTime }}</div>
                    </template>
                </el-table-column>
            </el-table>
        </div>
		<div class="total-table" v-if="!reportShow">

            <el-table 
                :data="tableData"
                :height="windowHeight"
                highlight-current-row
                @current-change="handleCurrentChange"
                class="_css-table-ele _css-table-ele2 css-scroll _css-customstyle css-fixedleftgbcolor-white css-table-cellborder"
                style="width: 100%">
                <el-table-column prop="LandAcquisitionArea" label="征地区域" width="150"> 
                    <template slot-scope="scope">
                        <div class="_css-text-align" >{{scope.row.LandAcquisitionArea}}</div>
                    </template>
                </el-table-column>
                <el-table-column prop="DermanentLandHandoverTarget" label="永久交地目标（亩）" width="180"> 
                    <template slot-scope="scope">
                        <div class="_css-text-align" >{{scope.row.DermanentLandHandoverTarget}}</div>
                    </template>
                </el-table-column>
                <el-table-column prop="TemporaryLandHandoverTarget" label="临时交地目标" width="180"> 
                    <template slot-scope="scope">
                        <div class="_css-text-align" >{{scope.row.TemporaryLandHandoverTarget}}</div>
                    </template>
                </el-table-column>
                <el-table-column prop="DermanentLandHandoverCompletion" label="永久交地完成" width="180"> 
                    <template slot-scope="scope">
                        <div class="_css-text-align" >{{scope.row.DermanentLandHandoverCompletion}}</div>
                    </template>
                </el-table-column>
                <el-table-column prop="TemporaryLandHandoverCompletion" label="临时交地完成" width="180"> 
                    <template slot-scope="scope">
                        <div class="_css-text-align" >{{scope.row.TemporaryLandHandoverCompletion}}</div>
                    </template>
                </el-table-column>
                <el-table-column prop="PermanentLandPay" label="永久用地支付（万）" width="180"> 
                    <template slot-scope="scope">
                        <div class="_css-text-align" >{{scope.row.PermanentLandPay}}</div>
                    </template>
                </el-table-column>
                <el-table-column prop="TemporaryLandPay" label="临时用地支付" width="180"> 
                    <template slot-scope="scope">
                        <div class="_css-text-align" >{{scope.row.TemporaryLandPay}}</div>
                    </template>
                </el-table-column>
            </el-table>
        </div>
        <div class="detail-table" v-if="detailTableItemShow && !reportShow">
            <div class="handel-btns">
                <div class="btn btn-add" :class="hasEditAuth ? '' : 'not-click'" @click.stop="createDetailMentBtn()">创建</div>
                <div class="btn btn-add" :class="hasEditAuth && editDetailTableItem.Id ? '' : 'not-click'" @click.stop="editDetailTable()">编辑</div>
                <div class="btn btn-add" :class="hasEditAuth && editDetailTableItem.Id ? '' : 'not-click'" @click.stop="deteleDetailMent()">删除</div>
            </div>
            <el-table 
                :data="tableDetailData"
                :height="windowHeight"
                @current-change="handleDetailCurrentChange"
                highlight-current-row
                class="_css-table-ele _css-table-ele2 css-scroll _css-customstyle css-fixedleftgbcolor-white css-table-cellborder"
                style="max-width:900px">
                <el-table-column prop="Annual" label="填报年度" width="100">
                    <template slot-scope="scope">
                        <div class="_css-text-align" >{{scope.row.Annual}}</div>
                    </template>
                </el-table-column>
                <el-table-column prop="Quarter" label="填报季度" width="100">
                    <template slot-scope="scope">
                        <div class="_css-text-align" >{{scope.row.Quarter | flt_dateQuarter}}</div>
                    </template>
                </el-table-column>
                <el-table-column prop="FillingTime" label="填报时间"  width="140">
                    <template slot-scope="scope">
                        <div class="_css-text-align" >{{scope.row.FillingTime | flt_FillingTime}}</div>
                    </template>
                </el-table-column>
                <el-table-column prop="NewPermanentLandHandover" label="新增永久交地" width="140"> 
                    <template slot-scope="scope">
                        <div class="_css-text-align" >{{scope.row.NewPermanentLandHandover}}</div>
                    </template>
                </el-table-column>
                <el-table-column prop="NewTemporaryLandHandover" label="新增临时交地" width="140"> 
                    <template slot-scope="scope">
                        <div class="_css-text-align" >{{scope.row.NewTemporaryLandHandover}}</div>
                    </template>
                </el-table-column>
                <el-table-column prop="NewPermanentPay" label="新增永久支付" width="140"> 
                    <template slot-scope="scope">
                        <div class="_css-text-align" >{{scope.row.NewPermanentPay}}</div>
                    </template>
                </el-table-column>
                <el-table-column prop="NewTemporaryPay" label="新增临时支付" width="140"> 
                    <template slot-scope="scope">
                        <div class="_css-text-align" >{{scope.row.NewTemporaryPay}}</div>
                    </template>
                </el-table-column>
            </el-table>
        </div>
        <zdialog-function
            :init_title="dialogTitle"
            :init_zindex="1003"
            :init_innerWidth="440"
            :init_width="440"
            :init_height="477"
            init_closebtniconfontclass="icon-suggested-close"
            :init_usecustomtitlearea="false"
            @onclose="resetDialog()"
            v-if="showDialog"
        >
            <div slot="mainslot" class="dialog-list" @mousedown="_stopPropagation($event)">
                <div class="_css-line _css-line-name">
                    <div class="_css-title _css-title-flowname ">
                        征地区域：
                    </div>
                    <div class="_css-fieldvaluename">
                        <el-input v-model="LandAcquisitionArea" :controls="false" placeholder="请输入征地区域"></el-input>
                    </div>
                </div>
                <div class="_css-line _css-line-name">
                    <div class="_css-title _css-title-flowname ">
                        永久交地目标：
                    </div>
                    <div class="_css-fieldvaluename">
                        <el-input-number v-model="DermanentLandHandoverTarget" :controls="false" placeholder="请输入永久交地目标（单位：亩）"></el-input-number>
                    </div>
                </div>
                <div class="_css-line _css-line-name">
                    <div class="_css-title _css-title-flowname ">
                        临时交地目标：
                    </div>
                    <div class="_css-fieldvaluename"> 
                        <el-input-number v-model="TemporaryLandHandoverTarget" :controls="false" placeholder="请输入临时交地目标（单位：亩）"></el-input-number>
                    </div>
                </div>
            </div>
            <div slot="buttonslot" class="css-common-zdialogbtnctn" >
            <zbutton-function
                :init_text="'取消'"
                :init_fontsize="14"
                :debugmode="true"
                :init_height="undefined"
                :init_width="'76px'"
                :init_bgcolor="'#fff'"
                :init_color="'#1890FF'"
                @onclick="resetDialog()">
            </zbutton-function>
            <zbutton-function
                :init_text="'确定'"
                :init_fontsize="14"
                :debugmode="true"
                :init_height="undefined"
                :init_width="'76px'"
                @onclick="submitDialog()">
            </zbutton-function>
            </div>
        </zdialog-function>
        <zdialog-function
            :init_title="dialogDetailTitle"
            :init_zindex="1003"
            :init_innerWidth="430"
            :init_width="430"
            :init_height="477"
            init_closebtniconfontclass="icon-suggested-close"
            :init_usecustomtitlearea="false"
            @onclose="resetDetailDialog()"
            v-if="showDetailDialog"
        >
            <div slot="mainslot" class="dialog-list" @mousedown="_stopPropagation($event)">
                <div class="_css-line _css-line-name">
                    <div class="_css-title _css-title-flowname ">
                        新增永久交地：
                    </div>
                    <div class="_css-fieldvaluename">
                        <el-input-number v-model="NewPermanentLandHandover" :controls="false" placeholder="请输入新增永久交地（单位：亩）"></el-input-number>
                    </div>
                </div>
                <div class="_css-line _css-line-name">
                    <div class="_css-title _css-title-flowname ">
                        新增临时交地：
                    </div>
                    <div class="_css-fieldvaluename">
                        <el-input-number v-model="NewTemporaryLandHandover" :controls="false" placeholder="请输入新增临时交地（单位：亩）"></el-input-number>
                    </div>
                </div>
                <div class="_css-line _css-line-name">
                    <div class="_css-title _css-title-flowname ">
                        新增永久支付：
                    </div>
                    <div class="_css-fieldvaluename">
                        <el-input-number v-model="NewPermanentPay" :controls="false" placeholder="请输入新增永久支付（单位：万元）"></el-input-number>
                    </div>
                </div>
                <div class="_css-line _css-line-name">
                    <div class="_css-title _css-title-flowname ">
                        新增临时支付：
                    </div>
                    <div class="_css-fieldvaluename">
                        <el-input-number v-model="NewTemporaryPay" :controls="false" placeholder="请输入新增临时支付（单位：万元）"></el-input-number>
                    </div>
                </div>
                <div class="_css-line _css-line-name">
                    <div class="_css-title _css-title-flowname ">
                        填报时间：
                    </div>
                    <div class="_css-fieldvaluename">
                        <el-date-picker
                        v-model="FillingTime"
                        @change="getDateChange"
                        type="date"
                        placeholder="请选择日期">
                        </el-date-picker>
                    </div>
                </div>
                <div class="_css-line _css-line-name">
                    <div class="_css-title _css-title-flowname ">
                        填报年度：
                    </div>
                    <div class="_css-fieldvaluename">
                        <el-date-picker
                        v-model="Annual"
                        type="year"
                        placeholder="选择年">
                        </el-date-picker>
                    </div>
                </div>
                <div class="_css-line _css-line-name">
                    <div class="_css-title _css-title-flowname ">
                        填报季度：
                    </div>
                    <div class="_css-fieldvaluename">
                        <el-select v-model="Quarter" placeholder="请选择">
                            <el-option
                            v-for="item in Quarteroptions"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value">
                            </el-option>
                        </el-select>
                    </div>
                </div>
            </div>
            <div slot="buttonslot" class="css-common-zdialogbtnctn" >
            <zbutton-function
                :init_text="'取消'"
                :init_fontsize="14"
                :debugmode="true"
                :init_height="undefined"
                :init_width="'76px'"
                :init_bgcolor="'#fff'"
                :init_color="'#1890FF'"
                @onclick="resetDetailDialog()">
            </zbutton-function>
            <zbutton-function
                :init_text="'确定'"
                :init_fontsize="14"
                :debugmode="true"
                :init_height="undefined"
                :init_width="'76px'"
                @onclick="submitDetailDialog()">
            </zbutton-function>
            </div>
        </zdialog-function>
	</div>
</template>
<script>
export default {
	name: "resettlement",
	data() {
		return {
            hasEditAuth: false, // 权限
            currentInfor: {}, // 当前选中的菜单传来的值,当前页面展示信息
            reportShow: false, // 报告
            reportTableData: [], 
            token: '',
            tableData:[],
            windowHeight:500,
            dialogTitle: '',
            showDialog: false,
            editTableItem: {}, // 编辑总体的信息

            LandAcquisitionArea: '',// 征地区域
            DermanentLandHandoverTarget: 0, // 永久交地目标
            TemporaryLandHandoverTarget: 0, // 临时交地目标
            // 二级
            tableDetailData: [], // 表格数据
            detailTableItem: {}, // 点击表格选中的数据
            detailTableItemShow: false, // 详情显示
            dialogDetailTitle: '',
            showDetailDialog: false,
            editDetailTableItem: {}, // 正在编辑的详情
            NewPermanentLandHandover: 0, // 新增永久交地
            NewTemporaryLandHandover: 0, // 新增临时交地
            NewPermanentPay: 0, // 新增永久支付
            NewTemporaryPay: 0, // 新增临时支付
            FillingTime: new Date(), // 填报时间
            Annual: new Date(), // 填报年度
            Quarter: 1, // 填报季度
            Quarteroptions:[
                {
                    value: 1,
                    label: '第一季度'
                },
                {
                    value: 2,
                    label: '第二季度'
                },
                {
                    value: 3,
                    label: '第三季度'
                },
                {
                    value: 4,
                    label: '第四季度'
                },
            ],
        };
	},
    props:{
        clickitem: {
            type: Object,
            default: {}
        }
    },
    filters:{
        flt_dateQuarter(a){
            let b = (a == 1) ? '第一季度' : (a == 2) ? '第二季度' : (a == 3) ? '第三季度' : (a == 4) ? '第四季度' : '';
            return b
        },
        flt_FillingTime(val){
            if(val){
                let _v = val.substr(0, 10)
                return _v
            }
        }
    },
    watch:{
        clickitem(newData){
            this.currentInfor = newData
            this.getTableData();
            this.detailTableItemShow = false;
            this.reportShow = false
            this.detailTableItem = {};
        }
    },
    created(){
        this.windowHeight = (window.innerHeight - 60 - 52 - 70) / 2;
    },
    mounted(){
        this.currentInfor = this.clickitem
        this.hasEditAuth = this.$staticmethod.hasSomeAuth('TZTB_Edit')  // 提交权限

        this.token = this.$staticmethod.Get("Token")
        window.addEventListener("resize", this.handleResize);
        this.getTableData();
    },
    methods: {
        getViewReport() {
            if(this.reportShow == true){
                this.reportShow = false
                return
            }
            this.$axios
            .get(`${this.$IRUrl.resettlementReport}?token=${this.token}&categoryId=${this.currentInfor.Id}`)
            .then(res=>{
            if(res.data.Ret == 1){
                this.reportTableData = res.data.Data;
                this.reportShow = true
            }else{
                this.$message.error(res.data.Msg)
            }
            })
            .catch(res=>{
                console.log(err,'===err')
            })
        },
        _stopPropagation(ev){      
            ev && ev.stopPropagation && ev.stopPropagation();     
        },
        getTableData(){
            this.$axios
            .get(`${this.$IRUrl.resettlementList}?token=${this.token}&categoryId=${this.currentInfor.Id}`)
            .then(res=>{
            if(res.data.Ret == 1){
                this.tableData = res.data.Data;
                // this.detailTableItemShow=false;
            }else{
                this.$message.error(res.data.Msg)
            }
            })
            .catch(res=>{
                console.log(err,'===err')
            })
        },
        // 点击创建按钮
        createMentBtn(){
            this.showDialog = true;
            this.dialogTitle = '新增'
        },
        // 创建接口调动
        createMent(){
            let data = {
                "Token": this.token,
                "CategoryId": this.currentInfor.Id,
                "LandAcquisitionArea": this.LandAcquisitionArea,
                "DermanentLandHandoverTarget":  this.DermanentLandHandoverTarget,
                "TemporaryLandHandoverTarget":  this.TemporaryLandHandoverTarget,
                "RegistrationTime": "", 
            }
            this.$axios
                .post(`${this.$IRUrl.resettlementCreate}`,data)
                .then(res=>{
                    if(res.data.Ret == 1){
                        this.$message.success('创建成功')
                        this.getTableData()
                        this.resetDialog()
                    }else{
                        this.$message.error(res.data.Msg)
                    }
                })
                .catch(res=>{
                this.$message.error('服务器请求异常')
                })
        },
        // 点击表格编辑
        editTable(){
            if(this.detailTableItem.Id && this.detailTableItem.Id.length > 0){
                this.editTableItem = this.detailTableItem;
            }else{
                this.$message.warning('请选择要操作的数据');
                return;
            }
            this.$axios
                .get(`${this.$IRUrl.resettlementInfo}?id=${this.editTableItem.Id}&token=${this.token}`)
                .then(res=>{
                    if(res.data.Ret == 1){ 
                        this.dialogTitle = '编辑';
                        let _data = res.data.Data;
                        this.LandAcquisitionArea = _data.LandAcquisitionArea; // 征地区域
                        this.DermanentLandHandoverTarget = _data.DermanentLandHandoverTarget;// 永久交地目标
                        this.TemporaryLandHandoverTarget = _data.TemporaryLandHandoverTarget; // 临时交地目标
                        this.showDialog = true;
                    }else{
                        this.$message.error(res.data.Msg)
                    }
                })
                .catch(res=>{
                    this.$message.error('服务器请求异常')
                }) 
            
        },
        // 编辑
        modifyMent(){
            let data = {
                "Token": this.token,
                "Id": this.editTableItem.Id,
                "LandAcquisitionArea": this.LandAcquisitionArea,
                "DermanentLandHandoverTarget":  this.DermanentLandHandoverTarget,
                "TemporaryLandHandoverTarget":  this.TemporaryLandHandoverTarget,
                "RegistrationTime": "",
            }
            this.$axios
                .post(`${this.$IRUrl.resettlementModify}`,data)
                .then(res=>{
                    if(res.data.Ret == 1){
                        this.$message.success('编辑成功')
                        this.getTableData()
                        this.resetDialog()
                    }else{
                        this.$message.error(res.data.Msg)
                    }
                })
                .catch(res=>{
                    this.$message.error('服务器请求异常')
                })
        },
        // 删除
        deteleMent(){
            this.$confirm("确定删除当前数据", {
                confirmButtonText:'确定',
                cancelButtonText:'取消',
                type:'warning'
            }).then(()=>{
                let data = {
                "Id": this.detailTableItem.Id,
                "Token": this.token
                }
                this.$axios
                    .post(`${this.$IRUrl.resettlementDelete}`,data)
                    .then(res=>{
                        if(res.data.Ret == 1){
                            this.$message.success('删除成功')
                            this.getTableData();
                            this.detailTableItem = {};
                            this.detailTableItemShow = false;
                        }else{
                            this.$message.error(res.data.Msg)
                        }
                    })
                    .catch(res=>{
                        this.$message.error('服务器请求异常')
                    })
                }).catch((err) => {
                    console.log(err)
                })
        },
        resetDialog(){
            this.showDialog = false;
            this.LandAcquisitionArea = '';
            this.DermanentLandHandoverTarget = 0;
            this.TemporaryLandHandoverTarget = 0;
        },
        submitDialog(){
            if(this.LandAcquisitionArea == '' ){
                this.$message.warning('请输入征地区域')
                return
            }
            if(this.DermanentLandHandoverTarget <= 0 ){
                this.$message.warning('永久交地目标必须大于0')
                return
            }
            if(this.TemporaryLandHandoverTarget <= 0 ){
                this.$message.warning('临时交地目标必须大于0')
                return
            }
            this.dialogTitle == '新增' ? this.createMent() : this.modifyMent()
        },
        handleCurrentChange(val){ 
            if (val != null && val != {}) {
                this.detailTableItem = val;
                this.detailTableItemShow = true;
                this.getDetailTableData();
                console.log(val,'====1111111111')
            }  
            console.log(val,'====valllll')
        },
        // 二级内容
        // 二级  创建
        createDetailMentBtn(){
            this.showDetailDialog = true;
            this.dialogDetailTitle = '新增'
        },
        // 获取二级表格数据
        getDetailTableData(){
            this.$axios
                .get(`${this.$IRUrl.resettlementDetailList}?token=${this.token}&resettlementId=${this.detailTableItem.Id}`)
                .then(res=>{
                if(res.data.Ret == 1){
                    this.tableDetailData = res.data.Data;
                }else{
                    this.$message.error(res.data.Msg)
                }
                })
                .catch(res=>{
                    console.log(err,'===err')
                })
        },
        resetDetailDialog(){
            this.showDetailDialog = false;
            this.NewPermanentLandHandover = 0;
            this.NewTemporaryLandHandover = 0;
            this.NewPermanentPay = 0;
            this.NewTemporaryPay = 0;
            this.FillingTime = new Date();
            this.Annual = new Date();
            this.Quarter = 1;
        },

        submitDetailDialog(){
            if(this.NewPermanentLandHandover == '' ||  this.NewPermanentLandHandover == undefined ){
                this.NewPermanentLandHandover = 0;
            }
            if(this.NewTemporaryLandHandover == '' ||  this.NewTemporaryLandHandover == undefined ){
                this.NewTemporaryLandHandover = 0;
            }
            if(this.NewPermanentPay == '' ||  this.NewPermanentPay == undefined ){
                this.NewPermanentPay = 0;
            }
            if(this.NewTemporaryPay == '' ||  this.NewTemporaryPay == undefined ){
                this.NewTemporaryPay = 0;
            } 
            if(this.FillingTime == null || this.FillingTime == '' ){
                this.$message.warning('请选择填报时间')
                return
            }
            if(this.Annual == null || this.Annual == '' ){
                this.$message.warning('请选择年度')
                return
            }
            if(this.Quarter <= 0 ){
                this.$message.warning('请选择季度')
                return
            }
            this.dialogDetailTitle == '新增' ? this.createDetailMent() : this.modifyDetailMent()
        },
        // 填报弹窗
        getDateChange(ev){
            this.FillingTime = this.$formatData.formatDateCheck(ev).substr(0,10);
        },
        editDetailTable(){
            if(this.editDetailTableItem.Id && this.editDetailTableItem.Id.length > 0){
                this.$axios
                    .get(`${this.$IRUrl.resettlementDetailInfo}?id=${this.editDetailTableItem.Id}&token=${this.token}`)
                    .then(res=>{
                        if(res.data.Ret == 1){
                            let _data = res.data.Data;
                            this.dialogDetailTitle = '编辑';
                            this.NewPermanentLandHandover = _data.NewPermanentLandHandover;
                            this.NewTemporaryLandHandover = _data.NewTemporaryLandHandover;
                            this.NewPermanentPay = _data.NewPermanentPay;
                            this.NewTemporaryPay = _data.NewTemporaryPay;
                            this.FillingTime = _data.FillingTime;
                            this.Annual = _data.Annual + '';
                            this.Quarter = _data.Quarter;
                            this.showDetailDialog = true;
                        }else{
                            this.$message.error(res.data.Msg)
                        }
                    })
                    .catch(res=>{
                        this.$message.error('服务器请求异常')
                    })
            }else{
                this.$message.warning('请选择要操作的数据');
                return;
            }
        },
        // 新增
        createDetailMent(){
            let data = {
                "Token": this.token,
                "ResettlementId": this.detailTableItem.Id,
                "Annual": new Date(this.Annual).getFullYear(),
                "Quarter": this.Quarter,
                "FillingTime": this.FillingTime,
                "NewPermanentLandHandover": this.NewPermanentLandHandover,
                "NewTemporaryLandHandover": this.NewTemporaryLandHandover,
                "NewPermanentPay": this.NewPermanentPay,
                "NewTemporaryPay": this.NewTemporaryPay,
            }
            this.$axios
                .post(`${this.$IRUrl.resettlementDetailCreate}`,data)
                .then(res=>{
                    if(res.data.Ret == 1){
                        this.getDetailTableData();
                        this.$message.success('创建成功')
                        this.resetDetailDialog();
                        this.getTableData();
                    }else{
                        this.$message.error(res.data.Msg)
                    }
                })
                .catch(res=>{
                this.$message.error('服务器请求异常')
                })
        },
        // 编辑
        modifyDetailMent(){
            let data = {
                "Token": this.token,
                "Id": this.editDetailTableItem.Id,
                "Annual": new Date(this.Annual).getFullYear(),
                "Quarter": this.Quarter,
                "FillingTime": this.FillingTime,
                "NewPermanentLandHandover": this.NewPermanentLandHandover,
                "NewTemporaryLandHandover": this.NewTemporaryLandHandover,
                "NewPermanentPay": this.NewPermanentPay,
                "NewTemporaryPay": this.NewTemporaryPay,
            }
            this.$axios
                .post(`${this.$IRUrl.resettlementDetailModify}`,data)
                .then(res=>{
                    if(res.data.Ret == 1){
                        this.$message.success('编辑成功')
                        this.getDetailTableData();
                        this.resetDetailDialog();
                        this.getTableData();
                    }else{
                        this.$message.error(res.data.Msg)
                    }
                })
                .catch(res=>{
                    this.$message.error('服务器请求异常')
                })
        },
        // 删除
        deteleDetailMent(){
            if(this.editDetailTableItem.Id && this.editDetailTableItem.Id.length > 0){
                this.$confirm("确定删除当前数据", {
                    confirmButtonText:'确定',
                    cancelButtonText:'取消',
                    type:'warning'
                }).then(()=>{
                    let data = {
                    "Id": this.editDetailTableItem.Id,
                    "Token": this.token
                    }
                    this.$axios
                        .post(`${this.$IRUrl.resettlementDetailDelete}`,data)
                        .then(res=>{
                            if(res.data.Ret == 1){
                                this.$message.success('删除成功')
                                this.getDetailTableData();
                                this.getTableData();
                                this.editDetailTableItem = {}
                            }else{
                                this.$message.error(res.data.Msg)
                            }
                        })
                        .catch(res=>{
                            this.$message.error('服务器请求异常')
                        })
                    }).catch((err) => {
                        console.log(err)
                    })
            }else{
                this.$message.warning('请选择要操作的数据');
                return;
            }
        }, 
        handleDetailCurrentChange(val){
            if (val != null && val != {}) {
                this.editDetailTableItem = val;
                this.showDetailDialog = false;
            }
        },
        handleResize() {
            this.windowHeight = (window.innerHeight - 60 - 52 - 70) / 2;
        }
    },
    beforeDestroy() {
        window.removeEventListener("resize", this.handleResize);
    },
};
</script>
<style lang="scss" scoped>
@import url("../../assets/css/invest.css");
._css-title-flowname{
    width: 30% !important;
}
</style>