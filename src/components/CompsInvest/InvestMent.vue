<template>
	<div class="invest-table">
       
        <div class="handel-btns">
            <div class="btn btn-add" :class="!reportShow && hasEditAuth ? '' : 'not-click'" @click.stop="createMentBtn()">创建</div>
            <div class="btn btn-add" :class="!reportShow && hasEditAuth && detailTableItem.Id ? '' : 'not-click'" @click.stop="editTable()">编辑</div>
            <div class="btn btn-add" :class="!reportShow && hasEditAuth && detailTableItem.Id ? '' : 'not-click'" @click.stop="deteleMent()">删除</div>
            <div class="btn btn-add" @click.stop="getViewReport">{{ reportShow ? '关闭报表' : '报表' }}</div>
        </div>
        <div class="report-table" v-if="reportShow">
            <el-table 
                :data="reportTableData"
                :height="(windowHeight * 2) + 40"
                highlight-current-row
                @current-change="handleCurrentChange"
                class="_css-table-ele _css-table-ele2 css-scroll _css-customstyle css-fixedleftgbcolor-white css-table-cellborder"
                style="width: 100%">
                <el-table-column prop="CategoryName" label="投资分类" width="150">
                    <template slot-scope="scope">
                        <div class="_css-text-align" >{{scope.row.CategoryName}}</div>
                    </template>
                </el-table-column>
                <el-table-column prop="TotalEstimate" label="总概算（W）" width="150">
                    <template slot-scope="scope">
                        <div class="_css-text-align" >{{scope.row.TotalEstimate}}</div>
                    </template>
                </el-table-column>
                <el-table-column prop="TotalCumulative" label="总投资完成" width="150">
                    <template slot-scope="scope">
                        <div class="_css-text-align" >{{scope.row.TotalCumulative}}</div>
                    </template>
                </el-table-column>
                <el-table-column prop="TotalPay" label="总投资支付" width="150">
                    <template slot-scope="scope">
                        <div class="_css-text-align" >{{scope.row.TotalPay}}</div>
                    </template>
                </el-table-column>
                <el-table-column prop="AnnualEstimate" :label="AnnualEstimateText" width="150">
                    <template slot-scope="scope">
                        <div class="_css-text-align" >{{scope.row.AnnualEstimate}}</div>
                    </template>
                </el-table-column>
                <el-table-column prop="AnnualCumulative" label="年度投资完成" width="150">
                    <template slot-scope="scope">
                        <div class="_css-text-align" >{{scope.row.AnnualCumulative}}</div>
                    </template>
                </el-table-column>
                <el-table-column prop="AnnualPay" label="年度投资支付" width="150">
                    <template slot-scope="scope">
                        <div class="_css-text-align" >{{scope.row.AnnualPay}}</div>
                    </template>
                </el-table-column>
                <el-table-column prop="QuarterEstimate" :label="QuarterEstimateText" width="150">
                    <template slot-scope="scope">
                        <div class="_css-text-align" >{{scope.row.QuarterEstimate}}</div>
                    </template>
                </el-table-column>
                <el-table-column prop="QuarterCumulative" label="季度投资完成" width="150">
                    <template slot-scope="scope">
                        <div class="_css-text-align" >{{scope.row.QuarterCumulative}}</div>
                    </template>
                </el-table-column>
                <el-table-column prop="QuarterPay" label="季度投资支付" width="150">
                    <template slot-scope="scope">
                        <div class="_css-text-align" >{{scope.row.QuarterPay}}</div>
                    </template>
                </el-table-column>
                <el-table-column prop="NewInvestment" label="新增投资" width="100">
                    <template slot-scope="scope">
                        <div class="_css-text-align" >{{scope.row.NewInvestment}}</div>
                    </template>
                </el-table-column>
                <el-table-column prop="NewPay" label="新增支付" width="100">
                    <template slot-scope="scope">
                        <div class="_css-text-align" >{{scope.row.NewPay | flt_dateQuarter}}</div>
                    </template>
                </el-table-column>
                <el-table-column prop="FillingTime" label="统计时间" width="130">
                    <template slot-scope="scope">
                        <div class="_css-text-align" >{{scope.row.FillingTime | flt_FillingTime }}</div>
                    </template>
                </el-table-column>
            </el-table>
        </div>
		<div class="total-table" v-if="!reportShow">
            <el-table 
                :data="tableData"
                :height="windowHeight"
                highlight-current-row
                @current-change="handleCurrentChange"
                class="_css-table-ele _css-table-ele2 css-scroll _css-customstyle css-fixedleftgbcolor-white css-table-cellborder"
                style="width: 100%">
                <el-table-column prop="CustomerCategoryName" label="投资分类" width="150">
                    <template slot-scope="scope">
                        <div class="_css-text-align" >{{scope.row.CustomerCategoryName}}</div>
                    </template>
                </el-table-column>
                <el-table-column prop="InvestmentAnnual" label="年度" width="100">
                    <template slot-scope="scope">
                        <div class="_css-text-align" >{{scope.row.InvestmentAnnual}}</div>
                    </template>
                </el-table-column>
                <el-table-column prop="InvestmentQuarter" label="季度" width="100">
                    <template slot-scope="scope">
                        <div class="_css-text-align" >{{scope.row.InvestmentQuarter | flt_dateQuarter}}</div>
                    </template>
                </el-table-column>
                <el-table-column prop="TotalEstimate" label="总概算（W）" width="150">
                    <template slot-scope="scope">
                        <div class="_css-text-align" >{{scope.row.TotalEstimate}}</div>
                    </template>
                </el-table-column>
                <el-table-column prop="TotalCumulative" label="总投资完成" width="150">
                    <template slot-scope="scope">
                        <div class="_css-text-align" >{{scope.row.TotalCumulative}}</div>
                    </template>
                </el-table-column>
                <el-table-column prop="TotalPay" label="总投资支付" width="150">
                    <template slot-scope="scope">
                        <div class="_css-text-align" >{{scope.row.TotalPay}}</div>
                    </template>
                </el-table-column>
                <el-table-column prop="AnnualEstimate" label="年度概算" width="150">
                    <template slot-scope="scope">
                        <div class="_css-text-align" >{{scope.row.AnnualEstimate}}</div>
                    </template>
                </el-table-column>
                <el-table-column prop="AnnualCumulative" label="年度投资完成" width="150">
                    <template slot-scope="scope">
                        <div class="_css-text-align" >{{scope.row.AnnualCumulative}}</div>
                    </template>
                </el-table-column>
                <el-table-column prop="AnnualPay" label="年度投资支付" width="150">
                    <template slot-scope="scope">
                        <div class="_css-text-align" >{{scope.row.AnnualPay}}</div>
                    </template>
                </el-table-column>
                <el-table-column prop="QuarterEstimate" label="季度概算" width="150">
                    <template slot-scope="scope">
                        <div class="_css-text-align" >{{scope.row.QuarterEstimate}}</div>
                    </template>
                </el-table-column>
                <el-table-column prop="QuarterCumulative" label="季度投资完成" width="150">
                    <template slot-scope="scope">
                        <div class="_css-text-align" >{{scope.row.QuarterCumulative}}</div>
                    </template>
                </el-table-column>
                <el-table-column prop="QuarterPay" label="季度投资支付" width="150">
                    <template slot-scope="scope">
                        <div class="_css-text-align" >{{scope.row.QuarterPay}}</div>
                    </template>
                </el-table-column>
                
            </el-table>
        </div>
        <div class="detail-table" v-if="detailTableItemShow && !reportShow">
            <div class="handel-btns">
                <div class="btn btn-add" :class="hasEditAuth ? '' : 'not-click'" @click.stop="createDetailMentBtn()">创建</div>
                <div class="btn btn-add" :class="hasEditAuth && editDetailTableItem.Id ? '' : 'not-click'" @click.stop="editDetailTable()">编辑</div>
                <div class="btn btn-add" :class="hasEditAuth && editDetailTableItem.Id ? '' : 'not-click'" @click.stop="deteleDetailMent()">删除</div>
            </div>
            <el-table 
                :data="tableDetailData"
                :height="windowHeight"
                @current-change="handleDetailCurrentChange"
                highlight-current-row
                class="_css-table-ele _css-table-ele2 css-scroll _css-customstyle css-fixedleftgbcolor-white css-table-cellborder"
                style="width: 690px">
                <el-table-column prop="Annual" label="填报年度" width="100"> 
                    <template slot-scope="scope">
                        <div class="_css-text-align" >{{scope.row.Annual}}</div>
                    </template>
                </el-table-column>
                <el-table-column prop="Quarter" label="填报季度" width="100">
                    <template slot-scope="scope">
                        <div class="_css-text-align" >{{scope.row.Quarter | flt_dateQuarter}}</div>
                    </template>
                </el-table-column>
                <el-table-column prop="NewInvestment" label="新增投资" width="130"> 
                    <template slot-scope="scope">
                        <div class="_css-text-align" >{{scope.row.NewInvestment}}</div>
                    </template>
                </el-table-column>
                <el-table-column prop="NewPay" label="新增支付" width="130"> 
                    <template slot-scope="scope">
                        <div class="_css-text-align" >{{scope.row.NewPay}}</div>
                    </template>
                </el-table-column>
                <el-table-column prop="FillingTime" label="填报时间" width="130">
                    <template slot-scope="scope">
                        <div class="_css-text-align" >{{scope.row.FillingTime | flt_FillingTime}}</div>
                    </template>
                </el-table-column>
                <el-table-column prop="UnitName" label="所属单位">
                    <template slot-scope="scope">
                        <div class="_css-text-align" >{{scope.row.UnitName}}</div>
                    </template>
                </el-table-column>
            </el-table>
        </div>
        <zdialog-function
            :init_title="dialogTitle"
            :init_zindex="1003"
            :init_innerWidth="540"
            :init_width="540"
            :init_height="477"
            init_closebtniconfontclass="icon-suggested-close"
            :init_usecustomtitlearea="false"
            @onclose="resetDialog()"
            v-if="showDialog"
        >
            <div slot="mainslot" class="dialog-list litter" @mousedown="_stopPropagation($event)">
                <div class="_css-line _css-line-name">
                    <div class="_css-title _css-title-flowname ">
                        投资分类：
                    </div>
                    <div class="_css-fieldvaluename">
                        <span class="padding16">{{ currentInfor.CategoryName }}</span>
                    </div>
                </div>
                <div class="_css-line _css-line-name">
                    <div class="_css-title _css-title-flowname ">
                        总概算：
                    </div>
                    <div class="_css-fieldvaluename">
                        <el-input-number v-model="TotalEstimate" :disabled="!editTotalEstimateInput" :controls="false" placeholder="请输入总概算（单位：万元）"></el-input-number>
                    </div>
                </div>
                <div class="_css-line _css-line-name">
                    <div class="_css-title _css-title-flowname ">
                        年度概算：
                    </div>
                    <div class="_css-fieldvaluename _css-selectednode">
                        <div class="left">
                            <el-date-picker
                            v-model="InvestmentAnnual"
                            @change="getYearChange"
                            type="year"
                            placeholder="选择年">
                            </el-date-picker>
                        </div>
                        <span class="selectednode-line"></span>
                        <el-input-number v-model="AnnualEstimate" :disabled="!editAnnualEstimateInput" :controls="false" placeholder="请输入年度概算（单位：万元）"></el-input-number>
                    </div>
                </div>
                <div class="_css-line _css-line-name">
                    <div class="_css-title _css-title-flowname ">
                        季度概算：
                    </div>
                    <div class="_css-fieldvaluename _css-selectednode">
                        <div class="left">
                            <el-select v-model="InvestmentQuarter" placeholder="请选择">
                                <el-option
                                v-for="item in InvestmentQuarteroptions"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value">
                                </el-option>
                            </el-select>
                        </div>
                        <span class="selectednode-line"></span>
                        <el-input-number v-model="QuarterEstimate" :disabled="!editQuarterEstimateInput" :controls="false" placeholder="请输入季度概算（单位：万元）"></el-input-number>
                    </div>
                </div>
            </div>
            <div slot="buttonslot" class="css-common-zdialogbtnctn" >
            <zbutton-function
                :init_text="'取消'"
                :init_fontsize="14"
                :debugmode="true"
                :init_height="undefined"
                :init_width="'76px'"
                :init_bgcolor="'#fff'"
                :init_color="'#1890FF'"
                @onclick="resetDialog()">
            </zbutton-function>
            <zbutton-function
                :init_text="'确定'"
                :init_fontsize="14"
                :debugmode="true"
                :init_height="undefined"
                :init_width="'76px'"
                @onclick="submitDialog()">
            </zbutton-function>
            </div>
        </zdialog-function>
        <zdialog-function
            :init_title="dialogDetailTitle"
            :init_zindex="1003"
            :init_innerWidth="400"
            :init_width="400"
            :init_height="477"
            init_closebtniconfontclass="icon-suggested-close"
            :init_usecustomtitlearea="false"
            @onclose="resetDetailDialog()"
            v-if="showDetailDialog"
        >
            <div slot="mainslot" class="dialog-list" @mousedown="_stopPropagation($event)">
                <div class="_css-line _css-line-name">
                    <div class="_css-title _css-title-flowname ">
                        新增投资：
                    </div>
                    <div class="_css-fieldvaluename">
                        <el-input-number v-model="NewInvestment" :controls="false" placeholder="请输入新增投资（单位：万元）"></el-input-number>
                    </div>
                </div>
                <div class="_css-line _css-line-name">
                    <div class="_css-title _css-title-flowname ">
                        新增支付：
                    </div>
                    <div class="_css-fieldvaluename">
                        <el-input-number v-model="NewPay" :controls="false" placeholder="请输入新增支付（单位：万元）"></el-input-number>
                    </div>
                </div>
                <div class="_css-line _css-line-name">
                    <div class="_css-title _css-title-flowname ">
                        填报时间：
                    </div>
                    <div class="_css-fieldvaluename _css-selectednode">
                        <el-date-picker
                        v-model="FillingTime"
                        @change="getDateChange"
                        type="date"
                        placeholder="请选择日期">
                        </el-date-picker>
                    </div>
                </div>
                <div class="_css-line _css-line-name">
                    <div class="_css-title _css-title-flowname ">
                        填报年度：
                    </div>
                    <div class="_css-fieldvaluename">
                        <span class="padding16">{{ detailTableItem.InvestmentAnnual }}</span>
                    </div>
                </div>
                <div class="_css-line _css-line-name">
                    <div class="_css-title _css-title-flowname ">
                        填报季度：
                    </div>
                    <div class="_css-fieldvaluename">
                        <span class="padding16">{{ detailTableItem.InvestmentQuarter | flt_dateQuarter }}</span>
                    </div>
                </div>
                <div class="_css-line _css-line-name">
                    <div class="_css-title _css-title-flowname ">
                        所属单位：
                    </div>
                    <div class="_css-fieldvaluename _css-selectednode">
                        <el-select v-model="UnitName" placeholder="请选择">
                            <el-option
                            v-for="item in UnitNameoptions"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value">
                            </el-option>
                        </el-select>
                    </div>
                </div>
            </div>
            <div slot="buttonslot" class="css-common-zdialogbtnctn" >
            <zbutton-function
                :init_text="'取消'"
                :init_fontsize="14"
                :debugmode="true"
                :init_height="undefined"
                :init_width="'76px'"
                :init_bgcolor="'#fff'"
                :init_color="'#1890FF'"
                @onclick="resetDetailDialog()">
            </zbutton-function>
            <zbutton-function
                :init_text="'确定'"
                :init_fontsize="14"
                :debugmode="true"
                :init_height="undefined"
                :init_width="'76px'"
                @onclick="submitDetailDialog()">
            </zbutton-function>
            </div>
        </zdialog-function>
	</div>
</template>
<script>
export default {
	name: "InvestMent",
	data() {
		return {
            hasEditAuth: false, // 权限
            currentInfor: {}, // 当前选中的菜单传来的值,当前页面展示信息
            reportShow: false, // 报告
            reportTableData: [], 
            AnnualEstimateText: '',  // 年度概算
            QuarterEstimateText: '', // 季度概算
            token: '',
            tableData:[],
            windowHeight:500,
            dialogTitle: '',
            showDialog: false,
            editTableItem: {}, // 编辑总体的信息
            editTotalEstimateInput: true,
            editAnnualEstimateInput: true,
            editQuarterEstimateInput: true,
            TotalEstimate: 0, // 总概算
            InvestmentAnnual: new Date(), // 年度
            AnnualEstimate: 0,// 年度概算
            InvestmentQuarter: 1, // 季度
            QuarterEstimate: 0, // 季度概算
            InvestmentQuarteroptions:[
                {
                    value: 1,
                    label: '第一季度'
                },
                {
                    value: 2,
                    label: '第二季度'
                },
                {
                    value: 3,
                    label: '第三季度'
                },
                {
                    value: 4,
                    label: '第四季度'
                },
            ],
            // 二级
            tableDetailData: [], // 表格数据
            detailTableItem: {}, // 点击表格选中的数据
            detailTableItemShow: false, // 详情显示
            dialogDetailTitle: '',
            showDetailDialog: false,
            editDetailTableItem: {}, // 正在编辑的详情
            NewInvestment: 0,
            NewPay: 0,
            FillingTime: new Date(),
            UnitName: '',
            UnitNameoptions:[
                {
                    value: '施工',
                    label: '施工',
                },
                {
                    value: '监理',
                    label: '监理',
                },
            ]
        };
	},
    props:{
        clickitem: {
            type: Object,
            default: {}
        }
    },
    filters:{
        flt_dateQuarter(a){
            let b = (a == 1) ? '第一季度' : (a == 2) ? '第二季度' : (a == 3) ? '第三季度' : (a == 4) ? '第四季度' : '';
            return b
        },
        flt_FillingTime(val){
            if(val){
                let _v = val.substr(0, 10)
                return _v
            }
        }
    },
    watch:{
        clickitem(newData){
            this.reportShow = false
            this.detailTableItem = {};
            this.editDetailTableItem = {}
            this.currentInfor = newData
            this.getTableData();
            this.detailTableItemShow = false;
        },

    },
    created(){
        this.windowHeight = (window.innerHeight - 60 - 52 - 70) / 2;
    },
    mounted(){
        this.currentInfor = this.clickitem
        this.hasEditAuth = this.$staticmethod.hasSomeAuth('TZTB_Edit')  // 提交权限

        this.token = this.$staticmethod.Get("Token")
        this.getTableData();
        window.addEventListener("resize", this.handleResize);
    },
    methods: {
        getViewReport() {
            if(this.reportShow == true){
                this.reportShow = false
                return
            }
            let year = null,currentQuarter = null
            if(this.tableData[0]){
                year = this.tableData[0].InvestmentAnnual
                currentQuarter = this.tableData[0].InvestmentQuarter
            }else{
                const date = new Date();
                year = date.getFullYear(); // 获取年
                const currentMonth = date.getMonth(); // 获取当前月份（0到11）
                currentQuarter = Math.floor(currentMonth / 3) + 1; // 根据当前月份计算季度
            }
            this.AnnualEstimateText = `${year}年度概算`
            let txt = this.$formatData.numberToChinese(currentQuarter)
            this.QuarterEstimateText = `${txt}季度概算`

            this.$axios
            .get(`${this.$IRUrl.investmentReport}?token=${this.token}&organizeId=${this.$staticmethod._Get("organizeId")}&annual=${year}&quarter=${currentQuarter}`)
            .then(res=>{
            if(res.data.Ret == 1){
                this.reportTableData = res.data.Data;
                this.reportShow = true
            }else{
                this.$message.error(res.data.Msg)
            }
            })
            .catch(res=>{
                console.log(err,'===err')
            })
        },
        _stopPropagation(ev){      
            ev && ev.stopPropagation && ev.stopPropagation();     
        },
        getTableData(){
            this.$axios
            .get(`${this.$IRUrl.investmentList}?token=${this.token}&categoryId=${this.currentInfor.Id}&annual=&quarter=`)
            .then(res=>{
            if(res.data.Ret == 1){
                this.tableData = res.data.Data;
            }else{
                this.$message.error(res.data.Msg)
            }
            })
            .catch(res=>{
                console.log(err,'===err')
            })
        },
        // 点击创建按钮
        createMentBtn(){
            // 先调用接口GetFirstEstimate 查找下当前年份是否有值、如果!=0，就不能编辑总概算和年度、==0可编辑
            this.showDialog = true;
            this.dialogTitle = '新增'
            this.getFirstEstimate();
        },
        getFirstEstimate(){
            let _year = new Date(this.InvestmentAnnual).getFullYear()
            this.$axios
                .get(`${this.$IRUrl.getFirstEstimate}?token=${this.token}&categoryId=${this.currentInfor.Id}&annual=${_year}`)
                .then(res=>{
                    if(res.data.Ret == 1){
                        let _data = res.data.Data;
                        this.TotalEstimate = _data.TotalEstimate;
                        this.AnnualEstimate = _data.AnnualEstimate;
                        // IsCanAdd是三个值整体的判断，如果IsCanAdd=true 是能填写
                        this.editQuarterEstimateInput = _data.IsCanAdd;
                        if(_data.IsCanAdd){
                            this.editTotalEstimateInput = _data.IsEnableTotalEstimate   
                            this.editAnnualEstimateInput = _data.IsEnableAnnualEstimate  
                        }else{
                            this.editTotalEstimateInput = this.editAnnualEstimateInput = _data.IsCanAdd
                        }
                    }else{
                        this.$message.error(res.data.Msg)
                    }
                })
        },
        // 创建接口调动
        createMent(){ 
            let data = {
                "Token": this.token,
                "CategoryId": this.currentInfor.Id,
                "InvestmentAnnual": new Date(this.InvestmentAnnual).getFullYear(),
                "InvestmentQuarter": this.InvestmentQuarter,
                "TotalEstimate": this.TotalEstimate,
                "AnnualEstimate": this.AnnualEstimate,
                "QuarterEstimate": this.QuarterEstimate,
                "RegistrationTime": ""
            }
            this.$axios
                .post(`${this.$IRUrl.investmentCreate}`,data)
                .then(res=>{
                    if(res.data.Ret == 1){
                        this.$message.success('创建成功')
                        this.getTableData();
                        this.resetDialog()
                    }else{
                        this.$message.error(res.data.Msg)
                    }
                })
                .catch(res=>{
                    this.$message.error('服务器请求异常')
                })
        },
        // 点击表格编辑
        editTable(){
            if(this.detailTableItem.Id && this.detailTableItem.Id.length > 0){
                this.editTableItem = this.detailTableItem;
            }else{
                this.$message.warning('请选择要操作的数据');
                return;
            }
            this.$axios
                .get(`${this.$IRUrl.investmentInfo}?id=${this.editTableItem.Id}&token=${this.token}`)
                .then(res=>{
                    if(res.data.Ret == 1){ 
                        this.dialogTitle = '编辑';
                        let _data = res.data.Data;
                        this.TotalEstimate = _data.TotalEstimate; // 总概算
                        this.InvestmentAnnual = _data.InvestmentAnnual + '';// 年度
                        this.AnnualEstimate = _data.AnnualEstimate; // 年度概算
                        this.InvestmentQuarter = _data.InvestmentQuarter;// 季度
                        this.QuarterEstimate = _data.QuarterEstimate; // 季度概算
                        console.log(_data, this.QuarterEstimate,'====', _data.QuarterEstimate)
                        this.showDialog = true;
                    }else{
                        this.$message.error(res.data.Msg)
                    }
                })
                .catch(res=>{
                    this.$message.error('服务器请求异常')
                }) 
            
        },
        // 编辑
        modifyMent(){  
            let data = {
                "Token": this.token,
                "Id": this.editTableItem.Id,
                "TotalEstimate": this.TotalEstimate,
                "InvestmentAnnual": new Date(this.InvestmentAnnual).getFullYear(),
                "InvestmentQuarter": this.InvestmentQuarter,
                "AnnualEstimate": this.AnnualEstimate,
                "QuarterEstimate": this.QuarterEstimate,
                "RegistrationTime": ""
            }
            this.$axios
                .post(`${this.$IRUrl.investmentModify}`,data)
                .then(res=>{
                    if(res.data.Ret == 1){
                        this.$message.success('编辑成功')
                        this.getTableData();
                        this.resetDialog();
                    }else{
                        this.$message.error(res.data.Msg)
                    }
                })
                .catch(res=>{
                    this.$message.error('服务器请求异常')
                }) 
        },
        // 删除
        deteleMent(){ 
            if(this.detailTableItem.Id && this.detailTableItem.Id.length > 0){
                this.$confirm("确定删除当前数据", {
                    confirmButtonText:'确定',
                    cancelButtonText:'取消',
                    type:'warning'
                }).then(()=>{
                    let data = {
                    "Id": this.detailTableItem.Id,
                    "Token": this.token
                    }
                    this.$axios
                        .post(`${this.$IRUrl.investmentDelete}`,data)
                        .then(res=>{
                            if(res.data.Ret == 1){
                                this.$message.success('删除成功')
                                this.getTableData();
                                this.detailTableItem = {};
                                this.detailTableItemShow = false;
                            }else{
                                this.$message.error(res.data.Msg)
                            }
                        })
                        .catch(res=>{
                            this.$message.error('服务器请求异常')
                        })
                    }).catch((err) => {
                        console.log(err)
                    })
            }else{
                this.$message.warning('请选择要操作的数据');
                return;
            }
            
        }, 
        getYearChange(){
            this.getFirstEstimate()
        }, 
        resetDialog(){
            this.showDialog = false;
            this.TotalEstimate = 0;
            this.InvestmentAnnual = new Date();
            this.AnnualEstimate = 0;
            this.InvestmentQuarter = 1;
            this.QuarterEstimate = 0;
        },
        submitDialog(){
            if(this.TotalEstimate <= 0 ){
                this.$message.warning('总概算金额必须大于0')
                return
            }
            if(this.InvestmentAnnual == null || this.InvestmentAnnual == '' ){
                this.$message.warning('请选择年度')
                return
            }
            if(this.AnnualEstimate <= 0 ){
                this.$message.warning('年度概算金额必须大于0')
                return
            }
            
            if(this.InvestmentQuarter <= 0 ){
                this.$message.warning('请选择季度')
                return
            }
            if(this.QuarterEstimate == '' ||  this.QuarterEstimate == undefined){
                this.QuarterEstimate = 0;
            }
            console.log(this.QuarterEstimate,'=this.QuarterEstimate')
            this.dialogTitle == '新增' ? this.createMent() : this.modifyMent()
        },
        handleCurrentChange(val){
            if (val != null && val != {}) {
                this.detailTableItem = val;
                this.detailTableItemShow = true;
                this.getDetailTableData();
                console.log(val,'====1111111111')
            }
        },
        // 二级内容
        // 二级  创建
        createDetailMentBtn(){
            this.showDetailDialog = true;
            this.dialogDetailTitle = '新增'
        },
        // 获取二级表格数据
        getDetailTableData(){
            this.$axios
                .get(`${this.$IRUrl.i_DetailList}?token=${this.token}&investmentId=${this.detailTableItem.Id}`)
                .then(res=>{
                if(res.data.Ret == 1){
                    this.tableDetailData = res.data.Data;
                    
                }else{
                    this.$message.error(res.data.Msg)
                }
                })
                .catch(res=>{
                    console.log(err,'===err')
                })
        },
        resetDetailDialog(){
            this.showDetailDialog = false;
            this.NewInvestment = 0;
            this.NewPay = 0;
            this.FillingTime = new Date();
            this.UnitName = '';
        },

        submitDetailDialog(){
            if(this.NewInvestment == '' ||  this.NewInvestment == undefined ){
                this.NewInvestment = 0;
            }
            if(this.NewPay == '' ||  this.NewPay == undefined ){
                this.NewPay = 0;
            }
            if(this.FillingTime == null || this.FillingTime == '' ){
                this.$message.warning('请选择填报时间')
                return
            }
            if(this.UnitName == '' ){
                this.$message.warning('请选择所属单位')
                return
            }
            this.dialogDetailTitle == '新增' ? this.createDetailMent() : this.modifyDetailMent()
        },
        // 填报弹窗
        getDateChange(ev){
            this.FillingTime = this.$formatData.formatDateCheck(ev).substr(0,10);
        },
        editDetailTable(){
            if(this.editDetailTableItem.Id && this.editDetailTableItem.Id.length > 0){
                this.$axios
                    .get(`${this.$IRUrl.i_DetailInfo}?id=${this.editDetailTableItem.Id}&token=${this.token}`)
                    .then(res=>{
                        if(res.data.Ret == 1){
                            let _data = res.data.Data;
                            this.dialogDetailTitle = '编辑';
                            this.NewInvestment = _data.NewInvestment;
                            this.NewPay = _data.NewPay;
                            this.FillingTime = _data.FillingTime;
                            this.UnitName = _data.UnitName;
                            this.showDetailDialog = true;
                        }else{
                            this.$message.error(res.data.Msg)
                        }
                    })
                    .catch(res=>{
                        this.$message.error('服务器请求异常')
                    })
            }else{
                this.$message.warning('请选择要操作的数据');
                return;
            }
        },
        // 新增
        createDetailMent(){
            let data = {
                "Token": this.token,
                "InvestmentId": this.detailTableItem.Id,
                "NewInvestment": this.NewInvestment,
                "NewPay": this.NewPay,
                "FillingTime": this.FillingTime,
                "UnitName": this.UnitName
            }
            this.$axios
                .post(`${this.$IRUrl.i_DetailCreate}`,data)
                .then(res=>{
                    if(res.data.Ret == 1){
                        this.$message.success('创建成功')
                        this.getDetailTableData();
                        this.resetDetailDialog();
                        this.getTableData();
                    }else{
                        this.$message.error(res.data.Msg)
                    }
                })
                .catch(res=>{
                this.$message.error('服务器请求异常')
                })
        },
        // 编辑
        modifyDetailMent(){ 
            if(this.editDetailTableItem.Id && this.editDetailTableItem.Id.length > 0){
                let data = {
                    "Token": this.token,
                    "Id": this.editDetailTableItem.Id,
                    "NewInvestment": this.NewInvestment,
                    "NewPay": this.NewPay,
                    "FillingTime": this.FillingTime,
                    "UnitName": this.UnitName
                }
                this.$axios
                    .post(`${this.$IRUrl.i_DetailModify}`,data)
                    .then(res=>{
                        if(res.data.Ret == 1){
                            this.$message.success('编辑成功')
                            this.getDetailTableData();
                            this.resetDetailDialog();
                            this.getTableData();
                        }else{
                            this.$message.error(res.data.Msg)
                        }
                    })
                    .catch(res=>{
                        this.$message.error('服务器请求异常')
                    })
            }else{
                this.$message.warning('请选择要操作的数据');
                return;
            }
            
        },
        // 删除
        deteleDetailMent(){
            if(this.editDetailTableItem.Id && this.editDetailTableItem.Id.length > 0){
                this.$confirm("确定删除当前数据", {
                    confirmButtonText:'确定',
                    cancelButtonText:'取消',
                    type:'warning'
                }).then(()=>{
                    let data = {
                    "Id": this.editDetailTableItem.Id,
                    "Token": this.token
                    }
                    this.$axios
                        .post(`${this.$IRUrl.i_DetailDelete}`,data)
                        .then(res=>{
                            if(res.data.Ret == 1){
                                this.$message.success('删除成功')
                                this.getDetailTableData();
                                this.getTableData();
                                this.editDetailTableItem = {}
                            }else{
                                this.$message.error(res.data.Msg)
                            }
                        })
                        .catch(res=>{
                            this.$message.error('服务器请求异常')
                        })
                    }).catch((err) => {
                        console.log(err)
                    })
            }else{
                this.$message.warning('请选择要操作的数据');
                return;
            }
        }, 
        handleDetailCurrentChange(val){
            if(val == null) return
            if (val.Id && val.Id.length > 0) {
                this.editDetailTableItem = val;
                this.showDetailDialog = false;
            } else {
                this.$message.warning('请选择表格数据');
                return;
            } 
        },
        handleResize() {
            this.windowHeight = (window.innerHeight - 60 - 52 - 70) / 2;
        },
        
    },
    beforeDestroy() {
        window.removeEventListener("resize", this.handleResize);
    },
};
</script>
<style lang="scss" scoped>
@import url("../../assets/css/invest.css");
.selectednode-line{
    width: 1px;
    height: 34px;
    border-left: 1px solid rgba(0,0,0,.1);
}
.litter ._css-title-flowname{
    width: 18%;
}
</style>