<template>
	<div class="invest-table">
        <div class="handel-btns">
            <div class="btn btn-add" :class="!reportShow && hasEditAuth ? '' : 'not-click'" @click.stop="createMentBtn()">创建</div>
            <div class="btn btn-add" :class="!reportShow && hasEditAuth && detailTableItem.Id ? '' : 'not-click'" @click.stop="editTable()">编辑</div>
            <div class="btn btn-add" :class="!reportShow && hasEditAuth && detailTableItem.Id ? '' : 'not-click'" @click.stop="deteleMent()">删除</div>
            <div class="btn btn-add" @click.stop="getViewReport">{{ reportShow ? '关闭报表' : '报表' }}</div>
        </div>
        <div class="report-table" v-if="reportShow">
            <el-table 
                :data="reportTableData"
                :height="(windowHeight * 2) + 40"
                @current-change="handleCurrentChange"
                highlight-current-row
                class="_css-table-ele _css-table-ele2 css-scroll _css-customstyle css-fixedleftgbcolor-white css-table-cellborder"
                style="width: 100%">
                <el-table-column prop="CategoryName" label="投资分类" width="150">
                    <template slot-scope="scope">
                        <div class="_css-text-align">
                            {{scope.row.CategoryName}}
                        </div>
                    </template>
                </el-table-column>
                <el-table-column prop="InvestmentCompletion" label="投资完成（W）" width="150"> 
                    <template slot-scope="scope">
                        <div class="_css-text-align" >{{scope.row.InvestmentCompletion}}</div>
                    </template>
                </el-table-column>
                <el-table-column prop="FundPay" label="资金支付" width="150"> 
                    <template slot-scope="scope">
                        <div class="_css-text-align" >{{scope.row.FundPay}}</div>
                    </template>
                </el-table-column> 
                <el-table-column prop="RegistrationTime" label="统计时间" width="150">
                    <template slot-scope="scope">
                        <div class="_css-text-align" >{{scope.row.RegistrationTime | flt_FillingTime}}</div>
                    </template>
                </el-table-column> 
            </el-table>
        </div>
		<div class="total-table" v-if="!reportShow">
            <el-table 
                :data="tableData"
                :height="windowHeight"
                @current-change="handleCurrentChange"
                highlight-current-row
                class="_css-table-ele _css-table-ele2 css-scroll _css-customstyle css-fixedleftgbcolor-white css-table-cellborder"
                style="width: 100%">
                <el-table-column prop="CustomerCategoryName" label="投资分类" width="225">
                    <template slot-scope="scope">
                        <div class="_css-text-left">
                            <el-tooltip class="item" effect="dark" :content="scope.row.CustomerCategoryName" placement="top-start">
                                <div class="tooltip">{{scope.row.CustomerCategoryName}}</div>
                            </el-tooltip>
                        </div>
                    </template>
                </el-table-column>
                <el-table-column prop="InvestmentCompletion" label="投资完成（W）" width="150"> 
                    <template slot-scope="scope">
                        <div class="_css-text-align" >{{scope.row.InvestmentCompletion}}</div>
                    </template>
                </el-table-column>
                <el-table-column prop="FundPay" label="资金支付" width="150"> 
                    <template slot-scope="scope">
                        <div class="_css-text-align" >{{scope.row.FundPay}}</div>
                    </template>
                </el-table-column>
                <el-table-column prop="NewInvestment" label="新增投资" width="150"> 
                    <template slot-scope="scope">
                        <div class="_css-text-align" >{{scope.row.NewInvestment}}</div>
                    </template>
                </el-table-column>
                <el-table-column prop="NewPay" label="新增支付" width="150"> 
                    <template slot-scope="scope">
                        <div class="_css-text-align" >{{scope.row.NewPay}}</div>
                    </template>
                </el-table-column>
                <el-table-column prop="RegistrationTime" label="填报时间" width="150">
                    <template slot-scope="scope">
                        <div class="_css-text-align" >{{scope.row.RegistrationTime | flt_FillingTime}}</div>
                    </template>
                </el-table-column>
                <el-table-column prop="Annual" label="年度" width="100">
                    <template slot-scope="scope">
                        <div class="_css-text-align" >{{scope.row.Annual}}</div>
                    </template>
                </el-table-column>
                <el-table-column prop="Quarter" label="季度" width="100">
                    <template slot-scope="scope">
                        <div class="_css-text-align" >{{scope.row.Quarter | flt_dateQuarter}}</div>
                    </template>
                </el-table-column>
            </el-table>
        </div> 
        <zdialog-function
            :init_title="dialogTitle"
            :init_zindex="1003"
            :init_innerWidth="400"
            :init_width="400"
            :init_height="477"
            init_closebtniconfontclass="icon-suggested-close"
            :init_usecustomtitlearea="false"
            @onclose="resetDialog()"
            v-if="showDialog"
        >
            <div slot="mainslot" class="dialog-list" @mousedown="_stopPropagation($event)">
                <div class="_css-line _css-line-name">
                    <div class="_css-title _css-title-flowname ">
                        投资分类：
                    </div>
                    <div class="_css-fieldvaluename">
                        <el-input v-model="CustomerCategoryName" :controls="false" placeholder="请输入投资分类"></el-input>
                    </div>
                </div>
                <div class="_css-line _css-line-name">
                    <div class="_css-title _css-title-flowname ">
                        新增投资：
                    </div>
                    <div class="_css-fieldvaluename">
                        <el-input-number v-model="NewInvestment" :controls="false" placeholder="请输入新增投资（单位：万元）"></el-input-number>
                    </div>
                </div>
                <div class="_css-line _css-line-name">
                    <div class="_css-title _css-title-flowname ">
                        新增支付：
                    </div>
                    <div class="_css-fieldvaluename">
                        <el-input-number v-model="NewPay" :controls="false" placeholder="请输入新增支付（单位：万元）"></el-input-number>
                    </div>
                </div>
                 <div class="_css-line _css-line-name">
                    <div class="_css-title _css-title-flowname ">
                        填报时间：
                    </div>
                    <div class="_css-fieldvaluename">
                        <el-date-picker
                        v-model="RegistrationTime"
                        @change="getDateRegistrationTime"
                        type="date"
                        placeholder="请选择日期">
                        </el-date-picker>
                    </div>
                </div>
                <div class="_css-line _css-line-name">
                    <div class="_css-title _css-title-flowname ">
                        所属年度：
                    </div>
                    <div class="_css-fieldvaluename">
                        <el-date-picker
                        v-model="Annual"
                        type="year"
                        placeholder="选择年">
                        </el-date-picker>
                    </div>
                </div>
                <div class="_css-line _css-line-name">
                    <div class="_css-title _css-title-flowname ">
                        所属季度：
                    </div>
                    <div class="_css-fieldvaluename">
                        <el-select v-model="Quarter" placeholder="请选择">
                            <el-option
                            v-for="item in Quarteroptions"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value">
                            </el-option>
                        </el-select>
                    </div>
                </div>
            </div>
            <div slot="buttonslot" class="css-common-zdialogbtnctn" >
            <zbutton-function
                :init_text="'取消'"
                :init_fontsize="14"
                :debugmode="true"
                :init_height="undefined"
                :init_width="'76px'"
                :init_bgcolor="'#fff'"
                :init_color="'#1890FF'"
                @onclick="resetDialog()">
            </zbutton-function>
            <zbutton-function
                :init_text="'确定'"
                :init_fontsize="14"
                :debugmode="true"
                :init_height="undefined"
                :init_width="'76px'"
                @onclick="submitDialog()">
            </zbutton-function>
            </div>
        </zdialog-function>
	</div>
</template>
<script>
export default {
	name: "InvestMent",
	data() {
		return {
            hasEditAuth: false, // 权限
            currentInfor: {}, // 当前选中的菜单传来的值,当前页面展示信息
            reportShow: false, // 报告
            reportTableData: [], 
            token: '',
            tableData:[],
            windowHeight: 800,
            dialogTitle: '',
            showDialog: false,
            editTableItem: {}, // 编辑总体的信息
            CustomerCategoryName: '', // 投资分类
            NewInvestment: 0, // 新增投资
            NewPay: 0, // 新增支付
            RegistrationTime: new Date(), // 填报时间
            Annual: new Date(), // 所属年度
            Quarter: 1, // 所属季度
            Quarteroptions:[
                {
                    value: 1,
                    label: '第一季度'
                },
                {
                    value: 2,
                    label: '第二季度'
                },
                {
                    value: 3,
                    label: '第三季度'
                },
                {
                    value: 4,
                    label: '第四季度'
                },
            ], 
            detailTableItem: {}
        };
	},
    props:{
        clickitem: {
            type: Object,
            default: {}
        }
    },
    filters:{
        flt_dateQuarter(a){
            let b = (a == 1) ? '第一季度' : (a == 2) ? '第二季度' : (a == 3) ? '第三季度' : (a == 4) ? '第四季度' : '';
            return b
        },
        flt_FillingTime(val){
            if(val){
                let _v = val.substr(0, 10)
                return _v
            }
        }
    },
    watch:{
        clickitem(newData){
            this.currentInfor = newData
            this.reportShow = false
            this.getTableData();
            this.detailTableItem = {};
        }
    },

    created(){
        this.windowHeight = window.innerHeight - 130 ;
    },
    mounted(){
        this.currentInfor = this.clickitem
        this.hasEditAuth = this.$staticmethod.hasSomeAuth('TZTB_Edit')  // 提交权限

        this.token = this.$staticmethod.Get("Token")
        this.getTableData();
    },
    methods: {
        getViewReport() {
            if(this.reportShow == true){
                this.reportShow = false
                return
            }
            this.$axios
            .get(`${this.$IRUrl.otherReport}?token=${this.token}&organizeId=${this.$staticmethod._Get("organizeId")}`)
            .then(res=>{
            if(res.data.Ret == 1){
                this.reportTableData = res.data.Data;
                this.reportShow = true
            }else{
                this.$message.error(res.data.Msg)
            }
            })
            .catch(res=>{
                console.log(err,'===err')
            })
        },
        _stopPropagation(ev){      
            ev && ev.stopPropagation && ev.stopPropagation();     
        },
        getTableData(){
            this.$axios
            .get(`${this.$IRUrl.otherList}?token=${this.token}&categoryId=${this.currentInfor.Id}`)
            .then(res=>{
            if(res.data.Ret == 1){
                this.tableData = res.data.Data;
                this.resetDialog()
            }else{
                this.$message.error(res.data.Msg)
            }
            })
            .catch(res=>{
                console.log(err,'===err')
            })
        },
        handleCurrentChange(val){ 
            if(!reportShow){
                if(val == null) return
                if (val.Id && val.Id.length > 0) {
                    this.detailTableItem = val;
                } else {
                    this.$message.warning('请选择表格数据');
                    return;
                }
            }
            
        },
        // 点击创建按钮
        createMentBtn(){
            this.showDialog = true;
            this.dialogTitle = '新增'
        },
        // 创建接口调动
        createMent(){ 
            let data = {
                "Token": this.token,
                "CategoryId": this.currentInfor.Id,
                "CustomerCategoryName": this.CustomerCategoryName,
                "Annual": new Date(this.Annual).getFullYear(),
                "Quarter": this.Quarter,
                "NewInvestment": this.NewInvestment,
                "NewPay": this.NewPay,
                "RegistrationTime": this.RegistrationTime,
            }
            this.$axios
                .post(`${this.$IRUrl.otherCreate}`,data)
                .then(res=>{
                    if(res.data.Ret == 1){
                        this.$message.success('创建成功')
                        this.getTableData()
                    }else{
                        this.$message.error(res.data.Msg)
                    }
                })
                .catch(res=>{
                    this.$message.error('服务器请求异常')
                })
        },
        // 点击表格编辑
        editTable(){
            if(this.detailTableItem.Id && this.detailTableItem.Id.length > 0){
                this.editTableItem = this.detailTableItem;
            }else{
                this.$message.warning('请选择要操作的数据');
                return;
            } 
            this.$axios
                .get(`${this.$IRUrl.otherInfo}?id=${this.editTableItem.Id}&token=${this.token}`)
                .then(res=>{
                    if(res.data.Ret == 1){ 
                        this.dialogTitle = '编辑';
                        let _data = res.data.Data;
                        this.CustomerCategoryName = _data.CustomerCategoryName;
                        this.NewInvestment = _data.NewInvestment;
                        this.NewPay = _data.NewPay;
                        this.RegistrationTime = _data.RegistrationTime;
                        this.Annual = _data.Annual + '';
                        this.Quarter = _data.Quarter;
                        this.showDialog = true;
                    }else{
                        this.$message.error(res.data.Msg)
                    }
                })
                .catch(res=>{
                    this.$message.error('服务器请求异常')
                }) 
            
        },
        // 编辑
        modifyMent(){  
            let data = {
                "Token": this.token,
                "Id": this.editTableItem.Id,
                "CustomerCategoryName": this.CustomerCategoryName,
                "Annual": new Date(this.Annual).getFullYear(),
                "Quarter": this.Quarter,
                "NewInvestment": this.NewInvestment,
                "NewPay": this.NewPay,
                "RegistrationTime": this.RegistrationTime,
            }
            this.$axios
                .post(`${this.$IRUrl.otherModify}`,data)
                .then(res=>{
                    if(res.data.Ret == 1){
                        this.$message.success('编辑成功')
                        this.getTableData()
                    }else{
                        this.$message.error(res.data.Msg)
                    }
                })
                .catch(res=>{
                    this.$message.error('服务器请求异常')
                }) 
        },
        // 删除
        deteleMent(){
            this.$confirm("确定删除当前数据", {
                confirmButtonText:'确定',
                cancelButtonText:'取消',
                type:'warning'
            }).then(()=>{
                let data = {
                "Id": this.detailTableItem.Id,
                "Token": this.token
                }
                this.$axios
                    .post(`${this.$IRUrl.otherDelete}`,data)
                    .then(res=>{
                        if(res.data.Ret == 1){
                            this.$message.success('删除成功')
                            this.getTableData();
                            this.detailTableItem = {}
                        }else{
                            this.$message.error(res.data.Msg)
                        }
                    })
                    .catch(res=>{
                        this.$message.error('服务器请求异常')
                    })
                }).catch((err) => {
                    console.log(err)
                })
        }, 
        resetDialog(){
            this.showDialog = false;
            this.CustomerCategoryName = '';
            this.NewInvestment = 0;
            this.NewPay = 0;
            this.RegistrationTime = new Date();
            this.Annual = new Date();
            this.Quarter = 1;
        },
        submitDialog(){
            if(this.CustomerCategoryName == '' ){
                this.$message.warning('请输入投资分类')
                return
            }
            if(this.NewInvestment == '' ||  this.NewInvestment == undefined){
                this.NewInvestment = 0;
            }
            if(this.NewPay == '' ||  this.NewPay == undefined){
                this.NewPay = 0;
            }
            if(this.RegistrationTime == null || this.RegistrationTime == '' ){
                this.$message.warning('请选择填报时间')
                return
            }
            if(this.Annual == null || this.Annual == '' ){
                this.$message.warning('请选择年度')
                return
            }
            if(this.Quarter <= 0 ){
                this.$message.warning('请选择季度')
                return
            }
            this.dialogTitle == '新增' ? this.createMent() : this.modifyMent()
        },
        getDateRegistrationTime(ev){
            this.RegistrationTime = this.$formatData.formatDateCheck(ev).substr(0,10);
        },
    }
};
</script>
<style lang="scss" scoped>
@import url("../../assets/css/invest.css");
.tooltip{
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
}
._css-text-left{
    text-align: left !important;
    width: 100%;
}
</style>