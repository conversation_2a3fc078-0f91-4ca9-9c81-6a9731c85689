<template>
    <div class="wrapper">
        <header>
            <div class="_d2header" >
                <i class="icon-arrow-left_outline"></i>
                <span>{{title}}</span>
            </div>
            <div>
                <slot name="btn"></slot>
            </div>
            <!-- <div
                v-if="btnName"
                class="btn-wp"
                @click="btnClick"
                :class="{'btn-disabled':isDisabled}"
            >{{btnName}}</div> -->
        </header>
        <main>
            <slot name="main"></slot>
        </main>
    </div>
</template>

<script>
export default {
    components: {},
    props: {
        title: {
            type: String,
            default: "默认标题"
        },
        btnName: {
            type: String
        },
        isDisabled: {
            type: Boolean,
            default: false
        }
    },
    data() {
        return {}
    },
    watch: {},
    computed: {},
    methods: {
        close() {
            this.$emit("closeCallBack")
        },
        btnClick() {
            this.$emit("btnClick")
        }
    },
    created() {},
    mounted() {}
}
</script>
<style lang="stylus" scoped rel="stylesheet/stylus">
.wrapper {
    width: 100%;
    height: 100%;
    background: #ffffff;
    position: fixed;
    top: 0;
    left: 0;
    z-index: 1001;
    display: flex;
    flex-direction: column;

    header {
        height: 0px;
        line-height: 64px;
        box-shadow: 0px 1px 3px 0px rgba(0, 21, 41, 0.12);
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0 28px;
        font-size: 14px;
        color: rgba(0, 0, 0, 0.85);

        ._d2header {
            visibility hidden
        }

        i {
            margin-right: 5px;
        }

        div {
            display: flex;
            align-items: center;
            cursor: pointer;

            &.btn-wp {
                width: 100px;
                height: 40px;
                text-align: center;
                -webkit-box-pack: center;
                -ms-flex-pack: center;
                justify-content: center;
                border-radius: 4px;
                background-color: #1890FF;
                color: #fff;
                cursor: pointer;
                opacity: 0.8;

                &:hover {
                    opacity: 1;
                }
            }

            &.btn-disabled {
                background-color: rgba(0, 0, 0, 0.25);
                color: #fff;
                cursor: not-allowed;
            }
        }
    }

    main {
        flex: 1;

        &>* {
            width: 100%;
            height: 100%;
            position: fixed;

        }
    }
}
</style>