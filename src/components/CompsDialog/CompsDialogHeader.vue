<template>
    <!-- 对话框头 -->
    <div class="_css-head">

        <!-- 对话框标题 -->
        <div class="_css-title" :title="title">
            <div>{{title_left}}</div>
            <div class="_css-title-main"
            :style="{'max-width':title_maxwidth?title_maxwidth+'px':'200px'}"
            >{{title}}</div>
            <slot name="btn"></slot>
            <div>{{title_right}}</div>
        </div>
        <div class="_css-closebtncontainer" >
            <div v-show="!hideclosebtn" class="_css-closebtn icon-suggested-close" @click.stop="_closebtn_click()"></div>
        </div>
        <!-- //对话框标题 -->

    </div>
    <!-- //对话框头 -->
</template>
<script>
/*
datainput:
    title_left:'',
    title:'',
    title_right:''
events:
    oncancel()
*/
export default {
    data(){
        return {

        };
    },
    props:{
        title: {
            type: String,
            required: true
        },
        title_maxwidth: {
            type: Number,
            required: false
        },
        title_left: {
            type: String,
            required: false
        },
        title_right: {
            type: String,
            required: false
        },
        hideclosebtn:{
            type: Boolean,
            required: false
        }
    },
    methods:{
        _closebtn_click(){
            var _this = this;
            _this.$emit("oncancel");
        }
    }
}
</script>
<style scoped>
    ._css-head{
        height:64px;
        display: flex;
        align-items: center;
        font-size:18px !important;
        font-weight:500;
        cursor: move;
        flex:none;
        /* background-color: rgba(0,0,0,0.02); */
    }
    ._css-title-main{
        /* max-width: 200px; */
        overflow-x: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }
    ._css-title{
        margin-left: 16px;
        display: flex;
        align-items: center;
    }
    ._css-closebtncontainer{
        flex:1;
        display: flex;
        height:100%;
        flex-direction: row-reverse;
        align-items: center;
    }
    ._css-closebtn{
        width: 20px;
        height: 20px;
        font-size: 20px;
        margin-right: 16px;
        display: flex;
        align-items: center;
        justify-content: space-around;
        cursor: pointer;
    }
    ._css-closebtn:hover{
        color:#1890FF;
        /* background-color: rgba(0,0,0,0.02); */
    }
</style>