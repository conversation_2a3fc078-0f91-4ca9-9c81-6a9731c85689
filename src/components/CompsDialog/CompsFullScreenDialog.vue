<template>
    <div class="wrapper" :style="getBigBimStyle()">
        <header>
            <div  @click="close">
                <i v-if="!init_hidebackbtn" class="icon-arrow-left_outline"></i>
                <span v-if="headerHide">{{title}}</span>
            </div>
            <div>
                <slot name="btn"></slot>
            </div>
            <!-- <div
                v-if="btnName"
                class="btn-wp"
                @click="btnClick"
                :class="{'btn-disabled':isDisabled}"
            >{{btnName}}</div> -->
        </header>
        <main data-debug="fullscreendialog">
            <slot name="main"></slot>
        </main>
    </div>
</template>

<script>
export default {
    components: {},
    props: {
        title: {
            type: String,
            default: "默认标题"
        },
        btnName: {
            type: String
        },
        isDisabled: {
            type: Boolean,
            default: false
        },
        init_hidebackbtn:{
            type: Boolean,
            default: false,
            required: false
        },
        bigBimLeft:{
            type: Number,
        },
        bigBimTop:{
            type: Number,
        },
        bigBimWidth:{
            type: Number, 
        },
        bigBimHeight:{
            type: Number, 
        }
    },
    data() {
        return {
            headerHide: false, // 大屏显示使用该组件，header不展示       
        }
    },
    watch: {},
    computed: {},
    methods: {
        close() {
            this.$emit("closeCallBack")
        },
        btnClick() {
            this.$emit("btnClick")
        },
        getBigBimStyle(){
            let _this = this;
            let _s = {};
            if(_this.headerHide){
                _s["width"] = '100%';
                _s["height"] = '100%';
                _s["left"] = 0;
                _s["top"] = 0;
                _s['background'] = '#fff'
            }else{
                _s["width"] = _this.bigBimWidth +'px';
                _s["height"] = _this.bigBimHeight +'px';
                _s["left"] = _this.bigBimLeft+ 'px';
                _s["top"] = _this.bigBimTop + 'px';
            }
            return _s;
        }

    },
    created() {},
    mounted() {
        if(this.$route.query.title == '进度方案'){
            this.headerHide = false; 
        }else{
            this.headerHide = true;
        }
    }
}
</script>
<style lang="stylus" scoped rel="stylesheet/stylus" >
.wrapper {
    width: 100%;
    height: 100%;
    
    position: fixed;
    top: 0;
    left: 0;
    z-index: 1001;
    display: flex;
    flex-direction: column;

    header {
        height: 64px;
        line-height: 64px;
        box-shadow: 0px 1px 3px 0px rgba(0, 21, 41, 0.12);
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0 28px;
        font-size: 14px;
        color: rgba(0, 0, 0, 0.85);

        i {
            margin-right: 5px;
        }

        div {
            display: flex;
            align-items: center;
            cursor: pointer;

            &.btn-wp {
                width: 100px;
                height: 40px;
                text-align: center;
                -webkit-box-pack: center;
                -ms-flex-pack: center;
                justify-content: center;
                border-radius: 4px;
                background-color: #1890FF;
                color: #fff;
                cursor: pointer;
                pointer-events :all;
             
            }

            &.btn-disabled {
                background-color: rgba(0, 0, 0, 0.25);
                color: #fff;
                cursor: not-allowed;
            }
        }
    }

    main {
        flex: 1;

        height calc(100% - 64px);

        &>* {
            width: 100%;
            height: 100%;
        }
    }
}
</style>