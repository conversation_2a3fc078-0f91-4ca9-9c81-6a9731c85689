<template>
  <div class="entry-wrapper" @click="emitMaskClick()">
      <div
      :id="centerid || 'id_compsdialogProgress'"
      class="center" @click.stop="emitCenterClick()" :style="{width}">
          <header>
              <i class="icon-arrow-left_outline" :style="{visibility:isBack?'visibility':'hidden'}" @click="back"></i>
              <span>{{title}}</span>
              <i class="icon icon-suggested-close" @click="close"></i>
          </header>
          <main>
              <slot name="main"></slot>
          </main>
          <footer>
              <slot name="btn"></slot>
          </footer>
      </div>
  </div>
</template>

<script>
export default {
  components:{},
  props:{
      title:{
          type:String,
          default:'进度方案管理'
      },
      isBack:{
          type:Boolean,
          default:false
      },
      width:{
          type:String,
          default:'300px'
      },
      centerid:{
          type: String,
          required:false
      }
  },
  data(){
    return {
    }
  },
  watch:{},
  computed:{},
  methods:{
      close(){
          this.$emit('close')
      },
      back(){
          let judge
          if(this.$props.title.indexOf('创建')>=0){
              judge = 0
          }else if(this.$props.title.indexOf('删除')>=0){
              judge =1
          }else{
              judge = 2
          }
          this.$emit('back',judge)
      },
      emitMaskClick(){
          this.$emit('maskClick')
      },
      emitCenterClick(){
          this.$emit('centerClick')
      }
  },
  created(){},
  mounted(){

  }
}
</script>
<style lang="stylus" scoped rel="stylesheet/stylus">
    .entry-wrapper{
        position fixed
        top 0
        left 0
        width 100%
        height 100%
        background rgba(0,0,0,.4)
        z-index 999
        .center{
            background #fff
            position absolute
            top 50%
            left 50%
            transform translate(-50%,-50%)
            box-shadow 0px 2px 14px 0px rgba(26,26,26,0.1)
            border-radius 4px
            header {
                height 44px
                line-height 44px
                display flex
                flex-direction row
                justify-content space-between
                align-items center
                border-bottom 1px solid rgba(0,0,0,.09)
                padding 0 24px
                span{
                    flex 1
                    text-align center
                }
                i{
                    opacity .8
                    &:hover{
                        opacity 1
                        cursor pointer
                    }
                }
            }
            footer{

                &>div{
                    height 64px
                    display flex
                    flex-direction row
                    padding 0 24px
                    border-top 1px solid rgba(0,0,0,.09)
                    align-items center
                    div{
                        flex 1
                        height 40px
                        line-height 40px
                        background #1890FF
                        color #fff
                        border-radius 4px
                        opacity .8
                        border 1px solid #1890FF
                        &:nth-child(1){
                            background #fff
                            color #1890FF
                        }
                        &:hover{
                            opacity 1
                            cursor pointer
                        }
                        &._pe-dis{
                            opacity .5
                            cursor not-allowed
                        }
                    }
                }

            }
        }
    }
</style>
