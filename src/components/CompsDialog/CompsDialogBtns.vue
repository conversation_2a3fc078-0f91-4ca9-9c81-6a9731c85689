<template>
  <div class="_css-all"
  :style="allstyle"
  >
    <template v-if="!data || !data.btnstype || data.btnstype == 'okcancel'">
      <el-button 
      class="_css-default" type="primary" @click.stop="_ok();" >{{oktext || '确定'}}</el-button>
      <!-- <el-button class="_css-other" @click.stop="_cancel();" >取消</el-button> -->
      <div class="_css-other" @click.stop="_cancel();" >取消</div>
    </template>
    <template v-else-if="data && data.btnstype == 'close'">
      <el-button class="_css-default" type="primary" @click.stop="_cancel();" >关闭</el-button>
    </template>
    <div @click.stop="_warning();" class="_css-warningbtn" v-if="warningbtn == true" >
      {{warningbtntext || '删除'}}
    </div>
  </div>
</template>
<script>
/*
properties:{
  btnstype: 'okcancel' // close
}
events:
    onok
    oncancel
    onwarning
*/
import { EventBus } from "@static/event.js";
export default {
  data(){
    return {
      R_lastTime:true,//第一次提交时的状态
    }
  },
  computed:{
    allstyle:{
      get(){
        var _this = this;
        var _s = {};
        if (_this.warningbtn == true) {
          _s["position"] = "relative"
        }
        return _s;
      }
    }
  },
  props: {
    data: {
      type: Object,
      required: false
    },
    oktext: {
      type:String,
      required: false
    },
    warningbtn:{
      type:Boolean,
      required: false
    },
    warningbtntext:{
      type:String,
      required: false
    }
  },
  methods:{
      _ok(){
          var _this = this;
          EventBus.$on('R_InitiateProblem',(i)=>{
            if(i){
              _this.R_lastTime = true
            }
          })
          _this.$emit("onok");
          // if(_this.R_lastTime){
          //     _this.R_lastTime = false
          //     _this.$emit("onok");
          // }else{
          //   // console.log('请勿过快点击');
          // }
      },

      _cancel(){
          var _this = this;
          _this.$emit("oncancel");
      },

      _warning(){
        var _this = this;
        _this.$emit("onwarning");
      }
  }
};
</script>
<style scoped>
._css-all {
  width: 100%;
  /* height: 48px; */
  height: 84px;
  display: flex;
  align-items: center;
  flex-direction: row-reverse;
}
._css-default{
    min-width:76px;
    padding-left:8px;
    padding-right:8px;
    height:40px;
    margin-right: 16px;
    border-radius:2px;
    font-size: 14px !important;
}
._css-default{
  background-color: #1890FF !important;
}
._css-default:hover{
  background-color: #3AA0FF !important;
}

._css-other{
    min-width:76px;
    height:40px;
    line-height: 40px;
    cursor: pointer;
    margin-right: 16px;
    border-radius:2px;
    font-size: 14px !important;
    text-align: center;
}
._css-other:hover{
  background-color: rgba(0,0,0,0.08);
}
._css-warningbtn{
  position:absolute;
  height:40px;
  left:24px;
  line-height: 40px;
  min-width:76px;
  padding-left:12px;
  padding-right:12px;
  box-sizing: border-box;
  color:#F5222D;
  cursor: pointer;
  font-size: 14px;
}
._css-warningbtn:hover{
  background-color: rgba(250, 84, 28, 0.1);
}
</style>