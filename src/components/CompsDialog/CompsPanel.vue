<template>
    <div class="_css-all" :style="{
        'padding-left':paddingLeft + 'px', 'padding-right': paddingRight + 'px'
        }"></div>
</template>
<script>
/*
input:
    paddingLeft: Number,
    paddingRight: Number
events:

*/
export default {
    data(){
        return {
            
        }
    },
    props:{
        paddingLeft:{
            type: Number,
            required:true
        },
        paddingRight:{
            type: Number,
            required:true
        }
    }
}
</script>
<style scoped>
._css-all{

}
</style>