<template>
    <div class="drag-wrapper" ref="box">
        <div class="drag" ref="drag"></div>
        <main>
            <slot name="main"></slot>
        </main>
        <div class="mask" v-show="showMask" ref="mask"></div>
    </div>
</template>

<script>
export default {
    components: {},
    props: {},
    data() {
        return {
            showMask: false
        }
    },
    watch: {},
    computed: {},
    methods: {
        dragControllerDiv() {
            let _this = this
            var box = this.$refs.box
            let resize = this.$refs.drag
            let mask = this.$refs.mask
            resize.left = resize.offsetLeft
            // 鼠标按下事件
            resize.onmousedown = function(e) {
                var startX = e.clientX
                let boxWidth = box.offsetWidth

                _this.showMask = true
                _this.$nextTick(() => {
                    mask.onmousemove = function(e) {
                        let moveX = e.clientX
                        box.style.width =
                            boxWidth + resize.left - (moveX - startX) + "px"
                    }
                    // 鼠标松开事件
                    mask.onmouseup = function(evt) {
                        document.onmousemove = null
                        document.onmouseup = null
                        _this.showMask = false
                    }
                })
            }
        }
    },
    created() {},
    mounted() {
        this.dragControllerDiv()
    }
}
</script>
<style lang="stylus" scoped rel="stylesheet/stylus">
.drag-wrapper {
    position: relative;
    .drag {
        position: absolute;
        top: 0;
        bottom: 0;
        width: 20px;
        left: -10px;
        cursor w-resize
    }

    .mask {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        z-index: 9;
        cursor w-resize
    }

    main {
        flex: 1;
        display flex;
        height 100%

        &>* {
            width: 100%;
            height: 100%;
        }
    }
}
</style>