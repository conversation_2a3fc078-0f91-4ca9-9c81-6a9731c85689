<template>
  <div class="_css-mobile-materials-all">
    <div class="back-last">
      <div @click.stop="back"> 
        <span class="back-icon icon-arrow-left_outline"></span> 
        返回
      </div>
      <p>{{title.Name}}</p>
    </div>
    <div class='model-iframe'>
      <iframe :src="iframeSrc" frameborder="0" width="100%" height="100%" ></iframe>
    </div>
  </div>
</template>
<script>
import '@/assets/css/mobStyle.css';
export default {
  name:"ModelIframe",
  data() {
    return {
      title: '',
      iframeSrc: ''
    };
  },
  created(){
     
  },
  mounted() {
    this.title = JSON.parse(sessionStorage.getItem('modelListClickItem'))
    this.iframeSrc = `${window.bim_config.bimviewerurl}?projectId=${this.title.ProjectID}&model=${this.title.ID}&ver=`
  },
  filters:{
    
  },
  computed:{
    
  },
  methods: {
    back(){
      window.location.href = `${window.bim_config.hasRouterFile}/#/detailModelList`
    },
  },
  
};
</script>
<style scoped>
  ._css-mobile-materials-all{
    background: #F7F7F7;
    font-size: 0.85rem;
    height: 100%;
    display: flex;
    flex-direction: column;
  }
  .back-last{
    text-align: center;
    line-height: 3.33rem;
    height: 3.33rem;
    display: flex;
    align-items: center;
    justify-items: center;
    text-align: center;
    position: relative;    
    background: #FFFFFF;
    box-shadow: 0px 1px 5px 0px rgba(0, 0, 0, 0.1);
  }
  .back-last div{
    position: absolute;
    left: 0.3rem;
    
  }
  .back-last p{
    flex:1;
    text-align: center;
    max-width: 63%; 
    overflow-x: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    margin: 0 auto;
  } 
  .back-icon{
    vertical-align:middle;
  }
  .model-iframe{
    flex:1;
  }
</style>