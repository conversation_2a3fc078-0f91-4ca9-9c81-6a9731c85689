<template>
    <div class="_css-msharemodel-all">
        <!-- 完整的输入密码的对话框 -->
        <div v-if="!shared_modelObj"></div>
        <div v-else-if="shared_modelObj.bIsOutOfDate == true"
           class="_css-msharemodel-pwdinput"
                
            >
                <div
                class="_css-msharemodel-pwdinput-in"
                >
                    <div 
                    id="id_iconinputandbtn"
                    class="_css-iconinputandbtn">
                        <div class="_css-icon-title" >
                            <div class="_css-icon-title-image mulcolor-logo" ></div>
                            <div class="_css-icon-title-text" >
                                <div class="_css-icon-title-text-in" >BIMe协作平台</div>
                            </div>
                        </div>
                        <div class="_css-outtip-area" >
                            <div class="_css-outtip-area-in" >
                                <div  class="_css-outtip-image icon-interface-linkfailed" ></div>
                                <div class="_css-under-outdate-tip" >链接已失效，无法浏览BIM模型</div>
                            </div>                       
                        </div>
                    </div>
                </div>

                <div class="_css-msharemodel-bottomtip">配合净网行动，BIMe协作平台严厉打击色情低俗等不良信息的传播行为，如发现，将封禁账号</div>
        </div>
        <div 
            v-else-if="shared_modelObj.shm_haspwd == true"
            class="_css-msharemodel-pwdinput"
                
            >
                <div
                class="_css-msharemodel-pwdinput-in"
                >
                    <div 
                    id="id_iconinputandbtn"
                    class="_css-iconinputandbtn">
                        <div class="_css-icon-title" >
                            <div class="_css-icon-title-image mulcolor-logo" ></div>
                            <div class="_css-icon-title-text" >
                                <div class="_css-icon-title-text-in" >BIMe协作平台</div>
                            </div>
                        </div>
                        <div class="_css-inputandbtn" >
                            <div class="_css-inputpwdarea " >
                                <div class="_css-inputpwd-title" >请输入访问密码</div>
                                <input v-model="inputtingPwd" class="_css-inputpwd" type="text" />
                            </div>
                            <div class="_css-inputbtnarea " >
                                <div 
                                @click="preview($event)"
                                class="_css-btnok"
                                :class="{'_css-dis':previewDis()}"
                                 >浏览模型</div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="_css-msharemodel-bottomtip">配合净网行动，BIMe协作平台严厉打击色情低俗等不良信息的传播行为，如发现，将封禁账号</div>
            </div>
        <!-- //完整的输入密码的对话框 -->
        <div v-if="bShowModelPage" class="_css-modelshowing" >
            <div class="_css-modelshowing-top">
                <div
                @click="jump()"
                 class="_css-logo-instead mulcolor-logo" ></div>
                <div class="_css-modelshowing-toptext" >{{ModelName}}</div>
            </div>
            <div 
            id="id_iframe_parent"
            class="_css-modelshowing-body">
                <iframe 
                id="id_iframe_modelview"
                :src="bShowModelUrl"
                class="_css-modelshowing-iframe"></iframe>
            </div>
        </div>
    </div>
</template>
<script>
export default {
    data() {
        return {

            // 是否显示 BIMComposer2 模型
            // -------------------------
            bShowOld: false,

            ModelName: '加载中...',                // 模型名称
            bShowModelPage: false,        // 是否显示模型页面
            bShowModelUrl: 'about:blank', // 显示模型页面的地址

            ModelSessionId:false, // bimviewer相关的SessionId
            SessionId: '', 
            inputtingPwd: '', // 已经输入的密码
            bIsOutOfDate: false, // 是否超时
            shared_modelObj: undefined// 读取的分享数据
            ,extdata: {

            }
        };
    },

    watch: {
        bShowModelPage(type) {
            if (type) {
                let url = `${window.bim_config.webserverurl}/api/User/Model/AddLinkNumber?SessionId=${this.SessionId}&Token=${this.$staticmethod.Get('Token')}`
                this.$axios.post(url)
            }
        }
    },
    methods:{

        viewerurlget() {
            var _this = this;
            if (!_this.bShowOld) {

                try {_this.$staticmethod.consoleLog('显示为 window.bim_config.bimviewerurl');} catch (e) {}
                return window.bim_config.bimviewerurl;//   新版
            } else {

                try {_this.$staticmethod.consoleLog('显示为 window.bim_config.bimviewerurlold');} catch (e) {}
                return window.bim_config.bimviewerurlold;//   旧版
            }
        
        },

        // // 必须放在 _this.bShowModelPage = true; 前执行
        // // -------------------------------------------
        // a1pplyoldornewmodelurl(){
        //     var _this = this;
        //     if (_this.bShowOld) {
        //         _this.bShowModelUrl = _this.bShowModelUrl.replace(w1indow.bim_config.bimviewerurl, w1indow.bim_config.bimviewerurlold);
        //     }
        //      _this.$staticmethod.consoleLog('[share]_this.bShowModelUrl=' + _this.bShowModelUrl);
        // },

        jump(){
            window.open('http://www.probim.com.cn', '_blank');
        },

        // 获取参数值
        // ----------
        getQueryVariable(name) {
            var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)", "i");

            // 读取 ? 后面的部分
            // ----------------
            var lastQIndex = window.location.href.lastIndexOf('?');
            var str = window.location.href.substr(lastQIndex + 1);
            var r = str.match(reg);
            if (r != null) {
                return unescape(r[2]);
            }
            return null;
        } ,

        // 得到模型名称
        GetModelDetail(){

            var _this = this;
            var shmjsonobj = JSON.parse(_this.shared_modelObj.shm_json);
            if (shmjsonobj) {
                var _Url = `${
                    _this.$staticmethod.getBIMServer()
                }/api/Prj/GetModel?ProjectID=${shmjsonobj.ProjectID}&ModelID=${_this.shared_modelObj.shm_modelid}&Token=${this.$staticmethod.Get('Token')}`;
                _this.$axios.get(_Url).then(x => {
                    if (x.data && x.data.Name) {
                        _this.ModelName = x.data.Name;
                    }
                }).catch(x => {

                });
            }

        },

        RegModelLoading() {
            var _this = this;
            //     id="id_iframe_parent"
            // class="_css-modelshowing-body">
            //     <iframe 
            //     id="id_iframe_modelview"
            var ifrParent = document.getElementById('id_iframe_parent');
            // var _LoadingIns = _this.$loading({
            //     text: '加载中',
            //     target: ifrParent
            // });
            var modelwin = document.getElementById("id_iframe_modelview");

            // setTimeout(x => {
            //     modelwin.contentWindow.BI1Me.event.BIMeEvent.finishRender.subscribe(() => {
            //         _LoadingIns.close();
            //     });
            // }, 2000);

        },

        // 预览
        preview(){

            // 根据密码及 SessionId 请求服务器，得到结果并处理
            var _this = this;
            if (_this.previewDis()) {
                _this.$staticmethod.debugshowlog('未输入');
                return;
            }

            // 调用接口
            var _Url = `${window.bim_config.webserverurl}/api/User/Model/ValidatePwd?Token=${this.$staticmethod.Get('Token')}`;
            var _LoadingIns = _this.$loading({
                text: '处理中',
                target: document.getElementById('id_iconinputandbtn')
            });
            _this.$axios({
                method:'post',
                url: _Url,
                data: _this.$qs.stringify({
                    SessionId: _this.SessionId,
                    Pwd: _this.inputtingPwd
                })
            }).then(x => {
                if (x.data.Ret >0 ){
                    // 最好不是再发请求，直接得到一个 ModelSessionId
                    // 再发请求，得到相关参数，并设置 shared_modelObj.shm_haspwd 。
                    // 接口返回数据后，要修改 shm_haspwd 并返回额外的数据，否则手动修改 shm_haspwd 为true就直接显示模型了
                    // ModelSessionId
                    // 426d91f8-eab2-4d18-8001-a2cad04e95d6
               
                    if (x.data.Data) {
                        // debugger;
                        // window.loca1tion.href = `${w1indow.bim_config.bimviewerurl}?SessionID=${x.data.Data}`;
                        
                        var shmjsonobj = JSON.parse(x.data.Data.shm_json);

                        if (shmjsonobj) {

                            //debugger;
                            if (_this.shared_modelObj.shm_modelversion != null &&_this.shared_modelObj.shm_modelversion!=undefined) {
                            _this.bShowModelUrl = `${_this.viewerurlget()}?projectId=${
                                shmjsonobj.ProjectID
                            }&model=${_this.shared_modelObj.shm_modelid}&ver=${
                                _this.shared_modelObj.shm_modelversion
                            }`;
                            } else {
                                _this.bShowModelUrl = `${_this.viewerurlget()}?projectId=${
                                        shmjsonobj.ProjectID
                                    }&model=${_this.shared_modelObj.shm_modelid}`;
                            }

                            // if (_1this.bShowOld) {
                            //     _this.bShowModelUrl = _this.bShowModelUrl.replace(w1indow.bim_config.bimviewerurl, w1indow.bim_config.bimviewerurlold);
                            // }
                            //_this.a1pplyoldornewmodelurl();

                            _this.bShowModelPage = true;

                            

                            _this.$nextTick(x => {
                                _this.RegModelLoading();
                            })
                            
                        }






                    } else {

                    }
                } else {
                    _this.$message.error(x.data.Msg);
                }
                _LoadingIns.close();
            }).catch(x => {
                console.error(x);
                _LoadingIns.close();
            });

        },
        previewDis(){
            var _this = this;
            if (!_this.inputtingPwd || _this.inputtingPwd.length == 0) {
                return true;
            } else {
                return false;
            }
        }
    },
    mounted(){
        

        // 拿到用户输入的 SessionId 参数
        var _this = this;
        window.msharemvue = _this;
        var SessionId = _this.$route.params.SessionId;
        _this.SessionId = SessionId;

        // 读取参数值
        // ---------
        var showold = _this.getQueryVariable('isold');
        //console.log(showold);

        // 将 showold 显示到 bShowOld 变量上
        // --------------------------------
        if (showold) {
            _this.bShowOld = true;
        } else {
            _this.bShowOld = false;
        }

        // 拿到 SessionId 后，调用接口，获取数据
        var _Url = `${window.bim_config.webserverurl}/api/User/Model/GetModelShareConfig?SessionId=${SessionId}&Token=${this.$staticmethod.Get('Token')}`;
        var _LoadingIns = _this.$loading({
            text:'加载中'
        });
        _this.$axios.get(_Url).then(x => {
            if (x.data.Ret > 0) {

                // mounted 后，将得到的模型信息数据赋给 shared_modelObj
                _this.shared_modelObj = x.data.Data;

                // 得到模型名称
                _this.GetModelDetail();

                if (_this.shared_modelObj 
                    && _this.shared_modelObj.bIsOutOfDate != true 
                    && !_this.shared_modelObj.shm_haspwd
                    && _this.shared_modelObj.ModelSessionId.length
                    && _this.shared_modelObj.ModelSessionId.length == 36
                    ) {
                        //debugger;
                        // shm_modelid: "fef875e1-a913-4382-8bc5-9e4b79f811e7"
                        // shm_modelversion: null
                        // shm_projectid: "48617e7b-07f2-4748-9199-238af8f2bfc6" organizeId

                        //debugger;
                        var  shmjsonobj = JSON.parse(x.data.Data.shm_json);                 

                        if (shmjsonobj) {
                            if (_this.shared_modelObj.shm_modelversion != null &&_this.shared_modelObj.shm_modelversion!=undefined) {
                            _this.bShowModelUrl = `${_this.viewerurlget()}?projectId=${
                                shmjsonobj.ProjectID
                            }&model=${_this.shared_modelObj.shm_modelid}&ver=${
                                _this.shared_modelObj.shm_modelversion
                            }`;
                            } else {
                                _this.bShowModelUrl = `${_this.viewerurlget()}?projectId=${
                                        shmjsonobj.ProjectID
                                    }&model=${_this.shared_modelObj.shm_modelid}`;
                            }

                            // if (_1this.bShowOld) {
                            //     _this.bShowModelUrl = _this.bShowModelUrl.replace(w1indow.bim_config.bimviewerurl, w1indow.bim_config.bimviewerurlold);
                            // }
                            //_this.a1pplyoldornewmodelurl();

                            _this.bShowModelPage = true;

                            

                            _this.$nextTick(x => {
                                _this.RegModelLoading();
                            })
                        }

                        //window.loc1ation.href = `${w1indow.bim_config.bimviewerurl}?SessionID=${_this.shared_modelObj.ModelSessionId}`;
                        // projectId=46d11566-6b7e-47a1-ba5d-12761ab9b55c&model=fef875e1-a913-4382-8bc5-9e4b79f811e7&ver=
                        

                        
                    }

            } else {
                _this.$message.error(x.data.Msg);
            }
            _LoadingIns.close();
        }).catch(x => {
            console.error(x);
            _LoadingIns.close();
        });
        
    }
}
</script>
<style scoped>

._css-logo-instead {
    margin-right: 8px;
    background-repeat: no-repeat;
    background-position: center;
    background-size: contain;
    width: 24px;
    height: 24px;
}

._css-modelshowing-iframe {
    border:none;
    width:100%;
    height:100%;
}
._css-modelshowing-top {
    height:48px;
    box-sizing: border-box;
    background-color: #ecede8;
    display: flex;
    align-items: center;
    justify-content: center;
}
._css-modelshowing-topicon {
    width:24px;
    height:24px;    
    margin-right:8px;
    cursor:pointer;
}
._css-modelshowing-toptext {
    height:48px;
    line-height: 48px;
    font-size: 14px;    
}
._css-modelshowing-body {
    height:calc(100% - 48px);
    box-sizing: border-box;
}
._css-modelshowing {
    position:fixed;
    width:100%;
    height:100%;
    top:0;
    left:0;
    /* background-color: #fff; */
    background-color: #ecede8;
    z-index:2;
}
._css-outtip-image{
    width: 77px;
    height: 48px;
    font-size: 56px;
}
._css-outtip-area-in{
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-around;
    position: relative;
    align-items: center;
}
._css-under-outdate-tip{
    font-size: 12px;
    color:#757d8a;
}
._css-btnok{
    width:100%;
    height:32px;
    border-radius: 2px;
    background-color:#1890ff;
    line-height: 32px;
    color:#fff;
    cursor: pointer;
    position: absolute;
    bottom:0;
}

._css-btnok._css-dis{
    cursor:not-allowed;
    opacity: 0.3;
}
._css-inputpwd-title {
    position: absolute;
    font-size: 12px;
    color: #757d8a;
    top: 5px;
}
._css-inputpwd{
width: 96%;
    height: 32px;
    background-color: #f5f9fc;
    border-radius: 2px;
    border: none;
    outline: none;
    padding: 0 10px;
    box-sizing: border-box;
    position: absolute;
    bottom: 1px;
}
._css-inputpwdarea{
        flex: 1;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    justify-content: space-around;
    position: relative;
}
._css-inputbtnarea {
    width: 98px;
    position:relative;
}
._css-icon-title-image{
    background-size: contain;
    width: 114px;
    height: 100%;
    flex: none;
}
._css-icon-title{
    margin-bottom:25px;
    height:114px;
    border:1px solid transparent;
    display: flex;
    position: relative;
}
._css-icon-title-text {
    font-size: 23px;
    color: #999eaa;
    margin-left: 10px;
    flex: 1;
    text-align: center;
    position: relative;
}
._css-icon-title-text-in{
    position:absolute;
    width:100%;
    bottom:0;
    text-align: center;
}
._css-iconinputandbtn{
    bottom:10px;
    position:absolute;
}

._css-outtip-area{
    background-color: #ffffff;
    box-shadow: 0px 20px 75px -15px rgba(87, 89, 89, 0.5);
    border-radius: 2px;    
    box-sizing: border-box;
    width:360px;
    height:120px;
    display: flex;
}

._css-inputandbtn{
    background-color: #ffffff;
    box-shadow: 0px 20px 75px -15px rgba(87, 89, 89, 0.5);
    border-radius: 2px;
    padding: 20px 20px 30px;
    box-sizing: border-box;
    width:360px;
    height:120px;
    display: flex;
}
._css-msharemodel-bottomtip{
    font-size: 14px;
    color: #b0bcd0;
    position: fixed;
    bottom: 20px;
    width: 100%;
    text-align: center;
}
._css-msharemodel-pwdinput-in{
    width:400px;
    height:350px;
    border:1px solid transparent;
    border-radius:4px;
    display:flex;
    flex-direction: column;
    align-items: center;
    position:relative;
}
._css-msharemodel-pwdinput{
    position:fixed;
    top:0;
    left:0;
    width:100%;
    height:100%;
    background-color: rgba(255, 255, 255, 0.75);
    display: flex;
    justify-content: space-around;
    align-items: center;
}
</style>