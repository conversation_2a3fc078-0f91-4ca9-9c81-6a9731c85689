<template>
  <div class="document-share-index" v-loading="isLoading" element-loading-text="文件信息获取中...">
    <div class="top-bar">
      <el-row>
        <!-- <el-col :span="7" class="css-tal">
          <div :class="[breadCrumbData.length > 0 ? '' : 'visibility']">
            <span class="icon icon-interface-unfolder"></span>
            {{breadCrumbData.length > 0 ? breadCrumbData[breadCrumbData.length-1].FileName : '文件夹插画' }}
          </div>
          <CompsEloButton
            v-buttonClickRipple
            :width="96"
            :height="32"
            :fontSize="12"
            color="#fff"
            text="上传"
            class="notUploadFile"
            :extraclassobj="{'_css-issue-newissue':true,'css-mt24':true}"
          ></CompsEloButton>
        </el-col> -->

        <!-- <el-col :span="10" class="r_docsearch">
          <div class="css-h100">
            <div class="_css-keyword-inputbtn css-fc css-usn">
              <CompsUsersInput
                @oninput="_ondocnameinput"
                :placeholder="'搜索文档'"
                iconclass="icon-interface-search"
                class="search_doc_r"
              ></CompsUsersInput>
            </div>
          </div>
        </el-col> -->

        <el-col :span="12" class="share-msg" v-if="MobileYON == false">
          <img class="right" width="80" :src="qrCodeImg" alt="">
          <ul class="right">
            <li><span>分享时间：</span>{{ parentTableData.CreateTime }}</li>
            <li><span>有效时间：</span>{{ parentTableData.ShardDays == -1? '永久':  parentTableData.ShardDays+'天'}}</li>
            <li>
              <span>创建者：</span>
              <el-tooltip placement="bottom-start" :content="parentTableData.CreateUserName">
                <span>{{ parentTableData.CreateUserName }}</span>
              </el-tooltip>
            </li>
          </ul>
        </el-col>
      </el-row>
    </div>

    <div class="share-content">
      <!-- 面包屑 -->
      <div class="share-breadCrumb">
        <div class="_css-table-top-left css-h100 css-fc css-usn css-bsb css-miniscroll css-oxa css-flex1">
          <template v-if="breadCrumbData.length > 0">
            <div class="css-breadcrumb-item css-cp" @click="handleBreadCrumbEvent(breadCrumbData[breadCrumbData.length - 2],breadCrumbData.length - 2)">
              <label class="css-cp">返回上一级</label>
            </div>
            <div class="css-breadcrumb-splitter">/</div>
          </template>

          <div class="css-breadcrumb-item css-cp" @click="getShareDocumentData">
            <label data-debug="line 181" class="css-cp">项目文档</label>
          </div>


            <template v-if="MobileYON == false" v-for="(item,index) in breadCrumbData">
              <div class="css-breadcrumb-splitter">/</div>
              <div class="css-breadcrumb-item css-cp" @click="handleBreadCrumbEvent(item,index)">
                <label class="css-cp">{{ item.FileName }}</label>
              </div>
            </template>
        </div>
        <div class="files-statistics" v-if="MobileYON == false">共{{ searchTableData.length }}个文件(夹)，已全部加载</div>
      </div>
      <!-- //面包屑 -->

      <el-table
        ref="multipleTable"
        :data="searchTableData"
        tooltip-effect="dark"
        style="width: 100%;height: 100%;"
        class="_css-customstyle"
        @row-dblclick = "folderDblclick"
        @selection-change="handleSelectionChange">
        <!-- <el-table-column
          type="selection"
          width="55">
        </el-table-column> -->
        <el-table-column
          label="名称"
          width="">
          <!--<template slot-scope="scope">{{ scope.row.FileName }}</template>-->
          <template slot-scope="scope">
            <div class="about-file-msg">
              <i :class="'css-icon20 css-fs18 css-fc css-jcsa r_icon_style1 ' + $staticmethod.getIconClassByExtname(scope.row.ObjectName, scope.row.FileSize)"></i>
              <span
                @click.stop="previewFIle(scope.row)"
                :title="scope.row.ObjectName"
                class="css-cp css-hoverunder css-ml10 css-ellipsis css-w100 r_docname"
                style="webkitBoxOrient: vertical"
              >{{scope.row.ObjectName}}</span>
              <span class="menu-icon" v-if="parentTableData.HasDownload">
                <i v-show="scope.row.FileSize > 0 && parentTableData.HasDownload == true" @click.stop="downLoadFile(scope.row)" class="icon icon-interface-download-fill" title="下载"></i>
              </span>
            </div>
          </template>
        </el-table-column>
        <!-- <el-table-column
        v-if="MobileYON == false"
          prop="CreateUserName"
          label="创建人"
          show-overflow-tooltip
          width="120">
        </el-table-column> -->
        <el-table-column
        v-if="MobileYON == false"
          label="大小"
          width="120"
          show-overflow-tooltip>
          <template slot-scope="scope">{{ scope.row.FileSize + (scope.row.SizeUnit=="Byte"?'B':scope.row.SizeUnit) }}</template>
        </el-table-column>
        <el-table-column
        v-if="MobileYON == false"
          prop="CreateTime"
          width="200"
          label="更新日期"
          show-overflow-tooltip>
          <template slot-scope="scope">{{ scope.row.CreateTime}}</template>
        </el-table-column>
      </el-table>
    </div>

    <!-- idocview , dwg 预览 iframe 载体 -->
    <div class="_css-doc-preview" :class="extdata._show_idocview?'css-fc':'css-hide'">
      <div class="_css-doc-preview-beforeiframe">
        <div class="_css-doc-preview-beforeiframe-01"></div>
        <div class="_css-doc-preview-beforeiframe-02"></div>
        <div class="_css-doc-preview-beforeiframe-03"></div>
        <div
          class="icon-suggested-close"
          :class="'_css-doc-preview-closebtn-' + extdata._docviewtype"
          @click="close_idocview($event)"
        ></div>
        <div class="_css-doc-preview-beforeiframe-04 __web-inspector-hide-shortcut__"></div>
      </div>
      <iframe class="_css-doc-preview-iframe" :src="extdata._idocviewurl"></iframe>
    </div>
    <!-- //idocview , dwg 预览 iframe 载体 -->

  </div>
</template>

<script>
import CompsUsersInput from "@/components/CompsAuth/CompsUsersInput";
import CompsEloButton from "@/components/CompsElOverride/CompsEloButton";
export default {
  name: "DocShareDocList",
  components: {
    CompsUsersInput,
    CompsEloButton
  },
  data(){
    return {
      extdata:{
        _show_idocview: false,
        _docviewtype: 'office',
        _idocviewurl: 'about:blank'
      },

      basicData: {
        title: '北京东晨工元科技发展有限公司',
        loginBtn: true
      },

      parentTableData: {},
      searchTableData: [],
      tableData: [
      ],

      multipleSelection: [],
      isLoading: false,
      docIds: '',
      breadCrumbData: [],
      qrCodeImg: '',
      DownloadPermissions:false, //是否可以下载
      r_sharetime:'',  //分享时间
      r_endtime:'',   //有效时间
      MobileYON:false,   //是否为手机端,false为不是
      Password:'',
    }
  },

  created() {
    this.$emit('getBasicData',this.basicData);
    this.docIds = this.$route.query.DocIds;
    this.Password = sessionStorage.getItem('DocPassword')
    this.getShareDocumentData();
    this.setQRCode();
  },

  mounted(){
    var _this = this;
    window.opensharedocvue = _this;
    if (this._isMobile()) {
      this.MobileYON = true
    }
  },

  methods: {
    _isMobile(){
      let flag = navigator.userAgent.match(/(phone|pad|pod|iPhone|iPod|ios|iPad|Android|Mobile|BlackBerry|IEMobile|MQQBrowser|JUC|Fennec|wOSBrowser|BrowserNG|WebOS|Symbian|Windows Phone)/i)
      return flag;
    },
    close_idocview(ev){
      var _this = this;
      if (ev) {
        ev.stopPropagation();
      }

      //
      _this.extdata._show_idocview = false;
    },

    getDownloadUrl(data){
      var _this = this;
      //debugger;
      let filedownloadurl = `${window.bim_config.webserverurl}/api/v1/file/preview?FileId=${data.Id}&Version=1&UserId=${this.$staticmethod.Get("UserId")}&Token=${this.$staticmethod.Get('Token')}`
      return filedownloadurl;
    },

    downLoadFile(data) {
      //console.log(data,data.FileId);
      var _this = this;
      // console.log(data);return
      let _selDirs,_selFiles = ''
      if(data.ContentType == 1){
          _selFiles = data.ObjectId;
      }else{
          _selDirs = data.ObjectId
      }
      let DocProjectId = sessionStorage.getItem('DocProjectId');
      // var url = _this.getDownloadUrl(data);
      var url = `${window.bim_config.webserverurl}/api/v1/file/download-out-pack?projectId=${DocProjectId}&fileIds=${_selFiles}&folderIds=${_selDirs}&Token=${this.$staticmethod.Get('Token')}`;;

      //window.open(url);
      window.location.href = url;
    },

    previewFIle(data) {
      if (data.FileSize == 0) {
        //扩展名为空，说明是文件夹或者非法格式的文件，不予预览
        this.folderDblclick(data)
        return
      }

      var _this = this;
      //debugger;

      // 拿到下载链接
      // -----------
      var downloadurl = `${window.bim_config.webserverurl}/api/v1/file/preview-out?FileId=${data.ObjectId}&Version=1&UserId=${this.$staticmethod.Get("UserId")}&Token=${this.$staticmethod.Get('Token')}`


      var url_iframe_all;

      // 判断是否是dwg
      if (data.ObjectName.toLowerCase().indexOf(".dwg") > 0) {
        _this.extdata._docviewtype = 'dwg';
        url_iframe_all = `${
          _this.$configjson.dwgurl
        }/Home/Index2?dwgurlcfg=${encodeURIComponent(downloadurl)}&name=${
          data.ObjectName
        }`;
      } else {
        _this.extdata._docviewtype = 'office';
        console.log('从文档分享的页面中打开预览');
        url_iframe_all = _this.$staticmethod.getHuangNewcomputeViewUrl(downloadurl, data.ObjectName,data.FileExtension);
      }
      console.log(url_iframe_all,'==url_iframe_all',downloadurl)
      _this.extdata._idocviewurl = url_iframe_all;
      _this.extdata._show_idocview = true;
    },

    setQRCode(){
      let SessionId = sessionStorage.getItem('SessionId')
      let url = `${window.location.origin}${window.bim_config.hasRouterFile}/#/LinkShare/DocShare/Index?SessionId=${SessionId}`;
      this.qrCodeImg = `${this.$urlPool.QRCode}?encodedUrl=${encodeURIComponent(url)}&token=${this.$staticmethod.Get("Token")}`;
    },

    _ondocnameinput(str, unknown, ev){

      // 通过 ev 判断是否按的是回车
      var _this = this;
      if (ev.keyCode == 13) {

        // 将 str 作为关键字进行文档搜索
        this.searchTableData = [];
        this.tableData.forEach(item=>{
          if (item.FileName && (item.FileName.toLowerCase()).search((str.toLowerCase())) != -1) {
            this.searchTableData.push(item);
          }
        });
      }

    },

    handleSelectionChange(val) {
      this.multipleSelection = val;
    },

    getShareDocumentData() {
      this.isLoading = true;
      this.breadCrumbData = [];
      this.$axios({
        method: "post",
        headers:{
          'Content-Type':'application/json'
        },
        url: `${window.bim_config.webserverurl}/api/v1/share/publish`,
        data: JSON.stringify({
          ShardId: this.docIds ,
          Password: this.Password,
          SharDetailId: '',
          FolderId: '',
          ObjectName: '',
        })
      }).then(res=>{
        if (res.data.Ret == 1) {
          this.parentTableData = JSON.parse(JSON.stringify(res.data.Data.ShardInfo));
          this.searchTableData = JSON.parse(JSON.stringify(res.data.Data.Detail));
          this.tableData = res.data.Data.Detail;
        } else {
          this.$message.error(res.data.Msg);
        }
        this.isLoading = false;
      }).catch(res=>{
        this.$message.error('请求出错，可能是分享被取消或分享时间已过期');
        this.isLoading = false;
      })
    },

    //双击打开某个文件夹
    folderDblclick(row, column, event) {
      if (row.FileSize > 0) return false;

      this.getChildrenFiles(row);

      this.breadCrumbData.push(row);
    },

    //获取某文件夹下的文件
    getChildrenFiles(row) {
      let _this = this
      this.isLoading = true;

      this.$axios({
        method: "post",
        headers:{
          'Content-Type':'application/json'
        },
        url: `${window.bim_config.webserverurl}/api/v1/share/publish`,
        data: JSON.stringify({
          ShardId: this.docIds,
          Password: this.Password,
          SharDetailId: row.Id,
          FolderId: row.ObjectId,
          ObjectName: '',
        })
      }).then(res=>{
        // console.log(res,'打开文件夹');
        if (res.data.Ret == 1) {
          this.searchTableData = JSON.parse(JSON.stringify(res.data));
          this.tableData = res.data;
        } else {
          this.$message.error(res.data.Msg);
        }
        this.isLoading = false;
      }).catch(res=>{
        console.warn(`请求失败${res}`);
      })
    },

    //导航
    handleBreadCrumbEvent(item,index) {
      if (index == this.breadCrumbData.length - 1) {
        return false;
      } else if (index < 0) {
        this.getShareDocumentData();
      } else {
        let len = this.breadCrumbData.length - index;
        this.getChildrenFiles(item);
        if (index == 0) {
          len = this.breadCrumbData.length;
        }
        this.breadCrumbData.splice(index + 1,len);
      }

    },

  },

  filters: {
    filesByteConversion(data) {
      if (!data) return '';

      let num = 1024.00;
      if (data < num) {
        return data + "B";
      } else if (data < Math.pow(num, 2)) {
        return (data / num).toFixed(2) + "K";
      } else if (data < Math.pow(num, 3)) {
        return (data / Math.pow(num, 2)).toFixed(2) + "M";
      } else if (data < Math.pow(num, 4)) {
        return (data / Math.pow(num, 3)).toFixed(2) + "G";
      } else {
        return (data / Math.pow(num, 4)).toFixed(2) + "T";
      }

    }
  }
}
</script>

<style>
.document-share-index .share-content ._css-customstyle .el-table__body-wrapper {
  overflow: auto;
  overflow-x: hidden;
  height: calc(100% - 70px);

}
</style>

<style scoped>
@media screen and (max-device-width: 450px) {
    .document-share-index .top-bar .search_doc_r{
      margin-left: 4.5rem;
    }
    .share-breadCrumb{
      line-height: 2rem;
      margin-left: 0.5rem;
    }
    .r_icon_style1{
      float: left;
    }
    .r_docname{
      white-space: normal;
      width: 78%;
      display: -webkit-box;
      overflow: hidden;
      text-overflow: ellipsis;
      -webkit-line-clamp: 1;
      float: left;
    }
    .share-content ._css-customstyle .about-file-msg {
      width: 100%;
      display: flex;
      align-items: center;
    }
    .share-content ._css-customstyle .about-file-msg .menu-icon {
      min-width: 20px;
      float: right;
      line-height: 2rem;
    }
    .share-content ._css-customstyle .about-file-msg .menu-icon .icon {
      cursor: pointer;
      vertical-align: middle;
    }
    .r_docsearch{
      margin-top:0;
    }
    .document-share-index {
  height: 100%;
}

.document-share-index .top-bar {
  box-sizing: border-box;
  width: 100%;
  padding: 1rem 0;
  background:rgba(255,255,255,1);
  box-shadow:0 1px 1px 0 rgba(0,21,41,0.12);
}

.document-share-index .top-bar .notUploadFile {
  cursor: not-allowed;
  background-color: #d0cece !important;
}

.document-share-index .top-bar .el-row {
  width: 100%;
  margin: 0 auto;
}

.document-share-index .top-bar .css-tal .icon {
  vertical-align: sub;
  color: rgba(86, 167, 255,1);
  margin-right: 5px;
}

.document-share-index .top-bar ._css-btn-upload {
  width: 96px;
  height: 32px;
  border-radius:2px;
}

.document-share-index .top-bar ._css-keyword-inputbtn {
  display: flex;
  justify-content: center;
}
.document-share-index .share-msg{
  float: right;
  margin-right: 80px;
}
.document-share-index .share-msg img {
  width: 94px;
}

.document-share-index .share-msg ul {
  padding: 0;
  margin: 0;
}

.document-share-index .share-msg ul li {
  list-style: none;
  text-align: right;
  font-size: 12px;
  font-weight: 400;
  color: rgba(0,0,0,0.45);
  padding: 5px;
  max-width: 200px;
  overflow-x: hidden;
  white-space: nowrap;
}

/*下方导航和文件列表*/
.share-content {
  width: 96%;
  height: calc(100% - 114px);
  margin: 0 auto;
}


.share-content .share-breadCrumb .files-statistics {
  position: absolute;
  top: 0;
  right: 0;
}

.share-content ._css-customstyle .about-file-msg:hover .menu-icon .icon {
  display: inline-block;
}

.visibility {
  visibility: hidden
}
._css-doc-preview-iframe {
  width: 99%;
  height: 99%;
}
 ._css-doc-preview-closebtn-,
  ._css-doc-preview-closebtn-office, ._css-doc-preview-closebtn-dwg {
    top: 0px;
    right: 0px;
    line-height: 30px;
    background-color: rgba(255,255,255,0.45);
  }

}
@media screen and (min-device-width: 451px) {
.document-share-index {
  height: 100%;
}

.document-share-index .top-bar {
  padding: 18px 0;
  box-sizing: border-box;
  width: 100%;
  height: 114px;
  background:rgba(255,255,255,1);
  box-shadow:0 1px 1px 0 rgba(0,21,41,0.12);
  margin-bottom: 20px;
}

.document-share-index .top-bar .notUploadFile {
  cursor: not-allowed;
  background-color: #d0cece !important;
}

.document-share-index .top-bar .el-row {
  width: 1000px;
  margin: 0 auto;
}

.document-share-index .top-bar .css-tal .icon {
  vertical-align: sub;
  color: rgba(86, 167, 255,1);
  margin-right: 5px;
}

.document-share-index .top-bar ._css-btn-upload {
  width: 96px;
  height: 32px;
  border-radius:2px;
}

.document-share-index .top-bar ._css-keyword-inputbtn {
  display: flex;
  justify-content: center;
}
.document-share-index .top-bar .search_doc_r{
  width: 100%;
  max-width: 275px;
}
.document-share-index .share-msg{
  float: right;
  margin-right: 80px;
}
.document-share-index .share-msg img {
  width: 94px;
}

.document-share-index .share-msg ul {
  padding: 0;
  margin: 0;
}

.document-share-index .share-msg ul li {
  list-style: none;
  text-align: right;
  font-size: 12px;
  font-weight: 400;
  color: rgba(0,0,0,0.45);
  padding: 5px;
  max-width: 200px;
  overflow-x: hidden;
  white-space: nowrap;
}

/*下方导航和文件列表*/
.share-content {
  width: 1000px;
  height: calc(100% - 114px);
  margin: 0 auto;
  padding: 15px 25px;
}

.share-content .share-breadCrumb {
  height: 20px;
  position: relative;
  margin-bottom: 10px;
  padding-right: 210px;
}

.share-content .share-breadCrumb .files-statistics {
  position: absolute;
  top: 0;
  right: 0;
}

.share-content ._css-customstyle .about-file-msg {
  width: 100%;
  display: flex;
  align-items: center;
}

.share-content ._css-customstyle .about-file-msg .menu-icon {
  min-width: 20px;
}

.share-content ._css-customstyle .about-file-msg .menu-icon .icon {
  display: none;
  cursor: pointer;
  vertical-align: middle;
}

.share-content ._css-customstyle .about-file-msg:hover .menu-icon .icon {
  display: inline-block;
}

.visibility {
  visibility: hidden
}
.r_docsearch{
  margin-top:47px;
}
._css-doc-preview-iframe {
  width: 99%;
  height: 99%;
}
 ._css-doc-preview-closebtn-,
  ._css-doc-preview-closebtn-office, ._css-doc-preview-closebtn-dwg {
    top: 0px;
    right: 0px;
    line-height: 30px;
    background-color: rgba(255,255,255,0.45);
  }

}
</style>
<style scoped>
._css-doc-preview {
  z-index: 70000;
  position: fixed;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  background-color: rgba(0, 0, 0, 0.45);
  opacity: 1;
  justify-content: space-around;
}
._css-doc-preview-iframe {
  height: 80%;
  width: 80%;
  border-width: 0;
  background-color: #fff;
}
._css-doc-preview-iframe._css-previewfull {
  height: 100%;
  width: 100%;
}
._css-docpreview-newtab {
  font-size: 20px;
  flex: none;
  width: 30px;
  height: 30px;
  position: fixed;
  background-repeat: no-repeat;
  cursor: pointer;
  color: rgba(0, 0, 0, 0.4);
  border: 1px solid transparent;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.8);
  right: calc(10% + 55px);
  top: calc(10% - 15px);
  line-height: 30px;
}

._css-docpreview-fullscreen {
  font-size: 20px;
  flex: none;
  width: 30px;
  height: 30px;
  position: fixed;
  background-repeat: no-repeat;
  cursor: pointer;
  color: rgba(0, 0, 0, 0.4);
  border: 1px solid transparent;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.8);
  right: calc(10% + 20px);
  top: calc(10% - 15px);
  line-height: 30px;
}

._css-doc-preview-closebtn-,
._css-doc-preview-closebtn-office {
  font-size: 20px;
  flex: none;
  width: 30px;
  height: 30px;
  position: fixed;
  background-repeat: no-repeat;
  cursor: pointer;
  color: rgba(0, 0, 0, 0.4);
  border: 1px solid transparent;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.8);
  right: calc(10% - 15px);
  top: calc(10% - 15px);
  line-height: 30px;
}

._css-doc-preview-closebtn-dwg {
  font-size: 20px;
  flex: none;
  width: 30px;
  height: 30px;
  position: fixed;
  background-image: url(/Content/images/ProjectManage/Project/close_normal.png);
  background-repeat: no-repeat;
  cursor: pointer;
  color: rgba(0, 0, 0, 0.4);
  border: 1px solid transparent;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.8);
  right: calc(10% - 15px);
  top: calc(10% - 15px);
  line-height: 30px;
}
._css-canfull._css-isfulling {
  top:0;
  right: 0px;
}
._css-docpreview-newtab._css-isfulling {
  right: 68px;
}
._css-docpreview-fullscreen._css-isfulling {
  right: 34px;
}
._css-doc-preview-beforeiframe {
  position: fixed;
  width: 30px;
  height: 40px;
  top: 0;
  right: 35px;
  background-color: transparent;
  display: flex;
  align-items: center;
  font-family: "微软雅黑";
}
._css-doc-preview-beforeiframe-01 {
  flex: none;
  width: 20px;
}
._css-doc-preview-beforeiframe-02 {
  flex: none;
  font-size: 20px;
  font-weight: normal;
  font-stretch: normal;
  letter-spacing: 0px;
  color: #ffffff;
}
._css-doc-preview-beforeiframe-03 {
  flex: 1;
  height: 100%;
}
._css-doc-preview-beforeiframe-04 {
  flex: none;
  width: 25px;
  height: 100%;
}
</style>
