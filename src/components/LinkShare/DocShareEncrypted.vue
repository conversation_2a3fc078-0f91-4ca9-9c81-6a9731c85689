<template>
    <div class="encrypted-dialog">
      <div class="title">
        <span class="css-mr8 _css-currentuser-icon">{{ encryptedInformation.CreateUserName | setAbbreviation }}</span>
        {{ encryptedInformation.CreateUserName }}给你分享了加密文件
      </div>
      <form @submit.prevent="linkToShareIndex">
        <el-input class="passWord" prefix-icon="icon-interface-lock" show-password placeholder="请输入密码" v-model="text"></el-input>
      </form>
      <CompsEloButton
        v-buttonClickRipple
        :width="352"
        :height="40"
        :fontSize="12"
        color="#fff"
        text="确定"
        :extraclassobj="{'_css-issue-newissue':true,'submit':true}"
        @onclick="linkToShareIndex"
      ></CompsEloButton>
    </div>
</template>

<script>
  import CompsEloButton from "@/components/CompsElOverride/CompsEloButton";
    export default {
      name: "DocShareEncrypted",
      components: {
        CompsEloButton
      },

      data() {
          return {
            text: '',
            basicData: {
              title: 'BIMe协作平台',
              loginBtn: false
            },

            SessionId: '',
            encryptedInformation: {},
          }
      },

      created() {
        this.$emit('getBasicData',this.basicData);
        this.getParameters();
      },

      methods: {
        getParameters() {

          this.SessionId = this.$route.query.SessionId;

          let data = sessionStorage.getItem('encryptedInformation');
          this.encryptedInformation = JSON.parse(data);
        },

        //验证密码 跳转到分享页面
        linkToShareIndex() {
          if (this.text.replace(/(^\s*)|(\s*$)/g, "") == '') {
            this.$message.warning('密码不可为空格或为空');
            return false;
          }

          // let fd = new FormData();
          // fd.append("SessionId",this.SessionId);
          // fd.append("Pwd",this.text);
          // this.$axios.post(`${window.bim_config.webserverurl}/api/User/Document/ValidPwd`,fd)
          this.$axios({
            method: "post",
            headers:{
              'Content-Type':'application/json'
            },
            url: `${window.bim_config.webserverurl}/api/v1/share/publish`,
            data: JSON.stringify({
              ShardId: this.SessionId,
              Password: this.text,
              SharDetailId: '',
              FolderId: '',
              ObjectName: '',
            })
          }).then(res=>{
            // console.log(res);
            if (res.data.Ret == 1) {
              //密码正确
              sessionStorage.setItem('Verified',JSON.stringify({key:this.SessionId,value:this.text}));

              this.$router.push({
                path: '/LinkShare/DocShare/docList',
                query: {
                  DocIds: this.encryptedInformation.Id
                }
              });
            } else {
              this.$message({
                type: 'warning',
                message: `${res.data.Msg}`,
              });
              return false;
            }
          })
        },

        authenticationEncryption() {

          this.isLoading = true;

          this.$axios.get(`${window.bim_config.webserverurl}/api/Document/Doc/TestDocSharePrivate?SessionId=${SessionId}&Token=${this.$staticmethod.Get('Token')}`).then(res=>{
            this.isLoading = false;

            if (res.data.Ret == 1) {
              if (res.data.Data.IsPublic) {
                // this.$router.push({name:'DocShareIndex'});
                this.$router.push({
                  path: '/LinkShare/DocShare/docList',
                  query: {
                    DocIds: res.data.Data.DocIds
                  }
                });
              } else {
                // this.$router.push({name:'DocShareEncrypted'});
                this.$router.push({
                  path: '/LinkShare/DocShare/verify',
                  query: {
                    SessionId: SessionId
                  }
                });
              }

            } else {
              this.$message.error('请求出错，请稍后再试');
            }

          }).catch(res=>{
            console.warn(`请求失败${res}`);
          })
        }
      }
    }
</script>

<style>
  .encrypted-dialog .passWord input::-webkit-input-placeholder {
    font-size: 14px;
    font-weight: 400;
    color: rgba(0,0,0,0.45);
  }

  .encrypted-dialog .passWord .el-input__prefix {
    line-height: 47px;
    color: rgba(0,0,0,0.45);
  }
</style>

<style scoped>
.encrypted-dialog {
  width: 400px;
  height: 260px;
  position: absolute;
  left: 50%;
  top: 40%;
  margin-left: -200px;
  margin-top: -130px;
  padding: 40px 24px;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
  text-align: left;
  background: rgba(255,255,255,1);
  box-shadow: 0 13px 24px -17px rgba(11,41,62,0.8);
  border-radius: 2px;
  overflow: hidden;
}

.encrypted-dialog .title {
  font-weight:400;
  margin-bottom: 45px;
}

.encrypted-dialog .title ._css-currentuser-icon {
  width: 32px;
  height: 32px;
  border-radius: 4px;
  display: inline-block;
  line-height: 32px;
  background-color: #282e3d;
  color: #fff;
  font-size: 12px;
  cursor: pointer;
  user-select: none;
  text-align: center;
}

.encrypted-dialog .passWord {
  margin-bottom: 20px;
  border-radius:2px;
  background-color: rgba(0,0,0,0.02);
}

.encrypted-dialog .submit {
  width: 100% !important;
  line-height: 40px;
  color: #FFFFFF;
  border-radius:2px;
  background-color: rgba(24,144,255,1);
}

.encrypted-dialog ._css-all._css-issue-newissue.submit {
  margin: 0;
}
</style>
