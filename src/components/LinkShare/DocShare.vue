<template>
  <div class="docShare">



  <div  class="share-top-head">
    <span  class="css-mr5 mulcolor-logo" style="width: 16px;height: 16px;background-size: contain;">
      &nbsp;&nbsp;&nbsp;&nbsp;
    </span>
    <span> {{ basicData.title }}</span>
       <!-- <el-button v-if="basicData.loginBtn" class="login" @click="toLogin">登录</el-button> -->
  </div>






    <!-- <div class="share-top-head">
      <div class="css-mr5 mulcolor-logo" style="width:30px;height:30px;" ></div>
      {{ basicData.title }}
      <el-button v-if="basicData.loginBtn" class="login" @click="toLogin">登录</el-button>
    </div> -->





    <div class="share-content">
      <p class="padding-top-15" v-if="isLoading">文件信息获取中...</p>
      <router-view @getBasicData = "getBasicData"></router-view>
    </div>
  </div>
</template>

<script>
    export default {
      name: "DocShare",
      data() {
        return {
          basicData: {
            title: '',
            loginBtn: false
          },
          isLoading: true,
        }
      },

      created() {
        this.authenticationEncryption();
      },

      methods: {
        getBasicData(data) {
          this.basicData.title = data.title;
          this.basicData.loginBtn = data.loginBtn;
        },

        toLogin() {
          this.$router.push('/');
        },

        authenticationEncryption() {
          // let hasSessionId = sessionStorage.hasOwnProperty('SessionId');

          let SessionId,Password,DocProjectId,token,userId = '';
          console.log(this.$route.query,'====this.$route.query')
          SessionId = this.$route.query.SessionId;
          Password = this.$route.query.DocPassword;
          DocProjectId = this.$route.query.DocProjectId;
          token = this.$route.query.token;
          userId = this.$route.query.userId;
          this.$staticmethod.Set("UserId", userId);
          if (SessionId == '' || SessionId == undefined) {
            SessionId = sessionStorage.getItem('SessionId');
            Password = sessionStorage.getItem('DocPassword');
            DocProjectId = sessionStorage.getItem('DocProjectId');
          } else {
            sessionStorage.setItem('SessionId',SessionId);
            sessionStorage.setItem('DocPassword',Password);
            sessionStorage.setItem('DocProjectId',DocProjectId);
          }
          this.isLoading = true;

          // this.$axios.get(`${window.bim_config.webserverurl}/api/Document/Doc/TestDocSharePrivate?SessionId=${SessionId}`)
          // this.$axios.get(`${window.bim_config.webserverurl}/api/v1/share/info?id=${SessionId}`)
          this.$axios({
            method: "post",
            headers:{
              'Content-Type':'application/json'
            },
            url: `${window.bim_config.webserverurl}/api/v1/share/publish`,
            data: JSON.stringify({
              ShardId: SessionId,
              Password: Password,
              SharDetailId: '',
              FolderId: '',
              ObjectName: '',
            })
          })
          .then(res=>{
            console.log(1);
            this.isLoading = false;
            // console.log(res);
            let hasVerified = sessionStorage.hasOwnProperty('Verified');
            let Verified = {};
            if (hasVerified) {
              Verified = JSON.parse(sessionStorage.getItem('Verified'));
            }
            // sessionStorage.setItem('r_TestDocSharePrivate',JSON.stringify(res.data.Data));//获取分享权限：上传、下载、有效时间
            if (res.data.Ret == 1) {
              // sessionStorage.setItem('bimcomposerId',res.data.Data.BIMComposerId);
              if (res.data.Data.ShardInfo.HasPassword && Verified.key !== SessionId) {
                // this.$router.push({name:'DocShareEncrypted'});
                //私密文件 保存相关数据
                sessionStorage.setItem('encryptedInformation',JSON.stringify(res.data.Data.ShardInfo));
                this.$router.push({
                  path: '/LinkShare/DocShare/verify',
                  query: {
                    SessionId: SessionId
                  }
                });
              } else {
                // this.$router.push({name:'DocShareIndex'});
                this.$router.push({
                  path: '/LinkShare/DocShare/docList',
                  query: {
                    DocIds: SessionId
                  }
                });
              }

            } else {
              this.$message.error('请求出错，请稍后再试');
            }

          }).catch(res=>{
            console.warn(`请求失败${res}`);
          })
        }
      }
    }
</script>

<style scoped>
  /*头部*/
  .docShare {
    height: 100%;
    background-color: #FFFFFF;
  }

  .docShare .share-top-head {
    position: relative;
    z-index: 1;
    padding: 0 15px;
    line-height: 64px;
    text-align: left;
    font-weight:500;
    box-shadow: 0 1px 3px 0 rgba(0,21,41,0.12)
  }

  .docShare .share-top-head .login {
    width: 70px;
    height: 32px;
    float: right;
    margin-top: 16px;
    font-weight:500;
    color:rgba(0,0,0,1);
    border-radius:2px;
    border: none;
    background-color: rgba(0,0,0,0.04);
  }

  .docShare .share-top-head img {
    vertical-align: middle;
    width: 30px;
  }

  .docShare .share-content {
    position: relative;
    height: calc(100% - 64px);
    background-color: #F0F2F5;
  }
</style>
