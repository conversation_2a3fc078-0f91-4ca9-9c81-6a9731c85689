<template>
  <div class="document-share-index">
    <div class="top-bar">
      <el-row>
        <el-col :span="7" class="css-tal">
          <div><span class="icon icon-interface-unfolder"></span>文件夹插画</div>
          <CompsEloButton
            v-buttonClickRipple
            :width="96"
            :height="32"
            :fontSize="12"
            color="#fff"
            text="上传"
            :extraclassobj="{'_css-issue-newissue':true,'css-mt24':true}"
          ></CompsEloButton>
        </el-col>

        <el-col :span="10" style="margin-top:45px;">
          <div class="css-h100">
            <div class="_css-keyword-inputbtn css-fc css-usn">
              <CompsUsersInput
                @oninput="_ondocnameinput"
                :placeholder="'搜索文档'"
                :width="'100%'"
                :maxWidth="'275px'"
                iconclass="icon-interface-search"
              ></CompsUsersInput>
            </div>
          </div>
        </el-col>

        <el-col :span="7" class="share-msg">
          <img class="right" width="80" src="https://www.probim.cn:8080/api/User/Document/QRCode?encodedUrl=https%3A%2F%2Fwww.probim.cn%3A8076%2FLinkShare%2FDocShare%2FIndex%3FSessionId%3Dad1a5e2e-cdb9-40eb-8e1c-ea921b3ba3d7" alt="">
          <ul class="right">
            <li><span>分享时间：</span>2019/09/09 22:22</li>
            <li><span>有效时间：</span>永久</li>
            <li><span>分享者：</span>王海彬</li>
          </ul>
        </el-col>
      </el-row>
    </div>

    <div class="share-content">
      <!-- 面包屑 -->
      <div class="share-breadCrumb _css-table-top-left css-h100 css-fc css-usn css-bsb css-miniscroll css-oxa css-flex1">
        <div class="css-breadcrumb-item css-cp">
          <label class="css-cp">返回上一级</label>
        </div>

        <div class="css-breadcrumb-splitter">/</div>

        <div class="css-breadcrumb-item css-cp">
          <label data-debug="line 181" class="css-cp">项目文档</label>
        </div>

        <div class="css-breadcrumb-splitter">/</div>

        <div class="css-breadcrumb-item css-cp">
          <label class="css-cp">二级文件夹</label>
        </div>

        <div class="files-statistics">共987654321个文件，已全部加载</div>
      </div>
      <!-- //面包屑 -->

      <el-table
        ref="multipleTable"
        :data="tableData"
        tooltip-effect="dark"
        style="width: 100%"
        class="_css-customstyle"
        @selection-change="handleSelectionChange">
        <el-table-column
          type="selection"
          width="55">
        </el-table-column>
        <el-table-column
          label="名称"
          width="">
          <template slot-scope="scope">{{ scope.row.name }}</template>
        </el-table-column>
        <el-table-column
          prop="creater"
          label="创建人"
          width="120">
        </el-table-column>
        <el-table-column
          prop="size"
          label="大小"
          width="120"
          show-overflow-tooltip>
        </el-table-column>
        <el-table-column
          prop="date"
          width="200"
          label="更新日期"
          show-overflow-tooltip>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script>
import CompsUsersInput from "@/components/CompsAuth/CompsUsersInput";
import CompsEloButton from "@/components/CompsElOverride/CompsEloButton";
export default {
  name: "DocShareIndex",
  components: {
    CompsUsersInput,
    CompsEloButton
  },
  data(){
    return {
      basicData: {
        title: '北京东晨工元科技发展有限公司',
        loginBtn: true
      },

      tableData: [
        {
          name: '这是文件夹',
          creater: '陈亚博',
          size: '1.1M',
          date: '2016-05-03',
        },
        {
          name: '这是一个文件',
          creater: '陈亚博',
          size: '23321kB',
          date: '2016-05-02',
        }
      ],

      multipleSelection: [],
    }
  },

  created() {
    this.$emit('getBasicData',this.basicData);
  },

  methods: {
    _ondocnameinput(str, unknown, ev){

      // 通过 ev 判断是否按的是回车
      var _this = this;
      if (ev.keyCode == 13) {

        // 将 str 作为关键字进行文档搜索
      }

    },

    handleSelectionChange(val) {
      this.multipleSelection = val;
    }
  },
}
</script>

<style scoped>
.document-share-index .top-bar {
  padding: 18px 0;
  box-sizing: border-box;
  width: 100%;
  height: 114px;
  background:rgba(255,255,255,1);
  box-shadow:0 1px 1px 0 rgba(0,21,41,0.12);
  margin-bottom: 20px;
}

.document-share-index .top-bar .el-row {
  width: 1000px;
  margin: 0 auto;
}

.document-share-index .top-bar .css-tal .icon {
  vertical-align: sub;
  color: rgba(86, 167, 255,1);
  margin-right: 5px;
}

.document-share-index .top-bar ._css-btn-upload {
  width: 96px;
  height: 32px;
  border-radius:2px;
}

.document-share-index .top-bar ._css-keyword-inputbtn {
  display: flex;
  justify-content: center;
}

.document-share-index .share-msg img {
  width: 80px;
}

.document-share-index .share-msg ul {
  padding: 0;
  margin: 0;
}

.document-share-index .share-msg ul li {
  list-style: none;
  text-align: right;
  font-size: 12px;
  font-weight: 400;
  color: rgba(0,0,0,0.45);
  padding: 5px;
}

/*下方导航和文件列表*/
.share-content {
  width: 1000px;
  margin: 0 auto;
  padding: 15px 25px;
}

.share-content .share-breadCrumb {
  height: 20px;
  position: relative;
  margin-bottom: 10px;
  padding-right: 210px;
}

.share-content .share-breadCrumb .files-statistics {
  position: absolute;
  right: 0;
}
</style>
