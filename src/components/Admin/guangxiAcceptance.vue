<template>
	<div class="_css-materials-all" ref="cssMaterialsAll">
		<div class="_css-bottomarea">
			<div class="_css-bottomlist" v-if="tableClose">
				<div class="_css-materialtypelist-head">
					<div class="_css-materhead-text">工程单元验收</div>
					<div  class="_css-btnimport _css-change-addnew" @click.stop="clickTreeOpenOrClose">
						<div >{{treeOpenOrCloseText}}</div>
					</div>
				</div>
				<div class="_css-materialtypelist css-miniscroll">
					<template>
						<div class="_css-treearea">
							<el-tree
								:data="leftTreeList"
								:props="treem_props"
								lazy
								ref="tree"
								class="_css-customstyle"
								:expand-on-click-node="false"
								@node-collapse="node_collapse"
								@node-expand="node_expand"
								:load="treefunc_loadChild"
								:default-expand-all="true"
								node-key="bmc_guid"
								:highlight-current="true"
								:auto-expand-parent="true"
								:default-expanded-keys="treem_default_expanded_keys"
								@current-change="treefunc_current_change"
							>
								<span
									class="css-fc _css-treenode-content"
									slot-scope="{ node, data }"
								>
									<i
										class="css-icon20 css-fs18 css-fc css-jcsa"
										:class="data.classname"
									></i>
									<span :title="node.label" class="css-ml4 _css-treenodelabel">
										<div class="_css-treenodellabelname">
											{{ data.bmc_name }}
										</div>
									</span>
								</span>
							</el-tree>
						</div>
					</template>
				</div>
			</div>
			<div class="_css-choose-switch-parent">
				<div class="_css-choose-switch" @click.stop="chooseCloseOrOpen">
					<i
						class="pro-in-out"
						:class="tableClose ? 'pro-in-out' : 'p-out'"
					></i>
				</div>
			</div>
			<div class="_css-bottomtable" >
				<div class="_css-materialtypetab-body">
					<div class="left-table-list" :class=" cacheModelID.length > 0 ? '' : 'width-100'">
						<div class="_css-materialtypetab-head">
							<span class="_css-click-close-or-open">
								<i
									:class="
										leftRightHide
											? 'icon-interface-toleft'
											: 'icon-interface-toright'
									"
								></i>
							</span>
							<div>{{ clickMaterialTypeName }}</div>
						</div>
						<el-table
							v-loading="elTableLoading"
							element-loading-text="数据加载中..."
							element-loading-spinner="el-icon-loading"
							element-loading-background="rgba(0, 0, 0, 0)"
							ref="doctable"
							@current-change="handleCurrentChange"
							highlight-current-row
							:border="true"
							:stripe="false"
							use-virtual
							:height="windowHeight"
							:data="extdata.tableData"
							style="width: 100%"
							:default-sort="{ prop: 'date', order: 'descending' }"
							class="_css-table-ele css-scroll  _css-qualitystyle"
							:row-class-name="tableRowClassName"
							:header-cell-style="{ 'background-color': 'transparent' }"
							:row-height="rowHeight"
						>
							<el-table-column
								:resizable="true"
								class="_css-col-filename"
								prop="materialname"
								label="工程划分"
								min-width="30"
							>
								<template slot-scope="scope">
									<template>
										<span
											class="table-text css-ellipsis basic-font-color-emphasize"
											>{{ scope.row.MaterialName }}</span
										>
									</template>
								</template>
							</el-table-column>
							<el-table-column
								:resizable="false"
								class="_css-col-relmodel"
								label="关联模型"
								width="90"
							>
								<template slot-scope="scope">
									<div class="table-text" v-if="scope.row.bhaselerel == 0">未关联</div>
									<div
										class="table-text _css-table-relele"
										v-else
										@click.stop="openAssociationModelComponents(scope.row)"
									>已关联
									</div>
									
								</template>
							</el-table-column>
							<el-table-column
								:resizable="false"
								class="_css-col-relmodel"
								label="验评开始时间"
								prop="EvaluationStart"
								min-width="30"
							>
								<template slot-scope="scope">
									<div class="table-text" v-if="scope.row.EvaluationStart">{{ scope.row.EvaluationStart | flttimeshorter}}</div>
									<div class="table-text" v-else>-</div>
								</template>
							</el-table-column>
							<el-table-column
								:resizable="false"
								class="_css-col-relmodel"
								label="验评结束时间"
								prop="EvaluationEnd"
								min-width="30"
							>
								<template slot-scope="scope">
									<div class="table-text" v-if="scope.row.EvaluationEnd ">{{ scope.row.EvaluationEnd | flttimeshorter}}</div>
									<div class="table-text" v-else>-</div>
								</template>
							</el-table-column>
							<el-table-column
								:resizable="false"
								class="_css-col-relmodel"
								label="验评结果"
								prop="EvaluationResult"
								width="90"
							>
								<template slot-scope="scope">
									<div class="table-text">{{ scope.row.EvaluationResult | filterEvaluationResult }}</div>
								</template>
							</el-table-column>
							<el-table-column
								:resizable="false"
								class="_css-col-relmodel"
								label="验评详情"
								prop="bhas_relexam"
								width="90"
							>
								<template slot-scope="scope">
									<div 
										class="table-text _css-table-relele _css-btnimport" 
										@click="handelClickOpenDocument(scope.row.Id)"
										v-if="scope.row.EvaluationResult && scope.row.EvaluationResult > 0"
									>查看</div>
								</template>
							</el-table-column>
						</el-table>
					</div>
					<div class="_css-right-model-iframe" v-if="cacheModelID.length > 0">
						<div class="legend">
							<ul>
								<li>
									<span class="_css-color" style="background: rgba(208,19,255, 0.3);"></span> 
									<span class="_css-num">优良</span>
								</li>
								<li>
									<span class="_css-color" style="background: rgba(34,141,240, 0.3);"></span> 
									<span class="_css-num">合格</span>
								</li>
							</ul>
						</div>
						<modelNewDetail
							:VaultID="VaultID"
							:featureID="openModelID"
							:modelTitle="newModelTitle"
							@closeModelDetail="onmodelview_close"
						>
						</modelNewDetail>
						<!-- <modelNewIframeLoading
							class="model-iframe"
							:VaultID=VaultID
							:featureID=openModelID
							:modelVersion="0"
						></modelNewIframeLoading> -->
					</div>
				</div>
			</div>
			
		</div>
		<div class="detail-view-iframe" v-if="openViewDoc">
			<p class="right-close"><i @click="openViewDoc=false"></i></p>
			<iframe width="100%" height="100%" :src="viewIframe" frameborder="0"></iframe>
		</div>
	</div>
</template>
<script>
import MaterialsMgrTree from "@/components/Home/ProjectBoot//MaterialsMgrTree";
import modelNewDetail from "@/components/Home/ProjectBoot/modelNewDetail";
import modelNewIframeLoading from '@/components/Home/ProjectBoot/modelNewIframeLoading'

import { mapGetters } from "vuex";
export default {
	components: {
		MaterialsMgrTree,
		modelNewDetail,
		modelNewIframeLoading,
	},
	name: "guangxiAcceptance",
	data() {
		return {
			windowHeight: '500',
      token: "", // 进度所有list
			organizeId: "",
			userId: '',
      		treeOpenOrClose: false,  // 点击展开折叠tree
			treeOpenOrCloseText: '点击展开',
			leftTreeList: [], // 左边的树结构数据
			// 树控件的 props 属性对象
			treem_props: {
				children: "children",
				label: "bmc_name",
				isLeaf: "isLeaf",
			},
			// 默认展开节点
			// 当前选中的节点
			treem_default_expanded_keys: [],
			selectTableDataList: [], // 储存首次加载表格data
			tableClose: true, // 控制点击左边菜单栏收起
			leftRightHide: true, // 控制右边icon
			clickMaterialTypeName: "",
			clickMaterialTypeID: "",
			extdata: {
				currentRow: null, // 点击表格该行高亮
				modelHoverName: "", // 鼠标滑过真是模型的名称

				text_filter_mat_name: "", // 单元名称过滤
				isshow_filter_mat_status: false, // 显示构件状态下拉
				selectedtypeid: undefined, // 选中的构件类型ID
				selectedbmid: "", // 选中的构件ID
				materialtypes: [],

				tableData: [],

				tableSortField: "bm_materialname",
				tableSortType: {},
				changeStatusSureId: "",
				statusSureIding: "",
				modeldetailstyle: {
					flex: 1,
					width: "calc(100% - 347px)",
					paddingLeft: "7px",
				},
			},
			cacheModelID: "", //是否打开模型窗口
			materialRelationModel: [],
			rowHeight: 40,
			uTableHeight: 800,
			oldSetmaterialtype: "", // 记录当前点击的和上次点击的是否为同一个
			elTableLoading: false,
			tree_table_id: "", // 保存点击的table中构件的MaterialId
			resetCheckedTree: [], // 选中的树节点

			VaultID: "", // 新引擎使用的
			openModelID: "", // 记录打开模型的modelid
			newModelTitle: "", // 打开模型的title
			modelLoadingOK: false, // 记录模型加载完毕
      Attachments: [],
      openDocView: false,
			viewIframe: '', // 验评详情文件预览
			openViewDoc: false,
		};
	},
	props: {
		showEdit: {
			type: Boolean,
			default: true,
		},
	},
	watch: {
		getModelLoad(data) {
			if (data.state == "end") {
				this.modelLoadingOK = true;
				// this.newModelGetAllElement();
				this.setColorAllElement();
			}
		},
	},

	created() {
		this.windowHeight = window.innerHeight - 80;
    this.token = this.$route.params.Token;
		this.organizeId = this.VaultID = this.$route.params.organizeId;
	},

	mounted() {
		this.loadtypes();
		this.getUserId();
		this.clickTreeOpenOrClose();
		
	},
	filters: {
		flttimeshorter(inputtime) {
			if (!inputtime || inputtime.trim().length == 0) {
				return inputtime;
			}
			if (inputtime.length >= "2019-09-16T11:14".length) {
				return inputtime.substr(0, 10);
			}
		},
		filterHasBeenAssociated(val) {
			if (val == 1) {
				return "已关联";
			} else {
				return "-";
			}
		},
    filterEvaluationResult(val) {
			if (val == 1) {
				return "合格";
			}else if (val == 2) {
				return "优良";
			}else if (val == 3) {
				return "不合格";
			} else {
				return "-";
			}
		},
	},
	computed: {
		...mapGetters(["getModelLoad"]),
	},
	methods: {
		getUserId(){
			this.$axios
				.get(`${window.bim_config.webserverurl}/api/User/User/GetUserSession?token=${this.token}`)
				.then((res) => {
          if (res.Ret == 1 && res.Data) {
						this.userId = res.Data.apisession_userid;
					}
				})
				.catch((x) => {
					console.log(x);
				});
		},
		_stopPropagation(ev) {
			ev && ev.stopPropagation && ev.stopPropagation();
		},
		// 收起节点 回调
		// 展开节点 回调
		node_collapse(itemi, node, comp) {
			// 移除展开过的节点
			var _this = this;
			if (_this.treem_default_expanded_keys.indexOf(node.key) >= 0) {
				_this.treem_default_expanded_keys =
					_this.treem_default_expanded_keys.filter((x) => x != node.key);
			}

			itemi.classname = "icon-interface-component_classification";
		},
		node_expand(itemi, node, comp) {
			// 记录展开过的节点
			// 记录到 treem_default_expanded_keys 中
			var _this = this;
			_this.clickMaterialTypeID = itemi.bmc_guid;
			if (_this.treem_default_expanded_keys.indexOf(node.key) < 0) {
				_this.treem_default_expanded_keys.push(node.key);
			}
			itemi.classname = "icon-interface-component_classification";
		},
		// 当前节点选中变化
		treefunc_current_change(data, node) {
			// console.log(data, node)
			// 不需要是叶子节点
			this.onmodelview_close();
			var _this = this;

			// 设置构件数据表格左上角显示的名称
			_this.clickMaterialTypeName = data.bmc_name;
			// extdata.selectedtypeid 为自动关联使用
			_this.extdata.selectedtypeid = data.bmc_guid;
			// 加载这个分类下的数据
			_this.loadmaterialitems2(data.bmc_guid);
		},
		// 加载子节点
		treefunc_loadChild(node, resolve) {
			// 页面初次加载会自动调用此处，且 node.id 为0
			// 如果 node.id 为 0，直接 return
			// console.log(node,'====node')
			if (node.id == 0) {
				return;
			}
			

			// 拿着 code 请求子一级数据
			var _this = this;
			var _organizeId = this.organizeId;
			var _pcode = node.data.bmc_code;

			// 收起又显示树的情况
			if (node.data.length != undefined && node.data.length >= 0) {
				_this.func_gettypelistAll();
				return;
			}

			var _url = `${this.$MgrBaseUrl.GetCategories}?organizeId=${_organizeId}&baseCode=${_pcode}&Token=${_this.$staticmethod.Get('Token')}`;
			_this.$axios
				.get(_url)
				.then((x) => {
					if (x.data && x.data.Ret > 0) {
						// 深拷贝、补充 classname、设置 isLeaf
						var retdata = _this.$staticmethod.DeepCopy(x.data.Data.list);
						for (var i = 0; i < retdata.length; i++) {
							retdata[i].classname = "icon-interface-component_classification";
							retdata[i].isLeaf = retdata[i].DirectChildrenCount == 0;
							if (retdata[i].isLeaf) {
								retdata[i].classname =
									"icon-interface-associated-component _css-1890ff";
								this.treefunc_current_change(retdata[0])
								this.$nextTick(()=>{
									this.$refs.tree.setCurrentKey(retdata[0].bmc_guid)
								}) 
							}
						}

						resolve(retdata);
					}
				})
				.catch((x) => {
					console.error(x);
				});
		},
		// 点击设置树结构展开折叠
		clickTreeOpenOrClose(){
				let _this = this;
				_this.treeOpenOrClose = !_this.treeOpenOrClose;
				_this.expandedList = []
				if(_this.treeOpenOrClose){ 
						_this.treeOpenOrCloseText = '点击折叠'; 
						for(var i=0;i<this.$refs.tree.store._getAllNodes().length;i++){
								_this.expandedList.push(_this.$refs.tree.store._getAllNodes()[i].data.bmc_guid)
								_this.$refs.tree.store._getAllNodes()[i].expanded = true;
						}
				}else{
						_this.treeOpenOrCloseText = '点击展开'; 
						_this.expandedList = []
						for(var i=0;i<this.$refs.tree.store._getAllNodes().length;i++){
								_this.$refs.tree.store._getAllNodes()[i].expanded = false;
						} 
				}

		},

		// 刷新页面或点击类型
		loadmaterialitems2(typeid, willnotshowloading) {
			// console.log(typeid,willnotshowloading)
			var _this = this;
			if (_this.oldSetmaterialtype !== typeid) {
				_this.oldSetmaterialtype = typeid;
				_this.extdata.selectedtypeid = typeid;
				_this.handleFilters(willnotshowloading);
			}
		},

		// 点击筛选
		handleFilters(willnotshowloading) {
			let _this = this;
			_this.extdata.tableData = [];
			_this.elTableLoading = false;
			if (!willnotshowloading) {
				_this.elTableLoading = true;
			}

			// 获取编码-单元名称-状态-关联构件-开始时间-结束时间
			let _data = `PageNum=1&PageSize=300000&categoryId=${
				_this.extdata.selectedtypeid
			}&quantityValuationResult=&token=${this.token}`;
			_this
				.$axios({
					url: `${this.$MgrBaseUrl.ValuationPaged}?${_data}`,
					method: "get",
				})
				.then((x) => {
					if (!willnotshowloading) {
						_this.elTableLoading = false;
					}
					if (x.status == 200 && x.data.Ret == 1) {
						// console.log(x);
            let items = x.data.Data.Data;
            let _arr = [];
            for (var i = 0; i < items.length; i++) {
              var tobj = items[i]
              tobj.materialid = items[i].MaterialId;
              tobj.materialname = items[i].MaterialName;
              tobj.bhaselerel = items[i].bhaselerel;
              tobj.materialcode = items[i].MaterialCode;
              _arr.push(tobj);
            }
						_this.extdata.tableData = _arr;
						this.openAssociationModelComponents(_arr[0])
						// _this.paginationPageLength = x.data.Data.Total;
					} else {
						this.$message.error(x.data.Msg)
					}
				})
				.catch((x) => {
					console.error(x);
				});
		},

		// 点击表格选择收起左边菜单栏
		chooseCloseOrOpen() {
			this.tableClose = !this.tableClose;
			this.leftRightHide = this.tableClose;
		},
		handleCurrentChange(currentRow) {
			this.extdata.currentRow = currentRow;
		},
		// 鼠标滑过模型，展示模型名称
		modelMouseEnter(row, column, cell) {
			let modelThis = this;
			if (column.label == "关联模型") {
				modelThis.getNewModelInfo(row.relelejson); // 新模型获取名字
				// 请求接口
			}
		},

		// 根据当前的过滤状态，加载数据
		loadmaterialitems_bycurrentfilters() {
			// 所选择的构件分类
			var _this = this;
			var _selectedtypeid = _this.extdata.selectedtypeid;

			_this.loadmaterialitems(_selectedtypeid);
			if (_this.oldSetmaterialtype == -1000) {
				_this.handleFilters();
			}
		},

		convertToDt(items) {
			var _arr = [];
			for (var i = 0; i < items.length; i++) {
				var tobj = {};
				tobj.materialid = items[i].bm_guid;
				tobj.materialname = items[i].bm_materialname;
				tobj.materialcode = items[i].bm_materialcode;
				tobj.statusid = items[i].bc_guid_materialstatus;
				tobj.bhaselerel = items[i].bhaselerel;
				tobj.bm_updatetime = items[i].bm_updatetime;
				tobj.relelejson = items[i].relelejson;
				tobj.bhas_relexam = items[i].bhas_relexam;
				tobj.bhas_relflow = items[i].bhas_relflow;
				_arr.push(tobj);
			}
			return _arr;
		},

		// 刷新数据
		refresh() {
			var _this = this;
			if (!_this.extdata.selectedtypeid) {
			} else {
				_this.loadmaterialitems2(_this.extdata.selectedtypeid);
			}
		},

		// 加载某分类下的构件数据
		loadmaterialitems(typeid) {
			var _this = this;
			var _url = `${
				this.$MgrBaseUrl.GetMaterialList
			}?TypeId=${typeid}&token=${this.token}`;
			// 可选的排序参数

			_this.$axios
				.get(_url)
				.then((x) => {
					if (x.status == 200) {
						if (x.data.Ret > 0) {
							//debugger;
							_this.selectTableDataList = _this.convertToDt(x.data.Data.List);
							_this.extdata.tableData = _this.convertToDt(x.data.Data.List);
							let allupdatetime = x.data.Data.allupdatetime;
							for (let i in allupdatetime) {
								allupdatetime[i] = this.$options.filters["flttimeshorter"](
									allupdatetime[i]
								);
							}

							// 刷新个数
							var thecateindex = _this.extdata.materialtypes.findIndex(
								(x) => x.materialtypeid == typeid
							);
							if (typeid && thecateindex >= 0) {
								_this.extdata.materialtypes[thecateindex].MaterialCnt =
									_this.extdata.tableData.length;
							}
						} else {
							_this.$message.error(x.data.Msg);
						}
					} else {
						console.error(x);
					}
				})
				.catch((x) => {
					console.error(x);
				});
		},
		// 加载节点：“单元管理”
		func_gettypelistAll() {
			// 请求接口，加载第一级的分类
			var _this = this;
			var _organizeId = this.organizeId;
			var _url = `${this.$MgrBaseUrl.GetCategories}?organizeId=${_organizeId}&baseCode=&Token=${_this.$staticmethod.Get('Token')}`;
			_this.$axios
				.get(_url)
				.then((x) => {
					// 处理 isLeaf、children、classname 等属性
					var retdata = _this.$staticmethod.DeepCopy(x.data.Data.list);

					// 标记是否有第一级分类
					var hasfirstwftc = retdata.length > 0;

					// 预设“单元管理”树节点
					var _all = [
						{
							classname: "icon-interface-component_classification",

							// 这里应设为多少？
							isLeaf: !hasfirstwftc,

							// 全部的 code 为空字符串
							// -1000 表示空节点，置空时使用，这里设为-1000
							// 其它值
							bmc_code: "",
							bmc_guid: "-1000",
							bmc_name: "单元管理",
							ChildrenItemCount: x.data.Data.totalcount,
						},
					];
					_this.leftTreeList = _all;
				})
				.catch((x) => {
					console.error(x);
				});
		},

		// 加载所有构件分类-新方法
		func_gettypelist(node) {
			// 调用接口，获取分类数据
			var _this = this;
			var _organizeId = this.organizeId;
			var _url = `${this.$MgrBaseUrl.GetCategories}?organizeId=${_organizeId}&baseCode=&Token=${_this.$staticmethod.Get('Token')}`;
			_this.$axios
				.get(_url)
				.then((x) => {
					// 处理 isLeaf、children、classname 等属性
					var retdata = _this.$staticmethod.DeepCopy(x.data.Data.list);

					// 图标属性补充
					for (var i = 0; i < retdata.length; i++) {
						retdata[i].classname = "icon-interface-component_classification";
						retdata[i].isLeaf = retdata[i].DirectChildrenCount == 0;
					}
					_this.leftTreeList = retdata;

					// 设置自动选中
					if (node && _this.$refs.ref_bmc) {
						setTimeout(() => {
							_this.$refs.ref_bmc.setCurrentNode(node);
						}, 1000);
					}
				})
				.catch((x) => {
					console.error(x);
				});
		},

		// 加载所有构件分类
		loadtypes() {
			// 先加载一个叫 “单元管理” 的节点
			this.func_gettypelistAll();
			return;
		},
		tableRowClassName({ row, rowIndex }) { 
			return "css-tdunder-test1";
		},
    // 文件预览
    handelClickOpenDocument(id){
      let _this = this;
      let doc={}
      this.$axios
        .get(`${this.$MgrBaseUrl.ValuationDetail}?id=${id}&token=${this.token}`)
        .then(res=>{
          if(res.data.Ret == 1){
            let _data = res.data.Data;
            doc={
              FileId: _data.Attachments[0].Id,
              FileName: _data.Attachments[0].AttachmentName,
              FileExtension: _data.Attachments[0].AttachmentExtension
            }
            var url = `${window.bim_config.webserverurl}/api/v1/attach/preview?id=${doc.FileId}&token=${this.$staticmethod.Get("Token")}`
              // 根据扩展名获取在线浏览地址
            
            if (doc.FileName.toLowerCase().indexOf(".dwg") > 0) {
              // dwg 在线预览
              this.viewIframe = `${
                _this.$configjson.dwgurl
              }/Home/Index2?dwgurlcfg=${encodeURIComponent(url)}&name=${
                doc.FileName
              }`;
            } else { 
              this.viewIframe = _this.$staticmethod.getHuangNewcomputeViewUrl(url, doc.FileName,doc.FileExtension);
            } 
						this.openViewDoc = true;
          }
        })
        .catch(()=>{

        })  
    },
		// 填报
		// submitFillIn(row) {
    //   this.ValuationId = row.Id;
    //   this.m_edit_name = row.MaterialName;
    //   this.status_showedit = true;
    // },
		//控制右侧table样式
		getStyleBottomtable() {
			let _s = {};
			_s["flex"] = this.extdata.modeldetailstyle.flex;
			_s["width"] = this.extdata.modeldetailstyle.width;
			_s["paddingLeft"] = this.extdata.modeldetailstyle.paddingLeft;
			return _s;
		},
		
		//打开模型（关联的构件）
		openAssociationModelComponents(tableData) {
			if(tableData.bhaselerel == 0) return
      
      this.openModelID = tableData.relelejson;
      
			this.getNewModelInfo(tableData.relelejson);
			this.tree_table_id = [];
			this.tree_table_id.push(tableData.materialid); 
			
			let selectModelID = "";
			// this.setColorAllElement();

			// 如果有关联构件bhaselerel==1，请求GetMaterialDetail获取关联的构件
			if (tableData.bhaselerel) {
				this.$axios
					.get(
						`${this.$MgrBaseUrl.GetMaterialDetail}?bm_guid=${
							tableData.materialid
						}&token=${this.token}`
					)
					.then((res) => {
						if (res.data.Ret == 1) {
							// this.extdata.modeldetailstyle.flex = "initial";
							// this.extdata.modeldetailstyle.width = 500 + "px";
							// this.extdata.modeldetailstyle.paddingLeft = 24 + "px";
							selectModelID = res.data.Data.mris[0].modelid;
							// 判断本次modelid是否与上次相同
							// debugger
							if (this.cacheModelID == selectModelID) {
								// 新模型处理=先取消当前高亮的构件==开始
								let sel_elements = window.scene.selectedObjects;
								let sel_elementIds = "";
								if (sel_elements.size > 0) {
									sel_elementIds = Array.from(sel_elements.keys());
								}
								for (let i = 0; i < sel_elementIds.length; i++) {
									let element_false = window.scene.findObject(
										sel_elementIds[i]
									);
									element_false.selected = false;
									window.scene.render();
								}
								// 新模型处理=先取消当前高亮的构件==结束

								// 新模型处理=高亮的构件
								this.materialRelationModel =
									res.data.Data.mris[0].elementids.map((key) => {
										return `${res.data.Data.mris[0].modelid}^${key}`;
									});

								// 新模型==聚焦高亮
								if (this.materialRelationModel.length > 0) {
									for (
										let _i = 0;
										_i < this.materialRelationModel.length;
										_i++
									) {
										let a = this.materialRelationModel[_i]; //'09c78392-5b51-4a85-af4b-2cdada0d6fa2^573547'
										let element = window.model3.objects.get(a);
										element.selected = true;
									}
									window.scene.render();
									// this.newModelsetElementColor(this.materialRelationModel,30,255,0,0.5)
								}
							} else {
								//循环拼接模型id和构件所关联的构件id 格式为 “模型id^构件id”
								this.materialRelationModel =
									res.data.Data.mris[0].elementids.map((key) => {
										return `${res.data.Data.mris[0].modelid}^${key}`;
									});
								let timeer = null;
								timeer = setInterval(() => {
									if (this.modelLoadingOK) {
										for (
											let _i = 0;
											_i < this.materialRelationModel.length;
											_i++
										) {
											let a = this.materialRelationModel[_i]; //'09c78392-5b51-4a85-af4b-2cdada0d6fa2^573547'
											let element = window.model3.objects.get(a);
											element.selected = true;
										}
										window.scene.render();
										clearInterval(timeer);
									}
								}, 1000);
							}

							this.cacheModelID = res.data.Data.mris[0].modelid;
							//  res.data.Data.mris[0].modelid cache
							// 缓存Modelid 作为下一次打开时的参考
						} else {
							this.$message({
								type: "error",
								message: res.data.Msg,
							});
						}
					})
					.catch((res) => {
						this.$message.warning("当前单元暂无关联构件");
						console.warn(`请求构件详情出错:${res}`);
					});
					
			} else {
				this.$message.error("当前单元暂无关联构件");
			}
		},
		// 获取所有验评数据绑定的模型构件
		setColorAllElement(){
			// quantityValuationResult 验评结果 1 合格 2 优良 3 不合格 为空则返回1和2
			this.$axios
				.get(`${this.$MgrBaseUrl.GetAllElements}?token=${this.token}&categoryId=${this.extdata.selectedtypeid}&modelId=${this.openModelID}&quantityValuationResult=1`)
				.then(res=>{
					if(res.data.Data){
						let _data = res.data.Data;
						let eles = _data.map((key) => {
							return `${this.openModelID}^${key}`;
						});
						this.newModelsetElementColor(eles,34,141,240, 0.3)
					}else{
						// this.$message.error(res.data.Msg);  // 没有相关模型构件
					}
				})
				.catch(()=>{
					this.$message.error("请求服务异常");
				})
			this.$axios
				.get(`${this.$MgrBaseUrl.GetAllElements}?token=${this.token}&categoryId=${this.extdata.selectedtypeid}&modelId=${this.openModelID}&quantityValuationResult=2`)
				.then(res=>{
					if(res.data.Data){
						let _data = res.data.Data;
						let eles = _data.map((key) => {
							return `${this.openModelID}^${key}`;
						});
						this.newModelsetElementColor(eles,208,19,255, 0.3)
					}else{
						// this.$message.error(res.data.Msg);  // 没有相关模型构件
					}
				})
				.catch(()=>{
					this.$message.error("请求服务异常");
				})
		},
		// 获取新模型详情
		getNewModelInfo(openModelID) {
			// console.log(openModelID)
			if (openModelID == null || openModelID.length == 0) return;
			let _this = this;
			_this.$axios
				.get(
					`${this.$ip("newModelHttpUrl")}/Vault/GetFeature?VaultID=${
						_this.VaultID
					}&FeatureID=${openModelID}`
				)
				.then((res) => {
					if (res.status === 200) {
						_this.newModelTitle = res.data.featureName;
						_this.extdata.modelHoverName = res.data.featureName;
					} else {
						_this.newModelTitle = "";
					}
				})
				.catch((err) => {});
		},
		// 获取所有构件
		newModelGetAllElement() {
			let allElements = window.scene.findFeature(this.openModelID).objects;
			let elementIds = Array.from(allElements.keys());
			window.scene.execute("color", {
				objectIDs: elementIds,
				color: "rgb(0,0,0)",
				opacity: 0.1,
			});
		},
		// 新模型=删除着色
		newModelresetElement(r_eles) {
			for (let i = 0; i < r_eles.length; i++) {
				let element = window.model3.objects.get(r_eles[i]);
				element.resetColor();
			}
			window.scene.render();
		},
		// 新模型=删除全部着色
		newModelresetAllElement() {
			window.scene.coloredObjects.forEach((obj) => {
				obj.reset();
			});
			window.scene.render();
		},
		// 新模型=着色
		newModelsetElementColor(eles, r, g, b, a) {
			this.newModelresetElement(eles);
			window.scene.execute("color", {
				objectIDs: eles,
				color: `rgb(${r},${g},${b})`,
				opacity: `${a}`,
			});
		},
		treefunc_checked(checked_data) {
			// console.log(checked_data,'这是选择后的高亮的构件',this.cacheModelID)
			if (checked_data.modelids) {
				if (this.cacheModelID == checked_data.modelids[0]) {
					let elementIds = checked_data.eleids;
					for (let i = 0; i < elementIds.length; i++) {
						let element = window.model3.objects.get(elementIds[i]);
						element.selected = false;
					}
					window.scene.render();
					// 当前选中构件着色
					let allCheckRemoveColor = [
						...new Set(checked_data.eleids.concat(this.resetCheckedTree)),
					];
					this.newModelsetElementColor(allCheckRemoveColor, 0, 0, 0, 0.1);

					this.resetCheckedTree = [];
					this.resetCheckedTree = checked_data.eleids;
					this.newModelsetElementColor(this.resetCheckedTree, 30, 255, 0, 0.5);
				}
			} else {
				this.$message({
					type: "warning",
					message: "当前分类下暂无关联构件，请确保选择的为末级节点",
				});
			}
		},
		onmodelview_close() { 
			this.cacheModelID = "";
			this.extdata.modeldetailstyle.flex = 1;
			this.extdata.modeldetailstyle.width = "calc(100% - 347px)";
			this.extdata.modeldetailstyle.paddingLeft = 7 + "px";
			this.tableClose = true;
			this.leftRightHide = true; 
		},
		handleSizeChange(val) {
			this.pageSize = val;
			this.handleFilters();
		},
		handleCurrentPaginationChange(val) {
			this.pageNum = val;
			this.handleFilters();
		},
	},
};
</script>
<style scoped lang="scss">
@import url("../../assets/css/MaterialsMgrStyle.css");

.pro-in-out {
	display: inline-block;
	width: 16px;
	height: 120px;
	background-image: url(../../assets/images/p-in.png);
	background-size: 100%;
	background-repeat: no-repeat;
}
.pro-in-out.p-out {
	background-image: url(../../assets/images/p-out.png);
	background-repeat: no-repeat;
	background-size: 100%;
	left: 0;
}
._css-dataitemname :hover {
	color: rgba(0, 0, 0, 0.85);
	cursor: default;
	text-decoration: auto;
}
._css-materialtypetab-body{
	display: flex;
	height: 100% !important;

}
._css-materialtypetab-body .left-table-list{
	width: 50%;
}
._css-materialtypetab-body .left-table-list.width-100{
	width: 100%;
}
._css-materialtypetab-body ._css-right-model-iframe{
	width: 50%;
}
._css-materialtypetab-body /deep/ ._css-qualitystyle.el-table th {
	font-size: 14px !important;
	padding-left: 12px !important;
}
.table-text {
	padding-left: 12px;
	font-size: 14px;
	font-weight: 500;
	color: rgba(0, 0, 0, 0.85);
}
._css-table-relele {
	cursor: pointer;
}
._css-btnimport {
	font-size: 12px;
	color: #1890ff;
	border: 1px solid #1890ff;
	border-radius: 4px;
	padding: 4px 6px 4px 6px;
	margin-right: 12px;
	cursor: pointer;
}
._css-btnimport:hover {
	color: #fff;
	background-color: #1890ff;
}
._css-addingnameinput-ctn /deep/ .el-date-editor--daterange{
  width: 100%;
}
._css-fieldvaluename {
    flex: 1;
    height: 36px;
    display: flex;
    align-items: center;
    border: 1px solid rgba(0,0,0,0.1);
    border-radius: 4px;
    box-sizing: border-box;
} 
._css-fieldvaluename /deep/ .el-input__inner{
    line-height: 34px;
    height: 34px;
}
._css-fieldvaluename /deep/ .el-date-editor.el-input, 
._css-fieldvaluename /deep/ .el-date-editor.el-input__inner{
    width: 100%;
}
._css-line {
    padding:0 24px;
    box-sizing: border-box;
    margin: 8px 0 0 0;
    display: flex;
    align-items: center;
}
._css-flowAddBtnCtn {
    display: flex;
    flex-direction: row-reverse;
    height: 64px;
    align-items: center;
    box-sizing: border-box;
    padding-right: 8px;
}
._css-title-flowname{
    width: 25%;
    text-align: left;
}
.not-allow{
	cursor: not-allowed;
	pointer-events: none;
	color: #606266;
  border: 1px solid #606266;
}
._css-bottomtable{
	padding-bottom: 0;
	padding-right: 0;
}
._css-right-model-iframe /deep/ .detail-header .flex-box .close{
	display: none;
}
._css-right-model-iframe /deep/ .detail-header .text-center{
	margin: 0 auto;
}
.detail-view-iframe{
	position: fixed;
	top: 10%;
	width: 80%;
	height: 80%;
	left: 10%;
	z-index: 10;
	background: rgba(0,0,0,0.1);
	
}
.detail-view-iframe .right-close{
	position: absolute;
	right: -10px;
	top: -10px;
	border:1px solid #fff;
	width: 25px;
	height: 25px;
	border-radius: 50%;
	background: #000;
}
.detail-view-iframe i{
	display: inline-block;
	width: 25px;
	height: 25px;
	cursor: pointer;
	background: url("../../assets/images/2-detail-close.png") no-repeat;
	background-size: 100% 100%;
}
.legend{
	position: absolute;
	right: 20px;
	bottom: 20px;	
	z-index: 2;
	li{
		span{
			display: inline-block;
		}
		span._css-color{
			display: inline-block;
			width: 20px;
			height: 8px;
			border-radius: 1px;
			margin-right: 10px;
		}
		span._css-num{
			color: rgba(255, 255, 255, 0.8);
		}
	}
}
</style>
