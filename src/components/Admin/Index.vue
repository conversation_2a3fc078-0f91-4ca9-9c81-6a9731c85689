<template>
	<div
		v-loading="loadingTable"
		style="width: 100%; height: 100%; overflow: hidden"
		@click="hideDialog($event)"
	>
		<!-- 可切换的整体 -->
		<div class="_css-head-tabcontrol">
			<div
				class="_css-head-tabcontrol-item"
				:class="{ selected: extdata.showtype == 'log' }"
				@click.stop="changeshowtype('log')"
			>
				日志
			</div>
			<span class="out icon-suggested-close-fill" @click="out">
				返回机构列表
			</span>
		</div>
		<!-- //可切换的整体 -->
		<div class="header">
			<div class="head">
				<span class="css-logo logo"> 北京东晨工元科技发展有限公司 </span>
			</div>
		</div>

		<!-- system 功能维护 -->
		<div class="_css-sys-func" v-if="extdata.showtype == 'funcs'">
			<div class="_css-sys-func-head"></div>
			<div class="_css-sys-func-body">
				<div class="_css-sys-btnarea btns">
					<input class="btn1" type="button" value="添加功能" />
					<span class="txt"
						><input class="txt_i" type="text" placeholder="搜索功能" /><i
							class="tip icon-interface-search"
						></i
					></span>
				</div>
			</div>
		</div>
		<!-- //system 功能维护 -->

		<template v-if="extdata.showtype == 'company'">
			<div class="_css-sys-btnarea btns">
				<input
					class="btn1"
					type="button"
					value="新建机构"
					@click="showForm(true)"
				/>
				<span class="txt"
					><input
						class="txt_i"
						type="text"
						placeholder="搜索机构"
						v-model="OrganizeSearchName"
						@keyup="searchOrganize($event)" /><i
						class="tip icon-interface-search"
					></i
				></span>
			</div>
			<div class="container css-miniscroll">
				<div class="list">
					<el-table
						class="OrganizeTable"
						:data="OrganizeList"
						style="width: 100%; heihgt: auto"
						:header-cell-style="InitCustomHeaderColumnStyle"
						:row-style="InitCustomRowStyle"
						:cell-style="InitCustomColumnStyle"
					>
						<el-table-column
							prop="name"
							label="名称"
							min-width="12%"
							:formatter="changeColumnVal_OrganizeName"
							show-overflow-tooltip
						></el-table-column>
						<el-table-column
							prop="createDate"
							label="创建时间"
							min-width="12%"
							:formatter="changeColumnVal_OrganizeCreateDate"
						></el-table-column>
						<el-table-column
							prop="createUserName"
							label="创建人"
							min-width="12%"
						></el-table-column>
						<el-table-column
							prop="existProjectNum"
							:formatter="changeColumnVal_ProjectNum"
							label="已存在/总项目个数"
							min-width="9%"
						></el-table-column>
						<el-table-column prop="" label="操作" min-width="11%">
							<template slot-scope="scope">
								<el-button
									size="mini"
									class="btnShowMember noHide"
									@click="showMember(scope)"
									>查看</el-button
								>
								<el-button
									size="mini"
									class="btnShowMember noHide"
									@click="authOrganize(scope)"
									>授权</el-button
								>
								<el-button
									size="mini"
									class="btnShowMember noHide"
									@click="editOrganize(scope)"
									>编辑</el-button
								>
								<el-button
									size="mini"
									class="btnShowMember noHide"
									@click="deleteOrganize(scope)"
									>删除</el-button
								>
							</template>
						</el-table-column>
					</el-table>
				</div>
			</div>
			<div class="boot">
				<page-object
					class="PgCss"
					@changePageInfo="changePageInfo"
					:PageIndex="PageIndex"
					:PageNum="PageNum"
					:PageTotal="PageTotal"
					style="box-shadow: 0px 0px 5px #ccc"
				></page-object>
			</div>
		</template>

		<!--日志-->
		<SystemLog v-if="extdata.showtype == 'log'" class="sl"></SystemLog>
		<!--日志-->

		<add-form
			v-show="showAdd"
			style="position: fixed; z-index: 9999"
			v-drag="draggreet"
			:style="dragstyle"
			:Info="AddFromData"
			:OrganizeId="currentOrganizeId"
			@closeForm="showForm(false)"
		>
		</add-form>
		<user-list
			class="organizeMenmber"
			:organizeId="currentOrganizeId"
			v-show="IsMemberList"
		></user-list>
		<div class="covery" v-show="showAdd"></div>

		<CompsComAuth
			v-if="companyAuthData.visible"
			:title="companyAuthData.title"
			@oncancel="cancelAuthOrganize"
			:zIndex="11"
			:organizeId="companyAuthData.organizeId"
			@onok="saveAuthCode"
		></CompsComAuth>
	</div>
</template>
<script>
import bootHeader from "@/components/Home";
import pageObject from "@/components/CompsCustom/PageObject";
import addForm from "@/components/Admin/Index/Form";
import userList from "@/components/CompsCustom/UserList";
import CompsComAuth from "@/components/CompsCommon/CompsComAuth";
import SystemLog from "@/components/Admin/Index/SystemLog";

export default {
	name: "OrganizeManage",
	components: {
		bootHeader,
		pageObject,
		addForm,
		userList,
		CompsComAuth,
		SystemLog,
	},
	data() {
		return {
			val: "0",
			dragstyle: {
				position: "fixed",
				right: "calc(50% - 200px)",
				top: "calc(50% - 150px)",
			},
			extdata: {
				showtype: "company",
			},
			companyAuthData: {
				organizeId: "", // 正在进行授权的机构ID
				title: "机构授权",
				visible: false,
			},
			OrganizeList: [],
			OrganizeSearchName: "",
			PageIndex: 1,
			PageNum: 15,
			PageTotal: 500,
			showAdd: false,
			AddFromData: {
				title: "新增机构",
			},
			loadingTable: false,
			currentOrganizeId: "-1", //当前的机构

			IsMemberList: false,
		};
	},
	methods: {
		draggreet(val) {
			var _this = this;
			_this.val = val;
		},
		changeshowtype(typestr) {
			var _this = this;
			_this.extdata.showtype = typestr;
		},
		// 保存机构授权
		saveAuthCode(organizeId, machineCode, authCode) {
			var _this = this;
			//console.log(organizeId, machineCode, authCode);
			var _Token = _this.$staticmethod.Get("Token");
			var _OrganizeId = organizeId;
			var _MachineCode = machineCode;
			var _AuthCode = authCode;
			const loading = this.$loading({
				lock: true,
				text: "正在授权中，请您耐心等待...",
				spinner: "el-icon-loading",
				background: "rgba(0, 0, 0, 0.3)",
			});
			// 调用接口，保存机构授权 需要有 system或supersystem 的 Token
			_this
				.$axios({
					method: "post",
					url: `${this.$urlPool.SaveCompanyAuth}`,
					data: {
						Token: _Token,
						ProjectId: _OrganizeId,
						MachineCode: _MachineCode,
						AuthCode: _AuthCode,
					},
				})
				.then((x) => {
					if (x.status == 200) {
						if (x.data.Ret > 0) {
							// 如果成功，刷新当前页面，并关闭对话框
							_this.companyAuthData.visible = false;
							_this.$message.success("操作成功");
							_this.loadingList();
						} else if (x.data.Ret == -10006) {
							_this.$message.error(`授权码错误(${x.data.Ret})`);
						} else {
							_this.$message.error(`服务器错误(${x.data.Ret})`);
						}
						loading.close();
					} else {
						loading.close();

						_this.$message.error(`服务器错误(${x.data.Msg})`);
					}
				})
				.catch((x) => {
					loading.close();
					_this.$message.error(`服务器错误`);
				});
		},

		// 取消机构授权
		cancelAuthOrganize() {
			var _this = this;
			_this.companyAuthData.visible = false;
		},

		// 弹出机构授权，并设置标题
		authOrganize(data) {
			var _this = this;
			// 设置正在进行机构授权的ID
			_this.companyAuthData.organizeId = data.row.organizeId;

			// 设置机构授权对话框为可见
			_this.companyAuthData.visible = true;

			// 设置授权对话框的标题
			_this.companyAuthData.title = `机构授权(${data.row.name})`;
		},
		InitCustomRowStyle({ row, rowIndex }) {
			return "";
		},
		InitCustomHeaderColumnStyle({ row, column, rowIndex, columnIndex }) {
			if (columnIndex != 6 && columnIndex != 7)
				return "background-color:#fff;font-size:12px;line-height:30px;";
			else
				return "background-color:#fff;font-size:12px;line-height:30px;display:none";
		},
		InitCustomColumnStyle({ row, column, rowIndex, columnIndex }) {
			if (columnIndex == 4)
				return "border-top:1px solid #efefef;position:relative;";
			else if (columnIndex != 6 && columnIndex != 7)
				return "border-top:1px solid #efefef;overflow:hidden;text-overflow: ellipsis;white-space: nowrap;";
			else
				return "border-top:1px solid #efefef;overflow:hidden;text-overflow: ellipsis;white-space: nowrap;display:none";
		},
		changeColumnVal_ProjectNum(row, col, cellVal, Index) {
			var existProjectNum = row["existProjectNum"];
			var allowPorjectNum = row["allowProjectNum"];
			allowPorjectNum =
				allowPorjectNum == "" || allowPorjectNum == undefined
					? "0"
					: allowPorjectNum;
			return existProjectNum + "/" + allowPorjectNum;
		},

		// 格式化机构的创建时间显示
		changeColumnVal_OrganizeCreateDate(row, col, cellVal, Index) {
			if (row && row.createDate) {
				//console.log(row.createDate);
				return row.createDate.toString().replace("T", " ");
			} else {
				return "--";
			}
		},

		changeColumnVal_OrganizeName(row, col, cellVal, Index) {
			var organizeName = row["name"];
			if (organizeName.length > 15)
				organizeName = organizeName.substring(0, 15);
			return organizeName;
		},
		changePageInfo(i, n) {
			console.log("Index.vue");
			this.PageIndex = i;
			this.PageNum = n;
			this.loadingList();
		},
		showForm(sign) {
			this.showAdd = sign;
			this.currentOrganizeId = "-1";
			this.AddFromData.title = "新建机构";
			this.loadingList();
		},
		showMember(data) {
			var OrganizeId = data.row.organizeId;
			this.IsMemberList = true;
			this.currentOrganizeId = OrganizeId;
		},
		editOrganize(data) {
			var OrganizeId = data.row.organizeId;
			this.currentOrganizeId = OrganizeId;
			this.AddFromData.title = "编辑机构";
			//这里弹出编辑窗口
			this.showAdd = true;
		},
		hideDialog(e) {
			var sign = e.target.innerText;
			var className = e.target.className;
			console.log(className, "点击的className");
			if (
				sign != "查看" &&
				sign != "编辑" &&
				sign != "删除" &&
				className.indexOf("noHide") == -1
			) {
				this.IsMemberList = false;
			}
		},
		out() {
			var _tval = this.$staticmethod.Get("Token");

			window.location.href = `${window.bim_config.hasRouterFile}/#/Admin/Index_V2/${_tval}` ;

		},
		loadingList() {
			//初始化机构列表数据
			this.loadingTable = true;
			this.$axios
				.get(
					`${_this.$urlPool.GetOrganizeList}?Token=${_this.$staticmethod.Get(
						"Token"
					)}&OrganizeName=${_this.conditionData.organizeName}&SkipNum=${
						_this.OrganizeList.length
					}&PageNum=16`
				)
				.then((res) => {
					let result = JSON.parse(res.data.Data);
					let tempData = result.ItemData;
					this.OrganizeList = [];
					for (let i = 0; i < tempData.length; i++) {
						this.OrganizeList.push({
							organizeId: tempData[i].OrganizeId,
							name: tempData[i].FullName,
							createDate: tempData[i].CreateDate,
							createUserName: tempData[i].CreateUserName,
							existProjectNum: tempData[i].ExistsActivityTotal,
							allowProjectNum: tempData[i].CertificateCode,
						});
					}
					this.loadingTable = false;
					this.PageTotal = result.AllCount;
				})
				.catch((error) => {});
		},
		searchOrganize(e) {
			if (e.keyCode == 13) {
				this.loadingList();
			}
		},
		deleteOrganize(data) {
			this.$confirm("是否要删除该机构？", "删除机构", {
				type: "warning",
				confirmButtonText: "确定",
				cancelButtonText: "取消",
			})
				.then((res) => {
					let OrganizeId = data.row.organizeId;
					let _data = {
						ProjectId: OrganizeId,
						token: this.$staticmethod.Get("Token")
					}
					this.$axios
						.post(`${this.$urlPool.DeleteOrganize_Logic}?ProjectId=${OrganizeId}&token=${this.$staticmethod.Get("Token")}`)
						.then((res) => {
							if (res.data.Ret == 1)
								this.$message({ type: "success", message: "删除成功" });
							else {
								this.$message({ type: "error", message: "删除失败" });
								console.log(res.data.Data);
							}
							this.loadingList();
						});
				})
				.catch((res) => {});
		},
	},
	created() {
		this.changeshowtype(this.$route.params.type);
	},
	watch: {
		PageIndex(val) {
			this.loadingList();
		},
	},
};
</script>
<style>
.OrganizeTable .el-table__header-wrapper .cell {
	line-height: 15px;
}
.OrganizeTable .el-table__body-wrapper .cell {
	line-height: 48px;
}
</style>

<style scoped>
body,
html {
	background-color: rgba(240, 242, 245, 1);
}
.header {
	width: 100%;
	height: 10%;
	position: absolute;
	overflow: hidden;
	font-size: 0px;
	box-shadow: 0px 1px 7px 0px rgba(0, 21, 41, 0.12);
	top: 0px;
	left: 0px;
	z-index: 10;
}
.header .head {
	width: 100%;
	height: 56px;
}
.header .head .logo {
	width: 275px;
	height: 32px;
	display: block;
	float: left;
	margin: 16px 0px 0px 16px;
	background-position: left center;
	font-size: 16px;
	font-weight: 500;
	font-family: PingFangSC-Medium;
	font-weight: 500;
	color: rgba(0, 0, 0, 1);
	line-height: 32px;
	text-indent: 18px;
}
.header .head .out {
	width: 136px;
	height: 32px;
	display: block;
	float: right;
	margin: 16px 16px 0px 0px;
	position: relative;
	font-size: 14px;
	font-family: PingFangSC-Regular;
	font-weight: 400;
	color: #505050;
	line-height: 32px;
	cursor: pointer;
}
._css-head-tabcontrol .out {
	width: 136px;
	height: 32px;
	display: block;
	position: absolute;
	font-size: 14px;
	font-family: PingFangSC-Regular;
	font-weight: 400;
	color: #505050;
	line-height: 32px;
	cursor: pointer;
	right: 0;
	top: -8px;
}
.header .head .out::before {
	position: absolute;
	left: 8px;
	line-height: 35px;
}
._css-sys-btnarea {
	top: 10%;
	position: absolute;
}

._css-head-tabcontrol {
	position: absolute;
	top: calc(10% - 40px);
	width: 100%;
	height: 40px;
	display: flex;
	justify-content: center;
	z-index: 15;
}
._css-head-tabcontrol-item {
	height: 100%;
	width: 200px;
	line-height: 40px;
	box-sizing: border-box;
	cursor: pointer;
}
._css-head-tabcontrol-item.selected {
	border-bottom: 2px solid #1890ff;
	color: #1890ff;
}

._css-sys-func {
	position: fixed;
	height: 100%;
	width: 100%;
	border: 1px solid transparent;
	box-sizing: border-box;
}

._css-sys-func-head {
	height: 10%;
	width: 100%;
}

._css-sys-func-body {
	height: 90%;
	width: 100%;
	border: 1px solid transparent;
}

.btns {
	width: calc(100% - 400px);
	height: 64px;
	padding: 0px 200px 0px 200px;
}
.btns .txt {
	position: relative;
	width: 316px;
	height: 32px;
	border: none;
	background-color: #f5f5f5;
	outline: none;
	padding: 0px 8px 0px 27px;
	border-radius: 2px;
	float: right;
	margin-top: 17px;
}
.btns .txt_i {
	position: absolute;
	width: 316px;
	height: 32px;
	border: none;
	background-color: #f5f5f5;
	outline: none;
	padding: 0px 8px 0px 27px;
	border-radius: 2px;
	left: 50%;
	top: 50%;
	transform: translate(-50%, -50%);
}
.btns .tip {
	width: 27px;
	height: 100%;
	display: inline-block;
	position: absolute;
	left: 2px;
	top: 0px;
	line-height: 35px;
	text-align: center;
	opacity: 0.5;
}
.btns .txt input::-webkit-input-placeholder {
	color: #aaa;
	font-size: 12px;
}
.btns .btn1 {
	position: relative;
	width: 96px;
	height: 32px;
	border: none;
	background-color: rgba(24, 144, 255, 1);
	outline: none;
	color: #fff;
	display: block;
	padding: 0px;
	cursor: pointer;
	border-radius: 2px;
	float: left;
	margin-top: 17px;
}
.bh {
	box-shadow: 0px 2px 2px 0px rgba(0, 21, 41, 0.12);
	position: absolute;
	z-index: 9999;
	width: 100%;
	height: 64px;
}
/*中部的list*/
.container {
	width: 100%;
	height: calc(90% - 64px - 32px);
	background-color: rgba(240, 242, 245, 1);
	position: absolute;
	top: calc(10% + 64px);
	overflow: auto;
}
.container .list {
	width: calc(100% - 400px - 48px);
	height: auto;
	margin: 24px auto;
	background-color: #fff;
	box-shadow: 0px 0px 3px #eee;
	padding: 16px;
}
.boot {
	position: absolute;
	bottom: 0px;
	width: 100%;
	height: 37px;
	background: rgba(240, 242, 245, 1);
	overflow-y: scroll;
	overflow-x: hidden;
}
.PgCss {
	width: calc(100% - 400px - 48px);
	margin: 0 auto;
	background-color: #fff;
	padding: 0px 16px 0px 16px;
	height: calc(100% - 4px);
	margin-top: 4px;
}
.organizeMenmber {
	position: absolute;
	width: 450px;
	height: 300px;
	left: 50%;
	top: 50%;
	background-color: #fff;
	box-shadow: 0px 1px 20px #ccc;
	z-index: 99;
	border-radius: 4px;
	transform: translate(-50%, -50%);
}
.btnShowMember {
	margin-right: 3px;
	border-radius: 3px;
	width: 55px;
	height: 25px;
	line-height: 25px;
	border: none;
	outline: none;
	color: #fff;
	background-color: #1890ff;
	display: inline-block;
}
.btnShowMember:hover {
	opacity: 0.8;
}

.covery {
	position: absolute;
	width: 100%;
	height: 100%;
	z-index: 9000;
	background: rgba(0, 0, 0, 1);
	opacity: 0.15;
}
</style>

