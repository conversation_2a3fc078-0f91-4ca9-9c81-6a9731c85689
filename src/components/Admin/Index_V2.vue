<template>
    <div class="OrganizeManage_V2" @click="hideAllDialog($event)">
        <div class="header">
            <button class="back" @click="backLogin"><i class="icon-suggested-close-fill"></i>返回登录</button>
            <el-menu :default-active="defaultActive" class="el-menu-demo" mode="horizontal" @select="handleSelect">
                <el-menu-item index="1">机构授权管理</el-menu-item>
                <el-menu-item index="2">系统菜单配置</el-menu-item>
            </el-menu>
        </div>
        <div class="list">
            <div class="org-list" v-if="defaultActive=='1'">
                <div class="org-header">
                    <div class="addOrganize" @click="isShowAdd=true">新建机构</div>
                    <div class="header-right">
                        <div class="txtSearch">
                            <input type="text" @keydown="keyDownSearch($event)" placeholder="输入机构名称" v-model="conditionData.organizeName" />
                            <i class="icon-interface-search" @click="loadOrganizeList()"></i>
                        </div>
                    </div>
               </div>
                <div class="con" @scroll="loadMore($event)">
                    <div
                    :data-organizeId="item.organizeId"
                    v-for="item in OrganizeList" :key="item.organizeId" class="unit">
                        <img :src="getOrganizeThumbOrDefault(item)" />
                        <ul>
                            <li class="t"><span class="" @click="loginAdmin(item.organizeId)">{{item.name}}</span><i class="blur">{{item.existProjectNum}}&nbsp;/&nbsp;{{MaxProjectNumTrans(item.allowProjectNum)}}</i></li>
                            <li class="b">剩余授权时间：<font>{{item.EndTime}}</font></li>
                        </ul>
                        <i class="more icon-interface-list" @click="setCurrentOrganizeId($event,item.organizeId,true)">
                        </i>
                        <ul v-show="currentOrganizeId==item.organizeId" class="moreMenu">
                            <li style="margin-bottom:0px" @click="showProjectNum(item.organizeId)"><i class="icon-interface-transfer-agency"></i>机构授权</li>
                            <li style="margin-top:0px" @click="showEndTime(item.organizeId)"><i class="icon-interface-transfer-agency"></i>时间授权</li>
                            <li style="width:100%;height:1px;background-color:rgba(0,0,0,0.15);margin:0px;"></li>
                            <li @click="showDeleteDialog(item.organizeId,item.name)"><i class="icon-interface-delete"></i>删除机构</li>
                        </ul>
                    </div>
                </div>
            </div>
            <div v-if="defaultActive=='2'" class="con-list">
                <siteLevelMenuList  :info_companyname="'站点级菜单配置'" :OrganizeId="'0'"></siteLevelMenuList>
            </div>
        </div>
        <add-form v-if="isShowAdd" style="position:fixed;z-index:9999;box-shadow:0px 0px 4px #cfcfcf;border-radius:4px;"
        :organizeShowSet="true"
        v-drag="draggreet"  :style="dragstyle"
        :Info="AddFromData" :OrganizeId="currentOrganizeId"
        @loadList="clearList();loadOrganizeList();"
        @closeForm="showForm(false)">
        </add-form>

        <ProjectNum class="pn" v-if="isShowProjectNum" :OrganizeId="transOrganizeId" @loadList="clearList();loadOrganizeList();" @CloseProjectNum='isShowProjectNum=false;IsDialogCovery=false;'></ProjectNum>
        <ProjectEndTime class="cet" v-if="isShowEndTime" :OrganizeId="transOrganizeId" @loadList="clearList();loadOrganizeList();" @CloseEndTime='isShowEndTime=false;IsDialogCovery=false;'></ProjectEndTime>
        <div v-show="IsDialogCovery" class="Covery"></div>
      <CompsSingleField
        :zIndex="2"
        title="删除机构"
        warningtext="该机构内的所有内容将无法找回，请谨慎操作"
        inputicon="icon-interface-creditcard"
        placeholder="请输入该机构的名称"
        v-if="deleteOrganizeObj.isShowDeleteOrganize"
        @oncancel="cancelDeleteDialog"
        @onok="confirmDeleteOrganize"
        @oninput="confirmDeleteInput"
      ></CompsSingleField>
    </div>
</template>
<script>
import CompsSelect from "@/components/CompsCommon/CompsSelect2";
import addForm from '@/components/Admin/Index/Form'
import ProjectNum from '@/components/Admin/Index/ProjectNum'
import ProjectEndTime from '@/components/Admin/Index/EndTime'
import siteLevelMenuList from '@/components/Admin/Index/siteLevelMenuList'
import CompsSingleField from "@/components/CompsCommon/CompsSingleField";


export default {
    name:'OrganizeManage_V2',
    components:{
        CompsSelect,
        addForm,
        ProjectNum,
        ProjectEndTime,
        siteLevelMenuList,
        CompsSingleField,
    },
    data(){
        return {
         deleteOrganizeObj:{
           isShowDeleteOrganize: false,
           inputStr:'',
           selectOrganizeName:'',
           selectOrganizeID:'',
         },
            defaultActive: '1',
            val:'0',
            dayNum: 1000*60*60*24,//一天多少毫秒
            hourNum: 1000*60*60,//一小时多少毫秒
            minutesNum:1000*60,//一分钟多少毫秒

            IsShowSearch:false,//显示筛选菜单
            isShowMoreMenu:false,//显示更多菜单
            isShowAdd:false,//显示新建机构表单
            isShowProjectNum:false,//显示项目授权个数表单
            isShowEndTime:false,//显示项目时间授权表单
            IsDialogCovery:false,//显示遮罩层
            OrganizeList:[],//机构列表数据
            //查询条件
            conditionData:{
                keyWord:'',
                remian_RoleDayVal:'-1',
                remian_RoleDay:'全部',
                remian_ProjectNumVal:'-1',
                remian_ProjectNum:'全部',
                organizeName:'',
                orderBy:''
            },
            AddFromData:{
                title:"新增机构"
            },
            currentOrganizeId:'-1',//当前的机构
            transOrganizeId:'',
            dragstyle: {
                position: 'fixed',
                right: 'calc(50% - 200px)',
                top: 'calc(50% - 150px)'
            },
        };
    },
    methods:{
      cancelDeleteDialog(){
        this.deleteOrganizeObj = this.$options.data().deleteOrganizeObj
      },
      showDeleteDialog(id,name){
        this.deleteOrganizeObj.selectOrganizeID = id
        this.deleteOrganizeObj.selectOrganizeName = name
        this.deleteOrganizeObj.isShowDeleteOrganize = true
      },
      confirmDeleteInput(str) {
        this.deleteOrganizeObj.inputStr = str;
      },
      confirmDeleteOrganize() {
        if (this.deleteOrganizeObj.inputStr !== this.deleteOrganizeObj.selectOrganizeName) {
          this.$message.error("输入的名称不正确");
          return;
        }
        this.deleteOrganize(this.deleteOrganizeObj.selectOrganizeID)
        this.cancelDeleteDialog()
      },
        handleSelect(key, keyPath){
            this.defaultActive = key;
        },
        draggreet(val){
            var _this = this;
            _this.val = val;
        },
        select_remian_RoleDay(obj){
            this.conditionData.remian_RoleDay=obj.text;
            this.conditionData.remian_RoleDayVal=obj.id;
        },
        select_remian_ProjectNum(obj){
            this.conditionData.remian_ProjectNum=obj.text;
            this.conditionData.remian_ProjectNumVal=obj.id;
        },
        keyDownSearch(e){
            if(e.keyCode==13) {
                this.clearList();
                this.loadOrganizeList();
            }
        },
        MaxProjectNumTrans(val){//根据最大项目个数解析应该显示的值
            if(val==undefined || val=='')
                return 0;
            else if(val<9999)
                return val;
            else
                return '无限项目';
        },
        getOrganizeThumbOrDefault(item)
        {
        //     if(item.organizeId=='d3cb14ea-9db0-48fc-b53c-6388cb3f6777')
        //     {
        //         debugger;
        //         var a=1;
        //     }
            if(item.Thumb!=''&&item.Thumb!=undefined){
                return item.Thumb;
            }
            else{
                return require('../../assets/svgs_loadbyurl/model-Merg.svg');
            }
        },
        loadMore(e){
            // console.log(e);
            let currentHeight=e.target.clientHeight;
            let AllItemHeight=e.target.children[0].clientHeight*e.target.children.length;
            let scrollHeight=e.target.scrollTop;
            // console.log((scrollHeight+currentHeight)+'|'+AllItemHeight);
            if(scrollHeight+currentHeight>=AllItemHeight)
            {
                //debugger;
                this.loadOrganizeList();
            }
        },
        clearList(){
            this.OrganizeList=[];
        },
        loadOrganizeList(){
            let loading=this.$loading({
                lock:true,
                text:'正在加载...'
            });
            var _this=this;
            let pagenum = parseInt(_this.OrganizeList.length / 16)
            let url = `${_this.$urlPool.GetOrganizeList}?Token=${_this.$staticmethod.Get("Token")}&OrganizeName=${_this.conditionData.organizeName}&PageNum=${_this.OrganizeList.length}&PageNum=16`
            _this.$axios
                .get(url)
                .then(res=>{
                    loading.close();
                    if(res.data.Ret == 1){
                        let tempData = res.data.Data.ItemData; // 机构List

                        let compelete=1,num=0;
                    let t=setInterval(function(){
                        if(compelete==1){
                            compelete=0;
                            if(num==tempData.length){
                                clearInterval(t);
                                return;
                            }
                        }else
                            return;
                        if(tempData[num]==undefined){
                            var a=1;
                            //debugger;
                        }
                        let item_EndTime=''
                        if(!tempData[num].ExpiryTime || tempData[num].ExpiryTime == null || tempData[num].ExpiryTime == undefined){
                            item_EndTime='无到期时间';
                        }else{
                            item_EndTime=new Date(tempData[num].ExpiryTime);
                            let timeSpan=item_EndTime.getTime()-new Date().getTime();
                            let endDays=parseInt(timeSpan/_this.dayNum)* _this.dayNum;//天数部分的毫秒数
                            let endHour=parseInt((timeSpan-endDays)/_this.hourNum)*_this.hourNum;
                            let endMinutes=parseInt((timeSpan-endDays-endHour)/_this.minutesNum)*_this.minutesNum;
                            item_EndTime=endDays/_this.dayNum+'天'+endHour/_this.hourNum+'小时'+endMinutes/_this.minutesNum+'分';
                        }

                        _this.$axios
                            .get(`${_this.$urlPool.GetOrganizeThumbnailBase64}?ProjectId=${tempData[num].OrganizeId}&Token=${_this.$staticmethod.Get("Token")}`)
                            .then(res=>{
                                let result=res.data.Data;

                                compelete=1;
                                // 如果不存在，添加到 OrganizeList中
                                var index = _this.OrganizeList.findIndex(x => x.OrganizeId == tempData[num].OrganizeId);
                                if (index < 0) {
                                    _this.OrganizeList.push({
                                        organizeId:tempData[num].OrganizeId,
                                        name:tempData[num].FullName,
                                        createDate:tempData[num].CreateDate,
                                        createUserName:tempData[num].CreateUserName,
                                        existProjectNum:tempData[num].ExistsActivityTotal,
                                        allowProjectNum:tempData[num].CertificateCode,
                                        EndTime:item_EndTime,
                                        Thumb:result.Thumb});
                                    if(tempData.length==_this.OrganizeList.length){
                                        _this.OrganizeList.sort(function(a,b){
                                            return new Date(a.EndTime).getTime() - new Date(b.EndTime).getTime()
                                        });
                                        // console.log(_this.OrganizeList);
                                    }
                                }
                                num++;
                            }).catch(res=>{
                                //debugger;
                                num++;
                            });
                    },10);
                    }
                })
                .catch(err=>{

                })
        },
        // gotoPage(pageUrl,params){
        //     window.location.href=pageUrl+this.$route.params.Token+params;
        // },
        hideAllDialog(e)
        {
            if(e.target.nodeName.toLowerCase()!='button'&&e.target.nodeName.toLowerCase()!='i')
            {
                this.isShowMoreMenu=false;
                this.IsShowSearch=false;
                this.currentOrganizeId='-1';
            }
        },
        showForm(sign)
        {
            this.isShowAdd=sign;
            this.currentOrganizeId="-1";
            this.AddFromData.title='新建机构';
        },
        setCurrentOrganizeId(e,id,sign)
        {
            if(e.target.className.indexOf('more')!=-1 ||e.target.className.indexOf('moreMenu')!=-1 )
            {
                if(this.currentOrganizeId==-'1')
                this.currentOrganizeId=id;
                else
                this.currentOrganizeId='-1';
            }
        },
        showProjectNum(OrganizeId){
            this.isShowProjectNum=true;
            this.transOrganizeId=OrganizeId;
            this.currentOrganizeId='-1';
            this.IsDialogCovery=true;
        },
        showEndTime(OrganizeId){
            this.isShowEndTime=true;
            this.transOrganizeId=OrganizeId;
            this.currentOrganizeId='-1';
            this.IsDialogCovery=true;
        },
        deleteOrganize(OrganizeId){
          this.transOrganizeId=OrganizeId;
          this.currentOrganizeId='-1';
          var loading=this.$loading({text: "执行中"});
          let _data = {
            ProjectId: OrganizeId,
            token: this.$staticmethod.Get("Token")
          }
          this.$axios.post(`${this.$urlPool.DeleteOrganize_Logic}?ProjectId=${OrganizeId}&token=${this.$staticmethod.Get("Token")}`).then(res=>{
            if(res.data.Ret == 1){
              this.$message({type:'success',message:'删除成功'});
              this.clearList();
              this.loadOrganizeList();
              loading.close();
            }else {
              this.$message({ type: "error", message: "删除失败" });
              console.log(res.data.Data);
              loading.close();
            }
          });
        },
        loginAdmin(OrganizeId){
            var token=this.$staticmethod.Get("Token");
            let LoadingIns = this.$loading({
                text: "执行中"
            });
            this.$axios.get(this.$urlPool.base_MainSystemApi+this.$urlPool.LoginByOrganizeId+'?organizeId='+OrganizeId+'&token='+token).then(res=>{
                let result=res.data;
                LoadingIns.close();
                if(result.Ret==1) {
                    this.$staticmethod.Set("Token",result.Data.Token);
                    this.$staticmethod.Set('RealName', result.Data.RealName);
                    this.$staticmethod.Set('Account', result.Data.Account); // 上传文件时，参数：CreateUserName。
                    this.$staticmethod.Set("UserId", result.Data.UserId); // Cache UserId
                    this.$staticmethod.Set("Email", result.Data.Email); // Cache Email
                    window.location.href = `${window.bim_config.hasRouterFile}/#/Home/Boot/${result.Data.Token}`;
                }else{
                    this.$message({type:'error',message:result.Msg});
                }
            });
        },
        backLogin(){
            var _this = this;
            let LoadingIns = _this.$loading({
                text: "执行中"
            });
            var _tval = _this.$staticmethod.Get("Token");
            _this.$axios({
                method: "post",
                url: `${_this.$urlPool.RemoveLogin}`,
                data: { Token: _tval }
            })
            .then(res => {
                _this.$staticmethod.Set("Token", "");
                window.location.href = `${window.bim_config.hasRouterFile}/#/`;
                LoadingIns.close();
            })
            .catch(res => {
                console.warn(res);
                _this.$message.error("服务器错误");
                _this.$staticmethod.Set("Token", "");
                window.location.href = `${window.bim_config.hasRouterFile}/#/`;
                LoadingIns.close();
            });
        },
    },
    created(){
        this.loadOrganizeList();
    }
}
</script>
<style scoped>
ul,
li {
    padding: 0px;
    margin: 0px;
    list-style-type: none;
}
.OrganizeManage_V2 {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    text-align: left;
    font-family: PingFangSC-Regular;
    background: #f0f2f5;
}
.OrganizeManage_V2 .back {
    width: auto;
    height: 25px;
    line-height: 25px;
    font-size: 14px;
    color: rgba(0, 0, 0, 0.85);
    border: none;
    outline: none;
    background-color: transparent;
    margin: 26px 0px 0px 32px;
    padding: 0px;
    letter-spacing: 0.5px;
    cursor: pointer;
}
.OrganizeManage_V2 .list {
    flex: 1;
    width: 1200px;
    background-color: #fff;
    margin: 16px auto;
    overflow: hidden;
}
.OrganizeManage_V2 .list .title .moreMenu {
    width: 120px;
    height: auto;
    padding: 4px 0px 4px 0px;
    background-color: #fff;
    border-radius: 4px;
    position: absolute;
    right: -15px;
    top: 40px;
    display: block;
    box-shadow: 0px 1px 3px 0px rgba(0, 21, 41, 0.12);
    z-index: 50;
    font-size: 0px;
}
.OrganizeManage_V2 .list .title .moreMenu li {
    width: 100%;
    height: 40px;
    line-height: 40px;
    text-align: left;
    text-indent: 16px;
    font-size: 14px;
}
.OrganizeManage_V2 .list .title .moreMenu li:hover {
    background-color: rgba(240, 242, 245, 1);
    cursor: pointer;
}
.OrganizeManage_V2 .list .addOrganize {
    width: 120px;
    height: 40px;
    background: #1990FF;
    border-radius: 2px;
    color: #fff;
    cursor: pointer;
    text-align: center;
    line-height: 40px;
}
.txtSearch {
    width: 240px;
    height: 32px;
    line-height: 32px;
    background: #FFFFFF;
    border-radius: 2px;
    border: 1px solid #D8D8D8;
    padding: 0 8px 0 12px;
    margin-left: 24px;
}
.txtSearch input{
    border: none;
    height: 28px;
    line-height: 28px;
    width: 90%;
}
.txtSearch i {
    display: block;
    float: right;
    margin-top: 6px;
    color: rgba(0, 0, 0, 0.45);
}
.OrganizeManage_V2 .list .con {
    width: calc(100% - 28px);
    height: calc(100% - 49px - 40px);
    padding: 49px 24px 0px 24px;
    overflow-x: hidden;
    overflow-y: scroll;
}
.OrganizeManage_V2 .list .con-list {
    width:100%;
    height: 100%;
    padding: 0;
    overflow: hidden;
}
.OrganizeManage_V2 .list .con .unit {
    width: 100%;
    height: 100px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.04);
    position: relative;
}
.OrganizeManage_V2 .list .con .unit:hover {
    background-color: #f5f5f5;
}
.OrganizeManage_V2 .list .con .unit .more {
    display: block;
    position: absolute;
    right: 35px;
    top: 51%;
    transform: translate(0, -50%);
    color: #888;
    cursor: pointer;
    z-index: 9;
    height: 22px;
}
.OrganizeManage_V2 .list .con .unit .moreMenu {
    display: block;
    height: auto;
    width: 140px;
    overflow: hidden;
    box-shadow: 0px 1px 3px 0px rgba(0, 21, 41, 0.12);
    z-index: 10;
    position: absolute;
    background-color: #fff;
    right: -20px;
    top: 40px;
}
.OrganizeManage_V2 .list .con .unit .moreMenu li {
    display: block;
    height: 40px;
    background-color: #fff;
    text-indent: 40px;
    line-height: 40px;
    cursor: pointer;
    position: relative;
    font-size: 14px;
    color: #595959;
    margin: 4px 0px 4px 0px;
}
.OrganizeManage_V2 .list .con .unit .moreMenu li i {
    width: 25px;
    height: 25px;
    border: none;
    font-size: 18px;
    text-indent: 0px;
    padding: 0px;
    margin: 0px;
    position: absolute;
    top: 50%;
    left: 10px;
    transform: translate(0, -40%);
    color: #595959;
}
.OrganizeManage_V2 .list .con .unit .moreMenu li:hover {
    background-color: #f5f5f5;
}
.OrganizeManage_V2 .list .con .unit .moreMenu li:last-child {
    border: none;
}
.OrganizeManage_V2 .list .con .unit img {
    width: 50px;
    height: 50px;
    position: absolute;
    left: 24px;
    top: 50%;
    transform: translate(0, -50%);
    border-radius: 100px;
    display: block;
    float: left;
    margin-right: 30px;
}
.OrganizeManage_V2 .list .con .unit ul {
    float: left;
    width: calc(100% - 30px - 50px - 24px - 60px);
    height: 60px;
    margin: 22px 0px 0px 104px;
}
.OrganizeManage_V2 .list .con .unit ul li {
    width: 100%;
    text-align: left;
    text-indent: 0px;
    display: block;
    float: left;
    color: rgba(0, 0, 0, 0.45);
    font-size: 14px;
}
.OrganizeManage_V2 .list .con .unit ul .t {
    height: 35px;
    line-height: 25px;
    cursor: pointer;
}
.OrganizeManage_V2 .list .con .unit ul .t:hover span {
    text-decoration: underline;
}
.OrganizeManage_V2 .list .con .unit ul .b {
    height: 25px;
    line-height: 25px;
}
.OrganizeManage_V2 .list .con .unit ul li font {
    padding-left: 5px;
}
.OrganizeManage_V2 .list .con .unit ul li span,
.OrganizeManage_V2 .list .con .unit ul li i {
    float: left;
    font-style: normal;
}
.OrganizeManage_V2 .list .con .unit ul li span {
    font-size: 16px;
    font-weight: bold;
    color: #000;
}
.OrganizeManage_V2 .list .con .unit ul li i {
    width: 75px;
    border-width: 1px;
    border-style: solid;
    font-size: 12px;
    padding: 0px 10px 0px 10px;
    margin: 0px 0px 0px 24px;
    line-height: 23px;
    border-radius: 3px;
    text-align: center;
    display: block;
}
.OrganizeManage_V2 .list .con .unit ul li .blur {
    background-color: rgba(24, 144, 255, 0.1);
    color: #1890FF;
}
.pn {
    position: absolute;
    width: 410px;
    height: 323px;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    box-shadow: 0px 0px 12px -17px rgba(11, 41, 62, 0.4);
    border-bottom: 4px;
    z-index: 20;
}
.cet {
    position: absolute;
    width: 410px;
    height: 254px;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    box-shadow: 0px 0px 12px -17px rgba(11, 41, 62, 0.4);
    border-bottom: 4px;
    z-index: 20;
}
.Covery {
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.4);
    position: absolute;
    top: 0px;
    left: 0px;
    z-index: 1;
}
::-webkit-scrollbar {
    width: 1px;
    height: 1px;
}
::-webkit-scrollbar-thumb {
    border-radius: 12px;
    border: 6px solid transparent;
    box-shadow: 1px 0px 0px #A5ADB7 inset;
}
::-webkit-scrollbar-thumb:hover {
    box-shadow: 1px 0px 0px #4A4A4A inset;
}
.header {
    background: #fff;
}
.header /deep/ .el-menu-item.is-active{
    color: #007AFF;
}
.header /deep/ .el-menu--horizontal>.el-menu-item.is-active{
    border-bottom: 2px solid #007AFF;
    color: #007AFF;
}
.header /deep/ .el-menu.el-menu--horizontal{
    display: flex;
    justify-content: center;
}
.header /deep/ .el-menu--horizontal>.el-menu-item{
    line-height: 44px;
    height: 44px;
    width:200px;
    text-align: center;
}
.header /deep/ .el-menu--horizontal>.el-menu-item{
    font-weight: 400;
    color: #222222;
}
.org-list{
    display: flex;
    flex-direction: column;
    height: 100%;
}
.org-header,.header-right{
    display: flex;
    justify-content: space-between;
}
.org-header{
    margin: 16px 16px 0;
    line-height: 36px;
}
.index-log i{
    width: 14px;
    height: 16px;
    background:url("../../assets/images/index-log.png") no-repeat center;
    display: inline-block;
    background-size: 100%;
    margin-right: 4px;
    vertical-align: middle;
}
</style>
