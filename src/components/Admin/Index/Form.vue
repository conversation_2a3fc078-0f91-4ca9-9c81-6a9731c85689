<template>
    <div v-loading="LoadingData" class="root">
        <div class="head">
            {{ this.Info.title }}<i class="icon-suggested-close" @click="clearData"></i>
        </div>
        <div class="con">
            <ul class="Form" @mousedown="_stopPropagation($event)">
                <li>
                    <compsAnimateInput
                    v-model="OrganizeName"            
                    placeholder=""   
                    :defaultValue="''"
                    height="100%"    
                    fontSize="16px"             
                    :label="'机构名称'" 
                    >             
                    </compsAnimateInput>
                </li>
                <li>
                    <compsAnimateInput
                    v-model="Manager"            
                    placeholder=""   
                    labelFootnote=""   
                    :defaultValue="''"
                    height="100%"        
                    fontSize="16px"           
                    :label="'机构管理员用户名'"
                    >             
                    </compsAnimateInput>
                </li>
                <li>                
                    <compsAnimateInput
                    v-model="ManagerEmail"            
                    placeholder=""   
                    labelFootnote=""   
                    :defaultValue="''"
                    height="100%"        
                    fontSize="16px"           
                    :label="'机构管理员邮箱'"
                    >             
                    </compsAnimateInput>
                </li>
                <li>                
                    <compsAnimateInput
                    v-model="ManagerPwd"            
                    placeholder=""   
                    labelFootnote=""   
                    :defaultValue="''"
                    height="100%"        
                    fontSize="16px"           
                    :label="'机构管理员密码'"
                    >             
                    </compsAnimateInput>
                </li>
            </ul>
            <div v-if="organizeShowSet" class="_css-checkedShow">
                <el-checkbox v-model="organizeShow">隐藏机构名称</el-checkbox>
            </div>
        </div>
        <div class="btns">
            <button class="transparent" @click="clearData">取消</button>
            <button class="blue2" @click="saveForm">确定</button>
        </div>
    </div>
</template>

<script>
import compsAnimateInput from "@/components/CompsCommon/compsAnimateInput";
export default {
    name:'OrganizeForm',
    components:{
        compsAnimateInput
    },
    data(){
        return {
            firstLoad:true,//是否是首次加载
            ManagerId:"",
            Manager:{value:''},//管理员账号（输入框里的）
            OrganizeName:{value:''},//机构名称（输入框里的）
            ManagerEmail:{value:''},//管理员邮箱（输入框里的）
            ManagerPwd:{value:''},//管理员密码（输入框里的）
            saveLoading:"",//加载等待
            allUsers:[],
            allOrganizeName:[], 

            LoadingData:false,
            organizeShow: false,
        };
    },
    props:{
        Info:{
            type:Object,
            required:false
        },
        OrganizeId:{
            type:String,
            required:false
        },
        organizeShowSet: {
            type: Boolean,
            required:false  
        }
    },
    methods: {
        _stopPropagation(ev){
            ev && ev.stopPropagation && ev.stopPropagation();
        },
        saveForm(){
            if(this.OrganizeName.value==""){
                this.$message.warning('请输入机构名称')
                return;
            }
            if(this.Manager.value==""){
                this.$message.warning('请输入用户名')
                return;
            }
            if(this.ManagerEmail.value==""){
                this.$message.warning('请输入邮箱')
                return;
            }
            let reg= /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/
            if(!reg.test(this.ManagerEmail.value)){
                this.$message.warning('请输入正确的邮箱格式')
                return;
            }
            if(this.ManagerPwd.value==""){
                this.$message.warning('请输入密码')
                return;
            }
             
            const Token = this.$staticmethod.Get("Token");
            
           
            this.LoadingData=true;
            
            let data = {
                OrganizeId: '',
                OrganizeName: this.OrganizeName.value,
                ManagerName: this.Manager.value,
                ManagerEmail: this.ManagerEmail.value,
                ManagerPwd: this.ManagerPwd.value,
                Account: this.Manager.value,
                ShowName: !this.organizeShow,  // 勾选=隐藏=false，所以写了!this.organizeShow
                Token: Token
            }
            
            
            this.$axios.post(`${this.$urlPool.AddOrganize}`,data).then(res=>{
                this.LoadingData=false;
                if (res.status == 200) {
                    if (res.data.Ret > 0) {
                        this.$message({type:"success",message:"添加机构成功"});
                        this.ManagerId="";
                        this.Manager.value="";
                        this.OrganizeName.value="";
                        this.$emit('loadList');
                        this.$emit('closeForm',false);
                    } else {
                          this.$message({type:"error",message:`保存失败：${res.data.Msg}`});
                    }
                } else {
                     this.$message({type:"error",message:`服务器错误${res.status}`});
                }
                
            });
        },
        clearData()
        {
            this.OrganizeName.value="";
            this.Manager="";
            this.$emit('closeForm',false);
        }
    },
    created(){
        
    },
    mounted(){

    },
    watch:{
        OrganizeId(val)
        {
            if(val==""||val=="-1")
                return;
            else{
                this.LoadingData=true;
                var Token = this.$staticmethod.Get("Token");
                this.$axios.get(this.$urlPool.base_MainSystemApi + this.$urlPool.GetOrganize_Unit+"?Token="+Token+"&organizeId="+this.OrganizeId).then(res=>{
                    var organizeObj=res.data.Data;
                    this.ManagerId=organizeObj.ManagerId;
                    this.Manager=organizeObj.Manager;
                    this.OrganizeName.value=organizeObj.FullName;
                    this.LoadingData=false;
                });
            }
        }
    }
}
</script>
<style scoped>
ul{padding: 0px;margin: 0px;}ul li{list-style-type: none;}i::before{float: left;text-indent: -1px;text-align:center;text-indent:0px;}
.root{width:400px;background-color:#fff;box-shadow: 0px 0px 4px #fff;border-radius:2px;}
.root .head{width:100%;height:48px;position: relative;float:left;line-height: 48px;font-size: 16px;font-family: 黑体;color:#000;text-align:left;text-indent:17px;cursor:move;}
.root .head i{position: absolute;width:16px;height:16px;right:14px;top:14px;}.root .head i:hover{cursor: pointer;}.root .head i::before{width:16px;height:16px;opacity: 0.4;}
.root .con{width:100%;height:calc(100% - 48px - 64px);float:left;}
.root .btns{width:calc(100% - 30px);height:64px;float:left;}

.Form{display:block;width:100%;height: 100%;overflow-y:visible;overflow: none;}
.Form li{width:calc(100% - 32px);height:60px;margin:10px 0px 15px 0px;background-color:transparent;margin-left: 16px;border-radius:2px;position: relative;}
.Form .wrong{border:1px solid red;border-radius:4px;width:calc(100% - 34px);height:38px;line-height: 38px;margin-left: 15px;}
.Form li .red{position: absolute;right:10px;top:40px;display: block;text-align: left;font-size: 12px;color:red;width:auto;height:20px;line-height:20px;}

.Form li span{display: inline-block;text-align: center;height: 100%;font-size: 14px;position:relative;}
.Form li span i{position: absolute;left:8px;top:10px;width:18px;}
.Form li.wrong span i{position: absolute;left:8px;top:9px;width:18px;}
.Form li span.t{width:100px;text-align:left;text-indent:30px;float:left;}.Form li span.c{width:calc(100% - 100px);}.Form li.wrong span.c{width:calc(100% - 101px);margin-left:1px;float:left;}
.Form li span.txt input{width:242px;height:22px;border:none;background-color: transparent;outline:none;}

.selectUser{position:absolute;width:calc(100% - 2px);overflow: hidden;visibility:hidden;}
.show_in{animation: heightShowIn 0.5s forwards ;}
.show_out{animation: heightShowOut 0.5s forwards; }

input::-webkit-input-placeholder{color:#8e8e8e;}
input::-moz-placeholder{color:#8e8e8e;}
input:-moz-placeholder{color:#8e8e8e;}
input:-ms-input-placeholder{color:#8e8e8e;}

.btns{line-height:64px;text-align: right;margin-left:15px;}
.btns button{width:75px;height:35px;line-height:32px;outline:none;border:none;border-radius:2px;text-align: center;overflow: hidden;cursor: pointer;}
.btns button:hover{opacity: 0.8;}
.btns button.blue1{background-color:#80C2FF;color: #fff; }
.btns button.blue2{background-color:#1890FF;color: #fff; }
.btns button.transparent{background-color: transparent;color: #8a8a8a;}

::-webkit-scrollbar {width: 20px;height: 8px;}
::-webkit-scrollbar-thumb{border-radius: 12px;border: 6px solid transparent;box-shadow: 8px 0px 0px #A5ADB7 inset;}
::-webkit-scrollbar-thumb:hover {box-shadow: 8px 0px 0px #4A4A4A inset;}
._css-checkedShow{
  text-align: left;
  margin-left: 16px;
  margin-top: 16px;
}
._css-checkedShow /deep/ .el-checkbox__inner {
  width: 14px;
  height: 14px;
}
._css-checkedShow
  /deep/
  .el-checkbox__inner::after {
  left: 5px;
}
._css-checkedShow /deep/ .el-checkbox__label {
  padding-left: 14px;
}
._css-checkedShow
  /deep/
  .el-date-editor.el-input,
.el-date-editor.el-input__inner {
  width: 168px;
}
._css-checkedShow
  /deep/
  .el-checkbox__input.is-checked
  + .el-checkbox__label {
  color: rgba(0, 0, 0, 0.65);
}

@keyframes heightShowIn{
    from{height:0;}
    to{height:300px;visibility: visible;}
}
@keyframes heightShowOut{
    from{height:300px;visibility: visible;}
    to{height:0px;visibility: hidden;}
}
@keyframes tipHide{
    form{opacity: 1;}
    to{opacity: 0;}
}
</style>
