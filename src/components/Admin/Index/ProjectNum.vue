<template>
    <div class="role_ProjectNum">
        <div class="title">项目授权<i class="icon-suggested-close close" @click="$emit('CloseProjectNum')"></i></div>
        <div class="con">
            <div class="rootCode">
                <input id="txtRootCode" type="text" v-model="rootCode" value="" />
                <button @click="copyTo()">复制机器码</button>
            </div>
            <div class="setRole">
                <textarea :class="isFocusTextarea?'blur':''" v-model="roleCode" placeholder="请输入授权码" @focus="textareaFocus" @blur="textareaBlur"></textarea>
            </div>
        </div>
        <div class="btns">
            <button class="white" @click="$emit('CloseProjectNum')">取消</button>
            <button class="blue" @click="SaveProjectNum()">确定</button>
        </div>
    </div>
</template>
<script>
export default {
    name:'role_ProjectNum',
    components:{

    },
    data(){
        return {
            isFocusTextarea:false,
            rootCode:"",
            roleCode:"",
        };
    },
    props:{
        OrganizeId:{
            type:String,
            required:false,
        }
    },
    methods:{
        SaveProjectNum()
        {
            var Token=this.$staticmethod.Get('Token');
            var JsonData={};
            JsonData.Token=Token;
            JsonData.MachineCode=this.rootCode;
            JsonData.AuthCode=this.roleCode;
            JsonData.ProjectId=this.OrganizeId;

            const loading = this.$loading({
                lock: true,
                text: '正在授权中，请您耐心等待...',
                spinner: 'el-icon-loading',
                background: 'rgba(255,255,255, 0.5)'
            });
            this.$axios.post(this.$urlPool.SaveCompanyAuth, JsonData).then(res=>{
                console.log(res);
                let sign=res.data.Ret;
                loading.close();
                if(sign==1)
                {
                    this.$message({type:'success',message:'授权成功'});
                    this.$emit('loadList');
                    this.$emit('CloseProjectNum');
                }
            });
        },
        textareaFocus(){
            this.isFocusTextarea=true;
        },
        textareaBlur(){
            this.isFocusTextarea=false;
        },
        copyTo(){
            document.getElementById("txtRootCode").select();
            document.execCommand("Copy");
            this.$message({type:'success',message:'复制成功'});
        }
    },
    mounted(){
        this.$axios.get(`${this.$urlPool.GetMachineCode}?Token=${this.$staticmethod.Get('Token')}`).then(res=>{
            this.rootCode=res.data.Msg;
        });
    }
}
</script>
<style scoped>
    .role_ProjectNum{overflow: hidden;background-color: #fff;box-shadow:0px 13px 24px -17px rgba(11,41,62,0.8);border-radius:4px;font-family:PingFangSC-Medium;padding:0px 24px 0px 24px;}
    .role_ProjectNum .title{width:100%;height:60px;font-size:16px;color:#333;line-height:60px;text-align:left;font-weight:bold; }
    .role_ProjectNum .title .close{width:18px;height:18px;position: absolute;right:15px;top:15px;display:block;}
    .role_ProjectNum .title .close::before{float:left;width:18px;height:18px;display:block;text-align:left;text-indent:0px;cursor: pointer;}
    .role_ProjectNum .btns{width: 100%;height:56px;line-height:64px;text-align: right;}
    .role_ProjectNum .con{width:100%;height:calc(100% - 60px - 64px);overflow:hidden;max-height:420px;min-height:40px;}
    .role_ProjectNum .rootCode{height:40px;width:100%;line-height:40px;font-size: 0px;}
    .role_ProjectNum .rootCode input{padding: 0px;margin: 0px;border:none;width:calc(100% - 99px);height:32px;display:block;float:left;margin:4px 2px 0px 2px;outline-color: #e0e0e0;}
    .role_ProjectNum .rootCode button{width:95px;color:#fff;height:32px;margin: 0px;padding: 0px;font-size:14px;display:block;float:left;margin-top: 4px;background-color:#1890FF;border:none;outline: none;border-radius: 2px;cursor: pointer;line-height: 32px;}
    .role_ProjectNum .btns button{width:56px;height: 32px;border:none;outline: none;border-radius: 3px;cursor: pointer;font-size:12px; letter-spacing:1px;}
    .role_ProjectNum .btns .blue{background-color:#1890FF;color:#fff; }
    .role_ProjectNum .btns .white{background-color:transparent;color:#666;}
    .role_ProjectNum .setRole{width:100%;height:calc(100% - 60px);margin-top: 20px;}
    .role_ProjectNum .setRole textarea{width:calc(100% - 8px);height:calc(100% - 10px);margin-top:4px;border:none;border-bottom:1px solid #ccc;outline:none;resize:none;font-size:16px;font-family: 黑体;text-indent:1px;}
    .role_ProjectNum .setRole .blur{border-color: #1890FF;}
</style>
