<template>
	<div class="project-menu-list">
		<div class="list-tree-left">
			<div class="left-title">
				<div class="companu-name name-list">菜单目录</div> 
				<div class="companu-name name-click" @click.stop="addFirstTreeList('站点')">{{ info_companyname }}</div>
			</div>
			<div class="left-tree-list left-menu">
				<el-menu
					:default-active="defaultActive"
					class="el-menu-vertical-demo css-menu-list"
					@select="handelSelectMenuList"
					@open="openList"
					@close="openList"
				>
					<div
						v-for="(item,index) in treeData" 
						:key='index' 
						:index="index + ''"
					>
						<el-submenu :index="index + ''" v-if="item.Children.length > 0">
							<template slot="title" > 
								<i class="icon-bg icon-img icon-img-before" v-if="getIconClass(item.MenuIcon)" :class="item.MenuIcon"></i>
								<i class="icon-bg icon-img" v-else :class="item.MenuIcon"></i>
								<span>{{item.MenuName}}</span>
							</template>
							<div 
								v-for="(itemC,indexC) in item.Children" :key='indexC'  
								:index="index + '-' + indexC" 
							>
								<el-submenu :index="index + '-' + indexC" v-if="itemC.Children.length > 0">
									<template slot="title"><span class="text-padding64">{{ itemC.MenuName }}</span></template>
										<el-menu-item 
											@click.native="handleSelectClick(itemC3,'333')"
											v-for="(itemC3,indexC3) in itemC.Children" :key='indexC3'
											:index="index+'-' +indexC+'-' + indexC3">
											<span class="text-padding76">{{itemC3.MenuName}}</span>
										</el-menu-item>
								</el-submenu>

								<el-menu-item @click.native="handleSelectClick(itemC,'222')" :index="index+1+'-' + indexC + 1" v-else>
									<span class="text-padding64">{{itemC.MenuName}}</span>
								</el-menu-item>
							</div>
						</el-submenu>
						<el-menu-item :index="index + ''" @click.native="handleSelectClick(item,'111')" v-else>
							<i class="icon-bg icon-img icon-img-before" v-if="getIconClass(item.MenuIcon)" :class="item.MenuIcon"></i>
							<i class="icon-bg icon-img" v-else :class="item.MenuIcon"></i>
							<span>{{item.MenuName}}</span>
						</el-menu-item>
					</div>
				</el-menu>
			</div> 
		</div>
		<div class="list-tree-right" >
			<div class="right-title">
				当前目录：<span class="c007AFF">{{ rightShowClickText }}</span>
			</div>
			<div class="css-right-btn" v-if="rightShowClickText !=''">
				<div class="edit-btn" v-if="addMenuJudgment()" @click.stop="addFirstTreeList(clickTreeListItem)">
					<p class="btn-icon"><i class="icon-f-add"></i></p>
					<p class="btn-text">新增</p>
				</div>
				<div class="edit-btn" v-if="editMenuJudgment(1)" @click.stop="formDialogEdit(clickTreeListItem)">
					<p class="btn-icon"><i class="icon-f-edit"></i></p>
					<p class="btn-text">编辑</p>
				</div>
				<div class="edit-btn" v-if="editMenuJudgment(2)" @click.stop="DeleteMenu(clickTreeListItem)">
					<p class="btn-icon"><i class="icon-f-del"></i></p>
					<p class="btn-text">删除</p>
				</div>
			</div>
		</div>
		<zdialog-function
      :init_title="editStateText + '菜单：' + rightShowClickText"
      :init_zindex="1003"
      :init_innerWidth="640"
      :init_width="640"
			:init_height="477"
      init_closebtniconfontclass="icon-suggested-close"
      :init_usecustomtitlearea="false"
      @onclose="resetForm()"
      v-if="showFormDialog"
    >
			<div slot="mainslot" class="form-list" @mousedown="_stopPropagation($event)">
				<div class="form-list">
					<el-form :model="ruleForm" :rules="rules" ref="ruleForm" label-width="110px" class="demo-ruleForm">
						<el-form-item label="机构ID" prop="OrganizeId" style="display:none;">
							<el-input v-model="ruleForm.MenuOrganizeIdName" placeholder="请输入菜单名称"></el-input>
						</el-form-item>
						<el-form-item label="菜单名称" prop="MenuName">
							<el-input v-model="ruleForm.MenuName" placeholder="请输入菜单名称"></el-input>
						</el-form-item>
						<el-form-item label="菜单描述">
							<el-input v-model="ruleForm.Description" placeholder="请输入菜单描述"></el-input>
						</el-form-item>
						<el-form-item label="父级菜单" v-if="addMenuListType != 0 && editStateText != '编辑'">
							<el-input class="parent-readonly" v-model="rightShowClickText" placeholder="" readonly></el-input>
						</el-form-item>
						<el-form-item label="菜单地址">
							<el-input class="parent-readonly" v-model="ruleForm.RoutePath" placeholder="请输入菜单地址"></el-input>
						</el-form-item>
						<el-form-item label="菜单类型" prop="MenuType">
							<el-select v-model="ruleForm.MenuType" :options="ruleForm.MenuType" class="ccfolw-list" placeholder="请选择菜单类型" @change="menuListTypeChange">
								<el-option v-for="(item,index) in typeSelectOption" :label="item.label" :value="item.value" :key="index"></el-option>
							</el-select>
							<el-select  v-if="isTypeFlow || ruleForm.MenuType == 3" class="search-listtype" v-model="ruleForm.BusinessCode" filterable placeholder="搜索流程名称" @change="menuListFlowNoChange">
								<el-option v-for="(item,index) in flowSelectOption" :label="item.name" :value="item.no" :key="index"></el-option>
							</el-select>
							<el-input style="width: 350px;" v-if="ruleForm.MenuType == 2" v-model="ruleForm.BusinessCode" placeholder=""></el-input>
						</el-form-item>
						<el-form-item label="菜单编码">
							<el-input class="parent-readonly" v-model="ruleForm.MenuCode" placeholder="请输入菜单编码，菜单名称的拼音"></el-input>
						</el-form-item>
						<el-form-item label="模块图标">
							<el-input v-model="ruleForm.MenuIcon" placeholder="请输入图标名称"></el-input>
						</el-form-item>
						<el-form-item label="菜单位置">
							<el-select class="search-listtype" v-model="ruleForm.NextMenuId" filterable placeholder="搜索放在某个菜单之前" @change="menuListNextMenuChange">
								<el-option v-for="(item,index) in menuListSelectArr" :label="item.MenuName" :value="item.Id" :key="index"></el-option>
							</el-select>
							<!-- <el-input class="parent-readonly" v-model="ruleForm.NextMenuId" placeholder="请选择菜单存放位置"></el-input> -->
						</el-form-item>
						<!-- <el-form-item label="权限按钮">
							<el-input class="parent-readonly" v-model="ruleForm.Buttons" placeholder=""></el-input>
						</el-form-item> -->
						
						
					</el-form>
				</div>
			</div>
			<div slot="buttonslot" class="css-common-zdialogbtnctn" >
				<zbutton-function
						:init_text="'取消'"
						:init_fontsize="14"
						:debugmode="true"
						:init_height="undefined"
						:init_width="'76px'"
						:init_bgcolor="'#fff'"
						:init_color="'#1890FF'"
						@onclick="resetForm()">
				</zbutton-function>
				<zbutton-function
					:init_text="'确定'"
					:init_fontsize="14"
					:debugmode="true"
					:init_height="undefined"
					:init_width="'76px'"
					@onclick="submitForm()">
				</zbutton-function>
			</div>
		</zdialog-function>
	</div>
</template>
<script>
let pinyin = require('js-pinyin');

import { EventBus } from "@static/event.js";
export default {
	name: "siteLevelMenuList",
	data() {
		return {
			token: '',
			treeData: [],
			// NotAddList: [],
			// NotEditDelList: [],
			// 项目主页、任务中心、我的任务、全部任务、模型菜单、全部模型、场景管理、模型校验、问题协同、项目文档、单元管理、进度方案、进度填报、现场巡查、质量验收、现场巡查、全景图管理、全景图对比、会议室管理、会议看板、会议控制台项目设置、分享管理、角色权限、菜单管理、签章管理、项目成员、流程管理、项目日志、质量划分、进度报送、
			NotAddList: ['XMZY','RWZX','WDRW','QBRW','MODEL','QBMX','CJGL','WTXT','XMWD', 'DYGL','JDFA','JDTB','ZLXCXC','ZLYS','AQXCXC','QJTGL','QJTDB','HYSGL','HYKB','HYKZT','XMSZ','FXGL','JSQX','CDGL','QZGL','XMCY','LCGL','XMRZ','ZLHF','JDBS'],
			// 原有的一级菜单、原有的一级普通菜单：项目主页、任务中心、我的任务、全部任务、模型管理、模型菜单、全部模型、场景管理、场景管理（二级）、模型校验、问题协同、文档管理、项目文档、工程结构、单元管理、进度管理、进度方案、进度填报、质量管理、现场巡查、质量验收、安全管理、现场巡查、720全景、全景图管理、全景图对比、会议管理、会议室管理、会议看板、会议控制台项目设置、分享管理、角色权限、菜单管理、签章管理、项目成员、流程管理、项目日志、质量划分、进度报送、
			NotEditDelList:  ['XMZY','RWZX','WDRW','QBRW','MXGL','MODEL','QBMX','CJGL_1', 'CJGL','WTZZ','WTXT','WDGL','XMWD','GCJG', 'DYGL','JDGL','JDFA','JDTB','ZLGL','ZLXCXC','ZLYS','AQGL','AQXCXC','720QJ','QJTGL','QJTDB','HYGL','HYSGL','HYKB','HYKZT','XMSZ','FXGL','JSQX','CDGL','QZGL','XMCY','LCGL','XMRZ','ZLHF','JDBS'],
			iconListArr: ["icon-newface-anquan","icon-newface-biangeng","icon-newface-chengben","icon-newface-gongcheng","icon-newface-hetong","icon-newface-home","icon-newface-huiyi","icon-newface-jiashicang","icon-newface-jindu","icon-newface-kancha","icon-newface-liucheng","icon-newface-moxing","icon-newface-qianqiguanli","icon-newface-quanjing","icon-newface-scene","icon-newface-set","icon-newface-sheji","icon-newface-touzi","icon-newface-wendang","icon-newface-xianchang","icon-newface-zhiliang"],
			ruleForm: {
				Id:'',
				MenuName: '',  // 菜单名称
				OrganizeId: '', // 站点级=0，机构下是机构id，项目下是项目id
				Description: '',  // 菜单描述
				ParentId: '',  // 父级ID 一级传值0
				RoutePath: '',  // 菜单路由地址，默认和ComponentPath一样
				ComponentPath: '',  // 路由地址
				NextMenuId: '',  // 选择放在某个菜单之后，传空位最后一个
				MenuType: 1,  // 菜单类型
				MenuCode: '',  // 后端规定写的是当前菜单名称的拼音、没有的话、自动转换成拼音大写字母
				MenuIcon: '',  // 图标
				BusinessCode: '', // BusinessCode  模型阶段值、流程菜单编码、三方菜单是返回地址、普通菜单是空、（模型菜单在项目下创建的，不是在这里）
				RequiresAuth: true,
				Buttons: [],  // 添加按钮
				/*
				Buttons   数组对象{
					"ButtonName": "string",
					"ButtonIcon": "string",
					"Description": "string",
					"ButtonSort": 0
				*/
			},
			rules: {
				MenuName: [
          { required: true, message: '菜单名称', trigger: 'blur' },
				],
				MenuType: [
					{ required: true, message: '请选择菜单类型', trigger: 'change' }
				],
			},
			typeSelectOption: [
				{ label: '普通菜单', value: 1},
				{ label: '自定义菜单', value: 2},
				{ label: '流程菜单', value: 3},
				// { label: '模型菜单', value: 4},  谢鹏说暂时先删掉4、5
				// { label: '文档菜单', value: 5},
			],
			defaultProps: {
				children: "children",
				label: "text",
			},
			clickAddCheckedParams: null, // 记录当前点击的父级
			showFormDialog: false,  // 表单填写框显示
			clickTreeListItem: {}, // 保存点击当前树结构的值
			addMenuListType: 0, // 0 新增一级，1 点击的iconlevel-1，新增的是二级，2 点击的iconlevel-2，新增的是二级


			isTypeFlow: false, // 是否选择的是流程
			rightShowClickText: '一级菜单', // 当前选中的菜单类型
			editStateText: '新增', // 新增add 编辑edit
			
			flowSelectOption: [], // 流程/表单数据

			defaultActive: '1',
			menuListSelectArr: [], // 菜单级别下可移动的菜单list
			menuListByTypeObj: {}, // 按照menutype分类 
			/*
				普通系统菜单 1
				自定义菜单 2
				流程菜单 3
				模型菜单 4
				文档类菜单 5
			*/
		};
	},
	props: {
		info_companyname: {
			type: String,
			required:true
		},
		OrganizeId: {
			type: String,
			default: '0'
		},
	},
	mounted() {
		this.token = this.$staticmethod.Get("Token");
		this.ruleForm.OrganizeId = this.OrganizeId; // 初始化的是机构id
		this.getMenuTree();
		
	},
	methods: {
		getTypeText(number){
			let status = ''
			switch(number) { 
				case 1: 
					status = '普通菜单'; 
					break; 
				case 2: 
					status = '自定义菜单'; 
					break; 
			  case 3: 
					status = '流程菜单'; 
					break; 
				case 4: 
					status = '模型菜单'; 
					break; 
				case 5: 
					status = '文档菜单'; 
					break; 
			}
			return status
		},
		getIconClass(icon){
			if(icon == null) return true
			let bool = true
			icon.indexOf('newface') == -1 ? bool = true : bool = false
			return bool
		},
		// 创建菜单
		createMenu(params){
			this.$axios
				.post(`${this.$urlPool.CreateMenu}?token=${this.token}`,params)
				.then(res=>{
					if(res.data.Ret == 1){
						this.$message.success(res.data.Msg);
						this.getMenuTree();
						// 创建成功后清空form表单
						this.resetForm();
						this.resetMenuList();
					}else{
						this.$message.error(res.data.Msg);
					}
				})
				.catch(err=>{})
		},
		// 加载菜单树结构
		getMenuTree(){
			this.$axios
				.get(`${this.$urlPool.menuTree}?token=${this.token}&organizeId=${this.OrganizeId}&parentId=0`)
				.then(res=>{
					if(res.data.Ret == 1){
						this.treeData = res.data.Data;
						let arr = this.flatten(res.data.Data)
						let getMenuTypeObj  = arr.reduce((result, menu) => {
							const { MenuLevel } = menu;
							if (!result[MenuLevel]) {
								result[MenuLevel] = [];
							}
							result[MenuLevel].push(menu);
							return result;
						}, {});

						this.menuListByTypeObj = getMenuTypeObj;
						EventBus.$emit("Rupdatephasecnt");
					}else{
						this.$message.error(res.data.Msg);
					}
				})
				.catch(err=>{})
		},
		// 删除菜单
		DeleteMenu(data){
			this.$confirm("确定删除当前菜单", {
        confirmButtonText:'确定',
        cancelButtonText:'取消',
        type:'warning'
      }).then(() => {
				this.$axios
					.post(`${this.$urlPool.DeleteMenu}?id=${data.Id}&token=${this.token}`)
					.then(res=>{
						if(res.data.Ret == 1){
							this.$message.success(res.data.Msg);
							this.getMenuTree();
							this.rightShowClickText = '';  // 删除要清空当前选中的text
						}else{
							this.$message.error(res.data.Msg);
						}
					})
					.catch(res=>{})
      }).catch((err) => {
        console.log(err)
      })
		},

		// 编辑菜单
		ModifyMenu(){
			this.$axios
				.post(`${this.$urlPool.ModifyMenu}?token=${this.token}`,this.ruleForm)
				.then(res=>{
					if(res.data.Ret == 1){
						this.getMenuTree();
						this.$message.success(res.data.Msg);
						this.resetForm();
						// 创建成功后清空form表单
					}else{
						this.$message.error(res.data.Msg);
					}
				})
				.catch(err=>{})
		},
		_stopPropagation(ev){      
			ev && ev.stopPropagation && ev.stopPropagation();     
    },
		// 新增一级菜单
		addFirstTreeList(params){
			if(params == '站点' || Object.keys(params).length === 0){
				this.rightShowClickText = '一级菜单';
				this.clickTreeListItem = { 
					MenuName: '',
					OrganizeId: this.OrganizeId,
					Description: '',
					ParentId: '0',
					RoutePath: '',
					ComponentPath: '',
					NextMenuId: '',
					MenuType: '',
					MenuCode: '',
					MenuIcon: '',
					BusinessCode: '', // BusinessCode  模型阶段值、流程菜单编码、三方菜单是返回地址、普通菜单是空
					RequiresAuth: true,
					Buttons: []
				}
				this.addMenuListType = 0;
				this.menuListSelectArr = this.menuListByTypeObj[1]
			
			}else{
				this.rightShowClickText = params.MenuName || '一级菜单';
				this.clickAddCheckedParams = params; // 记录当前点击的父级
				this.clickTreeListItem = {
					MenuName: '',
					OrganizeId: this.OrganizeId,
					Description: '',
					ParentId: params.Id,
					RoutePath: '',
					ComponentPath: '',
					NextMenuId: '',
					MenuType: '',
					MenuCode: '',
					MenuIcon: '',
					BusinessCode: '', // BusinessCode  模型阶段值、流程菜单编码、三方菜单是返回地址、普通菜单是空
					RequiresAuth: true,
					Buttons: []
				}
				this.addMenuListType = params.MenuLevel;
				this.menuListSelectArr = this.menuListByTypeObj[params.MenuLevel]
			}

			this.editStateText = '新增';
			this.ruleForm = this.clickTreeListItem;
			this.showFormDialog = true;
		},
		// 提交表单
		submitForm() {
			this.$refs.ruleForm.validate((valid) => {
				if (valid) {
					this.saveMenuList();
				} else {
					return false;
				}
			});
		},
		// 点击表单提交
		saveMenuList(){  
			if(!this.ruleForm.MenuCode){
				if(this.clickAddCheckedParams && this.ruleForm.MenuCode.length == 0 && this.rightShowClickText !== '一级菜单' && this.clickAddCheckedParams.MenuName == this.ruleForm.MenuName){
					this.ruleForm.MenuCode = pinyin.getCamelChars(this.ruleForm.MenuName) + '_1'
				}else{
					this.ruleForm.MenuCode = pinyin.getCamelChars(this.ruleForm.MenuName)
				}
			}
			this.$axios
				.get(`${this.$urlPool.GetSystemButton}?menuCode=${this.ruleForm.MenuCode}&token=${this.token}`)
				.then(res=>{
					if(res.data.Ret == 1){
						let _d = res.data.Data;
						let _btn = [];
						_d.forEach((item)=>{
							_btn.push(
								{
									ButtonCode:item.ButtonCode,
									ButtonName:item.ButtonName,
									ButtonIcon:item.ButtonIcon,
									Description:item.ButtonName,
									ButtonSort: item.ButtonSort
								}
							)
						})
						this.ruleForm.Buttons = _btn; 
						this.sureGetBtnSubmit();
					}else{
						this.ruleForm.Buttons = [];
						this.sureGetBtnSubmit();
					} 
				})
				.catch(err=>{
					this.ruleForm.Buttons = [];
					this.sureGetBtnSubmit();
				})
			console.log(this.ruleForm)
			// 判断点击的谁然后获取到值，调用创建接口
			
			// 保存成功后清空表单
		},
		sureGetBtnSubmit(){
			// 如果上级菜单和当前菜单名称一样，menucode在后面加上_1
			if(this.editStateText == '编辑'){
				this.ModifyMenu(this.ruleForm)
			}else{
				// if(this.ruleForm.MenuType == 4){
					// 	let newval = "v" + this.convertDateToStr(new Date());
				// 	this.ruleForm.RoutePath = `/#/Home/ProjectBoot/Model/@OrganizeId/@Token/${newval}`
				// 	this.ruleForm.BusinessCode = newval;
				// 	this.ruleForm.MenuCode = 'MODEL';
				// }
				if(this.clickAddCheckedParams && this.ruleForm.MenuCode.length == 0 && this.rightShowClickText !== '一级菜单' && this.clickAddCheckedParams.MenuName == this.ruleForm.MenuName){
					this.ruleForm.MenuCode = pinyin.getCamelChars(this.ruleForm.MenuName) + '_1'
				}else{
					if(!this.ruleForm.MenuCode){
						this.ruleForm.MenuCode = pinyin.getCamelChars(this.ruleForm.MenuName)
					}
				}
				this.createMenu(this.ruleForm)
			}
		},
		convertDateToStr(dt) {
			let year = dt.getFullYear();
			let month = dt.getMonth() + 1;
			let date = dt.getDate();
			let hour = dt.getHours();
			let minute = dt.getMinutes();
			let second = dt.getSeconds();
			let ms = dt.getMilliseconds();
			return (
				year + "" + month + "" + date + "" + hour + "" + minute + "" + second + "" + ms
			);
		},
		// 点击取消、清空表单
		resetForm(){
			this.$refs.ruleForm.resetFields();
			this.showFormDialog = false;
			this.isTypeFlow = false;
		},
		handelSelectMenuList(key){
      this.defaultActive = key;
    },
		openList(index){
			// 含有-是子集，open和close返回的值都是index，都调用openList这个方法，
			if(index.indexOf('-') > 0){
				let index_ = index.split('-')[0];
				let index2_ = index.split('-')[1];
				let data = this.treeData[index_].Children[index2_]
				this.rightShowClickText = data.MenuName;
				this.clickTreeListItem = data;
			}else{
				let _data = this.treeData[index]
				this.rightShowClickText = _data.MenuName;
				this.clickTreeListItem = _data;
			}
		},
		handleSelectClick(data,str){
			this.rightShowClickText = data.MenuName;
			this.clickTreeListItem = data;
		},
		
		// 点击编辑
		formDialogEdit(data){
			this.editStateText = '编辑';
			this.addMenuListType = data.MenuLevel;
			this.menuListSelectArr = this.menuListByTypeObj[data.MenuLevel]
			// 编辑时候先获取菜单的按钮
			this.getFlowList('流程'), // 再次调用下流程菜单的数据、如果是流程菜单需要显示对应的流程菜单名称
			this.$axios
				.get(`${this.$urlPool.menuInfo}?token=${this.token}&id=${data.Id}`)
				.then(res=>{
					if(res.data.Ret == 1){
						
						this.ruleForm = res.data.Data;
						this.showFormDialog = true;	
					}else{
						this.$message.error(res.data.Msg);
					}
				})
				.catch(err=>{})
		},
		addMenuJudgment(){
			let addAuth = true;
			// 站点级=当前逻辑不能新增的菜单：三级、任务中心、模型菜单、项目主页、项目设置、&& this.clickTreeListItem.MenuCode != 'xiangmushezhi'
			if(this.NotAddList.indexOf(this.clickTreeListItem.MenuCode) > -1 ){
				addAuth = false;
			}
			if(this.ruleForm.MenuLevel == 3 ){
				addAuth = false;
			}
			return addAuth;
		},
		editMenuJudgment(number){
			// 普通菜单不能编辑、删除和编辑一样
			let editAuth = true;
			// 站点级=当前逻辑不能新增的菜单：三级、原来的一级菜单、项目设置、&& this.clickTreeListItem.MenuCode != 'xiangmushezhi'
			if(this.NotEditDelList.indexOf(this.clickTreeListItem.MenuCode) > -1 ){
				editAuth = false;
			}
			// 2=删除、一级菜单没有删除按钮
			if(number == 2 && this.rightShowClickText == '一级菜单'){
				editAuth = false;
			}
			return editAuth;
		},
		// 重置
		resetMenuList(){
			this.rightShowClickText = '一级菜单';
			this.defaultActive =  '1';
			this.clickTreeListItem = {};
		},
		menuListFlowNoChange(value){
			// console.log(value)
			this.ruleForm.BusinessCode = value;
		},
		// 选择菜单类型
		menuListTypeChange(value){
			this.ruleForm.MenuType = value
			this.ruleForm.BusinessCode = ''
			if(value == 3){
				this.isTypeFlow = true;
				this.getFlowList('流程');
			}else{
				this.isTypeFlow = false;
				this.flowSelectOption = [];
			}
		},
		// 选择当前菜单位置
		menuListNextMenuChange(value){

		},
		flatten(arr) {
			let result = [];
			arr.forEach(item => {
				result.push(item);
				if (item.Children.length > 0) {
					result = result.concat(this.flatten(item.Children));
				}
			});
			return result;
		},
	
		// 获取流程菜单的数据
		getFlowList(type){
			let url = '';
			let _OrganizeId;
			this.OrganizeId == 0 ?  _OrganizeId = -1 : _OrganizeId = this.OrganizeId;
			type == '流程' ?
				url = `${window.bim_config.CCFlowUrl}/api/v1/flow/queryflows?userNo=${this.$staticmethod.Get("Account")}&organizeId=${_OrganizeId}&flowSort=`
			:
				url = `${window.bim_config.CCFlowUrl}/api/v1/flow/queryfrm?userNo=${this.$staticmethod.Get("Account")}&organizeId=${_OrganizeId}&frmType=`
			this.$axios
				.get(url)
				.then((res) => {
					if(res.status == 200){
						this.flowSelectOption = res.data;
					}else{
						this.flowSelectOption = []
					}
				})
				.catch((err) => {});
		},
		convertDateToStr() {
			let dt = new Date();
      var year = dt.getFullYear();
      var month = dt.getMonth() + 1;
      var date = dt.getDate();
      var hour = dt.getHours();
      var minute = dt.getMinutes();
      var second = dt.getSeconds();
      var ms = dt.getMilliseconds();
      return (
        year + "" + month + "" + date + "" + hour + "" + minute + "" + second + "" + ms
      );
    },
		
		
	 
		// */
	},
};
</script>
<style lang="scss" scoped >
.project-menu-list {
	margin: 16px auto;
	position: relative;
	height: calc(100% - 10px);
	overflow-y: auto;
	box-shadow: 0px 13px 24px -17px rgba(11, 41, 62, 0.8);
	border-radius: 4px;
	font-family: PingFangSC-Medium; 
	color: #606266;
	text-align: left;
	width: 1200px;
	display: flex;
	flex-direction: row;
	// background-color: #fff;
}
.companu-name{
	font-size: 16px;
	font-family: PingFangSC-Medium, PingFang SC;
	font-weight: 500;
	color: #FFFFFF;
	text-align: center;
}
.name-list{
	height: 52px;
	line-height: 52px;
	border: 1px solid rgba(255,255,255,0.2);
}
.name-click{
	cursor: pointer;
	width: 240px;
	margin: 0 auto;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
	height: 60px;
	line-height: 60px;
}
.list-tree-left{
	// height: calc( 100% - 40px);
	// padding: 20px;
	width: 260px;
	background: #162D59;
	overflow: hidden;
	display: flex;
	flex-direction: column;
}
.left-tree-list{
	flex: 1;
	overflow-y: scroll;
}
.list-tree-right{
	background: #fff;
	border-left: 1px solid #ccc;
	flex: 1;
	.right-title{
		width: calc(100% - 16px);
		margin-left: 16px;
		height: 50px;
		line-height: 50px;
		font-size: 16px;
		border-bottom: 1px solid #ccc;
		.c007AFF{
			color: #007AFF;
		}
	}
	// .css-right-btn{
	// 	margin-left: 16px;
	// }
	.edit-btn{
		display: flex;
		flex-direction: row;
		justify-content: center;
		align-items: center;
		width: calc(100% - 32px);
		height: 120px;
		text-align: center;
		border: 1px solid transparent;
		cursor: pointer;
		margin-top: 20px;
		margin-left: 16px;
		background: rgba(40,153,255,0.05);
		border-radius: 8px;
		.btn-icon i{
			margin-right: 40px;
			display: inline-block;
			width: 40px;
			height: 40px;
			background-size: 100% 100%;
			background-repeat: no-repeat;
		}
		.icon-f-edit {
			background-image: url('../../../assets/images/f-edit.png')
		}
		.icon-f-add {
			background-image: url('../../../assets/images/f-add.png')
		}
		.icon-f-del {
			background-image: url('../../../assets/images/f-del.png')
		}
		.btn-text{
			line-height: 120px;
			color: #007AFF;
			font-size: 20px;
			font-family: PingFangSC-Semibold, PingFang SC;
			font-weight: 600;
		}
	}
	.edit-btn:hover{
		border: 1px solid #007AFF;
	}
}
.node-list {
	width: 100%;
	display: flex;
}
 
.list-btn {
	display: none;
	margin-left: 30px;
}
.node-list:hover .list-btn{
	display: block;
}
.list-btn span {
	margin-left: 20px;
}
.list-btn span:hover {
	color: #1890ff;
	cursor: pointer;
}
.project-menu-list /deep/ .el-tree {
	background: transparent;
	width: 400px;
}
.form-list{
	margin-top: 20px;
	// height: 500px;
	overflow-y: auto;
}
.form-list /deep/ .el-input__inner{
	border-width: 1px;
	border-radius: 4px;
}
.form-list /deep/ .el-input{
	width:97%;	
}
.form-list /deep/ .el-select{
	border:none;
}
.form-list /deep/ .ccfolw-list{
	width: 160px;
}
.form-list /deep/ .ccfolw-list .el-select__caret {
	width: 16px;
	appearance:none;
	-moz-appearance:none;
	-webkit-appearance:none;
	padding-right: 0;
	background: url("../../../assets/images/select-arrow.png") no-repeat scroll right center transparent;
	background-size: 16px 16px;
	-webkit-transform: rotate(0)!important;
	transform: rotate(0)!important;
}
/*将小箭头的样式去去掉*/  
.form-list /deep/ .el-icon-arrow-up:before {
	content: '';
}
.form-list /deep/ .el-form-item.is-success .el-input__inner, 
.form-list /deep/ .el-form-item.is-success .el-input__inner:focus, 
.form-list /deep/ .el-form-item.is-success .el-textarea__inner, 
.form-list /deep/ .el-form-item.is-success .el-textarea__inner:focus{
	border: 1px solid #E8E8E8;
}
.form-list /deep/ .ccfolw-list .el-select .el-input .el-select__caret.is-reverse {
	-webkit-transform: rotate(180deg)!important;
	transform: rotate(180deg)!important;
}
.form-list /deep/ .search-listtype{
	width: 360px;
}
.form-list /deep/ .search-listtype .el-select__caret{
	width: 20px;
	appearance:none;
	-moz-appearance:none;
	-webkit-appearance:none;
	padding-right: 0;
	background: url("../../../assets/images/metting-search.png") no-repeat scroll right center transparent;
	background-size: 20px 20px;
	-webkit-transform: rotate(0)!important;
	transform: rotate(0)!important;
}
.form-list /deep/ .parent-readonly input[readonly]{ background-color: #E8E8E8 !important; }
.btn-level{
	text-align: right;
} 


.css-menu-list{
	width: 252px;
}
.form-list .el-select{
	border: none !important;
}
.left-menu /deep/ .el-menu-item:focus, 
.left-menu /deep/ .el-menu-item:hover,
.left-menu /deep/ .el-submenu__title:hover{
  background: rgba(255,255,255,0.2);
  color: rgba(255, 255, 255, 1);
  border-radius: 8px;
  margin: 0 8px;
}

.left-menu /deep/ .el-menu--horizontal>.el-menu-item:not(.is-disabled):focus,
.left-menu /deep/ .el-menu--horizontal>.el-menu-item:not(.is-disabled):hover, 
.left-menu /deep/ .el-menu--horizontal>.el-submenu .el-submenu__title:hover{
  background-color: #0d1640;
}
.left-menu /deep/ .el-menu-item.is-active{
  color: #007aff;
  background-color: #fff;
  border-radius: 8px;
  margin: 0 8px;
}
.left-menu /deep/ .el-menu{
  background: transparent;
}
.left-menu /deep/ .el-menu-item,
.left-menu /deep/ .el-submenu__title{
  margin: 0 8px;
  color: #ddd;
  padding-left: 0 !important;
}
.left-menu /deep/ .el-submenu .el-menu-item{
  padding: 0;
  min-width: 184px;
  text-align: left;
}
.left-menu /deep/ .el-menu-item.is-active {
	.icon-img{
		&:before {
			color: #007AFF;
    }
	}
  .icon-interface-home {
    background-image: url('../../../assets/images/menu-icon/index-active.png')
  }
  .icon-interface-model_list {
    background-image: url('../../../assets/images/menu-icon/model-active.png')
  }
  .icon-interface-document {
    background-image: url('../../../assets/images/menu-icon/document-active.png')
  }
  .icon-interface-720 {
    background-image: url('../../../assets/images/menu-icon/interface-active.png')
  }
  .icon-interface-associated-component {
    background-image: url('../../../assets/images/menu-icon/associated-active.png')
  }
  .icon-interface-model-statistics {
    background-image: url('../../../assets/images/menu-icon/statistics-active.png')
  }
  .icon-interface-component_cost {
    background-image: url('../../../assets/images/menu-icon/cost-active.png')
  }
  .icon-interface-project-process {
    background-image: url('../../../assets/images/menu-icon/project-progress-active.png')
  }
  .icon-interface-meeting {
    background-image: url('../../../assets/images/menu-icon/meeting-active.png')
  }
  .icon-interface-set_se {
    background-image: url('../../../assets/images/menu-icon/interface-set-active.png')
  }
  .icon-interface-quality {
    background-image: url('../../../assets/images/menu-icon/quality-active.png')
  }
}
.left-menu /deep/ .el-menu--inline{
  background: #0d1640;
}
.text-padding64{
	padding-left: 64px;
}
.text-padding76{
	padding-left: 76px;
}
.left-menu {
  .icon-img{
		margin-left: 20px;
		margin-right: 9px;
    display: inline-block;
    width: 20px;
    height: 20px;
		font-size: 20px;
    background-size: 100% 100%;
    background-position: 50%;
    background-repeat: no-repeat;
  }
  .icon-img-before{
		&:before {
      display: none;
    }
	}
  .icon-interface-home {
    background-image: url('../../../assets/images/menu-icon/index.png')
  }
  .icon-interface-model_list {
    background-image: url('../../../assets/images/menu-icon/model.png')
  }
  .icon-interface-document {
    background-image: url('../../../assets/images/menu-icon/document.png')
  }
  .icon-interface-720 {
    background-image: url('../../../assets/images/menu-icon/interface.png')
  }
  .icon-interface-associated-component {
    background-image: url('../../../assets/images/menu-icon/associated.png')
  }
  .icon-interface-model-statistics {
    background-image: url('../../../assets/images/menu-icon/statistics.png')
  }
  .icon-interface-component_cost {
    background-image: url('../../../assets/images/menu-icon/cost.png')
  }
  .icon-interface-project-process {
    background-image: url('../../../assets/images/menu-icon/project-progress.png')
  }
  .icon-interface-meeting {
    background-image: url('../../../assets/images/menu-icon/meeting.png')
  }
  .icon-interface-set_se {
    background-image: url('../../../assets/images/menu-icon/interface-set.png')
  }
  .icon-interface-quality {
    background-image: url('../../../assets/images/menu-icon/quality.png')
  }
}

</style>