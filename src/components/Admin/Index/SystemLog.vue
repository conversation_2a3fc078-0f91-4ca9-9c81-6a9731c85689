<template>
    <div id="system_SystemLog" class="SystemLog">
        <div class="l">
            <div class="title">日志分类</div>
            <ul class="con">
                <li :class="logType=='1'?'sel':''" @click="logType='1';loadingList()">登陆日志</li>
                <li :class="logType=='3'?'sel':''" @click="logType='3';loadingList()">操作日志</li>
            </ul>
        </div>
        <div class="r">
            <div class="btns">
                <button :class="currentTimeSpan==1?'blue':''" @click="SetTimeSpan(1)">今天</button>
                <button :class="currentTimeSpan==2?'blue':''" @click="SetTimeSpan(2)">近7天</button>
                <button :class="currentTimeSpan==3?'blue':''" @click="SetTimeSpan(3)">近1个月</button>
                <button :class="currentTimeSpan==4?'blue':''" @click="SetTimeSpan(4)">近3个月</button>
                <el-date-picker class="timeSelect"
                    v-model="Condition"
                    @change="loadingList()"
                    type="datetimerange"
                    range-separator="至"
                    start-placeholder="开始日期"
                    end-placeholder="结束日期">
                </el-date-picker>
            </div>
            <div class="list">
                <div class="title">
                    <ul>
                        <li :style="{width:'3%'}"></li>
                        <li :style="{width:ListParams[0].width}">操作人</li>
                        <li :style="{width:ListParams[1].width}">动作结果</li>
                        <li :style="{width:ListParams[2].width}">操作目标</li>
                        <li :style="{width:ListParams[3].width}">操作时间</li>
                    </ul>
                </div>
                <div class="con">
                    <div :data-debug="item.LogId" class="unit" v-for="item in ListDatas" :key="item.LogId">
                        <ul class="line">
                            <li :style="{width:'3%'}"><i :class="currentShowInfo!=item.LogId?'icon-arrow-right_outline':'icon-arrow-down_outline'" @click="showLine(item.LogId)"></i></li>
                            <li :class="ListParams[0].point==1?'point':''" :style="{width:ListParams[0].width}" :title="item.OperateAccount">{{item.OperateAccount}}</li>
                            <li :class="ListParams[1].point==1?'point':''" :style="{width:ListParams[1].width}">{{item.Module+(item.ExecuteResult==1?'成功':'失败')}}</li>
                            <li :class="ListParams[2].point==1?'point':''" :style="{width:ListParams[2].width}">{{item.ExecuteResultJson}}</li>
                            <li :class="ListParams[3].point==1?'point':''" :style="{width:ListParams[3].width}">{{item.OperateTime}}</li>
                        </ul>
                        <ul class="info" :class="changeLine(item.LogId)">
                            <li><span :style="{width:'3%'}"></span><span class="t" :style="{width:ListParams[0].width}">操作人</span><span class="c" :style="{width:parseInt(ListParams[1].width)+parseInt(ListParams[2].width)+parseInt(ListParams[3].width)+'%'}" :title="item.OperateAccount+'('+item.Email+')'">{{item.OperateAccount}}({{item.Email}})</span></li>
                            <li><span :style="{width:'3%'}"></span><span class="t" :style="{width:ListParams[0].width}">动作结果</span><span class="c" :style="{width:parseInt(ListParams[1].width)+parseInt(ListParams[2].width)+parseInt(ListParams[3].width)+'%'}">{{item.Module+(item.ExecuteResult==1?'成功':'失败')}}</span></li>
                            <li>
                                <span :style="{width:'3%'}"></span><span class="t" :style="{width:ListParams[0].width}">操作目标</span>
                                <el-tooltip effect="dark" :style="{top:'-10px'}" :content="item.ExecuteResultJson" placement="top-start">
                                    <span class="c" :style="{width:parseInt(ListParams[1].width)+parseInt(ListParams[2].width)+parseInt(ListParams[3].width)+'%'}" >{{item.ExecuteResultJson}}</span>
                                </el-tooltip>
                            </li>
                            <li><span :style="{width:'3%'}"></span><span class="t" :style="{width:ListParams[0].width}">操作IP</span><span class="c" :style="{width:parseInt(ListParams[1].width)+parseInt(ListParams[2].width)+parseInt(ListParams[3].width)+'%'}">{{item.ipaddress}}</span></li>
                            <li><span :style="{width:'3%'}"></span><span class="t" :style="{width:ListParams[0].width}">操作时间</span><span class="c" :style="{width:parseInt(ListParams[1].width)+parseInt(ListParams[2].width)+parseInt(ListParams[3].width)+'%'}">{{new Date(item.OperateTime).toLocaleString('chinese',{hour12:false})}}</span></li>
                        </ul>
                    </div>
                </div>
                <div class="boot">
                    <page-object class="PgCss" @changePageInfo='changePageInfo' :PageIndex=PageIndex :PageNum=PageNum :PageTotal=PageTotal></page-object>
                </div>
            </div>
        </div>
    </div>
</template>
<script>
import pageObject from '@/components/CompsCustom/PageObject'
export default {
    name:'SystemLog',
    components:{
        pageObject,
    },
    data(){
        return {
            ListParams:[
            {
                id:'id1',
                width:'10%',
                point:1,
            },
            {
                id:'id2',
                width:'10%',
                point:0,
            },
            {
                id:'id3',
                width:'47%',
                point:1,
            },
            {
                id:'id4',
                width:'20%',
                point:0,
            },
            ],
            PageIndex:1,
            PageNum:15,
            PageTotal:500,

            firstLoad:1,
            currentShowInfo:'',
            lastShowInfo:'',
            ListDatas:[],
            currentTimeSpan:1,
            Condition:[
                new Date(new Date().getFullYear()+'-'+(new Date().getMonth()+1)+'-'+new Date().getDate()+' 00:00:00'),
                new Date(),
            ],

            logType:'1'//login、function
        };
    },
    methods:{
        changePageInfo(i,n)
        {
            console.log('SystemLog.vue');
            this.PageIndex=i;
            this.PageNum=n;
            this.loadingList();
        },
        loadingList()
        {
            // debugger;
            var currentLoading=this.$loading({
                lock:true,
                text:'正在加载',
                target:document.getElementById('system_SystemLog')
            });
            if(this.Condition==null)
            {
                this.Condition=[
                    new Date('2010-01-01 00:00:00'),
                    new Date(),
                ];
            }
            debugger;
            // /api/Admin/AdminUser/GetPageListJson
            this.$axios.get(this.$urlPool.base_MainSystemApi+this.$urlPool.GetSystemLog+'?Token='+this.$staticmethod.Get("Token")+'queryJson={"Category":"1","StartTime":"'+this.Condition[0].toLocaleString('chinese',{hour12:false})+'","EndTime":"'+this.Condition[1].toLocaleString('chinese',{hour12:false})+'","OperateAccount":"","OperateRealNameKW":"","IPAddress":"","OperateType":"","Module":"","ProjKeyWord":"","CategoryId":"'+this.logType+'"}&rows='+this.PageNum+'&page='+this.PageIndex+'&sidx=OperateTime&sord=desc').then(res=>{
                currentLoading.close();
                var result=JSON.parse(res.data.Data);
                this.ListDatas=result.rows;
                this.PageTotal=result.records;
            });
        },
        showLine(LogId){
            this.firstLoad=-1;
            this.lastShowInfo=this.currentShowInfo;//记录上次点击的Id
            this.currentShowInfo=LogId;
            if(this.currentShowInfo==this.lastShowInfo)//点击相同的id则收缩
            {
                this.currentShowInfo=-1;
            }
            else//点击其他行则展开
            {
                this.currentShowInfo=LogId;
            }
            console.log(LogId);
        },
        changeLine(LogId)
        {
            var returnVal='';
            if(this.firstLoad==1)//首次加载无动画
                returnVal = '';
            if(this.currentShowInfo=='') 
                returnVal = ''; 
            else if(this.currentShowInfo==LogId)//当前行展开
                returnVal = 'show';
            else if(this.lastShowInfo==LogId)//上一次点击的行收缩
                returnVal = 'hide';
            else
                returnVal = '';//其他行无特效
            return returnVal;
        },
        SetTimeSpan(signNum)
        {
            var nowTime=new Date();
            var oneDayTime=new Date('2019-01-02 00:00:00').getTime()-new Date('2019-01-01 00:00:00').getTime();
            this.Condition=[new Date(),new Date()];
            this.Condition[1]=nowTime.toLocaleString('chinese',{hour12:false});
            this.currentTimeSpan=signNum;
            switch(signNum)
            {
                case 1:
                    {
                        this.Condition[0]=nowTime.getFullYear()+'-'+(nowTime.getMonth()+1)+'-'+nowTime.getDate()+" 00:00:00";
                    }
                break;
                case 2:
                    {
                        let minTime=new Date(nowTime.getTime()- oneDayTime*7);
                        this.Condition[0]=minTime.getFullYear()+'-'+(minTime.getMonth()+1)+'-'+minTime.getDate()+' '+minTime.getHours()+':'+minTime.getMinutes()+':'+minTime.getSeconds();
                    }
                break;
                case 3:
                    {
                        let minTime=new Date(nowTime.getTime()- oneDayTime*30);
                        this.Condition[0]=minTime.getFullYear()+'-'+(minTime.getMonth()+1)+'-'+minTime.getDate()+' '+minTime.getHours()+':'+minTime.getMinutes()+':'+minTime.getSeconds();
                    }
                break;
                case 4:
                    {
                        let minTime=new Date(nowTime.getTime()- oneDayTime*30*3);
                        this.Condition[0]=minTime.getFullYear()+'-'+(minTime.getMonth()+1)+'-'+minTime.getDate()+' '+minTime.getHours()+':'+minTime.getMinutes()+':'+minTime.getSeconds();
                    }
                break;
            }
            this.loadingList();
        }
    },
    created(){
        this.loadingList();
    }
}
</script>
<style>
#system_SystemLog .timeSelect .el-button:first-child{display: none;}
</style>

<style scoped>
    ul,li{padding:0px;margin:0px;list-style-type:none;}
    .SystemLog{
        position:absolute;top:10%;height:calc(90% - 40px);width:100%;padding-top:40px;font-family:黑体;background-color:#f0f3f5;
    }
    .SystemLog .l{
        width:calc(28% - 40px);height:calc(100% - 40px);float:left;padding-right: 40px;overflow: hidden;position: relative;border-right:1px solid rgba(0,0,0,0.09);float:left;
    }
    .SystemLog .l .title{
        width:196px;height:28px;line-height:28px;color:#000;font-size:20px;text-align: left;position: absolute;right:24px;top:0px;margin-right:44px;
    }
    .SystemLog .l .con{
        width:240px;height:calc(60% - 22px - 28px - 8px);overflow: hidden;position: absolute;right:24px;top:50px;
    }
    .SystemLog .l .con li{
        width:100%;height:50px;text-indent:16px;line-height:50px;font-size:14px;color:#000;text-align: left;
    }
    .SystemLog .l .con li.sel{
        background-color:#e6e8eb;
    }
    .SystemLog .l .con li:hover{
        background-color:#e6e8eb;cursor: pointer;
    }
    .SystemLog .r{
        width:calc(72% - 1px - 40px);height:100%;float:left;padding-left: 40px;float:left;
    }
    .SystemLog .r .btns{
        width:100%;height:40px;font-size:0px;text-align:left;position: relative;
    }
    .SystemLog .r .btns button{
        width:auto;height:40px;line-height:40px;font-size:12px;border-radius:2px;color:rgba(0,0,0,0.65);background-color:#e6e8eb;padding:0px 20px 0px 20px;border:none;margin-right: 16px;letter-spacing:1px;outline: none;cursor: pointer;
    }
    .SystemLog .r .btns .timeSelect{
        position: absolute;top:50%;left:50%;transform: translate(-10%,-50%);height:40px;background-color:#fff;border-radius: 4px;
    }
    .SystemLog .r .btns .blue{
        color:#fff;background-color: #1890FF;
    }
    .SystemLog .r .list{
        width:100%;height:calc(100% - 48px);overflow: hidden;
    }
    .SystemLog .r .list .title{
        width:100%;height:32px;line-height:32px;margin-top:8px;
    }
    .SystemLog .r .list .title ul{
        width:calc(100% - 11px);overflow:hidden;height:100%;border-bottom:1px solid rgba(0,0,0,0.04);
    }
    .SystemLog .r .list .title ul li{
        height:100%;text-indent: 16px;text-align:left;font-size:12px;color:rgba(0,0,0,0.65);float:left;text-indent: 16px;overflow:hidden;text-overflow:ellipsis;white-space: nowrap;
    }
    .SystemLog .r .list .con{
        width:calc(100% + 9px);height:calc(100% - 32px - 8px - 60px);overflow-x:hidden;overflow-y:scroll;
    }
    .SystemLog .r .list .con .unit
    {
        width:100%;
    }
    .SystemLog .r .list .con .line{
        width:100%;height:50px;line-height:50px;border-bottom:1px solid rgba(0,0,0,0.04);
    }
    .SystemLog .r .list .con .line li{
        height:100%;float:left;text-align: left;text-indent: 16px;overflow:hidden;text-overflow:ellipsis;white-space: nowrap;position: relative;
    }
    .SystemLog .r .list .con .line .point{
        font-size:14px;font-weight: bold;color:#000;
    }
    .SystemLog .r .list .con .line:hover{
        background-color:#e7e9eb;cursor: default;
    }
    .SystemLog .r .list .con .line li i{
        position: absolute;top:50%;left:30%;transform: translate(-50%,-50%);text-indent:0px;color:#afafaf;cursor: pointer;
    }
    @keyframes itemListShow{
        from{height:0px;}
        to{height:200px;}
    }
    @keyframes itemListHide{
        from{height:200px;}
        to{height:0px;}
    }
    .SystemLog .r .list .con .info{
        width:100%;height:auto;line-height:40px;text-align:left;font-size:0px;background-color: #ebedf0;overflow: hidden;display:none;
    }
    .SystemLog .r .list .con .info.show{
        animation: itemListShow 0.3s;display: block;height:auto;
    }
    .SystemLog .r .list .con .info.hide{
        animation: itemListHide 0.3s;display: block;height:0px;
    }
    .SystemLog .r .list .con .info li{
        width:100%;height:40px;
    }
    .SystemLog .r .list .con .info span{
        height:100%;display:inline-block;font-size:12px;text-indent:16px;overflow:hidden;text-overflow:hidden;white-space: nowrap;
    }
    .SystemLog .r .list .con .info .t{
        color:#000;
    }
    .SystemLog .r .list .con .info .c{
        color:#1890FF;
    }
    .SystemLog .r .list .boot{
        text-align: left;
    }
    .SystemLog .r .list .boot .PgCss{
        padding:7px 0px 7px 0px;
    }
    ::-webkit-scrollbar{width:20px; height:8px;}
    ::-webkit-scrollbar-thumb{border-radius: 12px;border:6px solid transparent;box-shadow: 8px 0px 0px #A5ADB7 inset;}
    ::-webkit-scrollbar-thumb:hover {box-shadow: 8px 0px 0px #4A4A4A inset;}
</style>

