<template>
    <div class="role_EndTime">
        <div class="title">时间授权<i class="icon-suggested-close close" @click="$emit('CloseEndTime')"></i></div>
        <div class="con">
            <el-date-picker class="txtEndTime"
            v-model="EndTime"
            type="date"
            placeholder="请选择授权到期时间">
            </el-date-picker>
        </div>
        <div class="btns">
            <button class="white" @click="$emit('CloseEndTime')">取消</button>
            <button class="blue" @click="SaveEndTime()">确定</button>
        </div>
    </div>
</template>
<script>
export default {
    name:'role_EndTime',
    components:{

    },
    data(){
        return {
            EndTime:'',
        };
    },
    props:{
        OrganizeId:{
            type:String,
            required:false,
        }
    },
    methods:{
        SaveEndTime(){
            let data= {
                ProjectId: this.OrganizeId,
                EndTime: this.$formatData.formatDateCheck(this.EndTime).substr(0,10),
                Token: this.$staticmethod.Get("Token")
            }
            this.$axios.post(this.$urlPool.SetOrganzieEndTime,data).then(res=>{
                console.log(res.data, "保存返回值");
                this.$message({type:'success',message:'授权成功'});
                this.$emit('loadList');
                this.$emit('CloseEndTime');
            });
        }
    }
}
</script>
<style scoped>
    .role_EndTime{overflow: hidden;background-color: #fff;box-shadow:0px 13px 24px -17px rgba(11,41,62,0.8);border-radius:4px;font-family:PingFangSC-Medium;padding:0px 24px 0px 24px;}
    .role_EndTime .title{width:100%;height:60px;font-size:16px;color:#333;line-height:60px;text-align:left;font-weight:bold; }
    .role_EndTime .title .close{width:18px;height:18px;position: absolute;right:15px;top:15px;display:block;}
    .role_EndTime .title .close::before{float:left;width:18px;height:18px;display:block;text-align:left;text-indent:0px;cursor: pointer;}
    .role_EndTime .btns{width: 100%;height:56px;line-height:64px;text-align: right;}
    .role_EndTime .con{width:100%;height:calc(100% - 60px - 64px);overflow:hidden;max-height:420px;min-height:40px;}
    .role_EndTime .con .txtEndTime{border:1px solid #dfdfdf;box-shadow:0px 0px 1px #aaa; border-radius: 4px;float:left;margin-top:20px;width:calc(100% - 4px);}
    .role_EndTime .btns button{width:56px;height: 32px;border:none;outline: none;border-radius: 3px;cursor: pointer;font-size:12px; letter-spacing:1px;}
    .role_EndTime .btns .blue{background-color:#1890FF;color:#fff; }
    .role_EndTime .btns .white{background-color:transparent;color:#666;}
</style>
