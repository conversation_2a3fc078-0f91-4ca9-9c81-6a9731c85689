<template>
	<div class="progress-content">
		<div class="left-content"> 

			<div
				class="pro-in-out"
				@click.stop="handleClickInOut"
				:class="proLeftIn ? 'p-out' : ''"
			></div>
			<div class="css-body-tree _css-tree-left" v-show="!proLeftIn">
				<div class="_css-materialtypelist-head">
					<div class="_css-materhead-text">进度方案</div>
					<div
						class="_css-btnimport _css-change-addnew"
						@click.stop="clickTreeOpenOrClose"
					>
						<div>{{ treeOpenOrCloseText }}</div>
					</div>
				</div>
				<template>
					<div class="_css-treearea">
						<el-tree
							:data="treeData"
							node-key="UID"
							class="_css-customstyle"
							auto-expand-parent
							ref="tree"
							:props="defaultProps"
							:expand-on-click-node="false"
							:current-node-key="checked"
							@node-click="nodeClick"
							@node-expand="nodeExpand"
							@node-collapse="nodeCollapse"
							:default-expanded-keys="expandedList"
							:default-expand-all="defaultExpandAll"
						>
						</el-tree>
					</div>
				</template>
			</div>
			<div
				class="css-body-table _css-imgprog-table"
				:class="proLeftIn ? 'width0' : ''"
			>
				<div class="_css-imgprog-tabletop">
					<div class="_css-btngroup">
						<span>工程任务：{{ clickMaterialName }}</span>
					</div>
					<!-- <div class="_css-button-detail" @click="func_model">
						<div class="_css-btnimport _css-change-addnew">
							<div>实际进度模拟</div>
						</div>
					</div> -->
				</div>
				<div class="_css-imgprog-tableself">
					<u-table
						ref="ref_table"
						:data="m_tabdata"
						style="width: 100%; margin-bottom: 20px"
						v-loading="elTableLoading"
						element-loading-text="数据加载中..."
						element-loading-spinner="el-icon-loading"
						element-loading-background="rgba(0, 0, 0, 0.3)"
						border
						class="_css-table-ele _css-table-ele2 css-scroll _css-customstyle css-fixedleftgbcolor-white css-table-cellborder"
						height="500"
                        :key="tableKey + Math.floor(Math.random() * 1000) + 1"
						:default-sort="{ prop: 'date', order: 'descending' }"
						:row-class-name="tableRowClassName"
						:highlight-current-row="true"
						:header-cell-style="{ 'background-color': 'transparent' }"
					>
						<u-table-column
							:resizable="true"
							prop="Progress_Name"
							border
							fixed="left"
							min-width="150"
							label="工程任务"
							class="_css-celllongcolumn"
						>
							<template slot-scope="scope">
								<div class="_css-costitem">{{ scope.row.Progress_Name }}</div>
							</template>
						</u-table-column>
							
						<u-table-column
							:resizable="true"
							prop="Progress_state"
							width="60"
							label="状态"
							class="_css-celllongcolumn"
						>
							<template slot-scope="scope">
								<div
									class="_css-costitem"
									:class="{
										redRoom: scope.row.Progress_state == '滞后',
										greenRoom: scope.row.Progress_state == '超前',
										normalRoom: scope.row.Progress_state,
									}"
								>
									{{ scope.row.Progress_state }}
								</div>
							</template>
						</u-table-column>
						
						<u-table-column
							:resizable="true"
							:fixed="false"
							prop="bip_planStartDT"
							border
							min-width="120"
							label="计划开始时间"
							class="_css-celllongcolumn"
						>
							<template slot-scope="scope">
								<div class="_css-costitem">
									{{
										scope.row.Progress_planstarttime | flt_datetimefromserver
									}}
								</div>
							</template>
						</u-table-column>
						<u-table-column
							:resizable="true"
							:fixed="false"
							prop="Progress_plannendtime"
							border
							min-width="120"
							label="计划结束时间"
							class="_css-celllongcolumn"
						>
							<template slot-scope="scope">
								<div class="_css-costitem">
									{{
										scope.row.Progress_plannendtime | flt_datetimefromserver
									}}
								</div>
							</template>
						</u-table-column>
						<u-table-column
							:resizable="true"
							:fixed="false"
							prop="Progress_actualstarttime"
							border
							min-width="120"
							label="实际开始时间"
							class="_css-celllongcolumn"
						>
							<template slot-scope="scope">
								<div class="_css-costitem">
									{{ scope.row.Progress_actualstarttime | flt_datetimefromserver }}
								</div>
							</template>
						</u-table-column>
						<u-table-column
							:resizable="true"
							:fixed="false"
							prop="Progress_actualendtime"
							border
							min-width="120"
							label="实际结束时间"
							class="_css-celllongcolumn"
						>
							<template slot-scope="scope">
								<div class="_css-costitem">
									{{ scope.row.Progress_actualendtime | flt_datetimefromserver }}
								</div>
							</template>
						</u-table-column>
					</u-table>
				</div>
			</div>
		</div>
		<div class="right-content" v-if="imageModelVisible">
			<!-- <ProgressPlayActual
				v-if="imageModelVisible"
				:projectID="organizeId"
				:modelIframeSrc='modelIframeSrc'
				:getModelData="getModelData"
				:allModelID="allModelID"
				:getPlayingData="getPlayingData"
				@close="imageModelVisible=false"
			></ProgressPlayActual> -->
			<ProgressPlayActual
				v-if="imageModelVisible"
				:ProgressId="click_projectUID"
				:modelIframeSrc='modelIframeSrc'
				:getModelData="getModelData"
				:allPlayObj="allPlayObj"
				:getPlayingData="getPlayingData"
				:closeHover='null'
				:selectSceneID="selectOpenSceneObj.SceneId"
				@close="imageModelVisible=false"
			></ProgressPlayActual>

		</div>
		<!-- <div class="right-content" v-if="!imageModelVisible && allModelID.length != 0">
			
		</div> -->
	</div>
</template>
<script>

import ProgressPlayActual from "@/components/Home/ProjectBoot/ProgressPlayActual"

export default {
	name: "guangxiLargeScreen",

	data() {
		return {
			token: "", // 进度所有list
			organizeId: "",
			proLeftIn:false,
			clickMaterialName: '', 
            treeData: null,  // 树结构的值
			defaultProps: {
                children: "children",
                label: "Name",
            },
            defaultExpandAll: false,  // 树结构展开
            expandedList: [],
            treeOpenOrClose: false,  // 点击展开折叠tree
            treeOpenOrCloseText: '点击展开',
            m_addingselectednode: undefined,
            checked: '', // 记录当前选中的节点UID
			m_tabdata: [], 
            tableKey:'',
            elTableLoading: false,
			imageModelVisible: false,
			modelIframeSrc:'',
            getModelData: null,  // 实际进度模拟请求接口返回的数据
            getPlayingData: [], // 这个值为模拟时候的数据，是根据填报数据来的

            click_projectUID: '',
            selectOpenSceneObj: {}, // 选中打开场景的场景相关值
            allPlayObj: {}, // 当前模拟相关设置值
			

		};
	},
	components: {
		ProgressPlayActual
	},
	filters: {
        flt_datetimefromserver(str) { 
            let str1 = str;
            if (str) {
                str1 = str.substr(0, 10)
            }
            return str1;
        },
    }, 
	created() {},
	mounted() {
		this.token = this.$route.params.Token;
		this.organizeId = this.$route.params.organizeId;
		this.$staticmethod._Set("organizeId", this.organizeId);
		this.getTreeData()
	},
	methods: {
		 getTreeData(){
            let _this = this;
            let _url = `${this.$MgrBaseUrl.planGetTree}?organizeId=${this.organizeId}&Token=${this.token}`;
            _this.$axios
                .get(_url)
                .then((x) => {
                    if (x.data.Ret > 0) {
                        this.treeData = x.data.Data;
                        this.$nextTick(()=>{
                            // 在tree数据更新后，需要将更新的tree结构数据，同时更新在选中的节点上，所以在这重新setCurrentKey，然后赋值给m_addingselectednode
                            
							if(_this.checked){
                                _this.$refs['tree'].setCurrentKey(_this.checked); 
                                _this.m_addingselectednode = _this.$refs.tree.getCurrentNode();
                            }else{
                                _this.$refs['tree'].setCurrentKey(x.data.Data[0].UID); 
								this.nodeClick(x.data.Data[0]);
								this.m_addingselectednode = _this.$refs.tree.getCurrentNode();
								// setTimeout(()=>{
									this.func_model()
								// },2000)
                            }
							console.log( _this.m_addingselectednode,'===== _this.m_addingselectednode')
                        })
                        // console.log(this.$refs['tree'].getCheckedNodes(this.checked),'====')
                    } else {
                        _this.$message.error(x.data.Msg);
                    }
                })
                .catch((x) => {
                    console.error(x);
                });
        },
		nodeClick(data){
			console.log(data)
            let _this = this;
            // data.children ? _this.change_btnimport = false : _this.change_btnimport = true;
            _this.clickMaterialName = data.Name;
            _this.m_addingselectednode = data;
            _this.checked = data.UID;
            _this.$nextTick(()=>{
                let arr =[]; arr.push(data)
                this.$refs['tree'].setCurrentKey(_this.checked); 
                _this.$refs.tree.setCurrentNode(data)
                _this.$refs.tree.setCheckedNodes(arr)
                
            })
            _this.getTableData(); 
		}, 
		getTableData(){
            let _this = this;
			this.imageModelVisible = false
            _this.elTableLoading = true;
            let _uid = _this.m_addingselectednode.UID;
            let _projectUID = _this.m_addingselectednode.ProjectUID ? _this.m_addingselectednode.ProjectUID : _this.m_addingselectednode.UID;
            this.click_projectUID = _projectUID
			let _url = `${this.$MgrBaseUrl.GetProject}?uid=${_uid}&projectid=${_projectUID}&Token=${this.token}`;
            _this.$axios
                .get(_url)
                .then((x) => {
                    if (x.data.Ret > 0) {
						_this.tableKey = new Date()+'' 
                        _this.m_tabdata = x.data.Data.baseProgressNewOutputs;
                        _this.elTableLoading = false;
						this.func_model()
                    //    console.log(x.data,'=====table')
                    } else {
                        _this.$message.error(x.data.Msg);
                    }
                })
                .catch((x) => {
                    console.error(x);
                });
        },
		// 根据是否选中了某一行，返回类字符串
        tableRowClassName({ row, rowIndex }) {
            return 'css-tdunder';
        },
		handleClickInOut(){
            this.proLeftIn = !this.proLeftIn;
        },
		// 点击设置树结构展开折叠
        clickTreeOpenOrClose(){
            let _this = this;
            _this.treeOpenOrClose = !_this.treeOpenOrClose;
            _this.expandedList = []
            if(_this.treeOpenOrClose){ 
                _this.treeOpenOrCloseText = '点击折叠'; 
                for(var i=0;i<this.$refs.tree.store._getAllNodes().length;i++){
                    _this.expandedList.push(_this.$refs.tree.store._getAllNodes()[i].data.UID)
                    _this.$refs.tree.store._getAllNodes()[i].expanded = true;
                }
            }else{
                _this.treeOpenOrCloseText = '点击展开'; 
                _this.expandedList = []
                for(var i=0;i<this.$refs.tree.store._getAllNodes().length;i++){
                    _this.$refs.tree.store._getAllNodes()[i].expanded = false;
                } 
            }

        },
		nodeExpand(data) {
	      this.expandedList.push(data.UID); // 在节点展开是添加到默认展开数组
	    },
	    nodeCollapse(data) {
	      this.expandedList.splice(this.expandedList.indexOf(data.UID), 1); // 收起时删除数组里对应选项
	    },
		// 点击进去当前详情
        func_model() {
            let _this = this;
            if(_this.m_addingselectednode != {} && _this.m_addingselectednode != undefined ){
                let _model_project = _this.m_addingselectednode.ProjectUID ? _this.m_addingselectednode.ProjectUID : _this.m_addingselectednode.UID
				_this.getProjectModelOpenScene(_model_project);    
				// _this.getProjectModel(_model_project);    
            }else{
                _this.$message.error('请先选择进度任务')
            } 
        },
		// 根据id  查找是否有绑定的场景
        async getProjectModelOpenScene(planid){
            let params = {
                projectId: this.organizeId,
                progressId: planid
            }
			console.log(params,'=====params')
            const res = await this.$api.getProgressFillingSettings(params)
            if(res.Ret == 1){
                if(res.Data.SceneId && res.Data.SceneId.length > 0){
                    this.selectOpenSceneObj.SceneId = res.Data.SceneId
                    this.selectOpenSceneObj.SceneModelId = res.Data.SceneModelId
                    this.allPlayObj = res.Data
                    
                    // 有选择场景
                    // this.getDataSceneAndModelPlayObj = res.Data;
                    // this.getDataSceneAndModelPlayObj.SceneId && this.getDataSceneAndModelPlayObj.SceneId.length > 0 ? this.hasSelectScene = true : this.hasSelectScene = false;
                    this.getProgressList(planid)
                }else{
                    this.$message.warning('请先在进度方案中选择场景')
                    return
                }
            }
        },
        async getProgressList(planid){
            this.axiosLoading = true;

            let params = {
                planId: planid
            }
            const res = await this.$api.getProgressByPlanIdOfDate(params)
            if(res.Ret == 1){
                // 请求成功
                // 处理Details中的数据、模型id需要处理  改成场景中的对应的id
                let _data = res.Data; 
                let modelToSceneID = JSON.parse(this.selectOpenSceneObj.SceneModelId)  // 当前选中场景的场景元素ID
                let _details = _data.Details;
                let ModelIds = _data.ModelIds;

                // ModelIds = ModelIds.map(id => modelToSceneID.find(item => item.modelid === id).sceneModelid)

                let found = false;
                for (let item of modelToSceneID) {
                    if (ModelIds.includes(item.modelid)) {
                        ModelIds = [item.sceneModelid];
                        found = true;
                        break;
                    }
                }

                if (!found) {
                    ModelIds = [];
                    this.axiosLoading = false;
                    this.$message.warning('当前进度任务未正确关联相关场景')
                    return
                }

                let _str_ModelIds = ModelIds.join('|');
                
                this.modelIframeSrc = { projectID: this.$staticmethod._Get("organizeId"),modelID:_str_ModelIds}; 

                let modelToSceneIDMap = new Map(modelToSceneID.map(item => [item.modelid, item.sceneModelid]));
                // console.log(_str_ModelIds,'==ModelIds')
                // console.log(this.modelIframeSrc,'==modelToSceneIDMap')
                for (const item of _details) {
                    for (const list of item.List) {
                        if(list.PteElementIds.length > 0){
                            const sceneModelid = modelToSceneIDMap.get(list.PteElementIds[0].modelid);
                            if (sceneModelid) {
                                list.PteElementIds[0].modelid = sceneModelid;
                                const elementIds = list.PteElementIds[0].elementids.map(elementId => `${list.PteElementIds[0].modelid}^${elementId}`);
                                list.PteElementIds[0].elementids = elementIds;
                                list.elementids = elementIds;
                            }
                        }
                    }
                }


                this.getPlayingData = _data; 
                this.imageModelVisible = true;
            }
            this.axiosLoading = false;
        },  
		// 点击查询模型，获取模型的相关数据，实际使用的是进度方案中的模拟数据
        // getProjectModel(_model_project){
        //     let _this = this;
        //     let _url = `${this.$MgrBaseUrl.GetMaterialEleInfoByPlanId}`; 
        //     _this.axiosLoading = true;
        //     let _moreParam = {
        //         PlanId: _model_project,
		// 		Token: this.token
        //     }
        //     // 这个接口获取的值是点击当前进度使用的值
        //     _this.$axios({
        //         url: _url,
        //         method: 'post',
        //         data: _moreParam
        //     }).then(x => {
        //         if(x.data.Ret == 1){
        //             _this.getModelData = x.data.Data;
        //             _this.allModelID = x.data.Data.ModelIds;
		// 			if(_this.allModelID.length == 0) {
		// 				_this.axiosLoading = false;
		// 				_this.imageModelVisible = false
        //                 this.$message.error('当前任务暂无绑定模型');
        //                 return
        //             }
        //             // 处理modelid 使用|分割
        //             let str = '';
        //             for(var i=0;i<_this.allModelID.length-1;i++){
        //                 str+=_this.allModelID[i]+"|";
        //             }
        //             let a_m= ''
        //             a_m = str + _this.allModelID[_this.allModelID.length-1]
        //             _this.modelIframeSrc = { projectID: this.organizeId,modelID:a_m}; 
 
        //             // _this.modelIframeSrc = { projectID: _projectId,modelID:x.data.Data.ModelIds};  
        //             console.log(_this.modelIframeSrc,'模型加载信息')
        //             _this.$axios({
        //                 url: `${this.$MgrBaseUrl.GetProgressByPlanId}`,
        //                 method: 'post',
        //                 data: _moreParam
        //             }).then(x => {
        //                 if(x.data.Ret == 1){
        //                     if(x.data.Data.List.length == 0){
        //                         _this.$message.error('当前任务暂无实际模拟数据')
        //                         _this.imageModelVisible = false;
        //                         _this.axiosLoading = false;
        //                         return
        //                     }
        //                     let _data = x.data.Data; 
        //                     let _arrList = []
        //                     _data.List.forEach((list) => {
        //                         _arrList = _this.$staticmethod.DeepCopy(list.Elements);
        //                         if(_arrList.length > 0){
                                    
        //                         }
        //                         let listElements = []
        //                         for(let q = 0; q < _arrList.length; q++){
        //                             let _m_ = JSON.parse(_arrList[q].bme_elementids);
        //                             let l__ = _this.getElement(_m_)
        //                             listElements.push(l__)
        //                         }
        //                         let arr = listElements.flat();
        //                         list.returnmodelElementArr = arr;
        //                     }) 
        //                     _this.getPlayingData = _data; 
        //                     _this.axiosLoading = false;
        //                     _this.imageModelVisible = true;
        //                 }else{
        //                     _this.$message.error('服务器异常，请稍后再试')
        //                 } 
        //             }).catch(x => {
                    
        //             })
        //         }else{
        //             _this.$message.error('服务器异常，请稍后再试')
        //         }
        //     }).catch(x => {
            
        //     })

        //     // 获取的值是播放使用的值
            
        // }, 
		getElement(listElements) {
            let arr = [];
            listElements.forEach(item => {
                item.elementids.forEach(ids => {
                arr.push(`${item.modelid}^${ids}`);
                });
            });
            return arr;
        },
	},
	destroyed() {},
};
</script>
<style lang="scss" scoped>
.progress-content {
	background: #fff;
	width: 100%;
	height: 100%;
	display: flex;
	.left-content {
		flex: 1;
		display: flex;
		.left-tree {
			width: 270px;
		}
		.left-table {
			flex: 1;
		}
	}
	.right-content {
		flex: 1;
		width: 50%;
		height: 100%;
		position: relative;
		/deep/ ._css-model-progress{
			position: absolute;
		}
		/deep/ ._css-model-close{
			position: absolute;
			right: 10px;
		    top: 10px;
		}
		/deep/ .model-iframe{
			position: absolute;
		}
		/deep/ .slider-list-content{
			position: absolute;
		}
		/deep/ ._css-time-line{
			margin: 45px 2% 15px 6%;
		}
		/deep/ ._css-model-close{
			display: none;
		}
	}
}
.pro-in-out{
  position: absolute;
  top: calc(50% - 120px);
  left:270px;
  width: 16px;
  height: 120px;
  background-image: url(../../assets/images/p-in.png) ;
  background-size: 100%;
  background-repeat: no-repeat;
  z-index: 2;
  cursor: pointer;
}
.pro-in-out.p-out{
  background-image: url(../../assets/images/p-out.png) ;
  background-repeat: no-repeat;
  background-size: 100%;
  z-index: 2;
  left: 0;
}
._css-tree-left{
    width: 270px;
    box-sizing: border-box;
    background-color: #fff;
    position: relative;
}
._css-materialtypelist-head {
  height: 64px;
  display: flex;
  align-items: center;
  padding-left: 8px;
}
._css-treearea {
    height: calc(99.99% - 64px - 24px);
    overflow-y: auto;
    margin-bottom: 24px;
}
._css-imgprog-table {
	flex: 1;
    position: relative;
    padding: 14px 10px 24px 10px;
    width: calc(100% - 300px);
    box-sizing: border-box;
    margin-left: 10px;
}
._css-imgprog-table.width0{
    width: calc(100% - 30px);
}
._css-imgprog-tabletop {
    height: 40px;
    background-color: #fff;
    display: flex;
    align-items: center;
    border-bottom:1px solid rgba(0, 0, 0, 0.05);
    box-sizing: border-box;
    justify-content: space-between;
}
._css-imgprog-tableself {
    position: absolute;
    width: 99%;
    height: calc(100% - 80px);
    background-color: #fff;
}
._css-btnimport {
    font-size: 12px;
    color: #1890FF;
    border: 1px solid #1890FF;
    border-radius: 4px;
    padding: 4px 6px 4px 6px;
    margin-right: 12px;
    cursor: pointer;
}
._css-btnimport:hover {
    color:#fff;
    background-color: #1890FF;
}
._css-materhead-text {
  flex: 1;
  text-align: left;
  margin-left: 8px;
  font-weight: 500;
  color: rgba(0, 0, 0, 1);
}
._css-customstyle, ._css-customstyle /deep/ .singleTable {
  height: calc(100% - 0px) !important;
}
</style>