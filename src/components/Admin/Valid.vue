<template>
    <div class="_css-valid-all" >
        <!-- 居中的验证码填写界面 -->
        <div class="_css-center">
            <CompsDialogHeader
            title="请输入验证码"
            :hideclosebtn="true"
            ></CompsDialogHeader>
            <div class="_css-inputmiddle">
                <CompsUsersInput
                @oninput="_oninput"
                :placeholder='""'
                :iconclass='""'
                ></CompsUsersInput>
            </div>            
            <CompsDialogBtns
            @onok="_onok"
            ></CompsDialogBtns>
        </div>
        <!-- //居中的验证码填写界面 -->
    </div>
</template>
<script>

import CompsDialogHeader from "@/components/CompsDialog/CompsDialogHeader"
import CompsDialogBtns from "@/components/CompsDialog/CompsDialogBtns"
import CompsUsersInput from "@/components/CompsAuth/CompsUsersInput"

export default {
    data(){
        return {
            extdata:{
                validatecode:''
            }
        };
    },
    methods:{
        _oninput(str){
            var _this = this;
            _this.extdata.validatecode = str;
        },
        _onok(){

            // 将验证码尝试提交给接口
            var _this = this;
            var _Token = _this.$staticmethod.Get("Token");
            var _ValidCode = _this.extdata.validatecode;

            // _Token 与  _ValidCode 提交给后台，应该能得到 avc 表中的数据。
            _this.$axios({
                method: 'post',
                url:`${_this.$configjson.webserverurl}/api/Admin/SuperToken/TestSuperToken`,
                data: {
                    Token: _Token,
                    ValidCode: _ValidCode
                }
            }).then(x => {
                if (x.status == 200 && x.data.Ret > 0) {
                    window.location.href = `${window.bim_config.hasRouterFile}/#/Admin/Index/${_Token}`
                } else {
                    _this.$message({
                        message: '验证码不正确',
                        type: 'error'
                    });
                }
            }).catch(x => {
                window.location.href = `${window.bim_config.hasRouterFile}/#/`;
            });

        }
    },
    components:{
        CompsDialogHeader,
        CompsDialogBtns,
        CompsUsersInput
    }
}
</script>
<style scoped>
._css-valid-all{
    display: flex;
    align-items: center;
    justify-content: space-around;
    background-color: rgba(0,0,0,0.01);
    height:100%;
}
._css-center{
    width:400px;
    height:auto;
    background:rgba(255,255,255,1);
    box-shadow:0px 13px 24px -17px rgba(11,41,62,0.8);
    border-radius:4px;
}
._css-all{
    display: flex;
    flex-direction: row-reverse;
}
._css-inputmiddle{
    padding-left: 24px;
    padding-right: 24px;
    box-sizing:border-box;
}
</style>