<template>
  <div class="_css-all-steptip" :style="computestyle">
    <!-- 对话框前景 -->
    <div class="_css-comps-front" v-drag="draggreet" :style="dragstyle">
      <CompsDialogHeader @oncancel="_oncancel" :title="title || '无标题'"></CompsDialogHeader>
      <div class="_css-compscoloritem-inputarea">
        <CompsUsersInput
          ref="cui_input1"
          :fieldname="extdata.fieldname"
          fieldwidth="120px"
          fieldcolor="rgba(0,0,0,0.65)"
          @oninput="_oninput"
          :placeholder="extdata.fieldplaceholder"
          :iconclass="''"
          :is100percent="true"
        ></CompsUsersInput>
      </div>

      <!-- 颜色块区域 -->
      <div class="_css-color-list">
        <div
          @click="selectColor(cor)"
          v-for="cor in extdata.colorarr"
          :key="cor"
          class="_css-color-item"
          :style="getColorItemStyle(cor)"
        >
          <div
            class="_css-color-item-in"
            :class="{'icon-checkbox-Selected-Disabled-dis': extdata.selectedColor == cor}"
          ></div>
        </div>
      </div>
      <!-- //颜色块区域 -->

      <CompsDialogBtns
        :warningbtn="extdata.bShowWarningBtn == true"
        warningbtntext="删除标签"
        @onok="_onok"
        @oncancel="_oncancel"
        @onwarning="_onwarning"
      ></CompsDialogBtns>
    </div>
    <!-- //对话框前景 -->
  </div>
</template>
<script>
/*
input:
    
events:
    onok();
    oncancel();
    oninput(txt, none, ev);
*/
import CompsDialogHeader from "@/components/CompsDialog/CompsDialogHeader";
import CompsDialogBtns from "@/components/CompsDialog/CompsDialogBtns";
import CompsUsersInput from "@/components/CompsAuth/CompsUsersInput";
export default {
  components: {
    CompsDialogHeader,
    CompsDialogBtns,
    CompsUsersInput
  },
  data() {
    return {
      // 拖动相关
      val2: "0",
      dragstyle: {
        position: "fixed",
        right: "calc(50% - 205px)",
        top: "calc(50% - 142px)"
      },
      // //拖动相关
      extdata: {
        bShowWarningBtn: false,
        inittext: "",
        colorarr: [
          "rgba(28, 50, 75, 1)",
          "rgba(86, 98, 112, 1)",
          "rgba(127, 179, 210, 1)",
          "rgba(151, 107, 61, 1)",
          "rgba(184, 158, 123, 1)",
          "rgba(29, 164, 140, 1)",
          "rgba(115, 113, 157, 1)"
        ], // 备选颜色
        selectedColor: "",
        fieldname: "",
        fieldplaceholder: ""
      }
    };
  },
  mounted() {
    var _this = this;
    if (_this.inittext) {
      _this.$refs.cui_input1.setText(_this.inittext);
      _this.extdata.inittext = _this.inittext;
    }
    if (_this.initcolor) {
      _this.extdata.selectedColor = _this.initcolor;
    }
    if (_this.fieldname) {
      _this.extdata.fieldname = _this.fieldname;
    }
    if (_this.fieldplaceholder) {
      _this.extdata.fieldplaceholder = _this.fieldplaceholder;
    }
  },
  computed: {
    computestyle: {
      get() {
        var _this = this;
        var _s = {};
        if (_this.zIndex) {
          _s["z-index"] = _this.zIndex;
        }
        return _s;
      }
    }
  },
  methods: {

    setWarningShow(bShow) {
      var _this = this;
      _this.extdata.bShowWarningBtn = bShow;
    },

    // 拖动相关
    draggreet(val2) {
      var _this = this;
      _this.val2 = val2;
    },
    // //拖动相关
    selectColor(cor) {
      var _this = this;
      _this.extdata.selectedColor = cor;
    },
    getColorItemStyle(cor) {
      var _this = this;
      var _s = {};
      _s["background-color"] = cor;
      return _s;
    },
    _oninput(txt, none, ev) {
      var _this = this;
      _this.extdata.inittext = txt;
      _this.$emit("oninput", txt, none, ev);
    },
    _oncancel() {
      var _this = this;
      _this.$emit("oncancel");
    },
    _onok() {
      var _this = this;
      _this.$emit("onok", _this.extdata.inittext, _this.extdata.selectedColor);
    }
    ,_onwarning(){
      var _this = this;
      _this.$emit("onwarning");
    }
  },
  props: {
    zIndex: {
      type: Number,
      required: false
    },
    title: {
      type: String,
      required: false
    },
    inittext: {
      type: String,
      required: false
    },
    initcolor: {
      type: String,
      required: false
    },
    fieldname: {
      type: String,
      required: false
    },
    fieldplaceholder: {
      type: String,
      required: false
    }
  }
};
</script>
<style scoped>
._css-color-item-in {
  width: 24px;
  height: 24px;
  line-height: 24px;
  font-size: 22px;
  color: #fff;
}
._css-color-list {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-left: 12px;
  padding-right: 12px;
  margin-top: 24px;
  margin-bottom: 12px;
}
._css-compscoloritem-inputarea {
  height: 50px;
  box-sizing: border-box;
  padding-left: 12px;
  padding-right: 12px;
  margin-top: 8px;
}
._css-comps-front {
  width: 410px;
  min-height: 48px;
  background-color: rgba(255, 255, 255, 1);
  box-shadow: 0px 13px 24px -17px rgba(11, 41, 62, 0.8);
  border-radius: 4px;
}
._css-all-steptip {
  position: fixed;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.15);
  display: flex;
  align-items: center;
  justify-content: space-around;
}

._css-color-item {
  height: 40px;
  width: 40px;
  margin-top: 2px;
  border-radius: 4px;
  background-color: black;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: space-around;
}
._css-color-item:hover {
  opacity: 0.5;
}
</style>