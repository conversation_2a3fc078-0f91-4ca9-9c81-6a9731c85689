<template>
  <div
  :class="classname"    
          :style="styleobj"
  >
    <!-- <CompsSelect
            :items="[{id:'0',text:'非公开'}, {id:'1', text:'公开'}]" 
            text="abc" 
            val="1"
            :inputclass="'_css-single-sel2'"                  
            width="276px"
            @onselected="_selected"
    ></CompsSelect>-->
    <CompsEloSelect
      :datas="dataSource"
      :text="showtext"
      :width="width"
      :itemWitdh="itemWitdh"
      innerwidth="100%"
      @onselected="_selected"
      :clearinnersplitter="true"
      :selectorClass="selectorClass"
      :selectorSize="selectorSize"
    ></CompsEloSelect>
  </div>
</template>
<script>
import CompsEloSelect from "@/components/CompsElOverride/CompsEloSelect";
export default {
  data() {
    return {
      extdata: {}
    };
  },
  components:{
      CompsEloSelect
  },
  computed:{
      styleobj:{
          get(){
              var _this = this;
              var _s = {};
              _s["width"] = _this.width;
              _s["height"] = _this.height;
              _s["background-color"] = _this.backgroundColor;
              _s["display"] = "flex";
              _s["align-items"] = "center";
              for (var attr in _this.positionstyleobj) {
                  _s[attr] = _this.positionstyleobj[attr];
              }
              return _s;
          }
      }
  },
  methods:{
      _selected(obj){
         var _this = this;
         _this.$emit("onselected", obj);
      }
  },
  props:{
      height:{
          type: String,
          required: true
      },
      width:{
          type: String,
          required: true
      },
      itemWitdh:{
          type: String,
          required: false
      },
      positionstyleobj:{
          type: Object,
          required: true
      },
      backgroundColor:{
          type: String,
          required: true
      },
      dataSource:{
          type: Array,
          required: true
      },
      classname:{
          type: String,
          required: true
      },
      showtext:{
          type: String,
          required: true
      },
      selectorClass:{
          type: String,
          required: false
      },
      selectorSize:{
          type: String,
          required: false
      }
  }
};
</script>
