<template>
  <div class="detail-content">
		<div class="detail-header">
			<span>{{ Name }}</span>
			<div @click="closeDetail" class="close"><i class="icon-scene-close"></i>关闭</div>
		</div>
		<div class="content-iframe">
			<iframe :src="iframeUrl" width="100%" height="100%" frameborder="0"></iframe>
		</div>
	</div>
</template>
<script>
export default {
	name: 'CompsIframeDialog',
	data(){
		return {

		}
	},
	props:{
		iframeUrl:{
			type: String,
			default: ''
		},
		Name:{
			type: String,
			default: ''
		},
	},
	mounted(){
	},
	methods:{
		closeDetail(){
			this.$emit('close')
		}
	}
}
</script>
<style lang="scss" scoped>
.detail-content{
	width: 100%;
	height: 100%;
	display: flex;
	flex-direction: column;
}
.content-iframe{
	flex: 1;
}
.detail-header{
	display: flex;
  justify-content: space-between;
  width: calc(100% - 40px);
  height: 48px;
  line-height: 48px;
  border-bottom: 1px solid rgba(0,0,0,.15);
  background: #061326;
  color: #fff;
	padding: 0 20px;
}
.close{
  width: 60px;
  border: 1px solid #2680fe;
  font-size: 12px;
  height: 20px;
  line-height: 20px;
  margin: auto 8px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
	.icon-scene-close{
		width: 12px;
		height: 12px; 
		margin-right: 5px;
		background-image: url('../../assets/images/scene-close.png');
		background-size: 100%;
		background-repeat: no-repeat; 
		background-position: center center;
	}
}
</style>