<template>
    <div style="position:fixed;display:none;top:100%;left:100%;" ></div>
</template>
<script>
export default {
    data(){
        return {
            msgduration: 5000,
            extdata:{
                timer: '' // 时间句柄
                , _timespan: 100 // 默认的 timespan
                , _disabled: false
            },
            dicdata:{
               Issue_Create: 4,
               Issue_Modify: 5,
               Issue_Delete: 6,
               Issue_Comment_Create: 7,
               Issue_Comment_Delete: 8,
               Issue_Doc_Create: 9,
               Issue_Doc_Delete: 10,

               Doc_NewFile: 21,
               Doc_RemoveFile: 22,
               Doc_ModifyFile: 23,
               Doc_MoveFile: 24,
               Doc_NewDir: 27,

               Web_Notify: 50,

               Issue_Comment_Create_At: 1007
            }
        };
    },
    mounted(){
        var _this = this;
        _this.$staticmethod._Set("needpausegetmsg", 0);

        // prop => data
        if (_this.timespan) {
            _this.extdata._timespan = _this.timespan;
        }
        if (_this.disabled) {
            _this.extdata._disabled = _this.disabled;
        }

        // 开启消息循环获取
        // _this.extdata.timer = setInterval(()=>{

        //     // 取得是否需要暂停消息获取
        //     var needPauseGetMsg = _this.$staticmethod._Get("needpausegetmsg");

        //     if (needPauseGetMsg == 1) {
        //         console.warn('此时不请求消息数据');
        //     }

        //     if (!_this.extdata._disabled && needPauseGetMsg != 1) {
        //         setTimeout(()=> {
        //             _this.getMe1ssage();
        //         }, 0);
        //     }
        // }, _this.extdata._timespan);

        // 改为只获取一次消息
        // 全部由 服务器端的 MessageCenter 来发送消息
        _this.extdata.timer = setTimeout(()=>{
            //_this.getM1essage();
        }, _this.extdata._timespan);

    },
    beforeDestroy() {
      var _this = this;      
      clearInterval(_this.extdata.timer);
    },
    methods:{

        processMessage(msgobj) {
//             var _this = this;      
//             console.log(msgobj.LogAndMsgType);

//             // 只要有消息过来，就更新数字
//             // 告诉外面，让更新一下数字
//             // -----------------------
//             _this.$emit("regetplease");

//             if (msgobj.LogAndMsgType == _this.dicdata.Issue_Create) {
//                 // 弹出消息
//                 this.$notify({
//                     title: '【问题】',
//                     message: `【${msgobj.Op_RealName}】创建了问题【${msgobj.BI_Title}】`,
//                     duration: _this.msgduration
//                 });
//             } else if (msgobj.LogAndMsgType == _this.dicdata.Issue_Delete){
//                 this.$notify({
//                     title: '【问题】',
//                     message: `【${msgobj.Op_RealName}】删除了问题【${msgobj.BI_Title}】`,
//                     duration: _this.msgduration
//                 });
//             } else if (msgobj.LogAndMsgType == _this.dicdata.Issue_Modify) {
//                 this.$notify({
//                     title: '【问题】',
//                     message: `【${msgobj.Op_RealName}】修改了问题【${msgobj.BI_Title}】`,
//                     duration: _this.msgduration
//                 });
//             } else if (msgobj.LogAndMsgType == _this.dicdata.Issue_Comment_Create) {
//                 this.$notify({
//                     title: '【问题】',
//                     message: `【${msgobj.Op_RealName}】向问题【${msgobj.BI_Title}】中添加了评论【${msgobj.BIT_Content}】`,
//                     duration: _this.msgduration
//                 });
//             } else if (msgobj.LogAndMsgType == _this.dicdata.Issue_Comment_Delete) {
//                 this.$notify({
//                     title: '【问题】',
//                     message: `【${msgobj.Op_RealName}】移除了问题【${msgobj.BI_Title}】中的评论`,
//                     duration: _this.msgduration
//                 });
//             } else if (msgobj.LogAndMsgType == _this.dicdata.Issue_Doc_Create) {
//                 this.$notify({
//                     title: '【问题】',
//                     message: `【${msgobj.Op_RealName}】向问题【${msgobj.BI_Title}】中添加了关联文档`,
//                     duration: _this.msgduration
//                 });
//                 //************ */
//             } else if (msgobj.LogAndMsgType == _this.dicdata.Issue_Doc_Delete) {
//                 this.$notify({
//                     title: '【问题】',
//                     message: `【${msgobj.Op_RealName}】移除了问题【${msgobj.BI_Title}】中的关联文档`,
//                     duration: _this.msgduration
//                 });
//                 //*************** */
//             } else if (msgobj.LogAndMsgType == _this.dicdata.Doc_NewFile) {
//                 this.$notify({
//                     title: '【文档】',
//                     message: `【${msgobj.Op_RealName}】上传了文档【${msgobj.mm_objname}】`,
//                     duration: _this.msgduration
//                 });
//             } else if (msgobj.LogAndMsgType == _this.dicdata.Doc_RemoveFile) {
//                 this.$notify({
//                     title: '【文档】',
//                     message: `【${msgobj.Op_RealName}】删除了文档【${msgobj.mm_objname}】`,
//                     duration: _this.msgduration
//             });
//             } else if (msgobj.LogAndMsgType == _this.dicdata.Doc_ModifyFile) {
//                 this.$notify({
//                     title: '【文档】',
//                     message: `【${msgobj.Op_RealName}】修改了文档【${msgobj.mm_objname}】`,
//                     duration: _this.msgduration
//                 });
//             } else if (msgobj.LogAndMsgType == _this.dicdata.Doc_MoveFile) {
//                 this.$notify({
//                     title: '【文档】',
//                     message: `【${msgobj.Op_RealName}】移动了文档【${msgobj.mm_objname}】`,
//                     duration: _this.msgduration
//                 });
//             } else if (msgobj.LogAndMsgType == _this.dicdata.Doc_NewDir) {
//                 this.$notify({
//                     title: '【文档】',
//                     message: `【${msgobj.Op_RealName}】新增了文件夹【${msgobj.mm_objname}】`,
//                     duration: _this.msgduration
//                 });
//             } else if (msgobj.LogAndMsgType == _this.dicdata.Issue_Comment_Create_At) {
//                 this.$notify({
//                     title: '【问题】',
//                     dangerouslyUseHTMLString: true,
//                     message: `【<span style="color:red;" >提到了你</span>】【${msgobj.Op_RealName}】向问题【${msgobj.BI_Title}】中添加了评论【${msgobj.BIT_Content}】`,
//                     duration: _this.msgduration
//                 });
//             } else if (msgobj.LogAndMsgType == _this.dicdata.Web_Notify) {
// // msgobj.Op_RealName
// // "mgr001-0"
// // msgobj.orgm
// // "test001"
//                 if (window.bim_config.debu1gmode) {debugger;}
//                 this.$notify({
//                     title: '【站内信】',
//                     message: `【${msgobj.Op_RealName}】邀请你加入机构【${msgobj.orgm}】`,
//                     duration: _this.msgduration
//                 });

//             } else {
//                 console.warn('有消息类型未处理', msgobj);
//             }
        },

        getMessage(){
            // var _this = this;
            // _this.$axios.get(`${window.bim_config.webserverurl}/api/Message/JPush/CurMsgAlert?Token=${_this.$staticmethod.Get("Token")}`)
            // .then(x => {
            //     //console.log(x.data);
            //     if (x.status == 200) {
            //         if (x.data.Ret > 0) {
            //             if (x.data.Data.length && x.data.Data.length > 0) {
            //                 for (let i = 0; i < x.data.Data.length; i++) {
            //                     let msgobj = x.data.Data[i];
            //                     setTimeout(function(){
            //                         _this.proces1sMessage(msgobj);
            //                     }, 10);
            //                 }
            //             }
            //         }
            //     } else {

            //     }
            // })
            // .catch(x => {

            // });
        }
    },
    props:{
        timespan:{
            type:Number, // 6000
            required: false
        },
        disabled:{
            type:Boolean,
            required: false
        }
    }
}
</script>
<style scoped>

</style>