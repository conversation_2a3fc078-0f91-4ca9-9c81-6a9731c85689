<template>
  <div class="_css-all-simplesearchlist" :style="computestyle" @click.stop="stopclick()">
    <!-- 对话框前景 -->
    <div class="_css-comps-front" v-drag="draggreet"  :style="overridestyle || dragstyle" >
      <CompsDialogHeader :title_maxwidth="360" @oncancel="_oncancel" :title="title || '无标题'"></CompsDialogHeader>

      <!-- 文本框上的提示 -->
      <div v-if="tipstr" class="_css-simplesearch-tip">{{tipstr || '无提示'}}</div>
      <!-- //文本框上的提示 -->

      <!-- 显示当前及即将修改的人 -->
      <div class="_css-simplesearch-state" v-if="oldname">
        <div class="_css-simplesearch-state-in">
          <div class="_css-simplesearch-state-old _css-simplesearch-state-item">
            <div class="_css-sstate-name">当前</div>
            <div class="_css-sstate-icon">{{oldname | setAbbreviation}}</div>
            <div class="_css-sstate-text">{{oldname}}</div>
          </div>
          <div class="_css-simplesearch-state-spli"></div>
          <div class="_css-simplesearch-state-new _css-simplesearch-state-item">
            <div class="_css-sstate-name _css-new">将替换为</div>
            <template v-if="extdata.newname">
              <div class="_css-sstate-icon">{{(extdata.newname || '') | setAbbreviation}}</div>
              <div class="_css-sstate-text">{{(extdata.newname || '')}}</div>
            </template>
          </div>
        </div>
      </div>
      <!-- //显示当前及即将修改的人 -->

      <div class="_css-simplesearch-input">
        <CompsUsersInput
          @oninput="_oninput"
          :placeholder="inputplaceholder"
          :iconclass="'icon-interface-user'"
          :is100percent="true"
        ></CompsUsersInput>
      </div>

      <div class="_css-simplesearch-userlist" v-loading="extdata.userlistloading">
        <div class="_css-simpleuserlist-in css-miniscroll">
          <!-- <CompsMemberItem></CompsMemberItem>
          <CompsMemberItem :ischecked="true"></CompsMemberItem>
          <CompsMemberItem></CompsMemberItem>
          <CompsMemberItem></CompsMemberItem>
          <CompsMemberItem></CompsMemberItem>
          <CompsMemberItem></CompsMemberItem>-->
          <CompsMemberItem
            @onitemclick="_itemclick(user.UserId, user)"
            v-for="user in extdata.users"
            v-if="ifnotcontainsinnotshow(user.UserId)"
            :key="user.UserId"
            :userId="user.UserId"
            :realname="user.RealName"
            :email="user.Email || user.Account"
            :ischecked="func_getIsChecked(user)"
          ></CompsMemberItem>

          <!-- 不考虑多选时 ischecked 的赋值 -->
          <!-- :ischecked="user.UserId == extdata.initCompanyManagerUserId" --><!-- //不考虑多选时 ischecked 的赋值 -->

        </div>
      </div>

      <CompsDialogBtns @onok="_onok" @oncancel="_oncancel"></CompsDialogBtns>
    </div>
    <!-- //对话框前景 -->
  </div>
</template>
<script>
/*
input:
    
events:
    onok();
    oncancel();
    oninput();
*/
import CompsDialogHeader from "@/components/CompsDialog/CompsDialogHeader";
import CompsDialogBtns from "@/components/CompsDialog/CompsDialogBtns";
import CompsUsersInput from "@/components/CompsAuth/CompsUsersInput";
import CompsMemberItem from "@/components/CompsCommon/CompsMemberItem";
export default {
  components: {
    CompsDialogHeader,
    CompsDialogBtns,
    CompsUsersInput,
    CompsMemberItem
  },
  data() {
    return {

          m_ismultiple:false,
          val:'0',
            dragstyle: {
                position: 'fixed',
                right: 'calc(50% - 205px)',
                top: 'calc(50% - 259px)'
            },
      extdata: {
        newname: "", // 可修改的newname,初始newname初始化
        users: [],
        userlistloading: false,
        initCompanyManagerUserId: "", // 初始时的机构管理员 UserId

        // 角色管理添加人员，当 m_ismultiple 开启时，记录当前选了哪些人
        // --------------------------------------------------------
        m_selectedUserIds:[],

        // 指定审批人，添加人员，当 showType 为 commonPjMember 时用到
        // -------------------------------------------------------
        m_selectedUsers:[],

        keyWord: "" // 搜索关键字
      },
      inputplaceholder:'',
      searchm:{
        canceltoken: undefined,
        canceltoken_source: undefined
      }
    };
  },
  computed: {
    computestyle: {
      get() {
        var _this = this;
        var _s = {};
        if (_this.zIndex) {
          _s["z-index"] = _this.zIndex;
        }
        return _s;
      }
    }
  },
  mounted() {
    // 初始时根据当前机构加载所有人员。(包括机构管理员)
    //console.log(_this.companyId);
    var _this = this;

    // init
    // ----

    // 将是否支持多选的属性值传递给内部 data 的字段
    // ----------------------------------------
    if (_this.init_ismultiple) {
      _this.m_ismultiple = true;
    } else {
      _this.m_ismultiple = false;
    }

    if (_this.init_inputplaceholder) {
      _this.inputplaceholder = _this.init_inputplaceholder;
    } else {
      _this.inputplaceholder = '搜索成员';
    }

    // 如果 showType 为公司
    if (_this.showType == "company") {

      // 移交机构时，需要指定一个初始化，也就是当前机构的机构管理员的 UserId
      _this.extdata.initCompanyManagerUserId = _this.$staticmethod.Get(
        "UserId"
      );
      _this.loadCompanyMembers();

    } else if (_this.showType == "project") {

      // 可修改的newname,初始newname初始化
      // 将外面传入的新项目经理的值（一般是空字符串）初始化给内部可修改，并会emit调用方的字段：extdata.newname
      _this.extdata.newname = _this.newname;
      _this.loadProjectMembers();

    } else if (_this.showType == "addToRole") {
      // 可修改的newname,初始newname初始化
      // 将外面传入的新项目经理的值（一般是空字符串）初始化给内部可修改，并会emit调用方的字段：extdata.newname
      _this.extdata.newname = _this.newname;
      _this.loadAddToRoleMembers();
    } else if (_this.showType == "inviteByAccount") {
      _this.loadByAccountKeyword();
    }

    // 通用，如果是多选，直接加载
    // ------------------------
    else if (_this.showType == 'commonPjMember') {
      _this.loadpjmemberByKeyword();
    }
  },
  methods: {

    loadpjmemberByKeyword(){
      var _this = this;
      var _keyWord = _this.extdata.keyWord;
      var _companyId = _this.companyId;

      // 如果之前有请求，则先cancel掉。
      // 确保 canceltoken_source 有值
      // ---------------------------
      if (_this.searchm.canceltoken_source) {
        _this.searchm.canceltoken_source.cancel('cancel');
        _this.extdata.userlistloading = false;
      }
      _this.searchm.canceltoken = _this.$axios.CancelToken;
      _this.searchm.canceltoken_source = _this.searchm.canceltoken.source();

      // 延迟请求
      // -------
      setTimeout(()=>{

        _this.extdata.userlistloading = true;
        var _organizeId = _this.$staticmethod._Get("organizeId");
        var _Token = _this.$staticmethod.Get("Token");
      let url = `${this.$urlPool.GetUserPaged}?PageNum=1&PageSize=3000&KeyWord=${_keyWord}&OrganizeId=${_organizeId}&searchType=0&RoleId=&Token=${_Token}`

        _this.$axios.get(url).then(res=>{
          if (res.status == 200) {
            if (res.data.Ret > 0) {
              _this.extdata.users = res.data.Data.list;
            }
          }
          _this.extdata.userlistloading = false;
        }).catch(x => {
          _this.extdata.userlistloading = false;
        });
        
      }, 0);
      
    },

    // 根据 user 返回ischecked 的属性值，内部考虑 ismultiple
    // ---------------------------------------------------
    func_getIsChecked(user){
      var _this = this;
      if (_this.m_ismultiple && (_this.showType == 'addToRole')) {
        return _this.extdata.m_selectedUserIds.indexOf(user.UserId) >= 0;
      } else if (_this.showType == 'commonPjMember') {
        var index = _this.extdata.m_selectedUsers.findIndex(x => x.UserId == user.UserId);
        return index >= 0;
      } else {
        return user.UserId == _this.extdata.initCompanyManagerUserId;
      }
    },

    // 阻止事件冒泡
    stopclick(){
      return false;
    },

    ifnotcontainsinnotshow(userId){
      var _this = this;
      if (_this.willnotshowUsers && _this.willnotshowUsers.length > 0) {
        var index = _this.willnotshowUsers.findIndex(x => x.UserId == userId);
        return index < 0;
      } else {
        return true;
      }
    },

       draggreet(val){
                var _this = this;
                _this.val = val;
            },
    _itemclick(userId, user) {

      var _this = this;
      _this.extdata.initCompanyManagerUserId = userId;

      if (_this.showType == "project") {
        var index = _this.extdata.users.findIndex(x => x.UserId == userId);
        if (index >= 0) {
          _this.extdata.newname = _this.extdata.users[index].RealName;
        }
        //
      }

      // 如果当前功能为 addToRole ，且已开启了多选，则给另外一个变量赋值（记录多选哪些项）
      // ------------------------------------------------------------------------
      if (_this.showType == "addToRole" && _this.m_ismultiple) {

        // 将 userId 追加到数组中，或从数组中移除
        // -----------------------------------
        if (_this.extdata.m_selectedUserIds.indexOf(userId) >= 0) {
          _this.extdata.m_selectedUserIds = _this.extdata.m_selectedUserIds.filter(x => x != userId);
        } else {
          _this.extdata.m_selectedUserIds.push(userId);
        }
        console.log(_this.extdata.m_selectedUserIds);

      } 
      
      // commonPjMember 的分支
      // --------------------
      else if (_this.showType == 'commonPjMember') {

        // 将 user 对象添加或从数组中移除
        // ----------------------------
        var ifcheckedindex = _this.extdata.m_selectedUsers.findIndex(x => x.UserId == user.UserId);
        if (ifcheckedindex >= 0) {
          _this.extdata.m_selectedUsers = _this.extdata.m_selectedUsers.filter(x => x.UserId != user.UserId);
        } else {
          _this.extdata.m_selectedUsers.push(user);
        }

      }


    },

    // 加载当前系统中所有匿名机构的人员
    // -----------------------------
    loadByAccountKeyword(){
      var _this = this;
      var _keyWord = _this.extdata.keyWord;
      var _companyId = _this.companyId;

      // 如果之前有请求，则先cancel掉。
      // 确保 canceltoken_source 有值
      // ---------------------------
      if (_this.searchm.canceltoken_source) {
        _this.searchm.canceltoken_source.cancel('cancel');
        _this.extdata.userlistloading = false;
      }
      _this.searchm.canceltoken = _this.$axios.CancelToken;
      _this.searchm.canceltoken_source = _this.searchm.canceltoken.source();

      // 请求
      // ----
      _this.extdata.userlistloading = true;
      // GetAnonymousOrgUsersByKW 按账号关键字获取匿名机构下的人员，如果关键字为空则返回空集合
      var url = `${window.bim_config.webserverurl}/api/User/User/GetAnonymousOrgUsersByKW?keyWord=${_keyWord}&Token=${this.$staticmethod.Get('Token')}`;
      _this.$axios.get(url,
      {
        cancelToken: _this.searchm.canceltoken_source.token  
      }).then(res=>{
        if (res.status == 200) {
          if (res.data.Ret > 0) {
            _this.extdata.users = res.data.Data;
          }
        }
        _this.extdata.userlistloading = false;
      }).catch(x => {
        _this.extdata.userlistloading = false;
      });

    },

    // 加载可添加到角色的人员
    loadAddToRoleMembers(){
         // 根据项目ID及关键字搜索项目内人员
      var _this = this;
      var _keyWord = _this.extdata.keyWord; 
      //console.log(_projectId, _keyWord);
      let _Token = _this.$staticmethod.Get("Token"); 
      let _OrganizeId = _this.$staticmethod._Get("_OrganizeId");
      let Url = `${this.$urlPool.GetUserPaged}?PageNum=1&PageSize=3000&KeyWord=${_keyWord}&OrganizeId=${_OrganizeId}&searchType=1&RoleId=&Token=${_Token}&IsExcludeRole=true`

      _this.extdata.userlistloading = true;
      _this
        .$axios({
          method: "get",
          url: Url,
        })
        .then(x => {
          if (x.status == 200 && x.data.Ret < 0) {
            _this.$message.error(x.data.Msg);
          }
          if (x.status == 200 && x.data.Ret > 0) {
            _this.extdata.users = x.data.Data.list;
          } 
          _this.extdata.userlistloading = false;
        })
        .catch(x => {
          _this.extdata.userlistloading = false;
        });
    },

    // 加载当前项目中的所有有效人员
    loadProjectMembers() {
      // 根据项目ID及关键字搜索项目内人员
      var _this = this;
      var _keyWord = _this.extdata.keyWord;
      var _projectId = _this.projectId;
      var _Token = _this.$staticmethod.Get("Token");
      //console.log(_projectId, _keyWord);

      // https://www.probim.cn:8080/api/User/User/GetUserAndRoles?organizeId=74f0e34d-e563-45cb-b144-8f8b5a9f21b5&Token=322DT2DE
      _this.extdata.userlistloading = true;
      _this
        .$axios({
          method: "get",
          url: `${
            this.$issueBaseUrl.GetToAddIssueJoiners
          }?projectId=${_projectId}&token=${_Token}&encodedKeyWord=${_keyWord}`
        })
        .then(x => {
          if (x.status == 200 && x.data.Ret > 0) {
            _this.extdata.users = x.data.Data;
          }
          _this.extdata.userlistloading = false;
        })
        .catch(x => {
          _this.extdata.userlistloading = false;
        });
    },

    // 加载当前机构下的所有成员
    loadCompanyMembers() {
      // 根据公司ID及关键字搜索机构内人员，再配合 initCompanyManagerUserId 标识当前机构管理员。
      var _this = this;
      var _companyId = _this.companyId;
      var _keyWord = _this.extdata.keyWord;
      var _Token = _this.$staticmethod.Get("Token");
      _this.extdata.userlistloading = true;
      _this
        .$axios({
          method: "get",
          url: `${
            window.bim_config.webserverurl
          }/api/User/User/GetValidUsersByCompanyId?Token=${_Token}&organizeId=${_companyId}&keyWord=${_keyWord}`
        })
        .then(x => {
          if (x.status == 200 && x.data.Ret > 0) {
            _this.extdata.users = x.data.Data;
          }
          _this.extdata.userlistloading = false;
        })
        .catch(x => {
          _this.extdata.userlistloading = false;
        });
    },
    _oncancel() {
      var _this = this;
      _this.$emit("oncancel");
    },
    _onok() {

      //debugger;
      var _this = this;
      if (_this.showType == "company") {
        _this.$emit("onok", _this.extdata.initCompanyManagerUserId);
      } else if (_this.showType == "project" || _this.showType == "addToRole") {

        // 项目添加角色的分支：保存时的操作
        // -----------------------------

        // 判断是否为多选
        // -------------
        if (_this.m_ismultiple) {
          _this.$emit("onok_multiple", _this.extdata.m_selectedUserIds);
        } else {
          _this.$emit(
            "onok",
            _this.extdata.initCompanyManagerUserId,
            _this.extdata.newname
          );
        }
        

      } else {

        if (_this.m_ismultiple) {
           _this.$emit("onok_multiple", _this.extdata.m_selectedUsers);
        } else {
          // 其它分支，包括按账号邀请
          // ----------------------
          console.log('onok的其它分支');
          _this.$emit("onok", _this.extdata.initCompanyManagerUserId);
        }

        
      }
    },
    _oninput(str) {
      var _this = this;
      _this.$emit("oninput");

      // 共用的关键字字段
      _this.extdata.keyWord = str;
      // console.log(str);

      // 公司
      if (_this.showType == "company") {
        _this.loadCompanyMembers();
      } else if (_this.showType == "project") {
        _this.loadProjectMembers();
      } else if (_this.showType == "addToRole") {
        clearTimeout(_this.timeout)
        _this.timeout = setTimeout(()=>{
            _this.loadAddToRoleMembers();
        },500)
      } else if (_this.showType == "inviteByAccount") {
        _this.loadByAccountKeyword();
      }

      // 判断如果为 commonPjMember ，则直接中止 上一次请求，并延迟请求
      // ---------------------------------------------------------
      else if (_this.showType == 'commonPjMember') {
        _this.loadpjmemberByKeyword();
      }
      


    }
  },
  props: {
    zIndex: {
      type: Number,
      required: false
    },
    title: {
      type: String,
      required: false
    },
    tipstr: {
      type: String,
      required: false
    },
    oldname: {
      type: String,
      required: false
    },
    newname: {
      type: String,
      required: false
    },
    companyId: {
      type: String,
      required: false
    },
    projectId: {
      type: String,
      required: false
    },
    showType: {
      // 'company', 'project'
      type: String,
      required: false
    },
    willnotshowUsers:{
      type: Array,
      required: false
    },
    overridestyle:{ // position! left? top?
      type:Object,
      required: false
    },
    init_inputplaceholder:{
      type: String,
      required: false
    }

    // 初始化：是否支持多选 true, other
    // ------------------------------
    ,init_ismultiple:{
      type:Boolean,
      required: false
    }
  }
};
</script>
<style scoped>
._css-sstate-name {
  height: 22px;
  line-height: 22px;
  font-size: 14px;
  color: #1890ff;
}
._css-sstate-name._css-new {
  height: 22px;
  line-height: 22px;
  margin-left: 20px;
}
._css-sstate-icon {
  height: 24px;
  width: 24px;
  background-color: #202020;
  line-height: 24px;
  margin-left: 16px;
  color: #fff;
}
._css-sstate-text {
  margin-left: 8px;
  color: #000;
  height: 22px;
  line-height: 22px;
}
._css-simplesearch-state-item {
  flex: 1;
  height: 100%;
  display: flex;
  align-items: center;
}
._css-simplesearch-state-spli {
  height: 24px;
  width: 2px;
  background-color: rgba(0, 0, 0, 0.15);
}
._css-simplesearch-state {
  height: 40px;
  margin-top: 16px;
}
._css-simplesearch-state-in {
  padding-left: 24px;
  padding-right: 24px;
  display: flex;
  align-items: center;
  height: 100%;
  width: 100%;
  box-sizing: border-box;
}
._css-simpleuserlist-in {
  height: 100%;
  overflow-y: auto;
  margin-left: 24px;
  margin-right: 24px;
}
._css-simplesearch-userlist {
  height: 300px;
  margin-top: 16px;
  width: 100%;
}
._css-simplesearch-input {
  height: 40px;
  padding-left: 24px;
  padding-right: 24px;
  margin-top: 10px;
}
._css-simplesearch-tip {
  font-size: 12px;
  color: rgba(0, 0, 0, 0.45);
  height: 20px;
  line-height: 20px;
  margin-top: 24px;
  /* text-align: center; */
  text-align: left;
  box-sizing: border-box;
  padding-left: 30px;
}
._css-comps-front {
  width: 410px;
  min-height: 48px;
  background-color: rgba(255, 255, 255, 1);
  box-shadow: 0px 13px 24px -17px rgba(11, 41, 62, 0.8);
  border-radius: 4px;
}
._css-all-simplesearchlist {
  position: fixed;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.15);
  display: flex;
  align-items: center;
  justify-content: space-around;
}
</style>