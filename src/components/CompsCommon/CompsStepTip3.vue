<template>
  <div class="_css-all-steptip" :style="computestyle">
    <!-- 对话框前景 -->
    <div class="_css-comps-front" :style="computefrontStyle"  
    v-drag="draggreet"
    >
      <CompsDialogHeader @oncancel="_oncancel" :title="title || '无标题'"></CompsDialogHeader>
      <div class="_css-comps-step _css-comps-step1">
        <div class="_css-step-tipicon">1</div>
        <div class="_css-step-tiptext">下载Excel模板</div>
        <div class="_css-step-btncon">
          <CompsEloButton
            :width="76"
            :height="32"
            text="下载模板"
            color="#1890ff"
            bgcolor="#fff"
            border="1px solid #1890ff"
            :fontSize="12"
            @onclick="download_template"
          ></CompsEloButton>
        </div>
      </div>
      <!-- //第一步 -->
      <!-- 第二步 -->
      <div class="_css-comps-step _css-comps-step2">
        <div class="_css-step-tipicon">2</div>
        <div class="_css-step-tiptext">编辑Excel模板（Excel中所有字段内容将对应到该类别下的字段）</div>
      </div>
      <!-- //第二步 -->

      <!-- 显示表格的示例图片 -->
      <div class="_css-step-tableimagearea">
        <div class="_css-stop-tableimage "
        :class="getmubanimgclass()"
        ></div>
      </div>
      <!-- //显示表格的示例图片 -->

      <!-- 第三步 -->
      <div class="_css-comps-step _css-comps-step3">
        <div class="_css-step-tipicon">3</div>
        <div class="_css-step-tiptext">上传编辑好的Excel文件</div>
        <div class="_css-step-btncon">
          <CompsEloButton
            :width="76"
            :height="32"
            text="上传文件"
            color="#1890ff"
            bgcolor="#fff"
            border="1px solid #1890ff"
            :fontSize="12"
            @onclick="upload_importingfile"
          ></CompsEloButton>
        </div>
      </div>
      <div class="_css-upload-file-show" v-if="uploadFileName.length>0">
        <p><i class="_css-upload-icon mulcolor-interface-xlsx"></i><span>{{uploadFileName}}</span></p>
      </div>
      <div class="_css-step-bottom-upload" :class="uploadAndsubmitFun()?'_css-step-bottom-upload-submit':''" >
        <p @click.stop="importbtnclick()">{{title}}</p>
        <p v-if="uploadAndsubmitFun()" @click.stop="uploadFileSure(1)">导入并下发</p>
      </div>
    </div>
    <!-- //对话框前景 -->

    <!-- 下方的导入用户 -->
    <input
      type="file"
      style="display:none"
      id="id_ImportMaterials_File"
      accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
      @change="importFileChange()"
    >
    <!-- //下方的导入用户 -->
  </div>
</template>
<script>
/*
input:
    
events:
    onok();
    oncancel();
      _this.$emit("onfinish", true, '导入成功');
*/
import CompsDialogHeader from "@/components/CompsDialog/CompsDialogHeader";
import CompsDialogBtns from "@/components/CompsDialog/CompsDialogBtns";
import CompsEloButton from "@/components/CompsElOverride/CompsEloButton";
export default {
  data() {
    return {
      val:'0',
      dragstyle:{
          position: 'fixed',
          right: 'calc(50% - 252px)',
          top: 'calc(50% - 242px)'
      },
      uploadFileName: '',
    };
  },
  computed: {
    computestyle: {
      get() {
        var _this = this;
        var _s = {};
        if (_this.zIndex) {
          _s["z-index"] = _this.zIndex;
        } else {
          _s["z-index"] = 2;
        }
        return _s;
      }
    },
    computefrontStyle: {
      get() {
        var _this = this;
        var _s = {};
        if (_this.width) {
          _s["width"] = _this.width;
        }
        for(var _sattr in _this.dragstyle) {
          _s[_sattr] = _this.dragstyle[_sattr];
        }
        return _s;
      }
    }
  },
  methods: {
    getmubanimgclass(){
      var _this = this;
      if (_this.mubanimgclass) {
        return _this.mubanimgclass;
      } else {
        return 'temp-muban-material';
      }
    },
    uploadAndsubmitFun() {
      //return window.bim_config.materia1l_custom == true
      return false;
    },
       draggreet(val){
                var _this = this;
                _this.val = val;
            },
    // 选择Excel文件后执行
    importFileChange() {
      // 准备 formData
      var _this = this;
      var dominputfile = document.getElementById("id_ImportMaterials_File");
      if (dominputfile.files.length == 1) {
        // 拿到文件对象
        var file = dominputfile.files[0];        
        console.log(file.name)
        _this.uploadFileName = file.name
      }
    },

    // 点击导入按钮
    importbtnclick(){
      this.uploadFileSure();
    },

    uploadFileSure(willarrange) {
      var _this = this;
      var dominputfile = document.getElementById("id_ImportMaterials_File");
      if (dominputfile.files.length == 1) {
        // 拿到文件对象
        var file = dominputfile.files[0];        
        // console.log(file.name)
        // _this.uploadFileName = file.name
        
      // 准备参数
        var fd = new FormData();
        fd.append("OrganizeId",  _this.$staticmethod._Get("organizeId")); // 分类ID
        fd.append("Token", _this.$staticmethod.Get("Token"));
        if(this.importUrl){
          fd.append("CategoryId", this.bc_guid);
        }
        fd.append("File", file);
 
        var config = {
          headers: {
            "Content-Type": "multipart/form-data"
          }
        };
        let url = ''
        if(this.importUrl){
          url = `${window.bim_config.webserverurl}${this.importUrl}`
        }else{
          url = `${window.bim_config.webserverurl}/api/Material/Division/Import`
        }
        _this.$axios
          .post(
            url,
            fd,
            config
          )
          .then(x => {
            if (x.status == 200) {
              if (x.data.Ret > 0) {
                // 提示，并刷新
                // _this.$message.success('添加成功');
                _this.$emit("onok");
              } else {
                _this.$message.error(x.data.Msg);
              }
            } else {
              console.error(x);  
            }
          })
          .catch(x => {
            console.error(x);
          });
      }
    },
    download_template() {
      if(this.downloadUrl){
        window.location.href = `${window.bim_config.webserverurl}${this.downloadUrl}?token=${this.$staticmethod.Get("Token")}`;
      }else{
        window.location.href = `${window.bim_config.webserverurl}/api/Material/Division/DownloadTemplate?token=${this.$staticmethod.Get("Token")}`;
      }
    },
    upload_importingfile() {
      var _this = this;
      var dominputfile = document.getElementById("id_ImportMaterials_File");
      dominputfile.value = "";
      dominputfile.click();
    },
    _oncancel() {
      var _this = this;
      _this.$emit("oncancel");
    },
    _onok() {
      var _this = this;
      //_this.$emit("onok");
      _this.$emit("oncancel");
    }
  },
  props: {
    zIndex: {
      type: Number,
      required: false
    },
    title: {
      type: String,
      required: false
    },
    width: {
      type: String,
      required: false
    },
    companyId: {
      type: String,
      required: false
    },
    bc_guid:{
      type: String,
      required: false
    },
     
    mubanimgclass:{
      type:String,
      required: false
    },
    downloadUrl:{
      type:String,
      required: false
    },
    importUrl:{
      type:String,
      required: false
    },
  },
  components: {
    CompsDialogHeader,
    CompsDialogBtns,
    CompsEloButton
  }
};
</script>
<style scoped>
._css-step-tableimagearea {
  height: 168px;
  margin-top: 18px;
  display: flex;
  align-items: center;
}
._css-stop-tableimage {
  height: 100%;
  margin-left: 84px;
  flex: 1;
  background-position-x: 0;
  background-size: contain;
}
._css-step-btncon {
  position: absolute;
  right: 16px;
}
._css-step-tipicon {
  width: 16px;
  height: 16px;
  line-height: 16px;
  margin-left: 24px;
  color: #666666;
  border: 1px solid #B8B8B8;
  border-radius: 50%;
  /* background-color: rgba(20, 20, 20, 0.04); */
  font-size: 12px;
}
._css-step-tiptext {
  margin-left: 12px;
  height: 20px;
  line-height: 22px;
  color: rgba(0, 0, 0, 0.85);
  font-size: 12px;
  text-align: left;
}
._css-comps-step1 {
  height: 32px;
  margin-top: 18px;
  display: flex;
  align-items: center;
  position: relative;
}
._css-comps-step2 {
  height: 32px;
  margin-top: 18px;
  display: flex;
  align-items: center;
  position: relative;
}
._css-comps-step3 {
  height: 32px;
  margin-top: 18px;
  display: flex;
  align-items: center;
  position: relative;
}
._css-comps-front {
  width:504px;
  min-height: 48px;
  background-color: rgba(255, 255, 255, 1);
  box-shadow: 0px 13px 24px -17px rgba(11, 41, 62, 0.8);
  border-radius: 4px;
}
._css-all-steptip {
  position: fixed;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.15);
  display: flex;
  align-items: center;
  justify-content: space-around;
}
._css-step-bottom-upload{
  margin-top:0px;
  /* border-top:1px solid rgba(0, 0, 0, .09); */
  display: flex;
  padding: 0 6%;
}
._css-step-bottom-upload._css-step-bottom-upload-submit p{
  width:45%;
  margin: 12px 5% ;
  line-height: 40px;
  border-radius: 4px;
  cursor: pointer;
  color: #fff;
  background-color: rgba(0, 0, 0, 0.25);
}
._css-step-bottom-upload._css-step-bottom-upload-submit p:last-child{
  background-color: #1890ff;
}
._css-step-bottom-upload p{
  width:90%;
  margin: 12px 5% ;
  line-height: 40px;
  border-radius: 4px;
  cursor: pointer;
  color: #fff;
  background-color: rgba(24, 144, 255, 1);
}
._css-upload-icon{
  width: 20px;
  height: 20px;
  padding-left:16px;
  padding-right:12px;
  vertical-align: middle;
}
._css-upload-file-show{
  margin-left:24px;
  line-height: 40px;
  text-align: left;
  align-content: center;
  color: rgba(0, 0, 0, 0.65);
  margin-top:15px;
}
._css-upload-file-show span{
  overflow:hidden;
  text-overflow:ellipsis;
  white-space:nowrap;

}
</style>