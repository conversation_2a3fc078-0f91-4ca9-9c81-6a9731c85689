<template>
    <div class="wrapperForCropper" :style="styleForWrapper">
      <div class="header">
        <div class="title">修改缩略图</div>
        <div class="close el-icon-close" @click.stop="close"></div>
      </div>

      <div class="content" v-loading="isLoadingImg"  element-loading-spinner="el-icon-loading"
      element-loading-background="rgba(0, 0, 0, 0.8)">
        <div class="col1">
          <vue-cropper
            ref='croppers'
            drag-mode="crop"
            alt="请选择一张图片"
            preview=".imgPreviewImg"
            :containerStyle="containerStyle"
            :imgStyle="imgStyle"
            :aspectRatio="aspectRatio"
            :autoCropArea="autoCropArea"
            :viewMode="viewMode"
            :dragMode="dragMode"
            :src="imgSrc">
          </vue-cropper>
        </div>

        <div class="col2">
          <div v-if="hasSelectImg" class="imgPreviewImg" :style="styleForPreview">
          </div>
          <div v-else class="imgPreviewText">图片预览</div>
          <div class="wrapperForPickImg">
            <div class="labelForPickImg" @click.stop="triggerPickImg">选择图片</div>
            <input ref="refInputFile" class="inputFile" type="file" accept="image/*" @change="onFilePickChange" />
          </div>
        </div>
      </div>

      <div class="footer">
        <el-button class="el-btn-cus" plain  size="default" @click.stop="close">取消</el-button>
        <el-button class="el-btn-cus" :disabled="isLoadingImg" type="primary" size="default" @click.stop="cropImage">确定</el-button>
      </div>

    </div>
</template>

<script>
  import VueCropper from 'vue-cropperjs';
  import 'cropperjs/dist/cropper.css';
  //图片裁剪上传
  export default {
    name: "CompsImageClippingUpload2",

    components: { VueCropper},

    data(){
      return {
        imgSrc: '',//占位图
        cropImgBase64: '',//裁剪后的图片 base64
        cropImgBinary: '',//裁剪后的图片 二进制文件流
        hasSelectImg: false,//是否选择了一张图片
        containerStyle: {width:'100%',height:'100%', overflow: 'hidden'}, // 设置裁剪容器（即包含裁剪框和图片的外层容器）的样式
        imgStyle: {
          maxWidth: '100%',
          maxHeight: '100%',
        }, // 用于设置裁剪区域内图片的样式(被隐藏起来的那个图片)
        viewMode: 2, // 用于控制裁剪区域的行为和限制 2 3 
        dragMode: 'crop', // 定义用户在裁剪区域内的拖动行为 crop move none
        autoCropArea: 0.8, // 自动裁剪区域比例
        isLoadingImg: false // 是否在加载图片
      }
    },

    computed: {
      styleForWrapper() {
        return {
          width: this.width +'px',
          height: this.height + 'px'
        }
      },
      aspectRatio() {
        return this.cropAspectRatio // 裁剪宽高比
      },
      styleForPreview() {
        return {
          maxWidth: '100% !important',
          maxHeight: `${100/this.aspectRatio}% !important`,
          aspectRatio: this.aspectRatio
        }
      }
    },

    props: {
      width: {
        default: 400,
        type: Number,
      },
      height: {
        default: 300,
        type: Number,
      },
      cropAspectRatio: {
        type: Number,
        default: 4/3
      },
      occupancyMap: {//占位图
        default: require('../../assets/images/crop-occupancy.png'),
        type: String,
      },
    },

    created() {
      this.imgSrc = this.occupancyMap;
    },

    methods: {
      close() {
        this.$emit('close')
      },
      setImage(file) {
        if (!file.type.includes('image/')) {
          this.$message({
            type: error,
            message: '请上传一张图片'
          });
          return;
        }
        // if(file.size > 204800){
        //   this.$message.error('图片不能大于200KB，请重新上传');
        //   return;
        // }
        this.isLoadingImg = true
        this.hasSelectImg = true;
        if (typeof FileReader === 'function') {
          const reader = new FileReader();
          reader.onload = (event) => {
            this.isLoadingImg = false
            this.imgSrc = event.target.result;
            // rebuild cropperjs with the updated source
            this.$refs.croppers.replace(event.target.result);
          };
          reader.readAsDataURL(file);
        } else {
          this.isLoadingImg = false
          this.$message({
            type: error,
            message: '对不起，暂不支持'
          });
        }
      },
      onFilePickChange(e) {
        const file = e.target.files[0];
        this.setImage(file)
      },
      cropImage() {
        if (!this.hasSelectImg) {
          this.$message({
            type: 'error',
            message: '请上传一张图片'
          });
          return false;
        }

        const previewDom = document.querySelector('.imgPreviewImg')
        const {width,height} = previewDom.getBoundingClientRect()
        // get image data for post processing, e.g. upload or setting image src
        this.cropImgBase64 = this.$refs.croppers.getCroppedCanvas({width:width*2,height:height*2}).toDataURL()
        this.$emit('onCrop',this.cropImgBase64)
        this.close()

        // this.$refs.croppers.getCroppedCanvas().toBlob(_=>{//二进制文件
        //   this.cropImgBinary = _;
        //   // this.$emit('onCrop',this.cropImgBase64,this.cropImgBinary)
        //   this.$emit('onCrop',this.cropImgBase64)
        // });
      },
      triggerPickImg() {
        this.$refs.refInputFile.click()
      }
    },
  }
</script>

<style scoped lang="scss">
.wrapperForCropper {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: stretch;
  background-color: #fff;
  border-radius: 6px;
  * {
    box-sizing: border-box;
  }
  .header {
      flex: 0 0 auto;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 12px 16px;
      border-bottom: 1px solid #ccc;
      .title {
        font-size: 16px;
      }
      .close {
        font-size: 16px;
        cursor: pointer;
      }
  }
  .content {
      flex: 1 1 auto;
      display: flex;
      justify-content: flex-start;
      align-items: stretch;
      overflow: hidden;
      .col1 {
        flex: 1 1 auto;
        padding: 16px;
        border-right: 1px solid #ccc;
        overflow: hidden;
      }
      .col2{
        flex: 0 1 auto;
        width: 33%;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        align-items: stretch;
        .imgPreviewImg {
          flex: 0 0 auto;
          margin: 16px;
          overflow: hidden;
        }
        .imgPreviewText {
          flex: 1 1 auto;
          display: flex;
          justify-content: center;
          align-items: center;
        }
        .wrapperForPickImg {
          display: flex;
          justify-content: center;
          align-items: center;
          padding: 16px 0;
          padding-top: 0;
          .labelForPickImg {
            background-color: #409eff;
            border-radius: 4px;
            padding: 6px 12px;
            cursor: pointer;
            color: #fff;
          }
          .inputFile {
            display: none;
          }
        }
      }
  }
  .footer {
      flex: 0 0 auto;
      display: flex;
      justify-content: flex-end;
      align-items: center;
      padding: 16px;
      border-top: 1px solid #ccc;
      .el-btn-cus {
        padding: 8px 18px;
        border-radius: 3px;
        &:not(:last-of-type) {
          margin-right: 12px;
        }
      }
  }
}
</style>
