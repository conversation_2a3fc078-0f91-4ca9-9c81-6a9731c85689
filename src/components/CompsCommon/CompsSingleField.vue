<template>
  <div class="_css-all-singlefield" :style="computestyle">
    <div class="_css-threefields-front" v-drag="draggreet"  :style="dragstyle" >
      <CompsDialogHeader 
      @oncancel="_oncancel"
      :title="title || '无标题'"></CompsDialogHeader>
      <div class="_css-line1 _css-line" >
        <CompsUsersInput 
        :inittext="inittext||''"
        @oninput="_oninput" :placeholder="placeholder || '请输入企业名称'" :iconclass="inputicon || 'icon-suggested-platform'"
        :is100percent="true"
        :autofocus="true"
        ></CompsUsersInput>
      </div>
      <div v-if="organizeShowSet" class="_css-checkedShow">
        <el-checkbox v-model="organizeShow">隐藏机构名称</el-checkbox>
      </div>
      <div v-if="warningtext != undefined" class="_css-warningtext"  >
        {{warningtext || ''}}
      </div>
      <CompsDialogBtns @onok="_onok"     
    @oncancel="_oncancel"
      ></CompsDialogBtns>
    </div>
  </div>
</template>
<script>
import CompsDialogHeader from "@/components/CompsDialog/CompsDialogHeader";
import CompsDialogBtns from "@/components/CompsDialog/CompsDialogBtns";
import CompsUsersInput from "@/components/CompsAuth/CompsUsersInput"
export default {
  data() {
    return {
      inputstr:'',
      val:'0',
      dragstyle: {
          position: 'fixed',
          right: 'calc(50% - 205px)',
          top: 'calc(50% - 95px)'
      },
      organizeShow: false
    };
  },
  computed: {
    computestyle: {
      get() {
        var _this = this;
        var _s = {};
        if (_this.zIndex) {
          _s["z-index"] = _this.zIndex;
        }
        return _s;
      }
    }
  },
  mounted(){
    var _this = this;
    _this.inputstr = _this.inittext;
    if(this.organizeShowSet){
      this.loadtokencominfo();
    }
  },
  props: {
    zIndex: {
      type: Number,
      required: false
    },
    title: {
      type: String,
      required: false
    },
    placeholder:{
      type: String,
      required: false
    },
    inputicon:{
      type: String,
      required: false
    },
    warningtext:{
      type: String,
      required: false
    },
    inittext:{
      type: String,
      required: false
    },
    organizeShowSet: {
      type: Boolean,
      required: false
    }

  },
  components: {
    CompsDialogHeader,
    CompsDialogBtns,
    CompsUsersInput
  },
  methods: {
    loadtokencominfo() {
      // info_companyname: "北京东晨工元科技发展有限公司", // 左上角公司名称
      // info_companylogo: '', // 左上角logo base64str

      // 根据当前 Token 获取其所在机构，如果为-1的话，则显示默认。
      var _this = this;
      var _Token = _this.$staticmethod.Get("Token");

      _this
        .$axios({
          method: "get",
          url: `${window.bim_config.webserverurl}/api/User/User/GetTokenComInfo?Token=${_Token}`
        })
        .then(x => {
          if (x.status == 200 && x.data.Ret > 0 && x.data.Data) {
            if(x.data.Data.IsShowname) {
              _this.organizeShow = false;
            }else{
              _this.organizeShow = true;
            }
          }
        })
        .catch(x => {});
    },
    draggreet(val){
      this.val = val;
    },
    _onok() {
      var _this = this;
      _this.$emit("onok", _this.inputstr,_this.organizeShow);
    },
    _oninput(str){
      var _this = this;
      _this.inputstr = str;
      _this.$emit("oninput", str);
    },
    _oncancel(){
      var _this = this;
      _this.$emit("oncancel");
    }
  }
};
</script>
<style scoped>
._css-warningtext{
  margin-left:16px;
  margin-right:16px;
  margin-top:14px;
  margin-bottom:18px;
  height:20px;
  line-height: 20px;
  text-align: left;
  color:#F5222D;
  font-size: 12px;
}
._css-ertip-icon{
  height:14px;
  width:14px;
  font-size:14px;
  margin-left:24px;
  color:rgb(245, 34, 45);
}
._css-ertip-text{
  margin-left:8px;
  height:20px;
  line-height:22px;
  color:rgba(0, 0, 0, 0.45);
  font-size:12px;
}
._css-emailrepeat-tip{
  height:20px;
  display: flex;
  align-items: center;
}
._css-threefields-front {
  width: 410px;
  min-height: 48px;
  background-color: rgba(255, 255, 255, 1);
  box-shadow: 0px 13px 24px -17px rgba(11, 41, 62, 0.8);
  border-radius: 4px;
}
._css-all-singlefield {
  position: fixed;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.15);
  display: flex;
  align-items: center;
  justify-content: space-around;
}
._css-line {
    margin-left: 16px;
    margin-right: 16px;
    height: 50px;    
}
._css-line1{
  margin-top: 22px;
  margin-bottom: 22px;
}
._css-checkedShow{
  text-align: left;
  margin-left: 16px;
}
._css-checkedShow /deep/ .el-checkbox__inner {
  width: 16px;
  height: 16px;
}
._css-checkedShow
  /deep/
  .el-checkbox__inner::after {
  left: 5px;
}
._css-checkedShow /deep/ .el-checkbox__label {
  padding-left: 14px;
}
._css-checkedShow
  /deep/
  .el-date-editor.el-input,
.el-date-editor.el-input__inner {
  width: 168px;
}
._css-checkedShow
  /deep/
  .el-checkbox__input.is-checked
  + .el-checkbox__label {
  color: rgba(0, 0, 0, 0.65);
}
</style>