<template>
  <div class="_css-all-memberinfo" :style="computestyle">
    <!-- 对话框前景 -->
    <div class="_css-comps-front" :style="computefrontStyle" v-drag="draggreet" >
      <CompsDialogHeader @oncancel="_oncancel" :title="title || '无标题'"></CompsDialogHeader>
      <!-- 名字展示区 -->
      <div class="_css-memberinfo-head">
        <div class="_css-memberinfo-headicon">{{detailobj.RealName | setAbbreviation}}</div>
        <div class="_css-memberinfo-headmiddle">
          <div class="_css-memberinfo-headmtop">
            <div class="_css-mhtop-name">{{detailobj.RealName}}</div>
            <div class="_css-mhtop-status">{{getComMemText(detailobj)}}</div>
          </div>
          <div class="_css-memberinfo-headmbottom">
            <div class="_css-mhbottom-email">{{detailobj | showAccount}}</div>
            <div class="_css-mhbottom-phone">
              <div class="_css-mhbottom-phoneicon icon-interface-phone"></div>
              <div class="_css-mhbottom-phonetext">{{detailobj.Mobile || '-'}}</div>
            </div>
          </div>
        </div>
        <el-tooltip
            popper-class="css-no-triangle"
            effect="dark"
            content="编辑成员信息"
            placement="bottom"
          >
          <div @click.stop="_cfgbtnclick" class="_css-memberinfo-headconfigbtn icon-interface-set_se"></div>
        </el-tooltip>

      </div>
      <!-- //名字展示区 -->

      <!-- 启用或禁用机构内成员 -->
      <div class="_css-number-set">
        <div class="_css-number-title">是否启用</div>
        <div class="_css-number-title2">{{getEnabled()?'关闭后该账号无法登录使用':'机构管理员账号不可被禁用'}}</div>
        <div class="_css-settingchecboxorswitch" >
            <CompsEloSwitch
            v-bind:editable="getEnabled()"
            v-bind:obj="extdata.enabledstatus"
            ></CompsEloSwitch>
        </div>
      </div>
      <!-- //启用或禁用机构内成员 -->

      <!-- 192 参与项目相关 -->
      <div class="_css-member-projects">
        <div class="_css-member-projecthead">
          <div class="_css-member-projectheadtitle">
            <div class="_css-member-projectheadtext">参与的项目</div>
          </div>
           
        </div>
        <div class="_css-member-projectbody css-miniscroll">
          <div class="_css-member-projectitem css-cp"
          v-for="pitem in extdata.projects" :key="pitem.OrganizeId"
          @click.stop="changeprojectjoining(pitem)"
          >
            <div class="_css-member-projectheadtitle">
              <div class="_css-member-projectitemtext">{{pitem.ProjectName}}</div>
            </div>
          </div>
        </div>
      </div>
      <!-- //192 参与项目相关 -->

      <CompsDialogBtns @onok="_onok" @oncancel="_oncancel" :warningbtn="getEnabled()" 
      @onwarning="_onwarning"
      warningbtntext="删除成员"></CompsDialogBtns>
    </div>
    <!-- //对话框前景 -->
  </div>
</template>
<script>
/*
input:
    
events:
    onok();
    oncancel();
    cfgbtnclick();
    onwarning();
*/
import CompsDialogHeader from "@/components/CompsDialog/CompsDialogHeader";
import CompsDialogBtns from "@/components/CompsDialog/CompsDialogBtns";
import CompsEloSwitch from '@/components/CompsElOverride/CompsEloSwitch';
export default {
  data() {
    return {
       val:'0',
            dragstyle: {
                position: 'fixed',
                right: 'calc(50% - 300px)',
                top: 'calc(50% - 211px)'
            },
      extdata: {
        projects:[
          {
            OrganizeId:'1',
            FullName:'项目1',
            checkstate: '0'
          },
          {
            OrganizeId:'2',
            FullName:'项目2',
            checkstate: '1'
          }
        ],        // 所有参与的项目，以及是否选中状态 （base_organize）
        enabledstatus:{
          checkstate: "1"   // 当前人是否是启用状态
        },
        meminfo_pjcount: 0 // 已分配的当前人可创建的最大项目个数
        //,meminfo_maxpjcount: 20 // 总可分配的当前人创建的最大项目个数
      }
    };
  },
  created(){

    var _this = this;

    if (_this.EnabledMark > 0) {
      _this.extdata.enabledstatus.checkstate = "1";
    } else {
      _this.extdata.enabledstatus.checkstate = "0";
    }
    
    _this.getCurUserProjects();
    
  },
  mounted(){
    var _this = this;
    window.mivue = _this;
  },
  computed: {
    computestyle: {
      get() {
        var _this = this;
        var _s = {};
        if (_this.zIndex) {
          _s["z-index"] = _this.zIndex;
        }
        return _s;
      }
    },
    computefrontStyle: {
      get() {
        var _this = this;
        var _s = {};
        if (_this.width) {
          _s["width"] = _this.width;
        }
            for(var _sattr in _this.dragstyle) {
          _s[_sattr] = _this.dragstyle[_sattr];
        }
        return _s;
      }
    }
  },
  methods: {

    getCurUserProjects(){
        let _this = this;
        let _Token = _this.$staticmethod.Get("Token");
        let _UserId = _this.detailobj.UserId;
        _this.$axios.get(`${window.bim_config.webserverurl}/api/User/User/GetUserInfo?token=${_Token}&userId=${_UserId}`)
            .then(x => {
              if (x.data.Ret > 0) {
               _this.extdata.projects = x.data.Data.UserProjectInfos;
              } 
            }).catch(x => {
              debugger;
            });

    },

    draggreet(val){
        var _this = this;
        _this.val = val;
    },

    // 获取当前成员信息是否可禁用。
    getEnabled(){
      var _this = this;
      if (_this.detailobj.UserId == _this.$staticmethod.Get("UserId")) {
        return false;
      } else {
        return true;
      }
    },


    

    // 修改项目是否参与
    changeprojectjoining(pitem){
      var _this = this;
      if (pitem.checkstate == '0') {
        pitem.checkstate = '1';
      } else {
        pitem.checkstate = '0';
      }
    },

    _onwarning(){
      var _this = this;
      _this.$emit("onwarning");
    },

// 计算人员激活的class
    // 只要DeleteMark为1，就为无效
    getComMemText(item){
      if (item.DeleteMark == 1) {
          return '无效'; // 无效
      } else {

        // EnabledMakr == 1时，为已激活，否则为待激活
        if (item.EnabledMark == 1) {
          return '已激活'; // 已激活
        } else {
          return '待激活'; // 待激活
        }
      }
      
    },
    // 计算人员激活的class
    // 只要DeleteMark为1，就为无效
    getComMemClass(item){
      if (item.DeleteMark == 1) {
          return 'invalid'; // 无效
      } else {

        // EnabledMakr == 1时，为已激活，否则为待激活
        if (item.EnabledMark == 1) {
          return 'actived'; // 已激活
        } else {
          return ''; // 待激活
        }
      }
      
    },

    _cfgbtnclick() {
      var _this = this;
      _this.$emit("cfgbtnclick");
    },
    _oncancel() {
      var _this = this;
      _this.$emit("oncancel");
    },
    _onok() {
      var _this = this;
      _this.$emit("onok", _this.extdata.meminfo_pjcount, _this.extdata.enabledstatus.checkstate);
    }
  },
  props: {
    zIndex: {
      type: Number,
      required: false
    },
    title: {
      type: String,
      required: false
    },
    width: {
      type: String,
      required: false
    },
    detailobj:{
      type: Object,
      required: true
    },
    EnabledMark:{
      type: Number,
      required: true
    }
  },
  components: {
    CompsDialogHeader,
    CompsDialogBtns,
    CompsEloSwitch
  }
};
</script>
<style scoped>

._css-alltosel{
  visibility: hidden;
}

._css-number-title2 {
    flex: 1;
    text-align: left;
    margin-left: 20px;
    font-size: 12px;
    color: rgba(0,0,0,0.45);
}
._css-member-projectheadtext {
  margin-left: 16px;
  /* height:44px; */
  line-height: 48px;
  color: rgba(0, 0, 0, 0.85);
  font-size: 14px;
  font-weight: 600;
  text-align: left;
}
._css-member-projectitemtext {
  margin-left: 16px;
  /* height:44px; */
  line-height: 48px;
  color: rgba(0, 0, 0, 0.65);
  font-size: 14px;
  text-align: left;
}
._css-member-projects {
  height: 192px;
  margin-left: 24px;
  margin-right: 24px;

  /* 临时禁用 */
  /* cursor: not-allowed;
  opacity: 0.3; */
  /* //临时禁用 */
}

._css-member-projecthead {
  height: 48px;
  display: flex;
  align-items: center;
}
._css-member-projectheadtitle {
  height: 100%;
  flex: 1;
}
._css-member-projectheadr {
  height: 100%;
  display: flex;
  align-items: center;
}
._css-member-title {
  height: 22px;
  line-height: 22px;
  color: rgba(0, 0, 0, 0.65);
}
._css-member-checkbox {
  width: 16px;
  height: 16px;
  margin-left: 16px;
  margin-right: 16px;
  cursor: pointer;
  font-size: 16px;
  color: rgba(0, 0, 0, 0.45);
  background-size: 16px 16px !important;
  visibility: hidden;
}
._css-member-projectbody {
  height: calc(100% - 48px);
  overflow-y: auto;
}
._css-member-projectitem {
  height: 48px;
  display: flex;
  align-items: center;
  padding-left: 70px;
}
._css-number-title {
  margin-left: 16px;
  height: 22px;
  line-height: 22px;
  color: rgba(0, 0, 0, 0.85);
  font-size: 14px;
  font-weight: 600;
}
._css-number-edit {
  margin-left: 24px;
  margin-right: 24px;
  flex: 1;
}
._css-number-show {
  height: 20px;
  min-width: 40px;
  line-height: 20px;
  color: rgba(0, 0, 0, 0.65);
  margin-right: 16px;
  font-size: 12px;
}
._css-number-set {
  height: 48px;
  margin-left: 24px;
  margin-right: 24px;
  margin-top: 18px;
  display: flex;
  align-items: center;
}
._css-mhbottom-phoneicon {
  width: 12px;
  height: 12px;
  margin-left: 8px;
  font-size: 12px;
  color: rgba(0, 0, 0, 0.45);
}
._css-mhbottom-phonetext {
  height: 20px;
  line-height: 20px;
  margin-left: 4px;
  font-size: 12px;
  color: rgba(0, 0, 0, 0.45);
}
._css-mhbottom-phone {
  margin-left: 16px;
  display: flex;
  align-items: center;
}
._css-mhbottom-email {
  height: 20px;
  line-height: 20px;
  color: rgba(0, 0, 0, 0.45);
  font-size: 12px;
}
._css-mhtop-name {
  height: 20px;
  line-height: 20px;
  font-weight: 600;
  color: rgba(0, 0, 0, 0.85);
  font-size: 12px;
}
._css-mhtop-status {
  height: 20px;
  line-height: 20px;
  font-size: 12px;
  background-color: #1890ff;
  color: #fff;
  width: 60px;
  margin-left: 16px;
  border-radius: 2px;
}
._css-memberinfo-headmtop {
  width: 100%;
  height: 20px;
  display: flex;
  align-items: center;
}
._css-memberinfo-headmbottom {
  width: 100%;
  height: 20px;
  display: flex;
  align-items: center;
}
._css-memberinfo-headmiddle {
  flex: 1;
  height: 100%;
  margin-left: 8px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
._css-memberinfo-headconfigbtn {
  width: 20px;
  height: 20px;
  margin-right: 40px;
  /* border: 1px dashed red; */
  font-size: 20px;
  color: #1890ff;
  cursor: pointer;
}
._css-memberinfo-headicon {
  height: 40px;
  width: 40px;
  margin-left: 24px;
  background-color: #282e3d;
  border-radius: 4px;
  line-height: 40px;
  color: #fff;
}
._css-memberinfo-head {
  height: 44px;
  margin-top: 24px;
  display: flex;
  align-items: center;
}
._css-comps-front {
  width: 410px;
  min-height: 48px;
  background-color: rgba(255, 255, 255, 1);
  box-shadow: 0px 13px 24px -17px rgba(11, 41, 62, 0.8);
  border-radius: 4px;
}
._css-all-memberinfo {
  position: fixed;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.15);
  display: flex;
  align-items: center;
  justify-content: space-around;
}
</style>