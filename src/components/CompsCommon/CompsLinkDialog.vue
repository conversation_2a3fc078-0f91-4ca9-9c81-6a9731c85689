<template>
  <div class="_css-all-singlefield" :style="computestyle">
    <!-- 对话框前景 -->
    <div class="_css-threefields-front" v-drag="draggreet"  :style="dragstyle" >
      <CompsDialogHeader 
      @oncancel="_oncancel"
      :title="title || '无标题'"></CompsDialogHeader>
      <div class="_css-line1 _css-line _css-outerpage-inputctn" >
        <!-- <CompsUsersInput 
        :inittext="inittext||''"
        @oninput="_oninput" :placeholder="placeholder || '请输入企业名称'" :iconclass="inputicon || 'icon-suggested-platform'"
        :is100percent="true"
        :autofocus="true"
        ></CompsUsersInput> -->

        <input 
        @mousedown="_stopPropagation($event)"
        class="_css-outerpage-input" type="text" v-model="inputstr" :placeholder="placeholder || ''" />



      </div>

      <div v-if="warningtext != undefined" class="_css-warningtext"  >
        {{warningtext || ''}}
      </div>
  
 
      <CompsDialogBtns @onok="_onok"     
    @oncancel="_oncancel"
      ></CompsDialogBtns>
    </div>
    <!-- //对话框前景 -->
  </div>
</template>
<script>
/*
input:
    
events:
  oncancel
  onok(str);
  oninput(str);
*/
import CompsDialogHeader from "@/components/CompsDialog/CompsDialogHeader";
import CompsDialogBtns from "@/components/CompsDialog/CompsDialogBtns";
import CompsUsersInput from "@/components/CompsAuth/CompsUsersInput"
export default {
  data() {
    return {
      inputstr:'',
        val:'0',
            dragstyle: {
                position: 'fixed',
                right: 'calc(50% - 205px)',
                top: 'calc(50% - 95px)'
            },
    };
  },
  computed: {
    computestyle: {
      get() {
        var _this = this;
        var _s = {};
        if (_this.zIndex) {
          _s["z-index"] = _this.zIndex;
        }
        return _s;
      }
    }
  },
  mounted(){
    var _this = this;
    _this.inputstr = _this.inittext;
  },
  props: {
    zIndex: {
      type: Number,
      required: false
    },
    title: {
      type: String,
      required: false
    },
    placeholder:{
      type: String,
      required: false
    },
    inputicon:{
      type: String,
      required: false
    },
    warningtext:{
      type: String,
      required: false
    },
    inittext:{
      type: String,
      required: false
    }

  },
  components: {
    CompsDialogHeader,
    CompsDialogBtns,
    CompsUsersInput
  },
  methods: {
       draggreet(val){
                var _this = this;
                _this.val = val;
            },
    _onok() {
      var _this = this;
      //debugger;
      _this.$emit("onok", _this.inputstr);
    },
    _oninput(str){
      var _this = this;
      _this.inputstr = str;
      _this.$emit("oninput", str);
    },
    _oncancel(){
      var _this = this;
      _this.$emit("oncancel");
    },

    _stopPropagation(ev) {
      ev && ev.stopPropagation && ev.stopPropagation();
    }
  }
};
</script>
<style scoped>

._css-outerpage-inputctn {
  display: flex;
  align-items: center;
  padding: 0 12px;
}

._css-outerpage-input {
    outline: none;
    border: 1px solid rgba(0,0,0,0.3);
    border-radius: 4px;
    height: 40px;
    box-sizing: border-box;
    padding: 0 8px 0 8px;
    width: 100%;
}

._css-warningtext{
  margin-left:16px;
  margin-right:16px;
  margin-top:14px;
  margin-bottom:18px;
  height:20px;
  line-height: 20px;
  text-align: left;
  color:#F5222D;
  font-size: 12px;
}
._css-ertip-icon{
  height:14px;
  width:14px;
  font-size:14px;
  margin-left:24px;
  color:rgb(245, 34, 45);
}
._css-ertip-text{
  margin-left:8px;
  height:20px;
  line-height:22px;
  color:rgba(0, 0, 0, 0.45);
  font-size:12px;
}
._css-emailrepeat-tip{
  height:20px;
  display: flex;
  align-items: center;
}
._css-threefields-front {
  width: 410px;
  min-height: 48px;
  background-color: rgba(255, 255, 255, 1);
  box-shadow: 0px 13px 24px -17px rgba(11, 41, 62, 0.8);
  border-radius: 4px;
}
._css-all-singlefield {
  position: fixed;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.15);
  display: flex;
  align-items: center;
  justify-content: space-around;
}
._css-line {
    margin-left: 16px;
    margin-right: 16px;
    height: 50px;    
}
._css-line1{
  margin-top: 22px;
  margin-bottom: 22px;
}
</style>