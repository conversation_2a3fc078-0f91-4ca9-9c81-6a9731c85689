<template>
  <div class="_css-all-memberitem" :style="computeStyle"
  @click.stop="itemclick()"
  >
      <div class="_css-head-icon" >{{(realname || '') | setAbbreviation}}</div>
      <div 
      :title="(realname || '')"
      class="_css-head-name" >{{realname || ''}}</div>
      <div class="_css-head-email" >{{email || ''}}</div>
      <div v-if="ischecked && ischecked == true" class="_css-head-checkbox icon-checkbox-Selected-Disabled-dis-blue"     
      ></div>
  </div>
</template>
<script>
/*
input:
    
events:
    onok();
    oncancel();
*/
export default {
  data() {
    return {};
  },
  methods:{
      itemclick(){
          var _this = this;
          _this.$emit("onitemclick", _this.userId);
      }
  },
  computed:{
      computeStyle:{
          get(){
              var _s = {};
              var _this = this;
              if (_this.width) {
                  _s["width"] = _this.width;
              } else {
                  _s["width"] = "100%";
              }

              if (_this.height) {
                  _s["height"] = _this.height;
              } else {
                  _s["height"] = "50px";
              }

              return _s;
          }
      }
  },
  props:{
      width:{
          type: String,
          required: false
      },
      height:{
          type: String,
          required: false
      },
      ischecked:{
          type: Boolean,
          required: false
      },
      realname:{
          type: String,
          required: false
      },
      email:{
          type: String,
          required: false
      },
      userId:{
          type: String,
          required: true
      }
  }
};
</script>
<style scoped>
._css-head-checkbox{
    width:24px;
    height:24px;
    margin-right: 16px;
    font-size: 24px;
}
._css-head-email{
    height: 20px;
    margin-left:24px;
    line-height: 20px;
    flex:1;
    color:rgba(0,0,0,0.45);
    text-align: left;
}
._css-head-name{
    height: 22px;
    margin-left:8px;
    line-height: 22px;
    /* width:60px; */
    width:80px;
    color:rgba(0,0,0,0.85);
    text-align: left;

    overflow-x: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
}
._css-head-icon{
    width:24px;
    height:24px;
    line-height: 24px;
    background-color: #202020;
    border-radius: 2px;
    font-size:12px;
    color:#fff;
    margin-left: 22px;
}
._css-all-memberitem{
    display: flex;
    align-items: center;
    cursor: pointer;
}
._css-all-memberitem:hover{
    background-color: rgba(0,0,0,0.02);
}
</style>