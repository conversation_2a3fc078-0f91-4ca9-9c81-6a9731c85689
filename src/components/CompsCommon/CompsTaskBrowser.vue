<template>
    <div class="_css-docbrowser" :style="{'z-index': m_zIndex || 1001}"
    >
        <div class="_css-docbrowser-in" :style="getFrontStyle()"
        v-drag="greet"
        >

            <!-- 标题区域 -->
            <div
            class="_css-docin-title css-fc"
            >
                <!-- 对话框标题及关闭按钮 -->
                <div class="_css-title-content css-f1">
                    {{init_title}}
                </div>
                <div
                @click="$emit('onclose')"
                class="_css-title-closebtn css-closebtn icon-suggested-close"></div>
                <!-- //对话框标题及关闭按钮 -->

            </div>
            <!-- //标题区域 -->

            <!-- 内容区域 -->
            <div
            @mousedown="_stopPropagation($event)"
            class="_css-docin-content"
            >
                <!-- 左树235右列表 -->
                <div class="_css-docin-tree"
                v-loading="m_treeloading"
                element-loading-text="加载中"
                >
                    <el-tree :data="m_rootfolders"
                    ref="ref_wftc"
                    class="_css-customstyle"
                    :expand-on-click-node="false"
                    @node-collapse="node_collapse"
                    @node-expand="node_expand"
                    @node-click="node_click"
                    :default-expand-all="true"
                    node-key="bop_planId"
                    :highlight-current="true"
                    :auto-expand-parent="true"
                    >
                    <span class="css-fc _css-treenode-content" slot-scope="{  data }">
                        <i class="css-icon20 css-fs18 css-fc css-jcsa" :class="data.classname" ></i>
                        <span :title="data.NAME_" class="css-ml4 _css-treenodelabel">{{data.NAME_}}</span>
                    </span>
                    </el-tree>
                </div>
                <div class="_css-docin-list"
                v-loading="m_tableloading"
                element-loading-text="加载中"
                >

                    <!-- 面包屑显示区域 -->
                    <div v-if="m_openstacks.length" class="_css-openstack-ctn">

                        <div
                        @click="func_openparent($event)"
                        class="_css-openstack-item _css-first">返回上一级</div>
                        <div class="css-breadcrumb-splitter _css-openstack-splitter">/</div>

                        <div
                        @click="func_openroot($event)"
                        class="_css-openstack-item _css-first">项目文档</div>
                        <div class="css-breadcrumb-splitter _css-openstack-splitter">/</div>

                        <template v-for="(ositem, index) in m_openstacks"
                        >
                            <div
                            @click="func_openstackitem($event, index)"
                            :key="ositem.FileId"
                            class="_css-openstack-item">{{ositem.FileName}}</div>
                            <div
                            :key="ositem.FileId + '__2'"
                            class="css-breadcrumb-splitter _css-openstack-splitter">/</div>
                        </template>

                    </div>
                    <!-- //面包屑显示区域 -->

                    <el-table
                    row-key="UID_"
                    :fit="true"
                    ref="ref_task_table"
                    :highlight-current-row="false"
                    @row-contextmenu="row_contextmenu"
                    @row-click="row_click"
                    @row-dblclick="on_row_dblclick"
                    @expand-change="ev_expandChange"
                    :border="true"
                    :stripe="false"
                    :data="m_currentfolderfiles"
                    style="width: 565px"
                    :default-sort="{prop: 'date', order: 'descending'}"
                    height="500"
                    class="_css-table-ele _css-table-ele2 css-scroll _css-customstyle"
                    :row-class-name="tableRowClassName"
                    :header-cell-style="{'background-color':'transparent'}"
                    >

                            <!-- 复选框列 -->
                            <el-table-column
                            :resizable="true"
                            min-width="40">
                                <template slot="header">
                                <span
                                    class="css-cb css-icon12 css-cp css-blk"
                                    :class="{'mulcolor-interface-checkbox-selected':m_containsallleaf}"
                                    @click="func_switchall($event)"
                                ></span>
                                </template>
                                <template slot-scope="scope">
                                <span
                                    class="css-cb css-icon12 css-cp _css-cbitem"
                                    :class="{'mulcolor-interface-checkbox-selected':func_testcontainsThis(scope.row)
                                    ,'_css-dis':scope.row.hasChildren}"
                                    @click="func_switchitem($event, scope.row)"
                                ></span>
                                </template>
                            </el-table-column>
                            <!-- 复选框列 -->



                           <el-table-column
                            :resizable="true"
                            class="_css-col-filename"
                            prop="FileName"
                            label="任务名称"
                            min-width="168"
                            >
                                <template slot-scope="scope">
                                    <i
                                        :class="'css-icon20 css-fs18 css-fc css-jcsa ' "
                                    ></i>

                                    <!--下面这个span 原本有一个点击事件，如下。只有定义，没有实现，不知道干啥用的。  -->
                                    <!-- @click="row_filename_click(scope.row, $event)" -->
                                     <span
                                        class="css-cp css-hoverunder css-ml10 css-ellipsis basic-font-color-emphasize"
                                     >
                                        {{scope.row.NAME_}}
                                     </span>
                                </template>
                            </el-table-column>

                    </el-table>
                </div>
                <!-- //左树235右列表 -->

            </div>
            <!-- //内容区域 -->

            <!-- 对话框按钮区域 -->
            <div
            class=" css-common-zdialogbtnctn _css-docin-btnctn"
            >
                <!-- 取消及确定按钮 -->
                <zbutton-function
                    :init_text="'取消'"
                    :init_fontsize="14"
                    :debugmode="true"
                    :init_height="undefined"
                    :init_width="'76px'"
                    :init_bgcolor="'#fff'"
                    :init_color="'#1890FF'"
                    @onclick="$emit('onclose')"
                    >
                </zbutton-function>
                    <zbutton-function
                    :init_text="'确定'"
                    :init_fontsize="14"
                    :debugmode="true"
                    :init_height="undefined"
                    :init_width="'76px'"
                    @onclick="_emit_onok()"
                    >
                </zbutton-function>
                <!-- //取消及确定按钮 -->

            </div>
            <!-- //对话框按钮区域 -->

        </div>
    </div>
</template>
<script>
export default {
    data() {
        return {

            // 已选中所有叶子节点
            // -----------------
            m_containsallleaf: false,

            // 左侧树加载中
            // -----------
            m_treeloading: false,
            m_tableloading: false,

            // 拖动相关
            // --------
            val:'123',

            m_organizeId:'', 

            // 接收传入参数
            // -----------
            m_zIndex: 0,
            m_height: 0,
            m_width: 0,

            // 根级文件夹
            // ---------
            m_rootfolders: [],

            // 当前正在显示的文件夹及文件
            // 当前选中了哪些文件
            // 记录的打开栈
            // -----------
            m_currentfolderfiles: [],
            m_selectedfiles:[],
            m_openstacks:[],

            // 树控件的 props 属性对象
            // ----------------------
            treem_props:{
                children: "children",
                label: "FileName",
                isLeaf: "isLeaf"
            },
        };
    },
    mounted(){
        var _this = this;
        window.taskbrowervue = this;
        _this.m_zIndex = _this.init_zIndex;
        _this.m_width = _this.init_width;
        _this.m_height = _this.init_height; 
        _this.m_organizeId = _this.init_organizeId;

        // 加载根级分类（不带“全部分类”)
        // ---------------------------
        _this.func_getRootCategories();

        // 加载根级文件夹及文件
        // ------------------
        _this.func_readroot();

        setInterval(()=>{
            _this.$refs.ref_task_table && _this.$refs.ref_task_table.doLayout();
        }, 1000);
    },
    methods: {

        // 节点展开
        // --------
        ev_expandChange() {

            var _this = this;
            console.log('line 261');
        },

        // 返回全部的叶子节点
        // ----------------
        func_getonlyleafdata(alldata) {
            var _this = this;
            var dataarr = [];
            for (var i = 0; i < alldata.length; i++) {
                if (!alldata[i].hasChildren) {
                    dataarr.push(alldata[i]);
                } else {
                    var childrenofthis = _this.func_getonlyleafdata(alldata[i].children);
                    for (var j = 0; j < childrenofthis.length; j++) {
                        dataarr.push(childrenofthis[j]);
                    }
                }
            }
            return dataarr;
        },

        // 切换全部的选中状态
        // _this.m_selectedfiles
        // m_currentfolderfiles
        // ----------------
        func_switchall(ev) {

            // 如果 m_currentfolderfiles 长度为0
            // 不操作
            // ------
            var _this = this;
            _this._stopPropagation(ev);
            if (_this.m_currentfolderfiles.length <= 0) {
                return;
            }

            // 如果 m_selectedfiles 长度小于 m_currentfolderfiles
            // 则直接赋予（深拷贝）
            // -----------------
            if (_this.m_selectedfiles.length < _this.m_currentfolderfiles.length) {
                //_this.m_selectedfiles = _this.$staticmethod.DeepCopy(_this.m_currentfolderfiles);

                // 将 _this.m_selectedfiles 改为全部叶子节点
                // ---------------------------------------
                _this.m_selectedfiles = _this.func_getonlyleafdata(_this.m_currentfolderfiles);

            } else {

                // 否则清空
                // --------
                _this.m_selectedfiles = [];

            }

            // 刷新 allleaf
            // ------------
            _this.$nextTick(()=>{
                _this.m_containsallleaf = _this.func_testcontainsAllLeaf();
            });
        },

        // 切换单条的选中状态
        // _this.m_selectedfiles
        // m_currentfolderfiles
        // -----------------
        func_switchitem(ev, item) {

            // 如果 m_currentfolderfiles 长度为0
            // 不操作
            // ------
            var _this = this;
            _this._stopPropagation(ev);
            if (_this.m_currentfolderfiles.length <= 0) {
                return;
            }

            // 非叶子节点直接跳出
            // -----------------
            if (item.hasChildren) {
                return;
            }

            // 如果包含它就移除掉它，否则加进来
            // -----------------------------
            if (_this.func_testcontainsThis(item)) {
                _this.m_selectedfiles = _this.m_selectedfiles.filter(x => x.UID_ != item.UID_);
            } else {
                _this.m_selectedfiles.push(item);
            }

            // 刷新 allleaf
            // ------------
            _this.$nextTick(()=>{
                _this.m_containsallleaf = _this.func_testcontainsAllLeaf();
            });


        },

        // 对于非全选框，判断当前是否包含了此条数据
        // -------------------------------------
        func_testcontainsThis(item) {
            var _this = this;
            if (_this.m_currentfolderfiles.length <= 0) {
                return false;
            }
            var hasIndex = _this.m_selectedfiles.findIndex(x => x.UID_ == item.UID_);
            return hasIndex >= 0;
        },

        func_testcontainsAllLeaf() {
            var _this = this;
            if (_this.m_currentfolderfiles.length <= 0) {
                return false;
            }
            var allleaf = _this.func_getonlyleafdata(_this.m_currentfolderfiles);
            var equalall = _this.m_selectedfiles.length == allleaf.length;
            return equalall;
        },

        func_testcontainsAll() {
            var _this = this;
            if (_this.m_currentfolderfiles.length <= 0) {
                return false;
            }
            var equalall = _this.m_currentfolderfiles.length == _this.m_selectedfiles.length;
            return equalall;
        },

        _emit_onok() {
            var _this = this;
            _this.$emit("onok", _this.m_selectedfiles);
        },

        // 单击节点，加载里面的文件夹及文件
        // -----------------------------
        node_click(itemi, node, comp) {
            console.log(itemi,'itemiitemiitemiitemiitemi')
            var _this = this;
            // var id = itemi.id;//原数据方法
            var id = itemi.bop_planId

            // 设置 openStacks
            // ---------------
            //_this.m_openstacks = _this.$staticmethod.DeepCopy(node.data.chain);

            // 读取文件夹及文件
            // ---------------
            _this.func_readroot(id);
        },

        // 文件大小格式化
        // -------------
        FileSize_formatter(row, column) {
            var _this = this;

            // 0B
            if (row.iszerobytes) {
            return "0B";
            }

            let size = row.FileSize;
            if (size) {
            if (size == 0) {
                // 文件夹不显示大小
                return "-";
            } else {
                // 显示文件的大小，需要转换为字符串
                return _this.$staticmethod.convertToSizeStr(size);
            }
            } else {
            // size is undefined.
            return "";
            }
        },

        // 打开指定的 openstack 中的一项
        // ---------------------------
        func_openstackitem(ev, index) {

            // slice(0, index + 1)
            // -------------------
            var _this = this;
            _this.m_openstacks = _this.m_openstacks.slice(0, index + 1);

            // 读取文件夹
            // ---------
            if (_this.m_openstacks.length > 0) {
                var folderId = _this.m_openstacks[_this.m_openstacks.length - 1].FileId;
                _this.func_readfolder(folderId);
            } else {
                _this.func_openroot();
            }

        },

        // 返回上一级
        // ----------
        func_openparent(ev) {

            // 设置打开栈
            // ---------
            var _this = this;
            _this.m_openstacks = _this.m_openstacks.slice(0, _this.m_openstacks.length - 1);

            // 打开指定文件夹
            // -------------
            if (_this.m_openstacks.length > 0) {
                var folderId = _this.m_openstacks[_this.m_openstacks.length - 1].FileId;
                _this.func_readfolder(folderId);
            } else {
                _this.func_openroot();
            }

        },

        // 点击“项目文档”打开根级目录
        // ------------------------
        func_openroot(ev) {

            // 清空“打开栈”
            // 获取数据
            // -------
            var _this = this;
            _this.m_openstacks = [];
            _this.func_readroot();
        },

        recfunc_additsdirectchildren(item, arrsrc) {

            // 从 arrsrc 中找到所有 PARENTTASKUID_ 为 item.UID_ 的数据
            // -----------------------------------------------------
            var _this = this;
            var ifhaschildren = arrsrc.filter(x => x.PARENTTASKUID_ == item.UID_);
            if (ifhaschildren.length) {
                item.children = _this.$staticmethod.DeepCopy(ifhaschildren);
                item.hasChildren = true;

                // 递归调用
                // --------
                if (item.children.length) {
                    for (var i = 0; i < item.children.length; i++) {
                        _this.recfunc_additsdirectchildren(item.children[i], arrsrc);
                    }
                }

            } else {
                item.children = [];
                item.hasChildren = false;
            }


        },

        // 构造为树形结构数据，直接修改数组 arr
        // ---------------------------------
        func_maketree(arr) {

            // 树形结构数组平铺结构构造为树形结构
            // -------------------------------
            var _this = this;

            // 找出所有没有 PARENTTASKUID_ （为-1）的数据，作为一级数组添加到新数组中
            // -----------------------------------------------------------------
            var allrootnodeDatas = arr.filter(x => x.PARENTTASKUID_ == '-1');

            // 对每一条刚挂的数据，调用可递归函数，将 arr 中找到其子节点，添加到这一条刚挂的数据上
            // 在这个动作中，被挂的数据（父数据，添加 children 属性，及 hasChildren 为 true）
            // 挂之后，递归调用这个动作
            // ----------------------
            for (var i = 0; i < allrootnodeDatas.length; i++) {
                _this.recfunc_additsdirectchildren(allrootnodeDatas[i], arr);
            }

            // 返回这个新数组
            // -------------
            return allrootnodeDatas;

        },

        // 获取 -1000 节点上的数据，如果没有节点，则
        // --------------------------------------
        func_readroot(planid) {

            var _this = this;
            if (!planid) {
                _this.m_currentfolderfiles = [];
                return;
            }

            // 调用接口，获取表格数据
            // --------------------
            _this.m_tableloading = true;
            var _url = `${this.$MgrBaseUrl.planList}?planId=${planid}&Token=${this.$staticmethod.Get('Token')}`;
            _this.$axios.get(_url).then(x => {

                // 这里需要构造树形组织结构
                console.log(x.data)
                console.log(x.data.Tasks)

                var arr = _this.$staticmethod.DeepCopy(x.data.Data.Tasks);


                arr = _this.func_maketree(arr);

                // 赋予表格的数据对象
                // -----------------
                _this.m_currentfolderfiles = arr;
                _this.m_tableloading = false;


            }).catch(x => {
                console.log('catch line 156');
                _this.m_tableloading = false;
            })
        },

        // 打开文件夹
        // ---------
        enterdir(folderId, folderName) {
            var _this = this;

            // 设置 openStacks
            // ---------------
            _this.m_openstacks.push({
                FileId: folderId,
                FileName: folderName
            });

            // 读取文件夹及文件数据
            _this.func_readfolder(folderId);
        },

        // 打开某一行数据（打开某个文件夹 or 选中某个文件并直接点击“确定”）
        // 表格、列表视图的文件（夹）进入方法（包括回收站不可进的逻辑）。
        // ----------------------------------------------------------
        item_enter(row){

            // 进行文件进入动作
            // ---------------
            var _this = this;
            if (row.FileSize == '0') {

                // 进入文件夹
                // ---------
                _this.enterdir(row.FileId, row.FileName, null);

            } else {

                // 打开或预览文件
                // -------------
                _this.begin_previewfile(row, null);

            }

        },

        // 打开的是文件
        // -----------
        begin_previewfile(row) {
            console.log('打开了文件');
        },

        // 被触发的双击事件
        // ---------------
        on_row_dblclick(row, column, ev) {
            var _this = this;
            ev.stopPropagation();
            _this.item_enter(row);
        },

        // 单击的不是复选框，而是行时，切换当前行的选中状态，同时确保其它行没有被选中。
        // --------------------------------------------------------------------
        row_click(row, column, ev) {
            var _this = this;
            // _this.m_selectedfiles = [row];
            // ev.stopPropagation();
            _this.func_switchitem(ev, row);
        },

        // 表格行右击
        // ---------
        row_contextmenu(row, column, ev) {
            console.log('表格行右击');
            var _this = this;
        },

        // 根据是否选中了某一行，返回类字符串
        // -------------------------------
        tableRowClassName({ row, rowIndex }) {
            var _this = this;
            var _sel_hasThisRow =
                _this.m_selectedfiles.filter(x => x.UID_ == row.UID_).length >
                0;
            return "css-tdunder " + (_sel_hasThisRow ? "css-tabrow-selected" : "");
        },

        // 加载某个文件夹下的子文件夹
        // ------------------------
        treefunc_loadChild(node, resolve) {

            // 获取所展开节点下的子文件夹
            // ------------------------
            var _this = this;
            var _nodedata = node.data;

            // 被第一次莫名其妙调用，直接跳出
            // ----------------------------
            if (!_nodedata.bmc_code) {
                return;
            }

            // 调用接口，获取子项
            // -----------------
            var _url = `${window.bim_config.webserverurl}/api/Material/MaterialCategory/GetCategories?organizeId=${_this.m_organizeId}&baseCode=${_nodedata.bmc_code}&Token=${_this.$staticmethod.Get('Token')}`;
            _this.$axios.get(_url).then(x => {

                // 拿到当前文件夹的子级，resolve 到下面
                // ----------------------------------
                var onlyFolder = x.data.Data.list;//.filter(x => x.FileSize == "0");

                // 挂上 classname
                // --------------
                for (var i = 0; i < onlyFolder.length; i++) {
                    onlyFolder[i].classname = 'icon-interface-component_classification';
                }

                _this.func_analysisfolders(onlyFolder, (toval)=>{
                    resolve(toval);
                });


            }).catch(x => {
                console.log('catch line 156');
            })
        },

        // 收起节点 回调
        // 展开节点 回调
        // ------------
        node_collapse(itemi, node, comp) {

            // 修改 classname
            // ---------------
            var _this = this;
            //itemi.classname = "icon-interface-component_classification";
        },
        node_expand(itemi, node, comp) {

            // 修改 classname
            // ------------------------------------
            var _this = this;
            //itemi.classname = "icon-interface-component_classification";
        },

        // 分析 表示多个文件夹的数组 maybeRootNotFile 是否有子文件夹
        // ------------------------------------------------------
        func_analysisfolders(maybeRootNotFile, callback) {

            var _this = this;

            // 遍历【当前需要解析是否有子文件夹的】所有文件夹
            // ------------------------------------------
            for (var i = 0; i < maybeRootNotFile.length; i++) {

                // 有子文件夹
                // ---------
                if (maybeRootNotFile[i].DirectChildrenCount == 0) {
                    maybeRootNotFile[i].isLeaf = true;
                    maybeRootNotFile[i].classname = 'icon-interface-associated-component _css-1890ff';
                } else {
                    maybeRootNotFile[i].isLeaf = false;
                }
            }

            // 赋值以渲染根级目录
            // -----------------
            if (callback) {
                callback(maybeRootNotFile);
            }


        },

        // 提供方法，获取 treedata
        // ----------------------
        func_getRootCategories() {
            var _this = this;
            _this.m_treeloading = true;
            // var _url = `${window.bim_config.webserverurl}/api/Plus/PlusProject/GetTreeData?organizeId=${_this.init_organizeId}&bimcomposerId=${_this.init_organizeId}`;
            // var _url = `${window.bim_config.webserverurl}/api/Plus/PlusProject/GetTaskPlans?organizeId=${_this.init_organizeId}&bimcomposerId=${_this.init_organizeId}`;
            
            //参数key是：organizeId。value是：bimcomposerId
            //其实key应该是bimcomposerId，但是懒得改了，注释说明下
            var _url = `${this.$MgrBaseUrl.planGetList}?organizeId=${this.m_organizeId}&Token=${this.$staticmethod.Get('Token')}`;
            _this.$axios.get(_url).then(x => {

                // 绑定根级文件夹到 tree
                // 先拿到所有非文件的数据
                // 绑定 icon-interface-unfolder 图标类
                // ----------------------------------
                // _this.m_rootfolders = x.data.Data.List; //2020-12-02原数据
                _this.m_rootfolders = x.data.Data;//接口“NewComm_GetListByOrganizeId”数据
                _this.m_treeloading = false;

            }).catch(x => {

                _this.m_treeloading = false;

                debugger;
            });
        },

        // 前景样式
        // --------
        _stopPropagation(ev) {
            ev && ev.stopPropagation && ev.stopPropagation();
        },
        greet(val){
            var _this = this;
            _this.val = val;
        },
        getFrontStyle() {
            var _this = this;
            var _s = {};
            _s["width"] = _this.m_width;
            _s["height"] = _this.m_height;
            _s["position"] = 'fixed';
            _s["right"] = `calc(50% - ${parseInt(_this.m_width.toString().replace('px', 'px')) / 2}px)`;
            _s["top"] = `calc(50% - ${parseInt(_this.m_height.toString().replace('px', 'px')) / 2}px)`;
            return _s;
        }
    },
    props: {

        // 前景高度
        // 前景宽度
        // 总体的 zindex
        // 标题
        // -------------
        init_height: {
            type: String,
            required: true
        },
        init_width: {
            type: String,
            required: true
        },
        init_zIndex: {
            type: Number,
            required: true
        },
        init_title: {
            type: String,
            required: true
        },
        
        init_organizeId: {
            type: String,
            required: true
        }
    }
}
</script>
<style scoped>

._css-cbitem {
    flex:none;
}

._css-dis {
    background-color: rgba(0, 0, 0, 0.06);
    cursor: not-allowed;
}

._css-1890ff {
    color: #1890FF;
}

._css-openstack-splitter {
     float:left;
}
._css-openstack-item {
    /* height: 18px;
    line-height: 18px;
    cursor: pointer;
    font-size: 12px;
    float:left; */

    height: 18px;
    line-height: 18px;
    cursor: pointer;
    font-size: 12px;
    float: left;
    /* max-width: 400px;
    min-width: 26px;
    overflow-x: hidden; */
    /* text-overflow: ellipsis; */
    white-space: nowrap;
}
._css-openstack-item:hover {
    text-decoration: #1890FF;
    color:#1890FF;
}
._css-openstack-ctn {
    height: 36px;
    display: flex;
    align-items: center;
    padding-left: 12px;
    overflow-x: auto;
}
._css-docin-tree {
    font-size: 12px;
    width:235px;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.02);
    overflow-y: auto;
}
._css-docin-list {
    /* flex: 1; */
    width:calc(100% - 235px);
    height: 100%;
    display: flex;
    flex-direction: column;
}
._css-title-content {
    font-size: 16px;
    margin: 0 24px 0 24px;
    flex:1;
    text-align: left;
}
._css-docin-content {
    width:100%;
    flex:1;
    display: flex;
    height: calc(100% - 50px - 64px);
}
._css-docin-btnctn {
    border-top: 1px solid rgba(0, 0, 0, 0.09);
    height:50px;
    margin-top: 0;
    flex:none;
    box-sizing: border-box;
}
._css-docin-title {
    height: 64px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.09);
    flex:none;
    box-sizing: border-box;
}
._css-title-closebtn {
    margin-right: 24px;
}
._css-docbrowser-in {
    background-color: #fff;
    border-radius: 4px;
    display: flex;
    flex-direction: column;
}
._css-docbrowser {
    width: 100%;
    height:100%;
    position: fixed;
    background: rgba(0, 0, 0, 0.3);
    display: flex;
    align-items: center;
    justify-content: space-around;
}
</style>
