<template>
  <div class="_css-modifydicitem" :style="computestyle">
    <!-- 对话框前景 -->
    <div class="_css-comps-front" v-drag="draggreet"  :style="dragstyle">
      <CompsDialogHeader @oncancel="_oncancel" :title="title || '无标题'"></CompsDialogHeader>

      <!-- 字典项名： -->
      <div class="_css-line1 _css-line">
        <CompsUsersInput
          :inittext="extdata.item_name"
          :fieldname="extdata.item_name_fieldname"
          fieldwidth="120px"
          fieldcolor="rgba(0,0,0,0.65)"
          @oninput="_oninput_name"
          :placeholder="extdata.item_name_placeholder"
          :iconclass="''"
          :is100percent="true"
        ></CompsUsersInput>
      </div>
      <!-- //字典项名： -->

      <!-- 字典项值： -->
      <div class="_css-line1 _css-line" v-show="false">
        <CompsUsersInput
          :inittext="extdata.item_val"
          fieldname="字典项值"
          fieldwidth="120px"
          fieldcolor="rgba(0,0,0,0.65)"
          @oninput="_oninput_val"
          placeholder="请输入字典项值"
          :iconclass="''"
          :is100percent="true"
        ></CompsUsersInput>
      </div>
      <!-- //字典项值： -->

      <!-- 排序： -->
      <div class="_css-line1 _css-line" v-show="false">
        <CompsUsersInput
          :inittext="extdata.item_sort"
          fieldname="字典排序"
          fieldwidth="120px"
          fieldcolor="rgba(0,0,0,0.65)"
          @oninput="_oninput_sort"
          placeholder="请输入字典项排序"
          :iconclass="''"
          :is100percent="true"
        ></CompsUsersInput>
      </div>
      <!-- //排序： -->

      <!-- 标签 v-show="hidecoloredit == false" -->
      <!-- <div class="_css-linetag _css-line _css-linelast"
      v-show="hidecoloredit == false"
      >
        <div class="_css-tag-icon"></div>
        <div class="_css-tag-text">颜色标签</div>
        <div
          @click.stop="changeliststatus"
          class="_css-tag-content"
          :style="{'background-color': extdata.item_color_ItemCode}"
        >
          <div class="_css-tag-content-colorlist"
           v-clickOutClose="function(){extdata.isshowing_colorlist = false;}"
           v-if="extdata.isshowing_colorlist == true">
            <div
              @click.stop="pickcolor(cor)"
              v-for="cor in extdata.colorarr"
              :key="cor"
              class="_css-color-item"
              :style="{'background-color': cor}"
            ></div>
          </div>
        </div>
      </div> -->
      <!-- //标签 -->

      <!-- 新版标签 -->
      <div class="_css-newtag" 
      v-show="hidecoloredit == false" 
      >
        <div v-for="cor in extdata.colorarr" :key="cor"
          @click.stop="extdata.item_color_ItemCode = cor"
           class="_css-newcolor-item"
           :class="{'checked':extdata.item_color_ItemCode == cor}"
           :style="{'background-color': cor}"
        >
          <div class="_css-newcolor-checkedicon icon-checkbox-Selected-Disabled-dis" ></div>
        </div>
      </div>
      <!-- //新版标签 -->

      <!-- 如果是新版本标签，且不显示色块选择，则需要添加占位 -->
      <div
      v-if="hidecoloredit == true" 
      class="_css-holder"
      >

      </div>
      <!-- //如果是新版本标签，且不显示色块选择，则需要添加占位 -->

      <CompsDialogBtns
        @onok="_onok"
        @oncancel="_oncancel"
        :warningbtn="id != '-1'"
        @onwarning="_onwarning"
        warningbtntext="删除"
      ></CompsDialogBtns>
    </div>
    <!-- //对话框前景 -->
  </div>
</template>
<script>
/*
input:
    
events:
    onok();
    oncancel();
*/
import CompsDialogHeader from "@/components/CompsDialog/CompsDialogHeader";
import CompsDialogBtns from "@/components/CompsDialog/CompsDialogBtns";
import CompsUsersInput from "@/components/CompsAuth/CompsUsersInput";
export default {
  data() {
    return {
         val2:'0',
            dragstyle: {
                position: 'fixed',
                right: 'calc(50% - 205px)',
                top: 'calc(50% - 199px)'
            },
      extdata: {
        isshowing_colorlist: false, // 正在显示颜色下拉列表
        colorarr: [
          "rgba(28, 50, 75, 1)",
          "rgba(86, 98, 112, 1)",
          "rgba(127, 179, 210, 1)",
          "rgba(151, 107, 61, 1)",
          "rgba(184, 158, 123, 1)",
          "rgba(29, 164, 140, 1)",
          "rgba(115, 113, 157, 1)"
        ], // 备选颜色
        item_id: "", // 字典项ID
        item_name: "", // 字典项名
        item_val: "", // 字典项值
        item_sort: "", // 字典项排序
        item_color_ItemCode: "", // 字典项颜色（标签）
        item_name_fieldname: '',
        item_name_placeholder:'',

      }
    };
  },
  computed: {
    computestyle: {
      get() {
        var _this = this;
        var _s = {};
        if (_this.zIndex) {
          _s["z-index"] = _this.zIndex;
        }
        return _s;
      }
    }
  },
  //mounted() {
  created() {
    var _this = this;
    //debugger;
    _this.extdata.item_id = _this.id;
    _this.extdata.item_name = _this.name;
    _this.extdata.item_val = _this.val;
    _this.extdata.item_sort = _this.sort;
    _this.extdata.item_color_ItemCode = _this.colorstr;
    _this.extdata.item_name_fieldname = _this.placeholdername;
    _this.extdata.item_name_placeholder = '请输入' + _this.placeholdername;
    console.log('===',this.hidecoloredit)
  },
  methods: {
    
      draggreet(val2){
                var _this = this;
                _this.val2 = val2;
            },
    pickcolor(cor) {
      var _this = this;
      _this.extdata.item_color_ItemCode = cor;
      _this.extdata.isshowing_colorlist = false;
    },
    changeliststatus() {
      var _this = this;
      if (_this.extdata.isshowing_colorlist == true) {
        _this.extdata.isshowing_colorlist = false;
      } else {
        _this.extdata.isshowing_colorlist = true;
      }
    },
    _onwarning(para) {
      var _this = this;
      _this.$emit("onwarning", _this.extdata.item_id);
    },
    _oncancel() {
      var _this = this;
      _this.$emit("oncancel");
    },
    _onok() {
      var _this = this;
      _this.$emit("onok", _this.extdata);
    },
    _oninput_name(str) {
      var _this = this;
      _this.extdata.item_name = str;
      _this.$emit("oninput", "name", str);
    },
    _oninput_val(str) {
      var _this = this;
      _this.extdata.item_val = str;
      _this.$emit("oninput", "val", str);
    },
    _oninput_sort(str) {
      var _this = this;
      _this.extdata.item_sort = str;
      _this.$emit("oninput", "sort", str);
    }
  },
  props: {
    zIndex: {
      type: Number,
      required: false
    },
    title: {
      type: String,
      required: false
    },
    id: {
      type: String,
      required: true
    },
    name: {
      type: String,
      required: true
    },
    val: {
      type: String,
      required: true
    },
    sort: {
      type: String,
      required: true
    },
    colorstr: {
      type: String,
      required: true
    },
    placeholdername: {
      type: String,
      default: ''
    },
    hidecoloredit:{
      type: Boolean,
      required:false
    }
  },
  components: {
    CompsDialogHeader,
    CompsDialogBtns,
    CompsUsersInput
  }
};
</script>
<style scoped>

._css-holder{
  height:16px;
}

._css-newcolor-checkedicon{
  height:24px;
  width:24px;
  font-size:24px;
  color:#fff;
  display: none;
}

._css-newcolor-item.checked ._css-newcolor-checkedicon{
  display: block;
}

._css-newcolor-item{
  height:40px;
  width:40px;
  border-radius:4px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: space-around;
}

._css-newtag{
  height:72px;
  display: flex;
  align-items: center;  
  justify-content: space-between;
  padding-left:16px;
  padding-right: 16px;
  box-sizing: border-box;
}

._css-color-item {
  height: 22px;
  margin-top: 2px;
  border-radius: 4px;
}
._css-color-item:hover {
  opacity: 0.5;
}
._css-tag-content-colorlist {
  position: absolute;
  top: 100%;
  left: 0;
  min-height: 20px;
  width: 100%;
  z-index: 2;
  background-color: #fff;
  border-radius: 4px;
  box-sizing: border-box;
  box-shadow: 0px 1px 3px 0px rgba(0, 21, 41, 0.12);
  border-radius: 2px;
}

._css-tag-content {
  flex: 1;
  width: 100%;
  height: 20px;
  margin-left: 4px;
  margin-right: 24px;
  cursor: pointer;
  position: relative;
  background-color: #fff;
  border-radius: 4px;
}
._css-tag-text {
  margin-left: 4px;
  text-align: left;
  width: 83.71px;
  color: rgba(0, 0, 0, 0.65);
}
._css-tag-icon {
  height: 14px;
  width: 14px;
  font-size: 14px;
  margin-left: 8px;
}
._css-linetag {
  background-color: rgba(0, 0, 0, 0.02);
  margin-top: 20px;
  display: flex;
  align-items: center;
}
._css-linelast {
  margin-bottom: 22px;
}
._css-comps-front {
  width: 410px;
  min-height: 48px;
  background-color: rgba(255, 255, 255, 1);
  box-shadow: 0px 13px 24px -17px rgba(11, 41, 62, 0.8);
  border-radius: 4px;
}
._css-modifydicitem {
  position: fixed;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.15);
  display: flex;
  align-items: center;
  justify-content: space-around;
}
._css-line {
  margin-left: 16px;
  margin-right: 16px;
  height: 50px;
}
._css-line1 {
  margin-top: 20px;
}
</style>