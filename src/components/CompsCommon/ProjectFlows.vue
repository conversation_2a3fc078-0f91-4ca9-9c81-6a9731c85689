<template>
	<div class="project-menu-list">
		<div class="list-tree-left">
			<div class="left-title">
				<div class="companu-name name-list">菜单目录</div>
				<div class="companu-name name-click" @click.stop="addFirstTreeList">{{ info_companyname }}</div>
			</div>
			<div class="left-tree-list left-menu">
				<el-menu
					:default-active="defaultActive"
					class="el-menu-vertical-demo css-menu-list"
					@select="handelSelectMenuList"
					@open="openList"
					@close="openList"
				>
					<div
						v-for="(item,index) in treeData" 
						:key='index' 
						:index="index + ''"
					>
						<el-submenu :index="index + ''" v-if="item.children.length > 0">
							<template slot="title" >
								<i class="icon-bg icon-img icon-img-before" v-if="getIconClass(item.iconclass)" :class="item.iconclass"></i>
								<i class="icon-bg icon-img" v-else :class="item.iconclass"></i>

								<span>{{item.text}}</span>
							</template>
							<div 
								v-for="(itemC,indexC) in item.children" :key='indexC'  
								:index="index + '-' + indexC" 
							>
								<el-submenu :index="index + '-' + indexC" v-if="itemC.children.length > 0">
									<template slot="title"><span class="text-padding64">{{ itemC.text }}</span></template>
										<el-menu-item 
											@click.native="handleSelectClick(itemC3,'333')"
											v-for="(itemC3,indexC3) in itemC.children" :key='indexC3'
											:index="index+'-' +indexC+'-' + indexC3">
											<span class="text-padding76">{{itemC3.text}}</span>
										</el-menu-item>
								</el-submenu>

								<el-menu-item @click.native="handleSelectClick(itemC,'222')" :index="index+1+'-' + indexC + 1" v-else>
									<span class="text-padding64">{{itemC.text}}</span>
								</el-menu-item>
							</div>
						</el-submenu>
						<el-menu-item :index="index + ''" @click.native="handleSelectClick(item,'111')" v-else>
							<i class="icon-bg icon-img icon-img-before" v-if="getIconClass(item.iconclass)" :class="item.iconclass"></i>
							<i class="icon-bg icon-img" v-else :class="item.iconclass"></i>
							<span>{{item.text}}</span>
						</el-menu-item>
					</div>
				</el-menu>
			</div> 
		</div>
		<div class="list-tree-right" v-if="rightShowClickText !=''">
			<div class="right-title">
				当前目录：<span class="c007AFF">{{ rightShowClickText }}</span>
			</div>
			<div class="css-right-btn">
				<div class="edit-btn" v-if="addMenuJudgment()" @click.stop="formDialogEdit('新增',clickTreeListItem)">
					<p class="btn-icon"><i class="icon-f-add"></i></p>
					<p class="btn-text">新增</p>
				</div>
				<div class="edit-btn" v-if="clickTreeListItem.Type != 0 && rightShowClickText != '一级菜单'" @click.stop="formDialogEdit('编辑',clickTreeListItem)">
					<p class="btn-icon"><i class="icon-f-edit"></i></p>
					<p class="btn-text">编辑</p>
				</div>
				<div class="edit-btn" v-if="clickTreeListItem.Type != 0 && rightShowClickText != '一级菜单'" @click.stop="delMenuList(clickTreeListItem)">
					<p class="btn-icon"><i class="icon-f-del"></i></p>
					<p class="btn-text">删除</p>
				</div>
			</div>
		</div>
		<zdialog-function
      :init_title="editStateText + '菜单：' + rightShowClickText"
      :init_zindex="1003"
      :init_innerWidth="640"
      :init_width="640"
			:init_height="477"
      init_closebtniconfontclass="icon-suggested-close"
      :init_usecustomtitlearea="false"
      @onclose="resetForm()"
      v-if="showFormDialog"
    >
			<div slot="mainslot" class="form-list" @mousedown="_stopPropagation($event)">
				<div class="form-list">
					<el-form :model="ruleForm" :rules="rules" ref="ruleForm" label-width="110px" class="demo-ruleForm">
						<el-form-item label="菜单名称" prop="text">
							<el-input v-model="ruleForm.text" placeholder=""></el-input>
						</el-form-item>
						<el-form-item label="父级菜单" v-if="addMenuListType != 0">
							<el-input class="parent-readonly" v-model="rightShowClickText" placeholder="" readonly></el-input>
						</el-form-item>
						<el-form-item label="菜单类型" prop="Type">
							<el-select v-model="ruleForm.Type" class="ccfolw-list" placeholder="请选择菜单类型" @change="menuListTypeChange">
								<el-option v-for="(item,index) in typeSelectOption" :label="item.label" :value="item.value" :key="index"></el-option>
							</el-select>
							<el-select  v-if="isTypeFlow" class="search-listtype" v-model="ruleForm.FlowNo" filterable placeholder="搜索流程名称" @change="menuListFlowNoChange">
								<el-option v-for="(item,index) in flowSelectOption" :label="item.name" :value="item.no" :key="index"></el-option>
							</el-select>
							<el-input style="width: 350px;"  v-if="ruleForm.Type == 3 || ruleForm.Type == '第三方链接'" v-model="ruleForm.url" placeholder=""></el-input>
						</el-form-item>
            <el-form-item label="模块序号">
							<el-input v-model="ruleForm.sort" placeholder="输入模块序号"></el-input>
						</el-form-item>
						<!-- <el-form-item label="流程/表单编号" prop="FlowNo">
						</el-form-item>
						<el-form-item label="url" prop="url">
							<el-input  v-if="ruleForm.Type == 3 || ruleForm.Type == '第三方链接'" v-model="ruleForm.url" placeholder=""></el-input>
						</el-form-item> -->
					</el-form>
				</div>
			</div>
			<div slot="buttonslot" class="css-common-zdialogbtnctn" >
				<zbutton-function
						:init_text="'取消'"
						:init_fontsize="14"
						:debugmode="true"
						:init_height="undefined"
						:init_width="'76px'"
						:init_bgcolor="'#fff'"
						:init_color="'#1890FF'"
						@onclick="resetForm()">
				</zbutton-function>
				<zbutton-function
					:init_text="'确定'"
					:init_fontsize="14"
					:debugmode="true"
					:init_height="undefined"
					:init_width="'76px'"
					@onclick="submitForm()">
				</zbutton-function>
			</div>
		</zdialog-function>
	</div>
</template>
<script>
export default {
	name: "ProjectFlows",
	data() {
		return {
			show: false,
			defaultProps: {
				children: "children",
				label: "text",
			},
			showFormDialog: false,  // 表单填写框显示
			treeData: [],
			isTypeFlow: false, // 是否选择的是流程
			addMenuListType: 0, // 0 新增一级，1 点击的iconlevel-1，新增的是二级，2 点击的iconlevel-2，新增的是二级
			rightShowClickText: '一级菜单', // 当前选中的菜单类型
			clickTreeListItem: {}, // 保存点击当前树结构的值
			editStateText: '新增', // 新增add 编辑edit
			ruleForm: {
				// OrganizeId: '', // ==必填==  项目/机构ID 为0则是站点级菜单模块
				// gm_Guid: '', // 为空则是新增,否则表示编辑
				id: "", // 模块ID
				pid: "", // 父级ID
				text: "", // ==必填== 模块/菜单名称
				levelclass: "", // 模块级别 iconlevel-1 或 iconlevel-2
				extendclass: "", // 扩展类别
				iconclass: "", // 模块图标
				listtype: "", // 模块类型例如：QualityManage
				Type: "", // 菜单类型 0 普通菜单 1 流程菜单 2  表单菜单
				url: "", // Url
				FlowNo: "", // 流程/表单编号
				expandstatus: "", // 折叠状态expanding或空
				showing: "", // 是否可见 1 可见 0 不可见
				authname: "", // 模块权限判定名称
				sort: '', // 模块序号
				route_path: "", // 路由地址
				route_name: "", // 路由名称
				route_componentpath: "", // 组件地址
				route_requiresAuth: "", // 是否需要权限判断 1 需要 或置空
			},
			rules: {
				text: [
          { required: true, message: '菜单名称', trigger: 'blur' },
				],
				Type: [
					{ required: true, message: '请选择菜单类型', trigger: 'change' }
				],
			},
			typeSelectOption: [
				// { label: '普通菜单', value: '0'},
				{ label: '流程菜单', value: '1'},
				// { label: '表单菜单', value: '2'},
				{ label: '第三方链接', value: '3'},
			],
			flowSelectOption: [], // 流程/表单数据

			defaultActive: '1'
		};
	},
	props: {
		info_companyname: {
			type: String,
			required:true
		}
	},
	mounted() {
		this.getMenuList();
	},
	methods: {
		getIconClass(icon){
			if(icon == null) return true
			let bool = true
			icon.indexOf('newface') == -1 ? bool = true : bool = false
			return bool
		},
		_stopPropagation(ev){      
			ev && ev.stopPropagation && ev.stopPropagation();     
    },
		onInput(){
			this.$forceUpdate();
		}, 
		// 处理接口获取的list，改为三级树结构
		listAdminModules(tabledata) {
			tabledata = tabledata.sort((x, y) => x.sort - y.sort);
			let menuList1 = [];
			let menuList3 = [];
			tabledata.map((item) => {
				if (item.levelclass == "iconlevel-1") {
					menuList1.push(item);
				}
			});
			tabledata.map((item) => {
				if (item.levelclass == "iconlevel-3") {
					menuList3.push(item);
				}
			});
			menuList1.forEach((ele) => {
				let indArr = [];
				tabledata.filter((tele) => {
					if (tele.pid == ele.id) {
						indArr.push(tele);
					}
				});
				ele.children = indArr;
				ele.children.forEach((eleChild) => {
					let childArr = [];
					menuList3.filter((teleChild) => {
						if (teleChild.pid == eleChild.id) {
							childArr.push(teleChild);
						}
					});
					eleChild.children = childArr;
				});
			});

			this.treeData = menuList1;
			this.show = true;
		},
		// 新增一级菜单
		addFirstTreeList(){
			this.rightShowClickText = '一级菜单'
			let _timestamp = this.convertDateToStr();
			this.clickTreeListItem = {
				id: 'id_'+_timestamp, // 模块ID  ====生成
				pid: "", // 父级ID
				text: "", // ==必填== 模块/菜单名称
				levelclass: "iconlevel-1", // 模块级别 iconlevel-1 或 iconlevel-2
				extendclass: "", // 扩展类别
				iconclass: "icon-interface-project-process", // 模块图标
				listtype: "", // 模块类型例如：QualityManage
				Type: "", // 菜单类型 0 普通菜单 1 流程菜单 2  表单菜单
				url: "", // Url
				FlowNo: "", // 流程/表单编号
				expandstatus: "", // 折叠状态expanding或空
				showing: "1", // 是否可见 1 可见 0 不可见
				authname: 'authname_' + _timestamp, // 模块权限判定名称
				sort: '', // 模块序号
				route_path: "", // 路由地址
				route_name: "", // 路由名称
				route_componentpath: "", // 组件地址
				route_requiresAuth: "1", // 是否需要权限判断 1 需要 或置空
			};
			this.addMenuListType = 0;
			// this.formDialogEdit('新增');
			this.editStateText = '新增';
			this.ruleForm = this.clickTreeListItem;
			this.showFormDialog = true;

		},
		// 获取当前机构下全部菜单列表
		getMenuList() {
			let _OrganizeId = this.$staticmethod._Get("_OrganizeId"); // 机构ID
			let _Token = this.$staticmethod.Get("Token");
			this.$axios
				.get(
					`${window.bim_config.webserverurl}/api/Global/ModuleMenu/GetModules?authname=all&OrganizeId=${_OrganizeId}&token=${_Token}`
				)
				.then((res) => {
					if (res.data.Ret == 1) {
						this.listAdminModules(res.data.Data);
					}else{
						console.log(res.data)
					}
				})
				.catch((err) => {});
		},
		// 点击编辑时候获取当前最新的数据，并赋值给form
		getChildrenMeneList(guid) {
			this.$axios
				.get(
					`${window.bim_config.webserverurl}/api/Global/ModuleMenu/GetModule?gm_guid=${guid}&token=${this.$staticmethod.Get("Token")}`
				)
				.then((res) => {
					if(res.data.Ret == 1){
						let _data = res.data.Data;
						this.ruleForm = _data;
						if(_data.Type == 0){
							this.isTypeFlow = false;
							// this.ruleForm.Type = '普通菜单'
						}else if(_data.Type == 1){
							this.ruleForm.Type = '流程菜单'
							this.isTypeFlow = true;
						}
						// else if(_data.Type == 2){
						// 	this.isTypeFlow = false;
						// 	this.ruleForm.Type = '表单菜单'
						// }
						else if(_data.Type == 3){
							this.isTypeFlow = false;
							this.ruleForm.Type = '第三方链接'
						}
						this.showFormDialog = true;
					}else{
						this.$message.error(res.data.Msg)
					}
				})
				.catch((err) => {});
		},
		// 判断当前层级是几级
		clickLevel(param){
			let type = 0
			if(param == 'iconlevel-1'){
				type = 1;
			}else if(param == 'iconlevel-1'){
				type = 2;
			}else{
				type = 0;
			}
			return type
		},
		// 点击编辑或者新增
		formDialogEdit(type,data){
			this.addMenuListType = this.clickLevel(data.levelclass);
			this.editStateText = type;
			if(this.editStateText == '编辑'){
				this.getChildrenMeneList(data.gm_guid);
			}else{
				let _timestamp = this.convertDateToStr();
				if(this.rightShowClickText == '一级菜单'){
					this.addFirstTreeList();
				}else{
					let _levelclass = '';
					if(data.levelclass == 'iconlevel-1'){
						this.addMenuListType = 1;	
						_levelclass = 'iconlevel-2';
					}else if(data.levelclass =='iconlevel-2'){
						_levelclass = 'iconlevel-3';
						this.addMenuListType = 2;
					}
					 
					this.ruleForm = {
						id: 'id_add' + _timestamp, // 模块ID
						pid: data.id, // 父级ID
						text: "", // ==必填== 模块/菜单名称
						levelclass: _levelclass, // 模块级别 iconlevel-1 或 iconlevel-2
						extendclass: "", // 扩展类别
						iconclass: "icon-interface-project-process", // 模块图标
						listtype: "", // 模块类型例如：QualityManage
						Type: "", // 菜单类型 0 普通菜单 1 流程菜单 2  表单菜单
						url: "", // Url
						FlowNo: "", // 流程/表单编号
						expandstatus: "", // 折叠状态expanding或空
						showing: "1", // 是否可见 1 可见 0 不可见
						authname: data.authname, // 模块权限判定名称
						sort: '', // 模块序号
						route_path: "", // 路由地址
						route_name: "", // 路由名称
						route_componentpath: "", // 组件地址
						route_requiresAuth: "1", // 是否需要权限判断 1 需要 或置空
					}
					this.showFormDialog = true;
				}
			}
		},
		// 删除当前选中菜单
		delMenuList(data){
			this.$confirm("确定删除当前菜单", {
        confirmButtonText:'确定',
        cancelButtonText:'取消',
        type:'warning'
      }).then(() => {
        this.$axios({
					method: "post",
					headers: {
						"Content-Type": "application/json",
					},
					url: `${
						window.bim_config.webserverurl
					}/api/Global/ModuleMenu/Del?token=${this.$staticmethod.Get("Token")}&gm_Guid=${data.gm_guid}`,
				})
				.then((res) => {
					if(res.data.Ret == 1){
						this.getMenuList();
						this.rightShowClickText = '';
						this.$message.success(res.data.Msg);
					}else{
						this.$message.error(res.data.Msg)
					}
				})
				.catch((x) => {
					console.warn(x);
				});
      }).catch((err) => {
        console.log(err)
      })
		},
		// 保存菜单
		saveMenuList() {
			let saveurl = `${
				window.bim_config.webserverurl
			}/api/Global/ModuleMenu/Save?token=${this.$staticmethod.Get("Token")}`;
		
			let addOrEdit = ''
			this.editStateText == '新增' ? addOrEdit = '' : addOrEdit = '编辑'
			if(this.ruleForm.Type == '普通菜单'){
				this.ruleForm.Type = 0
			}else if(this.ruleForm.Type == '流程菜单'){
				this.ruleForm.Type = 1;
			}
			// else if(this.ruleForm.Type == '表单菜单'){
			// 	this.ruleForm.Type = 2
			// }
			else if(this.ruleForm.Type == '第三方链接'){
				this.ruleForm.Type = 3
			}

			if(this.ruleForm.Type == 3 && this.ruleForm.url.length == 0){
				this.$message.warning('请填写第三方链接url')
				return;
			}
			let _data = {
				OrganizeId: this.$staticmethod._Get("_OrganizeId"), // ==必填==  项目/机构ID 为0则是站点级菜单模块
				gm_Guid: addOrEdit, // 为空则是新增,否则表示编辑
				...this.ruleForm
			}
			
			this.$axios({
				method: "post",
				headers: {
					"Content-Type": "application/json",
				},
				url: saveurl,
				data: JSON.stringify(_data),
			})
			.then((res) => {
				if(res.data.Ret == 1){
					// 成功后重置表单内容并隐藏
					this.getMenuList();
					this.resetForm();
					this.$message.success(res.data.Msg);
				}else{
					this.$message.error(res.data.Msg)
				}
			})
			.catch((x) => {
				console.warn(x);
			});
		},
		// 提交表单
		submitForm() {
			this.$refs['ruleForm'].validate((valid) => {
				if (valid) {
					this.saveMenuList();
				} else {
					return false;
				}
			});
		},
		// 重置表单
		resetForm() {
			// this.$refs[formName].resetFields();  
			this.ruleForm= {
				id: "", // 模块ID
				pid: "", // 父级ID
				text: "", // ==必填== 模块/菜单名称
				levelclass: "", // 模块级别 iconlevel-1 或 iconlevel-2
				extendclass: "", // 扩展类别
				iconclass: "", // 模块图标
				listtype: "", // 模块类型例如：QualityManage
				Type: '', // 菜单类型 0 普通菜单 1 流程菜单 2  表单菜单
				url: "", // Url
				FlowNo: "", // 流程/表单编号
				expandstatus: "", // 折叠状态expanding或空
				showing: "", // 是否可见 1 可见 0 不可见
				authname: "", // 模块权限判定名称
				sort: '', // 模块序号
				route_path: "", // 路由地址
				route_name: "", // 路由名称
				route_componentpath: "", // 组件地址
				route_requiresAuth: "", // 是否需要权限判断 1 需要 或置空
			}
			this.showFormDialog = false;
			this.isTypeFlow = false;
		},
		menuListTypeChange(value){
			if(value == '0'){
				// 普通菜单
				this.ruleForm.Type = '0'
				this.isTypeFlow = false;
				this.flowSelectOption = [];
			}else if(value == '1'){
				// 流程列表
				this.ruleForm.Type = '1'
				this.isTypeFlow = true;
				this.getFlowList('流程');
			}
			// else if(value == '2'){
			// 	// 表单列表
			// 	this.isTypeFlow = false;
			// 	this.getFlowList('表单');
			// }
			else if(value == '3'){
				this.isTypeFlow = false;
			}
			this.ruleForm.FlowNo = ''
		},
		menuListFlowNoChange(value){
			this.ruleForm.FlowNo = value;
		},
		// 获取流程菜单的数据
		getFlowList(type){
			let url = '';
			type == '流程' ?
				url = `${window.bim_config.CCFlowUrl}/api/v1/flow/queryflows?userNo=${this.$staticmethod.Get("Account")}&organizeId=${this.$staticmethod._Get("_OrganizeId")}&flowSort=`
			:
				url = `${window.bim_config.CCFlowUrl}/api/v1/flow/queryfrm?userNo=${this.$staticmethod.Get("Account")}&organizeId=${this.$staticmethod._Get("_OrganizeId")}&frmType=`
			this.$axios
				.get(url)
				.then((res) => {
					if(res.status == 200){
						this.flowSelectOption = res.data;
					}else{
						this.flowSelectOption = []
					}
				})
				.catch((err) => {});
		},
		convertDateToStr() {
			let dt = new Date();
      var year = dt.getFullYear();
      var month = dt.getMonth() + 1;
      var date = dt.getDate();
      var hour = dt.getHours();
      var minute = dt.getMinutes();
      var second = dt.getSeconds();
      var ms = dt.getMilliseconds();
      return (
        year + "" + month + "" + date + "" + hour + "" + minute + "" + second + "" + ms
      );
    },
		handelSelectMenuList(key){
      this.defaultActive = key;
    },
		openList(index){
			// 含有-是子集，open和close返回的值都是index，都调用openList这个方法，
			if(index.indexOf('-') > 0){
				let index_ = index.split('-')[0];
				let index2_ = index.split('-')[1];
				let data = this.treeData[index_].children[index2_]
				this.rightShowClickText = data.text;
				this.clickTreeListItem = data;
			}else{
				let _data = this.treeData[index]
				this.rightShowClickText = _data.text;
				this.clickTreeListItem = _data;
			}
		},
		handleSelectClick(data,str){
			// console.log(str,'==str')
			this.rightShowClickText = data.text;
			this.clickTreeListItem = data;
		},
		// 新增显示判断
		addMenuJudgment(){
			let addAuth = false;
			// 当前逻辑不能新增的菜单：三级、任务中心、模型菜单、
			if(this.clickTreeListItem.levelclass != 'iconlevel-3' && this.clickTreeListItem.id !== 'WorkFlowTask' && this.clickTreeListItem.pid !== 'WorkFlowTask' && this.clickTreeListItem.id !=='modelPhase'){
				addAuth = true;
			}
			return addAuth;
		}
	},
};
</script>
<style lang="scss" scoped >
.project-menu-list {
	margin: 16px auto;
	position: relative;
	height: calc(100% - 10px);
	overflow-y: auto;
	box-shadow: 0px 13px 24px -17px rgba(11, 41, 62, 0.8);
	border-radius: 4px;
	font-family: PingFangSC-Medium; 
	color: #606266;
	text-align: left;
	width: 1200px;
	display: flex;
	flex-direction: row;
	// background-color: #fff;
}
.companu-name{
	font-size: 16px;
	font-family: PingFangSC-Medium, PingFang SC;
	font-weight: 500;
	color: #FFFFFF;
	text-align: center;
}
.name-list{
	height: 52px;
	line-height: 52px;
	border: 1px solid rgba(255,255,255,0.2);
}
.name-click{
	cursor: pointer;
	width: 240px;
	margin: 0 auto;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
	height: 60px;
	line-height: 60px;
}
.list-tree-left{
	// height: calc( 100% - 40px);
	// padding: 20px;
	background: #162D59;
	overflow: hidden;
	display: flex;
	flex-direction: column;
}
.left-tree-list{
	flex: 1;
	overflow-y: scroll;
}
.list-tree-right{
	background: #fff;
	border-left: 1px solid #ccc;
	flex: 1;
	.right-title{
		width: calc(100% - 16px);
		margin-left: 16px;
		height: 50px;
		line-height: 50px;
		font-size: 16px;
		border-bottom: 1px solid #ccc;
		.c007AFF{
			color: #007AFF;
		}
	}
	// .css-right-btn{
	// 	margin-left: 16px;
	// }
	.edit-btn{
		display: flex;
		flex-direction: row;
		justify-content: center;
		align-items: center;
		width: calc(100% - 32px);
		height: 120px;
		text-align: center;
		border: 1px solid transparent;
		cursor: pointer;
		margin-top: 20px;
		margin-left: 16px;
		background: rgba(40,153,255,0.05);
		border-radius: 8px;
		.btn-icon i{
			margin-right: 40px;
			display: inline-block;
			width: 40px;
			height: 40px;
			background-size: 100% 100%;
			background-repeat: no-repeat;
		}
		.icon-f-edit {
			background-image: url('../../assets/images/f-edit.png')
		}
		.icon-f-add {
			background-image: url('../../assets/images/f-add.png')
		}
		.icon-f-del {
			background-image: url('../../assets/images/f-del.png')
		}
		.btn-text{
			line-height: 120px;
			color: #007AFF;
			font-size: 20px;
			font-family: PingFangSC-Semibold, PingFang SC;
			font-weight: 600;
		}
	}
	.edit-btn:hover{
		border: 1px solid #007AFF;
	}
}
.node-list {
	width: 100%;
	display: flex;
}
 
.list-btn {
	display: none;
	margin-left: 30px;
}
.node-list:hover .list-btn{
	display: block;
}
.list-btn span {
	margin-left: 20px;
}
.list-btn span:hover {
	color: #1890ff;
	cursor: pointer;
}
.project-menu-list /deep/ .el-tree {
	background: transparent;
	width: 400px;
}
.form-list{
	margin-top: 20px;
	// height: 500px;
	overflow-y: auto;
}
.form-list /deep/ .el-input__inner{
	border-width: 1px;
	border-radius: 4px;
}
.form-list /deep/ .el-input{
	width:97%;	
}
.form-list /deep/ .el-select{
	border:none;
}
.form-list /deep/ .ccfolw-list{
	width: 160px;
}
.form-list /deep/ .ccfolw-list .el-select__caret {
	width: 16px;
	appearance:none;
	-moz-appearance:none;
	-webkit-appearance:none;
	padding-right: 0;
	background: url("../../assets/images/select-arrow.png") no-repeat scroll right center transparent;
	background-size: 16px 16px;
	-webkit-transform: rotate(0)!important;
	transform: rotate(0)!important;
}
/*将小箭头的样式去去掉*/  
.form-list /deep/ .el-icon-arrow-up:before {
	content: '';
}
.form-list /deep/ .el-form-item.is-success .el-input__inner, 
.form-list /deep/ .el-form-item.is-success .el-input__inner:focus, 
.form-list /deep/ .el-form-item.is-success .el-textarea__inner, 
.form-list /deep/ .el-form-item.is-success .el-textarea__inner:focus{
	border: 1px solid #E8E8E8;
}
.form-list /deep/ .ccfolw-list .el-select .el-input .el-select__caret.is-reverse {
	-webkit-transform: rotate(180deg)!important;
	transform: rotate(180deg)!important;
}
.form-list /deep/ .search-listtype{
	width: 360px;
}
.form-list /deep/ .search-listtype .el-select__caret{
	width: 20px;
	appearance:none;
	-moz-appearance:none;
	-webkit-appearance:none;
	padding-right: 0;
	background: url("../../assets/images/metting-search.png") no-repeat scroll right center transparent;
	background-size: 20px 20px;
	-webkit-transform: rotate(0)!important;
	transform: rotate(0)!important;
}
.form-list /deep/ .parent-readonly input[readonly]{ background-color: #E8E8E8 !important; }
.btn-level{
	text-align: right;
} 


.css-menu-list{
	width: 252px;
}
.left-menu /deep/ .el-menu-item:focus, 
.left-menu /deep/ .el-menu-item:hover,
.left-menu /deep/ .el-submenu__title:hover{
  background: rgba(255,255,255,0.2);
  color: rgba(255, 255, 255, 1);
  border-radius: 8px;
  margin: 0 8px;
}

.left-menu /deep/ .el-menu--horizontal>.el-menu-item:not(.is-disabled):focus,
.left-menu /deep/ .el-menu--horizontal>.el-menu-item:not(.is-disabled):hover, 
.left-menu /deep/ .el-menu--horizontal>.el-submenu .el-submenu__title:hover{
  background-color: #0d1640;
}
.left-menu /deep/ .el-menu-item.is-active{
  color: #007aff;
  background-color: #fff;
  border-radius: 8px;
  margin: 0 8px;
}
.left-menu /deep/ .el-menu{
  background: transparent;
}
.left-menu /deep/ .el-menu-item,
.left-menu /deep/ .el-submenu__title{
  margin: 0 8px;
  color: #ddd;
  padding-left: 0 !important;
}
.left-menu /deep/ .el-submenu .el-menu-item{
  padding: 0;
  min-width: 184px;
  text-align: left;
}
.left-menu /deep/ .el-menu-item.is-active {
	.icon-img{
		&:before {
			color: #007AFF;
    }
	}
  .icon-interface-home {
    background-image: url('../../assets/images/menu-icon/index-active.png')
  }
  .icon-interface-model_list {
    background-image: url('../../assets/images/menu-icon/model-active.png')
  }
  .icon-interface-document {
    background-image: url('../../assets/images/menu-icon/document-active.png')
  }
  .icon-interface-720 {
    background-image: url('../../assets/images/menu-icon/interface-active.png')
  }
  .icon-interface-associated-component {
    background-image: url('../../assets/images/menu-icon/associated-active.png')
  }
  .icon-interface-model-statistics {
    background-image: url('../../assets/images/menu-icon/statistics-active.png')
  }
  .icon-interface-component_cost {
    background-image: url('../../assets/images/menu-icon/cost-active.png')
  }
  .icon-interface-project-process {
    background-image: url('../../assets/images/menu-icon/project-progress-active.png')
  }
  .icon-interface-meeting {
    background-image: url('../../assets/images/menu-icon/meeting-active.png')
  }
  .icon-interface-set_se {
    background-image: url('../../assets/images/menu-icon/interface-set-active.png')
  }
  .icon-interface-quality {
    background-image: url('../../assets/images/menu-icon/quality-active.png')
  }
}
.left-menu /deep/ .el-menu--inline{
  background: #0d1640;
}
.text-padding64{
	padding-left: 64px;
}
.text-padding76{
	padding-left: 76px;
}
.left-menu {
  .icon-img{
		margin-left: 20px;
		margin-right: 9px;
    display: inline-block;
    width: 20px;
    height: 20px;
		font-size: 20px;
    background-size: 100% 100%;
    background-position: 50%;
    background-repeat: no-repeat;
  }
  .icon-img-before{
		&:before {
      display: none;
    }
	}
  .icon-interface-home {
    background-image: url('../../assets/images/menu-icon/index.png')
  }
  .icon-interface-model_list {
    background-image: url('../../assets/images/menu-icon/model.png')
  }
  .icon-interface-document {
    background-image: url('../../assets/images/menu-icon/document.png')
  }
  .icon-interface-720 {
    background-image: url('../../assets/images/menu-icon/interface.png')
  }
  .icon-interface-associated-component {
    background-image: url('../../assets/images/menu-icon/associated.png')
  }
  .icon-interface-model-statistics {
    background-image: url('../../assets/images/menu-icon/statistics.png')
  }
  .icon-interface-component_cost {
    background-image: url('../../assets/images/menu-icon/cost.png')
  }
  .icon-interface-project-process {
    background-image: url('../../assets/images/menu-icon/project-progress.png')
  }
  .icon-interface-meeting {
    background-image: url('../../assets/images/menu-icon/meeting.png')
  }
  .icon-interface-set_se {
    background-image: url('../../assets/images/menu-icon/interface-set.png')
  }
  .icon-interface-quality {
    background-image: url('../../assets/images/menu-icon/quality.png')
  }
}

</style>