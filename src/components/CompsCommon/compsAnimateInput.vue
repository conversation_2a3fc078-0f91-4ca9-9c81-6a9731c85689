<template>
  <div class="container">

    <!--input输入框-->
    <div class="input-content" v-if="!needAt && !select">
      <input class="input-field"
             autofocus
             name="password"
             autocomplete="current-password"
             :type="type"
             :id="randomID"
             :value="value"
             :style="{fontSize:fontSize,backgroundColor:backgroundColor}"
             :placeholder="placeholder"
             @blur="$emit('itemblur')"
             @keyup="$emit('itemkeyup')"
             @input="handleInput">
      <label class="label-field" :for="randomID">{{ label }}{{ labelFootnote }}</label>
      <div class="bottom-line"></div>
    </div>

    <!--div输入框 @功能-->
    <div class="input-content" v-if="needAt">
      <div
      :id="'_div_animateinput_id'"
      :class="[{divAfter : divHasInput,'div-placeholder' : divhasFocus},'div-field css-prel']"
           ref="inputDiv"
           @focus="divInputOnfocus"
           @blur="divInputOnBlur"
           @keyup.esc="togglePersonnelList=false"
           @keydown.up="directionalkeySelect('up',$event)"
           @keydown.down="directionalkeySelect('down',$event)"
           @keydown.enter="directionalkeyEnter($event)"
           contenteditable="plaintext-only"
           @mousedown="_stopPropagation($event)"
           :value="value"
           :placeholder="placeholder"
           :style="{fontSize:fontSize,minHeight: height,backgroundColor:backgroundColor}"
           @input="handleInput"></div>
      <!--<label @click="divInputOnfocus" class="label-field" >{{ label }}{{ labelFootnote }}</label>-->
      <div class="bottom-line"></div>

      <div v-if="togglePersonnelList"
           v-clickOutClose="closePersonnelList"
           ref="personnelList"
           class="personnel-list"
           :style="{ top: `${caretCoordinate.y}px`,left: `${caretCoordinate.x}px` }">
        <!--<div class=""><el-input placeholder="请输入内容" prefix-icon="el-icon-search" v-model="personSearch">-->
        <!--</el-input></div>-->
        <div :class="[{active : selectActive==index},'list-item']" v-for="(item,index) in searchDataList" :key="item.UserId">
          <button @click="selectCurrentPerson(item,index)">
            <span class="name-icon">{{ item.RealName | setAbbreviation}}</span>
            <p class="name">{{ item.RealName }}</p>
            <p class="email" :title="item.bo_FullName">{{ item.bo_FullName || '—' }}</p>
          </button>
        </div>
      </div>
    </div>

    <!--select-->
    <div class="input-content" v-if="select" style="z-index: 999" v-click-out-close="toggleSelectListState">
      <input class="input-field select-field"
             :type="type" :id="randomID"
             :value="value"
             :style="{fontSize:fontSize,backgroundColor:backgroundColor}"
             readonly
             @blur="divInputOnBlur"
             @click="toggleSelectList=true"
             :placeholder="placeholder">
      <label class="label-field" @click="toggleSelectList=true" :for="randomID">{{ label }}{{ labelFootnote }}</label>
      <div class="bottom-line"></div>
      <span class="select-field-icon icon-arrow-down"></span>

      <transition name="select-fade">
        <div class="select-list" v-show="toggleSelectList" :style="{height:displayNum*40+'px'}">
          <ul>
            <li v-for="(item,index) in dataList" :key="index + Math.random()" @click="getSelectListItem(item)">
              {{ item[selectShow] }}
            </li>
          </ul>
        </div>
      </transition>
    </div>

  </div>
</template>

<script>
    export default {
      name: "compsAnimateInput",
      data() {
        return {
          value: '',
          randomID: '',//随机id
          divhasFocus: false,//div获取焦点
          divHasInput: true,//div输入框是否输入了文字
          caretPosition: 0,//光标位置
          caretCoordinate: {},//光标坐标
          divContent: '',//div编辑框的内容(带有html标签)
          inputContent: '',//编辑框的内容(纯文本)
          arrSubscript: [],//div编辑框中已存在人员的数据集合
          togglePersonnelList: false,//是否展示人员列表
          isLoading: false,
          textValueArr: [],//数组化后的纯文本字符串
          htmlValueArr: [],//数组化后的带标签的字符串
          atPersonalArr: [],//输入框中已@的成员的span标签集合
          personSearch: '',//人员搜索
          toggleSelectList: false,//是否展示下拉列表
          atPosition: -1,//@的位置
          selectActive: 0,//人员列表当前选中的索引
        }
      },

      props: {

        defaultValue: '',//输入框的默认值

        label: {//标签名字
          type: String,
          default: '请输入'
        },

        labelFootnote: {//标签的补充说明 如：标题（选填）
          type: String,
          default: '',
        },

        placeholder: {//input提示文字
          type: String,
          default: ' '
        },

        isRequired: {//是否是必填项
          type: Boolean
        },

        needAt: {//是否需要@功能 默认为false不需要
          type: Boolean,
          default: false
        },

        dataList: {//@人员列表数据
          type: Array,
        },

        height: {
          type: [String,Number]
        },

        type: {
          type: String,
          default: 'text'
        },

        fontSize: {//输入框字体大小
          type: String,
        },

        backgroundColor: {//背景颜色
          type: String,
          default: 'inherit'
        },

        select: {//是否需要下拉框
          type: Boolean,
          default: false
        },

        selectShow: {//用于展示option内容的字段名
          type: String,
        },

        displayNum: {//配置下拉列表展示几个
          type: [String,Number],
          default: 5
        },

        setFocus: {//设置@功能文本域是否获取焦点
          type: Boolean,
          default: false,
        },
        // borderColor:{
        //   type:String,
        //   default:false,
        // }
      },

      created() {
        this.setRandomClass();

        window.ai = this;

        if (this.setFocus) {
          this.$nextTick(()=>{
            this.divInputOnfocus();
          });
        }
        if(this.defaultValue!=''){
          this.value=this.defaultValue;
        }
      },

      methods: {
        // 清空
        clearText() {
          var _this = this;
          document.getElementById("_div_animateinput_id").innerHTML = '';
          document.getElementById("_div_animateinput_id").focus();
          _this.divHasInput = true;
          _this.divhasFocus = true;
          _this.value = '';
          _this.atPersonalArr = [];
          _this.arrSubscript = [];
        },

        //回车键确认选择人员
        directionalkeyEnter(e) {
          //如果人员列表没有打开 则返回
          if (!this.togglePersonnelList) return false;
          //如果人员列表已打开，则阻止默认键盘事件
          e.preventDefault();
          //获取当前选中的人员的信息
          let item = this.searchDataList[this.selectActive];
          this.selectCurrentPerson(item,this.selectActive);
        },

        //按键选择人员
        directionalkeySelect(operation,e) {
          //如果人员列表没有打开 则返回
          if (!this.togglePersonnelList) return false;
          //如果人员列表已打开，则阻止默认键盘事件
          e.preventDefault();
          let length = this.searchDataList.length;

          //方向键向上
          if (operation == 'up') {
            this.selectActive == 0 ? this.selectActive = length -1 : this.selectActive--;
            this.$refs.personnelList.scrollTop = this.selectActive * 51;
          } else {
            this.selectActive == length -1 ? this.selectActive = 0 : this.selectActive++;
            if (this.selectActive == length -1 || this.selectActive%4 == 0) {
              this.$refs.personnelList.scrollTop = this.selectActive * 51;
            }
          }
        },

        _stopPropagation(ev) {
          ev.stopPropagation();
        },

        //获取下拉选择框的内容
        getSelectListItem(item) {
          this.value = item[this.selectShow];
          this.returnInputValue('','',item);
        },

        //切换列表状态
        toggleSelectListState() {
          // this.toggleSelectList = !this.toggleSelectList;
          this.toggleSelectList = false;
        },

        closePersonnelList(){
          // console.log(this.togglePersonnelList)
          this.togglePersonnelList = false
        },

        //点击选择人员下拉列表某项
        selectCurrentPerson(item,index) {
          // console.log(item)
          this.$refs.inputDiv.focus();
          //插入名称前移除输入的@
          let deleteLenth = this.caretPosition - this.atPosition;
          for(let i = 0; i <= deleteLenth; i++) {
            document.execCommand('Delete');
          }
          let nameSpan = `&nbsp;<span data-id="${item.UserId}" contenteditable="false">@${item.RealName}</span>&nbsp;`;
          //在光标位置插入选择的名字
          this.insertHtmlAtCaret(nameSpan);
          this.getHasSelectPersonMsg();
          this.divContent = this.$refs.inputDiv.innerHTML;
          this.inputContent = this.$refs.inputDiv.innerText;
          //移除placeholder
          this.divHasInput = false;
          //关闭列表
          this.togglePersonnelList = false;
          //将@位置重置
          this.atPosition = -1;
          this.returnInputValue(this.inputContent,this.divContent,this.arrSubscript);
        },

        //div输入框失去焦点时
        divInputOnBlur(e) {
          let value = e.target.innerText;
          this.divhasFocus = false;
          setTimeout(_=>{
            //失去焦点时 让人员列表关闭
            this.togglePersonnelList = false;
          },300);
          // if (value.length!= 0 && value!='<br>') {
          //   this.divhasFocus = false;
          // } else {
          //   this.divhasFocus = true;
          // }

          return value;
        },

        //div输入框获取焦点时
        divInputOnfocus() {
          this.divhasFocus = true;
          this.$refs.inputDiv.focus();
        },

        handleInput(event) {
          let value = '';

          //正常输入框
          if (!this.needAt) {
            this.inputContent = event.target.value;
            // this.returnGeneralInputValue(this.inputContent);
            this.returnInputValue(this.inputContent)
          } else {

            //带有@功能的输入框
            let sel = window.getSelection();
            // console.log(sel);
            let nodeVal = sel.focusNode.nodeValue;
            //获取输入框的纯文本
            this.inputContent = event.target.innerText;

            //获取输入框的内容
            this.divContent = event.target.innerHTML;
            //获取输入框中@的成员的标签集合
            this.atPersonalArr = event.target.getElementsByTagName("span");
            let childNodeDiv = event.target.getElementsByTagName("div");
            let lastNum = childNodeDiv.length;

            this.getHasSelectPersonMsg();

            if (this.divContent.length!= 0 && this.divContent!='<br>') {
              //如果输入框不为空 移除placeholder
              this.divHasInput = false;
            } else {
              this.divHasInput = true;
            }

            //获取光标位置
            this.caretPosition = sel.focusOffset;

            //获取光标坐标
            this.caretCoordinate = this.getSelectionCoords();
            this.caretCoordinate.y += 20;
            this.caretCoordinate.x += 10;

            // console.log(nodeVal,this.caretPosition);
            //拆分已输入的字符串
            //如果最后输入的字符为@ 则打开人员列表
            if (nodeVal != null && nodeVal[this.caretPosition - 1] == '@') {
              if (this.dataList.length == 0) {
                this.$message({
                  type: 'error',
                  message: '该项目下暂无参与人可供通知。'
                })
              }
              //当检测到出现@时 保存@的位置
              this.atPosition = this.caretPosition;
              this.getPersonList();
            }
            //如果光标处没有了文字 则也关闭人员列表
            else if (nodeVal == null || !this.caretPosition || nodeVal[this.atPosition - 1] != '@') {
              this.atPosition = -1;
              this.togglePersonnelList = false;
            }

            if (nodeVal != null) {
              this.personSearch = nodeVal.slice(this.atPosition,this.caretPosition);
            }

            //this.atPosition > 0 证明@的功能在奏效 依然在检测输出 当选中一个人时 该值变为-1
            //this.searchDataList.length > 0 说明列表中可以查到此人
            if (this.atPosition > 0 && this.searchDataList.length > 0) {
              this.togglePersonnelList = true;
            } else {
              this.togglePersonnelList = false;
            }

            this.returnInputValue(this.inputContent,this.divContent,this.arrSubscript);
          }

        },

        //触发v-model 返回输入的内容
        //参数：value（纯文本），htmlValue（包含有span标签的文本），subscript(已@人员的信息)
        returnInputValue(value,htmlValue,subscript) {

          let msg = {
            value: value,
            nodeValue: htmlValue,
            subscript: subscript,
          };

          if (htmlValue != '' && htmlValue != undefined && htmlValue !=null) {
            let text = '';
            text = this.removeElementSpan(htmlValue);
            msg.text = text;
          }

          this.$emit('input',msg);
        },

        returnGeneralInputValue(value) {
          this.$emit('input',value);
        },

        removeElementSpan(htmlValue){
          // if (this.atPersonalArr.length == 0) return;

          let text = '';
          let div = document.createElement("div"); //创建一个div
          div.style.display = "none";
          div.setAttribute("id", "storageText");
          div.innerHTML = htmlValue;
          document.body.appendChild(div);

          let element = document.getElementById('storageText');
          let spanArr = element.getElementsByTagName("span");

          for (let i in spanArr) {
            if (spanArr.length > 0) {
              let parentElement = spanArr[0].parentNode;
              if(parentElement){
                parentElement.removeChild(spanArr[0]);
              }
            }
          }

          text = element.innerText;
          document.body.removeChild(element);

          return text;
        },

        //获取人员列表
        getPersonList() {
          this.togglePersonnelList = true;
        },

        //随机分配ID
        setRandomClass() {
          this.randomID = Date.parse(new Date()).toString() + Math.random();
        },

        //获取可编辑div的光标位置
        getDivPosition(element) {
          let caretOffset = 0;
          let doc = element.ownerDocument || element.document;
          let win = doc.defaultView || doc.parentWindow;
          let sel;
          if (typeof win.getSelection != "undefined") {
            sel = win.getSelection();
            if (sel.rangeCount > 0) {//选中的区域
              let range = win.getSelection().getRangeAt(0);
              let preCaretRange = range.cloneRange();//克隆一个选中区域
              preCaretRange.selectNodeContents(element);//设置选中区域的节点内容为当前节点
              preCaretRange.setEnd(range.endContainer, range.endOffset);  //重置选中区域的结束位置
              caretOffset = preCaretRange.toString().length;
            }
          } else if ((sel = doc.selection) && sel.type != "Control") {
            let textRange = sel.createRange();
            let preCaretTextRange = doc.body.createTextRange();
            preCaretTextRange.moveToElementText(element);
            preCaretTextRange.setEndPoint("EndToEnd", textRange);
            caretOffset = preCaretTextRange.text.length;
          }
          return caretOffset;
        },

        //获取当前输入框中已经@的人员的信息
        getHasSelectPersonMsg() {
          this.arrSubscript = [];
          if (this.atPersonalArr.length > 0) {

            for(let i=0;i<this.atPersonalArr.length;i++) {
              let currentItem = this.dataList.filter(item=>{
                return item.UserId == this.atPersonalArr[i].dataset.id
              });
              this.arrSubscript.push(currentItem[0]);
            }
          }
        },

        //获取可编辑div的光标坐标
        getSelectionCoords(win) {
          win = win || window;
          let doc = win.document;
          let sel = doc.selection, range, rects, rect;
          let x = 0, y = 0;
          if (sel) {
            if (sel.type != "Control") {
              range = sel.createRange();
              range.collapse(true);
              x = range.boundingLeft;
              y = range.boundingTop;
            }
          } else if (win.getSelection) {
            sel = win.getSelection();
            if (sel.rangeCount) {
              range = sel.getRangeAt(0).cloneRange();
              if (range.getClientRects) {
                range.collapse(true);
                rects = range.getClientRects();
                if (rects.length > 0) {
                  rect = rects[0];
                }
                // 光标在行首时，rect为undefined
                if(rect){
                  x = rect.left;
                  y = rect.top;
                }
              }

              if ((x == 0 && y == 0) || rect === undefined) {
                let span = doc.createElement("span");
                if (span.getClientRects) {

                  span.appendChild( doc.createTextNode("\u200b") );
                  range.insertNode(span);
                  rect = span.getClientRects()[0];
                  x = rect.left;
                  y = rect.top;
                  let spanParent = span.parentNode;
                  spanParent.removeChild(span);

                  spanParent.normalize();
                }
              }
            }
          }
          return { x: x, y: y };
        },

        insertHtmlAtCaret(html) {
          let sel, range;
          if (window.getSelection) {
            //创建getSelection对象
            sel = window.getSelection();

            if (sel.getRangeAt && sel.rangeCount) {
              range = sel.getRangeAt(0);
              range.deleteContents();

              let el = document.createElement("div");
              el.innerHTML = html;
              let frag = document.createDocumentFragment(), node, lastNode;
              while ((node = el.firstChild)) {
                lastNode = frag.appendChild(node);
              }

              range.insertNode(frag);
              if (lastNode) {
                range = range.cloneRange();
                range.setStartAfter(lastNode);
                range.collapse(true);
                sel.removeAllRanges();
                sel.addRange(range);
              }
            }
          } else if (document.selection && document.selection.type != "Control") {
            document.selection.createRange().pasteHTML(html);
          }
        },

        clearContent() {
          this.$refs.inputDiv.innerHTML = '';
        },
      },

      computed:{
        searchDataList () {
          let storage = [];
          for(var i = 0; i < this.dataList.length; i++) {
            if (this.dataList[i].RealName.search(this.personSearch) != -1 && this.arrSubscript.findIndex(x => x.UserId == this.dataList[i].UserId) == -1) {
               storage.push(this.dataList[i]);
            }
          }
          return storage;
        }
      },

      watch: {
        togglePersonnelList (n,o) {
          if (!n) {
            this.selectActive = 0;
          }
        },
      }
    }
</script>
<style>
  .input-content .div-field span {
    font-weight: 400;
    color: rgba(24,144,255,1);
  }
</style>
<style scoped>
  .input-content {
    position: relative;
    display: flex;
    flex-flow: column-reverse;
    margin-bottom: 1em;
  }

  .input-content > * {
    transition: all 0.5s;
  }

  .input-content .bottom-line {
    position: absolute;
    left: 0;
    bottom: 0;
    width: 0;
    height: 1px;
  }

  .input-content .personnel-list {
    position: fixed;
    z-index: 20;
    width: 180px;
    height: 258px;
    padding: 4px 0;
    box-sizing: border-box;
    background-color: #FFFFFF;
    border-radius: 2px;
    overflow: auto;
    box-shadow: 0 1px 3px 0 rgba(0,21,41,0.2);
  }

  .input-content .personnel-list .list-item {
    cursor: pointer;
    position: relative;
    /*padding: 8px 5px 8px 37px;*/
  }

  .input-content .personnel-list .list-item > button {
    width: 100%;
    cursor: pointer;
    border: none;
    outline: none;
    background: none;
    margin: 0;
    font-size: 14px;
    padding: 8px 5px 8px 37px;
  }

  .input-content .personnel-list .list-item.active,
  .input-content .personnel-list .list-item:hover {
    background-color: rgba(0,0,0,0.04);
  }

  .personnel-list .list-item .name-icon {
    position: absolute;
    top: 13px;
    left: 7px;
    width: 24px;
    height: 24px;
    color: #FFFFFF;
    font-size: 12px;
    font-weight: 500;
    overflow: hidden;
    line-height: 24px;
    border-radius: 2px;
    text-align: center;
    background-color: rgba(32,32,32,1);
  }

  .personnel-list .list-item p {
    margin: 0;
    text-align: left;
  }

  .personnel-list .list-item p.name {
    font-weight: 500;
    color: rgba(0,0,0,1);
  }

  .personnel-list .list-item p.email {
    font-size: 12px;
    font-weight: 400;
    color: rgba(0,0,0,0.45);
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
  }

  .input-content .div-field,
  .input-content .label-field,
  .input-content .input-field {
    touch-action: manipulation;
  }

  .input-content .div-field,
  .input-content .input-field {
    min-height: 30px;
    font-size: 14px;
    border: 0;
    font-family: inherit;
    -webkit-appearance: none;
    border-radius: 0;
    padding: 0;
    caret-color: #1890FF;
    cursor: text;
    font-weight:400;
    color:rgba(0,0,0,0.85);
    border-bottom: 1px solid rgba(0,0,0,0.45);
  }

  .input-content .div-field {
    box-sizing: border-box;
    padding: 5px 0;
  }

  .input-content .div-field:focus,
  .input-content .input-field:focus {
    outline: 0;
  }

  .input-content .label-field {
    height: 20px;
    font-size: 14px;
    cursor: text;
    font-weight: 400;
    color: rgba(0,0,0,0.45);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    transform-origin: left bottom;
    transform: translate(0, 26px);
  }

  ::-webkit-input-placeholder {
    opacity: 0;
    transition: inherit;
  }

  .input-content .input-field:focus::-webkit-input-placeholder {
    opacity: 1;
  }

  .input-content .input-field:not(:placeholder-shown) + .label-field,
  .input-content .input-field:focus + .label-field {
    transform: translate(0, 0);
    font-size: 12px;
    color: rgba(0,0,0,0.65);
  }

  .input-content .input-field:focus  + .label-field + .bottom-line,
  .div-placeholder + .label-field + .bottom-line,
  .div-placeholder + .bottom-line {
    width: 100%;
    height: 2px;
    background-color: #1890FF;
  }

  .div-field.divAfter::after  {
    content: attr(placeholder);
    position: absolute;
    top: 5px;
    color: rgba(0,0,0,0.55);
    opacity: 1;
  }

  .div-placeholder + .label-field {
    transform: translate(0, 0);
    cursor: pointer;
    font-size: 12px;
    color: rgba(0,0,0,0.65);
  }

  .div-placeholder.div-field::after  {
    opacity: 1;
  }

  .input-content .select-field {
    cursor: pointer;
  }

  .input-content .select-field + .label-field {
    cursor: pointer;
  }

  .input-content .select-list {
    min-height: 40px;
    width: 100%;
    position: absolute;
    top: 20px;
    left: 0;
    padding: 4px 0;
    background-color: #FFFFFF;
    box-shadow: 0 1px 3px 0 rgba(0,21,41,0.12);
    border-radius: 2px;
    overflow: auto;
  }

  .input-content .select-list ul {
    padding: 0;
    margin: 0;
  }

  .input-content .select-list ul li {
    cursor: pointer;
    list-style: none;
    height: 40px;
    line-height: 40px;
    padding: 0 15px;
    box-sizing: border-box;
  }

  .input-content .select-list ul li:hover {
    background-color: rgba(240,242,245,1);
  }

  .select-fade-enter-active, .select-fade-leave-active {
    transition: opacity 0.8s;
  }
  .select-fade-enter, .select-fade-leave-to {
    opacity: 0;
  }

  .input-content .select-field-icon {
    position: absolute;
    right: 0;
    bottom: 7px;
  }
</style>
