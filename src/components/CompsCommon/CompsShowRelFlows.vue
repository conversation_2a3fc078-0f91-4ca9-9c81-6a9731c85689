<template>
    <div class="_comps-flows-all">

        <!-- 看流程图的对话框 -->
        <zdialog-function
        v-if="status_showskeleton"
        :init_title="'流程图'"
        :init_zindex="1003"
        :init_width="840"
        :init_innerWidth="840"
        init_closebtniconfontclass="icon-suggested-close"
        :init_usecustomtitlearea="true"
        @onclose="func_closeskeleton"
        >
            <div slot="customtitlearea" class="css-fc" >
                        
                <!-- ml24 w24 h24 -->
                <div class="css-dialogtitle-icon icon-dl-viewflows"></div>
                <!-- ml24 w24 h24 -->

                <!-- ml4 -->
                <div class="css-dialogtitle-title" >流程图</div>
                <!-- ml4 -->

            </div>

            <!-- 高度为444的iframe显示 -->
            <div slot="mainslot" style="overflow-y:auto;height:444px;"
            @mousedown="_stopPropagation($event)"
            @click="_stopPropagation($event)"
            >
                <iframe
                :src="m_showskeletonsrc"
                class="_css-showsekelton-ifr"
                ></iframe>
            </div>
            <!-- //高度为444的iframe显示 -->
        </zdialog-function>
        <!-- //看流程图的对话框 -->

        <zdialog-function
        init_id="zid_viewflowinstance"
        v-if="m_selectedwfiguid"
        :init_title="'查看流程'"
        :init_zindex="1001"
        :init_width="1080"
        :init_innerWidth="1080"
        init_closebtniconfontclass="icon-suggested-close"
        :init_usecustomtitlearea="true"
        :debugmode="true"
        @onclose="m_selectedwfiguid = undefined"
        >

            <div slot="customtitlearea" class="css-fc" >
            
                <!-- ml24 w24 h24 -->
                <div class="css-dialogtitle-icon icon-dl-nodeicon"></div>
                <!-- ml24 w24 h24 -->

                <!-- ml4 -->
                <div class="css-dialogtitle-title" >查看流程</div>
                <!-- ml4 -->

            </div>

            <div slot="mainslot" style="overflow-y:auto;height:560px;"
            @mousedown="_stopPropagation($event)"
            @click="_stopPropagation($event)"
            >
                <!-- 左 744 -->
                <div class="_css-flownodecontent">
                    <div class="_css-flownodecontent-left">
                        <CompsFlowInsReadOnly
                            ref="ref_flownodeinfo"
                            :init_width="'100%'"
                            :init_height="'calc(100% - 0px)'"
                            :init_bgcolor="'#fff'"
                            :init_selectedWfiGuid="m_selectedwfiguid"
                            :init_bimcomposerId="func_getBIMComposerId()"
                            @onrefresh="evt_onrefresh"
                            @onmodelclick="evt_onmodelclick"
                            @ondocclick="evt_ondocclick"
                            @onloggot="evt_onloggot"
                        ></CompsFlowInsReadOnly>
                    </div>
                    <div class="_css-flownodecontent-right">
                        <div class="_css-flownodecontent-rtop">
                            <div class="_css-flownodecontent-rtop-log">
                                日志
                            </div>
                            <div 
                            @click="func_showskeleton_fromnodeinfo($event)"
                            class="_css-flownodecontent-rtop-skeleton">
                                流程图
                            </div>
                        </div>
                        <div class="_css-flownodecontent-rbottom">
                            <div v-for="(logi, index) in m_currentwftilogs" 
                            :key="index"
                            >
                                <div class="_css-logi">
                                    <div class="_css-logi-line">
                                        <div class="_css-logi-line-head">
                                        </div>
                                        <div class="_css-logi-line-body" 
                                        :class="{'_css-last': index == m_currentwftilogs.length - 1}"
                                        >
                                        </div>
                                    </div>
                                    <div class="_css-logi-content" 
                                    >
                                        <div class="_css-logi-content-date">{{logi.wfr_createtimestr}}</div>
                                        <div class="_css-logi-content-body">
                                            <div class="_css-logi-content-toppart"
                                            :class="{'_css-submit': logi.oper_type == '提交'
                                            , '_css-disapprove': logi.oper_type == '驳回'
                                            , '_css-approve': logi.oper_type == '审批'}"
                                            >
                                                <!-- width:206px ml:20px -->
                                                <div class="_css-headtitle"
                                                :title="func_getRecordText(logi)"
                                                >{{func_getRecordText(logi)}}</div>
                                                <!-- //width:206px ml:20px -->

                                                <!-- width:20px ml:20px -->
                                                <div class="_css-headicon"
                                                :class="{'icon-dl-submit': logi.oper_type == '提交'
                                            , 'icon-dl-disapprove': logi.oper_type == '驳回'
                                            , 'icon-checkbox-Selected-Disabled-dis-blue': logi.oper_type == '审批'
                                            , '_css-icon-approverepair':logi.oper_type == '审批'}"
                                                ></div>
                                                <!-- //width:20px ml:20px -->
                                            </div>
                                            <div class="_css-logi-content-commentpart">{{logi.wfr_comment}}</div>
                                        </div>
                                    </div>
                                </div>
                                
                            </div>
                        </div>
                    </div>
                </div>
                <!-- //左 744 -->
                
            </div>
        </zdialog-function>
        <!-- //显示详情的对话框 -->



















        
            <el-table
            ref="ref_compsflow"
            :highlight-current-row="false"
            :border="true"
            :stripe="false"
            :data="m_data"
            style="width: 100%"
            :default-sort="{prop: 'date', order: 'descending'}"
            height="500"
            class="_css-table-this css-scroll css-miniline"
            :class="{'_css-nofilter': true}"
            :row-class-name="tableRowClassName"
            :header-cell-style="{'background-color':'transparent'}"
            @row-click="evt_rowclick"
            >

                <el-table-column
                    :sortable="true"
                    :resizable="false"
                    min-width="180"
                    prop="wfi_title"
                    label="标题"
                >
                    <template slot-scope="scope">
                        <div class="_css-flowname-cell">
                            <div class="_css-flowname-cell-no">{{scope.$index + 1}}</div>
                            <div class="_css-flowname-cell-text">{{scope.row.wfi_title}}</div>
                        </div>
                    </template>
                </el-table-column>

                <el-table-column
                width="120"
                label="当前节点"
                >
                    <template slot-scope="scope">
                        <div 
                        :title="scope.row.currentNode == null?'':scope.row.currentNode.name"
                        class="_css-table-cell"
                        >{{scope.row.currentNode == null?'':scope.row.currentNode.name}}</div>
                    </template>
                </el-table-column>

            </el-table>

    </div>
</template>
<script>

import CompsFlowInsReadOnly from '@/components/CompsCommon/CompsFlowInsReadOnly'
export default {
    components: {
        CompsFlowInsReadOnly
    },
    filters: {
        flt_getChState(en) {
            var _this = this;
            if (en.indexOf('A_') == 0) {
                return '待检查';
            }
            else if (en.indexOf('B_') == 0) {
                return '待整改';
            }
            else if (en.indexOf('C_') == 0) {
                return '待验收';
            }
            else {
                return '已合格';
            }
        },

        flt_getStateClass(en) {
            var _this = this;
            if (en.indexOf('A_') == 0) {
                return '_css-a';
            }
            else if (en.indexOf('B_') == 0) {
                return '_css-b';
            }
            else if (en.indexOf('C_') == 0) {
                return '_css-c';
            }
            else {
                return '_css-d';
            }
        }    
    },
    methods: {

        evt_onrefresh() {

        },

        func_getRecordText(logi) {
            var _this = this;
            var _str = `${logi.wfr_senderbu} ${logi.oper_type}到 ${logi.nodeText}`;
            // {{logi.wfi_senderbu}}{{logi.oper_type}}{{logi.nodeText}}
            return _str;
        },

        evt_onmodelclick(modelid) {
            var _this = this;
            window.projectbootvue.func_previewmodel(modelid);
        },

        evt_ondocclick(FileObj) {
            var _this = this;
            window.projectbootvue.func_previewdoc(FileObj.FileId, FileObj.FileName);
        },

        evt_onloggot(list) {
            var _this = this;
            console.log(_this.m_currentwftilogs);
            _this.m_currentwftilogs = _this.$staticmethod.DeepCopy(list);
        },

        func_closeskeleton() {
            var _this = this;
            _this.status_showskeleton = false;
        },

        // 显示流程图（从节点弹层中）
        // -----------------------
        func_showskeleton_fromnodeinfo(ev) {
             var _this = this;
            _this._stopPropagation(ev);
            var currentid = '';
            if (_this.m_selectedObj && _this.m_selectedObj.currentNode && _this.m_selectedObj.currentNode.id) {
                currentid = _this.m_selectedObj.currentNode.id;
            }

            // 设置 src 地址
            // ------------
            //var _src = `${window.bim_config.integrated_innerview}/A1ddons/Module_flow/?devmode=0&wfs_guid=${
                //_this.m_selectedObj.wfs_guid}&currentNodeId=${currentid}`;
            var _src = _this.$staticmethod.getFlowUrl(true
                , 0, _this.m_selectedObj.wfs_guid, currentid, '', '');

            _this.m_showskeletonsrc = 'about:blank';
            _this.m_showskeletonsrc = _src;
            _this.status_showskeleton = true;
        },

        _stopPropagation(ev) {
            ev && ev.stopPropagation && ev.stopPropagation();
        },

        func_getBIMComposerId(){
            var _this = this;
            var _bimcomposerId = _this.$staticmethod._Get("bimcomposerId");
            return _bimcomposerId;
        },

        // 表格行点击
        // ----------
        evt_rowclick(row, col, ev) {
            var _this = this;
            _this.m_selectedwfiguid = row.wfi_guid;
            _this.m_selectedObj = row;
        },

        // 设置数据
        // -------
        func_setdatas(datas) {
            var _this = this;
            _this.m_data = _this.$staticmethod.DeepCopy(datas);
        },

        // getdata

        // 获取某构件相关联的现场数据
        // ------------------------
        func_getdatas() {
            var _this = this;
            var _Token = _this.$staticmethod.Get("Token");
            var _url = `${window.bim_config.webserverurl}/api/FlowForm/Flow/GetFlowInstances_ByBMGuid?bm_guid=${_this._bm_guid}&Token=${_Token}`;
            _this.$axios.get(_url).then(x => {
                if (x.data.Ret > 0) {
                    if (x.data.Data && x.data.Data.List) {
                        _this.m_data = _this.$staticmethod.DeepCopy(x.data.Data.List);
                    }
                } else {
                    _this.$message.error(x.data.Msg);
                }
            }).catch(x => {
                console.error(x);
            });
        },

        // //getdata

        // el-table
        
        // 根据是否选中了某一行，返回类字符串
        // -------------------------------
        tableRowClassName({ row, rowIndex }) {
            return 'css-tdunder';
        },

        // //el-table
    },
    created() {
        var _this = this;
        console.log('created 已执行', _this.init_bmguid);
        _this._bm_guid = _this.init_bmguid;
    },
    mounted() {
        var _this = this;
        console.log('mounted 已执行');
        if (_this.init_bmguid != '-noval-') { 
            _this.func_getdatas();
        }
    },
    data() {
        return {
            _bm_guid:'',

            // 表格数据
            // -------
            m_data:[],

            // 选中的数据
            // ---------
            m_selectedwfiguid:undefined,
            m_selectedObj: {},

            // 当前正在查看的流程实例操作记录
            // ---------------------------
            m_currentwftilogs: [],

            // 查看流程图时的页面的内部地址
            // --------------------------
            m_showskeletonsrc:'about:blank',

            // 是否显示流程图预览
            // -----------------
            status_showskeleton: false
        };
    },
    props:{
        init_bmguid:{
            type: String,
            required: true
        }
    }    
}
</script>
<style scoped>


._css-logi-content-toppart._css-disapprove {
    background:linear-gradient(270deg,rgba(255,104,104,1) 0%,rgba(244,21,21,1) 100%);
} 

._css-logi-content-toppart._css-approve {
    background:linear-gradient(270deg,rgba(0,178,255,1) 0%,rgba(0,122,255,1) 100%);
} 

._css-logi-content-toppart._css-submit {
    background:linear-gradient(270deg,rgba(81,108,135,1) 0%,rgba(40,58,79,1) 100%);
} 

._css-logi-content-commentpart {
    height: calc(100% - 36px);
    box-sizing: border-box;
    color:#616F7D;
    font-size: 13px;
    padding: 12px 20px 12px 20px;
    text-align: left;
}

._css-logi-content-toppart {
    height: 36px;
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
    color:#fff;
    display: flex;
    align-items: center;
    font-size: 14px;
}

._css-logi-content-body {
    /* height:80px; */
    min-height:80px;
    margin-top:10px;
    box-sizing: border-box;
    width:274px;
    box-shadow:0px 2px 10px -3px rgba(0,19,84,0.2);
    border-radius:4px;
}

._css-logi-content-date {
    color:#616F7D;
    font-size: 14px;
    height:20px;
    line-height: 20px;
    text-align: left;
}

._css-logi-content {
    margin-left: 14px;
    margin-right: 20px;
    flex: 1;
}
._css-logi {
    min-height: 144px;
    display: flex;
}
._css-logi-line-body {
    width: 1px;
    flex: 1;
    background-color: rgba(97, 111, 125, 0.2);
}

._css-logi-line-body._css-last {
    background-color: transparent;
}

._css-logi-line-head {
    width: 9px;
    height: 9px;
    border-radius: 50%;
    background-color: rgba(97, 111, 125, 1);
}
._css-logi-line {
    width: 9px;
    margin-left: 20px;
    display: flex;
    flex-direction: column;
    /* margin-top: 4px; */
    position: relative;
    top: 6px;
    align-items: center;
}


._css-headicon {
    width: 20px;
    height: 20px;
    margin-left: 20px;
    color: #fff;
    font-size: 18px;
    line-height: 20px;
}

._css-headtitle {
    width:206px;
    margin-left: 20px;
    text-align: left;
    white-space: nowrap;
    overflow-x: hidden;
    text-overflow: ellipsis;
    flex:none;
}

._css-showsekelton-ifr {
    width:100%;
    height:calc(100% - 4px);
    border:none;
}

._css-flownodecontent-rtop-skeleton {
    box-sizing: border-box;
    color:rgba(97, 111, 125, 1);
    font-size: 16px;
    line-height: 48px;
    cursor: pointer;
    height:100%;
    margin-left:32px;
}

._css-flownodecontent-rtop-log {
    box-sizing: border-box;
    border-bottom: 2px solid #1890FF;
    color:rgba(40, 58, 79, 1);
    font-size: 16px;
    line-height: 48px;
    cursor: pointer;
    height:100%;
    margin-left:20px;
}

._css-flownodecontent-rtop {
    height:48px;
    display: flex;
}

._css-flownodecontent-rbottom {
    height:calc(100% - 48px);
    overflow-y: auto;
    padding-top:24px;
    padding-bottom: 24px;
    box-sizing: border-box;
}

._css-flownodecontent-right {
    flex: none;
    width: 336px;
    background-color: rgba(244, 245, 246, 1);
}

._css-flownodecontent-left {
    /* flex: 1; */
    width: calc(100% - 336px);
}

._css-flownodecontent {
    display: flex;
    width: 100%;
    height: 100%;
}


._comps-flows-all {
    width:100%;
    height:100%;
}

._css-table-this {
    height: 100% !important;
}
._css-flowname-cell {
    display: flex;
    width:100;
    align-items: center;
}

._css-flowname-cell-no {
    min-width:20px;
    height:20px;
    font-size:14px;
    color:rgba(97,111,125,1);
    line-height:20px;
    font-weight: normal;
    margin-left:10px;
    margin-right: 10px;
    text-align: right;
}

._css-flowname-cell-text {
    font-weight: bold;
    color: rgba(40,58,79,1);
    font-size: 14px;
    cursor: pointer;
}

._css-a {
    width: 74px;
    height: 24px;
    line-height: 22px;
    border: 1px solid transparent;
    text-align: center;
    font-size: 14px;
    background-color: #A6AEB6;
    color: #fff;
    border-radius: 4px;
}

._css-b,
._css-d,
._css-c {
    width: 74px;
    height: 24px;
    line-height: 22px;
    border: 1px solid transparent;
    text-align: center;
    font-size: 14px;
    background-color:#006CB4;
    color: #fff;
    border-radius: 4px;
}


</style>