<template>
  <div class="_css-all-steptip" :style="computestyle">
    <!-- 对话框前景 -->
    <div class="_css-comps-front" :style="computefrontStyle"  
    >
      <div class="_css-typeitem-title">
        修改分享权限
        <span class="_css-typeitem-close icon-interface-guanbimoxing" @click.stop="_closeall()"></span>
      </div>
      <div class="_css-edit-main">
        <div class="_css-share-shape">
          <div class="_css-shape-left">
            分享形式
          </div>
          <div class="_css-shape-center">
            <div class="_css-encrypt">
              <div class="_css-click-share">
                <el-radio v-model="chooseRadioNum" label="1" @change="handelClickViewPwd">加密</el-radio>
              </div>
              <span class="_css-encrypt-explain">{{ chooseRadioNum == '1' ? checkSeePwd : ''}}</span>
            </div>
            <div class="_css-encrypt _css-public"> 
              <div class="_css-click-share">
                <el-radio v-model="chooseRadioNum" label="0">公共</el-radio>
              </div>
              <span class="_css-encrypt-explain">所有人都可直接访问</span>
            </div>
          </div>
          
        </div>
        <div class="_css-share-shape _css-share-time">
          
          有效时间
          <div class="_css-shape-center">
            <div class="share-radio">
              <el-radio v-model="shareValidTime" label="1">一天</el-radio>
              <el-radio v-model="shareValidTime" label="7">一周</el-radio>
              <el-radio v-model="shareValidTime" label="-1">永久有效</el-radio>
            </div>
          </div>
        </div>
      </div>
      <div class="_css-typeitem-button">
        <div class="_css-share-sure" @click.stop="shareEditSure">确定</div>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  data() {
    return {
      chooseRadioNum: '0',
      shareValidTime: 1,
      chooseRadio: false,
      dragstyle:{
          position: 'fixed',
          right: 'calc(50% - 252px)',
          top: 'calc(50% - 242px)'
      },
      shareHasPwd: '1',
      checkSeePwd: '需输入访问密码访问',
      chooseRadioButton: false
    };
  },
  props: {
    shareEditObj:{
      type: Object,
      default: true
    }
  },
  computed: {
    computestyle: {
      get() {
        var _this = this;
        var _s = {};
        if (_this.zIndex) {
          _s["z-index"] = _this.zIndex;
        } else {
          _s["z-index"] = 2;
        }
        return _s;
      }
    },
    computefrontStyle: {
      get() {
        var _this = this;
        var _s = {};
        if (_this.width) {
          _s["width"] = _this.width;
        }
        for(var _sattr in _this.dragstyle) {
          _s[_sattr] = _this.dragstyle[_sattr];
        }
        return _s;
      }
    }
  },
  mounted() {
    let _this = this
    
    _this.shareValidTime = this.shareEditObj.ShardDays + '';
    this.checkSeePwd = this.shareEditObj.Password;
     
    _this.chooseRadio = _this.shareEditObj.shareModelPublic
    _this.shareEditObj.shareModelPublic ? this.chooseRadioNum = '1' : this.chooseRadioNum = '0'
    if(_this.chooseRadio && _this.shareEditObj.shareModelPublic){
      _this.chooseRadioButton = true
      _this.chooseRadioNum = '1'
    }else{
      _this.chooseRadioButton = false
      _this.chooseRadioNum = '0'
    }
  },
  methods: {
 
    _closeall(){
      let _this = this;
      _this.$emit("closeall");
    },
    // 选择加密
    chooseEncrypt(num) {
      let _this = this
      _this.shareHasPwd = num
      num==0?_this.chooseRadio=true:_this.chooseRadio=false
    },
    // 选择有效期
    chooseDate(num) {
      let _this = this
      _this.shareValidTime = num
    },
    handelClickViewPwd () {
      if (this.chooseRadioNum == '1') {
        let code = ''
        const codeLength = 4
        // 设置随机字符
        const random = [0, 1, 2, 3, 4, 5, 6, 7, 8, 9]
        for (let i = 0; i < codeLength; i++) {
          const index = Math.floor(Math.random() * 9)
          code += random[index]
        }
        this.checkSeePwd = code
      }
    },
    shareEditSure() {
      let _this = this;
      let hpw = this.chooseRadioNum == '0'? false : true;
      let pwd = hpw ? this.checkSeePwd : ''
      let dataArg = {
        Id: _this.shareEditObj.shareId,
        ShardDays: this.shareValidTime * 1,
        HasPassword: hpw,
        Password: pwd,
      }
      _this.$emit('handelClickShareEditSure', dataArg)
      
    },
 
  },
};
</script>
<style scoped>
._css-all-steptip {
  position: fixed;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.15);
  display: flex;
  align-items: center;
  justify-content: space-around;
}
._css-comps-front {
  width:504px;
  min-height: 48px;
  background-color: rgba(255, 255, 255, 1);
  box-shadow: 0px 13px 24px -17px rgba(11, 41, 62, 0.8);
  border-radius: 4px;
}
._css-typeitem-title {
  text-align: left;
  position: relative;
  line-height: 44px;
  font-size: 16px;
  font-weight: 500;
  color: rgba(0, 0, 0, 1);
  padding-left: 24px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.09);
}
._css-typeitem-title ._css-typeitem-close {
  position: absolute;
  top: 12px;
  right: 12px;
  color: rgba(0, 0, 0, 0.45);
  cursor: pointer;
}
._css-typeitem-button{
  height:53px;
  padding-top:11px;
  text-align:right;
  flex-direction: row-reverse;
  display: flex;
  padding-right:24px;
  border-top:1px solid rgba(0, 0, 0, 0.09);
}
._css-share-sure{
  width:90px;
  height: 40px;
  border-radius: 4px;
  background: #1890FF;
  color:#fff;
  line-height: 40px;
  text-align: center;
  cursor:pointer;
}
._css-edit-main{
  padding:0 24px;
  font-size:14px;
  font-weight:500;
  color:rgba(0,0,0,0.45);
}
._css-share-shape{
  border:1px solid rgba(0, 0, 0, 0.15);
  border-radius: 4px;
  margin-top:26px;
  display: flex;
  padding:18px 16px 0px 24px;
}
._css-shape-left{
  line-height: 26px;
}
._css-shape-pwd{
  width:78px;
  height:26px;
  border-radius:4px;
  text-align: center;
  border:1px solid rgba(24,144,255,1);
  line-height: 26px;
  color:#1890FF;
  cursor:pointer;
}
._css-shape-center{
  flex:1;
}
._css-time-left{
  flex: 1;
  text-align: left
}
._css-time-right{
  text-align: right;
}
._css-encrypt{
  margin-bottom:20px;
  display: flex;
}
._css-click-share{
  width: 80px;
  cursor:pointer;
  text-align: left;
  padding-left: 20px;
}

._css-encrypt-icon{
  vertical-align: middle;
  display: inline-block;
  width:16px;
  height: 16px;
  line-height: 16px;
  cursor:pointer;
}
._css-encrypt-title{
  display: inline-block;
  width:56px;
  height: 22px;
  font-size: 14px;
  font-weight: 500;
}
._css-encrypt-explain{
  color: rgba(0, 0, 0, 0.25);
  font-size:12px;
  margin-right:26px;
  flex: 1;
  text-align: left;
}
._css-share-time{
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin: 16px 0 49px;
  padding:8px 16px 8px 24px;
}
._css-shape-indate{
  flex: 1;
  color: rgba(0,0,0,.45);
  padding-left: 5px;
}
._css-share-time .share-radio{
  text-align: left;
  padding-left: 20px;
}
._css-edit-main /deep/ .el-radio__inner{
  width: 16px;
  height: 16px;
}
._css-share-calendar{
  height: 60px;
  width:calc(100% - 80px);
  z-index: 9;
  cursor:pointer;
}
._css-share-time /deep/ .el-input__inner{
  line-height: inherit
}
._css-share-time /deep/ .el-date-editor.el-input, ._css-share-time /deep/.el-date-editor.el-input__inner{
  width:100%;
  line-height: 60px;
}
._css-share-time /deep/ .el-input__icon{
  display:none;
}
._css-share-time /deep/ .el-input--prefix .el-input__inner{
  padding:0;
  color: rgba(0,0,0,0.45);
  word-spacing: -3px;
}
._css-shape-pwd-disable{
  border:1px solid rgba(0, 0, 0, 0.25);
  color:rgba(0, 0, 0, 0.25);
  cursor:no-drop;
}
</style>