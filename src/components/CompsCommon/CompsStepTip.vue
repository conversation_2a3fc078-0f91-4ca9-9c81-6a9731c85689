<template>
  <div class="_css-all-steptip" :style="computestyle">
    <!-- 对话框前景 -->
    <div class="_css-comps-front" :style="computefrontStyle"  v-drag="draggreet">
      <CompsDialogHeader @oncancel="_oncancel" :title="title || '无标题'"></CompsDialogHeader>
      <!-- 第一步 -->
      <div class="_css-comps-step _css-comps-step1">
        <div class="_css-step-tipicon">第1步</div>
        <div class="_css-step-tiptext">下载Excel模板</div>
        <div class="_css-step-btncon">
          <CompsEloButton
            :width="76"
            :height="32"
            text="下载模板"
            color="rgba(0,0,0,0.65)"
            bgcolor="rgba(0,0,0,0.04)"
            border="1px solid rgba(0,0,0,0.15)"
            :fontSize="12"
            @onclick="download_template"
          ></CompsEloButton>
        </div>
      </div>
      <!-- //第一步 -->
      <!-- 第二步 -->
      <div class="_css-comps-step _css-comps-step2">
        <div class="_css-step-tipicon">第2步</div>
        <div class="_css-step-tiptext">编辑Excel模板（邮箱和手机号将作为登录账号，请确保正确）</div>
      </div>
      <!-- //第二步 -->

      <!-- 显示表格的示例图片 -->
      <div class="_css-step-tableimagearea">
        <div class="_css-stop-tableimage temp-muban"></div>
      </div>
      <!-- //显示表格的示例图片 -->

      <!-- 第三步 -->
      <div class="_css-comps-step _css-comps-step3">
        <div class="_css-step-tipicon">第3步</div>
        <div class="_css-step-tiptext">上传编辑好的Excel文件</div>
        <div class="_css-step-btncon">
          <CompsEloButton
            :width="76"
            :height="32"
            text="上传文件"
            color="rgba(0,0,0,0.65)"
            bgcolor="rgba(0,0,0,0.04)"
            border="1px solid rgba(0,0,0,0.15)"
            :fontSize="12"
            @onclick="upload_importingfile"
          ></CompsEloButton>
        </div>
      </div>
      <!-- //第三步 -->
      <CompsDialogBtns @onok="_onok" @oncancel="_oncancel"></CompsDialogBtns>
    </div>
    <!-- //对话框前景 -->

    <!-- 下方的导入用户 -->
    <input
      type="file"
      style="display:none"
      id="id_ImportUsers_File"
      accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
      @change="importFileChange()"
    >
    <!-- //下方的导入用户 -->
  </div>
</template>
<script>
/*
input:
    
events:
    onok();
    oncancel();
      _this.$emit("onfinish", true, '导入成功');
*/
import CompsDialogHeader from "@/components/CompsDialog/CompsDialogHeader";
import CompsDialogBtns from "@/components/CompsDialog/CompsDialogBtns";
import CompsEloButton from "@/components/CompsElOverride/CompsEloButton";
export default {
  data() {
    return {
      val:'0',
            dragstyle: {
                position: 'fixed',
                right: 'calc(50% - 252px)',
                top: 'calc(50% - 216px)'
            }
    };
  },
  computed: {
    computestyle: {
      get() {
        var _this = this;
        var _s = {};
        if (_this.zIndex) {
          _s["z-index"] = _this.zIndex;
        }
        return _s;
      }
    },
    computefrontStyle: {
      get() {
        var _this = this;
        var _s = {};
        if (_this.width) {
          _s["width"] = _this.width;
        }
        for(var _sattr in _this.dragstyle) {
          _s[_sattr] = _this.dragstyle[_sattr];
        }
        return _s;
      }
    }
  },
  methods: {
       draggreet(val){
                var _this = this;
                _this.val = val;
            },
    // 选择Excel文件后执行
    importFileChange() {
      // 准备 formData
      var _this = this;
      var dominputfile = document.getElementById("id_ImportUsers_File");
      if (dominputfile.files.length == 1) {
        // 拿到文件对象
        var file = dominputfile.files[0];        

        // 准备参数
        var fd = new FormData();
        fd.append("OrganizeId", _this.companyId); // 公司ID
        fd.append("File", file);
        fd.append("Token", this.$staticmethod.Get("Token"));
        
        var config = {
          headers: {
            "Content-Type": "multipart/form-data"
          }
        };
        _this.$axios
          .post(
            `${_this.$urlPool.ImportUser}`,
            fd,
            config
          )
          .then(x => {
            if (x.status == 200 && x.data.Ret > 0) {
              //_this.$message.success('导入成功');
              _this.$emit("onfinish", true, '导入成功');
            } else if (x.data.Ret == -15001) {
              _this.$emit("onfinish", false, `${x.data.Msg}(${x.data.Ret}))`);
            } else {
              _this.$emit("onfinish", false, `导入失败：(${x.data.Msg})`);
            }
          })
          .catch(x => {
            _this.$emit("onfinish", false, `导入失败`);
          });
     
      }
    },

    download_template() {
      var _this = this;
      window.location.href = `${
        window.bim_config.webserverurl
      }/Content/Resource/Template/机构人员导入模板.xlsx`;
    },
    upload_importingfile() {
      var _this = this;
      var dominputfile = document.getElementById("id_ImportUsers_File");
      dominputfile.value = "";
      dominputfile.click();
    },
    _oncancel() {
      var _this = this;
      _this.$emit("oncancel");
    },
    _onok() {
      var _this = this;
      _this.$emit("onok");
    }
  },
  props: {
    zIndex: {
      type: Number,
      required: false
    },
    title: {
      type: String,
      required: false
    },
    width: {
      type: String,
      required: false
    },
    companyId: {
      type: String,
      required: false
    }
  },
  components: {
    CompsDialogHeader,
    CompsDialogBtns,
    CompsEloButton
  }
};
</script>
<style scoped>
._css-step-tableimagearea {
  height: 168px;
  margin-top: 18px;
  display: flex;
  align-items: center;
}
._css-stop-tableimage {
  height: 100%;
  margin-left: 84px;
  flex: 1;
  background-position-x: 0;
  background-size: contain;
}
._css-step-btncon {
  position: absolute;
  right: 16px;
}
._css-step-tipicon {
  width: 56px;
  height: 20px;
  line-height: 22px;
  margin-left: 16px;
  color: rgba(0, 0, 0, 0.25);
  background-color: rgba(20, 20, 20, 0.04);
  font-size: 12px;
}
._css-step-tiptext {
  margin-left: 12px;
  height: 20px;
  line-height: 22px;
  color: rgba(0, 0, 0, 0.85);
  font-size: 12px;
  text-align: left;
}
._css-comps-step1 {
  height: 32px;
  margin-top: 18px;
  display: flex;
  align-items: center;
  position: relative;
}
._css-comps-step2 {
  height: 32px;
  margin-top: 18px;
  display: flex;
  align-items: center;
  position: relative;
}
._css-comps-step3 {
  height: 32px;
  margin-top: 18px;
  display: flex;
  align-items: center;
  position: relative;
}
._css-comps-front {
  /* width: 410px; */
  min-height: 48px;
  background-color: rgba(255, 255, 255, 1);
  box-shadow: 0px 13px 24px -17px rgba(11, 41, 62, 0.8);
  border-radius: 4px;
}
._css-all-steptip {
  position: fixed;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.15);
  display: flex;
  align-items: center;
  justify-content: space-around;
}
</style>