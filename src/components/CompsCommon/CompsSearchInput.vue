<template>
  <div :style="{width: width + 'px'}">
    <div v-if="mini" :class="[{boxShadow: inputFocus},'search-input']" ref="searchInput" :style="searchInputStyle">
      <label class="icon" :for="randomID"><span class="icon-interface-search"></span></label>
      <input :id="randomID"
         ref="inputContent"
         autocomplete="off"
         :placeholder="placeholder"
         class="input-content" type="text"
         :value="value"
         @keyup.esc="setBlur"
         @focus="toggleInputState('focus',$event)"
         @blur="toggleInputState('blur',$event)"
         @input="getInputValue">
      <span v-if="hasInput" @click.stop="clearInput" class="clearInput icon-suggested-close_circle"></span>
    </div>
    <div v-else :class="[{boxShadow: inputFocus},'search-input default-input']" :style="{backgroundColor:backgroundColor}">
      <label class="icon" :for="randomID"><span class="icon-interface-search"></span></label>
      <input :id="randomID"
             ref="inputContent"
             autocomplete="off"
             :placeholder="placeholder"
             class="input-content" type="text"
             :value="value"
             @keyup.esc="setBlur"
             @focus="inputFocus=true"
             @blur="inputFocus=false"
             @input="getInputValue">
      <span v-if="hasInput" @click.stop="clearInput" class="clearInput icon-suggested-close_circle"></span>
    </div>
  </div>
</template>

<script>
    export default {
      name: "CompsSearchInput",
      data() {
        return {
          randomID: '',
          inputMarginLeft: {
            marginLeft: ''
          },
          inputWidth: {
            width: ''
          },
          inputFocus: false,//input是否获取焦点
          hasInput: false,//输入框中是否有内容
        }
      },

      props: {
        value: '',

        width: {//宽度
          type: [String,Number],
          default: '200'
        },

        mini: {//输入框模式
          type: Boolean,
          default: false
        },

        backgroundColor: {//背景颜色
          type: String,
          default: '#FFFFFF'
        },

        placeholder: {//input提示文字
          type: String,
          default: '请输入'
        },

        onTheLeft: {//输入框在左边
          type: Boolean,
          default: false
        },

        onTheRight: {//输入框在右边
          type: Boolean,
          default: false
        }
      },

      created() {
        this.setRelevantParameters();
        this.setRandomClass();
      },

      methods: {
        //设置相关参数
        setRelevantParameters() {
          if (this.onTheRight) {
            this.inputMarginLeft.marginLeft = (this.width - 35) + 'px';
          } else {
            this.inputWidth.width = '35px';
          }
        },

        toggleInputState(action,event) {

          if (action == 'focus') {
            this.inputFocus = true;
            this.$refs.searchInput.style.background = this.backgroundColor;

            if (this.onTheRight) {
              //如果在右边 执行该动画
              Velocity(
                  this.$refs.searchInput,
                  {marginLeft: 0,},
                  {duration: 150}
                );
            } else {
              //如果再左边 执行该动画
              Velocity(
                this.$refs.searchInput,
                {width: this.width + 'px',},
                {duration: 150}
              );
            }
          } else if (action == 'blur') {
            if (event.target.value.length == 0) {
              this.inputFocus = false;
              this.$refs.searchInput.style.background = '';

              if (this.onTheRight) {
                //如果在右边 执行该动画
                Velocity(
                  this.$refs.searchInput,
                  {marginLeft: this.inputMarginLeft.marginLeft,},
                  {duration: 150}
                );
              } else {
                Velocity(
                  //如果再左边 执行该动画
                  this.$refs.searchInput,
                  {width: 35,},
                  {duration: 150}
                );
              }
            }
          }
        },

        setBlur() {
          this.$refs.inputContent.blur();
        },

        //随机分配ID
        setRandomClass() {
          this.randomID = Date.parse(new Date()).toString() + Math.random();
        },

        getInputValue(event) {
          let val = event.target.value;

          if (val.length > 0) {
            this.hasInput = true;
          } else {
            this.hasInput = false;
          }

          this.$emit('input',val);
        },

        //清除输入框
        clearInput() {
          this.hasInput = false;
          this.$refs.inputContent.focus();
          this.$emit('input','');
        }
      },

      computed: {
        searchInputStyle() {
          if (this.onTheRight) {
            return this.inputMarginLeft;
          } else {
            return this.inputWidth
          }
        }
      }
    }
</script>

<style scoped>
  .search-input {
    position: relative;
    overflow: hidden;
    height: 40px;
    border-radius: 20px;
  }

  .search-input.boxShadow {
    box-shadow: 0 1px 1px 0 rgba(0,21,41,0.12);
  }

  .search-input .icon {
    cursor: pointer;
    text-align: center;
    width: 35px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: absolute;
    left: 0;
    top: 0;
    color: #BFBFBF;
    font-weight: bold;
  }

  .search-input.boxShadow .icon {
    color: #1890FF;
  }

  .search-input .input-content {
    width: 100%;
    height: 100%;
    border: none;
    outline: none;
    box-sizing: border-box;
    padding: 0 25px 0 35px;
    background: none;
  }

  .search-input.boxShadow .input-content {
    caret-color: #1890FF;
  }

  .clearInput {
    position: absolute;
    right: 5px;
    top: 11px;
    font-size: 16px;
    color: #8C8C8C;
    cursor: pointer;
  }

  .clearInput:hover {
    opacity: .8;
  }

  .search-input .default-input {
    box-shadow: 0 1px 1px 0 rgba(0,21,41,0.12);
  }

  /*.search-input.default-input input.input-content:focus {*/
    /*color: #1890FF;*/
  /*}*/

  /*.input-content {*/
    /*width: 0;*/
    /*-webkit-transition: width 1s cubic-bezier(0.49, -0.3, 0.68, 1.23);*/
    /*transition: width 1s cubic-bezier(0.49, -0.3, 0.68, 1.23);*/
  /*}*/
  /*.input-content:focus {*/
    /*-webkit-transition: width 1s cubic-bezier(0.48, 0.43, 0.29, 1.3);*/
    /*transition: width 1s cubic-bezier(0.48, 0.43, 0.29, 1.3);*/
    /*width: 100%;*/
  /*}*/
</style>
