<template>
    <div class="_css-docbrowser" :style="{'z-index': m_zIndex || 1001}"
    >
        <div class="_css-docbrowser-in" :style="getFrontStyle()"
        v-drag="greet"  
        >

            <!-- 标题区域 -->
            <div
            class="_css-docin-title css-fc"
            >
                <!-- 对话框标题及关闭按钮 -->
                <div class="_css-title-content css-f1">
                    {{init_title}}
                </div>
                <div 
                @click="$emit('onclose')"
                class="_css-title-closebtn css-closebtn icon-suggested-close"></div>
                <!-- //对话框标题及关闭按钮 -->

            </div>
            <!-- //标题区域 -->
            
            <!-- 内容区域 -->
            <div class="_css-docin-content">
                
                <template
                    v-if="selectedObjList && selectedObjList.length > 0"
                >
                    <div class="mgr-list">
                        <div 
                        v-for="thedoc in selectedObjList"
                        :key="thedoc.bm_guid"
                        class="css-common-line ">

                            <div 
                            class="_css-flowModelNameSelected _css-closehoverctn">
                                <div class="_css-addattach-icon icon-interface-associated-component"></div>
                                <div class="_css-modelname-show" >{{thedoc.bm_materialname}}（{{thedoc.bm_materialcode}}）</div>
                                <div class="_css-removebtn-ctn" >
                                    <div 
                                    @click="func_removetask($event, thedoc)"
                                    class="icon-suggested-close _css-formadd-closebtn _css-static">
                                    </div>
                                </div>
                                
                            </div> 
                        </div>
                    </div>
                </template>
                <template v-else>
                    <div class="css-common-line">
                        <div class="_css-modelname-show _css-line-none">暂无已关联构件</div>
                    </div>
                </template>
                <div class="-css-edit">
                    <zbutton-function
                        :init_text="'添加'"
                        :init_fontsize="14"
                        :debugmode="true"
                        :init_height="undefined"
                        :init_width="'76px'"
                        @onclick="$emit('addMgr')"
                        >
                    </zbutton-function>
                    <zbutton-function
                        :init_text="'清空'"
                        :init_fontsize="14"
                        :debugmode="true"
                        :init_height="undefined"
                        :init_width="'76px'"
                        @onclick="clearMgrList()"
                        >
                    </zbutton-function>
                </div>
            </div>
            <!-- //内容区域 -->
            
            <!-- 对话框按钮区域 -->
            <!-- <div
            class=" css-common-zdialogbtnctn _css-docin-btnctn"
            >
               
                <zbutton-function
                    :init_text="'取消'"
                    :init_fontsize="14"
                    :debugmode="true"
                    :init_height="undefined"
                    :init_width="'76px'"
                    :init_bgcolor="'#fff'"
                    :init_color="'#1890FF'"
                    @onclick="$emit('onclose')"
                    >
                </zbutton-function>
                <zbutton-function
                    :init_text="'确定'"
                    :init_fontsize="14"
                    :debugmode="true"
                    :init_height="undefined"
                    :init_width="'76px'"
                    @onclick="_emit_onok()"
                    >
                </zbutton-function> 

            </div> -->
            <!-- //对话框按钮区域 -->

        </div>
    </div>
</template>
<script>
export default {
    data() {
        return { 
            m_zIndex: 0,
            m_height: 0,
            m_width: 0, 
            // selectedObjList: []
        };
    },
    mounted(){
         var _this = this;
        window.docbrowervue = this;
        _this.m_zIndex = _this.init_zIndex;
        _this.m_width = _this.init_width;
        _this.m_height = _this.init_height;
        _this.m_bimcomposerId = _this.init_bimcomposerId;
        _this.m_organizeId = _this.init_organizeId;
    },
    methods: { 

        // _emit_onok() {
        //     var _this = this;
        //     _this.$emit("onok", _this.m_selectedfiles);
        // }, 
        greet(val){
            var _this = this;
            _this.val = val;
        },
        getFrontStyle() {
            var _this = this;
            var _s = {};
            _s["width"] = _this.m_width;
            _s["height"] = _this.m_height;
            _s["position"] = 'fixed';
            _s["right"] = `calc(50% - ${parseInt(_this.m_width.toString().replace('px', 'px')) / 2}px)`;
            _s["top"] = `calc(50% - ${parseInt(_this.m_height.toString().replace('px', 'px')) / 2}px)`;
            return _s;
        },
        // 删除某一条
        func_removetask(ev,doc){
            // console.log(doc)
            let _this = this;
            ev && ev.stopPropagation && ev.stopPropagation();
            _this.$confirm("确认移除该关联构件?", "操作确认").then(x => {
                _this.$emit("onRemoveList",doc);
            });
        },
        // 清空
        clearMgrList() {
            let _this = this;
            console.log('CompsMaterialEdit.vue');
            _this.$confirm("确认清空所有关联构件?", "操作确认").then(x => {
                _this.$emit("onclearMgrList",this.selectedObjList);
            });
        }
    },
    props: {
        // 前景高度
        // 前景宽度
        // 总体的 zindex
        // 标题
        // -------------
        init_height: {
            type: String,
            required: true
        },
        init_width: {
            type: String,
            required: true
        },
        init_zIndex: {
            type: Number,
            required: true
        },
        init_title: {
            type: String,
            required: true
        },
        init_bimcomposerId: {
            type: String,
            required: true
        },
        init_organizeId: {
            type: String,
            required: true
        },
        selectedObjList: {
            type: Array,
            required: true
        },
    }
}
</script>
<style scoped>

._css-1890ff {
    color: #1890FF;
}

._css-openstack-splitter {
     float:left;
}
._css-openstack-item {
    /* height: 18px;
    line-height: 18px;
    cursor: pointer;
    font-size: 12px;
    float:left; */

    height: 18px;
    line-height: 18px;
    cursor: pointer;
    font-size: 12px;
    float: left;
    /* max-width: 400px;
    min-width: 26px;
    overflow-x: hidden; */
    /* text-overflow: ellipsis; */
    white-space: nowrap;
}
._css-openstack-item:hover {
    text-decoration: #1890FF;
    color:#1890FF;
}
._css-openstack-ctn {
    height: 36px;
    display: flex;
    align-items: center;
    padding-left: 12px;
    overflow-x: auto;
}
._css-docin-tree {
    font-size: 12px;
    width:235px;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.02);
    overflow-y: auto;
}
._css-docin-list {
    /* flex: 1; */
    width:calc(100% - 235px);
    height: 100%;
    display: flex;
    flex-direction: column;
}
._css-title-content {
    font-size: 16px;
    margin: 0 24px 0 24px;
    flex:1;
    text-align: left;
}
._css-docin-content {
    width:100%;
    flex:1;
    display: flex;
    height: calc(100% - 50px - 64px);
}
._css-docin-btnctn {
    border-top: 1px solid rgba(0, 0, 0, 0.09);
    height:50px;
    margin-top: 0;
    flex:none;
    box-sizing: border-box;
}
._css-docin-title {
    height: 64px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.09);
    flex:none;
    box-sizing: border-box;
}
._css-title-closebtn {
    margin-right: 24px;
}
._css-docbrowser-in {
    background-color: #fff;
    border-radius: 4px;
    display: flex;
    flex-direction: column;
}
._css-docbrowser {
    width: 100%;
    height:100%;
    position: fixed;
    background: rgba(0, 0, 0, 0.3);
    display: flex;
    align-items: center;
    justify-content: space-around;
}

._css-closehoverctn ._css-formadd-closebtn {
    right: 0;
    top: 0;
    border-radius: 50%;
    font-size: 16px;
    cursor: pointer;
    visibility: hidden;
    display: flex;
    width: 16px;
    height: 16px;
    margin-right: 12px;
}

._css-closehoverctn ._css-formadd-closebtn._css-static {
  position: static;
  margin-left: 8px;
}

._css-closehoverctn:hover ._css-formadd-closebtn {
  visibility: visible;
}
._css-addattach-icon {
    margin-left: 20px;
    margin-right: 10px;
    width:20px;
    height: 20px;
}
._css-flowModelNameSelected:hover ._css-addattach-icon {
    color: rgba(52, 69, 87, 1);
    font-weight: bold;
}
._css-modelname-show {
    flex: 1;
    overflow-x: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    /* max-width: calc(100% - 26px); */
    max-width: 261.5px;
    text-align: left;
}
._css-line-none{
    line-height: 40px;
}
._css-docin-content {
    width:100%;
    flex:1;
    display: flex;
    height: calc(100% - 50px - 64px);
    text-align: initial;
    flex-direction: column;
}
.css-common-line{
    /* align-items: inherit; */
    /* flex:1; */
    display: inline-block;
    width: 49%;
}
.-css-edit{
    display: flex;
    align-self: flex-end;

}
.-css-edit div{
    margin-right: 20px;
    margin-bottom: 10px;
}

._css-flowModelNameSelected{
    height: 40px;
    line-height: 40px;
    cursor: pointer;
    color: rgba(40, 58, 79, 1);
    -webkit-box-flex: 1;
    -ms-flex: 1;
    flex: 1;
    max-width: calc(100% - 0px);
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    border-radius: 4px;
    border: 1px solid rgba(208,208,208,1);
}
.mgr-list{
    /* display: flex; */
    height: calc( 100% - 60px);
    overflow-y: auto;
}
</style>

