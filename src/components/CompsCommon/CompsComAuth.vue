<template>
  <div class="_css-all-comauth" :style="computestyle">
    <!-- 对话框前景 -->
    <div class="_css-comps-front" v-drag="draggreet"  :style="dragstyle">
      <CompsDialogHeader 
      :title_maxwidth="350"
      @oncancel="_oncancel" :title="title || '无标题'"></CompsDialogHeader>
      <!-- 机器码 -->
      <div class="_css-machinecode _css-field">
        <div class="_css-field-name">机器码</div>
        <textarea 
        @mousedown="_stopPropagation($event)"
        class="_css-field-val _enable" readonly="readonly" v-model="extdata.machineCode"></textarea>
      </div>
      <!-- //机器码 -->
      <!-- 授权码 -->
      <div class="_css-authcode _css-field">
        <div class="_css-field-name">授权码</div>
        <textarea 
        @mousedown="_stopPropagation($event)"
        class="_css-field-val"
        v-model="extdata.authcode"
        ></textarea>
      </div>
      <!-- //授权码 -->
      <CompsDialogBtns @onok="_onok" @oncancel="_oncancel"></CompsDialogBtns>
    </div>
    <!-- //对话框前景 -->
  </div>
</template>
<script>
/*
input:
    
events:
    onok();
    oncancel();
*/
import CompsDialogHeader from "@/components/CompsDialog/CompsDialogHeader";
import CompsDialogBtns from "@/components/CompsDialog/CompsDialogBtns";
export default {
  data() {
    return {
        val:'0',
            dragstyle: {
                position: 'fixed',
                right: 'calc(50% - 205px)',
                top: 'calc(50% - 147px)'
            },
      extdata: {
        machineCode: "" // 机器码
        ,authcode:'' // 授权码
      }
    };
  },
  mounted() {
    var _this = this;
    
    // 获取机器码并显示
    this.$axios.get(`${this.$urlPool.GetMachineCode}?Token=${this.$staticmethod.Get('Token')}`).then(x => {
        if (x.status == 200) {
            if (x.data.Ret > 0) {
                _this.extdata.machineCode = x.data.Msg;
            }
        }
    }).catch(x => {

    });
  },
  computed: {
    computestyle: {
      get() {
        var _this = this;
        var _s = {};
        if (_this.zIndex) {
          _s["z-index"] = _this.zIndex;
        }
        return _s;
      }
    }
  },
  methods: {
       draggreet(val){
                var _this = this;
                _this.val = val;
            },
            _stopPropagation(ev){
              ev.stopPropagation();
            },
    _oncancel() {
      var _this = this;
      _this.$emit("oncancel");
    },
    _onok() {
      var _this = this;
      _this.$emit("onok", _this.organizeId, _this.extdata.machineCode, _this.extdata.authcode);
    }
  },
  props: {
    zIndex: {
      type: Number,
      required: false
    },
    title: {
      type: String,
      required: false
    },

    // 正在进行授权的机构ID
    organizeId: {
      type: String,
      required: false
    }
  },
  components: {
    CompsDialogHeader,
    CompsDialogBtns
  }
};
</script>
<style scoped>
._css-machinecode {
  margin-top: 12px;
}

._css-machinecode textarea{
  background-color: rgba(0, 0, 0, 0.05);
}

._css-authcode {
  margin-top: 16px;
  height: 120px;
}
._css-field-name {
  margin-left: 16px;
  line-height:30px;
  border:none;
}
._css-field-val {
  resize: none;
  border: 1px solid rgba(220, 223, 230);
  border-radius: 3px;
  flex: 1;
  margin-left: 8px;
  margin-right: 16px;
  outline: none;
  color: rgba(0, 0, 0, 0.65);
  line-height:20px;
  border:none;
  background-color:#f0fbfb;
  font-size:15px;
  font-family: PingFangSC-Regular;
  padding:5px;
}
._css-field {
  display: flex;
}
._css-comps-front {
  width: 410px;
  min-height: 48px;
  background-color: rgba(255, 255, 255, 1);
  box-shadow: 0px 13px 24px -17px rgba(11, 41, 62, 0.8);
  border-radius: 4px;
}
._css-all-comauth {
  position: fixed;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.15);
  display: flex;
  align-items: center;
  justify-content: space-around;
}
._enable{cursor: unset;}
</style>