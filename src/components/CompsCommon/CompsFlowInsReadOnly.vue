<template>
    <div 
    id="id_flownodeinfo_all"
    class="_css-flownodeinfo-all" :style="style_all()" >

        <!-- 可切换 tab 高度为 40 -->
        <div class="_css-flowdetail-tabs-head">
            <div 
            @click="func_setactivetab('basic')"
            class="_css-tab-operlog _css-tab-item "
            :class="{'_css-current': m_flowactivetab == 'basic'}"
            >基本信息</div>
            <!-- <div 
            @click="func_setactivetab('form')"
            class="_css-tab-flowpreview _css-tab-item"
            :class="{'_css-current': m_flowactivetab == 'form'}"
            >表单</div> -->
            <div 
            @click="func_setactivetab('attachments')"
            class="_css-tab-flowpreview _css-tab-item"
            :class="{'_css-current': m_flowactivetab == 'attachments'}"
            >关联对象</div>
            <div 
            @click="func_setactivetab('operlogs')"
            class="_css-tab-flowpreview _css-tab-item"
            :class="{'_css-current': m_flowactivetab == 'operlogs'}"
            >操作记录</div>
            <div 
            @click="func_setactivetab('skeleton')"
            class="_css-tab-flowpreview _css-tab-item"
            :class="{'_css-current': m_flowactivetab == 'skeleton'}"
            >流程图</div>
        </div>
        <!-- //可切换 tab 高度为 40 -->

        <!-- 展示节点操作记录的 div -->
        <div 
        v-if="false && false"
        v-loading="m_nodelogloading"
        element-loading-text="加载中"
        @mouseover="evt_nodelogover($event)"
        @mouseout="evt_nodelogout($event)"
        class="_css-node-operlog" :style="style_nodetip()"
        :class="{'_css-show': status_nodeinfo_show}"
        >
            <template v-if="m_currentnodelogs.length">
                <div 
                v-for="(nl,index) in m_currentnodelogs"
                :key="index"
                :title="func_getnltitle(nl)"
                class="_css-node-operlogi" >
                {{func_getnltitle(nl)}}
                </div>
            </template>
            <div v-else class="_css-node-operlogi">&lt;无审批记录&gt;</div>
           
        </div>
        <!-- //展示节点操作记录的 div -->

        <!-- 进度任务浏览器 -->
        <CompsTaskBrowser
        v-if="status_taskbrowser"
        :init_zIndex="1002"
        :init_width="'800px'"
        :init_height="'640px'"
        :init_bimcomposerId="getBIMComposerId()"
        :init_organizeId="getOrganizeId()"
        init_title="请选择关联任务"
        @onclose="evt_taskbrowserclose"
        @onok="evt_taskbrowseronok"
        ></CompsTaskBrowser>
        <!-- //进度任务浏览器 -->

        <!-- 构件浏览器 -->
        <CompsMaterialBrowser
        v-if="status_materialbrowser"
        :init_zIndex="1002"
        :init_width="'800px'"
        :init_height="'640px'"
        :init_bimcomposerId="getBIMComposerId()"
        :init_organizeId="getOrganizeId()"
        init_title="请选择关联构件"
        @onclose="evt_materialbrowserclose"
        @onok="evt_materialbrowseronok"
        ></CompsMaterialBrowser>
        <!-- //构件浏览器 -->

        <!-- 文档浏览器 -->
        <CompsDocBrowser
        v-if="status_docbrowser"
        :init_zIndex="1002"
        :init_width="'800px'"
        :init_height="'640px'"
        :init_bimcomposerId="_bimcomposerId"
        init_title="请选择关联文档"
        @onclose="evt_docbrowserclose"
        @onok="evt_docbrowseronok"
        ></CompsDocBrowser>
        <!-- //文档浏览器 -->

        <!-- 关联模型 -->
        <CompsSelectModel
        v-if="M_selectmodel.visible"
        :data="M_selectmodel"
        @getModelInfo="evt_modelselected"
        @close="M_selectmodel.visible = false"
        ></CompsSelectModel><!-- //关联模型 -->

        <CompsSimpleSearchList
        v-if="status_showmemberadd"
        @oncancel="status_showmemberadd = false"
        :title="'添加审批人'"
        :projectId="$staticmethod._Get('organizeId')"
        showType="commonPjMember"
        :zIndex="1002"
        @onok_multiple="evt_OK_Multiple"
        :willnotshowUsers="m_members"
        :init_ismultiple="true"
        ></CompsSimpleSearchList>

        <div class="_css-flownode-content">

            <!-- 流程名称显示区域  -->
          
            <div 
            v-show="m_flowactivetab == 'basic'"
            class="_css-line ">
                <div class="_css-title _css-title-flowname" data-debug="fn-1" >
                流程名称：
                </div> 
                <div class="_css-fieldvalue css-common-fieldvaluename
                _css-notallowouter  css-mr24 css-ml24
                ">
                    <input readonly="readonly"
                    type="text" class="css-common-fieldvaluename-in
                    _css-notallowinner"
                    :value="m_nodeshowingdata.wfi.wfi_title"
                    >
                </div>
            </div>
            <!-- //流程名称显示区域  -->

            <!-- 审批人不是在这里编辑的-->
            <div v-if="false"
            class="_css-line css-common-line">
                <div class="_css-title _css-title-flowname">
                    审批人：
                </div>
                <div class="_css-fieldvalue css-common-fieldvaluename _css-member-ctn">
                    <div 
                    :title="usel.RealName + '(' + usel.Email + ')'"
                    v-for="usel in m_members"
                    :key="usel.UserId"
                    class="_css-member-seli">
                        <div class="_css-member-name" >{{usel.RealName}}</div>
                     
                    </div>
                    <div 
                 
                </div>
            </div>
            <!-- 审批人不是在这里编辑的 -->

            <!-- 说明&amp;意见 -->
            <div 
            v-show="m_flowactivetab == 'basic'"
            class="_css-line ">
                <div class="_css-title _css-title-flowname">
                说明&amp;意见：
                </div> 
                <div class="_css-notallowouter _css-fieldvalue css-common-fieldvaluename _css-textareavalue css-mr24 css-ml24">
                    <textarea readonly="readonly" v-model="m_nodeshowingdata.wfr_comment"
                    class="_css-notallowinner css-common-fieldvaluename-in _css-teatarea-in css-miniscroll css-globalfont"></textarea>
                </div>
            </div>
            <!-- 说明&amp;意见 -->

            <!-- 已关联表单 -->
            <div 
            v-if="m_formattachedid"
            v-show="m_flowactivetab == 'basic'"
            class="_css-line  _css-common-line-form">
                <!-- <div  class="_css-title _css-title-flowname">
                已关联表单：
                </div> -->
                <div 
                v-loading="status_formloading"
                element-loading-text="加载中"
                    class="_css-fieldvalue _css-formpagectn _css-textareavalue _css-closehoverctn">
                    <!-- <div v-if="m1_currentsubmitable"
                    @click="func_removeform($event)"
                    class="icon-suggested-close _css-formadd-closebtn"></div> -->
                    <iframe 
                    id="id_form_reading_comps"
                    :src="m_formurl"
                    class="_css-form-inputting"></iframe>
                </div>
            </div>
            <!-- 未关联模型和（关联对象）的情况 -->
            <div 
            v-else
            v-show="m_flowactivetab == 'form'"
             class="_css-line  ">
                <div class="_css-title _css-title-flowname">
                    (未设置表单)
                </div>
            </div>
            <!-- //未关联模型和（关联对象）的情况 -->
            <!-- 已关联表单 -->

            <!-- 已关1联模型 -->
            <!-- <div 
            v-if="m_formmodelselectedObj"
            class="_css-line css-common-line ">
                <div class="_css-title _css-title-flowname">
                已关1联模型：
                </div>
                <div 
                @click="func_previewmodel(m_formmodelselectedObj.modelid)"
                class="_css-flowModelNameSelected _css-closehoverctn">
                    <div class="_css-nameshow-attached">{{m_formmodelselectedObj.modelname}}</div>
                        <div v-if="m1_currentsubmitable"
                        @click="func_removemodel($event)"
                        class="icon-suggested-close _css-formadd-closebtn _css-static _css-flowmodelname-remove">
                    </div>
                </div>
            </div> -->

            <!-- 未关联模型和（关联对象）的情况 -->
            <!-- <div 
               v-show="m_flowactivetab == 'basic'"
            v-if="(!m_formmodelselectedObj || !m_formmodelselectedObj.length)
            && (!m_formdocselectedObj || !m_formdocselectedObj.length)
            "
             class="_css-line css-common-line ">
                <div class="_css-title _css-title-flowname">
                    (无关联对象)
                </div>
            </div> -->
            <!-- //未关联模型和（关联对象）的情况 -->

            <div 
            v-show="m_flowactivetab == 'basic'"
            class="_css-attachment-label">
                <div class="_css-title _css-title-flowname">
                附件：
                </div> 
            </div>            

            <div class="_css-nodeinfo-modelanddoc">
                <div class="_css-nodeinfo-model">

                   

                    <!-- 模型 -->
                    <template
                    v-if="m_formmodelselectedObj && m_formmodelselectedObj.length"
                    >
                        <div 
                        v-for="(themodel, index) in m_formmodelselectedObj"
                        :key="themodel.modelid"
                            v-show="m_flowactivetab == 'basic'"
                        class="css-common-line ">
                            
                            <!-- <div 
                            class="_css-title _css-title-flowname"
                            :class="{'_css-hidden': index != 0}"
                            >
                            已关联模型：
                            </div> -->
                            <div 
                            @click="func_previewmodel(themodel.modelid)"
                            class="_css-flowModelNameSelected _css-closehoverctn">
                                <div class="_css-addattach-icon icon-dl-model"></div>
                                <div class="_css-modelname-show" >{{themodel.modelname}}</div>
                                <div class="_css-removebtn-ctn" >
                                 
                                </div>
                                
                            </div>

                        </div>
                    </template>
                    <!-- 已关联模型 -->

                   
                </div>
                <div class="_css-nodeinfo-doc">

                  

                    <template
                    v-if="m_formdocselectedObj && m_formdocselectedObj.length"
                    >
                        <div 
                        v-for="(thedoc, index) in m_formdocselectedObj"
                        :key="thedoc.FileId"
                        v-show="m_flowactivetab == 'basic'"
                        class="css-common-line ">
                            <!-- <div class="_css-title _css-title-flowname"
                            :class="{'_css-hidden': index != 0}">
                            已关联文档：
                            </div> -->
                            <div 
                            @click="func_previewdoc(thedoc.FileId, thedoc.FileName)"
                            class="_css-flowModelNameSelected _css-closehoverctn">
                            <div class="_css-addattach-icon "
                            :class="$staticmethod.getIconClassByExtname(thedoc.FileName, 1)"
                            ></div>
                            <div class="_css-modelname-show">{{thedoc.FileName}}</div>
                            <div class="_css-removebtn-ctn" >
                                 
                            </div>
                            
                            </div>
                        </div>
                    </template>
                    <!-- 已关联文档 -->

                    <!-- 添加模型、添加关联文档 -->
                
                  
                    <!-- //添加模型、添加关联文档 -->
                </div>
            </div>

            



            <!-- 任务与构件部分 -->
            <div class="_css-nodeinfo-modelanddoc">
                <div class="_css-nodeinfo-model">

               

                    <!-- 模型 -->
                    <template
                    v-if="m_formtasksselectedObj && m_formtasksselectedObj.length"
                    >
                        <div 
                        v-for="(themodel, index) in m_formtasksselectedObj"
                        :key="themodel.UID_"
                            v-show="m_flowactivetab == 'basic'"
                        class="css-common-line ">
                            
                            <!-- <div 
                            class="_css-title _css-title-flowname"
                            :class="{'_css-hidden': index != 0}"
                            >
                            已关联模型：
                            </div> -->
                            <div 
                            class="_css-flowModelNameSelected _css-closehoverctn">
                                <div class="_css-addattach-icon icon-dl-model"></div>
                                <div class="_css-modelname-show" >{{themodel.NAME_}}</div>
                                <div class="_css-removebtn-ctn" >
                                
                                </div>
                                
                            </div>

                        </div>
                    </template>
                    <!-- 已关联模型 -->

                    
                </div>
                <div class="_css-nodeinfo-doc">

              

                    <template
                    v-if="m_formmaterialselectedObj && m_formmaterialselectedObj.length"
                    >
                        <div 
                        v-for="(thedoc, index) in m_formmaterialselectedObj"
                        :key="thedoc.bm_guid"
                        v-show="m_flowactivetab == 'basic'"
                        class="css-common-line ">
                            <!-- <div class="_css-title _css-title-flowname"
                            :class="{'_css-hidden': index != 0}">
                            已关联文档：
                            </div> -->
                            <div 
                            class="_css-flowModelNameSelected _css-closehoverctn">
                            <div class="_css-addattach-icon icon-interface-associated-component"
                        
                            ></div>
                            <div class="_css-modelname-show">{{thedoc.bm.bm_materialname}}</div>
                            <div class="_css-removebtn-ctn" >
                                  
                            </div>
                            
                            </div>
                        </div>
                    </template>
                    <!-- 已关联文档 -->
                    
                </div>
            </div>
            <!-- //任务与构件部分 -->




            

            <!-- 已关联文档 -->
            <!-- <div 
            v-if="m_formdocselectedObj"
            class="_css-line css-common-line ">
                <div class="_css-title _css-title-flowname">
                已关联文档：
                </div>
                <div 
                @click="func_previewdoc(m_formdocselectedObj.FileId, m_formdocselectedObj.FileName)"
                
                class="_css-flowModelNameSelected _css-closehoverctn">
                    <div class="_css-nameshow-attached">{{m_formdocselectedObj.FileName}}</div>
                    <div v-if="m1_currentsubmitable"
                    @click="func_removedoc($event)"
                    class="icon-suggested-close _css-formadd-closebtn _css-static">
                    </div>
                </div>
            </div> -->
          



            <!-- 表格显示和图的显示 -->
            <div class="_css-flowskeleton-ctn"
            element-loading-text='加载中'
            v-if="m_flowactivetab == 'skeleton'"
            v-loading="m_skeletonisloading"
            >
                <iframe class="_css-flowskeleton"
                @load="evt_previewloaded()"
                id="id_ifr_preview" :src="m_url"></iframe>
            </div>

            <div class="_css-flowdetail-tabs-content"
            v-loading="m_logisloading"
            v-show="m_flowactivetab == 'operlogs'"
            element-loading-text='加载中'
            >
                <el-table
                ref="doctable"
                :highlight-current-row="false"
                :border="true"
                :stripe="false"
                :data="m_logdata"
                style="width: 100%"
                :default-sort="{prop: 'date', order: 'descending'}"
                height="500"
                class="_css-table-ele _css-table-ele2 css-scroll _css-customstyle _css-table-operlogs"
                :row-class-name="tableRowClassName"
                :header-cell-style="{'background-color':'transparent'}"
                >
                    <el-table-column
                    :resizable="false"
                    width="120"
                    prop="wfr_createtimestr"
                    label="时间"
                    ></el-table-column>
                    <el-table-column
                    :resizable="false"
                    min-width="80"
                    prop="nodeText"
                    label="节点名称"
                    ></el-table-column>
                    <el-table-column
                    :resizable="false"
                    min-width="60"
                    prop="wfr_senderbu"
                    label="操作者"
                    ></el-table-column>
                    <el-table-column
                    :resizable="false"
                    min-width="40"
                    prop="oper_type"
                    label="操作"
                    ></el-table-column>
                    <el-table-column
                    :resizable="false"
                    min-width="120"
                    label="说明&amp;意见"
                    class="_css-celllongcolumn"
                    >
                        <template slot-scope="scope">
                             <el-tooltip
                                popper-class="css-no-triangle"
                                effect="dark"
                                :content="scope.row.wfr_comment"
                                placement="left"
                            >
                                <div class="_css-celllongdiv">{{scope.row.wfr_comment}}</div>
                            </el-tooltip>
                        </template>
                    </el-table-column>
                </el-table>
            </div>
            <!-- //表格显示和图的显示 -->










        </div>

        <div class="_css-flownode-foot">
           
         

            <!-- 固定添加一个查看详情 -->
            <!-- <div v-if="false"
            @click="func1_ondetail($event)"
            class="_css-flowinstance-detail-link">查看详情</div> -->
            <!-- //固定添加一个查看详情 -->

        </div>

    </div>    
</template>
<script>
import CompsSimpleSearchList from "@/components/CompsCommon/CompsSimpleSearchList"
import CompsSelectModel from "@/components/CompsMaterial/CompsSelectModel"
import CompsDocBrowser from '@/components/CompsDocument/CompsDocBrowser'
import CompsMaterialBrowser from '@/components/CompsCommon/CompsMaterialBrowser'
import CompsTaskBrowser from '@/components/CompsCommon/CompsTaskBrowser'
import ProjectBootVue from '../Home/ProjectBoot.vue'
export default {
    components:{
        CompsSimpleSearchList,
        CompsSelectModel,
        CompsDocBrowser,
        CompsMaterialBrowser,
        CompsTaskBrowser
    },
    data() {
        return {

            // 流程图显示地址
            // -------------
            m_url:'',

            // oper log
            // --------
            m_logdata: [],

            // 查看流程时：当前激活的tab basic, form, attachments, skeleton, operlogs
            // ----------------------------------------------------------
            m_flowactivetab:'',

            // 查看流程时，操作记录的 表格 是否处于 loading 状态
            // ----------------------------------------------
            m_logisloading: false,
            m_skeletonisloading: false,

            _width:'',
            _height:'',
            _bgcolor:'',

            // 选中的流程实例数据
            // -----------------
            _selectedObj:'',

            _bimcomposerId:'',

            // 节点操作记录展示 div 样式
            // -----------------------
            m_nodeinfo_left: 0,
            m_nodeinfo_top: 0,
            status_nodeinfo_show: 0,
            m_nodelogloading: false,
            m_currentnodelogs: [],

            // 鼠标是否处于弹出的节点操作记录 div 内
            // ----------------------------------
            m_isintip: false,

            // 最后一次 timeout 句柄
            // --------------------
            h_lasttimeout: 0,
            h_nodedivtip: 0,

            // 与 CompsCorrelationModel 组件相关的 data
            // ---------------------------------------
            M_selectmodel: {
                visible: false,
                dataSource: [],
                projectID: '',
                fileMsg: [],
                CreateUserName: '',
            },

            m_formurl:'',
            //m1_currentsubmitable: true,
            //m1_currentapprovable: false,
            m_flowname:'',
            m_nodeshowingdata: {
                wfi:{
                    wfi_title: undefined
                },
                docobj:undefined,
                wfr_comment:undefined
            },
            m_members:[],
            m_flowcomment:undefined,
            m_formattachedid:undefined,
            m_formmodelselectedObj:undefined,
            m_formdocselectedObj:undefined,

            // 发起流程中，已经选中的构件
            // 已经选中的任务
            // -------------
            m_formmaterialselectedObj: undefined,
            m_formtasksselectedObj: undefined,

            m_nodeid:undefined,
            m_wfiguid:undefined,

            status_formloading: false,
            status_showmemberadd: false,

            m_loadingPtr: undefined,
            m_registedMsg: false,
            m_flowsubmittype:'',

            // 是否显示文档浏览对话框
            // --------------------
            status_docbrowser: false,
            status_materialbrowser: false,
            status_taskbrowser: false

            
        };
    },
    created(){
        var _this = this;
        
       
    },
    mounted(){

        // !测试值，固定了表单ID
        // --------------------
        var _this = this;
        window.flowreadonlyvue = this;

        // 调用接口，得到 _selectedObj 的值，并调用 func_onmounted
        // -----------------------------------------------------
        var _url = `${window.bim_config.webserverurl}/api/FlowForm/Flow/GetFlowInstanceNew?wfi_guid=${_this.init_selectedWfiGuid}`; 
        _this.$axios.get(_url).then(x => {
            if (x.data.Ret > 0) {
                _this._selectedObj = x.data.Data.wfi;
                _this.func_onmounted();
            } else {
                _this.$message.error('获取数据失败');
            }
        }).catch(x => {
            console.error(x);
        });


        // setTimeout(() => {

        //     //_this._selectedObj = {"UserId":"8a0f9c1c-05d7-4968-ab7e-47c434fd78c9","RealName":"薛友松","wfr_objs":[{"wfr_guid":"3fdb18bb-d65d-49fd-a299-84a0eb1d5494","wfi_guid":"5e25f69e-1d76-4bbd-83c0-1962f332f564","wfr_recordnum":1,"wfr_nodeId":"y915t7h8ro","wfr_stayStatus":1,"wfr_formjson":"{\"input_1591257480037\":\"2\",\"rate_1591270890099\":2,\"rate_1591270890459\":3,\"input_1591257480197\":\"\",\"time_1591257482325\":\"\",\"select_1591257483013\":\"\"}","wfr_comment":"1","wfr_createtime":"2020-07-06T17:46:24","wfr_sender":"8a0f9c1c-05d7-4968-ab7e-47c434fd78c9"}],"wfs_guid":"36befc63-a490-11ea-a0d8-fa163e36b61c","wfs_json":"{\r\n    \"name\": \"流程B\",\r\n    \"nodeList\": [\r\n        {\r\n            \"id\": \"y915t7h8ro\",\r\n            \"name\": \"开始3\",\r\n            \"type\": \"timer\",\r\n            \"left\": \"94px\",\r\n            \"top\": \"264px\",\r\n            \"ico\": \"icon-interface-set_se\",\r\n            \"state\": \"success\"\r\n        },\r\n        {\r\n            \"id\": \"jrghudxpvm\",\r\n            \"name\": \"审批\",\r\n            \"type\": \"end\",\r\n            \"left\": \"294px\",\r\n            \"top\": \"263px\",\r\n            \"ico\": \"icon-interface-log\",\r\n            \"state\": \"success\"\r\n        },\r\n        {\r\n            \"id\": \"akr5uqkjvi\",\r\n            \"name\": \"结束333\",\r\n            \"type\": \"task\",\r\n            \"left\": \"698px\",\r\n            \"top\": \"260px\",\r\n            \"ico\": \"icon-interface-clan\",\r\n            \"state\": \"success\"\r\n        },\r\n        {\r\n            \"id\": \"bzkmzajauj\",\r\n            \"name\": \"审批1\",\r\n            \"type\": \"end\",\r\n            \"left\": \"496px\",\r\n            \"top\": \"262px\",\r\n            \"ico\": \"icon-interface-log\",\r\n            \"state\": \"success\"\r\n        }\r\n    ],\r\n    \"lineList\": [\r\n        {\r\n            \"from\": \"y915t7h8ro\",\r\n            \"to\": \"jrghudxpvm\"\r\n        },\r\n        {\r\n            \"from\": \"jrghudxpvm\",\r\n            \"to\": \"bzkmzajauj\"\r\n        },\r\n        {\r\n            \"from\": \"bzkmzajauj\",\r\n            \"to\": \"akr5uqkjvi\"\r\n        }\r\n    ]\r\n}","currentNode":{"id":"y915t7h8ro","name":"开始3","type":"timer","left":"94px","top":"264px","ico":"icon-interface-set_se","state":"success"},"UpdateTime":"2020-07-06T17:46:24","UpdateTimeStr":"2020-07-06 17:46","wft_name":"流转测试234","rfm_list":null,"nextApproverIds":"8a0f9c1c-05d7-4968-ab7e-47c434fd78c9","IsPassed":false,"nextNode":{"id":"jrghudxpvm","name":"审批","type":"end","left":"294px","top":"263px","ico":"icon-interface-log","state":"success"},"wfi_guid":"5e25f69e-1d76-4bbd-83c0-1962f332f564","wfi_organizeId":"48617e7b-07f2-4748-9199-238af8f2bfc6","wft_guid":"0b6d4eb1-18dd-401b-83f4-43dc3fc97a00","wfi_title":"直接提交测试1745","wfi_approvers":null,"wfi_formjson":"{\"input_1591257480037\":\"2\",\"rate_1591270890099\":2,\"rate_1591270890459\":3,\"input_1591257480197\":\"\",\"time_1591257482325\":\"\",\"select_1591257483013\":\"\"}","wfi_sender":"8a0f9c1c-05d7-4968-ab7e-47c434fd78c9","wfi_model":null,"wfi_doc":null,"wfr_guid":"3fdb18bb-d65d-49fd-a299-84a0eb1d5494","wf_guid":"301dd5e1-71a0-4218-a93d-c25d612f1268"};
            
        //     // 初始化
        //     _this.func_onmounted();
        //     // //初始化

        // }, 500);
        
    },
    methods: {

        // 前提是 _selectedObj 有值
        // -----------------------
        func_onmounted() {
            var _this = this;

            _this.m_nodeid = _this._selectedObj.currentNode.id;
            _this.m_wfiguid = _this._selectedObj.wfi_guid;

            _this._width = _this.init_width;
            _this._height = _this.init_height;
            _this._bgcolor = _this.init_bgcolor;
            _this._bimcomposerId = _this.init_bimcomposerId;

            _this.m_flowactivetab = 'basic';

            // // 如果没注册过消息，注册消息
            // // ------------------------
            // window.onmessage = function(e) {
            //     _this.func_processmessage(e.data);
            // };
            
            // 初始化内部的静态值（流程标题等）和动态值（流程流转数据）
            // --------------------------------------------------
            _this.func_initfields();

            // 显示整体 loading
            // ----------------
            _this.func_showloading(true);

            // 获取操作日志数据
            // ---------------
            _this.func_getlogs(_this.m_wfiguid);
        },

        func_tasksmerge(arr) {
            var _this = this;
            if (!_this.m_formtasksselectedObj || !_this.m_formtasksselectedObj.length) {
                _this.m_formtasksselectedObj = [];
            }
            for (var i = 0; i < arr.length; i++) {
                var indexThis = _this.m_formtasksselectedObj.findIndex(x => x.UID_ == arr[i].UID_);
                if (indexThis < 0) {
                _this.m_formtasksselectedObj.push(arr[i]);
                }
            }
        },

        func_removetask(ev, id) {
            var _this = this;
            ev && ev.stopPropagation && ev.stopPropagation();
            _this.$confirm("确认移除关联任务?", "操作确认").then(x => {
                _this.m_formtasksselectedObj = _this.m_formtasksselectedObj.filter(x => x.UID_ != id);
            });
        },

        evt_taskbrowseronok(arr) {
            console.log(arr);
            var _this = this;
            if (!arr.length) {
                _this.$message.warning('未选中任务');
                return;
            }
            _this.func_tasksmerge(arr);
            _this.status_taskbrowser = false;
        
        },

        evt_taskbrowserclose() {
            var _this = this;
            _this.status_taskbrowser = false;
        },

        // 确保 m_formmaterialselectedObj 是个有效数组，并与 arr 数组合成并集
        // ---------------------------------------------------------------
        func_materialsmerge(arr) {
            var _this = this;
            if (!_this.m_formmaterialselectedObj || !_this.m_formmaterialselectedObj.length) {
                _this.m_formmaterialselectedObj = [];
            }
            for (var i = 0; i < arr.length; i++) {
                var indexThis = _this.m_formmaterialselectedObj.findIndex(x => x.bm && x.bm.bm_guid == arr[i].bm_guid);
                if (indexThis < 0) {
                    _this.m_formmaterialselectedObj.push({
                        bm: arr[i]
                    });
                }
            }
        },

        // 关闭构件浏览对话框点击确定后
        // -------------------------
        evt_materialbrowseronok(arr) {
            var _this = this;
            if (!arr.length) {
                _this.$message.warning('未选中构件');
                return;
            }

            // 确保 m_formmaterialselectedObj 是个有效数组，并与 arr 数组合成并集
            // --------------------------------------------------------------
            _this.func_materialsmerge(arr);

            // debugger;
            // _this.m_formdocselectedObj = fileobj[0];
            _this.status_materialbrowser = false;

        },

        evt_materialbrowserclose() {
            var _this = this;
            _this.status_materialbrowser = false;
        },

        getOrganizeId() {
            return this.$staticmethod._Get("organizeId");
        },

        getBIMComposerId() {
            return this.$staticmethod._Get("bimcomposerId");
        },

        // 先判断某模块有无权限
        // ------------------
        pre_testauth(ev, bmcode, successcb, modulechname) {
            var _this = this;
            var _Token = _this.$staticmethod.Get("Token");
            var _OrganizeId = _this.$staticmethod._Get("organizeId");
            var _bm_encode = bmcode;
            var _bmb_encode = 'lr-visible';
            var _url = `${window.bim_config.webserverurl}/api/User/Role/TestTokenSomeAuth?Token=${_Token}&OrganizeId=${_OrganizeId}&bm_encode=${_bm_encode}&bmb_encode=${_bmb_encode}`;
            _this.$axios.get(_url).then(x => {
                if (x.data.Ret > 0) {
                if (x.data.Data == 1) {
                    successcb();
                } else {
                    _this.$message.error('当前用户无'+modulechname+'权限');
                }

                } else {
                _this.$message.error(`判断权限出错：${x.data.Msg}`);
                }
            }).catch(x => {
                console.error(x);
            });
        },

        // 关联文档，先判断有无权限
        // ----------------------
        pre_adddoc(ev) {
            var _this = this;
            _this.pre_testauth(ev, 'Document', ev => {
                _this.func_adddoc(ev);
            }, '文档管理');
        },

        // 关联构件，先判断有无权限
        // ----------------------
        pre_addmaterial(ev) {
            var _this = this;
            _this.pre_testauth(ev, 'MaterialsMgr', ev => {
                _this.func_addmaterial(ev);
            }, '构件管理');
        },

        // 关联模型，先判断有无权限
        // ----------------------
        pre_addmodel(ev) {
            var _this = this;
            _this.pre_testauth(ev, 'BIMModel', ev => {
                _this.func_addmodel(ev);
            }, '模型管理');
            },

        // 关联任务，先判断有无权限
        // ----------------------
        pre_addtasks(ev) {
            var _this = this;
            _this.pre_testauth(ev, 'CompsProgress', ev => {
                _this.func_addtask(ev);
            }, '进度管理');
        },

        // 发起流程时，点击添加任务
        // ----------------------
        func_addtask(ev) {
            var _this = this;
            _this.status_taskbrowser = true;
        },

        // 发起流程时，点击添加构件
        // ----------------------
        func_addmaterial(ev) {
            var _this = this;
            _this.status_materialbrowser = true;
        },

        func_removematerial(ev, id) {
            var _this = this;
            ev && ev.stopPropagation && ev.stopPropagation();
            _this.$confirm("确认移除该构件?", "操作确认").then(x => {
                _this.m_formmaterialselectedObj = _this.m_formmaterialselectedObj.filter(x => x.bm && x.bm.bm_guid != id);
            });
        },

        // iframe 加载完成
        // ---------------
        evt_previewloaded() {

            // 先注册消息 onmessage =
            // 再关闭 loading
            // -------------
            var _this = this;
            _this.m_skeletonisloading = false;

        },

        func_getlogs(wfi_guid) {

            // 请求数据
            // -------
            var _this = this;
            _this.m_logisloading = true;
            var _url = `${window.bim_config.webserverurl}/api/FlowForm/Flow/GetFlowInstanceOperLogs?wfi_guid=${wfi_guid}`;
            _this.$axios.get(_url).then(x => {
                _this.m_logdata = x.data.Data.List;
                console.log('流程操作日志数据已获取到！');
                _this.$emit('onloggot', _this.m_logdata);
                _this.m_logisloading = false;
            }).catch(x => {
                console.error(x);
                _this.m_logisloading = false;
            });
        },

        // 根据是否选中了某一行，返回类字符串
        // -------------------------------
        tableRowClassName({ row, rowIndex }) {
            // var _this = this;
            // var _sel_hasThisRow =
            //     _this.m_selectedfiles.filter(x => x.FileId == row.FileId).length >
            //     0;
            // return "css-tdunder " + (_sel_hasThisRow ? "css-tabrow-selected" : "");
            return 'css-tdunder';
        },

        func_setactivetab(activetab) {
            var _this = this;
            _this.m_flowactivetab = activetab;

            //
            _this.$nextTick(()=>{
                if (activetab == 'operlogs') {

                    // 加载logs
                    // var _targetsrc = _this.func_getSkeletonShowUrl(_this.m_flowskelectonid);
                    // document.getElementById("id_iframe_creatingflowSkeleton").setAttribute('src', _targetsrc);
                    // ------------------------------------------------------------------------------------------
                    _this.func_getlogs(_this.m_wfiguid);
                }
                if (activetab == 'skeleton') {
                    _this.evt_onpreviewloading();
                }
            }); 
        
        },

        // 如果切换标签后的标签为 preview
        // 则需要做一系列处理
        // ----------------
        evt_onpreviewloading() {

            // 设置 iframe 的 src
            // ------------------
            var _this = this;

            // 先显示 loading
            // --------------
            _this.m_skeletonisloading = true;

            // 再设置地址
            // ---------
            var  _currentNodeId = _this.m_nodeid;

            //var _url = `${window.bim_config.integrated_innerview}/A1ddons/Module_flow/?devmode=0&wfs_guid=36befc63-a490-11ea-a0d8-fa163e36b61c&currentNodeId=${_currentNodeId}`;
            var _url = _this.$staticmethod.getFlowUrl(true
                , 0, '36befc63-a490-11ea-a0d8-fa163e36b61c', _currentNodeId, '', '');
            
            _this.m_url = _url;

        },

        // 打开模型
        // --------
        func_previewmodel(modelid) {
            var _this = this;
            _this.$emit("onmodelclick", modelid);
        },

        // 预览文档
        // --------
        func_previewdoc(FileId, FileName) {
            var _this = this;
            _this.$emit("ondocclick", {
                FileId: FileId, FileName: FileName
            });
        },

        func_getnltitle(nl) {
            var _this = this;
            var retstr = `【${nl.wfr_createtimestr}】【${nl.wfr_senderbu}】${nl.oper_type}到该节点，${nl.oper_type == '提交'?'备注':'处理意见'}为【${nl.wfr_comment}】`;
            return retstr;
        },

        // 从节点log的div移出鼠标，取消之前句柄，并标记 isintip 为 true
        // ---------------------------------------------------------
        evt_nodelogover() {
            var _this = this;
            _this.m_isintip = true;
            if (_this.h_nodedivtip) {
                clearTimeout(_this.h_nodedivtip);
                _this.h_nodedivtip = 0;
            }
        },

        // 从节点log的div移出鼠标，延迟500ms隐藏这个div，并标记 isintip 为 false
        // -----------------------------------------------------------------
        evt_nodelogout() {
            var _this = this;
            _this.h_nodedivtip = setTimeout(()=>{
                _this.status_nodeinfo_show = false;
                _this.m_isintip = false;
            }, 500);
            //_this.status_nodeinfo_show = false;
        },

        // 展示节点操作记录的 div 样式
        // -------------------------
        style_nodetip() {
            var _this = this;
            var _s = {};
            _s["left"] = _this.m_nodeinfo_left + 'px';
            _s["top"] = _this.m_nodeinfo_top + 'px';
            return _s;
        },

        // // 查看详情
        // // --------
        // func1_ondetail(ev) {
        //     var _this = this;
        //     _this.$message.warning('error');
        //     _this.$emit("on1detail");
        // },

        // 文档库选择后
        // -----------
        evt_docbrowseronok(fileobj) {
            // debugger;
            // var _this = this;
            // if (fileobjs.length == 0) {
            //     _this.$message.warning('未选中文件');
            //     return;
            // }
            // if (fileobjs[0].FileSize == 0) {
            //     _this.$message.warning('无法关联文件夹，请重新选择');
            //     return;
            // }

            // // 文档选择后，添加到右侧展示区
            // // --------------------------
            // var tempobj = _this.$staticmethod.DeepCopy(fileobjs[0]);
            // _this.status_docbrowser = false;
            // _this.$nextTick(() => {
            //     _this.m_formdocselectedObj = tempobj;
            // });

            var _this = this;

            if (!fileobj.length) {
                _this.$message.warning('未选中文件');
                return;
            }

            // 判断是否是正确的文档，而不是文件夹
            // -------------------------------
            // if (fileobj[0].FileSize == 0) {
            //   _this.$message.warning('无法关联文件夹，请重新选择');
            //   return;
            // }

            var folders = fileobj.filter(x => x.FileSize == 0);
            if (folders.length > 0) {
                var _strFolderNames = '';
                for (var i =0; i < folders.length; i++) {
                if (folders[i].FileName && folders[i].FileName != '') {
                    _strFolderNames += `${folders[i].FileName},`;
                }
                }
                if (_strFolderNames.lastIndexOf(',') == _strFolderNames.length - 1) {
                _strFolderNames = _strFolderNames.substr(0, _strFolderNames.length - 1);
                }
                _this.$message.warning(`请取消选中文件夹：【${_strFolderNames}】后重新操作`);
                return;
            }

            // 将其过滤为仅文件
            // --------------
            fileobj = fileobj.filter(x => x.FileSize != 0);

            // 确保 m_formdocselectedObj 是个有效数组，并与 fileobj 数组合成并集
            // --------------------------------------------------------------
            _this.func_docsmerge(fileobj);
            // debugger;
            // _this.m_formdocselectedObj = fileobj[0];
            _this.status_docbrowser = false;
        },

        // 确保 m_formdocselectedObj 是个有效数组，并与 fileobj 数组合成并集
        // --------------------------------------------------------------
        func_docsmerge(filearr) {
            var _this = this;
            if (!_this.m_formdocselectedObj || !_this.m_formdocselectedObj.length) {
                _this.m_formdocselectedObj = [];
            }
            for (var i = 0; i < filearr.length; i++) {
                var indexThis = _this.m_formdocselectedObj.findIndex(x => x.FileId == filearr[i].FileId);
                if (indexThis < 0) {
                _this.m_formdocselectedObj.push(filearr[i]);
                }
            }

            // _this.$nextTick(()=>{
            //     _this.func_setflowcreatetop();
            // });
        },

        // 隐藏文档库
        // ---------
        evt_docbrowserclose() {
            var _this = this;
            _this.status_docbrowser = false;
        },

        // type 为 1 表示保存草稿，2 为提交
        // type 为 disapprove 为 驳回， approve 为审批通过
        // --------------------------------------------
        func_submitflow(type) {
            var _this = this;
            _this.m_flowsubmittype = type;

            // 这里也需要判断是否有表单的 iframe
            // -------------------------------
            _this.func_getformdata();
        },

        // 获取表单里的数据
        // ---------------
        func_getformdata() {
            var _this = this;

            // 如果是从节点保存，则 ifrm 为另一个
            // -------------------------------
            var ifrm;
            if (_this.m_nodeshowingdata) {
                ifrm = document.getElementById("id_form_reading_comps");
            }

            if (ifrm) {
                var innerWin = ifrm.contentWindow;
                innerWin.postMessage({act:'reqToFormBuilder_getdynamicdatas'}, "*");
            } else {

                // 直接进行表单内容提交
                // ------------------
                _this.do_submitflow();
            }
        
        },

        do_submitflow(ifhasdata){
            var _this = this;

            // 公共参数前移
            // -----------
            var _Token = _this.$staticmethod.Get("Token");
            var _OrganizeId = _this.$staticmethod._Get("organizeId");
            var _wft_guid = _this.m_flowtselected;
            var _wfi_title = _this.m_flowname;
            var _wfi_comment = _this.m_flowcomment;
            var _wfi_approvers = _this.m_members.map(x => x.UserId).join(',');
            var _stayStatus = _this.m_flowsubmittype;
            var _wf_guid = _this.m_formattachedid;
            if (!_this.m_formmodelselectedObj || !_this.m_formmodelselectedObj.length) {
                _this.m_formmodelselectedObj = [];
            }
            var _wfi_model = JSON.stringify(_this.m_formmodelselectedObj);
            if (!_this.m_formdocselectedObj || !_this.m_formdocselectedObj.length) {
                _this.m_formdocselectedObj = [];
            }
            var _wfi_doc = JSON.stringify(_this.m_formdocselectedObj);

            // 需要补充构件及任务数据
            // --------------------
            var _wfi_material = '[]';
            if (_this.m_formmaterialselectedObj && _this.m_formmaterialselectedObj.length) {
                var onlybm = [];
                for (var i = 0; i < _this.m_formmaterialselectedObj.length; i++) {
                    onlybm.push(_this.m_formmaterialselectedObj[i].bm);
                }
                _wfi_material = JSON.stringify(onlybm);
            }

            // 任务数据
            // -------
            var _wfi_task = '[]';
            if (_this.m_formtasksselectedObj && _this.m_formtasksselectedObj.length) {
                _wfi_task = JSON.stringify(_this.m_formtasksselectedObj);
            }

            // 表单填写的内容直接序列化为字符串
            // -----------------------------
            var formjson = '';
            if (ifhasdata) {
                formjson = JSON.stringify(ifhasdata);
            }
            var _wfi_formjson = formjson;

            var _postdata = {
                "Token": _Token,
                "OrganizeId": _OrganizeId,
                "wft_guid": _wft_guid,
                "wfi_title": _wfi_title,
                "wfi_comment": _wfi_comment,
                "wfi_approvers": _wfi_approvers,
                "wfi_formjson": _wfi_formjson,
                "wfi_model": _wfi_model,
                "wfi_doc": _wfi_doc,
                "stayStatus": _stayStatus,
                "wf_guid": _wf_guid,
                "wfi_material" : _wfi_material,
                "wfi_task": _wfi_task
            };

            // 这里除了考虑 ifhasdata，还要判断是节点保存还是新增保存
            // --------------------------------------------------
            //deb1ugger;
            if (_this.m_nodeshowingdata) {
            
                // 补充字段
                // -------
                _postdata["wfi_guid"] = _this.m_nodeshowingdata.wfi_guid;
                _postdata["wfr_guid"] = _this.m_nodeshowingdata.wfr_guid;

                // 这里要判断是审批驳回操作，还是草稿保存提交操作
                // ------------------------------------------
                if (_this.m_flowsubmittype == 'disapprove'
                || _this.m_flowsubmittype == 'approve') {

                    // 审批或驳回
                    // ---------
                    _postdata["oper_type"] = _this.m_flowsubmittype;

                    // 修复 comment
                    // ------------
                    _postdata["wfi_comment"] = _this.m_nodeshowingdata.wfr_comment;

                    _this.func_approve(_postdata);
                    return;

                } else {

                    // 从开始节点进行保存或提交
                    // ----------------------
                    //debu1gger;
                    _this.func_submitfromnode(_postdata);
                    return;
                }
            
            }

            // 准备参数
            // -------
            // var _Token = _this.$staticmethod.Get("Token");
            // var _OrganizeId = _this.$staticmethod._Get("organizeId");
            // var _wft_guid = _this.m_flowtselected;
            // var _wfi_comment = _this.m_flowcomment;
            // var _wfi_approvers = _this.m_members.map(x => x.UserId).join(',');
            //var _wfi_formjson = formjson;
            // var _wfi_model = _this.m_formmodelselectedObj?_this.m_formmodelselectedObj.modelid:'';
            // var _wfi_doc = _this.m_formdocselectedObj?_this.m_formdocselectedObj.FileId:'';
            // var _stayStatus = _this.m_flowsubmittype;
            // var _wf_guid = _this.m_formattachedid;

            // 显示 loading, 调用接口
            // ---------------------
            var _LoadingIns = _this.$loading({
                text: '处理中',
                target: document.getElementById("id_jingruizhang_probim_vue_zdialog_inner")
            });
            var _Url = `${window.bim_config.webserverurl}/api/FlowForm/Flow/CreateFlow?debug=552`;
            _this.$axios({
                url: _Url,
                method: 'post',
                data: _this.$qs.stringify(_postdata)
            }).then(x => {
                _LoadingIns.close();
                if (x.data.Ret > 0) {

                // 提示并关闭发起流程对话框
                // ----------------------
                _this.$message.success('操作成功');
                _this.status_showflowcreate = false;

                // 判断当前的地址，如果是流程页面，需要刷新数据
                // ----------------------------------------
                _this.func_refreshflowlist(x.data.Data.wfi_guid);

                } else {
                _this.$message.error(x.data.Msg);
                }
            }).catch(x => {
                _LoadingIns.close();
                debugger;
            });

        },

        // 刷新流程实例列表，并打开显示指定的 wfi_guid
        // ----------------------------------------
        func_refreshflowlist(wfi_guid) {
            var _this = this;
            _this.$emit("onrefresh", wfi_guid);
        },

        // 请求接口，完成驳回或审批操作
        // -------------------------
        func_approve(_postdata){
            var _this = this;
            var _url = `${window.bim_config.webserverurl}/api/FlowForm/Flow/Approve`;
            _this.$axios({
                url: _url,
                method: 'post',
                data: _this.$qs.stringify(_postdata)
            }).then(x => {
                if (x.data.Ret > 0) {
                    _this.$message.success('操作成功');
                    _this.status_shownodeinfo = false;
                    
                    // 判断当前的地址，如果是流程页面，需要刷新数据
                    // ----------------------------------------
                    _this.func_refreshflowlist(x.data.Data.wfi_guid);

                } else {
                    _this.$message.error(x.data.Msg);
                }
            }).catch(x => {
                debugger;
            });
        },

        // 请求接口，完成从节点提交流程或再次保存草稿的操作
        // --------------------------------------------
        func_submitfromnode(_postdata) {
            var _this = this;

            // 显示 loading
            // ------------
            // var _LoadingIns = _this.$loading({
            //     text: '处理中',
            //     target: document.getElementById("id_jingruizhang_probim_vue_zdialog_inner")
            // });
            //deb1ugger;
            var _url = `${window.bim_config.webserverurl}/api/FlowForm/Flow/SubmitFlow?debug=625`;
            _this.$axios({
                url: _url,
                method: 'post',
                data: _this.$qs.stringify(_postdata)
            }).then(x => {
                if (x.data.Ret > 0) {
                _this.$message.success('操作成功');
                _this.status_shownodeinfo = false;
                
                // 判断当前的地址，如果是流程页面，需要刷新数据
                // ----------------------------------------
                _this.func_refreshflowlist(x.data.Data.wfi_guid);

                } else {
                _this.$message.error(x.data.Msg);
                }
            }).catch(x => {
                debugger;
            });
        },

        // 对外函数，显示和隐藏 loading
        // --------------------------
        func_showloading(isshow){
            var _this = this;
            if (isshow) {

                // 如果当前流程实例没有表单，则不要显示 loading 了
                // -------------------------------------------
                if (_this.m_formattachedid) {
                    _this.m_loadingPtr = _this.$loading({
                        text:'加载中',
                        target: document.getElementById('id_flownodeinfo_all')
                    });
                }

                
            } else {
                _this.m_loadingPtr && _this.m_loadingPtr.close();
            }
        },

        // 处理消息
        // -------
        func_processmessage(data) {

            console.log(`收到消息，类型为${data.act}`);
            var _this = this;
            if (data.act == 'resFromFormBuilder_instanceReady') {

                // 把表单数据赋予表单控件
                // --------------------
                var val = _this.m_nodeshowingdata;
                if (val && val.wfr_formjson) {
                    var jsonobj = JSON.parse(val.wfr_formjson);

                    // 处理流程实例列表页面，新建流程时，表单区域一直 loading 的问题
                    // 当 document.getElementById("id_form_reading_comps") 及其 contentWindow 有值时才调用
                    // ----------------------------------------------------------------------------------
                    if (document.getElementById("id_form_reading_comps") && document.getElementById("id_form_reading_comps").contentWindow) {
                        document.getElementById("id_form_reading_comps").contentWindow.postMessage({act:"reqToFormBuilder_setdynamicdatas"
                        , data: jsonobj}, "*"); 
                    }
                    
                }   

                // 隐藏 loading(如果是发起流程的表单页面)
                // ----------------------------------- 
                if (document.getElementById("id_iframe_creatingflowSkeleton")
                && document.getElementById("id_iframe_creatingflowSkeleton").contentWindow
                && ProjectBootVue) {
                    ProjectBootVue.status_formloading = false;
                }

                // id_form_reading_comps 高度调整
                // -----------------------------
                var dom_form = document.getElementById('id_form_reading_comps');
                if (dom_form && data.data) {
                    dom_form.style.height = data.data + 'px';
                }
                
                // 显示整体 loading
                // ----------------
                _this.func_showloading(false);
            }

            // resFromFormBuilder_getdynamicdatas
            // 获取表单中填写的数据（后续要检查fieldkey是否是静态的
            // 在更新表单后
            // -----------
            else if (data.act == 'resFromFormBuilder_getdynamicdatas') {

                var formdynamicdata = data.data;

                // 这里判断：如果 workflowvue.m_selectedObj 为 undefined ，则不做任何动作，这是由外面页面触发 
                // resFromFormBuilder_getdynamicdatas 引起的
                // -----------------------------------------
                if (!workflowvue.m_selectedObj) {
                    return;
                }


                _this.do_submitflow(formdynamicdata);
            }

            // 点击节点，查看填写数据
            // --------------------
            else if (data.act == 'resFromFlowDesign_nodeIdClick') {
                console.log(`点击的节点的nodeId为${data.data}`);

                
            }
            else if (data.act == 'resFromFlowDesign_nodeIdOver') {
                _this.func_processnodeOverOut(data.data, 'over');
            }
            else if (data.act == 'resFromFlowDesign_nodeIdOut') {
                _this.func_processnodeOverOut(data.data, 'out');
            }

        },

        // 处理鼠标移入或移出
        // ----------------
        func_processnodeOverOut(node, overorout) {

            // 展示浮层 div，加载该节点的操作记录
            // -------------------------------
            var _this = this;
            console.log(node);
            console.log(overorout);

            // 修改 m_nodeinfo_left 及 m_nodeinfo_top
            // --------------------------------------
            _this.m_nodeinfo_left = parseInt(node.nodedata.left.toString().replace("px", "") - node.panelleft);
            _this.m_nodeinfo_top = parseInt(node.nodedata.top.toString().replace("px", "") - 128 - node.paneltop);

            // 判断是显示还是隐藏
            // ----------------
            if (overorout == 'over') {
                
                // 显示
                // 直接结束掉之前的 setTimeout 句柄
                // 重置 m_isintip 为 false
                // ------------------------------
                
                _this.m_isintip = false;
                if (_this.h_lasttimeout) {
                    clearTimeout(_this.h_lasttimeout);
                    _this.h_lasttimeout = 0;
                }

                // 显示loading，并请求接口获取数据
                // -----------------------------
                _this.m_nodelogloading = true;
                _this.m_currentnodelogs = [];
     
                var _url = `${window.bim_config.webserverurl}/api/FlowForm/Flow/GetFlowInstanceOperLogs?wfi_guid=${_this.m_wfiguid}`;
                _this.$axios.get(_url).then(x => {
                    if (x.data.Ret > 0) {

                        // 过滤出当前节点的
                        // node.nodedata.id
                        // ----------------
                        var theNodeLogList = x.data.Data.List.filter(x => x.wfr_nodeId == node.nodedata.id);
                        _this.m_currentnodelogs = theNodeLogList;

                    } else {
                        console.error(x.data.Msg);
                    }

                    _this.status_nodeinfo_show = 1;
                    _this.m_nodelogloading = false;
                    
                }).catch(x => {
                    _this.status_nodeinfo_show = 1;
                    _this.m_nodelogloading = false;
                    console.error(x);
                });

            } else {

                // // 隐藏
                // // ----
                // _this.status_nodeinfo_show = 0;

                // 1 秒后执行，如果鼠标不在弹窗区域内，则设置为0
                // 另外，移除弹窗区域，直接设置为0
                // -----------------------------
                _this.h_lasttimeout = setTimeout(()=>{
                    if (!_this.m_isintip) {
                        _this.status_nodeinfo_show = 0;
                    }
                }, 150);
            }

        },

        // 发起流程，移除已添加的文档
        // ------------------------
        func_removedoc(ev, fileid) {
            var _this = this;
            ev && ev.stopPropagation && ev.stopPropagation();
            _this.$confirm("确认移除关联文档?", "操作确认").then(x => {
                _this.m_formdocselectedObj = _this.m_formdocselectedObj.filter(x => x.FileId != fileid);
            });
        },

        // 发起流程，移除已添加的模型
        // ------------------------
        func_removemodel(ev, modelid) {
            var _this = this;
            ev && ev.stopPropagation && ev.stopPropagation();
            _this.$confirm("确认移除关联模型?", "操作确认").then(x => {
                _this.m_formmodelselectedObj = _this.m_formmodelselectedObj.filter(x => x.modelid != modelid);
            });
        },

        // 拷贝给新增时的相关变量，以绑定相同的名称，使 do_submitflow 更简化
        // ------------------------------------------------------------
        func_syncnodedata() {
            var _this = this;
            _this.m_flowname = _this.m_nodeshowingdata.wfi.wfi_title;
            _this.m_flowcomment = _this.m_nodeshowingdata.wfr_comment;
            _this.m_formattachedid = _this.m_nodeshowingdata.wfi.wf_guid;
            _this.m_flowtselected = _this.m_nodeshowingdata.wfi.wft_guid;

            // 将接口返回的模型数组及文档数组深拷贝给 modelobj 及 docobj
            // ------------------------------------------------------
            var _tempModelArr = [];
            var _tempDocArr = [];
            if (_this.m_nodeshowingdata.wfi.wfi_model) {
                try {
                    _tempModelArr = JSON.parse(_this.m_nodeshowingdata.wfi.wfi_model);
                }catch(e) {

                }
            }
            if (_this.m_nodeshowingdata.wfi.wfi_doc) {
                try {
                    _tempDocArr = JSON.parse(_this.m_nodeshowingdata.wfi.wfi_doc);
                }catch(e) {

                }
            }
            
            // if (_this.m_nodeshowingdata.modelobj) {
            //     _this.m_formmodelselectedObj = {
            //     modelid: _this.m_nodeshowingdata.modelobj.ID,
            //     modelname: _this.m_nodeshowingdata.modelobj.Name
            //     };
            // }
            _this.m_formmodelselectedObj = _this.$staticmethod.DeepCopy(_tempModelArr);
            // if (_this.m_nodeshowingdata.docobj) {
            //     _this.m_formdocselectedObj = {
            //     FileId: _this.m_nodeshowingdata.docobj.FileId,
            //     FileName: _this.m_nodeshowingdata.docobj.FileName
            //     };
            // }
            _this.m_formdocselectedObj = _this.$staticmethod.DeepCopy(_tempDocArr);

            _this.m_formmaterialselectedObj = _this.$staticmethod.DeepCopy(_this.m_nodeshowingdata.rfm_bmlist);

            // 回显关联的任务数据
            // ----------------
            _this.m_formtasksselectedObj = _this.$staticmethod.DeepCopy(_this.m_nodeshowingdata.ptlist);


        },

        // 关联模型对话框，选择模型后
        // ------------------------
        evt_modelselected(model){
            var _this = this;

            // 添加时用的变量
            // -------------
            if (!_this.m_formmodelselectedObj || !_this.m_formmodelselectedObj.length) {
                _this.m_formmodelselectedObj = [];
            }

            // 判断如果 m_formmodelselectedObj 不包含 model，将 model push
            // ----------------------------------------------------------
            var ifhasIndex = _this.m_formmodelselectedObj.findIndex(x => x.modelid == model.modelid);
            if (ifhasIndex >= 0) {
                _this.$message.warning('已添加该模型');
            } else {

                _this.m_formmodelselectedObj.push(model);

                // 关闭对话框
                // ---------
                _this.M_selectmodel.visible = false;

                // _this.$nextTick(()=>{
                // _this.func_setflowcreatetop();
                // });
            }
        },

        // 发起流程时，点击添加模型
        // ----------------------
        func_addmodel(ev) {
            var _this = this;
            _this.M_selectmodel.visible = true;
        },

        // 添加添加文档
        // -----------
        func_adddoc(ev) {
            var _this = this;
            _this.status_docbrowser = true;
        },

        // 移除
        // ----
        func_rmmember(usel, ev){
            var _this = this;
            var ifhas = _this.m_members.findIndex(x => x.UserId == usel.UserId);
            if (ifhas >= 0) {
                _this.m_members = _this.m_members.filter(x => x.UserId != usel.UserId);
            }
        },

        // 显示添加人员的对话框
        // ------------------
        func_openaddmember(ev) {
            var _this = this;
            _this.status_showmemberadd = true;
        },

        // 获取当前的 m1_currentsubmitable 及 m1_currentapprovable
        // ----------------------------------------------------
        func_getactiontype(callback){
             if (callback) {
                callback();
            }
        },

        // 请求接口，获取节点上的数据
        // 调用过程中，传入和传出的值均存储到当前vue对象的数据对象中
        // ----------------------------------------------------
        func_getNodeData() {

            // 请求接口获取节点数据
            // ------------------
            var _this = this;
            var _Url = `${window.bim_config.webserverurl}/api/FlowForm/Flow/GetFlowNodeData?bimcomposerId=${_this._bimcomposerId}&wfi_guid=${_this.init_selectedWfiGuid}&nodeId=${_this.m_nodeid}`;
            _this.$axios.get(_Url).then(x => {
                if (x.data.Ret < 0) {
                _this.$message.error(x.data.Msg);
                } else {

                    // 如果没有数据，则提示：当前流程未流转至此节点
                    // ----------------------------------------
                    if (!x.data.Data) {
                        _this.$message.warning('当前流程未流转至此节点');
                    } else {

                        // 弹出类似发起流程的弹框，用于查看或填写表单内容
                        // ------------------------------------------
                        var _tempreceivingData = x.data.Data;
                        _this.m_nodeid = _tempreceivingData.wfr_nodeId;

                        // 设置渲染值
                        // ---------
                        _this.func_setrendervalue(_tempreceivingData);
                        
                    }
                }

            }).catch(x => {
                console.error(x);
            });
        },

        // 设置流程节点上挂的数据（ApproverList 等）
        // --------------------------------------
        func_setrendervalue(val) {
            var _this = this;
            _this.m_members = _this.$staticmethod.DeepCopy(val.ApproverList);

            // 先把节点数据 赋值给 m_nodeshowingdata
            // 再同步给“新增”需要的字段
            // ----------------------
            _this.m_nodeshowingdata = _this.$staticmethod.DeepCopy(val);

            // m_nodeshowingdata 有值后 设定 iframe 的url并显示, 显示前注册 message
            // ------------------------------------------------------------------
            // 如果没注册过消息，注册消息
            // ------------------------
            window.onmessage = function(e) {
                _this.func_processmessage(e.data);
            };

            // 调用接口获取 m1_currentsubmitable 及 m1_currentapprovable
            // ------------------------------------
            _this.func_getactiontype(()=>{
                _this.m_formurl = _this.func_getnodeformurl(_this.m_formattachedid, _this.init_selectedWfiGuid, _this._selectedObj.currentNode.id);
            });


            _this.func_syncnodedata();

            // // 把表单数据赋予表单控件
            // // --------------------
            // _this.$nextTick(()=>{
            //     if (val && val.wfr_formjson) {
            //         var jsonobj = JSON.parse(val.wfr_formjson);
            //         document.getElementById("id_form_reading_comps").contentWindow.postMessage({act:"reqToFormBuilder_setdynamicdatas"
            //         , data: jsonobj}, "*");
            //     }    
            // });
            
        },

        // evt_onmouted()
        // 根据传进来的流程对象数据，给这个组件的其它字段赋值
        // ----------------------------------------------
        func_initfields() {

            // 表单相关
            // -------
            var _this = this;
            _this.m_formattachedid = _this._selectedObj.wf_guid;
            var nodeId = _this._selectedObj.currentNode.id;
            var wfi_guid = _this._selectedObj.wfi_guid;

        
            

            // 流程名称
            // --------
            _this.m_flowname = _this._selectedObj.wfi_title;
            
            // 调用 GetFlowNodeData 获取节点上的数据
            // -----------------------------------
            _this.func_getNodeData();

      

            

        },

        // // 对外提供方法，修改 m_selectedObj 并刷新数据
        // // -----------------------------------------
        // func_loadwfi(wfi) {
        //     var _this = this;
        //     _this._selectedObj = wfi;
        //     _this.m_formattachedid = _this._selectedObj.wf_guid;
        //     _this.m_formurl = _this.func_getnodeformurl(_this.m_formattachedid);
        // },

        // 表单移除确认
        // -----------
        func_removeform() {
        
            var _this = this;
            _this.$confirm("确认移除表单?", "操作确认").then(x => {
                _this.m_formbeingselectObj = {};
                _this.m_formattachedid = undefined;
            });
        },

        // 计算节点数据的表单url
        // --------------------
        func_getnodeformurl(wf_guid, wfi_guid, nodeId) {
            var _this = this;

            //var _src = `${window.bim_config.integrated_innerview}/A1ddons/Module_Form/index.html?wf_guid=${wf_guid}&isInstance=1&wfi_guid=${wfi_guid}&nodeId=${nodeId}`;
            //已验证 A2ddons/Module_Form
            var _src = _this.$staticmethod.getFormUrl(window.bim_config.integrated_innerview
                , wf_guid, wfi_guid, nodeId, 1);
            _src += '&disall=1';
            return _src;
        },

        style_all() {
            var _this = this;
            var _s = {};
            _s["width"] = _this._width;
            _s["height"] = _this._height;
            _s["background-color"] = _this._bgcolor;
            return _s;
        }
    },
    props:{
        init_width:{
            type: String,
            required: true
        },
        init_height:{
            type: String,
            required: true
        },
        init_bgcolor:{
            type: String,
            required: true
        },
        init_selectedWfiGuid:{
            type: String,
            required: true
        },
        init_bimcomposerId:{
            type:String,
            required: true
        }
    }
}
</script>
<style scoped>

._css-addattach-icon {
    margin-left: 20px;
    margin-right: 10px;
    width:20px;
    height: 20px;
}

._css-attachment-label {
    border-top: 1px solid rgba(0,0,0,0.1);
    padding-top: 20px;
}

._css-nodeinfo-modelanddoc {
    display: flex;
    flex-direction: row-reverse;
}

._css-nodeinfo-model {
    /* flex:1; */
    flex:none;
    width:50%;
}

._css-nodeinfo-doc {
    /* flex:1; */
    flex:none;
    width:50%;
}

._css-line {
    box-sizing: border-box;
    margin: 20px 0 0 0;
    /* display: flex;
    align-items: center; */
}

._css-formpagectn {
    width:100%;
    border-top:1px solid rgba(236, 238, 240, 1);
}

._css-flowskeleton-ctn {
    height: 100%;
}

._css-flowskeleton {
    border:none;
    height:100%;
    width:100%;
}

._css-removebtn-ctn {
    /* flex:1; */
}

._css-common-line-form {
    /* height:calc(100% - 8px); */
}

._css-table-operlogs {
    height:100% !important;
}

._css-flowdetail-tabs-content {
    height: calc(100% - 0px);
}

._css-hidden {
  visibility: hidden;
}

._css-modelname-show {
    flex: 1;
    overflow-x: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    /* max-width: calc(100% - 26px); */
       max-width: 261.5px;
    text-align: left;
}

._css-flowdetail-tabs-head {
    height: 40px;
    /* display: flex; */
    display: none;
    align-items: center;
}

._css-tab-item {
    height:40px;
    line-height: 40px;
    flex:1;
    box-sizing: border-box;
    font-size: 14px;
    border-bottom: 2px solid transparent;
    background-color: rgba(24, 144, 255, 0.1);
    cursor:pointer;
    text-align: center;
}

._css-tab-item._css-current {
    color: #1890FF;
    border-bottom: 2px solid #1890FF;
}

._css-notallowinner {
    background-color: #f5f7fa;
    color: #c0c4cc;
    cursor: not-allowed;
}

._css-notallowouter {
    background-color: #f5f7fa;
    cursor: not-allowed;
}

._css-node-operlogi {
    box-sizing: border-box;
    padding: 0 4px 0 4px;
    font-size: 12px;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow-x: hidden;
    width: 100%;
    height: 32px;
    line-height: 32px;
    text-align: left;
    color: rgba(0,0,0,0.65);
}

._css-node-operlog {
    padding:6px;
    position:absolute;
    width: 300px;
    border:2px solid rgba(248, 171, 45, 0.65);

    box-sizing: border-box;
    background-color: #fff;
    height: 160px;
    overflow-y: scroll;
    border-radius: 4px;
   

    left: -100px ;
    top: 64px;
    z-index: -1;
    opacity: 0;
    transition: opacity,z-index 500ms;
}

._css-node-operlog._css-show {
    z-index: 1002;
    opacity: 1;
    transition: opacity,z-index 500ms;
}


._css-flowinstance-detail-link {
    flex: 1;
    text-align: left;
    margin-left: 24px;
    cursor: pointer;
    color: #1890FF;
    opacity: 0.8;
}
._css-flowinstance-detail-link:hover {
    opacity: 1;
}
._css-nameshow-attached {
    width: calc(100% - 30px);
    overflow-x: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    text-align: left;
}
._css-flowmodelname{
    display: flex;
    align-items: center;
}


._css-flowModelNameSelected {
    height: 40px;
    line-height: 40px;
    cursor: pointer;
    color: rgba(40, 58, 79, 1);
    flex: 1;
    max-width: calc(100% - 0px);
    display: flex;
    align-items: center;
    border-radius: 4px;
    border: 1px solid rgba(208,208,208,1);
}

._css-flowModelNameSelected:hover {
    background-color: rgba(244, 245, 246, 1);
    border-color:transparent;
    color: rgba(40, 58, 79, 1);
    font-weight: bold;
}

._css-flowModelNameSelected:hover ._css-addattach-icon {
    color: rgba(52, 69, 87, 1);
    font-weight: bold;
}

._css-member-seli {
    font-size: 12px;
    min-width: 36px;
    max-width: 200px;
    height: 20px;
    line-height: 20px;
    margin-right: 4px;
    border: 1px solid rgba(0,0,0,0.1);
    margin-top: 2px;
    border-radius: 4px;
    position: relative;
    color: rgba(0,0,0,0.65);
    overflow-x: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    padding-left: 4px;
    padding-right: 4px;
}
._css-member-rmbtn {
    border-radius: 50%;
    font-size: 12px;
    line-height: 16px;
    color:#fff;
    border:1px solid rgba(0,0,0,0.6);
    width: 16px;
    height: 16px;
    display: none;
    position: absolute;
    right: 0px;
    top: 0px;
    cursor: pointer;
    color: #fff;
    background-color: rgb(39,39,39);
    text-align: center;
    border-color: rgba(0,0,0,0);
}
._css-member-seli:hover ._css-member-rmbtn {
    display: block;
}
._css-member-rmbtn:hover {
    background-color:#1890FF;
}
._css-member-seli:hover ._css-member-rmbtn {
    display: block;
}
._css-member-addbtn {
    border: 1px solid transparent;
    background-color: #1890ff;
    color: #fff;
    height: 21px;
    padding: 2px 8px 2px 8px;
    border-radius: 4px;
    cursor: pointer;
    opacity: 0.8;
    margin-top: 0;
}
._css-flownode-content {
    overflow-y: auto;
    /* height: calc(100% - 64px - 40px); */
    height: calc(100% - 64px - 0px);
}
._css-flownode-foot {
    height: 64px;
    display: flex;
    align-items: center;
    flex-direction: row-reverse;
}
._css-form-inputting {
    border: none;
    width: 100%;
    /* height: 400px; */
}
._css-closehoverctn ._css-formadd-closebtn {
    right: 0;
    top: 0;
    border-radius: 50%;
    font-size: 16px;
    cursor: pointer;
    visibility: hidden;
    display: flex;
    width: 16px;
    height: 16px;
    margin-right: 12px;
}

._css-closehoverctn ._css-formadd-closebtn._css-static {
  position: static;
  margin-left: 8px;
}

._css-closehoverctn:hover ._css-formadd-closebtn {
  visibility: visible;
}

/* ._css-formadd-closebtn:hover {
  border-color: #1890FF;
  background-color: #1890FF;
}
._css-formadd-closebtn:hover {
  border-color: #1890FF;
  background-color: #1890FF;
} */


._css-addattach-text {
    height: 20px;
    line-height: 20px;
}

._css-addattach {
    margin-left: 0px;
    height: 40px;
    display: flex;
    align-items: center;
    color: rgba(97, 111, 125, 1);
    cursor: pointer;
    width: 100%;
    border-radius: 4px;
    border: 1px dashed rgba(208,208,208,1);
}
._css-teatarea-in {
  resize: none;
  line-height: unset;
  height: 100px;
}
._css-fieldvalue._css-textareavalue {
  height: auto;
  position: relative;
}
/* ._css-title {
    width: 120px;
    flex:none;
    text-align: right;
    height: 30px;
    line-height: 30px;
} */

._css-title {
    flex: none;
    text-align: left;
    height: 30px;
    line-height: 30px;
    padding-left: 24px;
    padding-right: 24px;
}

._css-flownodeinfo-all {
    border-radius: 4px;
}
</style>