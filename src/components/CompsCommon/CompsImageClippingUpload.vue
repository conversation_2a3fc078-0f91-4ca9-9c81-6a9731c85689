<template>
  <div class="image-clip-comps">

    <div class="crop-container" :style="{ 'width': width + 'px', 'height': height + 'px' }">
      <vue-cropper
        ref='croppers'
        :guides="true"
        :view-mode="2"
        drag-mode="crop"
        :auto-crop-area="0.5"
        :min-container-width="200"
        :min-container-height="150"
        :background="true"
        :rotatable="true"
        :src="imgSrc"
        preview=".imgPreview"
        alt="请选择一张图片"
        :img-style="{ 'width': width + 'px', 'height': height + 'px' }">
      </vue-cropper>
    </div>
    <div class="result-container" :style="{ 'height': height + 'px' }">
      <div :class="[{'circular-preview' : circularPreview},'result-cropImg']">
        <div v-if="hasSelectImg" class="imgPreview" style="width: 110px;height: 110px;"></div>
        <span v-else >预览</span>
      </div>

      <label for="choiceFileInput" class="choice-file">
        选择文件
        <input id="choiceFileInput" type="file" name="image" accept="image/*" style="display: none" @change="setImage" />
      </label>
        <!--<img :src="cropImg" alt="预览区" width="100%"/>-->
    </div>
    <!--<button @click="cropImage" v-if="imgSrc != ''" style="margin-right: 40px;">Crop</button>-->
    <!--<button @click="rotate">旋转</button>-->
    <!--<button @click="resetImg">重置</button>-->
  </div>
</template>

<script>
  import VueCropper from 'vue-cropperjs';
  import 'cropperjs/dist/cropper.css';
  //图片裁剪上传
  export default {
    name: "CompsImageClippingUpload",
    components: { VueCropper},
    data(){
      return {
        imgSrc: '',//占位图
        cropImgBase64: '',//裁剪后的图片 base64
        cropImgBinary: '',//裁剪后的图片 二进制文件流
        hasSelectImg: false,//是否选择了一张图片
      }
    },

    props: {
      width: {
        default: 400,
        type: Number,
      },
      height: {
        default: 300,
        type: Number,
      },

      circularPreview: {//预览区 是否为圆形
        default: true,
        type: Boolean
      },

      occupancyMap: {//占位图
        default: require('../../assets/images/crop-occupancy.png'),
      },
    },

    created() {
      this.imgSrc = this.occupancyMap;
    },

    methods: {
      setImage(e) {
        const file = e.target.files[0];
        if (!file.type.includes('image/')) {
          this.$message({
            type: error,
            message: '请上传一张图片'
          });
          return;
        }
        if(file.size > 204800){
          this.$message.error('图片不能大于200KB，请重新上传');
          return;
        }
        this.hasSelectImg = true;
        if (typeof FileReader === 'function') {
          const reader = new FileReader();
          reader.onload = (event) => {
            this.imgSrc = event.target.result;
            // rebuild cropperjs with the updated source
            this.$refs.croppers.replace(event.target.result);
          };
          reader.readAsDataURL(file);
        } else {
          this.$message({
            type: error,
            message: '对不起，暂不支持'
          });
        }
      },
      cropImage() {

        if (!this.hasSelectImg) {
          this.$message({
            type: 'error',
            message: '请上传一张图片'
          });
          return false;
        }

        // get image data for post processing, e.g. upload or setting image src
        this.cropImgBase64 = this.$refs.croppers.getCroppedCanvas().toDataURL();//base64
        this.$refs.croppers.getCroppedCanvas().toBlob(_=>{//二进制文件
          this.cropImgBinary = _;
          this.$emit('onCrop',this.cropImgBase64,this.cropImgBinary)
        });
      },
      rotate() {
        this.$refs.croppers.rotate(90);
      },

      //重置
      resetImg() {

        // 清空 choiceFileInput 的 value
        document.getElementById("choiceFileInput").value = ""

        this.$refs.croppers.replace(this.occupancyMap);
        this.hasSelectImg = false;
        this.cropImgBase64 = this.cropImgBinary = '';
      }
    },
  }
</script>

<style scoped>
.image-clip-comps {
  width: 100%;
  margin-bottom: 10px;
  display: flex;
}

.image-clip-comps .crop-container {
  box-shadow: 0 0 2px #cacaca;
  display: inline-block;
  overflow: hidden;
  margin-right: 15px;
}

.image-clip-comps .result-container {
  width: 140px;
  padding: 15px;
  position: relative;
  box-sizing: border-box;
  display: inline-block;
  /*box-shadow: 0 0 2px #cacaca;*/
  background:rgba(0,0,0,0.02);
}

.image-clip-comps .result-container .result-cropImg {
  border-radius: 100%;
  background: rgba(0,0,0,0.09);
  overflow: hidden;
  border: 2px solid rgba(0,0,0,0.15);
  width: 110px;
  height: 110px;
  line-height: 110px;
}

.image-clip-comps .result-container .result-cropImg.circular-preview {
  border-radius: 100%;
}

.image-clip-comps .result-container .result-cropImg img {
  vertical-align: middle;
}

.image-clip-comps .result-container .choice-file {
  cursor: pointer;
  display: block;
  position: absolute;
  left: 15px;
  bottom: 15px;
  padding: 0 27px;
  border-radius: 2px;
  background-color: #1890ff;
  color: #fff;
  line-height: 32px;
}

.image-clip-comps .result-container .choice-file:hover {
  opacity: 0.9;
}
</style>
