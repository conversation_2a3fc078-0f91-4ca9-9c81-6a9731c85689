<template>
  <div class="model-content">
    <section class="left-conter">
      <div class="model-wp">
        <iframe id="pure-scene-iframe" width="100%" height="100%" class="content-frame" src="" frameborder="0"></iframe>
      </div>
      <div
        class="switch-model"
        @click="closeSelectModel"
      >
				关闭
      </div>
			<div class="switch-rel-point" v-if="modelLoadEnding" @click="relPointFun">
				关联视点
			</div>
			<!-- <div class="switch-rel-close" @click="closeSelectModel"></div> -->
			
      <!-- <div class="wp">
				<img :src="imgsrc" alt />
        <span @click="selectModelConfigShow = true">选择关联模型</span>
      </div> -->
    </section>
  </div>
</template>
<script>
export default {
  name: "SelectiveModelForRelevance",
  props: {
    sceneData: {
      type:Object
    }
  },
  data() {
    return {
			modelViewShow: false, // 模型展示
			iframeSrc: '', // 选择模型的地址
			selectModelConfigShow: false,  // 选择模型的组件
			switchTextShow: false,
			modelLoadEnding: false,
      projectId: '',
      iframeWindow: null,
      sceneItemData: {}
    };
  },
  created(){
    this.getDetailItem()
  },
  mounted(){
    window.addEventListener('message', this.listeningMessage, false)
  },
  methods: {
    // 监听是否加载完毕
    listeningMessage(event){
      if(event.data == 'featureLoad'){
        this.loadScene(this.sceneItemData.SceneEventDataJson);
      }
    },
    // 获取场景详情
    async getDetailItem(){
      console.log(this.sceneData, 1234)
      let _data = {
        projectID:this.$staticmethod._Get("organizeId"),
        SceneId: this.sceneData.SceneId,
        userId: this.$staticmethod.Get("UserId"),
      }
      const res = await this.$api.getscenedetail(_data)
      this.sceneItemData = res.Data;
      const iframe = document.getElementById('pure-scene-iframe')
      const ifrSrc = this.getHttpUrl();
      iframe.src = ifrSrc + `?&projectId=${this.projectId}&lang=cn&edit=false`

    },
    loadScene(sceneEventDataJson){
      // console.log('=====场景加载进度ing')
      this.iframeWindow = document.getElementById('pure-scene-iframe').contentWindow
      this.iframeWindow.toggleSceneManageEditMode(false)
      window.scene = this.iframeWindow.scene
      // 还原场景 及 左侧场景管理树
      this.iframeWindow.saveSceneJson2Store(sceneEventDataJson)
      this.modelLoadEnding = true;

      // 移除postmessage事件监听
      window.removeEventListener('message', this.listeningMessage)
      // console.log('=====场景加载完毕')
      this.$emit('sceneLoadEnd')
    },
    getHttpUrl(){
      return window.bim_config.newModelApi;
    },
		relPointFun(){
			// 点击关联视点，获取视点的位置，存后台
			// let iframeWindow = this.$refs.iframeWp.contentWindow;
			

			// let view = this.$refs.iframeWp.contentWindow.model.BIM365API.Context.getViewPointBasicInfo()
			// let _h = iframeWindow.model.orbitControl.controls.azimuthAngle;
      // let _v = iframeWindow.model.orbitControl.controls.polarAngle;
      // let _f = iframeWindow.model.orbitControl.controls.camera.fov;
			// let view = {h:_h, v: _v, f: _f}

      // let pos = iframeWindow.scene.mv.orbitControl.controls.getPosition();
      // let target = iframeWindow.scene.mv.orbitControl.controls.getTarget();
      // let focalOffset = iframeWindow.scene.mv.orbitControl.controls.getFocalOffset();
      // let view = { pos,target,focalOffset};
      // console.log(view,'00000')
      const iframeWindow = document.getElementById('pure-scene-iframe').contentWindow;
      if (window.scene) {
        let storage = window.scene.getCameraInfo()
        let view = JSON.stringify(storage)
        this.$emit('relPointFun',view,this.sceneData.SceneId)
      }
		},
		closeSelectModel(){
			this.$emit('closeSelectModel')
		}
	
  },
};
</script>


<style lang="stylus" scoped rel="stylesheet/stylus">
.model-content{
	width: 100%;
	height: 100%;
	background: rgba(0, 0, 0, 0.15);
    section {
      &.left-conter {
        box-shadow: 2px 0px 4px 0px rgba(0, 21, 41, 0.12);
        position: relative;
					width: 100%;
					height: 100%;
        .switch-model {
          cursor: pointer;
          width: 100px;
          height: 40px;
          background: #1990FF;
          border-radius: 4px;
          position: absolute;
          top: 24px;
          left: 24px;
          text-align: center;
          line-height: 40px;
          font-size: 14px;
          z-index: 9; 
					color: #fff;
					i{
						font-size: 22px;
						vertical-align: middle;
						margin-right: 5px;
					}
        }
				.switch-rel-point{
					position: absolute;
          top: 24px;
          left: 135px;
					width: 88px;
					line-height: 40px;
					background: #FFFFFF;
          z-index: 9; 
					border-radius: 4px;
					color: #007AFF;
          cursor: pointer;
				}
				.switch-rel-close{
					position: absolute;
          top: 15px;
          right: 15px;
          z-index: 9; 
					width: 40px;
					height: 40px;
          cursor: pointer;
					background-image: url('../../assets/images/icon-pano-close-model.png');
					background-size: contain;
					background-repeat: no-repeat;
				}
        .model-wp {
          background: #f0f2f5;
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          z-index: 9;

          iframe {
            margin: 0;
            padding: 0;
            border: none;
            width: 100%;
            height: 100%;
          }
        }

        .wp {
          position: absolute;
          top: 250px;
          width: 180px;
          left: 0;
          right: 0;
          margin: auto;
          display: flex;
          flex-direction: column;
          align-items: center;
        }

        img {
          width: 400px;
          height: 238px;
        }

        span {
          width: 123px;
          height: 40px;
          line-height: 40px;
          border-radius: 4px;
          background: #1890ff;
          color: #fff;
          text-align: center;
          display: inline-block;
          cursor: pointer;
        }
      }
		}
}

</style>
