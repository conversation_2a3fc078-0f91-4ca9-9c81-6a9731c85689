<template>
  <div class="_css-all-singleselect" :style="computestyle">
    <!-- 对话框前景 -->
    <div class="_css-threefields-front" v-drag="draggreet"  :style="dragstyle" >
      <CompsDialogHeader @oncancel="_oncancel" :title="title || '无标题'"></CompsDialogHeader>
      <div class="_css-line1 _css-line">
          <div class="_css-line1-icon icon-interface-creditcard" ></div>
          <div class="_css-line1-fieldname" >项目属性</div>
          <CompsSelect
          :items="[{id:'0',text:'非公开'}, {id:'1', text:'公开'}]"
          :text="extdata.data_selectedtext"
          :val="extdata.data_selectedid"
          :inputclass="'_css-single-sel2'"
          width="276px"
          @onselected="_selected"
          ></CompsSelect>
      </div>
      <div v-if="warningtext != undefined" class="_css-warningtext">{{warningtext || ''}}</div>
      <CompsDialogBtns @onok="_onok" @oncancel="_oncancel"></CompsDialogBtns>
    </div>
    <!-- //对话框前景 -->
  </div>
</template>
<script>
/*
input:
    
events:
  oncancel
  onok
*/
import CompsDialogHeader from "@/components/CompsDialog/CompsDialogHeader";
import CompsDialogBtns from "@/components/CompsDialog/CompsDialogBtns";
// import CompsEloSelect from "@/components/CompsElOverride/CompsEloSelect";
import CompsSelect from "@/components/CompsCommon/CompsSelect"
export default {
  components: {
    CompsDialogHeader,
    CompsDialogBtns,
    CompsSelect
    //CompsEloSelect
  },
  data() {
    return {
          val:'0',
            dragstyle: {
                position: 'fixed',
                right: 'calc(50% - 205px)',
                top: 'calc(50% - 95px)'
            },
        extdata:{
            data_selectedid: '0',
            data_selectedtext: '非公开'
        }
    };
  },
  mounted(){
    var _this = this;
    if (_this.initid && _this.inittext) {
      _this.extdata.data_selectedid = _this.initid;
      _this.extdata.data_selectedtext = _this.inittext;
    }
  },
  computed: {
    computestyle: {
      get() {
        var _this = this;
        var _s = {};
        if (_this.zIndex) {
          _s["z-index"] = _this.zIndex;
        }
        return _s;
      }
    }
  },
  props: {
    zIndex: {
      type: Number,
      required: false
    },
    title: {
      type: String,
      required: false
    },
    placeholder: {
      type: String,
      required: false
    },
    inputicon: {
      type: String,
      required: false
    },
    warningtext: {
      type: String,
      required: false
    },
    initid:{
      type: String,
      required: false
    },
    inittext:{
      type: String,
      required: false
    }
  },
  methods: {
       draggreet(val){
                var _this = this;
                _this.val = val;
            },
      _selected(id, text){
          var _this = this;
          _this.extdata.data_selectedid = id;
          _this.extdata.data_selectedtext = text;
      },
    _onok() {
      var _this = this;
      _this.$emit("onok", _this.extdata.data_selectedid, _this.extdata.data_selectedtext);
    },
    _oninput() {},
    _oncancel() {
      var _this = this;
      _this.$emit("oncancel");
    }
  }
};
</script>
<style scoped>
._css-single-sel2{
    flex:1;
}
._css-line1-icon{
    font-size:14px;
    width:14px;
    height:14px;
    margin-left:8px;
}
._css-line1-fieldname{
    width:74px;
    height:22px;
    line-height: 22px;
    margin-left:4px;
    text-align: left;
    font-weight: 600;
    color:rgba(0, 0, 0, 0.85);
}
._css-line1-select{
    width:120px;
    height:20px;
    position:relative;
}
._css-warningtext {
  margin-left: 16px;
  margin-right: 16px;
  margin-top: 14px;
  margin-bottom: 18px;
  height: 20px;
  line-height: 20px;
  text-align: left;
  color: #f5222d;
  font-size: 12px;
}
._css-ertip-icon {
  height: 14px;
  width: 14px;
  font-size: 14px;
  margin-left: 24px;
  color: rgb(245, 34, 45);
}
._css-ertip-text {
  margin-left: 8px;
  height: 20px;
  line-height: 22px;
  color: rgba(0, 0, 0, 0.45);
  font-size: 12px;
}
._css-emailrepeat-tip {
  height: 20px;
  display: flex;
  align-items: center;
}
._css-threefields-front {
  width: 410px;
  min-height: 48px;
  background-color: rgba(255, 255, 255, 1);
  box-shadow: 0px 13px 24px -17px rgba(11, 41, 62, 0.8);
  border-radius: 4px;
}
._css-all-singleselect {
  position: fixed;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.15);
  display: flex;
  align-items: center;
  justify-content: space-around;
}
._css-line {
  margin-left: 16px;
  margin-right: 16px;
  height:50px;
}
._css-line1 {
  margin-top: 22px;
  margin-bottom: 22px;
  display: flex;
  align-items: center;
  /* background-color: rgba(0, 0, 0, 0.02); */
  background-color: #fafafa;
}
</style>