<template>
  <div class="_css-all-pjinfo" :style="computestyle">
    <!-- 对话框前景 -->
    <div 
    v-show="showfront"
    class="_css-comps-front" :style="computefrontStyle" v-drag="draggreet" >
      <CompsDialogHeader @oncancel="_oncancel" :title="title || '无标题'"></CompsDialogHeader>
      <!-- 名字展示区 -->
      <div class="_css-memberinfo-head">
        <div class="_css-memberinfo-headicon">
          <img :src="extdata.detaildata.boi_miniimg" class="css-h100 css-w100">
        </div>
        <div class="_css-memberinfo-headmiddle">
          <div class="_css-memberinfo-headmtop">
            <div class="_css-mhtop-name">{{extdata.detaildata.FullName}}</div>
            <div
              :class="{'_css-mhtop-status-nonpublic':extdata.detaildata.IsPublic != true, '_css-mhtop-status-public':extdata.detaildata.IsPublic == true}"
            >{{extdata.detaildata.IsPublic==true?'公开':'非公开'}}</div>
            <!-- <div class="_css-mhtop-status-public">公开</div> -->
          </div>
          <div class="_css-memberinfo-headmbottom">
            <div class="_css-mhbottom-email">{{extdata.detaildata.CreateDate}}</div>
            <div class="_css-mhbottom-phone">
              <div class="_css-mhbottom-phoneicon icon-interface-user"></div>
              <div class="_css-mhbottom-phonetext">{{extdata.detaildata.mgr_RealName}}</div>
            </div>
          </div>
        </div>

          <el-tooltip
            popper-class="css-no-triangle"
            effect="dark"
            content="编辑项目信息"
            placement="top"
          >

        <div @click.stop="_cfgbtnclick" class="_css-memberinfo-headconfigbtn icon-interface-set_se">
          <!-- 下拉框 -->
          <div class="_css-pjinfo-editoptions">
            <div class="_css-pjinfo-oitem" @click.stop="extdata.isshowing_editpj = true">编辑项目名称</div>
            <div class="_css-pjinfo-oitem" @click.stop="extdata.isshowing_image = true">编辑项目缩略图</div>
            <div class="_css-pjinfo-oitem" @click.stop="extdata.isshowing_transpm = true">编辑项目经理</div>
            <div class="_css-pjinfo-oitem" @click.stop="extdata.isshowing_editpjprop = true">编辑项目属性</div>
          </div>
          <!-- //下拉框 -->
        </div>
          </el-tooltip>


      </div>
      <!-- //名字展示区 -->

      <!-- 项目位置展示区 -->
      <!-- <div class="_css-pjinfo-pos">
        <div class="_css-pjinfo-posinner">
          <div class="_css-pjinfo-pos-title">项目位置</div>
          <div class="_css-pjinfo-pos-text">{{extdata.detaildata.Address}}</div>
          <div class="_css-pjinfo-pos-btn">
            <CompsEloButton
              :width="76"
              :height="32"
              text="编辑位置"
              color="#fff"
              bgcolor="#1890FF"
              :fontSize="12"
              @onclick="extdata.isshowing_editpjpos = true"
            ></CompsEloButton>
          </div>
        </div>
      </div> -->
      <!-- //项目位置展示区 -->

      <!-- 项目简介展示区 -->
      <div class="_css-pjinfo-desc">
        <div class="_css-pjinfo-descinner">
          <div class="_css-pjinfo-desctitle">项目简介</div>
          <div class="_css-pjinfo-desctext">
            <textarea
            @mousedown="_stopPropagation($event)"
              class="_css-pjinfo-descarea css-miniscroll"
              v-model="extdata.detaildata.Description"
            ></textarea>
          </div>
        </div>
      </div>
      <!-- //项目简介展示区 -->

      <CompsDialogBtns
        @onwarning="extdata.isshowing_delpj = true"
        @onok="_onok"
        @oncancel="_oncancel"
        :warningbtn="true"
        warningbtntext="删除项目"
      ></CompsDialogBtns>
    </div>
    <!-- //对话框前景 -->

    <!-- 对话框区域 -->
    <CompsSingleField
      :zIndex="2"
      title="删除项目"
      warningtext="该项目内的所有内容将无法找回，请谨慎操作"
      inputicon="icon-interface-creditcard"
      placeholder="请输入该项目的名称"
      v-if="extdata.isshowing_delpj"
      @oncancel="extdata.isshowing_delpj = false"
      @onok="delpj_ok"
      @oninput="delpj_confirminput"
    ></CompsSingleField>

    <CompsSingleField
      :zIndex="2"
      title="编辑项目名称"
      :inittext="extdata.detaildata.FullName"
      inputicon="icon-interface-creditcard"
      placeholder="请输入该项目的名称"
      v-if="extdata.isshowing_editpj"
      @onok="editpjname_ok"
      @oncancel="extdata.isshowing_editpj = false"
    ></CompsSingleField>

    <CompsSingleSelect
      :zIndex="2"
      title="编辑项目属性"
      inputicon="icon-interface-creditcard"
      placeholder="请输入该项目的名称"
      :initid="extdata.detaildata.IsPublic?'1':'0'"
      :inittext="extdata.detaildata.IsPublic?'公开':'非公开'"
      v-if="extdata.isshowing_editpjprop"
      @onok="setpjprop_ok"
      @oncancel="extdata.isshowing_editpjprop = false"
    ></CompsSingleSelect>

    <CompsSimpleSearchList
      :zIndex="2"
      title="选择项目经理"
      showType="project"
      :projectId="projectId"
      :oldname="extdata.detaildata.mgr_RealName"
      newname
      v-if="extdata.isshowing_transpm"
      @oncancel="extdata.isshowing_transpm = false"
      @onok="changepm_ok"
    ></CompsSimpleSearchList>

    <div class="_css-pjinfo-imageedit" v-if="extdata.isshowing_image">
      <div class="_css-pjinfo-imageedit-in" v-drag="draggreet"  :style="dragstyle" >
        <!-- 在线裁切标题栏 -->
        <CompsDialogHeader @oncancel="_oncancel_editimage" title="更换项目缩略图"></CompsDialogHeader>
        <!-- //在线裁切标题栏 -->

        <!-- 裁切组件部分 -->
        <div class="_css-clipcontainer">
          <CompsImageClippingUpload
            ref="cropImg"
            @onCrop="_onok_editimage_callback"
            :circularPreview="true"
            :width="368"
            :height="300"
          ></CompsImageClippingUpload>
        </div>

        <!-- //裁切组件部分 -->

        <!-- 在线裁切底部按钮 -->
        <CompsDialogBtns
          @onok="_onok_editimage"
          @oncancel="_oncancel_editimage"
          :warningbtn="true"
          @onwarning="_reset_editimage"
          warningbtntext="恢复默认"
        ></CompsDialogBtns>
        <!-- //在线裁切底部按钮 -->
      </div>
    </div>

    

    <!-- //对话框区域 -->
  </div>
</template>
<script>
/*
input:
    
events:
    onok();
    oncancel();
    cfgbtnclick();
*/
import CompsDialogHeader from "@/components/CompsDialog/CompsDialogHeader";
import CompsDialogBtns from "@/components/CompsDialog/CompsDialogBtns";
import CompsEloButton from "@/components/CompsElOverride/CompsEloButton";
import CompsSingleField from "@/components/CompsCommon/CompsSingleField";
import CompsSingleSelect from "@/components/CompsCommon/CompsSingleSelect";
import CompsSimpleSearchList from "@/components/CompsCommon/CompsSimpleSearchList";
import CompsImageClippingUpload from "@/components/CompsCommon/CompsImageClippingUpload";
export default {
  components: {
    CompsDialogHeader,
    CompsDialogBtns,
    CompsEloButton,
    CompsSingleField,
    CompsSingleSelect,
    CompsSimpleSearchList,
    CompsImageClippingUpload,
  },
  data() {
    return {
      showfront:false, // 在调整好 dragstyle.top 后，此值改为true.
        val:'0',
            dragstyle: {
                position: 'fixed',
                right: 'calc(50% - 300px)',
                top: 'calc(50% - 582px / 2)'//422/2  582/2
            },
      extdata: {
        delpj_inputting: "", // 确认删除项目时，输入的名称

        detaildata: {
          // 项目详情数据
          CreateDate: "",
          Description: "",
          FullName: "",
          RealName: "",
          boi_miniimg: "",
          mgr_UserId: "", // 项目经理 UserId
          mgr_RealName: "" // 项目经理 RealName

          , Address:''    // 位置中文
          , longtitude: 0 // 经度
          , latitude: 0   // 纬度
          , height: 0     // 高度
        },

        meminfo_pjcount: 0, // 已分配的当前人可创建的最大项目个数
        meminfo_maxpjcount: 20, // 总可分配的当前人创建的最大项目个数
        isshowing_editpjpos: false, // 正在显示编辑项目位置gis
        isshowing_delpj: false, // 正在显示删除项目的对话框
        isshowing_editpj: false, // 正在编辑项目名称的对话框
        isshowing_editpjprop: false, // 正在编辑项目属性的对话框
        isshowing_transpm: false, // 正在显示选择项目经理的对话框
        isshowing_image: false // 正在显示项目缩略图
      }
    };
  },
  mounted() {
    this.getProjectDetail();
  },
  computed: {
    computestyle: {
      get() {
        var _this = this;
        var _s = {};
        if (_this.zIndex) {
          _s["z-index"] = _this.zIndex;
        }
        return _s;
      }
    },
    computefrontStyle: {
      get() {
        var _this = this;
        var _s = {};
        if (_this.width) {
          _s["width"] = _this.width;
        }
                 for(var _sattr in _this.dragstyle) {
          _s[_sattr] = _this.dragstyle[_sattr];
        }
        return _s;
      }
    }
  },
  methods: {
       draggreet(val){
                var _this = this;
                _this.val = val;
            },
            _stopPropagation(ev){
              ev.stopPropagation();
            },
    _onselectpoint(posinfo){
      var _this = this;      
      _this.extdata.detaildata.Address = posinfo.address;
      _this.extdata.detaildata.longtitude = posinfo.coordinary.longitude;
      _this.extdata.detaildata.latitude = posinfo.coordinary.latitude;
      _this.extdata.detaildata.height = posinfo.coordinary.height;
      console.log(_this.extdata.detaildata);
      _this.extdata.isshowing_editpjpos = false;
    },

    // 获取 gis 地址
    getGISSrc(willauto, hideSearch) {
      return (
        window.bim_config.webgisurl +
        (willauto ? "?autoClick=1" : "?p=0") +
        (hideSearch ? "&hideSearch=1" : "&p2=0")
      );
    },

    // 在线裁切项目缩略图——确定——回调
    _onok_editimage_callback(base, binary) {
      var _this = this;
      _this.extdata.detaildata.boi_miniimg = base;
      _this.extdata.isshowing_image = false;
    },

    // 在线裁切项目缩略图——恢复默认
    _reset_editimage() {
      var _this = this;
      _this.$refs.cropImg.resetImg();
    },

    // 在线裁切项目缩略图——确定
    _onok_editimage() {
      var _this = this;
      _this.$refs.cropImg.cropImage();
    },

    // 在线裁切项目缩略图——取消
    _oncancel_editimage() {
      var _this = this;
      _this.extdata.isshowing_image = false;
    },

    // 删除项目的确认提示框中输入文本的回调
    delpj_confirminput(str) {
      var _this = this;
      _this.extdata.delpj_inputting = str;
    },

    // 点击删除项目的确定按钮（暂未判断输入文本及按钮可用性）
    delpj_ok() {
      var _this = this;
      if (_this.extdata.delpj_inputting != _this.extdata.detaildata.FullName) {
        _this.$message.error("输入的名称不正确");
        return;
      }
      _this.extdata.isshowing_delpj = false;
      _this.$emit("onconfirmremove", _this.projectId);
    },

    setpjprop_ok(id, text) {
      var _this = this;
      _this.extdata.detaildata.IsPublic = id == "0" ? false : true;
      _this.extdata.isshowing_editpjprop = false;
    },

    changepm_ok(userId, realname) {
      var _this = this;
      _this.extdata.isshowing_transpm = false;
      _this.extdata.detaildata.mgr_UserId = userId;
      _this.extdata.detaildata.mgr_RealName = realname;
    },

    editpjname_ok(str) {
      var _this = this;

      // 设置当前项目的名称，并关闭对话框
      _this.extdata.detaildata.FullName = str;
      _this.extdata.isshowing_editpj = false;
    },

    getProjectDetail() {
      var _this = this;
      var _Token = _this.$staticmethod.Get("Token");
      _this
        .$axios({
          method: "get",
          url: `${
            window.bim_config.webserverurl
          }/api/User/Project/GetProject?token=${_Token}&projectid=${
            _this.projectId
          }`
        })
        .then(x => {
          //debugger;
          if ((x.status == 200) & (x.data.Ret > 0)) {
            _this.extdata.detaildata.CreateDate = x.data.Data.CreateTime;
            _this.extdata.detaildata.Description = x.data.Data.Description;
            _this.extdata.detaildata.FullName = x.data.Data.ProjectName;
            _this.extdata.detaildata.RealName = x.data.Data.OrganizeName;
            _this.extdata.detaildata.boi_miniimg = x.data.Data.Thumbnail;
            _this.extdata.detaildata.IsPublic = x.data.Data.IsPublic;
            // _this.extdata.detaildata.Address = x.data.Data.Address;  // 地址暂时不要
            _this.extdata.detaildata.mgr_UserId = x.data.Data.ManagerId;
            _this.extdata.detaildata.mgr_RealName = x.data.Data.LeaderUserName;
           
            _this.showfront = true;
          }else if(x.data.Ret == -9999){
            this.$message.error(x.data.Msg);
            _this.showfront = true;
            return
          }
        })
        .catch(x => {});
    },
    _cfgbtnclick() {
      var _this = this;
      _this.$emit("cfgbtnclick");
    },
    _oncancel() {
      var _this = this;
      _this.$emit("oncancel");
    },
    _onok() {
      var _this = this;
      _this.$emit("onok", _this.extdata.detaildata);
    }
  },
  props: {
    zIndex: {
      type: Number,
      required: false
    },
    title: {
      type: String,
      required: false
    },
    width: {
      type: String,
      required: false
    },
    projectId: {
      type: String,
      required: false
    }
  }
};
</script>
<style scoped>

._css-pjinfo-cons-text{
  flex:1;
  box-sizing: border-box;
  padding:0 16px 0 16px;
  text-align: left;
  height: 40px;
  line-height: 40px;
  background-color: rgba(0,0,0,0.04);
}

._css-pjinfo-moni-text{
  flex:1;
  box-sizing: border-box;
  padding:0 16px 0 16px;
  text-align: left;
  height: 40px;
  line-height: 40px;
  background-color: rgba(0,0,0,0.04);
}

._css-moni-input{
  border:none;
  outline: none;
  background-color: transparent;
}

._css-cons-input{
  border:none;
  outline: none;
  background-color: transparent;
}

._css-pjinfo-editpos {
  position: fixed;
  width: 100%;
  height: 100%;
  z-index: 1;
}

._css-clipcontainer {
  padding-left: 16px;
  padding-right: 16px;
}

._css-pjinfo-imageedit-in {
  width: 510px;
  min-height: 410px;
  background-color: #fff;
  box-shadow: 0px 13px 24px -17px rgba(11, 41, 62, 0.8);
  border-radius: 4px;
}

._css-pjinfo-imageedit {
  position: fixed;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.3);
  align-items: center;
  justify-content: space-around;
  display: flex;
}

._css-pjinfo-descarea {
  background-color: transparent;
  border: none;
  margin: 0px;
  width: 100%;
  height: 100%;
  outline: none;
  resize: none;
  font-size: 14px;
  color: rgba(0, 0, 0, 1);
  font-family: Arial, Helveetica, sans-serif;
}

._css-memberinfo-headconfigbtn:hover ._css-pjinfo-editoptions {
  display: block;
}

._css-pjinfo-editoptions {
  display: none;
  position: absolute;
  right: -20px;
  top: 32px;
  padding-top: 4px;
  padding-bottom: 4px;
  width: 140px;
  background: rgba(255, 255, 255, 1);
  box-shadow: 0px 1px 3px 0px rgba(0, 21, 41, 0.12);
  border-radius: 2px;
}

._css-pjinfo-oitem {
  height: 40px;
  width: 100%;
  text-align: left;
  padding-left: 20px;
  line-height: 40px;
  cursor: pointer;
  color: rgba(0, 0, 0, 0.65);
  font-size: 14px;
  box-sizing: border-box;
}

._css-pjinfo-oitem:hover {
  background-color: rgba(0, 0, 0, 0.04);
}

._css-pjinfo-posinner {
  height: 100%;
  margin-left: 24px;
  margin-right: 24px;
  display: flex;
  align-items: center;
}

._css-pjinfo-pos-btn {
  margin-right: 16px;
}

._css-pjinfo-pos-text {
  margin-left: 16px;
  margin-right: 16px;
  height: 22px;
  line-height: 22px;
  text-align: left;
  color: rgba(0, 0, 0, 0.65);
  font-size: 14px;
  flex: 1;
}

._css-pjinfo-pos-title {
  width: 70px;
  height: 22px;
  line-height: 22px;
  margin-left: 16px;
  color: rgba(0, 0, 0, 0.85);
  font-size: 14px;
  font-weight: 600;
  text-align: left;
}

._css-pjinfo-desc {
  height: 180px;
  margin-top: 12px;
}

._css-pjinfo-descinner {
  height: 100%;
  margin-left: 24px;
  margin-right: 24px;
  display: flex;
}

._css-pjinfo-desctitle {
  margin-left: 16px;
  width: 70px;
  height: 22px;
  line-height: 22px;
  padding-top: 13px;
  font-size: 14px;
  color: rgba(0, 0, 0, 0.85);
  text-align: left;
  font-weight: 600;
}

._css-pjinfo-desctext {
  flex: 1;
  padding: 12px 15px;
  text-align: left;
  font-size: 14px;
  color: rgba(0, 0, 0, 0.65);
  background-color: rgba(0, 0, 0, 0.04);
}

._css-pjinfo-pos {
  height: 48px;
  margin-top: 18px;
}

._css-pjinfo-pos.pos2 {
  height: 48px;
  margin-top: 6px;
}

._css-member-projectheadtext {
  margin-left: 16px;
  /* height:44px; */
  line-height: 48px;
  color: rgba(0, 0, 0, 0.85);
  font-size: 14px;
  font-weight: 600;
  text-align: left;
}
._css-member-projectitemtext {
  margin-left: 16px;
  /* height:44px; */
  line-height: 48px;
  color: rgba(0, 0, 0, 0.65);
  font-size: 14px;
  text-align: left;
}
._css-member-projects {
  height: 192px;
  margin-left: 24px;
  margin-right: 24px;
}
._css-member-projecthead {
  height: 48px;
  display: flex;
  align-items: center;
}
._css-member-projectheadtitle {
  height: 100%;
  flex: 1;
}
._css-member-projectheadr {
  height: 100%;
  display: flex;
  align-items: center;
}
._css-member-title {
  height: 22px;
  line-height: 22px;
  color: rgba(0, 0, 0, 0.65);
}
._css-member-checkbox {
  width: 16px;
  height: 16px;
  margin-left: 16px;
  margin-right: 16px;
  cursor: pointer;
  font-size: 16px;
  color: rgba(0, 0, 0, 0.45);
  background-size: 16px 16px !important;
}
._css-member-projectbody {
  height: calc(100% - 48px);
  overflow-y: auto;
}
._css-member-projectitem {
  height: 48px;
  display: flex;
  align-items: center;
  padding-left: 70px;
}
._css-number-title {
  margin-left: 16px;
  height: 22px;
  line-height: 22px;
  color: rgba(0, 0, 0, 0.85);
  font-size: 14px;
  font-weight: 600;
}
._css-number-edit {
  margin-left: 24px;
  margin-right: 24px;
  flex: 1;
}
._css-number-show {
  height: 20px;
  min-width: 40px;
  line-height: 20px;
  color: rgba(0, 0, 0, 0.65);
  margin-right: 16px;
  font-size: 12px;
}
._css-number-set {
  height: 48px;
  margin-left: 24px;
  margin-right: 24px;
  margin-top: 18px;
  display: flex;
  align-items: center;
}
._css-mhbottom-phoneicon {
  width: 12px;
  height: 12px;
  margin-left: 8px;
  font-size: 12px;
  color: rgba(0, 0, 0, 0.45);
}
._css-mhbottom-phonetext {
  height: 20px;
  line-height: 20px;
  margin-left: 4px;
  font-size: 12px;
  color: rgba(0, 0, 0, 0.45);
}
._css-mhbottom-phone {
  margin-left: 16px;
  display: flex;
  align-items: center;
}
._css-mhbottom-email {
  height: 20px;
  line-height: 20px;
  color: rgba(0, 0, 0, 0.45);
  font-size: 12px;
}
._css-mhtop-name {
  height: 20px;
  line-height: 20px;
  font-weight: 600;
  color: rgba(0, 0, 0, 0.85);
  font-size: 12px;
}
._css-mhtop-status-nonpublic {
  height: 20px;
  line-height: 20px;
  font-size: 12px;
  background-color: rgba(250, 84, 28, 0.1);
  color: rgba(250, 84, 28, 1);
  width: 60px;
  margin-left: 16px;
  border-radius: 2px;
  border: 1px solid rgba(250, 84, 28, 0.45);
  box-sizing: border-box;
}

._css-mhtop-status-public {
  height: 20px;
  line-height: 20px;
  font-size: 12px;
  background-color: rgba(24, 144, 255, 0.1);
  color: rgba(24, 144, 255, 1);
  width: 60px;
  margin-left: 16px;
  border-radius: 2px;
  border: 1px solid rgba(24, 144, 255, 0.45);
  box-sizing: border-box;
}

._css-memberinfo-headmtop {
  width: 100%;
  height: 20px;
  display: flex;
  align-items: center;
}
._css-memberinfo-headmbottom {
  width: 100%;
  height: 20px;
  display: flex;
  align-items: center;
}
._css-memberinfo-headmiddle {
  flex: 1;
  height: 100%;
  margin-left: 8px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
._css-memberinfo-headconfigbtn {
  width: 20px;
  height: 20px;
  margin-right: 40px;
  /* border: 1px dashed red; */
  font-size: 20px;
  color: #1890ff;
  cursor: pointer;
}
._css-memberinfo-headicon {
  height: 40px;
  width: 40px;
  margin-left: 24px;
  background-color: #282e3d;
  border-radius: 4px;
  line-height: 40px;
  color: #fff;
}
._css-memberinfo-head {
  height: 44px;
  margin-top: 24px;
  display: flex;
  align-items: center;
  position: relative;
}
._css-comps-front {
  width: 410px;
  min-height: 48px;
  background-color: rgba(255, 255, 255, 1);
  box-shadow: 0px 13px 24px -17px rgba(11, 41, 62, 0.8);
  border-radius: 4px;
}
._css-all-pjinfo {
  position: fixed;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.15);
  display: flex;
  align-items: center;
  justify-content: space-around;
}
</style>