<template>
    <div class="_css-comps-select" 
    :class="inputclass || ''"
    >
        <CompsEloSelect :datas="items" :text="text" :width="width || '100%'"
        :itemWitdh="itemWitdh"
        @onselected="_selected"
        :selectorClass="selectorClass"
        :selectorSize="selectorSize"
        :clearinnersplitter="true"
        >

        </CompsEloSelect>
    </div>
</template>
<script>
/**
      <div class="_css-line1 _css-line">
          <CompsSelect
          :items="[{id:'0',text:'非公开'}, {id:'1', text:'公开'}]"
          :text="extdata.data_selectedtext"
          :val="extdata.data_selectedid"
          @onselected="_selected"
          ></CompsSelect>
      </div>
 */
import CompsEloSelect from "@/components/CompsElOverride/CompsEloSelect";
export default {
    components:{
        CompsEloSelect
    },
    data(){
        return {
            extdata:{

            }
        };
    },
    methods:{
        _selected(obj){
            var _this = this;
            _this.$emit("onselected", obj.id, obj.text);
        }
    },
    props:{
        items:{
            type: Array,
            required: true
        },
        text:{
            type: String,
            required: true
        },
        val:{
            type: String,
            required: true
        },
        inputclass:{
            type: String,
            required: false
        },
        width:{
            type: String,
            required: false
        },
        itemWitdh:{
            type: String,
            required: false
        },
        selectorClass:{
            type: String,
            required: false
        },
        selectorSize:{
            type: String,
            required: false
        }
    }
}
</script>
<style scoped>
._css-comps-select{
    position: relative;
}
</style>
