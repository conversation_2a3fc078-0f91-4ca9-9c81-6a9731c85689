
<template>
  <div class="container">

    <!--input输入框-->
    <div class="input-content" v-if="!needAt">
      <input class="input-field" type="text" :id="randomID" :value="value" :placeholder="placeholder" @input="handleInput">
      <label class="label-field" :for="randomID">{{ label }}{{ labelFootnote }}</label>
      <div class="bottom-line"></div>
    </div>

    <!--div输入框 @功能-->
    <div class="input-content" v-if="needAt">
      <div :class="[{divAfter : divHasInput,'div-placeholder' : divhasFocus},'div-field css-prel']"
           ref="inputDiv"
           @focus="divInputOnfocus"
           @blur="divInputOnBlur"
           @mousedown="_stopPropagation($event)"
           contenteditable="true"
           :value="value"
           :placeholder="placeholder"
           :style="{height: height}"
           id="test"
           @input="handleInput"></div>
      <label @click="divInputOnfocus" class="label-field" >{{ label }}{{ labelFootnote }}</label>
      <div class="bottom-line"></div>

      <div v-if="togglePersonnelList"
           v-clickOutClose="closePersonnelList"
           v-loading="isLoading"
           class="personnel-list"
           :style="{ top: `${caretCoordinate.y}px`,left: `${caretCoordinate.x}px` }">
        <div class="list-item" v-for="(item,index) in dataList" :key="item.UserId">
          <button @click="selectCurrentPerson(item,index)">
            <span class="name-icon">{{ item.RealName | setAbbreviation}}</span>
            <p class="name">{{ item.RealName }}</p>
            <p class="email">{{ item.Email || '—' }}</p>
          </button>
        </div>
      </div>
    </div>


  </div>
</template>

<script>
    export default {
      name: "compsAnimateInput",
      data() {
        return {
          value: '',
          randomID: '',//随机id
          divhasFocus: false,//div获取焦点
          divHasInput: true,//div输入框是否输入了文字
          caretPosition: 0,//光标位置
          caretCoordinate: {},//光标坐标
          divContent: '',//div编辑框的内容(带有html标签)
          inputContent: '',//编辑框的内容(纯文本)
          arrSubscript: [],//div编辑框中已存在人员的数据集合
          togglePersonnelList: false,//是否展示人员列表
          isLoading: false,
          textValueArr: [],//数组化后的纯文本字符串
          htmlValueArr: [],//数组化后的带标签的字符串
          atPersonalArr: [],//输入框中已@的成员的span标签集合
        }
      },

      props: {
        label: {//标签名字
          type: String,
          default: '请输入'
        },

        labelFootnote: {//标签的补充说明 如：标题（选填）
          type: String,
          default: '',
        },

        placeholder: {//input提示文字
          type: String,
          default: ''
        },

        isRequired: {//是否是必填项
          type: Boolean
        },

        needAt: {//是否需要@功能 默认为false不需要
          type: Boolean,
          default: false
        },

        dataList: {//@人员列表数据
          type: Array,
        },

        height: {
          type: [String,Number]
        }
      },

      created() {
        this.setRandomClass();
      },

      methods: {
        _stopPropagation(ev) {
          ev.stopPropagation();
        },

        closePersonnelList(){
          this.togglePersonnelList = false
        },

        //点击选择人员下拉列表某项
        selectCurrentPerson(item,index) {

          var l = document.getElementById('test').childNodes;
          var txt = l[l.length-1]
          var start = txt.nodeValue.lastIndexOf('@');
          var leng = txt.length - start

          //插入名称前移除输入的@
          for(var i = 0;i<leng ;i++){
            this.$refs.inputDiv.focus();
            document.execCommand('Delete');
          }


          let nameSpan = `<span data-index="${index}" contenteditable="false">&nbsp;@${item.RealName}&nbsp;</span>`;
          //在光标位置插入选择的名字
          this.insertHtmlAtCaret(nameSpan);
          this.getHasSelectPersonMsg();
          //移除placeholder
          this.divHasInput = false;
          //关闭列表
          this.togglePersonnelList = false;

          this.returnInputValue(this.inputContent,this.divContent,this.arrSubscript);

        },

        //div输入框失去焦点时
        divInputOnBlur(e) {
          let value = e.target.innerText;

          if (value.length!= 0 && value!='<br>') {
            this.divhasFocus = true;
          } else {
            this.divhasFocus = false;
          }
          return value;
        },

        //div输入框获取焦点时
        divInputOnfocus() {
          this.divhasFocus = true;
          this.$refs.inputDiv.focus();
        },

        handleInput(event) {
          let value = '';
          // divContent: '',//div编辑框的内容(带有html标签)
          //   inputContent: '',//编辑框的内容(纯文本)
          //   arrSubscript: '',//人员所在数组的下标
          // this.divContent = '';
          // this.inputContent = '';
          // this.arrSubscript = [];
          //正常输入框
          if (!this.needAt) {
            this.inputContent = event.target.value;
            this.returnInputValue(this.inputContent);
          } else {
            //带有@功能的输入框

            //获取输入框的纯文本
            this.inputContent = event.target.innerText;

            //获取输入框的内容
            this.divContent = event.target.innerHTML;
            //获取输入框中@的成员的标签集合
            this.atPersonalArr = event.target.getElementsByTagName("span");
            // console.log(this.atPersonalArr);

            this.getHasSelectPersonMsg();

            if (this.divContent.length!= 0 && this.divContent!='<br>') {
              //如果输入框不为空 移除placeholder
              this.divHasInput = false;
            } else {
              this.divHasInput = true;
            }

            //获取光标位置
            this.caretPosition = this.getDivPosition(event.target);

            //获取光标坐标
            this.caretCoordinate = this.getSelectionCoords();
            this.caretCoordinate.y += 20;
            this.caretCoordinate.x += 10;

            //拆分已输入的字符串
            //如果最后输入的字符为@ 则打开人员列表
            this.textValueArr = this.inputContent.split('');
            // console.log(this.textValueArr);
            if (this.textValueArr[this.caretPosition - 1] == '@' || this.togglePersonnelList) {
              this.getPersonList();
            }



            this.returnInputValue(this.inputContent,this.divContent,this.arrSubscript);
          }

          // console.log(this.divContent);
        },

        //触发v-model 返回输入的内容
        //参数：value（纯文本），htmlValue（包含有span标签的文本），subscript(已@人员所在数组的下标)
        returnInputValue(value,htmlValue,subscript) {
          let msg = {
            value: value,
            nodeValue: htmlValue,
            subscript: subscript
          };
          console.log(msg)
          this.$emit('input',msg);
        },

        //获取人员列表
        getPersonList() {
          this.togglePersonnelList = true;
          // 获取input里的输入内容 然后截取@后面得内容
          // console.log(this.divContent.match(/@(\S*)/g))
          this.$emit('searchInput',this.divContent.slice(this.divContent.lastIndexOf('@')+1,this.divContent.length))
          //查询人员(暂时写个定时器模拟)
          // this.isLoading = true;
          // setTimeout(_=>{
          //   this.isLoading = false;
          // },1000);
        },

        //随机分配ID
        setRandomClass() {
          this.randomID = Date.parse(new Date()).toString() + Math.random();
        },

        //获取可编辑div的光标位置
        getDivPosition(element) {
          let caretOffset = 0;
          let doc = element.ownerDocument || element.document;
          let win = doc.defaultView || doc.parentWindow;
          let sel;
          if (typeof win.getSelection != "undefined") {
            sel = win.getSelection();
            if (sel.rangeCount > 0) {//选中的区域
              let range = win.getSelection().getRangeAt(0);
              let preCaretRange = range.cloneRange();//克隆一个选中区域
              preCaretRange.selectNodeContents(element);//设置选中区域的节点内容为当前节点
              preCaretRange.setEnd(range.endContainer, range.endOffset);  //重置选中区域的结束位置
              caretOffset = preCaretRange.toString().length;
            }
          } else if ((sel = doc.selection) && sel.type != "Control") {
            let textRange = sel.createRange();
            let preCaretTextRange = doc.body.createTextRange();
            preCaretTextRange.moveToElementText(element);
            preCaretTextRange.setEndPoint("EndToEnd", textRange);
            caretOffset = preCaretTextRange.text.length;
          }
          return caretOffset;
        },

        //获取当前输入框中已经@的人员的信息
        getHasSelectPersonMsg() {
          if (this.atPersonalArr.length > 0) {
            this.arrSubscript = [];
            for(let i=0;i<this.atPersonalArr.length;i++) {
              // console.log(this.atPersonalArr[i].dataset.index);
              this.arrSubscript.push(this.dataList[this.atPersonalArr[i].dataset.index]);
            }
          }
        },

        //获取可编辑div的光标坐标
        getSelectionCoords(win) {
          win = win || window;
          let doc = win.document;
          let sel = doc.selection, range, rects, rect;
          let x = 0, y = 0;
          if (sel) {
            if (sel.type != "Control") {
              range = sel.createRange();
              range.collapse(true);
              x = range.boundingLeft;
              y = range.boundingTop;
            }
          } else if (win.getSelection) {
            sel = win.getSelection();
            if (sel.rangeCount) {
              range = sel.getRangeAt(0).cloneRange();
              if (range.getClientRects) {
                range.collapse(true);
                rects = range.getClientRects();
                if (rects.length > 0) {
                  rect = rects[0];
                }
                // 光标在行首时，rect为undefined
                if(rect){
                  x = rect.left;
                  y = rect.top;
                }
              }

              if ((x == 0 && y == 0) || rect === undefined) {
                let span = doc.createElement("span");
                if (span.getClientRects) {

                  span.appendChild( doc.createTextNode("\u200b") );
                  range.insertNode(span);
                  rect = span.getClientRects()[0];
                  x = rect.left;
                  y = rect.top;
                  let spanParent = span.parentNode;
                  spanParent.removeChild(span);

                  spanParent.normalize();
                }
              }
            }
          }
          return { x: x, y: y };
        },

        insertHtmlAtCaret(html) {
          let sel, range;
          if (window.getSelection) {
            //创建getSelection对象
            sel = window.getSelection();
            if (sel.getRangeAt && sel.rangeCount) {
              range = sel.getRangeAt(0);
              range.deleteContents();

              let el = document.createElement("div");
              el.innerHTML = html;
              let frag = document.createDocumentFragment(), node, lastNode;
              while ((node = el.firstChild)) {
                lastNode = frag.appendChild(node);
              }
              range.insertNode(frag);
              if (lastNode) {
                range = range.cloneRange();
                range.setStartAfter(lastNode);
                range.collapse(true);
                sel.removeAllRanges();
                sel.addRange(range);
              }
            }
          } else if (document.selection && document.selection.type != "Control") {
            document.selection.createRange().pasteHTML(html);
          }
        },
      },

      // computed:{
      //   currentValue:function () {
      //     return this.value
      //   }
      // },
    }
</script>
<style>
  .input-content .div-field span {
    font-weight: 400;
    color: rgba(24,144,255,1);
  }
</style>
<style scoped>
  .input-content {
    position: relative;
    display: flex;
    flex-flow: column-reverse;
    margin-bottom: 1em;
  }

  .input-content > * {
    transition: all 0.5s;
  }

  .input-content .bottom-line {
    position: absolute;
    left: 0;
    bottom: 0;
    width: 0;
    height: 1px;
  }

  .input-content .personnel-list {
    position: fixed;
    z-index: 20;
    width: 180px;
    height: 258px;
    padding: 4px 0;
    box-sizing: border-box;
    background-color: #FFFFFF;
    border-radius: 2px;
    overflow: auto;
    box-shadow: 0 1px 3px 0 rgba(0,21,41,0.2);
  }

  .input-content .personnel-list .list-item {
    cursor: pointer;
    position: relative;
    padding: 8px 5px 8px 37px;
  }

  .input-content .personnel-list .list-item > button {
    cursor: pointer;
    width: 100%;
    border: none;
    outline: none;
    background: none;
    padding: 0;
    margin: 0;
    font-size: 14px;
  }

  .input-content .personnel-list .list-item:hover {
    background-color: rgba(0,0,0,0.04);
  }

  .personnel-list .list-item .name-icon {
    position: absolute;
    top: 13px;
    left: 7px;
    width: 24px;
    height: 24px;
    color: #FFFFFF;
    font-size: 12px;
    font-weight: 500;
    overflow: hidden;
    line-height: 24px;
    border-radius: 2px;
    text-align: center;
    background-color: rgba(32,32,32,1);
  }

  .personnel-list .list-item p {
    margin: 0;
    text-align: left;
  }

  .personnel-list .list-item p.name {
    font-weight: 500;
    color: rgba(0,0,0,1);
  }

  .personnel-list .list-item p.email {
    font-size: 12px;
    font-weight: 400;
    color: rgba(0,0,0,0.45);
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
  }

  .input-content .div-field,
  .input-content .label-field,
  .input-content .input-field {
    touch-action: manipulation;
  }

  .input-content .div-field,
  .input-content .input-field {
    min-height: 30px;
    font-size: 14px;
    border: 0;
    font-family: inherit;
    -webkit-appearance: none;
    border-radius: 0;
    padding: 0;
    caret-color: #1890FF;
    cursor: text;
    font-weight:400;
    color:rgba(0,0,0,0.85);
    border-bottom: 1px solid rgba(0,0,0,0.45);
  }

  .input-content .div-field {
    box-sizing: border-box;
    padding: 5px 0;
  }

  .input-content .div-field:focus,
  .input-content .input-field:focus {
    outline: 0;
  }

  .input-content .label-field {
    height: 20px;
    font-size: 14px;
    cursor: text;
    font-weight: 400;
    color: rgba(0,0,0,0.45);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    transform-origin: left bottom;
    transform: translate(0, 26px);
  }

  ::-webkit-input-placeholder {
    opacity: 0;
    transition: inherit;
  }

  .input-content .input-field:focus::-webkit-input-placeholder {
    opacity: 1;
  }

  .input-content .input-field:not(:placeholder-shown) + .label-field,
  .input-content .input-field:focus + .label-field {
    transform: translate(0, 0);
    cursor: pointer;
    font-size: 12px;
    color: rgba(0,0,0,0.65);
  }

  .input-content .input-field:focus  + .label-field + .bottom-line,
  .div-placeholder + .label-field + .bottom-line {
    width: 100%;
    height: 2px;
    background-color: #1890FF;
  }

  .div-field.divAfter::after  {
    content: attr(placeholder);
    position: absolute;
    top: 5px;
    color: rgba(0,0,0,0.55);
    opacity: 0;
  }

  .div-placeholder + .label-field {
    transform: translate(0, 0);
    cursor: pointer;
    font-size: 12px;
    color: rgba(0,0,0,0.65);
  }

  .div-placeholder.div-field::after  {
    opacity: 1;
  }
</style>
