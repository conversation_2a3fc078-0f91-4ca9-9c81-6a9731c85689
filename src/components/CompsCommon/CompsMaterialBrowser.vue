<template>
    <div class="_css-docbrowser" :style="{'z-index': m_zIndex || 1002}"
    >
        <div class="_css-docbrowser-in" :style="getFrontStyle()"
        v-drag="greet"  
        >

            <!-- 标题区域 -->
            <div
            class="_css-docin-title css-fc"
            >
                <!-- 对话框标题及关闭按钮 -->
                <div class="_css-title-content css-f1">
                    {{init_title}}
                    
                </div>
                <div class="_css-dialog-search" v-if="isProgress">
                    <el-input  placeholder="请输入你要搜索的内容" suffix-icon="el-icon-search" icon="search"  class="search" style="width:55%" v-model="tableSearch"></el-input>
                    <!-- <i class="icon-interface-search"></i> -->
                    <span @click="handelClickMaterialEdit">查看已关联构件</span>
                </div>
                <div 
                @click="$emit('onclose')"
                class="_css-title-closebtn css-closebtn icon-suggested-close"></div>
                <!-- //对话框标题及关闭按钮 -->

            </div>
            <!-- //标题区域 -->
            <div class="_css-edit-selected-list" v-if="materialEditClick">
                <template
                    v-if="selectedObjList && selectedObjList.length > 0"
                >
                    <div class="mgr-list">
                        <div 
                        v-for="thedoc in selectedObjList"
                        :key="thedoc.bm_guid"
                        class="css-common-line ">

                            <div 
                            class="_css-flowModelNameSelected _css-closehoverctn">
                                <div class="_css-addattach-icon icon-interface-associated-component"></div>
                                <div class="_css-modelname-show" >{{thedoc.bm_materialname}}（{{thedoc.bm_materialcode}}）</div>
                                <div class="_css-removebtn-ctn" >
                                    <div 
                                    @click="func_removetask($event, thedoc)"
                                    class="icon-suggested-close _css-formadd-closebtn _css-static">
                                    </div>
                                </div>
                            </div> 
                        </div>
                    </div>
                    <div class="_css-btn-selected">
                        <div class="_css-clear-edit-selected" @click="clearSelected">清空</div>
                        <div class="_css-clear-edit-selected _css-close" @click="materialEditClick=false">取消</div>
                    </div>
                </template>
                <!-- <template v-else>
                    <div class="css-common-line">
                        <div class="_css-modelname-show _css-line-none">暂无已关联构件</div>
                    </div>
                </template> -->
            </div>
            <!-- 内容区域 -->
            <div
            @mousedown="_stopPropagation($event)"
            class="_css-docin-content"
            >
                <!-- 左树235右列表 -->
                <div class="_css-docin-tree" 
                v-loading="m_treeloading"
                element-loading-text="加载中..."
                >
                    <el-tree :data="m_rootfolders" :props="treem_props" lazy
                    ref="ref_wftc"
                    class="_css-customstyle"
                    :expand-on-click-node="false"
                    @node-collapse="node_collapse"
                    @node-expand="node_expand"
                    @node-click="node_click"
                    :load="treefunc_loadChild"

                    node-key="wftc_guid"
                    :highlight-current="true"
                    :auto-expand-parent="true"
                    >
                    <span class="css-fc _css-treenode-content" slot-scope="{ data }">
                        <i class="css-icon20 css-fs18 css-fc css-jcsa" :class="data.classname" ></i>
                        <span :title="data.bmc_name" class="css-ml4 _css-treenodelabel">{{data.bmc_name}}</span>
                    </span>
                    </el-tree>
                </div>
                <div class="_css-docin-list"
                v-loading="m_tableloading"
                element-loading-spinner="el-icon-loading"
                element-loading-background="rgba(0, 0, 0, 0)"
                element-loading-text="数据加载中..."
                >

                    <!-- 面包屑显示区域 -->
                    <div v-if="m_openstacks.length" class="_css-openstack-ctn">

                        <div 
                        @click="func_openparent($event)"
                        class="_css-openstack-item _css-first">返回上一级</div>
                        <div class="css-breadcrumb-splitter _css-openstack-splitter">/</div>

                        <div 
                        @click="func_openroot($event)"
                        class="_css-openstack-item _css-first">项目文档</div>
                        <div class="css-breadcrumb-splitter _css-openstack-splitter">/</div>

                        <template v-for="(ositem, index) in m_openstacks"
                        >
                            <div 
                            @click="func_openstackitem($event, index)"
                            :key="ositem.FileId"
                            class="_css-openstack-item">{{ositem.FileName}}</div>
                            <div 
                            :key="ositem.FileId + '__2'"
                            class="css-breadcrumb-splitter _css-openstack-splitter">/</div>
                        </template>
                        
                    </div>
                    <!-- //面包屑显示区域 -->

                    <el-table
                    use-virtual
                    :row-height="rowHeight"
                    ref="doctable"
                    :highlight-current-row="false"
                    @row-contextmenu="row_contextmenu"
                    @row-click="row_click"
                    @row-dblclick="on_row_dblclick"
                    :border="true"
                    :stripe="false"
                    :data="m_currentfolderfiles"
                    style="width: 100%;"
                    :default-sort="{prop: 'date', order: 'descending'}"
                    height="520"
                    class="_css-table-ele css-scroll _css-customstyle"
                    :row-class-name="tableRowClassName"
                    :header-cell-style="{'background-color':'transparent'}"
                    >
                            <el-table-column
                            type="index"
                            :index="getRowIndex"
                            width="50">
                            </el-table-column>

                            <!-- 复选框列 -->
                            <el-table-column
                            width="50"
                            :resizable="false"
                            >
                                <template slot="header" >
                                <span
                                    class="css-cb css-icon12 css-cp css-blk"
                                    :class="{'mulcolor-interface-checkbox-selected':func_testcontainsAll()}"
                                    @click="func_switchall($event)"
                                ></span>
                                </template>
                                <template slot-scope="scope">
                                <span
                                    class="css-cb css-icon12 css-cp"
                                    :class="{'mulcolor-interface-checkbox-selected':func_testcontainsThis(scope.row)}"
                                    @click="func_switchitem($event, scope.row)"
                                ></span>
                                </template>
                            </el-table-column>
                            <!-- 复选框列 -->

                            <el-table-column
                            :resizable="true"
                            class="_css-col-filename"
                            prop="bm_materialcode"
                            label="编码" 
                            fit
                            min-width='100'
                            >
                                <template slot-scope="scope" >
                                    <i
                                        :class="'css-icon20 css-fs18 css-fc css-jcsa ' + $staticmethod.getIconClassByExtname(scope.row.FileName, scope.row.FileSize)"
                                    ></i>

                                     <span
                                        class="css-cp css-hoverunder css-ml10 css-ellipsis basic-font-color-emphasize"
                                     >
                                        {{scope.row.bm_materialcode}}
                                     </span>
                                </template>
                            </el-table-column>

                           <el-table-column
                            :resizable="true"
                            class="_css-col-filename"
                            prop="bm_materialname"
                            label="构件名称"
                            min-width="168" 
                            >
                                <template slot-scope="scope">
                                    <i
                                        :class="'css-icon20 css-fs18 css-fc css-jcsa ' + $staticmethod.getIconClassByExtname(scope.row.FileName, scope.row.FileSize)"
                                    ></i>

                                     <span
                                        class="css-cp css-hoverunder css-ml10 css-ellipsis basic-font-color-emphasize"
                                        
                                     >
                                        {{scope.row.bm_materialname}}
                                     </span>
                                </template>
                            </el-table-column>

                    </el-table>
                    <el-pagination
                        class="el-pagination-cus"
                        @size-change="onPageSizeChange"
                        @current-change="onPageNumChange"
                        :current-page="pagerInfo.pageNum"
                        :page-sizes="[20,50,100,200,500,1000]"
                        :page-size="pagerInfo.pageSize"
                        layout="total, sizes, prev, pager, next, jumper"
                        :total="pagerInfo.total">
                    </el-pagination>
                </div>
                <!-- //左树235右列表 -->

            </div>
            <!-- //内容区域 -->
            
            <!-- 对话框按钮区域 -->
            <div
            class=" css-common-zdialogbtnctn _css-docin-btnctn"
            >
                <!-- 取消及确定按钮 -->
                <zbutton-function
                    :init_text="'取消'"
                    :init_fontsize="14"
                    :debugmode="true"
                    :init_height="undefined"
                    :init_width="'76px'"
                    :init_bgcolor="'#fff'"
                    :init_color="'#1890FF'"
                    @onclick="$emit('onclose')"
                    >
                </zbutton-function>
                    <zbutton-function
                    :init_text="'确定'"
                    :init_fontsize="14"
                    :debugmode="true"
                    :init_height="undefined"
                    :init_width="'76px'"
                    @onclick="_emit_onok()"
                    >
                </zbutton-function>
                <!-- //取消及确定按钮 -->

            </div>
            <!-- //对话框按钮区域 -->

        </div>
    </div>
</template>
<script>
export default {
    data() {
        return {

            m_treeloading: false,
            m_tableloading: false,

            // 拖动相关
            // --------
            val:'123',

            m_organizeId:'',
            m_bimcomposerId:'',

            // 接收传入参数
            // -----------
            m_zIndex: 0,
            m_height: 0,
            m_width: 0,

            // 根级文件夹
            // ---------
            m_rootfolders: [],

            // 当前正在显示的文件夹及文件
            // 当前选中了哪些文件
            // 记录的打开栈
            // -----------
            m_currentfolderfiles: [],
            m_selectedfiles:[],
            m_openstacks:[],

            // 树控件的 props 属性对象
            // ----------------------
            treem_props:{
                children: "children",
                label: "FileName",
                isLeaf: "isLeaf"
            },
            rowHeight:40,
            oldSetmaterialtype: '',// 记录当前点击的和上次点击的是否为同一个
            tableSearch:'', // input搜索框
            searchTableData:[], // 备份表格的数据，用于搜索过滤
            materialEditClick: false,

            pagerInfo: {
                pageSize: 100,
                pageNum: 1,
                total: 0
            }, // 分页信息
            currentSpecificCode: "" // 当前的SpecificCode
        };
    },
    mounted(){
        var _this = this;
        window.docbrowervue = this;
        _this.m_zIndex = _this.init_zIndex;
        _this.m_width = _this.init_width;
        _this.m_height = _this.init_height;
        _this.m_bimcomposerId = _this.init_bimcomposerId;
        _this.m_organizeId = _this.init_organizeId;

        // 加载根级分类（不带“全部分类”)
        // ---------------------------
        _this.func_getRootCategories();

        // 加载根级文件夹及文件
        // ------------------
        // _this.func_readroot();   // 需求为初始化不加载数据，若加载取消注释即可
    },
    watch:{
        tableSearch:function(){ 
            let _this= this;
            let search = _this.tableSearch;
            let _arr = [];   // 保存过滤后的符合项
            if(search){
                _this.searchTableData.filter(function(dataNews){
                    Object.keys(dataNews).some(function(key){
                        if(key == 'bm_materialcode' ||  key == 'bm_materialname'){  // 搜索只需在编码和名称中过滤
                            if(dataNews[key].indexOf(search) > -1)_arr.push(dataNews)
                            _this.m_currentfolderfiles = [...new Set(_arr)];
                        }
                    })
                })
            }else{
                _this.m_currentfolderfiles = _this.searchTableData;  // 搜索框为空
            }
        }
    },
    methods: {
        getRowIndex(idx) {
            let pn = this.pagerInfo.pageNum - 1
            pn = pn < 0 ? 0: pn
            return (this.pagerInfo.pageSize * pn) + idx + 1
        },
        onPageSizeChange(size) {
            this.pagerInfo.pageNum = 1
            this.pagerInfo.pageSize = size
            this.func_readroot(this.currentSpecificCode)
        },
        onPageNumChange(num) {
            this.pagerInfo.pageNum = num
            this.func_readroot(this.currentSpecificCode)
        },
        // 切换全部的选中状态
        // _this.m_selectedfiles
        // m_currentfolderfiles
        // ----------------
        func_switchall(ev) {

            // 如果 m_currentfolderfiles 长度为0
            // 不操作
            // ------
            var _this = this;
            _this._stopPropagation(ev);
            if (_this.m_currentfolderfiles.length <= 0) {
                return;
            }

            // 如果 m_selectedfiles 长度小于 m_currentfolderfiles
            // 则直接赋予（深拷贝）
            // -----------------
            if (_this.m_selectedfiles.length < _this.m_currentfolderfiles.length) {
                _this.m_selectedfiles = _this.$staticmethod.DeepCopy(_this.m_currentfolderfiles);
            } else {
                _this.m_selectedfiles = [];
            }

            // 否则清空
            // --------
        },

        // 切换单条的选中状态
        // _this.m_selectedfiles
        // m_currentfolderfiles
        // -----------------
        func_switchitem(ev, item) {

            // 如果 m_currentfolderfiles 长度为0
            // 不操作
            // ------
            var _this = this;
            _this._stopPropagation(ev);
            if (_this.m_currentfolderfiles.length <= 0) {
                return;
            }

            // 如果包含它就移除掉它，否则加进来
            // -----------------------------
            if (_this.func_testcontainsThis(item)) {
                _this.m_selectedfiles = _this.m_selectedfiles.filter(x => x.bm_guid != item.bm_guid);
            } else {
                _this.m_selectedfiles.push(item);
            }

        },

        // 对于非全选框，判断当前是否包含了此条数据
        // -------------------------------------
        func_testcontainsThis(item) {
            var _this = this;
            if (_this.m_currentfolderfiles.length <= 0) {
                return false;
            }
            var hasIndex = _this.m_selectedfiles.findIndex(x => x.bm_guid == item.bm_guid);
            return hasIndex >= 0;
        },
        func_testcontainsAll() {
            var _this = this;
            if (_this.m_currentfolderfiles.length <= 0) {
                return false;
            }
            var equalall = _this.m_currentfolderfiles.length == _this.m_selectedfiles.length;
            return equalall;
        },

        _emit_onok() {
            var _this = this;
            _this.$emit("onok", _this.m_selectedfiles);
        },

        // 单击节点，加载里面的文件夹及文件
        // -----------------------------
        node_click(itemi, node, comp) {
            console.log('========happy')
            var _this = this;
            var id = itemi.bmc_guid;
            if(_this.oldSetmaterialtype !== id){

                _this.oldSetmaterialtype = id;

                // 设置 openStacks
                // ---------------
                //_this.m_openstacks = _this.$staticmethod.DeepCopy(node.data.chain);

                this.pagerInfo.pageNum = 1
                this.pagerInfo.total = 0

                // 读取文件夹及文件
                // ---------------
                _this.func_readroot(id);
            } 
        },

        // 文件大小格式化
        // -------------
        FileSize_formatter(row, column) {
            var _this = this;

            // 0B
            if (row.iszerobytes) {
            return "0B";
            }

            let size = row.FileSize;
            if (size) {
            if (size == 0) {
                // 文件夹不显示大小
                return "-";
            } else {
                // 显示文件的大小，需要转换为字符串
                return _this.$staticmethod.convertToSizeStr(size);
            }
            } else {
            // size is undefined.
            return "";
            }
        },

        // 打开指定的 openstack 中的一项
        // ---------------------------
        func_openstackitem(ev, index) {

            // slice(0, index + 1)
            // -------------------
            var _this = this;
            _this.m_openstacks = _this.m_openstacks.slice(0, index + 1);

            // 读取文件夹
            // ---------
            if (_this.m_openstacks.length > 0) {
                var folderId = _this.m_openstacks[_this.m_openstacks.length - 1].FileId;
                _this.func_readfolder(folderId);
            } else {
                _this.func_openroot();
            }

        },

        // 返回上一级
        // ----------
        func_openparent(ev) {

            // 设置打开栈
            // ---------
            var _this = this;
            _this.m_openstacks = _this.m_openstacks.slice(0, _this.m_openstacks.length - 1);

            // 打开指定文件夹
            // -------------
            if (_this.m_openstacks.length > 0) {
                var folderId = _this.m_openstacks[_this.m_openstacks.length - 1].FileId;
                _this.func_readfolder(folderId);
            } else {
                _this.func_openroot();
            }

        },

        // 点击“项目文档”打开根级目录
        // ------------------------
        func_openroot(ev) {

            // 清空“打开栈”
            // 获取数据
            // -------
            var _this = this;
            _this.m_openstacks = [];
            _this.func_readroot();
        },

        // 获取 -1000 节点上的数据，如果没有节点，则
        // --------------------------------------
        func_readroot(specificCode) {
            var _this = this;
            _this.currentSpecificCode = specificCode
            _this.m_currentfolderfiles = [];
            _this.m_tableloading = true;
            const {pageNum,pageSize} = this.pagerInfo
            let _para = `token=${this.$staticmethod.Get("Token")}&PageNum=${pageNum}&PageSize=${pageSize}&bc_guid_materialtype=${specificCode || '-1000'}&bm_materialcode=&bm_materialname=&statusIdlistjson=[]&ifhasrelation=[]&updatetimestart=&updatetimeend=&SortField=sort&SortType=asc&organizeId=${_this.m_organizeId}`

            var _url = `${this.$MgrBaseUrl.GetMaterialList_Condition2}?${_para}`;
            _this.$axios({
                url: _url,
                method: 'get',
            }).then(x => {

                // 赋予表格的数据对象
                // -----------------
                _this.m_currentfolderfiles = x.data.Data.Data;
                _this.searchTableData = x.data.Data.Data;
                _this.m_tableloading = false;
                this.pagerInfo.total = x.data.Data.Total
            }).catch(x => {
                _this.m_tableloading = false;
                console.log('catch line 156');
                this.pagerInfo = {pageSize,pageNum:1,total:0}
            })
        },
 

        // 打开文件夹
        // ---------
        enterdir(folderId, folderName) {
            var _this = this;

            // 设置 openStacks
            // ---------------
            _this.m_openstacks.push({
                FileId: folderId,
                FileName: folderName
            });

            // 读取文件夹及文件数据
            _this.func_readfolder(folderId);
        },

        // 打开某一行数据（打开某个文件夹 or 选中某个文件并直接点击“确定”）
        // 表格、列表视图的文件（夹）进入方法（包括回收站不可进的逻辑）。
        // ----------------------------------------------------------
        item_enter(row){

            // 进行文件进入动作
            // ---------------
            var _this = this;
            if (row.FileSize == '0') {

                // 进入文件夹
                // ---------
                _this.enterdir(row.FileId, row.FileName, null);

            } else {

                // 打开或预览文件
                // -------------
                _this.begin_previewfile(row, null);

            }

        },

        // 打开的是文件
        // -----------
        begin_previewfile(row) {
            console.log('打开了文件');
        },

        // 被触发的双击事件
        // ---------------
        on_row_dblclick(row, column, ev) {
            var _this = this;
            ev.stopPropagation();
            _this.item_enter(row);
        },

        // 单击的不是复选框，而是行时，切换当前行的选中状态，同时确保其它行没有被选中。
        // --------------------------------------------------------------------
        row_click(row, column, ev) {

            // 点击行时，点一下，选中，再点一下，取消这一行
            // 其它行不受影响
            // -------------
            var _this = this;
            //_this.m_selectedfiles = [row];
            ev.stopPropagation();

            if (_this.m_currentfolderfiles.length <= 0) {
                return;
            }

            // 如果包含它就移除掉它，否则加进来
            // -----------------------------
            if (_this.func_testcontainsThis(row)) {
                _this.m_selectedfiles = _this.m_selectedfiles.filter(x => x.bm_guid != row.bm_guid);
            } else {
                _this.m_selectedfiles.push(row);
            }

        },

        // 表格行右击
        // ---------
        row_contextmenu(row, column, ev) {
            console.log('表格行右击');
            var _this = this;
        },

        // 根据是否选中了某一行，返回类字符串
        // -------------------------------
        tableRowClassName({ row, rowIndex }) {
        var _this = this;
        var _sel_hasThisRow =
            _this.m_selectedfiles.filter(x => x.bm_guid == row.bm_guid).length >
            0;
        return "css-tdunder " + (_sel_hasThisRow ? "css-tabrow-selected" : "");
        },

        // 加载某个文件夹下的子文件夹
        // ------------------------
        treefunc_loadChild(node, resolve) {

            // 获取所展开节点下的子文件夹
            // ------------------------
            var _this = this;
            var _nodedata = node.data;

            // 被第一次莫名其妙调用，直接跳出
            // ----------------------------
            if (!_nodedata.bmc_code) {
                return;
            }

            // 调用接口，获取子项
            // -----------------
            var _url = `${this.$MgrBaseUrl.GetCategories}?organizeId=${_this.m_organizeId}&baseCode=${_nodedata.bmc_code}&Token=${_this.$staticmethod.Get('Token')}`;
            _this.$axios.get(_url).then(x => {

                // 拿到当前文件夹的子级，resolve 到下面
                // ----------------------------------
                var onlyFolder = x.data.Data.list;//.filter(x => x.FileSize == "0");

                // 挂上 classname
                // --------------
                for (var i = 0; i < onlyFolder.length; i++) {
                    onlyFolder[i].classname = 'icon-interface-component_classification';
                }

                _this.func_analysisfolders(onlyFolder, (toval)=>{
                    resolve(toval);
                });
                

            }).catch(x => {
                console.log('catch line 156');
            })
        },

        // 收起节点 回调
        // 展开节点 回调
        // ------------
        node_collapse(itemi, node, comp) {

            // 修改 classname
            // ---------------
            var _this = this;
            itemi.classname = "icon-interface-component_classification";
        },
        node_expand(itemi, node, comp) {
            
            // 修改 classname
            // ------------------------------------
            var _this = this;
            itemi.classname = "icon-interface-component_classification";
        },

        // 分析 表示多个文件夹的数组 maybeRootNotFile 是否有子文件夹
        // ------------------------------------------------------
        func_analysisfolders(maybeRootNotFile, callback) {
         
            var _this = this;

            // 遍历【当前需要解析是否有子文件夹的】所有文件夹
            // ------------------------------------------
            for (var i = 0; i < maybeRootNotFile.length; i++) {

                // 有子文件夹
                // ---------
                if (maybeRootNotFile[i].DirectChildrenCount == 0) {
                    maybeRootNotFile[i].isLeaf = true;
                    maybeRootNotFile[i].classname = 'icon-interface-associated-component _css-1890ff';
                } else {
                    maybeRootNotFile[i].isLeaf = false;
                }
            }

            // 赋值以渲染根级目录
            // -----------------
            if (callback) {
                callback(maybeRootNotFile);
            }


        },

        // 提供方法，获取第一级文件夹
        // ------------------------
        func_getRootCategories() {
            var _this = this;
            _this.m_treeloading = true;
            var _url = `${this.$MgrBaseUrl.GetCategories}?organizeId=${_this.m_organizeId}&baseCode=&Token=${_this.$staticmethod.Get('Token')}`;
            _this.$axios.get(_url).then(x => {
                
                _this.m_treeloading = false;

                // 绑定根级文件夹到 tree
                // 先拿到所有非文件的数据
                // 绑定 icon-interface-unfolder 图标类
                // ----------------------------------
                var maybeRootNotFile = x.data.Data.list;// .filter(y => y.FileSize == 0);

                // 遍历添加 classname
                // -----------------
                var i = 0;
                for (i = 0; i < maybeRootNotFile.length; i++) {

                    // 赋予 classname 以展示树节点图标
                    // -----------------------------------------------------------------
                    maybeRootNotFile[i].classname = 'icon-interface-component_classification';

                    // 赋予 chain 属性（以提供给“打开栈”）
                    // --------------------------------
                    // maybeRootNotFile[i].chain = [];
                    // maybeRootNotFile[i].chain.push({
                    //     FileId: maybeRootNotFile[i].bmc_guid,
                    //     FileName: maybeRootNotFile[i].bmc_name
                    // });
                }

                _this.func_analysisfolders(maybeRootNotFile, (valtoset) => {
                    _this.m_rootfolders = valtoset;
                });

            }).catch(x => {
                _this.m_treeloading = false;
                debugger;
            });
        },

        // 前景样式
        // --------
        _stopPropagation(ev) {
            ev && ev.stopPropagation && ev.stopPropagation();
        },
        greet(val){
            var _this = this;
            _this.val = val;
        },
        getFrontStyle() {
            var _this = this;
            var _s = {};
            _s["width"] = _this.m_width;
            _s["height"] = _this.m_height;
            _s["position"] = 'fixed';
            _s["right"] = `calc(50% - ${parseInt(_this.m_width.toString().replace('px', 'px')) / 2}px)`;
            _s["top"] = `calc(50% - ${parseInt(_this.m_height.toString().replace('px', 'px')) / 2}px)`;
            return _s;
        },
        // 点击查看构件，可编辑
        handelClickMaterialEdit(){
            this.$emit('clickedit');
            this.materialEditClick = true;
        },
        func_removetask(ev,doc){
            let _this = this;
            ev && ev.stopPropagation && ev.stopPropagation();
            _this.$confirm("确认移除该关联构件?", "操作确认").then(x => {
                _this.$emit("onRemoveList",doc);
            });
        },
        clearSelected(){
            let _this = this;
            console.log('CompsMaterialBrowser.vue');
            _this.$confirm("确认清空所有关联构件?", "操作确认").then(x => {
                _this.$emit("clearSelected",_this.selectedObjList);
            });
        }
    },
    props: {

        // 前景高度
        // 前景宽度
        // 总体的 zindex
        // 标题
        // -------------
        init_height: {
            type: String,
            required: true
        },
        init_width: {
            type: String,
            required: true
        },
        init_zIndex: {
            type: Number,
            required: true
        },
        init_title: {
            type: String,
            required: true
        },
        init_bimcomposerId: {
            type: String,
            required: true
        },
        init_organizeId: {
            type: String,
            required: true
        },
        isProgress:{   // 当进度管理点击进入展示搜索和查看关联构件
            type: Boolean,
            required: false 
        },
        selectedObjList:{   // 当进度管理点击进入、已关联的构件数组
            type: Array,
            required: false 
        },
    }
}
</script>
<style scoped>

._css-1890ff {
    color: #1890FF;
}

._css-openstack-splitter {
     float:left;
}
._css-openstack-item {
    /* height: 18px;
    line-height: 18px;
    cursor: pointer;
    font-size: 12px;
    float:left; */

    height: 18px;
    line-height: 18px;
    cursor: pointer;
    font-size: 12px;
    float: left;
    /* max-width: 400px;
    min-width: 26px;
    overflow-x: hidden; */
    /* text-overflow: ellipsis; */
    white-space: nowrap;
}
._css-openstack-item:hover {
    text-decoration: #1890FF;
    color:#1890FF;
}
._css-openstack-ctn {
    height: 36px;
    display: flex;
    align-items: center;
    padding-left: 12px;
    overflow-x: auto;
}
._css-docin-tree {
    font-size: 12px;
    width:235px;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.02);
    overflow-y: auto;
}
._css-docin-list {
    /* flex: 1; */
    width:calc(100% - 235px);
    height: 100%;
    display: flex;
    flex-direction: column;
    border-left: 1px solid #EBEEF5;
}
.el-pagination-cus {
    border-top: 1px solid rgba(245,245,245,0.35);
}
._css-title-content {
    font-size: 16px;
    margin: 0 24px 0 24px;
    flex:1;
    text-align: left;
}
._css-docin-content {
    width:100%;
    flex:1;
    display: flex;
    height: calc(100% - 50px - 64px);
}
._css-docin-btnctn {
    border-top: 1px solid rgba(0, 0, 0, 0.09);
    height:50px;
    margin-top: 0;
    flex:none;
    box-sizing: border-box;
}
._css-docin-title {
    height: 64px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.09);
    flex:none;
    box-sizing: border-box;
}
._css-title-closebtn {
    margin-right: 24px;
}
._css-docbrowser-in {
    border: 1px solid rgba(0,0,0,.09);
    background-color: #fff;
    border-radius: 4px;
    display: flex;
    flex-direction: column;
}
._css-docbrowser {
    width: 100%;
    height:100%;
    position: fixed;
    background: rgba(0, 0, 0, 0.3);
    display: flex;
    align-items: center;
    justify-content: space-around;
}
._css-docin-list /deep/ .el-loading-spinner .el-loading-text{
  font-size: 15px;
}
._css-docin-list /deep/ .el-loading-spinner i{
  font-size: 18px;
}
/* ._css-docin-list /deep/ .plTableBox .el-table td, ._css-docin-list /deep/ .plTableBox .el-table th{
  padding:0;
} */
._css-docin-list /deep/ .plTableBox .el-table__empty-block{
    display: block;
}
._css-docin-list /deep/ .plTableBox .el-table--border::after,
._css-docin-list /deep/ .plTableBox .el-table--group::after, 
._css-docin-list /deep/  .plTableBox .el-table::before{
    background-color: transparent;
}
._css-docin-list /deep/ .plTableBox .el-table--border, ._css-docin-list /deep/ .plTableBox .el-table--group{
    border:none;
}
._css-docin-list /deep/ ._css-customstyle th.is-leaf, ._css-docin-list /deep/ ._css-customstyle .el-table__fixed-right-patch{
    border-right: 1px solid #EBEEF5;
}
._css-docin-list /deep/ .plTableBox .el-table th>.cell{
    font-weight: 600;
    /* text-align: center; */
}
._css-dialog-search{
    width: 480px;
}
._css-dialog-search span{
    width: 130px;
    height: 36px;
    border-radius: 4px;
    border: 1px solid #1890FF;
    display: inline-block;
    line-height: 36px;
    margin-left: 20px;
    font-size: 14px;
    font-weight: 400;
    color: #1890FF;
    cursor: pointer;
}
._css-dialog-search span:hover,._css-clear-edit-selected:hover{
    background: #1890FF;
    color: #fff;
}
._css-dialog-search /deep/ .el-input__icon.el-icon-search{
    color: #1890FF;
    font-size: 18px; 
}
._css-dialog-search /deep/ .search{
    border-radius: 4px;
    border: 1px solid rgba(0, 0, 0, 0.09);
    padding-left: 16px;
}
._css-dialog-search /deep/ .el-table._css-table-ele2 th div{
    border-right: none;
}
/* ==============查看已关联构件css */
._css-clear-edit-selected{
    margin: 10px 0;
    display: inline-block;
    padding:0px 12px;
    height: 32px;
    line-height: 32px;
    border-radius: 4px;
    border: 1px solid #1890FF;
    font-size: 12px;
    font-weight: 400;
    color: #1890FF;
    cursor: pointer;
    margin-right: 10px;
} 
._css-clear-edit-selected._css-close{
    border: none;
}
._css-edit-selected-list{
    position: absolute;
    top: 61px;
    right: 0;
    background: #fff;
    border: 1px solid rgba(0, 0, 0, 0.09);
    border-right: none;
    border-radius: 4px 0 0 4px;
    /* width: 240px; */
    height: 60%;
    overflow-y: auto;
    z-index: 1;
}
.mgr-list{
    height: calc( 100% - 60px);
    overflow-y: auto;
}
.css-common-line{ 
    /* display: inline-block; */
    padding: 0;
} 
._css-flowModelNameSelected{
    height: 34px;
    line-height: 34px;
    cursor: pointer;
    color: rgba(40, 58, 79, 1);
    /* -webkit-box-flex: 1; */
    /* -ms-flex: 1; */
    /* flex: 1; */
    width: 100%;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;

    /* border-radius: 4px; */
    /* border: 1px solid rgba(208,208,208,1); */
}
._css-flowModelNameSelected:hover{
    background: rgba(0, 0, 0, 0.04);
}
._css-addattach-icon {
    margin-left: 16px;
    margin-right: 10px;
    width:20px;
    height: 20px;
    color: #1890ff;
    /* font-weight: bold */
}
/* ._css-flowModelNameSelected:hover ._css-addattach-icon {
    color: #1890ff;
    font-weight: bold;
} */
._css-modelname-show {
    flex: 1;
    overflow-x: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    max-width: 261.5px;
    text-align: left;
}
._css-closehoverctn ._css-formadd-closebtn {
    right: 0;
    top: 0;
    border-radius: 50%;
    font-size: 16px;
    cursor: pointer;
    visibility: hidden;
    display: flex;
    width: 16px;
    height: 16px;
    margin-right: 10px;
}
._css-closehoverctn ._css-formadd-closebtn._css-static {
  position: static;
  /* margin-left: 8px; */
}
._css-closehoverctn:hover ._css-formadd-closebtn {
  visibility: visible;
}
._css-btn-selected{
    text-align: right;
    border-top: 1px solid rgba(0, 0, 0, 0.09);
}
._css-line-none{
    padding: 15px;
}
</style>