<template>
	<div class="doc-content">
		<zdialog-function
			:init_title="'线上文档'"
			:init_zindex="1005"
			:init_innerWidth="700"
			:init_width="700"
			init_closebtniconfontclass="icon-suggested-close"
			@onclose="close"
		>
			<div
				slot="mainslot"
				class="_css-addingnameinput-ctn"
				@mousedown="_stopPropagation($event)"
			>
                <div class="doc-module">
                    <div class="left">
                        <div class="title css-cp" @click="getAllList()">项目文档</div>
                        <el-tree
                            :data="treedata.items"
                            class="_css-customstyle"
                            :class="extdata._doc_showtree?'':'css-hide'"
                            lazy
                            :load="loadNodeChild"
                            node-key="Id"
                            :props="treedata.props1"
                            :expand-on-click-node="false"
                            @node-collapse="node_collapse"
                            @node-expand="node_expand"
                            @node-click="node_click"
                            ref="tree"
                            :defaultExpandedKeys="_openstacks_withprocessing"
                        >
                            <span class="css-fc" slot-scope="{ node, data }">
                            <i class="css-icon20 css-fs18 css-fc css-jcsa css-folder icon-interface-unfolder"></i>
                            <span :title="data.FolderName" class="css-ml4">{{ node.label }}</span>
                            </span>
                        </el-tree>
                        </div>
                        <div class="right">
                        <div class="_css-keyword-inputbtn css-fc css-usn">
                            <input :placeholder="'请输入文档名称'"
                            class="search-input"
                            v-model="keyword"
                            @keydown.enter="_ondocnameinput" />
                            <i class="icon-interface-search"></i>
                            </div>
                        <div class="css-h100" style="width:374px" id="id_datamain">
                            <!-- 共？个文件，选中的文件个数，操作按钮区域 -->
                            <div class="_css-table-top css-mt12 css-usn" v-if="keyword == '' && extdata._openstacks.length > 0">
                            <div class="css-h100 css-fc">
                                <!-- 面包屑 -->
                                <div
                                class="_css-table-top-left css-h100 css-fc css-usn css-bsb css-miniscroll css-oxa css-flex1"
                                >
                                <!-- 返回上一级及分隔符 -->
                                <div
                                    v-if="extdata._openstacks.length > 0"
                                    @click="open_parentfolder($event);"
                                    class="css-breadcrumb-item css-cp"
                                >
                                    <label class="css-cp _css-backpfolder c2680FE">返回上一级</label>
                                </div>
                                <div v-if="extdata._openstacks.length > 0" class="css-breadcrumb-splitter">/</div>
                                <!-- //显示“项目文档/搜索结果”及分隔符 -->
                                <!-- 所有打开过的文件夹栈 -->
                                <template v-for="(item,index) in extdata._openstacks">
                                    <div
                                    class="css-breadcrumb-item css-cp"
                                    :key="item.FileId"
                                    @click="open_current_history(index, $event);"
                                    >
                                    <label class="css-cp"
                                        :class="{'_css-parentsfolder':index != extdata._openstacks.length - 1
                                        , '_css-currentfolder':index == extdata._openstacks.length - 1}"
                                    >{{item.FileName}}</label>
                                    </div>
                                    <div
                                    :key="'_' + item.FileId"
                                    v-if="index != extdata._openstacks.length - 1"
                                    class="css-breadcrumb-splitter"
                                    >/</div>
                                </template>
                                </div>

                            </div>
                            </div>
                            <!-- //共？个文件，选中的文件个数，操作按钮区域 -->
                            <!-- 表格体区域 -->
                            <div
                            class="_css-table-body css-bsb"
                            >
                            <div
                                class="css-h100 css-w100 css-bsb css-usn"
                                :class="extdata.showtype != 1?'css-none':''"
                            >
                                <!-- 高度为40的列表头 -->
                                <div class="css-h40 css-w100 css-bsb css-fc css-bglgray">
                                <!-- 复选框 -->
                                <div class="css-h100 css-ml10 css-fc">
                                    <div
                                    @click="list_item_head_check($event)"
                                    class="css-cb css-icon14 css-bsb css-cp"
                                    :class="(extdata._selectedobjs.length == extdata.tableData.length) && extdata.tableData.length > 0?'mulcolor-interface-checkbox-selected':''"
                                    ></div>
                                </div>
                                </div>
                                <!-- //高度为100-40的列表体 -->
                            </div>
                            <!-- //列表显示 -->
                            <div
                                data-debugflag="line192"
                                class="css-h100 css-w100 css-bsb css-usn css-prel"
                                :class="extdata.showtype != 0?'css-none':''"
                            >
                                <el-table
                                ref="doctable"
                                :highlight-current-row="false"
                                @row-click="row_click"
                                :border="true"
                                :stripe="false"
                                :data="extdata.tableData"
                                style="width: 100%"
                                :default-sort="{prop: 'date', order: 'descending'}"
                                height="480"
                                class="_css-table-ele css-scroll _css-customstyle"
                                :row-class-name="tableRowClassName"
                                :header-cell-style="{'background-color':'transparent'}"
                                >
                                <el-table-column
                                :resizable="false"
                                width="40">
                                    <!-- <template slot="header" slot-scope="scope">
                                    <span
                                        class="css-cb css-icon12 css-cp css-blk"
                                        :data-use1="typeof scope"
                                        :class="(extdata.tableData.length == extdata._selectedobjs.length) && extdata.tableData.length > 0?'mulcolor-interface-checkbox-selected':''"
                                        @click.stop="head_check_toggle($event)"
                                        @dblclick="_stopPropagation($event)"
                                    ></span>
                                    </template> -->
                                    <template slot-scope="scope">
                                    <span
                                        class="css-cb css-icon12 css-cp"
                                        @click.stop="row_check_toggle(scope.row, $event)"
                                        @dblclick="_stopPropagation($event)"
                                        :class="selectedContains(scope.row.FileId)?'mulcolor-interface-checkbox-selected':''"
                                    ></span>
                                    </template>
                                </el-table-column>

                                <el-table-column
                                    :resizable="true"
                                    class="_css-col-filename"
                                    prop="FileName"
                                    label="文件名称"
                                    min-width="168"

                                >
                                    <template slot-scope="scope">
                                    <i
                                        :class="'css-icon20 css-fs18 css-fc css-jcsa ' + $staticmethod.getIconClassByExtname(scope.row.FileName, scope.row.FileSize)"
                                    ></i>

                                    <template>
                                        <span
                                        class="css-cp css-hoverunder css-ml10 css-ellipsis basic-font-color-emphasize"
                                        @click="row_filename_click(scope.row, $event)"
                                        :title="scope.row.FileName"
                                        >{{scope.row.FileName}}</span>
                                    </template>

                                    </template>
                                </el-table-column>
                                </el-table>
                            </div>

                            </div>
                            <!-- //表格体区域 -->
                        </div>
                        </div>
                </div>
			</div>

			<div slot="buttonslot" class="_css-flowAddBtnCtn">
				<zbutton-function
					:init_text="'确定'"
					:init_fontsize="14"
					:debugmode="true"
					:init_height="'28px'"
					:init_width="'68px'"
					@onclick="func_saveedit"
				>
				</zbutton-function>

                <zbutton-function
					:init_text="'取消'"
					:init_color="'rgba(24, 144, 255)'"
					:init_bgcolor="'#fff'"
					:init_fontsize="14"
					:debugmode="true"
					:init_height="'28px'"
					:init_width="'68px'"
					@onclick="func_canceledit"
				>
				</zbutton-function>
			</div>

		</zdialog-function>
            <!-- idocview , dwg 预览 iframe 载体 -->
        <div class="_css-doc-preview" :class="extdata._show_idocview?'css-fc':'css-hide'">
        <div class="_css-doc-preview-beforeiframe">
            <div class="_css-doc-preview-beforeiframe-01"></div>
            <div class="_css-doc-preview-beforeiframe-02"></div>
            <div class="_css-doc-preview-beforeiframe-03"></div>

            <!-- 新标签打开按钮 -->
            <div
            :title="'在新标签页中查看'"
            class="icon-interface-attributes _css-docpreview-newtab _css-canfull"
            :class="{'_css-isfulling':m_docPreviewIsFull}"
            @click="func_openDocNewtab($event)"
            ></div><!-- //新标签打开按钮 -->

            <!-- 当前页全屏按钮 -->
            <div
            @click="func_switchfull($event)"
            :title="m_docPreviewIsFull?'取消全屏':'全屏'"
            :class="{'_css-docpreview-fullscreen':true
            , '_css-isfulling':m_docPreviewIsFull
            , '_css-canfull': true
            , 'icon-arrow-fullscreen_exit':m_docPreviewIsFull
            , 'icon-arrow-fullscreen':!m_docPreviewIsFull}"
            ></div><!-- //当前页全屏按钮 -->

            <!-- 关闭预览 -->
            <div
            :title="'关闭预览'"
                class="icon-suggested-close _css-canfull"
                :class="(m_docPreviewIsFull?'_css-isfulling':'')  + ' '+'_css-doc-preview-closebtn-' + extdata._docviewtype"
                @click="close_idocview($event)"
            ></div><!-- //关闭预览 -->

            <div class="_css-doc-preview-beforeiframe-04 __web-inspector-hide-shortcut__"></div>
        </div>
        <iframe class="_css-doc-preview-iframe"
        :class="{'_css-previewfull':m_docPreviewIsFull}"
        :src="extdata._idocviewurl"></iframe>
        </div>
	</div>
</template>
<script>
export default {
	name: "CompsDocumentDialog",
	data() {
		return {
            organizeId: '',
            // 树形数据
            keyword: '',

            treedata: {
                props1: {
                children: "Children",
                label: "FolderName",
                isLeaf:"isLeaf"
                },
                items: [
                ] // classname: 默认为未展开的文件夹图标
            },
            // 在线预览是否已全屏
            m_docPreviewIsFull: false,
            extdata: {
                _doc_showtree: false, // 是否显示目录树
                _lastclickedFileId: "", // 最后一次点击的 FileId
                _lastkeyword: "", // 最后一次搜索使用的关键字
                _selectedobjs: [], // 选中的所有数据（对象数组）
                _openstacks: [
                //{FileId:'a', FileName: 'a'}
                ], // 当前打开的所有文件夹（栈结构）
                //_waitforsecondc1lickfileid: undefined, // 等待第二次点击的FileId
                /* declare */ doclisttype: "doclib", // 'doclib':项目文档 'mysuscribe'：我的收藏 'myshared'：我的分享 'recycle'：回收站
                //btngrouptip: "选中了个文件，可以执行右侧操作",
                showtype: 0, // 0为表格， 1为列表
                tableData: [], // 加载到数据主区域的表格/列表数据
                _docviewtype: '',
                _show_idocview: false,
                _idocviewurl: ''
            },
        };
	},
	props: {},
	watch: {},
	created() {
		this.organizeId = this.$staticmethod._Get("organizeId");
	},
	mounted() {
        this.load_tree_firstlevelfolder()
    },
	filters: {},
	computed: {
        // 带处理的 _openstacks
        _openstacks_withprocessing:{
            get(){
                var _this = this;
                return _this.extdata._openstacks;
            }
        },
    },
	methods: {
        _stopPropagation(ev) {
			ev && ev.stopPropagation && ev.stopPropagation();
		},
        _ondocnameinput(str, unknown, ev){
            // 通过 ev 判断是否按的是回车
            var _this = this;
            // 将 str 作为关键字进行文档搜索
            _this.search_doc_bykeyword(undefined, undefined);
        },
        // 加载树的第一级文件夹(in mounted)
        load_tree_firstlevelfolder(recycle,newdirid) {
            var _this = this;
            // 调用 ajax 获取第一层的文件夹

            var _LoadingIns = _this.$loading({
                text: "加载中",
                target: document.getElementById("id_datamain")
                ,background:"rgba(240, 242, 245, 1)"
            });
            _this
                .$axios({
                method: "get",
                url: `${window.bim_config.webserverurl}/api/v1/folder/tree?Token=${this.$staticmethod.Get('Token')}&projectId=${_this.$staticmethod._Get("organizeId")}&parentId=0&userId=${_this.$staticmethod.Get("UserId")}`
                })
                .then(x => {
                if (x.data.Ret == 1){
                    if(_this.v_Id == undefined){
                    _this.v_Id = x.data.Data[0].Id
                    }
                    _this.firstLevelFolderData = x.data.Data[0]
                    _this.$axios
                    .get(`${window.bim_config.webserverurl}/api/v1/folder/tree?Token=${this.$staticmethod.Get('Token')}&projectId=${_this.$staticmethod._Get("organizeId")}&parentId=${x.data.Data[0].Id}&userId=${_this.$staticmethod.Get("UserId")}`)
                    .then(y => {
                    if(y.data.Ret == 1){
                        var _arrgot = y.data.Data
                        for (let i = 0; i < _arrgot.length; i++) {
                        _arrgot[i].isLeaf = !_arrgot[i].HasChildren;
                        }
                        _this.treedata.items = _arrgot;
                        _this.extdata._doc_showtree = true;

                        // 赋值数据
                        _this.tableDatapatch(y.data.Data);
                        if (_this.extdata._openstacks.length > 0) {
                        _this.open_current_history(
                            _this.extdata._openstacks.length - 1,
                            undefined,
                            newdirid
                        );
                        _this.doc_file_folder(this.v_Id)
                        } else {
                        // 未打开任何文件夹下，处理根级目录
                        // _this.resetpath(undefined, newdirid);
                        // 赋值面包屑
                        _this.extdata._openstacks = [];
                        }
                        if(!recycle){
                        _this.doc_file_folder(this.v_Id)
                        }
                        _LoadingIns.close();
                    }
                    }).catch(x => {
                    console.warn(x);
                    _LoadingIns.close();
                    });
                }else{
                    _this.$message({
                    message: x.data.Msg,
                    type: "warning"
                    });
                    _LoadingIns.close();
                }
                })
                .catch(x => {
                console.warn(x);
                // _LoadingIns.close();
                });

        },
        getAllList(){
            this.GetAllFolderAndFileByProjectID({
                ProjectID: this.$staticmethod._Get("bimcomposerId"),
                LikeName: "",
                NormalOrDrawings: "Normal",
                FolderID: 0,
                _if_dir_change_suc_openstacks: []
            });
        },
        // 搜索按钮点击
        search_doc_bykeyword(ev, newdirid) {
            var _this = this;
            if (ev) {
                ev.stopPropagation();
            }
            _this.doc_file_search(_this.keyword)
            // 保存最后一次搜索使用的关键字
            // _this.extdata._lastkeyword = _this.keyword;
        },
        //搜索文档
        doc_file_search(fileName){
            let _this = this
            _this.$axios({
                method: "get",
                url: `${window.bim_config.webserverurl}/api/v1/file/search?projectId=${_this.$staticmethod._Get("organizeId")}&fileName=${fileName}&userId=${_this.$staticmethod.Get("UserId")}&Token=${this.$staticmethod.Get('Token')}`
            }).then(x => {
                if(x.data.Ret == 1){
                    _this.extdata.tableData = x.data.Data;
                }else{
                    _this.extdata.tableData = []
                    _this.$message({
                        message: x.data.Msg,
                        type: 'warning'
                    })
                }
            }).catch(x => {
                console.log(x);
            });
        },
        // 某节点展开时，加载子节点的途径
        loadNodeChild(node, resolve) {
            // 设置必要的参数
            var _this = this;

            // 请求接口，获取当前正在展开的节点的子节点数据
            _this
                .$axios({
                method: "get",
                url: `${window.bim_config.webserverurl}/api/v1/folder/tree?Token=${this.$staticmethod.Get('Token')}&projectId=${_this.$staticmethod._Get("organizeId")}&parentId=${node.data.Id}&userId=${_this.$staticmethod.Get("UserId")}`
                })
                .then(x => {
                if (x.data.Ret == 1) {
                    var _arrgot
                    // 默认情况是加载当前正在展开的节点内的全部文件及文件夹，此处过滤出文件夹

                    _arrgot = x.data.Data;
                    // var _arrgot = x.data.Data;
                    for (var i = 0; i < _arrgot.length; i++) {
                    // 对于每一个即将拼接的新子文件夹，设置及 chilren 使其带有展开按钮图标
                    _arrgot[i].Children = [];

                    // 使该文件夹项带有文件夹图标
                    _arrgot[i].classname = "";
                    _arrgot[i].isLeaf = !_arrgot[i].HasChildren;

                    // 设置其 chain 属性。
                    // 记录了从根级节点到当前节点的路径（有序节点数组）
                    _arrgot[i].chain = [];

                    // 添加父节点的 chain
                    if (node.data.chain && node.data.chain.length) {
                        for (var j = 0; j < node.data.chain.length; j++) {
                        _arrgot[i].chain.push(node.data.chain[j]);
                        }
                    }

                    // 添加自己节点的 chain item
                    _arrgot[i].chain.push({
                        Id: _arrgot[i].Id,
                        FolderName: _arrgot[i].FolderName,
                        isLeaf:!_arrgot[i].HasChildren
                    });
                    }
                    // 将获取到并处理过的数据加载到正在展开的节点上
                    resolve(_arrgot);
                } else {
                    console.warn(x);
                }
                })
                .catch(x => {
                console.warn(x);
                });

        },
        // 展开节点 回调
        node_expand(itemi, node, comp) {
            itemi.classname = "icon-interface-folder";
        },

        // 收起节点 回调
        node_collapse(itemi, node, comp) {
            itemi.classname = "icon-interface-unfolder";
        },
        // 单击节点 回调
        node_click(itemi, node, comp) {
            this.sharedoclisttype = false
            this.breadList = []; //初始化
            console.log(this.$refs.tree)
            this.getTreeNode(this.$refs.tree.getNode(itemi.Id));
            var _this = this;
            _this.extdata._selectedobjs = [];
            _this.extdata.doclisttype = "doclib";
            _this.v_Id = itemi.Id
            this.keyword = ''
            _this.doc_file_folder(itemi.Id)
        },
        getTreeNode(node){
            var _this = this;
            if (node&&node.label) {
                _this.breadList.unshift({ FileId: node.key, FileName: node.label });
                _this.getTreeNode(node.parent);
                _this.GetAllFolderAndFileByProjectID({
                ProjectID: _this.$staticmethod._Get("bimcomposerId"),
                LikeName: "",
                NormalOrDrawings: "Normal",
                FolderID: node.key,
                _if_dir_change_suc_openstacks: _this.breadList,
                });
            }
        },
        GetAllFolderAndFileByProjectID(json) {
            // 请求文档服务接口，获取数据。
            var _this = this;
            // 只要请求数据，就将已选中的数组置空
            _this.extdata._selectedobjs = [];
            if(json.FolderID){
                _this.extdata._openstacks = json._if_dir_change_suc_openstacks;
                this.v_Id = json.FolderID
            }else{
            // 显示loading
            let LoadingIns = _this.$loading({
                text: "加载中",
                target: document.getElementById("id_datamain")
                ,background:"rgba(240, 242, 245, 1)"
            });
            _this.$axios
                .get(`${window.bim_config.webserverurl}/api/v1/folder/tree?Token=${this.$staticmethod.Get('Token')}&projectId=${_this.$staticmethod._Get("organizeId")}&parentId=${json.FolderID}&userId=${_this.$staticmethod.Get("UserId")}`)
                .then(x => {
                if (x.data.Ret == 1 && (x.data.Data.length || x.data.Data.length == 0)) {
                    this.v_Id = x.data.Data[0].Id
                    // 赋值面包屑
                    _this.extdata._openstacks = json._if_dir_change_suc_openstacks;
                    // 赋值数据
                    _this.tableDatapatch(x.data);
                    _this.doc_file_folder(this.v_Id)
                    LoadingIns.close();
                    setTimeout(() => {
                        this.R_lastTime = true;
                    },500);
                }
                })
                .catch(x => {
                    LoadingIns.close();
                })};
        },
        tableDatapatch(arr){
            var _this = this;
            for(var i = 0; i < arr.length; i++) {
                arr[i].relprop = {
                ModelID:''
                };
            }
        },
        // 打开上一级文件夹
        open_parentfolder(ev) {
            var _this = this;
            ev.stopPropagation();
            // 当没有目录栈时，直接跳出
            if (_this.extdata._openstacks.length == 0) {
                return;
            }

            var bimcomposerId = _this.$staticmethod._Get("bimcomposerId");
            var newstacks = [];
            // 打开栈只剩一个的时候，即将回到最初始的位置，即：项目文档根目录
            if (_this.extdata._openstacks.length == 1) {
                // 判断最后一次搜索使用的关键字
                if (_this.extdata._lastkeyword && _this.extdata._lastkeyword != "") {
                // 进入到根目录，但带有搜索关键字，等同于直接搜索
                _this.keyword = _this.extdata._lastkeyword;
                _this.GetAllFolderAndFileByProjectID({
                    ProjectID: bimcomposerId,
                    LikeName: _this.keyword,
                    NormalOrDrawings: "Normal",
                    FolderID: undefined,
                    _if_dir_change_suc_openstacks: []
                });
                } else {
                // 进入到根目录
                _this.GetAllFolderAndFileByProjectID({
                    ProjectID: bimcomposerId,
                    LikeName: "",
                    NormalOrDrawings: "Normal",
                    FolderID: 0,
                    _if_dir_change_suc_openstacks: []
                });
                }
            } else {
                // 进入到上一级目录，通过 FileId，并将 extdata._openstacks 弹栈
                newstacks = [];
                for (var i = 0; i < _this.extdata._openstacks.length - 1; i++) {
                newstacks.push(_this.extdata._openstacks[i]);
                }
                _this.extdata._openstacks = newstacks;
                _this.v_Id = newstacks[newstacks.length - 1].FileId
                _this.doc_file_folder(this.v_Id)
            }
        },
        //查询当前文件夹下信息
        doc_file_folder(folderId){
            let _this = this
            _this.$axios({
                method: "get",
                url: `${window.bim_config.webserverurl}/api/v1/folder/file-folder?Token=${this.$staticmethod.Get('Token')}&projectId=${_this.$staticmethod._Get("organizeId")}&folderId=${folderId}&userId=${_this.$staticmethod.Get("UserId")}`
            }).then(x => {
                if(x.data.Ret == 1){
                _this.extdata.tableData = x.data.Data;
                }else{
                _this.$message({
                    message: x.data.Msg,
                    type: 'warning'
                })
                }
            }).catch(x => {
                console.log(x);
            });
        },
        // 打开某一层级（从面包屑）
        open_current_history(index, ev, newdirid) {
            if (ev) {
                ev.stopPropagation();
            }
            var _this = this;
            // // 判断如果是最后一个，不执行动作
            if (index == _this.extdata._openstacks.length - 1) {
                return;
            }
            // _this.collapseoptions();

            var newhisarr = [];
            for (var i = 0; i <= index; i++) {
                newhisarr.push(_this.extdata._openstacks[i]);
            }
            var bimcomposerId = _this.$staticmethod._Get("bimcomposerId");
            _this.extdata._openstacks = newhisarr;
            _this.v_Id = _this.extdata._openstacks[index].FileId
            _this.doc_file_folder(this.v_Id)
        },
        tableRowClassName({ row, rowIndex }) {
            var _this = this;
            var _sel_hasThisRow =
                _this.extdata._selectedobjs.filter(x => x.FileId == row.FileId).length >
                0;
            return "css-tdunder " + (_sel_hasThisRow ? "css-tabrow-selected" : "");
        },
        // 在新标签页中打开在线预览
        func_openDocNewtab(ev) {
            window.open(this.extdata._idocviewurl, '_blank');
        },
        // 全屏切换
        func_switchfull(ev) {
            this.m_docPreviewIsFull = !this.m_docPreviewIsFull
        },
        close_idocview(ev) {
            ev.stopPropagation();
            var _this = this;
            _this.extdata._idocviewurl = "about:blank";
            _this.extdata._show_idocview = false;
            _this.m_docPreviewIsFull = false
        },
        // 仅改变单行的选中状态
        row_check_toggle(row, ev) {
            ev.stopPropagation();
            var _this = this;
            _this.list_item_checkbox_click(row.FileId, ev);
        },
        // 列表视图，点击一个文件的c heckbox
        list_item_checkbox_click(FileId, ev) {
            ev.stopPropagation();
            var _this = this;
            //_this.$refs.doctable.toggleRowSele1ction(row);
            if (_this.selectedContains(FileId)) {
                _this.extdata._selectedobjs = _this.extdata._selectedobjs.filter(
                x => x.FileId != FileId
                );
            } else {
                var topush = _this.extdata.tableData.filter(x => x.FileId == FileId)[0];
                if (topush) {
                _this.extdata._selectedobjs.push(topush);
                }
            }
        },
        // 单击的不是复选框，而是行时，切换当前行的选中状态，同时确保其它行没有被选中。
        row_click(row, column, ev) {
            var _this = this;
            _this.list_item_click(row.FileId, ev);
            ev.stopPropagation();
        },
        // 列表视图，选中一个文件，同时保证其它的未选中
        list_item_click(FileId, ev) {
            ev.stopPropagation();
            var _this = this;
            // //隐藏上下文菜单
            // 如果按下了 ctrlkey
            if (ev.ctrlKey) {
                //TO1DO 同时也按了 shiftkey
                if (ev.shiftKey) {
                // 拷贝原有的所有选中的数据
                var _originSelects = _this.$staticmethod.DeepCopy(
                    _this.extdata._selectedobjs
                );

                // 声明要与 _originSelected 取并集的数组
                var _tomergetoorigin;

                // 只按 shiftkey 的情况
                var preindex = _this.extdata.tableData.findIndex(
                    x => x.FileId == _this.extdata._lastclickedFileId
                );
                var nowindex = _this.extdata.tableData.findIndex(
                    x => x.FileId == FileId
                );
                if (preindex == nowindex) {
                    //_this.extdata._selectedobjs = [_this.extdata.tableData[preindex]];
                    _tomergetoorigin = [_this.extdata.tableData[preindex]];
                } else {
                    var _less = preindex < nowindex ? preindex : nowindex;
                    var _greator = preindex > nowindex ? preindex : nowindex;
                    var _tosetselected = [];
                    for (var i = _less; i <= _greator; i++) {
                    _tosetselected.push(_this.extdata.tableData[i]);
                    }
                    _tomergetoorigin = _tosetselected;
                }
                // //只按 shiftkey 的情况

                // 把已选中的数据，与 “只按 shiftkey”的情况取并集
                var _all_notdistinct = _originSelects.concat(_tomergetoorigin);

                // 将 _all_notdistinct 排重
                var _distinct = _this.$staticmethod.Unique(
                    _all_notdistinct,
                    x => x.FileId
                );

                // 赋值给 extdata
                _this.extdata._selectedobjs = _distinct;
                } else {
                // 只按了 ctrlKey
                var _maybehas = _this.extdata._selectedobjs.filter(
                    x => x.FileId == FileId
                );
                if (_maybehas.length > 0) {
                    // 移除出去
                    _this.extdata._selectedobjs = _this.extdata._selectedobjs.filter(
                    x => x.FileId != FileId
                    );
                } else {
                    // 添加进来
                    var _has = _this.extdata.tableData.filter(x => x.FileId == FileId);
                    for (var i = 0; i < _has.length; i++) {
                    _this.extdata._selectedobjs.push(_has[i]);
                    }
                }
                // 记录最后一次点击的项的 FileId
                // 仅有 ctrl 键被按下时也要记录
                _this.extdata._lastclickedFileId = FileId;

                // //只按了 ctrlKey
                }
            } else if (ev.shiftKey) {
                // 只按了 shiftkey
                console.log("只按了shiftkey " + FileId);

                var preindex = _this.extdata.tableData.findIndex(
                x => x.FileId == _this.extdata._lastclickedFileId
                );
                var nowindex = _this.extdata.tableData.findIndex(
                x => x.FileId == FileId
                );

                console.log(`preindex = ${preindex}, nowindex = ${nowindex}`);

                // 保证选中 [preindex, nowindex] 范围内的数据
                if (preindex == nowindex) {
                // 仅选中索引为 preindex的数据
                _this.extdata._selectedobjs = [_this.extdata.tableData[preindex]];
                } else {
                var _less = preindex < nowindex ? preindex : nowindex;
                var _greator = preindex > nowindex ? preindex : nowindex;
                // 设置 [_less, _greator]这个索引区间范围内的为将要选中的
                var _tosetselected = [];
                for (var i = _less; i <= _greator; i++) {
                    _tosetselected.push(_this.extdata.tableData[i]);
                }
                _this.extdata._selectedobjs = _tosetselected;
                }
            } else {
                // 无 ctrlKey 无 shiftKey
                _this.extdata._selectedobjs = [];
                _this.extdata._selectedobjs = _this.extdata.tableData.filter(
                x => x.FileId == FileId
                );
                // 记录最后一次点击的项的 FileId
                // 仅有 ctrl 键被按下时也要记录
                _this.extdata._lastclickedFileId = FileId;
            }
            return;
        },
        // 判断已选中的数据（指Vue内存数据）是否包含某一FileId
        selectedContains(fileid, use_el_select1ion) {
            var _this = this;
            if (use_el_select1ion) {
                return (
                _this.$refs.doctable.selection.filter(x => x.FileId == fileid)
                    .length > 0
                );
            } else {
                return (
                _this.extdata._selectedobjs.filter(x => x.FileId == fileid).length > 0
                );
            }
        },
        row_filename_click(obj, ev) {
            var _this = this;
            _this.on_row_dblclick(obj, null, ev);
            ev.stopPropagation();
        },
        // 被触发的双击事件
        on_row_dblclick(row, column, ev) {
            var _this = this;
            ev.stopPropagation();
            _this.item_enter(row);
        },
        // 表格、列表视图的文件（夹）进入方法（包括回收站不可进的逻辑）。
        item_enter(row){
            // 判断是否是处于回收站状态
            var _this = this;
            if (_this.extdata.doclisttype == 'recycle') {
                if (_this.$configjson.debugmode == '1') {
                console.log('当前为回收站，不进行文件（夹）进入动作');
                }
                return;
            }
            // 进行文件进入动作
            if (row.FileSize == '0') {
                if (_this.extdata.doclisttype == 'mysuscribe') {
                _this.turntodocmain(row.FileId);
                } else {
                _this.enterdir(row.FileId, row.FileName, null);
                }
            } else {
                _this.begin_previewfile(row, null);
            }
        },
        // 转到项目文档
        turntodocmain(fid){
            // 获取面包屑
            var _this = this;
            //var _bimserverurl = _this.$configjson.b1imserverurl;
            var _bimcomposerId = _this.$staticmethod._Get("bimcomposerId");
            var _item = _this.extdata.tableData.filter(x => x.FileId == fid)[0];
            var _FolderID;
            var _FileID;
            if (_item.FileSize == '0') {
                _FolderID = fid;
                _FileID = '';
            } else {
                _FolderID = '';
                _FileID = fid;
            }

            _this.collapseoptions();
            _this.extdata.doclisttype = "doclib";
            // _this.enterroot(fid);
            _this.GetAllFolderAndFileByProjectID({
                ProjectID: _this.$staticmethod._Get("bimcomposerId"), //_this.$staticmethod._Get("bimcomposerId"),
                LikeName: "",
                NormalOrDrawings: "Normal",
                FolderID: 0,
                _if_dir_change_suc_openstacks: [],
                AutoSelectFileId: fid
            });
            return
        },
        // 预览文件（文件库列表或矩阵数据中，不包括历史版本界面的预览）
        begin_previewfile(row, ev) {
            if (ev) {
                ev.stopPropagation();
            }
            var _this = this;
            _this.previewfile(row, ev);
        },
        // begin_previewfile 方法后执行
        previewfile(row, ev){
            var _this = this;
            // 根据扩展名类型，取得在线浏览地址
            let filedownloadurl = `${window.bim_config.webserverurl}/api/v1/file/preview?FileId=${row.FileId}&Version=1&UserId=${this.$staticmethod.Get("UserId")}&Token=${this.$staticmethod.Get('Token')}`
            _this.func_previewbydownloadurl(row, filedownloadurl,row.FileExtension);
        },
        // 根据下载地址来进行预览
        func_previewbydownloadurl(row, filedownloadurl,FileExtension) {
            var _this = this;
            // 根据扩展名获取在线浏览地址
            var url_iframe_all;

            // 判断如果是压缩文件的相关扩展名，直接下载：
            var zipExts = ['.zip', '.rar', '.7z', '.jar', '.tar'];
            var lastIndexOfDot = row.FileName.toLowerCase().lastIndexOf('.');
            if (lastIndexOfDot < 0) {
                // 不包含.，也直接下载
                window.location.href = filedownloadurl;
                return;
            }

            var theFileExt = row.FileName.substr(lastIndexOfDot);
            if (row.FileName.toLowerCase().indexOf(".rvt") > 0){
                this.$message({
                message: "rvt格式不支持打开预览",
                type: "warning"
                });
                return
            }
            if (zipExts.indexOf(theFileExt) >= 0) {
                this.$message({
                message: "压缩文件不支持打开预览",
                type: "warning"
                });
                return;
            }
            if (row.FileName.toLowerCase().indexOf(".dwg") > 0) {
                // 修改当前预览的关闭按钮类
                this.extdata._docviewtype = 'dwg'
                url_iframe_all = `${
                _this.$configjson.dwgurl
                }/Home/Index2?dwgurlcfg=${encodeURIComponent(filedownloadurl)}&name=${
                row.FileName
                }`;
            } else {
                // 修改当前预览的关闭按钮类
                this.extdata._docviewtype = 'office'

                url_iframe_all = _this.$staticmethod.getHuangNewcomputeViewUrl(filedownloadurl, row.FileName,FileExtension);
            }
            // 打开在线预览。
            this.extdata._show_idocview = true
            this.extdata._idocviewurl = url_iframe_all
        },
        enterdir(fileid, filename, ev, setopenstacks, AutoSelectFileId) {
            // 验证 fileid 文件夹对于当前人有无打开权限。（需要前置判断，这里做额外判断）
            if (ev) {
                ev.stopPropagation();
            }
            var _this = this;
            _this.v_Id = fileid
            _this.doc_file_folder(fileid)
            var new_openstacks = [];
            if (!setopenstacks) {
                // 没有指定打开文件夹后的“打开栈”时，通过fileid及filename计算“打开栈”
                for (var i = 0; i < _this.extdata._openstacks.length; i++) {
                new_openstacks.push(_this.extdata._openstacks[i]);
                }
                new_openstacks.push({ FileId: fileid, FileName: filename });
            } else {
                // 设置指定的“打开栈”
                new_openstacks = setopenstacks;
            }
            _this.GetAllFolderAndFileByProjectID({
                ProjectID: _this.$staticmethod._Get("bimcomposerId"),
                LikeName: "",
                NormalOrDrawings: "Normal",
                FolderID: fileid,
                _if_dir_change_suc_openstacks: new_openstacks,
                AutoSelectFileId: AutoSelectFileId
            });
        },
        func_canceledit(){
            this.docListHidden = false
            this.keyword = ''
            this.extdata._selectedobjs = []
            this.close();
        },
        func_saveedit () {
            // 判断当前选中是否有文件夹，不支持关联文件夹

            const fileExtension = this.extdata._selectedobjs.map(v=>v.FileExtension)

            let supportedformat = ['doc', 'docx', 'dwg', 'jpeg', 'jpg', 'pdf', 'png', 'ppt', 'pptx', 'txt', 'xls', 'xlsx'];
            let uploadSuffixName = [] // 上传的文件类型
            for (var i = 0; i < fileExtension.length; i++) {
                let suffixName = fileExtension[i].substring(fileExtension[i].lastIndexOf('.') + 1);
                uploadSuffixName.push(suffixName)
            }
            let allSupported = uploadSuffixName.every(item => supportedformat.includes(item));
            if (allSupported) {
                const filterSize = this.extdata._selectedobjs.filter(v=>v.IsFolder)
                if (filterSize.length) {
                    this.$message({
                        message: '暂不支持上传文件夹',
                        type: 'warning'
                    })
                    return
                }
                const ids = this.extdata._selectedobjs.filter(v=>v._FileSize!==0)
                if (ids.length) {
                    let selectArr = [];
                    this.extdata._selectedobjs.forEach(ele => {
                        selectArr.push(
                            {
                                Type: 0,
                                AttachmentId: ele.FileId,
                                AttachmentName: ele.FileName,
                                AttachmentTime: ele.CreateTime,
                                ArchivesType: 0,
                                IsFile: true,
                                ArchivesTypeOption: [
                                    {
                                        value: 0,
                                        label: '纸质原件'
                                    },
                                    {
                                        value: 1,
                                        label: '电子版'
                                    }
                                ]
                            }
                        )
                    });

                    this.$emit('selectDocList',selectArr)
                }
            } else {
                this.$message.warning(`可上传类型：${supportedformat}`)
            }
        },
        close(){
            this.$emit('close')
        },



    },
	beforeDestroy() {},
};
</script>
<style lang="scss" scoped>
.doc-module {
    flex: 1;
    display: flex;
    padding: 24px 16px;
    box-sizing: border-box;
    .left {
        background: #F8F8F8;
        width: 240px;
        border-radius: 2px;
        overflow-y: scroll;
        .title {
            color: #132B4D;
            font-size: 16px;
            font-weight: bolder;
            padding: 12px 16px;
            border-bottom: 1px solid #E8E8E8;
            margin-bottom: 16px;
        }
    }
    .right {
        flex: 1;
        margin-left: 24px;
    }
    /deep/ .el-table__header-wrapper {
        background: #F4F4F4;
        font-weight: bolder;
    }
}
._css-keyword-inputbtn {
    position: relative;
    .icon-interface-search {
      position: absolute;
      left: 14px
    }
}
.search-input {
    height: 32px;
    width: 100%;
    border: 1px solid #B8B8B8;
    background: #fff;
    border-radius: 2px;
    padding-left: 40px;
    outline: none;
}
._css-doc-preview {
  z-index: 70000;
  position: fixed;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  background-color: rgba(0, 0, 0, 0.45);
  opacity: 1;
  justify-content: space-around;
}
._css-doc-preview-iframe {
  height: 80%;
  width: 80%;
  border-width: 0;
  background-color: #fff;
}
._css-doc-preview-iframe._css-previewfull {
  height: 100%;
  width: 100%;
}
._css-doc-preview-beforeiframe {
  position: fixed;
  width: 30px;
  height: 40px;
  top: 0;
  right: 35px;
  background-color: transparent;
  display: flex;
  align-items: center;
  font-family: "微软雅黑";
}
._css-doc-preview-beforeiframe-01 {
  flex: none;
  width: 20px;
}
._css-doc-preview-beforeiframe-02 {
  flex: none;
  font-size: 20px;
  font-weight: normal;
  font-stretch: normal;
  letter-spacing: 0px;
  color: #ffffff;
}
._css-doc-preview-beforeiframe-03 {
  flex: 1;
  height: 100%;
}
._css-doc-preview-beforeiframe-04 {
  flex: none;
  width: 25px;
  height: 100%;
}
 ._css-docpreview-newtab {
    font-size: 20px;
    flex: none;
    width: 30px;
    height: 30px;
    position: fixed;
    background-repeat: no-repeat;
    cursor: pointer;
    color: rgba(0, 0, 0, 0.4);
    border: 1px solid transparent;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.8);
    right: calc(10% + 55px);
    top: calc(10% - 15px);
    line-height: 30px;
    text-align: center;
}
._css-docpreview-newtab._css-isfulling {
  right: 68px;
}
._css-canfull._css-isfulling {
    top:0;
    right: 0px;
}
._css-doc-preview-iframe._css-previewfull {
  height: 100%;
  width: 100%;
}
._css-docpreview-fullscreen {
    font-size: 20px;
  flex: none;
  width: 30px;
  height: 30px;
  position: fixed;
  background-repeat: no-repeat;
  cursor: pointer;
  color: rgba(0, 0, 0, 0.4);
  border: 1px solid transparent;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.8);
  right: calc(10% + 20px);
  top: calc(10% - 15px);
  line-height: 30px;
}
._css-docpreview-fullscreen._css-isfulling {
  right: 34px;
}
._css-doc-preview-closebtn-,
._css-doc-preview-closebtn-office {
  font-size: 20px;
  flex: none;
  width: 30px;
  height: 30px;
  position: fixed;
  background-repeat: no-repeat;
  cursor: pointer;
  color: rgba(0, 0, 0, 0.4);
  border: 1px solid transparent;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.8);
  right: calc(10% - 15px);
  top: calc(10% - 15px);
  line-height: 30px;
}

._css-doc-preview-closebtn-dwg {
  text-align: center;
  font-size: 20px;
  flex: none;
  width: 30px;
  height: 30px;
  position: fixed;
  background-image: url(/Content/images/ProjectManage/Project/close_normal.png);
  background-repeat: no-repeat;
  cursor: pointer;
  color: rgba(0, 0, 0, 0.4);
  border: 1px solid transparent;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.8);
  right: calc(10% - 15px);
  top: calc(10% - 15px);
  line-height: 30px;
}
.c2680FE{
    color: #2680FE;
}
</style>
