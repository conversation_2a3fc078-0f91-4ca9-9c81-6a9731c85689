<template>
  <div class="_css-all-threefields" :style="computestyle">
    <!-- 对话框前景 -->
    <div class="_css-threefields-front" v-drag="draggreet"  :style="dragstyle"  >
      <CompsDialogHeader 
      @oncancel="_oncancel"
      :title="title || '无标题'"></CompsDialogHeader>
      <div class="_css-line1 _css-line" >
        <CompsUsersInput 
        :inittext="extdata.input_name"
        fieldname="姓名"
        fieldwidth="56px"
        fieldcolor="rgba(0,0,0,0.65)"
        @oninput="_oninput_name" placeholder="请输入成员姓名" :iconclass="'icon-interface-user'"
        :is100percent="true"
        ></CompsUsersInput>
      </div>

      <div class="_css-line2 _css-line" >
        <CompsUsersInput 
        :inittext="extdata.input_account"
        fieldname="用户名"
        fieldwidth="56px"
        fieldcolor="rgba(0,0,0,0.65)"
        @oninput="_oninput_account" placeholder="请输入成员用户名" :iconclass="'icon-interface-user'"
        :is100percent="true"
        ></CompsUsersInput>
      </div>

      <div class="_css-line2 _css-line" >
        <CompsUsersInput 
        :inittext="extdata.input_pwd"
        fieldname="密码"
        fieldwidth="56px"
        fieldcolor="rgba(0,0,0,0.65)"
        @oninput="_oninput_pwd" :placeholder="ismodify?'<未修改>':'请输入密码'" :iconclass="'icon-interface-user'"
        :is100percent="true"
        ></CompsUsersInput>
      </div>

      <div class="_css-line2 _css-line" >
        <CompsUsersInput 
        :inittext="extdata.input_email"
        fieldname="邮箱"
        fieldwidth="56px"
        fieldcolor="rgba(0,0,0,0.65)"
        :is100percent="true"
        @oninput="_oninput_email" placeholder="该邮箱为登录账号，请确保正确" :iconclass="'icon-interface-email'"></CompsUsersInput>
      </div>
      <!-- 邮箱重复的提示 -->
      <div class="_css-emailrepeat-tip"
      :style="{'visibility':(show_emailerror?'visible':'hidden')}"
      
       >
        <div class="_css-ertip-icon icon-suggested-close_circle-outline" ></div>
        <div class="_css-ertip-text" >该邮箱已注册在其他机构</div>
      </div>
      <!-- //邮箱重复的提示 -->
      <div class="_css-line3  _css-line" >
        <CompsUsersInput
        :inittext="extdata.input_mobile"
        fieldname="手机号"
        fieldwidth="56px"
        fieldcolor="rgba(0,0,0,0.65)"
        :is100percent="true"
        @oninput="_oninput_mobile" placeholder="请输入联系方式" :iconclass="'icon-interface-phone'" ></CompsUsersInput>
      </div>
        <!-- 手机号的提示 -->
      <div class="_css-emailrepeat-tip" 
      :style="{'visibility':(show_mobileerror && ismodify?'visible':'hidden')}"
      >
        <div class="_css-ertip-icon icon-suggested-close_circle-outline" ></div>
        <div class="_css-ertip-text" >修改手机号会使原手机号不可用</div>
      </div>
      <!-- //手机号的提示 -->
      <CompsDialogBtns @onok="_onok"     
    @oncancel="_oncancel"
      ></CompsDialogBtns>
    </div>
    <!-- //对话框前景 -->
  </div>
</template>
<script>
/*
input:
    
events:
  onok(name, email, mobile, ismodify)
  oncancel
  oninput('name', str)
  oninput('email', str)
  oninput('mobile', str)
*/
import CompsDialogHeader from "@/components/CompsDialog/CompsDialogHeader";
import CompsDialogBtns from "@/components/CompsDialog/CompsDialogBtns";
import CompsUsersInput from "@/components/CompsAuth/CompsUsersInput"
export default {
  data() {
    
    return {    
       val:'0',
            dragstyle: {
                position: 'fixed',
                right: 'calc(50% - 205px)',
                top: 'calc(50% - 235px)'
            },  
      extdata:{
        input_name:'',
        input_email:'',
        input_mobile:'',
        input_account:'',
        input_pwd:''
      }
    };
  },
  created(){
    var _this = this;
    _this.extdata.input_name = _this.init_name;
    _this.extdata.input_email = _this.init_email;
    _this.extdata.input_mobile = _this.init_mobile;
    _this.extdata.input_account = _this.init_account;
    _this.extdata.input_pwd = _this.init_pwd;
  },
  computed: {
    emailerror:{
      get(){
        var _this = this;
        if (_this.show_emailerror && _this.show_emailerror == true){
          return true;
        } else {
          return false;
        }
      }
    },
    mobileerror:{
      get(){
        var _this = this;
        if (_this.show_mobileerror && _this.show_mobileerror == true){
          return true;
        } else {
          return false;
        }
      }
    },
    computestyle: {
      get() {
        var _this = this;
        var _s = {};
        if (_this.zIndex) {
          _s["z-index"] = _this.zIndex;
        }
        return _s;
      }
    }
  },
  props: {
    show_emailerror:{ // 该邮箱已注册在其它机构
      type: Boolean,
      required:false
    },
    show_mobileerror:{ // 编辑时用：修改手机号会使原手机号不可用
      type: Boolean,
      required:false
    },
    zIndex: {
      type: Number,
      required: false
    },
    title: {
      type: String,
      required: false
    },
    ismodify:{
      type: Boolean,
      required: false
    },
    init_name:{
      type: String,
      required: false
    },
    init_email:{
      type: String,
      required: false
    },
    init_mobile:{
      type: String,
      required: false
    },
    init_account: {
      type: String,
      required: false
    },
    init_pwd:{
      type: String,
      required: false
    }
  },
  components: {
    CompsDialogHeader,
    CompsDialogBtns,
    CompsUsersInput
  },
  methods: {
   
      draggreet(val){
                var _this = this;
                _this.val = val;
            },
    _onok() {
      var _this = this;
      //debugger;
      // console.log(_this.extdata.input_name);
      // console.log(_this.extdata.input_email);
      // console.log(_this.extdata.input_mobile);
      // console.log(_this.ismodify);
      _this.$emit("onok"
      ,_this.extdata.input_name
      ,_this.extdata.input_email
      ,_this.extdata.input_mobile
      ,_this.extdata.input_account
      ,_this.extdata.input_pwd
      ,_this.ismodify)
    },


    _oninput_account(str) {
      var _this = this;
      _this.extdata.input_account = str;
      _this.$emit("oninput", 'account', str);
    },

    _oninput_pwd(str) {
      var _this = this;
      _this.extdata.input_pwd = str;
      _this.$emit("oninput", 'pwd', str);
    },

    _oninput_name(str){
      var _this = this;
      _this.extdata.input_name = str;
      _this.$emit("oninput", 'name', str);
    },
    _oninput_email(str){
      var _this = this;
      _this.extdata.input_email = str;
      _this.$emit("oninput", 'email', str);
    },
    _oninput_mobile(str){
      var _this = this;
      _this.extdata.input_mobile = str;
      _this.$emit("oninput", 'mobile', str);
    },
    _oncancel(){
      var _this = this;
      _this.$emit("oncancel");
    }
  }
};
</script>
<style scoped>
._css-ertip-icon{
  height:14px;
  width:14px;
  font-size:14px;
  margin-left:24px;
  color:rgb(245, 34, 45);
}
._css-ertip-text{
  margin-left:8px;
  height:20px;
  line-height:22px;
  color:rgba(0, 0, 0, 0.45);
  font-size:12px;
}
._css-emailrepeat-tip{
  height:12px;
  display: flex;
  align-items: center;
}
._css-threefields-front {
  width: 410px;
  min-height: 48px;
  background-color: rgba(255, 255, 255, 1);
  box-shadow: 0px 13px 24px -17px rgba(11, 41, 62, 0.8);
  border-radius: 4px;
}
._css-all-threefields {
  position: fixed;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.15);
  display: flex;
  align-items: center;
  justify-content: space-around;
}
._css-line {
    margin-left: 16px;
    margin-right: 16px;
    height: 50px;    
}
._css-line1{
  margin-top: 12px;
}
._css-line2{
  margin-top: 12px;
}
._css-line3{
  margin-top: 0px;
}
</style>