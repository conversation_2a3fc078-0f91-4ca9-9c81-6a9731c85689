<template>
    <div class="_comps-exams-all">
        
            <el-table
            ref="ref_compsexamable"
            :highlight-current-row="false"
            :border="true"
            :stripe="false"
            :data="m_data"
            style="width: 100%"
            :default-sort="{prop: 'date', order: 'descending'}"
            height="500"
            class="_css-table-this css-scroll css-miniline"
            :class="{'_css-nofilter': true}"
            :row-class-name="tableRowClassName"
            :header-cell-style="{'background-color':'transparent'}"
            >
                <el-table-column
                    :sortable="true"
                    :resizable="false"
                    min-width="180"
                    prop="ExamineRemark"
                    label="标题"
                >
                    <template slot-scope="scope">
                        <div class="_css-flowname-cell">
                            <div class="_css-flowname-cell-no">{{scope.$index + 1}}</div>
                            <div class="_css-flowname-cell-text">{{scope.row.ExamineRemark}}</div>
                        </div>
                    </template>
                </el-table-column>

                <el-table-column
                width="120"
                label="状态"
                >
                    <template slot-scope="scope">
                        <div 
                        :title="scope.row.ExamineResult | flt_getChState"
                        class="_css-table-cell"
                        :class="scope.row.ExamineResult | flt_getStateClass"
                        >{{scope.row.ExamineResult | flt_getChState}}</div>
                    </template>
                </el-table-column>

            </el-table>

    </div>
</template>
<script>
export default {
    filters: {
        flt_getChState(en) {
            var _this = this;
            if (en.indexOf('A_') == 0) {
                return '待检查';
            }
            else if (en.indexOf('B_') == 0) {
                return '待整改';
            }
            else if (en.indexOf('C_') == 0) {
                return '待验收';
            }
            else {
                return '已合格';
            }
        },

        flt_getStateClass(en) {
            var _this = this;
            if (en.indexOf('A_') == 0) {
                return '_css-a';
            }
            else if (en.indexOf('B_') == 0) {
                return '_css-b';
            }
            else if (en.indexOf('C_') == 0) {
                return '_css-c';
            }
            else {
                return '_css-d';
            }
        }    
    },
    methods: {

        // 设置数据
        // -------
        func_setdatas(datas) {
            var _this = this;
            _this.m_data = _this.$staticmethod.DeepCopy(datas);
        },

        // getdata

        // 获取某构件相关联的现场数据
        // ------------------------
        func_getdatas() {
            var _this = this;
            var _url = `${window.bim_config.webserverurl}/api/Material/Mtr/GetRelExams?bm_guid=${_this._bm_guid}&Token=${_this.$staticmethod.Get('Token')}`;
            _this.$axios.get(_url).then(x => {
                if (x.data.Ret > 0) {
                    if (x.data.Data && x.data.Data.List) {
                        _this.m_data = _this.$staticmethod.DeepCopy(x.data.Data.List);
                    }
                } else {
                    _this.$message.error(x.data.Msg);
                }
            }).catch(x => {
                console.error(x);
            });
        },

        // //getdata

        // el-table
        
        // 根据是否选中了某一行，返回类字符串
        // -------------------------------
        tableRowClassName({ row, rowIndex }) {
            return 'css-tdunder';
        },

        // //el-table
    },
    created() {
        var _this = this;
        console.log('created 已执行', _this.init_bmguid);
        _this._bm_guid = _this.init_bmguid;
    },
    mounted() {
        var _this = this;
        console.log('mounted 已执行');
        if (_this.init_bmguid != '-noval-') {
            _this.func_getdatas();
        }
    },
    data() {
        return {
            _bm_guid:'',

            // 表格数据
            // -------
            m_data:[]
        };
    },
    props:{
        init_bmguid:{
            type: String,
            required: true
        }
    }    
}
</script>
<style scoped>
._comps-exams-all {
    width:100%;
    height:100%;
}

._css-table-this {
    height: 100% !important;
}
._css-flowname-cell {
    display: flex;
    width:100;
    align-items: center;
}

._css-flowname-cell-no {
    min-width:20px;
    height:20px;
    font-size:14px;
    color:rgba(97,111,125,1);
    line-height:20px;
    font-weight: normal;
    margin-left:10px;
    margin-right: 10px;
    text-align: right;
}

._css-flowname-cell-text {
    font-weight: bold;
    color: rgba(40,58,79,1);
    font-size: 14px;
    cursor: pointer;
}

._css-a {
    width: 74px;
    height: 24px;
    line-height: 22px;
    border: 1px solid transparent;
    text-align: center;
    font-size: 14px;
    background-color: #A6AEB6;
    color: #fff;
    border-radius: 4px;
}

._css-b,
._css-d,
._css-c {
    width: 74px;
    height: 24px;
    line-height: 22px;
    border: 1px solid transparent;
    text-align: center;
    font-size: 14px;
    background-color:#006CB4;
    color: #fff;
    border-radius: 4px;
}


</style>