<template>
    <div class="_css-compstabs">
        <div v-for="item in tablist" :key="item.id" 
        @click="on_change(item)"
        :class="{'_css-tabitem':true, '_css-selected':item.id == (selecteditemid||'')}"
        >
            <div class="_css-tabitemin">
                <div class="_css-tabinleft"></div>
                <div class="_css-tabinmiddle">{{item.text}}</div>
                <!-- 选中该 tab 时，才会显示额外的按钮 -->
                <div class="_css-tabinright" v-if="item.id == (selecteditemid||'')">
                    <template v-for="btn in item.extrabtns" >
                        <div
                         @click.stop="btnclick(btn.btnid)"
                         class="_css-rightbtn" :class="btn.classname"  :key="btn.btnid" >
                            <div>{{btn.btntext}}</div>
                         </div>
                    </template>
                </div>    
                <div class="_css-tabinright" v-else>
                </div>
                <!-- //选中该 tab 时，才会显示额外的按钮 -->
            </div>
            
        </div>
    </div>
</template>
<script>
/*
datainput:
    tablist: [{id:'', text:'', extrabtns:['classname':'', btnid:'']}]
events:
    onchange(tabitem)
    onbtnclick(btnid)
*/
export default {
    props: {
        tablist:{
            type: Array,
            required: true
        },
        selecteditemid:{
            type: String,
            required: false
        }
    },
    methods:{
        btnclick(btnid){
            var _this = this;
            _this.$emit('onbtnclick', btnid);
        },
        on_change(item){
            var _this = this;
            _this.$emit("onchange", item);
        }
    }
}
</script>
<style scoped>
._css-rightbtn{
      /* width: 20px; */
    height: 20px;
    font-size: 12px;
    margin-right: 16px;
    color: #1890ff;
    /* display: none; */
    display: flex;
    align-items: center;
    justify-content: space-around;
    padding-left: 6px;
    padding-right: 6px;
    border: 1px solid #1890FF;
    border-radius: 2px;
}

._css-rightbtn:hover {
    color: #fff;
    background-color: #1890ff;
}

._css-tabitemin:hover ._css-rightbtn{
    /* display: block; */
}

._css-tabinleft{
    flex:1;
    height: 100%;
}
._css-tabinmiddle{
    flex:none;
}
._css-tabinright{
    flex:1;
    height: 100%;
    display: flex;
    flex-direction: row-reverse;
    align-items: center;
}
._css-compstabs{
    width:100%;
    height:100%;
    display: flex;
}
._css-tabitemin{
    display: flex;
    align-items: center;
    height:100%;
    width:100%;
}
._css-tabitem{
    box-sizing: border-box;
    flex:1;
    display: flex;
    align-items: center;
    justify-content: space-around;
    height:100%;
    cursor: pointer;
    position: relative;
    background-color: rgba(127, 179, 210, 0.1);
}
._css-tabitem._css-selected{
    border-bottom: 2px solid #1890ff;
    color:rgba(24, 144, 255, 1);
    font-size:14px;
}
</style>




