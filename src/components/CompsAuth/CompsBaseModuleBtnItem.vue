<template>
  <div class="_css-all">
    <div class="_css-collapsebtnandname">
      <div class="_css-collapsebtn"></div>
      <div class="_css-modulename">{{authname}}</div>
    </div>
    <div class="_css-moduleorbuttondesc">
      <div class="_css-moduleorbuttondesc_in _css-visihidden">当前角色{{authname}}权限</div>
    </div>
    <div class="_css-settingchecboxorswitch">
    
      <el-checkbox v-model="obj_checkstate"
      :disabled="isdisabled"
      v-if="editable==true"
      @change="_onchange"
      ></el-checkbox>
      <span :data-debug16="obj_checkstate" v-else :class="
      {
        'icon-checkbox-Selected-Disabled-dis':isdisabled && obj_checkstate
        , 'icon-checkbox-Selected-Disabled-dis-blue':!isdisabled && obj_checkstate
        }
        " ></span>

    </div>
  </div>
</template>
<script>
export default {
  data() {
    return {
        
    };
  },
  mounted(){
      var _this = this;
  },
  methods:{
    _onchange(val){
      var _this =this;
      _this.$emit("onchange", val);
    }
  },
  computed:{
      obj_checkstate:{
          get(){
              var _this = this;
              return _this.obj.checkstate == '1';
          },
          set(val){
              var _this = this;
              if (val == true) {
                  _this.obj.checkstate = '1';
              } else {
                  _this.obj.checkstate = '0';
              }
          }
      }
  },
  components:{
  },
  props: {
    authname: {
      type: String,
      required: true
    },
    isdisabled: {
        type:Boolean,
        required:true
    },
    obj:{
        type:Object,
        required:true
    },
    editable:{
        type:Boolean,
        required:true
    }
  }
};
</script>
<style scoped>
._css-visihidden{
    visibility: hidden;
}
._css-modulename {
  flex: 1;
  height: 100%;
  display: flex;
  align-items: center;
  box-sizing: border-box;
  /* padding-left: 16px; */
  padding-left:40px;
}
._css-all:not(.css-hide) {
  height: 50px;
  display: flex;
  align-items: center;
}
._css-collapsebtn {
  width: 48px;
  height: 100%;
  display: flex;
  justify-content: space-around;
  align-items: center;
}
._css-collapsebtnandname {
  height: 100%;
  /* width: 172px; */
  width:100%;
  color: rgba(0, 0, 0, 0.65);
  font-size: 14px;
  display: flex;
  align-items: center;
}
._css-moduleorbuttondesc {
  height: 100%;
  flex: 1;
  color: rgba(0, 0, 0, 0.45);
  font-size: 12px;
  display: flex;
  align-items: center;
  padding-left: 156px;
}
    ._css-moduleorbuttondesc_in{
        overflow-y: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    } 
._css-settingchecboxorswitch {
  height: 100%;
  width: 279px;
  flex:1;
  display: flex;
  align-items: center;
  flex-direction: row-reverse;
  box-sizing: border-box;
  /* padding-right: 48px; */
  padding-right: 62px;
}
</style>