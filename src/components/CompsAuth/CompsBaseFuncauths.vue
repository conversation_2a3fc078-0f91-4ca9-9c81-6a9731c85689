<template>
	<div class="auth-content">
		<el-tree
			ref="treeData"
			:data="treeData"
			node-key="MenuId"
			:props="defaultProps"
			default-expand-all
			:expand-on-click-node="false"
		>
			<span class="custom-tree-node tree-list" slot-scope="{ node, data }">
				<div class="lineheight">
					{{ data.MenuName }}
					<el-switch
						class="btn-switch"
						v-model="data.HasAuth"
						:disabled="editable"
						active-color="#1890ff"
						inactive-color="rgba(0,0,0,.25)"
					>
					</el-switch>
				</div>
				<div
					v-if="data.Buttons.length > 0"
					:style="{ height: data.Buttons.length - 1 * 34 + 'px' }"
				>
					<div
						class="lineheight padding36"
						v-for="item in data.Buttons"
						:key="item.ButtonId"
					>
						{{ item.ButtonName }}
						<el-checkbox
							class="btn-switch"
							:disabled="editable"
							v-model="item.HasAuth"
						></el-checkbox>
					</div>
				</div>
			</span>
		</el-tree>
	</div>
</template>
<script>
export default {
	name: "CompsBaseFuncauths",
	data() {
		return {
			defaultProps: {
				children: "Children",
				label: "MenuName",
			},
		};
	},
	components: {},
	mounted() {},

	props: {
		treeData: {
			type: Array,
			required: true,
		},
		editable: {
			type: Boolean,
			required: true,
		},
	},
	watch: {},

	methods: {
		append(data) {
			const newChild = { id: id++, label: "testtest", Children: [] };
			if (!data.Children) {
				this.$set(data, "Children", []);
			}
			data.Children.push(newChild);
		},

		remove(node, data) {
			const parent = node.parent;
			const Children = parent.data.Children || parent.data;
			const index = Children.findIndex((d) => d.id === data.id);
			Children.splice(index, 1);
		},
	},
};
</script>
<style scoped>
.lineheight {
	height: 34px;
	line-height: 34px;
	display: flex;
	justify-content: space-between;
}
.padding36 {
	padding-left: 28px;
}
.custom-tree-node {
	height: auto;
	width: 90%;
}
.auth-content /deep/ .el-tree{
	position: relative;
}
.auth-content /deep/ .el-switch{
	position: absolute;
	right: 50px;
}
.auth-content /deep/ .el-checkbox{
	position: absolute;
	right: 60px;
}
.auth-content /deep/ .el-tree-node__content {
	/* height: 50px; */
	height: auto;
	width: 100%;
	text-align: left;
	border-bottom: 1px solid #e0e2e5;
	margin-bottom: 8px;
	background: rgba(0,0,0,.02);
}
.auth-content /deep/ .el-checkbox__input.is-disabled.is-checked .el-checkbox__inner:after,
.auth-content /deep/ .el-checkbox__input.is-disabled.is-checked .el-checkbox__inner{
	border-color: rgb(24, 144, 255);
}
.auth-content /deep/ .el-tree-node__children .el-tree-node__content {
	align-items: flex-start;
	background: transparent;
	height: auto;
	border-bottom: 1px solid transparent;
}
.auth-content /deep/ .el-tree-node__content>.el-tree-node__expand-icon{
	padding: 12px 8px;
}
</style>