<template>
    <div class="_css-all" >
        <div class="_css-main">
            <div class="_css-collapsebtnandname" >
                <div class="_css-collapsebtn">
                    <div class="_css-collapsebtnicon" 
                    @click.stop="_switchstatus"
                    :class="isexpanding?'icon-suggested-minus_square':'icon-suggested-plus_square'"
                    ></div>
                </div>
                <div class="_css-modulename" >{{obj.Bm_FullName}}</div>
            </div>
            <div class="_css-moduleorbuttondesc" > 
                <div class="_css-moduleorbuttondesc_in" >{{obj.Bm_FullName}}是否可见，打开{{obj.Bm_FullName}}后可对下方按钮进行配置</div>
            </div>
            <div class="_css-settingchecboxorswitch" >
                <CompsEloSwitch
                v-bind:obj="obj"
                v-bind:editable="editable"
                @onchanging="CompsEloSwitch_changing"
                ></CompsEloSwitch>
            </div>
            </div>
            <CompsBaseModuleBtnItem
                v-for="item in obj.Bmbs" :key="item.ModuleButtonId" 
                v-bind:authname="item.Bmb_FullName"
                v-bind:isdisabled="obj.checkstate != '1'"
                v-bind:obj="item"
                v-bind:editable="editable"
                class="_css-btnitem"
                :class="isexpanding&&visiblebtns(obj.Bm_EnCode, item.Bmb_EnCode)?'':'css-hide'"
                @onchange="_change"
            ></CompsBaseModuleBtnItem>
    </div>
</template>
<script>
/*
datainput:
events:
*/
import CompsBaseModuleBtnItem from "@/components/CompsAuth/CompsBaseModuleBtnItem";
import CompsEloSwitch from '@/components/CompsElOverride/CompsEloSwitch';

export default {
    data(){
        return {
            isexpanding: true //私有成员，默认为展开状态。
        }
    },
    components:{
        CompsBaseModuleBtnItem,
        CompsEloSwitch
    },
    mounted(){
        var _this = this;
        window.modulevue = _this;        
    },
    methods:{

        // 文档管理中，只显示“可见”权限
        // 模型列表中，只显示“可见”与“编辑”权限
        // --------------------------
        visiblebtns(bmEnCode, bmbEnCode){
            var _this = this;
            var bmbCodeArr;

            // 文档
            // ----
            if (bmEnCode == "Document") {
                bmbCodeArr = ['lr-visible'];
                return bmbCodeArr.includes(bmbEnCode);

                // 模型
                // ---
            } else if (bmEnCode == "BIMModel") {
                console.log(`第76行，模块及功能为：${bmEnCode} - ${bmbEnCode}`);
                bmbCodeArr = ['lr-visible', 'lr-upload', 'lr-edit', 'lr-delete', 'lr-merge', 'lr-share'];
                return bmbCodeArr.includes(bmbEnCode);

                // 构件管理
                // -------
            } else if (bmEnCode == "MaterialsMgr") {
                bmbCodeArr = ['lr-visible', 'lr-edit'];
                return bmbCodeArr.includes(bmbEnCode);

                // 质量安全
                // -------
            } else if (bmEnCode == "QualityManage") {
                bmbCodeArr = ['lr-visible', 'lr-edit'];
                return bmbCodeArr.includes(bmbEnCode);

                // 问题追踪
                // -------
            } else if (bmEnCode == "IssueTracking") {
                bmbCodeArr = ['lr-add']; // 排除 lr-add
                return !bmbCodeArr.includes(bmbEnCode);

            } else {
                return true;
            }
        },

        CompsEloSwitch_changing(currentmoduleauthval){
            var _this = this;
            _this.obj.checkstate = currentmoduleauthval;
            
            // 打开当前角色的某一项功能权限时，需要判断是否已经存在有权限的bmbs，
            // 如果不存在，则需要找到“可见”，如果没有“可见”找到第一个，设置按钮权限。
            // if (_this.obj.checkstate == "1") {
            //     if (_this.obj.Bmbs.findIndex(x => x.checkstate == '1') < 0) {
            //         var _lrvisible_index = _this.obj.Bmbs.findIndex(x => x.Bmb_EnCode == 'lr-visible');
            //         if (_lrvisible_index >= 0) {
            //             _this.obj.Bmbs[_lrvisible_index].checkstate = '1';
            //         } else if (_this.obj.Bmbs.length > 0) {
            //             _this.obj.Bmbs[0].checkstate = '1';
            //         }
            //     }
            // }

            // 改为直接让“可见”或“第一个子功能”选中
            if (_this.obj.checkstate == "1") {
                var _lrvisible_index = _this.obj.Bmbs.findIndex(x => x.Bmb_EnCode == 'lr-visible');
                if (_lrvisible_index >= 0) {
                    _this.obj.Bmbs[_lrvisible_index].checkstate = '1';
                } else if (_this.obj.Bmbs.length > 0) {
                    _this.obj.Bmbs[0].checkstate = '1';
                }
            }

        }, 
        _change(val){
            var _this = this;

            // 如果关掉了某项功能权限，需要判断是否还剩下权限，如果全部子权限都被关闭，则直接关闭掉总功能权限
            // if (val == false) {
            //     if (_this.obj.Bmbs.findIndex(x => x.checkstate == '1') < 0) {
            //         _this.obj.checkstate = '0';
            //     }
            // }

            // 改为，如果关闭了“可见”，则直接关闭模块权限
            if (val == false) {
                if (_this.obj.Bmbs.findIndex(x => x.checkstate == '1') < 0) {
                    _this.obj.checkstate = '0';
                    return;
                }

                var _lrvisible_index = _this.obj.Bmbs.findIndex(x => x.Bmb_EnCode == 'lr-visible');
                if (_lrvisible_index >= 0) {
                    if (_this.obj.Bmbs[_lrvisible_index].checkstate == '0') {
                        _this.obj.checkstate = '0';
                        return;
                    }
                }
            }

        },
        _switchstatus(){
            var _this = this;
            if (_this.isexpanding) {
                _this.isexpanding = false;
            } else {
                _this.isexpanding = true;
            }
        }
    },
    props:{
        obj:{
            type: Object,
            required: true
        },
        editable:{
            type: Boolean,
            required: true
        }
        // btnitems:{
        //     type:Array,
        //     required:true
        // },
        // modulename:{
        //     type:String,
        //     required:true
        // }
        // ,currents1tatus:{
        //     type:Boolean,
        //     required:true
        // }
    }
}
</script>
<style scoped>
    ._css-hide{
        display: none;
    }
    ._css-collapsebtnandname{
        height: 100%;        
        width: 172px;
        color:rgba(0,0,0,0.65);
        font-size:14px;
        display: flex;
        align-items: center;
    }
    ._css-collapsebtn{
        width:48px;
        height:100%;
        display: flex;
        justify-content: space-around;
        align-items: center;
    }
    ._css-collapsebtnicon{
        width:20px;
        height:20px;
        font-size: 20px;
        /* border:1px dotted red; */
        cursor: pointer;
    }
    ._css-modulename{
        flex:1;
        height:100%;
        display: flex;
        align-items: center;
        box-sizing: border-box;
        padding-left: 16px;
    }
    ._css-moduleorbuttondesc{
        height:100%;
        flex:1;
        color:rgba(0,0,0,0.45);
        font-size:12px;
        display: flex;
        align-items: center;
        padding-left: 156px;
    } 
    ._css-moduleorbuttondesc_in{
        overflow-y: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    } 
    ._css-settingchecboxorswitch{
        height:100%;
        width:279px;
        flex:1;
        display: flex;
        align-items: center;
        flex-direction: row-reverse;
        box-sizing: border-box;
        padding-right: 48px;
    }
    ._css-all{
        min-height: 50px;
    }
    ._css-main{
        height:50px;
        background-color: rgba(0,0,0,0.02);
        display: flex;
        align-items: center;
    }
    ._css-btnitem{
        border-bottom: 1px solid rgba(0,0,0,0.04);
        background-color: white;
    }
</style>