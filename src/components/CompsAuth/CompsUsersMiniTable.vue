<template>
    <div class="_css-all" :style="{'margin-top': marginTop + 'px', 'height': height + 'px'
    , 'border': border}">
        <div class="_css-tablehead" :style="{'height': theadHeight}" >
            <div class="_css-thtr" >
                <div class="_css-td _css-tdcb">
                     <div  class="css-cb css-icon14 css-cp  "
                     @click.stop="switchallcheckstate"
                     :class="{'mulcolor-interface-checkbox-selected':allHadSelected()}"
                     ></div>
                </div>
                <div class="_css-td _css-tdName">
                    <div>姓名</div>
                </div>
                <div class="_css-td _css-tdEmail">
                    <div>邮箱</div>
                </div>
                <div class="_css-td _css-tdMobile">
                    <div>手机号</div>
                </div>
            </div>
        </div>
        <div class="_css-tablebody" :style="{'height': tbodyHeight}" >            
            <div class="_css-tr" v-for="item in dataarr" :key="item.UserId">
                <div class="_css-td _css-tdcb">
                    <div  class="css-cb css-icon14 css-cp  "
                    @click.stop="switchitemcheckstate(item)"
                    :class="hadSelectedThisUser(item.UserId)?'mulcolor-interface-checkbox-selected':''"
                    ></div>
                </div>
                <div class="_css-td _css-tdName">
                    <div>{{item.RealName}}</div>
                </div>
                <div class="_css-td _css-tdEmail _css-light">
                    <div>{{item.Email}}</div>
                </div>
                <div class="_css-td _css-tdMobile _css-light">
                    <div>{{item.Mobile}}</div>
                </div>
            </div>          
        </div>
    </div>
</template>
<script>
/*
input:
    dataarr:[
        {
            UserId: '',
            RealName: ''
            Email: '',
            Mobile: ''
        }
    ]
events:
    onselectedchange(userItem)
    onclearall(list)
    onaddall(list)
*/
export default {
    data(){
        return {
            datain:{
                dataseled:[]
            }
        }
    },
    mounted(){
        var _this = this;
        //_this.dataselected = _this.dataselected;
    },
    methods:{

        switchallcheckstate(){
            var _this = this;
            if (_this.dataarr.length == 0) {
                _this.$message({
                    message: '当前没有成员可选择',
                    type: 'warning'
                });
                return;
            }

            var hadAllSelected = _this.allHadSelected();
            if (hadAllSelected) {
                _this.$emit("onclearall", _this.dataarr);
            } else {
                _this.$emit("onaddall", _this.dataarr);
            }
        },

        // 判断当前人是否已处于已选中状态
        hadSelectedThisUser(userId){
            var _this = this;
            if (!_this.dataselected || _this.dataselected.length == 0) {
                return false;
            }
            return _this.dataselected.filter(x => x.UserId == userId).length > 0;
        },

        // 判断已经搜索出来的是不是都包含到 dataselected 中，且 dataarr.length ? 0
        allHadSelected(){
            var _this = this;
            if (_this.dataarr.length == 0) {
                return false;
            }
            if (_this.dataselected.length == 0) {
                return false;
            }
            var allhasselected = true;
            for (var i = 0; i < _this.dataarr.length; i++) {
                if (_this.dataselected.filter(x => x.UserId == _this.dataarr[i].UserId).length == 0) {
                    allhasselected = false;
                    break;
                }
            }
            return allhasselected;
        },

        // 切换 userId 在 dataselected 中的状态
        switchitemcheckstate(userItem){
            var _this = this;
            // if (_this.dataselected.filter(x => x.UserId == userItem.UserId).length > 0) {
            //     _this.dataselected = _this.dataselected.filter(x => x.UserId != userItem.UserId);
            // } else {
            //     _this.dataselected.push(userItem);
            // }
            _this.$emit("onselectedchange", userItem);
        }
    },
    props:{
        height:{
            type: Number,
            required:true
        },
        marginTop:{
            type: Number,
            required:true
        },
        border:{
            type: String,
            required:true
        },
        theadHeight:{
            type: String,
            required:true
        },
        tbodyHeight:{
            type: String,
            required:true
        },
        dataarr: {
            type: Array,
            required:true
        },
        dataselected:{
            type: Array,
            required: true
        }
    }
}
</script>
<style scoped>
._css-all{
    width:100%;
    display: flex;
    flex-direction: column;
}
._css-tablehead{
    border:1px solid transparent;
}
._css-tablebody{
    overflow-y: auto;
    border:1px solid transparent;
}
._css-tr{
    height:32px;
    display: flex;
    align-items: center;
}
._css-thtr{
    display: flex;
    align-items: center;
    height:100%;
}
._css-td{
    height:100%;
    flex:1;
    display: flex;
    align-items: center;
    box-sizing: border-box;
    padding-left: 8px;
    padding-right: 8px;
    color:rgba(0,0,0,0.45);
}

._css-td._css-light{
    color:rgba(0,0,0,0.25);
}

._css-tablebody::-webkit-scrollbar {/*滚动条整体样式*/
  width: 5px;     /*高宽分别对应横竖滚动条的尺寸*/
  height: 4px;
}
._css-tablebody::-webkit-scrollbar-thumb {/*滚动条里面小方块*/
  border-radius: 5px;
  -webkit-box-shadow: inset 0 0 5px rgba(0,0,0,0.2);
  background: rgba(0,0,0,0.2);
}
._css-tablebody::-webkit-scrollbar-track {/*滚动条里面轨道*/
  -webkit-box-shadow: inset 0 0 5px rgba(0,0,0,0.2);
  border-radius: 0;
  background: rgba(0,0,0,0.2);
}

._css-tdcb{
    width:57px;
    flex:none;
    justify-content: space-around;
}
._css-tdName{
    width:87px;
    flex:none;
}
._css-tdEmail{
    width:180px;
    flex:none;
}
</style>