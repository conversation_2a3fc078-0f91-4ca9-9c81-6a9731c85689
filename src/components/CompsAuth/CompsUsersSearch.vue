<template>
    <!-- 用户选择组件 -->
    <div class="_css-hover"
    :style="{'z-index': zIndex && zIndex > 0?zIndex:999, 'background-color':hovercolor || 'black'}"
     >
        <div class="_css-inner" :style="{'width':innerWidth && innerWidth > 0?innerWidth + 'px':500 + 'px'}">
            <CompsDialogHeader @oncancel="_oncancel" title="添加成员"></CompsDialogHeader>
            
            <div class="_css-middlearea" >
                <CompsUsersInput
                @oninput="_oninput"
                :placeholder="'搜索邮箱、手机号、姓名'"
                iconclass="icon-interface-search"
                ></CompsUsersInput>
                <CompsUsersMiniTable
                @onselectedchange="_onselectedchange"
                @onclearall="_onclearall"
                @onaddall="_onaddall"
                :dataselected="addedItems"
                :dataarr="searchdata.tableData"
                :border="'1px dashed rgba(0,0,0,0.15)'"
                :marginTop="16"
                :height="180"
                :theadHeight="'27px'"
                :tbodyHeight="'calc(100% - 27px)'"
                ></CompsUsersMiniTable>
                <div class="_css-selected-result" >
                    <div class="_css-selected-resulti" v-for="item in addedItems" :key="item.UserId">
                        <div class="_css-selected-resulti-name" >{{item.RealName}}</div>
                        <div class="_css-selected-resulti-btn css-icon12 css-fs12 icon-suggested-close"
                        @click.stop="remove_this(item.UserId)"
                         ></div>
                    </div>
                </div>
            </div>

            <CompsDialogBtns
            @onok="_onok"
            @oncancel="_oncancel"
            ></CompsDialogBtns>
        </div>
    </div>
    <!-- //用户选择组件 -->

</template>
<script>
/*
input:
    [hovercolorclass: String("", "black")]
    [innerWidth: Number]
    [zIndex: Number]
events:
    oncancel()
    onok(list)
*/
// import CompsTopTitleBtns from "@/components/CompsAuth/CompsTopTitleBtns";
import CompsDialogHeader from "@/components/CompsDialog/CompsDialogHeader"
import CompsDialogBtns from "@/components/CompsDialog/CompsDialogBtns"
import CompsUsersInput from "@/components/CompsAuth/CompsUsersInput"
import CompsUsersMiniTable from "@/components/CompsAuth/CompsUsersMiniTable"

export default {
    data(){
        return {
            searchdata:{
                tableData:[] // 表格数据，即检索用户后的数据
            }
            ,addedItems:[] // 已经添加了的人。
        }
    },
    mounted(){
        var _this = this;
        _this.SetPickAreaDataByKeyword('');
    },
    methods:{

        //
        _onok(){
            var _this = this;
            _this.$emit("onok", _this.addedItems);
        },

        // 确保 list 中的数据都不存在于 addedItem 中
        _onclearall(list){
            var _this = this;
            var newaddedItems = [];
            for(var i = 0; i < list.length; i++) {
                if (_this.addedItems.filter(x => x.UserId == list[i].UserId).length >= 0) {
                    continue;
                } else {
                    newaddedItems.push(list[i]);
                }
            }
            _this.addedItems = newaddedItems;
        },

        // 确保 list 中的数据都存在于 addedItems 中
        _onaddall(list){            
            var _this = this;
            if (list.length > 100) {
                _this.$message({
                    message: '最多可同时选择100个人员',
                    type: 'warning'
                });
                _this.addedItems = list.slice(0, 99);
            } else {
                _this.addedItems = list;//_this.$staticmethod.DeepCopy(newaddedItems);
            }
        },

        _oncancel(){
            var _this = this;
            _this.$emit("oncancel");
        },

        _onselectedchange(userItem){
            var _this = this;
            
            // 判断 userId 是否在 addItem中。
            if (_this.addedItems.filter(x => x.UserId == userItem.UserId).length > 0) {
                _this.addedItems = _this.addedItems.filter(x => x.UserId != userItem.UserId);
            } else {
                _this.addedItems.push(userItem);
            }
        },

        remove_this(UserId){
            var _this = this;
            _this.addedItems = _this.addedItems.filter(x => x.UserId != UserId);
        },

        // 根据关键字（邮箱、手机号、姓名）请求数据，并设置即将添加到当前项目角色的备选池
        SetPickAreaDataByKeyword(key){
            var _this = this;

            var _encodedKeyWord = encodeURIComponent(key);
            var _ProjectID = _this.$staticmethod._Get("organizeId");
            var _RoleId = _this.roleId; //rolevue.CompsElo1ContextMenu_roleid
            var _Token = _this.$staticmethod.Get("Token");

            var apiurlname = _this.apiurlname || 'GetToAddRoleUsers';

            _this.$axios({
                method: 'get',
                url: `${_this.$configjson.webserverurl}/api/User/User/${apiurlname}?Token=${_Token}&RoleId=${_RoleId}&ProjectID=${_ProjectID}&encodedKeyWord=${_encodedKeyWord}`
            }).then(x => {
                if (x.status == 200 && x.data.Ret > 0) {
                    _this.searchdata.tableData = x.data.Data;
                } else if (_this.$configjson.debugmode == '1') {
                    console.warn(x);
                }
            }).catch(x => {
                if (_this.$configjson.debugmode == '1') {
                    console.warn(x);
                }
            });

        },
        _oninput(str){
            var _this = this;

            // 延迟发请求
            if (str && str.length > 0) {
                clearTimeout(_this.timeoutId);
                _this.timeoutId = setTimeout(function () {
                    // 发送请求设置可 pick 区域数据
                    _this.SetPickAreaDataByKeyword(str);
                }, 500);
            } else {
                // 请空可 pick 区域数据
                _this.SetPickAreaDataByKeyword('');
            }
            
        }
    },
    props:{
        hovercolor:{
            type: String,
            required: false
        },
        zIndex:{
            type: Number,
            required: false
        },
        innerWidth:{
            type: Number,
            required: false
        },
        roleId:{
            type: String,
            required: true
        },
        apiurlname:{
            type: String,
            required: false
        }
    },
    components:{
        CompsDialogHeader,
        CompsDialogBtns,
        CompsUsersInput,
        CompsUsersMiniTable
    }
}
</script>
<style scoped>
._css-hover{
    position: fixed;
    width:100%;
    height:100%;
    top:0;
    left:0;
    display: flex;
    justify-content: space-around;
    align-items: center;
}
._css-inner{
    background:rgba(255,255,255,1);
    box-shadow:0px 13px 24px -17px rgba(11,41,62,0.8);
    border-radius:4px;
    min-height: 50px;
}
._css-middlearea{
    padding-left:24px;
    padding-right: 24px;
    margin-top:15px;
}
._css-table-members{
    margin-top:16px;
    border-radius:4px;
    border:1px dashed rgba(0,0,0,0.15);
}
._css-selected-result{
    height:144px;
    border:1px dashed rgba(0,0,0,0.15);
    margin-top:33px;
    display: flex;
    flex-wrap: wrap;
    align-content: flex-start;
    justify-content: flex-start;
    overflow-y: auto;
}

._css-selected-result::-webkit-scrollbar {/*滚动条整体样式*/
  width: 5px;     /*高宽分别对应横竖滚动条的尺寸*/
  height: 4px;
}
._css-selected-result::-webkit-scrollbar-thumb {/*滚动条里面小方块*/
  border-radius: 5px;
  -webkit-box-shadow: inset 0 0 5px rgba(0,0,0,0.2);
  background: rgba(0,0,0,0.2);
}
._css-selected-result::-webkit-scrollbar-track {/*滚动条里面轨道*/
  -webkit-box-shadow: inset 0 0 5px rgba(0,0,0,0.2);
  border-radius: 0;
  background: rgba(0,0,0,0.2);
}
._css-selected-resulti{
    height:20px;
    box-sizing: border-box;
    padding-left: 8px;
    padding-right: 8px;
    border-radius:2px;
    border:1px solid rgba(0,0,0,0.15);
    display: flex;
    align-items: center;
    font-size: 12px;
    color:rgba(0,0,0,0.65);
    margin-left:16px;
    margin-top:9px;
    user-select: none;
}
._css-selected-resulti-btn{
    margin-left:8px;
    cursor: pointer;
}

</style>