<template>
    <div class="_css-all"
    :class="obj.visiable?'_css-visible':'_css-hide'">
        <div class="_css-title">{{obj.title}}</div>
        <div class="_css-btns">
            <div class="_css-btn _css-save" 
            @click.stop="onok">
                保存
            </div>
            <div class="_css-btn _css-cancel"
            @click.stop="oncancel"
            >取消</div>
        </div>
    </div>
</template>
<script>
/*
v-bind:obj={
    btns:Array, optional

    title:String, optional
    visiable:Boolean, required
}
@oncancel=function(){

}
@onok=function(){

}
*/
export default {
    data(){
        return {
            
        }
    },
    methods:{
        oncancel(){
            var _this = this;
            _this.$emit("oncancel");
        },
        onok(){
            var _this = this;
            _this.$emit("onok");
        }
    },
    props:{
        obj:{
            type: Object,
            required: true
        }
    }
}
</script>
<style scoped>
    ._css-all{
        height:100%;
        width: 100%;
        position:absolute;
        top:0;
        left:0;
        background-color: #fff;
        box-sizing: border-box;
        border-bottom: 2px solid #1890FF;
        align-items: center;
    }
    ._css-all._css-visible{
        display: flex;
    }
    ._css-all._css-hide{
        display: none;
    }
    ._css-btns{
        flex:1;
        display: flex;
        flex-direction: row-reverse;
    }
    ._css-title{
        font-size:14px;
        color:rgba(0,0,0,0.85);
        padding-left:10px;
        box-sizing: border-box;
    }
    ._css-btn{
        /* width:50px; */
        height:24px;
        font-size:12px;
        display: flex;
        align-items: center;
        justify-content: space-around;
        padding-left:12px;
        padding-right: 12px;
        cursor: pointer;
    }
    ._css-save{
        margin-right:12px;
        color:#1890FF;
    }
    ._css-cancel{
        margin-right:16px;
    }
</style>