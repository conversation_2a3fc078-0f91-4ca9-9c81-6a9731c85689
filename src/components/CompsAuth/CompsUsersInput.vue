<template>
    <div class="_css-all"
    :style="getstyle()" >
        <div class="_css-icon" :class="iconclass?iconclass:''"></div>
        <div v-if="fieldname && fieldwidth && fieldcolor"
        :style="{'width':fieldwidth, 'color':fieldcolor,'text-align':'left','margin-left':'4px'}"
        >
            {{fieldname}}
        </div>

        <!-- 当赋值 m_isemails 为 true 时，input 外面直接包一个div-->
        <div v-if="m_isemails" 
        class="_css-emailsinput-outerdiv"
        >

            <span 
            :title="userck.Email"
            v-for="userck in m_showingchecked"
            :key="userck.UserId"
            class="_css-checkeditem">
                <span  class="_css-checkeditem-in">{{userck.RealName}}</span>;
            </span>

            <input 
              @blur="_blur"
              autocomplete="off"
        @focus="_focus()"
        @mousedown="_stopPropagation($event)"
        @keyup.stop="keywordkeyup($event)"
        :id="inputid || 'input_anonymous'"
        
         class="_css-innerinput _css-innerinput2  basic-font-color-emphasize" :type="ispassword == true?'password':'text'" v-model="objdata.inputtext" :placeholder="placeholder"
            />
        </div><!-- //当赋值 m_isemails 为 true 时，input 外面直接包一个div-->
        <input
        v-else
        autocomplete="off"
        @blur="_blur"
        @focus="_focus()"
        @mousedown="_stopPropagation($event)"
        @keyup.stop="keywordkeyup($event)"
        @change="keywordkeyup($event)"
        :id="inputid || 'input_anonymous'"
        
         class="_css-innerinput basic-font-color-emphasize" :type="ispassword == true?'password':'text'" v-model="objdata.inputtext" :placeholder="placeholder"/>

        <!-- 检查按钮 -->
        <div 
        v-if="m_isemails"
        @mousedown="_stopPropagation($event)"
        @click="func_checkinputting($event)"
        class="_css-userinputs-check">检查</div><!-- //检查按钮 -->

        <!-- 下拉项（如果有的话） -->
        <div v-show="showlist == true" class="_css-userinput-suglist" v-if="autocomplete == true">
            <div class="_css-listitem" v-for="user in listdata" :key="user.UserId" @click.stop="_itemclick(user)">
                <div class="_css-listitem-inner"  >{{user.RealName}}</div>
            </div>
        </div>
        <!-- //下拉项（如果有的话） -->
    </div>
</template>
<script>
/*
input:
    [placeholder: String("搜索邮箱、手机号、姓名")]
events:
    oninput(String),
    onuserinputfocus(),
    onuserinputblur()
*/
export default {
    data(){
        return {

            // 邀请人员时，对应的选择的roleid
            // ----------------------------
            m_joinselectedId:'',
            
            // init_isemails
            // -------------
            m_isemails: false,

            // 已通过检查的用户数组
            // ------------------
            m_showingchecked:[],
            m_oldvalue:'',

            objdata:{
                inputtext:''
            }
        };
    },
    methods:{

        // 外部调用方法
        // -----------
        func_setselectedId(selectedId){
            var _this = this;
            _this.m_joinselectedId = selectedId;
        },
        func_getdata(){
            var _this = this;
            return {
                selectedId: _this.m_joinselectedId,
                inputtext: _this.objdata.inputtext,
                inputarr: _this.m_showingchecked
            };
        },

        // 将 list 中邮箱地址匹配的，从 _this.objdata.inputtext 中移除
        // 将 list 中，m_showingchecked中不存在的元素，补充到m_showingchecked中
        // -----------------------------------------------------------------
        func_processcheck(list) {
            var _this = this;
            var i = 0;
            var index;
            for (i = 0; i < list.length; i++) {
                index = _this.m_showingchecked.findIndex(x => x.UserId == list[i].UserId);
                if (index < 0) {
                    _this.m_showingchecked.push(list[i]);
                }

                // 不管是否已存在，从文本中移除
                // -------------------------
                //_this.objdata.inputtext = _this.objdata.inputtext.replace()
                // var containsCase1 = list[i].Email + ',';
                // var containsCase2 = ',' + list[i].Email + ',';
                // var containsCase3 = ',' + list[i].Email + ',';

                // 使用正则替换
                // ----------
                _this.objdata.inputtext = _this.objdata.inputtext.replace(eval('/' + list[i].Email + '/g'), '');
                _this.objdata.inputtext = _this.objdata.inputtext.replace(eval('/' + '(,)+' + '/g'), ',');

            }

            if (_this.objdata.inputtext.trim() == ',') {
                _this.objdata.inputtext = '';
            }
            _this.$emit("oninput", _this.objdata.inputtext, _this.indexnumber, null);
        },

        // 点击检查按钮，检查文本框中输入的内容，并使用,分隔每一项，查询db中是否存在
        // 将已存在于db中的数据从文本中删除（包含逗号）
        // 若数组中不存在，则添加到数组中
        // ---------------------------
        func_checkinputting(ev){

            // 取到本文本框的文本
            // ----------------
            var _this = this;
            var arrinputting = _this.objdata.inputtext.split(',');
            console.log(arrinputting);

            // 数组是为后续从文本中删除使用，查询时只需要传文本
            // --------------------------------------------
            console.log(_this.objdata.inputtext);

            // 当查询时，所有字母改为小写
            // ------------------------
            _this.objdata.inputtext = _this.objdata.inputtext.toLowerCase();

            // 请求接口
            // -------
            var _url = `${window.bim_config.webserverurl}/api/User/User/GetUsersByEmails`;
            _this.$axios({
                method:'post',
                url: _url,
                data: {
                    Emails: _this.objdata.inputtext,
                    Token: _this.$staticmethod.Get("Token")
                }
            }).then(x => {
                if (x.status == 200) {
                    if (x.data.Ret > 0) {
                        _this.func_processcheck(x.data.Data.List);
                    } else {
                        _this.$message.error(x.data.Msg);
                    }
                } else {
                    debugger;
                }
            }).catch(x => {
                debugger;
            });

        },

        setText(str){
            var _this = this;
            _this.objdata.inputtext = str;
        },
        _stopPropagation(ev){
            ev.stopPropagation();
        },
        _itemclick(user){
            var _this = this;
            _this.$emit("onitemclick", user);
        },
        getstyle(){
            var _this = this;
            var objstyle = {
                'height': _this.is100percent==true?'100%': _this.height && _this.height > 0?height + 'px':'32px'
            };

            if (_this.width) {
                objstyle["width"] = _this.width;
            }

            if (_this.maxWidth) {
                objstyle["max-width"] = _this.maxWidth;
            }

            if (_this.m_isemails) {
                objstyle["flex"] = 1;
            }

            return objstyle;
        },
        _blur(){
            var _this = this;
            _this.$emit("onuserinputblur");
        },
        _focus(){
            var _this = this;
            _this.$emit("onuserinputfocus");
        },
        keywordkeyup(ev){
            var _this = this;

            // 如果是 m_emails，则当没有内容时，删除最后一个人员
            // ---------------------------------------------
            if (_this.m_isemails) {
                if (ev.keyCode == 8 && _this.objdata.inputtext == '' && _this.m_oldvalue == '' && _this.m_showingchecked.length > 0) {
                    _this.m_showingchecked.pop();
                }
            }

            _this.m_oldvalue = ev.currentTarget.value;

            _this.$emit("oninput", _this.objdata.inputtext, _this.indexnumber, ev);
        }
    },
    created(){
        var _this = this;
        if (_this.inittext) {
            _this.objdata.inputtext = _this.inittext;
        }
    },
    mounted(){
        var _this = this;

        // init
        // ----
        if (_this.init_isemails) {
            _this.m_isemails = _this.init_isemails;
        }

        if (_this.autofocus) {
            document.getElementById(_this.inputid || 'input_anonymous').focus();
        }
        
    },
    props:{
        inputid:{
            type: String,
            required: false
        },
        inittext:{
            type: String,
            required: false
        },
        placeholder:{
            type: String,
            required: true
        },
        iconclass:{
            type:String,
            required: false
        },
        height: {
            type: Number,
            required:false
        },
        is100percent: {
            type: Boolean,
            required:false
        },
        width:{
            type: String,
            required: false
        },
        maxWidth:{
            type: String,
            required: false
        },
        // theval:{
        //     type: String,
        //     required:false
        // },
        indexnumber:{
            type:Number,
            required:false
        },
        ispassword:{
            type:Boolean,
            required:false
        },
        // 带有下拉联想
        autocomplete:{
            type:Boolean,
            required:false
        },
        // 显示下拉联想
        showlist:{
            type:Boolean,
            required:false
        },
        // 下拉项数据
        listdata:{
            type:Array,
            required:false
        },


        // 字段名
        fieldname:{
            type: String,
            required: false
        },

        // 字段名宽度
        fieldwidth:{
            type:String,
            required: false
        },

        // 字段名颜色
        fieldcolor:{
            type:String,
            required: false
        },

        autofocus:{
            type:Boolean,
            required: false
        }

        // 如果为 true ，则支持多个邮箱地址，且点击检查后会显示姓名并以姓名形式显示
        // 初始化时赋值给 m_isemails
        // ------------------------
        ,init_isemails:{
            type:Boolean,
            required: false
        }

    }
}
</script>
<style scoped>

._css-listitem{
    height: 24px;
    margin-top: 4px;
    margin-bottom: 4px;
    display: flex;
    align-items: center;
    cursor: pointer;
}

._css-listitem:hover{
    background-color: rgba(0,0,0,0.04);
}

._css-listitem-inner{
    padding-left:26px;
}

._css-userinput-suglist{
    box-shadow:0px 1px 3px 0px rgba(0,21,41,0.12);
    border-radius:2px;
    position: absolute;
    width: 100%;
    top: calc(100% + 6px);
    background-color: #fff;
    box-sizing: border-box;
    padding-top: 4px;
    padding-bottom: 4px;
    max-height: 200px;
    overflow-y: auto;
}
._css-innerinput{
    outline: none;
    border:1px solid transparent;
    
    box-sizing: border-box;
    margin-left:4px;
    background-color: transparent;
    font-size: 14px;
    flex: 1;
}
._css-innerinput:not(._css-innerinput) {
    width: 100%;
}
._css-innerinput2 {
    min-width: 100%;
    margin-top: 2px;
}

._css-all{
    border-radius:2px;
    background-color: rgba(0,0,0,0.02);
    display: flex;
    align-items: center;
    position:relative;
}
._css-icon{
    width:14px;
    height:14px;
    margin-left:8px;
    font-size: 14px;
}
._css-emailsinput-outerdiv{
    flex:1;
    margin-left: 8px;
    display: flex;
    flex-wrap: wrap;
    padding-top: 2px;
    padding-bottom: 2px;
}
._css-checkeditem{
    margin-left: 4px;
}

._css-checkeditem-in {
    margin-right: 4px;
    cursor: pointer;
    color:#1890ff;
    text-decoration: underline;
}
._css-userinputs-check {
    border: 1px solid transparent;
    background-color: #1890ff;
    color: #fff;
    height: 21px;
    padding: 2px 8px 2px 8px;
    border-radius: 4px;
    cursor: pointer;
    opacity: 0.8;
}
._css-userinputs-check:hover {
    opacity: 1;
}
._css-userinputs-check:active {
    opacity: 0.9;
}
</style>