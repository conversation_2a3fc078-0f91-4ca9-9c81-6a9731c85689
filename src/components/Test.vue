<template>
  <div>
    <VueCropper ref="cropper" :src="imgSrc" alt="Source Image"
    containerStyle="position:fixed;top:0;left:0;height:100%;width:100%;"
    imgStyle="width:100%;height:100%;"
      :cropmove="cropImage"
    ></VueCropper>
  </div>
</template>
<script>
import VueCropper from "vue-cropperjs";
import "cropperjs/dist/cropper.css";
export default {
  data() {
    return {
      imgSrc: "https://ss0.baidu.com/6ONWsjip0QIZ8tyhnq/it/u=2864786301,2267292249&fm=173&app=49&f=JPG?w=218&h=146&s=5150CD389DDE75C85A7541CB010080B2",
      tableData1: [
        {
          id: 1,
          date: "2016-05-02",
          name: "王小虎",
          address: "上海市普陀区金沙江路 1518 弄"
        },
        {
          id: 2,
          date: "2016-05-04",
          name: "王小虎",
          address: "上海市普陀区金沙江路 1517 弄"
        },
        {
          id: 3,
          date: "2016-05-01",
          name: "王小虎",
          address: "上海市普陀区金沙江路 1519 弄",
          hasChildren: true
        },
        {
          id: 4,
          date: "2016-05-03",
          name: "王小虎",
          address: "上海市普陀区金沙江路 1516 弄"
        }
      ]
    };
  },
  components: {
    VueCropper
  },
  methods: {
    load(tree, treeNode, resolve) {
      //debugger;
      resolve([
        {
          id: 31,
          date: "2016-05-01",
          name: "王小虎",
          address: "上海市普陀区金沙江路 1519 弄"
        },
        {
          id: 32,
          date: "2016-05-01",
          name: "王小虎",
          address: "上海市普陀区金沙江路 1519 弄"
        }
      ]);
    }
  }
};
</script>
<style scoped>
</style>