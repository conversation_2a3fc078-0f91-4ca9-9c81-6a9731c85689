<template>
  <div class="_css-mobile-materials-all">
    <div class="_css-mobile-title">
      基本信息
    </div>
    <div class="_css-mobile-list">
      <ul v-for='(item,index) in formLabel' :key="index">
        <li class="_css-mobile-list-index">
          <label for="">{{item.label}}</label>
          <span class="_css-mobile-form-label">{{item.value}}</span>
        </li>
      </ul>
    </div>
    <div class="_css-mobile-title">
      附件信息
    </div>
    <div class="_css-mobile-doc">
      <ul v-for='(item,index) in docList' :key='index'>
        <li 
          class="_css-mobile-doc-list" 
          :class="$staticmethod.getIconClassByExtname(item.name, 1)"
          @click.stop="previewdoc(item.id,item.name)"
        >
            <!-- getIconClassByExtname -->{{item.name}}
        </li>
      </ul>
    </div>
    <div class="_css-mobile-title">
      图片信息
    </div>
    <div class="_css-mobile-images-container">
      <div class="swiper-container" :option="swiperOptions">
        <div class="swiper-wrapper">
          <div class="swiper-slide" v-for="(item,index) in imgsList" :key="index"  @click.stop="handelClickLargerImage(item)">
              <div class="swiper-zoom-container">
                  <img :src="item">
              </div>
          </div>
        </div>
        <div class="swiper-pagination swiper-pagination-white"></div>
      </div>
    </div>
    <!-- 图片点击放大start -->
    <div class="_css-mobile-dialog" v-if="showLargeImages" @click.stop="handelClickLargerImage">
      <div class="_css-mobile-large-clickImg">
        <img :src="largeClickImg">
      </div>
    </div>
    <!-- 图片点击放大end -->

    <!-- idocview , dwg 预览 iframe 载体 -->
    <div class="_css-doc-preview" :class="extdata._show_idocview?'css-fc':'css-hide'">
      <div class="_css-doc-preview-beforeiframe">
        <div class="_css-doc-preview-beforeiframe-01"></div>
        <div class="_css-doc-preview-beforeiframe-02"></div>
        <div class="_css-doc-preview-beforeiframe-03"></div>
        <div
          class="icon-suggested-close"
          :class="'_css-doc-preview-closebtn-' + extdata._docviewtype"
          @click="close_idocview($event)"
        ></div>
        <div class="_css-doc-preview-beforeiframe-04 __web-inspector-hide-shortcut__"></div>
      </div>
      <!-- <div style="width:100%;height:100%;-webkit-overflow-scrolling: touch;overflow-y:scroll;">
        <iframe class="_css-doc-preview-iframe" :src="extdata._idocviewurl"></iframe>
      </div> -->
      <div style="overflow: auto;-webkit-overflow-scrolling:touch;width:100%;height:100%;">　
    　　<iframe v-if="type"  class="_css-doc-preview-iframe" :src="extdata._idocviewurl" scrolling="auto" frameborder="0" width="80%" height="100%"></iframe>
    　　<iframe v-else  class="_css-doc-preview-iframe" :src="extdata._idocviewurl" frameborder="0" scrolling='no' style="width: 1px;min-width: 80%; *width: 80%;"></iframe>
      </div>
    </div>
    <!-- //idocview , dwg 预览 iframe 载体 -->
  </div>
</template>
<script>
import '@/assets/css/mobStyle.css';
import '@/assets/css/onlinePreview.css';
import Swiper from "swiper";
import 'swiper/dist/css/swiper.min.css';
export default {
  name:"MaterialsMgrMobile",
  data() {
    return {
      bm_guid: '',             // 构件ID
      fullData: {},            // 构件详细数据
      showLargeImages: false,
      largeClickImg: '',
      formLabel: [],
      docList: [],
      imgsList:[],
      swiperOptions: {},
      dwgurl:'',  // 在线预览dwg
      extdata:{
        _show_idocview: false, // 显示 idocview
        _idocviewurl: "about:blank", // 在线预览文档地址
        _docviewtype: "document" // ''默认， 'office', 'dwg'
      },
      type: Boolean
    };
  },
  created(){
    var _this = this; // bm_guid
    _this.getMaterialDetail();
    console.log(_this.$route.params)

    var u = navigator.userAgent;
    var isAndroid = u.indexOf('Android') > -1 || u.indexOf('Adr') > -1; //android终端
    // var isiOS = !!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/); //ios终端
    // console.log('是否是Android：'+isAndroid);
    // console.log('是否是iOS：'+isiOS);
    if(isAndroid){
    　　this.type = true
    }else{
    　　this.type = false
    }
  },
  mounted() {
    this.swiperOptions = new Swiper ('.swiper-container', {
      zoom: false,
      pagination: {
        el: '.swiper-pagination',
        type:'fraction'
      },
      navigation: {
        nextEl: '.swiper-button-next',
        prevEl: '.swiper-button-prev',
      }, 
    })
  },
  filters:{
    
  },
  computed:{
    
  },
  methods: {
    getMaterialDetail(){
      var _this =this;
      _this.bm_guid = _this.$route.params.bm_guid;
      let token =  _this.$route.params.Token;
      _this.$axios.get(`${this.$MgrBaseUrl.GetMaterialDetail}?bm_guid=${_this.bm_guid}&Token=${token}`)
      .then(x => {
        if (x.status == 200) {
          if (x.data.Ret > 0) {
            _this.fullData = x.data.Data;
            // 先清空 formLabel
            _this.formLabel = [];
            //debugger;
            if(_this.fullData.bm.bm_materialname){
              _this.formLabel.push({
                label: "构件名称",
                value: _this.fullData.bm.bm_materialname
              });
            }
            if(_this.fullData.bm.bm_materialcode){
              _this.formLabel.push({
                label: "编码（ID）",
                value: _this.fullData.bm.bm_materialcode
              });
            }
            if(_this.fullData.bm.bm_materialcount){
              _this.formLabel.push({
                label: "数量",
                value: _this.fullData.bm.bm_materialcount
              });
            }
            if(_this.fullData.bm.bm_materialunit){
              _this.formLabel.push({
                label: "单位",
                value: _this.fullData.bm.bm_materialunit
              });
            }
            if(_this.fullData.bm.bm_planarrtime){
              _this.formLabel.push({
                label: "时间",
                value: _this.fullData.bm.bm_planarrtime.replace('T', ' ')
              });
            }
            if (_this.fullData.bm.bm_extjson) {
              var obj = JSON.parse(_this.fullData.bm.bm_extjson);
              if (obj) {
                for (var i = 0; i < obj.length; i++) {
                  _this.formLabel.push({
                    label: obj[i].fieldname,
                    value: obj[i].fieldval
                  });
                }
              }
            }

            // brs_fileinfo
            if (_this.fullData.brs_fileinfo && _this.fullData.brs_fileinfo.length > 0) {
              _this.docList = [];
              for (var i = 0; i < _this.fullData.brs_fileinfo.length; i++) {
                _this.docList.push({
                  name:_this.fullData.brs_fileinfo[i].file.FileName,
                  //FileId
                  id:_this.fullData.brs_fileinfo[i].file.FileId
                });
              }
            }
            // brbfs
            if (_this.fullData.brbfs && _this.fullData.brbfs.length > 0) {
              //_this.docList = [];
              _this.imgsList = [];
              for (var i = 0; i < _this.fullData.brbfs.length; i++) {
                // _this.docList.push({
                //   name:_this.fullData.brbfs[i].file.FileName,
                //   //FileId
                //   id:_this.fullData.brbfs[i].file.FileId
                // });
                _this.imgsList.push(`${window.bim_config.webserverurl}/${_this.fullData.brbfs[i].fileobj.bf_path}`);
              }
            }
          }
        }
      })
      .catch(x => {

      });
    },
    handelClickLargerImage(index) {
      this.largeClickImg = index
      this.showLargeImages = !this.showLargeImages;
    },
    // 在线预览
    previewdoc(fileid, filename){
      // 获得文件的下载地址
      var _this = this;     
      _this.dwgurl = _this.$route.params.dwgurl
      let token =  _this.$route.params.Token;

      let filedownloadurl = `${window.bim_config.webserverurl}/api/v1/attach/preview?id=${fileid}&token=${token}`
      // console.log(filedownloadurl)
      // 根据扩展名获取在线浏览地址
      var url_iframe_all;
      if (filename.toLowerCase().indexOf(".dwg") > 0) {
        // _this.$emit("set_projectboot_extdata", "_docviewtype", "dwg");
        _this.extdata._docviewtype = "dwg";
        url_iframe_all = `${
          _this.dwgurl
        }/Home/Index2?dwgurlcfg=${encodeURIComponent(filedownloadurl)}&name=${
          filename
        }`;
      } else {

        _this.extdata._docviewtype = "office";
     
        console.log('扫描构件管理中的二维码打开的页面，预览构件的关联附件');
        url_iframe_all = _this.$staticmethod.getHuangNewcomputeViewUrl(filedownloadurl,filename, filename);

      }
      _this.extdata._show_idocview = true;
      _this.extdata._idocviewurl = url_iframe_all;
    },
    // 关闭 idocview 在线预览
    close_idocview(ev) {
      ev.stopPropagation();
      var _this = this;
      _this.extdata._idocviewurl = "about:blank";
      _this.extdata._show_idocview = false;
    },
  },
  
};
</script>
<style scoped>
  ._css-mobile-materials-all{
    background: #F6F5F8;
    font-size: 0.85rem;
    height: 100%;
    overflow-y: scroll;
  }
  ._css-mobile-title{
    font-size: 1rem;
    text-align: left;
    height: 3.2rem;
    line-height: 3.2rem;
    padding-left:1rem;
  }
  ._css-mobile-list{
    background: #fff;
  }
  ._css-mobile-list-index{
    height: 2.6rem;
    line-height: 2.6rem;
    border-bottom: 0.05rem solid rgba(0,0,0,.1);
  }
  ._css-mobile-list-index label{
    float: left;
    color:#A5A9AA;
    padding-left: 1rem;
  }
  ._css-mobile-list-index span{
    float: right;
    color:#666666;
    padding-right: 1rem;
  }
  ._css-mobile-doc-list{
    margin:0 0.5rem 0.5rem;
    border-radius: .2rem;
    background-color: #fff;
    background-position-x: 0.5rem;
    line-height: 2.6rem;
    height: 2.6rem;
    color:#666666;
    padding-left:2.4rem;
  }
  ._css-mobile-images-container .swiper-container {
    width: 100%;
    margin: 0 auto;
  }
  ._css-mobile-images-container .swiper-slide {
    overflow: hidden;
    border-radius: 0.4rem;
  }
  ._css-mobile-images-container .swiper-pagination{
    text-align: center;
  }
  ._css-mobile-images-container .swiper-zoom-container{
    width:90%;
    margin: 0 5%;
  }
  ._css-mobile-images-container .swiper-container-horizontal>.swiper-pagination-bullets, .swiper-pagination-custom, .swiper-pagination-fraction{
    bottom: 1.4rem !important;
    right: 1.6rem !important;
    left:auto;
    color: #fff;
    font-size: 14px;
    width: 3.5rem;
    font-weight:500;
    height: 1.6rem;
    line-height: 1.6rem;
    border-radius: 0.3rem;
    background: rgba(0,0,0,.3);
  }
  ._css-mobile-images-container .swiper-zoom-container>img{
    border-radius: 0.4rem;
  }
  ._css-mobile-dialog{
    width:100%;
    height:100%;
    position: fixed;
    left: 0;
    top:0;
    bottom: 0;
    right: 0;
    z-index: 99;
    background: #eae6e6;
    overflow: hidden;
  }
  ._css-mobile-large-clickImg{
    position: absolute;
    left:50%;
    top:50%;
    width: 96%;
    transform: translate(-50%, -50%);
  }
  ._css-mobile-large-clickImg img{
    border-radius: 0.4rem;
  }
</style>