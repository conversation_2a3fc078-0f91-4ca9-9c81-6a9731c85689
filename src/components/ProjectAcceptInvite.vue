<template>
  <div class="_css-all-ProjectAcceptInvite">
    <div class="_css-form-PAI">
      <div class="_css-form-PAI-inner">

<div class="_css-title" >
    <div class="_css-title-inner " >
        <div class="_css-title-icon icon-interface-lock" ></div>
        <div class="_css-title-text">
            <div>{{newaccount==true?'设置信息':'接受邀请'}}</div>
        </div>
    </div>
</div>

        <template v-if="newaccount == true">

            <!-- 请输入姓名 -->
            <div class="_css-pwd">
                <CompsUsersInput
                    @oninput="_realname"
                    :placeholder="'请输入姓名'"
                    iconclass="icon-interface-user"
                    :ispassword="false"
                ></CompsUsersInput>
            </div>
            <!-- //请输入姓名 -->

            <!-- 请输入手机号 -->
            <div class="_css-pwd">
                <CompsUsersInput
                    @oninput="_mobilephone"
                    :placeholder="'请输入手机号（可选）'"
                    iconclass="icon-interface-phone"
                    :ispassword="false"
                ></CompsUsersInput>
            </div>
            <!-- //请输入手机号 -->

            <div class="_css-pwd">
            <CompsUsersInput
                @oninput="_pwd"
                :placeholder="'请输入密码,6-32个字符，至少使用两种字符组合'"
                iconclass="icon-interface-lock"
                :ispassword="true"
            ></CompsUsersInput>
            </div>
            <div class="_css-pwd2">
            <CompsUsersInput
                @oninput="_pwd2"
                :placeholder="'请再次输入密码'"
                iconclass="icon-interface-lock"
                :ispassword="true"
            ></CompsUsersInput>
            </div>
        </template>
      

        <div class="_css-btnok">
          <CompsEloButton
            @onclick="btnok_click"
            :disabled="isbtndisabled"
            :text="'确定'"
            :color="'#fff'"
            :fontSize="12"
            :width="352"
            :height="40"
          ></CompsEloButton>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import CompsUsersInput from "@/components/CompsAuth/CompsUsersInput";
import CompsEloButton from "@/components/CompsElOverride/CompsEloButton";

export default {
  data() {
    return {
        passwordsettinginfo:{

            // 姓名
            // ----
            realname:'',

            // 手机号
            // -----
            mobile:'',

            pwd:'',
            pwd2:''
        },
        newaccount: false // 为true时，显示密码输入框
        ,isbtndisabled: false
    };
  },
  mounted(){
      var _this = this;
      var _InvGuid = _this.$route.params.InvGuid;

      // 清空本地的账号及密码
      _this.$staticmethod.Set("Token", "");
      _this.$staticmethod.Set("RealName", "");
      _this.$staticmethod.Set("Account", "");
      _this.$staticmethod.Set("UserId", "");
      _this.$staticmethod.Set("Email", "");



      // 判断是新账号还是老账号
      _this.$axios({
          method: 'get',
          url: `${_this.$configjson.webserverurl}/api/User/User/TestEmailExists?InvGuid=${_InvGuid}&Token=${this.$staticmethod.Get('Token')}`
      }).then(x => {
            if (x.status != 200) {
              _this.$message({
                  message: '服务器错误',
                  type: 'error'
              });
              _this.$staticmethod.debug(x);

              // 禁用提交按钮
              _this.isbtndisabled = true;

              // 跳转到倒计时页面
              setTimeout(()=>{
                  _this.jumptocountdown();
              }, 1000);
              

          } else if (x.data.Ret < 0) {
              _this.$message({
                  message: x.data.Msg,
                  type: 'error'
              });
              _this.$staticmethod.debug(x);

              // 禁用提交按钮
              _this.isbtndisabled = true;

              // 跳转到倒计时页面
              setTimeout(()=>{                  
                  _this.jumptocountdown();
              }, 1000);
              

          } else {
              
             _this.newaccount = x.data.Data.IsExists != true;

          }
      }).catch(x => {
           _this.$message({
              message: '服务器错误',
              type: 'error'
          });
          _this.$staticmethod.debug(x);
      });
  },
  methods: {

    // 姓名、手机号
    // -----------
    _realname(str) {
        var _this = this;
        _this.passwordsettinginfo.realname = str;
    },
    _mobilephone(str) {
        var _this = this;
        _this.passwordsettinginfo.mobile = str;
    },

    jumptocountdown(){
        var _this = this;
        window.location.href = `${window.bim_config.hasRouterFile}/#/CountDown`;
    },
    _pwd(str) {
        var _this = this;
        _this.passwordsettinginfo.pwd = str;
    },
    _pwd2(str) {
        var _this = this;
        _this.passwordsettinginfo.pwd2 = str;
    },
    btnok_click() {
        
        //debugger;
        var _this = this;

        // 判断添加判空
        // -----------
        if (_this.passwordsettinginfo.realname == '') {
            _this.$message({
                message: '姓名不能为空',
                type:'error'
            });
            return;
        }

        // 手机号不为空时，判断格式
        // ----------------------
        if (_this.passwordsettinginfo.mobile && _this.passwordsettinginfo.mobile != '') {
            if (!/(\d){9}/.test(_this.passwordsettinginfo.mobile)) {
                 _this.$message({
                    message: '手机号格式不正确',
                    type:'error'
                });
                return;
            }
        }

        // 判断添加判空
        // -----------
        if (_this.passwordsettinginfo.pwd == '') {
            _this.$message({
                message: '密码不能为空',
                type:'error'
            });
            return;
        }

        if (_this.passwordsettinginfo.pwd != _this.passwordsettinginfo.pwd2) {
            _this.$message({
                message: '两次输入的密码不一致',
                type:'error'
            });
            return;
        }

        // 调用接口，形成当前用户的用户数据
        var _InvGuid = _this.$route.params.InvGuid;
        var pwdcried;
        if (_this.newaccount==true) {
            pwdcried = _this.$staticmethod.computepwdstr(
                _this.passwordsettinginfo.pwd + _this.$configjson.publickey
                , _this.$CryptoJS);
        } else {
            pwdcried = '';
        }
        
        // 禁用提交按钮
        _this.isbtndisabled = true;

        _this.$axios({
            method: 'post',
            url:`${_this.$urlPool.AcceptInvite}?Token=${this.$staticmethod.Get('Token')}`,
            data: {
                InvGuid: _InvGuid,
                PwdCyied: pwdcried,
                RealName: _this.passwordsettinginfo.realname,
                Mobile: _this.passwordsettinginfo.mobile
            }
        }).then(x => {
            if (x.status != 200) {
              _this.$message({
                  message: '服务器错误',
                  type: 'error'
              });
              _this.$staticmethod.debug(x);
          } else if (x.data.Ret < 0) {
              _this.$message({
                  message: x.data.Msg,
                  type: 'error'
              });
              _this.$staticmethod.debug(x);

              // 按钮启用回来
              // -----------
              _this.isbtndisabled = false;

          } else {
            _this.$message({
                message: _this.newaccount==true?'密码设置成功，您现在可以登录了':'操作成功，您现在可以登录了',
                type: 'success'
            });

            // 跳转到倒计时页面
            setTimeout(()=>{
                _this.jumptocountdown();
            }, 1000);

          }
        }).catch(x => {
        //   _this.$message({
        //       message: '错误',
        //       type: 'error'
        //   });
        //   _this.$staticmethod.debug(x);
            console.log(x);
        });
    }
  },
  components: {
    CompsUsersInput,
    CompsEloButton
  }
};
</script>
<style scoped>
._css-title-inner{
    display: flex;
    align-items: center;
    justify-content:center;

}
._css-title{
    height:28px;
    margin-top:46px;
    color:rgba(0,0,0,0.85);
    font-size: 20px;
}
._css-title-icon{
    height:20px;
    width:20px;
    color:#1890FF;
}
._css-title-text{
    margin-left: 8px;
    display: flex;
    align-items: center;
}
._css-pwd {
    margin-top: 28px;
}
._css-pwd2 {
    margin-top: 24px;
}
._css-btnok{
    margin-top: 32px;
}
._css-form-PAI {
  width: 400px;
  background: rgba(255, 255, 255, 1);
  box-shadow: 0px 13px 24px -17px rgba(11, 41, 62, 0.8);
  border-radius: 2px;
  min-height: 48px;
  padding-bottom: 50px;
  user-select: none;
}
._css-all-ProjectAcceptInvite {
  display: flex;
  align-items: center;
  justify-content: space-around;
  background: rgba(240, 242, 245, 1);
  height: 100%;
  width: 100%;
}
._css-form-PAI-inner {
  margin-left: 24px;
  margin-right: 24px;
}
</style>