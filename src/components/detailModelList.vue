<template>
  <div class="_css-mobile-materials-all">
    <div class="back-last">
      <div @touchend.stop="back"> 
        <span class="back-icon icon-arrow-left_outline"></span> 
        返回
      </div>
      <p>{{title.FullName}}</p>
    </div>
    <ul  class="model-list-ul" v-if="modelList.length>0">
      <li class="model-list" v-for="(item,index) in modelList" :key='index' @click.stop="handelClickList(item)">
        <div class="list-img">
          <img :src="GetModelImg(item.Thumbnail,item.IsMerge)" alt="" width="100%" height="100%">
        </div>
        <div class="list-content">
          <p>{{item.Name}}</p>
          <span>更新时间：{{ new Date(item.CreateTime).toLocaleString('chinese',{hour12:false}) }}</span>
        </div>
      </li>
    </ul>
    <div v-if="modelListNo" class="no-list">
      暂无数据
    </div>
  </div>
</template>
<script>
import '@/assets/css/mobStyle.css';
export default {
  name:"detailModelList",
  data() {
    return {
      title: '',
      modelList: [],
      modelListNo: false,
    };
  },
  created(){
     
  },
  mounted() { 
    this.title = JSON.parse(sessionStorage.getItem('firstClickItem'))
    this.getPhaseVal();
  },
  filters:{
    
  },
  computed:{
    
  },
  methods: {
    back(item){
      let _parentId = sessionStorage.getItem('parentIdParam');
      let _exceptGuid = sessionStorage.getItem('exceptGuidParam');
      window.location.href = `${window.bim_config.hasRouterFile}/#/LookModelWeChat/${_parentId}/${_exceptGuid}`;
    },
    handelClickList(item){ 
      sessionStorage.setItem('modelListClickItem',JSON.stringify(item));
      window.location.href = `${window.bim_config.hasRouterFile}/#/ModelIframe`;
    },
    getPhaseVal(){
      let _this = this;
      _this.$axios
        .get(`${window.bim_config.webserverurl}/api/DHCC/Dpm/GetPhaseValByBIMComposerId?bimcomposerId=${this.title.BIMComposerID}&token=${this.$staticmethod.Get("Token")}`)
        .then( x => {
          if(x.status == 200) {
            _this.getModelList(x.data.Data);
          }

        }).catch( error => {

        })
    },
    getModelList(param) {
      let _this = this;
      fetch(
        `${
          _this.$staticmethod.getBIMServer()
        }/Api/Prj/GetAllModels?ProjectID=${_this.title.BIMComposerID}&Phase=${param}&sign=''`
        ).then(res => {
            return res.json();
        })
        .then(res => {
            if (typeof [] == typeof res) {
                res = JSON.stringify(res);
            }
            this.modelList = JSON.parse(res); 
            if(this.modelList.length<=0){
              this.modelListNo = true;
            }else{
              this.modelListNo = false;
            }
        });
    },
    GetModelImg(Thumbnail,IsMerg){
      if(Thumbnail==''){
        if(IsMerg){
          return require('../assets/svgs_loadbyurl/model-Merg.svg');
        }else{
          return require('../assets/svgs_loadbyurl/model-Default.svg');
        }
      }else{
        return 'data:image/png;base64,'+ Thumbnail;
      }
    },
  },
  
};
</script>
<style scoped>
  ._css-mobile-materials-all{
    background: #F7F7F7;
    font-size: 0.85rem;
    height: 100%;
  }
  .model-list{
    display: flex;
    height: 4.7rem;
    background: #FFFFFF;
    box-shadow: 0px 4px 4px 0px rgba(41, 44, 54, 0.01);
    border-radius: 6px;
    margin: 15px 15px 0;
    padding: 10px;
  }
  .list-img{
    width: 4.66rem;
    height: 4.66rem;
    border-radius: 6px;
    background: rgba(0, 0, 0, 0.1);
  }
  .list-content {
    margin-left: 10px;
    display: flex;
    flex-direction: column;
    overflow-x: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    flex: 1;
  }
  .list-content p{
    font-size: 1rem;
    font-weight: 500;
    color: #283A4F;
    line-height: 20px;
    letter-spacing: 1px;
    overflow-x: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  .list-content span{
    display: inline-block;
    font-size: 0.85rem; 
    color: #A6AEB6;
    margin-top:2rem;
    overflow-x: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  .back-last{
    text-align: center;
    line-height: 3.33rem;
    height: 3.33rem;
    display: flex;
    align-items: center;
    justify-items: center;
    text-align: center;
    position: relative;    
    background: #FFFFFF;
    box-shadow: 0px 1px 5px 0px rgba(0, 0, 0, 0.1);
  }
  .back-last div{
    position: absolute;
    left: 0.3rem;
    
  }
  .back-last p{
    flex:1;
    text-align: center;
    max-width: 63%;
    overflow: hidden;
    overflow-x: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    margin: 0 auto;
  }
  .model-list-ul{
    overflow-y: scroll;
    height: calc(100% - 3.3rem);
  }
  .back-icon{
    vertical-align:middle;
  }
  .no-list{
    margin: 1.5rem auto;
    line-height: 2rem;
    font-size: 0.85rem;
    text-align: center;
  }
</style>