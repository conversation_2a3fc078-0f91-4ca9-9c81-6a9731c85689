<template>
    <div class="Model_Info mi" :style="{zIndex:ZIndex}">
        <div class="title"><font class="l">模型详情</font><i class="icon-suggested-close close" @click="$emit('CloseModelInfo')"></i></div>
        <div class="con">
            <div class="p1">
                <ul>
                    <li 
                    :data-showingversionno="lastOpenVersionNo"
                    :data-versionno="item.VersionNO" 
                    v-for="(item,index) in this.VersionList" :class="selCurrentVersion==item.VersionNO?'sel':''" @click="selCurrentVersion=item.VersionNO" :key="item.VersionNO">
                        V {{item.VersionNO}}.0

                        <!-- index==0 就 current? -->
                        <!-- <i :class="index==0?'current':''"></i> -->

                        <i :class="{'current':IsShowingVersion(item.VersionNO, index)}"></i>
                        <i class="icon-suggested-info_circle Info" @mouseover="showDetile($event,index==0,item)" @mouseout="hideDetile()"></i>
                    </li>
                </ul>
                <button class="openCurrent" @click="openVersion">打开V {{selCurrentVersion}}.0</button>
            </div>
            <div class="p2">
                <ul class="totalInfo">
                    <li v-for="item in CountList_p2" :key="item.key">
                        <span class="num">{{item.num}}</span>
                        <span class="name">{{item.name}}</span>
                    </li>
                </ul>
            </div>
            <div class="p3">
                <ul class="totalInfo">
                    <li v-for="item in CountList_p3" :key="item.key">
                        <span class="num">{{item.num}}</span>
                        <span class="name">{{item.name}}</span>
                    </li>
                </ul>
            </div>
        </div>
        <div class="btns _css-btns-bottom">
            <div class="_css-btn-leftarea" >

                <input id="idprojectid"  class="_css-btn-leftinput" type="text" readonly :value="GetOrganizeId()" />
                <div @click="copyOrganizeId($event)"  class="_css-btn-leftbtn" >
                    复制项目ID
                </div>
                <input id="idmodelid"  class="_css-btn-leftinput _css-btn-leftinput2" type="text" readonly :value="ModelID" />
                <div @click="copyModelId($event)" class="_css-btn-leftbtn" >
                    复制模型ID
                </div>
            </div>
            <button class="blue" @click="$emit('CloseModelInfo')">关闭</button>
        </div>
        <span v-if="IsShowDetile" class="Detile" :style="{top:DetileY}">{{ModelInfoStr}}</span>
    </div>
</template>
<script>
export default {
    name:'Model_Info',
    components:{
        
    },
    props:{
        ProjectID:{
            type:String,
            required:true,
        },
        SelModel:{
            type:Object,
            required:true,
        },
        tranIndex:{
            type:Number,
            required:true,
        },
        lastOpenVersionNo:{
            required: false
        }
    },
    mounted(){

        // 调试信息：当前modelinfo对应模型的版本
        // ----------------------------------
        var _this = this;
        console.log(`lastOpenVersionNo: ${_this.lastOpenVersionNo}`);
    },
    data(){
        return {
            ModelID:'',
            IsMerge:false,
            Phase:'',
            CountList_p2:[
                {key:'Count_View',name:'视图',num:0},
                {key:'Count_Element',name:'构件',num:0},
                {key:'Count_Space',name:'空间',num:0},
                {key:'Count_System',name:'系统',num:0},
                {key:'Count_Viewpoint',name:'视点',num:0},
                {key:'Count_PZ',name:'批注',num:0},
                {key:'Count_2D',name:'2D',num:0},
                {key:'Count_Level',name:'楼层',num:0},
            ],
            CountList_p3:[
                {key:'Count_Doc',name:'文档',num:0},
                {key:'Count_Issue',name:'问题',num:0},
            ],
            VersionList:[],
            ModelInfoStr:"",
            IsShowDetile:false,
            DetileY:'',

            selCurrentVersion:0,
            ZIndex:1001,
        };
    },
    methods:{

        copyOrganizeId(ev) {
            var _this = this;
            document.getElementById("idprojectid").focus();
            document.getElementById("idprojectid").select();
            document.execCommand("Copy");
            _this.$message({
                message: "复制成功",
                type: "success"
            });

        },

        copyModelId(ev) {
            var _this = this;
            document.getElementById("idmodelid").focus();
            document.getElementById("idmodelid").select();
            document.execCommand("Copy");
            _this.$message({
                message: "复制成功",
                type: "success"
            });
        },

        GetOrganizeId(){
            var _this = this;
            //var organizeId = _this.$staticmethod._Get("organizeId");
            var bimcomposerId = _this.$staticmethod._Get("bimcomposerId");
            return bimcomposerId;
        },

        // 是否显示 current class
        // ---------------------
        IsShowingVersion(VersionNO, index) {

            // 第一次打开ModelInfo前，并未指定版本，lastOpenVersionNo 为最新版本号
            // ---------------------------------------------------------------
            var _this = this;
            if (index == 0 && (_this.lastOpenVersionNo == '' || _this.VersionList.length == 1)) {
                return true;
            }

            // 相同版本号时返回true
            // -------------------
            if (VersionNO == _this.lastOpenVersionNo) {
                return true;
            }
            return false;

        },

        SetValOfkey(list,key,val)
        {
            for(let i=0;i<list.length;i++)
            {
                if(list[i].key==key)
                    list[i].num=val;
            }
        },
        showDetile(e,isFirst,item)
        {
             // 调用接口通过 UserId 获取 RealName
            // UserId 为 this.Creator
            // ----------------------
            var _this = this;
            var _url = `${window.bim_config.webserverurl}/api/User/User/GetUserInfoByUserId?UserId=${this.Creator}&Token=${this.$staticmethod.Get('Token')}`;
            setTimeout(()=>{
                
                _this.$axios.get(_url).then(x => {
                    if (x.data.Ret > 0) {
                        var realname = x.data.Data.RealName;
    
                        this.IsShowDetile=true;
                        var ty=e.y-(document.body.clientHeight-420)/2-10;
                        this.DetileY=ty+"px";
                        if(this.IsMerge)
                            this.ModelInfoStr+='合并模型-';
                        this.ModelInfoStr+=realname+"于";
                         if(isFirst){
                            this.ModelInfoStr+=new Date(this.CreateTime).toLocaleString('chinese',{hour12:false}).slice(0,10);
                            this.ModelInfoStr+='  ';
                            this.ModelInfoStr+=new Date(this.CreateTime).toLocaleString('chinese',{hour12:false}).slice(10);
                        }
                        else{
                            this.ModelInfoStr+=new Date(item.CreateTime).toLocaleString('chinese',{hour12:false}).slice(0,10);
                            this.ModelInfoStr+='  ';
                            this.ModelInfoStr+=new Date(item.CreateTime).toLocaleString('chinese',{hour12:false}).slice(10);
                        }
                        this.ModelInfoStr+="上传";
                    }
                }).catch(x => {
    
                });
            },10)

            //debugger;
        },
        hideDetile(){
            this.IsShowDetile=false;
            this.ModelInfoStr="";
        },

        // 打开指定版本的模型
        // ----------------
        openVersion() {
            this.$staticmethod.debugshowlog(`emit openVersion this.selCurrentVersion = ${this.selCurrentVersion}`);
            this.$emit('openVersion',this.SelModel,'',this.selCurrentVersion);
        }
    },
    created(){

        // 私有成员赋值
        // -----------
        var _this = this;
        _this.ModelID = _this.SelModel.ID;
        _this.IsMerge = _this.SelModel.IsMerge;
        _this.Phase = (_this.SelModel.Phase || '').toString();

        if(this.tranIndex<0) {
            this.ZIndex=1001;
        }
        if (this.ZIndex < this.tranIndex) {
            this.ZIndex = this.tranIndex;
        }
        this.$staticmethod.debugshowlog(`this.tranIndex = ${this.tranIndex}, this.ZIndex = ${this.ZIndex}`);
        //统计信息------------start
        this.$axios.get(this.$urlPool.base_ModelApi+this.$urlPool.GetModelInfo+"?ProjectID="+this.ProjectID+"&ModelID="+this.ModelID).then(res=>{
            this.SetValOfkey(this.CountList_p2,'Count_View',res.data.Views.length);
        }).catch(error=>{
            this.SetValOfkey(this.CountList_p2,'Count_View',0);
        });
        this.$axios.get(this.$urlPool.base_ModelApi+this.$urlPool.GetAllElementCount+"?ProjectID="+this.ProjectID+"&ModelID="+this.ModelID+"&VersionNO=").then(res=>{
            this.SetValOfkey(this.CountList_p2,'Count_Element',res.data);
        }).catch(error=>{
            this.SetValOfkey(this.CountList_p2,'Count_Element',0);
        });
        this.$axios.get(this.$urlPool.base_ModelApi+this.$urlPool.GetSpaceCount+"?ProjectID="+this.ProjectID+"&ModelID="+this.ModelID+"&VersionNO=").then(res=>{
            this.SetValOfkey(this.CountList_p2,'Count_Space',res.data);
        }).catch(error=>{
            this.SetValOfkey(this.CountList_p2,'Count_Space',0);
        });
        this.$axios.get(this.$urlPool.base_ModelApi+this.$urlPool.GetSystemNum+"?ProjectID="+this.ProjectID+"&ModelID="+this.ModelID+"&VersionNO=").then(res=>{
            this.SetValOfkey(this.CountList_p2,'Count_System',res.data.length);
        }).catch(error=>{
            this.SetValOfkey(this.CountList_p2,'Count_System',0);
        });
        this.$axios.get(this.$urlPool.base_ModelApi+this.$urlPool.GetAllViewPoint+"?ProjectID="+this.ProjectID+"&ModelID="+this.ModelID).then(res=>{
            let ViewPointNum=0;
            let PZNum=0;
            let tempList=res.data;
            for(let i=0;i<tempList.length;i++)
            {
                if(tempList[i].Type==0)
                    ViewPointNum++;
                else
                    PZNum++;
            }
            this.SetValOfkey(this.CountList_p2,'Count_Viewpoint',ViewPointNum);
            this.SetValOfkey(this.CountList_p2,'Count_PZ',PZNum);
        }).catch(error=>{
            this.SetValOfkey(this.CountList_p2,'Count_Viewpoint',0);
            this.SetValOfkey(this.CountList_p2,'Count_PZ',0);
        });
        this.$axios.get(this.$urlPool.base_ModelApi+this.$urlPool.GetFile+"?ProjectID="+this.ProjectID+"&ModelID="+this.ModelID+"&VersionNO=&FileType=PlanView&FileName=sheets").then(res=>{
            let tempList=res.data;
            if(tempList.length)
                this.SetValOfkey(this.CountList_p2,'Count_2D',tempList.length);
            else
                this.SetValOfkey(this.CountList_p2,'Count_2D',0);
        }).catch(error=>{
            this.SetValOfkey(this.CountList_p2,'Count_2D',0);
        });
        this.$axios.get(this.$urlPool.base_ModelApi+this.$urlPool.GetAllLevelCount+"?ProjectID="+this.ProjectID+"&ModelID="+this.ModelID+"&VersionNO=").then(res=>{
            let result=res.data;
            if(result==-1)
                this.SetValOfkey(this.CountList_p2,'Count_Level',0);
            else
                this.SetValOfkey(this.CountList_p2,'Count_Level',result);
        }).catch(error=>{
            this.SetValOfkey(this.CountList_p2,'Count_Level',0);
        });
        this.$axios.get(this.$urlPool.base_ModelApi+this.$urlPool.GetFileCountBySourceID+"?ProjectID="+this.ProjectID+"&LikeName=&SourceID="+this.ModelID).then(res=>{
            this.SetValOfkey(this.CountList_p3,'Count_Doc',res.data);
        }).catch(error=>{
            this.SetValOfkey(this.CountList_p3,'Count_Doc',0);
        });
        debugger;
        this.$axios.get("https://api.probim.cn/Project/GetRelationIssueBySourceID?SourceID="+this.ModelID+"&BIMComposerID="+this.ProjectID).then(res=>{
            this.SetValOfkey(this.CountList_p3,'Count_Issue',res.data.rows.length);
        }).catch(error=>{
            this.SetValOfkey(this.CountList_p3,'Count_Issue',0);
        });
        //统计信息--------------end

        // created 时，获取 ProjectID 项目下 ModelID 的所有版本信息
        // -----------------------------------------------------
        _this.$axios.get(this.$urlPool.base_ModelApi+this.$urlPool.GetAllVersions+"?ProjectID="+this.ProjectID+"&ModelID="+this.ModelID).then(res=>{
            
            // res.data 为版本列表
            // ------------------
            var templist= JSON.parse(res.data);
            _this.VersionList=templist;
            
            // 大括号换行，if 不加大括号，还循环判断+赋值？
            // ----------------------------------------
            console.log(226);
            var currentVersion=0;
            for(let i=0;i<templist.length;i++)
            {
                if(templist[i].VersionNO>currentVersion) {
                    currentVersion=templist[i].VersionNO;
                }
            }
            currentVersion++;
            _this.selCurrentVersion=currentVersion;

            // 当前版本 selCurrentVersion 赋值
            // ------------------------------
            console.log(_this.selCurrentVersion);

            _this.VersionList.unshift({'CreateTime':'','Creator':'','Description':'','ModelID':this.ModelID,'Phase':this.Phase,'Thumbnail':'','VersionNO':currentVersion});
        });
    }
}
</script>
<style scoped>

.mi{
  width:869px;height:420px;position: absolute;top:50%;left:50%;transform:translate(-50%,-50%);z-index:97;
}

._css-btns-bottom{
        width: 100%;
    height: 32px;
    line-height: 32px;
    font-size: 0px;
    position: relative;
    display: flex;
}

ul{padding: 0px;margin: 0px;}li{padding: 0px;margin: 0px;list-style-type: none;}
.Model_Info{overflow: hidden;background-color: #fff;box-shadow:0px 13px 24px -17px rgba(11,41,62,0.8);border-radius:4px;font-family:PingFangSC-Medium;padding:6px 24px 16px 24px;}
.Model_Info .title{width:100%;height:48px;font-size:16px;color:#333;line-height:48px;text-align:left;font-weight:bold; position: relative;overflow: hidden;}
.Model_Info .title .close{width:18px;height:18px;position: absolute;right:0px;top:15px;display:block;}
.Model_Info .title .close::before{float:left;width:18px;height:18px;display:block;text-align:left;text-indent:0px;cursor: pointer;}
.Model_Info .con{width:100%;height:calc(100% - 32px - 48px - 20px);margin-top:20px;overflow:hidden;line-height:50px;}

.Model_Info .btns[data-v-1247c066] {

}

._css-btn-leftbtn{
    height: 32px;
    padding-left: 8px;
    padding-right: 8px;
    color: rgba(24, 144, 255, 1) !important;
    border-radius: 4px;
    opacity: 1;
    background-color: white;
    border:1px solid rgba(0,0,0,0.25);
    font-size: 12px;
    margin-left:8px;
    cursor:pointer;
}

._css-btn-leftinput {
    height: 24px;
    min-width: 235px;
    line-height: 24px;
    text-align: left;
    color: rgba(0,0,0,0.65);
    padding-left: 8px;
    padding-right: 8px;
    border-radius: 4px;
    outline: none;
    border:1px solid rgba(0,0,0,0.25);
}

._css-btn-leftinput2{
    margin-left:65px;
}

._css-btn-leftarea{
    height:100%;
    width:calc(100% - 50px);
    display: flex;
    align-items:center;
}

.Model_Info .btns button{width:auto;height: 32px;border:none;outline: none;border-radius: 3px;cursor: pointer;font-size:12px; letter-spacing:1px;font-size:12px;padding:0px 12px 0px 12px;}
.Model_Info .btns button:hover{opacity:0.8;}
.Model_Info .btns .blue{
    background-color:#1890FF;color:#fff; 
    position: absolute;
    right:0;
    }
.Model_Info .btns .white{background-color:transparent;color:#666;}
.Model_Info .btns .delete{background-color:transparent;color:rgba(245,34,45,1);float:left;}
::-webkit-scrollbar{width:20px; height:8px;}
::-webkit-scrollbar-thumb{border-radius: 12px;border:6px solid transparent;box-shadow: 8px 0px 0px #A5ADB7 inset;}
::-webkit-scrollbar-thumb:hover {box-shadow: 8px 0px 0px #4A4A4A inset;} 

.Model_Info .con .p1{
    width:259px;height:100%;border-right:1px solid rgba(0,0,0,0.09);overflow: hidden;line-height:0px;float:left;
}
.Model_Info .con .p1 ul{
    width:calc(100% + 11px);height:calc(100% - 40px);overflow-x: hidden;overflow-y:scroll;
}
.Model_Info .con .p1 ul li{
    width:calc(100% - 24px - 24px + 11px);height:50px;line-height:50px;text-align: left;padding-left:24px;position:relative;
}
.Model_Info .con .p1 ul li i{
    position: absolute;top:15px;display: block;font-style: normal;
}
.Model_Info .con .p1 ul li i.current{
    width:57px;height:20px;background-color: #1890FF;border-radius:3px;right:40px;
}
.Model_Info .con .p1 ul li i.current::before{
    content: '当前版本';float:left;width:100%;height:100%;line-height: 20px;font-size:12px;color:#fff;text-align: center;
}
.Model_Info .con .p1 ul li i.Info{
    width:20px;height:20px;right:5px;opacity:0.7;cursor: pointer;
}
.Model_Info .con .p1 ul li i.Info:hover{
    color:#1890ff;
}
.Model_Info .con .p1 ul li:hover{
    background-color:#f5f5f5;
}
.Model_Info .con .p1 ul li.sel{
    background-color:#f5f5f5;
}
.openCurrent{
    width:calc(100% - 20px);height:38px;border-radius:20px;background:#f5f5f5;box-shadow:0px 1px 3px 0px rgba(0,21,41,0.22);border:none;outline:none;cursor: pointer;
}
.openCurrent:hover{
    background-color:#1890ff;color:#fff;
}
.Model_Info .con .p2{
    width:calc(100% - 260px - 135px - 8px);height:100%;float:left;
}
.Model_Info .con .p2 ul{
    width:calc(100% - 40px);height:100%;margin-left:24px;
}
.Model_Info .con .p2 ul li{
    width:132px;height:99px;border:1px solid rgba(0,0,0,0.09);float:left;margin: 0px 8px 8px 0px;border-radius:4px;box-shadow: 0px 0px 1px #e0e0e0;
}
.Model_Info .con .p2 ul li span{
    display:block;float:left;
}
.Model_Info .con .p2 ul li .num{
    width:108px;height:38px;margin: 16px 0px 0px 12px;line-height:38px;font-size:30px;color:#1DA48C;
}
.Model_Info .con .p2 ul li .name{
    width:108px;height:20px;margin: 11px 0px 0px 12px;line-height:20px;font-size:12px;color:rgba(0,0,0,0.42);
}

.Model_Info .con .p3{
    width:132px;height:100%;float:left;
}
.Model_Info .con .p3 ul{
    width:100%;height:100%;
}
.Model_Info .con .p3 ul li{
    width:132px;height:99px;border:1px solid rgba(0,0,0,0.09);float:left;margin: 0px 8px 8px 0px;border-radius:4px;box-shadow: 0px 0px 1px #e0e0e0;
}
.Model_Info .con .p3 ul li span{
    display:block;float:left;
}
.Model_Info .con .p3 ul li .num{
    width:108px;height:38px;margin: 16px 0px 0px 12px;line-height:38px;font-size:30px;color:#FAAD14;
}
.Model_Info .con .p3 ul li .name{
    width:108px;height:20px;margin: 11px 0px 0px 12px;line-height:20px;font-size:12px;color:rgba(0,0,0,0.42);
}

.Detile{
    position: absolute;left:260px;top:0px;width:auto;height:40px;padding:0px 20px 0px 20px;background-color:rgba(0,0,0,0.65);color:#fff;border-radius: 2px;line-height:38px;
}
</style>
