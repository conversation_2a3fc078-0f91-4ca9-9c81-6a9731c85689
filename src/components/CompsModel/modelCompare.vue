<template>
  <div class="Compare" ref="Compare">
     <div class="title">
      <span>{{ modelTitle }}</span>
      <div class="tools" @click="handleClose">
        <i class="icon-suggested-close close"></i>
      </div>
    </div>
    <div class="model-container">
      <div class="content">
        <iframe id="model-iframe" class="content-frame" src="" frameborder="0" ></iframe>
        <iframe id="model-iframe1" class="content-frame" src="" frameborder="0" ></iframe>
      </div>
    </div>
    <div class="compare-type">
      <div class="show-item" @click="getExpandShow">
        <span>对比项</span>
        <img :src="require('../../assets/images/compare-down.png')" class="show-img" v-if="isExpandShow">
        <img :src="require('../../assets/images/compare-up.png')" class="show-img" v-else>
      </div>
      <div class='expand-items' v-if="isExpandShow">
        <div class="compare-item" @click="getDiffCompare('add')">
          <div class='left-part'>
            <img :src="require('../../assets/images/compare-add.png')">
            <span>新增</span>
          </div>
          <div class='right-part'>
            <img :src="require('../../assets/images/compare-selected.png')" class="num-img" v-if="selectedCompareType.includes('add')">
            <img :src="require('../../assets/images/compare-select.png')" class="num-img" v-else>
          </div>
        </div>
        <div class="compare-item" @click="getDiffCompare('modi')">
          <div class='left-part'>
            <img :src="require('../../assets/images/compare-modi.png')">
            <span>修改</span>
          </div>
          <div class='right-part'>
            <img :src="require('../../assets/images/compare-selected.png')" class="num-img" v-if="selectedCompareType.includes('modi')">
            <img :src="require('../../assets/images/compare-select.png')" class="num-img" v-else>
          </div>
        </div>
        <div class="compare-item" @click="getDiffCompare('del')">
          <div class='left-part'>
            <img :src="require('../../assets/images/compare-del.png')">
            <span>删除</span>
          </div>
          <div class='right-part'>
            <img :src="require('../../assets/images/compare-selected.png')" class="num-img" v-if="selectedCompareType.includes('del')">
            <img :src="require('../../assets/images/compare-select.png')" class="num-img" v-else>
          </div>
        </div>
      </div>
      <div class="line"></div>
      <div class="version">
        <img src="../../assets/images/model-version.png" alt="">
        <span>版本</span>
        <span class="num">{{'V' + ver1}}</span>
      </div>

    </div>
    <div class="compare-left">
      <div class="version">
        <img src="../../assets/images/model-version.png" alt="">
        <span>版本</span>
        <span class="num">{{'V' + ver2}}</span>
      </div>
    </div>
    <div class="num-node">
      <img :src="require('../../assets/images/compare-model.png')" class="num-img">
      <span class="text">已选中{{totalNum}}个差异构件</span>
    </div>
    <div class="lock-node" v-if="showLock">
      <img :src="require('../../assets/images/icon-pano-lock-open.png')" class="lock-img" v-if="!isLock" @click="lockCamera('lock')">
      <img :src="require('../../assets/images/icon-pano-lock-close.png')" class="lock-img" v-else @click="lockCamera('unlock')">
    </div>

  </div>
</template>

<script>
export default {
  name: 'Compare',
  props: {
    modelDetail: {
      type: Object
    },
    compareArr: {
      type: Array
    }
  },
  components: {
  },
  computed: {
    projectId () {
      return this.$staticmethod._Get("organizeId");
    }
  },
  data () {
    return {
      modelId: '',
      version: '',
      modelTitle: '',
      readyInd: 0,
      totalNum: 0, // 差异构件数量
      selectedCompareType: ['add'], // 对比类型
      isExpandShow: false, // 展开
      diffArr: [], // 全部的差异构件
      addDiffer: [], // 新增差异构件
      modiDiffer: [], // 修改差异构件
      delDiffer: [], // 删除差异构件
      formerDiffArr: [],
      formerType: 0, // 前一次点击差异类型
      isLock: false,
      showLock: true, // 模型加载完后出现
      ver1: 0, // 右边新版本
      ver2: 0 // 左边旧版本
    }
  },
  methods: {
    getDiffCompare (type) {
      if (this.selectedCompareType.includes(type)) {
        this.selectedCompareType.splice(this.selectedCompareType.indexOf(type), 1)
        // console.log(type, this.selectedCompareType)
        // 取消已勾选的类型，并重置着色
        this.resetModelColor(type)
      } else {
        this.selectedCompareType.push(type)
        const typenum = this.getTypeNum(type)
        this.getCompareVersion(typenum, 'switch')
      }
    },
    getTypeNum (type) {
      let num = -1
      switch (type) {
        // case 'all':
        //   num = 0
        //   break
        case 'add':
          num = 0
          break
        case 'modi':
          num = 1
          break
        case 'del':
          num = 2
          break
      }
      return num
    },
    getExpandShow () {
      this.isExpandShow = !this.isExpandShow
    },
    lockCamera (type) {
      this.isLock = !this.isLock
      if (type === 'unlock') {
        this.getComeraUnlock()
      }
    },
    getComeraUnlock () {
      window.scene.mv.controller.removeEventListener('update', this.leftCameraMainly)
      window.scene1.mv.controller.removeEventListener('update', this.rightCameraMainly)
    },
    // 以左侧场景为主时
    leftCameraMainly () {
      const info = this.getCameraInfo(window.scene)
      this.setCameraInfo(window.scene1, info.position, info.target, info.focaloffset)
    },

    // 以右侧场景为主时
    rightCameraMainly () {
      const info = this.getCameraInfo(window.scene1)
      // console.log(info)
      this.setCameraInfo(window.scene, info.position, info.target, info.focaloffset)
    },

    // 获取相机位置
    getCameraInfo (obj) {
      var position = obj.mv.controller.getPosition()
      var target = obj.mv.controller.getTarget()
      var focaloffset = obj.mv.controller.getFocalOffset()
      return {
        position, target, focaloffset
      }
    },

    // 设置相机位置
    setCameraInfo (obj, position, target, focaloffset) {
      obj.mv.controller.setFocalOffset(focaloffset.x, focaloffset.y, focaloffset.z, false)
      obj.mv.controller.setLookAt(position.x, position.y, position.z, target.x, target.y, target.z, false)
    },
    async loadmodel2 () {
      // console.log('模型浏览版本', this.compareArr)
      this.ver1 = 0
      this.ver2 = 0 // ver1 新版本 ver2 旧版本
      if (this.compareArr[0].value > this.compareArr[1].value) {
        this.ver1 = this.compareArr[0].value
        this.ver2 = this.compareArr[1].value
      } else {
        this.ver1 = this.compareArr[1].value
        this.ver2 = this.compareArr[0].value
      }
      const data = { isVothingScenemanager: true }
      const contentWindow = document.getElementById('model-iframe').contentWindow
      contentWindow.savePreviewSceneData(data)
      contentWindow.toggleSceneManageEditMode(false)
      contentWindow.toogleModelOnlyPreview(false)
      const contentWindow1 = document.getElementById('model-iframe1').contentWindow
      contentWindow1.savePreviewSceneData(data)
      contentWindow1.toggleSceneManageEditMode(false)
      contentWindow1.toogleModelOnlyPreview(false)

      window.scene = contentWindow.scene
      window.scene1 = contentWindow1.scene

      // 添加中午天空盒
      const sk1 = window.scene.addFeature('skybox')
      sk1.name = '中午'
      sk1.load()
      sk1.setTime('noon')
      // 场景编辑器数据同步 天空盒
      contentWindow.deepUpdateScene('skybox')

      // 添加中午天空盒
      const sk2 = window.scene1.addFeature('skybox')
      sk2.name = '中午'
      sk2.load()
      sk2.setTime('noon')
      // 场景编辑器数据同步 天空盒
      contentWindow1.deepUpdateScene('skybox')
      // console.log('0000000000000000000000', this.diffArr)

      // 左侧模型 最新版本 --------------------------------------------------------
      // addfeature第二个参数 模型唯一性的元素id
      window.model = window.scene.addFeature('model', this.modelId)
      // 指定模型服务地址
      window.model.server = window.bim_config.newModelHttpUrl
      // 指定vaultID
      window.model.vaultID = this.projectId
      // 模型版本
      const version = this.ver2
      if (typeof version === 'string') {
        window.model.version = parseInt(version)
      } else {
        window.model.version = version
      }
      // 基点的经纬度坐标
      window.model.origin = [0, 0]
      // 基点的高程
      window.model.altitude = 0
      // 基点的正北旋转角度
      window.model.rotation = 0
      // 相对于基点的XYZ偏移
      window.model.offset = [0, 0, 0]
      // 加载模型
      window.model.load().then(() => {
        this.showLock = true
        // 加载模型主视图
        window.model.activeView().then(() => {
          // 加载成功后，定位到当前模型
          window.scene.fit2Feature(window.model)
          // 场景编辑器数据同步 模型结构树
          contentWindow.deepUpdateScene('model')
          const args2 = {
            objectIDs: this.delDiffer,
            color: '#FF0000',
            opacity: 0.6
          }
          window.scene.execute('color', args2)
          window.scene.render()
        })
        // 场景编辑器数据同步 模型结构树
        contentWindow.deepUpdateScene('model')
      })

      // 右侧模型   --------------------------------------------------------
      // addfeature第二个参数 模型唯一性的元素id
      window.model1 = window.scene1.addFeature('model', this.modelId)
      // 指定模型服务地址
      window.model1.server = window.bim_config.newModelHttpUrl
      // 指定vaultID
      window.model1.vaultID = this.projectId
      // 模型版本
      const version1 = this.ver1
      if (typeof version1 === 'string') {
        window.model1.version = parseInt(version1)
      } else {
        window.model1.version = version1
      }
      // 基点的经纬度坐标
      window.model1.origin = [0, 0]
      // 基点的高程
      window.model1.altitude = 0
      // 基点的正北旋转角度
      window.model1.rotation = 0
      // 相对于基点的XYZ偏移
      window.model1.offset = [0, 0, 0]
      // 加载模型
      window.model1.load().then(() => {
        // 加载模型主视图
        window.model1.activeView().then(() => {
          this.showLock = true
          // 加载成功后，定位到当前模型
          window.scene1.fit2Feature(window.model1)
          // 场景编辑器数据同步 模型结构树
          contentWindow1.deepUpdateScene('model')
          const args = {
            objectIDs: this.addDiffer,
            color: '#64C800',
            opacity: 0.6
          }
          window.scene1.execute('color', args)
          const args1 = {
            objectIDs: this.modiDiffer,
            color: '#FFEB00',
            opacity: 0.6
          }
          window.scene1.execute('color', args1)
          window.scene1.render()
        })
        // 场景编辑器数据同步 模型结构树
        contentWindow1.deepUpdateScene('model')
      })
    },
    // 取消同类型构件选中
    resetModelColor (type) {
      if (type === 'add') {
        const aa = []
        if (this.addDiffer.length > 0) {
          this.addDiffer.forEach(item => {
            aa.push(window.scene1.findObject(item))
          })
          const arr = window.scene1.createArrayObject(aa)
          arr.resetColor()
          window.scene1.render()
        }

        this.addDiffer = []
      } else if (type === 'modi') {
        const aa = []
        if (this.modiDiffer.length > 0) {
          this.modiDiffer.forEach(item => {
            aa.push(window.scene1.findObject(item))
          })
          const arr = window.scene1.createArrayObject(aa)
          arr.resetColor()
          window.scene1.render()
        }

        this.modiDiffer = []
      } else {
        const aa = []
        if (this.delDiffer.length > 0) {
          this.delDiffer.forEach(item => {
            aa.push(window.scene.findObject(item))
          })
          const arr = window.scene.createArrayObject(aa)
          arr.resetColor()
          window.scene.render()
        }
        this.delDiffer = []
      }
    },
    resetModel (type) {
      if (type === 0) {
        const args = {
          objectIDs: this.addDiffer,
          color: '#64C800',
          opacity: 0.6
        }
        window.scene1.execute('color', args)
      } else if (type === 1) {
        const args1 = {
          objectIDs: this.modiDiffer,
          color: '#FFEB00',
          opacity: 0.6
        }
        window.scene1.execute('color', args1)
      } else {
        const args2 = {
          objectIDs: this.delDiffer,
          color: '#FF0000',
          opacity: 0.6
        }
        window.scene.execute('color', args2)
      }
    },
    async getCompareVersion (type, txt) {
      let url = `${this.$ip('newModelHttpUrl')}/Model/CompareVersion?VaultID=${this.projectId}&ModelID=${this.modelId}&VersionNO=${this.compareArr[0].value}&DestVersionNO=${this.compareArr[1].value}&CompareType=${type}`;
      this.$axios
        .get(url)
        .then(res=>{
          if (res.data.length > 0) {
            if (type === 0) {
              this.addDiffer = res.data.map(item => this.modelId + '^' + item)
            } else if (type === 1) {
              this.modiDiffer = res.data.map(item => this.modelId + '^' + item)
            } else {
              this.delDiffer = res.data.map(item => this.modelId + '^' + item)
            }


          } else {
            if (type === 0) {
              this.addDiffer = []
            } else if (type === 1) {
              this.modiDiffer = []
            } else {
              this.delDiffer = []
            }
          }
          const arr = this.addDiffer.concat(this.modiDiffer, this.delDiffer)
            if (arr.length > 0) {
              this.diffArr = Array.from(new Set(arr))
            } else {
              this.diffArr = []
            }
            this.totalNum = this.diffArr.length
            // console.log(this.diffArr)
            if (txt === 'first') {
              this.formerType = type
              this.formerDiffArr = this.diffArr
              this.loadmodel2()
            } else {
              this.resetModel(type)
            }
        })

    },
    receiveMessageFromIndex () {
      if (event.data === 'featureLoad') {
        // console.log(1)
        this.readyInd++
        if (this.readyInd === 2) {
          // console.log(2)
          this.getCompareVersion(0, 'first')
        }
      }
    },
    handleClose () {
      this.$emit('close')
    }
  },
  created () {
  },
  mounted () {
    // console.log(this.modelDetail)
    this.readyInd = 0
    // 监听message事件
    window.addEventListener('message', this.receiveMessageFromIndex, false)
    this.modelId = this.modelDetail.featureID
    this.modelTitle = this.modelDetail.featureName
    this.version = this.modelDetail.currentVersion

    // 加载引擎
    let ifrSrc = ''
    ifrSrc = `${window.bim_config.newModelApi}?projectId=${this.VaultID}`
    // ifrSrc = `http://localhost:8081/#/?token=${token}&projectId=${this.projectId}&isPreview=1`
    const iframe = document.getElementById('model-iframe')
    const iframe1 = document.getElementById('model-iframe1')
    iframe.src = ifrSrc
    iframe1.src = ifrSrc

    // 监听相机视角
    const _this = this
    document.querySelector('#model-iframe').onmouseenter = function () {
      // console.log('model-iframe,移入', _this.isLock)
      if (_this.isLock) {
        window.scene.mv.controller.addEventListener('update', _this.leftCameraMainly)
      }
    }

    document.querySelector('#model-iframe').onmouseleave = function () {
      // console.log('model-iframe,移出')
      if (_this.isLock) {
        window.scene.mv.controller.removeEventListener('update', _this.leftCameraMainly)
      }
    }

    document.querySelector('#model-iframe1').onmouseenter = function () {
      // console.log('model-iframe1,移入')
      if (_this.isLock) {
        window.scene1.mv.controller.addEventListener('update', _this.rightCameraMainly)
      }
    }

    document.querySelector('#model-iframe1').onmouseleave = function () {
      // console.log('model-iframe1,移出')
      if (_this.isLock) {
        window.scene1.mv.controller.removeEventListener('update', _this.rightCameraMainly)
      }
    }
  },
  beforeDestroy () {
    window.removeEventListener('message', this.receiveMessageFromIndex, false)
  }

}
</script>
<style lang="scss" scoped>
.Compare{
  .title {
     z-index: 100;
     position: absolute;
     top: 0;
     left: 0;
     height: 48px;
     width: calc(100% - 40px);
     background-color: #061326;
     display: flex;
     align-items: center;
     font-size: 16px;
     font-weight: 500;
     color: #FFFFFF;
     padding: 0 20px;

     .tools {
       display: flex;
       float: right;
       height: 48px;
       margin-left: auto;
       align-items: center;
       justify-content: center;
       i{
         vertical-align: middle;
         line-height: 30px;
       }
       .tools-left-container{
         height: 100%;
         display: flex;
         margin-right: 20px;
         .tools-item{
           display: flex;
           width: 100%;
           height: 100%;
           justify-content: center;
           align-items: center;
           &:hover{
             background-color: rgba(255, 255, 255, 0.15);
           }
           .create {
             margin-left: 10px;
             margin-right: 10px;
             width: 76px;
             height: 20px;
             cursor: pointer;
           }
         }

       }
       .close {
         cursor: pointer;
         width: 26px;
         height: 26px;
       }

     }
   }

   #model-iframe, #model-iframe1 {
     position: absolute;
     top: 46px;
     bottom: 0;
     width: 50%;
     height: calc(100% - 46px);
   }
   #model-iframe {
     left: 0;
   }
   #model-iframe1 {
     left: 50%;
   }

   .model-container {
     width: 100%;
     height: 100%;

     .content {
       width: 100%;
       height: 100%;

       .content-frame {
         width: 100%;
         height: 100%;
       }
     }

     .model-select-img {
       margin-top: 60px;
       margin-left: 50px;
       z-index: 100;
       float: left;
       position: absolute;
       width: 88px;
       height: 44px;
     }
   }
   .num-node {
      position: fixed;
      right: 16px;
      bottom: 40px;
      width: 180px;
      padding: 12px 0;
      text-align: center;
      background: rgba(19,43,77,0.8);
      box-shadow: inset 0px -1px 0px 0px #475059;
      border-radius: 4px;
      opacity: 0.8;
      display: flex;
      align-items: center;

      .num-img{
        width: 13px;
        height: 15px;
        margin-left: 10px;
        margin-right: 8px;
      }
      .text{
        font-size: 12px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #FFFFFF;
      }
   }
  .compare-left{
    position: fixed;
    left: calc(50% - 180px - 16px);
    bottom: 40px;
    background: rgba(255,255,255,0.9);
    border-radius: 4px;
    opacity: 0.8;
    width: 180px;
    .version{
      padding: 12px 16px;
      display: flex;
      align-items: center;
      img{
        width: 16px;
        height: 16px;
        margin-right: 6px;
      }
      span{
        font-size: 12px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #132B4D;
      }
      .num{
        margin-left: auto;
      }
    }
  }

  .compare-type{
      position: fixed;
      right: 16px;
      bottom: 88px;
      background: rgba(255,255,255,0.9);
      border-radius: 4px;
      opacity: 0.8;
      width: 180px;
     .version{
       padding: 12px 16px;
       display: flex;
       align-items: center;
       img{
         width: 16px;
         height: 16px;
         margin-right: 6px;
       }
       span{
          font-size: 12px;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: #132B4D;
       }
       .num{
         margin-left: auto;
       }
     }
     .line{
       margin: 0 16px;
        height: 1px;
        background: #E5E5E5;
     }
     .show-item{
        padding: 12px 16px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        cursor: pointer;
        span{
          font-size: 12px;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: #132B4D;
        }
        .show-img{
          width: 16px;
          height: 16px;
        }
      }
      .expand-items{
        padding: 6px 16px;
        .compare-item{
          display: flex;
          align-items: center;
          justify-content: space-between;
          margin: 6px 0;
          cursor: pointer;
          .left-part{
            img{
              width: 16px;
              height: 16px;
              margin-right: 6px;
              vertical-align: middle;
            }
            span {
              font-size: 12px;
              font-family: PingFangSC-Regular, PingFang SC;
              font-weight: 400;
              color: #132B4D;
            }
          }
          .right-part{
            img{
              width: 16px;
              height: 16px;
            }
          }
        }
      }
   }
   .lock-node{
      position: fixed;
      left: 50%;
      top: 25%;
      transform: translate(-50%, -50%);
      .lock-img{
        width: 40px;
        height: 40px;
        cursor: pointer;
      }
   }
   .version-bk {
      // width: 94px;
      padding: 0 8px;
      height: 32px;
      background: rgba(255,255,255,0.8);
      border-radius: 4px;
      opacity: 0.8;
      position: fixed;
      top: 8%;
      text-align: center;
      line-height: 32px;
      span {
        font-size: 12px;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 500;
        color: #132B4D;
      }
   }
}

</style>
