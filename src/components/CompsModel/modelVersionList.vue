<template>
  <div class="pano-content">
    <div class="header-title">
      <div class="title">模型版本</div>
      <div class="header-btn">
        <div  class="header-cancle" @click="cancel">
          <i class="add-icon cancelcontrast-icon"></i>
          <span class="add-title">取消</span>
        </div>
        <div  class="header-contrast">
          <div class="func" @click="getPanoDetails">
            <i class="add-icon startcontrast-icon"></i>
            <span class="add-title">确认</span>
          </div>
        </div>
      </div>
    </div>
    <div class="content-list">
      <ul>
        <li
         class="list-li"
         v-for="(item, index) in list"
         :key="index"
         @click="getItem(item)">
          <div :class="item.isSelect? 'list-img active' :'list-img'">
            <img :src="item.img" class="img" alt="" :title="item.label">
          </div>
          <span :class="item.isSelect? 'item-name active':'item-name'" :title="item.label">{{ item.label }}</span>
        </li>
      </ul>
    </div>
  </div>
</template>
<script>
export default {
  name: 'VersionList',
  data () {
    return {
      contrastArr: [],
      isCompare: false
    }
  },
  props: {
    list: {
      type: Array,
      default: () => {
        return []
      }
    }
  },
  filters: {
    filtTime (inputtime) {
      if (!inputtime || inputtime.trim().length === 0) {
        return inputtime
      }
      if (inputtime.length >= '2019-09-16T11:14'.length) {
        return inputtime.substr(0, 10)
      }
    }
  },
  mounted () {
    // console.log(this.list,'===')
  },
  methods: {
    getItem (item) {
      this.$set(item, 'isSelect', !item.isSelect)
      // console.log(item)
    },
     
    cancel () {
      this.selectedItem = {}
      this.$emit('close')
    },
    async getPanoDetails () {
      const newArr = []
      this.list.map(item => {
        if (item.isSelect) {
          newArr.push(item)
        }
      })
      if (newArr.length < 2 || newArr.length > 2) {
        this.$message.warning('请选择两个版本进行对比！')
        return false
      }
      // console.log(newArr)
      this.$emit('getVersionCompare', newArr)
    }
  }
}
</script>
<style lang="scss" scoped>
/deep/.el-checkbox {
  .el-checkbox__input {
    &.is-focus, &:hover {
      .el-checkbox__inner {
        border-color: #1890ff;
      }
    }
    &.is-indeterminate, &.is-checked {
      .el-checkbox__inner {
        background-color: #1890ff;
        border-color: #1890ff;
      }
    }
  }
}
.pano-content {
  border: 1px solid #e8e8e8 !important;
  position: fixed;
  top: 35%;
  right: calc(10% - 100px);
  margin: 0 auto;
  width: 80%;
  padding: 12px 16px;
  background: hsla(0,0%,100%,.9);
  border-radius: 8px;
  z-index: 1001;
}
.header-title{
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 22px;
  .title {
   font-size: 16px;
   font-weight: 600;
   color: #222222;
  }
  .header-btn {
    display: flex;
    .header-cancle{
      cursor: pointer;
      display: flex;
      align-items: center;
      margin-right: 16px;
    }
    .header-contrast {
      cursor: pointer;
      display: flex;
      align-items: center;
    }
    .add-icon {
      display: block;
      width: 16px;
      height: 16px;
      background-repeat: no-repeat;
      background-size: cover;
      background-position: center center;
    }
    // .startcontrast-icon {
    //   background-image: url(../../../assets/images/model-confirm.png);
    // }
    // .cancelcontrast-icon {
    //   background-image: url(../../../assets/images/model-cancel.png);
    // }
    .add-title {
      color: #333;
      font-size: 13px;
      font-weight: 400;
      margin-left: 4px;
    }
    .func {
      display: flex;
      align-items: center;
    }
  }
}

.notallowed {
  cursor: not-allowed;
}

.content-list {
  margin-top: 12px;
  width: 100%;
  height: 160px;
  overflow-x: auto;
}
.content-list ul {
  display: flex;
  height: 100%;
}
.list-li {
  text-align: center;
  cursor: pointer;
  margin-right: 16px;
   .list-img {
     position: relative;
     margin-bottom: 12px;
     &:hover{

       .check-img{
         border-color: #1890ff;
       }
       .img{
         border-color: #1890ff;
       }
     }
     .check-img{
       width: 16px;
       height: 16px;
       position: absolute;
       top: 8px;
       left: 8px;
       border: 1px solid #666;
       border-radius: 50%;
       background: #fff;
     }
     .img {
       display: inline-block;
       width: 158px;
       height: 108px;
       border-radius: 4px;
       border: 1px solid #FFFFFF;
     }
   }

   .item-name {
     margin-top: 16px;
     color: #666;
     font-size: 14px;
     font-weight: 500;
     width: 158px;
     overflow: hidden;
     text-overflow: ellipsis;
     white-space: nowrap;
   }
   .active{
    color: #1890ff;
    &:hover{
       .check-img{
         border-color: transparent;
       }
       .img{
         border-color: #1890ff;
       }
     }
    .img{
       border-color: #1890ff;
     }
     .check-img{
       border-color: transparent;
       background: transparent;
     }
   }
}

.contrast-scene {
  position: absolute;
  left: 12px;
  top: 12px;
  width: 24px;
  height: 24px;
}
.del-scene {
  position: absolute;
  right: -6px;
  top: -6px;
  width: 16px;
  height: 16px;
  display: none;
}
.time-line {
  position: relative;
}
.time-line span {
  display: block;
}
.line {
  position: absolute;
  top: 10px;
  left: 0;
  width: 100%;
  height: 2px;
  background: #666666;
}
.line-one {
  left: 50%;
  width: 50%;
}
.line-last {
  left: 0;
  width: 50%;
}
.pano-content div::-webkit-scrollbar {
  width: 5px;
  height: 5px;
}
.pano-content div::-webkit-scrollbar-track {
  background: transparent;
  border-radius: 2px;
}
.pano-content div::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.19);
  border-radius: 10px;
}
.pano-content div::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.09);
}
.pano-content div::-webkit-scrollbar-corner {
  background: rgba(0, 0, 0, 0.39);
}

</style>
