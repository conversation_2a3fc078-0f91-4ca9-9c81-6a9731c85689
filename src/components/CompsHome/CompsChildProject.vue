<template>

    <!-- 创建子项目 -->
    <div 
    class="_css-subpj-all"
    data-debug="debug-createsubproject" >
    
        <!-- 表单对话框 -->
        <zdialog-function
            init_id="zid_createsubproject"
            :init_title="'创建子项目'"
            :init_zindex="2"
            :init_innerWidth="348"
            init_closebtniconfontclass="icon-suggested-close"
            @onclose="evt_onclose"
        >

        <!-- 正文区域 -->
        <div slot="mainslot" @mousedown="_stopPropagation($event)" class="_css-copydialogcenter">
              <div class="_css-copyprojseltitle">选择父级项目</div>
              <zselect-function
              ref="ref_copytemplateid"
                :init_items="m_parents"
                :init_width="'300px'"
                :init_height="40"
                :init_listzindex="undefined"
                :init_iconclass="'icon-arrow-down_outline'"
                :debugmode="false"
                placeholder="请选择父级项目"
                @itemclick="evt_parentselected"
              ></zselect-function>
        </div>
        <!-- //正文区域 -->
        
        <!-- 按钮区域 -->
        <div slot="buttonslot" class="_css-zdialogbtnctn  " >

            <!-- 确定按钮 -->
              <zbutton-function
                :init_text="'确定'"
                :init_fontsize="14"
                :init_height="undefined"
                :init_width="'100%'"
                :init_bgcolor="'#1890FF'"
                :init_color="'#FFF'"
                @onclick="func_ok"
                >
            </zbutton-function>
            <!-- //确定按钮 -->

        </div>
        <!-- //按钮区域 -->

        </zdialog-function>
        <!-- //表单对话框 -->

    </div>

</template>
<script scoped>
import HomeVue from '../Home.vue';
export default {
    data(){
        return {

            // 选中的项目对象
            // -------------
            m_selectedOrg: undefined,

            // 对话框标题
            // ---------
            m_dialogtitle: '',

            // 父级项目数据列表
            // --------------
            m_parents: []
        };
    },
    props: ['init_dialogtitle', 'init_parents'],
    created(){
        var _this = this;
        _this.m_parents = _this.$staticmethod.DeepCopy(_this.init_parents);
    },
    mounted(){
      
    },
    methods:{

        // 选中了某一个项目作为父项目时
        // -------------------------
        evt_parentselected(item) {
            
            // 将选中的值赋予 m_selectedOrganizeId
            // ----------------------------------
            var _this = this;
            _this.m_selectedOrg = item;

        },

        do_funcok() {

            var _this = this;
            // 点击后，应隐藏这个选择父项目的对话框，同时将值带到 GIS 那个创建项目的页面
            // --------------------------------------------------------------------
            bootvue.btncreateissue_click(_this.m_selectedOrg);

            // 关闭自己
            // --------
            _this.evt_onclose();
        },

        func_ok(){

            var _this = this;

            // 如果没有选择任何父项目，弹一个提示
            // -------------------------------
            if (_this.m_selectedOrg == undefined) {
                _this.$confirm('未选择父项目，是否直接创建项目？', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '返回'
                }).then(x => {
                    _this.do_funcok();
                })
                return;
            } else {
                _this.do_funcok();
            }
             
        },

        _stopPropagation(ev) {
            ev && ev.stopPropagation && ev.stopPropagation();
        },

        evt_onclose(){
            var _this = this;
            _this.$emit("onclose")
        }
    }
}
</script>
<style scoped>

._css-zdialogbtnctn {
    padding: 12px 24px;
    border-top: 1px solid rgba(0,0,0,0.09);
}

._css-copyprojseltitle{
  height:22px;
  color:rgba(0, 0, 0, 0.85);
  font-size: 14px;
  padding:0 24px 0 24px;
  box-sizing: border-box;
  margin-top:11px;
  text-align: left;
  width:100%;
}

._css-copydialogcenter{
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-top:5px;
  margin-bottom: 50px;
}

._css-subpj-all {
    position:fixed;
    z-index: 2;
    width:100%;
    height:100%;
    background-color: rgba(0, 0, 0, 0.15);
}
</style>