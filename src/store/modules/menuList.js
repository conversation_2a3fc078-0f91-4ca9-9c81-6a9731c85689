export default {
    state: {
        menuList: {}, // 设置模型获取的值
        menuListArr: [], // 左侧菜单数据
        // leftMenuActivated: null, // 左侧处于激活状态的menu
    },
    getters: {
        // 参数列表state里的menuList数据
        getMenuListChange(state) {
            return state.menuList;
        },
    },
    mutations: {
        // state指的是state的数据
        setMenuListChange(state, data) {
            state.menuList = data;
        },
        // setLeftMenuActivated(state, data) {
        //     state.leftMenuActivated = data;
        // },
        setMenuListArr(state, data) {
          state.menuListArr = data
        }
    },
    actions: {

    },
    modules: {}
}
