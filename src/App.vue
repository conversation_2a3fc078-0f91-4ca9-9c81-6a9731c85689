<template>
  <div id="app">
    <router-view/>
  </div>
</template>

<script>
export default {
  name: "App",
  mounted(){
    if(process.env.NODE_ENV === 'development'){
      window.bim_config.bimviewerurl = '../../../static/BIMComposer/index.html';
      window.bim_config.newModelApi = '../../../static/scenemanager/#/';
      //window.bim_config.bimviewerurlold = '../../../static/BIMComposer/index.html';
    }
  }
};
</script>

<style>



</style>
