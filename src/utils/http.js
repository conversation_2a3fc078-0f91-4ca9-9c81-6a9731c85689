import axios from 'axios'
import { getToken } from '@/utils/token'
import { Message } from 'element-ui'
import router from '@/router'

// 允许携带cookie
// axios.defaults.withCredentials = true

// create an axios instance
const http = axios.create()

const pending = []
const removePending = config => {
    for (const p in pending) {
        if (pending[p].u === config.url + '&' + config.method) {
            pending[p].fn()
            pending.splice(p, 1)
        }
    }
}

// request interceptor
http.interceptors.request.use(
    config => {
        if (config.isToken) {
            config.headers.Token = `${getToken('Token')}`
        }
        if (!config.isLoop) {
            removePending(config)
            config.cancelToken = new axios.CancelToken(fn => {
                pending.push({ u: config.url + '&' + config.method, fn })
            })
        }
        let url = config.url
        url += url.indexOf('?') >= 0 ? '&' : '?'
        url += 'r=' + (Math.random() * 100000 + 1)
        config.url = url
        return config
    },
    err => {
        return Promise.reject(err)
    }
)

// respone interceptor
http.interceptors.response.use(
    response => {
        if (!response.config.isLoop) {
            removePending(response.config)
        }
        return response
    },
    error => {
        return Promise.reject(error)
    }
)

/**
 * @param {String} url 路劲
 * @param {String} method 请求方式
 * @param {Object} params 参数
 * @param {String} baseURL 请求头
 * @param {Object} headers 请求头配置
 * @param {Number} timeout 请求时间
 * @param {Boolean} isToken 请求是否携带token
 * @param {Boolean} isLoop 接口是否轮询而不被取消
 * @param {Boolean} isBlob 接口是否为BLOB
 * @return {AxiosPromise}
 */
export const request = async({ url, method = 'get', params, baseURL = 'webserverurl', headers = {}, timeout, isToken = true, isLoop, isError = false, isBlob = false }) => {
    return new Promise(async(resolve, reject) => {
        try {
            const result = await http({
                method,
                url: isError ? url : `${window.bim_config[baseURL]}${url}`,
                params: (method === 'get' || method === 'delete') && params,
                data: ((method === 'post' || method === 'put') && params) || null,
                headers: {
                    ...headers
                },
                responseType: isBlob ? 'blob' : '',
                timeout: timeout || 30000,
                isToken: isToken,
                isLoop: isLoop || false
            })
            if (result.data.Ret === -2) { // -2为token过期、跳转到登录
                // debugger
                Message({
                    type: 'error',
                    message: result.data.Msg,
                    duration: 3 * 1000,
                })

                localStorage.removeItem("Token")
                router.push({ path: '/' })
            } else if (result.data.Ret === -1) { // -1为错误、跳转到登录
                Message({
                    type: 'error',
                    message: result.data.Msg,
                    duration: 5 * 1000,
                })
            } else if (result.data.Ret == -9999) { // -9999、项目过期、跳转项目列表也
                Message({
                    type: 'error',
                    message: result.data.Msg,
                    duration: 3 * 1000,
                })
                return
            } else {
                return resolve(result.data)
            }

            resolve(result.data)
        } catch (error) {
            reject(error)
        }
    })
}