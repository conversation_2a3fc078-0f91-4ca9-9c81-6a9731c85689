import Vue from 'vue'

//点击空白处关闭弹出层指令
//调用方法 v-clickOutClose="function"  function为当前弹窗的关闭方法
Vue.directive('clickOutClose', {
  // 初始化指令
  bind(el, binding, vnode) {
    function documentHandler(e) {
      // 这里判断点击的元素是否是本身，是本身，则返回
      // if (el.contains(e.target)) {
      //   return false;
      // }

      if (el == e.target || el.contains(e.target)) {
        return false;
      }

      // 判断指令中是否绑定了函数
      if (binding.expression) {
        // 如果绑定了函数 则调用那个函数，此处binding.value就是handleClose方法
        binding.value(e);
      }
    }
    // 给当前元素绑定个私有变量，方便在unbind中可以解除事件监听
    el.__vueClickOutside__ = documentHandler;
    document.addEventListener('click', documentHandler);
  },
  unbind(el, binding) {
    // 解除事件监听
    document.removeEventListener('click', el.__vueClickOutside__);
    delete el.__vueClickOutside__;
  },
});

Vue.directive('buttonClickRipple',{
  inserted(el,binding) {
    el.setAttribute('ripple','ripple');
    let cleanUp, debounce, i, len, ripple, rippleContainer, ripples, showRipple;

    debounce = function(func, delay) {
      let inDebounce;
      inDebounce = undefined;
      return function() {
        let args, context;
        context = this;
        args = arguments;
        clearTimeout(inDebounce);
        return inDebounce = setTimeout(function() {
          return func.apply(context, args);
        }, delay);
      };
    };

    showRipple = function(e) {
      let pos, ripple, rippler, size, style, x, y;
      ripple = this;
      rippler = document.createElement('span');
      size = ripple.offsetWidth;
      pos = ripple.getBoundingClientRect();
      x = e.pageX - pos.left - (size / 2);
      y = e.pageY - pos.top - (size / 2);
      style = 'top:' + y + 'px; left: ' + x + 'px; height: ' + size + 'px; width: ' + size + 'px;';
      ripple.rippleContainer.appendChild(rippler);
      return rippler.setAttribute('style', style);
    };

    cleanUp = function() {
      while (this.rippleContainer.firstChild) {
        this.rippleContainer.removeChild(this.rippleContainer.firstChild);
      }
    };

    ripples = document.querySelectorAll('[ripple]');

    for (i = 0, len = ripples.length; i < len; i++) {
      ripple = ripples[i];
      rippleContainer = document.createElement('div');
      rippleContainer.className = 'ripple--container';
      ripple.addEventListener('mousedown', showRipple);
      ripple.addEventListener('mouseup', debounce(cleanUp, 2000));
      ripple.rippleContainer = rippleContainer;
      ripple.appendChild(rippleContainer);
    }
  }
});

// 拖动相关
/**
 * <div class="_css-front" v-drag="draggreet"  :style="dragstyle" >
 *          val:'0',
            dragstyle: {
                position: 'fixed',
                right: 'calc(50% - 300px)',
                top: 'calc(50% - 115px)'
            },
            draggreet(val){
                var _this = this;
                _this.val = val;
            },
*/
//使用方法v-drag="draggreet"
//某些时候需要可拖动和不可拖动之间切换，使用下面的方法：
//定义一个变量 needDrag
//v-drag:[needDrag]="draggreet"  改变needDrag的值为true或false即可
Vue.directive('drag',function (el, binding) {
      let op = el;  //当前元素
      let self = this; //上下文
      op.onmousedown = function (e) {
        //鼠标按下，计算当前元素距离可视区的距离
        let disX = e.clientX - op.offsetLeft;
        let disY = e.clientY - op.offsetTop;
        document.onmousemove = function (e) {
          if (binding.arg != false) {
            //通过事件委托，计算移动的距离
            let l = e.clientX - disX;
            let t = e.clientY - disY;
            //移动当前元素
            op.style.left = l + 'px';
            op.style.top = t + 'px';
            //将此时的位置传出去
            binding.value({x:e.pageX,y:e.pageY})
          }
        };
        document.onmouseup = function (e) {
          document.onmousemove = null;
          document.onmouseup = null;
        };
      };
    }
);

export default {

}
