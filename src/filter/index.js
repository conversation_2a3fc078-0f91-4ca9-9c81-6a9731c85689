const vFilters = {
  //设置用户名称简写的图标
  //参数str: 用户名称（字符串）
  //使用方法 {{ message | setAbbreviation }}
  setAbbreviation(str) {
    let reg= /^[\u4E00-\u9FA5]+$/;
    let storage = str.split('');

    if (reg.test(str)){
      //如果是中文 则取最后一个字
      return storage[storage.length - 1];

    }else{
      //如果是英文或者中英结合，取第一个
      return storage[0];

    }
  },

  // 显示用户的账号
  // 参数userObj：包含 Email 及 Account 的对象
  showAccount(userObj){
    return userObj.Email?userObj.Email + '':(userObj.Account?userObj.Account+'':'-');
  }
};

export default vFilters;
