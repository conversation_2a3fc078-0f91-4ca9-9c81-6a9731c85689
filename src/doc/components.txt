1、图片裁剪：(CompsImageClippingUpload)
2、GIS选点：(CompsGIS)
3、下拉框：(CompsSelect2)
4、对话框底部按钮： (CompsDialogBtns)
5、对话框标题：(CompsDialogHeader)
6、新版输入框(@功能)  (compsAnimateInput)
7、输入框组件         （CompsSearchInput）
8、单字段颜色选择弹框组件 (CompsColorItem)

/***********************************************************/
import CompsColorItem from "@/components/CompsCommon/CompsColorItem";
 <CompsColorItem 
    :title="pEditingFormTitle"                                // 弹窗标题
    :fieldname="'标签名称'"                                    // 标签名称
    :fieldplaceholder="'请输入标签名称'"                       // 标签名称输入框的 placeholder
    v-if="bShowingEditingForm"                                
    @oncancel="coloritem_oncancel"></CompsColorItem>
/***********************************************************/




/*****************CompsImageClippingUpload*****************/

import CompsImageClippingUpload from "@/components/CompsCommon/CompsImageClippingUpload";

<CompsImageClippingUpload
  ref="cropImg"             //ref 可自定义
  @onCrop = "testCropImg"   //当裁剪时，获取裁剪后图片的信息 返回值有两个
  :circularPreview = true>  //预览区域形状 true为圆形  false为方形（可选）
  width="400"               //宽度 默认400（可选）
  height="300"              //高度 默认300（可选）
  :occupancyMap='imageUrl'  //初始化时加载一张图片 （可选）
</CompsImageClippingUpload>

e.g:
this.$refs.cropImg.resetImg();//重置
this.$refs.cropImg.cropImage();//裁剪 点击事件
testCropImg(base,binary){ //裁剪后的图片数据
  console.log(base,binary);
}

/*********************************************************/




/*****************CompsGIS*****************/

import CompsGIS from "@/components/CompsCommon/CompsGIS";

    <CompsGIS
    classname="_css-pjinfo-editposgis"    // 指定一个类名
    gisid="id_pjinfo_editposgis"          // 指定一个内部ID
    :width="'80%'"                        // 宽度
    :height="'80%'"                       // 高度
    :zIndex="2"                           // z-index
    @onselectpoint="_onselectpoint"       // 当选点时，会触发此方法。
    :hideclosebtn="true"                  // 隐藏关闭按钮
    innerGISSrc=""                        // 另指定内部gis页面的地址
    ></CompsGIS>

e.g:
    onselectpoint(pointinfo) {
      console.log(pointinfo);
    }

/*********************************************************/


/*****************CompsSelect2*****************/

import CompsSelect2 from "@/components/CompsCommon/CompsSelect2";

      <CompsSelect2
            :positionstyleobj="{'position':'absolute', 'right':'0'}"                             // select 控件位置相关样式
            height="40px"                                                                        // 最外层高度
            width="276px"                                                                        // 最外层宽度
            backgroundColor="rgba(0,0,0,0.02)"                                                   // 背景色
            :dataSource="[{id:'IssueStatue',text:'问题状态'}, {id:'IssueType', text:'问题分类'}]"  // 下拉项的数据源
            classname="_css-changedic-showtype"                                                  // 下拉框的最外层 class
            :showtext="extdata.dictype_select_text"                                              // 显示的文本（绑定在调用方变量上）
            @onselected="_dicshowtype_select"                                                    // 选择选项后的回调
          ></CompsSelect2>

e.g:
      _dicshowtype_select(obj){
          var _this = this;
          _this.extdata.dictype_select_id = obj.id;
          _this.extdata.dictype_select_text = obj.text;
      }

/*********************************************************/



/*****************CompsDialogBtns*****************/

import CompsDialogBtns from "@/components/CompsDialog/CompsDialogBtns";

  <CompsDialogBtns
      @onok="_onok"                 // 点击确定的处理
      @oncancel="_oncancel"         // 点击取消的处理
      :warningbtn="true"            // 左下方是否显示红色危险按钮
      @onwarning="_onwarning"       // 红色危险按钮的事件处理程序
      warningbtntext="删除成员">     // 红色危险按钮的按钮文本
  </CompsDialogBtns>

e.g:

/*********************************************************/





/*****************CompsDialogHeader***********************/

import CompsDialogHeader from "@/components/CompsDialog/CompsDialogHeader"

   <CompsDialogHeader
      title="请输入验证码"    // 标题内容
      :hideclosebtn="true"   // 是否隐藏关闭按钮
      :title_maxwidth="350"  // 标题区域的最大长度限制
      @oncancel="_oncancel"  // 关闭按钮事件处理程序
    ></CompsDialogHeader>

/*********************************************************/






/*****************compsAnimateInput*****************/

import compsAnimateInput from "@/components/CompsCommon/compsAnimateInput";

//基础用法（普通输入框）
<compsAnimateInput
  v-model="data"            //返回值(详见下方描述)
  placeholder="请输入姓名"   //input提示文字(选填,如果添加该属性，则没有动画效果)
  labelFootnote="（选填）"   //标签的补充说明 如：姓名（选填）
  :defaultValue="inputValue"//输入框默认显示的value
  height="100px"            //@功能 输入框的高度
  fontSize="16px"           //输入框字体大小
  label="姓名">             //输入框的标签名字
</compsAnimateInput>

//下拉选择框
<compsAnimateInput
  v-model="chentest"          //返回值(详见下方描述)
  select                      //带有该参数则表示是下拉框 （必填）
  :dataList="projectMember"   //下拉列表的数据（json）
  selectShow="RealName"       //下拉列表的数据的某个属性，指定某个属性，该属性值即为列表展示项
  displayNum="10"             //设置下拉列表第一页展示多少条数据
  label="姓名">               //下拉框的名字
</compsAnimateInput>


//带有@功能的输入框
<compsAnimateInput
  ref="test"                //this.$refs.test.clearContent() 清空@组件输入框内的内容
  v-model="data"            //@功能的返回值(详见下方描述)
  :dataList="projectMember" //@功能人员列表数据
  placeholder="请输入姓名"   //input提示文字
  labelFootnote="（选填）"   //标签的补充说明 如：姓名（选填）
  needAt                    //是否需要@功能
  setFocus                  //使输入框获取焦点
  height="100px"            //@功能 输入框的高度
  label="姓名">             //输入框的标签名字
</compsAnimateInput>

e.g: 如需要@功能，以下参数不可缺一

needAt 是否需要@功能 默认为不需要，即普通输入框

v-model 接收一个object类型的数据 属性有四个：
  1、nodeValue：输入框中的内容（包含span标签）  类型：string
  2、value：输入框中的内容（纯文本）            类型：string
  3、subscript：已@的人员信息                 类型：array
  4、text：不包含@人员以及任何标签样式的纯文本   类型：string（只有当使用@功能时 才会有该返回值）

dataList 接收一个array类型的数据  传入需要@的人员列表数据
  :dataList="projectMember"

/*********************************************************/





/*****************CompsSearchInput***********************/

import CompsSearchInput from "@/components/CompsCommon/CompsSearchInput";

<CompsSearchInput
  width="200"                 //宽度
  backgroundColor="#FFFFFF"   //输入框背景颜色
  placeholder="请输入项目名称" //input提示文字
  v-model="inputMsg">         //双向绑定数据
</CompsSearchInput>

普通输入框参数：
  width             宽度             类型：string/number    可选（默认：200）
  backgroundColor   背景颜色          类型：string           可选（默认：#FFFFFF）
  placeholder       占位文字          类型：string           可选（默认：“请输入”）
  v-model           双向绑定          类型：string

mini型带动画效果参数：（mini为必选，onTheLeft和onTheRight二选一）
  mini        使用该参数，输入框切换为mini型输入框，点击可展开输入框     必选 类型：bool
  onTheLeft   使用该参数，mini型输入框将从左往右展开                   可选  类型：bool
  onTheRight  使用该参数，mini型输入框将从右往左展开                   可选  类型：bool
/*********************************************************/
