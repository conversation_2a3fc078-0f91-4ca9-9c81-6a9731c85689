import Vue from 'vue'
import Router from 'vue-router'

Vue.use(Router)

var routes = [

    // 构件管理移动端
    {
        path: '/LookModelWeChat/:parentId?/:exceptGuid?',
        name: 'LookModelWeChat',
        component: () =>
            import ('@/components/LookModelWeChat'),
    },
    {
        path: '/detailModelList',
        name: 'detailModelList',
        component: () =>
            import ('@/components/detailModelList')
    },
    {
        path: '/ModelIframe',
        name: 'ModelIframe',
        component: () =>
            import ('@/components/ModelIframe')
    },

    // demo
    {
        path: '/MaterialDemo/Index/:materialcode?',
        name: 'MaterialDemo',
        component: () =>
            import ('@/components/MaterialDemo/Index')
    },

    // 问题追踪-设置
    {
        path: '/Home/ProjectBoot/IssueAdmin/:organizeId?/:Token?',
        name: 'IssueAdmin',
        component: () =>
            import ('@/components/Home/ProjectBoot/IssueAdmin'),
        meta: {
            requiresAuth: true
        }
    },
    // //问题追踪-设置
    {
        path: '/Admin/Valid/:Token?',
        name: 'AdminValid',
        component: () =>
            import ('@/components/Admin/Valid'),
        meta: {
            requiresAuth: true
        }
    },
    {
        path: '/Admin/Index/:Token/:type?',
        name: 'AdminIndex',
        component: () =>
            import ('@/components/Admin/Index'),
        meta: {
            requiresAuth: true
        }
        // //该页面需要登录
    },
    // 为了开发广西玉林大屏的问题=施工进度（进度填报）
    {
        path: '/Admin/guangxiLargeScreen/:organizeId/:Token?',
        name: 'Adminguangxi',
        component: () =>
            import ('@/components/Admin/guangxiLargeScreen'),

        // //该页面需要登录
    },
    // 为了开发广西玉林大屏的问题=验收可视化（质量验收）
    {
        path: '/Admin/guangxiAcceptance/:organizeId/:Token?',
        name: 'AdminAcc',
        component: () =>
            import ('@/components/Admin/guangxiAcceptance'),

        // //该页面需要登录
    },
    {
        path: '/Admin/Index_V2/:Token?',
        name: 'AdminIndex_V2',
        component: () =>
            import ('@/components/Admin/Index_V2'),
        meta: {
            requiresAuth: true
        }
        // //该页面需要登录
    },
    // 构件管理移动端
    {
        path: '/MaterialsMgrMobile/:bm_guid/:dwgurl/:Token?',
        name: 'MaterialsMgrMobile',
        component: () =>
            import ('@/components/MaterialsMgrMobile'),
        meta: {
            requiresAuth: false
        }
    },
    {
        path: '/Test',
        name: 'Test',
        component: () =>
            import ('@/components/Test')
    },
    {
        path: '/CountDown',
        name: 'CountDown',
        component: () =>
            import ('@/components/CountDown')
    },
    {
        path: '/About',
        name: 'About',
        component: () =>
            import ('@/components/About')
    },
    {
        path: '/:ReturnUrl?',
        name: 'Login',
        component: () =>
            import ('@/components/Login')
    },
    {
        path: '/ProjectAcceptInvite/:InvGuid?',
        name: 'ProjectAcceptInvite',
        component: () =>
            import ('@/components/ProjectAcceptInvite'),
        meta: {
            requiresAuth: false
        }
        // //该页面不需要登录
    }, {
        path: '/Home/',
        name: 'Home',
        component: () =>
            import ('@/components/Home'),
        children: [

            // 项目列表-Pano
            {
                path: '/Home/ProjectBootPano/:organizeId?/:Token?',
                name: 'ProjectBootPano',
                component: () =>
                    import ('@/components/Home/ProjectBootPano'),
                meta: {
                    requiresAuth: true
                }
                // //该页面需要登录
            },

            // 个人设置
            {
                path: '/Home/Settings/:Token?',
                name: 'Settings',
                component: () =>
                    import ('@/components/Home/Settings'),
                meta: {
                    requiresAuth: true
                }
                // //该页面需要登录
            },

            // 机构列表
            {
                path: '/Home/OrganizeManage/:Token?',
                name: 'OrganizeManage',
                component: () =>
                    import ('@/components/Home/OrganizeManage'),
                meta: {
                    requiresAuth: true
                }
                // //该页面需要登录
            },
            // 机构日志
            {
                path: '/Home/OrganizationLog/:Token?',
                name: 'OrganizationLog',
                component: () =>
                    import ('@/components/CompsBoot/compsOrganizationLog'),
                meta: {
                    requiresAuth: true
                }
                // //该页面需要登录
            },
            // 构件库管理
            {
                path: '/Home/OrganizationComponentLibrary/:Token?',
                name: 'OrganizationComponentLibrary',
                component: () =>
                    import ('@/components/CompsBoot/CompsComponentLibrary'),
                meta: {
                    requiresAuth: true
                }
                // //该页面需要登录
            },
            // 定制直接选择模型，选择绑定==为了流程定制化
            {
                path: '/Home/CompModelSelect/:organizeId/:Token?',
                name: 'CompModelSelect',
                component: () =>
                    import ('@/components/Home/ProjectBoot/CompModelSelect'),
                meta: {
                    requiresAuth: false
                }
            },
            // 定制直接选择模型，查看模型==为了流程定制化
            {
                path: '/Home/CompModelDetail/:organizeId?/:featureID?',
                name: 'CompModelDetail',
                component: () =>
                    import ('@/components/Home/ProjectBoot/CompModelDetail'),
                meta: {
                    requiresAuth: false
                }
            },
            // 定制直接进去关联工程结构页面，选择绑定==为了流程定制化
            {
                path: '/Home/CompRelConstruct/:organizeId',
                name: 'CompRelConstruct',
                component: () =>
                    import ('@/components/Home/ProjectBoot/CompRelConstruct'),
                meta: {
                    requiresAuth: false
                }
            },

            // 定制直接进去工程结构页面，查看工程结构==为了流程定制化
            {
                path: '/Home/CompRelMater/:organizeId/:guid/:type?',
                name: 'CompRelMater',
                component: () =>
                    import ('@/components/Home/ProjectBoot/CompRelMater'),
                meta: {
                    requiresAuth: false
                }
            },
            {
                path: '/Home/sceneManagementDetail/:organizeId?/:SceneId?/:userId?',
                name: 'HomeSceneManagementDetail',
                component: () =>
                    import ('@/components/Home/ProjectBoot/sceneManagementDetail'),
            },
            {
                path: '/Home/DocumentSummaryOnly/:token?',
                name: 'DocumentSummaryOnly',
                component: () =>
                    import ('@/components/Home/ProjectBoot/DocumentSummary'),
            },


            // 项目列表
            , {
                path: '/Home/Boot/:Token?/:toGIS?/:organizeId?',
                name: 'Boot',
                component: () =>
                    import ('@/components/Home/Boot'),
                meta: {
                    requiresAuth: true
                }
                // //该页面需要登录
            }
            // //项目列表

            // 项目内页
            , {
                path: '/Home/ProjectBoot/:organizeId?/:Token?/:isMain?',
                name: 'ProjectBoot',
                component: () =>
                    import ('@/components/Home/ProjectBoot'),
                children: [
                    // 项目主页
                    {
                        path: '/Home/ProjectBoot/Main/:organizeId?/:Token?',
                        name: 'Main',
                        component: () =>
                            import ('@/components/Home/ProjectBoot/Main'),
                        meta: {
                            requiresAuth: true
                        }
                        // //该页面需要登录
                    }, {
                        path: '/Home/ProjectBoot/indexSingle2/:bimcomposerId?/:modelid?/:planid?',
                        name: 'indexSingle2',
                        component: () =>
                            import ('@/components/Home/ProjectBoot/indexSingle2') //indexSingle2
                    }, {
                        path: '/Home/ProjectBoot/indexSingle/:bimcomposerId?/:modelid?/:planid?',
                        name: 'indexSingle',
                        component: () =>
                            import ('@/components/Home/ProjectBoot/indexSingle') //indexSingle
                    }, {
                        // 模型列表页面
                        path: '/Home/ProjectBoot/Model/:organizeId?/:Token?/:Phase?',
                        name: 'Model',
                        component: () =>
                            import ('@/components/Home/ProjectBoot/Model'),
                        meta: {
                            requiresAuth: true
                        }
                        // //该页面需要登录
                    }, 
                    {
                         // 项目结构页面
                        path: '/Home/ProjectBoot/ProjectStructure/:organizeId?/:Token?',
                        name: 'ProjectStructure',
                        component: () =>
                            import ('@/components/Home/ProjectBoot/ProjectStructure'),
                        meta: {
                            requiresAuth: true
                        }
                        // //该页面需要登录
                    },
                    {
                        // 流程菜单
                        path: '/Home/ProjectBoot/FormFlow/:organizeId?/:Token?/:flowNo?',
                        name: 'FormFlow',
                        component: () =>
                            import ('@/components/Home/ProjectBoot/FormFlow'),
                        meta: {
                            requiresAuth: true
                        }
                        // //该页面需要登录
                    }, {
                        // 模型单独打开页面
                        path: '/Home/ProjectBoot/Model/BIMViewerRefresh/:organizeId?/:Token?/:Phase?',
                        name: 'BIMViewerRefresh',
                        component: () =>
                            import ('@/components/Home/ProjectBoot/Model/BIMViewerRefresh') //BIMViewerRefresh
                            ,
                        meta: {
                            requiresAuth: true
                        }
                    }, {
                        path: '/Home/ProjectBoot/sceneManagementDetail/:organizeId?/:SceneId?/:userId?',
                        name: 'HomeProjectBootSceneManagementDetail',
                        component: () =>
                            import ('@/components/Home/ProjectBoot/sceneManagementDetail'),
                    }, {
                        // 文档管理-文档管理-项目文档
                        path: '/Home/ProjectBoot/Document/:organizeId?/:Token?',
                        name: 'Document',
                        component: () =>
                            import ('@/components/Home/ProjectBoot/Document'),
                        meta: {
                            requiresAuth: true
                        }
                        // //该页面需要登录
                    },
                    {
                        // 文档管理-文档管理-文档汇总
                        path: '/Home/ProjectBoot/DocumentSummary/:organizeId?/:Token?',
                        name: 'DocumentSummary',
                        component: () =>
                            import ('@/components/Home/ProjectBoot/DocumentSummary'),
                        meta: {
                            requiresAuth: true
                        }
                        // //该页面需要登录
                    },
                    {
                        // 档案管理--档案汇总
                        path: '/Home/ProjectBoot/ProjectArchivesSummary/:organizeId?/:Token?',
                        name: 'ProjectArchivesSummary',
                        component: () =>
                            import ('@/components/Home/ProjectBoot/ProjectArchivesSummary'),
                        meta: {
                            requiresAuth: true
                        }
                        // //该页面需要登录
                    },
                    {
                        // 档案管理--项目档案
                        path: '/Home/ProjectBoot/ProjectArchives/:organizeId?/:Token?',
                        name: 'ProjectArchives',
                        component: () =>
                            import ('@/components/Home/ProjectBoot/ProjectArchives'),
                        meta: {
                            requiresAuth: true
                        }
                        // //该页面需要登录
                    },
                    {
                        // 消息列表
                        path: '/Home/ProjectBoot/Message/MessageList/:Token?',
                        name: 'MessageList',
                        component: () =>
                            import ('@/components/Home/ProjectBoot/Message/MessageList') //MessageList
                            ,
                        meta: {
                            requiresAuth: true
                        }
                    }, {
                        // 项目设置
                        path: '/Home/ProjectBoot/Setting/:organizeId?/:Token?',
                        name: 'Setting',
                        component: () =>
                            import ('@/components/Home/ProjectBoot/Setting'),
                        meta: {
                            requiresAuth: true
                        }
                        // //该页面需要登录
                    },

                    {
                        // 模型分享管理页面
                        path: '/Home/ProjectBoot/ModelSharedMgr/:organizeId?/:Token?/',
                        name: 'ModelSharedMgr',
                        component: () =>
                            import ('@/components/Home/ProjectBoot/ModelSharedMgr') //ModelSharedMgr
                            ,
                        meta: {
                            requiresAuth: true
                        }
                    }, {
                        // 角色权限
                        path: '/Home/ProjectBoot/RoleAuth/:organizeId?/:Token?',
                        name: 'RoleAuth',
                        component: () =>
                            import ('@/components/Home/ProjectBoot/RoleAuth'),
                        meta: {
                            requiresAuth: true
                        }
                        // //该页面需要登录
                    },
                    {
                        // 项目级=项目设置=菜单管理
                        path: '/Home/ProjectBoot/projectMenuManage/:organizeId?/:Token?',
                        name: 'projectMenuManage',
                        component: () =>
                            import ('@/components/Home/ProjectBoot/projectMenuManage'),
                        meta: {
                            requiresAuth: true
                        }
                    }, {
                        // 签章管理
                        path: '/Home/ProjectBoot/projectSignature/:organizeId?/:Token?',
                        name: 'projectSignature',
                        component: () =>
                            import ('@/components/Home/ProjectBoot/projectSignature'),
                        meta: {
                            requiresAuth: true
                        }
                    }, {
                        // 项目成员
                        path: '/Home/ProjectBoot/ProjectMembers/:organizeId?/:Token?',
                        name: 'ProjectMembers',
                        component: () =>
                            import ('@/components/Home/ProjectBoot/ProjectMembers'),
                        meta: {
                            requiresAuth: true
                        }
                        // //该页面需要登录
                    }, {
                        // 流程管理
                        path: '/Home/ProjectBoot/processManagement/:organizeId?/:Token?',
                        name: 'processManagement',
                        component: () =>
                            import ('@/components/Home/ProjectBoot/processManagement'),
                        meta: {
                            requiresAuth: true
                        }
                    }, {
                        // 项目日志
                        path: '/Home/ProjectBoot/Log/:organizeId?/:Token?',
                        name: 'ProjectLog',
                        component: () =>
                            import ('@/components/Home/ProjectBoot/Log') //ProjectLog
                            ,
                        meta: {
                            requiresAuth: true
                        }
                    },
                    // 会议看板
                    {
                        path: '/Home/ProjectBoot/Meeting/:organizeId?/:Token?/:status?',
                        name: 'Meeting',
                        component: () =>
                            import ('@/components/Home/ProjectBoot/Meeting/Board/index'),
                        meta: {
                            requiresAuth: true
                        }
                    },
                    // 新建会议
                    {
                        path: '/Home/ProjectBoot/createMeeting/:organizeId?/:Token?/:id?/:status?',
                        name: 'createMeeting',
                        component: () =>
                            import ('@/components/Home/ProjectBoot/Meeting/Board/createMeeting'),
                        meta: {
                            requiresAuth: true
                        }
                    },
                    // 会议室管理
                    {
                        path: '/Home/ProjectBoot/MeetingSetting/:organizeId?/:Token?/:Phase?',
                        name: 'MeetingSetting',
                        component: () =>
                            import ('@/components/Home/ProjectBoot/Meeting/MeetingSetting/index'),
                        meta: {
                            requiresAuth: true
                        }
                    },
                    // 会议控制台
                    {
                        path: '/Home/ProjectBoot/MeetingControl/:organizeId?/:Token?/:Phase?',
                        name: 'MeetingControl',
                        component: () =>
                            import ('@/components/Home/ProjectBoot/Meeting/MeetingControl/index'),
                        meta: {
                            requiresAuth: true
                        }
                    },
                    // =====分割线
                    // /*
                    {
                        // 我的任务
                        path: '/Home/ProjectBoot/MyWorkFlowTask/:organizeId?/:Token?',
                        name: 'MyWorkFlowTask',
                        component: () =>
                            import ('@/components/Home/ProjectBoot/MyWorkFlowTask'),
                        meta: {
                            requiresAuth: true
                        }
                    },
                    {
                        // 全部任务
                        path: '/Home/ProjectBoot/AllWorkFlowTask/:organizeId?/:Token?',
                        name: 'AllWorkFlowTask',
                        component: () =>
                            import ('@/components/Home/ProjectBoot/AllWorkFlowTask'),
                        meta: {
                            requiresAuth: true
                        }
                    },
                    {
                        // 场景管理
                        path: '/Home/ProjectBoot/sceneManagement/:organizeId?/:Token?/:Id?',
                        name: 'sceneManagement',
                        component: () =>
                            import ('@/components/Home/ProjectBoot/sceneManagement'),
                        meta: {
                            requiresAuth: true
                        }
                    },
                    {
                        // 问题协同
                        path: '/Home/ProjectBoot/Issue/:organizeId?/:Token?/:showconfig?',
                        name: 'issue',
                        component: () =>
                            import ('@/components/Home/ProjectBoot/Issue'),
                        meta: {
                            requiresAuth: true
                        }
                    },
                    {
                        path: '/Home/ProjectBoot/MaterialsMgr/:organizeId?/:Token?',
                        name: 'MaterialsMgr',
                        component: () =>
                            import ('@/components/Home/ProjectBoot/MaterialsMgr'),
                        meta: {
                            requiresAuth: true
                        }
                    },
                    // 质量验收
                    {
                        path: '/Home/ProjectBoot/qualityAcceptance/:organizeId?/:Token?',
                        name: 'qualityAcceptance',
                        component: () =>
                            import ('@/components/Home/ProjectBoot/qualityAcceptance'),
                        meta: {
                            requiresAuth: true
                        }
                    },
                    // 质量划分
                    {
                        path: '/Home/ProjectBoot/qualityDivision/:organizeId?/:Token?',
                        name: 'qualityDivision',
                        component: () =>
                            import ('@/components/Home/ProjectBoot/qualityDivision'),
                        meta: {
                            requiresAuth: true
                        }
                    },

                    {
                        // 进度方案
                        path: '/Home/ProjectBoot/CompsProgress/:organizeId?/:Token?',
                        name: 'CompsProgress',
                        component: () =>
                            import ('@/components/Home/ProjectBoot/CompsProgress'),
                        meta: {
                            requiresAuth: true
                        }
                    },
                    {
                        // 进度填报
                        path: '/Home/ProjectBoot/ProgressReporting/:organizeId?/:Token?',
                        name: 'ProgressReporting',
                        component: () =>
                            import ('@/components/Home/ProjectBoot/ProgressReporting'),
                        meta: {
                            requiresAuth: true
                        }
                    },
                    {
                        // 现场管理-现场巡查--质量管理
                        path: '/Home/ProjectBoot/QualityManage/:organizeId?/:Token?',
                        name: 'QualityManage',
                        component: () =>
                            import ('@/components/Home/ProjectBoot/QualityManage'),
                        meta: {
                            requiresAuth: true
                        }
                    },
                    {
                        // 现场管理-现场巡查--安全管理
                        path: '/Home/ProjectBoot/QualityManageSafety/:organizeId?/:Token?',
                        name: 'QualityManageSafety',
                        component: () =>
                            import ('@/components/Home/ProjectBoot/QualityManageSafety'),
                        meta: {
                            requiresAuth: true
                        }
                    },
                    {
                        // 投资填报
                        path: '/Home/ProjectBoot/InvestReport/:organizeId?/:Token?',
                        name: 'InvestReport',
                        component: () =>
                            import ('@/components/Home/ProjectBoot/InvestReport'),
                        meta: {
                            requiresAuth: true
                        }
                    },

                    // 之前的成本管理、删除了
                    // {
                    //     // 成本管理
                    //     path: '/Home/ProjectBoot/costmgr/:organizeId?/:Token?',
                    //     name: 'costmgr',
                    //     component: () =>
                    //         import ('@/components/Home/ProjectBoot/costmgr'),
                    //     meta: {
                    //         requiresAuth: true
                    //     }
                    // },
                    {
                        // 全景图管理
                        path: '/Home/ProjectBoot/Pano/:organizeId?/:Token?',
                        name: 'Pano',
                        component: () =>
                            import ('@/components/Home/ProjectBoot/Pano'),
                        meta: {
                            requiresAuth: true
                        }
                    },
                    {
                        // 全景图对比
                        path: '/Home/ProjectBoot/PanoComparison/:organizeId?/:Token?',
                        name: 'PanoComparison',
                        component: () =>
                            import ('@/components/Home/ProjectBoot/PanoComparison'),
                        meta: {
                            requiresAuth: true
                        }
                    },
                    {
                        // 全景图的标签设置=实际已经删除，功能在全景图管理中
                        path: '/Home/ProjectBoot/PanoSetting/:organizeId?/:Token?',
                        name: 'PanoSetting',
                        component: () =>
                            import ('@/components/Home/ProjectBoot/PanoSetting'),
                        meta: {
                            requiresAuth: true
                        }
                    },

                  {
                    path: '/Home/ProjectBoot/TZManage/fill/:organizeId?/:Token?',
                    name: 'TZFill',
                    component: () =>
                      import ('@/components/Home/ProjectBoot/TZManage/Fill'),
                    meta: {
                      requiresAuth: true
                    }
                  },
                  {
                    path: '/Home/ProjectBoot/TZManage/plan/:organizeId?/:Token?',
                    name: 'TZPlan',
                    component: () =>
                      import ('@/components/Home/ProjectBoot/TZManage/Plan'),
                    meta: {
                      requiresAuth: true
                    }
                  },

                    // */

                ]
            }
            // //项目内页
        ]
    },

    // 全景图分享地址
    {
        path: '/LinkShare/PanoShare/:PanoId/:organizeId',
        component: () =>
            import ('@/components/LinkShare/PanoShare'),
        meta: {
            requiresAuth: false
        }
    },
    // //全景图分享地址

    // 全景图分享地址2 - App端的
    {
        path: '/LinkShare/PanoShare/:PanoId/:organizeId/:StartScene',
        component: () =>
            import ('@/components/LinkShare/PanoShare'),
        meta: {
            requiresAuth: false
        }
    },
    // //全景图分享地址2 - App端的

    {
        path: '/LinkShare/MShare/MShareModel/:SessionId?',
        component: () =>
            import ('@/components/LinkShare/MShare/MShareModel'),
        meta: {
            requiresAuth: false, //该页面不需要登录
        }
    },
    // 新版本直接打开模型
    {
        path: '/modelNewIframeLoading/:vaultID?/:featureID?',
        name: 'modelNewIframeLoading',
        component: () =>
            import ('@/components/Home/ProjectBoot/modelNewIframeLoading') //BIMViewerRefresh
            ,
        meta: {
            requiresAuth: false
        }
    },
    // 模型分享
    {
        path: '/modelNewShareLoad/:PID/:MID?',
        name: 'modelNewShareLoad',
        component: () =>
            import ('@/components/Home/ProjectBoot/Model/modelNewShareLoad'),
        meta: {
            requiresAuth: false, //该页面不需要登录
        }
    },

    {
        path: '/LinkShare/DocShare/index',
        component: () =>
            import ('@/components/LinkShare/DocShare'),
        meta: {
            requiresAuth: false, //该页面不需要登录
        },
        children: [{
                path: '/LinkShare/DocShare/verify',
                name: 'DocShareEncrypted',
                component: () =>
                    import ('@/components/LinkShare/DocShareEncrypted'),
                meta: {
                    requiresAuth: false, //该页面不需要登录
                }
            },

            {
                path: '/LinkShare/DocShare/docList',
                name: 'DocShareDocList',
                component: () =>
                    import ('@/components/LinkShare/DocShareDocList'),
                meta: {
                    requiresAuth: false, //该页面不需要登录
                }
            }
        ]
    },
    //app下载地址
    {
        path: '/LinkShare/AppDownloadLinkQR',
        name: 'AppDownloadLinkQR',
        component: () =>
            import ('@/components/LinkShare/AppDownloadLinkQR')
    },
    {
        path: '/Visual/VisualShow',
        name: 'Visual',
        component: () =>
            import ('@/components/Visual/VisualShow')
    },
    {
        path: '/VisualizationPanel/VisualizationPanelShow/:organizeId?/:Token?',
        name: 'VisualizationPanel',
        component: () =>
            import ('@/components/VisualizationPanel/VisualizationPanelShow')
    },
    {
        path: '/KrpanoPreview/:PanoId',
        name: 'KrpanoPreview',
        component: () =>
            import ('@/components/Krpano')
    }
];
var router = new Router({
    routes: routes
});

export default router;
