'use strict'
// Template version: 1.3.1
// see http://vuejs-templates.github.io/webpack for documentation.

const path = require('path')

const proxyIp = require('../static/config/proxyIp')
module.exports = {
    dev: {

        // Paths
        assetsSubDirectory: 'static',
        assetsPublicPath: '/',
        proxyTable: {
            // '/mapBaidu': { //百度地图查询api代理
            //     target: 'https://api.map.baidu.com/place/v2',
            //     changeOrigin: true,
            //     pathRewrite: {
            //         '^/mapBaidu': ''
            //     }
            // },
            "/newModelApi": {
                target: proxyIp.newModelHttpUrl, //'http://multiverse-server.vothing.com',
                changeOrigin: true,
                pathRewrite: {
                    "^/newModelApi": "/" //这里是将/api替换为空字符串“” ，也就是删除的意思
                }
            }
            // "/api": {
            //     target: proxyIp.BASE_URL, //"http://workflow-api.probim.cn", //  bime==只要是以/api开头的链接都会被代理到 这个target属性所代表的位置（我这里是：http://help.jflow.cn:8081/）
            //     // target: "http://help.jflow.cn:8081", // 驰骋===只要是以/api开头的链接都会被代理到 这个target属性所代表的位置（我这里是：http://help.jflow.cn:8081/）
            //     ws: false,
            //     changeOrigin: true,
            //     cookieDomainRewrite: {
            //         "*": ""
            //     },
            //     cookiePathRewrite: {
            //         "*": ""
            //     },
            //     timeout: 300000, //设置超时时间
            //     pathRewrite: {
            //         "^/api": "" //这里是将/api替换为空字符串“” ，也就是删除的意思
            //     }
            // }
        },

        // Various Dev Server settings
        host: '0.0.0.0', //'localhost', // can be overwritten by process.env.HOST
        port: 8081, // can be overwritten by process.env.PORT, if port is in use, a free one will be determined
        autoOpenBrowser: false,
        errorOverlay: true,
        notifyOnErrors: true,
        poll: false, // https://webpack.js.org/configuration/dev-server/#devserver-watchoptions-


        /**
         * Source Maps
         */

        // https://webpack.js.org/configuration/devtool/#development
        devtool: 'source-map',
        useEslint: false,
        // If you have problems debugging vue-files in devtools,
        // set this to false - it *may* help
        // https://vue-loader.vuejs.org/en/options.html#cachebusting
        cacheBusting: true,

        cssSourceMap: true
    },

    build: {
        // Template for index.html
        index: path.resolve(__dirname, '../dist/index.html'),

        // Paths
        assetsRoot: path.resolve(__dirname, '../dist'),
        assetsSubDirectory: 'static',
        assetsPublicPath: './',

        /**
         * Source Maps
         */

        productionSourceMap: false,
        // https://webpack.js.org/configuration/devtool/#production
        devtool: '#source-map',

        // Gzip off by default as many popular static hosts such as
        // Surge or Netlify already gzip all static assets for you.
        // Before setting to `true`, make sure to:
        // npm install --save-dev compression-webpack-plugin
        productionGzip: false,
        productionGzipExtensions: ['js', 'css'],

        // Run the build command with an extra argument to
        // View the bundle analyzer report after build finishes:
        // `npm run build --report`
        // Set to `true` or `false` to always turn it on or off
        bundleAnalyzerReport: process.env.npm_config_report
    }
}
