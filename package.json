{"name": "probim_newweb", "version": "1.0.0", "description": "A Vue.js project", "author": "", "private": true, "scripts": {"dev": "webpack-dev-server --inline --progress --config build/webpack.dev.conf.js", "start": "npm run dev", "build": "node build/build.js"}, "dependencies": {"axios": "^0.21.1", "crypto-js": "^3.1.9-1", "dayjs": "^1.11.7", "echarts": "^4.9.0", "echarts-liquidfill": "^2.0.6", "element-ui": "^2.7.0", "html2canvas": "^1.4.1", "jingruizhang-probim-vue": "^1.1.15", "jquery": "^3.7.0", "js-cookie": "^3.0.1", "js-md5": "^0.7.3", "js-pinyin": "^0.2.5", "jsplumb": "^2.15.6", "less": "^4.1.3", "less-loader": "^5.0.0", "moment": "^2.29.4", "npm": "^6.12.1", "qrcodejs2": "0.0.2", "qs": "^6.6.0", "swiper": "^4.5.0", "tdesign-vue": "^0.51.1", "throttle-debounce": "^5.0.0", "umy-ui": "^1.1.6", "velocity-animate": "^2.0.5", "vue": "^2.5.2", "vue-axios": "^2.1.4", "vue-cropperjs": "^4.0.0", "vue-echarts": "^4.0.4", "vue-router": "^3.0.1", "vue-skeleton-webpack-plugin": "^1.2.2", "vuex": "^3.6.2"}, "devDependencies": {"@babel/plugin-proposal-optional-chaining": "^7.18.9", "@jiaminghi/data-view": "^2.10.0", "autoprefixer": "^7.1.2", "babel-core": "^6.22.1", "babel-helper-vue-jsx-merge-props": "^2.0.3", "babel-loader": "^7.1.1", "babel-plugin-syntax-jsx": "^6.18.0", "babel-plugin-transform-runtime": "^6.22.0", "babel-plugin-transform-vue-jsx": "^3.5.0", "babel-preset-env": "^1.3.2", "babel-preset-stage-2": "^6.22.0", "chalk": "^2.0.1", "copy-webpack-plugin": "^4.0.1", "css-loader": "^0.28.0", "extract-text-webpack-plugin": "^3.0.0", "file-loader": "^1.1.4", "friendly-errors-webpack-plugin": "^1.6.1", "html-webpack-plugin": "^2.30.1", "node-notifier": "^5.1.2", "node-sass": "^4.14.1", "optimize-css-assets-webpack-plugin": "^3.2.0", "ora": "^1.2.0", "portfinder": "^1.0.13", "postcss-import": "^11.0.0", "postcss-loader": "^2.0.8", "postcss-url": "^7.2.1", "rimraf": "^2.6.0", "sass-loader": "^7.0.3", "semver": "^5.3.0", "shelljs": "^0.7.6", "stylus": "^0.54.7", "stylus-loader": "^3.0.2", "svg-sprite-loader": "^6.0.11", "uglifyjs-webpack-plugin": "^1.1.1", "url-loader": "^0.5.8", "vue-loader": "^13.3.0", "vue-seamless-scroll": "^1.1.23", "vue-style-loader": "^3.0.1", "vue-template-compiler": "^2.5.2", "webpack": "^3.6.0", "webpack-bundle-analyzer": "^2.9.0", "webpack-dev-server": "^2.9.1", "webpack-merge": "^4.1.0"}, "engines": {"node": ">= 6.0.0", "npm": ">= 3.0.0"}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 8"]}